<?php

namespace App\Http\Controllers\Sales;

use App\Facades\Permissions;
use App\Facades\Settings;
use App\Http\Controllers\Controller;
use Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\ButtonServiceErrorHandlerDecorator;
use Izam\Daftra\AppManager\Utils\AppButtonLocationUtil;
use Izam\Daftra\Common\Utils\FollowUpReminderUtil;
use App\Facades\Plugins;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\PluginUtil;

class SettingsController extends Controller
{

    const GROUP_BILLING_INVOICING = 'billing_invoicing';
    const GROUP_ESTIMATES = 'estimates';
    const GROUP_SALES_ORDERS = 'sales_orders';
    const GROUP_INTEGRATIONS = 'integrations';

    public function __construct(
        private ButtonServiceErrorHandlerDecorator $buttonService
    ) {
        parent::__construct();
    }

    public function __invoke()
    {
        $links = array_merge(
            static::getBillingAndInvoicingCards(),
            static::getEstimateCards(),
            static::getSalesOrdersCards(),
            $this->getIntegrationsCards(),
        );
        $cardsMeatData = static::getCardsMeta();
        return view('sales_settings/settings', compact('links', 'cardsMeatData'));
    }

    private static function getCardsMeta():array
    {
        return [
            static::GROUP_BILLING_INVOICING => [
                'title' => 'Billing & Invoicing',
                'description' => '',
            ],
            static::GROUP_ESTIMATES => [
                'title' => 'Estimates',
                'description' => '',
            ],
            static::GROUP_SALES_ORDERS => [
                'title' => 'Sales Orders',
                'description' => '',
            ],
            static::GROUP_INTEGRATIONS => [
                'title' => 'Integrations',
                'description' => '',
            ],
        ];
    }

    private static function getBillingAndInvoicingCards(): array
    {
        $authUserID = getAuthOwner('staff_id');
        return [
            static::GROUP_BILLING_INVOICING => [
                [
                    'title' => 'Invoicing Settings',
                    'description' => 'Manage the invoice creation form and how invoices are displayed.',
                    'url' => '/owner/invoices/settings',
                    'icon' => 'mdi mdi-receipt-text',
                    'show' => true,
                ],
                [
                    'title' => 'Invoice Statuses',
                    'description' => 'Customize invoice statuses and use them across invoices and sales reports.',
                    'url' => '/owner/follow_up_statuses/index/' . FollowUpReminderUtil::INVOICE_TYPE,
                    'icon' => 'mdi mdi-flag',
                    'show' => true,
                ],
                [
                    'title' => __t('Electronic Invoice Settings'),
                    'description' => 'Customize electronic invoicing to fit your sales needs.',
                    'url' => route('owner.electronic_invoice_settings'),
                    'icon' => 'mdi mdi-file-cog',
                    'show' => Plugins::pluginActive(PluginUtil::ETA_PLUGIN),
                ],
                [
                    'title' => 'Electronic Invoice Settings',
                    'description' => 'Configure and connect your e-invoicing and receipt setup with the local tax authority.',
                    'url' => route('owner.list_ksa_electronic_invoice_settings'),
                    'icon' => 'mdi mdi-file-cog',
                    'show' => Plugins::pluginActive(PluginUtil::EINVOICE_PLUGIN),
                ],
                [
                    'title' => 'Jordanian E-Invoice Settings',
                    'description' => 'Configure and connect your e-invoicing and receipt setup with the local tax authority.',
                    'url' => route('owner.jordanian.einvoice.settings.list'),
                    'icon' => 'mdi mdi-receipt',
                    'show' => Plugins::pluginActive(PluginUtil::JORDAN_EINVOICE_PLUGIN),
                ],
                [
                    'title' => 'Invoice/Estimate Layouts',
                    'description' => 'Manage and customize layouts for sales documents.',
                    'url' => '/owner/invoice_layouts/index',
                    'icon' => 'mdi mdi-brush-variant',
                    'show' => true,
                ],
                [
                    'title' => 'Invoice Custom Fields',
                    'description' => 'Manage additional fields for invoices with different types of available fields and their details.',
                    'url' => '/owner/custom_forms/edit_custom_fields/invoices/?redir=0',
                    'icon' => 'mdi mdi-auto-fix',
                    'show' => true,
                ],
                [
                    'title' => 'Price Lists',
                    'description' => 'Set up and control multiple sales price lists for different customers and markets.',
                    'url' => '/v2/owner/price_lists',
                    'icon' => 'mdi mdi-tag-text',
                    'show' => true,
                ],
                [
                    'title' => 'Order Sources',
                    'description' => 'Add, activate, deactivate, or set default sources for tracking where orders originate.',
                    'url' => route('owner.order_sources.index'),
                    'icon' => 'mdi mdi-filter',
                    'show' => Permissions::checkPermission(PermissionUtil::Edit_General_Settings, $authUserID),
                ],
                [
                    'title' => 'Shipping & Delivery Options',
                    'description' => 'Manage shipping options to define methods, set availability, and customize preferences.',
                    'url' => route("owner.shipping_options.index"),
                    'icon' => 'mdi mdi-truck',
                    'show' => Permissions::checkPermission(PermissionUtil::Edit_General_Settings, $authUserID),
                ],
                [
                    'title' => 'Offers',
                    'description' => 'Customize offers and pricing rules effectively for your business.',
                    'url' => '/owner/offers/index',
                    'icon' => 'mdi mdi-sale',
                    'show' => Plugins::pluginActive(PluginUtil::OffersPlugin),
                ],
            ],
        ];
    }

    private static function getEstimateCards(): array
    {
        return [
            static::GROUP_ESTIMATES => [
                [
                    'title' => 'Estimate Settings',
                    'description' => 'Configure how estimates are created, managed, and converted into invoices.',
                    'url' => '/owner/invoices/settings',
                    'icon' => 'fa fa-file-contract',
                    'show' => true,
                ],
                [
                    'title' => 'Estimate Statuses',
                    'description' => 'Customize estimate statuses and use them across estimates and sales reports.',
                    'url' => '/owner/follow_up_statuses/index/'. FollowUpReminderUtil::ESTIMATE_TYPE,
                    'icon' => 'mdi mdi-flag',
                    'show' => Settings::getValue(PluginUtil::InvoicesPlugin, 'enable_estimate_status') && Plugins::pluginActive(PluginUtil::FollowupPlugin),
                ],
            ],
        ];
    }

    private static function getSalesOrdersCards(): array
    {
        return [
            static::GROUP_SALES_ORDERS => [
                [
                    'title' => 'Sales Order Settings',
                    'description' => 'Set up and manage sales order creation, numbering, and statuses.',
                    'url' => '/owner/invoices/settings',
                    'icon' => 'mdi mdi-clipboard-text',
                    'show' => true,
                ],
                [
                    'title' => 'Sales Order Statuses',
                    'description' => 'Customize sales order statuses and use them across sales orders and sales reports.',
                    'url' => '/owner/follow_up_statuses/index/'. FollowUpReminderUtil::SALES_ORDER_TYPE,
                    'icon' => 'mdi mdi-flag',
                    'show' => Settings::getValue(PluginUtil::InvoicesPlugin, 'enable_sales_order_status') && Plugins::pluginActive(PluginUtil::FollowupPlugin),
                ],
            ],
        ];
    }
    private function getIntegrationsCards(): array
    {
        $staffId = getAuthStaff('id');
        if (
            Permissions::checkPermission(PermissionUtil::INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS, $staffId) ||
            Permissions::checkPermission(PermissionUtil::Invoices_Add_New_Invoices, $staffId)
        ) {
            $cards = $this->buttonService->getCardsByLocationKey(AppButtonLocationUtil::SALES_SETTINGS)->toArray();
        }
        if (empty($cards)) {
            return [];
        }
        $mappedCards = [];
        foreach ($cards  as $tabAction) {
            $mappedCards[static::GROUP_INTEGRATIONS][] = [
                'title' => $tabAction->getLabel(),
                'description' => '',
//                'description' => $tabAction->getDescription(),
                'url'   => $tabAction->getUrl(),
                'icon'  => $tabAction->getIcon(),
                'show'  => true,
            ];
        }
        return $mappedCards;
    }
}
