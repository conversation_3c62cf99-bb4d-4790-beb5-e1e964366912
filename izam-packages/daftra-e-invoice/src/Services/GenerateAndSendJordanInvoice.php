<?php

namespace Izam\EInvoice\Services;

use App\Facades\Settings;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\ServerException;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\EInvoice\DTO\Config\InvoiceAuthentication;
use Izam\EInvoice\DTO\UBL\InvoiceDTO;
use Izam\EInvoice\Exceptions\FatooraException;
use Izam\Daftra\Common\Repositories\EntityAppDataRepository;
use Izam\Daftra\Common\Utils\ElectronicInvoicesActionsUtil;
use Izam\Daftra\Common\Utils\EntityAppDataKeysUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Exception;
use Izam\EInvoice\Utils\HttpClient;

class GenerateAndSendJordanInvoice
{

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private InvoiceDTO $invoice,
        private $url,
        private InvoiceAuthentication $invoiceAuthentication,
        private EntityAppDataRepository $entityAppDataRepository,
        private $production = true,
        private $is_silent = true,
    )
    {
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function send()
    {
        $this->deleteError();
        try {
            $this->entityAppDataRepository->deleteCritria(EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE, ElectronicInvoicesActionsUtil::SIGNED_INVOICE, EntityKeyTypesUtil::INVOICE_ENTITY_KEY, $this->invoice->getOriginalId());
        } catch (\Izam\EInvoice\FatooraException $exception) {
            if (!$this->is_silent) {
                throw new Exception("Unknown Error Happened from fatoorah please, try again ". $exception->getMessage());
            }
//            Log::error("Unknown Error Happened from fatoorah please, try again", ["exception" => $exception]);
            $this->insertError([$exception->getMessage()]);
        } catch (Exception $exception) {
            if (!$this->is_silent) {
                throw new Exception("Unknown Error Happened please, try again ".$exception->getMessage());
            }
//            Log::error("Unknown Error Happened please, try again later ");
            $this->insertError(["Unknown Error Happened please, try again later ".$exception->getMessage()]);
        }
        $data['uuid'] = $this->invoice->getUuid();
        if (PHP_SAPI != 'cli') {
            $this->sentInvoiceRequest($data);
        }

    }
    private function sentInvoiceRequest($data)
    {
        $payload = [];
        $payload['timeout'] = 10;          // max seconds for the whole request
        $payload['connect_timeout'] = 5;
        $payload['defaults'] = ['verify' => false];
        $payload['headers'] = [
            'Accept-Language' => 'en',
            'Accept-Version' => 'V2',
            'Content-Type' => 'application/json',
            'accept' => 'application/json',
            "Client-id"=> $this->invoiceAuthentication->getClientId(),
            "Secret-Key"=> $this->invoiceAuthentication->getClientSecret(),
        ];
        $payload['body'] = json_encode([
            'invoice' => base64_encode($this->invoice->toString())
        ], JSON_UNESCAPED_UNICODE);
        try {
            $clients = new HttpClient;
            $response =  $clients->sendRequest($this->url, "post", $payload);
            if ($this->production) {
                $rawResponse = json_decode($response->getBody()->getContents(), true);
                $this->entityAppDataRepository->deleteCritria(EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE, 'error', EntityKeyTypesUtil::INVOICE_ENTITY_KEY, $this->invoice->getOriginalId());
                $this->entityAppDataRepository->insertGetId(
                    [
                        'entity_key' => EntityKeyTypesUtil::INVOICE_ENTITY_KEY,
                        'entity_id' => $this->invoice->getOriginalId(),
                        'app_key' => EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE,
                        'data' => json_encode(['status' => 'Valid', 'uuid' => $this->invoice->getUuid(), "response" => $rawResponse , 'env' => $this->invoiceAuthentication->getEnvironment() ]),
                        'action_key' => ElectronicInvoicesActionsUtil::GET_DOCUMENT
                    ]);

            }
        } catch (ClientException|ServerException $exception) {
            $this->fail($exception);
        } catch (ConnectException $exception) {
            $this->insertError(["Couldn't connect to Jordan e-Invoice portal"]);
        } catch (Exception $exception) {

            $this->insertError(["Unknown Error Happened please, try again later"]);
        }
    }

    public function fail($exception = null)
    {
        $body = json_decode($exception->getResponse()->getBody()->getContents(), true);
        if (!empty($body) && !empty($body["EINV_RESULTS"]["ERRORS"])) {
            $errors = array_filter(array_column($body["EINV_RESULTS"]["ERRORS"], "EINV_MESSAGE"));
        } else {
            $errors[] = $exception->getMessage();
        }
        $this->insertError($errors);
        if (!$this->is_silent) {
            throw new Exception(implode("<br/>", $errors));
        }
    }

    public function failEInvoice()
    {

    }

    public function failJordan()
    {

    }

    private function insertError($errors)
    {
        if (!$this->production) {
            return;
        }
        $this->deleteError();
        $this->entityAppDataRepository->insertGetId(
            [
                'entity_key' => EntityKeyTypesUtil::INVOICE_ENTITY_KEY,
                'entity_id' => $this->invoice->getOriginalId(),
                'app_key' => EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE,
                'data' => json_encode($errors),
                'action_key' => 'error'
            ]);
    }

    private function deleteError()
    {
        $this->entityAppDataRepository->deleteCritria(EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE, 'error', EntityKeyTypesUtil::INVOICE_ENTITY_KEY, $this->invoice->getOriginalId());
    }
}
