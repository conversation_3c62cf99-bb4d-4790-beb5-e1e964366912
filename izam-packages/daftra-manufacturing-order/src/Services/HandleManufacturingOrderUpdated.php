<?php

namespace Izam\ManufacturingOrder\Services;

/**
 * Framework-agnostic coordinator for side effects after a Manufacturing Order is updated.
 *
 * Call run($manufacturingOrderData) from Laravel listeners, CakePHP subscribers, jobs, or controllers.
 * $manufacturingOrderData should be an object with at least: id, status, quantity
 * and any fields required by the cost sheet service.
 */
class HandleManufacturingOrderUpdated
{
    public function __construct(
        protected ManufacturingOrderCostSheetService $manufacturingOrderCostSheetService,
        protected ProductionPlanService $productionPlanService
    ) {}

    public function run(object $manufacturingOrderData): void
    {
        if (($manufacturingOrderData->status ?? 'draft') !== 'draft') {
            $this->manufacturingOrderCostSheetService->updateCostsOnUpdateManufacturingOrder($manufacturingOrderData);
            $this->productionPlanService->updateManufacturingOrderProductionPlan($manufacturingOrderData);
        }
    }
}
