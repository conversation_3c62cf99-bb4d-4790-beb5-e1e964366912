<?php

namespace Izam\ManufacturingOrder\Services;

use Izam\Daftra\Common\Entity\Actions\ShowAction\AppEntityShowAction;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\ManufacturingOrderStatusUtil;
use Izam\Daftra\Common\Utils\RequisitionOrderTypeUtil;
use Izam\Entity\Repository\DynamicRepo;
use Izam\Daftra\Common\Utils\RequisitionUtil;
use Izam\Daftra\Product\Services\ProductService;
use Izam\ManufacturingOrder\Events\ManufacturingOrderUpdated;
use Izam\ManufacturingOrder\Exceptions\CannotExceedProductQuantity;
use Izam\ManufacturingOrder\Exceptions\CannotSelectNewProduct;
use Izam\ManufacturingOrder\Exceptions\CannotUpdateOrDeleteFinishedManufacturingOrderRequisitions;
use Izam\StockRequest\Exceptions\RequestedProductNotFoundInSourceRequest;
use Izam\ManufacturingOrder\Repositories\ManufacturingOrderRepository;

class ManufacturingOrderService
{
    private $dynamicRepo;
    private $manufactureOrderRepository;
    public function __construct(ManufacturingOrderRepository $manufactureOrderRepository) {
        $this->dynamicRepo = resolve(DynamicRepo::class);
        $this->manufactureOrderRepository = $manufactureOrderRepository;
    }

    public function getManufacturingOrderRequisitions($manufacturingOrderId, $requisitionTypes):array {
        return $this->dynamicRepo->findBY(
            'requisitions',
            [
                'order_id' => $manufacturingOrderId,
            ],
            [],
            [
                [
                    'column'=> 'order_type',
                    'values' => $requisitionTypes
                ],
            ]
        );
    }

    public function getAllRequisitionsItems(array $requisitions){
        $requisitionsIds = array_map(function($requisition){
            if($requisition['status'] != RequisitionUtil::STATUS_CANCELLED){
                return $requisition['id'];
            }
        }, $requisitions);
        return  $this->dynamicRepo->findWhereIn('requisition_items', 'requisition_id', $requisitionsIds);
    }

    private function compareItems($referenceItems, $requisitionsItems):array{
        $referenceItems = $referenceItems->toArray();
        $referenceItemsQtys = [];
        foreach($referenceItems as $referenceItem){
            if(isset($referenceItemsQtys[$referenceItem->product_id])){
                $referenceItemsQtys[$referenceItem->product_id]['requestedQty'] += $referenceItem->quantity;
            }else{
                $referenceItemsQtys[$referenceItem->product_id]  = [
                    'requestedQty' => $referenceItem->quantity,
                    'convertedQty' => 0,
                    'itemData' => $referenceItem
                ];
            }
         
        }

        foreach($requisitionsItems as $requisitionsItem){
            $referenceItemsQtys[$requisitionsItem->product_id]['convertedQty']  += $requisitionsItem->quantity;
        }

        return $referenceItemsQtys;
    }

    public function getRequisitionsCanRefund($manufacturingOrderId){
        $manufacturingOrderOutboundRequisitions = $this->getManufacturingOrderRequisitions(
            $manufacturingOrderId,
            [RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL]
        );
        $manufacturingOrderInboundRequisitions = $this->getManufacturingOrderRequisitions(
            $manufacturingOrderId,
            [RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND]
        );
        $outboundRequisitionsItems = $this->getAllRequisitionsItems($manufacturingOrderOutboundRequisitions);
        $inboundRequisitionsItems = $this->getAllRequisitionsItems($manufacturingOrderInboundRequisitions);
 
        $referenceItemsCompared = $this->compareItems($outboundRequisitionsItems, $inboundRequisitionsItems);
        $resultRequisition = [];
        $resultRequisitionItems = [];
        foreach($referenceItemsCompared as $item){
            $remainingQty = $item['requestedQty'] - $item['convertedQty'];
            if($remainingQty > 0){
                $resultRequisitionItems[] = [
                    "quantity" => 0,
                    "requested_quantity" => $remainingQty,
                    "product_id" => $item['itemData']->product_id,
                    "unit_name" => $item['itemData']->unit_name,
                    "unit_small_name" => $item['itemData']->unit_small_name,
                    "unit_factor" => $item['itemData']->factor,
                    "unit_factor_id" => $item['itemData']->unit_factor_id,
                ];
            }
        }
        //  refers to default store id
        $resultRequisition[] = [
            'Requisition'=>[
                'status'=> 1,
                'type'=> RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
            ],
            'RequisitionItem'=> $resultRequisitionItems
        ];
        return $resultRequisition;
        
    }


        /**
     * @param int $manufacturingOrderId
     * @param array $requestedRequisitionItems
     * @return bool
     * @throws RequestedProductNotFoundInSourceRequest
     */
    public function returnMaterialsRequisitionValidation($manufacturingOrderId, $requestedRequisitionItems , $editMode = false , $requisitionId = null){
        $manufacturingOrderOutboundRequisitions = $this->getManufacturingOrderRequisitions(
            $manufacturingOrderId,
            [RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL]
        );

        $manufacturingOrderInboundRequisitions = $this->getManufacturingOrderRequisitions(
            $manufacturingOrderId,
            [RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND]
        );
        $outboundRequisitionsItems = $this->getAllRequisitionsItems($manufacturingOrderOutboundRequisitions);
        $currentRequisitionsItems = $this->getAllRequisitionsItems($manufacturingOrderInboundRequisitions);
        $newRequisitionsItems = array_merge($currentRequisitionsItems->toArray(), $requestedRequisitionItems);

        $itemsCompared = [];
        foreach($outboundRequisitionsItems as $item){
            if(!array_key_exists($item->product_id, $itemsCompared)){
                $itemsCompared[$item->product_id]  = [
                    'requestedQty' => $item->quantity,
                    'convertedQty' => 0,
                ];
            }else{
                $itemsCompared[$item->product_id]['requestedQty']  += $item->quantity;
            }
           
        }

        foreach($newRequisitionsItems as $requisitionsItem){
            if(is_object($requisitionsItem)){
                $requisitionsItem =  (array)$requisitionsItem;
            }
            if(!array_key_exists($requisitionsItem['product_id'], $itemsCompared)){
                throw new RequestedProductNotFoundInSourceRequest();
            }
            if ($editMode && $requisitionsItem['requisition_id'] == $requisitionId){
               continue;
            }
            $itemsCompared[$requisitionsItem['product_id']]['convertedQty']  += $requisitionsItem['quantity'];
        }

        foreach($itemsCompared as $item){
            if($item['convertedQty'] > $item['requestedQty']){
                return true;
            }
        }
        return false;
    }

      /**
     * @param int $manufacturingOrderId
     * @throws CannotUpdateOrDeleteFinishedManufacturingOrderRequisitions
     */
    public function onDeleteOrUpdateManufacturingOrderRequisitionValidations($manufacturingOrderId){
        $manufacturingOrder = $this->dynamicRepo->findRecord('manufacturing_orders', $manufacturingOrderId);
        if(in_array($manufacturingOrder->status, ['finished', 'closed'])){
            throw new CannotUpdateOrDeleteFinishedManufacturingOrderRequisitions();
        }
    }

    public function updateManufactureOrderData($orderId)
    {
        $manufactureOrderStockTransactions = $this->getManufactureOrderStockTransactions($orderId,true );
        $this->processManufactureOrderTransactions($manufactureOrderStockTransactions , $orderId);
    }

    /**
     * @desc validates manufacture order outbound requisitions
     * @throws CannotSelectNewProduct
     * @throws CannotExceedProductQuantity
     */
    public function validateOutboundMaterialRequisitionData($orderId, $data , $editMode=false)
    {
        $manufacturingOrderMaterial = $this->getAllManufactureOrderMaterials($orderId);
        $groupedMaterials = $this->groupMaterialsByProductId($manufacturingOrderMaterial);
        $requestRequisitionItems = $data['RequisitionItem'];
        $requisitionMaterialIds = array_column($requestRequisitionItems , 'product_id');
        $manufacturingOrderMaterialIds = array_column( $manufacturingOrderMaterial , 'product_id');
        if (count(array_diff($requisitionMaterialIds , $manufacturingOrderMaterialIds)) >= 1){
            throw new CannotSelectNewProduct();
        }

        $manufactureOrderStockTransactions = $this->getManufactureOrderStockTransactions($orderId , true);
        foreach ($groupedMaterials as $material){
            if (!in_array($material['product_id'] , $requisitionMaterialIds)) continue;
            $materialQuantity = $material['quantity'];
            $transactionsTotalQuantity = 0;
            $additionalQuantity = 0;
            $productId = $material['product_id'];
            $materialTransactions = array_filter($manufactureOrderStockTransactions , function ( $transaction ) use ($productId){
                return $transaction['product_id'] == $productId;
            });
            if (!$editMode){
                $requisitionMaterialProductArray = array_filter($requestRequisitionItems , function ( $item ) use ($productId){
                    return $item['product_id'] == $productId;
                });
                $requisitionMaterialProduct = array_pop($requisitionMaterialProductArray);
                $additionalQuantity = $requisitionMaterialProduct['quantity'];
            }
            foreach ($materialTransactions as $transaction){
                $transactionsTotalQuantity += $transaction['quantity'];
            }
            $total = abs($transactionsTotalQuantity) + $additionalQuantity;
            if ($total > $materialQuantity ){
                throw new CannotExceedProductQuantity();
            }
        }
    }

    public function addNewManufactureOrderMaterial($orderId, $data)
    {
        $manufacturingOrderMaterial = $this->getAllManufactureOrderMaterials($orderId);
        $manufacturingOrderMaterialIds = array_column( $manufacturingOrderMaterial , 'product_id');
        $requestRequisitionItems = $data['RequisitionItem'];
        foreach ($requestRequisitionItems as &$item){
            if (!in_array($item['product_id'] , $manufacturingOrderMaterialIds)){
                $item['requested_quantity'] = "0";
            }
        }
        $requisitionMaterialIds = array_column($requestRequisitionItems , 'product_id');
        $toBeAddedProductsIds = array_diff($requisitionMaterialIds , $manufacturingOrderMaterialIds);
        $toBeAddedArray = [];
        foreach ($requestRequisitionItems as &$item){
            if (in_array($item['product_id'] , $toBeAddedProductsIds)){
                $toBeAddedArray[] =$item;
            }
        }
        $this->manufactureOrderRepository->attachManufatureOrderMaterial($orderId , $toBeAddedArray);

    }

    private function getAllManufactureOrderRequisitions($manufactureOrderId , $includeRefunds = false):array {
        $values = [
            RequisitionUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
        ];
        if ($includeRefunds){
            $values[]= RequisitionUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND;
        }
        return $this->dynamicRepo->findBY(
            'requisitions',
            [
                'order_id' => $manufactureOrderId,
            ],
            [],
            [
                [
                    'column'=> 'order_type',
                    'values' =>$values
                ],
            ]
        );
    }

    private function getManufactureOrderStockTransactions($orderId , $includeRefunds=false)
    {
        $requisitionIds = [];
        $transactionTypes = [
            \StockTransaction::SOURCE_RQ_MANUFACTURE_ORDER_MATERIAL_OUTBOUND,
        ];
        if ($includeRefunds){
            $transactionTypes[] = \StockTransaction::SOURCE_RQ_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND;
        }
        $orderRequisitions = $this->getAllManufactureOrderRequisitions($orderId , true);
        foreach ($orderRequisitions as $requisition){
            $requisitionIds[] = $requisition['id'];
        }
        return $this->dynamicRepo->findBY(
            'stock_transactions',
            [],
            [],
            [
                [
                    'column'=> 'order_id',
                    'values' =>$requisitionIds
                ],
                [
                    'column'=> 'source_type',
                    'values' =>$transactionTypes
                ],
            ]
        );
    }

    public function getAllManufactureOrderMaterials($manufactureOrderId ):array {
        return $this->dynamicRepo->findBY(
            'manufacturing_order_materials',
            [
                'manufacturing_order_id' => $manufactureOrderId,
            ],
        );
    }

    public function groupMaterialsByProductId(array $manufacturingOrderMaterial) :array
    {
        $groupedMaterials = [];
        foreach ( $manufacturingOrderMaterial AS $key => $material ) {
            if ( !array_key_exists($material['product_id'], $groupedMaterials) ) {
                $groupedMaterials[$material['product_id']] =$material;
            }else{
                $groupedMaterials[$material['product_id']]['quantity'] += $material['quantity'];
            }
        }
        return  $groupedMaterials;
    }

    /**
     * @desc takes (outbound and refund) material transactions related to a manufacture order, to update actual costs
     * @return void
     */
    private function processManufactureOrderTransactions($manufactureOrderStockTransactions, $orderId)
    {
        $total = 0;
        $transactionsProductsArray = [];
        foreach ($manufactureOrderStockTransactions as $transaction){
            // inbound quantity values are positive, while outbound values are negative
            // here we only care about outbound values.
            $quantity = ($transaction['quantity'] * -1 );
            if (!array_key_exists($transaction['product_id'] , $transactionsProductsArray)){
                $transactionsProductsArray[$transaction['product_id']] = [
                    'actual_quantity'=> $quantity,
                    'actual_price'=>$transaction['price'] * $quantity,
                ];
            }else{
                $transactionsProductsArray[$transaction['product_id']]['actual_quantity'] += $quantity;
                $transactionsProductsArray[$transaction['product_id']]['actual_price'] += $transaction['price'] * $quantity;

            }
            $total += ($transaction['price'] * $quantity);
        }
        $this->manufactureOrderRepository->updateMaterialData($orderId , $transactionsProductsArray);
        $this->manufactureOrderRepository->update($orderId , ['materials_actual_total_cost'=>abs($total)]);
    }

    public function updateManufacturingOrdersTotalIndirectCosts($indirectCostData){
        $manufacturingOrdersIds = [];
        foreach($indirectCostData->distributions as $distribution){
            $manufacturingOrdersIds[] = $distribution->manufacturing_order_id;
        }
        
        $this->manufactureOrderRepository->updateManufacturingOrdersTotalIndirectCosts($manufacturingOrdersIds);
    }

    public function prepareInboundRequistionsNewData($indirectCostData){
        $manufacturingOrdersIds = [];
        foreach($indirectCostData->distributions as $distribution){
            $manufacturingOrdersIds[] = $distribution->manufacturing_order_id;
        }
        $results = [];
        $orders =$this->manufactureOrderRepository->whereIn('id' , $manufacturingOrdersIds);
        foreach ($orders as $order){
            $results[$order['id']]=[
                "RequisitionItem"=>[
                    "newAmount"=>( $order['total_cost'] - $order['materials_total_cost'] + $order['materials_actual_total_cost'] + ($order['total_indirect_costs'] ?? 0) ) / $order['quantity']
                ]
            ];
        }
        return $results;
    }
    public function update($id, $data) {
        return $this->manufactureOrderRepository->update($id, $data);        
    }

    public function isManufacturingOrderFinishedOrClosed($manufacturingOrderData){
        if(in_array($manufacturingOrderData['status'], [ManufacturingOrderStatusUtil::FINISHED, ManufacturingOrderStatusUtil::CLOSED])){
            return true;
        }
        return false;
    }

    public function getManufacturingOrderRelatedRequisitions($manufacturingOrderId){
        return $this->dynamicRepo->findBY(
            'requisitions',
            [
                'order_id' => $manufacturingOrderId,
            ],
            [],
            [
                [
                    'column'=> 'order_type',
                    'values' => [
                        RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
                        RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
                        RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP,
                        RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT
                    ]
                ],
            ]
        );
    }

    public function getStaffOrderIds($staffUserId)
    {
        return $this->manufactureOrderRepository->getStaffOrderIds($staffUserId);
    }

    public function getManufacturingOrderRelatedStockRequest($manufacturingOrderId){
        return $this->dynamicRepo->findBY(
            'stock_request_relations',
            [],
            [],
            [
                [
                    'column'=> 'related_model',
                    'values' => [\app\Models\ManufacturingOrder::class]
                ],[
                    'column'=> 'related_model_id',
                    'values' => [$manufacturingOrderId]
                ],
            ]
        );
    }


    public function getInProgressManufacturingOrders($query = null)
    {
        return $this->manufactureOrderRepository->searchManufacturingOrder( $query);
    }

    public function updateAssetDeprAccForManufacturing($assetId) {
        return $this->manufactureOrderRepository->updateAssetDeprAccForManufacturing($assetId);
    }

    /**
     * Updates manufacturing order materials with current average prices
     * 
     * @param int $orderId The manufacturing order ID
     * @return bool Success status
     */
    public function updateManufacturingOrderMaterialsAveragePrices($orderId)
    {
        $this->manufactureOrderRepository->transaction(function () use ($orderId) {
            $this->updateManufactureOrderData($orderId);
            $appEntityShowAction = resolve(AppEntityShowAction::class);
            $manufacturingOrderEntity = $appEntityShowAction->handle(EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY, $orderId, 2);
            resolve(HandleManufacturingOrderUpdated::class)->run($manufacturingOrderEntity);
//            dispatch_event_action(new ManufacturingOrderUpdated($manufacturingOrderEntity));
        });
        return true;

    }


}
