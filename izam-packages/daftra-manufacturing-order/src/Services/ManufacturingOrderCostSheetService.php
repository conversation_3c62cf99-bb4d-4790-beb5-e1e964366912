<?php

namespace Izam\ManufacturingOrder\Services;

use Izam\Daftra\Common\Utils\ManufacturingOrderCostSheetRecordTypeUtil;
use Izam\Daftra\Common\Utils\RequisitionOrderTypeUtil;
use Izam\Database\Capsule\Manager as DB;
use Izam\Entity\Repository\DynamicRepo;
use Izam\ManufacturingOrder\Repositories\ManufacturingOrderCostSheetRepository;

class ManufacturingOrderCostSheetService
{
    private $dynamicRepo;
    private $repository;
    public function __construct(ManufacturingOrderCostSheetRepository $repository) {
        $this->dynamicRepo = resolve(DynamicRepo::class);
        $this->repository = $repository;
    }

    // START handle sheet costs on create/update manufacturing order
    public function saveCostsOnCreateManufacturingOrder($manufacturingOrderData){
        $this->saveDirectCosts($manufacturingOrderData);
        $this->saveManufacturingOperationCosts($manufacturingOrderData);
        $this->saveEstimatedProductsSubformsCosts($manufacturingOrderData);
        $this->saveInboundScrapsCosts($manufacturingOrderData);
    }

    public function updateCostsOnUpdateManufacturingOrder($manufacturingOrderData){
        $this->repository->deleteOldManufacturingOrderCosts($manufacturingOrderData);
        $this->saveDirectCosts($manufacturingOrderData);
        $this->saveManufacturingOperationCosts($manufacturingOrderData);
        $this->saveInboundScrapsCosts($manufacturingOrderData);
    }

    private function saveDirectCosts($manufacturingOrderData){
        $costSheetData = [];
        $estimatedExpensesAmount = [];
        $expensesBomExpensesIds = [];
        foreach($manufacturingOrderData->estimated_expenses as $estimated_expense){
            $estimatedExpensesAmount[$estimated_expense->bom_expenses_id] = $estimated_expense->amount;
        }
        foreach($manufacturingOrderData->expenses as $expense){
            if($expense->bom_expenses_id){
                $expensesBomExpensesIds[] = $expense->bom_expenses_id;
            }
            $costSheetData[] = [
                'manufacturing_order_id' => $manufacturingOrderData->id,
                'branch_id' => $manufacturingOrderData->branch_id,
                'journal_account_id' => $expense->journal_account_id,
                'number' => '',
                'sub_production_routing_id' => $expense->sub_production_routing_id,
                'description' => $expense->description ?? '',
                'record_type' => ManufacturingOrderCostSheetRecordTypeUtil::DIRECT_COST,
                'entity_type' => 'manufacturing_order_expenses',
                'entity_id' => $expense->id,
                'transaction_type' => 'credit',
                'estimated_amount' => $estimatedExpensesAmount[$expense->bom_expenses_id] ?? 0,
                'actual_amount' => $expense->amount,
            ];
        }

        foreach($manufacturingOrderData->estimated_expenses as $estimatedExpense){
            if(!in_array($estimatedExpense->bom_expenses_id, $expensesBomExpensesIds)){
                $costSheetData[] = [
                    'manufacturing_order_id' => $manufacturingOrderData->id,
                    'branch_id' => $manufacturingOrderData->branch_id,
                    'journal_account_id' => $estimatedExpense->journal_account_id,
                    'number' => '',
                    'sub_production_routing_id' => $estimatedExpense->sub_production_routing_id,
                    'description' => $estimatedExpense->description ?? '',
                    'record_type' => ManufacturingOrderCostSheetRecordTypeUtil::DIRECT_COST,
                    'entity_type' => 'manufacturing_order_expenses',
                    'entity_id' => $estimatedExpense->id,
                    'transaction_type' => 'credit',
                    'estimated_amount' => $estimatedExpense->sub_total_cost,
                    'actual_amount' => 0,
                ];
            }
        }

        if($costSheetData){
            $this->repository->bulkInsert($costSheetData);
        }
    }

    private function saveManufacturingOperationCosts($manufacturingOrderData){
        $costSheetData = [];
        $estimatedOperationWorkstaion = [];
        $operationsBomOperationsIds = [];
        $estimatedOperationOperatingTime = [];
        foreach($manufacturingOrderData->estimated_operations as $estimated_operation){
            $estimatedOperationWorkstaion[$estimated_operation->bom_operations_id] = json_decode($estimated_operation->workstation_data?? []);
            $estimatedOperationOperatingTime[$estimated_operation->bom_operations_id] = $estimated_operation->operating_time;
        }
        $estimatedAmountFactor = 1;
        if($manufacturingOrderData->bom){
            $estimatedAmountFactor = $manufacturingOrderData->quantity/ $manufacturingOrderData->bom->quantity;
        }
        foreach($manufacturingOrderData->operations as $operation){
            $operationsBomOperationsIds[] = $operation->bom_operations_id;

            $workstationData = '';
            if($operation->workstation_data){
                $workstationData = json_decode($operation->workstation_data);
            }

            if($workstationData->asset_cost){
                $costSheetData[] = [
                    'manufacturing_order_id' => $manufacturingOrderData->id,
                    'branch_id' => $manufacturingOrderData->branch_id,
                    'journal_account_id' => $workstationData->asset_derpreciation_expense_account->id ?? null,
                    'number' => $operation->workstation->code,
                    'sub_production_routing_id' => $operation->sub_production_routing_id,
                    'description' => ($operation->description ?? '').' '.($workstationData->asset->name ?? '').' #'.($workstationData->asset->code ?? ''),
                    'record_type' => 'manufacturing_operation',
                    'entity_type' => 'workstation_asset',
                    'entity_id' => $operation->workstation->id,
                    'transaction_type' => 'credit',
                    'estimated_amount' => ($estimatedOperationWorkstaion[$operation->bom_operations_id]->asset_cost ?? 0) * 
                    ($estimatedOperationOperatingTime[$operation->bom_operations_id] ?? 0) * $estimatedAmountFactor,
                    'actual_amount' => $workstationData->asset_cost * $operation->operating_time,
                ];
            }

            if($workstationData->wages_cost){
                $costSheetData[] = [
                    'manufacturing_order_id' => $manufacturingOrderData->id,
                    'branch_id' => $manufacturingOrderData->branch_id,
                    'journal_account_id' => $workstationData->wages_account->id,
                    'number' => $operation->workstation->code,
                    'sub_production_routing_id' => $operation->sub_production_routing_id,
                    'description' => ($operation->description ?? '').' '.($workstationData->wages_account->name ?? '').' #'.($workstationData->wages_account->code ?? ''),
                    'record_type' => 'manufacturing_operation',
                    'entity_type' => 'workstation_wage',
                    'entity_id' => $operation->workstation->id,
                    'transaction_type' => 'credit',
                    'estimated_amount' => ($estimatedOperationWorkstaion[$operation->bom_operations_id]->wages_cost ?? 0) *
                        ($estimatedOperationOperatingTime[$operation->bom_operations_id] ?? 0)* $estimatedAmountFactor,
                    'actual_amount' => $workstationData->wages_cost * $operation->operating_time,
                ];
            }

            foreach($workstationData->other_expense as $i => $workstationOtherExpense){
                $costSheetData[] = [
                    'manufacturing_order_id' => $manufacturingOrderData->id,
                    'branch_id' => $manufacturingOrderData->branch_id,
                    'journal_account_id' => $workstationOtherExpense->account->id,
                    'number' => $workstationOtherExpense->account->code,
                    'sub_production_routing_id' => $operation->sub_production_routing_id,
                    'description' =>  ($operation->description ?? '').' '.($workstationOtherExpense->account->name ?? '').' #'.($workstationOtherExpense->account->code ?? ''),
                    'record_type' => 'manufacturing_operation',
                    'entity_type' => 'workstation_other_expense',
                    'entity_id' => $operation->id,
                    'transaction_type' => 'credit',
                    'estimated_amount' => ($estimatedOperationWorkstaion[$operation->bom_operations_id]->other_expense[$i]->amount ?? 0) *
                        ($estimatedOperationOperatingTime[$operation->bom_operations_id] ?? 0) * $estimatedAmountFactor,
                    'actual_amount' => $workstationOtherExpense->amount * $operation->operating_time,
                ];
            }
        }

        foreach($manufacturingOrderData->estimated_operations as $operation){
            if(!in_array($operation->bom_operations_id, $operationsBomOperationsIds)){
                $workstationData = '';
                if($operation->workstation_data){
                    $workstationData = json_decode($operation->workstation_data);
                }
                // add workstation asset cost
                if($workstationData->asset_cost){
                    $costSheetData[] = [
                        'manufacturing_order_id' => $manufacturingOrderData->id,
                        'branch_id' => $manufacturingOrderData->branch_id,
                        'journal_account_id' => $workstationData->asset_derpreciation_expense_account->id ?? null,
                        'number' => $operation->workstation->code,
                        'sub_production_routing_id' => $operation->sub_production_routing_id,
                        'description' => ($operation->description ?? '').' '.($workstationData->asset->name ?? '').' #'.($workstationData->asset->code ?? ''),
                        'record_type' => 'manufacturing_operation',
                        'entity_type' => 'workstation_asset',
                        'entity_id' => $operation->workstation->id,
                        'transaction_type' => 'credit',
                        'estimated_amount' => $workstationData->asset_cost *
                        ($estimatedOperationOperatingTime[$operation->bom_operations_id] ?? 0) * $estimatedAmountFactor,
                        'actual_amount' => 0,
                    ];
                }

                // add workstation wage cost
                if($workstationData->wages_cost){
                    $costSheetData[] = [
                        'manufacturing_order_id' => $manufacturingOrderData->id,
                        'branch_id' => $manufacturingOrderData->branch_id,
                        'journal_account_id' => $workstationData->wages_account->id,
                        'number' => $operation->workstation->code,
                        'sub_production_routing_id' => $operation->sub_production_routing_id,
                        'description' => ($operation->description ?? '').' '.($workstationData->wages_account->name ?? '').' #'.($workstationData->wages_account->code ?? ''),
                        'record_type' => 'manufacturing_operation',
                        'entity_type' => 'workstation_wage',
                        'entity_id' => $operation->workstation->id,
                        'transaction_type' => 'credit',
                        'estimated_amount' => $workstationData->wages_cost *
                        ($estimatedOperationOperatingTime[$operation->bom_operations_id] ?? 0) * $estimatedAmountFactor,
                        'actual_amount' => 0,
                    ];
                }

                // add workstation other expense costs
                foreach($workstationData->other_expense as $workstationOtherExpense){
                    $costSheetData[] = [
                        'manufacturing_order_id' => $manufacturingOrderData->id,
                        'branch_id' => $manufacturingOrderData->branch_id,
                        'journal_account_id' => $workstationOtherExpense->account->id,
                        'number' => $workstationOtherExpense->account->code,
                        'sub_production_routing_id' => $operation->sub_production_routing_id,
                        'description' =>  ($operation->description ?? '').' '.($workstationOtherExpense->account->name ?? '').' #'.($workstationOtherExpense->account->code ?? ''),
                        'record_type' => 'manufacturing_operation',
                        'entity_type' => 'workstation_other_expense',
                        'entity_id' => $operation->id,
                        'transaction_type' => 'credit',
                        'estimated_amount' => $workstationOtherExpense->amount *
                        ($estimatedOperationOperatingTime[$operation->bom_operations_id] ?? 0) * $estimatedAmountFactor,
                        'actual_amount' => 0,
                    ];
                }
            }
        }
        if($costSheetData){
            $this->repository->bulkInsert($costSheetData);
        }
    }

    private function saveEstimatedProductsSubformsCosts($manufacturingOrderData){
        $costSheetData = [];
        foreach(['estimated_materials', 'estimated_scraps'] as $subformKey){
            $subform = $manufacturingOrderData->{$subformKey};
            if($subform){
                $recordType = match($subformKey){
                    'estimated_materials' => ManufacturingOrderCostSheetRecordTypeUtil::OUTBOUND_MATERIAL,
                    'estimated_scraps' => ManufacturingOrderCostSheetRecordTypeUtil::INBOUND_SCRAP_ITEM,
                };
                $transactionType = match($subformKey){
                    'estimated_materials' => 'credit',
                    'estimated_scraps' => 'debit',
                };
                foreach($subform as $subformRecord){
                        $costSheetData[] = [
                            'manufacturing_order_id' => $manufacturingOrderData->id,
                            'branch_id' => $manufacturingOrderData->branch_id,
                            'journal_account_id' => null,
                            'number' => $subformRecord->product->product_code ?? '',
                            'sub_production_routing_id' => $subformRecord->sub_production_routing_id,
                            'description' => ($subformRecord->product->name ?? '') .' #'. ($subformRecord->product->product_code ?? ''),
                            'record_type' => $recordType,
                            'entity_type' => 'product',
                            'entity_id' => $subformRecord->product_id,
                            'transaction_type' => $transactionType,
                            'estimated_amount' => $subformRecord->sub_total_cost,
                            'actual_amount' => 0,
                        ];
                }
            }
        }

        if($costSheetData){
            $this->repository->bulkInsert($costSheetData);
        }

    }

    private function saveInboundScrapsCosts($manufacturingOrderData){
        $costSheetData = [];
        $subform = $manufacturingOrderData->scraps;
        if($subform){
            $recordType = ManufacturingOrderCostSheetRecordTypeUtil::INBOUND_SCRAP_ITEM;
            $transactionType = 'debit';
            foreach($subform as $subformRecord){
                if($subformRecord->bom_scraps_id == null){
                    $costSheetData[] = [
                        'manufacturing_order_id' => $manufacturingOrderData->id,
                        'branch_id' => $manufacturingOrderData->branch_id,
                        'journal_account_id' => null,
                        'number' => $subformRecord->product->product_code ?? '',
                        'sub_production_routing_id' => $subformRecord->sub_production_routing_id,
                        'description' => ($subformRecord->product->name ?? '') .' #'. ($subformRecord->product->product_code ?? ''),
                        'record_type' => $recordType,
                        'entity_type' => 'product',
                        'entity_id' => $subformRecord->product_id,
                        'transaction_type' => $transactionType,
                        'estimated_amount' => 0,
                        'actual_amount' => $subformRecord->sub_total_cost,
                    ];
                }
            }
        }

        if($costSheetData){
            $this->repository->bulkInsert($costSheetData);
        }

    }
    // END handle sheet costs on create/update manufacturing order


    // START handle sheet cost on requisition related to manufacturing order created / updated / deleted
    public function saveMaterialsRequistionCosts($requisitionData, $requisitionOrderType){
        $manufacturingOrderCostSheetRecordType = [
            RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL => ManufacturingOrderCostSheetRecordTypeUtil::OUTBOUND_MATERIAL,
            RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND => ManufacturingOrderCostSheetRecordTypeUtil::INBOUND_MATERIAL,
            RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP => ManufacturingOrderCostSheetRecordTypeUtil::INBOUND_SCRAP_ITEM,
        ];
        $manufacturingOrderCostSheetRecordType = $manufacturingOrderCostSheetRecordType[$requisitionOrderType];

        $requisitionProductsIds = array_map(
            function($item){
                return $item['product_id'];
            },
            $requisitionData['RequisitionItem']
        );
        $this->repository->model()::where('manufacturing_order_id', $requisitionData['Requisition']['order_id'])
        ->where('record_type', $manufacturingOrderCostSheetRecordType)
        ->whereIn('entity_id', $requisitionProductsIds)
        ->delete();

        $this->createMaterialsRequistionCosts($requisitionData, $manufacturingOrderCostSheetRecordType, $requisitionOrderType);
    }

    private function createMaterialsRequistionCosts($requisitionData, $manufacturingOrderCostSheetRecordType, $requisitionOrderType){
        $costSheetData = [];

        // get manufacturing order details
        $manufacturingOrderId = $requisitionData['Requisition']['order_id'];
        $manufacturingOrder = $this->dynamicRepo->findBY('manufacturing_orders', ['id'=> $manufacturingOrderId]);

        // get all products from the created requsition items
        $requisitionProductsIds = array_map(
            function($item)use(&$productsData){
                return $item['product_id'];
            },
            $requisitionData['RequisitionItem']
        );
        if($requisitionProductsIds){
            // get total actual price for each product, by sum of all prices from all manufacturing order related requisitions
            $materialsRequisitionData = $this->getMaterialsRequisitionDataGroupedByProduct(
                $manufacturingOrderId,
                $requisitionOrderType,
                $requisitionProductsIds
            );
            if($materialsRequisitionData){
                $transactionType = match($manufacturingOrderCostSheetRecordType){
                    ManufacturingOrderCostSheetRecordTypeUtil::OUTBOUND_MATERIAL => 'credit',
                    ManufacturingOrderCostSheetRecordTypeUtil::INBOUND_MATERIAL, ManufacturingOrderCostSheetRecordTypeUtil::INBOUND_SCRAP_ITEM => 'debit'
                };
                // prepare cost sheet data
                foreach($materialsRequisitionData as $materialRequisitionData){
                    $estimatedAmount = null;
                    if($manufacturingOrderCostSheetRecordType == ManufacturingOrderCostSheetRecordTypeUtil::INBOUND_MATERIAL){
                        $estimatedAmount = 0;
                    }else{
                        $estimatedAmount = $materialRequisitionData->total_actual_estimated_amount;
                    }
                    $costSheetData[] = [
                        'manufacturing_order_id' => $manufacturingOrderId,
                        'branch_id' => $manufacturingOrder['branch_id'],
                        'journal_account_id' => $manufacturingOrder['journal_account_id'],
                        'number' => $materialRequisitionData->product_code ?? '',
                        'sub_production_routing_id' => $materialRequisitionData->sub_production_routing_id,
                        'description' => ($materialRequisitionData->product_name ?? '') . ' #'.($materialRequisitionData->product_code ?? ''),
                        'record_type' => $manufacturingOrderCostSheetRecordType,
                        'entity_type' => 'product',
                        'entity_id' => $materialRequisitionData->product_id,
                        'transaction_type' => $transactionType,
                        'estimated_amount' => $estimatedAmount,
                        'actual_amount' => $materialRequisitionData->total_actual_amount,
                    ];
                }
            }
        }

        if($costSheetData){
            $this->repository->bulkInsert($costSheetData);
        }
    }

    private function getMaterialsRequisitionDataGroupedByProduct($manufacturingOrderId, $requisitionsType, $materialProductsIds){
        $results = [];

        $relatedRequisitions = $this->dynamicRepo->findBY(
            'requisitions',
            [
                'order_id' => $manufacturingOrderId,
                'order_type' => $requisitionsType
            ]
        );

        $estimatedRecordTable = '';
        $estimatedRecordTableJoinColumn = '';
        $mainRecordTable = '';
        $mainRecordTableJoinEstimatedColumn = '';
        if(in_array($requisitionsType, [ RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
        RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND])){
            $estimatedRecordTable = 'manufacturing_order_estimated_materials';
            $estimatedRecordTableJoinColumn = 'bom_materials_id';
            $mainRecordTable = 'manufacturing_order_materials';
            $mainRecordTableJoinEstimatedColumn = 'bom_materials_id';
        }elseif($requisitionsType == RequisitionOrderTypeUtil::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP){
            $estimatedRecordTable = 'manufacturing_order_estimated_scraps';
            $estimatedRecordTableJoinColumn = 'bom_scraps_id';
            $mainRecordTable = 'manufacturing_order_scraps';
            $mainRecordTableJoinEstimatedColumn = 'bom_scraps_id';
        }

        if($relatedRequisitions){
            $relatedRequisitionsIds =  array_map(
                function($item){
                    return $item['id'];
                },
                $relatedRequisitions
            );
            $q = DB::select(
                '
                    SELECT
                        req_items.product_id product_id,
                        products.name product_name,
                        products.product_code product_code,
                        sum(req_items.subtotal) total_actual_amount,
                        '.$mainRecordTable.'.sub_production_routing_id sub_production_routing_id,
                        '.$estimatedRecordTable.'.sub_total_cost total_actual_estimated_amount
                    FROM
                        requisition_items req_items
                    JOIN
                        products ON products.id = req_items.product_id
                    JOIN
                        '.$mainRecordTable.' ON '.$mainRecordTable.'.product_id = req_items.product_id
                    LEFT JOIN
                        '.$estimatedRecordTable.'
                        ON ('.$mainRecordTable.'.manufacturing_order_id = '.$estimatedRecordTable.'.manufacturing_order_id AND '.$mainRecordTable.'.'.$mainRecordTableJoinEstimatedColumn.' = '.$estimatedRecordTable.'.'.$estimatedRecordTableJoinColumn.')
                    WHERE
                        '.$mainRecordTable.'.manufacturing_order_id = '.$manufacturingOrderId.'
                        AND req_items.requisition_id IN ('.implode(",", $relatedRequisitionsIds).')
                        AND req_items.product_id IN ('.implode(',', $materialProductsIds).')
                    GROUP BY
                        req_items.product_id;
                '
            );
            foreach($q as $product){
                $results[$product->product_id] = $product;
            }
        }

        return $results;
    }
    // END handle sheet cost on requisition related to manufacturing order created / updated / deleted


    // START handle sheet costs on create/update/delete manufacturing order indirect cost
    public function saveCostsOnUpdateManufacturingOrderIndirectCost($indirectCostData){
        $this->deleteCostsOnDeleteManufacturingOrderIndirectCost($indirectCostData);
        $this->saveCostsOnCreateManufacturingOrderIndirectCost($indirectCostData);
    }

    public function deleteCostsOnDeleteManufacturingOrderIndirectCost($indirectCostData){
        $this->repository->model()::where('record_type', ManufacturingOrderCostSheetRecordTypeUtil::INDIRECT_COST)
        ->where('entity_id', $indirectCostData->id)
        ->delete();
    }

    public function saveCostsOnCreateManufacturingOrderIndirectCost($indirectCostData){
        $costSheetData = [];
        $journals_total = $indirectCostData->journals_total;

        foreach($indirectCostData->transactions as $transaction){
            $journalTransaction = $transaction->journal_transaction;
            $account = $journalTransaction?->journal_account;
            $journal = $transaction->journal_transaction?->journal;
            $dateFormat = getDateFormats('std')[getCurrentSite('date_format')];
            $journalDate = date($dateFormat, strtotime($journal->date));
            $journalDebit = format_price($journalTransaction->currency_debit, $journalTransaction->currency_code);
            $description = "({$account?->name} #{$account?->code}) - {$journalTransaction?->description} - Journal #{$journal->number} - {$journalDate} - ({$journalDebit}) ";
            $transactionAmount = $journalTransaction->currency_debit;
            foreach($indirectCostData->distributions as $distribution){
                $distributionPercentage =  $distribution->amount / $journals_total;
                $distributionAmount = $distributionPercentage * $transactionAmount;
                $manufactureOrder = $distribution->manufacture_order;
                $costSheetData[] = [
                    'manufacturing_order_id' => $manufactureOrder->id,
                    'branch_id' => $manufactureOrder->branch_id,
                    'journal_account_id' => $account->id,
                    'number' => $journal->number,
                    'sub_production_routing_id' => null,
                    'description' => $description,
                    'record_type' => ManufacturingOrderCostSheetRecordTypeUtil::INDIRECT_COST,
                    'entity_type' => 'manufacturing_order_indirect_costs',
                    'entity_id' => $indirectCostData->id,
                    'transaction_type' => 'credit',
                    'estimated_amount' => null,
                    'actual_amount' => $distributionAmount,
                ];
            }
        }

        if($costSheetData){
            $this->repository->bulkInsert($costSheetData);
        }

    }
    // END handle sheet costs on create/update/delete manufacturing order indirect cost

}
