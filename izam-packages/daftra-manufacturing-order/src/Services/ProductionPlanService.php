<?php

namespace Izam\ManufacturingOrder\Services;

use Izam\ManufacturingOrder\Repositories\ProductionPlanRepository;

class ProductionPlanService
{
    public function __construct(protected ProductionPlanRepository $repo)
    {
    }

    public function updateManufacturingOrderProductionPlan($manufacturingOrderData): void
    {
        $item = $this->repo->getProudctionPlanItemRelatedToManufacturingOrder($manufacturingOrderData->id);
        if ($item) {
            $this->repo->updateProductionPlanItem($item->id, $manufacturingOrderData->quantity);
        }
    }
}
