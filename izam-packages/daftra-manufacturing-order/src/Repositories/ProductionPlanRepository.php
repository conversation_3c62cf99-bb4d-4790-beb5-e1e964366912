<?php

namespace Izam\ManufacturingOrder\Repositories;

use Izam\Database\Capsule\Manager as DB;

class ProductionPlanRepository
{
    public function getProudctionPlanItemRelatedToManufacturingOrder($manufacturingOrderId)
    {
        return DB::table('production_plan_items')
            ->where('manufacturing_order_id', $manufacturingOrderId)
            ->first();
    }

    public function updateProductionPlanItem($productionPlanItemId, $quantity)
    {
        return DB::table('production_plan_items')
            ->where('id', $productionPlanItemId)
            ->update([
                'quantity' => $quantity,
            ]);
    }
}
