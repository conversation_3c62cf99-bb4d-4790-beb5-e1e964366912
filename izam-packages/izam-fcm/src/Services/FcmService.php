<?php
namespace Izam\Fcm\Services;

use App\Repositories\Criteria\EqualCriteria;
use App\Repositories\DeviceAppNotificationRepository;
use App\Strategies\EntityNotifications\NotificationMessageDto;
use App\Utils\FcmDevicesAppUtil;
use Izam\Database\Capsule\Manager as DB;
use Google\Auth\Credentials\ServiceAccountCredentials;
use Throwable;

/**
 * Class FcmService
 *
 * Responsible for sending mobile push notifications through Firebase Cloud Messaging (FCM).
 * Handles token authentication, message formatting, and device token cleanup on delivery failure.
 */
class FcmService
{
    public function __construct() {
    }

    /**
     * Sends a list of push notifications to devices.
     *
     * @param NotificationMessageDto[] $notificationDataList List of messages mapped by tokens.
     * @param string $source The source of the notification (used for cleanup logic, e.g., 'leave_application').
     * @return void
     */
    public function mobilePushNotification($notificationDataList, $source){
        try{
            $accessToken = $this->getAccessToken();
            foreach($notificationDataList as $notificationData){
                $data = [
                    'message'=> $notificationData->jsonSerialize()
                ];
                $this->sendPushNotification($data, $accessToken, 1, $source);
            }
        } catch (Throwable $exception) { 
            \Rollbar\Rollbar::warning('exception in push mobile notification', ['message' => $exception->getMessage()]);
        }
    }

    /**
     * Sends a single push notification using cURL to the FCM endpoint.
     *
     * @param array $postBody The message payload to send.
     * @param string $accessToken OAuth2 access token for Firebase.
     * @param int $trial Number of retries attempted (default: 1).
     * @param string $source The source of the message (used for device cleanup).
     * @return bool True if notification was sent successfully, otherwise false.
     */
    private function sendPushNotification($postBody, $accessToken, $trial = 1, $source = '') {
        try{
            $url = 'https://fcm.googleapis.com/v1/projects/'.FIREBASE_PROJECT_NAME.'/messages:send';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization:Bearer ' . $accessToken,
                'Content-Type:application/json'
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postBody));
            $result = curl_exec($ch);
            $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            if($http_status != 200){
                if($http_status == 404){
                    $errorObject = json_decode($result);
                    $this->handleUnregisteredDeviceToken($errorObject, $source, $postBody['message']['token']);
                }
                \Rollbar\Rollbar::warning('error in push mobile notification', ['message' => $result]);
            }
            curl_close($ch);
            return $http_status == 200;
        } catch (Throwable $exception) {
            // dd($exception);
            \Rollbar\Rollbar::warning('exception in push mobile notification', ['message' => $exception->getMessage()]);
            return false;
        }
      
    }
    
    /**
     * Retrieves the OAuth2 access token using the Firebase service account credentials.
     *
     * @return string The valid access token.
     */
    private function getAccessToken(): string
    {
        $scopes = ['https://www.googleapis.com/auth/firebase.messaging'];
        $credentials = new ServiceAccountCredentials($scopes, FIREBASE_PRIVATE_KEY_PATH);        
        $authToken = $credentials->fetchAuthToken();

        return $authToken['access_token'];
    }

    /**
     * Deletes device records for unregistered tokens (e.g., deleted app).
     *
     * This prevents the system from repeatedly trying to send to invalid device tokens.
     *
     * @param object $errorObject Parsed error response from FCM.
     * @param string $source The source of the notification.
     * @param string $token The FCM push token that failed.
     * @return void
     */
    private function handleUnregisteredDeviceToken($errorObject, $source, $token){
        try{
            if($errorObject->error->details[0]->errorCode == "UNREGISTERED"){
                if($source == 'leave_application'){
                    /** @var DeviceAppNotificationRepository */
                    $deviceAppNotificationRepository = resolve(DeviceAppNotificationRepository::class);
                    $deviceAppNotificationRepository->pushCriteria(new EqualCriteria('app_key', FcmDevicesAppUtil::LEAVE_APP))
                        ->pushCriteria(new EqualCriteria('push_token', $token))
                        ->deleteMany();
                }
            }
        } catch (Throwable $exception) {
            \Rollbar\Rollbar::warning('exception in push mobile notification', ['message' => $exception->getMessage()]);
        }
    }

}
