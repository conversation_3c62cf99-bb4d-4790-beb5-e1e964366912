<?php
function get_diagnostics()
{
    $branch_id = $_SESSION['branch_id'];
    return [
        "invoices" => [
            "title" => "sales invoices",
            "diagnostics" => [
                "invoices_with_wrong_summary_total" => [
                    "title" => "invoices that summary total != summary paid + summary unpaid",
                    "check_query" => "SELECT id, summary_paid, summary_unpaid, summary_total FROM `invoices` where payment_status = 2 and abs(summary_paid + summary_unpaid - summary_total) > 0.00000001;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoice_totals",
                    "report" => [
                        "query" => "SELECT id, summary_paid, summary_unpaid, summary_total,date FROM `invoices` where payment_status != 2 and abs(summary_paid + summary_unpaid - summary_total) > 0.00000001;",
                        "fields" => [
                            "id",
                            "date",
                        ],
                        'view_link' => '/owner/invoices/view/%id%'
                    ]
                ],
                "staff_pos_sessions_created_from_unpermitted_branch" => [
                    "title" => "Staff Point-of-Sale Sessions Created from an Unpermitted Branch",
                    "check_query" => "SELECT *
                            FROM pos_shifts
                            WHERE 1 = 1
                              AND pos_shifts.branch_id NOT in (
                                SELECT item_staffs.item_id
                                FROM item_staffs
                                WHERE item_staffs.item_type = 13
                                  AND item_staffs.staff_id = pos_shifts.staff_id
                            )
                                 AND pos_shifts.staff_id != 0
                    ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/staff_pos_sessions_created_from_unpermitted_branch",
                    "report" => [
                        "query" => " 
                        SELECT pos_shifts.id,
                                   (
                                       SELECT group_concat(item_staffs.item_id)
                                       FROM item_staffs
                                       WHERE item_staffs.item_type = 13
                                         AND item_staffs.staff_id = pos_shifts.staff_id) as staff_permitted_branches,
                                   pos_shifts.staff_id,
                                   pos_shifts.branch_id,
                                   pos_shifts.created
                            FROM pos_shifts
                            WHERE 1 = 1
                              AND pos_shifts.branch_id NOT IN (
                                SELECT item_staffs.item_id
                                FROM item_staffs
                                WHERE item_staffs.item_type = 13
                                  AND item_staffs.staff_id = pos_shifts.staff_id
                            )
                                AND pos_shifts.staff_id != 0
                            
                            ",
                        "fields" => [
                            "id",
                            "staff_id",
                            "branch_id",
                            "staff_permitted_branches",
                            "created",
                        ],
                        'view_link' => '/owner/pos_shifts/view/%id%'
                    ]
                ],
                "invoice_refund_payments_with_positive_amount" => [
                    "title" => "invoice refund payments with positive amount",
                    "check_query" => "select * from invoice_payments where invoice_id in (select id from invoices where type = 6) and amount > 0;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_refund_receipt_invoice_payments",
                    "report" => [
                        "query" => "select * from invoice_payments where invoice_id in (select id from invoices where type = 6) and amount > 0",
                        "fields" => [
                            "id",
                            "date",
                            "no",
                        ],
                        'view_link' => '/owner/invoice_payments/view/%id%'
                    ]
                ],
                "fix_invoices_without_sales_cost_journal" => [
                    "title" => "Invoices without sales cost journal",
                    "check_query" => "SELECT invoices.id FROM invoices
                                        WHERE invoices.type = 0
                                        AND invoices.id NOT IN (SELECT entity_id FROM journals WHERE entity_type = 'invoice_sales_cost')
                                        AND invoices.id IN (SELECT invoice_items.invoice_id FROM invoice_items JOIN products ON products.id = invoice_items.product_id WHERE invoice_items.product_id IS NOT NULL AND products.track_stock = 1 AND products.average_price > 0)
                                        AND invoices.id NOT IN (SELECT order_id FROM requisitions WHERE order_type = 3)
                                        ORDER BY invoices.id ASC LIMIT 200;
                                        ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoices_without_sales_cost_journal",
                    "report" => [
                        "query" => "SELECT invoices.* FROM invoices
                                    WHERE invoices.type = 0
                                    AND invoices.id NOT IN (SELECT entity_id FROM journals WHERE entity_type = 'invoice_sales_cost')
                                    AND invoices.id IN (SELECT invoice_items.invoice_id FROM invoice_items JOIN products ON products.id = invoice_items.product_id WHERE invoice_items.product_id IS NOT NULL AND products.track_stock = 1 AND products.average_price > 0)
                                    AND invoices.id NOT IN (SELECT order_id FROM requisitions WHERE order_type = 3)
                                    ORDER BY invoices.id ;
",
                        "fields" => [
                            "id",
                            "date",
                            "no",
                        ],
                        'view_link' => '/owner/invoices/view/%id%'
                    ]
                ],
                "invoices_that_have_refund_receipt_with_different_client" => [
                    "title" => "invoices that is refunded but the refund client is not the invoice client",
                    "check_query" => "select RR.* from invoices I
                                            join invoices RR on I.id = RR.subscription_id and RR.type = 6
                                            where I.client_id != RR.client_id and I.type = 0;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_refund_invoices_wrong_client",
                    "report" => [
                        "query" => "select RR.* from invoices I
                                            join invoices RR on I.id = RR.subscription_id and RR.type = 6
                                            where I.client_id != RR.client_id and I.type = 0;",
                        "fields" => [
                            "id",
                            "date",
                            "no",
                        ],
                        'view_link' => '/owner/invoices/view/%id%'
                    ]
                ],
                "duplicated_invoices" => [
                    "title" => "duplicated  invoices",
                    "check_query" => "SELECT COUNT(*) AS `Rows`, `created`, GROUP_CONCAT(id)  as repeated_invoices FROM `invoices` GROUP BY `created` HAVING COUNT(*) > 1 ORDER BY `Rows` DESC;
                    ",
                    "report" => [
                    "query" => "SELECT COUNT(*) AS `Rows`, `created`, GROUP_CONCAT(id)  as repeated_invoices FROM `invoices` GROUP BY `created` HAVING COUNT(*) > 1 ORDER BY `Rows` DESC;"
                       ,
                       
                        "fields" => [
                            "Rows",
                            "created",
                            "repeated_invoices",
                        ],
                        'view_link' => ''
                    ]
                ],
                "missing_invoice_sales_cost" => [
                    "title" => "Possible missing invoice sales cost",
                    "check_query" => "SELECT COUNT(st.order_id) AS `Rows` FROM `stock_transactions` st LEFT JOIN journals j ON j.entity_id = st.order_id AND j.entity_type = 'invoice_sales_cost' WHERE 1 AND st.source_type = 2 AND j.id IS NULL GROUP BY st.order_id;
                    ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/owner/fake/fix_possible_missing_invoice_sales_cost_journals",
                    "report" => [
                    "query" => "SELECT
                    st.order_id as `invoice_id`
                FROM
                    `stock_transactions` st
                LEFT JOIN journals j ON
                    j.entity_id = st.order_id AND j.entity_type = 'invoice_sales_cost'
                WHERE
                    1 AND st.source_type = 2 AND j.id IS NULL
                GROUP BY
                    st.order_id;"
                       ,
                       
                        "fields" => [
                            "invoice_id"
                        ],
                        
                        'view_link' => '/owner/invoices/view/%invoice_id%'
                    ]
                ],
                "refunded_invoices_with_wrong_summary_refund" => [
                    "title" => "invoices that is refunded but the summary_refund != unpaid of refund invoice",
                    "check_query" => "select *
                                        from invoices sales_invoices
                                        where sales_invoices.type = 0
                                          and sales_invoices.summary_refund != (select sum(refund_invoices.summary_unpaid) from invoices refund_invoices where type = 6 and refund_invoices.subscription_id = sales_invoices.id);
                                        ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/recalculate_summary_refund_invoices",
                    "report" => [
                        "query" => "select *
                                        from invoices sales_invoices
                                        where sales_invoices.type = 0
                                          and sales_invoices.summary_refund != (select sum(refund_invoices.summary_unpaid) from invoices refund_invoices where type = 6 and refund_invoices.subscription_id = sales_invoices.id)
                                        ;",
                        "fields" => [
                            "id",
                            "date",
                            "no",
                        ],
                        'view_link' => '/owner/invoices/view/%id%'
                    ]
                ],
	            "invoices_with_missing_some_stock_transactions_no_requisitions" => [
		            "title" => "Invoices that have some missing trasnactions from one of their items(products)",
		            "check_query" => "SELECT i.id AS invoice_id, ii.product_id  FROM invoices AS i INNER JOIN invoice_items AS ii ON ii.invoice_id = i.id LEFT JOIN products p on p.id = ii.product_id LEFT JOIN stock_transactions AS st ON (st.order_id = i.id AND st.product_id = ii.product_id) WHERE st.id IS NULL AND i.type IN(0, 5, 6) AND i.draft = 0 and ii.product_id is not null AND invoice_id not in (SELECT order_id FROM requisitions) and p.type != 2",
		            "fix_strategy" => "auto",
		            "fix_type" => "url",
		            "fix_url" => "/fake/fix_product_transactions_missing_in_stock_but_exist_in_invoices",
		            "report" => [
			            "query" => "SELECT i.id AS invoice_id, ii.product_id  FROM invoices AS i INNER JOIN invoice_items AS ii ON ii.invoice_id = i.id LEFT JOIN products p on p.id = ii.product_id LEFT JOIN stock_transactions AS st ON (st.order_id = i.id AND st.product_id = ii.product_id) WHERE st.id IS NULL AND i.type IN(0, 5, 6) AND i.draft = 0 and ii.product_id is not null AND invoice_id not in (SELECT order_id FROM requisitions) and p.type != 2;",
			            "fields" => [
				            "invoice_id",
				            "product_id"
			            ],
			            "view_link" => "/owner/invoices/view/%invoice_id%"
		            ]
	            ],
	            "invoices_with_missing_client_data" => [
		            "title" => "Invoices that have some missing  client data and total in listing for recurring invoices",
		            "check_query" => "SELECT * FROM `invoices` WHERE `client_id` IS NULL",
		            "fix_strategy" => "auto",
		            "fix_type" => "url",
		            "fix_url" => "owner/fake/fix_missed_data_in_recurring_invoices_subscription_from_action_lines",
		            "report" => [
			            "query" => "SELECT * FROM `invoices` WHERE `client_id` IS NULL",
			            "fields" => [
				            "id",
			            ],
			            "view_link" => "/owner/invoices/view/%id%"
		            ]
	            ],
                "invoices_with_deleted_clients" => [
                    "title" => "Invoices with deleted clients",
                    "check_query" => "SELECT * FROM `invoices` WHERE client_id not in (SELECT id from clients);",
                    "report" => [
                        "query" => "SELECT * FROM `invoices` WHERE client_id not in (SELECT id from clients);",
                        "fields" => [
                            "id",
                            "client_id"
                        ],
                        'view_link' => "/owner/invoices/view/%id%"
                    ]
                ],
                "reseller_missing_credit_charges" => [
                    "title" => "Reseller with missing credit charges",
                    "check_query" => "SELECT id, client_id FROM invoices where source_type = 7 and draft = 0 and id not in (select invoice_id from credit_charges)",
                    "fix_type" => "url",
                    "fix_url" => "/v2/owner/fix/fix_reseller_credit_charges"
                ],
                "invoices_with_null_discount_type" => [
                    "title" => "Invoices with null discount type",
                    "check_query" => "SELECT * FROM invoice_items WHERE discount_type IS NULL and discount != 0 and discount is not null",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoices_with_null_discount_type"
                ],
                "open_sales_order_with_paid_invoices" => [
                    'title' => 'Open sales orders with paid invoices',
                    'check_query' => "SELECT id
                        FROM `invoices`
                        WHERE `type` = 0 -- invoice
                          and source_type = 12
                          AND `payment_status` in (1, 2)
                          AND `source_id` IN (SELECT `id`
                                              FROM `invoices`
                                              WHERE `type` = 12 -- sales order
                                                AND `draft` = 0
                                                AND `payment_status` = 0);",
                    'fix_type' => 'url',
                    'fix_url' => '/owner/fake/fix_open_sales_order_with_paid_invoices',
                ],
            ]
        ],
        "custom_form" => [
            "Title" => "Custom Forms",
            "plugin_id" => 78,
            "diagnostics" => [
                "deleted_fields" => [
                    "title" => "Deleted Fields",
                    "check_query" => "SELECT CFF.id, CFF.label, CF.table_name, CFF.type FROM custom_form_fields CFF left join custom_forms CF on CF.id = CFF.custom_form_id  where CFF.is_deleted = 1;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/custom_forms/restore_fields",
                    "report" => [
                        "query" => "SELECT CFF.id, CFF.label, CF.table_name, CFF.type FROM custom_form_fields CFF left join custom_forms CF on CF.id = CFF.custom_form_id  where CFF.is_deleted = 1;",
                        "fields" => [
                            "id",
                            "label",
                            "table_name",
                            "type"
                        ],
                        'view_link' => '#'
                    ]
                ],
            ]
        ],

        "inventory" => [
            "Title" => "Inventory",
            "diagnostics" => [
                "bundle_outbound_stock_transactions_without_inbound" => [
                    "title" => "Bundle outbound stock transactions without inbound ones",
                    "check_query" => "SELECT *
                                        FROM `stock_transactions` outbound_stock_transactions
                                        WHERE outbound_stock_transactions.product_id in (select id from products where type = 3)
                                          and outbound_stock_transactions.source_type = 2
                                          and outbound_stock_transactions.id not in (select inbound_stock_transctions.order_id
                                                                                     from stock_transactions inbound_stock_transctions
                                                                                     where inbound_stock_transctions.source_type = 10
                                                                                       and inbound_stock_transctions.product_id =
                                                                                           outbound_stock_transactions.product_id)
                                          ;
                                        ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/bundle_outbound_stock_transactions_without_inbound",
                    "report" => [
                        "query" => "SELECT *
                                        FROM `stock_transactions` outbound_stock_transactions
                                        WHERE outbound_stock_transactions.product_id in (select id from products where type = 3)
                                          and outbound_stock_transactions.source_type = 2
                                          and outbound_stock_transactions.id not in (select inbound_stock_transctions.order_id
                                                                                     from stock_transactions inbound_stock_transctions
                                                                                     where inbound_stock_transctions.source_type = 10
                                                                                       and inbound_stock_transctions.product_id =
                                                                                           outbound_stock_transactions.product_id)
                                          ;",
                        "fields" => [
                            "id",
                            "received_date",
                            "product_id",
                        ],
                        'view_link' => "/owner/products/view/%product_id%"
                    ]
                ],
                "stock_transactions_not_has_requisitions" => [
                    "title" => "stock transactions not has requisitions",
                    "check_query" => " SELECT `stock_transactions`.product_id  ,`stock_transactions`.id FROM `stock_transactions`
                        WHERE `stock_transactions`.`source_type` IN (114, 113) AND
                    `ref_id` NOT IN ( SELECT `requisition_items`.`id` FROM `requisition_items`) ORDER BY id ASC;
                                                ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/fake/remove_stock_transactions_not_has_requisitions",
                    "report" => [
                        "query" => "SELECT `stock_transactions`.product_id  ,`stock_transactions`.id FROM `stock_transactions`
                        WHERE `stock_transactions`.`source_type` IN (114, 113) AND
                        `ref_id` NOT IN ( SELECT `requisition_items`.`id` FROM `requisition_items`) ORDER BY id ASC;",
                        "fields" => [
                            "id",
                            "product_id",
                        ],
                        'view_link' => "/owner/products/view/%product_id%"
                    ]
                ],
                "requisition_items_different_unit_price_then_related_purchase_order" => [
                    "title" => "Requisition items have different unit price then the related purchase order items",
                    "check_query" => "SELECT RI.*, POI.*
                                        FROM requisition_items RI
                                                 join requisitions R on R.id = RI.requisition_id and R.order_type = 6
                                                 join purchase_orders PO on PO.id = R.order_id
                                                 join purchase_order_items POI on POI.purchase_order_id = PO.id
                                        where RI.product_id = POI.product_id
                                          and RI.unit_price != POI.unit_price
                                          and POI.summary_tax1 = 0
                                          and POI.summary_tax2 = 0
                                          ", //inclusive taxes can make then different thats why its a must to be 0
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/purchase_order_items_related_requisition_items_price",
                    "report" => [
                        "query" => "SELECT RI.*, POI.*
                                        FROM requisition_items RI
                                                 join requisitions R on R.id = RI.requisition_id and R.order_type = 6
                                                 join purchase_orders PO on PO.id = R.order_id
                                                 join purchase_order_items POI on POI.purchase_order_id = PO.id
                                        where RI.product_id = POI.product_id
                                          and RI.unit_price != POI.unit_price
                                          and POI.summary_tax1 = 0
                                          and POI.summary_tax2 = 0
                                          ",
                        "fields" => [
                            "requisition_id",
                            "product_id",
                        ],
                        'view_link' => "/owner/requisitions/view/%requisition_id%"
                    ]
                ],
                "fix_requisition_items_without_stock_transactions" => [
                    "title" => "create stock transactions for requisition items without stock transactions",
                    "check_query" => "SELECT *
                        FROM requisition_items RI
                        where id not in (select ref_id from stock_transactions ST where ST.source_type in (101, 102, 103, 104, 105, 106, 107));",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_requisition_items_missing_stock_transactions",
                    "report" => [
                        "query" => "SELECT *
                        FROM requisition_items RI
                        where id not in (select ref_id from stock_transactions ST where ST.source_type in (101, 102, 103, 104, 105, 106, 107));",
                        "fields" => [
                            "id",
                            "requisition_id",
                            "product_id",
                        ],
                        'view_link' => "/owner/requisitions/view/%requisition_id%"
                    ]
                ],
                "fix_received_requisitions_without_received_stock_transactions" => [
                    "title" => "Fix Received requisitions with stock transactions that its status is not received",
                    "check_query" => "SELECT R.* FROM `stock_transactions` ST
                                        join requisitions R on R.id = ST.order_id and ST.source_type in (101, 102, 103, 104, 105, 106, 107)
                                        WHERE ST.status != 4 and R.status = 3  order by ST.order_id DESC;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_received_requisitions_with_unreceived_stock_transactions",
                    "report" => [
                        "query" => "SELECT ST.* FROM `stock_transactions` ST
                                        join requisitions R on R.id = ST.order_id and ST.source_type in (101, 102, 103, 104, 105, 106, 107)
                                        WHERE ST.status != 4 and R.status = 3  order by ST.order_id DESC;",
                        "fields" => [
                            "id",
                            "product_id",
                            "order_id",
                            "received_date"
                        ],
                        'view_link' => "/owner/requisitions/view/%order_id%"
                    ]
                ],
                "wrong_stock_balance" => [
                    "title" => "Wrong Product Stock Balance when its quantity is 0 but the amount is not 0",
                    "check_query" => "SELECT stock_totals.`id`,
                                       (stock_totals.qty_inward + stock_totals.qty_outward),
                                       stock_totals.amount_inward + stock_totals.amount_outward
                                from (SELECT `P`.`id`,
                                             `P`.`product_code`,
                                             `P`.`barcode`,
                                             `P`.`name`,
                                             SUM(CASE
                                                     WHEN ST.quantity > 0
                                                         THEN ST.quantity
                                                     else 0 END) as qty_inward,
                                             SUM(CASE
                                                     WHEN ST.quantity < 0
                                                         THEN ST.quantity
                                                     else 0 END) as qty_outward,
                                             0                   as amount_before,
                                             SUM(CASE
                                                     WHEN ST.quantity > 0 THEN
                                                             ST.quantity *
                                                             if(ST.source_type = 4 OR ST.source_type = 6 OR ST.source_type = 105 OR
                                                                ST.source_type = 104,
                                                                ST.purchase_price, ST.price - ST.discount) * ST.currency_rate
                                                     ELSE 0 END) as amount_inward,
                                             SUM(CASE
                                                     WHEN ST.quantity < 0 
                                                         THEN ST.quantity * ST.purchase_price
                                                     ELSE 0 END) as amount_outward,

                                             null                as group_by_field
                                      FROM stock_transactions ST
                                               RIGHT join products P on P.id = ST.product_id
                                               LEFT join stores S on S.id = ST.store_id
                                      WHERE ST.ignored = 0
                                        AND ST.status = 4
                                      GROUP BY P.id) as stock_totals
                                where ((stock_totals.qty_inward + stock_totals.qty_outward)) = 0
                                  and abs(stock_totals.amount_inward + stock_totals.amount_outward) >= 0.005;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_stock_balance",
                    "report" => [
                        "query" => "SELECT stock_totals.`id`,
                                       (stock_totals.qty_inward + stock_totals.qty_outward) as total_amount,
                                       (stock_totals.amount_inward + stock_totals.amount_outward) as total_quantity
                                from (SELECT `P`.`id`,
                                             `P`.`product_code`,
                                             `P`.`barcode`,
                                             `P`.`name`,
                                             SUM(CASE
                                                     WHEN ST.quantity > 0
                                                         THEN ST.quantity
                                                     else 0 END) as qty_inward,
                                             SUM(CASE
                                                     WHEN ST.quantity < 0
                                                         THEN ST.quantity
                                                     else 0 END) as qty_outward,
                                             0                   as amount_before,
                                             SUM(CASE
                                                     WHEN ST.quantity > 0 THEN
                                                             ST.quantity *
                                                             if(ST.source_type = 4 OR ST.source_type = 6 OR ST.source_type = 105 OR
                                                                ST.source_type = 104,
                                                                ST.purchase_price, ST.price - ST.discount) * ST.currency_rate
                                                     ELSE 0 END) as amount_inward,
                                             SUM(CASE
                                                     WHEN ST.quantity < 0 
                                                         THEN ST.quantity * ST.purchase_price
                                                     ELSE 0 END) as amount_outward,

                                             null                as group_by_field
                                      FROM stock_transactions ST
                                               RIGHT join products P on P.id = ST.product_id
                                               LEFT join stores S on S.id = ST.store_id
                                      WHERE ST.ignored = 0
                                        AND ST.status = 4
                                      GROUP BY P.id) as stock_totals
                                where ((stock_totals.qty_inward + stock_totals.qty_outward)) = 0
                                  and abs(stock_totals.amount_inward + stock_totals.amount_outward) >= 0.005;",
                        "fields" => [
                            "id",
                            "total_amount",
                            "total_quantity",
                        ],
                        'view_link' => "/owner/products/view/%id%"
                    ]
                ],
                "wrong_cached_stock_balance" => [
                    "title" => "Recalculate cached stock balance field in products table",
                    "check_query" => "
                        SELECT
                            products.id,
                            StockTransaction.product_id,
                            products.stock_balance,
                            store_id,
                            SUM(quantity) AS total_quantity
                        FROM
                            stock_transactions AS StockTransaction
                        INNER JOIN products ON products.id = StockTransaction.product_id
                        WHERE
                        STATUS
                            = 4 AND(ignored = 0 OR ignored IS NULL)
                        GROUP BY
                            StockTransaction.product_id
                        HAVING
                            total_quantity != products.stock_balance;
                        ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/sync_stock_balance_field",
                    "report" => [
                        "query" => "
                            SELECT
                                products.id,
                                StockTransaction.product_id,
                                products.stock_balance,
                                store_id,
                                SUM(quantity) AS total_quantity
                            FROM
                                stock_transactions AS StockTransaction
                            INNER JOIN products ON products.id = StockTransaction.product_id
                            WHERE
                            STATUS
                                = 4 AND(ignored = 0 OR ignored IS NULL)
                            GROUP BY
                                StockTransaction.product_id
                            HAVING
                                total_quantity != products.stock_balance;",
                        "fields" => [
                            "id",
                            "stock_balance",
                            "total_quantity",
                        ],
                        'view_link' => "/owner/products/view/%id%"
                    ]
                ],
                "mixed_inventory" => [
                    "title" => "The system contains both requisitions and stock_transactions",
                    "check_query" => "select sum(temp.id) > 2
                                        from ((select 1 as id
                                              from stock_transactions
                                              where source_type in (1, 2, 3, 4, 5, 6, 7) limit 1)
                                              UNION
                                              (
                                              select 2 as id
                                              from stock_transactions
                                              where source_type in (101, 102, 103, 104, 105, 106, 107) limit 1
                                              )) temp;",
                    "fix_strategy" => "manual",
                ],
                "invoices_with_no_stock_transactions" => [
                    "title" => "discover invoices with no stock transactions",
                    "check_query" => "select *
                                            from invoices I
                                                     left join invoice_items item on I.id = item.invoice_id
                                                     left join products P on P.id = item.product_id
                                            where P.track_stock = 1 and I.type in (0, 5, 6)
                                              and (P.type in (0,1,3) or P.type is NULL)
                                              and I.id not in (SELECT order_id from requisitions where order_type in (3, 4, 5))
                                              and I.id not in (SELECT order_id from stock_transactions where source_type in (2, 4, 6))
                                              and ((SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) = 0 or (SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) IS NULL)
                                              and I.branch_id = $branch_id
                                              and (pos_shift_id IS NULL)
                                              group by I.id;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoices_stock_transactions",
                    "report" => [
                        "query" => "select I.*
                                            from invoices I
                                                     left join invoice_items item on I.id = item.invoice_id
                                                     left join products P on P.id = item.product_id
                                                     left join pos_shifts PS on PS.id = I.pos_shift_id
                                            where P.track_stock = 1 and I.type in (0, 5, 6)
                                              and (P.type in (0,1,3) or P.type is NULL)
                                              and I.id not in (SELECT order_id from requisitions where order_type in (3, 4, 5))
                                              and I.id not in (SELECT order_id from stock_transactions where source_type in (2, 4, 6))
                                              and ((SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) = 0 or (SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) IS NULL)
                                              and (I.pos_shift_id is null OR PS.is_calculated_per_invoice = 1)
                                              and I.branch_id = $branch_id group by I.id;",
                        "fields" => [
                            "id",
                            "no",
                            "date",
                        ],
                        'view_link' => "/owner/invoices/view/%id%"
                    ]
                ],
                "invoice_items_with_no_stock_transactions" => [
                    "title" => "discover invoice items with no stock transactions",
                    "check_query" => "select *
                                            from invoices I
                                                     left join invoice_items item on I.id = item.invoice_id
                                                     left join products P on P.id = item.product_id
                                                    left join pos_shifts PS on PS.id = I.pos_shift_id
                                            where P.track_stock = 1 and I.type in (0, 5, 6)
                                              and (P.type in (0,1,3) or P.type is NULL)
                                              and I.id not in (SELECT order_id from requisitions where order_type in (3, 4, 5))
                                              and item.id not in (SELECT ref_id from stock_transactions where source_type in (2, 4, 6))
                                              and ((SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) = 0 or (SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) IS NULL)
                                              and I.branch_id = $branch_id
                                              and (pos_shift_id IS NULL or PS.is_calculated_per_invoice)
                                              group by I.id;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoice_items_stock_transactions",
                    "report" => [
                        "query" => "select *
                                            from invoices I
                                                     left join invoice_items item on I.id = item.invoice_id
                                                     left join products P on P.id = item.product_id
                                                    left join pos_shifts PS on PS.id = I.pos_shift_id
                                            where P.track_stock = 1 and I.type in (0, 5, 6)
                                              and (P.type in (0,1,3) or P.type is NULL)
                                              and I.id not in (SELECT order_id from requisitions where order_type in (3, 4, 5))
                                              and item.id not in (SELECT ref_id from stock_transactions where source_type in (2, 4, 6))
                                              and ((SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) = 0 or (SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) IS NULL)
                                              and I.branch_id = $branch_id
                                              and (pos_shift_id IS NULL or PS.is_calculated_per_invoice)
                                              group by I.id;",
                        "fields" => [
                            "id",
                            "no",
                            "date",
                        ],
                        'view_link' => "/owner/invoices/view/%id%"
                    ]
                ],
                "requisition_items_with_zero_price" => [
                    "title" => "fix purchase invoice and invoice and manual inbound requisition items with zero price",
                    "check_query" => "select RI.requisition_id
                                            from stock_transactions ST
                                                     left join requisition_items RI
                                                               on RI.requisition_id = ST.order_id and ST.source_type in (101, 106,103) and ST.ref_id = RI.id
                                            where ST.purchase_price != 0 and RI.unit_price = 0
                                            group by RI.requisition_id
                                            ORDER BY `RI`.`requisition_id` DESC;
                                            ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_requisition_items_with_zero_price",
                    "report" => [
                        "query" => "select RI.requisition_id
                                            from stock_transactions ST
                                                     left join requisition_items RI
                                                               on RI.requisition_id = ST.order_id and ST.source_type in (101, 106,103) and ST.ref_id = RI.id
                                            where ST.purchase_price != 0 and RI.unit_price = 0
                                            group by RI.requisition_id
                                            ORDER BY `RI`.`requisition_id` DESC;",
                        "fields" => [
                            "requisition_id",
                        ],
                        'view_link' => "/owner/requisitions/view/%requisition_id%"
                    ]
                ],
                "invoices_without_requisitions" => [
                    "title" => "discover invoices with no requisitions",
                    "check_query" => "select *
                                            from invoices
                                            where draft = 0 and
                                            id not in (select order_id from requisitions where order_type = 3) and
                                            id not in (select order_id from stock_transactions where source_type in (2)) and
                                            id in (select invoice_id from invoice_items where product_id in (select id from products where track_stock = 1))
                                              and type = 0
                                              and (select `value` from settings where `key` = 'enable_requisitions' and `value` = 1 and branch_id = invoices.branch_id) is not null;",

                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoices_without_requisition",
                    "report" => [
                        "query" => "select *
                                            from invoices
                                            where draft = 0 and
                                            id not in (select order_id from requisitions where order_type = 3) and
                                            id not in (select order_id from stock_transactions where source_type in (2)) and
                                            id in (select invoice_id from invoice_items where product_id in (select id from products where track_stock = 1))
                                              and type = 0
                                              and (select `value` from settings where `key` = 'enable_requisitions' and `value` = 1 and branch_id = invoices.branch_id) is not null;",
                        "fields" => [
                            "id",
                            "no",
                            "date",
                        ],
                        'view_link' => "/owner/invoices/view/%id%"
                    ]
                ],
                "po_and_pr_without_requisitions" => [
                    "title" => "fix purchase invoices and purchase refunds without requisitions",
                    "check_query" => "SELECT id, type FROM `purchase_orders` as PurchaseOrder 
                        WHERE type in (0, 6)
                        AND id in (
                            select purchase_order_id 
                            from purchase_order_items 
                            where product_id in (select id from products where track_stock = 1)
                        )
                        AND id NOT IN (
                            SELECT order_id FROM requisitions WHERE order_type IN (6, 7)
                        )
                        AND (select `value` from settings where `key` = 'enable_requisitions' and `value` = 1)",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/fake/fix_purchase_invoices_and_refunds_without_requisition",
                    "report" => [
                        "query" => "SELECT * FROM `purchase_orders` as PurchaseOrder 
                            WHERE type in (0, 6)
                            AND id in (
                                select purchase_order_id 
                                from purchase_order_items 
                                where product_id in (select id from products where track_stock = 1)
                            )
                            AND id NOT IN (
                                SELECT order_id FROM requisitions WHERE order_type IN (6, 7)
                            )
                            AND (select `value` from settings where `key` = 'enable_requisitions' and `value` = 1)",
                        "fields" => [
                            "id",
                            "no",
                            "date",
                        ],
                        'view_link' => "/owner/purchase_invoices/view/%id%"
                    ]
                ],
                "stocktaking_without_requisitions" => [
                    "title" => "discover stocktaking with no requisitions",
                    "check_query" => "select id
                        from stocktakings
                        where stocktakings.status = 1 and
                        stocktakings.id not in (select order_id from requisitions where order_type in (13, 14)) and
                        stocktakings.id not in (select order_id from stock_transactions where source_type in (113, 114)) and 
                        stocktakings.id in (select stocktaking_id from stocktaking_records where product_id in (select id from products where track_stock = 1))
                          and (select `value` from settings where `key` = 'enable_requisitions' and `value` = 1 and branch_id = stocktakings.branch_id) is not null;",

                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_stocktakings_without_requisition",
                    "report" => [
                        "query" => "select id
                        from stocktakings
                        where stocktakings.status = 1 and
                        stocktakings.id not in (select order_id from requisitions where order_type in (13, 14)) and
                        stocktakings.id not in (select order_id from stock_transactions where source_type in (113, 114)) and 
                        stocktakings.id in (select stocktaking_id from stocktaking_records where product_id in (select id from products where track_stock = 1))
                          and (select `value` from settings where `key` = 'enable_requisitions' and `value` = 1 and branch_id = stocktakings.branch_id) is not null;",
                        "fields" => [
                            "id",
                            "date",
                        ],
                        'view_link' => "/owner/stocktakings/view/%id%"
                    ]
                ],
                "remove_requisitions_with_deleted_sources" => [
                    "title" => "Discover requistions for deleted sources",
                    "check_query" => "SELECT id FROM requisitions WHERE
                                        (order_type IN (3,4,5) and order_id NOT in (SELECT id FROM invoices WHERE draft <> 1))
                                        OR (order_type IN (6,7) and order_id NOT in (SELECT id FROM purchase_orders WHERE draft <> 1))
                                        OR (order_type IN (13,14) and order_id NOT in (SELECT id FROM stocktakings))",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/remove_requisitions_with_deleted_sources",
                    "report" => [
                        "query" => "SELECT id FROM requisitions WHERE
                                    (order_type IN (3,4,5) and order_id NOT in (SELECT id FROM invoices WHERE draft <> 1))
                                    OR (order_type IN (6,7) and order_id NOT in (SELECT id FROM purchase_orders WHERE draft <> 1))
                                    OR (order_type IN (13,14) and order_id NOT in (SELECT id FROM stocktakings))",
                        "fields" => [
                            "id"
                        ],
                        'view_link' => "/owner/requisitions/view/%id%"
                    ]
                ],
                'update_rate_of_stock_transactions_with_expense_distributions' => [
                    "title" => "stock transactions with different rate then its related expense distributions",
                    "check_query" => "select * from stock_transactions ST join expense_distributions ED on ED.requisition_id = ST.order_id where abs(ST.currency_rate - ED.requisition_rate) > 0.01 and ST.source_type = 106;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/update_expense_distributions_transactions",
                    "report" => [
                        "query" => "select * from stock_transactions ST join expense_distributions ED on ED.requisition_id = ST.order_id where abs(ST.currency_rate - ED.requisition_rate) > 0.01 and ST.source_type = 106;",
                        "fields" => [
                            "order_id"
                        ],
                        'view_link' => "/owner/requisitions/view/%order_id%"
                    ]
                ]
            ]
        ],

        "pos" => [
            "Title" => "Pos",
            "plugin_id" => \Izam\Daftra\Common\Utils\PluginUtil::PosPlugin,
            "diagnostics" => [
                "invoices_with_no_stock_transactions_pos" => [
                    "title" => "discover invoices with no stock transactions",
                    "check_query" => "select *
                                            from invoices I
                                                     left join invoice_items item on I.id = item.invoice_id
                                                     left join products P on P.id = item.product_id
                                                     left join pos_shifts PS on PS.id = I.pos_shift_id
                                            where P.track_stock = 1 and I.type = 0 
                                              and (P.type in (0,1,3) or P.type is NULL)
                                              and I.id not in (SELECT order_id from requisitions where order_type in (3, 4, 5))
                                              and I.id not in (SELECT order_id from stock_transactions where source_type in (2, 4, 6))
                                              and ((SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) = 0 or (SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) IS NULL)
                                              and (I.pos_shift_id is null OR PS.is_calculated_per_invoice = 1)
                                              and I.branch_id = $branch_id group by I.id;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoices_stock_transactions",
                    "report" => [
                        "query" => "select I.*
                                            from invoices I
                                                     left join invoice_items item on I.id = item.invoice_id
                                                     left join products P on P.id = item.product_id
                                                     left join pos_shifts PS on PS.id = I.pos_shift_id
                                            where P.track_stock = 1 and I.type = 0
                                              and (P.type in (0,1,3) or P.type is NULL)
                                              and I.id not in (SELECT order_id from requisitions where order_type in (3, 4, 5))
                                              and I.id not in (SELECT order_id from stock_transactions where source_type in (2, 4, 6))
                                              and ((SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) = 0 or (SELECT value FROM `settings` WHERE `key` LIKE 'enable_requisitions' AND `branch_id` = $branch_id) IS NULL)
                                              and (I.pos_shift_id is null OR PS.is_calculated_per_invoice = 1)
                                              and I.branch_id = $branch_id group by I.id;",
                        "fields" => [
                            "id",
                            "no",
                            "date",
                        ],
                        'view_link' => "/owner/invoices/view/%id%"
                    ]
                ],
                "pos_shift_invoices_without_payment" => [
                    "title" => "pos shift calculated per session invoices that have no payments",
                    "check_query" => "select *
                                        from invoices
                                        where summary_unpaid > 0 and pos_shift_id in (select id from pos_shifts where is_calculated_per_invoice = 0)
                                          and id not in (select invoice_id from invoice_payments where invoice_id is not null);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/save_invoice_payments_for_pos_shift_invoices",
                    "report" => [
                        "query" => "select *
                                        from invoices
                                        where summary_unpaid > 0 and pos_shift_id in (select id from pos_shifts where is_calculated_per_invoice = 0)
                                          and id not in (select invoice_id from invoice_payments where invoice_id is not null);",
                        "fields" => [
                            "id",
                            "no",
                            "date",
                        ],
                        'view_link' => "/owner/invoices/view/%id%"
                    ]
                ],
                "pos_invoice_payments_journals" => [
                    "title" => "fix pos shift invoices that have invoice payment with no journals and are calculated per invoice",
                    "check_query" => "select * from invoice_payments where payment_method != 'client_credit' and id not in (select entity_id from journals where entity_type = 'invoice_payment') and invoice_id in (select id from invoices where pos_shift_id in (select id from pos_shifts where is_calculated_per_invoice = 1));",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/pos_shifts/fix_invoice_payments_journals",
                    "report" => [
                        "query" => "select * from invoice_payments where payment_method != 'client_credit' and id not in (select entity_id from journals where entity_type = 'invoice_payment') and invoice_id in (select id from invoices where pos_shift_id in (select id from pos_shifts where is_calculated_per_invoice = 1));",
                        "fields" => [
                            "id",
                            "date",
                            "invoice_id"
                        ],
                        'view_link' => "/owner/invoice_payments/view/%id%"
                    ]
                ],
                "pos_invoice_payments_should_have_no_journals" => [
                    "title" => "fix pos shift invoices that have invoice payment with journals and are calculated per session",
                    "check_query" => "select *
                                        from invoice_payments
                                        where id in (select entity_id from journals where entity_type = 'invoice_payment')
                                          and invoice_id in (select id
                                                             from invoices
                                                             where pos_shift_id in (select id
                                                                                    from pos_shifts
                                                                                    where is_calculated_per_invoice = 0 or is_calculated_per_invoice is null));",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/delete_pos_invoice_payments_journals",
                    "report" => [
                        "query" => "select *
                                    from invoice_payments
                                    where id in (select entity_id from journals where entity_type = 'invoice_payment')
                                      and invoice_id in (select id
                                                         from invoices
                                                         where pos_shift_id in (select id
                                                                                from pos_shifts
                                                                                where is_calculated_per_invoice = 0 or is_calculated_per_invoice is null));",
                        "fields" => [
                            "id",
                            "date",
                            "invoice_id"
                        ],
                        'view_link' => "/owner/invoice_payments/view/%id%"
                    ]
                ],
                "sessions_per_invoice_but_have_journals" => [
                    "title" => "Pos Sessions That Are calculated per invoice but sessions have journals",
                    "check_query" => "select * from pos_shifts where is_calculated_per_invoice = 1 and id in (select entity_id from journals where entity_type = 'pos_shift_sales' or entity_type = 'pos_shift_refund');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/pos_shifts/remove_pos_shift_journals_for_per_invoice_shifts",
                ],
                "sessions_per_session_but_have_invoices_have_stock_transactions" => [
                    "title" => "Pos Sessions That Are calculated per session but has invoices that has stock transactions",
                    "check_query" => "select * from invoices where pos_shift_id in (select id from pos_shifts where is_calculated_per_invoice = 0) and id in (select order_id from stock_transactions where source_type in (2,4,6));",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/remove_stock_transactions_for_invoices_per_session",
                    "report" => [
                        "query" => "select * from invoices where pos_shift_id in (select id from pos_shifts where is_calculated_per_invoice = 0) and id in (select order_id from stock_transactions where source_type in (2,4,6));",
                        "fields" => [
                            "id",
                            "no",
                            "date",
                            "pos_shift_id"
                        ],
                        'view_link' => "/owner/invoices/view/%id%"
                    ]
                ],
                "sessions_per_session_with_no_requisitions" => [
                    "title" => "Pos sessions that have no requisition but are calculated per invoice",
                    "check_query" => "SELECT * FROM pos_shifts WHERE pos_shifts.is_calculated_per_invoice = 0 AND NOT EXISTS( SELECT id FROM requisitions WHERE requisitions.order_type = 12 AND requisitions.order_id = pos_shifts.id );",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/add_requisitions_for_pos_shifts_calculated_per_session",
                    "report" => [
                        "query" => "SELECT id as pos_shift_id FROM pos_shifts WHERE pos_shifts.is_calculated_per_invoice = 0 AND NOT EXISTS( SELECT id FROM requisitions WHERE requisitions.order_type = 12 AND requisitions.order_id = pos_shifts.id );",
                        "fields" => [
                            "pos_shift_id",
                        ],
                        'view_link' => "/owner/pos_shifts/view/%id%"
                    ]
                ],
                "sessions_per_invoice_but_have_requisitions" => [
                    "title" => "Pos Sessions That Are calculated per invoice but sessions have requisitions",
                    "check_query" => "select * from pos_shifts where is_calculated_per_invoice = 1 and id in (select order_id from requisitions where order_type = 12 or order_type = 11);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/requisitions/fix_pos_per_invoice_requisitions",
                    "report" => [
                        "query" => "select * from pos_shifts where is_calculated_per_invoice = 1 and id in (select order_id from requisitions where order_type = 12 or order_type = 11);",
                        "fields" => [
                            "id",
                        ],
                        'view_link' => "/owner/pos_shifts/view/%id%"
                    ]
                ],
                "session_invoices_has_not_journals" => [
                    "title" => "Pos Sessions That Are calculated per invoice but has invoices that has not journals",
                    "check_query" => "Select * from pos_shifts where is_calculated_per_invoice = 1 and id in (select distinct pos_shift_id from invoices where (type = 0 or type = 6) and summary_total > 0 and draft = 0 and id not in (select entity_id from journals where entity_type = 'invoice' or entity_type = 'refund_receipt'))",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/pos_shifts/update_pos_shifts_invoices_journals",
                    "report" => [
                        "query" => "Select * from pos_shifts where is_calculated_per_invoice = 1 and id in (select distinct pos_shift_id from invoices where (type = 0 or type = 6) and summary_total > 0 and draft = 0 and id not in (select entity_id from journals where entity_type = 'invoice' or entity_type = 'refund_receipt'))",
                        "fields" => [
                            "id",
                        ],
                        'view_link' => "/owner/pos_shifts/view/%id%"
                    ]
                ],
                "per_session_pos_but_invoices_has_journals" => [
                    "title" => "Pos Sessions That Are calculated per session but has invoices that has journals",
                    "check_query" => "SELECT *
                                        FROM invoices I
                                                 join pos_shifts PS on PS.id = I.pos_shift_id
                                        where PS.is_calculated_per_invoice = 0
                                          and I.id in (select entity_id from journals where entity_type in ('invoice', 'refund_receipt'));
                                        ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/remove_pos_shifts_invoices_journals",
                    "report" => [
                        "query" => "SELECT *
                                    FROM invoices I
                                             join pos_shifts PS on PS.id = I.pos_shift_id
                                    where PS.is_calculated_per_invoice = 0
                                      and I.id in (select entity_id from journals where entity_type in ('invoice', 'refund_receipt'));
                                    ",
                        "fields" => [
                            "id",
                            "no",
                            "date"
                        ],
                        'view_link' => "/owner/invoices/view/%id%"
                    ]
                ],
                "session_have_no_requisitions" => [
                    "title" => "Pos Sessions That are not calculated per invoice and has no requisitions",
                    "check_query" => "SELECT * FROM pos_shifts where status = 3 and (is_calculated_per_invoice = 0 or is_calculated_per_invoice is null) and id not in (select DISTINCT order_id from requisitions where order_type in (11,12));",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_pos_shifts_without_requisitions",
                    "report" => [
                        "query" => "SELECT * FROM pos_shifts where status = 3 and (is_calculated_per_invoice = 0 or is_calculated_per_invoice is null) and id not in (select DISTINCT order_id from requisitions where order_type in (11,12));",
                        "fields" => [
                            "id",
                        ],
                        'view_link' => "/owner/pos_shifts/view/%id%"
                    ]
                ],
                "session_have_no_journals" => [
                    "title" => "Pos Sessions That has no sales journals",
                    "check_query" => "select * from pos_shifts where is_calculated_per_invoice = 0 and id not in (select entity_id from journals where entity_type = 'pos_shift_sales' or entity_type = 'pos_shift_refund');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/create_pos_shift_journals_for_sessions_without_journals",
                    "report" => [
                        "query" => "select * from pos_shifts where is_calculated_per_invoice = 0 and id not in (select entity_id from journals where entity_type = 'pos_shift_sales' or entity_type = 'pos_shift_refund');",
                        "fields" => [
                            "id",
                        ],
                        'view_link' => "/owner/pos_shifts/view/%id%"
                    ]
                ],
                "session_falsely_set_as_calculated_per_invoice" => [
                    "title" => "Pos Sessions That are calculated per session but falsly set as calculated per invoice",
                    "check_query" => "select * from pos_shifts where 
                                        is_calculated_per_invoice = 1 and 
                                        (
                                            id in (select order_id from requisitions where order_type in (11,12)) OR
                                            id in (select entity_id from journals where entity_type in ('pos_shift_sales', 'pos_shift_refund'))
                                        );",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update pos_shifts 
                            set is_calculated_per_invoice = 0 
                            where 
                            is_calculated_per_invoice = 1 and 
                            (
                                id in (select order_id from requisitions where order_type in (11,12)) OR
                                id in (select entity_id from journals where entity_type in ('pos_shift_sales', 'pos_shift_refund'))
                            );"
                    ],
                    "report" => [
                        "query" => "select * from pos_shifts where 
                                        is_calculated_per_invoice = 1 and 
                                        (
                                            id in (select order_id from requisitions where order_type in (11,12)) OR
                                            id in (select entity_id from journals where entity_type in ('pos_shift_sales', 'pos_shift_refund'))
                                        );",
                        "fields" => [
                            "id",
                        ],
                        'view_link' => "/owner/pos_shifts/view/%id%"
                    ]
                ],
//                "sessions_per_invoice" => [
//                    "title" => "Pos Sessions Per Invoice",
//                    "check_query" => "Select * from pos_shifts where is_calculated_per_invoice = 1",
//                    "fix_strategy" => "auto",
//                    "fix_type" => "url",
//                    "fix_url" => "/owner/pos_shifts/convert_sessions_to_per_session",
//                    "report" => [
//                        "query" => "Select * from pos_shifts where is_calculated_per_invoice = 1;",
//                        "fields" => [
//                            "id",
//                            "date",
//                            "no",
//                        ],
//                        'view_link' => '#'
//                    ]
//                ],
                    "fix_pos_with_wrong_branch" => [
                        "title" => "Pos With Wrong Branch (2023-04-01 : 2023-04-03)",
                        "check_query" => "SELECT * FROM `pos_shifts` JOIN staffs on pos_shifts.staff_id = staffs.id where pos_shifts.branch_id != staffs.branch_id AND pos_shifts.open_time BETWEEN '2023-04-01' AND '2023-04-03'" ,
                        "fix_strategy" => "auto",
                        "fix_type" => "url",
                        "fix_url" => "/owner/fake/fix_pos_shifts_wrong_branch",
                    ],
            ],

        ],
        "Cheques" => [
            "Title" => "Cheques",
            "plugin_id" => \Izam\Daftra\Common\Utils\PluginUtil::HRM_CHEQUE_CYCLE_PLUGIN,
            "diagnostics" => [
                "collected_payable_cheques_with_no_journals" => [
                    "title" => "Collected Payable Cheques with no journals",
                    "check_query" => "SELECT * FROM payable_cheques where status = 'collected' and id not in (select entity_id from journals where entity_type = 'collected_pay_cheque');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/v2/owner/fix/payable_cheques_collected_journals"
                ],
            ]
        ],

        'desktop' => [
            'Title' => "Desktop",
            "plugin_id" => null,
            "diagnostics" => [
                'invoices_no_item' => [
                    "title" => 'Invoices without Items (fix from desktop log files)',
                    "check_query" => "SELECT invoices.id, invoices.date FROM `invoices` left outer JOIN invoice_items on invoices.id = invoice_items.invoice_id where invoice_items.invoice_id is null AND invoices.order_number IS NOT NULL ORDER BY `invoices`.`id` ASC;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoices_with_empty_items",
                    "report" => [
                        "query" => "SELECT invoices.id, invoices.date FROM `invoices` left outer JOIN invoice_items on invoices.id = invoice_items.invoice_id where invoice_items.invoice_id is null AND invoices.order_number IS NOT NULL ORDER BY `invoices`.`id` ASC;",
                        "fields" => [
                            "id",
                        ],
                        "view_link" => "/owner/invoices/view/%id%"
                    ]
                ],
                'partially_or_over_paid_invoices' => [
                    "title" => 'items subtotal is less or greater than paid amount (fix from desktop log files)',
                    "check_query" => "SELECT
    invoices.id,
    invoices.type,
    invoices.order_number,
    invoices.summary_paid,
    SUM(invoice_items.subtotal),
    ABS(invoices.summary_paid - SUM(invoice_items.subtotal)) AS diff
FROM
    invoices
INNER JOIN invoice_items ON invoices.id = invoice_items.invoice_id
GROUP BY
    invoice_items.invoice_id
HAVING
    invoices.summary_paid != SUM(invoice_items.subtotal) AND diff > 0.03 and invoices.type = 0 and order_number IS NOT NULL;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_partially_or_over_paid_invoices",
                    "report" => [
                        "query" => "SELECT
    invoices.id,
    invoices.type,
    invoices.order_number,
    invoices.summary_paid,
    SUM(invoice_items.subtotal),
    ABS(invoices.summary_paid - SUM(invoice_items.subtotal)) AS diff
FROM
    invoices
INNER JOIN invoice_items ON invoices.id = invoice_items.invoice_id
GROUP BY
    invoice_items.invoice_id
HAVING
    invoices.summary_paid != SUM(invoice_items.subtotal) AND diff > 0.03 and invoices.type = 0 and order_number IS NOT NULL;",
                        "fields" => [
                            "id",
                        ],
                        "view_link" => "/owner/invoices/view/%id%"
                    ]
                ],
            ]
        ],
        'purchases' => [
            "Title" => "Purchases",
            "plugin_id" => null,
            "diagnostics" => [
                "supplier_id_conflict" => [
                    "title" => "Payment have different supplier than purchase invoice",
                    "check_query" => "SELECT * FROM `purchase_order_payments` POP join purchase_orders PO on PO.id = POP.purchase_order_id and PO.supplier_id != POP.supplier_id and POP.supplier_id IS NOT NULL;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_purchase_order_payment_supplier"
                ],
                "orphan_stock_transactions" => [
                    "title" => "Delete orphan stock transactions assigned to purchase refund with deleted supplier",
                    "check_query" => "SELECT * FROM `stock_transactions` WHERE source_type = 7 and order_id not in (SELECT id from purchase_orders)",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "DELETE FROM `stock_transactions` WHERE source_type = 7 and order_id not in (SELECT id from purchase_orders)"
                    ]
                ]
            ]
        ],
        'payroll' => [
            "Title" => "Payroll",
            "plugin_id" => \Izam\Daftra\Common\Utils\PluginUtil::HRM_PAYROLL_PLUGIN,
            "diagnostics" => [
                "pay_runs_without_journals" => [
                    "title" => "Payruns without journals",
                    "check_query" => "SELECT * FROM payruns where id not in (select entity_id from journals where entity_type = 'pay_run') and id in (select payrun_id from payslips where status = 'approved' or status = 'paid');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/v2/owner/payruns/fix_wtihout_journals",
                ],
                "pay_runs_wrong_journals" => [
                    "title" => "Payruns with jorunals not equal to paid or approved payslips",
                    "check_query" => "select J.*
                                        from journals J
                                                 join (SELECT payrun_id, sum(net_pay) as total
                                                       FROM `payslips`
                                                       where (status = 'approved' or status = 'paid')
                                                         and deleted_at is null
                                                       group by payrun_id) PT on PT.payrun_id = J.entity_id and J.entity_type = 'pay_run'
                                                 join journal_transactions JT on JT.journal_id = J.id
                                        where JT.subkey = 'wages-payroll_wages'
                                          and ABS(JT.currency_credit - PT.total) > 0.0001;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/v2/owner/payruns/fix_payruns",
                    "report" => [
                        "query" => "select J.*
                                    from journals J
                                             join (SELECT payrun_id, sum(net_pay) as total
                                                   FROM `payslips`
                                                   where (status = 'approved' or status = 'paid')
                                                     and deleted_at is null
                                                   group by payrun_id) PT on PT.payrun_id = J.entity_id and J.entity_type = 'pay_run'
                                             join journal_transactions JT on JT.journal_id = J.id
                                    where JT.subkey = 'wages-payroll_wages'
                                      and ABS(JT.credit - PT.total) > 0.0001;",
                        "fields" => [
                            "id",
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ],
                ],
                "pay_run_journals_of_deleted_payruns" => [
                    "title" => "payrun journals of deleted pay runs",
                    "check_query" => "SELECT * FROM journals where entity_type = 'pay_run' and entity_id not in (select id from payruns where deleted_at IS NULL);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "delete FROM `journals` WHERE entity_type='pay_run' and entity_id  not in (select id from payruns where deleted_at IS NULL)",
                        "delete FROM `journal_transactions` WHERE journal_id  not in (select id from journals)"
                    ],
                    "report" => [
                        "query" => "SELECT * FROM journals where entity_type = 'pay_run' and entity_id not in (select id from payruns where deleted_at IS NULL);",
                        "fields" => [
                            "id",
                            "date",
                            "description"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ],
                ],

                "fix_empty_payslip_branch_id" => [
                    "title" => "fix payslip not appear in payslip reports  empty payslip branch_id",
                    "check_query" => "select * from payslips WHERE ( payslips.branch_id IS NULL OR payslips.branch_id = '' OR payslips.branch_id = 0 ) AND( payslips.branch_name IS NOT NULL AND payslips.branch_name <> '' );",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/fake/fix_empty_payslip_branch_id",
                    "report" => [
                        "query" => "select * from payslips WHERE ( payslips.branch_id IS NULL OR payslips.branch_id = '' OR payslips.branch_id = 0 ) AND( payslips.branch_name IS NOT NULL AND payslips.branch_name <> '' );",
                        "fields" => [
                            "id",
                            "designation_name",
                            "department_name",
                            "status"
                        ],
                        "view_link" => "/v2/owner/payslips/%id%"
                    ],
                ]
                
            ]
        ],
        "accounting" => [
            "Title" => "Accounting",
            "plugin_id" => 78,
            "diagnostics" => [
                "delete_invoice_payment_exchange_difference_journal" => [
                    "title" => "Delete invoice payment exchange difference journal when invocie payment currency = system currency",
                    "check_query" => "SELECT
                                            *
                                        FROM
                                            journals J
                                        JOIN invoice_payments IP ON
                                            J.entity_id = IP.id AND J.entity_type = 'invoice_payment_currency_rate_difference'
                                        WHERE
                                            IP.currency_code =(
                                            SELECT
                                                settings.value
                                            FROM
                                                settings
                                            WHERE
                                                settings.key = 'journals_local_currency_code'
                                        );",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/owner/fake/fix_exchange_journal_with_same_currency",
                    "report" => [
                        "query" => "SELECT
                                            *
                                        FROM
                                            journals J
                                        JOIN invoice_payments IP ON
                                            J.entity_id = IP.id AND J.entity_type = 'invoice_payment_currency_rate_difference'
                                        WHERE
                                            IP.currency_code =(
                                            SELECT
                                                settings.value
                                            FROM
                                                settings
                                            WHERE
                                                settings.key = 'journals_local_currency_code'
                                        );",
                        "fields" => [
                            'id',
                            'date',
                            'no',
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ],
                    [
                        "title" => "Assets with status depreciated and asset_current_value = 0 and has asset_operation type_sell",
                        "check_query" => "SELECT assets.id FROM assets WHERE asset_status = 3 AND asset_current_value < 0.000001 AND asset_current_value >= 0 AND id IN (SELECT asset_id FROM asset_operations WHERE type = 1);",
                        "fix_strategy" => "auto",
                        "fix_type" => "url",
                        "fix_url" => "/owner/fake/fix_depreciated_assets_with_zero_value",
                    ]
                ],
                "purchase_order_wrong_requisition_items_value" => [
                    "title" => "purchase invoices which have requisition items
                     have different price then its stock transactions 
                     causes imbalance in purchases account in purchase order journal and its related requisitions",
                    "check_query" => "select RI.unit_price,ST.price, RI.quantity,R.order_id, RI.product_id,R.id from requisition_items RI
                                        join requisitions R on R.id = RI.requisition_id 
                                        join stock_transactions ST on ST.product_id = RI.product_id and ST.source_type = 106  and ST.order_id = R.id and R.order_type = 6 where abs(RI.unit_price - ST.price) > 0.01;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/owner/fake/fix_requisition_item_wrong_unit_price",
                    "report" => [
                        "query" => "select RI.unit_price,ST.price, RI.quantity,R.order_id, R.id from requisition_items RI
                                        join requisitions R on R.id = RI.requisition_id 
                                        join stock_transactions ST on ST.product_id = RI.product_id and ST.source_type = 106  and ST.order_id = R.id and R.order_type = 6 where abs(RI.unit_price - ST.price) > 0.01;",
                        "fields" => [
                            'id',
                            'unit_price',
                            'price',
                            'quantity'
                        ],
                        "view_link" => "/owner/requisitions/view/%id%"
                    ]
                ],
                "requisition_journal_not_having_expense_journal_transaction" => [
                    "title" => "requisition has expense distribution but not reflected in journals",
                    "check_query" => "SELECT R.* FROM requisitions R 
                                        join expense_distributions ED on ED.requisition_id = R.id
                                        join journals J on R.id = J.entity_id and J.entity_type = 'requisition'
                                        where J.id not in (select DISTINCT journal_id from journal_transactions where subkey like '%account_expense_distribution_journal%' or subkey like '%account_%');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/owner/fake/fix_generate_expense_distribution_for_requisitions",
                    "report" => [
                        "query" => "SELECT R.* FROM requisitions R 
                                        join expense_distributions ED on ED.requisition_id = R.id
                                        join journals J on R.id = J.entity_id and J.entity_type = 'requisition'
                                        where J.id not in (select DISTINCT journal_id from journal_transactions where subkey like '%account_expense_distribution_journal%' or subkey like '%account_%');",
                        "fields" => [
                            'id',
                            'date',
                            'number',
                            'branch_id'
                        ],
                        "view_link" => "/owner/requisitions/view/%id%"
                    ]
                ],
                "invoice_payment_without_invoice_but_have_diff_journal" => [
                    "title" => "invoice payments without invoice but have currency difference in journal",
                    "check_query" => "SELECT
                                            *
                                        FROM
                                            `invoice_payments`
                                        WHERE
                                            invoice_id IS NULL AND id IN(
                                            SELECT
                                                entity_id
                                            FROM
                                                journals
                                            WHERE
                                                entity_type = 'invoice_payment_currency_rate_difference'
                                        );",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/owner/fake/fix_invoice_payments_without_invoice_but_have_diff_journal",
                    "report" => [
                        "query" => "SELECT
                                        *
                                    FROM
                                        `invoice_payments`
                                    WHERE
                                        invoice_id IS NULL AND id IN(
                                        SELECT
                                            entity_id
                                        FROM
                                            journals
                                        WHERE
                                            entity_type = 'invoice_payment_currency_rate_difference'
                                    );",
                        "fields" => [
                            'id',
                            'date',
                            'notes',
                        ],
                        "view_link" => "/owner/invoice_payments/view/%id%"
                    ]
                ],
                "purchase_order_supplier_transaction_different_then_PO_total" => [
                    "title" => "purchase invoices which its total is different then supplier transaction total",
                    "check_query" => "select * from purchase_orders PO
                                        join journals J on J.entity_id = PO.id and J.entity_type in ('purchase_order', 'purchase_refund')
                                        join journal_transactions JT on JT.journal_id = J.id
                                        where JT.subkey = 'supplier' and abs(JT.currency_credit + JT.currency_debit - PO.summary_total) > 0.01;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/owner/fake/fix_purchase_order_supplier_transaction",
                    "report" => [
                        "query" => "select * from purchase_orders PO
                                        join journals J on J.entity_id = PO.id and J.entity_type in ('purchase_order', 'purchase_refund')
                                        join journal_transactions JT on JT.journal_id = J.id
                                        where JT.subkey = 'supplier' and abs(JT.currency_credit + JT.currency_debit - PO.summary_total) > 0.01;",
                        "fields" => [
                            'id',
                            'no',
                            'date',
                        ],
                        "view_link" => "/owner/purchase_invoices/view/%id%"
                    ]
                ],
                "purchase_order_draft_with_journals" => [
                    "title" => "draft purchase invoices which have journals",
                    "check_query" => "select * from purchase_orders PO
                                        join journals J on J.entity_id = PO.id and J.entity_type in ('purchase_order', 'purchase_refund')
                                        where PO.draft = 1;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/owner/fake/fix_purchase_order_draft_journal",
                    "report" => [
                        "query" => "select * from purchase_orders PO
                                        join journals J on J.entity_id = PO.id and J.entity_type in ('purchase_order', 'purchase_refund')
                                        where PO.draft = 1;",
                        "fields" => [
                            'id',
                            'no',
                            'date',
                        ],
                        "view_link" => "/owner/purchase_invoices/view/%id%"
                    ]
                ],
                "purchase_order_payments_with_wrong_journal_account_in_journal" => [
                    "title" => "purchase order payments with supplier different then supplier journal",
                    "check_query" => "select POP.id from suppliers S
                                        join journal_accounts JA on JA.entity_id = S.id and JA.entity_type = 'supplier'
                                        join purchase_orders PO on PO.supplier_id = S.id
                                        join purchase_order_payments POP on POP.purchase_order_id = PO.id
                                        join journals J on J.entity_id = POP.id and J.entity_type = 'purchase_order_payment'
                                        join journal_transactions JT on JT.journal_id = J.id
                                        where JT.subkey = 'supplier' and JT.journal_account_id != JA.id;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/owner/fake/fix_purchase_order_payments_with_wrong_supplier_transaction",
                    "report" => [
                        "query" => "select PO.* from suppliers S
                                        join journal_accounts JA on JA.entity_id = S.id and JA.entity_type = 'supplier'
                                        join purchase_orders PO on PO.supplier_id = S.id
                                        join purchase_order_payments POP on POP.purchase_order_id = PO.id
                                        join journals J on J.entity_id = POP.id and J.entity_type = 'purchase_order_payment'
                                        join journal_transactions JT on JT.journal_id = J.id
                                        where JT.subkey = 'supplier' and JT.journal_account_id != JA.id;",
                        "fields" => [
                            'id',
                            'no',
                            'date',
                        ],
                        "view_link" => "/owner/purchase_order_payments/view/%id%"
                    ]
                ],
                "possible_invoice_duplicates" => [
                    "title" => "Possible invoice duplications",
                    "check_query" => "SELECT GROUP_CONCAT(id), summary_total, client_id, temp_items.* FROM invoices INNER JOIN( SELECT invoice_id, COUNT(id) AS items_count, GROUP_CONCAT(quantity) AS items_quantities, GROUP_CONCAT(item) AS items FROM `invoice_items` GROUP BY invoice_id ) AS temp_items ON invoices.id = temp_items.invoice_id WHERE TYPE = 0 GROUP BY temp_items.items_count, temp_items.items_quantities, temp_items.items, client_id, summary_total, DATE_FORMAT(created, '%d-%m-%Y-%H') HAVING COUNT(*) > 1 ORDER BY `GROUP_CONCAT(id)` DESC;",
                    "report" => [
                        "query" => "SELECT GROUP_CONCAT(id), summary_total, client_id, temp_items.* FROM invoices INNER JOIN( SELECT invoice_id, COUNT(id) AS items_count, GROUP_CONCAT(quantity) AS items_quantities, GROUP_CONCAT(item) AS items FROM `invoice_items` GROUP BY invoice_id ) AS temp_items ON invoices.id = temp_items.invoice_id WHERE TYPE = 0 GROUP BY temp_items.items_count, temp_items.items_quantities, temp_items.items, client_id, summary_total, DATE_FORMAT(created, '%d-%m-%Y-%H') HAVING COUNT(*) > 1 ORDER BY `GROUP_CONCAT(id)` DESC;",
                        "fields" => [
                            'client_id',
                            'summary_total'
                        ],
                        "view_link" => "/owner/invoices/index?client_id=%client_id%&summary_total_from=%summary_total%&summary_total_to=%summary_total%"
                    ]
                ],
                "journals_with_wrong_date" => [
                    "title" => "Journals with wrong date",
                    "check_query" => "SELECT * FROM `journals` WHERE `date` = '0000-00-00' and is_automatic = 1;",
//                    "fix_strategy" => "auto",
//                    "fix_type" => "url",
//                    "fix_url" =>  "/owner/fake/fix_journals_with_wrong_date",
                    "view_link" => "/owner/journals/view/%id%",
                    "report" => [
                        "query" => "SELECT * FROM `journals` WHERE `date` = '0000-00-00' and is_automatic = 1;",
                        "fields" => [
                            'id',
                            'date',
                            'description'
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
             "requisition_expense_distribution_different_currency" => [
                    "title" => "update requisition which have expense distribution with different currency journal",
                    "check_query" => "SELECT R.* FROM expense_distributions ED join expense_distribution_items EDI on ED.id = EDI.expense_distribution_id join requisitions R on R.id = ED.requisition_id where EDI.expense_currency != R.currency_code group by ED.id;",
                     "fix_strategy" => "auto",
                     "fix_type" => "url",
                     "fix_url" =>  "/owner/fake/fix_requisitions_expense_distributions_journals_with_different_currencies",
                     "report" => [
                            "query" => "SELECT R.* FROM expense_distributions ED join expense_distribution_items EDI on ED.id = EDI.expense_distribution_id join requisitions R on R.id = ED.requisition_id where EDI.expense_currency != R.currency_code group by ED.id;",
                            "fields" => [
                                'id',
                                'date',
                                'number'
                            ],
                            "view_link" => "/owner/requisitions/view/%id%"
                        ]
                ],

                "different_invoice_requisitions_stock_transaction" => [
                    "title" => "Invoice Different Branch than requisitions, stock transaction ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/fake/fix_wrong_branches",
                    "check_query" => "	select count(R.id) from `requisitions` R LEFT JOIN invoices I ON (I.id = R.order_id AND R.order_type in (3,4,5))	WHERE R.branch_id <> I.branch_id
UNION
	select  count(R.id)  from  `requisitions` R
		LEFT JOIN purchase_orders PO ON (PO.id = R.order_id AND R.order_type in (6,7))
		WHERE R.branch_id <> PO.branch_id
        
        UNION

	select count(ST.id) from  `stock_transactions` ST
		LEFT JOIN requisitions R ON (R.id = ST.order_id AND ST.source_type IN (103,104,105,106,107))
		WHERE ST.branch_id <> R.branch_id
        
        UNION

select count(ST.id) FROM  `stock_transactions` ST
		LEFT JOIN invoices I ON (I.id = ST.order_id AND ST.source_type IN (2,4,6))
		WHERE ST.branch_id <> I.branch_id
     UNION
	select count(ST.id) from  `stock_transactions` ST
		LEFT JOIN purchase_orders PO ON (PO.id = ST.order_id AND ST.source_type IN (3,7))

		WHERE ST.branch_id <> PO.branch_id",
                    "report" => [
                        "query" => "	select count(R.id) from `requisitions` R LEFT JOIN invoices I ON (I.id = R.order_id AND R.order_type in (3,4,5))	WHERE R.branch_id <> I.branch_id
UNION
	select  count(R.id)  from  `requisitions` R
		LEFT JOIN purchase_orders PO ON (PO.id = R.order_id AND R.order_type in (6,7))
		WHERE R.branch_id <> PO.branch_id
        
        UNION

	select count(ST.id) from  `stock_transactions` ST
		LEFT JOIN requisitions R ON (R.id = ST.order_id AND ST.source_type IN (103,104,105,106,107))
		WHERE ST.branch_id <> R.branch_id
        
        UNION

select count(ST.id) FROM  `stock_transactions` ST
		LEFT JOIN invoices I ON (I.id = ST.order_id AND ST.source_type IN (2,4,6))
		WHERE ST.branch_id <> I.branch_id
     UNION
	select count(ST.id) from  `stock_transactions` ST
		LEFT JOIN purchase_orders PO ON (PO.id = ST.order_id AND ST.source_type IN (3,7))

		WHERE ST.branch_id <> PO.branch_id",
                        "fields" => [
                            'count(R.id)',
                        ],
                        "view_link" => "/fake/fix_wrong_branches",
                    ],

                ]
                ,
                "invoice_payments_journals_without_staff_petty_cash_transactions" => [
                    "title" => "gets invoice payments journals without staff petty cash journal transaction ",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" =>  "/owner/fake/fix_invoice_payment_staff_petty_cash_journal_transactions",
                    "check_query" => "select J.*
                from journal_transactions JT
                         join journals J
                              on J.id = JT.journal_id and J.entity_type = 'invoice_payment' and JT.subkey = 'staff_petty_cash' and
                                 JT.journal_account_id not in (select id from journal_accounts where entity_type = 'staff_petty_cash') and J.branch_id = $branch_id;
                ",
                    "report" => [
                        "query" => "select J.*
                from journal_transactions JT
                         join journals J
                              on J.id = JT.journal_id and J.entity_type = 'invoice_payment' and JT.subkey = 'staff_petty_cash' and
                                 JT.journal_account_id not in (select id from journal_accounts where entity_type = 'staff_petty_cash') and J.branch_id = $branch_id;
                ",
                        "fields" => [
                            'id',
                        ],
                        "view_link" => "/owner/journals/view/%id%",
                    ],

                ],
//                "requisition_journals_total_not_equal" => [
//                    "title" => "Requisition which have journals but its total is different then requisition total",
//                    "check_query" => "SELECT R.*
//                                        FROM journals J
//                                                 join requisitions R on R.id = J.entity_id and J.entity_type = 'requisition'
//                                        where abs(J.total_debit - (select sum(quantity*unit_price) from requisition_items where requisition_id = R.id) -
//                                                  COALESCE((select sum_expense_amount from expense_distributions where requisition_id = R.id), 0)) > 0.01;
//",
//                    "fix_strategy" => "auto",
//                    "fix_type" => "url",
//                    "fix_url" => "/owner/fake/equalize_requisition_journals_total",
//                    "report" => [
//                        "query" => "SELECT R.*
//                                    FROM journals J
//                                             join requisitions R on R.id = J.entity_id and J.entity_type = 'requisition'
//                                    where abs(J.total_debit - (select sum(quantity*unit_price) from requisition_items where requisition_id = R.id) -
//                                              COALESCE((select sum_expense_amount from expense_distributions where requisition_id = R.id), 0)) > 0.01;
//",
//                        "fields" => [
//                            'id',
//                            'date'
//                        ],
//                        "view_link" => "/owner/requisitions/view/%id%"
//                    ]
//                ],

                "invoice_payments_without_journals" => [
                    "title" => "Invoice Payments without journals",
                    "check_query" => "select IP.*
                                        from invoice_payments IP
                                                 join invoices I on I.id = IP.invoice_id
                                        where (I.pos_shift_id IS NUlL OR I.pos_shift_id in (select id from pos_shifts where is_calculated_per_invoice = 1))
                                          AND IP.id not in (select entity_id from journals where entity_type = 'invoice_payment')
                                          and IP.payment_method != 'client_credit' and IP.amount > 0 and status = 1
                                        ",
                    "report" => [
                        "query" => "select IP.*
                                        from invoice_payments IP
                                                 join invoices I on I.id = IP.invoice_id
                                        where (I.pos_shift_id IS NUlL OR I.pos_shift_id in (select id from pos_shifts where is_calculated_per_invoice = 1))
                                          AND IP.id not in (select entity_id from journals where entity_type = 'invoice_payment')
                                          and IP.payment_method != 'client_credit' and IP.amount > 0 and status = 1;",
                        "fields" => [
                            'id',
                            'invoice_id'
                        ],
                        "view_link" => "/owner/invoice_payments/view/%id%"
                    ],
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoice_payments_without_journals",
                ],
                "removed_purchase_orders_journal" => [
                    "title" => "Removed purchase order journals",
                    "check_query" => "select * from journals where entity_type in ('purchase_order', 'purchase_refund') and entity_id not in (select id from purchase_orders);",
                    "report" => [
                        "query" => "select * from journals where entity_type in ('purchase_order', 'purchase_refund') and entity_id not in (select id from purchase_orders);",
                        "fields" => [
                            'id',
                            'date'
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ],
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/remove_deleted_purchase_orders_journals",
                ],
                "purchase_invoices_with_taxes_but_no_records_in_taxes_table" => [
                    "title" => "Purchase Invoices with taxes but has no records in purchase order taxes",
                    "check_query" => "select *
                                        from purchase_orders
                                        where id in (select purchase_order_id from purchase_order_items where tax1 is not null or tax2 is not null)
                                        and id not in (select purchase_order_id from purchase_order_taxes);",
                    "report" => [
                        "query" => "select *
                                        from purchase_orders
                                        where id in (select purchase_order_id from purchase_order_items where tax1 is not null or tax2 is not null)
                                        and id not in (select purchase_order_id from purchase_order_taxes);",
                        "fields" => [
                            'id',
                            'no'
                        ],
                        "view_link" => "/owner/purchase_orders/view/%id%"
                    ],
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/update_purchase_order_taxes",
                ],
                "accounting_non_existent" => [
                    "title" => "Accounting Accounts not exists",
                    "check_query" => "SELECT T.number from (select 1 as number) T where 4 > (SELECT count(*) from journal_cats where journal_cat_id = -1);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/restore_chart_accounts",
                ],
                "purchase_order_journals_wrong_sales_cost_transactions" => [
                    "title" => "Purchase Order journals which dont have service nor untracked products and has sales cost transaction in its related journal",
                    "check_query" => "SELECT *
                                        FROM `journals` J
                                                 join journal_transactions JT on JT.journal_id = J.id and JT.subkey = 'sales_cost'
                                        WHERE entity_type = 'purchase_order'
                                          and entity_id NOT IN (select DISTINCT purchase_order_id
                                                                from purchase_order_items POI
                                                                         left join products P on P.id = POI.product_id
                                                                where P.track_stock = 0
                                                                   OR P.track_stock IS NULL
                                                                   OR P.type = 2 or POI.product_id is null);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_purchase_order_journals_with_wrong_sales_cost",
                ],
                "invoices_with_taxes_but_journals_have_no_taxes_transactions" => [
                    "title" => "Invoices with taxes but journals have no taxes transactions",
                    "check_query" => "select I.*
                                        from journals J
                                                 join invoices I on I.id = J.entity_id and J.entity_type in ('invoice', 'refund_receipt', 'credit_note')
                                                 join invoice_taxes IT on IT.invoice_id = I.id
                                        where IT.invoice_value > 0
                                          and J.id not in (select DISTINCT(journal_id)
                                                           from journal_transactions
                                                           where journal_account_id in
                                                                 (select id from journal_accounts where entity_type in ('income_tax', 'outcome_tax')))",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoices_without_tax_transactions",
                    "report" => [
                        "query" => "select I.*
                                        from journals J
                                                 join invoices I on I.id = J.entity_id and J.entity_type in ('invoice', 'refund_receipt', 'credit_note')
                                                 join invoice_taxes IT on IT.invoice_id = I.id
                                        where IT.invoice_value > 0
                                          and J.id not in (select DISTINCT(journal_id)
                                                           from journal_transactions
                                                           where journal_account_id in
                                                                 (select id from journal_accounts where entity_type in ('income_tax', 'outcome_tax')));",
                        "fields" => [
                            "id",
                            "date",
                            "no",
                        ],
                        "view_link" => "/owner/invoices/view/%id%"
                    ]
                ],
                "purchase_refund_journals_wrong_sales_cost_transactions" => [
                    "title" => "Purchase refund journals which dont have service nor untracked products and has sales cost transaction in its related journal",
                    "check_query" => "SELECT *
                                        FROM `journals` J
                                                 join journal_transactions JT on JT.journal_id = J.id and JT.subkey = 'sales_cost'
                                        WHERE entity_type = 'purchase_refund'
                                          and entity_id NOT IN (select DISTINCT purchase_order_id
                                                                from purchase_order_items POI
                                                                         left join products P on P.id = POI.product_id
                                                                where P.track_stock = 0
                                                                   OR P.track_stock IS NULL
                                                                   OR P.type = 2 OR POI.product_id is null);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_purchase_refund_journals_with_wrong_sales_cost",
                ],
                "fix_assets_deprecation_branch_id_diffrent_from_journal_id" => [
                    "title" => "Fix Journals branch id different from asset branch id ",
                    "check_query" => "SELECT journals.*,assets.id as asset_id, assets.branch_id as asset_branch_id,asset_deprecations.id as asset_dep_id FROM `journals` inner join asset_deprecations on journals.entity_id = asset_deprecations.id and entity_type = 'asset_deprecation' inner join assets on assets.id = asset_deprecations.asset_id where journals.branch_id != assets.branch_id;",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "UPDATE  journals inner join asset_deprecations on journals.entity_id = asset_deprecations.id and entity_type = 'asset_deprecation' inner join assets on assets.id = asset_deprecations.asset_id SET journals.branch_id = assets.branch_id where journals.branch_id != assets.branch_id OR journals.branch_id iS NULL;"
                    ],
                    "report" => [
                        "query" => "SELECT journals.*,assets.id as asset_id, assets.branch_id as asset_branch_id,asset_deprecations.id as asset_dep_id FROM `journals` inner join asset_deprecations on journals.entity_id = asset_deprecations.id and entity_type = 'asset_deprecation' inner join assets on assets.id = asset_deprecations.asset_id where journals.branch_id != assets.branch_id;",
                        "fields" => [
                            "id",
                            "asset_id",
                            "branch_id",
                            "asset_dep_id",
                            "asset_branch_id"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "fix_assets_with_infinite_currency_unit_depreciation " => [
                    "title" => "Fix assets that keep losing value by one unit of money.",
                    "check_query" => "SELECT 'row' FROM DUAL WHERE exists(SELECT `AssetDeprecation`.`id` FROM `asset_deprecations` AS `AssetDeprecation`  LEFT JOIN `assets` AS `Asset` ON (`AssetDeprecation`.`asset_id` = `Asset`.`id`) WHERE `AssetDeprecation`.`cost` < 0.001 GROUP BY `AssetDeprecation`.`asset_id` ORDER BY `AssetDeprecation`.`id` ASC) or EXISTS(SELECT `Asset`.`id` FROM `assets` AS `Asset` WHERE `Asset`.`asset_current_value` < -0.001);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/delete_invalid_deprecations",
                ],
                "assets_total_deprecations_not_balanced_with_asset" => [
                    "title" => "Total Deprecations != equal to the amount saved in the asset",
                    "check_query" => "select A.id, sum(AD.cost),A.total_deprecation, abs(sum(AD.cost) - A.total_deprecation) from asset_deprecations AD
                                        join assets A on A.id = AD.asset_id
                                        group by asset_id 
                                        having abs(sum(AD.cost) - A.total_deprecation) > 0.01;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_assets_with_wrong_deprecation_total",
                    "report" => [
                        "query" => "select A.* from asset_deprecations AD
                                        join assets A on A.id = AD.asset_id
                                        group by asset_id 
                                        having abs(sum(AD.cost) - A.total_deprecation) > 0.01;",
                        "fields" => [
                            "id",
                            "name",
                            "date",
                        ],
                        "view_link" => "/owner/assets/view/%id%"
                    ]
                ],

                "fix_purchase_order_journal_with_wrong_discount_received_transaction" => [
                    "title" => "Fix Purchase Order Journals which have discount received transaction but The Main Discount is 0",
                    "check_query" => "select * from purchase_orders where summary_discount = 0 and id in (select DISTINCT entity_id from journals J join journal_transactions JT on JT.journal_id = J.id where entity_type = 'purchase_order' and JT.subkey = 'discount_received');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    'fix_url' => '/owner/fake/fix_purchase_invoice_journals_with_wrong_discount_received',
                    "report" => [
                        "query" => "select * from purchase_orders where summary_discount = 0 and id in (select DISTINCT entity_id from journals J join journal_transactions JT on JT.journal_id = J.id where entity_type = 'purchase_order' and JT.subkey = 'discount_received');",
                        "fields" => [
                            "id",
                            "no",
                            "date",
                        ],
                        "view_link" => "/owner/purchase_invoices/view/%id%"
                    ]
                ],
                "journals_with_wrong_fiscal_year" => [
                    "title" => "journals with wrong fiscal year",
                    "check_query" => "select *
                                            from journals J
                                                     join financial_years FY on FY.is_closed = 1 and FY.start_date <= J.date and FY.end_date >= J.date and
                                                                                J.financial_year_id != FY.id and FY.branch_id = J.branch_id
                                            where J.entity_type not in ('year_closing_balance', 'year_opening_balance');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/update_journals_without_fiscal_year",
                    "report" => [
                        "query" => "select J.*
                                        from journals J
                                                 join financial_years FY on FY.is_closed = 1 and FY.start_date <= J.date and FY.end_date >= J.date and
                                                                            J.financial_year_id != FY.id and FY.branch_id = J.branch_id
                                        where J.entity_type not in ('year_closing_balance', 'year_opening_balance');",
                        "fields" => [
                            "id",
                            "date",
                            "description"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "journals_with_wrong_fiscal_year_2" => [
                    "title" => "journal is out of the range of the fiscal year but with its id",
                    "check_query" => "SELECT * FROM `journals` J 
                                        join financial_years FY on FY.id = J.financial_year_id
                                        where J.date <= FY.start_date OR J.date >= FY.end_date;",
                    "report" => [
                        "query" => "SELECT J.*,FY.start_date, FY.end_date FROM `journals` J 
                                        join financial_years FY on FY.id = J.financial_year_id
                                        where J.date <= FY.start_date OR J.date >= FY.end_date;",
                        "fields" => [
                            "id",
                            "date",
                            "description",
                            "start_date",
                            "end_date"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "journals_with_deleted_fiscal_year" => [
                    "title" => "journals with deleted fiscal year",
                    "check_query" => "select *
                                            from journals J
                                                     
                                            where J.financial_year_id not in (select id from financial_years) and J.financial_year_id != 0 and financial_year_id != -1;",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update journals set financial_year_id = 0 where financial_year_id not in (select id from financial_years) and financial_year_id != 0 and financial_year_id != -1;"
                    ],
                    "report" => [
                        "query" => "select *
                                            from journals J
                                                     
                                            where J.financial_year_id not in (select id from financial_years) and J.financial_year_id != 0 and financial_year_id != -1;",
                        "fields" => [
                            "id",
                            "date",
                            "description",
                            "financial_year_id"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "non_balanced_journal" => [
                    "title" => "Non-Balanced Journals (its ok if its fiscal year journals but it may require us to open and close the year)",
                    "check_query" => "SELECT journal_id, SUM( credit ) , SUM( debit )FROM journal_transactions GROUP BY journal_id HAVING ABS( SUM( credit ) - SUM( debit ) ) > 0.00001;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/update_unbalanced_auto_journals",
                    "report" => [
                        "query" => "select * from journals where id in (SELECT journal_id FROM journal_transactions GROUP BY journal_id HAVING ABS( SUM( credit ) - SUM( debit ) ) > 0.00001);",
                        "fields" => [
                            "id",
                            "date",
                            "description"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "journals_different_branch_id_purchase_order" => [
                    "title" => "Journals with different branch id than its related purchase order",
                    "check_query" => "SELECT * FROM purchase_orders PO join journals J on J.entity_id = PO.id and J.entity_type = 'purchase_order' where PO.branch_id != J.branch_id OR J.branch_id iS NULL;",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "UPDATE purchase_orders PO join journals J on J.entity_id = PO.id and J.entity_type = 'purchase_order' SET J.branch_id = PO.branch_id where PO.branch_id != J.branch_id OR J.branch_id iS NULL;"
                    ],
                    "report" => [
                        "query" => "SELECT J.* FROM purchase_orders PO join journals J on J.entity_id = PO.id and J.entity_type = 'purchase_order' where PO.branch_id != J.branch_id OR J.branch_id iS NULL;",
                        "fields" => [
                            "id",
                            "date",
                            "description"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],

                "journals_without_transaction_but_has_journal_transaction_log" => [
                    "title" => "journals without transaction but has journal transaction log if not fixed try change branch",
                    "check_query" => "SELECT `Journal`.* FROM `journals` AS `Journal` WHERE `Journal`.`id` NOT IN( SELECT  journal_id FROM  journal_transactions);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/fake/fix_empty_journals_transaction__from_journal_transaction_logs",
                    "report" => [
                        "query" => "SELECT `Journal`.* FROM `journals` AS `Journal` WHERE `Journal`.`id` NOT IN( SELECT  journal_id FROM  journal_transactions);",
                        "fields" => [
                            "id",
                            "date",
                            "description"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "orphan_journal_transactions" => [
                    "title" => "Orphan journal transactions",
                    "check_query" => "Select * from journal_transactions where journal_id not in (select id from journals);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "delete FROM `journal_transactions` WHERE journal_id  not in (select id from journals)"
                    ]
                ],
                "non_matching_journal_transactions" => [
                    "title" => "Journal Transactions total not equal journal total if  not fixed try change branch",
                    "check_query" => "select J.id, abs(sum(JT.debit) - J.total_debit), sum(JT.debit),J.total_debit,sum(JT.credit),J.total_credit from journals J left join journal_transactions JT on JT.journal_id = J.id group by J.id having abs(sum(JT.credit) - J.total_credit) > 0.01 or abs(sum(JT.debit) - J.total_debit) > 0.01;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_non_matching_journal_total",
                    "report" => [
                        "query" => "select J.id,J.description, abs(sum(JT.debit) - J.total_debit), sum(JT.debit),J.total_debit,sum(JT.credit),J.total_credit from journals J left join journal_transactions JT on JT.journal_id = J.id group by J.id having abs(sum(JT.credit) - J.total_credit) > 0.01 or abs(sum(JT.debit) - J.total_debit) > 0.01;",
                        "fields" => [
                            "id",
                            "description"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "journal_branch_different_than_invoice_branch" => [
                    "title" => "Journal branch different then invoice branch",
                    "check_query" => "select J.* from invoices I join journals J on I.id = J.entity_id and J.entity_type in ('invoice', 'credit_note', 'refund', 'invoice_sales_cost') where I.branch_id != J.branch_id OR J.branch_id is null ORDER BY `J`.`created` DESC;",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update journals J join invoices I on I.id = J.entity_id and J.entity_type in ('invoice','credit_note', 'refund', 'invoice_sales_cost') set J.branch_id = I.branch_id where (I.branch_id != J.branch_id OR J.branch_id IS NULL);"
                    ]
                ],
                "fix_sales_cost_journals_staff_id" => [
                    "title" => "Fix Sales Cost Journals staff id different than invoice staff id",
                    "check_query" => "select *
                                        from journals J
                                                 join invoices I on I.id = J.entity_id and J.entity_type = 'invoice_sales_cost'
                                        where J.entity_type = 'invoice_sales_cost'
                                          and J.staff_id != I.staff_id;
",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update journals J
                                join invoices I
                            on I.id = J.entity_id and J.entity_type = 'invoice_sales_cost'
                                set J.staff_id = I.staff_id
                            where J.entity_type = 'invoice_sales_cost' and J.staff_id != I.staff_id;"
                    ]
                ],
                "invoice_payment_journal_branch_different_than_invoice_payment_journal" => [
                    "title" => "Invoice Payment branch different then invoice branch",
                    "check_query" => "select J.* from invoice_payments IP join journals J on IP.id = J.entity_id and J.entity_type in ('invoice_payment') where (IP.branch_id != J.branch_id OR J.branch_id is null )",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update journals J join invoice_payments IP  on IP.id = J.entity_id and J.entity_type in ('invoice_payment') set J.branch_id = IP.branch_id where (IP.branch_id != J.branch_id OR J.branch_id IS NULL);"
                    ]
                ],
                "purchase_order_journal_branch_different_than_purchase_order_journal" => [
                    "title" => "Purchase Order branch different then journal branch",
                    "check_query" => "select * from purchase_orders PO
                                            JOIN journals J on PO.id = J.entity_id and J.entity_type in ('purchase_order', 'purchase_refund')
                                            and (J.branch_id != PO.branch_id or J.branch_id IS NULL);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update journals J join invoice_payments IP  on IP.id = J.entity_id and J.entity_type in ('invoice_payment') set J.branch_id = IP.branch_id where (IP.branch_id != J.branch_id OR J.branch_id IS NULL);"
                    ]
                ],
                "expense_journal_branch_different_than_expense_journal" => [
                    "title" => "Expense Journal branch different then Expense branch",
                    "check_query" => "select * from expenses E JOIN journals J on E.id = J.entity_id and J.entity_type in ('expense', 'income') and (J.branch_id != E.branch_id or J.branch_id IS NULL);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update journals J join expenses E  on E.id = J.entity_id and J.entity_type in ('expense') set J.branch_id = E.branch_id where (E.branch_id != J.branch_id OR J.branch_id IS NULL);"
                    ]
                ],
                "purchase_order_payment_journal_branch_different_than_purchase_order_payment_journal" => [
                    "title" => "Purchase Order Payment branch different then Purchase Order Payment branch",
                    "check_query" => "select * from purchase_order_payments POP
                                        JOIN journals J on POP.id = J.entity_id and J.entity_type in ('purchase_order_payment')
                                        and (J.branch_id != POP.branch_id or J.branch_id IS NULL);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update journals J join purchase_order_payments POP  on POP.id = J.entity_id and J.entity_type in ('purchase_order_payment') set J.branch_id = POP.branch_id where (POP.branch_id != J.branch_id OR J.branch_id IS NULL);"
                    ]
                ],
                "requisition_journal_branch_different_requisition_journal" => [
                    "title" => "Requisition branch different then journal branch",
                    "check_query" => "select * from requisitions R
                                        JOIN journals J on R.id = J.entity_id and J.entity_type in ('requisition')
                                        and (J.branch_id != R.branch_id or J.branch_id IS NULL);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update journals J join requisitions R  on R.id = J.entity_id and J.entity_type in ('requisition') set J.branch_id = R.branch_id where (R.branch_id != J.branch_id OR J.branch_id IS NULL);"
                    ]
                ],
                "stock_transaction_journal_branch_different_stock_transaction_journal" => [
                    "title" => "stock_transaction branch different then journal branch",
                    "check_query" => "select * from stock_transactions ST JOIN journals J on ST.id = J.entity_id and J.entity_type in ('stock_transaction') and (J.branch_id != ST.branch_id or J.branch_id IS NULL);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "update journals J join stock_transactions ST  on ST.id = J.entity_id and J.entity_type in ('stock_transaction') set J.branch_id = ST.branch_id where (ST.branch_id != J.branch_id OR J.branch_id IS NULL);"
                    ]
                ],
                "none_deleted_invoice_journals" => [
                    "title" => "Invoices with no journals",
                    "check_query" => "SELECT * FROM `journals` WHERE entity_type='invoice' and entity_id not in (select id from invoices)",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "delete FROM `journals` WHERE entity_type='invoice' and entity_id  not in (select id from invoices)",
                        "delete FROM `journal_transactions` WHERE journal_id  not in (select id from journals)"
                    ]
                ],
                "journal_transactions_with_wrong_account" => [
                    "title" => "Journal transactions with deleted journal accounts",
                    "check_query" => "SELECT * FROM `journal_transactions` where journal_account_id not in (select id from journal_accounts) limit 10000;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_auto_journal_transactions_with_wrong_journal_accounts",
                    "report" => [
                        "query" => "SELECT * FROM `journal_transactions` where journal_account_id not in (select id from journal_accounts) limit 10000;",
                        "fields" => [
                            "journal_id",
                            "description"
                        ],
                        "view_link" => "/owner/journals/view/%journal_id%"
                    ]
                ],
                "journal_accounts_with_wrong_category" => [
                    "title" => "journal accounts with wrong parent",
                    "check_query" => "SELECT * FROM journal_accounts where journal_cat_id not in (select id from journal_cats) and journal_cat_id != -1;",
                    "report" => [
                        "query" => "SELECT * FROM journal_accounts where journal_cat_id not in (select id from journal_cats) and journal_cat_id != -1;",
                        "fields" => [
                            "id",
                            "name",
                            "code",
                        ],
                        "view_link" => "/v2/owner/accounting/%id%/account-details"
                    ]
                ],
                "non_linked_journal_cats" => [
                    "title" => "Journal cats with wrong parents",
                    "check_query" => "select * from journal_cats where journal_cat_id != -1 and journal_cat_id not in (select id from journal_cats);",
                    "fix_strategy" => "manual",
                    "report" => [
                        "query" => "select * from journal_cats where journal_cat_id != -1 and journal_cat_id not in (select id from journal_cats);",
                        "fields" => [
                            "id",
                            "name",
                            "code",
                            "journal_cat_id",
                            "parent_cat_ids"
                        ],
                        "view_link" => "/v2/owner/chart-of-accounts/cats/%id%"
                    ]
                ],
                "non_deleted_invoice_payments_journals" => [
                    "title" => "Journals for deleted invoice payments",
                    "check_query" => "SELECT * FROM `journals` WHERE entity_type='invoice_payment' and entity_id not in (select id from invoice_payments);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "delete FROM `journals` WHERE entity_type='invoice_payment' and entity_id  not in (select id from invoice_payments)",
                        "delete FROM `journal_transactions` WHERE journal_id  not in (select id from journals)"
                    ]
                ],
                "non_deleted_invoice_sales_cost_journals" => [
                    "title" => "Journals for deleted invoice sales cost",
                    "check_query" => "SELECT * FROM `journals` WHERE entity_type='invoice_sales_cost' and entity_id not in (select id from invoices);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "delete FROM `journals` WHERE entity_type='invoice_sales_cost' and entity_id  not in (select id from invoices);",
                        "delete FROM `journal_transactions` WHERE journal_id  not in (select id from journals);"
                    ]
                ],
                "cost_center_transactions_for_deleted_journal_transactions" => [
                    "title" => "Cost center transactions for deleted journal transactions",
                    "check_query" => "SELECT * FROM `cost_center_transactions` WHERE journal_transaction_id not in (select id from journal_transactions);",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "DELETE FROM `cost_center_transactions` WHERE journal_transaction_id not in (select id from journal_transactions) ",
                    ]
                ],
                "journals_different_branch_than_invoice" => [
                    "title" => "journal that has different branch than invoice",
                    "check_query" => "SELECT journals.* ,invoices.branch_id AS 'invoices_branch'
                    FROM
                        `journals`
                    INNER JOIN invoices ON journals.entity_id = invoices.id  
                    WHERE
                        (journals.`entity_type` = 'invoice' OR  journals.`entity_type` = 'refund_receipt') 
                        AND (journals.branch_id IS NULL OR journals.branch_id  <> invoices.branch_id);",

                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/update_journal_branch_id",
                    "report" => [
                        "query" => "SELECT journals.* ,invoices.branch_id AS 'invoices_branch'
                                                    FROM
                                                        `journals`
                                                    INNER JOIN invoices ON journals.entity_id = invoices.id  
                                                    WHERE
                                                        (journals.`entity_type` = 'invoice' OR  journals.`entity_type` = 'refund_receipt' )
                                                        AND (journals.branch_id IS NULL OR journals.branch_id  <> invoices.branch_id);",
                        "fields" => [
                            "id",
                            "number",
                            "date",
                            "branch_id",
                            "invoices_branch"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "journals_different_branch_than_expenses" => [
                    "title" => "journal that has different branch than expense",
                    "check_query" => "SELECT
                    journals.*,
                     journals.id,
                  
                     expenses.branch_id AS 'expenses_branch'
                 FROM
                     `journals`
                 INNER JOIN expenses ON journals.entity_id = expenses.id
                 WHERE
                     journals.`entity_type` = 'expense' AND(
                         journals.branch_id IS NULL OR journals.branch_id <> expenses.branch_id
                     )   ORDER BY `id` DESC;",

                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/update_journals_different_branch_than_expenses",
                    "report" => [
                        "query" => "SELECT
                        journals.*,
                         journals.branch_id,
                      
                         expenses.branch_id AS 'expenses_branch'
                     FROM
                         `journals`
                     INNER JOIN expenses ON journals.entity_id = expenses.id
                     WHERE
                         journals.`entity_type` = 'expense' AND(
                             journals.branch_id IS NULL OR journals.branch_id <> expenses.branch_id
                         )   ORDER BY `id` DESC;",
                        "fields" => [
                            "id",
                            "number",
                            "date",
                            "branch_id",
                            "expenses_branch"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "non_balanced_purchase_refund_journals" => [
                    "title" => "Fix Non Balanced Purchase Refund Journals",
                    "check_query" => "SELECT J.* FROM `journals` J left join purchase_orders PO on PO.id = J.entity_id and J.entity_type = 'purchase_refund' WHERE PO.type = 6 and PO.summary_total < J.total_debit having count(*) > 0;",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_unbalanced_purchase_refunds",
                    "report" => [
                        "query" => "SELECT J.* FROM `journals` J left join purchase_orders PO on PO.id = J.entity_id and J.entity_type = 'purchase_refund' WHERE PO.type = 6 and PO.summary_total < J.total_debit having count(*) > 0;",
                        "fields" => [
                            "id",
                            "number",
                            "date",
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ],
                ],
                "fix_assets_with_no_journals" => [
                    "title" => "Fix Assets with not journals",
                    "check_query" => "select * from assets where id not in (select entity_id from journals where entity_type = 'asset');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_assets_have_no_journal",

                ],
                "fix_asset_deprecations_with_no_journals" => [
                    "title" => "Fix Asset deprecations with not journals",
                    "check_query" => "select * from asset_deprecations where id not in (select entity_id from journals where entity_type = 'asset_deprecation');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_asset_deprecations_have_no_journal",

                ],
                "invoices_with_no_journals" => [
                    "title" => "Invoices that have no journals",
                    "check_query" => "select * from invoices where (type = 0 or type = 6) and summary_total > 0 and ( pos_shift_id IS NULL OR pos_shift_id not in (select entity_id from journals where entity_type = 'pos_shift_sales' or entity_type = 'pos_shift_refund')) and draft = 0 and id not in (select entity_id from journals where entity_type = 'invoice' or entity_type = 'refund_receipt');",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/invoices/fix_invoices_has_no_journals",
                    "report" => [
                        "query" => "select * from invoices where (type = 0 or type = 6) and summary_total > 0 and ( pos_shift_id IS NULL OR pos_shift_id not in (select entity_id from journals where entity_type = 'pos_shift_sales' or entity_type = 'pos_shift_refund')) and draft = 0 and id not in (select entity_id from journals where entity_type = 'invoice' or entity_type = 'refund_receipt');",
                        "fields" => [
                            "id",
                            "no",
                            "date"
                        ],
                        "view_link" => "/owner/invoices/view/%id%"
                    ]
                ],
                "invoices_with_wrong_item name" => [
                    "title" => "invoices_with_wrong_item",
                    "check_query" => "SELECT * FROM `invoice_items` WHERE `item` LIKE 'product_image';",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoice_item_item_name",
                    "report" => [
                        "query" => "SELECT * FROM `invoice_items` WHERE `item` LIKE 'product_image';",
                        "fields" => [
                            "id",
                            "item",
                            "description",
                            "invoice_id",
                        ],
                        'view_link' => '/owner/invoices/view/%invoice_id%'


                    ]
                ],
                "journals_different_branch_than_requisition" => [
                    "title" => "Journals which have different branch than the requisitions its source requisition",
                    "check_query" => "SELECT J.* FROM journals J left join requisitions R on R.id = J.entity_id and J.entity_type = 'requisition' where J.branch_id != R.branch_id ORDER BY `J`.`created` DESC;",
                    "fix_strategy" => "auto",
                    "fix_type" => "query",
                    "fix_queries" => [
                        "UPDATE journals J
                                left join requisitions R on R.id = J.entity_id and J.entity_type = 'requisition' 
                                set J.branch_id = R.branch_id
                                where J.branch_id != R.branch_id  
                                ;"
                    ],
                    "report" => [
                        "query" => "SELECT J.*, R.branch_id as requisition_branch_id FROM journals J left join requisitions R on R.id = J.entity_id and J.entity_type = 'requisition' where J.branch_id != R.branch_id ORDER BY `J`.`created` DESC;",
                        "fields" => [
                            "id",
                            "number",
                            "date",
                            "branch_id",
                            "requisition_branch_id"
                        ],
                        "view_link" => "/owner/journals/view/%id%"
                    ]
                ],
                "bundle_stock_transaction_without_source_bundle_journal" => [
                    "title" => "Bundle stock transactions which have children transactions with no journal",
                    "check_query" => "select *
                                            from stock_transactions ST
                                                     join products P on P.id = ST.product_id
                                            where (P.type = 3 AND ST.quantity > 0)
                                              AND ST.quantity > 0
                                              and (select value from settings where `key` = 'bundle_type' and branch_id = ST.branch_id) = 1
                                              and ST.id not in (select entity_id from journals where entity_type = 'pack_bundle_outbound_stock_transaction')
                                              and ST.id in (select distinct order_id from stock_transactions where source_type = 8);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "",
                    "report" => [
                        "query" => "select ST.id,ST.received_date,P.name,ST.prodcut_id
                                            from stock_transactions ST
                                                     join products P on P.id = ST.product_id
                                            where (P.type = 3 AND ST.quantity > 0)
                                              AND ST.quantity > 0
                                              and (select value from settings where `key` = 'bundle_type' and branch_id = ST.branch_id) = 1
                                              and ST.id not in (select entity_id from journals where entity_type = 'pack_bundle_outbound_stock_transaction')
                                              and ST.id in (select distinct order_id from stock_transactions where source_type = 8);",
                        "fields" => [
                            "id",
                            "received_date",
                            "name",
                            "product_id",
                        ],
                        "view_link" => "/owner/products/view/%product_id%"
                    ]
                ],
                "payments_without_currency_code" => [
                    "title" => "Payments created without currency",
                    "check_query" => "SELECT * FROM `invoice_payments` WHERE currency_code is null or currency_code = '';",
                    "fix_strategy" => "manual",
                    "report" => [
                        "query" => "SELECT * FROM `invoice_payments` WHERE currency_code is null or currency_code = ''",
                        "fields" => [
                            "id",
                        ],
                        "view_link" => "/owner/invoice_payments/view/%id%"
                    ]
                ],
                "fix_purchase_payment_journal" => [
                    "title" => "Purchase payments without journals",
                    "check_query" => 'SELECT id FROM `purchase_order_payments` WHERE id NOT in (SELECT `entity_id` from `journals` where `entity_type`="purchase_order_payment") and payment_method != "supplier_credit"' ,
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_purchase_payment_journal",
                ],

                "fix_invoice_with_wrong_branch" => [
                    "title" => "Invoice With Wrong Branch (2023-04-01 : 2023-04-02)",
                    "check_query" => "SELECT * FROM `invoices` JOIN staffs on invoices.staff_id = staffs.id where invoices.branch_id != staffs.branch_id AND invoices.date BETWEEN '2023-04-01' AND '2023-04-02'" ,
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_invoice_wrong_branch",
                ],
                "journal_accounts_without_branch_full_control_permission" => [
                    "title" => "Journal accounts with missing record in item_permissions",
                    "check_query" => "SELECT * FROM `journal_accounts` WHERE id not in (SELECT item_id from item_permissions WHERE item_type = 3)" ,
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_journal_accounts_missing_permissions",
                    "report" => [
                        "query" => "SELECT * FROM `journal_accounts` WHERE id not in (SELECT item_id from item_permissions WHERE item_type = 3)",
                        "fields" => [
                            "id",
                            "code",
                            "name"
                        ],
                        "view_link" => "/v2/owner/accounting/%id%/account-details"
                    ]
                ],
                'duplicate_hidden_journal_accounts_without_transactions' => [
                    'title' => 'Duplicate hidden journal accounts without transactions',
                    'check_query' => 'SELECT * FROM journal_accounts WHERE journal_accounts.is_hidden = 1 AND id NOT IN (SELECT DISTINCT journal_account_id FROM journal_transactions);',
                    'fix_strategy' => 'auto',
                    'fix_type' => 'query',
                    'fix_queries' => [
                        'delete from journal_accounts where is_hidden = 1 and not exists (select 1 from journal_transactions where journal_transactions.journal_account_id = journal_accounts.id);'
                    ],
                ],
                "journal_cats_without_branch_full_control_permission" => [
                    "title" => "Journal cats with missing record in item_permissions",
                    "check_query" => "SELECT * FROM `journal_cats` WHERE id not in (SELECT item_id from item_permissions WHERE item_type = 4)" ,
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/fix_journal_cats_missing_permissions",
                    "report" => [
                        "query" => "SELECT * FROM `journal_cats` WHERE id not in (SELECT item_id from item_permissions WHERE item_type = 4)",
                        "fields" => [
                            "id",
                            "code",
                            "name"
                        ],
                        "view_link" => "/v2/owner/accounting/%id%/account-details"
                    ]
                ],

            ]
        ],
        "sales" => [
            "Title" => "Sales",
            "plugin_id" => 100,
            "diagnostics" => [
                "commissions_without_invoices" => [
                    "title" => "Commissions without invoices",
                    "check_query" => "SELECT * FROM `commissions` WHERE invoice_id not in (SELECT id from invoices);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/fake/delete_removed_invoices_commissions",
                ]
            ]
        ],
        "installments" => [
            "Title" => "Installments",
            "plugin_id" => 97,
            "diagnostics" => [
                "installments_without_invoices" => [
                    "title" => "Installment agreements without invoices",
                    "check_query" => "SELECT * FROM `invoice_installment_agreements` WHERE deleted_at is null and invoice_id not in (SELECT id from invoices);",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/fake/delete_removed_invoices_agreements",
                ]
            ]
        ],
        "workflows" => [
            "Title" => "Workflows",
            "plugin_id" => 107,
            "diagnostics" => [
                "orphaned_permissions_related_to_deleted_workflows_types" => [
                    "title" => "Orphaned permissions related to deleted workflows types",
                    "check_query" => "SELECT
                            ep.*
                        FROM
                            entity_permissions ep
                        LEFT JOIN local_entities le ON
                            ep.entity_id COLLATE utf8_unicode_ci = le.key
                        WHERE
                            le.key IS NULL AND ep.entity_id LIKE 'le_workflow-type-entity-%';",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/fake/delete_orphaned_permissions_of_workflow_types",
                ]
            ]
        ],
        "general" => [
            "Title" => "General",
            "plugin_id" => null,
            "diagnostics" => [
                "activity_log_before_site_creation" => [
                    "title" => "Activity log before site creation",
                    "check_query" => "SELECT * FROM `activity_log` WHERE `created` < '" . getCurrentSite('created') . "'",
                    "fix_strategy" => "auto",
                    "fix_type" => "url",
                    "fix_url" => "/owner/fake/remove_activity_log_before_site_creation",
                ]
            ]
        ]

    ];

}
