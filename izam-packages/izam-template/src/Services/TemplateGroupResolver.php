<?php

namespace Izam\Template\Services;

use Izam\Template\Utils\EmailTemplatesGroupUtil;
use Izam\Template\Utils\PrintableTemplatesGroupUtil;
use Izam\Template\Utils\TemplateTypeUtil;

class TemplateGroupResolver
{
    protected static array $map = [
        TemplateTypeUtil::EMAIL => EmailTemplatesGroupUtil::class,
        TemplateTypeUtil::PDF => PrintableTemplatesGroupUtil::class,
    ];

    public static function findByTypeAndEntity(string $type, string $entityKey): ?array
    {
        $utilClass = static::$map[$type] ?? PrintableTemplatesGroupUtil::class;
        return $utilClass::getTemplateGroupByEntityKey($entityKey);
    }
}