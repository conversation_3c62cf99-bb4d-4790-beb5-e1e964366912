<?php

namespace Izam\Template\EmailSender\Services;

use GuzzleHttp\Psr7\Utils;
use Izam\Logging\Service\RollbarLogService;
use Izam\Template\EmailSender\DTO\SMTPConfig;
use Izam\Template\EmailSender\DTO\SMTPMessage;
use League\OAuth2\Client\Token\AccessToken;
use Microsoft\Graph\Generated\Models\BodyType;
use Microsoft\Graph\Generated\Models\EmailAddress;
use Microsoft\Graph\Generated\Models\FileAttachment;
use Microsoft\Graph\Generated\Models\InternetMessageHeader;
use Microsoft\Graph\Generated\Models\ItemBody;
use Microsoft\Graph\Generated\Models\Message;
use Microsoft\Graph\Generated\Models\Recipient;
use Microsoft\Graph\Generated\Users\Item\SendMail\SendMailPostRequestBody;
use Rollbar\Payload\Level;
use Throwable;

class OutlookMailer implements MailerInterface
{
    public function __construct(private SMTPConfig $config)
    {
    }

    public function send(SMTPMessage $message): void
    {
        $credentials = json_decode($this->config->credentials(), true);
        $graphServiceClient = $this->createGraphServiceClient($credentials['access_token'], $credentials['refresh_token']);
        $requestBody = $this->createRequestBody($message);

        try {
            $this->sendMail($graphServiceClient, $requestBody);
        } catch (Throwable $e) {
            $this->handleTokenRefresh($credentials['refresh_token'], $requestBody);
        }
    }

    private function createGraphServiceClient(string $accessToken, string $refreshToken)
    {
        return Outlook::createGraphServiceClient($accessToken, $refreshToken);
    }

    private function sendMail($graphServiceClient, SendMailPostRequestBody $requestBody): void
    {
        $graphServiceClient->me()->sendMail()->post($requestBody)->wait();
    }

    private function handleTokenRefresh(string $refreshToken, SendMailPostRequestBody $requestBody): void
    {
        try {
            $token = $this->refreshAccessToken($refreshToken);
            $graphServiceClient = $this->createGraphServiceClient($token->getToken(), $token->getRefreshToken());
            $this->sendMail($graphServiceClient, $requestBody);
            $this->updateTokens($token);
        } catch (Throwable $exception) {
            RollbarLogService::log(Level::ERROR, $exception->getMessage(), [],true);

            $link = $this->config->senderId() == -1 ? '/smtp_settings' :
                '/v2/owner/smtp/email-address/edit/1' . $this->config->senderId();

            throw new \Exception(sprintf(
                __t('Email sending failed. Please reauthenticate to continue by clicking %s to refresh your credentials.'),
                sprintf(__t("<a  href='$link'>%s</a>"), __t('here')),
            ));
        }
    }

    private function updateTokens(AccessToken $token)
    {
        $tokens = json_encode([
            'access_token' => $token->getToken(),
            'refresh_token' => $token->getRefreshToken()
        ]);

        if ($this->config->senderId() == -1) {
            SiteSMTPTokenManager::updateStoredTokens($tokens);
        } else {
            SMTPTokenManager::updateStoredTokens($this->config->senderId(), $tokens);
        }
    }

    private function refreshAccessToken(string $refreshToken)
    {
        $provider = Outlook::createProvider();
        return $provider->getAccessToken('refresh_token', ['refresh_token' => $refreshToken]);
    }

    private function createRequestBody(SMTPMessage $smtpMessage): SendMailPostRequestBody
    {
        $messageBody = new ItemBody();
        $messageBody->setContentType(new BodyType(BodyType::HTML));
        $messageBody->setContent($smtpMessage->body());

        $message = new Message();

        $recipientEmail = new EmailAddress();
        $recipientEmail->setAddress($this->config->senderMail());
        $recipientEmail->setName($this->config->senderName());

        $recipient = new Recipient();
        $recipient->setEmailAddress($recipientEmail);

        $message->setFrom($recipient);
        $message->setSubject($smtpMessage->subject());
        $message->setBody($messageBody);
        $message->setToRecipients($this->getRecipients($smtpMessage->validTo()));
        $message->setCcRecipients($this->getRecipients($smtpMessage->validCC()));
        $message->setBccRecipients($this->getRecipients($smtpMessage->validBCC()));
        $message->setAttachments($this->getAttachments($smtpMessage->getAttachments()));

        $headers = [];
        foreach ($smtpMessage->headers() as $key => $header) {
            $headerItem = new InternetMessageHeader();
            $headerItem->setName($key);
            $headerItem->setValue($header);

            $headers[] = $headerItem;
        }
        /*$message->setInternetMessageHeaders($headers);*/

        $requestBody = new SendMailPostRequestBody();
        $requestBody->setMessage($message);
        $requestBody->setSaveToSentItems(false);

        return $requestBody;
    }

    private function getRecipients(array $emails): array
    {
        return array_map(function ($email) {
            $recipientEmail = new EmailAddress();
            $recipientEmail->setAddress($email);

            $recipient = new Recipient();
            $recipient->setEmailAddress($recipientEmail);

            return $recipient;
        }, $emails);
    }

    private function getAttachments(array $attachments): array
    {
        return array_map(function ($attachment) {
            $fileAttachment = new FileAttachment();
            $fileAttachment->setName($attachment->getName());
            $fileAttachment->setContentBytes(Utils::streamFor(base64_encode($attachment->getContent())));

            return $fileAttachment;
        }, $attachments);
    }
}
