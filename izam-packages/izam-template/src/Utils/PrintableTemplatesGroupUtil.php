<?php

namespace Izam\Template\Utils;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class PrintableTemplatesGroupUtil extends TemplatesGroupUtil
{
    const PURCHASE_TYPE = 1;
    const CHEQUE_CYCLE_TYPE = 2;
    const ATTENDANCE_TYPE = 4;
    const INVENTORY_TYPE = 5;
    const RESERVATION_ORDER_TYPE = 6;

    public static function getTypes(): array
    {
        return [
            self::PURCHASE_TYPE => [
                'entity_keys' => [
                    EntityKeyTypesUtil::PURCHASE_REQUEST,
                    EntityKeyTypesUtil::QUOTATION_REQUEST,
                    EntityKeyTypesUtil::PURCHASE_QUOTATION,
                    EntityKeyTypesUtil::PURCHASE_ORDER
                ],
                'breadcrumbs' => [
                    [
                        'link' => route('owner.purchase_invoices.settings'),
                        'title' => __t('Purchase Invoice Settings')
                    ]
                ]
            ],
            self::CHEQUE_CYCLE_TYPE => [
                'entity_keys' => [
                    EntityKeyTypesUtil::PAYABLE_CHEQUE,
                    EntityKeyTypesUtil::RECEIVABLE_CHEQUE
                ],
                'breadcrumbs' => [
                    [
                        'link' => route('owner.cheque_cycle.settings'),
                        'title' => sprintf(__t('%s Settings'), __t('Cheque Cycle'))
                    ]
                ]
            ],
            self::PAYROLL_TYPE => [
                'entity_keys' => [
                    EntityKeyTypesUtil::PAYSLIP,
                    EntityKeyTypesUtil::PAY_RUN,
                    EntityKeyTypesUtil::CONTRACT,
                    EntityKeyTypesUtil::LOAN_ENTITY_KEY,
                ],
                'breadcrumbs' => [
                    [
                        'link' => route('owner.contract.settings'),
                        'title' => sprintf(__t('%s Settings'), __t('Payroll'))
                    ]
                ]
            ],
            self::ATTENDANCE_TYPE => [
                'entity_keys' => [
                    EntityKeyTypesUtil::ATTENDANCE_SHEET,
                    EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY
                ],
                'breadcrumbs' => [
                    [
                        'link' => route('owner.attendance_settings'),
                        'title' => __t('Attendance Settings')
                    ]
                ]
            ],
            self::INVENTORY_TYPE => [
                'entity_keys' => [
                    EntityKeyTypesUtil::REQUISITION,
                    EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY
                ],
                'breadcrumbs' => [
                    [
                        'link' => '/v2/owner/inventory/settings',
                        'title' => __t('Inventory Settings')
                    ]
                ]
            ],
            self::RESERVATION_ORDER_TYPE => [
                'entity_keys' => [
                    EntityKeyTypesUtil::RENTAL_RESERVATION_ORDER
                ],
                'breadcrumbs' => [
                    [
                        'link' => '/v2/owner/rental/settings',
                        'title' => __t('Rental and Unit Management Settings')
                    ]
                ]
            ],
        ];
    }

    public static function getGroupIndexRoute($type): string
    {
        return route('owner.manage_printable_templates_for_group', $type);
    }
}
