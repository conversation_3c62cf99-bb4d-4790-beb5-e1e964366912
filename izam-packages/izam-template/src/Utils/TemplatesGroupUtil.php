<?php

namespace Izam\Template\Utils;


class TemplatesGroupUtil
{
    const PAYROLL_TYPE = 3;

    public static function getTypes(): array
    {
        return [];
    }

    public static function getTypeTemplatesGroup(int $type): ?array
    {
        $types = static::getTypes();
        if (!isset($types[$type])) {
            return null;
        }
        return $types[$type];
    }

    public static function getTemplateGroupByEntityKey(string $entityKey): ?array
    {
        $templatesTypes = static::getTypes();
        foreach ($templatesTypes as $key => $templatesType) {
            if (in_array($entityKey, $templatesType['entity_keys'])) {
                $templatesType['type'] = $key;
                $templatesType['route'] = static::getGroupIndexRoute($key);
                return $templatesType;
            }
        }
        return null;
    }

    public static function getGroupIndexRoute($type): string
    {
        return '';
    }

}
