<?php

namespace Izam\Template\Utils;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class EmailTemplatesGroupUtil extends TemplatesGroupUtil
{
    public static function getTypes(): array
    {
        return [
            self::PAYROLL_TYPE => [
                'entity_keys' => [
                    EntityKeyTypesUtil::PAYSLIP,
                    EntityKeyTypesUtil::CONTRACT
                ],
                'breadcrumbs' => [
                    [
                        'link' => route('owner.contract.settings'),
                        'title' => sprintf(__t('%s Settings'), __t('Payroll'))
                    ]
                ]
            ],
        ];
    }

    public static function getGroupIndexRoute($type): string
    {
        return route('owner.manage_email_templates_for_group', $type);
    }
}
