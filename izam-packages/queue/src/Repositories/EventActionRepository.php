<?php

namespace Izam\Daftra\Queue\Repositories;

use Izam\Daftra\Queue\Factory\ListenerFactory;
use App\Utils\SiteStatusUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Izam\Daftra\Common\Queue\DaftraEventRepository;
use Izam\Daftra\Common\Queue\DaftraListener;
use Izam\Daftra\Common\Queue\EventEnvUtil;
use Izam\Daftra\Common\Queue\EventPlatformUtil;
use Izam\Daftra\Common\Queue\ListenerStatusUtil;
use Izam\Daftra\Queue\Events\EventAbstract;
use Izam\Daftra\Queue\Models\EventAction;
use Izam\Daftra\Queue\Models\EventActionListener;
use Izam\Daftra\Queue\Models\EventQueueCommand;


/**
 * AttendanceSheetRepository Class attendance sheet repository
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 */
class EventActionRepository extends DaftraEventRepository implements IEventRepository
{

    public static $Saved = false;

    private $model;


    public function __construct(EventAction $eventQueue)
    {
        $this->model =  $eventQueue;
    }

    public function __call($name, $arguments)
    {
        if (!method_exists($this, $name)) {
            return $this->model->$name(...$arguments);
        }
    }


    public function getCommands($connection = null)
    {
      try {
          if ($connection) {
              return EventQueueCommand::on($connection)->limit(100)->get();
          }
          return EventQueueCommand::query()->limit(100)->get();
      } catch (\Exception $exception) {
          return collect();
      }
    }
    public function changeEventStatusById($id, $status)
    {
       $this->model->where('id', $id)->update(['status' => $status]);
    }

    public function deleteCommands($ids, $connection = null)
    {
       try {
           if ($connection) {
               return EventQueueCommand::on($connection)->whereIn('id', $ids)->delete();
           }
           return EventQueueCommand::whereIn('id', $ids)->delete();
       } catch (\Exception $exception) {
           return ;
       }
    }
    public function addQueueCommand($data)
    {
        EventQueueCommand::insert($data);
    }

    /**
     * @param EventAbstract $eventAbstract
     * @return EventAbstract|void
     */
    public function save($eventAbstract)
    {
        $eventData = $eventAbstract->toArray();
        $eventData['site_id'] = getCurrentSite('id');
        $eventData['platform'] = EventPlatformUtil::LARAVEL;
        if ($_SERVER['SCRIPT_NAME'] ==  "/webroot/index.php") {
            $eventData['platform'] = EventPlatformUtil::CAKE;
        }
        if (PHP_SAPI !== 'cli') {
            $eventData['runtime_env'] = EventEnvUtil::FOREGROUND;
        } else {
            $eventData['runtime_env'] = EventEnvUtil::BACKGROUND;
        }
        $eventRecord = EventAction::create($eventData);
        $eventAbstract->setId($eventRecord->id);
        self::$Saved = true;
        return $eventAbstract;
    }

    /**
     * @param array[DaftraListener] $eventListeners
     * @return array|mixed
     */
    public function saveEventListeners($eventListeners = [])
    {
        $lisData = [];
        foreach ($eventListeners as $eventListener) {
            $data = $eventListener->toArray();
            if(!isset($data['event_action_id']) || (isset($data['event_action_id']) && empty($data['event_action_id']))) {
                /** @TODO: add shared Logging method */
//                Log::error('empty listener event action', $data);
                continue;
            }
            /**
             * @var $eventListener DaftraListener
             */
            $lisData[] = $eventListener->toArray();
        }
        EventActionListener::insert($lisData);
    }

    /**
     * @param $eventId
     * @return array
     */
    public function getEventForeGroundListener($eventId)
    {
        return EventActionListener::where('event_action_id', '=', $eventId)
            ->where('runtime_env', '=', EventEnvUtil::FOREGROUND)
            ->where('status', ListenerStatusUtil::CREATED)
            ->get()
            ->toArray();
    }

    public function getLaravelListeners($siteId, $runTime, $eventId = null, $listenerId = null)
    {

        $query = EventActionListener::where('site_id', $siteId)
            ->with('action_event')
            ->where('status', '=', ListenerStatusUtil::CREATED)
            ->where('platform', '=', EventPlatformUtil::LARAVEL)
//        ->where('runtime_env','=', $runTime)
            ->orderBy('created', 'asc');
        if ($eventId) {
            $query->where('event_action_id', $eventId);
        }
        if ($listenerId) {
            $query->where('id', $listenerId);
        } else {
            $query->whereNotExists( function ($sql) use ($siteId) {
                $sql->from('event_action_listeners')->select('id')
                    ->where('status', ListenerStatusUtil::PROCESSING)
                    ->where('site_id', $siteId);
            });
        }
        return $query->get();
    }
    public function getStuckProcessNotExceededTries()
    {
        return EventActionListener::where('status', '=', ListenerStatusUtil::PROCESSING)
            ->where('tries', '<=', 5)
            ->where('started_at', '<',  Carbon::now()->timezone('UTC')->add('-4', 'hour'))
            ->orderBy('id', 'asc')
            ->get();
    }


    public function getStuckProcess()
    {
       return EventActionListener::where('tries',  '>=',5)
            ->where('started_at', '<',  Carbon::now()->timezone('UTC')->add('-4', 'hour'))
            ->get();
    }

    public function getEventAndListenersThatExceedWeek()
    {
        $listeners = EventActionListener::whereDate('created', '<',  Carbon::now()->timezone('UTC')->add('-7', 'days'))
            ->limit(50)->get();
        $events = EventAction::whereDate('created', '<',  Carbon::now()->timezone('UTC')->add('-7', 'days'))
            ->limit(50)->get();
        return [$events, $listeners];
    }

    public function deleteEventAndListenersThatExceedWeek()
    {

         EventActionListener::whereDate('created', '<',  Carbon::now()->timezone('UTC')->add('-7', 'days'))
            ->delete();

        EventAction::whereDate('created', '<',  Carbon::now()->timezone('UTC')->add('-7', 'days'))
            ->delete();
    }

    public function generateListenerData($eventListenerHandler)
    {
        return ListenerFactory::getInstance($eventListenerHandler);
    }





    private function getUnFinishedEventsQuery($sql)
    {
        $sql->select('t.event_action_id')->fromSub(function ($sql) {
            $sql->from('event_action_listeners')
                ->select('event_action_id')
                ->leftJoin('event_actions', 'event_action_listeners.event_action_id', '=', 'event_actions.id')
                ->where('event_action_listeners.status', '!=', ListenerStatusUtil::FINISHED)
                ->groupBy('event_action_id');
        },'t');
    }

    private function getEventForDeletedSitesQuery($sql)
    {
        $sql->select('t.id')->fromSub(function ($sql) {
            $sql->from('event_actions')
                ->select('id')
                ->where(function ($sql) {
                    $sql->whereRaw( DB::raw('site_id in (select id from sites where status = ?)'), SiteStatusUtil::ARCHIVED)
                        ->orWhereRaw( DB::raw('site_id in (select id from sites where status = ?)'), SiteStatusUtil::DELETED)
                        ->orWhereRaw( DB::raw('site_id not in (select id from sites)'));
                });
        }, 't');
    }

    public function getAllEvents($connection = null)
    {
        try {
            if ($connection) {
                $events =  EventAction::on($connection)->limit(1000)->get();
                $listeners = EventActionListener::on($connection)->whereIn('event_action_id', $events->pluck('id')->toArray())->get();
                $ret = [];
                foreach ($events as $event) {
                    $eventListeners = $listeners->filter(function ($listener) use ($event) {
                       return $listener->event_action_id == $event->id;
                    });
                    if (!empty($eventListeners)) {
                        $event->listeners = $eventListeners;
                        $ret[] = $event;
                    }
                }
                return  $ret;
            }
            return EventAction::whereHas('listeners')->with(['listeners'])->limit(1000)->get();
        } catch (\Exception $exception) {
            return  collect();
        }
    }

    public function delete($event_ids, $connection = null)
    {
       try {
           if ($connection) {
               EventAction::on($connection)->whereIn('id', $event_ids)->delete();
               EventActionListener::on($connection)->whereIn('event_action_id', $event_ids)->delete();
               return;
           }
           EventAction::whereIn('id', $event_ids)->delete();
           EventActionListener::whereIn('event_action_id', $event_ids)->delete();
       } catch (\Exception $exception) {
           return;
       }
    }

    public function first($conditions = [])
    {
        return EventAction::where($conditions)->first();
    }

    public function update($conditions, $data)
    {
        return EventAction::where($conditions)->update($data);
    }
}
