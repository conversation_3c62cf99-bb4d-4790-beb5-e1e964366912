<?php

namespace Izam\Daftra\Queue\Events;

use Izam\Daftra\Common\Queue\EventStatusUtil;
use Izam\Daftra\Queue\Repositories\IEventRepository;
use Izam\Daftra\Queue\Tokens\ITokenGenerator;
use Izam\Daftra\Queue\Util\EventEnvUtil;
use Izam\Daftra\Queue\Util\EventPlatformUtil;

class EventDispatcher
{
    /**
     * @var self
     */
    private static $instance;

    /**
     * @var array $eventList
     */
    protected $eventList = [];


    private $uniqueEvents = [];

    /**
     * @var IEventRepository
     */
    private $repository;

    /**
     * @var ITokenGenerator
     */
    private $tokenGenerator;

    /**
     * @var array $afterDo
     */
    public static $afterDo = [];

    private function __construct()
    {
    }

    /**
     * @return EventDispatcher
     */
    public static function getInstance() {
        if(!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * @param  IEventRepository $eventRepository
     * @param ITokenGenerator $tokenGenerator
     */
    public static function init($eventRepository, $tokenGenerator) {
        $instance = self::getInstance();
        $instance->repository = $eventRepository;
        $instance->tokenGenerator = $tokenGenerator;

        self::$instance =  $instance;
        register_shutdown_function(function () use ($instance) {
            $instance->fireUniqueEvents();
        });
    }

    public function fireUniqueEvents() {
        foreach ($this->uniqueEvents as $k => $uniqueEvent) {
            $this->saveEventAndListeners($uniqueEvent);
            unset($this->uniqueEvents[$k]);
        }
        return $this;
    }

    /**
     * @param $eventAbstract EventAbstract
     */
    public function fire($eventAbstract)
    {
        if($eventAbstract->isUniqueOverTransaction()) {
            $this->uniqueEvents[$eventAbstract->getEventIdentifier()] = $eventAbstract;
            $this->fireUniqueEvents();
        } else {
            $this->saveEventAndListeners($eventAbstract);
        }
        return;
    }

    /**
     * @param EventAbstract $eventAbstract
     */
    protected function saveEventAndListeners($eventAbstract) {
        $instance = $this;

        $eventListeners = $instance->repository->getEventTypeListeners($eventAbstract->getType());

        if (count($eventListeners) == 0) {
            return;
        }

        $eventAbstract->setToken($instance->tokenGenerator->generate());
        $eventAbstract = $instance->repository->save($eventAbstract);
        $this->eventList[] =$eventAbstract->getId();
        $eventListeners = $this->prepareData($eventListeners, $eventAbstract);
        $instance->repository->saveEventListeners($eventListeners);
        $foreGroundListener = $instance->repository->getEventForeGroundListener($eventAbstract->getId());
        if (!empty($foreGroundListener)) {
//            $instance->repository->changeEventStatusById($eventAbstract->getId(), EventStatusUtil::PROCESSING);
            $instance->runCallBacks($foreGroundListener);
        }
    }

    public function prepareData($eventListeners, $eventAbstract)
    {
        $listenerObjects = [];
        foreach ($eventListeners as $eventListener) {
            $lisObj = $this->repository->generateListenerData($eventListener);
            $lisObj->setEventActionId($eventAbstract->getId());
            //we can add any function here to alter the listener data
            $listenerObjects[] = $lisObj;
        };

        return $listenerObjects;
    }

    public function runCallBacks($eventListenerRecord)
    {
        foreach ($eventListenerRecord as $listener) {
          foreach (static::$afterDo as $doFunc) {
              $doFunc($listener);
          }
        }
    }

    /**
     * @return array
     */
    public function getEventList()
    {
        return $this->eventList;
    }


}
