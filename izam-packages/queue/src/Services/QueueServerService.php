<?php


namespace Izam\Daftra\Queue\Services;



use Izam\Daftra\Common\Queue\EventStatusUtil;
use Izam\Daftra\Portal\Models\Site;
use Izam\Daftra\Queue\Repositories\EventActionServerRepository;

class QueueServerService
{
    private $eventActionRepository;

    public function __construct(EventActionServerRepository $eventActionRepository)
    {
        $this->eventActionRepository = $eventActionRepository;
    }



    public function addQueueCommands($data)
    {
        $this->eventActionRepository->addQueueCommands($data);
    }

    public function  runCommands()
    {
        $commands = $this->eventActionRepository->getCommands();
        foreach ($commands as $command) {
            $output = exec($command->command, $status);
        }
      //  $this->eventActionRepository->deleteCommands($commands->pluck('id')->toArray());
    }

    public function getSitesWillBeRun()
    {
        return $this->eventActionRepository->getDistinctSites();
    }


    public function getListenersWillBeRun($siteId)
    {
        return $this->eventActionRepository->getListenersWillBeExecutedBySiteId($siteId);
    }

    public function processEvent($event)
    {
        if ($event->status == EventStatusUtil::CREATED) {
            $this->eventActionRepository->changeEventStatus($event, EventStatusUtil::PROCESSING);
        }
    }

    public function finishEvent($event)
    {
        $event->load('listenerFinished');
        // We Check for event listener > 0 before finishing the Event incase of a race condition that might finish the event before the insertion of it's listeners
        if ($event->listeners->count() > 0 && $event->listeners->count() == $event->listenerFinished->count()) {
            $this->eventActionRepository->changeEventStatus($event, EventStatusUtil::FINISHED);
        }
    }

    public function getCurrentProcessingCount($siteId)
    {
        return $this->eventActionRepository->getCurrentProcessingCount($siteId);
    }

    public function getEvent($eventId)
    {
        return $this->eventActionRepository->getCreatedEvent($eventId);
    }

    public function getListener($id)
    {
        return $this->eventActionRepository->getListener($id);
    }

    public function getCurrentProcessingListenersCount()
    {
        return $this->eventActionRepository->getCurrentProcessingListenersCount();
    }

    public function getCurrentProcessingListenersCountPerServer($server_id)
    {
        return $this->eventActionRepository->getCurrentProcessingListenersCountPerServer($server_id);
    }

    public function isSiteExceedMaxProcessing($siteId)
    {
        $maxProcessing = 5;
        $currentCount = $this->getCurrentProcessingCount($siteId);
        return ($currentCount >= $maxProcessing);
    }

    public function isEventRunningBySignature($signature, $siteId)
    {
        return (!empty($signature) && $this->eventActionRepository->getProcessingEventBySignatureCount($signature, $siteId));
    }

    public function checkProcessExceedTries($listener)
    {
        if ($listener->tries >= 5) {
            $this->killProcess($listener);
            exit;
        }
    }

    public function killProcess($listener, $error = null)
    {
        $this->eventActionRepository->killProcess($listener, $error);
    }

    public function executeListener($listener, $serverId)
    {
      $this->eventActionRepository->executeListener($listener, $serverId);
    }

    public function finishListener($listener, $result, $error = null)
    {
       $this->eventActionRepository->finishListener($listener, $result, $error);
    }

    public function listenerHasError($listener, $exception)
    {
        $this->eventActionRepository->listenerHasError($listener, $exception);
    }

    public function getCommandsWillBeRun($limit = 1000)
    {
        return $this->eventActionRepository->getCommandsWillBeRun($limit);
    }


    public function getEventsPerSite($site_id)
    {
        return $this->eventActionRepository->getEventsPerSite($site_id);
    }

    public function deleteCommand($id)
    {
        return $this->eventActionRepository->deleteCommands([$id]);
    }

    public function deleteCommandBySite($siteId)
    {
        return $this->eventActionRepository->deleteCommandsWithSiteId($siteId);
    }
}
