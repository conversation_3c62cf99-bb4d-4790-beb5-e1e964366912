<?php

namespace Izam\Daftra\Portal\Services;

use Izam\Daftra\Portal\Models\Site;

class SiteService extends BaseService
{
    protected static $model = Site::class;

    protected static function afterSave($created, $data = null)
    {
        parent::afterSave($created);
        setLastUpdatedAt(0, 'site_info_updated_at');
    }

    public static function siteLogoUploadError($data)
    {
        // added condition is to check if the uploaded file is empty don't do the check.
        if (!empty($data['site_logo']) and is_array($data['site_logo'])) {
            unset($data['site_logo']);
            // $data['error_upload_logo'] = true;
            return true;
        }
        // $data['error_upload_logo'] = false;
        return false;
    }
    
    protected static function beforeSave($data = array())
    {
        foreach ($data as $key => $value) {
            if (in_array($key, ['first_name', 'last_name'])) {
                $data[$key] = htmlspecialchars($value);
            }
        }
        parent::beforeSave($data);
        if (!empty($data['password']) && strlen($data['password']) != strlen(HashPassword($data['password']))) {
            $data['password'] = HashPassword($data['password']);
        }
        return $data;
    }
}