<?php

namespace Izam\Forms\View\Helper\Show;

use App\Utils\EntityFieldUtil;
use Izam\Forms\ElementInterface;
use Izam\Forms\View\FormInput;

class ShowAssignedStaff extends FormInput
{
    protected function getType($element)
    {
        return EntityFieldUtil::ENTITY_FIELD_TYPE_ASSIGNED_STAFF;
    }

    /**
     * @param $element ElementInterface
     */
    public function render($element)
    {
        $html = " ";
        if($element->getValue()) {
            $html = '<div class="panel">
            <div class="panel-head">
                <h3>' . __t($element->getLabel()) .'</h3>
            </div>
        </div>
        <div class="p-3">
            <div class="row col-12 mt-4">';
            foreach ($element->getValue() as $value) {
                $html .= '<div class="col-4 mb-4">';
                $html .= $this->includeWithVariables(__DIR__."/templates/assigned_staff.php", [
                    'id' => $value['staff_id'], 'code'=>$value['code'] ?? $value['staff_id'], 'name' => $value['name'], 'image' => $value['image']
                ], false);
                $html .= '</div>';
            }
            $html .= '</div>
        </div>';
        }

        return $html;
    }
}
