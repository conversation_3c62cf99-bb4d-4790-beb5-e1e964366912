<?php
namespace Izam\Daftra\Common\Auth;

use App\Facades\Branch;
use CakeSession;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Staff\Util\StaffTypeUtil;
use Rollbar\Rollbar;

class AuthHelper
{

    /**
     * @var IAuthRepository
     */
    public static $authRepo;

    /**
     * @var IAuthSession
     */
    public static $authSession;


    /**
     * @param IAuthRepository $authRepo
     * @param IAuthSession $authSession
     */
    public static function setConfig($authRepo, $authSession)
    {
        static::$authRepo = $authRepo;
        static::$authSession = $authSession;
    }

    public static function generateLoggedUserSessionData($userType, $userId) {
        switch ($userType) {
            case AuthUserTypeUtil::OWNER:
                $site = self::$authRepo->findSite($userId);
                $data = array('is_super_admin' => 1, 'is_staff' => 0, 'type' => 'owner', 'user' => $site['Site']);
                break;
            case AuthUserTypeUtil::STAFF:
                $site = self::$authRepo->getCurrentSite();
                $staff = self::$authRepo->findStaff($userId);
                //default values when staff is employee
                $data = array('is_super_admin' => false, 'is_staff' => $staff['Staff']['id'], 'type' => 'owner', 'user' => $site, 'staff' => $staff['Staff']);
                if ($staff['Staff']['type'] == StaffTypeUtil::USER && isset($staff['Role']['is_super_admin'])) {
                    $data = array('is_super_admin' => $staff['Role']['is_super_admin'], 'is_staff' => $staff['Staff']['id'], 'type' => 'owner', 'user' => $site, 'staff' => $staff['Staff']);
                }

                break;
            case AuthUserTypeUtil::CLIENT:
                $client = self::$authRepo->findClient($userId);
                $data = array('type' => 'client', 'user' => $client['Client'], 'success' => true, 'is_staff' => 0, 'is_super_admin' => 0);
                break;
        }
        $data['user']['type'] = $data['type'];
        $data['user']['staff_id'] = $data['is_staff'];
        $data['user']['is_super_admin'] = $data['is_super_admin'];
        $staffBranches = [];
        //Handle both cakephp and laravel branch facade
        //Because getStaffBranches is not available in laravel with the same signature
        if (class_exists(\App\Helpers\Branch::class) && method_exists(\App\Helpers\Branch::class, 'getStaffBranches') && isset($data['staff'])) {
            $staffBranches = array_keys(Branch::getStaffBranches('list', $data['staff']['id'])->toArray());
        } else if(function_exists('getStaffBranches') && isset($data['staff']) && function_exists('ifPluginActive') && ifPluginActive(PluginUtil::BranchesPlugin)) {
            $staffBranches = array_keys(getStaffBranches('list', $data['staff']['id']));
        }

        $staffBranch = settingsGetValue(PluginUtil::BranchesPlugin, 'branch-' . $userId);
        $data['branch_id'] = $staffBranch && in_array($staffBranch, $staffBranches) ? $staffBranch : (isset($data['staff']) ? $data['staff']['branch_id'] : $staffBranch);     
        return $data;
    }

    public static function setLoggedUserSessionData($sessionData) {
        $session_name = strtoupper($sessionData['type']);
        self::$authSession->write($session_name, $sessionData['user']);
        if(class_exists('CakeSession')) {
            $session = new CakeSession;
            if(method_exists($session, 'delete' )){
                $session->delete('branch_id');
            }
        }
        self::$authSession->write('STAFF', (isset($sessionData['staff']) ? $sessionData['staff'] : false));
    }

    public static function revoke($type, $id)
    {
        self::$authRepo->revokeAllAccessTokensForUser($type, $id);
    }

    public static function decodeToken($jwt, $publicKey)
    {
        if (!$jwt) {
            return false;
        }

        try {
            $decodedToken = JWT::decode($jwt, new Key($publicKey, 'RS256'));
        } catch (ExpiredException) {
            // mute this exception from rollbar
            return false;
        } catch (\Exception $exception) {
            return false;
        }
        if (!$decodedToken) {
            return false;
        }

        return (array) $decodedToken;
    }
}
