<?php

namespace Izam\Daftra\Common\EntityStructure;



use App\Modules\LocalEntity\Helpers\CacheLogger;
use Izam\Daftra\Common\Entity\Actions\ShowAction\RelationLoader;
use Izam\Daftra\Common\EntityStructure\Fields\Separator;
use Izam\Daftra\Common\EntityStructure\Relation\BelongsToRelation;
use Izam\Daftra\Common\EntityStructure\Relation\HasManyRelation;
use Izam\Daftra\Common\EntityStructure\Relation\HasOneRelation;
use Izam\Daftra\Common\EntityStructure\Relation\Relation;
use Izam\Daftra\Common\Utils\Entity\EntityFieldRelationUtil;
use Izam\Daftra\Common\Utils\Entity\EntityFieldUtil;

class Entity implements IEntity
{
    private $entityKey;
    private $belongsTo = [];
    private $hasOne = [];
    private $hasMany = [];

    /**
     * @var ExtendFromData $extendData
     */
    protected $extendData ;

    /**
     * @var ParentEntityData
     */
    protected $parentEntityData;

    protected $childrenKeys = [];


    private $listingFields;
    /**
     * @var Field[]
     */
    private $fields = [];
    private $relations = [];
    private $table;
    private $primaryKey;
    private $listingField;
    private $label;
    private $layout;
    private $type;
    private $plugin;
    private $connection;
    private $isGlobal;
    protected $subFormField = null;

    private $thumbnailField;
    private $permissions;
    private $options;

    /**
     * @return mixed
     */
    public function getIsGlobal()
    {
        return $this->isGlobal;
    }

    /**
     * @param mixed $isGlobal
     */
    public function setIsGlobal($isGlobal)
    {
        $this->isGlobal = $isGlobal;
        return $this;
    }


    /**
     * @return mixed
     */
    public function getConnection()
    {
        return $this->connection;
    }

    /**
     * @param mixed $connection
     * @return Entity
     */
    public function setConnection($connection)
    {
        $this->connection = $connection;
        return $this;
    }

    /**
     * @return BelongsToRelation[]
     */
    public function getBelongsTo()
    {
        return $this->belongsTo;
    }

    /**
     * @param array $belongsTo
     * @return Entity
     */
    public function setBelongsTo($belongsTo)
    {
        $this->belongsTo = $belongsTo;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param mixed $type
     * @return Entity
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getLayout()
    {
        return $this->layout;
    }

    /**
     * @param mixed $layout
     */
    public function setLayout($layout)
    {
        $this->layout = $layout;
        foreach ($this->getFields() as $field) {
            $field->setLayout($layout);
        }
        return $this;
    }

    /**
     * @return mixed
     */
    public function getOptions()
    {
        return $this->options;
    }

    /**
     * @param mixed $options
     */
    public function setOptions($options)
    {
        $this->options = $options;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * @param mixed $label
     */
    public function setLabel($label)
    {
        $this->label = $label;
        return $this;
    }


    /**
     * Entity constructor.
     * @param string $entityKey
     */
    public function __construct($entityKey)
    {
        $this->setEntityKey($entityKey);
    }

    /**
     * @param ExtendFromData
     * @return $this
     */
    public function setExtendedData(ExtendFromData $data) {
        $this->extendData = $data;
        return $this;
    }

    /**
     * @return ExtendFromData
     */
    public function getExtendedData() {
        return $this->extendData;
    }

    public function isExtendedEntity() {
        return !empty($this->extendData);
    }

    /**
     * @return mixed
     */
    public function getTable()
    {
        return $this->table;
    }

    /**
     * @param mixed $table
     * @return Entity
     */
    public function setTable($table)
    {
        $this->table = $table;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getPrimaryKey()
    {
        return $this->primaryKey;
    }

    /**
     * @return mixed
     */
    public function getPrimaryKeyField()
    {
        return $this->getFieldBy(['name' => $this->primaryKey]);
    }

    /**
     * @param mixed $primaryKey
     * @return Entity
     */
    public function setPrimaryKey($primaryKey)
    {
        $this->primaryKey = $primaryKey;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getPermissions()
    {
        return $this->permissions;
    }

    /**
     * @param mixed $permissions
     * @return Entity
     */
    public function setPermissions($permissions)
    {
        $this->permissions = json_decode($permissions);
        return $this;
    }

    /**
     * @return Field mixed
     */
    public function getListingField()
    {
        $field =  $this->getFieldByName($this->listingField);
        if(empty($field)) {
            $field = $this->getFieldBy(['label' => EntityFieldUtil::ENTITY_FIELD_TYPE_TEXT]);
            if (empty($field)) {
                $fields = $this->getFields();
                $field = $this->getPrimaryKeyField();
            }
        }
        return $field;
    }

    public function getThumbnailField()
    {
        return $this->getFieldByName($this->thumbnailField);
    }

    public function getFieldByName($name)
    {
        $field = array_filter($this->fields, function ($field) use ($name) {
            return $field->getName() == $name;
        });
        return array_pop($field);
    }

    public function getFieldBy($conditions)
    {
        $fields = $this->getFieldsBy($conditions);
        return array_pop($fields);
    }

    /**
     * @param $key
     * @return Field|null
     */
    public function getFieldByKey($key) {
        foreach ($this->getFields() as $field) {
            if($field->getKey() == $key) {
                return $field;
            }
        }
        return null;
    }

    /**
     * @param $conditions
     * @return Field[]
     */
    public function getFieldsBy($conditions)
    {
        $fields = array_filter($this->fields, function ($field) use ($conditions) {
            foreach ($conditions as $key => $value) {
                if (!$field->hasProperty($key) || $field->getPropertyValue($key) != $value) {
                    return false;
                }
            }
            return true;
        });
        return array_values($fields);
    }



    public function getRelationsBy($conditions)
    {
        $relations = array_filter($this->relations, function ($relation) use ($conditions) {
            foreach ($conditions as $key => $value) {
                if (!$relation->hasProperty($key) || $relation->getPropertyValue($key) != $value) {
                    return false;
                }
            }
            return true;
        });
        return array_values($relations);
    }

    /**
     * @param mixed $listingField
     */
    public function setListingField($listingField)
    {
        $this->listingField = $listingField;
        return $this;
    }

    public function setThumbnailField($thumbnailField)
    {
        $this->thumbnailField = $thumbnailField;
        return $this;
    }

    /**
     * @return string
     */
    public function getEntityKey()
    {
        return $this->entityKey;
    }


    /**
     * @return HasOneRelation[]
     */
    public function getHasOneRelations()
    {
        return $this->hasOne;
    }

    /**
     * @return BelongsToRelation[]
     */
    public function getBelongToRelation()
    {
        return $this->belongsTo;
    }

    /**
     * @return HasManyRelation[]
     */
    public function getHasManyRelations()
    {
        return $this->hasMany;
    }

    /**
     * @return Relation[]
     */
    public function getSubFormRelations()
    {
        return array_filter($this->getHasManyRelations(), function ($item) {
            return $item->isValidRelation() && $item->getSourceField()->getType() === EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM;
        });
    }

    /**
     * @return Relation[]
     */
    public function getManyToManyRelations() {
        return array_filter($this->getHasManyRelations(), function ($item) {
            return $item->isValidRelation() && $item->getSourceField()->getType() === EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_DYNAMIC_DROPDOWN;
        });
    }



    /**
     * @return Field[]
     */
    public function getFields()
    {
        $fields = [];
        $keys = [];
        $i = 0;
        foreach ($this->fields as $field) {
            $fieldName = $field->getName();

            if (
                ($field->getPlugin() && !AbstractEntityStructureGetter::getPluginService()->install($field->getPlugin())) ||
                (isset($keys[$fieldName]) && $field->getEntityKey() != $field->getOriginalEntityKey())
            ) {
                continue;
            }

            $keys[$fieldName] = $i;
            $fields[] = $field;
            $i++;
        }

        return $fields;
    }

    public function getFieldNames()
    {
        return array_map(function ($field) {
            return $field->getName();
        }, $this->getFields());
    }
    /**
     * @return array
     */
    public function getFieldsAssoc()
    {
        $ret = [];
        foreach ($this->getFields() as $field) {
            $ret[$field->getName()] = $field;
        }
        return $ret;
    }

    /**
     * @param null $entityKey
     */
    public function setEntityKey($entityKey)
    {
        $this->entityKey = $entityKey;
    }


    /**
     * @param Relation $relation
     */
    public function addRelation($relation)
    {
        switch ($relation->getType()) {
            case EntityFieldRelationUtil::HAS_MANY_TYPE:
                $this->hasMany[] = $relation;
                break;
            case EntityFieldRelationUtil::BELONGS_TO_RELATION_TYPE:
                $this->addOrReplaceBelongsToRelation($relation);
                break;
            default:
                $this->hasOne[] = $relation;
        }
        $this->relations[$relation->getName()] = $relation;
    }

    public function addOrReplaceBelongsToRelation($relation)
    {

        $index = -1;

        foreach ($this->belongsTo as $key => $belongsToRelation) {

            if ($belongsToRelation->getName() == $relation->getName()) {
                $index = $key;
                break;
            }
        }

        if ($index != -1) {
            $this->belongsTo[$index] = $relation;
        } else {
            $this->belongsTo[] = $relation;
        }
    }
    /**
     * @param IField $field
     */
    public function addField($field)
    {
        $this->fields[] = $field;
    }

    /**
     * @return Relation[]
     */
    public function getRelations()
    {
        return $this->relations;
    }

    public function loadRelations($relationLevel) {
        $relationLoader = new RelationLoader();
        return $relationLoader->load($this, $relationLevel);
    }

    /**
     * @param $name
     * @return Relation|null
     */
    public function getRelationByName($name) {
        if($this->hasRelation($name)) {
            return $this->relations[$name];
        }
        return null;
    }

    /**
     * @param $name
     * @return bool
     */
    public function hasRelation($name) {
        return isset($this->relations[$name]);
    }

    public function getLayoutFields()
    {
        $ret = [$this->getPrimaryKeyField()];
        if (empty($this->layout )) {
            return  $this->getFields();
        }
        foreach ($this->layout as $field) {

           if ($field['type'] == "Separator") {
                $separtor = new Separator();
                try {
                    $separtor->setLabel($field['attributes']['property']['section_title']['value'] ?: '');
                    // added because separator in edit not displayed .
                    $separtor->setIsUpdate(1);

                } catch (\Exception $e) {
                    $separtor->setLabel('');
                }
                $ret[] = $separtor;
            } else {
                $ret[] = $this->getFieldBy(['id' => $field['id']]);
            }

        }
        if (count($ret) == 1) {
            return  $this->getFields();
        }
        return array_filter($ret);
    }

    /**
     * @return IField[]
     */
    public function getListingFields()
    {
        if(empty($this->listingFields)) {
            foreach ($this->getFields() as $field) {
                if(!in_array($field->getType(),[
                    EntityFieldUtil::ENTITY_FIELD_TYPE_SUBFORM,
                    EntityFieldUtil::ENTITY_FIELD_TYPE_TAGS,
                    EntityFieldUtil::ENTITY_FIELD_TYPE_ASSIGNED_STAFF,
                    EntityFieldUtil::ENTITY_FIELD_TYPE_MULTIPLE_FILE,
                    EntityFieldUtil::ENTITY_FIELD_TYPE_PASSWORD
                ])) {
                    $this->listingFields[] = $field;
                }
            }
        }
        return $this->listingFields;
    }

    /**
     * @return Field
     */
    public function getSubFormField()
    {
        return $this->subFormField;
    }

    /**
     * @param null $subFormField
     * @return Entity
     */
    public function setSubFormField($subFormField)
    {
        $this->subFormField = $subFormField;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getPlugin()
    {
        return $this->plugin;
    }

    /**
     * @param mixed $plugin
     */
    public function setPlugin($plugin)
    {
        $this->plugin = $plugin;
        return $this;
    }

    /**
     * @return ParentEntityData
     */
    public function getParentEntityData()
    {
        return $this->parentEntityData;
    }

    /**
     * @param ParentEntityData $parentEntityData
     * @return Entity
     */
    public function setParentEntityData($parentEntityData)
    {
        $this->parentEntityData = $parentEntityData;
        return $this;
    }

    /**
     * @return array
     */
    public function getChildrenKeys()
    {
        return $this->childrenKeys;
    }

    /**
     * @return array
     */
    public function getChildren()
    {
        $children = [];
        foreach ($this->childrenKeys as $key) {
            if($childEntity = EntityAndRelationCache::getEntityFromCache($key)) {
                $children[] = $childEntity;
            }
        }
        return $children;
    }

    /**
     * @param array $childrenKeys
     * @return Entity
     */
    public function setChildrenKeys($childrenKeys)
    {
        $this->childrenKeys = $childrenKeys;
        return $this;
    }


    public function getExtendedEntityKeyOrEntityKey() {
        return $this->isExtendedEntity() ? $this->getExtendedData()->getEntityKey() : $this->getEntityKey();
    }

    public function __toString(){
        return $this->entityKey;
    }

    public function toArray()
    {
        return [
            'entityKey' => $this->entityKey,
            'fields' => $this->fields,
            'listingField' => $this->listingField,
        ];
    }

    public function getFilterFields()
    {
        $filtered = array_filter($this->getFields(), function ($el) {
            return $el->getIsFilter();
        });

        usort($filtered, function($a, $b) {
            return $a->getOrderIndex() <=> $b->getOrderIndex();
        });
        return $filtered;
    }


    private $filters = [];

    public function setFilters($filters)
    {
        $this->filters = $filters;
        return $this;
    }

    public function getFilters()
    {
        return $this->filters;
    }

    public function getTrashedOptions(): ?array
    {
        $trashed = $this->getOptions();
        if (empty($trashed['trashed'])) {
            return [];
        }
        return $trashed['trashed'];
    }

    /** Soft-delete for fields: field is marked deleted but data remains in the table */
    public function hasSufficientFieldsForDisplay(): bool
    {
        return count($this->getFields()) > 2;
    }
}
