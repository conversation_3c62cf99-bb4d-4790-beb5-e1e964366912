<?php
namespace Izam\Daftra\Common\RelatedEntities;

use Izam\Daftra\Common\Entity\ChildRecordsService;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Dynamic\List\Repository\LocalEntityRepository;
use Izam\DynamicPermissions\DynamicPermissionService;
use Izam\Forms\Tab\GenerateIframePath;
use Izam\Forms\Tab\IframeElement;
use Izam\Forms\Tab\TabElement;
use Izam\Forms\Tab\Tabs;

class ViewChildEntitiesHelper
{
    public static function setTabs($id,$staffId,\Izam\Daftra\Common\EntityStructure\Entity $entity) {
        $tabs = new Tabs('view-tabs');
        $childrenEntities = $entity->getChildren();
        $localEntityRepository = resolve(LocalEntityRepository::class);  

        foreach ($childrenEntities as $child) {
            if (!$child->hasSufficientFieldsForDisplay()){
                continue;
            }
            $hasPermission = DynamicPermissionService::isEmployeeHavePermission(
                $staffId,
                EntityKeyTypesUtil::LOCAL_ENTITY,
                $child->getEntityKey(),
                'view'
            );

            if(!$hasPermission) {
                continue;
            }
            if ((substr($child->getEntityKey(), 0, 3) === 'le_') &&  !$localEntityRepository->isActiveLocalEntity($child->getEntityKey())) {
                continue;
            }
            $count = ChildRecordsService::getChildCount($child, $id);
            if($count == 0) {
                continue;
            }
            $foreignKey = $child->getParentEntityData()->getForeignKeyName();
            $conditions =  $child->getParentEntityData()->getRelation()->getConditions();
            $value = ['entity_key' => $child->getEntityKey(), 'id' => $id, 'foreign_key' => $foreignKey];
            if (!empty($conditions)) {
                $value['conditions'] = $conditions;
            }


            $childTemp = new TabElement($child->getEntityKey(),['label' => $child->getLabel()]);
            $childElement = new IframeElement($child->getEntityKey());
            $value['src'] = GenerateIframePath::getUrl($value);
            $childElement->setValue($value);
            $childTemp->setValue($childElement);
            $tabs->add($childTemp);
        }
        return $tabs;
    }
}