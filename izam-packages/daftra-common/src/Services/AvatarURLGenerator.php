<?php

namespace Izam\Daftra\Common\Services;

use Izam\Aws\Aws;

class AvatarURLGenerator
{
    /**
     * this should always return absolute path, whether its is called from cake or laravel and
     * generated photo from name or uploaded image to s3 or local image
     * */
    static public function generate($name, $id, $size, $url, $isLocal = false, $ignoreResizer = false): string
    {
        if (!$url) {
            $q = base64_encode(http_build_query([
                'n' => $name,
                'id' => $id,
                's' => $size
            ]));

            return getCakeBaseURl() . "avatar.php?q=$q";
        }

        if ($isLocal) {
            return resizeImage(
                "/files/" . SITE_HASH . "/photos/" . $url,
                ['w' => $size, 'h' => $size, 'c' => 1]
            );
        }
        /** handle full_url images like sit-logos */
        if (str_starts_with($url, "http")){
            return $url."?w=$size&h=$size";
        }
        /** use this case in PDF when show S3 img  */
        if ($ignoreResizer){
            return Aws::getProxyUrl($url);
        }
        return resizeImage(
            Aws::getPermanentUrl($url),
            ['w' => $size, 'h' => $size, 'c' => 1]
        );
    }
}
