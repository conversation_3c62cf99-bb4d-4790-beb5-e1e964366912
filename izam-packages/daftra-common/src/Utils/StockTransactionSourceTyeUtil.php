<?php

namespace Izam\Daftra\Common\Utils;

class StockTransactionSourceTyeUtil
{

    const SOURCE_MANUAL = 1;
    const SOURCE_INVOICE = 2;
    const SOURCE_PO = 3;
    const SOURCE_CN = 4;
    const SOURCE_TRANSFER = 5;
    const SOURCE_RR = 6;
    const SOURCE_PR = 7;
    const SOURCE_PDN = 14;

    const SOURCE_BUNDLE_RECON = 10;
    const SOURCE_BUNDLE = 8;
    const SOURCE_REQUISITION = 9;

    //REQUISITION TYPES + 100
    const SOURCE_RQ_MANUAL_INBOUND = 101;
    const SOURCE_RQ_MANUAL_OUTBOUND = 102;
    const SOURCE_RQ_INVOICE = 103;
    const SOURCE_RQ_INVOICE_REFUND = 104;
    const SOURCE_RQ_INVOICE_CREDIT_NOTE = 105;
    const SOURCE_RQ_PURCHASE_ORDER = 106;
    const SOURCE_RQ_PURCHASE_REFUND = 107;
    const SOURCE_RQ_PURCHASE_DEBIT_NOTE = 115;
    const SOURCE_RQ_TRANSFER_REQUISITION = 108;
    const SOURCE_RQ_TRANSFER_INBOUND = 109;
    const SOURCE_RQ_TRANSFER_OUTBOUND = 110;
    const SOURCE_RQ_POS_INBOUND = 111;
    const SOURCE_RQ_POS_OUTBOUND = 112;
    const SOURCE_RQ_STOCKTAKING_OUT = 113;
    const SOURCE_RQ_STOCKTAKING_IN = 114;


    public static function getPendingStockSourceType(): array
    {
        return [
            self::SOURCE_MANUAL,
            self::SOURCE_INVOICE,
            self::SOURCE_RQ_MANUAL_INBOUND,
            self::SOURCE_RQ_MANUAL_OUTBOUND,
            self::SOURCE_RQ_INVOICE,
//            self::SOURCE_RQ_INVOICE_REFUND,
//            self::SOURCE_RQ_INVOICE_CREDIT_NOTE,
            self::SOURCE_RQ_POS_OUTBOUND,
        ];
    }

}
