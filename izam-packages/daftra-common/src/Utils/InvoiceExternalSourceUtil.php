<?php

namespace Izam\Daftra\Common\Utils;


class InvoiceExternalSourceUtil
{
    const SOURCE_DESKTOP_POS = 'desktop_pos';
    const SOURCE_MOBILE_POS = 'mobile_pos';
    const SOURCE_SALLA = 'salla';
    const SOURCE_API = 'api';
    const SOURCE_APP = 'app';
    const SOURCE_DEBIT = 'debit_note';

    public static function getSources()
    {
        return [
            self::SOURCE_DESKTOP_POS,
            self::SOURCE_MOBILE_POS,
            self::SOURCE_SALLA,
            self::SOURCE_API,
            self::SOURCE_APP,
            self::SOURCE_DEBIT
        ];
    }

    public static function isValidSource($source)
    {
        return str_contains($source, self::SOURCE_APP) || in_array($source, self::getSources());
    }

    /**
     * get external source text for db
     * @param $source
     * @param $appId
     * @return mixed|string|null
     */
    public static function getSourceDBValue($source, $appId = null)
    {
        if (!self::isValidSource($source)) {
            return null;
        }
        return $appId ? self::SOURCE_APP . '_' . $appId : $source;
    }

    public static function getSourceList()
    {
        $sources = [
            self::SOURCE_MOBILE_POS => __t('Mobile POS'),
            self::SOURCE_API => __t('API')
        ];
        if (Domain_Name_Only == 'daftra') {
            $sources = array_merge($sources, [
                self::SOURCE_DESKTOP_POS => __t('Desktop Application'),
                self::SOURCE_SALLA => __t('Salla')
            ]);
        }
        return $sources;
    }

    public static function getSourceForPreview($source)
    {
        if (!self::isValidSource($source)) {
            return null;
        }
        if (str_contains($source, self::SOURCE_APP)) {
            list(, $appId) = explode('_', $source);
            return sprintf(__t("App:%s"), $appId); // TODO get app name
        }
        return match ($source) {
            self::SOURCE_DESKTOP_POS => __t('Desktop Application'),
            self::SOURCE_MOBILE_POS => __t('Mobile POS'),
            self::SOURCE_SALLA => __t('Salla'),
            self::SOURCE_API => __t('API'),
            self::SOURCE_DEBIT => __t('Debit Note'),
            default => null
        };
    }
}