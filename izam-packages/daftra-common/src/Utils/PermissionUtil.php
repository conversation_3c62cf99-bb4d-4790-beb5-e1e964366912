<?php

namespace Izam\Daftra\Common\Utils;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class PermissionUtil
{
//Client
    CONST Clients_Add_New_Client = 1;
    CONST Clients_View_his_own_Clients = 2;
    CONST Clients_View_All_Clients = 3;
    CONST Edit_And_delete_his_own_added_clients = 18;
    CONST Edit_Delete_all_clients = 19;
    CONST View_All_Activity_Logs = 99;
    CONST View_His_Activity_Logs = 100;
    CONST EDIT_CLIENT_Settings = 158;
    CONST VIEW_ALL_CLIENTS_REPORTS = 159;
    CONST VIEW_HIS_OWN_CLIENTS_REPORTS = 160;
//Invoice
    CONST Invoices_Add_New_Invoices = 4;
    CONST Invoices_Edit_All_Invoices = 5;
    CONST Invoices_Edit_his_own_Invoices = 6;
    CONST Invoices_View_Invoices_Details = 7;
    CONST Invoices_View_All_Invoices = 24;
    CONST Edit_Taxes_Settings = 36;
    CONST Edit_Payment_Options = 35;
//payments
    CONST Invoices_Add_Invoice_Payments = 25;
    CONST Invoices_Add_Payments_to_All = 67;
    CONST INVOICE_PAYMENTS_EDIT_REMOVE_ALL = 165;
    CONST INVOICE_PAYMENTS_EDIT_REMOVE_HIS_OWN = 166;


    CONST INVOICE_ALL_PRODUCTS = 124;
    CONST INVOICE_ADD_NEW_BAILER = 164;

//CONST View_Payment_History = 43;
    CONST View_All_Tax_Report = 44;
//CONST View_his_own_Tax_Report = 45;
//CONST View_All_Payment_Report = 46;
//CONST View_his_own_Payment_Report = 47;
//CONST View_his_own_Invoices_Report = 48;
//CONST View_All_Invoices_Report = 49;
//Staff
    CONST Staff_Add_New_Staffs = 16;
    CONST Staff_Edit_Staffs = 17;
    const Staff_View_Staffs = 273;
    const VIEW_ONLY_DEPARTMENT_CHART = 275;
    CONST Add_Roles = 20;
    CONST Edit_Roles = 21;


//CONST View_Roles = 22;
//Product
    CONST Proudcts_Add_New_Proudct = 26;
    CONST View_his_own_Products = 27;
    CONST View_All_Products = 28;
    CONST Edit_Delete_all_Products = 29;
    CONST Edit_And_delete_his_own_added_Products = 30;
    CONST ALLOW_MINIMUM_PRICE = 135;
    CONST EDIT_STOCK_TRANSACTION_PRICE = 145;
    CONST VIEW_STOCK_TRANSACTION_PRICE = 144;

//Settings
    CONST Create_email_templates = 31;           //Deprecated & Replaced By Edit_General_Settings
//CONST View_email_templates = 32;
    CONST Edit_Delete_email_templates = 33;      //Deprecated & Replaced By Edit_General_Settings
    CONST Create_sms_templates = 121;            //Deprecated & Replaced By Edit_General_Settings
    CONST Edit_Delete_sms_templates = 120;       //Deprecated & Replaced By Edit_General_Settings
    CONST Edit_sms_sender_name = 122;            //Deprecated & Replaced By Edit_General_Settings
    CONST Edit_General_Settings = 34;
    CONST Settings_Set_Taxes = 36;
    CONST Settings_Plugin_Manager = 66;          //Deprecated & Replaced By Edit_General_Settings
// Documents
    CONST View_Terms_and_Conditions = 37;        //Deprecated & Replaced By Edit_General_Settings
//CONST Add_Terms_and_Conditions = 38;
    CONST Edit_Delete_Terms_and_Conditions = 39; //Deprecated & Replaced By Edit_General_Settings
//CONST View_File_and_Documents = 40;
    CONST Add_File_Documents = 41;               //Deprecated & Replaced By Edit_General_Settings
    CONST Edit_delete_File_and_Documents = 42;   //Deprecated & Replaced By Edit_General_Settings
//Time Tracking
    CONST Add_New_Project = 50;
    CONST Edit_Delete_Projects = 51;
    CONST Add_New_Time_Activity = 52;
    CONST Edit_Delete_Time_Activity = 53;
    CONST Enter_Timesheet = 54;
    CONST Edit_All_Timesheets = 55;
    CONST Track_All_Staffs_Times = 60;
    CONST View_All_Hourly_Rates = 119;//New
    CONST View_His_Hourly_Rates = 112;
    CONST View_All_Time_Sheets = 113;

    CONST VIEW_ALL_WORK_ORDERS = 114;
    CONST VIEW_HIS_WORK_ORDERS = 115;
    CONST EDIT_REMOVE_HIS_WORK_ORDERS = 168;
    CONST EDIT_REMOVE_ALL_WORK_ORDERS = 167;
    CONST ADD_NEW_WORK_ORDERS = 116;
    CONST EDIT_WORK_ORDER_STATUS = 125;
    CONST ASSIGN_TRANSACTION_TO_WORK_ORDERS = 720;

    CONST ADD_NEW_ASSETS = 117;


    CONST CHANGE_DEFAULT_TREASURY = 118;
    CONST VIEW_ASSIGNED_TREASURY = 123;
    CONST ADD_TREASURY = 161;
    CONST ADD_TREASURY_BANK = 162;

    CONST TRANSFER_STOCK_PERMISSION = 111;

//CONST Edit_All_Time_Sheets = 114;
//CONST Edit_Time_Tracking_Settings = 56;
//CONST View_Projects = 57;
//CONST View_Time_Activity = 58;
//CONST View_Times = 59;

//Expenses
    CONST Add_New_Expenses = 61;
    CONST Edit_Delete_all_expenses = 62;
    CONST Edit_delete_his_own_expenses = 63;
    CONST View_his_own_expenses = 64;
    CONST View_All_expenses = 65;

//Expenses
    CONST Add_New_Incomes = 68;
    CONST Edit_Delete_all_incomes = 69;
    CONST Edit_delete_his_own_incomes = 70;
    CONST View_his_own_incomes = 71;
    CONST View_All_incomes = 72;

    CONST ADD_EXPENSE_CATEGORY = 163;
//Inventory
    CONST Add_New_Purchase_Orders = 74;
    CONST Edit_Delete_All_Purchase_Orders = 75;
    CONST Edit_Delete_his_own_created_Purchase_Orders = 76;
    CONST View_his_own_created_Purchase_Orders = 77;
    CONST View_All_Purchase_Orders = 78;
    CONST Track_Inventory = 85;
    CONST Adjust_Product_Inventory = 84;

    CONST ADD_STOCK_REQUEST = 600;
    CONST EDIT_STOCK_REQUEST = 601;
    CONST DELETE_STOCK_REQUEST = 602;
    CONST VIEW_STOCK_REQUESTS = 603;
    CONST APPROVE_OR_REJECT_STOCK_REQUEST = 604;


//Supplier
    CONST Add_New_Supplier = 79;
    CONST View_his_own_Suppliers = 80;
    CONST View_All_Suppliers = 81;
    CONST Edit_And_delete_his_own_added_suppliers = 82;
    CONST Edit_Delete_all_suppliers = 83;


//FollowUp
    CONST Add_Notes_Attachments_For_All_Clients = 88;
    CONST Edit_Delete_His_Own_Attachments_Notes_Only = 89;
    CONST Edit_Delete_All_Notes_Attachments = 90;
    CONST Add_Notes_Attachments_For_His_Assigned_Clients_Only = 91;
    CONST View_His_Own_Notes_Attachments_Only = 92;
    CONST View_All_Attachments_ANd_Notes_For_His_Assigned_Clients = 93;
    CONST View_All_Attachments_And_Notes_For_All_Clients = 97;

    CONST Re_Assign_All_Clients_To_Other_Staff_Members = 94;
    CONST Re_Assign_Only_His_Assigned_Clients = 95;
    CONST Assign_Clients_To_Staff = 96;

// Group Price
    /** Based On: DAFTRA-6927 **/
//CONST GroupPrice_Add_New_GroupPrice = 101; // Add New GroupPrice
//CONST Edit_Delete_all_GroupPrices = 102; // Edit Delete All GroupPrices
//CONST Edit_And_delete_his_own_added_GroupPrices = 103; //
//CONST View_All_GroupPrice = 104;
//CONST View_His_GroupPrice = 105;

    CONST Add_Edit_Price_List = 101;
    CONST View_Price_List = 102;
    CONST Delete_Price_List = 103;

    CONST View_His_Own_Reports = 110;
    CONST CHANGE_SALES_PERSON = 119;

//Cost Centers Permissions
    CONST VIEW_COST_CENTERS = 126;
    CONST MANAGE_COST_CENTERS = 127;

//Closed Periods Permissions
    CONST MANAGE_CLOSED_PERIODS = 128;
    CONST VIEW_CLOSED_PERIODS = 129;
// Time Tracking

//Journals
    CONST MANAGE_JOURNAL_ACCOUNTS = 130;
    CONST VIEW_ALL_JOURNALS = 131;
    CONST VIEW_OWN_JOURNALS = 132;
    CONST MANAGE_ALL_JOURNALS = 133;
    CONST MANAGE_OWN_JOURNALS = 134;
    const DELETE_JOURNALS = 722;
    CONST MANAGE_DRAFT_JOURNALS = 420;

//POS Permissions
    CONST OPEN_ALL_POS_SESSIONS = 136;
    CONST OPEN_OWN_POS_SESSIONS = 137;
    CONST CLOSE_ALL_POS_SESSIONS = 138;
    CONST CLOSE_OWN_POS_SESSIONS = 139;
    CONST VIEW_ALL_POS_SESSIONS = 140;
    CONST VIEW_OWN_POS_SESSIONS = 141;
    CONST VALIDATE_ALL_POS_SESSIONS = 142;
    CONST VALIDATE_OWN_POS_SESSIONS = 143;
    CONST EDIT_PRODUCT_PRICE_POS = 146;
    CONST ADD_DISCOUNT_POS = 147;
    const DELETE_POS_SESSION = 402;

//Requisitions
    CONST REQUISITION_ADD = 148;
    CONST REQUISITION_MODIFY = 149;
    CONST REQUISITION_VIEW = 150;


//APPOINTMENTS
    CONST ADD_APPOINTMENTS_FOR_ALL_CLIENTS = 151;
    CONST ADD_APPOINTMENTS_FOR_HIS_CLIENTS = 154;
    CONST EDIT_DELETE_HIS_OWN_APPOINTMENTS_ONLY = 152;
    CONST EDIT_DELETE_ALL_APPOINTMENTS = 153;
    CONST VIEW_HIS_APPOINTMENTS_ONLY = 155;
    CONST View_HIS_CLIENTS_APPOINTMENTS = 156;
    CONST VIEW_ALL_APPOINTMENTS = 157;

//HRM
    CONST MANAGE_HRM_SYSTEM = 169;
    CONST MANAGE_EMPLOYMENT_LEVELS_DEPRECATED = 170;
    CONST MANAGE_EMPLOYMENT_TYPES_DEPRECATED = 171;
    CONST MANAGE_DEPARTMENTS_DEPRECATED = 172;
    CONST MANAGE_DESIGNATIONS_DEPRECATED = 173;

//HRM_ATTENDANCE
    CONST TAKE_EMPLOYEES_ATTENDANCE = 174;
    CONST TAKE_HIS_OWN_ATTENDANCE = 175;
    CONST PULL_MACHINE_ATTENDANCE_LOG = 176;
    CONST IMPORT_ATTENDANCE_LOG = 177;
    CONST VIEW_ALL_THE_ATTENDANCE_LOG = 178;
    CONST VIEW_HIS_OWN_ATTENDANCE_LOG = 179;
    CONST MANAGE_ATTENDANCE_SETTINGS = 180;
    CONST MANAGE_ATTENDANCE_PERMISSION = 181;
    CONST DELETE_ATTENDANCE_LOG = 182;
    CONST CALCULATE_ATTENDANCE_DAY = 183;
    CONST EDIT_ATTENDANCE_DAY = 184;
    CONST CREATE_ATTENDANCE_SHEET = 185;
    CONST VIEW_HIS_OWN_ATTENDANCE_SHEET = 186;
    CONST VIEW_OTHER_ATTENDANCE_SHEETS = 187;
    CONST CHANGE_ATTENDANCE_SHEET_STATUS = 188;
    CONST DELETE_ATTENDANCE_SHEET = 189;
    CONST VIEW_ATTENDANCE_REPORT = 190;

    CONST ADD_LEAVE_APPLICATION = 500;
    CONST EDIT_DELETE_HIS_OWN_LEAVE_APPLICATIONS = 501;
    CONST EDIT_DELETE_ALL_LEAVE_APPLICATIONS = 502;
    CONST VIEW_HIS_OWN_LEAVE_APPLICATIONS = 503;
    CONST VIEW_ALL_LEAVE_APPLICATION = 504;
    CONST APPROVE_OR_REJECT_LEAVE_APPLICATION = 505;
    CONST VIEW_HIS_TEAM_LEAVE_APPLICATIONS = 506;
    


//HRM_PAYROLL
    CONST MANAGE_LOANS_AND_INSTALLMENT_SETTINGS = 274;
    CONST MANAGE_PAYROLL_SETTINGS = 191;
    CONST VIEW_PAY_RUN = 192;
    CONST VIEW_HIS_OWN_PAYSLIPS = 193;
    CONST CREATE_PAY_RUN = 194;
    CONST DELETE_PAY_RUN = 195;
    CONST APPROVE_PAYSLIPS = 196;
    CONST DELETE_PAYSLIPS = 197;
    CONST EDIT_PAYSLIPS = 198;
    CONST PAY_PAYSLIPS = 199;
    CONST DELETE_PAY_RUN_PAYMENTS = 200;
    CONST ADD_PAYROLL_CONTRACT = 201;
    CONST VIEW_PAYROLL_CONTRACT = 202;
    CONST EDIT_DELETE_PAYROLL_CONTRACTS = 203;
    CONST PAYROLL_CONTRACT_NOTIFICATION = 204;
    CONST CONTRACT_NOTIFICATIONS = 205;
    CONST VIEW_HIS_OWN_CONTRACT = 440;
    CONST EDIT_DELETE_HIS_OWN_PAYROLL_CONTRACTS = 441;
//HRM CHEQUE CYCLE

    CONST ADD_CHEQUE_BOOK = 269;
    CONST VIEW_CHEQUE_BOOK = 270;
    CONST EDIT_DELETE_CHEQUE_BOOK = 271;
    CONST MANAGE_RECEIVABLE_CHEQUES = 272;

    CONST MANAGE_INSURANCE_AGENTS = 2073;

// REQUEST TYPES
    CONST MANAGE_REQUESTS = 300;
    CONST MANAGE_REQUESTS_SETTINGS = 301;
    CONST CREATE_REQUESTS = 302;
    CONST VIEW_REQUESTS = 303;
    CONST UPDATE_REQUESTS = 304;
    CONST DELETE_REQUESTS = 305;
    CONST APPROVE_REJECT_REQUESTS = 306;

    /*
     * Credit Permissions
     */

    CONST MANAGE_PACKAGES = 310;
    CONST MANAGE_CREDIT_CHARGES = 311;
    CONST MANAGE_CREDIT_USAGE = 312;
    CONST MANAGE_CREDIT_SETTING = 313;

    CONST MANAGE_MEMBERSHIP = 314;
    CONST MANAGE_MEMBERSHIP_SETTING = 315;

    CONST VIEW_CLIENT_ATTENDANCE = 316;
    CONST MANAGE_CLIENT_ATTENDANCE = 317;
    //Shop Front
    CONST MANAGE_SHOP_FRONT_CONTENT = 320;

    //COMMISSION PERMISSION
    CONST MANAGE_COMMISSION_RULES  = 340;
    CONST VIEW_ALL_SALES_COMMISSIONS = 341;
    CONST VIEW_HIS_SALES_COMMISSIONS = 342;
    CONST MANAGE_COMMISSION_SHEETS = 343;

    //Purchase cycle permissions
    CONST MANAGE_PURCHASE_REQUESTS              = 344;
    CONST APPROVE_OR_REJECT_PURCHASE_REQUESTS   = 345;
    CONST MANAGE_PURCHASE_QUOTATIONS            = 346;
    CONST APPROVE_OR_REJECT_PURCHASE_QUOTATIONS = 347;
    CONST CONVERT_PURCHASES_QUOTATION_TO_ORDER  = 348;
    CONST MANAGE_PURCHASE_ORDERS                = 349;
    const CONVERT_PURCHASES_ORDER_TO_INVOICES   = 350;

    //WORKFLOW_PLUGIN permissions
    CONST MANAGE_WORKFLOW_SETTINGS              = 351;
    CONST ADD_WORKFLOW                          = 352;
    CONST EDIT_AND_DELETE_ALL_WORKFLOWS         = 353;
    CONST EDIT_AND_DELETE_HIS_OWN_WORKFLOWS     = 354;
    CONST VIEW_ALL_WORKFLOWS                    = 355;
    CONST VIEW_HIS_OWN_WORKFLOWS                = 356;
    CONST ASSIGN_TRANSACTION_TO_WORKFLOWS       = 721;

    //Client Loyalty
    CONST MANAGE_LOYALTY_RULES = 357;
    CONST REDEEM_LOYALTY_POINTS = 358;

    CONST VIEW_INVOICE_PROFIT = 400;

    //BANK TRANSACTION
    CONST DELETE_BANK_TRANSACTION                = 401;
    CONST LIST_BANK_TRANSACTION                  = 402;
    CONST MATCH_BANK_TRANSACTION                 = 403;
    CONST UN_MATCH_BANK_TRANSACTION              = 404;
    CONST EXCLUDE_BANK_TRANSACTION               = 405;
    CONST UNDO_EXCLUDE_BANK_TRANSACTION          = 406;
    CONST IMPORT_BANK_TRANSACTION                = 407;
    CONST EXPORT_BANK_TRANSACTION                = 408;

    //Rental PLugin
    CONST MANAGE_RENTAL_SETTINGS                 = 409;
    CONST MANAGE_RESERVATION_ORDERS              = 410;
    CONST VIEW_RESERVATION_ORDERS                = 411;
    CONST DELETE_RESERVATION_ORDERS              = 523;



    CONST INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS = 412;
    CONST INVOICES_EDIT_DATE = 413;

    //ESTIMATES
    CONST ESTIMATES_ADD_NEW_TO_ALL_CLIENTS = 414;
    CONST ESTIMATES_ADD_NEW_TO_HIS_OWN_CLIENTS = 415;

    CONST ESTIMATES_EDIT_DELETE_ALL_ESTIMATES = 416;
    CONST ESTIMATES_EDIT_DELETE_HIS_OWN_ESTIMATES = 417;

    CONST ESTIMATES_VIEW_ALL_ESTIMATES = 418;
    CONST ESTIMATES_VIEW_HIS_OWN_ESTIMATES = 419;

    // SALES ORDER
    CONST SALES_ORDER_ADD_NEW_TO_ALL_CLIENTS = 421;
    CONST SALES_ORDER_ADD_NEW_TO_HIS_OWN_CLIENTS = 422;

    CONST SALES_ORDER_EDIT_DELETE_ALL_SALES_ORDER = 423;
    CONST SALES_ORDER_EDIT_DELETE_HIS_OWN_SALES_ORDER = 424;

    CONST SALES_ORDER_VIEW_ALL_SALES_ORDER = 425;
    CONST SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER = 426;

    //SALES DEBIT NOTE
    CONST DEBIT_NOTE_ADD_NEW_TO_ALL_CLIENTS = 427;
    CONST DEBIT_NOTE_ADD_NEW_TO_HIS_OWN_CLIENTS = 428;

    CONST DEBIT_NOTE_EDIT_DELETE_ALL = 429;
    CONST DEBIT_NOTE_EDIT_DELETE_HIS_OWN = 430;

    CONST DEBIT_NOTE_VIEW_ALL = 431;
    CONST DEBIT_NOTE_VIEW_HIS_OWN = 432;

    CONST ADD_EDIT_DELETE_DRAFT_EXPENSE = 442;

    CONST ADD_EDIT_DELETE_DRAFT_INCOME = 443;

    CONST SYNC_E_INVOICE_KSA = 520;

    CONST SYNC_E_INVOICE_ETA = 521;

    CONST CHANGE_SITE_VERSION = 522;

    // Manufacturing
    CONST MANAGE_MANUFACTURING_SETTINGS = 700;
    CONST MANAGE_PRODUCTION_ROUTINGS = 701;
    CONST MANAGE_WORKSTATIONS = 702;
    CONST ADD_PRODUCTION_PLANS = 703;
    CONST VIEW_HIS_OWN_PRODUCTION_PLANS = 704;
    CONST VIEW_ALL_PRODUCTION_PLANS = 705;
    CONST EDIT_DELETE_ALL_PRODUCTION_PLANS = 706;
    CONST EDIT_DELETE_HIS_OWN_PRODUCTION_PLANS = 707;
    CONST ADD_NEW_BILL_OF_MATERIAL = 708;
    CONST VIEW_ALL_BILL_OF_MATERIALS = 709;
    CONST VIEW_HIS_OWN_BILL_OF_MATERIALS = 710;
    CONST EDIT_DELETE_ALL_BILL_OF_MATERIALS = 711;
    CONST EDIT_DELETE_HIS_OWN_BILL_OF_MATERIALS = 712;
    CONST ADD_NEW_MANUFACTURING_ORDER = 713;
    CONST VIEW_ALL_MANUFACTURING_ORDERS = 714;
    CONST VIEW_HIS_OWN_MANUFACTURING_ORDERS = 715;
    CONST EDIT_DELETE_ALL_MANUFACTURING_ORDERS = 716;
    CONST EDIT_DELETE_HIS_OWN_MANUFACTURING_ORDERS = 717;

    // department based for attendance and Payroll
    CONST VIEW_HIS_DEPARTMENT_ATTENDANCE = 730;
    CONST VIEW_HIS_DEPARTMENT_PAYSLIPS = 731;
    CONST EDIT_HIS_DEPARTMENT_ATTENDANCE = 732;
    CONST EDIT_HIS_DEPARTMENT_PAYSLIPS = 733;

    //New Booking
    CONST ADD_NEW_BOOKING = 800;
    CONST VIEW_ALL_BOOKINGS = 801;
    CONST VIEW_HIS_OWN_BOOKINGS = 802;
    CONST EDIT_DELETE_ALL_BOOKINGS = 803;
    CONST EDIT_DELETE_HIS_OWN_BOOKINGS = 804;
    CONST MANAGE_EMPLOYEES_BREAKS = 805;
    CONST MANAGE_BOOKING_SETTINGS = 806;
    CONST SYNC_JORDANIAN_INVOICE = 734;

    public static function getViewAllPermissionsByEntity($entityKey)
    {
        /**  */
        switch ($entityKey) {
            case EntityKeyTypesUtil::CLIENT_ENTITY_KEY :
                return self::Clients_View_All_Clients;
            case EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY :
                return self::View_All_Suppliers;
            case EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY :
                return self::VIEW_ALL_LEAVE_APPLICATION;
        }
        return false;
    }

    public static function getViewHisOwnPermissionsByEntity($entityKey)
    {
        /**  */
        switch ($entityKey) {
            case EntityKeyTypesUtil::CLIENT_ENTITY_KEY :
                return self::Clients_View_his_own_Clients;
            case EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY :
                return self::View_his_own_Suppliers;
            case EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY :
                return self::VIEW_HIS_OWN_LEAVE_APPLICATIONS;
        }
        return false;
    }


}
