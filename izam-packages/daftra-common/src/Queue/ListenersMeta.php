<?php


namespace Izam\Daftra\Common\Queue;

class ListenersMeta
{
    const LISTENER = [
        ListenerTypeUtil::SAVE_JOURNAL_LISTENER => [
            'platform' => EventPlatformUtil::CAKE,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::INSERT_CAKE_NOTIFICATION_HANDLER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::UPDATE_PRODUCT_AVERAGE_PRICE_LISTENER => [
            'platform' => EventPlatformUtil::CAKE,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::MANUFACTURING_ORDER_LISTENER => [
            'platform' => EventPlatformUtil::CAKE,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
	    ListenerTypeUtil::UPDATE_FOR_ORDER_REQUESTED_LISTENER => [
		    'platform' => EventPlatformUtil::CAKE,
		    'runtime_env' => EventEnvUtil::BACKGROUND,
	    ],
        ListenerTypeUtil::APP_HTTP_HANDLER => [
		    'platform' => EventPlatformUtil::LARAVEL,
		    'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::APP_TRIGGER_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::CLEAR_ENTITY_CACHE_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::CREATE_WORKFLOW_ENTITY_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::CREATE_REQUEST_TYPE_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::UPDATE_REQUEST_TYPE_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::UPDATE_WORKFLOW_ENTITY_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::DELETE_WORKFLOW_ENTITY_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::GENERATE_LOYALTY_POINTS => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::DELETE_WORKFLOW_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::DELETE_EMPLOYEE_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::DELETE_CONTRACT_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::CALCULATE_AUTOMATICALLY_GENERATED_INVOICE_FOR_RESERVATION_ORDER_HANDLER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::CREATE_PAYRUN_ENTITY_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::DELETE_APP_RECORD_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::DELETE_ADDITIONAL_FIELDS_LISTENER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::UPDATE_SALES_ORDER_STATUS => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::ITEM_GROUP_CREATED => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::BOM_CREATED => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::WORKSTATION_UPDATED => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::STOCK_REQUEST_DELETED => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::MANUFACTURING_ORDER_CREATED => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::MANUFACTURING_ORDER_UPDATED => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::MO_MATERIALS_AVERAGE_PRICE_UPDATED_LISTENER => [
            'platform' => EventPlatformUtil::CAKE,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::MANUFACTURING_ORDER_INDIRECT_COST_CREATED => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::MANUFACTURING_ORDER_INDIRECT_COST_UPDATED => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::MANUFACTURING_ORDER_INDIRECT_COST_DELETED => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::SEND_EMAIL_TEMPLATE_TO_APPROVED_PAYSLIP => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::SEND_LEAVE_APPLICATION_EMAIL => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::SEND_APPROVED_LEAVE_APPLICATION_EMAIL => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::SEND_REJECTED_LEAVE_APPLICATION_EMAIL => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::SEND_REQUEST_EMAIL => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::SEND_APPROVED_REQUEST_EMAIL => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::SEND_REJECTED_REQUEST_EMAIL => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::BACKGROUND,
        ],
        ListenerTypeUtil::UPDATE_PRODUCTION_PLAN_MANUFACTURING_ORDERS => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::DELETE_PRODUCTION_PLAN_MANUFACTURING_ORDERS => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::DELETE_PRODUCTION_PLAN_ITEM_MANUFACTURING_ORDER => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::UPDATE_BRAND_PRODUCTS => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::DELETE_LEASE_CONTRACT => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::CREATE_CONTRACT_INSTALLMENT => [
            'platform'  => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::DELETE_CONTRACT_INSTALLMENT => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
        ListenerTypeUtil::REMATCH_MULTI_CYCLE_APPROVAL_CONFIGURATIONS => [
            'platform' => EventPlatformUtil::LARAVEL,
            'runtime_env' => EventEnvUtil::FOREGROUND,
        ],
    ];
}
