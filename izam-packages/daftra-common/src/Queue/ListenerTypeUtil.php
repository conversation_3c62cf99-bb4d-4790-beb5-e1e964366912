<?php

namespace Izam\Daftra\Common\Queue;

use App\Listeners\MoMaterialsAveragePriceUpdatedListener;
use App\Modules\LocalEntity\Queue\Listeners\CreateBomEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\CreateContractInstallmentListener;
use App\Modules\LocalEntity\Queue\Listeners\CreateItemGroupEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\CreateManufacturingOrderEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\CreateManufacturingOrderIndirectCostEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\CreatePayrunEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\CreateRequestTypeEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\DeleteContractInstallmentListener;
use App\Modules\LocalEntity\Queue\Listeners\DeleteLeaseContractListener;
use App\Modules\LocalEntity\Queue\Listeners\DeleteManufacturingOrderIndirectCostEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\DeleteProductionPlanItemManufacturingOrder;
use App\Modules\LocalEntity\Queue\Listeners\DeleteProductionPlanManufacturingOrders;
use App\Modules\LocalEntity\Queue\Listeners\DeleteWorkflowListener;
use App\Modules\LocalEntity\Queue\Listeners\RematchMultiCycleApprovalConfigurations;
use App\Modules\LocalEntity\Queue\Listeners\UpdateProductionPlanManufacturingOrders;
use App\Modules\Template\Listeners\Payslip\SendEmailTemplateToApprovedPayslipListener;
use App\Modules\LocalEntity\Queue\Listeners\StockRequestDeletedListener;
use App\Modules\LocalEntity\Queue\Listeners\UpdateBrandProductsListener;
use App\Modules\LocalEntity\Queue\Listeners\UpdateManufacturingOrderEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\UpdateManufacturingOrderIndirectCostEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\UpdateRequestTypeEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\UpdateWorkFlowEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\UpdateWorkstationEntityListener;
use App\Modules\Template\Listeners\LeaveApplication\SendApprovedLeaveApplicationEmail;
use App\Modules\Template\Listeners\LeaveApplication\SendRejectedLeaveApplicationEmail;
use App\Modules\Template\Listeners\LeaveApplication\SendLeaveApplicationEmail;
use App\Modules\Template\Listeners\Request\SendApprovedRequestEmail;
use App\Modules\Template\Listeners\Request\SendRejectedRequestEmail;
use App\Modules\Template\Listeners\Request\SendRequestEmail;
use App\Queue\Listeners\ClearEntityCacheListener;
use App\Queue\Listeners\DeleteContractListener;
use App\Queue\Listeners\DeleteEmployeeListener;
use Izam\Daftra\AppManager\Listeners\DeleteAppRecordListener;
use Izam\Daftra\Invoice\Listeners\UpdateSalesOrderStatusHandler;
use Izam\Entity\Listeners\DeleteAdditionalFieldListener;

class ListenerTypeUtil
{
    // cake listeners
    const SAVE_JOURNAL_LISTENER = '\App\Listeners\SaveJournalListener';
    const UPDATE_PRODUCT_AVERAGE_PRICE_LISTENER = '\App\Listeners\UpdateProductAveragePriceListener';
    const MANUFACTURING_ORDER_LISTENER = '\App\Listeners\ManufactureOrderListener';
	const UPDATE_FOR_ORDER_REQUESTED_LISTENER = '\App\Listeners\UpdateForOrderRequestedListener';
	const APP_HTTP_HANDLER = '\App\Queue\Listeners\AppHttpHandler';

    // laravel listeners
    const INSERT_CAKE_NOTIFICATION_HANDLER = '\App\Queue\Listeners\InsertCakeNotificationHandler';
    const APP_TRIGGER_LISTENER = '\App\Queue\Listeners\AppTriggerHandler';
    const CLEAR_ENTITY_CACHE_LISTENER = ClearEntityCacheListener::class;

    const DELETE_WORKFLOW_LISTENER = DeleteWorkflowListener::class;

    const CREATE_WORKFLOW_ENTITY_LISTENER = "App\Modules\LocalEntity\Queue\Listeners\CreateWorkFlowEntityListener";
    const UPDATE_WORKFLOW_ENTITY_LISTENER = UpdateWorkFlowEntityListener::class;
    const DELETE_WORKFLOW_ENTITY_LISTENER = "App\Modules\LocalEntity\Queue\Listeners\DeleteWorkFlowEntityListener";

    const CREATE_REQUEST_TYPE_LISTENER = CreateRequestTypeEntityListener::class;

    const UPDATE_REQUEST_TYPE_LISTENER = UpdateRequestTypeEntityListener::class;

    const GENERATE_LOYALTY_POINTS = 'App\Queue\Listeners\GenerateLoyaltyPointsHandler';

    const CALCULATE_AUTOMATICALLY_GENERATED_INVOICE_FOR_RESERVATION_ORDER_HANDLER =
        '\App\Modules\Rental\Listeners\Reservation\CalculateAutomaticallyGeneratedInvoiceForReservationOrderHandler';

    const DELETE_EMPLOYEE_LISTENER = DeleteEmployeeListener::class;

    const DELETE_CONTRACT_LISTENER = DeleteContractListener::class;

    const CREATE_PAYRUN_ENTITY_LISTENER  = CreatePayrunEntityListener::class;

    const DELETE_APP_RECORD_LISTENER = DeleteAppRecordListener::class;

    const DELETE_ADDITIONAL_FIELDS_LISTENER = DeleteAdditionalFieldListener::class;

    const UPDATE_SALES_ORDER_STATUS = UpdateSalesOrderStatusHandler::class;

    const ITEM_GROUP_CREATED = CreateItemGroupEntityListener::class;

    const BOM_CREATED = CreateBomEntityListener::class;

    const WORKSTATION_UPDATED = UpdateWorkstationEntityListener::class;

    const STOCK_REQUEST_DELETED = StockRequestDeletedListener::class;

    const MANUFACTURING_ORDER_CREATED = CreateManufacturingOrderEntityListener::class;

    const MANUFACTURING_ORDER_UPDATED = UpdateManufacturingOrderEntityListener::class;
    const MO_MATERIALS_AVERAGE_PRICE_UPDATED_LISTENER = MoMaterialsAveragePriceUpdatedListener::class;

    const MANUFACTURING_ORDER_INDIRECT_COST_CREATED = CreateManufacturingOrderIndirectCostEntityListener::class;
    const MANUFACTURING_ORDER_INDIRECT_COST_UPDATED = UpdateManufacturingOrderIndirectCostEntityListener::class;
    const MANUFACTURING_ORDER_INDIRECT_COST_DELETED = DeleteManufacturingOrderIndirectCostEntityListener::class;

    const UPDATE_PRODUCTION_PLAN_MANUFACTURING_ORDERS = UpdateProductionPlanManufacturingOrders::class;
    const DELETE_PRODUCTION_PLAN_MANUFACTURING_ORDERS = DeleteProductionPlanManufacturingOrders::class;

    const DELETE_PRODUCTION_PLAN_ITEM_MANUFACTURING_ORDER = DeleteProductionPlanItemManufacturingOrder::class;

    const SEND_EMAIL_TEMPLATE_TO_APPROVED_PAYSLIP = SendEmailTemplateToApprovedPayslipListener::class;

    const SEND_LEAVE_APPLICATION_EMAIL = SendLeaveApplicationEmail::class;
    const SEND_APPROVED_LEAVE_APPLICATION_EMAIL = SendApprovedLeaveApplicationEmail::class;
    const SEND_REJECTED_LEAVE_APPLICATION_EMAIL = SendRejectedLeaveApplicationEmail::class;

    const SEND_REQUEST_EMAIL = SendRequestEmail::class;
    const SEND_APPROVED_REQUEST_EMAIL = SendApprovedRequestEmail::class;
    const SEND_REJECTED_REQUEST_EMAIL = SendRejectedRequestEmail::class;

    const UPDATE_BRAND_PRODUCTS = UpdateBrandProductsListener::class;

    const DELETE_LEASE_CONTRACT = DeleteLeaseContractListener::class;

    const CREATE_CONTRACT_INSTALLMENT = CreateContractInstallmentListener::class;
    const DELETE_CONTRACT_INSTALLMENT = DeleteContractInstallmentListener::class;

    const REMATCH_MULTI_CYCLE_APPROVAL_CONFIGURATIONS = RematchMultiCycleApprovalConfigurations::class;

}
