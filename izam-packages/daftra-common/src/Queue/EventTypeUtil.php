<?php

namespace Izam\Daftra\Common\Queue;

class EventTypeUtil
{
    const APP_TRIGGER_HTTP_EVENT = 'app_trigger_http_event';
    const JOURNAL_ENTITY_SAVED = 'journal_entity_saved';
    const AVERAGE_PRICE_CHANGED = 'average_price_changed';
	const UPDATE_FOR_ORDER_REQUESTED = 'update_for_order_requested';
	const TEST_EVENT = 'test_event';
    const NOTIFICATION_INSERTED = 'notification_inserted';

    const INVOICE_CREATED = 'invoice_created';
    const INVOICE_UPDATED = 'invoice_updated';
    const INVOICE_DELETED = 'invoice_deleted';

    const CREDIT_NOTE_CREATED = 'credit_note_created';
    const CREDIT_NOTE_UPDATED = 'credit_note_updated';
    const CREDIT_NOTE_DELETED = 'credit_note_deleted';

    const DEBIT_NOTE_CREATED = 'debit_note_created';
    const DEBIT_NOTE_UPDATED = 'debit_note_updated';
    const DEBIT_NOTE_DELETED = 'debit_note_deleted';

    const ESTIMATE_CREATED = 'estimate_created';
    const ESTIMATE_UPDATED = 'estimate_updated';
    const ESTIMATE_DELETED = 'estimate_deleted';

    const SALES_ORDER_CREATED = 'sales_order_created';
    const SALES_ORDER_UPDATED = 'sales_order_updated';
    const SALES_ORDER_DELETED = 'sales_order_deleted';

    const REFUND_RECEIPT_CREATED = 'refund_receipt_created';
    const REFUND_RECEIPT_UPDATED = 'refund_receipt_updated';
    const REFUND_RECEIPT_DELETED = 'refund_receipt_deleted';

    const INVOICE_PAYMENT_CREATED = 'invoice_payment_created';
    const INVOICE_PAYMENT_UPDATED = 'invoice_payment_updated';
    const INVOICE_PAYMENT_DELETED = 'invoice_payment_deleted';

    const EXPENSE_CREATED = 'expense_created';
    const EXPENSE_UPDATED = 'expense_updated';
    const EXPENSE_DELETED = 'expense_deleted';

    const PURCHASE_ORDER_CREATED = 'purchase_order_created';
    const PURCHASE_ORDER_UPDATED = 'purchase_order_updated';
    const PURCHASE_ORDER_DELETED = 'purchase_order_deleted';

    const CLIENT_CREATED = 'client_created';
    const CLIENT_UPDATED = 'client_updated';
    const CLIENT_DELETED = 'client_deleted';

    const JOURNAL_CREATED = 'journal_created';
    const JOURNAL_UPDATED = 'journal_updated';
    const JOURNAL_DELETED = 'journal_deleted';

    const SUPPLIER_CREATED = 'supplier_created';
    const SUPPLIER_UPDATED = 'supplier_updated';
    const SUPPLIER_DELETED = 'supplier_deleted';

    const PRODUCT_CREATED = 'product_created';
    const PRODUCT_UPDATED = 'product_updated';
    const PRODUCT_DELETED = 'product_deleted';


    const ENTITY_STRUCTURE_SAVED = 'entity_structure_saved';

    const WORKFLOW_TYPE_UPDATED = 'workflow_type_updated';
    const WORKFLOW_TYPE_CREATED = 'workflow_type_created';
    const WORKFLOW_TYPE_DELETED = 'workflow_type_deleted';

    const REQUEST_TYPE_CREATED = 'request_type_created';

    const REQUEST_TYPE_UPDATED = 'request_type_updated';

    const WORKFLOW_DELETED = 'workflow_deleted';

    const RESERVATION_ORDER_CREATED = 'reservation_order_created';

    const EMPLOYEE_DELETED = 'employee_deleted';

    const CONTRACT_DELETED = 'contract_deleted';

    const PAYRUN_CREATED = 'payrun_created';

    const ITEM_GROUP_CREATED = 'item_group_created';

    const BOM_CREATED = 'bom_created';

    const WORKSTATION_UPDATED = 'workstation_updated';


    const STOCK_REQUEST_DELETED = 'stock_request_deleted';

    const MANUFACTURING_ORDER_CREATED = 'manufacturing_order_created';
    const MANUFACTURING_ORDER_UPDATED = 'manufacturing_order_updated';
    const MO_MATERIALS_AVERAGE_PRICE_UPDATED = 'mo_materials_average_price_updated';

    const MANUFACTURING_ORDER_INDIRECT_COST_CREATED = 'manufacturing_order_indirect_cost_created';
    const MANUFACTURING_ORDER_INDIRECT_COST_UPDATED = 'manufacturing_order_indirect_cost_updated';
    const MANUFACTURING_ORDER_INDIRECT_COST_DELETED = 'manufacturing_order_indirect_cost_deleted';

    const PRODUCTION_PLAN_CREATED = 'production_plan_created';

    const PRODUCTION_PLAN_UPDATED = 'production_plan_updated';
    const PRODUCTION_PLAN_DELETED = 'production_plan_deleted';

    const PRODUCTION_PLAN_ITEM_DELETED = 'production_plan_item_deleted';

    const PAYSLIP_APPROVED = 'payslip_approved';

    const LEAVE_APPLICATION_CREATED = 'leave_application_created';
    const LEAVE_APPLICATION_APPROVED = 'leave_application_approved';
    const LEAVE_APPLICATION_REJECTED = 'leave_application_rejected';

    const REQUEST_CREATED = 'request_created';
    const REQUEST_APPROVED = 'request_approved';
    const REQUEST_REJECTED = 'request_rejected';
    const REQUEST_UPDATED = 'request_updated';

    const BRAND_UPDATED = 'brand_updated';

    const LEASE_CONTRACT_DELETED = 'lease_contract_deleted';

    const CONTRAC_INSTALLMENT_CREATED = 'contract_installment_created';
    const CONTRACT_INSTALLMENT_DELETED = 'contract_installment_deleted';

    const LEAVE_APPLICATION_UPDATED = "leave_application_updated";

    const MULTI_CYCLE_APPROVAL_CONFIGURATION_CHANGED = "multi_cycle_approval_configuration_changed";


}
