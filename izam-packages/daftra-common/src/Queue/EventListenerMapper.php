<?php

namespace Izam\Daftra\Common\Queue;

use App\Modules\LocalEntity\Queue\Listeners\CreateRequestTypeEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\CreateWorkFlowEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\DeleteWorkFlowEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\UpdateRequestTypeEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\UpdateWorkFlowEntityListener;
use App\Modules\LocalEntity\Queue\Listeners\DeleteWorkflowListener;

class EventListenerMapper
{
    public static $mapping = [
        EventTypeUtil::JOURNAL_ENTITY_SAVED => [
            ListenerTypeUtil::SAVE_JOURNAL_LISTENER,
        ],
        EventTypeUtil::TEST_EVENT => [
            ListenerTypeUtil::INSERT_CAKE_NOTIFICATION_HANDLER,
        ],
        EventTypeUtil::APP_TRIGGER_HTTP_EVENT => [
            ListenerTypeUtil::APP_HTTP_HANDLER,
        ],
        EventTypeUtil::NOTIFICATION_INSERTED => [
            ListenerTypeUtil::INSERT_CAKE_NOTIFICATION_HANDLER,
        ],
        EventTypeUtil::AVERAGE_PRICE_CHANGED => [
            ListenerTypeUtil::UPDATE_PRODUCT_AVERAGE_PRICE_LISTENER,
            ListenerTypeUtil::MANUFACTURING_ORDER_LISTENER,
        ],
        EventTypeUtil::UPDATE_FOR_ORDER_REQUESTED => [
            ListenerTypeUtil::UPDATE_FOR_ORDER_REQUESTED_LISTENER,
        ],
        EventTypeUtil::ENTITY_STRUCTURE_SAVED => [
          ListenerTypeUtil::CLEAR_ENTITY_CACHE_LISTENER
        ],
        EventTypeUtil::WORKFLOW_TYPE_CREATED => [
          CreateWorkFlowEntityListener::class
        ],
        EventTypeUtil::REQUEST_TYPE_CREATED => [
          CreateRequestTypeEntityListener::class
        ],
        EventTypeUtil::REQUEST_TYPE_UPDATED => [
            UpdateRequestTypeEntityListener::class
        ],
        EventTypeUtil::WORKFLOW_TYPE_UPDATED => [
          UpdateWorkFlowEntityListener::class,
          ListenerTypeUtil::CLEAR_ENTITY_CACHE_LISTENER
        ],
        EventTypeUtil::WORKFLOW_TYPE_DELETED => [
          DeleteWorkFlowEntityListener::class
        ],
        EventTypeUtil::WORKFLOW_DELETED => [
            DeleteWorkflowListener::class
        ],
        EventTypeUtil::INVOICE_CREATED => [
            ListenerTypeUtil::GENERATE_LOYALTY_POINTS,
            ListenerTypeUtil::UPDATE_SALES_ORDER_STATUS,
        ],
        EventTypeUtil::INVOICE_DELETED => [
            ListenerTypeUtil::UPDATE_SALES_ORDER_STATUS,
        ],
        EventTypeUtil::INVOICE_UPDATED => [
            ListenerTypeUtil::UPDATE_SALES_ORDER_STATUS,
        ],
        EventTypeUtil::RESERVATION_ORDER_CREATED => [
            ListenerTypeUtil::CALCULATE_AUTOMATICALLY_GENERATED_INVOICE_FOR_RESERVATION_ORDER_HANDLER,
        ],

        EventTypeUtil::EMPLOYEE_DELETED => [
          ListenerTypeUtil::DELETE_EMPLOYEE_LISTENER
        ],

        EventTypeUtil::CONTRACT_DELETED => [
          ListenerTypeUtil::DELETE_CONTRACT_LISTENER
        ],

        EventTypeUtil::PAYRUN_CREATED => [
            ListenerTypeUtil::CREATE_PAYRUN_ENTITY_LISTENER
        ],
        EventTypeUtil::ITEM_GROUP_CREATED => [
            ListenerTypeUtil::ITEM_GROUP_CREATED
        ],
        EventTypeUtil::BOM_CREATED => [
            ListenerTypeUtil::BOM_CREATED
        ],
        EventTypeUtil::WORKSTATION_UPDATED => [
            ListenerTypeUtil::WORKSTATION_UPDATED
        ],
        EventTypeUtil::STOCK_REQUEST_DELETED => [
            ListenerTypeUtil::STOCK_REQUEST_DELETED
        ],
        EventTypeUtil::MANUFACTURING_ORDER_CREATED => [
            ListenerTypeUtil::MANUFACTURING_ORDER_CREATED
        ],
        EventTypeUtil::MANUFACTURING_ORDER_UPDATED => [
            ListenerTypeUtil::MANUFACTURING_ORDER_UPDATED
        ],
        EventTypeUtil::MO_MATERIALS_AVERAGE_PRICE_UPDATED => [
            ListenerTypeUtil::MO_MATERIALS_AVERAGE_PRICE_UPDATED_LISTENER
        ],
        EventTypeUtil::MANUFACTURING_ORDER_INDIRECT_COST_CREATED => [
            ListenerTypeUtil::MANUFACTURING_ORDER_INDIRECT_COST_CREATED
        ],
        EventTypeUtil::MANUFACTURING_ORDER_INDIRECT_COST_UPDATED=> [
            ListenerTypeUtil::MANUFACTURING_ORDER_INDIRECT_COST_UPDATED
        ],
        EventTypeUtil::MANUFACTURING_ORDER_INDIRECT_COST_DELETED => [
            ListenerTypeUtil::MANUFACTURING_ORDER_INDIRECT_COST_DELETED
        ],
        EventTypeUtil::PRODUCTION_PLAN_CREATED => [
            ListenerTypeUtil::UPDATE_PRODUCTION_PLAN_MANUFACTURING_ORDERS
        ],
        EventTypeUtil::PRODUCTION_PLAN_UPDATED => [
            ListenerTypeUtil::UPDATE_PRODUCTION_PLAN_MANUFACTURING_ORDERS
        ],
        EventTypeUtil::PAYSLIP_APPROVED => [
            ListenerTypeUtil::SEND_EMAIL_TEMPLATE_TO_APPROVED_PAYSLIP
        ],
        EventTypeUtil::LEAVE_APPLICATION_CREATED => [
            ListenerTypeUtil::SEND_LEAVE_APPLICATION_EMAIL
        ],
        EventTypeUtil::LEAVE_APPLICATION_APPROVED => [
            ListenerTypeUtil::SEND_APPROVED_LEAVE_APPLICATION_EMAIL
        ],
        EventTypeUtil::LEAVE_APPLICATION_REJECTED => [
            ListenerTypeUtil::SEND_REJECTED_LEAVE_APPLICATION_EMAIL
        ],
        EventTypeUtil::REQUEST_CREATED => [
            ListenerTypeUtil::SEND_REQUEST_EMAIL
        ],
        EventTypeUtil::REQUEST_APPROVED => [
            ListenerTypeUtil::SEND_APPROVED_REQUEST_EMAIL
        ],
        EventTypeUtil::REQUEST_REJECTED => [
            ListenerTypeUtil::SEND_REJECTED_REQUEST_EMAIL
        ],
        EventTypeUtil::PRODUCTION_PLAN_ITEM_DELETED => [
            ListenerTypeUtil::DELETE_PRODUCTION_PLAN_ITEM_MANUFACTURING_ORDER
        ],
        EventTypeUtil::BRAND_UPDATED => [
            ListenerTypeUtil::UPDATE_BRAND_PRODUCTS
        ],
        EventTypeUtil::LEASE_CONTRACT_DELETED => [
            ListenerTypeUtil::DELETE_LEASE_CONTRACT
        ],
        EventTypeUtil::CONTRAC_INSTALLMENT_CREATED => [
            ListenerTypeUtil::CREATE_CONTRACT_INSTALLMENT
        ],
        EventTypeUtil::CONTRACT_INSTALLMENT_DELETED => [
            ListenerTypeUtil::DELETE_CONTRACT_INSTALLMENT
        ],
        EventTypeUtil::LEAVE_APPLICATION_UPDATED => [
            ListenerTypeUtil::REMATCH_MULTI_CYCLE_APPROVAL_CONFIGURATIONS
        ],
        EventTypeUtil::MULTI_CYCLE_APPROVAL_CONFIGURATION_CHANGED => [
            ListenerTypeUtil::REMATCH_MULTI_CYCLE_APPROVAL_CONFIGURATIONS
        ],
        EventTypeUtil::REQUEST_UPDATED => [
            ListenerTypeUtil::REMATCH_MULTI_CYCLE_APPROVAL_CONFIGURATIONS
        ],
    ];
    /**
     * @param $eventType string
     */
    public static function getEventListeners($eventType) {
        $listeners = [];
        if(isset(self::$mapping[$eventType])) {
            $listeners = self::$mapping[$eventType];
        }
	    return $listeners;
    }

    public static function getMetaForListener($handlerName)
    {
        return ListenersMeta::LISTENER[$handlerName];
    }

    public static function addEventListener($eventType, $handler): void
    {
        self::$mapping[$eventType][] = $handler;
    }

    public static function hasEventListener($eventType): bool
    {
        return isset(self::$mapping[$eventType]);
    }
}
