<?php

namespace Izam\Entity\Components\ListingPageHeader\EntityComponents;

use App\Facades\Plugins;
use App\Utils\PermissionUtil;
use App\Utils\PluginUtil;
use Izam\Entity\Components\ListingPageHeader\Interfaces\IEntityComponent;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Button;
use Izam\View\Form\Element\SplitButtonDropdowns;
use Izam\View\Form\Element\MultipleSelectAction;

class StaffComponent extends DefaultEntityComponent implements IEntityComponent
{

    public static function generateRightPageHeaderButtons(): array
    {

        if (ifPluginActive(PluginUtil::HRM_PLUGIN)) {
            $splitBtn = new SplitButtonDropdowns('Split-Btn');
            $splitBtn->setLabel(__t('Add New'))
                ->setOption('theme', 'dropdown')
                ->setUrl('/v2/owner/staff/create/user');

            $manualAnchor = new Anchor('Add-User');
            $manualAnchor->setLabel(__t('User'))
                ->setAttribute('href', '/v2/owner/staff/create/user');

            $autoAnchor = new Anchor('Add-Employee');
            $autoAnchor->setLabel(__t('Employee'))
                ->setAttribute('href', '/v2/owner/staff/create/employee');

            $splitBtn->add($manualAnchor)
                ->add($autoAnchor);
        } else {
            $splitBtn = new Anchor('Add-User');
            $splitBtn->setLabel(sprintf(__t('New %s'), __t('User')))
                ->setAttribute('href', '/v2/owner/staff/create/user');
        }


        $anchorExport = new Anchor('Export-Staff');
        $anchorExport->setLabel(__t('Export'))
            ->setOption('theme', 'light')
            ->setOption('icon', 'file-export')
            ->setAttribute('href', '/v2/owner/export/staff?ids=all');

        $searchBtn = new Button('search-btn');
        $searchBtn->setOption('theme', 'hideInDesktop');

        $returnContent = [
            $searchBtn,
            $anchorExport,
            $splitBtn,
        ];

        if ( ifPluginActive(PluginUtil::HRM_PLUGIN) && check_permission(PermissionUtil::MANAGE_HRM_SYSTEM) && IS_PC ) {
            $setupHrm = new Anchor('setup-hrm');

            $setupHrm->setLabel(__t('Setup HRM Wizard'))
                ->setOption('theme', 'light')
                ->setOption('icon', 'cog')
                ->setAttribute('href', route('owner.setup.hrm.wizard'));

            $returnContent = [
                $searchBtn,
                $setupHrm,
                $anchorExport,
                $splitBtn,
            ];
        }


        return $returnContent;
    }

    public static function generateLeftPageHeaderButtons(): array
    {
        $multipleSelectAction = new MultipleSelectAction('Multiple-Select-Action');
        // get all query paramas and set them as conditions_link
        $queryParams = urldecode(http_build_query($_GET));
        $multipleSelectAction->setOption('conditions_link', $queryParams);

        $btnPrintPdf = new Button('Export-Staff');
        $btnPrintPdf->setOption('icon', 'export')
            ->setOption('action', '/v2/owner/export/staff?ids=all')
            ->setOption('method', 'GET')
            ->setAttribute('data-la-separate', 'comma')
            ->setLabel(__t('Export'));

        $multipleSelectAction->add($btnPrintPdf);

        return [
            $multipleSelectAction
        ];
    }

    public static function pageButtonsWithoutData(): array
    {
        $addBtn = new Anchor('Add-Employee');
        $addBtn->setLabel(sprintf(__t('Add %s'), __t('Employee')))
            ->setOption('icon', 'plus')
            ->setOption('theme', 'defaultSuccess')
            ->setAttribute('href', '/v2/owner/staff/create/user')
            ->setAttribute('target', '_top');
           

        return [
            $addBtn,
        ];
    }
}
