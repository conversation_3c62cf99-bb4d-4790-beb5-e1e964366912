<?php

namespace Izam\Entity\Components\ListingPageHeader\EntityComponents;


use Izam\Entity\Components\ListingPageHeader\Interfaces\IEntityComponent;
use Izam\View\Form\Element\Anchor;
use Izam\View\Form\Element\Button;
use Izam\View\Form\Element\MultipleSelectAction;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class SalesOrderComponent extends DefaultEntityComponent implements IEntityComponent
{

    public static function generateRightPageHeaderButtons(): array
    {
        $anchor = new Anchor('Add-Sales-Order');
        $anchor->setLabel(sprintf(__t('Add %s'), __t('Sales Order')))
            ->setAttribute('href', '/owner/invoices/add_sales_order');

        $searchBtn = new Button('search-btn');
        $searchBtn->setOption('theme', 'hideInDesktop');

        return [
            $searchBtn,
            $anchor,
        ];
    }

    public static function generateLeftPageHeaderButtons(): array
    {
        $multipleSelectAction = new MultipleSelectAction('Multiple-Select-Action');

        $btnDelete = new Button('Delete');
        $btnDelete->setOption('icon', 'delete')
            ->setLabel(__t('Delete'))
            ->setOption('action', '/owner/bulk/update')
            ->setOption('method', 'POST')
            ->setAttributes([
                'data-la-confirm' => json_encode([
                    'message' => sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t('Sales Orders')),
                    'icon' => 'mdi mdi-trash-can-outline',
                    'color' => 'danger',
                ]),
                'data-la-data' => json_encode([
                    'entity_key' => EntityKeyTypesUtil::SALES_ORDER,
                    'entity_name' => __t('Sales Orders'),
                    'target_url' => '/api2/sales_orders/__id__',
                    'title' => __t('Sales Orders'),
                    'back_url' => '/v2/owner/entity/sales_order/list',
                    'action_type' => 'Deleting',
                    'action' => 'DELETE',
                    '_method' => 'POST',
                    'filter_query' => json_encode(request()->query()),
                    '_token' => csrf_token(),
                    'title_for_layout' => sprintf(__t('Deleting %s', true), __t('Sales Orders', true)),
                    'breadcrumbs' => json_encode([
                        [
                            'title' => __t('Sales Orders', true),
                            'link' => '/v2/owner/entity/sales_order/list',
                        ],
                        ['title' => __t('Delete')]
                    ])
                ]),
            ]);


        $multipleSelectAction
            ->add($btnDelete);

        $btnPdf = new Button('pdf');
        $btnPdf->setOption('icon', 'file-pdf-box')
            ->setOption('action', '/owner/invoices/multi_pdf_sales_orders')
            ->setOption('method', 'POST')
            ->setAttribute('data-la-separate', 'comma')
            ->setLabel(__t('Print PDF'));


        $multipleSelectAction
            ->add($btnPdf);


        $btnExport = new Button('Export');
        $btnExport->setOption('icon', 'export')
            ->setOption('action', '/owner/invoices/export_sales_orders')
            ->setOption('method', 'POST')
            ->setAttribute('data-la-separate', 'comma')
            ->setLabel(__t('Export'))
            ->setAttribute('data-la-input', '[name="data[Invoice][id]"]')
            ->setAttribute('data-get-ids-ajax', json_encode([
                'url' => '/v2/owner/entity/get_ids/' . EntityKeyTypesUtil::SALES_ORDER,
                'method' => 'POST',
                'data' => json_encode(array_merge(request()->query(), ['_token' => csrf_token()])),
            ]));


        $multipleSelectAction
            ->add($btnExport);

        return [
            $multipleSelectAction
        ];
    }

    public static function pageButtonsWithoutData(): array
    {
        $addBtn = new Anchor('Add-Sales-Order');
        $addBtn->setLabel(sprintf(__t('Add %s'), __t('Sales Order')))
            ->setOption('icon', 'plus')
            ->setOption('theme', 'defaultSuccess')
            ->setAttribute('href', '/owner/invoices/add_sales_order');

        return [
            $addBtn,
        ];
    }
}