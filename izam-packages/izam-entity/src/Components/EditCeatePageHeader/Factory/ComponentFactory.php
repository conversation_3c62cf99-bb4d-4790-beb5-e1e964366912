<?php

namespace Izam\Entity\Components\EditCeatePageHeader\Factory;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\ContractInstallmentComponent;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\DefaultEntityComponent;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\LeaseContractComponent;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\MultiApprovalCycleConfiguration;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\RequestComponent;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\ReservationOrderComponent;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\SmtpEmailAddressComponent;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\UnitComponent;
use Izam\Entity\Components\EditCeatePageHeader\EntityComponents\WorkFlowComponent;
use Izam\Entity\Components\EditCeatePageHeader\Interfaces\IComponentFactory;
use Izam\Entity\Components\EditCeatePageHeader\Interfaces\IEntityComponent;

class ComponentFactory implements IComponentFactory
{
    public static function getEntityComponent(string $entityKey): IEntityComponent
    {
        if (str_contains($entityKey,'le_workflow-type'))
            return new WorkFlowComponent($entityKey);

        return match ($entityKey) {
            EntityKeyTypesUtil::RENTAL_UNIT => new UnitComponent(),
            EntityKeyTypesUtil::RENTAL_RESERVATION_ORDER => new ReservationOrderComponent(),
            EntityKeyTypesUtil::SMTP_EMAIL_ADDRESS => new SmtpEmailAddressComponent(),
            EntityKeyTypesUtil::REQUEST_ENTITY_KEY => new RequestComponent(),
            EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => new MultiApprovalCycleConfiguration(),
            EntityKeyTypesUtil::CONTRACT_INSTALLMENT => new ContractInstallmentComponent(),
            EntityKeyTypesUtil::LEASE_CONTRACT => new LeaseContractComponent(),
            default => new DefaultEntityComponent(),
        };
    }

}