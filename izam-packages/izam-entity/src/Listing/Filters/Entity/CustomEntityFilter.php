<?php

namespace Izam\Entity\Listing\Filters\Entity;

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Entity\Listing\Filters\StaffPermissionFilter;
use Izam\Entity\Listing\Filters\Entity\ContractListingFilter;

class CustomEntityFilter
{
    private static $entityFilters = [
        EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY => [
            'plugin' => PluginUtil::HRM_ATTENDANCE_PLUGIN,
            'setting' => null,
            'class' => LeaveApplicationListingFilter::class,
        ],
        EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY => [
            'plugin' => PluginUtil::InventoryPlugin,
            'setting' => SettingsUtil::ENABLE_STOCK_REQUESTS,
            'class' => StockRequestListingFilter::class,
        ],
        EntityKeyTypesUtil::PURCHASE_INVOICE => [
            'plugin' => null,
            'setting' => null,
            'class' => PurchaseInvoiceListingFilter::class,
        ],
        EntityKeyTypesUtil::REQUEST_ENTITY_KEY => [
            'plugin' => PluginUtil::REQUESTS_PLUGIN,
            'setting' => null,
            'class' => RequestListingFilter::class,
        ],
        EntityKeyTypesUtil::PURCHASE_REFUND => [
            'plugin' => null,
            'setting' => null,
            'class' => PurchaseInvoiceListingFilter::class,
        ],
        EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY => [
            'plugin' => PluginUtil::InventoryPlugin,
            'setting' => null,
            'class' => ItemGroupListingFilter::class,
        ],
        EntityKeyTypesUtil::STAFF_ENTITY_KEY => [
            'plugin' => null,
            'setting' => null,
            'class' => StaffListingFilter::class,
        ],
        EntityKeyTypesUtil::PAYROLL_CONTRACT => [
            'plugin' => PluginUtil::HRM_PAYROLL_PLUGIN,
            'setting' => null,
            'class' => ContractListingFilter::class,
        ],
        EntityKeyTypesUtil::CLIENT_ENTITY_KEY => [
            'plugin' => null,
            'setting' => null,
            'class' => ClientListingFilter::class,
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY => [
            'plugin' => PluginUtil::MANUFACTURING_PLUGIN,
            'setting' => null,
            'class' => ManufacturingOrdersListingFilter::class,
        ],
        EntityKeyTypesUtil::MANUFACTURING_ORDER_INDIRECT_COST => [
            'plugin' => PluginUtil::MANUFACTURING_PLUGIN,
            'setting' => null,
            'class' => ManufacturingOrderIndirectCostListingFilter::class
        ],
        EntityKeyTypesUtil::JOURNAL => [
            'plugin' => null,
            'setting' => null,
            'class' => JournalListingFilter::class,
        ],
        EntityKeyTypesUtil::PRODUCTION_PLAN => [
            'plugin' => PluginUtil::MANUFACTURING_PLUGIN,
            'setting' => null,
            'class' => ProductionPlanListingFilter::class,
        ],
        EntityKeyTypesUtil::LEASE_CONTRACT => [
            'plugin' => PluginUtil::LEASE_CONTRACT_PLUGIN,
            'setting' => null,
            'class' => LeaseContractListingFilter::class,
        ],
        EntityKeyTypesUtil::CONTRACT_INSTALLMENT => [
            'plugin' => PluginUtil::LEASE_CONTRACT_PLUGIN,
            'setting' => null,
            'class' => ContractInstallmentListingFilter::class,
        ],
        EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION => [
            'plugin' => null,
            'setting' => null,
            'class' => MultiCycleApprovalConfigurationListingFilter::class,
        ],
    ];

    public function __construct()
    {
    }

    public static function hasCustomFilters($entityKey): bool{
        return isset(self::$entityFilters[$entityKey]);
    }

    public static function get(string $entityKey): array
    {
        $filters = [];

        if (!in_array($entityKey, static::getCustomStaffPermissionFilterEntities())) {
            $filters[] = resolve(StaffPermissionFilter::class);
        }

        $currentEntity = self::$entityFilters[$entityKey] ?? null;

        if (!$currentEntity) return $filters;

        $currentEntityFilterClass = self::$entityFilters[$entityKey]['class'];
        $currentEntityPlugin = self::$entityFilters[$entityKey]['plugin'];

        $pluginCheck = empty($currentEntityPlugin) || ifPluginActive($currentEntityPlugin);

        //@TODO: Check if settings enabled

        if (isset($currentEntityFilterClass) && $pluginCheck) {
            $filters[] = resolve($currentEntityFilterClass);
        }

        return $filters;
    }

    private static function getCustomStaffPermissionFilterEntities()
    {
        return [
            EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY,
            EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY,
            EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY,
            EntityKeyTypesUtil::STOCK_REQUEST_ENTITY_KEY,
            EntityKeyTypesUtil::PRODUCTION_PLAN,
        ];
    }
}
