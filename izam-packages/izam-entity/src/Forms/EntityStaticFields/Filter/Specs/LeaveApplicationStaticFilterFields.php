<?php

namespace Izam\Entity\Forms\EntityStaticFields\Filter\Specs;

use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationService;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\View\Form\Element\Select;
use Izam\Entity\Repository\DynamicRepo;
use Izam\View\Form\Element\DynamicDropdown;
use Izam\Entity\Forms\EntityStaticFields\Filter\Interfaces\IStaticFieldFilter;
use App\Repositories\LeaveTypeRepository;
use App\Services\LeaveApplicationService;
use Izam\Daftra\Common\Utils\LeaveApplicationStatusUtil;
use Izam\Daftra\Common\Utils\MultiCycleApprovalUtil;
use Izam\View\Form\Element\AutoSuggest;
use Laminas\Form\Element\Hidden;

class LeaveApplicationStaticFilterFields implements IStaticFieldFilter
{

    public function generateElements(): array
    {
        //Employee , Department , shift , Employment Type , Employment Level , Designation , Branches , Date , status
        $formFilters =[
            [
                'spec' => [
                    'name' => 'filter[staff_leave_application.staff_info.department_id]',
                    'type' => DynamicDropdown::class,
                    'options' => [
                        'label' => sprintf(__t('Select %s'), __t('Department')),
                        'order' => 6,
                        'advanced' => true,
                        'empty_option' => sprintf(__t('Select %s'), __t('Department')),
                        'target_class' => DynamicRepo::class,
                        "find_method" => ['name' => 'getByEntityKey', 'params' => ['department',['deleted_at'=> null]]],
                        'property' => 'name'
                    ],
                    'attributes' => [
                        'id' => "applicant-department-id-filter",
                        'empty_option' => sprintf(__t('Select %s'), __t('Department')),
                        'placeholder' => sprintf(__t('Select %s'), __t('Department')),
                    ],
                ],
            ],
            [
                'spec' => [
                    'name' => 'filter[staff_leave_application.staff_info.designation_id]',
                    'type' => DynamicDropdown::class,
                    'options' => [
                        'label' => __t('Designation'),
                        'order' => 6,
                        'advanced' => true,
                        'empty_option' => sprintf(__t('Select %s'), __t('Designation')),
                        'target_class' =>  DynamicRepo::class,
                        "find_method" => ['name' => 'getByEntityKey', 'params' => ['designation',['deleted_at'=> null]]],
                        'property' => 'name'
                    ],
                    'attributes' => [
                        'id' => "applicant-designation-id-filter",
                        'empty_option' => sprintf(__t('Select %s'), __t('Designation')),
                        'placeholder' => sprintf(__t('Select %s'), __t('Designation')),
                    ],
                ],
            ],
            [
                'spec' => [
                    'name' => 'filter[staff_leave_application.staff_info.employment_level_id]',
                    'type' => DynamicDropdown::class,
                    'options' => [
                        'label' => __t("Employment Level"),
                        'order' => 6,
                        'advanced' => true,
                        'empty_option' => sprintf(__t('Select %s'),__t("Employment Level")),
                        'target_class' =>  DynamicRepo::class,
                        "find_method" => ['name' => 'getByEntityKey', 'params' => ['employment_level',['deleted_at'=> null]]],
                        'property' => 'name'
                    ],
                    'attributes' => [
                        'id' => "applicant-employment_level-id-filter",
                        'empty_option' => sprintf(__t('Select %s'),__t("Employment Level")),
                        'placeholder' => sprintf(__t('Select %s'),__t("Employment Level")),
                    ],
                ],
            ],
            [
                'spec' => [
                    'name' => 'filter[staff_leave_application.staff_info.employment_type_id]',
                    'type' => DynamicDropdown::class,
                    'options' => [
                        'label' => __t("Employment Type"),
                        'order' => 6,
                        'advanced' => true,
                        'empty_option' => sprintf(__t('Select %s'),__t("Employment Type")),
                        'target_class' =>  DynamicRepo::class,
                        "find_method" => ['name' => 'getByEntityKey', 'params' => ['employment_type',['deleted_at'=> null]]],
                        'property' => 'name'
                    ],
                    'attributes' => [
                        'id' => "applicant-employment_type-id-filter",
                        'empty_option' => sprintf(__t('Select %s'),__t("Employment Type")),
                        'placeholder' => sprintf(__t('Select %s'),__t("Employment Type")),
                    ],
                ],
            ],
        ];
        if (ifPluginActive(PluginUtil::BranchesPlugin)) {
            if (function_exists('getStaffBranchesSuspended')) {
                $branches = getStaffBranchesSuspended();
            } else {
                $branches = \App\Facades\Branch::getStaffBranchesSuspended()->toArray();
            }
            $formFilters[] = [
                'spec' => [
                    'name' => 'filter[staff_leave_application.branch_id]',
                    'type' => Select::class,
                    'options' => [
                        'label' => __t('Branch'),
                        'order' => 6,
                        'advanced' => true,
                        'empty_option' => sprintf(__t('Select %s'),__t('Branch')),
                        'options' => $branches
                    ],
                    'attributes' => [
                        'id' => "applicant-branch-id-filter",
                        'placeholder' => sprintf(__t('Select %s'), __t('Branch')),
                    ],
                ],
            ];
        }

        $formFilters[] = [
            'spec' => [
                'name' => 'filter[staff_leave_application.staff_info.attendance_shift_id]',
                'type' => DynamicDropdown::class,
                'options' => [
                    'label' =>  __t("Shift"),
                    'order' => 6,
                    'advanced' => true,
                    'empty_option' => sprintf(__t('Select %s'), __t("Shift")),
                    'target_class' =>  DynamicRepo::class,
                    "find_method" => ['name' => 'getByEntityKey', 'params' => ['shift',['deleted_at'=> null]]],
                    'property' => 'name'
                ],
                'attributes' => [
                    'id' => "applicant-shift-id-filter",
                    'empty_option' => sprintf(__t('Select %s'), __t("Shift")),
                    'placeholder' => sprintf(__t('Select %s'), __t("Shift")),
                ],
            ],
        ];

        $formFilters[] = [
            'spec' => [
                'name' => 'status',
                'type' => Hidden::class,
                'attributes' => [
                    'value' => request()->status ?? '',
                ],
            ],
        ];

        $leaveTypeRepository = resolve(LeaveTypeRepository::class);
        $leaveTypes = $leaveTypeRepository->list(["id", "name"])->toArray();

        if ($leaveTypes) {
            $formFilters[] = [
                'spec' => [
                    'name' => 'filter[leave_type_id]',
                    'type' => Select::class,
                    'options' => [
                        'label' => __t('Leave Type'),
                        'order' => 3,
                        'advanced' => false,
                        'empty_option' => sprintf(__t('Select %s'),__t('Leave Type')),
                        'options' => $leaveTypes
                    ],
                    'attributes' => [
                        'id' => "leave-type-filter",
                        'placeholder' => sprintf(__t('Select %s'), __t('Leave Type')),
                    ],
                ],
            ];
        }

        $statueses = resolve(LeaveApplicationService::class)->getDropDownStatusesData();
        $formFilters[] = [
            'spec' => [
                'name' => 'filter[status][in]',
                'type' => Select::class,
                'options' => [
                    'label' => __t('Status'),
                    'order' => 3,
                    "filter_size"=> 3,
                    'advanced' => false,
                    'empty_option' => sprintf(__t('All')),
                    'options' => $statueses
                ],
                'attributes' => [
                    'id' => "leave-type-filter",
                    'multiple' => "multiple",
                    'placeholder' => sprintf(__t('Select %s'), __t('Status')),
                ],
            ],
        ];
         return $formFilters;
    }

    public function generateFieldSets(): array
    {
        return [

        ];
    }
}
