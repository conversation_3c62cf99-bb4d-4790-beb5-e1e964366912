<?php

namespace Izam\Entity\Forms\EntityStaticFields\Filter\Specs;

use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\ManufacturingOrderOption;
use Izam\Daftra\Common\Formatter\AutoSuggest\Entity\ProductOption;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Forms\EntityStaticFields\Filter\Interfaces\IStaticFieldFilter;
use Izam\Entity\Repository\DynamicRepo;
use Izam\View\Form\Element\AutoSuggest;

class ManufacturingOrdersIndirectCostsStaticFilterFields implements IStaticFieldFilter
{
    public function generateElements(): array
    {
        return  [
            [
                'spec' => [
                    'name' => 'filter[journal_account_id][in]',
                    'type' => AutoSuggest::class,
                    'options' => [
                        "label" => __t('Account'),
                        "order" => 1,
                        "size" => 4,
                        "property" => "name",
                        'auto_suggest_url' => '/owner/journal_accounts/json_find?q=__q__&cat_name=1',
                        'empty_option' => null,
                        "identifier" => "id",
                        "target_class" => DynamicRepo::class,
                        "find_method" => ['name' => 'getByEntityKey', 'params' => [EntityKeyTypesUtil::JOURNAL_ACCOUNT]],
                    ],
                    'attributes' => [
                        'id' => 'account-select-id',
                        'multiple' => "multiple",
                        'data-placeholder' => sprintf(__t('Filter by %s'), __t('Account')),
                    ],
                ],
            ],
            [
                'spec' => [
                    'name' => 'filter[distributions.manufacture_order.product_id][in]',
                    'type' => AutoSuggest::class,
                    'options' => [
                        "identifier" => "id",
                        "property" => "name",
                        'order' => 3,
                        'label' => __t('Product'),
                        'empty_option' => sprintf(__t('Filter by %s'), __t('Product')),
                        "find_method" => ['name' => 'getByEntityKey', 'params' => [EntityKeyTypesUtil::PRODUCT_ENTITY_KEY]],
                        'target_class' => DynamicRepo::class,
                        'auto_suggest_url'=> ProductOption::getAutoSuggestUrl(),
                        'template' => ProductOption::class,
                        'target_entity' => EntityKeyTypesUtil::PRODUCT_ENTITY_KEY,
                    ],
                    'attributes' => [
                        'placeholder' => sprintf(__t('Filter by %s'), __t('Main Product') . ' - ' . __t('Manufacture Order')),
                    ],
                ],
            ],
            [
                'spec' => [
                    'name' => 'filter[distributions.manufacturing_order_id][in]',
                    'type' => AutoSuggest::class,
                    'options' => [
                        "identifier" => "id",
                        "property" => "name",
                        'order' => 3,
                        'label' => __t('Manufacture Order'),
                        'empty_option' => sprintf(__t('Filter by %s'), __t('Manufacturing Order')),
                        "target_class" => DynamicRepo::class,
                        "find_method" =>  ['name' => 'getByEntityKey', 'params' => [EntityKeyTypesUtil::MANUFACTURING_ORDER_ENTITY_KEY]],
                        'auto_suggest_url'=> ManufacturingOrderOption::getAutoSuggestUrl(),
                        'template' => ManufacturingOrderOption::class,
                    ],
                    'attributes' => [
                        'placeholder' => sprintf(__t('Filter by %s'), __t('Manufacture Order')),
                    ],
                ],


            ],
        ];
    }

    public function generateFieldSets(): array
    {
        return [];
    }
}
