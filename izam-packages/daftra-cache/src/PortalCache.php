<?php
namespace Izam\Daftra\Cache;
use Symfony\Component\Cache\Adapter\MemcachedAdapter;
use Symfony\Contracts\Cache\ItemInterface;

class PortalCache
{

    /** @var \Memcached|null */
    protected static $connection = null;
    protected static  $adapters = [];

    protected static $localCache = [];

    protected static string $localCacheKey = "local_cache";


    protected static function getCacheInstance()
    {
        $siteId = (string) self::getSiteId();

        // 1) Create the Memcached connection once
        if (!self::$connection) {
            $linkString = 'memcached://' . (defined('CACHED_SERVER') ? CACHED_SERVER : '127.0.0.1') . ':11211';
            self::$connection = MemcachedAdapter::createConnection($linkString);
        }

        // 2) Create (and memoize) one adapter per site using the same connection
        if (!isset(self::$adapters[$siteId])) {
            $namespace = 'portal_cache_site_' . $siteId;
            $defaultLifetime = 60 * 60 * 24; // 1 day

            $adapter = new MemcachedAdapter(self::$connection, $namespace, $defaultLifetime);
            $adapter->enableVersioning(false);

            self::$adapters[$siteId] = $adapter;
        }
        return self::$adapters[$siteId];
    }

    protected static function getSiteId()
    {
        // Centralize site id retrieval in case you change it later
        return getCurrentSite('id');
    }

    public static function getAllCached()
    {
        $cache = self::getCacheInstance();
        $items = $cache->getItems();
        $data = [];
        foreach ($items as $item) {
            $data[$item->getKey()] = $item->get();
        }
        return $data;
    }

    public static function getLocalCache()
    {
        $cache = self::getCacheInstance();
        $item = $cache->getItem(self::$localCacheKey);
        if($item->isHit()) {
            return $item->get();
        }
        return [];
    }

    public static function get($key, $default = null) {

        $cache = self::getCacheInstance();
        if(empty(self::$localCache)) {
            $item = $cache->getItem(self::$localCacheKey);
            if($item->isHit()) {
                self::$localCache = $item->get();
            }
        }

        if(isset(self::$localCache[$key])) {
            return self::$localCache[$key];
        }

        $value = $cache->get($key, function (ItemInterface $item) use ($default) {
            if(is_null($default)) {
                return null;
            }
            if(is_string($default)) {
                 return $default;
            }
             //check if default is closure
            if(is_callable($default)) {
                return $default();
            }
        });

        if(!is_null($value)) {
            $localCacheItem = $cache->getItem(self::$localCacheKey);
            self::$localCache = $localCacheItem->get();
            self::$localCache[$key] = $value;
            $localCacheItem->set(self::$localCache);
            $cache->save($localCacheItem);
        }

        return $value;
    }

    public static function delete($key) {
        $cache = self::getCacheInstance();
        //remove from local cache
        $item = $cache->getItem(self::$localCacheKey);
        if($item->isHit()) {
            $localCache = $item->get();
            unset($localCache[$key]);
            self::$localCache = $localCache;
            $item->set($localCache);
            $cache->save($item);
        }
        $cache->deleteItem($key);
    }

    public static function set($key, $value) {
        $cache = self::getCacheInstance();
        //set to local cache
        $localCacheItem = $cache->getItem(self::$localCacheKey);
        if($localCacheItem->isHit()) {
            self::$localCache = $localCacheItem->get();
        }
        self::$localCache[$key] = $value;
        $localCacheItem->set(self::$localCache);
        $cache->save($localCacheItem);

        $item = $cache->getItem($key)->set($value);
        $cache->save($item);
    }

    public static function clear() {
        $cache = self::getCacheInstance();
        self::$localCache = self::getLocalCache();
        foreach (self::$localCache as $key => $value) {
            $cache->deleteItem($key);
        }
        $cache->clear();
        self::$localCache = [];
        $item = $cache->getItem(self::$localCacheKey)->set(self::$localCache);
        $cache->save($item);
        $cache->deleteItem(self::$localCacheKey);
    }

}
