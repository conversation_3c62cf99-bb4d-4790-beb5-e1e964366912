import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js"; // Import the plugin
import tailwindcss from "@tailwindcss/vite";

export default defineConfig({
	plugins: [
		react(),
		tailwindcss(),
		cssInjectedByJsPlugin({
			jsAssetsFilterFunction: function customJsAssetsfilterFunction(
				outputChunk,
			) {
				return (
					outputChunk.fileName == "main-email-settings.js" ||
					outputChunk.fileName == "public-email-settings.js" ||
					outputChunk.fileName == "main-employee-documents.js"
				);
			},
		}),
	],
	base: "/v2/js/app/",
	build: {
		outDir: "../../laravel/public/js/app",
		rollupOptions: {
			input: {
				email_settings_main: "src/pages/email-settings/page.tsx",
				email_settings_public: "src/pages/email-settings/public.tsx",
				employee_documents_main: "src/pages/employee-documents/page.tsx",
			},
			output: {
				entryFileNames: (chunkInfo) => {
					// Generate unique names for each entry point
					if (chunkInfo.name === "email_settings_main") {
						return "main-email-settings.js";
					}
					if (chunkInfo.name === "email_settings_public") {
						return "public-email-settings.js";
					}
					if (chunkInfo.name === "employee_documents_main") {
						return "main-employee-documents.js";
					}
					return "[name]-[hash].js";
				},
				chunkFileNames: "[name]-[hash].js",
				assetFileNames: "[name]-[hash].[ext]",
			},
		},
	},
	server: {
		open: true,
		cors: true,
		allowedHosts: true,
	}
});
