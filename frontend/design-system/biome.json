{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "files": {"includes": ["**", "!**/dist/**/*", "!**/generated/**/*", "!**/src/graphql-sdk/**/*", "!**/public/locales/**/*", "!**/public/webviewer/**/*", "!**/storybook-static/**/*", "!**/.next/**/*", "!**/package.json", "!**/mockServiceWorker.js", "!**/.oxlintrc.json"]}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 4, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "formatWithErrors": true}, "linter": {"enabled": true, "rules": {"nursery": {"useSortedClasses": "error"}}}, "javascript": {"formatter": {"semicolons": "asNeeded", "quoteStyle": "single"}}, "json": {"formatter": {"enabled": true}}, "css": {"parser": {"cssModules": true}, "formatter": {"enabled": true}}, "overrides": [{"includes": ["**/tailwind/**/*"], "javascript": {"formatter": {"semicolons": "always"}}}]}