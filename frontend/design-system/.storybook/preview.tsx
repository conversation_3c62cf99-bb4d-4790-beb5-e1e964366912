import type { Preview } from '@storybook/react-vite'

import './styles.css'
import { Heading1, Heading2, Heading3 } from '../src/docs/design-tokens/heading'
import { DocumentContainer } from '../src/docs/document-container'
import { withProviders } from './decorators/withProviders'

const preview: Preview = {
    parameters: {
        docs: {
            container: DocumentContainer,
            components: {
                h1: Heading1,
                h2: Heading2,
                h3: Heading3,
            },
        },
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/i,
            },
        },
        backgrounds: { disable: true },
        grids: { disabled: true },
    },
    decorators: [withProviders],
}

export default preview
