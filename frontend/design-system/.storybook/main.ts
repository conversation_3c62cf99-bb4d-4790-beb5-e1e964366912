import type { StorybookConfig } from '@storybook/react-vite'
import { mergeConfig } from 'vite'

const config: StorybookConfig = {
    stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
    addons: [
        '@chromatic-com/storybook',
        '@storybook/addon-docs',
        '@storybook/addon-a11y',
        '@storybook/addon-vitest',
        '@storybook/addon-designs',
        'storybook-addon-rtl',
    ],
    framework: {
        name: '@storybook/react-vite',
        options: {},
    },

    async viteFinal(config) {
        const { default: tailwindcss } = await import('@tailwindcss/vite')
        return mergeConfig(config, {
            plugins: [
                // Add the Tailwind CSS Vite plugin here
                // This is the core difference for v4 setup
                tailwindcss(),
            ],
            // If you had a postcss.config.js for other PostCSS plugins,
            // you might need to ensure they are loaded.
            // But for core Tailwind v4, the plugin handles it.
        })
    },
}
export default config
