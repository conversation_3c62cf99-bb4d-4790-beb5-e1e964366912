import type { StoryContext } from '@storybook/react'
import type { ComponentType, JSX } from 'react'
import { ToastProvider } from '../../src/components/toast/toast-provider'
import { DesignSystemProvider } from '../../src/providers/design-system-provider'

export const withProviders = (
    Story: ComponentType,
    context: StoryContext,
): JSX.Element => {
    // const locale = context.globals.addonRtl === 'rtl' ? 'ar-SA-u-nu-latn' : 'en'
    const locale = context.globals.addonRtl === 'rtl' ? 'ar-SA' : 'en'
    const direction = context.globals.addonRtl === 'rtl' ? 'rtl' : 'ltr'

    return (
        <DesignSystemProvider locale={locale}>
            <ToastProvider direction={direction}>
                <Story />
            </ToastProvider>
        </DesignSystemProvider>
    )
}
