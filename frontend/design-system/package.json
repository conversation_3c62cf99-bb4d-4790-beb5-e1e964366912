{"name": "@izam/design-system", "version": "0.0.0", "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "files": ["dist", "src"], "exports": {"./components/accordion/accordion": "./dist/components/accordion/accordion.js", "./components/accordion/accordion-arrow-icon": "./dist/components/accordion/accordion-arrow-icon.js", "./components/accordion/accordion-header": "./dist/components/accordion/accordion-header.js", "./components/accordion/accordion-icon": "./dist/components/accordion/accordion-icon.js", "./components/accordion/accordion-item": "./dist/components/accordion/accordion-item.js", "./components/accordion/accordion-panel": "./dist/components/accordion/accordion-panel.js", "./components/box/box": "./dist/components/box/box.js", "./components/button-group/button-group": "./dist/components/button-group/button-group.js", "./components/button-radio-group/button-radio-group": "./dist/components/button-radio-group/button-radio-group.js", "./components/button-radio-group/button-radio-group-item": "./dist/components/button-radio-group/button-radio-group-item.js", "./components/button/button": "./dist/components/button/button.js", "./components/button/button-icon": "./dist/components/button/button-icon.js", "./components/button/button-variants": "./dist/components/button/button-variants.js", "./components/card/card": "./dist/components/card/card.js", "./components/card/card-content": "./dist/components/card/card-content.js", "./components/card/card-header": "./dist/components/card/card-header.js", "./components/card/card-title": "./dist/components/card/card-title.js", "./components/checkbox/checkbox": "./dist/components/checkbox/checkbox.js", "./components/color-picker/color-picker": "./dist/components/color-picker/color-picker.js", "./components/container/container": "./dist/components/container/container.js", "./components/date-picker/date-picker": "./dist/components/date-picker/date-picker.js", "./components/dropdown/dropdown": "./dist/components/dropdown/dropdown.js", "./components/flex/flex": "./dist/components/flex/flex.js", "./components/grid/grid": "./dist/components/grid/grid.js", "./components/input/input": "./dist/components/input/input.js", "./components/label/label": "./dist/components/label/label.js", "./components/link/link": "./dist/components/link/link.js", "./components/menu/menu": "./dist/components/menu/menu.js", "./components/menu/menu-item": "./dist/components/menu/menu-item.js", "./components/menu/menu-item-icon": "./dist/components/menu/menu-item-icon.js", "./components/menu/menu-popover": "./dist/components/menu/menu-popover.js", "./components/menu/menu-trigger": "./dist/components/menu/menu-trigger.js", "./components/page-content/page-content": "./dist/components/page-content/page-content.js", "./components/page-header/page-header": "./dist/components/page-header/page-header.js", "./components/page-sticky-header/page-sticky-header": "./dist/components/page-sticky-header/page-sticky-header.js", "./components/radio/radio": "./dist/components/radio/radio.js", "./components/stack/stack": "./dist/components/stack/stack.js", "./components/tabs/tab": "./dist/components/tabs/tab.js", "./components/tabs/tab-list": "./dist/components/tabs/tab-list.js", "./components/tabs/tab-panel": "./dist/components/tabs/tab-panel.js", "./components/tabs/tabs": "./dist/components/tabs/tabs.js", "./components/textarea/textarea": "./dist/components/textarea/textarea.js", "./components/toast/toast": "./dist/components/toast/toast.js", "./components/toast/toast-provider": "./dist/components/toast/toast-provider.js", "./components/toggle/toggle": "./dist/components/toggle/toggle.js", "./components/uploader/uploader": "./dist/components/uploader/uploader.js", "./components/uploader/uploader-file-card": "./dist/components/uploader/uploader-file-card.js", "./components/uploader/uploader-icons": "./dist/components/uploader/uploader-icons.js", "./components/uploader/uploader-input": "./dist/components/uploader/uploader-input.js", "./components/uploader/uploader-types": "./dist/components/uploader/uploader-types.js", "./components/uploader/uploader-utils": "./dist/components/uploader/uploader-utils.js", "./icons/add-icon": "./dist/icons/add-icon.js", "./icons/alert-icon": "./dist/icons/alert-icon.js", "./icons/alert-rhombus": "./dist/icons/alert-rhombus.js", "./icons/available-icon": "./dist/icons/available-icon.js", "./icons/bell-icon": "./dist/icons/bell-icon.js", "./icons/calendar-icon": "./dist/icons/calendar-icon.js", "./icons/cancel-icon": "./dist/icons/cancel-icon.js", "./icons/check-icon": "./dist/icons/check-icon.js", "./icons/chevron-down-icon": "./dist/icons/chevron-down-icon.js", "./icons/chevron-left-icon": "./dist/icons/chevron-left-icon.js", "./icons/chevron-right-icon": "./dist/icons/chevron-right-icon.js", "./icons/close-circle": "./dist/icons/close-circle.js", "./icons/close-icon": "./dist/icons/close-icon.js", "./icons/close-icon-2": "./dist/icons/close-icon-2.js", "./icons/collapse-icon": "./dist/icons/collapse-icon.js", "./icons/content-save-icon": "./dist/icons/content-save-icon.js", "./icons/deactivate-icon": "./dist/icons/deactivate-icon.js", "./icons/download-icon": "./dist/icons/download-icon.js", "./icons/drag-icon": "./dist/icons/drag-icon.js", "./icons/edit-icon": "./dist/icons/edit-icon.js", "./icons/folder-lock-icon": "./dist/icons/folder-lock-icon.js", "./icons/info-circle": "./dist/icons/info-circle.js", "./icons/invoice-icon": "./dist/icons/invoice-icon.js", "./icons/loading-icon": "./dist/icons/loading-icon.js", "./icons/menu-icon": "./dist/icons/menu-icon.js", "./icons/message-icon": "./dist/icons/message-icon.js", "./icons/money-icon": "./dist/icons/money-icon.js", "./icons/more-icon": "./dist/icons/more-icon.js", "./icons/open-external-icon": "./dist/icons/open-external-icon.js", "./icons/previous-icon": "./dist/icons/previous-icon.js", "./icons/save-icon": "./dist/icons/save-icon.js", "./icons/search-icon": "./dist/icons/search-icon.js", "./icons/sort-icon": "./dist/icons/sort-icon.js", "./icons/trash-icon": "./dist/icons/trash-icon.js", "./icons/treasury-icon": "./dist/icons/treasury-icon.js", "./icons/upload-icon": "./dist/icons/upload-icon.js", "./pages/bid-add": "./dist/pages/bid-add.js", "./providers/design-system-provider": "./dist/providers/design-system-provider.js", "./utils/cn": "./dist/utils/cn.js", "./package.json": "./package.json", "./theme.css": "./src/theme/index.css", "./fonts.css": "./src/theme/fonts.css"}, "scripts": {"build": "tsdown", "dev": "tsdown --watch", "type-check": "tsc --noEmit", "format": "biome check --write .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "storybook": "storybook dev -p 6006", "build:storybook": "storybook build", "build-storybook": "storybook build", "test-storybook": "vitest --project=storybook"}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@biomejs/biome": "^2.0.6", "@chromatic-com/storybook": "^4.0.1", "@mdx-js/react": "^3.1.0", "@storybook/addon-a11y": "^9.0.14", "@storybook/addon-designs": "^10.0.1", "@storybook/addon-docs": "^9.0.14", "@storybook/addon-vitest": "^9.0.14", "@storybook/react-vite": "^9.0.14", "@tailwindcss/vite": "^4.1.11", "@types/moment-hijri": "^2.1.4", "@types/node": "^24.0.6", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "babel-plugin-react-compiler": "^19.1.0-rc.2", "globals": "^16.2.0", "playwright": "^1.53.1", "storybook": "^9.0.14", "storybook-addon-rtl": "^2.0.0", "tsdown": "^0.12.9", "typescript": "^5.8.3", "usehooks-ts": "^3.1.1", "vite-plugin-babel": "^1.3.2", "vitest": "^3.2.4"}, "dependencies": {"@tailwindcss/container-queries": "^0.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "moment": "^2.30.1", "moment-hijri": "^3.0.0", "react-aria": "^3.41.1", "react-aria-components": "^1.10.1", "react-compiler-runtime": "^19.1.0-rc.2", "react-textarea-autosize": "^8.5.9", "sonner": "^1.5.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "tailwindcss-react-aria-components": "^2.0.0", "tw-animate-css": "^1.3.4"}}