import { defineConfig } from 'tsdown'
import babel from 'vite-plugin-babel'

const ReactCompilerConfig = { target: '18' }

export default defineConfig({
    entry: [
        'src/**/*.{ts,tsx,js,jsx}',
        '!src/stories/**/*',
        '!src/docs/**/*',
        '!src/**/*.stories.{ts,tsx,js,jsx}',
    ],
    outDir: 'dist',
    format: 'esm',
    platform: 'browser',
    sourcemap: true,
    dts: true,
    unbundle: true,
    exports: {
        customExports: (pkg) => {
            return {
                ...pkg,
                './theme.css': './src/theme/index.css',
                './fonts.css': './src/theme/fonts.css',
            }
        },
    },
    external: [
        'react',
        'react-dom',
        'react/jsx-runtime',
        'react-compiler-runtime',
        'lucide-react',
    ],
    plugins: [
        babel({
            filter: /^src\/.*\.[jt]sx?$/,
            babelConfig: {
                presets: ['@babel/preset-typescript'], // if you use TypeScript
                plugins: [['babel-plugin-react-compiler', ReactCompilerConfig]],
            },
        }),
    ],
})
