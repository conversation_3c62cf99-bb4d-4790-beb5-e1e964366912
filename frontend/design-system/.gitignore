# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
**/.next
**/out/

# Turbo
.turbo

# production
**/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

public/workbox-*.js*
public/sw.js*

nul

env-script.sh
_testResult
_build
_infra
cue.mod/**/obj
cue.mod/**/bin
.VERSION
.CHANGELOG
**/dist

# IntelliJ

.idea
**/junit.xml
image.tar
eslint_report.json
.pnp.*

**/.yarn/install-state.gz

# storybook

certificates
storybook-static
_testResult
_storybook_build
_yjs_build
_storybook_infra
cue.mod/**/obj
cue.mod/**/bin
storybook_image.tar
.vitest-test-results.json
coverage

# other

~
*storybook.log
%SystemDrive%
logs.txt
oxlint_report.json
*.tsbuildinfo
