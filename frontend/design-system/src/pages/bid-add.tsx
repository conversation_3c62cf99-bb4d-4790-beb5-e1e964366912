import { PlusIcon } from 'lucide-react'
import type { JSX } from 'react'
import { But<PERSON> } from '../components/button/button'
import { ButtonIcon } from '../components/button/button-icon'
import { Card } from '../components/card/card'
import { CardContent } from '../components/card/card-content'
import { CardHeader } from '../components/card/card-header'
import { CardTitle } from '../components/card/card-title'
import { Container } from '../components/container/container'
import { DatePicker } from '../components/date-picker/date-picker'
import {
    DropdownList,
    DropdownRoot,
    DropdownTrigger,
    SelectItem,
} from '../components/dropdown/dropdown'
import { Col, Row } from '../components/grid/grid'
import {
    ErrorMessage,
    Input,
    InputField,
    InputGroup,
    SupportText,
} from '../components/input/input'
import { Label } from '../components/label/label'
import { PageContent } from '../components/page-content/page-content'
import { PageHeader } from '../components/page-header/page-header'
import { PageStickyHeader } from '../components/page-sticky-header/page-sticky-header'
import { Stack } from '../components/stack/stack'
import { TextArea } from '../components/textarea/textarea'
import { Uploader } from '../components/uploader/uploader'
import { CancelIcon } from '../icons/cancel-icon'
import { ContentSaveIcon } from '../icons/content-save-icon'
import { FolderLockIcon } from '../icons/folder-lock-icon'
import { PageWrapper } from '../components/page-wrapper/page-wrapper'

export const BidAdd = (): JSX.Element => {
    return (
        <PageWrapper>
            <PageStickyHeader>
                <PageHeader className="tw:max-sm:p-0">
                    <Container>
                        <Row className="tw:justify-between">
                            <Col>
                                <Button
                                    className="tw:max-sm:rounded-none"
                                    color="neutral"
                                >
                                    <ButtonIcon>
                                        <FolderLockIcon size={20} />
                                    </ButtonIcon>
                                    <span className="tw:max-sm:hidden">
                                        Save as Draft
                                    </span>
                                </Button>
                            </Col>
                            <Col>
                                <Stack className="tw:flex-row tw:justify-end tw:gap-3 tw:max-sm:gap-0">
                                    <Button
                                        className="tw:max-sm:rounded-none"
                                        color="neutral"
                                    >
                                        <ButtonIcon>
                                            <CancelIcon size={20} />
                                        </ButtonIcon>
                                        <span className="tw:max-sm:hidden">
                                            Cancel
                                        </span>
                                    </Button>
                                    <Button
                                        className="tw:max-sm:rounded-none"
                                        color="primary"
                                    >
                                        <ButtonIcon>
                                            <ContentSaveIcon size={20} />
                                        </ButtonIcon>
                                        <span className="tw:max-sm:hidden">
                                            Save
                                        </span>
                                    </Button>
                                </Stack>
                            </Col>
                        </Row>
                    </Container>
                </PageHeader>
            </PageStickyHeader>
            <PageContent>
                <Container>
                    <Stack className="tw:gap-5">
                        <Card>
                            <CardHeader>
                                <CardTitle>Bid Information</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Row>
                                    <Col lg={6}>
                                        <InputField>
                                            <Label required>Bid Title</Label>
                                            <InputGroup>
                                                <Input placeholder="e.g. Electrical Works" />
                                            </InputGroup>
                                            <ErrorMessage>
                                                __ERROR_MESSAGE__
                                            </ErrorMessage>
                                        </InputField>
                                    </Col>
                                    <Col lg={6}>
                                        <InputField>
                                            <Label>Bid No.</Label>
                                            <InputGroup>
                                                <Input value="000137" />
                                            </InputGroup>
                                            <ErrorMessage>
                                                __ERROR_MESSAGE__
                                            </ErrorMessage>
                                        </InputField>
                                    </Col>
                                    <Col lg={6}>
                                        <InputField>
                                            <Label required>Client</Label>
                                            <Stack className="tw:flex-row tw:gap-3">
                                                <InputGroup>
                                                    <DropdownRoot
                                                        onOpenChange={() => {}}
                                                        onSelectionChange={() => {}}
                                                        selectedKey="2"
                                                    >
                                                        <DropdownTrigger
                                                            placeholder="Please Select"
                                                            selectedKey="2"
                                                        />
                                                        <DropdownList>
                                                            <SelectItem
                                                                item={{
                                                                    id: '1',
                                                                    name: 'Option 1',
                                                                }}
                                                            />
                                                            <SelectItem
                                                                item={{
                                                                    id: '2',
                                                                    name: 'Option 2',
                                                                }}
                                                            />
                                                            <SelectItem
                                                                item={{
                                                                    id: '3',
                                                                    name: 'Option 3',
                                                                }}
                                                            />
                                                        </DropdownList>
                                                    </DropdownRoot>
                                                </InputGroup>
                                                <Button
                                                    color="neutral"
                                                    className="tw:shrink-0"
                                                >
                                                    <ButtonIcon>
                                                        <PlusIcon size={20} />
                                                    </ButtonIcon>
                                                    <span className="tw:max-sm:hidden">
                                                        New Client
                                                    </span>
                                                </Button>
                                            </Stack>
                                            <ErrorMessage>
                                                __ERROR_MESSAGE__
                                            </ErrorMessage>
                                        </InputField>
                                    </Col>
                                    <Col lg={6}>
                                        <InputField>
                                            <Label>Valid Until</Label>
                                            <DatePicker
                                                onChange={() => {}}
                                                placeholder="Select Date"
                                                value="2024-01-15"
                                            />
                                            <ErrorMessage>
                                                __ERROR_MESSAGE__
                                            </ErrorMessage>
                                        </InputField>
                                    </Col>
                                    <Col lg={6}>
                                        <InputField>
                                            <Label>Description</Label>
                                            <InputGroup size="xl" autoSize>
                                                <TextArea autoSize />
                                            </InputGroup>
                                            <ErrorMessage>
                                                __ERROR_MESSAGE__
                                            </ErrorMessage>
                                        </InputField>
                                    </Col>
                                    <Col lg={6}>
                                        <InputField>
                                            <Label>Attachments</Label>
                                            <Uploader
                                                multiple
                                                maxFileSize={25}
                                                acceptedFileTypes={[
                                                    'image/png',
                                                    'image/jpeg',
                                                    'image/gif',
                                                    'image/bmp',
                                                    'application/vnd.rar',
                                                    'application/x-rar-compressed',
                                                    'application/octet-stream',
                                                ]}
                                                value={null}
                                            />
                                            <SupportText>
                                                Max file size 25MB.
                                                <br />
                                                Allowed file types (png,
                                                jpeg,gif,bmp,rar)
                                            </SupportText>
                                            <ErrorMessage>
                                                __ERROR_MESSAGE__
                                            </ErrorMessage>
                                        </InputField>
                                    </Col>
                                </Row>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle>Bill of Quantities</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Stack>
                                    <Stack className="tw:flex-row tw:gap-3">
                                        Lorem ipsum, dolor sit amet consectetur
                                        adipisicing elit. Suscipit ex modi iusto
                                        sed dolor, inventore nesciunt eveniet
                                        beatae repudiandae tempora odit
                                        consectetur odio doloremque expedita
                                        eum, molestias vero amet minima.
                                    </Stack>
                                </Stack>
                            </CardContent>
                        </Card>
                    </Stack>
                </Container>
            </PageContent>
        </PageWrapper>
    )
}
