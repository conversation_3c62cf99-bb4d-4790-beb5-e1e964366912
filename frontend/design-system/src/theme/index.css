@import "tw-animate-css";
@import "tailwindcss" prefix(tw);
@plugin "tailwindcss-react-aria-components";

@custom-variant dark (&:is(.dark *));

@theme {
    /* Fonts */
    --font-default: <PERSON><PERSON>, "Noto Sans Arabic", system-ui, sans-serif;

    /* Typography */
    --text-display-xl: 2rem;
    --text-display-l: 1.5rem;
    --text-display-rg: 1.25rem;
    --text-display-s: 1.125rem;
    --text-large: 1rem;
    --text-base: 14px;
    --text-annotation: 12px;

    --font-weight-bold: 700;
    --font-weight-medium: 500;
    --font-weight-normal: 400;

    --leading-normal: 1.25;
    --leading-high: 1.4;

    /* Shadows */
    --shadow-navbar: 0px 4px 12.7px 0px rgba(78, 83, 129, 0.25);

    /* Radius */
    --radius-xs: 2px;

    /* Colors */
    --color-secondary-blue-dark: #247dbd;
    --color-secondary-blue: #00b0ef;
    --color-primary-green: #13b272;

    --color-light-gray-5: #9b9eb8;
    --color-light-gray-4: #9ea1ba;
    --color-light-gray-3: #b4b6c9;
    --color-light-gray-2: #e4ebf2;
    --color-light-gray-1: #f6f9fc;
    --color-light-gray-0: #f0f0f0;
    --color-light-gray: #f0f0f0;
    --color-primary-black: #202124;
    --color-dark-gray-3: #373b50;
    --color-dark-gray-2: #4e5381;
    --color-dark-gray-1: #75799d;
    --color-dark-gray: #5f6368;

    --color-danger-2: #df0000;
    --color-danger-1: #eb2121;
    --color-danger: #fdf1f0;
    --color-warning-2: #f88e00;
    --color-warning-1: #ffa500;
    --color-warning: #fff9f0;
    --color-success-2: #0c744a;
    --color-success-1: #1ea702;
    --color-success: #f0faf7;
    --color-info-2: #2356c2;
    --color-info-1: #1877f2;
    --color-info: #f8faff;

    @keyframes shimmer {
        from {
            background-position: 100%;
        }

        to {
            background-position: 0%;
        }
    }
}

@layer base {
    :root {
        --background: oklch(1 0 0);
        --foreground: oklch(0.141 0.005 285.823);
        --accent: var(--color-primary-main);
        --border: oklch(0.92 0.004 286.32);
        --muted: oklch(0.552 0.016 285.938);
        --ring: oklch(0.705 0.015 286.067);
        --shadow-outline:
            0px 0px 0px 1px oklch(0 0 0 / 0.07), 0px 2px 3px -1px oklch(0 0 0 / 0.08),
            0px 1px 0px 0px oklch(0 0 0 / 0.02);
        --skeleton-background: linear-gradient(to right,
                #e9e9e9 33%,
                #fff,
                #e9e9e9 66%);
    }
}

@layer base {
    body {
        @apply tw:font-default tw:h-full tw:antialiased;
        font-size: 16px;
    }
}

.dir-rtl {
    direction: rtl;
}

.dir-ltr {
    direction: ltr;
}