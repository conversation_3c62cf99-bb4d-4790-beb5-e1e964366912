import { type ClassValue, clsx } from 'clsx'
import { extendTailwindMerge } from 'tailwind-merge'

const twMerge = extendTailwindMerge({
    extend: {
        classGroups: {
            'font-size': [
                {
                    text: [
                        'display-xl',
                        'display-l',
                        'display-rg',
                        'display-s',
                        'large',
                        'base',
                        'annotation',
                    ],
                },
            ],
        },
    },
})

export function cn(...inputs: ClassValue[]): string {
    return twMerge(clsx(inputs))
}
