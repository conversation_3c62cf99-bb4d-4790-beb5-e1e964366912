import type { <PERSON>a, StoryObj } from '@storybook/react'
import { Box } from './box'

const meta: Meta<typeof Box> = {
    title: 'Layout/Box',
    component: Box,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    args: {
        className: 'tw:bg-red-500 tw:w-60 tw:h-60',
    },
    argTypes: {
        style: {
            control: false,
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {}
