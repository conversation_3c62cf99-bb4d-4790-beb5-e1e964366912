import type { Meta, StoryObj } from '@storybook/react'
import { Label } from './label'

const meta: Meta<typeof Label> = {
    title: 'Components/Label',
    component: Label,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        children: {
            control: 'text',
            description: 'The label text content',
        },
        required: {
            control: 'boolean',
            description: 'Whether to show a required indicator (*)',
        },
        className: {
            table: {
                disable: true,
            },
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: 'Email Address',
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic label for form fields.',
            },
        },
    },
}

export const Required: Story = {
    args: {
        children: 'Email Address',
        required: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'A label with a required indicator (red asterisk).',
            },
        },
    },
}

export const LongText: Story = {
    args: {
        children:
            'This is a very long label text that might wrap to multiple lines',
        required: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'A label with longer text content.',
            },
        },
    },
}

export const WithSpecialCharacters: Story = {
    args: {
        children: 'Field Name (Optional)',
        required: false,
    },
    parameters: {
        docs: {
            description: {
                story: 'A label with special characters and parentheses.',
            },
        },
    },
}
