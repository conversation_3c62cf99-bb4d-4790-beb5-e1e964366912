import type { JSX, ReactNode } from 'react'
import { Label as AriaLabel } from 'react-aria-components'
import { cn } from '../../utils/cn'

export interface LabelProps {
    children: ReactNode
    className?: string
    required?: boolean
}

export function Label({
    children,
    className = '',
    required = false,
}: LabelProps): JSX.Element {
    return (
        <AriaLabel
            className={cn(
                'tw:flex tw:gap-1 tw:font-normal tw:text-base tw:text-dark-gray tw:leading-normal',
                className,
            )}
        >
            {children}
            {required && (
                <span className="tw:text-danger-1 tw:group-disabled:text-dark-gray">
                    *
                </span>
            )}
        </AriaLabel>
    )
}
