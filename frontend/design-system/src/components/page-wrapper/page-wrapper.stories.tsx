import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { PageWrapper } from './page-wrapper'

const meta: Meta<typeof PageWrapper> = {
    title: 'Components/PageWrapper',
    parameters: {
        layout: 'fullscreen',
    },
    tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: (
            <PageWrapper>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Veritatis
                quisquam optio corrupti quas repellat, maxime distinctio eos velit
                nihil dolorum, deserunt unde minus consequuntur accusamus commodi.
                Explicabo aut repudiandae eaque dolores consectetur eligendi quidem
                neque illum sed commodi nesciunt amet ipsa tenetur, officiis itaque
                dignissimos fugiat, doloremque ut alias. Dolorum voluptas
                dignissimos blanditiis fugiat quos sint possimus asperiores non aut
                unde quasi eveniet natus inventore debitis voluptates est id
                voluptatibus et aspernatur recusandae cumque expedita tempore, sunt
                itaque! Obcaecati quidem, libero vero consequuntur asperiores
                expedita quis reiciendis rem corrupti numquam. Sequi consequuntur
                repellendus ut. Voluptatum non exercitationem accusamus tempore?
                expedita quis reiciendis rem corrupti numquam. Sequi consequuntur
                repellendus ut. Voluptatum non exercitationem accusamus tempore?
            </PageWrapper>
        ),
    },
}

export const WithCustomClassName: Story = {
    args: {
        children: (
            <PageWrapper className="tw:bg-blue-50">
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Veritatis
                quisquam optio corrupti quas repellat, maxime distinctio eos velit
                nihil dolorum, deserunt unde minus consequuntur accusamus commodi.
                Explicabo aut repudiandae eaque dolores consectetur eligendi quidem
                neque illum sed commodi nesciunt amet ipsa tenetur, officiis itaque
                dignissimos fugiat, doloremque ut alias. Dolorum voluptas
                dignissimos blanditiis fugiat quos sint possimus asperiores non aut
                unde quasi eveniet natus inventore debitis voluptates est id
                voluptatibus et aspernatur recusandae cumque expedita tempore, sunt
                itaque! Obcaecati quidem, libero vero consequuntur asperiores
                expedita quis reiciendis rem corrupti numquam. Sequi consequuntur
                repellendus ut. Voluptatum non exercitationem accusamus tempore?
                expedita quis reiciendis rem corrupti numquam. Sequi consequuntur
            </PageWrapper>
        ),
    },
}
