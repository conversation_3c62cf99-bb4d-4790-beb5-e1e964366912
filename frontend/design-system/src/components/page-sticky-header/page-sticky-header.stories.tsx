import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { PageStickyHeader } from './page-sticky-header'

const meta: Meta<typeof PageStickyHeader> = {
    title: 'Components/PageStickyHeader',
    render: (args) => (
        <div className="tw:h-[200px] tw:overflow-auto tw:bg-light-gray-2">
            <PageStickyHeader {...args} />
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Veritatis
            quisquam optio corrupti quas repellat, maxime distinctio eos velit
            nihil dolorum, deserunt unde minus consequuntur accusamus commodi.
            Explicabo aut repudiandae eaque dolores consectetur eligendi quidem
            neque illum sed commodi nesciunt amet ipsa tenetur, officiis itaque
            dignissimos fugiat, doloremque ut alias. Dolorum voluptas
            dignissimos blanditiis fugiat quos sint possimus asperiores non aut
            unde quasi eveniet natus inventore debitis voluptates est id
            voluptatibus et aspernatur recusandae cumque expedita tempore, sunt
            itaque! Obcaecati quidem, libero vero consequuntur asperiores
            expedita quis reiciendis rem corrupti numquam. Sequi consequuntur
            repellendus ut. Voluptatum non exercitationem accusamus tempore?
            expedita quis reiciendis rem corrupti numquam. Sequi consequuntur
            repellendus ut. Voluptatum non exercitationem accusamus tempore?
        </div>
    ),
    parameters: {
        layout: 'fullscreen',
    },
    tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: (
            <div className="tw:bg-white tw:p-4 tw:shadow-md">
                <h1 className="tw:font-bold tw:text-xl">Sticky Header Content</h1>
            </div>
        ),
    },
}

export const WithCustomClassName: Story = {
    args: {
        className: 'tw:bg-blue-50',
        children: (
            <div className="tw:bg-blue-100 tw:p-4 tw:shadow-md">
                <h1 className="tw:font-bold tw:text-blue-800 tw:text-xl">
                    Custom Sticky Header
                </h1>
            </div>
        ),
    },
}
