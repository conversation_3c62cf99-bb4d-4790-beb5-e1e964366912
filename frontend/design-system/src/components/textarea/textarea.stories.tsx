import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import type React from 'react'
import { useEffect, useState } from 'react'
import { CalendarIcon } from '../../icons/calendar-icon'
import { CheckIcon } from '../../icons/check-icon'
import { CloseIcon } from '../../icons/close-icon'
import { MoneyIcon } from '../../icons/money-icon'
import { SearchIcon } from '../../icons/search-icon'
import {
    ErrorMessage,
    InputField,
    InputGroup,
    InputPostfix,
    InputPrefix,
    SupportText,
} from '../input/input'
import { Label } from '../label/label'
import { TextArea } from './textarea'

// Define the args type for the template
interface TextAreaTemplateArgs {
    // InputField props
    label?: string
    supportText?: string
    errorMessage?: string
    required?: boolean
    invalid?: boolean
    success?: boolean
    isDisabled?: boolean
    name?: string
    autoFocus?: boolean
    className?: string
    // TextArea props
    placeholder?: string
    value?: string
    disabled?: boolean
    onChange?: (value: string) => void
    rows?: number
    cols?: number
    // Additional props for prefix/postfix
    hasPrefixIcon?: boolean
    hasPostfixIcon?: boolean
    hasPrefixAddon?: boolean
    hasPostfixAddon?: boolean
    prefixIcon?: 'search' | 'calendar' | 'money'
    postfixIcon?: 'check' | 'close'
    prefixAddon?: string
    postfixAddon?: string
}

// Template function that renders InputField with InputGroup and TextArea and handles all props
const TextAreaTemplate = (args: TextAreaTemplateArgs): React.JSX.Element => {
    const {
        // TextAreaField props
        label,
        supportText,
        errorMessage,
        required,
        invalid,
        success,
        isDisabled,
        name,
        autoFocus,
        className,
        // TextArea props
        placeholder,
        value: initialValue,
        disabled,
        rows,
        cols,
        // Additional props for prefix/postfix
        hasPrefixIcon,
        hasPostfixIcon,
        hasPrefixAddon,
        hasPostfixAddon,
        prefixIcon,
        postfixIcon,
        prefixAddon,
        postfixAddon,
    } = args

    const [value, setValue] = useState(initialValue ?? '')

    useEffect(() => {
        setValue(initialValue ?? '')
    }, [initialValue])

    return (
        <InputField
            invalid={invalid}
            success={success}
            isDisabled={isDisabled}
            name={name}
            autoFocus={autoFocus}
            className={className}
        >
            {label && <Label required={required}>{label}</Label>}
            <InputGroup>
                {hasPrefixIcon && (
                    <InputPrefix>
                        {prefixIcon === 'search' && <SearchIcon />}
                        {prefixIcon === 'calendar' && <CalendarIcon />}
                        {prefixIcon === 'money' && <MoneyIcon />}
                    </InputPrefix>
                )}
                {hasPrefixAddon && <InputPrefix>{prefixAddon}</InputPrefix>}
                <TextArea
                    placeholder={placeholder}
                    value={value}
                    disabled={disabled}
                    onChange={setValue}
                    name={name}
                    autoFocus={autoFocus}
                    rows={rows}
                    cols={cols}
                />
                {hasPostfixAddon && <InputPostfix>{postfixAddon}</InputPostfix>}
                {hasPostfixIcon && (
                    <InputPostfix>
                        {postfixIcon === 'check' && <CheckIcon />}
                        {postfixIcon === 'close' && <CloseIcon />}
                    </InputPostfix>
                )}
            </InputGroup>
            {supportText && <SupportText>{supportText}</SupportText>}
            {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
        </InputField>
    )
}

const meta: Meta<typeof TextAreaTemplate> = {
    title: 'Components/TextArea',
    component: TextAreaTemplate,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        // TextAreaField props
        label: {
            control: 'text',
            description: 'The label text displayed above the textarea',
        },
        supportText: {
            control: 'text',
            description: 'Help text displayed below the textarea',
        },
        errorMessage: {
            control: 'text',
            description: 'Error message displayed below the textarea',
        },
        required: {
            control: 'boolean',
            description: 'Whether the field is required (shows red asterisk)',
        },
        invalid: {
            control: 'boolean',
            description:
                'Whether the textarea is in an invalid state (shows red border)',
        },
        success: {
            control: 'boolean',
            description:
                'Whether the textarea is in a success state (shows green border)',
        },
        isDisabled: {
            control: 'boolean',
            description: 'Whether the TextAreaField is disabled',
        },
        name: {
            control: 'text',
            description: 'The name attribute for the textarea field',
        },
        autoFocus: {
            control: 'boolean',
            description: 'Whether the textarea should be focused on mount',
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes for the TextAreaField',
        },
        // TextArea props
        placeholder: {
            control: 'text',
            description: 'The placeholder text for the textarea',
        },
        value: {
            control: 'text',
            description: 'The value of the textarea field',
        },
        disabled: {
            control: 'boolean',
            description: 'Whether the textarea is disabled',
        },
        onChange: {
            table: {
                disable: true,
            },
        },
        rows: {
            control: 'number',
            description: 'The number of visible text lines',
        },
        cols: {
            control: 'number',
            description: 'The number of visible text columns',
        },
        // Additional props for prefix/postfix
        hasPrefixIcon: {
            control: 'boolean',
            description: 'Whether to show a prefix icon',
        },
        hasPostfixIcon: {
            control: 'boolean',
            description: 'Whether to show a postfix icon',
        },
        hasPrefixAddon: {
            control: 'boolean',
            description: 'Whether to show a prefix addon',
        },
        hasPostfixAddon: {
            control: 'boolean',
            description: 'Whether to show a postfix addon',
        },
        prefixIcon: {
            control: 'select',
            options: ['search', 'calendar', 'money'],
            description: 'The prefix icon to display',
        },
        postfixIcon: {
            control: 'select',
            options: ['check', 'close'],
            description: 'The postfix icon to display',
        },
        prefixAddon: {
            control: 'text',
            description: 'The prefix addon text to display',
        },
        postfixAddon: {
            control: 'text',
            description: 'The postfix addon text to display',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Description',
        supportText: 'Please provide a detailed description of your project.',
        placeholder: 'Enter your description here...',
        value: 'This is a sample description for the textarea component.',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic textarea field with label and support text.',
            },
        },
    },
}

export const RTL: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'الوصف',
        supportText: 'يرجى تقديم وصف مفصل لمشروعك.',
        placeholder: 'أدخل وصفك هنا...',
        value: 'هذا وصف تجريبي لمكون منطقة النص.',
        rows: 4,
        hasPrefixIcon: true,
        hasPostfixAddon: true,
        prefixIcon: 'search',
        postfixIcon: 'check',
        postfixAddon: 'وصف',
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with RTL (right-to-left) text direction support.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}

export const WithPlaceholder: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Description',
        placeholder: 'Enter your detailed description here...',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with placeholder text.',
            },
        },
    },
}

export const Disabled: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Disabled Textarea',
        supportText: 'This textarea is disabled.',
        placeholder: 'Disabled textarea...',
        value: 'Cannot edit this content',
        disabled: true,
        isDisabled: true,
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Disabled textarea field that cannot be interacted with.',
            },
        },
    },
}

export const WithPrefixIcon: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Notes',
        supportText: 'Add any additional notes or comments.',
        placeholder: 'Enter your notes...',
        value: 'Important notes about the project',
        hasPrefixIcon: true,
        prefixIcon: 'search',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with a search icon on the left side.',
            },
        },
    },
}

export const WithPostfixIcon: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Feedback',
        supportText: 'We appreciate your feedback.',
        placeholder: 'Enter your feedback',
        value: 'Great product, very satisfied!',
        hasPostfixIcon: true,
        postfixIcon: 'check',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with a check icon on the right side.',
            },
        },
    },
}

export const WithPrefixAddon: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Code Snippet',
        supportText: 'Enter your code snippet.',
        placeholder: 'function example() { ... }',
        value: 'console.log("Hello World");',
        hasPrefixAddon: true,
        prefixAddon: 'JS',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with a prefix addon showing the language.',
            },
        },
    },
}

export const WithPostfixAddon: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Character Count',
        supportText: 'Enter your message.',
        placeholder: 'Type your message...',
        value: 'This is a sample message',
        hasPostfixAddon: true,
        postfixAddon: '25/500',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with a character count addon on the right side.',
            },
        },
    },
}

export const WithBothIcons: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Review',
        supportText: 'Write your review.',
        placeholder: 'Share your experience...',
        value: 'Excellent service and quality!',
        hasPrefixIcon: true,
        hasPostfixIcon: true,
        prefixIcon: 'search',
        postfixIcon: 'check',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with icons on both sides.',
            },
        },
    },
}

export const WithMultipleAddons: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Code with Language',
        supportText: 'Enter your code with language specification.',
        placeholder: 'function example() { ... }',
        value: 'const greeting = "Hello World";',
        hasPrefixAddon: true,
        hasPostfixAddon: true,
        prefixAddon: 'JS',
        postfixAddon: 'ES6',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with addons on both sides.',
            },
        },
    },
}

export const DisabledWithIcon: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Disabled with Icon',
        supportText: 'This textarea is disabled but still shows the icon.',
        placeholder: 'Disabled with icon...',
        value: 'Cannot edit',
        disabled: true,
        isDisabled: true,
        hasPrefixIcon: true,
        prefixIcon: 'search',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Disabled textarea with an icon.',
            },
        },
    },
}

export const InvalidWithIcon: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Review',
        errorMessage: 'Please provide a more detailed review.',
        invalid: true,
        placeholder: 'Invalid review...',
        value: 'Good',
        hasPrefixIcon: true,
        hasPostfixIcon: true,
        prefixIcon: 'search',
        postfixIcon: 'close',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with error message and icons on both sides.',
            },
        },
    },
}

export const InvalidWithAddon: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Description',
        errorMessage: 'Please provide a more detailed description.',
        invalid: true,
        placeholder: 'invalid-description',
        value: 'Short',
        hasPrefixAddon: true,
        hasPostfixAddon: true,
        prefixAddon: 'Desc',
        postfixAddon: 'Required',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with error message and addons on both sides.',
            },
        },
    },
}

export const DisabledWithRequired: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Required but Disabled',
        supportText: 'This field is required but currently disabled.',
        required: true,
        disabled: true,
        isDisabled: true,
        placeholder: 'Cannot edit',
        value: 'Disabled value',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea that is both required and disabled. The required indicator is shown, but the field cannot be edited.',
            },
        },
    },
}

export const LargeTextArea: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Large Description',
        supportText: 'This textarea has more rows for longer content.',
        placeholder: 'Enter a longer description...',
        value: 'This is a larger textarea with more rows to accommodate longer content. It provides more space for users to write detailed descriptions, long-form content, or extensive feedback.',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'A larger textarea with more rows for longer content.',
            },
        },
    },
}

export const SuccessState: Story = {
    render: TextAreaTemplate,
    args: {
        label: 'Description',
        supportText: 'Your description has been saved successfully!',
        success: true,
        placeholder: 'Enter your description...',
        value: 'This is a well-written description that meets all requirements.',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea in success state with green border and success message.',
            },
        },
    },
}

export const TextAreaWithoutInputGroup: Story = {
    render: (args: TextAreaTemplateArgs) => {
        const {
            // InputField props
            label,
            supportText,
            errorMessage,
            required,
            invalid,
            success,
            isDisabled,
            name,
            autoFocus,
            className,
            // TextArea props
            placeholder,
            value: initialValue,
            disabled,
            rows,
            cols,
        } = args

        const [value, setValue] = useState(initialValue ?? '')

        useEffect(() => {
            setValue(initialValue ?? '')
        }, [initialValue])

        return (
            <InputField
                invalid={invalid}
                success={success}
                isDisabled={isDisabled}
                name={name}
                autoFocus={autoFocus}
                className={className}
            >
                {label && <Label required={required}>{label}</Label>}
                <TextArea
                    placeholder={placeholder}
                    value={value}
                    disabled={disabled}
                    onChange={setValue}
                    name={name}
                    autoFocus={autoFocus}
                    rows={rows}
                    cols={cols}
                />
                {supportText && <SupportText>{supportText}</SupportText>}
                {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
            </InputField>
        )
    },
    args: {
        label: 'Description',
        supportText:
            'This TextArea is not wrapped in InputGroup, notice the styled background.',
        placeholder: 'Enter your description...',
        value: 'This is a sample description.',
        rows: 4,
    },
    parameters: {
        docs: {
            description: {
                story: 'TextArea component used without InputGroup wrapper shows standalone styling. This demonstrates how the TextArea component works when used independently with its own border and styling.',
            },
        },
    },
}
