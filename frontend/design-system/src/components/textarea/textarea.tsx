import type { JSX } from 'react'
import { TextArea as AriaTextArea } from 'react-aria-components'
import TextareaAutosize from 'react-textarea-autosize'
import { cn } from '../../utils/cn'

export interface TextAreaProps {
    placeholder?: string
    className?: string
    value?: string
    onChange?: (value: string) => void
    disabled?: boolean
    name?: string
    autoFocus?: boolean
    rows?: number
    cols?: number
    autoSize?: boolean
}

export function TextArea({
    className = '',
    onChange,
    placeholder,
    value,
    disabled,
    name,
    autoFocus,
    rows,
    cols,
    autoSize,
}: TextAreaProps): JSX.Element {
    if (autoSize) {
        return (
            <TextareaAutosize
                className={cn(
                    // Base styles
                    'tw:flex-1 tw:resize-none tw:border-0 tw:bg-transparent tw:px-4 tw:py-[10.25px] tw:text-base tw:text-primary-black tw:leading-normal tw:outline-none tw:placeholder:text-light-gray-4 tw:group-disabled:cursor-not-allowed',
                    'tw:disabled:bg-transparent tw:disabled:text-light-gray-4',

                    // Standalone styles
                    'tw:rounded-xs tw:border-[1px] tw:bg-white tw:transition tw:focus-within:border-info-1',
                    'tw:border-[#E4EBF2]',
                    'tw:group-invalid:border-danger-1 tw:group-invalid:focus-within:border-danger-1',
                    'tw:group-data-[success=true]:border-success-2 tw:group-data-[success=true]:focus-within:border-success-2',
                    'tw:hover:ring-2 tw:hover:ring-[#D8E8FD]',
                    'tw:[[data-group-size=lg]>&]:min-h-[42px] tw:[[data-group-size=md]>&]:min-h-[38px] tw:[[data-group-size=sm]>&]:min-h-[34px] tw:[[data-group-size=xl]>&]:min-h-[80px]',
                    'tw:group-invalid:hover:ring-2 tw:group-invalid:hover:ring-danger-1/50',
                    'tw:group-data-[success=true]:hover:ring-2 tw:group-data-[success=true]:hover:ring-success-2/50',
                    'tw:group-disabled:cursor-not-allowed tw:group-disabled:bg-light-gray-1',
                    'tw:group-disabled:hover:ring-0',

                    // Reset styles when inside an InputGroup
                    'tw:group-data-[group=true]/input-group:rounded-none tw:group-data-[group=true]/input-group:border-none tw:group-data-[group=true]/input-group:ring-0 tw:group-data-[group=true]/input-group:group-data-[success=true]:hover:ring-0 tw:group-data-[group=true]/input-group:hover:ring-0',
                    className,
                )}
                onChange={
                    onChange ? (e) => onChange(e.target.value) : undefined
                }
                placeholder={placeholder}
                value={value}
                disabled={disabled}
                name={name}
                autoFocus={autoFocus}
                rows={rows}
                cols={cols}
            />
        )
    }
    return (
        <AriaTextArea
            className={cn(
                // Base styles
                'tw:min-h-[38px] tw:flex-1 tw:resize-none tw:border-0 tw:bg-transparent tw:px-4 tw:py-[10.25px] tw:text-base tw:text-primary-black tw:leading-normal tw:outline-none tw:placeholder:text-light-gray-4 tw:group-disabled:cursor-not-allowed',
                'tw:disabled:bg-transparent tw:disabled:text-light-gray-4',

                // Standalone styles
                'tw:rounded-xs tw:border-[1px] tw:bg-white tw:transition tw:focus-within:border-info-1',
                'tw:border-[#E4EBF2]',
                'tw:group-invalid:border-danger-1 tw:group-invalid:focus-within:border-danger-1',
                'tw:group-data-[success=true]:border-success-2 tw:group-data-[success=true]:focus-within:border-success-2',
                'tw:hover:ring-2 tw:hover:ring-[#D8E8FD]',
                'tw:group-invalid:hover:ring-2 tw:group-invalid:hover:ring-danger-1/50',
                'tw:group-data-[success=true]:hover:ring-2 tw:group-data-[success=true]:hover:ring-success-2/50',
                'tw:group-disabled:cursor-not-allowed tw:group-disabled:bg-light-gray-1',
                'tw:group-disabled:hover:ring-0',

                // Reset styles when inside an InputGroup
                'tw:group-data-[group=true]/input-group:rounded-none tw:group-data-[group=true]/input-group:border-none tw:group-data-[group=true]/input-group:ring-0 tw:group-data-[group=true]/input-group:group-data-[success=true]:hover:ring-0 tw:group-data-[group=true]/input-group:hover:ring-0',
                className,
            )}
            onChange={onChange ? (e) => onChange(e.target.value) : undefined}
            placeholder={placeholder}
            value={value}
            disabled={disabled}
            name={name}
            autoFocus={autoFocus}
            rows={rows}
            cols={cols}
        />
    )
}
