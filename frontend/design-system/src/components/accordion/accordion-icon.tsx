import { cn } from '../../utils/cn'

const AccordionIcon = (
    props: React.SVGProps<SVGSVGElement>,
): React.ReactElement => {
    return (
        <>
            <svg
                width="10"
                height="16"
                viewBox="0 0 10 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                aria-expanded="false"
                className={cn(
                    // Default
                    `tw:ms-auto tw:shrink-0`,
                    `tw:text-primary-black`,
                    // Collapsed
                    `tw:[[aria-expanded=false]_&[aria-expanded=false]]:hidden tw:[[aria-expanded=true]_&[aria-expanded=true]]:hidden`,
                    // Expanded
                    `tw:[[aria-expanded=false]_&[aria-expanded=false]]:hidden tw:[[aria-expanded=true]_&[aria-expanded=true]]:hidden`,
                    // Disabled
                    `tw:[[data-disabled]_&]:text-light-gray-3`,
                    props.className,
                )}
                {...props}
            >
                <title>Arrow Icon</title>
                <path
                    d="M9.33137 1.41012L7.91137 0.00012207L4.74137 3.17012L1.57137 0.00012207L0.151367 1.41012L4.74137 6.00012M0.151367 14.5901L1.57137 16.0001L4.74137 12.8301L7.91137 16.0001L9.32137 14.5901L4.74137 10.0001L0.151367 14.5901Z"
                    fill="currentColor"
                />
            </svg>
            <svg
                width="10"
                height="18"
                viewBox="0 0 10 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                aria-expanded="true"
                className={cn(
                    // Default
                    `tw:ms-auto tw:shrink-0`,
                    `tw:text-primary-black`,
                    // Collapsed
                    `tw:[[aria-expanded=false]_&[aria-expanded=false]]:hidden tw:[[aria-expanded=true]_&[aria-expanded=true]]:hidden`,
                    // Expanded
                    `tw:[[aria-expanded=false]_&[aria-expanded=false]]:hidden tw:[[aria-expanded=true]_&[aria-expanded=true]]:hidden`,
                    // Disabled
                    `tw:[[data-disabled]_&]:text-light-gray-3`,
                    props.className,
                )}
                {...props}
            >
                <title>Arrow Icon</title>
                <path
                    d="M4.74137 15.1701L1.57137 12.0001L0.161367 13.4101L4.74137 18.0001L9.33137 13.4101L7.91137 12.0001M4.74137 2.83012L7.91137 6.00012L9.32137 4.59012L4.74137 0.00012207L0.151367 4.59012L1.57137 6.00012L4.74137 2.83012Z"
                    fill="currentColor"
                />
            </svg>
        </>
    )
}

export { AccordionIcon }
