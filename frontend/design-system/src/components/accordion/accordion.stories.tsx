import type { Key } from '@react-types/shared'

import type { <PERSON>a, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { Accordion } from './accordion'
import { AccordionArrowIcon } from './accordion-arrow-icon'
import { AccordionHeader } from './accordion-header'
import { AccordionIcon } from './accordion-icon'
import { AccordionItem } from './accordion-item'
import { AccordionPanel } from './accordion-panel'

// Define the args type for the template
interface AccordionTemplateArgs {
    controlled?: boolean
    variant?: 'default' | 'bordered'
    colorSchema?: 'neutral' | 'green'
}

// Template function for bordered + green variant
const BorderedGreenTemplate = (
    args: AccordionTemplateArgs,
): React.JSX.Element => {
    const {
        controlled = true,
        variant = 'bordered',
        colorSchema = 'green',
    } = args
    const [value, setValue] = useState<Set<Key>>(new Set(['accordion1']))

    return (
        <Accordion
            {...(controlled && {
                expandedKeys: value,
                onExpandedChange: (keys: Set<Key>) => setValue(keys),
            })}
            {...(!controlled && {
                defaultExpandedKeys: ['accordion1'],
            })}
            colorSchema={colorSchema}
            variant={variant}
        >
            <AccordionItem id="accordion1">
                <AccordionHeader>
                    <AccordionArrowIcon />
                    Accordion Title 1
                </AccordionHeader>
                <AccordionPanel>
                    Lorem Accordion 1 ipsum dolor sit amet consectetur,
                    adipisicing elit. Quos quaerat neque corporis consequatur
                    deserunt blanditiis ea, officia aliquid ab voluptatibus vel,
                    consequuntur eaque iure! Blanditiis, assumenda? Illo
                    nesciunt dolorum exercitationem.
                </AccordionPanel>
            </AccordionItem>
            <AccordionItem id="accordion2">
                <AccordionHeader>
                    <AccordionArrowIcon />
                    Accordion Title 2
                </AccordionHeader>
                <AccordionPanel>
                    Lorem Accordion 2 ipsum dolor sit amet consectetur,
                    adipisicing elit. Quos quaerat neque corporis consequatur
                    deserunt blanditiis ea, officia aliquid ab voluptatibus vel,
                    consequuntur eaque iure! Blanditiis, assumenda? Illo
                    nesciunt dolorum exercitationem.
                </AccordionPanel>
            </AccordionItem>
            <AccordionItem id="accordion3" isDisabled>
                <AccordionHeader>
                    <AccordionArrowIcon />
                    Accordion Title 3
                </AccordionHeader>
                <AccordionPanel>
                    Lorem Accordion 3 ipsum dolor sit amet consectetur,
                    adipisicing elit. Quos quaerat neque corporis consequatur
                    deserunt blanditiis ea, officia aliquid ab voluptatibus vel,
                    consequuntur eaque iure! Blanditiis, assumenda? Illo
                    nesciunt dolorum exercitationem.
                </AccordionPanel>
            </AccordionItem>
        </Accordion>
    )
}

// Template function for default variant
const DefaultTemplate = (args: AccordionTemplateArgs): React.JSX.Element => {
    const {
        controlled = true,
        variant = 'default',
        colorSchema = 'neutral',
    } = args
    const [value, setValue] = useState<Set<Key>>(new Set(['accordion1']))

    return (
        <Accordion
            {...(controlled && {
                expandedKeys: value,
                onExpandedChange: (keys: Set<Key>) => setValue(keys),
            })}
            {...(!controlled && {
                defaultExpandedKeys: ['accordion1'],
            })}
            colorSchema={colorSchema}
            variant={variant}
        >
            <AccordionItem id="accordion1">
                <AccordionHeader>
                    Accordion Title 1
                    <AccordionIcon />
                </AccordionHeader>
                <AccordionPanel>
                    Lorem Accordion 1 ipsum dolor sit amet consectetur,
                    adipisicing elit. Quos quaerat neque corporis consequatur
                    deserunt blanditiis ea, officia aliquid ab voluptatibus vel,
                    consequuntur eaque iure! Blanditiis, assumenda? Illo
                    nesciunt dolorum exercitationem.
                </AccordionPanel>
            </AccordionItem>
            <div className="tw:my-[30px] tw:h-[1px] tw:w-full tw:border-light-gray-2 tw:border-b tw:border-dashed" />
            <AccordionItem id="accordion2">
                <AccordionHeader>
                    Accordion Title 2
                    <AccordionIcon />
                </AccordionHeader>
                <AccordionPanel>
                    Lorem Accordion 2 ipsum dolor sit amet consectetur,
                    adipisicing elit. Quos quaerat neque corporis consequatur
                    deserunt blanditiis ea, officia aliquid ab voluptatibus vel,
                    consequuntur eaque iure! Blanditiis, assumenda? Illo
                    nesciunt dolorum exercitationem.
                </AccordionPanel>
            </AccordionItem>
            <div className="tw:my-[30px] tw:h-[1px] tw:w-full tw:border-light-gray-2 tw:border-b tw:border-dashed" />
            <AccordionItem id="accordion3" isDisabled>
                <AccordionHeader>
                    Accordion Title 3
                    <AccordionIcon />
                </AccordionHeader>
                <AccordionPanel>
                    Lorem Accordion 3 ipsum dolor sit amet consectetur,
                    adipisicing elit. Quos quaerat neque corporis consequatur
                    deserunt blanditiis ea, officia aliquid ab voluptatibus vel,
                    consequuntur eaque iure! Blanditiis, assumenda? Illo
                    nesciunt dolorum exercitationem.
                </AccordionPanel>
            </AccordionItem>
        </Accordion>
    )
}

const meta: Meta<typeof BorderedGreenTemplate> = {
    title: 'Components/Accordion',
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        controlled: {
            control: 'boolean',
            description: 'Whether the accordion is controlled or uncontrolled',
        },
        variant: {
            control: 'select',
            options: ['default', 'bordered'],
            description: 'The visual variant of the accordion',
        },
        colorSchema: {
            control: 'select',
            options: ['neutral', 'green'],
            description: 'The color scheme of the accordion',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

// Bordered + Green Variant Stories
export const BorderedGreenControlled: Story = {
    render: BorderedGreenTemplate,
    args: {
        controlled: true,
        variant: 'bordered',
        colorSchema: 'green',
    },
    parameters: {
        docs: {
            description: {
                story: 'Bordered green accordion with controlled state.',
            },
        },
    },
}

export const BorderedGreenUncontrolled: Story = {
    render: BorderedGreenTemplate,
    args: {
        controlled: false,
        variant: 'bordered',
        colorSchema: 'green',
    },
    parameters: {
        docs: {
            description: {
                story: 'Bordered green accordion with uncontrolled state.',
            },
        },
    },
}

// Default Variant Stories
export const DefaultControlled: Story = {
    render: DefaultTemplate,
    args: {
        controlled: true,
        variant: 'default',
        colorSchema: 'neutral',
    },
    parameters: {
        docs: {
            description: {
                story: 'Default accordion with controlled state.',
            },
        },
    },
}

export const DefaultUncontrolled: Story = {
    render: DefaultTemplate,
    args: {
        controlled: false,
        variant: 'default',
        colorSchema: 'neutral',
    },
    parameters: {
        docs: {
            description: {
                story: 'Default accordion with uncontrolled state.',
            },
        },
    },
}

export const RTL: Story = {
    render: DefaultTemplate,
    args: {
        controlled: true,
        variant: 'default',
        colorSchema: 'neutral',
    },
    parameters: {
        docs: {
            description: {
                story: 'Default accordion with RTL (right-to-left) text direction support.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}
