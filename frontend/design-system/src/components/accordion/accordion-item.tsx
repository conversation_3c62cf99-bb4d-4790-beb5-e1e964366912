import { Disclosure as ReactAriaAccordionItem } from 'react-aria-components'
import { cn } from '../../utils/cn'

const AccordionItem = ({
    children,
    ...props
}: React.ComponentProps<typeof ReactAriaAccordionItem>): React.ReactElement => {
    return (
        <ReactAriaAccordionItem
            className={cn(
                // [Base styles]
                `tw:[&:not(:last-child)]:mb-[1px]`,
                // [colorSchema]
                //-- [colorSchema][green]
                //--- Expanded
                `tw:[[data-acc-color-schema=green]_&[data-expanded=true]:not([data-disabled])]:border-primary-green`,
                // [variant]
                //-- [variant][bordered]
                //--- Collapsed
                `tw:[[data-acc-variant=bordered]_&]:border tw:[[data-acc-variant=bordered]_&]:border-light-gray-2`,
                props.className,
            )}
            {...props}
        >
            {children}
        </ReactAriaAccordionItem>
    )
}

export { AccordionItem }
