import { DisclosurePanel as ReactAriaAccordionPanel } from 'react-aria-components'
import { cn } from '../../utils/cn'

const AccordionPanel = ({
    children,
    ...props
}: React.ComponentProps<
    typeof ReactAriaAccordionPanel
>): React.ReactElement => {
    return (
        <ReactAriaAccordionPanel className={cn(props.className)} {...props}>
            {children}
        </ReactAriaAccordionPanel>
    )
}

export { AccordionPanel }
