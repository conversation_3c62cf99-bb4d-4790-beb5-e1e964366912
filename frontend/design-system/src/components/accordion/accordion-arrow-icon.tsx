import { cn } from '../../utils/cn'

const AccordionArrowIcon = (
    props: React.SVGProps<SVGSVGElement>,
): React.ReactElement => {
    return (
        <svg
            width="9"
            height="13"
            viewBox="0 0 9 13"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={cn(
                // Default
                `tw:shrink-0`,
                `tw:transition-transform`,
                `tw:text-light-gray-4`,
                // Collapsed
                `tw:[[aria-expanded=false]_&]:rotate-[0deg]`,
                // Expanded
                `tw:[[aria-expanded=true]:not([data-disabled])_&]:rotate-[90deg]`,
                props.className,
            )}
            {...props}
        >
            <title>Arrow Icon</title>
            <path
                d="M0.848145 10.9939L5.44189 6.40015L0.848145 1.8064L2.25439 0.400146L8.25439 6.40015L2.25439 12.4001L0.848145 10.9939Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { AccordionArrowIcon }
