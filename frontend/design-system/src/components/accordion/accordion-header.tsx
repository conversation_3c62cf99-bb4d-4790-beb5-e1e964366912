import {
    Button as ReactAriaAccordionButton,
    Heading as <PERSON>actAriaAccordionTitle,
} from 'react-aria-components'
import { cn } from '../../utils/cn'

const AccordionHeader = ({
    children,
    ...props
}: React.ComponentProps<typeof ReactAriaAccordionButton> & {
    children: React.ReactNode
}): React.ReactElement => {
    return (
        <ReactAriaAccordionTitle>
            <ReactAriaAccordionButton
                className={cn(
                    // [Base styles]
                    `tw:w-full`,
                    `tw:flex tw:w-full tw:items-center tw:gap-[12px]`,
                    `tw:px-[16px]`,
                    `tw:leading-1`,
                    // Enabled
                    `tw:[&:not([data-disabled])]:cursor-pointer`,
                    // Disabled
                    `tw:[&[data-disabled]]:border-transparent tw:[&[data-disabled]]:border-b tw:[&[data-disabled]]:bg-[#fff]`,
                    `tw:[&[data-disabled]]:text-light-gray-3`,
                    // [colorSchema]
                    //-- [colorSchema][neutral]
                    //--- Collapsed + Expanded
                    `tw:[[data-acc-color-schema=neutral]_&]:h-[38px] tw:[[data-acc-color-schema=neutral]_&]:bg-light-gray-1`,
                    //-- [colorSchema][green]
                    `tw:[[data-acc-color-schema=green]_&]:h-[50px]`,
                    //--- Expanded
                    `tw:[[data-acc-color-schema=green]_&[aria-expanded=true]:not([data-disabled])]:border-primary-green`,
                    // [variant]
                    //-- [variant][bordered]
                    //--- Collapsed
                    `tw:[[data-acc-variant=bordered]_&[aria-expanded=false]]:bg-[#fff]`,
                    `tw:[[data-acc-variant=bordered]_&[aria-expanded=false]]:border-transparent tw:[[data-acc-variant=bordered]_&[aria-expanded=false]]:border-b`,
                    //-- [colorSchema][neutral]
                    //-- [variant][bordered]
                    `tw:[[data-acc-color-schema=neutral][data-acc-variant=bordered]_&[aria-expanded=true]]:border-light-gray-2 tw:[[data-acc-color-schema=neutral][data-acc-variant=bordered]_&[aria-expanded=true]]:border-b`,
                    //-- [colorSchema][green]
                    //--- Expanded
                    `tw:[[data-acc-color-schema=green]_&[aria-expanded=true]]:bg-[#EFFDF8]`,
                    //-- [variant][bordered]
                    //--- Expanded
                    `tw:[[data-acc-color-schema=green][data-acc-variant=bordered]_&[aria-expanded=true]]:border-primary-green tw:[[data-acc-color-schema=green][data-acc-variant=bordered]_&[aria-expanded=true]]:border-b`,
                    props.className,
                )}
                {...props}
                slot="trigger"
            >
                {children}
            </ReactAriaAccordionButton>
        </ReactAriaAccordionTitle>
    )
}

export { AccordionHeader }
