import { DisclosureGroup as ReactAriaAccordion } from 'react-aria-components'
import { cn } from '../../utils/cn'

const Accordion = ({
    children,
    colorSchema = 'neutral',
    variant = 'default',
    ...props
}: React.ComponentProps<typeof ReactAriaAccordion> & {
    colorSchema?: 'neutral' | 'green'
    variant?: 'default' | 'bordered'
}): React.ReactElement => {
    return (
        <ReactAriaAccordion
            className={cn(props.className)}
            allowsMultipleExpanded
            data-acc-color-schema={colorSchema}
            data-acc-variant={variant}
            {...props}
        >
            {children}
        </ReactAriaAccordion>
    )
}

export { Accordion }
