import { TabList as ReactAriaTabList } from 'react-aria-components'
import { cn } from '../../utils/cn'

const TabList: typeof ReactAriaTabList = ({ children, ...props }) => {
    return (
        <ReactAriaTabList
            className={cn(
                'tw:flex tw:shrink-0 tw:overflow-auto tw:whitespace-nowrap tw:border-[#E4EBF2] tw:[&[data-orientation=horizontal]]:border-b tw:[&[data-orientation=vertical]]:flex-col tw:[&[data-orientation=vertical]]:border-e',
                props.className,
            )}
            {...props}
        >
            {children}
        </ReactAriaTabList>
    )
}

export { TabList }
