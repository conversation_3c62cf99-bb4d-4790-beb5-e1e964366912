import { Tab as React<PERSON>riaTab } from 'react-aria-components'
import { cn } from '../../utils/cn'

const Tab: typeof ReactAriaTab = ({ children, ...props }) => {
    return (
        <ReactAriaTab
            className={cn(
                `tw:relative tw:mx-[10px] tw:cursor-pointer tw:border-t-[3px] tw:border-t-[transparent] tw:border-b-[3px] tw:border-b-[transparent] tw:py-[10px] tw:text-[var(--text-color-base)] tw:text-sm tw:outline-none tw:transition-colors tw:duration-200 tw:[forced-color-adjust:none] tw:after:absolute tw:after:inset-[-3px_-8px] tw:after:z-[-1] tw:after:transition-colors tw:after:duration-200 tw:after:content-[''] tw:hover:bg-[#E6F0FE] tw:data-[disabled]:cursor-not-allowed tw:data-[disabled][data-selected]:border-b-[3px] tw:data-[disabled][data-selected]:border-b-[var(--text-color-disabled)] tw:data-[selected]:border-b-[3px] tw:data-[selected]:border-b-[var(--highlight-background)] tw:data-[disabled]:text-[var(--text-color-disabled)] tw:data-[focused]:text-[var(--text-color-hover)] tw:data-[hovered]:text-[var(--text-color-hover)] tw:data-[selected]:text-[var(--highlight-background)] tw:data-[selected]:hover:bg-[#fff] tw:data-[focus-visible]:after:absolute tw:data-[focus-visible]:after:inset-[-4px] tw:data-[focus-visible]:after:rounded-[4px] tw:data-[focus-visible]:after:border-2 tw:data-[focus-visible]:after:border-[var(--focus-ring-color)] tw:data-[focus-visible]:after:content-[''] tw:data-[selected]:hover:after:bg-[#fff]! tw:[div:not([data-selected])>&]:hover:after:bg-[#E6F0FE]`,
                props.className,
            )}
            {...props}
        >
            {children}
        </ReactAriaTab>
    )
}

export { Tab }
