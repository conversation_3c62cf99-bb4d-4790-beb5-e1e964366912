import { Tabs as ReactAriaTabs } from 'react-aria-components'
import { cn } from '../../utils/cn'

const Tabs: typeof ReactAriaTabs = ({ children, ...props }) => {
    return (
        <ReactAriaTabs
            className={cn(
                'tw:flex tw:[--focus-ring-color:#1877F2] tw:[--highlight-background:#1877F2] tw:[--text-color-base:#4E5381] tw:[--text-color-disabled:#afafaf] tw:[&[data-orientation=horizontal]]:flex-col tw:[&[data-orientation=vertical]]:flex-row',
                props.className,
            )}
            {...props}
        >
            {children}
        </ReactAriaTabs>
    )
}

export { Tabs }
