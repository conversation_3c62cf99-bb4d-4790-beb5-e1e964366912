import { TabPanel as ReactAriaTabPanel } from 'react-aria-components'
import { cn } from '../../utils/cn'

const TabPanel: typeof ReactAriaTabPanel = ({ children, ...props }) => {
    return (
        <ReactAriaTabPanel
            className={cn(
                `tw:data-[focus-visible]:outline tw:data-[focus-visible]:outline-[var(--focus-ring-color)] tw:[div[data-orientation=horizontal]>&]:mt-2 tw:[div[data-orientation=vertical]>&]:ms-2`,
                props.className,
            )}
            {...props}
        >
            {children}
        </ReactAriaTabPanel>
    )
}

export { TabPanel }
