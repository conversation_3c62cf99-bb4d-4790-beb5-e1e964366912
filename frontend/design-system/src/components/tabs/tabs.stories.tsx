import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { Tab } from './tab'
import { TabList } from './tab-list'
import { TabPanel } from './tab-panel'
import { Tabs } from './tabs'

// Define the args type for the template
interface TabsTemplateArgs {
    // props
    orientation?: 'horizontal' | 'vertical'
    controlled?: boolean
}

// Template function that renders InputField with InputGroup and TextArea and handles all props
const TabsTemplate = (args: TabsTemplateArgs): React.JSX.Element => {
    const {
        // props
        orientation = 'horizontal',
        controlled = true,
    } = args

    const [value, setValue] = useState('tab1')

    return (
        <Tabs
            orientation={orientation}
            {...(controlled && {
                selectedKey: value,
                onSelectionChange: (key) => setValue(key as string),
            })}
            {...(!controlled && {
                defaultSelectedKey: 'tab1',
            })}
        >
            <TabList>
                <Tab id="tab1">Tab 1</Tab>
                <Tab id="tab2">Tab 2</Tab>
                <Tab id="tab3" isDisabled={true}>
                    Tab 3
                </Tab>
                <Tab id="tab4">
                    Very long tab name lorem ipsum dolor sit amet lorem ipsum
                </Tab>
            </TabList>
            <TabPanel id="tab1">
                Lorem Tab 1 ipsum, dolor sit amet consectetur adipisicing elit.
                Repudiandae, neque voluptas. Asperiores doloremque nemo
                repudiandae numquam recusandae quas ducimus dicta? Recusandae
                deleniti explicabo quia temporibus aliquam omnis odit autem
                molestias.
            </TabPanel>
            <TabPanel id="tab2">
                Lorem Tab 2 ipsum, dolor sit amet consectetur adipisicing elit.
                Repudiandae, neque voluptas. Asperiores doloremque nemo
                repudiandae numquam recusandae quas ducimus dicta? Recusandae
                deleniti explicabo quia temporibus aliquam omnis odit autem
                molestias.
            </TabPanel>
            <TabPanel id="tab3">
                Lorem Tab 3 ipsum, dolor sit amet consectetur adipisicing elit.
                Repudiandae, neque voluptas. Asperiores doloremque nemo
                repudiandae numquam recusandae quas ducimus dicta? Recusandae
                deleniti explicabo quia temporibus aliquam omnis odit autem
                molestias.
            </TabPanel>
            <TabPanel id="tab4">
                Lorem Tab 4 ipsum, dolor sit amet consectetur adipisicing elit.
                Repudiandae, neque voluptas. Asperiores doloremque nemo
                repudiandae numquam recusandae quas ducimus dicta? Recusandae
                deleniti explicabo quia temporibus aliquam omnis odit autem
                molestias.
            </TabPanel>
        </Tabs>
    )
}

const meta: Meta<typeof TabsTemplate> = {
    title: 'Components/Tabs',
    component: TabsTemplate,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        // props
        orientation: {
            control: 'select',
            options: ['horizontal', 'vertical'],
            description: 'The orientation of the tabs',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const ControlledState: Story = {
    render: TabsTemplate,
    args: {
        orientation: 'horizontal',
        controlled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic tabs component with controlled state.',
            },
        },
    },
}

export const UncontrolledState: Story = {
    render: TabsTemplate,
    args: {
        orientation: 'horizontal',
        controlled: false,
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic tabs component with uncontrolled state.',
            },
        },
    },
}

export const Vertical: Story = {
    render: TabsTemplate,
    args: {
        orientation: 'vertical',
    },
    parameters: {
        docs: {
            description: {
                story: 'Tabs with vertical orientation.',
            },
        },
    },
}

export const RTL: Story = {
    render: TabsTemplate,
    args: {
        orientation: 'horizontal',
    },
    parameters: {
        docs: {
            description: {
                story: 'Tabs with RTL (right-to-left) text direction support.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}
