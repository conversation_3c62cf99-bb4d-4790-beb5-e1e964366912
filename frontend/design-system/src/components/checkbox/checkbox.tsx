import type { JSX } from 'react'
import { Checkbox as AriaCheckbox } from 'react-aria-components'
import { cn } from '../../utils/cn'

export interface CheckboxProps {
    label?: string
    value?: boolean
    onChange?: (isSelected: boolean) => void
    disabled?: boolean
    className?: string
    name?: string
    invalid?: boolean
    indeterminate?: boolean
}

export function Checkbox({
    label,
    value,
    onChange,
    disabled = false,
    className = '',
    name,
    invalid = false,
    indeterminate = false,
}: CheckboxProps): JSX.Element {
    return (
        <AriaCheckbox
            name={name}
            isSelected={value}
            onChange={onChange}
            isDisabled={disabled}
            isInvalid={invalid}
            isIndeterminate={indeterminate}
            className={cn(
                'tw:group tw:relative tw:flex tw:items-center tw:gap-2 tw:text-annotation tw:text-primary-black tw:transition',
                'tw:disabled:cursor-not-allowed tw:disabled:text-[#75799D]',
                'tw:rounded-sm tw:bg-light-gray-1 tw:px-4 tw:py-3',
                className,
            )}
        >
            {({ isSelected, isIndeterminate }) => (
                <>
                    <div
                        className={cn(
                            'tw:flex tw:h-5 tw:w-5 tw:shrink-0 tw:items-center tw:justify-center tw:rounded-xs tw:border-1 tw:transition',
                            'tw:focus-visible:outline-2 tw:focus-visible:outline-black tw:focus-visible:outline-offset-2',
                            'tw:border-light-gray-3 tw:bg-white',
                            'tw:group-selected:border-info-1 tw:group-selected:bg-info-1',
                            'tw:group-disabled:group-selected:border-[#A5C8F7] tw:group-disabled:group-selected:bg-[#A5C8F7]',
                            'tw:group-indeterminate:border-info-1 tw:group-indeterminate:bg-info-1',
                            'tw:group-disabled:group-indeterminate:border-[#A5C8F7] tw:group-disabled:group-indeterminate:bg-[#A5C8F7]',
                            'tw:group-invalid:border-danger-1 tw:group-invalid:group-indeterminate:border-danger-1 tw:group-invalid:group-indeterminate:bg-danger-1 tw:group-invalid:group-selected:border-danger-1 tw:group-invalid:group-selected:bg-danger-1',
                            'tw:group-disabled:group-invalid:border-danger-1/50 tw:group-disabled:group-invalid:group-indeterminate:border-danger-1/50 tw:group-disabled:group-invalid:group-indeterminate:bg-transparent tw:group-disabled:group-invalid:group-selected:border-transparent tw:group-disabled:group-invalid:group-selected:bg-danger-1/50',
                            'tw:group-disabled:border-light-gray-3/50 tw:group-disabled:bg-white/50',
                        )}
                    >
                        {isIndeterminate ? (
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                aria-hidden
                                className="tw:h-[18px] tw:w-[18px]"
                            >
                                <title>Minus</title>
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M6.5 10C6.5 9.44772 6.94772 9 7.5 9H13.5C14.0523 9 14.5 9.44772 14.5 10C14.5 10.5523 14.0523 11 13.5 11H7.5C6.94772 11 6.5 10.5523 6.5 10Z"
                                    fill="white"
                                />
                            </svg>
                        ) : isSelected ? (
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                aria-hidden
                                className="tw:h-[18px] tw:w-[18px]"
                            >
                                <title>Check</title>
                                <path
                                    d="M17.1875 5.3125C17.5938 5.6875 17.5938 6.34375 17.1875 6.71875L9.1875 14.7188C8.8125 15.125 8.15625 15.125 7.78125 14.7188L3.78125 10.7188C3.375 10.3438 3.375 9.6875 3.78125 9.3125C4.15625 8.90625 4.8125 8.90625 5.1875 9.3125L8.46875 12.5938L15.7812 5.3125C16.1562 4.90625 16.8125 4.90625 17.1875 5.3125Z"
                                    fill="white"
                                />
                            </svg>
                        ) : null}
                    </div>
                    {label}
                </>
            )}
        </AriaCheckbox>
    )
}
