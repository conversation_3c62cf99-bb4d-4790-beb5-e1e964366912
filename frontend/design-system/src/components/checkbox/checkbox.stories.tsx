import type { <PERSON>a, StoryObj } from '@storybook/react'
import { Checkbox } from './checkbox'

const meta: Meta<typeof Checkbox> = {
    title: 'Components/Checkbox',
    component: Checkbox,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    args: {
        label: 'Accept terms and conditions',
        disabled: false,
    },
    argTypes: {
        label: {
            control: 'text',
            description: 'The label text displayed next to the checkbox',
        },
        value: {
            control: 'boolean',
            description:
                'Whether the checkbox is selected (controlled component)',
        },
        disabled: {
            control: 'boolean',
            description: 'Whether the checkbox is disabled',
        },
        invalid: {
            control: 'boolean',
            description: 'Whether the checkbox is in an invalid state',
        },
        indeterminate: {
            control: 'boolean',
            description:
                'Whether the checkbox is in an indeterminate state (partially selected)',
        },
        onChange: {
            table: {
                disable: true,
            },
        },
        className: {
            table: {
                disable: true,
            },
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        label: 'Accept terms and conditions',
    },
}

export const RTL: Story = {
    args: {
        label: 'أوافق على الشروط والأحكام',
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox demonstrates right-to-left (RTL) layout with Arabic text.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}

export const Selected: Story = {
    args: {
        label: 'Subscribe to newsletter',
        value: true,
    },
}

export const Indeterminate: Story = {
    args: {
        label: 'Select all notifications',
        indeterminate: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox is in an indeterminate state, typically used when some but not all child checkboxes are selected.',
            },
        },
    },
}

export const Invalid: Story = {
    args: {
        label: 'Accept terms and conditions',
        invalid: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox is in an invalid state, typically used for form validation errors.',
            },
        },
    },
}

export const InvalidSelected: Story = {
    args: {
        label: 'Accept terms and conditions',
        value: true,
        invalid: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox is selected and in an invalid state, showing both states simultaneously.',
            },
        },
    },
}

export const InvalidIndeterminate: Story = {
    args: {
        label: 'Select all notifications',
        indeterminate: true,
        invalid: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox is indeterminate and in an invalid state, showing both states simultaneously.',
            },
        },
    },
}

export const Disabled: Story = {
    args: {
        label: 'Premium feature (disabled)',
        disabled: true,
    },
}

export const DisabledSelected: Story = {
    args: {
        label: 'System updates (disabled)',
        value: true,
        disabled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox is disabled and selected (value is true). It shows the disabled state while maintaining the selected appearance.',
            },
        },
    },
}

export const DisabledIndeterminate: Story = {
    args: {
        label: 'Select all notifications',
        indeterminate: true,
        disabled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox is disabled and indeterminate, showing both states simultaneously.',
            },
        },
    },
}

export const InvalidDisabled: Story = {
    args: {
        label: 'Accept terms and conditions',
        invalid: true,
        disabled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox is invalid and disabled, showing both states simultaneously.',
            },
        },
    },
}

export const InvalidDisabledIndeterminate: Story = {
    args: {
        label: 'Select all notifications',
        value: true,
        invalid: true,
        disabled: true,
        indeterminate: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox is selected, invalid, disabled, and indeterminate, showing all four states simultaneously.',
            },
        },
    },
}

export const InvalidDisabledSelected: Story = {
    args: {
        label: 'Accept terms and conditions',
        value: true,
        invalid: true,
        disabled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This checkbox is selected, invalid, and disabled, showing all three states simultaneously.',
            },
        },
    },
}

export const ShortLabel: Story = {
    args: {
        label: 'Yes',
    },
}

export const LongLabel: Story = {
    args: {
        label: 'I agree to receive marketing communications and promotional materials from the company',
    },
}

export const MultipleCheckboxes: Story = {
    render: () => (
        <div className="tw:flex tw:flex-col tw:gap-3">
            <Checkbox label="Email notifications" />
            <Checkbox label="SMS notifications" value={true} />
            <Checkbox label="Push notifications" />
            <Checkbox label="Newsletter subscription" disabled={true} />
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Example of multiple checkboxes in a form, showing different states.',
            },
        },
    },
}
