import type { VariantProps } from 'class-variance-authority'
import type { JSX, ReactNode } from 'react'
import { Link as ReactAriaLink } from 'react-aria-components'
import { cn } from '../../utils/cn'
import {
    type ButtonVariants,
    buttonVariants,
} from '../button/button-variants'

interface LinkProps extends ButtonVariants {
    children: ReactNode
    href: string
    variant?: 'filled' | 'outlined' | 'subtle' | 'text'
    color?: 'primary' | 'secondary' | 'warning' | 'error' | 'neutral'
    size?: 'sm' | 'icon' | 'lg'
    className?: string
    isDisabled?: boolean
    target?: '_blank' | '_self' | '_parent' | '_top'
}

export function Link({
    children,
    href,
    target,
    variant = 'filled',
    color = 'primary',
    size = 'lg',
    className = '',
    isDisabled = false,
    ...props
}: LinkProps): JSX.Element {

    const linkClasses = cn(
        buttonVariants({ variant, color, size } as VariantProps<
            typeof buttonVariants
        >),
        className,
    )

    return (
        <ReactAriaLink
            href={href}
            className={linkClasses}
            isDisabled={isDisabled}
            target={target}
            data-button-size={size}
            {...props}
        >
            {children}
        </ReactAriaLink>
    )
}
