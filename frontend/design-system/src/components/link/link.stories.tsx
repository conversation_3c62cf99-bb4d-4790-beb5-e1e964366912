import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Link } from './link'

const meta: Meta<typeof Link> = {
    title: 'Components/Link',
    component: Link,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        variant: {
            control: { type: 'select' },
            options: ['filled', 'outlined', 'subtle', 'text'],
        },
        color: {
            control: { type: 'select' },
            options: ['primary', 'secondary', 'warning', 'error', 'neutral'],
        },
        size: {
            control: { type: 'select' },
            options: ['sm', 'lg', 'icon'],
        },
        isDisabled: {
            control: { type: 'boolean' },
        },
        target: {
            control: { type: 'select' },
            options: ['_self', '_blank', '_parent', '_top'],
            description: 'Target attribute for the link',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const ExternalLink: Story = {
    args: {
        children: 'Link',
        href: '#',
        target: '_blank',
    },
}

export const Disabled: Story = {
    args: {
        children: 'Disabled Link',
        href: '#',
        target: '_blank',
        isDisabled: true,
    },
}
