import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Card } from './card'
import { CardContent } from './card-content'
import { CardHeader } from './card-header'
import { CardTitle } from './card-title'

const meta: Meta<typeof Card> = {
    title: 'Components/Card',
    render: (args) => (
        <div className="tw:bg-light-gray-3 tw:p-4">
            <Card {...args} />
        </div>
    ),
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: (
            <>
                <CardHeader>
                    <CardTitle>Card Title</CardTitle>
                </CardHeader>
                <CardContent>
                    Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                    Suscipit ex modi iusto sed dolor, inventore nesciunt eveniet
                    beatae repudiandae tempora odit consectetur odio doloremque
                    expedita eum, molestias vero amet minima.
                </CardContent>
            </>
        ),
    },
}

export const WithCustomClassName: Story = {
    args: {
        className: 'tw:shadow-lg tw:border',
        children: (
            <>
                <CardHeader>
                    <CardTitle>Custom Card</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="tw:text-gray-600">
                        Card with custom styling including shadow and border.
                    </p>
                </CardContent>
            </>
        ),
    },
}
