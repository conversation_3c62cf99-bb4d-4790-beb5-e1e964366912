import type { JSX } from 'react/jsx-runtime'
import { cn } from '../../utils/cn'

export const CardTitle = ({
    children,
    className,
}: {
    children?: React.ReactNode
    className?: string
}): JSX.Element => {
    return (
        <div
            className={cn(
                'tw:font-bold tw:text-black tw:text-sm tw:leading-125%',
                className,
            )}
        >
            {children}
        </div>
    )
}
