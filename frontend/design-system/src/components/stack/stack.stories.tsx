import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Box } from '../box/box'
import { Stack } from './stack'

const meta: Meta<typeof Stack> = {
    title: 'Layout/Stack',
    component: Stack,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    args: {
        className: 'tw:gap-3',
    },
    argTypes: {
        style: {
            control: false,
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
    render: (args) => (
        <Stack {...args}>
            <Box className="tw:h-60 tw:w-60 tw:bg-blue-500" />
            <Box className="tw:h-60 tw:w-60 tw:bg-green-500" />
            <Box className="tw:h-60 tw:w-60 tw:bg-yellow-500" />
            <Box className="tw:h-60 tw:w-60 tw:bg-purple-500" />
            <Box className="tw:h-60 tw:w-60 tw:bg-orange-500" />
            <Box className="tw:h-60 tw:w-60 tw:bg-pink-500" />
            <Box className="tw:h-60 tw:w-60 tw:bg-brown-500" />
            <Box className="tw:h-60 tw:w-60 tw:bg-gray-500" />
        </Stack>
    ),
}
