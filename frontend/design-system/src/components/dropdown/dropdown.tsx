import type { J<PERSON><PERSON>, ReactNode } from 'react'
import { useEffect, useRef, useState } from 'react'
import {
    Select as AriaSelect,
    Button,
    ListBox,
    ListBoxItem,
    type ListBoxItemProps,
    Popover,
    SelectValue,
} from 'react-aria-components'
import { cn } from '../../utils/cn'

export interface DropdownItem {
    id: string
    name: string
    textValue?: string
    isDisabled?: boolean
}

export interface DropdownRootProps {
    children: ReactNode
    selectedKey?: string
    onSelectionChange?: (key: string) => void
    isDisabled?: boolean
    name?: string
    autoFocus?: boolean
    onOpenChange?: (open: boolean) => void
    className?: string
    // Search functionality props
    searchable?: boolean
    searchPlaceholder?: string
    searchFunction?: (
        items: DropdownItem[],
        searchTerm: string,
    ) => DropdownItem[]
    onSearchChange?: (searchTerm: string) => void
}

// Default search function that filters by name and textValue
const defaultSearchFunction = (
    items: DropdownItem[],
    searchTerm: string,
): DropdownItem[] => {
    if (!searchTerm.trim()) return items

    const lowerSearchTerm = searchTerm.toLowerCase()
    return items.filter(
        (item) =>
            item.name.toLowerCase().includes(lowerSearchTerm) ||
            item.textValue?.toLowerCase().includes(lowerSearchTerm),
    )
}

export function DropdownRoot({
    children,
    selectedKey,
    onSelectionChange,
    isDisabled = false,
    name,
    autoFocus,
    onOpenChange,
    className = '',
    searchable = true,
    searchPlaceholder = 'Please enter 1 keyword or more to search',
    searchFunction = defaultSearchFunction,
    onSearchChange,
}: DropdownRootProps): JSX.Element {
    const [searchTerm, setSearchTerm] = useState('')
    const [isOpen, setIsOpen] = useState(false)
    const searchInputRef = useRef<HTMLInputElement>(null)
    const listBoxRef = useRef<HTMLDivElement>(null)

    const handleOpenChange = (open: boolean) => {
        setIsOpen(open)
        if (!open) {
            setSearchTerm('')
            onSearchChange?.('')
        }
        onOpenChange?.(open)
    }

    const handleSearchChange = (value: string) => {
        setSearchTerm(value)
        onSearchChange?.(value)
    }

    return (
        <AriaSelect
            selectedKey={selectedKey}
            onSelectionChange={(key) => onSelectionChange?.(key as string)}
            isDisabled={isDisabled}
            name={name}
            autoFocus={autoFocus}
            onOpenChange={handleOpenChange}
            className={cn(
                'tw:peer tw:relative tw:flex tw:flex-1 tw:group-data-[group=true]/input-group:static',
                className,
            )}
            data-select="true"
            aria-labelledby={name || 'dropdown-label'}
        >
            <DropdownContext.Provider
                value={{
                    searchable,
                    searchTerm,
                    onSearchChange: handleSearchChange,
                    searchPlaceholder,
                    searchFunction,
                    isOpen,
                    setIsOpen: setIsOpen,
                    searchInputRef: searchInputRef,
                    listBoxRef: listBoxRef,
                    selectedKey: selectedKey,
                    onSelectionChange: onSelectionChange,
                    onOpenChange: onOpenChange,
                }}
            >
                {children}
            </DropdownContext.Provider>
        </AriaSelect>
    )
}

// Context for passing search state down to child components
import { createContext, useContext } from 'react'

interface DropdownContextValue {
    searchable: boolean
    searchTerm: string
    onSearchChange: (value: string) => void
    searchPlaceholder: string
    searchFunction: (
        items: DropdownItem[],
        searchTerm: string,
    ) => DropdownItem[]
    isOpen: boolean
    setIsOpen: (isOpen: boolean) => void
    searchInputRef: React.RefObject<HTMLInputElement | null>
    listBoxRef: React.RefObject<HTMLDivElement | null>
    selectedKey: string | undefined
    onSelectionChange: ((key: string) => void) | undefined
    onOpenChange: ((open: boolean) => void) | undefined
}

const DropdownContext = createContext<DropdownContextValue | null>(null)

const useDropdownContext = () => {
    const context = useContext(DropdownContext)
    if (!context) {
        throw new Error('Dropdown components must be used within DropdownRoot')
    }
    return context
}

// DropdownTrigger component
export interface DropdownTriggerProps {
    placeholder?: string
    selectedKey?: string
    isOpen?: boolean
    className?: string
}

export function DropdownTrigger({
    placeholder = 'Select an option...',
    selectedKey,
    isOpen = false,
    className = '',
}: DropdownTriggerProps): JSX.Element {
    return (
        <Button
            className={cn(
                // Base styles
                'tw:min-h-[38px] tw:w-full tw:flex-1 tw:border-0 tw:bg-transparent tw:px-4 tw:text-base! tw:text-primary-black tw:leading-normal tw:outline-none tw:group-disabled:cursor-not-allowed',
                'tw:disabled:bg-transparent tw:disabled:text-light-gray-4',
                'tw:relative tw:flex tw:items-center',

                // Standalone styles
                'tw:rounded-xs tw:border-[1px] tw:bg-white tw:transition tw:focus-within:border-info-1',
                'tw:border-[#E4EBF2]',
                'tw:group-invalid:border-danger-1 tw:group-invalid:focus-within:border-danger-1',
                'tw:group-data-[success=true]:border-success-2 tw:group-data-[success=true]:focus-within:border-success-2',
                'tw:hover:ring-2 tw:hover:ring-[#D8E8FD]',
                'tw:group-invalid:hover:ring-2 tw:group-invalid:hover:ring-danger-1/50',
                'tw:group-data-[success=true]:hover:ring-2 tw:group-data-[success=true]:hover:ring-success-2/50',
                'tw:group-disabled:cursor-not-allowed tw:group-disabled:bg-light-gray-1',
                'tw:group-disabled:hover:ring-0',

                // Reset styles when inside an InputGroup
                'tw:group-data-[group=true]/input-group:static tw:group-data-[group=true]/input-group:rounded-none tw:group-data-[group=true]/input-group:border-none tw:group-data-[group=true]/input-group:ring-0 tw:group-data-[group=true]/input-group:group-data-[success=true]:hover:ring-0 tw:group-data-[group=true]/input-group:hover:ring-0',
                className,
            )}
        >
            <SelectValue
                className={cn(
                    'tw:flex-1 tw:truncate tw:text-left',
                    !selectedKey && 'tw:text-light-gray-4',
                )}
            >
                {({ defaultChildren, isPlaceholder }) => {
                    return isPlaceholder ? placeholder : defaultChildren
                }}
            </SelectValue>

            <span
                className={cn(
                    'tw:-translate-y-1/2 tw:absolute tw:end-2 tw:top-1/2 tw:h-6 tw:w-6 tw:text-light-gray-4 tw:transition-transform tw:duration-200',
                    // TODO: not reflecting the open state (to be fixed)
                    isOpen && 'tw:rotate-180',
                )}
            >
                <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    aria-hidden="true"
                >
                    <path
                        d="M8 10L12 14L16 10L17.4 11.4L12 16.8L6.6 11.4L8 10Z"
                        fill="currentColor"
                    />
                </svg>
            </span>
        </Button>
    )
}

// DropdownList component
export interface DropdownListProps {
    children: ReactNode
    className?: string
    items?: DropdownItem[]
}

export function DropdownList({
    children,
    className = '',
    items = [],
}: DropdownListProps): JSX.Element {
    const {
        searchable,
        searchTerm,
        onSearchChange,
        searchPlaceholder,
        searchFunction,
        isOpen,
        setIsOpen,
        searchInputRef,
        listBoxRef,
        selectedKey,
        onSelectionChange,
        onOpenChange,
    } = useDropdownContext()

    // Focus search input when dropdown opens
    useEffect(() => {
        if (searchable && isOpen && searchInputRef.current) {
            // Small delay to ensure the popover is fully rendered
            setTimeout(() => {
                searchInputRef.current?.focus()
            }, 100)
        }
    }, [isOpen, searchable])

    // Add keyboard navigation between search input and list items
    useEffect(() => {
        if ((!isOpen && !searchable) || !listBoxRef.current) return

        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'ArrowUp') {
                const activeElement = document.activeElement as HTMLElement
                if (
                    activeElement &&
                    activeElement.getAttribute('role') === 'option'
                ) {
                    const listBox = listBoxRef.current
                    if (listBox) {
                        const allOptions = Array.from(
                            listBox.querySelectorAll(
                                '[role="option"]:not([aria-disabled="true"])',
                            ),
                        )
                        const currentIndex = allOptions.indexOf(activeElement)

                        if (currentIndex === 0 && searchInputRef.current) {
                            e.preventDefault()
                            searchInputRef.current.focus()
                        }
                    }
                }
            }
        }

        const listBox = listBoxRef.current
        listBox.addEventListener('keydown', handleKeyDown)

        return () => {
            listBox.removeEventListener('keydown', handleKeyDown)
        }
    }, [isOpen, searchable, searchInputRef, listBoxRef])

    // Handle ArrowDown from search input to focus first list item
    const handleSearchInputKeyDown = (
        e: React.KeyboardEvent<HTMLInputElement>,
    ) => {
        if (e.key === 'ArrowDown' && listBoxRef.current) {
            e.preventDefault()
            // Focus the first item in the list
            const firstItem = listBoxRef.current.querySelector(
                '[role="option"]:not([aria-disabled="true"])',
            ) as HTMLElement
            if (firstItem) {
                firstItem.focus()
            }
        }
    }

    // Filter items based on search term
    const filteredItems =
        searchable && items.length > 0
            ? searchFunction(items, searchTerm)
            : items

    // If items are provided, render filtered SelectItems
    const renderChildren = () => {
        if (items.length > 0) {
            return filteredItems.map((item) => (
                <SelectItem key={item.id} item={item} />
            ))
        }
        return children
    }

    // Empty option
    const renderEmptyOption = (onClick: () => void) => {
        return (
            <Button
            className={cn(
                'tw:group tw:flex tw:cursor-pointer tw:select-none tw:items-center tw:gap-2 tw:px-4 tw:py-3 tw:text-start tw:text-primary-black tw:outline-none',
                'tw:hover:bg-light-gray-1 tw:focus:bg-light-gray-1 tw:focus:text-info-1',
                'tw:disabled:cursor-not-allowed tw:disabled:opacity-50',
                'tw:data-[selected=true]:bg-light-gray-1 tw:data-[selected=true]:text-info-1',
            )}
            onClick={onClick}
        >
            <span className="tw:flex-1 tw:truncate tw:font-normal">None</span>
        </Button>
        )
    }

    // If items are provided, render filtered SelectItems
    const renderSelectedItems = () => {
        if (items.length > 0) {
            return items
                .filter((item) => selectedKey === item.id)
                .map((item) => <SelectItem key={item.id} item={item} />)
        }
        return children
    }

    return (
        <Popover
            className={cn(
                'tw:flex tw:max-h-80 tw:min-w-[var(--trigger-width)] tw:flex-col tw:rounded-xs tw:border tw:border-[#E4EBF2] tw:bg-white tw:text-base tw:shadow-lg tw:ring-1 tw:ring-black/5',
                'tw:entering:fade-in tw:entering:zoom-in-95 tw:entering:animate-in',
                'tw:exiting:fade-out tw:exiting:zoom-out-95 tw:exiting:animate-out',
                'tw:data-[placement=top]:slide-in-from-bottom-2',
                'tw:data-[placement=bottom]:slide-in-from-top-2',
                className,
            )}
        >
            {searchable && (
                <div className="tw:border-[#E4EBF2] tw:border-b tw:p-2">
                    <div className="tw:relative">
                        <input
                            ref={searchInputRef}
                            type="text"
                            placeholder={searchPlaceholder}
                            value={searchTerm}
                            onChange={(e) => onSearchChange(e.target.value)}
                            onKeyDown={handleSearchInputKeyDown}
                            className="tw:w-full tw:rounded-xs tw:border tw:border-[#E4EBF2] tw:bg-white tw:p-2 tw:text-primary-black tw:text-sm tw:placeholder-light-gray-4 tw:outline-none tw:focus:border-info-1 tw:focus:ring-2 tw:focus:ring-[#D8E8FD]"
                            onClick={(e) => e.stopPropagation()}
                        />
                    </div>
                </div>
            )}
            {filteredItems.length > 0 && renderEmptyOption(() => {
                // TODO: not hiding the dropdown when clicking on the empty option (to be fixed)
                setIsOpen(false)
                onOpenChange?.(false)
                onSelectionChange?.('')
            })}
            <ListBox
                ref={listBoxRef}
                className="tw:flex-1 tw:overflow-auto tw:outline-none"
            >
                {searchable &&
                items.length > 0 &&
                filteredItems.length === 0 ? (
                    <>
                        {renderSelectedItems()}
                        <ListBoxItem
                            id="no-results"
                            textValue="No results found"
                            isDisabled={true}
                            className="tw:flex tw:min-h-[60px] tw:cursor-default tw:items-center tw:justify-center tw:p-4"
                        >
                            <span className="tw:text-center tw:text-light-gray-4 tw:text-sm">
                                No results found
                            </span>
                        </ListBoxItem>
                    </>
                ) : (
                    <>
                        {filteredItems.filter((item) => selectedKey === item.id)
                            .length === 0 && renderSelectedItems()}
                        {renderChildren()}
                    </>
                )}
            </ListBox>
        </Popover>
    )
}

interface SelectItemProps extends ListBoxItemProps {
    item: DropdownItem
}

function SelectItem({ item, ...props }: SelectItemProps): JSX.Element {
    return (
        <ListBoxItem
            {...props}
            id={item.id}
            textValue={item.textValue || item.name}
            isDisabled={item.isDisabled}
            className={cn(
                'tw:group tw:flex tw:cursor-pointer tw:select-none tw:items-center tw:gap-2 tw:px-4 tw:py-3 tw:text-primary-black tw:outline-none',
                'tw:hover:bg-light-gray-1 tw:focus:bg-light-gray-1 tw:focus:text-info-1',
                'tw:disabled:cursor-not-allowed tw:disabled:opacity-50',
                'tw:data-[selected=true]:bg-light-gray-1 tw:data-[selected=true]:text-info-1',
            )}
        >
            <span className="tw:flex-1 tw:truncate tw:font-normal">{item.name}</span>
        </ListBoxItem>
    )
}

// Export the individual SelectItem for use in stories
export { SelectItem }
