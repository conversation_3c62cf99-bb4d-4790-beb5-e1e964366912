import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import type React from 'react'
import { useEffect, useState } from 'react'
import { CalendarIcon } from '../../icons/calendar-icon'
import { CheckIcon } from '../../icons/check-icon'
import { CloseIcon } from '../../icons/close-icon'
import { MoneyIcon } from '../../icons/money-icon'
import { SearchIcon } from '../../icons/search-icon'
import {
    ErrorMessage,
    InputField,
    InputGroup,
    InputPostfix,
    InputPrefix,
    SupportText,
} from '../input/input'
import { Label } from '../label/label'
import {
    type DropdownItem,
    DropdownList,
    DropdownRoot,
    DropdownTrigger,
    SelectItem,
} from './dropdown'

// Sample data
const sampleItems: DropdownItem[] = [
    { id: '1', name: 'Option 1' },
    { id: '2', name: 'Option 2' },
    { id: '3', name: 'Option 3' },
    { id: '4', name: 'Option 4' },
    { id: '5', name: 'Disabled Option', isDisabled: true },
]

const languageItems: DropdownItem[] = [
    { id: 'en', name: 'English' },
    { id: 'es', name: 'Spanish' },
    { id: 'fr', name: 'French' },
    { id: 'de', name: 'German' },
    { id: 'ja', name: 'Japanese' },
]

const countryItems: DropdownItem[] = [
    { id: 'us', name: 'United States' },
    { id: 'ca', name: 'Canada' },
    { id: 'uk', name: 'United Kingdom' },
    { id: 'fr', name: 'France' },
    { id: 'de', name: 'Germany' },
    { id: 'jp', name: 'Japan' },
]

const currencyItems: DropdownItem[] = [
    { id: 'usd', name: 'US Dollar (USD)' },
    { id: 'eur', name: 'Euro (EUR)' },
    { id: 'gbp', name: 'British Pound (GBP)' },
    { id: 'jpy', name: 'Japanese Yen (JPY)' },
]

// Define the args type for the template
interface DropdownTemplateArgs {
    // InputField props
    label?: string
    supportText?: string
    errorMessage?: string
    required?: boolean
    invalid?: boolean
    success?: boolean
    isDisabled?: boolean
    name?: string
    autoFocus?: boolean
    className?: string
    // Dropdown props
    placeholder?: string
    selectedKey?: string
    onSelectionChange?: (key: string) => void
    // Search functionality props
    searchable?: boolean
    searchPlaceholder?: string
    customSearchFunction?: boolean
    // Additional props for prefix/postfix
    hasPrefixIcon?: boolean
    hasPostfixIcon?: boolean
    hasPrefixAddon?: boolean
    hasPostfixAddon?: boolean
    prefixIcon?: 'search' | 'calendar' | 'money'
    postfixIcon?: 'check' | 'close'
    prefixAddon?: string
    postfixAddon?: string
    // Data options
    dataSet?: 'sample' | 'languages' | 'countries' | 'currencies'
}

// Custom search function that searches only by the first letter
const firstLetterSearchFunction = (
    items: DropdownItem[],
    searchTerm: string,
): DropdownItem[] => {
    if (!searchTerm.trim()) return items

    const lowerSearchTerm = searchTerm.toLowerCase()
    return items.filter((item) =>
        item.name.toLowerCase().startsWith(lowerSearchTerm),
    )
}

// Template function that renders InputField with InputGroup and Dropdown and handles all props
const DropdownTemplate = (args: DropdownTemplateArgs): React.JSX.Element => {
    const {
        // InputField props
        label,
        supportText,
        errorMessage,
        required,
        invalid,
        success,
        isDisabled,
        name,
        autoFocus,
        className,
        // Dropdown props
        placeholder,
        selectedKey: initialSelectedKey,
        // Search functionality props
        searchable,
        searchPlaceholder,
        customSearchFunction,
        // Additional props for prefix/postfix
        hasPrefixIcon,
        hasPostfixIcon,
        hasPrefixAddon,
        hasPostfixAddon,
        prefixIcon,
        postfixIcon,
        prefixAddon,
        postfixAddon,
        // Data options
        dataSet = 'sample',
    } = args

    const [selectedKey, setSelectedKey] = useState<string | undefined>(
        initialSelectedKey,
    )
    const [isOpen, setIsOpen] = useState(false)

    useEffect(() => {
        setSelectedKey(initialSelectedKey)
    }, [initialSelectedKey])

    // Select the appropriate data set
    let items: DropdownItem[]
    switch (dataSet) {
        case 'languages':
            items = languageItems
            break
        case 'countries':
            items = countryItems
            break
        case 'currencies':
            items = currencyItems
            break
        default:
            items = sampleItems
            break
    }

    return (
        <InputField
            invalid={invalid}
            success={success}
            isDisabled={isDisabled}
            name={name}
            autoFocus={autoFocus}
            className={className}
        >
            {label && <Label required={required}>{label}</Label>}
            <InputGroup>
                {hasPrefixIcon && (
                    <InputPrefix>
                        {prefixIcon === 'search' && <SearchIcon />}
                        {prefixIcon === 'calendar' && <CalendarIcon />}
                        {prefixIcon === 'money' && <MoneyIcon />}
                    </InputPrefix>
                )}
                {hasPrefixAddon && <InputPrefix>{prefixAddon}</InputPrefix>}
                <DropdownRoot
                    selectedKey={selectedKey}
                    onSelectionChange={setSelectedKey}
                    onOpenChange={setIsOpen}
                    isDisabled={isDisabled}
                    name={name}
                    autoFocus={autoFocus}
                    searchable={searchable}
                    searchPlaceholder={searchPlaceholder}
                    searchFunction={
                        customSearchFunction
                            ? firstLetterSearchFunction
                            : undefined
                    }
                >
                    <DropdownTrigger
                        placeholder={placeholder}
                        selectedKey={selectedKey}
                        isOpen={isOpen}
                    />
                    <DropdownList items={items}>
                        {items.map((item) => (
                            <SelectItem key={item.id} item={item} />
                        ))}
                    </DropdownList>
                </DropdownRoot>
                {hasPostfixAddon && <InputPostfix>{postfixAddon}</InputPostfix>}
                {hasPostfixIcon && (
                    <InputPostfix>
                        {postfixIcon === 'check' && <CheckIcon />}
                        {postfixIcon === 'close' && <CloseIcon />}
                    </InputPostfix>
                )}
            </InputGroup>
            {supportText && <SupportText>{supportText}</SupportText>}
            {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
        </InputField>
    )
}

const meta: Meta<typeof DropdownTemplate> = {
    title: 'Components/Dropdown',
    component: DropdownTemplate,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        // InputField props
        label: {
            control: 'text',
            description: 'The label text displayed above the dropdown',
        },
        supportText: {
            control: 'text',
            description: 'Help text displayed below the dropdown',
        },
        errorMessage: {
            control: 'text',
            description: 'Error message displayed below the dropdown',
        },
        required: {
            control: 'boolean',
            description: 'Whether the field is required (shows red asterisk)',
        },
        invalid: {
            control: 'boolean',
            description:
                'Whether the dropdown is in an invalid state (shows red border)',
        },
        success: {
            control: 'boolean',
            description:
                'Whether the dropdown is in a success state (shows green border)',
        },
        isDisabled: {
            control: 'boolean',
            description: 'Whether the InputField and dropdown are disabled',
        },
        name: {
            control: 'text',
            description: 'The name attribute for the dropdown field',
        },
        autoFocus: {
            control: 'boolean',
            description: 'Whether the dropdown should be focused on mount',
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes for the InputField',
        },
        // Dropdown props
        placeholder: {
            control: 'text',
            description: 'The placeholder text for the dropdown',
        },
        selectedKey: {
            control: 'text',
            description: 'The selected option key',
        },
        onSelectionChange: {
            table: {
                disable: true,
            },
        },
        // Search functionality props
        searchable: {
            control: 'boolean',
            description: 'Whether to show a search input in the dropdown',
        },
        searchPlaceholder: {
            control: 'text',
            description: 'The placeholder text for the search input',
        },
        customSearchFunction: {
            control: 'boolean',
            description:
                'Whether to use a custom search function (first letter only)',
        },
        // Prefix/Postfix controls
        hasPrefixIcon: {
            control: 'boolean',
            description: 'Whether to show a prefix icon',
        },
        hasPostfixIcon: {
            control: 'boolean',
            description: 'Whether to show a postfix icon',
        },
        hasPrefixAddon: {
            control: 'boolean',
            description: 'Whether to show a prefix addon',
        },
        hasPostfixAddon: {
            control: 'boolean',
            description: 'Whether to show a postfix addon',
        },
        prefixIcon: {
            control: 'select',
            options: ['search', 'calendar', 'money'],
            description: 'The type of prefix icon to display',
        },
        postfixIcon: {
            control: 'select',
            options: ['check', 'close'],
            description: 'The type of postfix icon to display',
        },
        prefixAddon: {
            control: 'text',
            description: 'The text for the prefix addon',
        },
        postfixAddon: {
            control: 'text',
            description: 'The text for the postfix addon',
        },
        // Data options
        dataSet: {
            control: 'select',
            options: ['sample', 'languages', 'countries', 'currencies'],
            description: 'The data set to use for dropdown options',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Select Option',
        supportText: 'Choose an option from the dropdown menu.',
        placeholder: 'Select an option...',
        selectedKey: '2',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic dropdown field with label and support text.',
            },
        },
    },
}

export const RTL: Story = {
    render: DropdownTemplate,
    args: {
        label: 'اختر اللغة',
        supportText: 'اختر لغتك المفضلة من القائمة.',
        placeholder: 'اختر خيار...',
        selectedKey: 'ar',
        hasPrefixIcon: true,
        hasPostfixAddon: true,
        prefixIcon: 'search',
        postfixAddon: 'لغة',
        dataSet: 'languages',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with RTL (right-to-left) text direction support.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}

export const WithPlaceholder: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Country',
        placeholder: 'Choose your country...',
        dataSet: 'countries',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with placeholder text.',
            },
        },
    },
}

export const Language: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Language',
        supportText: 'Select your preferred language for the interface.',
        placeholder: 'Choose language...',
        selectedKey: 'en',
        dataSet: 'languages',
    },
    parameters: {
        docs: {
            description: {
                story: 'Language selection dropdown with predefined options.',
            },
        },
    },
}

export const Currency: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Currency',
        supportText: 'Select the currency for transactions.',
        placeholder: 'Choose currency...',
        selectedKey: 'usd',
        dataSet: 'currencies',
    },
    parameters: {
        docs: {
            description: {
                story: 'Currency selection dropdown with predefined options.',
            },
        },
    },
}

export const Disabled: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Disabled Dropdown',
        supportText: 'This dropdown is disabled.',
        placeholder: 'Disabled dropdown...',
        selectedKey: '1',
        isDisabled: true,
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Disabled dropdown field that cannot be interacted with.',
            },
        },
    },
}

export const WithPrefixIcon: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Search Category',
        supportText: 'Select a category to filter your search.',
        placeholder: 'Choose category...',
        selectedKey: '2',
        hasPrefixIcon: true,
        prefixIcon: 'search',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with a search icon on the left side.',
            },
        },
    },
}

export const WithPostfixIcon: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Verified Options',
        supportText: 'Select from verified options only.',
        placeholder: 'Choose option...',
        selectedKey: '1',
        hasPostfixIcon: true,
        postfixIcon: 'check',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with a check icon on the right side.',
            },
        },
    },
}

export const WithPrefixAddon: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Domain Type',
        supportText: 'Select the type of domain.',
        placeholder: 'example.com',
        selectedKey: '1',
        hasPrefixAddon: true,
        prefixAddon: 'https://',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with a prefix addon showing the protocol.',
            },
        },
    },
}

export const WithPostfixAddon: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Currency Amount',
        supportText: 'Select the currency type.',
        placeholder: 'Choose currency...',
        selectedKey: 'usd',
        hasPostfixAddon: true,
        postfixAddon: 'RATE',
        dataSet: 'currencies',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with a postfix addon on the right side.',
            },
        },
    },
}

export const WithBothIcons: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Financial Category',
        supportText: 'Select a category for your financial transaction.',
        placeholder: 'Choose category...',
        selectedKey: '2',
        hasPrefixIcon: true,
        hasPostfixIcon: true,
        prefixIcon: 'money',
        postfixIcon: 'check',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with icons on both sides.',
            },
        },
    },
}

export const WithError: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Required Selection',
        errorMessage: 'Please select an option from the dropdown.',
        invalid: true,
        placeholder: 'Select an option...',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with an error message and red border styling.',
            },
        },
    },
}

export const WithSuccess: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Country',
        supportText: 'Country selection is valid and confirmed.',
        success: true,
        placeholder: 'Choose country...',
        selectedKey: 'us',
        dataSet: 'countries',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with success state styling (green border and support text).',
            },
        },
    },
}

export const Required: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Language Preference',
        supportText: 'This field is required to set up your account.',
        required: true,
        placeholder: 'Select language...',
        selectedKey: 'en',
        dataSet: 'languages',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with a required indicator (red asterisk) in the label.',
            },
        },
    },
}

export const WithIconAndAddon: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Event Date',
        supportText: 'Select the type of date format.',
        placeholder: 'Choose format...',
        selectedKey: '1',
        hasPrefixIcon: true,
        hasPostfixAddon: true,
        prefixIcon: 'calendar',
        postfixAddon: 'Format',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with an icon on the left and addon on the right.',
            },
        },
    },
}

export const WithAddonAndIcon: Story = {
    render: DropdownTemplate,
    args: {
        label: 'User Type',
        supportText: 'Select the type of user account.',
        placeholder: 'Choose type...',
        selectedKey: '2',
        hasPrefixAddon: true,
        hasPostfixIcon: true,
        prefixAddon: '@',
        postfixIcon: 'check',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with an addon on the left and icon on the right.',
            },
        },
    },
}

export const WithMultipleAddons: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Pricing Tier',
        supportText: 'Select the pricing tier for your subscription.',
        placeholder: 'Choose tier...',
        selectedKey: '3',
        hasPrefixAddon: true,
        hasPostfixAddon: true,
        prefixAddon: '$',
        postfixAddon: '/mo',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with addons on both sides.',
            },
        },
    },
}

export const DisabledWithIcon: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Disabled with Icon',
        supportText: 'This dropdown is disabled but still shows the icon.',
        placeholder: 'Disabled dropdown...',
        selectedKey: '1',
        isDisabled: true,
        hasPrefixIcon: true,
        prefixIcon: 'search',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Disabled dropdown with an icon.',
            },
        },
    },
}

export const InvalidWithIcon: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Search Category',
        errorMessage: 'Invalid selection. Please choose a different option.',
        invalid: true,
        placeholder: 'Invalid selection...',
        selectedKey: '5',
        hasPrefixIcon: true,
        hasPostfixIcon: true,
        prefixIcon: 'search',
        postfixIcon: 'close',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with error message and icons on both sides.',
            },
        },
    },
}

export const InvalidWithAddon: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Domain Selection',
        errorMessage: 'Please select a valid domain option.',
        invalid: true,
        placeholder: 'Invalid selection...',
        hasPrefixAddon: true,
        hasPostfixAddon: true,
        prefixAddon: 'www.',
        postfixAddon: '.com',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with error message and addons on both sides.',
            },
        },
    },
}

export const DisabledWithRequired: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Required but Disabled',
        supportText: 'This field is required but currently disabled.',
        required: true,
        isDisabled: true,
        placeholder: 'Cannot select',
        selectedKey: '1',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown that is both required and disabled. The required indicator is shown, but the field cannot be interacted with.',
            },
        },
    },
}

export const DropdownWithoutGroup: Story = {
    render: (args: DropdownTemplateArgs) => {
        const {
            // InputField props
            label,
            supportText,
            errorMessage,
            required,
            invalid,
            success,
            isDisabled,
            name,
            autoFocus,
            className,
            // Dropdown props
            placeholder,
            selectedKey: initialSelectedKey,
            dataSet = 'sample',
        } = args

        const [selectedKey, setSelectedKey] = useState<string | undefined>(
            initialSelectedKey,
        )
        const [isOpen, setIsOpen] = useState(false)

        useEffect(() => {
            setSelectedKey(initialSelectedKey)
        }, [initialSelectedKey])

        // Select the appropriate data set
        let items: DropdownItem[]
        switch (dataSet) {
            case 'languages':
                items = languageItems
                break
            case 'countries':
                items = countryItems
                break
            case 'currencies':
                items = currencyItems
                break
            default:
                items = sampleItems
                break
        }

        return (
            <InputField
                invalid={invalid}
                success={success}
                isDisabled={isDisabled}
                name={name}
                autoFocus={autoFocus}
                className={className}
            >
                {label && <Label required={required}>{label}</Label>}
                <DropdownRoot
                    selectedKey={selectedKey}
                    onSelectionChange={setSelectedKey}
                    onOpenChange={setIsOpen}
                    isDisabled={isDisabled}
                    name={name}
                    autoFocus={autoFocus}
                >
                    <DropdownTrigger
                        placeholder={placeholder}
                        selectedKey={selectedKey}
                        isOpen={isOpen}
                    />
                    <DropdownList>
                        {items.map((item) => (
                            <SelectItem key={item.id} item={item} />
                        ))}
                    </DropdownList>
                </DropdownRoot>
                {supportText && <SupportText>{supportText}</SupportText>}
                {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
            </InputField>
        )
    },
    args: {
        label: 'Select Option',
        supportText:
            'This Dropdown is not wrapped in InputGroup, notice the dropdown styling.',
        placeholder: 'Select an option...',
        selectedKey: '2',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown component used without InputGroup wrapper shows standalone styling. This demonstrates the visual difference between grouped and standalone dropdowns.',
            },
        },
    },
}

export const SearchableDropdown: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Searchable Country',
        supportText: 'Type to search for countries.',
        placeholder: 'Choose a country...',
        searchable: true,
        searchPlaceholder: 'Search countries...',
        dataSet: 'countries',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with search functionality. Users can type to filter options.',
            },
        },
    },
}

export const SearchableLanguages: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Language Selection',
        supportText: 'Search and select your preferred language.',
        placeholder: 'Choose language...',
        searchable: true,
        searchPlaceholder: 'Type to search...',
        selectedKey: 'en',
        dataSet: 'languages',
    },
    parameters: {
        docs: {
            description: {
                story: 'Language dropdown with search functionality enabled.',
            },
        },
    },
}

export const CustomSearchFunction: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Custom Search (First Letter Only)',
        supportText:
            'This dropdown uses a custom search function that only matches the first letter.',
        placeholder: 'Choose a currency...',
        searchable: true,
        searchPlaceholder: 'Type first letter...',
        customSearchFunction: true,
        dataSet: 'currencies',
    },
    parameters: {
        docs: {
            description: {
                story: 'Dropdown with a custom search function that only matches items starting with the typed letter.',
            },
        },
    },
}

export const SearchableWithIcons: Story = {
    render: DropdownTemplate,
    args: {
        label: 'Searchable with Icons',
        supportText: 'Searchable dropdown with prefix and postfix icons.',
        placeholder: 'Search and select...',
        searchable: true,
        searchPlaceholder: 'Search options...',
        hasPrefixIcon: true,
        hasPostfixIcon: true,
        prefixIcon: 'search',
        postfixIcon: 'check',
        selectedKey: '2',
        dataSet: 'sample',
    },
    parameters: {
        docs: {
            description: {
                story: 'Searchable dropdown combined with prefix and postfix icons.',
            },
        },
    },
}
