import React from 'react'

import { cn } from '../../utils/cn'

interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {}

const Container: React.ForwardRefExoticComponent<
    ContainerProps & React.RefAttributes<HTMLDivElement>
> = React.forwardRef<HTMLDivElement, ContainerProps>(
    ({ className, ...props }, ref) => {
        return (
            <div
                ref={ref}
                className={cn(
                    'tw:max-w-screen-sm tw:sm:max-w-screen-md tw:md:max-w-screen-lg tw:lg:max-w-screen-xl tw:xl:max-w-screen-2xl',
                    className,
                )}
                {...props}
            />
        )
    },
)
Container.displayName = 'Container'

export { Container }
export type { ContainerProps }
