import type { <PERSON>a, StoryObj } from '@storybook/react'
import { Container } from './container'

const meta: Meta<typeof Container> = {
    title: 'Layout/Container',
    component: Container,
    tags: ['autodocs'],
    parameters: {
        layout: 'padded',
        docs: {
            description: {
                component: 'A responsive container component that provides consistent max-widths across different screen sizes.',
            },
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

// Demo content component
const DemoBox = ({ children }: { children: React.ReactNode }) => (
    <div className="tw:rounded tw:border-2 tw:border-blue-300 tw:bg-blue-100 tw:p-4 tw:text-center tw:text-blue-800">
        {children}
    </div>
)

// Default story
export const Default: Story = {
    render: (args) => (
        <Container {...args}>
            <DemoBox>
                <div className="tw:font-semibold">Default Container</div>
                <div className="tw:text-sm">Responsive max-width container</div>
            </DemoBox>
        </Container>
    ),
}

// With custom className
export const WithCustomStyling: Story = {
    render: (args) => (
        <Container {...args}>
            <DemoBox>
                <div className="tw:font-semibold">Custom Styled Container</div>
                <div className="tw:text-sm">Container with custom background and padding</div>
            </DemoBox>
        </Container>
    ),
    args: {
        className: 'tw:bg-gray-100 tw:p-8',
    },
}
