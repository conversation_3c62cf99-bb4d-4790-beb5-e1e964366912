import type { JSX } from 'react'
import { toast as sonnerToast } from 'sonner'
import { AlertRhombus } from '../../icons/alert-rhombus'
import { CheckCircle } from '../../icons/close-circle'
import { CloseIcon2 } from '../../icons/close-icon-2'
import { InfoCircle } from '../../icons/info-circle'
import { LoadingIcon } from '../../icons/loading-icon'
import { cn } from '../../utils/cn'
import { Button } from '../button/button'

export interface ToastContent {
    title: string
    description?: string
    variant?: 'success' | 'error' | 'warning' | 'info' | 'neutral' | 'loading'
    actions?: JSX.Element
    timeout?: number
}

const variantClasses: Record<NonNullable<ToastContent['variant']>, string[]> = {
    success: [
        'tw:bg-success tw:text-success-1',
        'tw:border-t-[3px] tw:border-success-1',
        'tw:[--toast-color:theme(colors.success-1)]',
    ],
    error: [
        'tw:bg-danger tw:text-danger-1',
        'tw:border-t-[3px] tw:border-danger-2',
        'tw:[--toast-color:theme(colors.danger-1)]',
    ],
    warning: [
        'tw:bg-warning tw:text-warning-2',
        'tw:border-t-[3px] tw:border-warning-2',
        'tw:[--toast-color:theme(colors.warning-2)]',
    ],
    info: [
        'tw:bg-info tw:text-info-2',
        'tw:border-t-[3px] tw:border-info-2',
        'tw:[--toast-color:theme(colors.info-2)]',
    ],
    neutral: [
        'tw:bg-light-gray-1 tw:text-primary-black',
        'tw:border-t-[3px] tw:border-primary-black',
        'tw:[--toast-color:theme(colors.primary-black)]',
    ],
    loading: [
        'tw:bg-info tw:text-info-2',
        'tw:border-t-[3px] tw:border-info-2',
        'tw:[--toast-color:theme(colors.info-2)]',
    ],
}

function iconFor(
    kind: NonNullable<ToastContent['variant']>,
): JSX.Element | null {
    switch (kind) {
        case 'success':
            return <CheckCircle size={29} />
        case 'error':
        case 'warning':
            return <AlertRhombus size={29} />
        case 'info':
        case 'neutral':
            return <InfoCircle size={29} />
        case 'loading':
            return <LoadingIcon size={29} />
        default:
            return <InfoCircle size={29} />
    }
}

function show(
    kind: 'success' | 'error' | 'warning' | 'info' | 'neutral' | 'loading',
    title: string,
    description?: string,
    options?: {
        timeout?: number
        actions?: JSX.Element
    },
): { id: string; dismiss: () => void } {
    const duration = options?.timeout ?? 4000

    const sonnerId = sonnerToast.custom(
        (t) => (
            <div className="tw:w-full tw:cursor-default tw:border-0 tw:bg-transparent tw:p-0 tw:text-start">
                <div
                    className={cn(
                        'tw:lg:min-w-[375px] tw:lg:max-w-sm',
                        'tw:relative tw:flex tw:w-full tw:items-start tw:gap-3 tw:overflow-hidden tw:rounded-xs tw:px-[30px] tw:py-[24px] tw:shadow-[0_6px_12px_0_rgba(0,0,0,0.25)] tw:transition-all tw:duration-200',
                        'tw:focus:outline-none tw:focus:ring-2 tw:focus:ring-offset-2',
                        variantClasses[kind],
                        options?.actions ? '' : '',
                    )}
                >
                    <div className="tw:flex tw:min-w-0 tw:flex-1 tw:flex-col tw:gap-1">
                        <div className="tw:flex tw:items-start tw:gap-[12px]">
                            <div className="tw:shrink-0">{iconFor(kind)}</div>
                            <div className="tw:flex tw:flex-col tw:gap-[10px]">
                                {title && (
                                    <div className="tw:mt-[1px] tw:font-bold tw:text-[20px] tw:leading-normal">
                                        {title}
                                    </div>
                                )}
                                {description && (
                                    <div className="tw:font-normal tw:text-[16px] tw:leading-normal">
                                        {description}
                                    </div>
                                )}
                                {options?.actions && (
                                    <div className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline">
                                        {options.actions}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                    <Button
                        variant="text"
                        color="neutral"
                        className="tw:h-[29px] tw:w-[29px] tw:flex-shrink-0 tw:p-0 tw:text-current"
                        onClick={() => sonnerToast.dismiss(t)}
                    >
                        <CloseIcon2 size={29} />
                    </Button>
                </div>
            </div>
        ),
        { duration },
    )

    return {
        id: String(sonnerId),
        dismiss: () => sonnerToast.dismiss(sonnerId),
    }
}

function messageOrFallback(title?: string, description?: string): string {
    if (title) return title
    if (description) return description
    return 'Notification'
}

// Public helper
export const toast = {
    success: (
        title: string,
        description?: string,
        options?: Partial<ToastContent>,
    ): { id: string; dismiss: () => void } =>
        show(
            'success',
            messageOrFallback(title, description),
            title && description ? description : undefined,
            options,
        ),
    error: (
        title: string,
        description?: string,
        options?: Partial<ToastContent>,
    ): { id: string; dismiss: () => void } =>
        show(
            'error',
            messageOrFallback(title, description),
            title && description ? description : undefined,
            options,
        ),
    warning: (
        title: string,
        description?: string,
        options?: Partial<ToastContent>,
    ): { id: string; dismiss: () => void } =>
        show(
            'warning',
            messageOrFallback(title, description),
            title && description ? description : undefined,
            options,
        ),
    info: (
        title: string,
        description?: string,
        options?: Partial<ToastContent>,
    ): { id: string; dismiss: () => void } =>
        show(
            'info',
            messageOrFallback(title, description),
            title && description ? description : undefined,
            options,
        ),
    neutral: (
        title: string,
        description?: string,
        options?: Partial<ToastContent>,
    ): { id: string; dismiss: () => void } =>
        show(
            'neutral',
            messageOrFallback(title, description),
            title && description ? description : undefined,
            options,
        ),
    loading: (
        title: string,
        description?: string,
        options?: Partial<ToastContent>,
    ): { id: string; dismiss: () => void } =>
        show(
            'loading',
            messageOrFallback(title, description),
            title && description ? description : undefined,
            options,
        ),
}
