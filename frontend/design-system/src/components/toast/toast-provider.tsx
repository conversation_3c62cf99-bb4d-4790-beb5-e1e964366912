import type { JSX, ReactNode } from 'react'
import { Toaster } from 'sonner'

export interface ToastProviderProps {
    direction?: 'ltr' | 'rtl'
    children: ReactNode
}

export function ToastProvider({
    direction = 'ltr',
    children,
}: ToastProviderProps): JSX.Element {
    return (
        <>
            <Toaster
                position={direction === 'rtl' ? 'bottom-left' : 'bottom-right'}
                dir={direction}
                closeButton
            />
            {children}
        </>
    )
}
