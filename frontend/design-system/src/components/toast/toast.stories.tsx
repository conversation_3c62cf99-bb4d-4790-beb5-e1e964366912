import type { <PERSON>a, StoryObj } from '@storybook/react'
import { Box } from '../box/box'
import { Button } from '../button/button'
import { toast } from './toast'

// Note: ToastProvider should be used at the top level of the app, not in individual components

const meta: Meta = {
    title: 'Components/Toast',
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {},
}

export default meta
type Story = StoryObj<typeof meta>

// Individual Toast Stories
export const Success: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="primary"
                onClick={() => {
                    toast.success('File Upload Complete')
                }}
            >
                Show Success Toast
            </Button>
        </Box>
    ),
}

export const ErrorToast: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="error"
                onClick={() => {
                    toast.error(
                        '',
                        'Unable to connect to the server. Please check your internet connection.',
                    )
                }}
            >
                Show Error Toast
            </Button>
        </Box>
    ),
}

export const Warning: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="warning"
                onClick={() => {
                    toast.warning(
                        'Storage Space Low',
                        'You are running low on storage space. Consider cleaning up unused files.',
                    )
                }}
            >
                Show Warning Toast
            </Button>
        </Box>
    ),
}

export const Info: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="secondary"
                onClick={() => {
                    toast.info(
                        'New Feature Available',
                        'We have added new collaboration tools to improve your workflow.',
                    )
                }}
            >
                Show Info Toast
            </Button>
        </Box>
    ),
}

export const Neutral: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="neutral"
                onClick={() => {
                    toast.neutral(
                        'Your account is currently inactive',
                        'Please contact support to reactivate your account.',
                    )
                }}
            >
                Show Neutral Toast
            </Button>
        </Box>
    ),
}

export const Loading: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="secondary"
                onClick={() => {
                    const { dismiss } = toast.loading(
                        'Processing Request',
                        'Please wait while we complete your request...',
                    )
                    setTimeout(() => {
                        dismiss()
                        toast.success(
                            'Request Completed',
                            'Your request has been successfully completed.',
                        )
                    }, 4000)
                }}
            >
                Show Loading Toast
            </Button>
        </Box>
    ),
}

// Toast with Actions Stories
export const SuccessWithAction: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="primary"
                onClick={() => {
                    toast.success(
                        'File Upload Complete',
                        'Your document has been successfully uploaded to the server.',
                        {
                            actions: (
                                <Button
                                    variant="text"
                                    color="neutral"
                                    size="sm"
                                    className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                    onClick={() =>
                                        alert('Opening file viewer...')
                                    }
                                >
                                    View File
                                </Button>
                            ),
                            timeout: 8000,
                        },
                    )
                }}
            >
                Show Success Toast with Action
            </Button>
        </Box>
    ),
}

export const ErrorWithAction: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="error"
                onClick={() => {
                    toast.error(
                        'Connection Failed',
                        'Unable to connect to the server. Please check your internet connection.',
                        {
                            actions: (
                                <Button
                                    variant="text"
                                    color="neutral"
                                    size="sm"
                                    className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                    onClick={() =>
                                        alert('Retrying connection...')
                                    }
                                >
                                    Retry Connection
                                </Button>
                            ),
                            timeout: 10000,
                        },
                    )
                }}
            >
                Show Error Toast with Action
            </Button>
        </Box>
    ),
}

export const WarningWithAction: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="warning"
                onClick={() => {
                    toast.warning(
                        'Storage Space Low',
                        'You are running low on storage space. Consider cleaning up unused files.',
                        {
                            actions: (
                                <Button
                                    variant="filled"
                                    color="neutral"
                                    onClick={() =>
                                        alert('Opening storage manager...')
                                    }
                                >
                                    Manage Storage
                                </Button>
                            ),
                            timeout: 12000,
                        },
                    )
                }}
            >
                Show Warning Toast with Action
            </Button>
        </Box>
    ),
}

export const InfoWithAction: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="secondary"
                onClick={() => {
                    toast.info(
                        'New Feature Available',
                        'We have added new collaboration tools to improve your workflow.',
                        {
                            actions: (
                                <Button
                                    variant="text"
                                    color="neutral"
                                    size="sm"
                                    className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                    onClick={() =>
                                        alert('Opening feature guide...')
                                    }
                                >
                                    Learn More
                                </Button>
                            ),
                            timeout: 6000,
                        },
                    )
                }}
            >
                Show Info Toast with Action
            </Button>
        </Box>
    ),
}

export const NeutralWithAction: Story = {
    render: () => (
        <Box className="tw:p-6">
            <Button
                variant="filled"
                color="neutral"
                onClick={() => {
                    toast.neutral(
                        'Your account is currently inactive',
                        'Please contact support to reactivate your account.',
                        {
                            actions: (
                                <Button
                                    variant="text"
                                    color="neutral"
                                    size="sm"
                                    className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                    onClick={() =>
                                        alert('Opening feature guide...')
                                    }
                                >
                                    Learn More
                                </Button>
                            ),
                            timeout: 6000,
                        },
                    )
                }}
            >
                Show Neutral Toast with Action
            </Button>
        </Box>
    ),
}

// RTL Toast Stories
export const SuccessRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="primary"
                onClick={() => {
                    toast.success('تم رفع الملف بنجاح')
                }}
            >
                عرض رسالة النجاح
            </Button>
        </Box>
    ),
}

export const ErrorToastRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="error"
                onClick={() => {
                    toast.error(
                        '',
                        'تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت الخاص بك.',
                    )
                }}
            >
                عرض رسالة الخطأ
            </Button>
        </Box>
    ),
}

export const WarningRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="warning"
                onClick={() => {
                    toast.warning(
                        'مساحة التخزين منخفضة',
                        'مساحة التخزين الخاصة بك منخفضة. فكر في تنظيف الملفات غير المستخدمة.',
                    )
                }}
            >
                عرض رسالة التحذير
            </Button>
        </Box>
    ),
}

export const InfoRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="secondary"
                onClick={() => {
                    toast.info(
                        'ميزة جديدة متاحة',
                        'لقد أضفنا أدوات تعاون جديدة لتحسين سير العمل الخاص بك.',
                    )
                }}
            >
                عرض رسالة المعلومات
            </Button>
        </Box>
    ),
}

export const NeutralRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="neutral"
                onClick={() => {
                    toast.neutral(
                        'حسابك غير نشط حاليًا',
                        'يرجى الاتصال بالدعم لإعادة تفعيل حسابك.',
                    )
                }}
            >
                عرض رسالة محايدة
            </Button>
        </Box>
    ),
}

export const LoadingRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="secondary"
                onClick={() => {
                    const { dismiss } = toast.loading(
                        'معالجة الطلب',
                        'يرجى الانتظار بينما نكمل طلبك...',
                    )
                    setTimeout(() => {
                        dismiss()
                        toast.success('تم إكمال الطلب', 'تم إكمال طلبك بنجاح.')
                    }, 4000)
                }}
            >
                عرض رسالة التحميل
            </Button>
        </Box>
    ),
}

// RTL Toast with Actions Stories
export const SuccessWithActionRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="primary"
                onClick={() => {
                    toast.success(
                        'تم رفع الملف بنجاح',
                        'تم رفع المستند الخاص بك بنجاح إلى الخادم.',
                        {
                            actions: (
                                <Button
                                    variant="text"
                                    color="neutral"
                                    size="sm"
                                    className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                    onClick={() => alert('فتح عارض الملفات...')}
                                >
                                    عرض الملف
                                </Button>
                            ),
                            timeout: 8000,
                        },
                    )
                }}
            >
                عرض رسالة النجاح مع الإجراء
            </Button>
        </Box>
    ),
}

export const ErrorWithActionRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="error"
                onClick={() => {
                    toast.error(
                        'فشل الاتصال',
                        'تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت الخاص بك.',
                        {
                            actions: (
                                <Button
                                    variant="text"
                                    color="neutral"
                                    size="sm"
                                    className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                    onClick={() =>
                                        alert('إعادة محاولة الاتصال...')
                                    }
                                >
                                    إعادة المحاولة
                                </Button>
                            ),
                            timeout: 10000,
                        },
                    )
                }}
            >
                عرض رسالة الخطأ مع الإجراء
            </Button>
        </Box>
    ),
}

export const WarningWithActionRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="warning"
                onClick={() => {
                    toast.warning(
                        'مساحة التخزين منخفضة',
                        'مساحة التخزين الخاصة بك منخفضة. فكر في تنظيف الملفات غير المستخدمة.',
                        {
                            actions: (
                                <Button
                                    variant="filled"
                                    color="neutral"
                                    onClick={() => alert('فتح مدير التخزين...')}
                                >
                                    إدارة التخزين
                                </Button>
                            ),
                            timeout: 12000,
                        },
                    )
                }}
            >
                عرض رسالة التحذير مع الإجراء
            </Button>
        </Box>
    ),
}

export const InfoWithActionRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="secondary"
                onClick={() => {
                    toast.info(
                        'ميزة جديدة متاحة',
                        'لقد أضفنا أدوات تعاون جديدة لتحسين سير العمل الخاص بك.',
                        {
                            actions: (
                                <Button
                                    variant="text"
                                    color="neutral"
                                    size="sm"
                                    className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                    onClick={() => alert('فتح دليل الميزات...')}
                                >
                                    تعلم المزيد
                                </Button>
                            ),
                            timeout: 6000,
                        },
                    )
                }}
            >
                عرض رسالة المعلومات مع الإجراء
            </Button>
        </Box>
    ),
}

export const NeutralWithActionRTL: Story = {
    globals: {
        addonRtl: 'rtl',
    },
    render: () => (
        <Box className="tw:p-6" dir="rtl">
            <Button
                variant="filled"
                color="neutral"
                onClick={() => {
                    toast.neutral(
                        'حسابك غير نشط حاليًا',
                        'يرجى الاتصال بالدعم لإعادة تفعيل حسابك.',
                        {
                            actions: (
                                <Button
                                    variant="text"
                                    color="neutral"
                                    size="sm"
                                    className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                    onClick={() => alert('فتح دليل الميزات...')}
                                >
                                    تعلم المزيد
                                </Button>
                            ),
                            timeout: 6000,
                        },
                    )
                }}
            >
                عرض رسالة محايدة مع الإجراء
            </Button>
        </Box>
    ),
}

// Full Demo Story (kept for reference)
function FullToastDemo() {
    return (
        <div className="tw:mx-auto tw:w-full tw:max-w-6xl tw:p-4">
            {/* Toasts */}
            <Box className="tw:mb-8 tw:rounded-xs tw:border tw:border-light-gray-2 tw:p-6">
                <h3 className="tw:mb-4 tw:font-medium tw:text-lg">Toasts</h3>
                <div className="tw:grid tw:grid-cols-1 tw:gap-3 tw:sm:grid-cols-2">
                    <Button
                        variant="filled"
                        color="primary"
                        onClick={() => {
                            toast.success('File Upload Complete')
                        }}
                        className="tw:w-full"
                    >
                        Success
                    </Button>
                    <Button
                        variant="filled"
                        color="error"
                        onClick={() => {
                            toast.error(
                                '',
                                'Unable to connect to the server. Please check your internet connection.',
                            )
                        }}
                        className="tw:w-full"
                    >
                        Error
                    </Button>
                </div>

                <div className="tw:mt-3 tw:grid tw:grid-cols-1 tw:gap-3 tw:sm:grid-cols-2">
                    <Button
                        variant="filled"
                        color="warning"
                        onClick={() => {
                            toast.warning(
                                'Storage Space Low',
                                'You are running low on storage space. Consider cleaning up unused files.',
                            )
                        }}
                        className="tw:w-full"
                    >
                        Warning
                    </Button>
                    <Button
                        variant="filled"
                        color="secondary"
                        onClick={() => {
                            toast.info(
                                'New Feature Available',
                                'We have added new collaboration tools to improve your workflow.',
                            )
                        }}
                        className="tw:w-full"
                    >
                        Info
                    </Button>
                    <Button
                        variant="filled"
                        color="neutral"
                        onClick={() => {
                            toast.neutral(
                                'Your account is currently inactive',
                                'Please contact support to reactivate your account.',
                            )
                        }}
                        className="tw:w-full"
                    >
                        Neutral
                    </Button>
                    <Button
                        variant="filled"
                        color="secondary"
                        onClick={() => {
                            const { dismiss } = toast.loading(
                                'Processing Request',
                                'Please wait while we complete your request...',
                            )
                            setTimeout(() => {
                                dismiss()
                                toast.success(
                                    'Request Completed',
                                    'Your request has been successfully completed.',
                                )
                            }, 4000)
                        }}
                        className="tw:w-full"
                    >
                        Loading
                    </Button>
                </div>
            </Box>

            {/* Toasts with Actions */}
            <Box className="tw:mb-8 tw:rounded-xs tw:border tw:border-light-gray-2 tw:p-6">
                <h3 className="tw:mb-4 tw:font-medium tw:text-lg">
                    Toasts with Actions
                </h3>
                <div className="tw:grid tw:grid-cols-1 tw:gap-3 tw:sm:grid-cols-2">
                    <Button
                        variant="filled"
                        color="primary"
                        onClick={() => {
                            toast.success(
                                'File Upload Complete',
                                'Your document has been successfully uploaded to the server.',
                                {
                                    actions: (
                                        <Button
                                            variant="text"
                                            color="neutral"
                                            size="sm"
                                            className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                            onClick={() =>
                                                alert('Opening file viewer...')
                                            }
                                        >
                                            View File
                                        </Button>
                                    ),
                                    timeout: 8000, // Custom timeout for action toasts
                                },
                            )
                        }}
                        className="tw:w-full"
                    >
                        Success with Action (8s)
                    </Button>
                    <Button
                        variant="filled"
                        color="error"
                        onClick={() => {
                            toast.error(
                                'Connection Failed',
                                'Unable to connect to the server. Please check your internet connection.',
                                {
                                    actions: (
                                        <Button
                                            variant="text"
                                            color="neutral"
                                            size="sm"
                                            className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                            onClick={() =>
                                                alert('Retrying connection...')
                                            }
                                        >
                                            Retry Connection
                                        </Button>
                                    ),
                                    timeout: 10000, // Longer timeout for error with action
                                },
                            )
                        }}
                        className="tw:w-full"
                    >
                        Error with Action (10s)
                    </Button>
                </div>

                <div className="tw:mt-3 tw:grid tw:grid-cols-1 tw:gap-3 tw:sm:grid-cols-2">
                    <Button
                        variant="filled"
                        color="warning"
                        onClick={() => {
                            toast.warning(
                                'Storage Space Low',
                                'You are running low on storage space. Consider cleaning up unused files.',
                                {
                                    actions: (
                                        <Button
                                            variant="filled"
                                            color="neutral"
                                            onClick={() =>
                                                alert(
                                                    'Opening storage manager...',
                                                )
                                            }
                                        >
                                            Manage Storage
                                        </Button>
                                    ),
                                    timeout: 12000, // Extended timeout for important warnings
                                },
                            )
                        }}
                        className="tw:w-full"
                    >
                        Warning with Action (12s)
                    </Button>
                    <Button
                        variant="filled"
                        color="secondary"
                        onClick={() => {
                            toast.info(
                                'New Feature Available',
                                'We have added new collaboration tools to improve your workflow.',
                                {
                                    actions: (
                                        <Button
                                            variant="text"
                                            color="neutral"
                                            size="sm"
                                            className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                            onClick={() =>
                                                alert(
                                                    'Opening feature guide...',
                                                )
                                            }
                                        >
                                            Learn More
                                        </Button>
                                    ),
                                    timeout: 6000, // Standard timeout for info with action
                                },
                            )
                        }}
                        className="tw:w-full"
                    >
                        Info with Action (6s)
                    </Button>
                    <Button
                        variant="filled"
                        color="neutral"
                        onClick={() => {
                            toast.neutral(
                                'Your account is currently inactive',
                                'Please contact support to reactivate your account.',
                                {
                                    actions: (
                                        <Button
                                            variant="text"
                                            color="neutral"
                                            size="sm"
                                            className="tw:h-auto tw:min-h-0 tw:self-start tw:p-0 tw:text-xs tw:underline"
                                            onClick={() =>
                                                alert(
                                                    'Opening feature guide...',
                                                )
                                            }
                                        >
                                            Learn More
                                        </Button>
                                    ),
                                    timeout: 6000, // Standard timeout for info with action
                                },
                            )
                        }}
                        className="tw:w-full"
                    >
                        Neutral with Action (6s)
                    </Button>
                </div>
            </Box>
        </div>
    )
}

export const Full: Story = {
    render: (_args) => {
        return <FullToastDemo />
    },
}
