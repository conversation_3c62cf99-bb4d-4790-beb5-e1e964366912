import type React from 'react'
import {
    CsvFileIcon,
    ExcelFileIcon,
    GenericFileIcon,
    ImageFileIcon,
    PdfFileIcon,
    WordFileIcon,
    ZipFileIcon,
} from './uploader-icons'

const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${`${parseFloat((bytes / k ** i).toFixed(2))}${sizes[i]}`}`
}

const isPdf = (name: string): boolean => {
    return name.endsWith('.pdf')
}

const isExcel = (name: string): boolean => {
    return name.endsWith('.xls') || name.endsWith('.xlsx')
}

const isCsv = (name: string): boolean => {
    return name.endsWith('.csv')
}

const isImage = (name: string): boolean => {
    return (
        name.endsWith('.jpg') ||
        name.endsWith('.jpeg') ||
        name.endsWith('.png') ||
        name.endsWith('.gif') ||
        name.endsWith('.bmp') ||
        name.endsWith('.webp')
    )
}

const isWord = (name: string): boolean => {
    return name.endsWith('.doc') || name.endsWith('.docx')
}

const isZip = (name: string): boolean => {
    return name.endsWith('.zip')
}

const fileTypeToIconMap = new Map<string, React.ComponentType>([
    ['image', ImageFileIcon],
    ['pdf', PdfFileIcon],
    ['excel', ExcelFileIcon],
    ['csv', CsvFileIcon],
    ['zip', ZipFileIcon],
    ['word', WordFileIcon],
])

const getFileType = (fileName: string): string => {
    if (isImage(fileName)) return 'image'
    if (isPdf(fileName)) return 'pdf'
    if (isExcel(fileName)) return 'excel'
    if (isCsv(fileName)) return 'csv'
    if (isZip(fileName)) return 'zip'
    if (isWord(fileName)) return 'word'
    return 'generic'
}

const getFileIcon = (fileName: string): React.ComponentType => {
    const fileType = getFileType(fileName)
    return fileTypeToIconMap.get(fileType) || GenericFileIcon
}

export {
    formatFileSize,
    isPdf,
    isExcel,
    isCsv,
    isImage,
    isWord,
    isZip,
    getFileIcon,
}
