import type { JSX } from 'react'

// Icons
export const FileIcon = (): JSX.Element => (
    <svg
        width="34"
        height="44"
        viewBox="0 0 34 44"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>File</title>
        <path
            d="M33.1516 10.4786C33.1242 9.938 32.9124 9.4231 32.5516 9.0196L24.1316 0.602596C23.7281 0.241855 23.2132 0.0301203 22.6726 0.00259781H22.1516V11.0026H33.1516V10.4786ZM21.4656 13.7486C20.919 13.747 20.3951 13.5292 20.0086 13.1426C19.6221 12.7561 19.4042 12.2323 19.4026 11.6856V-0.00140381H2.21461C1.66747 -0.00140381 1.14274 0.215949 0.75585 0.602837C0.368962 0.989724 0.151611 1.51445 0.151611 2.0616L0.151611 41.9386C0.151611 42.4857 0.368962 43.0105 0.75585 43.3974C1.14274 43.7842 1.66747 44.0016 2.21461 44.0016H31.0906C31.6374 44.0011 32.1616 43.7835 32.5481 43.3967C32.9345 43.0098 33.1516 42.4854 33.1516 41.9386V13.7486H21.4656ZM9.82361 15.1246C10.3655 15.1242 10.9021 15.2306 11.4029 15.4376C11.9036 15.6447 12.3587 15.9484 12.742 16.3314C13.1254 16.7143 13.4295 17.1691 13.637 17.6696C13.8446 18.1702 13.9515 18.7067 13.9516 19.2486C13.9516 20.3426 13.517 21.3918 12.7434 22.1654C11.9698 22.939 10.9206 23.3736 9.82661 23.3736C9.28466 23.374 8.74794 23.2676 8.24712 23.0605C7.74631 22.8533 7.29122 22.5496 6.90786 22.1665C6.5245 21.7834 6.22038 21.3285 6.0129 20.8279C5.80541 20.3272 5.69861 19.7906 5.69861 19.2486C5.69888 18.1548 6.13359 17.1058 6.90715 16.3324C7.68071 15.5591 8.72977 15.1246 9.82361 15.1246ZM27.6996 35.7486H5.69961L5.74161 31.5816L9.14161 28.1816C9.23613 28.0889 9.3484 28.0162 9.47168 27.968C9.59496 27.9197 9.72672 27.8969 9.85906 27.9008C9.99139 27.9047 10.1216 27.9353 10.2418 27.9908C10.362 28.0463 10.4698 28.1255 10.5586 28.2236L13.9586 31.6236L22.8586 22.7236C22.9544 22.6277 23.0681 22.5517 23.1933 22.4998C23.3184 22.4479 23.4526 22.4212 23.5881 22.4212C23.7236 22.4212 23.8578 22.4479 23.983 22.4998C24.1081 22.5517 24.2219 22.6277 24.3176 22.7236L27.7176 26.1236L27.6996 35.7486Z"
            fill="#E4EBF2"
        />
    </svg>
)

export const FileUploadIcon = ({
    className,
}: {
    className?: string
}): JSX.Element => (
    <svg
        width="26"
        height="18"
        viewBox="0 0 26 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
    >
        <title>File Upload</title>
        <path
            d="M12.8588 5.57013C12.815 5.52626 12.763 5.49145 12.7058 5.4677C12.6486 5.44395 12.5872 5.43172 12.5253 5.43172C12.4633 5.43172 12.402 5.44395 12.3447 5.4677C12.2875 5.49145 12.2355 5.52626 12.1918 5.57013L8.33777 9.42413C8.2939 9.46788 8.25909 9.51986 8.23534 9.57709C8.21159 9.63431 8.19936 9.69567 8.19936 9.75763C8.19936 9.81959 8.21159 9.88094 8.23534 9.93817C8.25909 9.99539 8.2939 10.0474 8.33777 10.0911L8.56077 10.3141C8.60452 10.358 8.6565 10.3928 8.71373 10.4166C8.77096 10.4403 8.83231 10.4525 8.89427 10.4525C8.95623 10.4525 9.01758 10.4403 9.07481 10.4166C9.13204 10.3928 9.18402 10.358 9.22777 10.3141L11.9058 7.64013V13.5491C11.906 13.674 11.9557 13.7936 12.044 13.8819C12.1323 13.9702 12.2519 14.0199 12.3768 14.0201H12.6908C12.8156 14.0199 12.9353 13.9702 13.0235 13.8819C13.1118 13.7936 13.1615 13.674 13.1618 13.5491V7.64013L15.8308 10.3181C15.8745 10.362 15.9265 10.3968 15.9837 10.4206C16.041 10.4443 16.1023 10.4565 16.1643 10.4565C16.2262 10.4565 16.2876 10.4443 16.3448 10.4206C16.402 10.3968 16.454 10.362 16.4978 10.3181L16.7208 10.0951C16.7646 10.0514 16.7994 9.9994 16.8232 9.94217C16.8469 9.88494 16.8592 9.82359 16.8592 9.76163C16.8592 9.69967 16.8469 9.63832 16.8232 9.58109C16.7994 9.52386 16.7646 9.47188 16.7208 9.42813L12.8588 5.57413V5.57013ZM23.0218 8.32613C23.2084 7.67242 23.2407 6.98433 23.1163 6.31599C22.9919 5.64765 22.7141 5.0173 22.3048 4.47451C21.8955 3.93173 21.3658 3.49132 20.7574 3.18794C20.1491 2.88456 19.4786 2.72648 18.7988 2.72613C18.1477 2.7243 17.5045 2.86887 16.9168 3.14913C16.0886 1.97043 14.9122 1.08052 13.5526 0.604182C12.1931 0.127846 10.7186 0.0889782 9.33581 0.49303C7.95306 0.897081 6.73145 1.72379 5.84229 2.85721C4.95313 3.99064 4.44102 5.37394 4.37777 6.81313C3.12019 7.25485 2.05948 8.12733 1.38345 9.27608C0.707424 10.4248 0.459693 11.7757 0.684121 13.0896C0.908549 14.4035 1.59066 15.5956 2.60967 16.4548C3.62869 17.314 4.91887 17.7849 6.25177 17.7841H20.6808C21.802 17.7844 22.891 17.409 23.7741 16.7181C24.6571 16.0271 25.2833 15.0603 25.5527 13.9719C25.8222 12.8835 25.7193 11.7362 25.2606 10.7131C24.8018 9.68999 24.0136 8.84899 23.0218 8.32613ZM20.6808 16.5261H6.25177C5.13854 16.5253 4.06716 16.1017 3.2543 15.3411C2.44144 14.5805 1.94775 13.5395 1.87306 12.4288C1.79837 11.3181 2.14825 10.2204 2.85195 9.35783C3.55566 8.49522 4.56067 7.93203 5.66377 7.78213C5.63697 7.56081 5.62395 7.33805 5.62477 7.11513C5.62384 5.80471 6.0786 4.53475 6.91114 3.52279C7.74369 2.51083 8.9022 1.81985 10.1882 1.56822C11.4743 1.3166 12.8078 1.51998 13.9603 2.14353C15.1129 2.76708 16.0127 3.772 16.5058 4.98613C17.0553 4.39178 17.8133 4.03293 18.6213 3.98464C19.4293 3.93635 20.2247 4.20237 20.8411 4.72705C21.4575 5.25173 21.8471 5.99441 21.9285 6.79977C22.0099 7.60512 21.7767 8.41075 21.2778 9.04813C22.2127 9.19727 23.057 9.69322 23.6425 10.4371C24.2281 11.1811 24.5118 12.1183 24.4371 13.0621C24.3624 14.0058 23.9348 14.8868 23.2396 15.5293C22.5443 16.1719 21.6325 16.5289 20.6858 16.5291L20.6808 16.5261Z"
            fill="#1877F2"
        />
    </svg>
)

export const DownloadIcon = (): JSX.Element => (
    <svg
        width="21"
        height="20"
        viewBox="0 0 21 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Download</title>
        <path
            d="M4.3313 16.6798H15.9719V15.0001H4.3313V16.6798ZM15.9719 7.50012H12.6516V2.50012H7.65161V7.50012H4.3313L10.1516 13.3204L15.9719 7.50012Z"
            fill="#1877F2"
        />
    </svg>
)

export const EditIcon = (): JSX.Element => (
    <svg
        width="21"
        height="20"
        viewBox="0 0 21 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Edit</title>
        <path
            d="M17.4099 5.8668C17.7349 5.5418 17.7349 5.00013 17.4099 4.6918L15.4599 2.7418C15.1516 2.4168 14.6099 2.4168 14.2849 2.7418L12.7516 4.2668L15.8766 7.3918M2.65161 14.3751V17.5001H5.77661L14.9933 8.27513L11.8683 5.15013L2.65161 14.3751Z"
            fill="#75799D"
        />
    </svg>
)

export const DeleteIcon = (): JSX.Element => (
    <svg
        width="21"
        height="20"
        viewBox="0 0 21 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Delete</title>
        <path
            d="M7.65165 2.50012V3.33346H3.48499V5.00012H4.31832V15.8335C4.31832 16.2755 4.49391 16.6994 4.80647 17.012C5.11903 17.3245 5.54296 17.5001 5.98499 17.5001H14.3183C14.7603 17.5001 15.1843 17.3245 15.4968 17.012C15.8094 16.6994 15.985 16.2755 15.985 15.8335V5.00012H16.8183V3.33346H12.6517V2.50012H7.65165ZM7.65165 6.66679H9.31832V14.1668H7.65165V6.66679ZM10.985 6.66679H12.6517V14.1668H10.985V6.66679Z"
            fill="#EC2121"
        />
    </svg>
)


export const GenericFileIcon = () : JSX.Element => (
    <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Generic File</title>
        <g clip-path="url(#clip0_1226_12761)">
            <path
                d="M25.9333 17.7164V6.34189L37.3078 17.7164M11.4568 3.23989C10.9132 3.23811 10.3747 3.34386 9.87212 3.55105C9.36957 3.75825 8.91297 4.0628 8.52859 4.44717C8.14422 4.83155 7.83967 5.28815 7.63248 5.7907C7.42528 6.29324 7.31953 6.83181 7.32131 7.37539V40.4639C7.32131 41.5607 7.75701 42.6126 8.53257 43.3881C9.30813 44.1637 10.36 44.5994 11.4568 44.5994H36.2713C37.3681 44.5994 38.42 44.1637 39.1956 43.3881C39.9711 42.6126 40.4068 41.5607 40.4068 40.4639V15.6479L28.0018 3.23989H11.4568Z"
                fill="currentColor"
            />
        </g>
        <defs>
            <clipPath id="clip0_1226_12761">
                <rect width="48" height="48" fill="white" />
            </clipPath>
        </defs>
    </svg>
)

export const ImageFileIcon = () : JSX.Element => (
    <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Image File</title>
        <path
            d="M26 18H37L26 7V18ZM12 4H28L40 16V40C40 41.0609 39.5786 42.0783 38.8284 42.8284C38.0783 43.5786 37.0609 44 36 44H12C9.78 44 8 42.2 8 40V8C8 5.78 9.78 4 12 4ZM12 40H30H36V24L28 32L24 28L12 40ZM16 18C14.9391 18 13.9217 18.4214 13.1716 19.1716C12.4214 19.9217 12 20.9391 12 22C12 23.0609 12.4214 24.0783 13.1716 24.8284C13.9217 25.5786 14.9391 26 16 26C17.0609 26 18.0783 25.5786 18.8284 24.8284C19.5786 24.0783 20 23.0609 20 22C20 20.9391 19.5786 19.9217 18.8284 19.1716C18.0783 18.4214 17.0609 18 16 18Z"
            fill="currentColor"
        />
    </svg>
)

export const CsvFileIcon = () : JSX.Element => (
    <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Csv File</title>
        <path
            d="M28 4H12C9.8 4 8 5.8 8 8V40C8 42.2 9.8 44 12 44H36C38.2 44 40 42.2 40 40V16L28 4ZM30 32L26 40H20L24 32H18V22H30V32ZM26 18V7L37 18H26Z"
            fill="currentColor"
        />
    </svg>
)

export const ExcelFileIcon = () : JSX.Element => (
    <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Xls File</title>
        <path
            d="M28 4H12C10.9391 4 9.92172 4.42143 9.17157 5.17157C8.42143 5.92172 8 6.93913 8 8V40C8 41.0609 8.42143 42.0783 9.17157 42.8284C9.92172 43.5786 10.9391 44 12 44H36C37.0609 44 38.0783 43.5786 38.8284 42.8284C39.5786 42.0783 40 41.0609 40 40V16L28 4ZM31.6 40H28L24 33.2L20 40H16.4L22.2 31L16.4 22H20L24 28.8L28 22H31.6L25.8 31L31.6 40ZM26 18V7L37 18H26Z"
            fill="currentColor"
        />
    </svg>
)

export const ZipFileIcon = () : JSX.Element => (
    <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Zip File</title>
        <path
            d="M40 12H24L20 8H8C5.8 8 4 9.8 4 12V36C4 38.2 5.8 40 8 40H40C42.2 40 44 38.2 44 36V16C44 13.8 42.2 12 40 12ZM36 24H32V28H36V32H32V36H28V32H32V28H28V24H32V20H28V16H32V20H36V24Z"
            fill="currentColor"
        />
    </svg>
)

export const PdfFileIcon = () : JSX.Element => (
    <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Pdf File</title>
        <path
            d="M38 6H10C7.8 6 6 7.8 6 10V38C6 40.2 7.8 42 10 42H38C40.2 42 42 40.2 42 38V10C42 7.8 40.2 6 38 6ZM19 23C19 24.6 17.6 26 16 26H14V30H11V18H16C17.6 18 19 19.4 19 21V23ZM29 27C29 28.6 27.6 30 26 30H21V18H26C27.6 18 29 19.4 29 21V27ZM37 21H34V23H37V26H34V30H31V18H37V21ZM24 21H26V27H24V21ZM14 21H16V23H14V21Z"
            fill="currentColor"
        />
    </svg>
)

export const WordFileIcon = () : JSX.Element => (
    <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Word File</title>
        <path
            d="M28 4H12C10.9391 4 9.92172 4.42143 9.17157 5.17157C8.42143 5.92172 8 6.93913 8 8V40C8 41.0609 8.42143 42.0783 9.17157 42.8284C9.92172 43.5786 10.9391 44 12 44H36C37.0609 44 38.0783 43.5786 38.8284 42.8284C39.5786 42.0783 40 41.0609 40 40V16L28 4ZM30.4 40H27.6L24 26.4L20.4 40H17.6L13.2 22H16.2L19 35.6L22.6 22H25.2L28.8 35.6L31.6 22H34.6L30.4 40ZM26 18V7L37 18H26Z"
            fill="currentColor"
        />
    </svg>
)

export const AbortIcon = () : JSX.Element => (
    <svg
        width="21"
        height="20"
        viewBox="0 0 21 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <title>Abort</title>
        <path
            d="M16.8176 5.75813L14.3926 3.33313L10.1516 7.57513L5.90962 3.33313L3.48462 5.75813L7.72662 10.0001L3.48462 14.2411L5.90962 16.6661L10.1516 12.4241L14.3936 16.6661L16.8186 14.2411L12.5766 9.99913L16.8176 5.75813Z"
            fill="#EB2121"
        />
    </svg>
)

