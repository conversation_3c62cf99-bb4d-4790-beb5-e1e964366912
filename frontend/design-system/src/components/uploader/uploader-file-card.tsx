import { type JSX, useMemo } from 'react'
import {
    <PERSON><PERSON>,
    FileTrigger,
    Focusable,
    Link,
    OverlayArrow,
    type PressEvent,
    Tooltip as ReactAriaTooltip,
    TooltipTrigger,
} from 'react-aria-components'
import { cn } from '../../utils/cn'
import { AbortIcon, DeleteIcon, DownloadIcon, EditIcon } from './uploader-icons'
import type { UploaderValue } from './uploader-types'
import { formatFileSize, getFileIcon, isImage } from './uploader-utils'

const ArrowIcon = () => (
    <svg width={8} height={8} viewBox="0 0 8 8">
        <title>Arrow</title>
        <path d="M0 0 L4 4 L8 0" fill="currentColor" />
    </svg>
)

const TooltipArrow = ({ placement }: { placement: string | null }) => (
    <OverlayArrow
        className={cn(
            'tw:absolute tw:text-black',
            placement === 'bottom' && 'tw:rotate-180',
            placement === 'right' && 'tw:rotate-90',
            placement === 'left' && 'tw:-rotate-90',
        )}
    >
        <ArrowIcon />
    </OverlayArrow>
)

const Tooltip = ({ children }: { children: React.ReactNode }) => (
    <ReactAriaTooltip
        offset={12}
        className="tw:relative tw:max-w-[200px] tw:rounded tw:bg-black tw:p-3 tw:text-white tw:text-xs tw:shadow-lg"
    >
        {({ placement }) => (
            <>
                <TooltipArrow placement={placement} />
                {children}
            </>
        )}
    </ReactAriaTooltip>
)

const ProgressBar = ({ progress }: { progress: number }) => (
    <div className={cn('tw:relative tw:h-[12px] tw:w-full tw:bg-[#E4EBF2]')}>
        <div
            className={cn(
                'tw:absolute tw:top-0 tw:flex tw:h-[12px] tw:w-[12px] tw:min-w-[20px] tw:items-center tw:justify-end tw:bg-[#1877F2] tw:text-[12px] tw:text-white tw:leading-[normal] tw:tracking-[0]',
            )}
            style={{ width: `${progress}%` }}
        >
            {progress}%
        </div>
    </div>
)

const AbortAction = ({ onAbort }: { onAbort?: (e: PressEvent) => void }) => (
    <TooltipTrigger delay={0}>
        <Button
            className="tw:flex tw:size-[36px] tw:shrink-0 tw:cursor-pointer tw:items-center tw:justify-center tw:rounded-full! tw:hover:bg-[rgba(0,0,0,0.05)]!"
            onPress={onAbort}
        >
            <AbortIcon />
        </Button>
        <Tooltip>Abort</Tooltip>
    </TooltipTrigger>
)

const DownloadAction = ({ fileUrl }: { fileUrl: string }) => (
    <TooltipTrigger delay={0}>
        <Link
            className="tw:flex tw:size-[36px] tw:shrink-0 tw:cursor-pointer tw:items-center tw:justify-center tw:rounded-full! tw:hover:bg-[rgba(0,0,0,0.05)]!"
            href={fileUrl}
            target="_blank"
        >
            <DownloadIcon />
        </Link>
        <Tooltip>Download</Tooltip>
    </TooltipTrigger>
)

const EditAction = ({
    acceptedFileTypes,
    onEdit,
}: {
    acceptedFileTypes: string[] | undefined
    onEdit: (file: File) => void
}) => (
    <TooltipTrigger delay={0}>
        <FileTrigger
            acceptedFileTypes={acceptedFileTypes}
            onSelect={(files) => {
                if (files && files.length > 0) {
                    onEdit(files[0])
                }
            }}
        >
            <Button className="tw:flex tw:size-[36px] tw:shrink-0 tw:cursor-pointer tw:items-center tw:justify-center tw:rounded-full! tw:hover:bg-[rgba(0,0,0,0.05)]!">
                <EditIcon />
            </Button>
        </FileTrigger>
        <Tooltip>Edit</Tooltip>
    </TooltipTrigger>
)

const DeleteAction = ({ onDelete }: { onDelete: (e: PressEvent) => void }) => (
    <TooltipTrigger delay={0}>
        <Button
            className="tw:flex tw:size-[36px] tw:shrink-0 tw:cursor-pointer tw:items-center tw:justify-center tw:rounded-full! tw:hover:bg-[rgba(0,0,0,0.05)]!"
            onPress={onDelete}
        >
            <DeleteIcon />
        </Button>
        <Tooltip>Delete</Tooltip>
    </TooltipTrigger>
)

const FileThumbnail = ({
    file,
    style,
}: {
    file: UploaderValue
    style: 'card' | 'list'
}) => {
    const FileDynamicIcon = useMemo(() => getFileIcon(file.name), [file.name])
    const isImg = useMemo(() => isImage(file.name), [file.name])

    return (
        <div
            className={cn(
                'tw:flex tw:size-[68px] tw:shrink-0 tw:items-center tw:justify-center tw:rounded-[2px] tw:bg-[#E4EBF2]',
                style === 'card' && 'tw:h-[110px] tw:w-full tw:border tw:border-[#E4EBF2]',
            )}
        >
            {isImg && file.url ? (
                <img
                    src={file.url}
                    alt={file.name}
                    className={cn(
                        'tw:h-full tw:w-full tw:rounded-[2px] tw:object-cover',
                        style === 'card' && 'tw:object-contain',
                    )}
                />
            ) : (
                <FileDynamicIcon />
            )}
        </div>
    )
}

const FileName = ({
    file,
    message,
    invalid,
}: {
    file: UploaderValue
    message?: string
    invalid?: boolean
}) => (
    <TooltipTrigger delay={0}>
        <Focusable>
            <div
                className={cn(
                    'tw:relative tw:mt-[-1.00px] tw:shrink tw:overflow-hidden tw:text-ellipsis tw:whitespace-nowrap tw:font-normal tw:text-[#3a3e63] tw:text-base tw:leading-[normal] tw:tracking-[0]',
                    invalid && 'tw:text-[#EB2121]',
                )}
            >
                {message || file.name}
            </div>
        </Focusable>
        <Tooltip>
            {file.name}
            {message && (
                <div
                    className={cn(
                        'tw:mt-[4px] tw:text-[#75799D] tw:text-xs tw:leading-[normal] tw:tracking-[0]',
                        invalid && 'tw:text-[#EB2121]',
                    )}
                >
                    {message}
                </div>
            )}
        </Tooltip>
    </TooltipTrigger>
)

type UploaderFileCardProps = {
    file: UploaderValue
    onEdit: (file: File) => void
    acceptedFileTypes: string[] | undefined
    onDelete: (e: PressEvent) => void
    invalid?: boolean
    message?: string
    pending?: boolean
    onAbort?: ((e: PressEvent) => void) | undefined
    progress?: number
    style: 'card' | 'list'
}

export const UploaderFileCard = ({
    file,
    onEdit,
    acceptedFileTypes,
    onDelete,
    invalid,
    message,
    pending,
    onAbort,
    progress,
    style,
}: UploaderFileCardProps): JSX.Element => {
    return (
        <div className="tw:h-full tw:w-full tw:grow tw:overflow-hidden tw:rounded-[2px] tw:bg-[#F6F9FC] ">
            <div
                className={cn(
                    'tw:flex tw:w-full tw:flex-col tw:items-start tw:justify-between tw:gap-[16px] tw:overflow-hidden tw:p-[6px] tw:lg:flex-row',
                    style === 'card' && 'tw:p-0',
                )}
            >
                <div className="tw:relative tw:inline-flex tw:w-full tw:flex-[0_0_auto] tw:flex-col tw:items-start tw:gap-4 tw:overflow-hidden">
                    <div
                        className={cn(
                            'tw:relative tw:inline-flex tw:w-full tw:flex-[0_0_auto] tw:items-center tw:overflow-hidden',
                            style === 'card' && 'tw:flex-col',
                        )}
                    >
                        <FileThumbnail file={file} style={style} />
                        <div className="tw:relative tw:inline-flex tw:w-full tw:grow tw:items-center tw:gap-[16px] tw:overflow-hidden tw:px-[16px]">
                            {(message || file.name) && !pending && (
                                <FileName
                                    file={file}
                                    message={message}
                                    invalid={invalid}
                                />
                            )}
                            {pending && (
                                <ProgressBar progress={progress || 0} />
                            )}
                            {!pending && !invalid && (
                                <div className="tw:relative tw:ms-auto tw:w-fit tw:shrink-0 tw:overflow-hidden tw:font-normal tw:text-[#B4B6C9] tw:text-variable-collection-subtext-color tw:text-xs tw:leading-[normal] tw:tracking-[0]">
                                    {formatFileSize(Number(file.size))}
                                </div>
                            )}
                            <div
                                className={cn(
                                    'tw:-me-[12px] tw:flex tw:items-center tw:gap-[4px]',
                                    (pending || invalid) && 'tw:ms-auto',
                                )}
                            >
                                {/* Abort */}
                                {(pending || invalid) && (
                                    <AbortAction onAbort={onAbort} />
                                )}
                                {!pending && !invalid && file.url && (
                                    <>
                                        {style === 'list' ? (
                                            <>
                                                {/* Download */}
                                                <DownloadAction
                                                    fileUrl={file.url}
                                                />
                                                {/* Edit */}
                                                <EditAction
                                                    acceptedFileTypes={
                                                        acceptedFileTypes
                                                    }
                                                    onEdit={onEdit}
                                                />
                                            </>
                                        ) : null}
                                        {/* Delete */}
                                        <DeleteAction onDelete={onDelete} />
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
