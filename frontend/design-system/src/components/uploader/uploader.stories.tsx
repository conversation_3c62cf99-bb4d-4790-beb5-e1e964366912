import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { useEffect, useState } from 'react'
import { ErrorMessage, SupportText } from '../input/input'
import { Label } from '../label/label'
import { Uploader } from './uploader'
import type { UploaderValue } from './uploader-types'

// Define the args type for the template
interface UploaderTemplateArgs {
    // InputField props
    label?: string
    supportText?: string
    errorMessage?: string
    required?: boolean
    invalid?: boolean
    success?: boolean
    isDisabled?: boolean
    name?: string
    autoFocus?: boolean
    className?: string
    // Uploader props
    value?: UploaderValue | UploaderValue[] | null
    onUpload?: (value: string) => void
    size?: 'sm' | 'md' | 'lg'
    // Additional Uploader props
    multiple?: boolean
    acceptedFileTypes?: string[]
    maxFileSize?: number
    fileItemStyle?: 'list' | 'card'
}

// Mock API function for handling file uploads
const dummyAPI = async (file?: File): Promise<UploaderValue> => {
    return new Promise<UploaderValue>((resolve) => {
        setTimeout(() => {
            const isInvalid = Math.floor(Math.random() * 100) % 2 === 0
            const fileValue: UploaderValue = {
                id: Math.floor(Math.random() * 1000000),
                name: file?.name ?? '',
                size: file?.size ?? 0,
                url: URL.createObjectURL(file ?? new Blob()),
                ...(Math.random() > 0.5
                    ? {
                          invalid: isInvalid,
                          message: isInvalid ? 'Backend error' : undefined,
                          pending: Math.floor(Math.random() * 100) % 2 === 0,
                          progress:
                              Math.floor(Math.random() * 100) % 2 === 0
                                  ? Math.floor(Math.random() * 100)
                                  : 0,
                      }
                    : {}),
            }
            resolve(fileValue)
        }, 1000)
    })
}

// Template function that renders InputField with InputGroup and TextArea and handles all props
const UploaderTemplate = (args: UploaderTemplateArgs): React.JSX.Element => {
    const {
        // TextAreaField props
        label,
        supportText,
        errorMessage,
        required,
        invalid,
        success,
        isDisabled,
        name,
        autoFocus,
        className,
        // Uploader props
        value: initialValue,
        size,
        multiple,
        acceptedFileTypes,
        maxFileSize,
        fileItemStyle,
    } = args

    const [value, setValue] = useState<UploaderValue | UploaderValue[] | null>(
        initialValue ?? null,
    )

    useEffect(() => {
        setValue(initialValue ?? null)
    }, [initialValue])

    return (
        <div className="tw:w-[444px]">
            <div>
                {label && <Label required={required}>{label}</Label>}
                <Uploader
                    value={value}
                    onUpload={(file) => {
                        dummyAPI(file).then((newValue) => {
                            if (multiple) {
                                setValue((prev) => {
                                    if (Array.isArray(prev)) {
                                        return [...prev, newValue]
                                    }
                                    return [newValue]
                                })
                            } else {
                                setValue(newValue)
                            }
                        })
                    }}
                    onDelete={(_fileItemValue, index) => {
                        dummyAPI().then(() => {
                            if (multiple) {
                                setValue((prev) => {
                                    if (!Array.isArray(prev)) return null
                                    const newValue = [...prev]
                                    newValue.splice(index, 1)
                                    return newValue.length > 0 ? newValue : null
                                })
                            } else {
                                setValue(null)
                            }
                        })
                    }}
                    onAbort={(_fileItemValue, index) => {
                        dummyAPI().then(() => {
                            if (multiple) {
                                setValue((prev) => {
                                    if (!Array.isArray(prev)) return null
                                    const newValue = [...prev]
                                    newValue.splice(index, 1)
                                    return newValue.length > 0 ? newValue : null
                                })
                            } else {
                                setValue(null)
                            }
                        })
                    }}
                    onEdit={(_fileItemValue, index, newFile) => {
                        dummyAPI(newFile).then((newValue) => {
                            if (multiple) {
                                setValue((prev) => {
                                    if (!Array.isArray(prev)) return null
                                    const newValues = [...prev]
                                    newValues[index] = newValue
                                    return newValues
                                })
                            } else {
                                setValue(newValue)
                            }
                        })
                    }}
                    isDisabled={isDisabled}
                    size={size}
                    multiple={multiple}
                    acceptedFileTypes={acceptedFileTypes}
                    maxFileSize={maxFileSize}
                    fileItemStyle={fileItemStyle}
                />
                {supportText && <SupportText>{supportText}</SupportText>}
                {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
            </div>
        </div>
    )
}

const meta: Meta<typeof UploaderTemplate> = {
    title: 'Components/Uploader',
    component: UploaderTemplate,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        // TextAreaField props
        label: {
            control: 'text',
            description: 'The label text displayed above the textarea',
        },
        supportText: {
            control: 'text',
            description: 'Help text displayed below the textarea',
        },
        errorMessage: {
            control: 'text',
            description: 'Error message displayed below the textarea',
        },
        required: {
            control: 'boolean',
            description: 'Whether the field is required (shows red asterisk)',
        },
        invalid: {
            control: 'boolean',
            description:
                'Whether the textarea is in an invalid state (shows red border)',
        },
        success: {
            control: 'boolean',
            description:
                'Whether the textarea is in a success state (shows green border)',
        },
        isDisabled: {
            control: 'boolean',
            description: 'Whether the TextAreaField is disabled',
        },
        name: {
            control: 'text',
            description: 'The name attribute for the textarea field',
        },
        autoFocus: {
            control: 'boolean',
            description: 'Whether the textarea should be focused on mount',
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes for the TextAreaField',
        },
        // Uploader props
        value: {
            control: 'text',
            description: 'The value of the textarea field',
        },
        onUpload: {
            table: {
                disable: true,
            },
        },
        size: {
            control: 'select',
            options: ['sm', 'md', 'lg'],
            description: 'The size of the uploader',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: UploaderTemplate,
    argTypes: {
        value: {
            control: 'object',
        },
    },
    args: {
        label: 'Attachment',
        supportText: 'Please provide a valid file',
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic uploader with label and support text.',
            },
        },
    },
}

export const SingleUploader: Story = {
    render: UploaderTemplate,
    args: {
        label: 'Upload File',
        supportText: 'Accepts images and PDFs up to 10MB',
        acceptedFileTypes: ['image/*', 'application/pdf'],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        fileItemStyle: 'list',
        required: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Single file uploader with list style view. Accepts images and PDFs up to 10MB.',
            },
        },
    },
}

export const SingleUploaderWithOldValue: Story = {
    render: UploaderTemplate,
    argTypes: {
        value: {
            control: 'object',
        },
    },
    args: {
        label: 'Attachment',
        supportText: 'Please provide a valid file',
        value: {
            id: 1,
            name: 'istockphoto-184962061-612x612-2489572342304.png',
            size: 100,
            url: 'https://placehold.co/600x400/FDDD0/FFF',
        },
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic uploader with label and support text.',
            },
        },
    },
}

export const MultipleUploader: Story = {
    render: UploaderTemplate,
    args: {
        label: 'Upload Files',
        supportText: 'Upload multiple files up to 20MB each',
        multiple: true,
        maxFileSize: 20 * 1024 * 1024, // 20MB
        fileItemStyle: 'list',
        required: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Multiple file uploader that allows uploading multiple files at once.',
            },
        },
    },
}

export const MultipleUploaderWithOldValue: Story = {
    render: UploaderTemplate,
    argTypes: {
        value: {
            control: 'object',
        },
    },
    args: {
        label: 'Attachment',
        supportText: 'Please provide a valid file',
        multiple: true,
        value: [
            {
                id: 1,
                name: 'istockphoto-184962061-612x612-2489572342304.png',
                size: 100,
                url: 'https://placehold.co/600x400/FDDD0/FFF',
            },
            {
                id: 2,
                name: '2489572342304.png',
                size: 2200,
                url: 'https://placehold.co/600x400/FDDD0/FFF',
            },
        ],
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic uploader with label and support text.',
            },
        },
    },
}

export const MultipleUploaderWithCardStyle: Story = {
    render: UploaderTemplate,
    argTypes: {
        value: {
            control: 'object',
        },
    },
    args: {
        label: 'Attachment',
        supportText: 'Please provide a valid file',
        multiple: true,
        fileItemStyle: 'card',
        value: [
            {
                id: 1,
                name: 'istockphoto-184962061-612x612-2489572342304.png',
                size: 100,
                url: 'https://placehold.co/600x400/FDDD0/FFF',
            },
            {
                id: 2,
                name: 'lorem-612x612-2489572342304.pdf',
                size: 2200,
                url: 'https://placehold.co/600x400/FDDD0/FFF',
            },
        ],
    },
    parameters: {
        docs: {
            description: {
                story: 'Multiple file uploader with card style view.',
            },
        },
    },
}

export const AllowedFileTypes: Story = {
    render: UploaderTemplate,
    args: {
        label: 'Upload Excel Files',
        supportText: 'Only Excel files are allowed',
        multiple: true,
        acceptedFileTypes: [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
        ],
        maxFileSize: 20 * 1024 * 1024, // 20MB
        fileItemStyle: 'list',
        required: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Multiple file uploader that only accepts Excel files. Shows how to handle multiple file uploads with specific file type restrictions.',
            },
        },
    },
}

export const MaxFileSize: Story = {
    render: UploaderTemplate,
    args: {
        label: 'Upload Small Files',
        supportText: 'Files up to 1MB are allowed',
        multiple: true,
        maxFileSize: 1 * 1024 * 1024, // 1MB
        fileItemStyle: 'list',
        required: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Multiple file uploader with a small file size limit of 1MB.',
            },
        },
    },
}

export const DisabledUploader: Story = {
    render: UploaderTemplate,
    args: {
        label: 'Upload Files',
        supportText: 'This uploader is disabled',
        isDisabled: true,
        fileItemStyle: 'list',
    },
    parameters: {
        docs: {
            description: {
                story: 'Disabled state of the uploader. User cannot interact with it.',
            },
        },
    },
}

export const InvalidUploader: Story = {
    render: UploaderTemplate,
    args: {
        label: 'Upload Files',
        supportText: 'Please provide valid files',
        errorMessage: 'The uploaded files are invalid',
        invalid: true,
        fileItemStyle: 'list',
        required: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Invalid state of the uploader, typically used to show validation errors.',
            },
        },
    },
}
