import type { JS<PERSON> } from 'react'
import { useCallback } from 'react'
import type { DropEvent, DropItem } from 'react-aria'
import { Button, DropZone, FileTrigger } from 'react-aria-components'
import { cn } from '../../utils/cn'
import { UploaderFileCard } from './uploader-file-card'
import UploaderInput from './uploader-input'
import type { UploaderValue } from './uploader-types'

type UploaderProps = {
    value: UploaderValue | UploaderValue[] | null
    className?: string
    isDisabled?: boolean
    invalid?: boolean
    size?: 'sm' | 'md' | 'lg'
    acceptedFileTypes?: string[] | undefined
    maxFileSize?: number
    onUpload?: (fileObject: File) => void
    onDelete?: (fileItemValue: UploaderValue, index: number) => void
    onEdit?: (
        fileItemValue: UploaderValue,
        index: number,
        newFile: File,
    ) => void
    onAbort?: (fileItemValue: UploaderValue, index: number) => void
    multiple?: boolean
    fileItemStyle?: 'list' | 'card'
}

const Uploader = ({
    value = null,
    className,
    isDisabled = false,
    invalid = false,
    size = 'sm',
    acceptedFileTypes,
    maxFileSize = 10 * 1024 * 1024, // 10MB default
    onUpload = (_fileObject: File) => {},
    onDelete = (_fileItemValue: UploaderValue, _index: number) => {},
    onEdit = (
        _fileItemValue: UploaderValue,
        _index: number,
        _newFile: File,
    ) => {},
    onAbort = (_fileItemValue: UploaderValue, _index: number) => {},
    multiple = false,
    fileItemStyle = 'list',
}: UploaderProps): JSX.Element => {
    const files = value === null ? [] : Array.isArray(value) ? value : [value]

    const handleDrop = useCallback(
        async (e: DropEvent) => {
            if (e.items && e.items.length > 0) {
                e.items.forEach(async (item: DropItem) => {
                    if (item.kind === 'file') {
                        handleFileSelect(
                            (await item.getFile()) as unknown as File,
                        )
                    }
                })
            }
        },
        [acceptedFileTypes, maxFileSize, onUpload],
    )

    const handleFileSelect = (file: File) => {
        // Check file size
        if (file.size > maxFileSize) {
            alert('File size exceeds the maximum limit')
            return
        }

        // Check file type
        if (
            acceptedFileTypes &&
            !acceptedFileTypes.some((type) =>
                file.type.startsWith(type.replaceAll('*', '')),
            )
        ) {
            alert('File type not supported')
            return
        }

        onUpload(file)
    }

    const handleFileSelectMultiple = useCallback(
        (files: FileList | null) => {
            if (files) {
                const filesArray = Array.from(files)
                filesArray.forEach((file) => {
                    handleFileSelect(file)
                })
            }
        },
        [acceptedFileTypes, maxFileSize, onUpload],
    )

    return (
        <div
            className={cn(
                'tw:relative tw:flex tw:w-full tw:flex-col tw:gap-[8px]',
                isDisabled && 'tw:pointer-events-none tw:opacity-50',
                className,
            )}
        >
            {files.length === 0 || multiple ? (
                <DropZone onDrop={handleDrop}>
                    <FileTrigger
                        allowsMultiple={multiple}
                        acceptedFileTypes={acceptedFileTypes}
                        onSelect={handleFileSelectMultiple}
                    >
                        <Button
                            className={cn(
                                'tw:flex tw:w-full tw:cursor-pointer tw:flex-col tw:items-center tw:justify-center tw:gap-[6px] tw:rounded-[2px] tw:border-1 tw:border-[#E4EBF2] tw:border-dashed tw:hover:border-[#1877F2] tw:hover:bg-[#F5F7FA] tw:[[data-drop-target]_&]:border-[#1877F2] tw:[[data-drop-target]_&]:bg-[#b3d4ff]',
                                invalid && 'tw:border-red-500',
                                className,
                            )}
                        >
                            <UploaderInput size={size} />
                        </Button>
                    </FileTrigger>
                </DropZone>
            ) : null}
            {files.length > 0 ? (
                <div
                    className={cn(
                        'tw:flex tw:flex-col tw:gap-[8px]',
                        fileItemStyle === 'card' && 'tw:flex-row',
                    )}
                >
                    {files.map((file, index) => (
                        <UploaderFileCard
                            key={file.id}
                            file={file}
                            onEdit={(newFile) => {
                                onEdit(file, index, newFile)
                            }}
                            onDelete={() => {
                                onDelete(file, index)
                            }}
                            acceptedFileTypes={acceptedFileTypes}
                            style={fileItemStyle}
                            invalid={file.invalid}
                            message={file.message}
                            pending={file.pending}
                            onAbort={() => {
                                onAbort(file, index)
                            }}
                            progress={file.progress}
                        />
                    ))}
                </div>
            ) : null}
        </div>
    )
}

export { Uploader }
