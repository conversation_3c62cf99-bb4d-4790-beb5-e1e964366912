import { cva, type VariantProps } from 'class-variance-authority'
import type { JSX } from 'react'
import { FileIcon, FileUploadIcon } from './uploader-icons'

const UploaderInputVariants = cva(
    ['tw:flex tw:w-full tw:items-center tw:justify-center tw:gap-4 tw:px-[18px] tw:py-[16px]'],
    {
        variants: {
            size: {
                sm: 'tw:min-h-[80px]',
                md: 'tw:min-h-[96px]',
                lg: 'tw:min-h-[112px]',
            },
        },
        defaultVariants: {
            size: 'sm',
        },
    },
) as (props?: { size?: 'sm' | 'md' | 'lg' }) => string

type UploaderInputVariants = VariantProps<typeof UploaderInputVariants>

const UploaderInput = ({ size }: UploaderInputVariants): JSX.Element => {
    return (
        <div className={UploaderInputVariants({ size })}>
            <FileIcon />
            <div className="tw:flex tw:grow tw:items-center tw:justify-center tw:gap-[8px] tw:[[data-drop-target]_&]:mx-auto">
                <FileUploadIcon className="tw:[[data-drop-target]_&]:animate-bounce" />
                <p className="tw:mb-0! tw:text-[#202124] tw:text-[14px] tw:[[data-drop-target]_&]:hidden">
                    Drop image here or{' '}
                    <span className="tw:text-[#1877F2]">
                        select from your computer
                    </span>
                </p>
                <p className="tw:mb-0 tw:hidden tw:font-normal tw:text-[#1877F2] tw:text-base tw:[[data-drop-target]_&]:block tw:[[data-drop-target]_&]:animate-bounce">
                    Drop here
                </p>
            </div>
        </div>
    )
}

export default UploaderInput
