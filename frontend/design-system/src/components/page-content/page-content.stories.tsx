import type { Meta, StoryObj } from '@storybook/react'
import { PageContent } from './page-content'

const meta: Meta<typeof PageContent> = {
    title: 'Components/PageContent',
    component: PageContent,
    parameters: {
        layout: 'fullscreen',
    },
    tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: (
            <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Saepe
                aspernatur rerum, veritatis modi illo accusamus odio molestias
                maxime est doloribus eos deleniti dicta id, molestiae voluptate
                iste, quaerat quis in?
            </p>
        ),
    },
}

export const WithCustomClassName: Story = {
    args: {
        className: 'tw:bg-gray-50',
        children: (
            <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Saepe
                aspernatur rerum, veritatis modi illo accusamus odio molestias
                maxime est doloribus eos deleniti dicta id, molestiae voluptate
                iste, quaerat quis in?
            </p>
        ),
    },
}
