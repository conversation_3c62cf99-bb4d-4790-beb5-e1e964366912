import type { <PERSON>a, StoryObj } from '@storybook/react'
import { ChevronDownIcon } from '../../icons/chevron-down-icon'
import { Button } from '../button/button'
import { Checkbox } from '../checkbox/checkbox'
import { ButtonGroup } from './button-group'
import { MenuTrigger } from '../menu/menu-trigger'
import { MenuPopover } from '../menu/menu-popover'
import { Menu } from '../menu/menu'
import { MenuItem } from '../menu/menu-item'
import { MenuItemIcon } from '../menu/menu-item-icon'
import { SortIcon } from '../../icons/sort-icon'

const meta: Meta<typeof ButtonGroup> = {
    title: 'Components/ButtonGroup',
    component: ButtonGroup,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        orientation: {
            control: { type: 'select' },
            options: ['horizontal', 'vertical'],
        },
        gap: {
            control: { type: 'boolean' },
            description:
                'Whether to show gaps between buttons (false = connected buttons)',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const WithGap: Story = {
    args: {
        children: (
            <>
                <Button variant="filled" color="secondary">
                    Save
                </Button>
                <Button variant="filled" color="secondary">
                    Cancel
                </Button>
            </>
        ),
    },
}

export const WithGapSplitDropdown: Story = {
    args: {
        children: (
            <>
                <Button variant="filled" color="primary">
                    Save without payment
                </Button>
                <Button variant="filled" color="primary" className="tw:px-[1px]">
                    <ChevronDownIcon />
                </Button>
            </>
        ),
    },
}

export const WithoutGap: Story = {
    args: {
        gap: false,
        children: (
            <>
                <Button variant="filled" color="primary">
                    Save
                </Button>
                <Button variant="filled" color="primary">
                    Cancel
                </Button>
            </>
        ),
    },
}

export const VerticalWithGap: Story = {
    args: {
        orientation: 'vertical',
        children: (
            <>
                <Button variant="filled" color="primary">
                    Top
                </Button>
                <Button variant="filled" color="primary">
                    Middle
                </Button>
                <Button variant="filled" color="primary">
                    Bottom
                </Button>
            </>
        ),
    },
}

export const VerticalWithoutGap: Story = {
    args: {
        orientation: 'vertical',
        gap: false,
        children: (
            <>
                <Button variant="filled" color="primary">
                    Top
                </Button>
                <Button variant="filled" color="primary">
                    Middle
                </Button>
                <Button variant="filled" color="primary">
                    Bottom
                </Button>
            </>
        ),
    },
}

export const BulkActions: Story = {
    args: {
        children: (
            <>
                <Button
                    variant="filled"
                    color="neutral"
                    className="tw:relative tw:size-[42px] tw:p-0"
                >
                    <Checkbox className="tw:absolute tw:inset-0 tw:flex tw:cursor-pointer tw:items-center tw:justify-center tw:bg-transparent tw:p-0" />
                </Button>
                <MenuTrigger
                >
                    <Button variant="filled" color="neutral" className="tw:px-[1px]">
                        <ChevronDownIcon />
                    </Button>
                    <MenuPopover>
                        <Menu>
                            <MenuItem onAction={() => alert('none')}>
                                <MenuItemIcon>
                                    <SortIcon />
                                </MenuItemIcon>
                                <span>None</span>
                            </MenuItem>
                            <MenuItem onAction={() => alert('all-current')}>
                                <MenuItemIcon>
                                    <SortIcon />
                                </MenuItemIcon>
                                <span>All (Current Page)</span>
                            </MenuItem>
                            <MenuItem onAction={() => alert('all-filtered')}>
                                <MenuItemIcon>
                                    <SortIcon />
                                </MenuItemIcon>
                                <span>All Filtered (All Pages)</span>
                            </MenuItem>
                        </Menu>
                    </MenuPopover>
                </MenuTrigger>
            </>
        ),
    },
}
