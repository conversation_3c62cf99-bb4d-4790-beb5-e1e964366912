import type React from 'react'
import type { ReactNode } from 'react'
import { cn } from '../../utils/cn'

export interface ButtonGroupProps {
    children: ReactNode
    className?: string
    orientation?: 'horizontal' | 'vertical'
    gap?: boolean
}

export function ButtonGroup({
    children,
    className = '',
    orientation = 'horizontal',
    gap = true,
}: ButtonGroupProps): React.JSX.Element {
    const groupClasses = cn(
        'tw:inline-flex',
        {
            'tw:flex-row': orientation === 'horizontal',
            'tw:flex-col': orientation === 'vertical',
            'tw:gap-[1px]': gap,
        },
        // Always apply border radius to be 0 for appearance
        orientation === 'horizontal' && [
            'tw:[&>*:first-child]:rounded-e-none',
            'tw:[&>*:not(:first-child):not(:last-child)]:rounded-none',
            'tw:[&>*:last-child]:rounded-s-none',
        ],
        orientation === 'vertical' && [
            'tw:[&>*:first-child]:rounded-b-none',
            'tw:[&>*:not(:first-child):not(:last-child)]:rounded-none',
            'tw:[&>*:last-child]:rounded-t-none',
        ],
        // Apply negative margins only when gap is false for better connected appearance
        !gap &&
            orientation === 'horizontal' &&
            'tw:[&>*:not(:first-child)]:-ms-[2px]',
        !gap &&
            orientation === 'vertical' &&
            'tw:[&>*:not(:first-child)]:-mt-[2px]',
        className,
    )

    return <div className={groupClasses}>{children}</div>
}
