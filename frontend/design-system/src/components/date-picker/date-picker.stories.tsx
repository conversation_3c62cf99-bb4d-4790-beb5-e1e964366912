import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { useEffect, useState } from 'react'
import { ErrorMessage, InputField, SupportText } from '../input/input'
import { Label } from '../label/label'
import { DatePicker } from './date-picker'

// Define the args type for the template
interface DatePickerTemplateArgs {
    // InputField props
    label?: string
    supportText?: string
    errorMessage?: string
    required?: boolean
    invalid?: boolean
    success?: boolean
    isDisabled?: boolean
    name?: string
    autoFocus?: boolean
    className?: string
    // DatePicker props
    value?: string
    onChange?: (value: string | null) => void
    placeholder?: string
    minValue?: string
    maxValue?: string
    size?: 'sm' | 'md' | 'lg'
}

// Template function that renders InputField with DatePicker and handles all props
const DatePickerTemplate = (
    args: DatePickerTemplateArgs,
): React.JSX.Element => {
    const {
        // InputField props
        label,
        supportText,
        errorMessage,
        required,
        invalid,
        success,
        isDisabled,
        name,
        autoFocus,
        className,
        // DatePicker props
        value: initialValue,
        placeholder,
        minValue,
        maxValue,
        size,
    } = args

    const [value, setValue] = useState(initialValue ?? '')

    useEffect(() => {
        setValue(initialValue ?? '')
    }, [initialValue])

    return (
        <InputField
            invalid={invalid}
            success={success}
            isDisabled={isDisabled}
            name={name}
            autoFocus={autoFocus}
            className={className}
        >
            {label && <Label required={required}>{label}</Label>}
            <DatePicker
                value={value}
                onChange={(newValue) => {
                    setValue(newValue ?? '')
                }}
                placeholder={placeholder}
                minValue={minValue}
                maxValue={maxValue}
                isInvalid={invalid}
                size={size}
            />
            {supportText && <SupportText>{supportText}</SupportText>}
            {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
        </InputField>
    )
}

const meta: Meta<typeof DatePickerTemplate> = {
    title: 'Components/DatePicker',
    component: DatePickerTemplate,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        // InputField props
        label: {
            control: 'text',
            description: 'The label text displayed above the date picker',
        },
        supportText: {
            control: 'text',
            description: 'Help text displayed below the date picker',
        },
        errorMessage: {
            control: 'text',
            description: 'Error message displayed below the date picker',
        },
        required: {
            control: 'boolean',
            description: 'Whether the field is required (shows red asterisk)',
        },
        invalid: {
            control: 'boolean',
            description:
                'Whether the date picker is in an invalid state (shows red border)',
        },
        success: {
            control: 'boolean',
            description:
                'Whether the date picker is in a success state (shows green border)',
        },
        isDisabled: {
            control: 'boolean',
            description: 'Whether the date picker is disabled',
        },
        name: {
            control: 'text',
            description: 'The name attribute for the date picker field',
        },
        autoFocus: {
            control: 'boolean',
            description: 'Whether the date picker should be focused on mount',
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes for the InputField',
        },
        // DatePicker props
        value: {
            control: 'text',
            description: 'The selected date value (ISO string)',
        },
        onChange: {
            table: {
                disable: true,
            },
        },
        placeholder: {
            control: 'text',
            description: 'The placeholder text for the date picker',
        },
        minValue: {
            control: 'text',
            description: 'The minimum allowed date (ISO string)',
        },
        maxValue: {
            control: 'text',
            description: 'The maximum allowed date (ISO string)',
        },
        size: {
            control: 'select',
            options: ['sm', 'md', 'lg'],
            description: 'The size of the date picker',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: DatePickerTemplate,
    args: {
        label: 'Start Date',
        supportText: 'Choose start date from the calendar.',
        placeholder: 'Select Date',
        value: '2024-01-15',
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic date picker field with label and support text.',
            },
        },
    },
}

export const RTL: Story = {
    render: DatePickerTemplate,
    args: {
        label: 'اختر تاريخ البداية',
        supportText: 'اختر تاريخ البداية من التقويم.',
        placeholder: 'اختر تاريخ البداية',
        value: '2024-01-15',
    },
    parameters: {
        docs: {
            description: {
                story: 'Date picker with RTL (right-to-left) text direction support.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}