import { type DateValue, parseDate } from '@internationalized/date'
import { ChevronRightIcon } from 'lucide-react'
import moment from 'moment'
// import momentHijri from 'moment-hijri'
import type { JSX } from 'react'
import {
    DatePicker as AriaDatePicker,
    Button,
    Calendar,
    CalendarCell,
    CalendarGrid,
    CalendarGridBody,
    CalendarGridHeader,
    CalendarHeaderCell,
    Dialog,
    DialogTrigger,
    Heading,
    Popover,
    Pressable,
} from 'react-aria-components'
import { CalendarIcon } from '../../icons/calendar-icon'
import { ChevronLeftIcon } from '../../icons/chevron-left-icon'
import { cn } from '../../utils/cn'
import { Input, InputGroup, InputPostfix } from '../input/input'

// Helper function to convert DateValue back to string
function dateValueToString(dateValue: DateValue | null): string | null {
    if (!dateValue) return null
    return dateValue.toString()
}

const DatePicker = ({
    value,
    onChange = () => {},
    className,
    placeholder = 'Select Date',
    minValue,
    maxValue,
    isDateUnavailable,
    isInvalid = false,
    size = 'lg',
    name,
}: {
    value: string
    onChange?: (value: string | null) => void
    className?: string
    placeholder?: string
    minValue?: string
    maxValue?: string
    isDateUnavailable?: (date: DateValue) => boolean
    isInvalid?: boolean
    size?: 'sm' | 'md' | 'lg'
    name?: string
}): JSX.Element => {
    // Convert string values to DateValue objects
    const dateValue = value ? parseDate(value) : null
    const minDateValue = minValue ? parseDate(minValue) : null
    const maxDateValue = maxValue ? parseDate(maxValue) : null

    // Handle onChange to convert back to string
    const handleChange = (newValue: DateValue | null) => {
        const stringValue = dateValueToString(newValue)
        onChange?.(stringValue)
    }

    return (
        <AriaDatePicker
            value={dateValue}
            onChange={handleChange}
            isInvalid={isInvalid}
            minValue={minDateValue}
            maxValue={maxDateValue}
            isDateUnavailable={isDateUnavailable}
        >
            <DialogTrigger>
                <Pressable>
                    <div className={cn('tw:w-full', className)}>
                        <InputGroup size={size}>
                            <Input
                                className={cn(
                                    'tw:dir-ltr tw:disabled:text-black tw:ltr:text-left tw:rtl:text-right',
                                )}
                                disabled
                                placeholder={placeholder}
                                value={
                                    value
                                        ? moment(value).format('DD MMM YYYY')
                                        : ''
                                }
                                // value={value ? momentHijri(value).format('iYYYY-iMM-iDD') : ''}
                                type="text"
                                name={name}
                            />
                            <InputPostfix>
                                <CalendarIcon />
                            </InputPostfix>
                        </InputGroup>
                    </div>
                </Pressable>

                <Popover
                    className={cn(
                        'tw:flex tw:max-h-80 tw:min-w-[var(--trigger-width)] tw:flex-col tw:overflow-auto tw:rounded-xs tw:border tw:border-[#E4EBF2] tw:bg-white tw:text-base tw:shadow-lg tw:ring-1 tw:ring-black/5',
                        'tw:entering:fade-in tw:entering:zoom-in-95 tw:entering:animate-in',
                        'tw:exiting:fade-out tw:exiting:zoom-out-95 tw:exiting:animate-out',
                        'tw:data-[placement=top]:slide-in-from-bottom-2',
                        'tw:data-[placement=bottom]:slide-in-from-top-2',
                        'tw:p-4',
                    )}
                >
                    <Dialog className="tw:outline-none">
                        <Calendar className="tw:outline-none">
                            <header className="tw:mb-4 tw:flex tw:items-center tw:justify-between">
                                <Button
                                    slot="previous"
                                    className={cn(
                                        'tw:flex tw:h-8 tw:w-8 tw:cursor-pointer tw:items-center tw:justify-center tw:rounded-xs tw:text-primary-black! tw:transition-colors',
                                        'tw:hover:bg-light-gray-1 tw:focus:bg-light-gray-1',
                                        'tw:disabled:cursor-not-allowed tw:disabled:text-light-gray-4',
                                    )}
                                >
                                    <span className="tw:rtl:rotate-180">
                                        <ChevronLeftIcon size={24} />
                                    </span>
                                </Button>
                                <Heading className="tw:text-primary-black! tw:text-[16px]! tw:font-medium! tw:mb-0!" />
                                <Button
                                    slot="next"
                                    className={cn(
                                        'tw:flex tw:h-8 tw:w-8 tw:cursor-pointer tw:items-center tw:justify-center tw:rounded-xs tw:text-primary-black! tw:transition-colors',
                                        'tw:hover:bg-light-gray-1 tw:focus:bg-light-gray-1',
                                        'tw:disabled:cursor-not-allowed tw:disabled:text-light-gray-4',
                                    )}
                                >
                                    <span className="tw:rtl:rotate-180">
                                        <ChevronRightIcon size={24} />
                                    </span>
                                </Button>
                            </header>
                            <CalendarGrid className="tw:mx-auto tw:border-separate! tw:border-spacing-1!">
                                <CalendarGridHeader>
                                    {(day) => (
                                        <CalendarHeaderCell className="tw:bg-transparent! tw:pb-3 tw:text-center! tw:font-medium! tw:text-info-1! tw:text-xs! tw:uppercase!">
                                            {day}
                                        </CalendarHeaderCell>
                                    )}
                                </CalendarGridHeader>
                                <CalendarGridBody>
                                    {(date) => (
                                        <CalendarCell
                                            date={date}
                                            className={cn(
                                                'tw:flex tw:h-8 tw:w-8 tw:cursor-pointer tw:items-center tw:justify-center tw:rounded-xs tw:border tw:border-light-gray-2 tw:font-medium tw:text-dark-gray-2 tw:text-sm tw:transition-colors tw:lg:h-11 tw:lg:w-11',
                                                'tw:hover:bg-light-gray-1',
                                                'tw:focus:bg-info-1 tw:focus:text-white tw:focus:outline-none',
                                                'tw:selected:border-2 tw:selected:border-info-1 tw:selected:bg-info-1 tw:selected:text-white',
                                                'tw:disabled:cursor-not-allowed tw:disabled:text-light-gray-4',
                                                'tw:outside-month:text-light-gray-4',
                                                'tw:unavailable:cursor-not-allowed tw:unavailable:text-light-gray-4 tw:unavailable:line-through',
                                            )}
                                        />
                                    )}
                                </CalendarGridBody>
                            </CalendarGrid>
                        </Calendar>
                    </Dialog>
                </Popover>
            </DialogTrigger>
        </AriaDatePicker>
    )
}

export { DatePicker }
