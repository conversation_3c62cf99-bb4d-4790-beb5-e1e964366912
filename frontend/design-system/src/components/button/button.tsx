import type { VariantProps } from 'class-variance-authority'
import type { JSX, ReactNode } from 'react'
import type { ButtonProps as ReactAriaButtonProps } from 'react-aria-components'
import { Button as ReactAriaButton } from 'react-aria-components'
import { cn } from '../../utils/cn'
import { type ButtonVariants, buttonVariants } from './button-variants'

export interface ButtonProps extends ButtonVariants, ReactAriaButtonProps {
    children: ReactNode
    variant?: 'filled' | 'outlined' | 'subtle' | 'text'
    color?:
        | 'primary'
        | 'secondary'
        | 'warning'
        | 'error'
        | 'neutral'
        | 'neutral-2'
        | 'neutral-3'
    size?: 'sm' | 'lg' | 'icon'
    isLoading?: boolean
    className?: string
    isActive?: boolean
    onClick?: () => void
}

export function Button({
    children,
    variant = 'filled',
    color = 'primary',
    size = 'lg',
    isLoading = false,
    className = '',
    isActive = false,
    onClick,
    ...props
}: ButtonProps): JSX.Element {
    // Extract isDisabled from props
    const { isDisabled, ...restProps } = props

    const buttonClasses = cn(
        buttonVariants({ variant, color, size, isActive } as VariantProps<
            typeof buttonVariants
        >),
        className,
    )

    if (isLoading) {
        return (
            <ReactAriaButton
                className={buttonClasses}
                data-button-size={size}
                {...restProps}
            >
                <div
                    className={cn(
                        `tw:[[data-button-size="sm"]>&]:size-[16px]`,
                        `tw:[[data-button-size="icon"]>&]:size-[20px]`,
                        `tw:[[data-button-size="lg"]>&]:size-[24px]`,
                        'tw:animate-spin',
                    )}
                >
                    <svg
                        className="tw:h-full tw:w-full"
                        fill="none"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                        role="img"
                    >
                        <title>Loading</title>
                        <circle
                            className="tw:opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                        />
                        <path
                            className="tw:opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                    </svg>
                </div>
            </ReactAriaButton>
        )
    }

    // Regular button rendering
    return (
        <ReactAriaButton
            className={buttonClasses}
            isDisabled={isDisabled || isLoading}
            onPress={onClick}
            data-button-size={size}
            data-button-active={isActive}
            {...restProps}
        >
            {children}
        </ReactAriaButton>
    )
}
