import { cn } from '../../utils/cn'

interface ButtonIconProps extends React.HTMLAttributes<HTMLSpanElement> {
    children: React.ReactNode
    className?: React.HTMLAttributes<HTMLSpanElement>['className']
}

export function ButtonIcon({
    children,
    className = '',
    ...props
}: ButtonIconProps): React.ReactElement {
    return (
        <span
            className={cn(
                [
                    `tw:[[data-button-size="sm"]>&]:size-[16px]`,
                    `tw:[[data-button-size="icon"]>&]:size-[20px]`,
                    `tw:[[data-button-size="lg"]>&]:size-[20px]`,
                    `tw:inline-flex tw:shrink-0 tw:items-center tw:justify-center`,
                ],
                className,
            )}
            {...props}
        >
            {children}
        </span>
    )
}
