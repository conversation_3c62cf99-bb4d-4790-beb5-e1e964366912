import { cva, type VariantProps } from 'class-variance-authority'

export const buttonVariants: ReturnType<typeof cva> = cva(
    // Base classes
    [
        'tw:inline-flex tw:gap-2 tw:items-center tw:justify-center tw:font-medium tw:text-[14px]! tw:rounded-[2px] tw:cursor-pointer',
        'tw:transition-all tw:duration-200 tw:focus:outline-none tw:focus:ring-2 tw:focus:ring-offset-2',
        'tw:disabled:cursor-not-allowed tw:disabled:opacity-50',
    ],
    {
        variants: {
            variant: {
                filled: [
                    'tw:border-2 tw:bg-[var(--button-bg)] tw:text-[var(--button-filled-text)] tw:border-[var(--button-border)]',
                    'tw:hover:bg-[var(--button-hover-bg)] tw:hover:border-[var(--button-hover-border)] tw:hover:text-[var(--button-hover-text)]',
                    'tw:focus:ring-2 tw:focus:ring-offset-2 tw:focus:ring-[var(--button-focus-ring-color)]',
                    'tw:active:ring-2 tw:active:ring-offset-2 tw:active:ring-[var(--button-focus-ring-color)] tw:active:bg-[var(--button-active-bg)]',
                    'tw:data-[pressed=true]:bg-[var(--button-text-active-bg)] tw:data-[pressed=true]:text-[var(--button-text-active-text)] tw:data-[pressed=true]:bg-[var(--button-active-bg)]',
                ],
                outlined: [
                    'tw:border-2 tw:bg-transparent tw:text-[var(--button-outlined-text)] tw:border-[var(--button-border)]',
                    'tw:hover:bg-[var(--button-hover-bg)] tw:hover:text-[var(--button-hover-text)]',
                    'tw:focus:ring-2 tw:focus:ring-offset-2 tw:focus:ring-[var(--button-focus-ring-color)]',
                    'tw:active:ring-2 tw:active:ring-offset-2 tw:active:ring-[var(--button-focus-ring-color)] tw:active:bg-[var(--button-active-bg)]',
                    'tw:data-[pressed=true]:bg-[var(--button-text-active-bg)] tw:data-[pressed=true]:text-[var(--button-text-active-text)] tw:data-[pressed=true]:bg-[var(--button-active-bg)]',
                ],
                subtle: [
                    'tw:border-0 tw:bg-transparent tw:text-[var(--button-subtle-text)]',
                    'tw:hover:bg-[var(--button-subtle-hover-bg)]',
                    'tw:focus:ring-2 tw:focus:ring-offset-2 tw:focus:ring-[var(--button-focus-ring-color)]',
                    'tw:active:ring-2 tw:active:ring-offset-2 tw:active:ring-[var(--button-focus-ring-color)] tw:active:bg-[var(--button-subtle-active-bg)]',
                    'tw:data-[pressed=true]:bg-[var(--button-text-active-bg)] tw:data-[pressed=true]:text-[var(--button-text-active-text)] tw:data-[pressed=true]:bg-[var(--button-subtle-active-bg)]',
                ],
                text: [
                    'tw:border-0 tw:bg-transparent tw:text-[var(--button-text-text)] tw:p-0! tw:min-h-[unset]!',
                    'tw:hover:underline tw:hover:text-[var(--button-text-hover-text)]',
                    'tw:focus:underline tw:focus:ring-2 tw:focus:ring-offset-5 tw:focus:ring-[var(--button-focus-ring-color)]',
                    'tw:active:ring-2 tw:active:ring-offset-5 tw:active:ring-[var(--button-focus-ring-color)] tw:active:text-[var(--button-text-active-text)]',
                    'tw:data-[pressed=true]:bg-[var(--button-text-active-bg)] tw:data-[pressed=true]:text-[var(--button-text-active-text)] tw:data-[pressed=true]:text-[var(--button-text-active-text)]',
                ],
            },
            color: {
                primary: [
                    'tw:[--button-bg:theme(colors.primary-green)]',
                    'tw:[--button-filled-text:theme(colors.white)]',
                    'tw:[--button-outlined-text:theme(colors.primary-green)]',
                    'tw:[--button-subtle-text:theme(colors.primary-green)]',
                    'tw:[--button-text-text:theme(colors.primary-green)]',
                    'tw:[--button-border:theme(colors.primary-green)]',
                    'tw:[--button-hover-bg:theme(colors.success-2)]',
                    'tw:[--button-hover-border:theme(colors.success-2)]',
                    'tw:[--button-hover-text:theme(colors.white)]',
                    'tw:[--button-subtle-hover-bg:rgb(19_178_114/0.1)]',
                    'tw:[--button-subtle-active-bg:rgb(19_178_114/0.2)]',
                    'tw:[--button-text-hover-text:theme(colors.success-2)]',
                    'tw:[--button-text-active-text:theme(colors.success-2)]',
                    'tw:[--button-focus-ring-color:theme(colors.primary-green)]',
                    'tw:[--button-active-bg:theme(colors.success-2)]',
                    'tw:[--button-active-text:theme(colors.white)]',
                    'tw:[--button-active-border:theme(colors.success-2)]',
                ],
                secondary: [
                    'tw:[--button-bg:theme(colors.info-1)]',
                    'tw:[--button-filled-text:theme(colors.white)]',
                    'tw:[--button-outlined-text:theme(colors.info-1)]',
                    'tw:[--button-subtle-text:theme(colors.info-1)]',
                    'tw:[--button-text-text:theme(colors.info-1)]',
                    'tw:[--button-border:theme(colors.info-1)]',
                    'tw:[--button-hover-bg:theme(colors.info-2)]',
                    'tw:[--button-hover-border:theme(colors.info-2)]',
                    'tw:[--button-hover-text:theme(colors.white)]',
                    'tw:[--button-subtle-hover-bg:rgb(24_119_242/0.1)]',
                    'tw:[--button-subtle-active-bg:rgb(24_119_242/0.2)]',
                    'tw:[--button-text-hover-text:theme(colors.info-2)]',
                    'tw:[--button-text-active-text:theme(colors.info-2)]',
                    'tw:[--button-focus-ring-color:theme(colors.info-1)]',
                    'tw:[--button-active-bg:theme(colors.info-2)]',
                    'tw:[--button-active-text:theme(colors.white)]',
                    'tw:[--button-active-border:theme(colors.info-2)]',
                ],
                warning: [
                    'tw:[--button-bg:theme(colors.warning-1)]',
                    'tw:[--button-filled-text:theme(colors.white)]',
                    'tw:[--button-outlined-text:theme(colors.warning-1)]',
                    'tw:[--button-subtle-text:theme(colors.warning-1)]',
                    'tw:[--button-text-text:theme(colors.warning-1)]',
                    'tw:[--button-border:theme(colors.warning-1)]',
                    'tw:[--button-hover-bg:theme(colors.warning-2)]',
                    'tw:[--button-hover-border:theme(colors.warning-2)]',
                    'tw:[--button-hover-text:theme(colors.white)]',
                    'tw:[--button-subtle-hover-bg:rgb(255_165_0/0.1)]',
                    'tw:[--button-subtle-active-bg:rgb(255_165_0/0.2)]',
                    'tw:[--button-text-hover-text:theme(colors.warning-2)]',
                    'tw:[--button-text-active-text:theme(colors.warning-2)]',
                    'tw:[--button-focus-ring-color:theme(colors.warning-1)]',
                    'tw:[--button-active-bg:theme(colors.warning-2)]',
                    'tw:[--button-active-text:theme(colors.white)]',
                    'tw:[--button-active-border:theme(colors.warning-2)]',
                ],
                error: [
                    'tw:[--button-bg:theme(colors.danger-1)]',
                    'tw:[--button-filled-text:theme(colors.white)]',
                    'tw:[--button-outlined-text:theme(colors.danger-1)]',
                    'tw:[--button-subtle-text:theme(colors.danger-1)]',
                    'tw:[--button-text-text:theme(colors.danger-1)]',
                    'tw:[--button-border:theme(colors.danger-1)]',
                    'tw:[--button-hover-bg:theme(colors.danger-2)]',
                    'tw:[--button-hover-border:theme(colors.danger-2)]',
                    'tw:[--button-hover-text:theme(colors.white)]',
                    'tw:[--button-subtle-hover-bg:rgb(235_33_33/0.1)]',
                    'tw:[--button-subtle-active-bg:rgb(235_33_33/0.2)]',
                    'tw:[--button-text-hover-text:theme(colors.danger-2)]',
                    'tw:[--button-text-active-text:theme(colors.danger-2)]',
                    'tw:[--button-focus-ring-color:theme(colors.danger-1)]',
                    'tw:[--button-active-bg:theme(colors.danger-2)]',
                    'tw:[--button-active-text:theme(colors.white)]',
                    'tw:[--button-active-border:theme(colors.danger-2)]',
                ],
                neutral: [
                    'tw:[--button-bg:theme(colors.light-gray-2)]',
                    'tw:[--button-filled-text:theme(colors.primary-black)]',
                    'tw:[--button-outlined-text:theme(colors.primary-black)]',
                    'tw:[--button-subtle-text:theme(colors.primary-black)]',
                    'tw:[--button-text-text:theme(colors.primary-black)]',
                    'tw:[--button-border:theme(colors.light-gray-2)]',
                    'tw:[--button-hover-bg:theme(colors.light-gray-3)]',
                    'tw:[--button-hover-border:theme(colors.light-gray-3)]',
                    'tw:[--button-hover-text:theme(colors.primary-black)]',
                    'tw:[--button-subtle-hover-bg:theme(colors.light-gray-2/0.5)]',
                    'tw:[--button-subtle-active-bg:theme(colors.light-gray-2)]',
                    'tw:[--button-text-hover-text:theme(colors.primary-black)]',
                    'tw:[--button-text-active-text:theme(colors.primary-black)]',
                    'tw:[--button-focus-ring-color:theme(colors.light-gray-2)]',
                    'tw:[--button-active-bg:theme(colors.light-gray-3)]',
                    'tw:[--button-active-text:theme(colors.primary-black)]',
                    'tw:[--button-active-border:theme(colors.light-gray-3)]',
                ],
                'neutral-2': [
                    'tw:[--button-bg:theme(colors.light-gray-1)]',
                    'tw:[--button-filled-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-outlined-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-subtle-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-text-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-border:theme(colors.light-gray-1)]',
                    'tw:[--button-hover-bg:theme(colors.light-gray-0)]',
                    'tw:[--button-hover-border:theme(colors.light-gray-0)]',
                    'tw:[--button-hover-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-subtle-hover-bg:theme(colors.light-gray-1/0.5)]',
                    'tw:[--button-subtle-active-bg:theme(colors.light-gray-1)]',
                    'tw:[--button-text-hover-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-text-active-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-focus-ring-color:theme(colors.light-gray-1)]',
                    'tw:[--button-active-bg:theme(colors.light-gray-2)]',
                    'tw:[--button-active-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-active-border:theme(colors.light-gray-2)]',
                ],
                'neutral-3': [
                    'tw:[--button-bg:theme(colors.light-gray-1)]',
                    'tw:[--button-filled-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-outlined-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-subtle-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-text-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-border:theme(colors.light-gray-1)]',
                    'tw:[--button-hover-bg:theme(colors.light-gray-1)]',
                    'tw:[--button-hover-border:theme(colors.light-gray-1)]',
                    'tw:[--button-hover-text:theme(colors.info-1)]',
                    'tw:[--button-subtle-hover-bg:theme(colors.light-gray-1/0.5)]',
                    'tw:[--button-subtle-active-bg:theme(colors.light-gray-1)]',
                    'tw:[--button-text-hover-text:theme(colors.dark-gray-2)]',
                    'tw:[--button-text-active-text:theme(colors.info-1)]',
                    'tw:[--button-focus-ring-color:theme(colors.light-gray-1)]',
                    'tw:[--button-active-bg:theme(colors.light-gray-2)]',
                    'tw:[--button-active-text:theme(colors.info-1)]',
                    'tw:[--button-active-border:theme(colors.light-gray-2)]',
                ],
            },
            size: {
                sm: ['tw:px-[12px] tw:py-[6px]', 'tw:min-h-[32px]'],
                icon: ['tw:px-[2px] tw:py-[2px]', 'tw:w-[42px] tw:h-[42px]', 'tw:text-[20px]'],
                lg: ['tw:px-[16px] tw:py-[6px]', 'tw:min-h-[42px]'],
            },
            isActive: {
                true: [
                    'tw:bg-[var(--button-active-bg)] tw:border-[var(--button-active-border)] tw:text-[var(--button-active-text)]',
                    'tw:hover:bg-[var(--button-active-bg)] tw:hover:border-[var(--button-active-border)] tw:hover:text-[var(--button-active-text)]',
                ],
                false: [],
            },
        },
        defaultVariants: {
            variant: 'filled',
            color: 'primary',
            size: 'lg',
            isActive: false,
        },
    },
)

export type ButtonVariants = VariantProps<typeof buttonVariants>
