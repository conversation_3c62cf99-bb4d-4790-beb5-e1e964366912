import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { CheckIcon } from '../../icons/check-icon'

import { DownloadIcon } from '../../icons/download-icon'
import { SortIcon } from '../../icons/sort-icon'
import { TrashIcon } from '../../icons/trash-icon'
import { Menu } from '../menu/menu'
import { MenuItem } from '../menu/menu-item'
import { MenuItemIcon } from '../menu/menu-item-icon'
import { MenuPopover } from '../menu/menu-popover'
import { MenuTrigger } from '../menu/menu-trigger'
import { Button } from './button'
import { ButtonIcon } from './button-icon'

interface ButtonStoryArgs {
    children: React.ReactNode
    variant?: 'filled' | 'outlined' | 'subtle' | 'text'
    color?:
        | 'primary'
        | 'secondary'
        | 'warning'
        | 'error'
        | 'neutral'
        | 'neutral-2'
        | 'neutral-3'
    size?: 'sm' | 'icon' | 'lg'
    isLoading?: boolean
    className?: string
    isActive?: boolean
    isDisabled?: boolean
    type?: 'button' | 'submit' | 'reset'
    onClick?: () => void
}

const meta: Meta<ButtonStoryArgs> = {
    title: 'Components/Button',
    component: Button,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        variant: {
            control: 'select',
            options: ['filled', 'outlined', 'subtle', 'text'],
            description: 'The visual style variant of the button',
        },
        color: {
            control: 'select',
            options: [
                'primary',
                'secondary',
                'warning',
                'error',
                'neutral',
                'neutral-2',
                'neutral-3',
            ],
            description: 'The color scheme of the button',
        },
        size: {
            control: 'select',
            options: ['sm', 'icon', 'lg'],
            description: 'The size of the button',
        },
        isLoading: {
            control: 'boolean',
            description: 'Whether the button shows a loading spinner',
        },
        isDisabled: {
            control: 'boolean',
            description: 'Whether the button is disabled',
        },
        onClick: {
            action: 'clicked',
            description: 'Function called when button is clicked',
        },
        children: {
            control: 'text',
            description: 'The button text content',
        },
    },
}

export default meta
type Story = StoryObj<ButtonStoryArgs>

export const FilledPrimary: Story = {
    args: {
        children: 'Primary Button',
        variant: 'filled',
        color: 'primary',
        onClick: () => console.log('Primary button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Default primary button with green background.',
            },
        },
    },
}

export const FilledSecondary: Story = {
    args: {
        children: 'Secondary Button',
        variant: 'filled',
        color: 'secondary',
        onClick: () => console.log('Secondary button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Secondary button with blue background.',
            },
        },
    },
}

export const FilledWarning: Story = {
    args: {
        children: 'Warning Button',
        variant: 'filled',
        color: 'warning',
        onClick: () => console.log('Warning button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Warning button with orange background.',
            },
        },
    },
}

export const FilledNeutral: Story = {
    args: {
        children: 'Neutral Button',
        variant: 'filled',
        color: 'neutral',
        onClick: () => console.log('Neutral button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Neutral button with gray background.',
            },
        },
    },
}

export const FilledError: Story = {
    args: {
        children: 'Error Button',
        variant: 'filled',
        color: 'error',
        onClick: () => console.log('Error button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Error button with red background for destructive actions.',
            },
        },
    },
}

export const OutlinedPrimary: Story = {
    args: {
        children: 'Outline Button',
        variant: 'outlined',
        color: 'primary',
        onClick: () => console.log('Outline button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Outline button with border and transparent background.',
            },
        },
    },
}

export const OutlinedSecondary: Story = {
    args: {
        children: 'Outline Button',
        variant: 'outlined',
        color: 'secondary',
        onClick: () => console.log('Outline button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Outline button with border and transparent background for secondary color.',
            },
        },
    },
}

export const OutlinedWarning: Story = {
    args: {
        children: 'Outline Button',
        variant: 'outlined',
        color: 'warning',
        onClick: () => console.log('Outline button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Outline button with border and transparent background for warning color.',
            },
        },
    },
}

export const OutlinedNeutral: Story = {
    args: {
        children: 'Outline Button',
        variant: 'outlined',
        color: 'neutral',
        onClick: () => console.log('Outline button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Outline button with border and transparent background for neutral color.',
            },
        },
    },
}

export const OutlinedError: Story = {
    args: {
        children: 'Outline Button',
        variant: 'outlined',
        color: 'error',
        onClick: () => console.log('Outline button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Outline button with border and transparent background for error color.',
            },
        },
    },
}

export const SubtlePrimary: Story = {
    args: {
        children: 'Primary Subtle',
        variant: 'subtle',
        color: 'primary',
        onClick: () => console.log('Primary subtle button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Subtle button with primary color text and no background or border.',
            },
        },
    },
}

export const SubtleSecondary: Story = {
    args: {
        children: 'Secondary Subtle',
        variant: 'subtle',
        color: 'secondary',
        onClick: () => console.log('Secondary subtle button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Subtle button with secondary color text and no background or border.',
            },
        },
    },
}

export const SubtleWarning: Story = {
    args: {
        children: 'Warning Subtle',
        variant: 'subtle',
        color: 'warning',
        onClick: () => console.log('Warning subtle button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Subtle button with warning color text and no background or border.',
            },
        },
    },
}

export const SubtleError: Story = {
    args: {
        children: 'Error Subtle',
        variant: 'subtle',
        color: 'error',
        onClick: () => console.log('Error subtle button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Subtle button with error color text and no background or border.',
            },
        },
    },
}

export const SubtleNeutral: Story = {
    args: {
        children: 'Neutral Subtle',
        variant: 'subtle',
        color: 'neutral',
        onClick: () => console.log('Neutral subtle button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Subtle button with neutral color text and no background or border.',
            },
        },
    },
}

export const TextPrimary: Story = {
    args: {
        children: 'Text Button',
        variant: 'text',
        color: 'primary',
        onClick: () => console.log('Text button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Text button',
            },
        },
    },
}

export const TextSecondary: Story = {
    args: {
        children: 'Text Button',
        variant: 'text',
        color: 'secondary',
        onClick: () => console.log('Text button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Text button with secondary color',
            },
        },
    },
}

export const TextWarning: Story = {
    args: {
        children: 'Text Button',
        variant: 'text',
        color: 'warning',
        onClick: () => console.log('Text button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Text button with warning color',
            },
        },
    },
}

export const TextError: Story = {
    args: {
        children: 'Text Button',
        variant: 'text',
        color: 'error',
        onClick: () => console.log('Text button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Text button with error color',
            },
        },
    },
}

export const TextNeutral: Story = {
    args: {
        children: 'Text Button',
        variant: 'text',
        color: 'neutral',
        onClick: () => console.log('Text button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Text button with neutral color',
            },
        },
    },
}

export const FilledNeutral2: Story = {
    args: {
        children: 'Text Button',
        color: 'neutral-2',
        onClick: () => console.log('Text button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Text button with neutral-2 color',
            },
        },
    },
}

export const Dropdown: Story = {
    render: (args) => (
        <MenuTrigger>
            <Button {...args} color="neutral-3">
                Sort
                <ButtonIcon>
                    <SortIcon />
                </ButtonIcon>
            </Button>
            <MenuPopover>
                <Menu>
                    <MenuItem onAction={() => alert('none')}>
                        <MenuItemIcon>
                            <SortIcon />
                        </MenuItemIcon>
                        <span>None</span>
                    </MenuItem>
                    <MenuItem onAction={() => alert('all-current')}>
                        <MenuItemIcon>
                            <SortIcon />
                        </MenuItemIcon>
                        <span>All (Current Page)</span>
                    </MenuItem>
                    <MenuItem onAction={() => alert('all-filtered')}>
                        <MenuItemIcon>
                            <SortIcon />
                        </MenuItemIcon>
                        <span>All Filtered (All Pages)</span>
                    </MenuItem>
                </Menu>
            </MenuPopover>
        </MenuTrigger>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Dropdown button',
            },
        },
    },
}

export const SizeSmall: Story = {
    args: {
        children: 'Small Button',
        size: 'sm',
        onClick: () => console.log('Small button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Small button (32px height).',
            },
        },
    },
}

export const SizeLarge: Story = {
    args: {
        children: 'Large Button',
        size: 'lg',
        onClick: () => console.log('Large button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Large button (42px height).',
            },
        },
    },
}

export const SizeIcon: Story = {
    args: {
        children: <DownloadIcon />,
        size: 'icon',
        onClick: () => console.log('Icon button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Icon-only button with square dimensions.',
            },
        },
    },
}

export const WithLeftIcon: Story = {
    render: (args) => (
        <Button {...args}>
            <ButtonIcon>
                <CheckIcon />
            </ButtonIcon>
            {args.children}
        </Button>
    ),
    args: {
        children: 'Save Changes',
        onClick: () => console.log('Save button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Button with an icon on the left side.',
            },
        },
    },
}

export const WithRightIcon: Story = {
    render: (args) => (
        <Button {...args}>
            {args.children}
            <ButtonIcon>
                <DownloadIcon />
            </ButtonIcon>
        </Button>
    ),
    args: {
        children: 'Download',
        variant: 'filled',
        color: 'secondary',
        onClick: () => console.log('Download button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Button with an icon on the right side.',
            },
        },
    },
}

export const WithBothIcons: Story = {
    render: (args) => (
        <Button {...args}>
            <ButtonIcon>
                <TrashIcon />
            </ButtonIcon>
            {args.children}
            <ButtonIcon>
                <DownloadIcon />
            </ButtonIcon>
        </Button>
    ),
    args: {
        children: 'Delete Item',
        variant: 'filled',
        color: 'error',
        onClick: () => console.log('Delete button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Button with icons on both sides.',
            },
        },
    },
}

export const Loading: Story = {
    render: (args) => (
        <div className="tw:flex tw:flex-col tw:items-center tw:gap-2">
            <h3>Small</h3>
            <Button size="sm" {...args} />
            <h3>Icon</h3>
            <Button size="icon" {...args} />
            <h3>Large</h3>
            <Button size="lg" {...args} />
        </div>
    ),
    args: {
        children: 'Loading...',
        isLoading: true,
        onClick: () => console.log('Loading button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Button in loading state with spinner animation.',
            },
        },
    },
}

export const Active: Story = {
    render: (args) => (
        <div className="tw:flex tw:flex-col tw:items-center tw:gap-3">
            <div>
                <h3>Filled</h3>
                <div className="tw:flex tw:flex-row tw:items-center tw:gap-2">
                    <Button variant="filled" color="primary" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="filled" color="secondary" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="filled" color="warning" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="filled" color="neutral" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="filled" color="error" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                </div>
            </div>
            <div>
                <h3>Outlined</h3>
                <div className="tw:flex tw:flex-row tw:items-center tw:gap-2">
                    <Button variant="outlined" color="primary" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="outlined" color="secondary" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="outlined" color="warning" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="outlined" color="neutral" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="outlined" color="error" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                </div>
            </div>
            <div>
                <h3>Subtle</h3>
                <div className="tw:flex tw:flex-row tw:items-center tw:gap-2">
                    <Button variant="subtle" color="primary" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="subtle" color="secondary" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="subtle" color="warning" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="subtle" color="neutral" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="subtle" color="error" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                </div>
            </div>
            <div>
                <h3>Text</h3>
                <div className="tw:flex tw:flex-row tw:items-center tw:gap-2">
                    <Button variant="text" color="primary" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="text" color="secondary" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="text" color="warning" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="text" color="neutral" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                    <Button variant="text" color="error" {...args}>
                        <ButtonIcon>
                            <CheckIcon />
                        </ButtonIcon>
                        {args.children}
                    </Button>
                </div>
            </div>
        </div>
    ),
    args: {
        isActive: true,
        children: 'Active Button',
        onClick: () => console.log('Active button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Button in active state.',
            },
        },
    },
}

export const Disabled: Story = {
    args: {
        children: 'Disabled Button',
        isDisabled: true,
        onClick: () => console.log('Disabled button clicked'),
    },
    parameters: {
        docs: {
            description: {
                story: 'Disabled button that cannot be interacted with.',
            },
        },
    },
}
