import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import type React from 'react'
import { useEffect, useState } from 'react'
import { CalendarIcon } from '../../icons/calendar-icon'
import { CheckIcon } from '../../icons/check-icon'
import { CloseIcon } from '../../icons/close-icon'
import { MoneyIcon } from '../../icons/money-icon'
import { SearchIcon } from '../../icons/search-icon'
import { Label } from '../label/label'
import {
    ErrorMessage,
    Input,
    InputAddon,
    InputField,
    InputGroup,
    InputPostfix,
    InputPrefix,
    SupportText,
} from './input'

// Define the args type for the template
interface InputTemplateArgs {
    // InputField props
    label?: string
    supportText?: string
    errorMessage?: string
    required?: boolean
    invalid?: boolean
    success?: boolean
    isDisabled?: boolean
    name?: string
    autoFocus?: boolean
    className?: string
    // Input props
    placeholder?: string
    value?: string
    type?: string
    disabled?: boolean
    onChange?: (value: string) => void
    size?: 'sm' | 'md' | 'lg'
    // Additional props for prefix/postfix
    hasPrefixIcon?: boolean
    hasPostfixIcon?: boolean
    hasPrefixAddon?: boolean
    hasPostfixAddon?: boolean
    prefixIcon?: 'search' | 'calendar' | 'money'
    postfixIcon?: 'check' | 'close'
    prefixAddon?: string
    postfixAddon?: string
}

// Template function that renders InputField with InputGroup and Input and handles all props
const InputTemplate = (args: InputTemplateArgs): React.JSX.Element => {
    const {
        // InputField props
        label,
        supportText,
        errorMessage,
        required,
        invalid,
        success,
        isDisabled,
        name,
        autoFocus,
        className,
        // Input props
        placeholder,
        value: initialValue,
        type,
        disabled,
        size,
        // Additional props for prefix/postfix
        hasPrefixIcon,
        hasPostfixIcon,
        hasPrefixAddon,
        hasPostfixAddon,
        prefixIcon,
        postfixIcon,
        prefixAddon,
        postfixAddon,
    } = args

    const [value, setValue] = useState(initialValue ?? '')

    useEffect(() => {
        setValue(initialValue ?? '')
    }, [initialValue])

    return (
        <InputField
            invalid={invalid}
            success={success}
            isDisabled={isDisabled}
            name={name}
            autoFocus={autoFocus}
            className={className}
        >
            {label && <Label required={required}>{label}</Label>}
            <InputGroup size={size}>
                {hasPrefixIcon && (
                    <InputPrefix>
                        {prefixIcon === 'search' && <SearchIcon />}
                        {prefixIcon === 'calendar' && <CalendarIcon />}
                        {prefixIcon === 'money' && <MoneyIcon />}
                    </InputPrefix>
                )}
                {hasPrefixAddon && <InputPrefix>{prefixAddon}</InputPrefix>}
                <Input
                    placeholder={placeholder}
                    value={value}
                    type={type}
                    disabled={disabled}
                    onChange={setValue}
                    name={name}
                    autoFocus={autoFocus}
                />
                {hasPostfixAddon && <InputPostfix>{postfixAddon}</InputPostfix>}
                {hasPostfixIcon && (
                    <InputPostfix>
                        {postfixIcon === 'check' && <CheckIcon />}
                        {postfixIcon === 'close' && <CloseIcon />}
                    </InputPostfix>
                )}
            </InputGroup>
            {supportText && <SupportText>{supportText}</SupportText>}
            {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
        </InputField>
    )
}

const meta: Meta<typeof InputTemplate> = {
    title: 'Components/Input',
    component: InputTemplate,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        // InputField props
        label: {
            control: 'text',
            description: 'The label text displayed above the input',
        },
        supportText: {
            control: 'text',
            description: 'Help text displayed below the input',
        },
        errorMessage: {
            control: 'text',
            description: 'Error message displayed below the input',
        },
        required: {
            control: 'boolean',
            description: 'Whether the field is required (shows red asterisk)',
        },
        invalid: {
            control: 'boolean',
            description:
                'Whether the input is in an invalid state (shows red border)',
        },
        success: {
            control: 'boolean',
            description:
                'Whether the input is in a success state (shows green border)',
        },
        isDisabled: {
            control: 'boolean',
            description: 'Whether the InputField is disabled',
        },
        name: {
            control: 'text',
            description: 'The name attribute for the input field',
        },
        autoFocus: {
            control: 'boolean',
            description: 'Whether the input should be focused on mount',
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes for the InputField',
        },
        // Input props
        placeholder: {
            control: 'text',
            description: 'The placeholder text for the input',
        },
        value: {
            control: 'text',
            description: 'The value of the input field',
        },
        type: {
            control: 'select',
            options: ['text', 'email', 'password', 'number', 'tel', 'url'],
            description: 'The type of the input field',
        },
        disabled: {
            control: 'boolean',
            description: 'Whether the input is disabled',
        },
        size: {
            control: 'select',
            options: ['sm', 'md', 'lg'],
            description: 'The size of the input field',
        },
        onChange: {
            table: {
                disable: true,
            },
        },
        // Prefix/Postfix controls
        hasPrefixIcon: {
            control: 'boolean',
            description: 'Whether to show a prefix icon',
        },
        hasPostfixIcon: {
            control: 'boolean',
            description: 'Whether to show a postfix icon',
        },
        hasPrefixAddon: {
            control: 'boolean',
            description: 'Whether to show a prefix addon',
        },
        hasPostfixAddon: {
            control: 'boolean',
            description: 'Whether to show a postfix addon',
        },
        prefixIcon: {
            control: 'select',
            options: ['search', 'calendar', 'money'],
            description: 'The type of prefix icon to display',
        },
        postfixIcon: {
            control: 'select',
            options: ['check', 'close'],
            description: 'The type of postfix icon to display',
        },
        prefixAddon: {
            control: 'text',
            description: 'The text for the prefix addon',
        },
        postfixAddon: {
            control: 'text',
            description: 'The text for the postfix addon',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: InputTemplate,
    args: {
        label: 'Email Address',
        supportText: 'We will never share your email with anyone else.',
        placeholder: 'Enter your email...',
        value: '<EMAIL>',
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic input field with label and support text.',
            },
        },
    },
}

export const Small: Story = {
    render: InputTemplate,
    args: {
        label: 'Email Address',
        supportText: 'We will never share your email with anyone else.',
        placeholder: 'Enter your email...',
        value: '<EMAIL>',
        size: 'sm',
    },
    parameters: {
        docs: {
            description: {
                story: 'A small input field (34px height) with label and support text.',
            },
        },
    },
}

export const Medium: Story = {
    render: InputTemplate,
    args: {
        label: 'Email Address',
        supportText: 'We will never share your email with anyone else.',
        placeholder: 'Enter your email...',
        value: '<EMAIL>',
        size: 'md',
    },
    parameters: {
        docs: {
            description: {
                story: 'A medium input field (38px height, default) with label and support text.',
            },
        },
    },
}

export const Large: Story = {
    render: InputTemplate,
    args: {
        label: 'Email Address',
        supportText: 'We will never share your email with anyone else.',
        placeholder: 'Enter your email...',
        value: '<EMAIL>',
        size: 'lg',
    },
    parameters: {
        docs: {
            description: {
                story: 'A large input field (42px height) with label and support text.',
            },
        },
    },
}

export const SizeComparison: Story = {
    render: () => (
        <div className="tw:flex tw:flex-col tw:gap-6">
            <InputTemplate
                label="Small Input (34px)"
                placeholder="Enter your email..."
                value="<EMAIL>"
                size="sm"
            />
            <InputTemplate
                label="Medium Input (38px) - Default"
                placeholder="Enter your email..."
                value="<EMAIL>"
                size="md"
            />
            <InputTemplate
                label="Large Input (42px)"
                placeholder="Enter your email..."
                value="<EMAIL>"
                size="lg"
            />
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Comparison of all three input sizes: Small (34px), Medium (38px), and Large (42px).',
            },
        },
    },
}

export const RTL: Story = {
    render: InputTemplate,
    args: {
        label: 'البحث',
        supportText: 'ابحث في المنتجات والخدمات.',
        placeholder: 'ابحث هنا...',
        value: 'كلمة البحث',
        hasPrefixIcon: true,
        hasPostfixAddon: true,
        prefixIcon: 'search',
        postfixAddon: 'بحث',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with RTL (right-to-left) text direction support.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}

export const WithPlaceholder: Story = {
    render: InputTemplate,
    args: {
        label: 'Email Address',
        placeholder: 'Enter your email address...',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with placeholder text.',
            },
        },
    },
}

export const Email: Story = {
    render: InputTemplate,
    args: {
        label: 'Email Address',
        supportText: 'We will use this email to send you important updates.',
        type: 'email',
        placeholder: '<EMAIL>',
        value: '<EMAIL>',
    },
    parameters: {
        docs: {
            description: {
                story: 'Email input field with email validation.',
            },
        },
    },
}

export const Password: Story = {
    render: InputTemplate,
    args: {
        label: 'Password',
        supportText: 'Your password must be at least 8 characters long.',
        type: 'password',
        placeholder: 'Enter your password',
        value: 'secretpassword',
    },
    parameters: {
        docs: {
            description: {
                story: 'Password input field with hidden text.',
            },
        },
    },
}

export const Disabled: Story = {
    render: InputTemplate,
    args: {
        label: 'Disabled Input',
        supportText: 'This input is disabled.',
        placeholder: 'Disabled input...',
        value: 'Cannot edit this',
        disabled: true,
        isDisabled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Disabled input field that cannot be interacted with.',
            },
        },
    },
}

export const WithPrefixIcon: Story = {
    render: InputTemplate,
    args: {
        label: 'Search',
        supportText: 'Search for products, categories, or brands.',
        placeholder: 'Search...',
        value: 'search term',
        hasPrefixIcon: true,
        prefixIcon: 'search',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with a search icon on the left side.',
            },
        },
    },
}

export const WithPostfixIcon: Story = {
    render: InputTemplate,
    args: {
        label: 'Email',
        supportText: 'We will send you a confirmation email.',
        placeholder: 'Enter your email',
        value: '<EMAIL>',
        hasPostfixIcon: true,
        postfixIcon: 'check',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with a check icon on the right side.',
            },
        },
    },
}

export const WithPrefixAddon: Story = {
    render: InputTemplate,
    args: {
        label: 'Website URL',
        supportText: 'Enter your website address.',
        placeholder: 'example.com',
        value: 'mysite',
        hasPrefixAddon: true,
        prefixAddon: 'https://',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with a prefix addon showing the protocol.',
            },
        },
    },
}

export const WithPostfixAddon: Story = {
    render: InputTemplate,
    args: {
        label: 'Price',
        supportText: 'Enter the price in US dollars.',
        placeholder: '0.00',
        value: '99.99',
        hasPostfixAddon: true,
        postfixAddon: 'USD',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with a currency addon on the right side.',
            },
        },
    },
}

export const WithBothIcons: Story = {
    render: InputTemplate,
    args: {
        label: 'Amount',
        supportText: 'Enter the amount to transfer.',
        placeholder: '0.00',
        value: '150.00',
        hasPrefixIcon: true,
        hasPostfixIcon: true,
        prefixIcon: 'money',
        postfixIcon: 'check',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with icons on both sides.',
            },
        },
    },
}

export const WithError: Story = {
    render: InputTemplate,
    args: {
        label: 'Email',
        errorMessage: 'Please enter a valid email address.',
        invalid: true,
        placeholder: 'Invalid input...',
        value: 'invalid-email',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with an error message and red border styling.',
            },
        },
    },
}

export const WithSuccess: Story = {
    render: InputTemplate,
    args: {
        label: 'Email',
        supportText: 'Email address is valid and available.',
        success: true,
        placeholder: 'Valid input...',
        value: '<EMAIL>',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with success state styling (green border and support text).',
            },
        },
    },
}

export const Required: Story = {
    render: InputTemplate,
    args: {
        label: 'Email Address',
        supportText: 'This field is required to create your account.',
        required: true,
        placeholder: 'Enter your email',
        value: '<EMAIL>',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with a required indicator (red asterisk) in the label.',
            },
        },
    },
}

export const WithIconAndAddon: Story = {
    render: InputTemplate,
    args: {
        label: 'Birth Date',
        supportText: 'Enter your date of birth.',
        placeholder: 'MM/DD/YYYY',
        value: '12/25/1990',
        hasPrefixIcon: true,
        hasPostfixAddon: true,
        prefixIcon: 'calendar',
        postfixAddon: 'Date',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with an icon on the left and addon on the right.',
            },
        },
    },
}

export const WithAddonAndIcon: Story = {
    render: InputTemplate,
    args: {
        label: 'Username',
        supportText: 'Choose a unique username.',
        placeholder: 'username',
        value: 'john_doe',
        hasPrefixAddon: true,
        hasPostfixIcon: true,
        prefixAddon: '@',
        postfixIcon: 'check',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with an addon on the left and icon on the right.',
            },
        },
    },
}

export const WithMultipleAddons: Story = {
    render: InputTemplate,
    args: {
        label: 'Price',
        supportText: 'Enter the price in US dollars.',
        placeholder: '0.00',
        value: '99.99',
        hasPrefixAddon: true,
        hasPostfixAddon: true,
        prefixAddon: '$',
        postfixAddon: 'USD',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with addons on both sides.',
            },
        },
    },
}

export const DisabledWithIcon: Story = {
    render: InputTemplate,
    args: {
        label: 'Disabled with Icon',
        supportText: 'This input is disabled but still shows the icon.',
        placeholder: 'Disabled with icon...',
        value: 'Cannot edit',
        disabled: true,
        isDisabled: true,
        hasPrefixIcon: true,
        prefixIcon: 'search',
    },
    parameters: {
        docs: {
            description: {
                story: 'Disabled input with an icon.',
            },
        },
    },
}

export const InvalidWithIcon: Story = {
    render: InputTemplate,
    args: {
        label: 'Search',
        errorMessage: 'No results found. Please try a different search term.',
        invalid: true,
        placeholder: 'Invalid search...',
        value: 'no results',
        hasPrefixIcon: true,
        hasPostfixIcon: true,
        prefixIcon: 'search',
        postfixIcon: 'close',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with error message and icons on both sides.',
            },
        },
    },
}

export const InvalidWithAddon: Story = {
    render: InputTemplate,
    args: {
        label: 'Email',
        errorMessage: 'Please enter a valid email address.',
        invalid: true,
        placeholder: 'invalid-email',
        value: 'invalid@',
        hasPrefixAddon: true,
        hasPostfixAddon: true,
        prefixAddon: '@',
        postfixAddon: 'domain.com',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with error message and addons on both sides.',
            },
        },
    },
}

export const WithSelectAddon: Story = {
    render: (args: InputTemplateArgs) => (
        <InputField
            invalid={args.invalid}
            success={args.success}
            isDisabled={args.isDisabled}
            name={args.name}
            autoFocus={args.autoFocus}
            className={args.className}
        >
            {args.label && <Label required={args.required}>{args.label}</Label>}
            <InputGroup size={args.size}>
                <InputAddon>
                    <select className="tw:h-full tw:max-h-full tw:border-0 tw:border-light-gray-3 tw:border-e tw:bg-transparent tw:px-2 tw:font-medium tw:text-light-gray-7 tw:text-xs tw:outline-none">
                        <option value="+1">+1</option>
                        <option value="+44">+44</option>
                        <option value="+33">+33</option>
                        <option value="+49">+49</option>
                        <option value="+81">+81</option>
                    </select>
                </InputAddon>
                <Input
                    placeholder={args.placeholder}
                    value={args.value}
                    type={args.type}
                    disabled={args.disabled}
                    onChange={args.onChange}
                    name={args.name}
                    autoFocus={args.autoFocus}
                />
            </InputGroup>
            {args.supportText && <SupportText>{args.supportText}</SupportText>}
            {args.errorMessage && (
                <ErrorMessage>{args.errorMessage}</ErrorMessage>
            )}
        </InputField>
    ),
    args: {
        label: 'Phone Number',
        supportText: 'Enter your phone number with country code.',
        placeholder: '(*************',
        value: '5551234567',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with a select dropdown as an addon for country codes.',
            },
        },
    },
}

export const WithSelectPostfixAddon: Story = {
    render: (args: InputTemplateArgs) => (
        <InputField
            invalid={args.invalid}
            success={args.success}
            isDisabled={args.isDisabled}
            name={args.name}
            autoFocus={args.autoFocus}
            className={args.className}
        >
            {args.label && <Label required={args.required}>{args.label}</Label>}
            <InputGroup size={args.size}>
                <Input
                    placeholder={args.placeholder}
                    value={args.value}
                    type={args.type}
                    disabled={args.disabled}
                    onChange={args.onChange}
                    name={args.name}
                    autoFocus={args.autoFocus}
                />
                <InputPostfix>
                    <select className="tw:h-full tw:border-0 tw:border-light-gray-3 tw:border-s tw:bg-transparent tw:px-2 tw:font-medium tw:text-light-gray-7 tw:text-xs tw:outline-none">
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                        <option value="JPY">JPY</option>
                        <option value="CAD">CAD</option>
                    </select>
                </InputPostfix>
            </InputGroup>
            {args.supportText && <SupportText>{args.supportText}</SupportText>}
            {args.errorMessage && (
                <ErrorMessage>{args.errorMessage}</ErrorMessage>
            )}
        </InputField>
    ),
    args: {
        label: 'Amount',
        supportText: 'Enter the amount with currency selection.',
        placeholder: '0.00',
        value: '99.99',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input with a select dropdown as a postfix addon for currency selection.',
            },
        },
    },
}

export const DisabledWithRequired: Story = {
    render: InputTemplate,
    args: {
        label: 'Required but Disabled',
        supportText: 'This field is required but currently disabled.',
        required: true,
        disabled: true,
        isDisabled: true,
        placeholder: 'Cannot edit',
        value: 'Disabled value',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input that is both required and disabled. The required indicator is shown, but the field cannot be edited.',
            },
        },
    },
}

export const InputWithoutGroup: Story = {
    render: (args: InputTemplateArgs) => {
        const {
            // InputField props
            label,
            supportText,
            errorMessage,
            required,
            invalid,
            success,
            isDisabled,
            name,
            autoFocus,
            className,
            // Input props
            placeholder,
            value: initialValue,
            type,
            disabled,
            size,
        } = args

        const [value, setValue] = useState(initialValue ?? '')

        useEffect(() => {
            setValue(initialValue ?? '')
        }, [initialValue])

        return (
            <InputField
                invalid={invalid}
                success={success}
                isDisabled={isDisabled}
                name={name}
                autoFocus={autoFocus}
                className={className}
            >
                {label && <Label required={required}>{label}</Label>}
                <Input
                    placeholder={placeholder}
                    value={value}
                    type={type}
                    disabled={disabled}
                    onChange={setValue}
                    name={name}
                    autoFocus={autoFocus}
                    size={size}
                />
                {supportText && <SupportText>{supportText}</SupportText>}
                {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
            </InputField>
        )
    },
    args: {
        label: 'Email Address',
        supportText:
            'This Input is not wrapped in InputGroup, notice the red background.',
        placeholder: 'Enter your email...',
        value: '<EMAIL>',
    },
    parameters: {
        docs: {
            description: {
                story: 'Input component used without InputGroup wrapper shows a red background to indicate incorrect usage. This demonstrates the visual feedback system for proper component composition.',
            },
        },
    },
}
