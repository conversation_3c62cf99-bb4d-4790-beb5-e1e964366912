import type { JSX, ReactNode } from 'react'
import {
    Input as AriaInput,
    FieldError,
    Text,
    TextField,
} from 'react-aria-components'
import { cn } from '../../utils/cn'

export interface InputFieldProps {
    children: ReactNode
    className?: string
    autoFocus?: boolean
    isDisabled?: boolean
    name?: string
    invalid?: boolean
    success?: boolean
}

export function InputField({
    children,
    className = '',
    autoFocus,
    isDisabled,
    name,
    invalid = false,
    success = false,
}: InputFieldProps): JSX.Element {
    return (
        <TextField
            autoFocus={autoFocus}
            isDisabled={isDisabled}
            name={name}
            className={cn('tw:group tw:flex tw:w-full tw:flex-col tw:gap-2', className)}
            isInvalid={invalid}
            data-success={success}
        >
            {children}
        </TextField>
    )
}

export interface InputGroupProps {
    children: ReactNode
    className?: string
    size?: 'sm' | 'md' | 'lg' | 'xl'
    autoSize?: boolean
}

export function InputGroup({
    children,
    className = '',
    size = 'lg',
    autoSize,
}: InputGroupProps): JSX.Element {
    const sizeClasses = {
        sm: 'tw:min-h-[34px] tw:max-h-[34px]',
        md: 'tw:min-h-[38px] tw:max-h-[38px]',
        lg: 'tw:min-h-[42px] tw:max-h-[42px]',
        xl: 'tw:min-h-[80px] tw:max-h-[80px]',
    }

    return (
        <div
            className={cn(
                'tw:group/input-group tw:relative tw:flex tw:w-full tw:items-stretch tw:overflow-hidden tw:rounded-xs tw:border-[1px] tw:bg-white tw:transition tw:focus-within:border-info-1',
                'tw:border-[#E4EBF2]',
                'tw:group-invalid:border-danger-1 tw:group-invalid:focus-within:border-danger-1',
                'tw:group-data-[success=true]:border-success-2 tw:group-data-[success=true]:focus-within:border-success-2',
                'tw:[&>input:not(:first-child)]:ps-2 tw:[&>input:not(:last-child)]:pe-2',
                'tw:[&>textarea:not(:first-child)]:ps-2 tw:[&>textarea:not(:last-child)]:pe-2',
                'tw:hover:ring-2 tw:hover:ring-[#D8E8FD]',
                'tw:group-invalid:hover:ring-2 tw:group-invalid:hover:ring-danger-1/50',
                'tw:group-data-[success=true]:hover:ring-2 tw:group-data-[success=true]:hover:ring-success-2/50',
                'tw:group-disabled:cursor-not-allowed tw:group-disabled:bg-light-gray-1',
                'tw:group-disabled:hover:ring-0',
                // Add padding-end when Select component is present (targets data-select attribute)
                'tw:[&:has([data-select])]:pe-[34px]',
                sizeClasses[size],
                autoSize && 'tw:max-h-[unset]',
                className,
            )}
            data-group="true"
            data-group-size={size}
        >
            {children}
        </div>
    )
}

export interface InputProps {
    placeholder?: string
    className?: string
    type?: string
    value?: string
    onChange?: (value: string) => void
    disabled?: boolean
    name?: string
    autoFocus?: boolean
    size?: 'sm' | 'md' | 'lg'
}

export function Input({
    className = '',
    onChange,
    placeholder,
    type,
    value,
    disabled,
    name,
    autoFocus,
    size = 'md',
}: InputProps): JSX.Element {
    const sizeClasses = {
        sm: 'tw:min-h-[34px]',
        md: 'tw:min-h-[38px]',
        lg: 'tw:min-h-[42px]',
    }

    return (
        <AriaInput
            className={cn(
                // Base styles
                'tw:flex-1 tw:border-0 tw:bg-transparent tw:px-4 tw:text-base! tw:text-primary-black tw:leading-normal tw:outline-none tw:placeholder:text-light-gray-4 tw:group-disabled:cursor-not-allowed',
                'tw:disabled:bg-transparent tw:disabled:text-light-gray-4',

                // Standalone styles
                'tw:rounded-xs tw:border-[1px] tw:bg-white tw:transition tw:focus-within:border-info-1',
                'tw:border-[#E4EBF2]',
                'tw:group-invalid:border-danger-1 tw:group-invalid:focus-within:border-danger-1',
                'tw:group-data-[success=true]:border-success-2 tw:group-data-[success=true]:focus-within:border-success-2',
                'tw:hover:ring-2 tw:hover:ring-[#D8E8FD]',
                'tw:group-invalid:hover:ring-2 tw:group-invalid:hover:ring-danger-1/50',
                'tw:group-data-[success=true]:hover:ring-2 tw:group-data-[success=true]:hover:ring-success-2/50',
                'tw:group-disabled:cursor-not-allowed tw:group-disabled:bg-light-gray-1',
                'tw:group-disabled:hover:ring-0',

                // Reset styles when inside an InputGroup
                'tw:group-data-[group=true]/input-group:min-h-full! tw:group-data-[group=true]/input-group:rounded-none tw:group-data-[group=true]/input-group:border-none tw:group-data-[group=true]/input-group:ring-0 tw:group-data-[group=true]/input-group:group-data-[success=true]:hover:ring-0 tw:group-data-[group=true]/input-group:hover:ring-0',
                sizeClasses[size],
                className,
            )}
            onChange={onChange ? (e) => onChange(e.target.value) : undefined}
            placeholder={placeholder}
            type={type}
            value={value}
            disabled={disabled}
            name={name}
            autoFocus={autoFocus}
        />
    )
}

export interface InputPreAndPostfixProps {
    children: ReactNode
    className?: string
}

export function InputPrefix({
    children,
    className = '',
}: InputPreAndPostfixProps): JSX.Element {
    return (
        <span
            className={cn(
                'tw:flex tw:min-h-full tw:items-center tw:ps-4 tw:pe-0 tw:text-light-gray-4',
                className,
            )}
        >
            {children}
        </span>
    )
}

export function InputPostfix({
    children,
    className = '',
}: InputPreAndPostfixProps): JSX.Element {
    return (
        <span
            className={cn(
                'tw:flex tw:min-h-full tw:items-center tw:ps-0 tw:pe-4 tw:text-light-gray-4 tw:peer-data-[select=true]:pe-2',
                className,
            )}
        >
            {children}
        </span>
    )
}

export interface InputAddonProps {
    children: ReactNode
    className?: string
}

export function InputAddon({
    children,
    className = '',
}: InputAddonProps): JSX.Element {
    return (
        <div
            className={cn(
                'tw:flex tw:min-h-full tw:items-center tw:font-medium tw:text-light-gray-7 tw:text-xs',
                className,
            )}
        >
            {children}
        </div>
    )
}

export interface SupportTextProps {
    children: ReactNode
    className?: string
}

export function SupportText({
    children,
    className = '',
}: SupportTextProps): JSX.Element {
    return (
        <Text
            slot="description"
            className={cn(
                'tw:font-normal tw:text-annotation tw:text-light-gray-4 tw:group-data-[success=true]:text-success-2',
                className,
            )}
        >
            {children}
        </Text>
    )
}

export interface ErrorMessageProps {
    children: ReactNode
    className?: string
}

export function ErrorMessage({
    children,
    className = '',
}: ErrorMessageProps): JSX.Element {
    return (
        <FieldError
            className={cn(
                'tw:font-normal tw:text-annotation tw:text-danger-1',
                className,
            )}
        >
            {children}
        </FieldError>
    )
}
