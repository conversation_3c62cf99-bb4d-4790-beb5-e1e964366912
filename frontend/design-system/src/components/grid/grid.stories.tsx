import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Col, Row } from './grid'

const meta: Meta<typeof Row> = {
    title: 'Layout/Grid',
    component: Row,
    tags: ['autodocs'],
    parameters: {
        layout: 'padded',
        docs: {
            description: {
                component:
                    'A flexible grid system based on CSS Flexbox with 12-column layout, responsive breakpoints, and customizable gaps.',
            },
        },
    },
    argTypes: {
        gap: {
            control: { type: 'select' },
            options: [0, 1, 2, 3, 4, 5, 6, 8],
            description:
                'Sets both horizontal and vertical gap between columns',
        },
        gapX: {
            control: { type: 'select' },
            options: [0, 1, 2, 3, 4, 5, 6, 8],
            description: 'Sets horizontal gap between columns',
        },
        gapY: {
            control: { type: 'select' },
            options: [0, 1, 2, 3, 4, 5, 6, 8],
            description: 'Sets vertical gap between rows',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

// Demo content components
const DemoBox = ({
    children,
    className = '',
    variant = 'primary',
}: {
    children: React.ReactNode
    className?: string
    variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'info'
}) => {
    const variants = {
        primary: 'tw:border-blue-300 tw:bg-blue-100 tw:text-blue-800',
        secondary: 'tw:border-gray-300 tw:bg-gray-100 tw:text-gray-800',
        success: 'tw:border-green-300 tw:bg-green-100 tw:text-green-800',
        warning: 'tw:border-yellow-300 tw:bg-yellow-100 tw:text-yellow-800',
        info: 'tw:border-purple-300 tw:bg-purple-100 tw:text-purple-800',
    }

    return (
        <div
            className={`tw:rounded tw:border-2 tw:p-4 tw:text-center tw:font-medium ${variants[variant]} ${className}`}
        >
            {children}
        </div>
    )
}

// Basic Examples
export const Overview: Story = {
    render: (args) => (
        <div className="tw:space-y-8">
            <div>
                <h2 className="tw:mb-4 tw:font-bold tw:text-2xl">
                    Grid System Overview
                </h2>
                <p className="tw:mb-4 tw:text-gray-600">
                    Our grid system uses a series of rows and columns to layout
                    and align content. It's built with flexbox and is fully
                    responsive with 6 breakpoints.
                </p>
                <div className="tw:space-y-4">
                    <div>
                        <h3 className="tw:mb-2 tw:font-semibold tw:text-lg">
                            Breakpoints
                        </h3>
                        <ul className="tw:list-disc tw:pl-6 tw:text-gray-600 tw:text-sm">
                            <li>
                                <strong>xs</strong>: 0px and up (extra small
                                devices)
                            </li>
                            <li>
                                <strong>sm</strong>: 640px and up (small
                                devices)
                            </li>
                            <li>
                                <strong>md</strong>: 768px and up (medium
                                devices)
                            </li>
                            <li>
                                <strong>lg</strong>: 1024px and up (large
                                devices)
                            </li>
                            <li>
                                <strong>xl</strong>: 1280px and up (extra large
                                devices)
                            </li>
                            <li>
                                <strong>2xl</strong>: 1536px and up (2x extra
                                large devices)
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div>
                <h3 className="tw:mb-4 tw:font-semibold tw:text-lg">Basic Example</h3>
                <Row {...args}>
                    <Col lg={12}>
                        <DemoBox>12 columns</DemoBox>
                    </Col>
                    <Col lg={6}>
                        <DemoBox>6 columns</DemoBox>
                    </Col>
                    <Col lg={6}>
                        <DemoBox>6 columns</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>4 columns</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>4 columns</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>4 columns</DemoBox>
                    </Col>
                </Row>
            </div>
        </div>
    ),
}

// Fill Width Columns
export const FillWidthColumns: Story = {
    render: () => (
        <div className="tw:space-y-8">
            <div>
                <h2 className="tw:mb-4 tw:font-bold tw:text-xl">Fill Width Columns</h2>
                <p className="tw:mb-4 tw:text-gray-600">
                    Use the{' '}
                    <code className="tw:rounded tw:bg-gray-100 tw:px-1">fill</code> prop
                    to create fill-width columns that automatically distribute
                    available space.
                </p>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Two Fill Columns</h3>
                <Row gap={4}>
                    <Col lg="fill">
                        <DemoBox>Fill width</DemoBox>
                    </Col>
                    <Col lg="fill">
                        <DemoBox>Fill width</DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Three Fill Columns</h3>
                <Row gap={4}>
                    <Col lg="fill">
                        <DemoBox>Fill width</DemoBox>
                    </Col>
                    <Col lg="fill">
                        <DemoBox>Fill width</DemoBox>
                    </Col>
                    <Col lg="fill">
                        <DemoBox>Fill width</DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">
                    Mixed: Fill + Fixed Width
                </h3>
                <Row gap={4}>
                    <Col lg="fill">
                        <DemoBox>Fill width</DemoBox>
                    </Col>
                    <Col lg={6}>
                        <DemoBox>Fixed 6 columns</DemoBox>
                    </Col>
                    <Col lg="fill">
                        <DemoBox>Fill width</DemoBox>
                    </Col>
                </Row>
            </div>
        </div>
    ),
}

// Auto Layout Columns
export const AutoLayoutColumns: Story = {
    render: () => (
        <div className="tw:space-y-8">
            <div>
                <h2 className="tw:mb-4 tw:font-bold tw:text-xl">Auto Layout Columns</h2>
                <p className="tw:mb-4 tw:text-gray-600">
                    Columns with{' '}
                    <code className="tw:rounded tw:bg-gray-100 tw:px-1">auto</code>{' '}
                    sizing take up only the space their content needs.
                </p>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Auto-sizing Columns</h3>
                <Row gap={4}>
                    <Col lg="auto">
                        <DemoBox>Auto width</DemoBox>
                    </Col>
                    <Col lg="auto">
                        <DemoBox>Auto width with longer content</DemoBox>
                    </Col>
                    <Col lg="auto">
                        <DemoBox>Auto</DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">
                    Mixed: Auto + Fill + Fixed
                </h3>
                <Row gap={4}>
                    <Col lg="auto">
                        <DemoBox>Auto</DemoBox>
                    </Col>
                    <Col lg="fill">
                        <DemoBox>Fill (fills remaining space)</DemoBox>
                    </Col>
                    <Col lg={3}>
                        <DemoBox>Fixed 3</DemoBox>
                    </Col>
                </Row>
            </div>
        </div>
    ),
}

// Responsive Breakpoints
export const ResponsiveBreakpoints: Story = {
    render: () => (
        <div className="tw:space-y-8">
            <div>
                <h2 className="tw:mb-4 tw:font-bold tw:text-xl">
                    Responsive Breakpoints
                </h2>
                <p className="tw:mb-4 tw:text-gray-600">
                    Resize your browser to see how columns adapt at different
                    breakpoints.
                </p>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Stacked to Horizontal</h3>
                <p className="tw:mb-4 tw:text-gray-500 tw:text-sm">
                    Stack on small screens, horizontal on medium screens and up
                </p>
                <Row gap={4}>
                    <Col md={6}>
                        <DemoBox>
                            <div className="tw:text-xs">xs: full width</div>
                            <div className="tw:text-xs">md: 6 columns</div>
                        </DemoBox>
                    </Col>
                    <Col md={6}>
                        <DemoBox>
                            <div className="tw:text-xs">xs: full width</div>
                            <div className="tw:text-xs">md: 6 columns</div>
                        </DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">
                    Complex Responsive Layout
                </h3>
                <Row gap={4}>
                    <Col xs={12} sm={6} md={4} lg={3} xl={2}>
                        <DemoBox variant="info">
                            <div className="tw:text-xs">xs:12 sm:6</div>
                            <div className="tw:text-xs">md:4 lg:3 xl:2</div>
                        </DemoBox>
                    </Col>
                    <Col xs={12} sm={6} md={4} lg={3} xl={2}>
                        <DemoBox variant="info">
                            <div className="tw:text-xs">xs:12 sm:6</div>
                            <div className="tw:text-xs">md:4 lg:3 xl:2</div>
                        </DemoBox>
                    </Col>
                    <Col xs={12} sm={6} md={4} lg={3} xl={2}>
                        <DemoBox variant="info">
                            <div className="tw:text-xs">xs:12 sm:6</div>
                            <div className="tw:text-xs">md:4 lg:3 xl:2</div>
                        </DemoBox>
                    </Col>
                    <Col xs={12} sm={6} md={4} lg={3} xl={2}>
                        <DemoBox variant="info">
                            <div className="tw:text-xs">xs:12 sm:6</div>
                            <div className="tw:text-xs">md:4 lg:3 xl:2</div>
                        </DemoBox>
                    </Col>
                    <Col xs={12} sm={6} md={4} lg={3} xl={2}>
                        <DemoBox variant="info">
                            <div className="tw:text-xs">xs:12 sm:6</div>
                            <div className="tw:text-xs">md:4 lg:3 xl:2</div>
                        </DemoBox>
                    </Col>
                    <Col xs={12} sm={6} md={4} lg={3} xl={2}>
                        <DemoBox variant="info">
                            <div className="tw:text-xs">xs:12 sm:6</div>
                            <div className="tw:text-xs">md:4 lg:3 xl:2</div>
                        </DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Mobile First Approach</h3>
                <Row gap={4}>
                    <Col xs={12} lg={8}>
                        <DemoBox variant="success">
                            <div className="tw:text-xs">Main Content</div>
                            <div className="tw:text-xs">
                                xs: full width, lg: 8 columns
                            </div>
                        </DemoBox>
                    </Col>
                    <Col xs={12} lg={4}>
                        <DemoBox variant="warning">
                            <div className="tw:text-xs">Sidebar</div>
                            <div className="tw:text-xs">
                                xs: full width, lg: 4 columns
                            </div>
                        </DemoBox>
                    </Col>
                </Row>
            </div>
        </div>
    ),
}

// Gap and Spacing
export const GapAndSpacing: Story = {
    render: () => (
        <div className="tw:space-y-8">
            <div>
                <h2 className="tw:mb-4 tw:font-bold tw:text-xl">Gap and Spacing</h2>
                <p className="tw:mb-4 tw:text-gray-600">
                    Control spacing between columns using gap props.
                </p>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">No Gap (gap=0)</h3>
                <Row gap={0}>
                    <Col lg={4}>
                        <DemoBox>No gap</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>No gap</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>No gap</DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Small Gap (gap=2)</h3>
                <Row gap={2}>
                    <Col lg={4}>
                        <DemoBox>Small gap</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>Small gap</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>Small gap</DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Default Gap (gap=4)</h3>
                <Row gap={4}>
                    <Col lg={4}>
                        <DemoBox>Default gap</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>Default gap</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>Default gap</DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Large Gap (gap=8)</h3>
                <Row gap={8}>
                    <Col lg={4}>
                        <DemoBox>Large gap</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>Large gap</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>Large gap</DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">
                    Different Horizontal and Vertical Gaps
                </h3>
                <p className="tw:mb-4 tw:text-gray-500 tw:text-sm">
                    gapX=6 (horizontal), gapY=2 (vertical)
                </p>
                <Row gapX={6} gapY={2}>
                    <Col lg={6}>
                        <DemoBox>Row 1, Col 1</DemoBox>
                    </Col>
                    <Col lg={6}>
                        <DemoBox>Row 1, Col 2</DemoBox>
                    </Col>
                    <Col lg={6}>
                        <DemoBox>Row 2, Col 1</DemoBox>
                    </Col>
                    <Col lg={6}>
                        <DemoBox>Row 2, Col 2</DemoBox>
                    </Col>
                </Row>
            </div>
        </div>
    ),
}

// Nesting
export const NestedGrids: Story = {
    render: () => (
        <div className="tw:space-y-8">
            <div>
                <h2 className="tw:mb-4 tw:font-bold tw:text-xl">Nested Grids</h2>
                <p className="tw:mb-4 tw:text-gray-600">
                    Nest grids within columns to create complex layouts.
                </p>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Basic Nesting</h3>
                <Row gap={4}>
                    <Col lg={8}>
                        <DemoBox className="!tw:p-2" variant="secondary">
                            <div className="tw:mb-2 tw:font-semibold tw:text-sm">
                                Outer Column (8/12)
                            </div>
                            <Row gap={2}>
                                <Col lg={6}>
                                    <DemoBox
                                        variant="success"
                                        className="tw:text-sm"
                                    >
                                        Nested 6/12
                                    </DemoBox>
                                </Col>
                                <Col lg={6}>
                                    <DemoBox
                                        variant="success"
                                        className="tw:text-sm"
                                    >
                                        Nested 6/12
                                    </DemoBox>
                                </Col>
                            </Row>
                        </DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox variant="secondary">
                            Outer Column (4/12)
                        </DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Complex Nesting</h3>
                <Row gap={4}>
                    <Col lg={6}>
                        <DemoBox className="!tw:p-2" variant="secondary">
                            <div className="tw:mb-2 tw:font-semibold tw:text-sm">
                                Level 1 (6/12)
                            </div>
                            <Row gap={2}>
                                <Col lg={8}>
                                    <DemoBox
                                        className="!tw:p-2 tw:text-sm"
                                        variant="success"
                                    >
                                        <div className="tw:mb-1 tw:font-medium">
                                            Level 2 (8/12)
                                        </div>
                                        <Row gap={1}>
                                            <Col lg={6}>
                                                <DemoBox
                                                    variant="warning"
                                                    className="tw:py-2 tw:text-xs"
                                                >
                                                    Level 3 (6/12)
                                                </DemoBox>
                                            </Col>
                                            <Col lg={6}>
                                                <DemoBox
                                                    variant="warning"
                                                    className="tw:py-2 tw:text-xs"
                                                >
                                                    Level 3 (6/12)
                                                </DemoBox>
                                            </Col>
                                        </Row>
                                    </DemoBox>
                                </Col>
                                <Col lg={4}>
                                    <DemoBox
                                        variant="success"
                                        className="tw:text-sm"
                                    >
                                        Level 2 (4/12)
                                    </DemoBox>
                                </Col>
                            </Row>
                        </DemoBox>
                    </Col>
                    <Col lg={6}>
                        <DemoBox variant="secondary">Level 1 (6/12)</DemoBox>
                    </Col>
                </Row>
            </div>
        </div>
    ),
}

// Alignment and Ordering
export const AlignmentAndOrdering: Story = {
    render: () => (
        <div className="tw:space-y-8">
            <div>
                <h2 className="tw:mb-4 tw:font-bold tw:text-xl">
                    Alignment and Ordering
                </h2>
                <p className="tw:mb-4 tw:text-gray-600">
                    Use utility classes to control alignment and ordering of
                    columns.
                </p>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Vertical Alignment</h3>
                <p className="tw:mb-4 tw:text-gray-500 tw:text-sm">
                    Using flex utility classes on Row component
                </p>
                <Row gap={4} className="tw:min-h-32 tw:items-center">
                    <Col lg={4}>
                        <DemoBox>Centered</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox className="tw:py-8">Taller content</DemoBox>
                    </Col>
                    <Col lg={4}>
                        <DemoBox>Centered</DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Horizontal Alignment</h3>
                <Row gap={4} className="tw:justify-center">
                    <Col lg={3}>
                        <DemoBox>Centered</DemoBox>
                    </Col>
                    <Col lg={3}>
                        <DemoBox>Centered</DemoBox>
                    </Col>
                </Row>
            </div>

            <div>
                <h3 className="tw:mb-2 tw:font-semibold">Column Ordering</h3>
                <p className="tw:mb-4 tw:text-gray-500 tw:text-sm">
                    Using order utility classes
                </p>
                <Row gap={4}>
                    <Col lg={4} className="tw:order-3">
                        <DemoBox variant="info">
                            First in HTML (order-3)
                        </DemoBox>
                    </Col>
                    <Col lg={4} className="tw:order-1">
                        <DemoBox variant="success">
                            Second in HTML (order-1)
                        </DemoBox>
                    </Col>
                    <Col lg={4} className="tw:order-2">
                        <DemoBox variant="warning">
                            Third in HTML (order-2)
                        </DemoBox>
                    </Col>
                </Row>
            </div>
        </div>
    ),
}
