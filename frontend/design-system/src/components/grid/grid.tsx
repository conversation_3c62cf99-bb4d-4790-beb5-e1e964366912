import { cva, type VariantProps } from 'class-variance-authority'
import type { ClassProp } from 'class-variance-authority/types'
import * as React from 'react'
import { cn } from '../../utils/cn'

const rowVariants: (
    props?:
        | ({
              gapX?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8
              gapY?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8
          } & ClassProp)
        | undefined,
) => string = cva('tw:flex tw:flex-wrap', {
    variants: {
        gapX: {
            0: 'tw:gap-x-0',
            1: 'tw:gap-x-1',
            2: 'tw:gap-x-2',
            3: 'tw:gap-x-3',
            4: 'tw:gap-x-4',
            5: 'tw:gap-x-5',
            6: 'tw:gap-x-6',
            8: 'tw:gap-x-8',
        },
        gapY: {
            0: 'tw:gap-y-0',
            1: 'tw:gap-y-1',
            2: 'tw:gap-y-2',
            3: 'tw:gap-y-3',
            4: 'tw:gap-y-4',
            5: 'tw:gap-y-5',
            6: 'tw:gap-y-6',
            8: 'tw:gap-y-8',
        },
    },
    defaultVariants: {
        gapX: 4,
        gapY: 4,
    },
})

interface RowProps
    extends React.HTMLAttributes<HTMLDivElement>,
        VariantProps<typeof rowVariants> {
    gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 // applies to both X and Y
}

const Row: React.ForwardRefExoticComponent<
    RowProps & React.RefAttributes<HTMLDivElement>
> = React.forwardRef<HTMLDivElement, RowProps>(
    ({ className, gap, gapX = 4, gapY = 4, ...props }, ref) => {
        const finalGapX = gap !== undefined ? gap : gapX
        const finalGapY = gap !== undefined ? gap : gapY

        // Convert Tailwind spacing to rem values (assuming 1 = 0.25rem)
        const gapXValue = finalGapX * 0.25
        const gapYValue = finalGapY * 0.25

        const style = {
            '--grid-columns': '12',
            '--grid-columnSpacing': `${gapXValue}rem`,
            '--grid-rowSpacing': `${gapYValue}rem`,
            gap: 'var(--grid-rowSpacing) var(--grid-columnSpacing)',
        } as React.CSSProperties

        return (
            <div
                ref={ref}
                className={cn('tw:flex tw:flex-wrap', className)}
                style={style}
                {...props}
            />
        )
    },
)
Row.displayName = 'Row'

const colVariants: (props?: ClassProp | undefined) => string = cva(
    'tw:flex-shrink-0 tw:w-full',
)

interface ColProps extends React.HTMLAttributes<HTMLDivElement> {
    xs?: number | 'auto' | 'fill'
    sm?: number | 'auto' | 'fill'
    md?: number | 'auto' | 'fill'
    lg?: number | 'auto' | 'fill'
    xl?: number | 'auto' | 'fill'
    '2xl'?: number | 'auto' | 'fill'
}

const Col: React.ForwardRefExoticComponent<
    ColProps & React.RefAttributes<HTMLDivElement>
> = React.forwardRef<HTMLDivElement, ColProps>(
    ({ className, xs, sm, md, lg, xl, '2xl': xl2, ...props }, ref) => {
        const getResponsiveStyles = () => {
            const styles: Record<string, string | number> = {}

            // Helper function to calculate width percentage
            const getWidthPercentage = (span: number) => {
                return `calc(${(span / 12) * 100}% - (${12 - span} * var(--grid-columnSpacing, 0rem) / 12))`
            }

            // Set CSS custom properties for each breakpoint
            if (typeof xs === 'number') {
                styles['--xs-width'] = getWidthPercentage(xs)
            } else if (xs === 'auto') {
                styles['--xs-width'] = 'auto'
                styles['--xs-flex-grow'] = '0'
            } else if (xs === 'fill') {
                styles['--xs-width'] = '100%'
                styles['--xs-flex-grow'] = '1'
            }

            if (typeof sm === 'number') {
                styles['--sm-width'] = getWidthPercentage(sm)
            } else if (sm === 'auto') {
                styles['--sm-width'] = 'auto'
                styles['--sm-flex-grow'] = '0'
            } else if (sm === 'fill') {
                styles['--sm-width'] = '100%'
                styles['--sm-flex-grow'] = '1'
            }

            if (typeof md === 'number') {
                styles['--md-width'] = getWidthPercentage(md)
            } else if (md === 'auto') {
                styles['--md-width'] = 'auto'
                styles['--md-flex-grow'] = '0'
            } else if (md === 'fill') {
                styles['--md-width'] = '100%'
                styles['--md-flex-grow'] = '1'
            }

            if (typeof lg === 'number') {
                styles['--lg-width'] = getWidthPercentage(lg)
            } else if (lg === 'auto') {
                styles['--lg-width'] = 'auto'
                styles['--lg-flex-grow'] = '0'
            } else if (lg === 'fill') {
                styles['--lg-width'] = '100%'
                styles['--lg-flex-grow'] = '1'
            }

            if (typeof xl === 'number') {
                styles['--xl-width'] = getWidthPercentage(xl)
            } else if (xl === 'auto') {
                styles['--xl-width'] = 'auto'
                styles['--xl-flex-grow'] = '0'
            } else if (xl === 'fill') {
                styles['--xl-width'] = '100%'
                styles['--xl-flex-grow'] = '1'
            }

            if (typeof xl2 === 'number') {
                styles['--2xl-width'] = getWidthPercentage(xl2)
            } else if (xl2 === 'auto') {
                styles['--2xl-width'] = 'auto'
                styles['--2xl-flex-grow'] = '0'
            } else if (xl2 === 'fill') {
                styles['--2xl-width'] = '100%'
                styles['--2xl-flex-grow'] = '1'
            }

            return styles as React.CSSProperties
        }

        const getResponsiveClasses = () => {
            const classes: string[] = ['tw:min-w-0 tw:box-border']

            // Base width - full width by default
            if (xs === undefined) {
                classes.push('tw:w-full')
            } else if (xs === 'auto') {
                classes.push('tw:w-auto tw:flex-grow-0 tw:flex-shrink-0')
            } else if (xs === 'fill') {
                classes.push('tw:flex-grow tw:flex-shrink-0 tw:basis-0 tw:min-w-0')
            } else {
                classes.push('tw:[width:var(--xs-width)]')
            }

            // Responsive classes using CSS custom properties
            if (sm !== undefined) {
                if (sm === 'auto') {
                    classes.push('tw:sm:w-auto tw:sm:flex-grow-0 tw:sm:flex-shrink-0')
                } else if (sm === 'fill') {
                    classes.push(
                        'tw:sm:flex-grow tw:sm:flex-shrink-0 tw:sm:basis-0 tw:sm:min-w-0',
                    )
                } else {
                    classes.push('tw:sm:[width:var(--sm-width)]')
                }
            }

            if (md !== undefined) {
                if (md === 'auto') {
                    classes.push('tw:md:w-auto tw:md:flex-grow-0 tw:md:flex-shrink-0')
                } else if (md === 'fill') {
                    classes.push(
                        'tw:md:flex-grow tw:md:flex-shrink-0 tw:md:basis-0 tw:md:min-w-0',
                    )
                } else {
                    classes.push('tw:md:[width:var(--md-width)]')
                }
            }

            if (lg !== undefined) {
                if (lg === 'auto') {
                    classes.push('tw:lg:w-auto tw:lg:flex-grow-0 tw:lg:flex-shrink-0')
                } else if (lg === 'fill') {
                    classes.push(
                        'tw:lg:flex-grow tw:lg:flex-shrink-0 tw:lg:basis-0 tw:lg:min-w-0',
                    )
                } else {
                    classes.push('tw:lg:[width:var(--lg-width)]')
                }
            }

            if (xl !== undefined) {
                if (xl === 'auto') {
                    classes.push('tw:xl:w-auto tw:xl:flex-grow-0 tw:xl:flex-shrink-0')
                } else if (xl === 'fill') {
                    classes.push(
                        'tw:xl:flex-grow tw:xl:flex-shrink-0 tw:xl:basis-0 tw:xl:min-w-0',
                    )
                } else {
                    classes.push('tw:xl:[width:var(--xl-width)]')
                }
            }

            if (xl2 !== undefined) {
                if (xl2 === 'auto') {
                    classes.push('tw:2xl:w-auto tw:2xl:flex-grow-0 tw:2xl:flex-shrink-0')
                } else if (xl2 === 'fill') {
                    classes.push(
                        'tw:2xl:flex-grow tw:2xl:flex-shrink-0 tw:2xl:basis-0 tw:2xl:min-w-0',
                    )
                } else {
                    classes.push('tw:2xl:[width:var(--2xl-width)]')
                }
            }

            if (
                xs === undefined &&
                sm === undefined &&
                md === undefined &&
                lg === undefined &&
                xl === undefined &&
                xl2 === undefined
            ) {
                classes.push('tw:w-auto tw:flex-grow-0 tw:flex-shrink-0')
            }

            return classes.join(' ')
        }

        const responsiveStyles = getResponsiveStyles()

        return (
            <div
                ref={ref}
                className={cn(colVariants(), getResponsiveClasses(), className)}
                style={responsiveStyles}
                {...props}
            />
        )
    },
)
Col.displayName = 'Col'

export { Row, Col }
export type { RowProps, ColProps }
