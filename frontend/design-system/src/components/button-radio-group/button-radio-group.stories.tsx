import type { Meta, StoryObj } from '@storybook/react'
import { SearchIcon } from 'lucide-react'
import { useState } from 'react'
import { CheckIcon } from '../../icons/check-icon'
import { SortIcon } from '../../icons/sort-icon'
import { ButtonIcon } from '../button/button-icon'
import { ButtonRadioGroup } from './button-radio-group'
import { ButtonRadioGroupItem } from './button-radio-group-item'

const meta: Meta<typeof ButtonRadioGroup> = {
    title: 'Components/ButtonRadioGroup',
    component: ButtonRadioGroup,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        orientation: {
            control: { type: 'select' },
            options: ['horizontal', 'vertical'],
        },
        gap: {
            control: { type: 'boolean' },
        },
        defaultSelected: {
            control: { type: 'text' },
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Controlled: Story = {
    render: (args) => {
        const [value, setValue] = useState(args.selected)
        return (
            <ButtonRadioGroup {...args} selected={value} onSelected={setValue}>
                <ButtonRadioGroupItem value="option1">
                    Option 1
                </ButtonRadioGroupItem>
                <ButtonRadioGroupItem value="option2">
                    Option 2
                </ButtonRadioGroupItem>
                <ButtonRadioGroupItem value="option3">
                    Option 3
                </ButtonRadioGroupItem>
            </ButtonRadioGroup>
        )
    },
    args: {
        selected: 'option2',
    },
}

export const Uncontrolled: Story = {
    render: (args) => (
        <ButtonRadioGroup
            {...args}
            defaultSelected={args.defaultSelected}
            onSelected={(value) => console.log('Selected:', value)}
        >
            <ButtonRadioGroupItem value="option1">
                Option 1
            </ButtonRadioGroupItem>
            <ButtonRadioGroupItem value="option2">
                Option 2
            </ButtonRadioGroupItem>
            <ButtonRadioGroupItem value="option3">
                Option 3
            </ButtonRadioGroupItem>
        </ButtonRadioGroup>
    ),
    args: {
        defaultSelected: 'option1',
    },
}

export const WithIcons: Story = {
    render: (args) => {
        const [value, setValue] = useState(args.selected)
        return (
            <ButtonRadioGroup {...args} selected={value} onSelected={setValue}>
                <ButtonRadioGroupItem value="option1">
                    <ButtonIcon>
                        <SearchIcon />
                    </ButtonIcon>
                    {value === 'option1' && 'Search'}
                </ButtonRadioGroupItem>
                <ButtonRadioGroupItem value="option2">
                    <ButtonIcon>
                        <SortIcon />
                    </ButtonIcon>
                    {value === 'option2' && 'Sort'}
                </ButtonRadioGroupItem>
                <ButtonRadioGroupItem value="option3">
                    <ButtonIcon>
                        <CheckIcon />
                    </ButtonIcon>
                    {value === 'option3' && 'Check'}
                </ButtonRadioGroupItem>
            </ButtonRadioGroup>
        )
    },
    args: {
        selected: 'option2',
    },
}
