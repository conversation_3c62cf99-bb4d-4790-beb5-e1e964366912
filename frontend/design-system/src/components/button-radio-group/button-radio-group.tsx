import type { JSX } from 'react'
import {
    createContext,
    useCallback,
    useContext,
    useMemo,
    useState,
} from 'react'
import {
    ButtonGroup,
    type ButtonGroupProps,
} from '../button-group/button-group'

export interface ButtonRadioGroupContextValue {
    selected: string
    onSelected: (value: string) => void
}

// Extend the ButtonGroup's props interface to include all button-group properties + context value
export interface ButtonRadioGroupProps
    extends ButtonGroupProps,
        Partial<ButtonRadioGroupContextValue> {
    defaultSelected?: string
}

// Create the context for the button radio group
const ButtonRadioGroupContext =
    createContext<ButtonRadioGroupContextValue | null>(null)

// Create a hook to get the context value
export function useButtonRadioGroup(): ButtonRadioGroupContextValue {
    const context = useContext(ButtonRadioGroupContext)
    if (!context) {
        throw new Error(
            'ButtonRadioGroupItem must be used within ButtonRadioGroup',
        )
    }
    return context
}

export function ButtonRadioGroup({
    children,
    selected,
    onSelected,
    defaultSelected = '',
    ...props
}: ButtonRadioGroupProps): JSX.Element {
    // State for the uncontrolled internal selected value
    const [internalSelected, setInternalSelected] =
        useState<string>(defaultSelected)

    // Determine if the component is controlled or uncontrolled
    const isControlled = useMemo(() => selected !== undefined, [selected])

    // Get the selected value
    const selectedValue = useMemo(
        () => (isControlled ? selected || '' : internalSelected),
        [isControlled, selected, internalSelected],
    )

    // Handle the selected value
    const handleSelected = useCallback(
        (value: string) => {
            if (!isControlled) {
                setInternalSelected(value)
            }
            onSelected?.(value)
        },
        [isControlled, onSelected],
    )

    return (
        <ButtonRadioGroupContext.Provider
            value={{ selected: selectedValue, onSelected: handleSelected }}
        >
            <ButtonGroup {...props}>{children}</ButtonGroup>
        </ButtonRadioGroupContext.Provider>
    )
}
