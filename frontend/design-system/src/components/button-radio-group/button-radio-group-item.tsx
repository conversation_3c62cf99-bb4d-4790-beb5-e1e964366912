import { type JSX, useCallback, useMemo } from 'react'

import type { ButtonProps } from '../button/button'
import { Button } from '../button/button'
import { useButtonRadioGroup } from './button-radio-group'

// Extend the Button's props interface to include all button properties + value
export interface ButtonRadioGroupItemProps
    extends Omit<ButtonProps, 'onClick'> {
    value: string
}

export function ButtonRadioGroupItem(
    props: ButtonRadioGroupItemProps,
): JSX.Element {
    // Destructure the props we need
    const {
        isActive: propIsActive,
        value,
        children,
        color = 'neutral-2',
        ...restProps
    } = props

    // Get the selected value and the onSelected handler from the button radio group context
    const { selected, onSelected } = useButtonRadioGroup()

    // Determine if the button is active based on the IsActive prop or the selected value
    const isActive = useMemo(
        () => (propIsActive !== undefined ? propIsActive : selected === value),
        [propIsActive, selected, value],
    )

    // Handle the click event
    const handleClick = useCallback(() => {
        onSelected(value)
    }, [onSelected, value])

    return (
        <Button
            isActive={isActive}
            onClick={handleClick}
            color={color}
            {...restProps}
        >
            {children}
        </Button>
    )
}
