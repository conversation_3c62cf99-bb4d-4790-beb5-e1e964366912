import type { JSX } from 'react/jsx-runtime'
import { cn } from '../../utils/cn'

export const PageHeader = ({
    children,
    className,
}: {
    children?: React.ReactNode
    className?: string
}): JSX.Element => {
    return (
        <div
            className={cn(
                'tw:bg-white tw:px-5 tw:py-3 tw:shadow-[0_4px_12.7px_0_rgba(78,83,129,0.25)]',
                className,
            )}
        >
            {children}
        </div>
    )
}
