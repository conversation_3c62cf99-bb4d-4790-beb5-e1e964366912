import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { <PERSON><PERSON>ead<PERSON> } from './page-header'
import { Stack } from '../stack/stack'
import { Button } from '../button/button'
import { ButtonIcon } from '../button/button-icon'

const meta: Meta<typeof PageHeader> = {
    title: 'Components/PageHeader',
    component: PageHeader,
    parameters: {
        layout: 'fullscreen',
    },
    tags: ['autodocs'],
}

const Content = () => {
    return (
        <Stack className="tw:flex-row tw:justify-between tw:gap-3">
                <Button className="tw:max-md:rounded-none" color="neutral">
                    <ButtonIcon>
                        <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <title>folder lock</title>
                            <path
                                d="M2.50065 3.33333C1.57565 3.33333 0.833984 4.07499 0.833984 4.99999V15C0.833984 15.442 1.00958 15.8659 1.32214 16.1785C1.6347 16.4911 2.05862 16.6667 2.50065 16.6667H8.55065C9.64232 18.2333 11.4256 19.1667 13.334 19.1667C14.8811 19.1667 16.3648 18.5521 17.4588 17.4581C18.5527 16.3642 19.1673 14.8804 19.1673 13.3333C19.1673 11.8083 18.5673 10.35 17.5006 9.25833V6.66666C17.5006 5.74166 16.7506 4.99999 15.834 4.99999H9.16732L7.50065 3.33333H2.50065ZM13.334 9.16666C14.4391 9.16666 15.4989 9.60565 16.2803 10.387C17.0617 11.1685 17.5006 12.2283 17.5006 13.3333C17.5006 14.4384 17.0617 15.4982 16.2803 16.2796C15.4989 17.061 14.4391 17.5 13.334 17.5C12.2289 17.5 11.1691 17.061 10.3877 16.2796C9.6063 15.4982 9.16732 14.4384 9.16732 13.3333C9.16732 12.2283 9.6063 11.1685 10.3877 10.387C11.1691 9.60565 12.2289 9.16666 13.334 9.16666ZM12.5007 9.99999V14.1667L15.509 15.9667L16.134 14.95L13.7506 13.5417V9.99999H12.5007Z"
                                fill="currentColor"
                            />
                        </svg>
                    </ButtonIcon>
                    <span className="tw:max-md:hidden">Save as Draft</span>
                </Button>
                <Stack className="tw:flex-row tw:gap-3 tw:max-md:gap-0">
                    <Button
                        className="tw:max-md:rounded-none"
                        color="neutral"
                    >
                        <ButtonIcon>
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <title>cancel</title>
                                <path
                                    d="M15.8327 5.34167L14.6577 4.16667L9.99935 8.82501L5.34102 4.16667L4.16602 5.34167L8.82435 10L4.16602 14.6583L5.34102 15.8333L9.99935 11.175L14.6577 15.8333L15.8327 14.6583L11.1743 10L15.8327 5.34167Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </ButtonIcon>
                        <span className="tw:max-md:hidden">Cancel</span>
                    </Button>
                    <Button
                        className="tw:max-md:rounded-none"
                        color="primary"
                    >
                        <ButtonIcon>
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <title>content save</title>
                                <path
                                    d="M12.5 7.5H4.16667V4.16667H12.5M10 15.8333C9.33696 15.8333 8.70107 15.5699 8.23223 15.1011C7.76339 14.6323 7.5 13.9964 7.5 13.3333C7.5 12.6703 7.76339 12.0344 8.23223 11.5656C8.70107 11.0967 9.33696 10.8333 10 10.8333C10.663 10.8333 11.2989 11.0967 11.7678 11.5656C12.2366 12.0344 12.5 12.6703 12.5 13.3333C12.5 13.9964 12.2366 14.6323 11.7678 15.1011C11.2989 15.5699 10.663 15.8333 10 15.8333ZM14.1667 2.5H4.16667C3.24167 2.5 2.5 3.25 2.5 4.16667V15.8333C2.5 16.2754 2.67559 16.6993 2.98816 17.0118C3.30072 17.3244 3.72464 17.5 4.16667 17.5H15.8333C16.2754 17.5 16.6993 17.3244 17.0118 17.0118C17.3244 16.6993 17.5 16.2754 17.5 15.8333V5.83333L14.1667 2.5Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </ButtonIcon>
                        <span className="tw:max-md:hidden">Save</span>
                    </Button>
                </Stack>
            </Stack>
    )
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: (
            <Content />
        ),
    },
}

export const WithCustomClassName: Story = {
    args: {
        className: 'tw:bg-light-gray-3',
        children: (
            <Content />
        ),
    },
}

