import { Menu as ReactAriaMenu } from 'react-aria-components'
import { cn } from '../../utils/cn'

const Menu: typeof ReactAriaMenu = ({ children, ...props }) => {
    return (
        <ReactAriaMenu
            className={cn(
                'tw:min-w-0 tw:rounded-xs tw:bg-white tw:shadow-lg',
                props.className,
            )}
            {...props}
        >
            {children}
        </ReactAriaMenu>
    )
}

export { Menu }
