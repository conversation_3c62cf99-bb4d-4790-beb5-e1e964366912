import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { SortIcon } from '../../icons/sort-icon'
import { But<PERSON> } from '../button/button'
import { ButtonIcon } from '../button/button-icon'
import { Menu } from './menu'
import { MenuItem } from './menu-item'
import { MenuItemIcon } from './menu-item-icon'
import { MenuPopover } from './menu-popover'
import { MenuTrigger } from './menu-trigger'

// Define the args type for the template
interface MenuTemplateArgs {
    controlled?: boolean
}

// Template function for default variant
const DefaultTemplate = (args: MenuTemplateArgs): React.JSX.Element => {
    const { controlled = true } = args
    const [isOpen, setIsOpen] = useState(false)

    return (
        <MenuTrigger
            {...(controlled && {
                onOpenChange: setIsOpen,
                isOpen: isOpen,
            })}
        >
            <Button color="secondary">
                <ButtonIcon>
                    <SortIcon />
                </ButtonIcon>
                Sort
            </Button>
            <MenuPopover>
                <Menu>
                    <MenuItem onAction={() => alert('none')}>
                        <MenuItemIcon>
                            <SortIcon />
                        </MenuItemIcon>
                        <span>None</span>
                    </MenuItem>
                    <MenuItem onAction={() => alert('all-current')}>
                        <MenuItemIcon>
                            <SortIcon />
                        </MenuItemIcon>
                        <span>All (Current Page)</span>
                    </MenuItem>
                    <MenuItem onAction={() => alert('all-filtered')}>
                        <MenuItemIcon>
                            <SortIcon />
                        </MenuItemIcon>
                        <span>All Filtered (All Pages)</span>
                    </MenuItem>
                </Menu>
            </MenuPopover>
        </MenuTrigger>
    )
}

const meta: Meta<typeof DefaultTemplate> = {
    title: 'Components/Menu',
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        controlled: {
            control: 'boolean',
            description: 'Whether the menu is controlled or uncontrolled',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

// Default Variant Stories
export const DefaultControlled: Story = {
    render: DefaultTemplate,
    args: {
        controlled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Default menu with controlled state.',
            },
        },
    },
}

export const DefaultUncontrolled: Story = {
    render: DefaultTemplate,
    args: {
        controlled: false,
    },
    parameters: {
        docs: {
            description: {
                story: 'Default menu with uncontrolled state.',
            },
        },
    },
}

export const RTL: Story = {
    render: DefaultTemplate,
    args: {
        controlled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'Default menu with RTL (right-to-left) text direction support.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}
