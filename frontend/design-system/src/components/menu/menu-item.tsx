import { MenuItem as ReactAriaMenuItem } from 'react-aria-components'
import { cn } from '../../utils/cn'

const MenuItem: typeof ReactAriaMenuItem = ({ children, ...props }) => {
    return (
        <ReactAriaMenuItem
            className={cn(
                'tw:group tw:flex tw:h-12 tw:cursor-pointer tw:items-center tw:gap-2 tw:border-y-light-gray-2 tw:px-4 tw:py-3 tw:font-normal tw:text-[#75799D] tw:text-sm tw:outline-none tw:transition-colors tw:duration-200 tw:hover:bg-light-gray-1 tw:hover:text-[#1877F2] tw:focus:border-y tw:focus:bg-light-gray-1 tw:focus:text-[#1877F2] tw:focus-visible:outline-none tw:focus-visible:ring-2 tw:focus-visible:ring-primary-blue tw:focus-visible:ring-offset-2',
                props.className,
            )}
            {...props}
        >
            {children}
        </ReactAriaMenuItem>
    )
}

export { MenuItem }
