import { cn } from '../../utils/cn'

const MenuItemIcon = ({
    children,
    ...props
}: {
    children: React.ReactNode
} & React.HTMLAttributes<HTMLDivElement>): React.JSX.Element => {
    return (
        <div
            className={cn(
                'tw:flex tw:h-6 tw:w-6 tw:flex-shrink-0 tw:items-center tw:justify-center tw:text-[#75799D] tw:transition-colors tw:duration-200 tw:group-hover:text-[#1877F2] tw:group-focus:text-[#1877F2]',
                props.className,
            )}
            {...props}
        >
            {children}
        </div>
    )
}

export { MenuItemIcon }
