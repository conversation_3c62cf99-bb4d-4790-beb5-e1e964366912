import type { <PERSON>a, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { Label } from '../label/label'
import { Stack } from '../stack/stack'
import { Toggle } from './toggle'

interface TemplateArgs {
    label?: string
    fieldLabel?: string
    required?: boolean
    value?: boolean
    onChange?: (isSelected: boolean) => void
    disabled?: boolean
    className?: string
}

const meta: Meta<TemplateArgs> = {
    title: 'Components/Toggle',
    component: Toggle,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    args: {
        label: 'Wi-Fi',
        disabled: false,
    },
    argTypes: {
        label: {
            control: 'text',
            description: 'The label text displayed next to the toggle',
        },
        fieldLabel: {
            control: 'text',
            description: 'The label text displayed above the toggle',
        },
        required: {
            control: 'boolean',
            description: 'Whether the toggle is required',
        },
        value: {
            control: 'boolean',
            description:
                'Whether the toggle is selected (controlled component)',
        },
        disabled: {
            control: 'boolean',
            description: 'Whether the toggle is disabled',
        },
        onChange: {
            table: {
                disable: true,
            },
        },
        className: {
            table: {
                disable: true,
            },
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

function ControlledTemplate(args: TemplateArgs) {
    const [value, setValue] = useState(args.value ?? false)
    return (
        <Stack className="tw:gap-2">
            {args.fieldLabel && (
                <Label required={args.required}>{args.fieldLabel}</Label>
            )}
            <Toggle {...args} value={value} onChange={setValue} />
        </Stack>
    )
}

export const Default: Story = {
    args: {
        label: 'Wi-Fi',
    },
}

export const Controlled: Story = {
    render: ControlledTemplate,
    args: {
        label: 'Wi-Fi',
    },
    parameters: {
        docs: {
            description: {
                story: 'This toggle demonstrates controlled behavior with the label positioned above the toggle switch.',
            },
        },
    },
}

export const WithFieldLabel: Story = {
    render: ControlledTemplate,
    args: {
        fieldLabel: 'Wi-Fi Settings',
        label: 'Wi-Fi',
    },
    parameters: {
        docs: {
            description: {
                story: 'This toggle demonstrates the field label positioned above the toggle switch.',
            },
        },
    },
}

export const WithRequiredFieldLabel: Story = {
    render: ControlledTemplate,
    args: {
        fieldLabel: 'Terms and Conditions',
        label: 'I agree to the terms',
        required: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This toggle demonstrates a required field with the label positioned above the toggle switch.',
            },
        },
    },
}

export const RTL: Story = {
    args: {
        label: 'واي فاي',
    },
    parameters: {
        docs: {
            description: {
                story: 'This toggle demonstrates right-to-left (RTL) layout with Arabic text. The toggle switch will animate in the opposite direction.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}

export const Disabled: Story = {
    args: {
        label: 'Airplane Mode',
        disabled: true,
    },
}

export const DisabledSelected: Story = {
    args: {
        label: 'System Updates',
        value: true,
        disabled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This toggle is disabled and selected (value is true). It shows the disabled state while maintaining the selected appearance.',
            },
        },
    },
}

export const CustomLabel: Story = {
    args: {
        label: 'Dark Mode',
    },
}

export const LongLabel: Story = {
    args: {
        label: 'Enable automatic software updates and security patches',
    },
}

export const ShortLabel: Story = {
    args: {
        label: 'On',
    },
}
