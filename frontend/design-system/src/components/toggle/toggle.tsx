import type { JSX } from 'react'
import { Switch } from 'react-aria-components'
import { cn } from '../../utils/cn'

interface ToggleProps {
    label?: string
    value?: boolean
    onChange?: (isSelected: boolean) => void
    disabled?: boolean
    className?: string
}

const Toggle = ({
    label = 'Toggle',
    value,
    onChange,
    disabled = false,
    className = '',
}: ToggleProps): JSX.Element => {
    return (
        <Switch
            className={cn(
                `tw:group tw:relative tw:flex tw:items-center tw:gap-2 tw:font-normal tw:text-annotation tw:text-primary-black tw:leading-normal tw:disabled:cursor-not-allowed tw:disabled:text-[#75799D]`,
                'tw:rounded-sm tw:bg-light-gray-1 tw:px-4 tw:py-3',
                className,
            )}
            isSelected={value}
            onChange={onChange}
            isDisabled={disabled}
        >
            <div className="tw:flex tw:h-[22px] tw:w-[40px] tw:rounded-full tw:bg-[#A8ACC2] tw:bg-clip-padding tw:p-1 tw:outline-hidden tw:ring-black tw:transition tw:duration-200 tw:ease-in-out tw:group-focus-visible:ring-2 tw:group-disabled:bg-[#A8ACC2]/30 tw:group-selected:bg-[#80C342] tw:group-disabled:group-selected:bg-[#DBF4C7] tw:rtl:flex-row-reverse">
                <span className="tw:h-[14px] tw:w-[14px] tw:translate-x-0 tw:transform tw:rounded-full tw:bg-white tw:transition tw:duration-200 tw:ease-in-out tw:group-selected:translate-x-[18px]" />
            </div>
            {label}
        </Switch>
    )
}

export { Toggle }
