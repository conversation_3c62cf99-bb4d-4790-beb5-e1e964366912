import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { Label } from '../label/label'
import { Stack } from '../stack/stack'
import { Radio, RadioGroup, type RadioGroupProps } from './radio'

interface TemplateArgs extends RadioGroupProps {
    label?: string
    required?: boolean
}

const meta: Meta<TemplateArgs> = {
    title: 'Components/Radio',
    component: RadioGroup,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        label: {
            control: 'text',
            description: 'The label text displayed above the radio group',
        },
        value: {
            control: 'text',
            description: 'The currently selected value',
        },
        disabled: {
            control: 'boolean',
            description: 'Whether the entire radio group is disabled',
        },
        invalid: {
            control: 'boolean',
            description: 'Whether the radio group is in an invalid state',
        },
        required: {
            control: 'boolean',
            description: 'Whether the field is required (shows red asterisk)',
        },
        name: {
            control: 'text',
            description: 'The name attribute for the radio group',
        },
        onChange: {
            table: {
                disable: true,
            },
        },
        className: {
            table: {
                disable: true,
            },
        },
        children: {
            table: {
                disable: true,
            },
        },
    },
}

export default meta

const radioOptions = [
    { value: 'dogs', label: 'Dogs' },
    { value: 'cats', label: 'Cats' },
    { value: 'birds', label: 'Birds' },
]

type Story = StoryObj<TemplateArgs>

function ControlledTemplate(args: TemplateArgs) {
    const [value, setValue] = useState(args.value ?? '')
    return (
        <Stack className="tw:gap-2">
            {args.label && <Label required={args.required}>{args.label}</Label>}
            <RadioGroup {...args} value={value} onChange={setValue}>
                {radioOptions.map((option) => (
                    <Radio key={option.value} value={option.value}>
                        {option.label}
                    </Radio>
                ))}
            </RadioGroup>
        </Stack>
    )
}

export const Default: Story = {
    render: (args: TemplateArgs) => <ControlledTemplate {...args} />,
    args: {
        label: 'Favorite pet',
    },
}

export const RTL: Story = {
    render: (args: TemplateArgs) => (
        <div dir="rtl">
            <Stack className="tw:gap-2">
                <Label required={args.required}>ما هو حيوانك المفضل؟</Label>
                <RadioGroup {...args}>
                    <Radio value="dogs">كلب</Radio>
                    <Radio value="cats">قطة</Radio>
                    <Radio value="birds">طائر</Radio>
                </RadioGroup>
            </Stack>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'This radio group demonstrates right-to-left (RTL) layout with Arabic text.',
            },
        },
    },
}

export const Selected: Story = {
    render: (args: TemplateArgs) => (
        <ControlledTemplate {...args} value="cats" />
    ),
    args: {
        label: 'Favorite pet',
        value: 'cats',
    },
}

export const Disabled: Story = {
    render: (args: TemplateArgs) => <ControlledTemplate {...args} disabled />,
    args: {
        label: 'Favorite pet',
        disabled: true,
    },
}

export const DisabledSelected: Story = {
    render: (args: TemplateArgs) => (
        <ControlledTemplate {...args} value="cats" disabled />
    ),
    args: {
        label: 'Favorite pet',
        value: 'cats',
        disabled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This radio group is disabled and has a selected value.',
            },
        },
    },
}

export const Invalid: Story = {
    render: (args: TemplateArgs) => <ControlledTemplate {...args} invalid />,
    args: {
        label: 'Favorite pet',
        invalid: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This radio group is in an invalid state, typically used for form validation errors.',
            },
        },
    },
}

export const InvalidSelected: Story = {
    render: (args: TemplateArgs) => (
        <ControlledTemplate {...args} value="cats" invalid />
    ),
    args: {
        label: 'Favorite pet',
        value: 'cats',
        invalid: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This radio group is selected and in an invalid state.',
            },
        },
    },
}

export const InvalidDisabled: Story = {
    render: (args: TemplateArgs) => (
        <ControlledTemplate {...args} invalid disabled />
    ),
    args: {
        label: 'Favorite pet',
        invalid: true,
        disabled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This radio group is invalid and disabled.',
            },
        },
    },
}

export const InvalidDisabledSelected: Story = {
    render: (args: TemplateArgs) => (
        <ControlledTemplate {...args} value="cats" invalid disabled />
    ),
    args: {
        label: 'Favorite pet',
        value: 'cats',
        invalid: true,
        disabled: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This radio group is selected, invalid, and disabled.',
            },
        },
    },
}

export const MixedDisabled: Story = {
    render: (args: TemplateArgs) => (
        <Stack className="tw:gap-2">
            <Label required={args.required}>Notification preferences</Label>
            <RadioGroup {...args}>
                <Radio value="email">Email notifications</Radio>
                <Radio value="sms" disabled>
                    SMS notifications
                </Radio>
                <Radio value="push">Push notifications</Radio>
            </RadioGroup>
        </Stack>
    ),
    parameters: {
        docs: {
            description: {
                story: 'This radio group has some options disabled while others remain enabled.',
            },
        },
    },
}

export const LongOptions: Story = {
    render: (args: TemplateArgs) => (
        <Stack className="tw:gap-2">
            <Label required={args.required}>
                Select your preferred communication method
            </Label>
            <RadioGroup {...args}>
                <Radio value="email">
                    Email - I prefer to receive notifications via email at my
                    registered email address
                </Radio>
                <Radio value="sms">
                    SMS - Send me text messages to my mobile phone number
                </Radio>
                <Radio value="push">
                    Push notifications - Show me notifications directly on my
                    device
                </Radio>
            </RadioGroup>
        </Stack>
    ),
    parameters: {
        docs: {
            description: {
                story: 'This radio group demonstrates how long option labels are handled.',
            },
        },
    },
}

export const NoLabel: Story = {
    render: (args: TemplateArgs) => (
        <RadioGroup {...args}>
            <Radio value="option1">Option 1</Radio>
            <Radio value="option2">Option 2</Radio>
            <Radio value="option3">Option 3</Radio>
        </RadioGroup>
    ),
    parameters: {
        docs: {
            description: {
                story: 'This radio group has no label, useful when the context is clear.',
            },
        },
    },
}

export const Required: Story = {
    render: (args: TemplateArgs) => <ControlledTemplate {...args} required />,
    args: {
        label: 'Favorite pet',
        required: true,
    },
    parameters: {
        docs: {
            description: {
                story: 'This radio group is required, indicated by a red asterisk in the label.',
            },
        },
    },
}
