import type { JSX, ReactNode } from 'react'
import {
    Radio as AriaRadio,
    RadioGroup as AriaRadioGroup,
} from 'react-aria-components'
import { cn } from '../../utils/cn'

export interface RadioGroupProps {
    value?: string
    onChange?: (value: string) => void
    disabled?: boolean
    invalid?: boolean
    name?: string
    className?: string
    children?: ReactNode
}

export interface RadioProps {
    value: string
    disabled?: boolean
    className?: string
    children?: ReactNode
}

export function RadioGroup({
    value,
    onChange,
    disabled = false,
    invalid = false,
    name,
    className = '',
    children,
}: RadioGroupProps): JSX.Element {
    return (
        <AriaRadioGroup
            value={value}
            onChange={onChange}
            isDisabled={disabled}
            isInvalid={invalid}
            name={name}
            className={cn(
                'tw:group tw:flex tw:flex-col tw:gap-2',
                'tw:rounded-sm tw:bg-light-gray-1 tw:px-4 tw:py-3',
                className,
            )}
        >
            {children}
        </AriaRadioGroup>
    )
}

export function Radio({
    value,
    disabled = false,
    className = '',
    children,
}: RadioProps): JSX.Element {
    return (
        <AriaRadio
            value={value}
            isDisabled={disabled}
            className={cn(
                'tw:group tw:relative tw:flex tw:items-center tw:gap-2 tw:text-annotation tw:text-primary-black tw:transition-colors',
                'tw:disabled:cursor-not-allowed tw:disabled:text-[#75799D]',
                className,
            )}
        >
            <div
                className={cn(
                    'tw:flex tw:h-5 tw:w-5 tw:shrink-0 tw:items-center tw:justify-center tw:rounded-full tw:border-[2.5px] tw:transition-colors',
                    'tw:focus-visible:outline-2 tw:focus-visible:outline-black tw:focus-visible:outline-offset-2',
                    'tw:border-light-gray-3 tw:bg-white',
                    'tw:group-selected:border-info-1',
                    'tw:group-disabled:group-selected:border-[#A5C8F7]',
                    'tw:group-invalid:border-danger-1 tw:group-invalid:group-selected:border-danger-1',
                    'tw:group-disabled:group-invalid:border-danger-1/50 tw:group-disabled:group-invalid:group-selected:border-danger-1/50',
                    'tw:group-disabled:border-light-gray-3/50 tw:group-disabled:bg-white/50',
                )}
            >
                <div
                    className={cn(
                        'tw:h-[8px] tw:w-[8px] tw:rounded-full tw:opacity-0 tw:transition-colors',
                        'tw:group-selected:opacity-100',
                        'tw:group-selected:bg-info-1',
                        'tw:group-disabled:group-selected:bg-[#A5C8F7]',
                        'tw:group-invalid:group-selected:bg-danger-1',
                        'tw:group-disabled:group-invalid:group-selected:bg-danger-1/50',
                    )}
                />
            </div>
            {children}
        </AriaRadio>
    )
}
