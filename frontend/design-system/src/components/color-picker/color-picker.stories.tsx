import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { useEffect, useState } from 'react'
import type { ColorFormat } from 'react-aria-components'
import { ErrorMessage, InputField, SupportText } from '../input/input'
import { Label } from '../label/label'
import { ColorPicker } from './color-picker'

// Define the args type for the template
interface ColorPickerTemplateArgs {
    // InputField props
    label?: string
    supportText?: string
    errorMessage?: string
    required?: boolean
    invalid?: boolean
    success?: boolean
    isDisabled?: boolean
    name?: string
    autoFocus?: boolean
    className?: string
    // ColorPicker props
    value?: string
    onChange?: (value: string) => void
    freeColors?: boolean
    thumbnailOnly?: boolean
    palette?: string[]
    colorFormat?: ColorFormat
    size?: 'sm' | 'md' | 'lg'
}

// Template function that renders InputField with InputGroup and TextArea and handles all props
const ColorPickerTemplate = (
    args: ColorPickerTemplateArgs,
): React.JSX.Element => {
    const {
        // TextAreaField props
        label,
        supportText,
        errorMessage,
        required,
        invalid,
        success,
        isDisabled,
        name,
        autoFocus,
        className,
        // ColorPicker props
        value: initialValue,
        freeColors,
        thumbnailOnly,
        palette,
        colorFormat,
        size,
    } = args

    const [value, setValue] = useState(initialValue ?? '')

    useEffect(() => {
        setValue(initialValue ?? '')
    }, [initialValue])

    return (
        <InputField
            invalid={invalid}
            success={success}
            isDisabled={isDisabled}
            name={name}
            autoFocus={autoFocus}
            className={className}
        >
            {label && <Label required={required}>{label}</Label>}
            <ColorPicker
                value={value}
                onChange={(newColor) => {
                    setValue(newColor)
                }}
                thumbnailOnly={thumbnailOnly}
                freeColors={freeColors}
                palette={palette}
                colorFormat={colorFormat}
                isDisabled={isDisabled}
                size={size}
            />
            {supportText && <SupportText>{supportText}</SupportText>}
            {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
        </InputField>
    )
}

const meta: Meta<typeof ColorPickerTemplate> = {
    title: 'Components/ColorPicker',
    component: ColorPickerTemplate,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        // TextAreaField props
        label: {
            control: 'text',
            description: 'The label text displayed above the textarea',
        },
        supportText: {
            control: 'text',
            description: 'Help text displayed below the textarea',
        },
        errorMessage: {
            control: 'text',
            description: 'Error message displayed below the textarea',
        },
        required: {
            control: 'boolean',
            description: 'Whether the field is required (shows red asterisk)',
        },
        invalid: {
            control: 'boolean',
            description:
                'Whether the textarea is in an invalid state (shows red border)',
        },
        success: {
            control: 'boolean',
            description:
                'Whether the textarea is in a success state (shows green border)',
        },
        isDisabled: {
            control: 'boolean',
            description: 'Whether the TextAreaField is disabled',
        },
        name: {
            control: 'text',
            description: 'The name attribute for the textarea field',
        },
        autoFocus: {
            control: 'boolean',
            description: 'Whether the textarea should be focused on mount',
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes for the TextAreaField',
        },
        // ColorPicker props
        value: {
            control: 'text',
            description: 'The value of the textarea field',
        },
        onChange: {
            table: {
                disable: true,
            },
        },
        freeColors: {
            control: 'boolean',
            description: 'Whether to show free colors',
        },
        thumbnailOnly: {
            control: 'boolean',
            description: 'Whether to show a thumbnail only',
        },
        palette: {
            control: 'object',
            description: 'The palette of colors to display',
        },
        colorFormat: {
            control: 'select',
            options: [
                'hsla',
                'hex',
                'hexa',
                'rgb',
                'rgba',
                'hsl',
                'hsb',
                'hsba',
            ],
            description: 'The color format to use',
        },
        size: {
            control: 'select',
            options: ['sm', 'md', 'lg'],
            description: 'The size of the color picker',
        },
    },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: ColorPickerTemplate,
    argTypes: {
        palette: {
            control: 'object',
            description: 'The palette of colors to display',
        },
        value: {
            control: 'color',
        },
    },
    args: {
        label: 'Color',
        supportText: 'Please provide a valid color',
        value: 'hsla(0, 100%, 50%, 1)',
        freeColors: true,
        colorFormat: 'hsla',
        thumbnailOnly: false,
        errorMessage: '',
        required: true,
        invalid: false,
        success: false,
        isDisabled: false,
        autoFocus: false,
        className: '',
    },
    parameters: {
        docs: {
            description: {
                story: 'A basic color picker with label and support text.',
            },
        },
    },
}

export const RTL: Story = {
    render: ColorPickerTemplate,
    args: {
        label: 'اللون',
        supportText: 'يرجى تقديم لون صالح',
        value: 'hsla(0, 100%, 50%, 1)',
        freeColors: true,
        colorFormat: 'hsla',
        thumbnailOnly: false,
        errorMessage: '',
    },
    parameters: {
        docs: {
            description: {
                story: 'Textarea with RTL (right-to-left) text direction support.',
            },
        },
    },
    globals: {
        addonRtl: 'rtl',
    },
}

export const ThumbnailOnly: Story = {
    render: ColorPickerTemplate,
    args: {
        label: 'Color',
        supportText: 'Please provide a valid color',
        value: 'hsla(0, 100%, 50%, 1)',
        freeColors: true,
        colorFormat: 'hsla',
        thumbnailOnly: true,
        errorMessage: '',
        required: true,
        invalid: false,
        success: false,
        isDisabled: false,
        autoFocus: false,
        className: '',
    },
    parameters: {
        docs: {
            description: {
                story: 'Color picker with thumbnail only',
            },
        },
    },
}

export const WithoutFreeColors: Story = {
    render: ColorPickerTemplate,
    args: {
        label: 'Color',
        supportText: 'Please provide a valid color',
        value: 'hsla(0, 100%, 50%, 1)',
        freeColors: false,
        colorFormat: 'hsla',
        thumbnailOnly: false,
        errorMessage: '',
        required: true,
        invalid: false,
        success: false,
        isDisabled: false,
        autoFocus: false,
        className: '',
    },
    parameters: {
        docs: {
            description: {
                story: 'Color picker with palette only',
            },
        },
    },
}

export const CustomPalette: Story = {
    render: ColorPickerTemplate,
    args: {
        label: 'Color',
        supportText: 'Please provide a valid color',
        value: 'hsla(0, 100%, 50%, 1)',
        freeColors: false,
        colorFormat: 'hsla',
        palette: ['#000000', '#FF0000', '#00FF00', '#0000FF'],
        thumbnailOnly: false,
        errorMessage: '',
        required: true,
        invalid: false,
        success: false,
        isDisabled: false,
        autoFocus: false,
        className: '',
    },
    parameters: {
        docs: {
            description: {
                story: 'Color picker with palette only',
            },
        },
    },
}

export const Disabled: Story = {
    render: ColorPickerTemplate,
    args: {
        label: 'Color',
        supportText: 'Please provide a valid color',
        value: 'hsla(0, 100%, 50%, 1)',
        colorFormat: 'hsla',
        errorMessage: '',
        required: true,
        invalid: false,
        success: false,
        isDisabled: true,
        autoFocus: false,
        className: '',
    },
    parameters: {
        docs: {
            description: {
                story: 'Color picker with palette only',
            },
        },
    },
}

export const WithSuccess: Story = {
    render: ColorPickerTemplate,
    args: {
        label: 'Color',
        supportText: 'Please provide a valid color',
        value: 'hsla(0, 100%, 50%, 1)',
        colorFormat: 'hsla',
        errorMessage: '',
        required: false,
        invalid: false,
        success: true,
        isDisabled: false,
        autoFocus: false,
        className: '',
    },
    parameters: {
        docs: {
            description: {
                story: 'Color picker with success',
            },
        },
    },
}

export const WithError: Story = {
    render: ColorPickerTemplate,
    args: {
        label: 'Color',
        supportText: 'Please provide a valid color',
        value: 'hsla(0, 100%, 50%, 1)',
        colorFormat: 'hsla',
        errorMessage: 'Error Text',
        required: false,
        invalid: true,
        success: false,
        isDisabled: false,
        autoFocus: false,
        className: '',
    },
    parameters: {
        docs: {
            description: {
                story: 'Color picker with error',
            },
        },
    },
}

export const CustomColorSpace: Story = {
    render: ColorPickerTemplate,
    args: {
        label: 'Color',
        supportText: 'You cannot select transparent colors with hex format',
        value: '#900C3F',
        colorFormat: 'hex',
        freeColors: true,
        errorMessage: '',
        required: false,
        invalid: false,
        success: false,
        isDisabled: false,
        autoFocus: false,
        className: '',
    },
    parameters: {
        docs: {
            description: {
                story: 'Color picker with custom color space',
            },
        },
    },
}
