import type { JSX } from 'react'

import {
    type Color,
    ColorArea,
    ColorField,
    type ColorFormat,
    ColorSlider,
    ColorSwatch,
    ColorSwatchPicker,
    ColorSwatchPickerItem,
    ColorThumb,
    Dialog,
    DialogTrigger,
    Popover,
    Pressable,
    ColorPicker as ReactAriaColorPicker,
    SliderTrack,
} from 'react-aria-components'
import { cn } from '../../utils/cn'
import { Input, InputAddon, InputGroup } from '../input/input'

const defaultPalette = [
    'hsl(340, 84%, 59%)',
    'hsl(201, 98%, 41%)',
    'hsl(174, 100%, 27%)',
    'hsl(39, 95%, 59%)',
    'hsl(200, 13%, 55%)',
    'hsl(93, 48%, 42%)',
    'hsl(15, 17%, 47%)',
    'hsl(261, 54%, 55%)',
    'hsl(24, 98%, 47%)',
    'hsl(14, 90%, 39%)',
    'hsl(258, 59%, 42%)',
    'hsl(174, 100%, 15%)',
    'hsl(207, 100%, 30%)',
    'hsl(335, 78%, 43%)',
    'hsl(291, 68%, 38%)',
    'hsl(200, 19%, 33%)',
    'hsl(14, 92%, 54%)',
    'hsl(15, 19%, 29%)',
    'hsl(93, 57%, 27%)',
    'hsl(235, 50%, 56%)',
    'hsl(186, 100%, 33%)',
]

const ColorPicker = ({
    value,
    onChange = () => {},
    className,
    palette = defaultPalette,
    colorFormat = 'hsla',
    thumbnailOnly = false,
    isDisabled = false,
    freeColors,
    invalid,
    size = 'lg',
}: {
    value: string
    onChange?: (value: string) => void
    className?: string
    palette?: string[]
    colorFormat?: ColorFormat
    thumbnailOnly?: boolean
    freeColors?: boolean
    isDisabled?: boolean
    invalid?: boolean
    size?: 'sm' | 'md' | 'lg'
}): JSX.Element => {
    return (
        <ReactAriaColorPicker
            value={value}
            onChange={(value) => {
                onChange?.(value.toString(colorFormat))
            }}
        >
            <DialogTrigger {...(isDisabled && { isOpen: false })}>
                <Pressable>
                    <div
                        className={cn(
                            thumbnailOnly && 'tw:inline-flex',
                            className,
                        )}
                    >
                        {thumbnailOnly && (
                            <span
                                className={cn(
                                    // 'shadow-[0_0_1px_0_rgba(0,0,0,0.4)_inset]' | 'border border-neutral-200',
                                    'tw:inline-flex tw:h-[40px] tw:w-[40px] tw:shrink-0 tw:cursor-pointer',
                                    isDisabled && 'tw:cursor-not-allowed',
                                )}
                                style={{ backgroundColor: value }}
                            ></span>
                        )}
                        {!thumbnailOnly && (
                            <InputGroup className={cn('tw:w-full')} size={size}>
                                <InputAddon>
                                    <span
                                        className={cn(
                                            // 'shadow-[0_0_1px_0_rgba(0,0,0,0.4)_inset]' | 'border border-neutral-200',
                                            'tw:inline-flex tw:h-[40px] tw:w-[40px] tw:shrink-0 tw:cursor-pointer',
                                            isDisabled && 'tw:cursor-not-allowed',
                                        )}
                                        style={{ backgroundColor: value }}
                                    ></span>
                                </InputAddon>
                                <ColorField
                                    className="tw:dir-ltr tw:grow"
                                    isDisabled={isDisabled}
                                    isInvalid={invalid}
                                    value={value}
                                    onChange={(newColor: Color | null) => {
                                        if (newColor) {
                                            const colorStr =
                                                newColor.toString(colorFormat)
                                            onChange?.(colorStr)
                                        }
                                    }}
                                >
                                    <Input className="tw:w-full" />
                                </ColorField>
                            </InputGroup>
                        )}
                    </div>
                </Pressable>

                {!isDisabled && (
                    <Popover
                        placement="top start"
                        offset={0}
                        className={cn(
                            'tw:w-[var(--trigger-width)] tw:overflow-auto tw:rounded-[2px] tw:bg-white tw:p-[12px] tw:shadow-[0px_3px_6px_0px_rgba(0,0,0,0.1)]',
                            thumbnailOnly && 'tw:w-[350px] tw:max-w-[90vw]',
                        )}
                    >
                        <Dialog className="tw:flex tw:flex-col tw:gap-[16px]">
                            {freeColors && (
                                <>
                                    <ColorArea
                                        colorSpace="hsb"
                                        xChannel="saturation"
                                        yChannel="brightness"
                                        className="tw:h-[152px] tw:w-full"
                                    >
                                        <ColorThumb className="tw:box-border tw:h-[20px] tw:w-[20px] tw:cursor-pointer tw:rounded-full tw:border-[2px] tw:border-white tw:shadow-[0_0_0_1px_black,inset_0_0_0_1px_black] tw:data-[focus-visible]:h-[24px] tw:data-[focus-visible]:w-[24px]" />
                                    </ColorArea>
                                    <ColorSlider
                                        className="tw:w-full"
                                        colorSpace="hsb"
                                        channel="hue"
                                    >
                                        <SliderTrack className="tw:h-[8px] tw:rounded-full">
                                            <ColorThumb className="tw:top-[4px] tw:h-[8px] tw:w-[8px] tw:cursor-pointer tw:rounded-[50%] tw:shadow-[0_0_0_2px_#fff,0px_4px_6px_0px_rgba(31,41,55,0.1),0px_2px_4px_0px_rgba(31,41,55,0.06)]" />
                                        </SliderTrack>
                                    </ColorSlider>
                                    <ColorSlider
                                        className="tw:w-full"
                                        colorSpace="hsb"
                                        channel="alpha"
                                    >
                                        <SliderTrack className="tw:h-[8px] tw:rounded-full">
                                            <ColorThumb className="tw:top-[4px] tw:h-[8px] tw:w-[8px] tw:cursor-pointer tw:rounded-[50%] tw:shadow-[0_0_0_2px_#fff,0px_4px_6px_0px_rgba(31,41,55,0.1),0px_2px_4px_0px_rgba(31,41,55,0.06)]" />
                                        </SliderTrack>
                                    </ColorSlider>
                                </>
                            )}
                            <ColorSwatchPicker className="tw:flex tw:flex-row tw:flex-wrap tw:gap-[7.5px]">
                                {palette.map((color) => (
                                    <ColorSwatchPickerItem
                                        color={color}
                                        className="tw:h-[34px] tw:w-[34px] tw:shrink tw:basis-[34px] tw:cursor-pointer tw:rounded-[2px] tw:shadow-[0_0_1px_0_rgba(0,0,0,0.4)]"
                                    >
                                        <ColorSwatch className="tw:h-[34px] tw:w-[34px] tw:cursor-pointer tw:rounded-[2px]" />
                                    </ColorSwatchPickerItem>
                                ))}
                                <ColorSwatchPickerItem
                                    color=""
                                    className="tw:mt-[8px] tw:w-full tw:shrink-0 tw:grow tw:basis-[100%] tw:cursor-pointer tw:rounded-[2px] tw:bg-[#E4EBF2] tw:p-[12px] tw:text-center tw:text-[#373B50] tw:text-sm tw:hover:bg-[#D1D8DE]"
                                >
                                    Clear
                                </ColorSwatchPickerItem>
                            </ColorSwatchPicker>
                        </Dialog>
                    </Popover>
                )}
            </DialogTrigger>
        </ReactAriaColorPicker>
    )
}

export { ColorPicker }
