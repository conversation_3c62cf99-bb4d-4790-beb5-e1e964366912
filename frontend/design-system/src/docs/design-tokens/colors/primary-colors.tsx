import type { JSX } from 'react'
import { Box } from '../../../components/box/box'
import { Flex } from '../../../components/flex/flex'
import { cn } from '../../../utils/cn'

export const PrimaryColors = (): JSX.Element => {
    return (
        <Flex className="flex-wrap items-end gap-3">
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-secondary-blue-dark',
                )}
            >
                <p className="text-white">secondary-blue-dark</p>
                <p className="text-white">#247DBD</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-secondary-blue',
                )}
            >
                <p className="text-white">secondary-blue</p>
                <p className="text-white">#00B0EF</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-primary-green',
                )}
            >
                <p className="text-white">primary-green</p>
                <p className="text-white">#13B272</p>
            </Box>
        </Flex>
    )
}
