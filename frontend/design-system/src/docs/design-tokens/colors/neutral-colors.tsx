import type { JSX } from 'react'
import { Box } from '../../../components/box/box'
import { Flex } from '../../../components/flex/flex'
import { cn } from '../../../utils/cn'

export const LightGrayColors = (): JSX.Element => {
    return (
        <Flex className="flex-wrap items-end gap-3">
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-light-gray-5',
                )}
            >
                <p>light-gray-5</p>
                <p>#9B9EB8</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-light-gray-4',
                )}
            >
                <p>light-gray-4</p>
                <p>#9EA1BA</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-light-gray-3',
                )}
            >
                <p>light-gray-3</p>
                <p>#B4B6C9</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-black',
                    'bg-light-gray-2',
                )}
            >
                <p>light-gray-2</p>
                <p>#E4EBF2</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-black',
                    'bg-light-gray-1',
                )}
            >
                <p>light-gray-1</p>
                <p>#F6F9FC</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-black',
                    'bg-light-gray',
                )}
            >
                <p>light-gray</p>
                <p>#F0F0F0</p>
            </Box>
        </Flex>
    )
}

export const DarkGrayColors = (): JSX.Element => {
    return (
        <Flex className="flex-wrap items-end gap-3">
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-primary-black',
                )}
            >
                <p>primary-black</p>
                <p>#202124</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-dark-gray-3',
                )}
            >
                <p>dark-gray-3</p>
                <p>#373B50</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-dark-gray-2',
                )}
            >
                <p>dark-gray-2</p>
                <p>#4E5381</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-dark-gray-1',
                )}
            >
                <p>dark-gray-1</p>
                <p>#75799D</p>
            </Box>
            <Box
                className={cn(
                    'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                    'bg-dark-gray',
                )}
            >
                <p>dark-gray</p>
                <p>#5F6368</p>
            </Box>
        </Flex>
    )
}
