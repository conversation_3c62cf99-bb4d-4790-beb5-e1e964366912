import type { JSX } from 'react'
import { Box } from '../../../components/box/box'
import { Flex } from '../../../components/flex/flex'
import { Stack } from '../../../components/stack/stack'
import { cn } from '../../../utils/cn'

export const SemanticColors = (): JSX.Element => {
    return (
        <Stack className="gap-5">
            <Flex className="flex-wrap items-end gap-3">
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                        'bg-danger-2',
                    )}
                >
                    <p>danger-2</p>
                    <p>#247DBD</p>
                </Box>
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                        'bg-danger-1',
                    )}
                >
                    <p>danger-1</p>
                    <p>#00B0EF</p>
                </Box>
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-black',
                        'bg-danger',
                    )}
                >
                    <p>danger</p>
                    <p>#FDF1F0</p>
                </Box>
            </Flex>
            <Flex className="flex-wrap items-end gap-3">
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                        'bg-warning-2',
                    )}
                >
                    <p>warning-2</p>
                    <p>#DF0000</p>
                </Box>
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                        'bg-warning-1',
                    )}
                >
                    <p>warning-1</p>
                    <p>#EB2121</p>
                </Box>
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-black',
                        'bg-warning',
                    )}
                >
                    <p>warning</p>
                    <p>#FFF9F0</p>
                </Box>
            </Flex>
            <Flex className="flex-wrap items-end gap-3">
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                        'bg-success-2',
                    )}
                >
                    <p>success-2</p>
                    <p>#0C744A</p>
                </Box>
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                        'bg-success-1',
                    )}
                >
                    <p>success-1</p>
                    <p>#1877F2</p>
                </Box>
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-black',
                        'bg-success',
                    )}
                >
                    <p>success</p>
                    <p>#F0FAF7</p>
                </Box>
            </Flex>
            <Flex className="flex-wrap items-end gap-3">
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                        'bg-info-2',
                    )}
                >
                    <p>info-2</p>
                    <p>#2356C2</p>
                </Box>
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-white',
                        'bg-info-1',
                    )}
                >
                    <p>info-1</p>
                    <p>#1877F2</p>
                </Box>
                <Box
                    className={cn(
                        'flex h-[100px] w-[200px] flex-col justify-between p-2 text-black',
                        'bg-info',
                    )}
                >
                    <p>info</p>
                    <p>#F8FAFF</p>
                </Box>
            </Flex>
        </Stack>
    )
}
