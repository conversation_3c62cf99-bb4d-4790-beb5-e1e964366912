import { type JSX, useState } from 'react'
import { <PERSON><PERSON> } from 'react-aria-components'
import { useCopyToClipboard, useDebounceCallback } from 'usehooks-ts'
import { AddIcon } from '../../../icons/add-icon'
import { AlertIcon } from '../../../icons/alert-icon'
import { AvailableIcon } from '../../../icons/available-icon'
import { BellIcon } from '../../../icons/bell-icon'
import { CalendarIcon } from '../../../icons/calendar-icon'
import { CheckIcon } from '../../../icons/check-icon'
import { CloseIcon } from '../../../icons/close-icon'
import { CollapseIcon } from '../../../icons/collapse-icon'
import { DeactivateIcon } from '../../../icons/deactivate-icon'
import { DownloadIcon } from '../../../icons/download-icon'
import { DragIcon } from '../../../icons/drag-icon'
import { EditIcon } from '../../../icons/edit-icon'
import { InvoiceIcon } from '../../../icons/invoice-icon'
import { MenuIcon } from '../../../icons/menu-icon'
import { MessageIcon } from '../../../icons/message-icon'
import { MoneyIcon } from '../../../icons/money-icon'
import { MoreIcon } from '../../../icons/more-icon'
import { OpenExternalIcon } from '../../../icons/open-external-icon'
import { PreviousIcon } from '../../../icons/previous-icon'
import { SaveIcon } from '../../../icons/save-icon'
import { SearchIcon } from '../../../icons/search-icon'
import { SortIcon } from '../../../icons/sort-icon'
import { TrashIcon } from '../../../icons/trash-icon'
import { TreasuryIcon } from '../../../icons/treasury-icon'
import { UploadIcon } from '../../../icons/upload-icon'
import { cn } from '../../../utils/cn'

type IconItemType = {
    icon: React.ReactNode
    code: string
}

const IconsList: IconItemType[] = [
    {
        icon: <AddIcon />,
        code: '<AddIcon />',
    },
    {
        icon: <AlertIcon />,
        code: '<AlertIcon />',
    },
    {
        icon: <AvailableIcon />,
        code: '<AvailableIcon />',
    },
    {
        icon: <BellIcon />,
        code: '<BellIcon />',
    },
    {
        icon: <CalendarIcon />,
        code: '<CalendarIcon />',
    },
    {
        icon: <CheckIcon />,
        code: '<CheckIcon />',
    },
    {
        icon: <CloseIcon />,
        code: '<CloseIcon />',
    },
    {
        icon: <CollapseIcon />,
        code: '<CollapseIcon />',
    },
    {
        icon: <DeactivateIcon />,
        code: '<DeactivateIcon />',
    },
    {
        icon: <DownloadIcon />,
        code: '<DownloadIcon />',
    },
    {
        icon: <DragIcon />,
        code: '<DragIcon />',
    },
    {
        icon: <EditIcon />,
        code: '<EditIcon />',
    },
    {
        icon: <InvoiceIcon />,
        code: '<InvoiceIcon />',
    },
    {
        icon: <MenuIcon />,
        code: '<MenuIcon />',
    },
    {
        icon: <MessageIcon />,
        code: '<MessageIcon />',
    },
    {
        icon: <MoneyIcon />,
        code: '<MoneyIcon />',
    },
    {
        icon: <MoreIcon />,
        code: '<MoreIcon />',
    },
    {
        icon: <OpenExternalIcon />,
        code: '<OpenExternalIcon />',
    },
    {
        icon: <PreviousIcon />,
        code: '<PreviousIcon />',
    },
    {
        icon: <SaveIcon />,
        code: '<SaveIcon />',
    },
    {
        icon: <SearchIcon />,
        code: '<SearchIcon />',
    },
    {
        icon: <SortIcon />,
        code: '<SortIcon />',
    },
    {
        icon: <TrashIcon />,
        code: '<TrashIcon />',
    },
    {
        icon: <TreasuryIcon />,
        code: '<TreasuryIcon />',
    },
    {
        icon: <UploadIcon />,
        code: '<UploadIcon />',
    },
]

const IconsListContainer = ({
    children,
}: {
    children: React.ReactNode
}): JSX.Element => {
    return (
        <div className={cn('flex flex-wrap justify-center gap-6')}>
            {children}
        </div>
    )
}

const IconBox = ({ children }: { children: React.ReactNode }): JSX.Element => {
    return (
        <div
            className={cn(
                'flex h-[180px] w-[180px] flex-col items-center justify-center gap-7 rounded-3xl border border-light-gray-2 bg-white',
            )}
        >
            {children}
        </div>
    )
}

const IconCodeBlock = ({
    children,
}: {
    children: React.ReactNode
}): JSX.Element => {
    return (
        <pre className={cn('mt-0 text-center font-monospace text-sm')}>
            {children}
        </pre>
    )
}

const IconItem = ({ icon }: { icon: IconItemType }): JSX.Element => {
    const [, copy] = useCopyToClipboard()
    const [copied, setCopied] = useState(false)
    const debounced = useDebounceCallback(setCopied, 2000)

    const handleCopy = () => {
        copy(icon.code)
        setCopied(true)
        debounced(false)
    }

    return (
        <IconBox>
            {icon.icon}
            <IconCodeBlock>{icon.code}</IconCodeBlock>
            <Button
                onClick={handleCopy}
                className={cn(
                    'rounded-2xl bg-light-gray-2 px-5 py-1 text-md',
                    copied && 'bg-green-500 text-white',
                )}
            >
                {copied ? 'Copied' : 'Copy'}
            </Button>
        </IconBox>
    )
}

export const Icongraphy = (): JSX.Element => {
    return (
        <IconsListContainer>
            {IconsList.map((iconItem) => (
                <IconItem key={iconItem.code} icon={iconItem} />
            ))}
        </IconsListContainer>
    )
}
