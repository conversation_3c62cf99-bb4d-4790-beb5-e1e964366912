import type { JSX, ReactNode } from 'react'
import { cn } from '../../utils/cn'

const Heading1 = ({ children }: { children: ReactNode }): JSX.Element => {
    return (
        <h1 className={cn('text-display-xl font-normal leading-high mb-5')}>
            {children}
        </h1>
    )
}

const Heading2 = ({ children }: { children: ReactNode }): JSX.Element => {
    return (
        <h2 className={cn('text-display-rg font-normal mt-10 mb-3')}>
            {children}
        </h2>
    )
}

const Heading3 = ({ children }: { children: ReactNode }): JSX.Element => {
    return <h3 className={cn('text-display-s mt-10 mb-3')}>{children}</h3>
}

export { Heading1, Heading2, Heading3 }
