import type { JSX } from 'react'
import { Box } from '../../../components/box/box'
import { Flex } from '../../../components/flex/flex'

export const BorderRadius = (): JSX.Element => {
    return (
        <Flex className="flex-wrap gap-5">
            <Box className="flex h-[100px] w-[200px] flex-col items-center justify-center rounded-xs border border-light-gray-2 bg-white">
                <p className="mt-3 text-black text-sm">xs</p>
            </Box>
        </Flex>
    )
}
