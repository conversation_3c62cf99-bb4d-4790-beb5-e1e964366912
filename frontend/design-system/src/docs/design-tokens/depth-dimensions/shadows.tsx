import type { JSX } from 'react'
import { Box } from '../../../components/box/box'
import { Flex } from '../../../components/flex/flex'

export const Shadows = (): JSX.Element => {
    return (
        <Flex className="flex-wrap gap-32">
            <Box className="flex h-[100px] w-[200px] flex-col items-center justify-center rounded-xs bg-white shadow-navbar">
                <p className="mt-3 text-black text-sm">navbar-shadow</p>
            </Box>
        </Flex>
    )
}
