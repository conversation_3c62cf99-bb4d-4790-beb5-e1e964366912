import type { JSX } from 'react'
import { Box } from '../../../components/box/box'
import { Flex } from '../../../components/flex/flex'
import { Stack } from '../../../components/stack/stack'
import { cn } from '../../../utils/cn'

const spaces = [
    { value: 0, className: 'w-0' },
    { value: 1, className: 'w-1' },
    { value: 2, className: 'w-2' },
    { value: 3, className: 'w-3' },
    { value: 4, className: 'w-4' },
    { value: 5, className: 'w-5' },
    { value: 6, className: 'w-6' },
    { value: 7, className: 'w-7' },
    { value: 8, className: 'w-8' },
    { value: 9, className: 'w-9' },
    { value: 10, className: 'w-10' },
    { value: 11, className: 'w-11' },
    { value: 12, className: 'w-12' },
    { value: 13, className: 'w-13' },
    { value: 14, className: 'w-14' },
    { value: 15, className: 'w-15' },
    { value: 16, className: 'w-16' },
    { value: 17, className: 'w-17' },
    { value: 18, className: 'w-18' },
    { value: 19, className: 'w-19' },
    { value: 20, className: 'w-20' },
]

const SpaceItemBox = ({
    children,
}: {
    children: React.ReactNode
}): JSX.Element => {
    return (
        <Flex className="flex w-[200px] items-center justify-start gap-3 bg-white">
            {children}
        </Flex>
    )
}

const SpaceItem = ({
    className,
    value,
}: {
    className: string
    value: number
}): JSX.Element => {
    return (
        <SpaceItemBox>
            <p className="w-3 text-black text-sm">{value}</p>
            <Box className={cn('h-5 bg-black', className)} />
            <p className="text-black text-sm">{value * 4}</p>
        </SpaceItemBox>
    )
}

export const Space = (): JSX.Element => {
    return (
        <Stack className="flex-wrap gap-5">
            {spaces.map((space) => (
                <SpaceItem
                    key={space.value}
                    className={space.className}
                    value={space.value}
                />
            ))}
        </Stack>
    )
}
