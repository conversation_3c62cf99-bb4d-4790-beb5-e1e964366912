import type { JSX } from 'react'
import { Box } from '../../../components/box/box'
import { Flex } from '../../../components/flex/flex'

const FontItemBox = ({
    children,
}: {
    children: React.ReactNode
}): JSX.Element => {
    return (
        <Box className="flex h-[150px] w-[200px] flex-col items-center justify-center gap-1 rounded-3xl border border-light-gray-2 bg-white">
            {children}
        </Box>
    )
}

export const FontSizes = (): JSX.Element => {
    return (
        <Flex className="flex-wrap gap-5">
            <FontItemBox>
                <p className="text-black text-display-xl">display-xl</p>
                <p>2rem</p>
                <p>32px</p>
            </FontItemBox>
            <FontItemBox>
                <p className="text-black text-display-l">display-l</p>
                <p>1.5rem</p>
                <p>24px</p>
            </FontItemBox>
            <FontItemBox>
                <p className="text-black text-display-rg">display-rg</p>
                <p>1.25rem</p>
                <p>20px</p>
            </FontItemBox>
            <FontItemBox>
                <p className="text-black text-display-s">display-s</p>
                <p>1.125rem</p>
                <p>18px</p>
            </FontItemBox>
            <FontItemBox>
                <p className="text-black text-large">large</p>
                <p>1rem</p>
                <p>16px</p>
            </FontItemBox>
            <FontItemBox>
                <p className="text-base text-black">base</p>
                <p>14px</p>
            </FontItemBox>
            <FontItemBox>
                <p className="text-annotation text-black">annotation</p>
                <p>12px</p>
            </FontItemBox>
        </Flex>
    )
}
