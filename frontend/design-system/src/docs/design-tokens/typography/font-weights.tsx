import type { JSX } from 'react'
import { Box } from '../../../components/box/box'
import { Flex } from '../../../components/flex/flex'

const FontItemBox = ({
    children,
}: {
    children: React.ReactNode
}): JSX.Element => {
    return (
        <Box className="flex h-[150px] w-[200px] flex-col items-center justify-center gap-1 rounded-3xl border border-light-gray-2 bg-white">
            {children}
        </Box>
    )
}

export const FontWeights = (): JSX.Element => {
    return (
        <Flex className="flex-wrap items-end gap-5">
            <FontItemBox>
                <p className="font-normal text-base text-black">normal</p>
                <p>400</p>
            </FontItemBox>
            <FontItemBox>
                <p className="font-medium text-base text-black">medium</p>
                <p>500</p>
            </FontItemBox>
            <FontItemBox>
                <p className="font-bold text-base text-black">bold</p>
                <p>700</p>
            </FontItemBox>
        </Flex>
    )
}
