import type { JSX } from 'react'
import { Box } from '../../../components/box/box'
import { Flex } from '../../../components/flex/flex'

const FontItemBox = ({
    children,
}: {
    children: React.ReactNode
}): JSX.Element => {
    return (
        <Box className="flex h-[150px] w-[200px] flex-col items-center justify-center gap-1 rounded-3xl border border-light-gray-2 bg-white">
            {children}
        </Box>
    )
}
export const LineHeights = (): JSX.Element => {
    return (
        <Flex className="flex-wrap gap-5">
            <FontItemBox>
                <p className="text-base text-black leading-normal">normal</p>
                <p>1.25</p>
            </FontItemBox>
            <FontItemBox>
                <p className="text-base text-black leading-high">high</p>
                <p>1.4</p>
            </FontItemBox>
        </Flex>
    )
}
