import {
    DocsContainer,
    type <PERSON>sContainerProps,
    Unstyled,
} from '@storybook/addon-docs/blocks'
import type { JSX, PropsWithChildren } from 'react'

const DocumentContainer = ({
    children,
    ...props
}: PropsWithChildren<DocsContainerProps>): JSX.Element => {
    return (
        <DocsContainer {...props}>
            <Unstyled>
                {children}
            </Unstyled>
        </DocsContainer>
    )
}

export { DocumentContainer }
