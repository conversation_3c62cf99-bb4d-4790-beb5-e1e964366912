import type { JSX } from 'react'
import { cn } from '../utils/cn'

const LoadingIcon = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="loading"
            className={cn('animate-spin text-current', className)}
            width={size}
            height={size}
            role="img"
            viewBox="0 0 28 29"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>loading</title>
            <path
                d="M14 2.83301C11.6925 2.83281 9.43669 3.5169 7.51793 4.79877C5.59917 6.08063 4.10362 7.9027 3.22042 10.0345C2.33722 12.1664 2.10603 14.5123 2.55609 16.7755C3.00615 18.9088 4.11725 20.9877 5.74887 22.7495C7.38049 24.3812 9.45935 25.4925 11.7225 25.9428C13.9858 26.393 16.3316 26.162 18.4636 25.279C20.5955 24.396 22.4177 22.9006 23.6997 20.9819C24.9818 19.0633 25.666 16.8076 25.666 14.5"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
            />
        </svg>
    )
}

export { LoadingIcon }
