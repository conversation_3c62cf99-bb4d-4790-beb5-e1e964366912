import type { JSX } from 'react'
import { cn } from '../utils/cn'

const CheckCircle = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="check circle"
            className={cn('text-current', className)}
            width={size}
            height={size}
            viewBox="0 0 29 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>check circle</title>
            <path
                d="M14.5 2.203C12.1924 2.20281 9.93666 2.88689 8.0179 4.16876C6.09914 5.45063 4.60359 7.27269 3.72039 9.40454C2.83719 11.5364 2.606 13.8823 3.05606 16.1455C3.50612 18.4088 4.61722 20.4877 6.24884 22.1195C7.88046 23.7512 9.95932 24.8625 12.2225 25.3128C14.4857 25.763 16.8316 25.532 18.9635 24.649C21.0955 23.766 22.9177 22.2706 24.1997 20.3519C25.4817 18.4333 26.166 16.1776 26.166 13.87C26.1573 10.7786 24.9254 7.81623 22.7396 5.63016C20.5537 3.44409 17.5914 2.21197 14.5 2.203ZM12.166 19.703L6.33301 13.87L7.97801 12.225L12.166 16.403L21.021 7.548L22.666 9.203L12.166 19.703Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { CheckCircle }
