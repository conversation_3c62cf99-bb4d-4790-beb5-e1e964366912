import type { JSX } from 'react'
import { cn } from '../utils/cn'

const ContentSaveIcon = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="content save"
            className={cn('text-current', className)}
            width={size}
            height={size}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>content save</title>
            <path
                d="M12.5 7.5H4.16667V4.16667H12.5M10 15.8333C9.33696 15.8333 8.70107 15.5699 8.23223 15.1011C7.76339 14.6323 7.5 13.9964 7.5 13.3333C7.5 12.6703 7.76339 12.0344 8.23223 11.5656C8.70107 11.0967 9.33696 10.8333 10 10.8333C10.663 10.8333 11.2989 11.0967 11.7678 11.5656C12.2366 12.0344 12.5 12.6703 12.5 13.3333C12.5 13.9964 12.2366 14.6323 11.7678 15.1011C11.2989 15.5699 10.663 15.8333 10 15.8333ZM14.1667 2.5H4.16667C3.24167 2.5 2.5 3.25 2.5 4.16667V15.8333C2.5 16.2754 2.67559 16.6993 2.98816 17.0118C3.30072 17.3244 3.72464 17.5 4.16667 17.5H15.8333C16.2754 17.5 16.6993 17.3244 17.0118 17.0118C17.3244 16.6993 17.5 16.2754 17.5 15.8333V5.83333L14.1667 2.5Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { ContentSaveIcon }
