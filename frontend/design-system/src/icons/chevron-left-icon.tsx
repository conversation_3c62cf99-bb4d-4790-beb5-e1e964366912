import type { JSX } from 'react'
import { cn } from '../utils/cn'

const ChevronLeftIcon = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="chevron left"
            className={cn('text-current', className)}
            width={size}
            height={size}
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>chevron left</title>
            <path
                d="M15.5615 16.5801L10.9815 12.0001L15.5615 7.41012L14.1515 6.00012L8.15149 12.0001L14.1515 18.0001L15.5615 16.5801Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { ChevronLeftIcon }
