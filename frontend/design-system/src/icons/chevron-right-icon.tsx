import type { JSX } from 'react'
import { cn } from '../utils/cn'

const ChevronRightIcon = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="chevron right"
            className={cn('text-current', className)}
            width={size}
            height={size}
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>chevron right</title>
            <path
                d="M8.74149 16.5801L13.3215 12.0001L8.74149 7.41012L10.1515 6.00012L16.1515 12.0001L10.1515 18.0001L8.74149 16.5801Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { ChevronRightIcon }
