import type { JSX } from 'react'
import { cn } from '../utils/cn'

const CloseIcon2 = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="close"
            className={cn('text-current', className)}
            width={size}
            height={size}
            role="img"
            viewBox="0 0 29 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>close</title>
            <path
                d="M22.666 7.56999L21.021 5.92499L14.499 12.447L7.978 5.92499L6.333 7.56999L12.855 14.092L6.333 20.613L7.978 22.258L14.5 15.736L21.022 22.258L22.667 20.613L16.145 14.091L22.666 7.56999Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { CloseIcon2 }
