import type { JSX } from 'react'
import { cn } from '../utils/cn'

const InfoCircle = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="info circle"
            className={cn('text-current', className)}
            width={size}
            height={size}
            viewBox="0 0 29 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>info circle</title>
            <path
                d="M15.666 10.5H13.333V8.16601H15.666M15.666 19.833H13.333V12.833H15.666M14.5 2.33301C12.1925 2.33281 9.93669 3.0169 8.01793 4.29877C6.09917 5.58063 4.60362 7.4027 3.72042 9.53455C2.83722 11.6664 2.60603 14.0123 3.05609 16.2755C3.50615 18.5388 4.61725 20.6177 6.24887 22.2495C7.88049 23.8812 9.95935 24.9925 12.2225 25.4428C14.4858 25.893 16.8316 25.662 18.9636 24.779C21.0955 23.896 22.9177 22.4006 24.1997 20.4819C25.4818 18.5633 26.166 16.3076 26.166 14C26.166 10.9059 24.937 7.9385 22.7492 5.75055C20.5614 3.56259 17.5941 2.33327 14.5 2.33301Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { InfoCircle }
