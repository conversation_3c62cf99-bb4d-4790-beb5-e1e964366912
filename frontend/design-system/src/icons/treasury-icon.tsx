import type { JSX } from 'react'

const TreasuryIcon = (): JSX.Element => {
    return (
        <svg
            role="img"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            aria-label="treasury icon"
        >
            <path
                d="M3 3H21V7H3V3ZM4 8H20V21H4V8ZM9.5 11C9.36739 11 9.24021 11.0527 9.14645 11.1464C9.05268 11.2402 9 11.3674 9 11.5V13H15V11.5C15 11.3674 14.9473 11.2402 14.8536 11.1464C14.7598 11.0527 14.6326 11 14.5 11H9.5Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { TreasuryIcon }
