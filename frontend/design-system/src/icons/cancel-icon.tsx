import type { JSX } from 'react'
import { cn } from '../utils/cn'

const CancelIcon = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="cancel"
            className={cn('text-current', className)}
            width={size}
            height={size}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>cancel</title>
            <path
                d="M15.8327 5.34167L14.6577 4.16667L9.99935 8.82501L5.34102 4.16667L4.16602 5.34167L8.82435 10L4.16602 14.6583L5.34102 15.8333L9.99935 11.175L14.6577 15.8333L15.8327 14.6583L11.1743 10L15.8327 5.34167Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { CancelIcon }
