import type { JSX } from 'react'
import { cn } from '../utils/cn'

const AlertRhombus = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="alert rhombus"
            className={cn('text-current', className)}
            width={size}
            height={size}
            viewBox="0 0 29 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>alert rhombus</title>
            <path
                d="M14.503 2.33301C13.8853 2.33582 13.2938 2.58319 12.858 3.02101L3.52504 12.355C3.30583 12.5692 3.13164 12.8251 3.01271 13.1076C2.89378 13.3901 2.83252 13.6935 2.83252 14C2.83252 14.3065 2.89378 14.6099 3.01271 14.8924C3.13164 15.1749 3.30583 15.4308 3.52504 15.645L12.858 24.978C13.0723 25.1972 13.3281 25.3714 13.6106 25.4903C13.8931 25.6093 14.1965 25.6705 14.503 25.6705C14.8096 25.6705 15.113 25.6093 15.3955 25.4903C15.678 25.3714 15.9338 25.1972 16.148 24.978L25.481 15.645C25.7003 15.4308 25.8745 15.1749 25.9934 14.8924C26.1123 14.6099 26.1736 14.3065 26.1736 14C26.1736 13.6935 26.1123 13.3901 25.9934 13.1076C25.8745 12.8251 25.7003 12.5692 25.481 12.355L16.148 3.02101C15.7122 2.58319 15.1208 2.33582 14.503 2.33301ZM13.336 8.16601H15.669V15.166H13.336V8.16601ZM13.336 17.499H15.669V19.833H13.336V17.499Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { AlertRhombus }
