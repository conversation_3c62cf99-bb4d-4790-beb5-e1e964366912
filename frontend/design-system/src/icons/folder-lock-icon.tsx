import type { JSX } from 'react'
import { cn } from '../utils/cn'

const FolderLockIcon = ({
    size = 16,
    className,
}: {
    size?: number | string
    className?: string
}): JSX.Element => {
    return (
        <svg
            aria-label="folder lock"
            className={cn('text-current', className)}
            width={size}
            height={size}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <title>folder lock</title>
            <path
                d="M2.50065 3.33333C1.57565 3.33333 0.833984 4.07499 0.833984 4.99999V15C0.833984 15.442 1.00958 15.8659 1.32214 16.1785C1.6347 16.4911 2.05862 16.6667 2.50065 16.6667H8.55065C9.64232 18.2333 11.4256 19.1667 13.334 19.1667C14.8811 19.1667 16.3648 18.5521 17.4588 17.4581C18.5527 16.3642 19.1673 14.8804 19.1673 13.3333C19.1673 11.8083 18.5673 10.35 17.5006 9.25833V6.66666C17.5006 5.74166 16.7506 4.99999 15.834 4.99999H9.16732L7.50065 3.33333H2.50065ZM13.334 9.16666C14.4391 9.16666 15.4989 9.60565 16.2803 10.387C17.0617 11.1685 17.5006 12.2283 17.5006 13.3333C17.5006 14.4384 17.0617 15.4982 16.2803 16.2796C15.4989 17.061 14.4391 17.5 13.334 17.5C12.2289 17.5 11.1691 17.061 10.3877 16.2796C9.6063 15.4982 9.16732 14.4384 9.16732 13.3333C9.16732 12.2283 9.6063 11.1685 10.3877 10.387C11.1691 9.60565 12.2289 9.16666 13.334 9.16666ZM12.5007 9.99999V14.1667L15.509 15.9667L16.134 14.95L13.7506 13.5417V9.99999H12.5007Z"
                fill="currentColor"
            />
        </svg>
    )
}

export { FolderLockIcon }
