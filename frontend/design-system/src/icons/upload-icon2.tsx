import type { JSX } from "react";

const UploadIcon2 = (): JSX.Element => {
  return (
    <svg
      width="67"
      height="64"
      viewBox="0 0 67 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M33.5002 33.547L44.8077 44.8537L41.0208 48.6403L36.167 43.787V58.667H30.8333V43.787L25.9796 48.6403L22.1926 44.8537L33.5002 33.547ZM33.5002 5.33366C36.6293 5.33366 39.5807 6.08033 42.3542 7.57366C44.9855 8.99588 47.1724 10.9692 48.9147 13.4937C50.6571 16.0181 51.706 18.8092 52.0616 21.867C54.124 22.4359 55.973 23.4048 57.6087 24.7737C59.2444 26.1425 60.5245 27.8048 61.449 29.7603C62.3735 31.7159 62.8358 33.7959 62.8358 36.0003C62.8358 38.5248 62.2491 40.8537 61.0757 42.987C59.9022 45.1203 58.2932 46.8714 56.2486 48.2403C54.204 49.6092 51.9549 50.4003 49.5014 50.6137V45.227C50.9949 45.0137 52.355 44.4714 53.5817 43.6003C54.8085 42.7292 55.7686 41.627 56.462 40.2937C57.1554 38.9603 57.502 37.5292 57.502 36.0003C57.502 34.2937 57.0842 32.7292 56.2486 31.307C55.413 29.8848 54.284 28.7559 52.8617 27.9203C51.4394 27.0848 49.8748 26.667 48.168 26.667C47.5991 26.667 47.0479 26.7203 46.5145 26.827C46.7279 25.9025 46.8345 24.9603 46.8345 24.0003C46.8345 21.5826 46.2389 19.3514 45.0477 17.307C43.8565 15.2626 42.2386 13.6448 40.194 12.4537C38.1494 11.2626 35.9181 10.667 33.5002 10.667C31.0822 10.667 28.8509 11.2626 26.8063 12.4537C24.7617 13.6448 23.1438 15.2626 21.9526 17.307C20.7614 19.3514 20.1658 21.5826 20.1658 24.0003C20.1658 24.9603 20.2725 25.9025 20.4858 26.827C19.9169 26.7203 19.3657 26.667 18.8324 26.667C17.1256 26.667 15.561 27.0848 14.1387 27.9203C12.7163 28.7559 11.5874 29.8848 10.7517 31.307C9.91611 32.7292 9.4983 34.2937 9.4983 36.0003C9.4983 37.4937 9.82722 38.8892 10.485 40.187C11.1429 41.4848 12.0407 42.5692 13.1786 43.4403C14.3165 44.3114 15.5966 44.8892 17.0189 45.1737L17.4989 45.227V50.6137C15.0454 50.4003 12.7963 49.6092 10.7517 48.2403C8.70713 46.8714 7.09811 45.1203 5.92469 42.987C4.75126 40.8537 4.16455 38.5248 4.16455 36.0003C4.16455 33.7959 4.62681 31.7159 5.55133 29.7603C6.47584 27.8048 7.75594 26.1425 9.39163 24.7737C11.0273 23.4048 12.8763 22.4359 14.9387 21.867C15.2943 18.8092 16.3433 16.0181 18.0856 13.4937C19.828 10.9692 22.0148 8.99588 24.6462 7.57366C27.4197 6.08033 30.371 5.33366 33.5002 5.33366Z"
        fill="#9CA3AF"
      />
    </svg>
  );
};

export { UploadIcon2 };