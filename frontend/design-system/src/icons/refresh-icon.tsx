import type { JSX } from "react";

interface RefreshIconProps {
  className?: string;
  fill?: string;
}

const RefreshIcon = ({
  className = "",
  fill = "#202124",
}: RefreshIconProps): JSX.Element => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M14.709 5.55625C13.5007 4.2875 11.8423 3.5 10.0007 3.5C8.23254 3.5 6.53685 4.2375 5.28661 5.55025C4.03636 6.86301 3.33398 8.64348 3.33398 10.5C3.33398 12.3565 4.03636 14.137 5.28661 15.4497C6.53685 16.7625 8.23254 17.5 10.0007 17.5C13.109 17.5 15.7007 15.2688 16.4423 12.25H14.709C14.0257 14.2887 12.1757 15.75 10.0007 15.75C8.67457 15.75 7.4028 15.1969 6.46512 14.2123C5.52744 13.2277 5.00065 11.8924 5.00065 10.5C5.00065 9.10761 5.52744 7.77226 6.46512 6.78769C7.4028 5.80312 8.67457 5.25 10.0007 5.25C11.384 5.25 12.6173 5.85375 13.5173 6.8075L10.834 9.625H16.6673V3.5L14.709 5.55625Z"
        fill={fill}
      />
    </svg>
  );
};

export { RefreshIcon };