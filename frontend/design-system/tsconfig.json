{"compilerOptions": {"target": "esnext", "jsx": "react-jsx", "lib": ["dom", "dom.iterable", "esnext"], "moduleDetection": "force", "module": "preserve", "moduleResolution": "bundler", "resolveJsonModule": true, "types": ["node"], "strict": true, "noUnusedLocals": true, "declaration": true, "emitDeclarationOnly": true, "esModuleInterop": true, "isolatedModules": true, "verbatimModuleSyntax": true, "skipLibCheck": true, "isolatedDeclarations": true}, "include": ["src", ".storybook/**/*"]}