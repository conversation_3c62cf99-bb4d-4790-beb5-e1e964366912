/** Attendance Flags Table **/
CREATE TABLE IF NOT EXISTS `attendance_flags`
  (
     `id`          INT NOT NULL auto_increment,
     `name`        VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
     `description` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
     `color`       VARCHAR(7) NULL DEFAULT NULL,
     `condition`   TEXT NOT NULL,
     `formula`     TEXT NULL DEFAULT NULL,
     `status`      BOOLEAN NOT NULL DEFAULT true,
     `created`     TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,
     `modified`    TIMESTAMP   DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
     `deleted_at`  DATETIME DEFAULT NULL,
     PRIMARY KEY (`id`),
     INDEX(`status`),
     INDEX(`deleted_at`)
  )
engine = innodb;

/** Shift Attendance Flags Table **/
CREATE TABLE IF NOT EXISTS `shift_attendance_flags`
(
    `shift_id` INT NULL DEFAULT 0,
    `attendance_flag_id` INT NULL DEFAULT 0,
    INDEX (`shift_id`),
    INDEX (`attendance_flag_id`)
)
engine = innodb;

/** Allocated shifts **/
CREATE TABLE IF NOT EXISTS `allocated_shifts`
  (
     `id`                INT NOT NULL auto_increment,
     `name`              VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
     `start_date`        Date NOT NULL,
     `end_date`          Date NOT NULL,
     `shift_id`          INT NOT NULL,
     `shift_type`        ENUM('primary','secondary') NOT NULL DEFAULT 'primary',
     `criteria`          ENUM('rule_selection','employee_selection') NOT NULL DEFAULT 'rule_selection',
     `criteria_shift_id` INT NULL DEFAULT NULL,
     `criteria_department_id` INT NULL DEFAULT NULL,
     `criteria_designation_id` INT NULL DEFAULT NULL,
     `criteria_branch_id` INT NULL DEFAULT NULL,
     `exclude_criteria`  VARCHAR (255) NULL DEFAULt NULL,
     `created`           TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     `modified`          TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
     `deleted_at`        DATETIME DEFAULT NULL,
     PRIMARY KEY (`id`),
     INDEX (`shift_id`),
     INDEX (`criteria_shift_id`),
     INDEX (`criteria_department_id`),
     INDEX (`criteria_designation_id`),
     INDEX (`criteria_branch_id`),
     INDEX(`deleted_at`)

  )
engine = innodb;

/** holiday lists table **/
CREATE TABLE IF NOT EXISTS `holiday_lists`
  (
     `id`         INT NOT NULL auto_increment,
     `name`       VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
     `count`      INT NOT NULL DEFAULT 0,
     `created`    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     `modified`   TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
     `deleted_at` DATETIME DEFAULT NULL,
     `can_work`   tinyint(1) DEFAULT '0',
     PRIMARY KEY (`id`),
     INDEX(`deleted_at`)

  )
engine = innodb;


/** holiday list days table **/
CREATE TABLE IF NOT EXISTS `holiday_list_days`
  (
     `id`              INT NOT NULL auto_increment,
     `date`            Date NOT NULL,
     `title`           VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
     `holiday_list_id` INT NOT NULL,
     `created`         TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     `modified`        TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
     `deleted_at`      DATETIME DEFAULT NULL,
     PRIMARY KEY (`id`),
     INDEX (`holiday_list_id`),
     INDEX(`deleted_at`)

  )
engine = innodb;

CREATE TABLE IF NOT EXISTS `leave_types`
    (
       `id`                                INT NOT NULL auto_increment,
       `name`                              VARCHAR(255) NOT NULL,
       `description`                       TEXT NULL,
       `max_leave_days_allowed_per_year`   INT NOT NULL,
       `max_continuous_leave_days_allowed` INT NULL,
       `applied_after`                     INT NULL,
       `need_permission`                   BOOLEAN NOT NULL DEFAULT false,
       `color`                             VARCHAR(10) NULL,
       `condition`                         TEXT NULL,
       `override_weekend`                  BOOLEAN NOT NULL DEFAULT false,
       `allow_outside_policy`              BOOLEAN NOT NULL DEFAULT true,
       `created_at`                        DATETIME NOT NULL,
       `updated_at`                        DATETIME NOT NULL,
       `deleted_at`                        DATETIME NULL,
       PRIMARY KEY (`id`),
       INDEX(`deleted_at`),
       UNIQUE (`name`)
    )
  engine = innodb;

CREATE TABLE IF NOT EXISTS `staff_holiday_lists` (
    `staff_id` INT NOT NULL ,
    `holiday_list_id` INT NOT NULL ,
    PRIMARY KEY (`staff_id`, `holiday_list_id`)
) ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `leave_policy_types` (
    `leave_policy_id` INT NOT NULL ,
    `leave_type_id` INT NOT NULL ,
    `leave_type_order` INT NOT NULL ,
    PRIMARY KEY (`leave_policy_id`, `leave_type_id`)
) ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `leave_policies` (
    `id` INT NOT NULL AUTO_INCREMENT ,
    `name` VARCHAR(50) NOT NULL ,
    `description` TEXT NULL DEFAULT NULL ,
    `status` TINYINT NOT NULL ,
    `deleted_at` DATETIME NULL DEFAULT NULL ,
    `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `modified` DATETIME on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    PRIMARY KEY (`id`),
    INDEX(`status`),
    INDEX(`deleted_at`)
) ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `attendance_permissions` (
    `id` INT NOT NULL AUTO_INCREMENT ,
    `staff_id` INT NOT NULL ,
    `from_date` DATE NOT NULL ,
    `to_date` DATE NOT NULL ,
    `type` ENUM('leave','delay', 'early', 'half_leave') NOT NULL ,
    `leave_type_id` INT NULL DEFAULT NULL ,
    `late_time` INT NULL DEFAULT NULL ,
    `early_time` INT NULL DEFAULT NULL ,
    `note` TEXT DEFAULT NULL ,
    `application_date` DATE NULL DEFAULT NULL ,
    `shift_type` ENUM('primary','secondary') NOT NULL DEFAULT 'primary' ,
    `deleted_at` TIMESTAMP NULL DEFAULT NULL ,
    `created` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `modified` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    PRIMARY KEY (`id`),
    INDEX(`deleted_at`),
    CONSTRAINT `attendance_permissions_staffs_fk` FOREIGN KEY (`staff_id`) REFERENCES `staffs`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT `attendance_permissions_leave_types_fk` FOREIGN KEY (`leave_type_id`) REFERENCES `leave_types`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB;

/** Attendance Sheets Table **/
CREATE TABLE IF NOT EXISTS `attendance_sheets` (
    `auto_id` INT NOT NULL AUTO_INCREMENT,
    `id` CHAR(36) COLLATE utf8_unicode_ci  NOT NULL ,
    `staff_id` INT NOT NULL ,
    `date_from` DATE NOT NULL ,
    `date_to` DATE NOT NULL ,
    `status` ENUM('pending','approved') NOT NULL DEFAULT 'pending' ,
    `working_days` INT NOT NULL ,
    `actual_working_hours` FLOAT NOT NULL ,
    `expected_working_hours` FLOAT NOT NULL ,
    `present_days` DOUBLE NOT NULL ,
    `absence_days` INT NOT NULL ,
    `leaves` DOUBLE NOT NULL ,
    `fiscal_start_date` DATE NOT NULL ,
    `sign_in_only_count` INT NOT NULL ,
    `sign_out_only_count` INT NOT NULL ,
    `total_delay_amount` FLOAT NOT NULL ,
    `total_delay_count` INT NOT NULL ,
    `total_early_leave_amount` FLOAT NOT NULL ,
    `total_early_leave_count` INT NOT NULL ,
    `off_working_days` FLOAT  NOT NULL DEFAULT 0,
    `modified` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `created` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    PRIMARY KEY (`id`),
    INDEX(`status`),
    UNIQUE (`auto_id`),
    CONSTRAINT `attendance_sheet_staff_fk` FOREIGN KEY (`staff_id`) REFERENCES `staffs`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `attendance_days` (
    `id` CHAR(36) COLLATE utf8_unicode_ci  NOT NULL,
    `staff_id` INT NOT NULL ,
    `attendance_sheet_id` CHAR(36) COLLATE utf8_unicode_ci  NULL ,
    `date` DATE NOT NULL ,
    `status` ENUM('present','absent','sign_in_only','sign_out_only','leave','day_off') NOT NULL ,
    `sign_in` DATETIME NULL DEFAULT NULL ,
    `sign_out` DATETIME NULL DEFAULT NULL ,
    `shift_id` INT NULL DEFAULT NULL ,
    `on_duty` DATETIME NULL DEFAULT NULL ,
    `beginning_in` DATETIME NULL DEFAULT NULL ,
    `beginning_out` DATETIME NULL DEFAULT NULL ,
    `off_duty` DATETIME NULL DEFAULT NULL ,
    `ending_in` DATETIME NULL DEFAULT NULL ,
    `ending_out` DATETIME NULL DEFAULT NULL ,
    `expected_working_hours` FLOAT NULL DEFAULT NULL ,
    `actual_working_hours` FLOAT NULL DEFAULT NULL ,
    `leave_type_id` INT NULL DEFAULT NULL ,
    `leave_count` DOUBLE NOT NULL DEFAULT '0',
    `attendance_permission_id` INT NULL DEFAULT NULL ,
    `attendance_delay` FLOAT NULL DEFAULT NULL ,
    `early_leave` FLOAT NULL DEFAULT NULL ,
    `calculation_type` ENUM('automatic','manual') NOT NULL DEFAULT 'automatic',
    `day_off_type` ENUM('holiday','no_shift','week_day_off') NULL DEFAULT NULL ,
    `day_off_title` VARCHAR(100) NULL DEFAULT NULL,
    `is_secondary` TINYINT(1) NOT NULL DEFAULT '0',
    `notes` TEXT NULL DEFAULT NULL ,
    `can_work_on_off_days` tinyint(1) DEFAULT '0',
    `deleted_at` DATETIME NULL DEFAULT NULL ,
    `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `modified` DATETIME on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    PRIMARY KEY (`id`),
    CONSTRAINT `attendance_day_sheet_fk` FOREIGN KEY (`attendance_sheet_id`) REFERENCES `attendance_sheets`(`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
    CONSTRAINT `attendance_days_shifts_fk` FOREIGN KEY (`shift_id`) REFERENCES `shifts`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT `attendance_days_leave_types_fk` FOREIGN KEY (`leave_type_id`) REFERENCES `leave_types`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT `attendance_days_attendance_permissions_fk` FOREIGN KEY (`attendance_permission_id`) REFERENCES `attendance_permissions`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    INDEX `attendance_days_date_index` (`date`),
    INDEX `attendance_days_status_index` (`status`),
    INDEX `attendance_days_staff_date_status_index` (`staff_id`, `date` DESC, `status`),
    INDEX `attendance_days_staff_id_index` (`staff_id`),
    INDEX(`deleted_at`)
) ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `attendance_logs` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT ,
    `staff_id` INT NOT NULL ,
    `time` DATETIME NOT NULL ,
    `session_id` INT NULL DEFAULT NULL ,
    `status` ENUM('draft','sign_in','sign_out','repeated_sign_in','repeated_sign_out','invalid') NOT NULL DEFAULT 'draft' ,
    `source_id` INT NULL DEFAULT NULL ,
    `source_name` VARCHAR(50) NULL DEFAULT NULL ,
    `source_type` ENUM('machine','supervisor','file','self') NULL DEFAULT NULL ,
    `source_method` VARCHAR(50) NULL DEFAULT NULL ,
    `attendance_day_id` CHAR(38) COLLATE utf8_unicode_ci NULL DEFAULT NULL ,
    `attendance_restriction_log_id` INT NULL DEFAULT NULL,
    `deleted_at` DATETIME NULL DEFAULT NULL ,
    `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `modified` DATETIME on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    PRIMARY KEY (`id`),
    INDEX(`deleted_at`),
    INDEX `attendance_logs_time_index` (`time`),
    INDEX `attendance_logs_session_id_index` (`session_id`),
    INDEX `attendance_logs_status_index` (`status`),
    INDEX `attendance_logs_source_id_index` (`source_id`),
    INDEX `attendance_logs_source_name_index` (`source_name`),
    INDEX `attendance_logs_source_type_index` (`source_type`),
    INDEX `attendance_logs_source_method_index` (`source_method`),
    INDEX `attendance_logs_attendance_day_id_index` (`attendance_day_id`),
    CONSTRAINT `attendance_logs_staffs_fk` FOREIGN KEY (`staff_id`) REFERENCES `staffs`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT `attendance_day_logs_ibfk_1` FOREIGN KEY (`attendance_day_id`) REFERENCES `attendance_days`(`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
    UNIQUE `attendance_logs_time_staff_unique` (`time`, `staff_id`)
) ENGINE = MyISAM;

CREATE TABLE IF NOT EXISTS `attendance_sessions` (
    `id` INT NOT NULL AUTO_INCREMENT ,
    `open_time` DATETIME NOT NULL ,
    `status` ENUM('closed','open') NOT NULL DEFAULT 'open',
    `signs_count` INT NOT NULL DEFAULT 0 ,
    `close_time` DATETIME NULL DEFAULT NULL ,
    `source_id` INT NULL DEFAULT NULL ,
    `source_name` VARCHAR(50) NULL DEFAULT NULL ,
    `source_type` ENUM('machine','supervisor','file') NULL DEFAULT NULL ,
    `source_method` VARCHAR(50) NULL DEFAULT NULL ,
    `deleted_at` DATETIME NULL DEFAULT NULL ,
    `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `modified` DATETIME on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    PRIMARY KEY (`id`),
    INDEX `attendance_sessions_open_time_index` (`open_time`),
    INDEX `attendance_sessions_status_index` (`status`),
    INDEX `attendance_sessions_signs_count_index` (`signs_count`),
    INDEX `attendance_sessions_close_time_index` (`close_time`),
    INDEX `attendance_sessions_source_id_index` (`source_id`),
    INDEX `attendance_sessions_source_name_index` (`source_name`),
    INDEX `attendance_sessions_source_type_index` (`source_type`),
    INDEX `attendance_sessions_source_method_index` (`source_method`),
    INDEX(`deleted_at`)
) ENGINE = InnoDB;

ALTER TABLE `staff_info` ADD CONSTRAINT `staff_info_leave_policies_fk` FOREIGN KEY (`leave_policy_id`) REFERENCES `leave_policies`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

CREATE TABLE IF NOT EXISTS `attendance_day_flags` (
	`attendance_day_id` CHAR(36) COLLATE utf8_unicode_ci NOT NULL,
	`attendance_flag_id` INTEGER NOT NULL,
	`value` FLOAT NOT NULL DEFAULT 0,
	PRIMARY KEY (`attendance_day_id`, `attendance_flag_id`),
	FOREIGN KEY attendance_day_flags_attendance_day_fk(`attendance_day_id`) REFERENCES `attendance_days` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
	FOREIGN KEY attendance_day_flags_attendance_flag_fk(`attendance_flag_id`) REFERENCES `attendance_flags` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
)ENGINE = InnoDB;

/** Attendance Sheet Leaves Table **/
CREATE TABLE IF NOT EXISTS `attendance_sheet_leaves` (
    `attendance_sheet_id` CHAR(36) COLLATE utf8_unicode_ci  NOT NULL ,
    `leave_type_id` INT NOT NULL ,
    `total_leave_taken` DOUBLE NOT NULL ,
    `total_leave_taken_before` DOUBLE NOT NULL ,
    `total_leave_after` DOUBLE NOT NULL ,
    `leaves_credit_before` DOUBLE NOT NULL ,
    `leaves_credit_after` DOUBLE NOT NULL  ,
    PRIMARY KEY (`attendance_sheet_id`,`leave_type_id`),
    CONSTRAINT `attendance_sheet_sheet_leaves_fk` FOREIGN KEY (`attendance_sheet_id`) REFERENCES `attendance_sheets`(`id`) ON DELETE CASCADE ON UPDATE RESTRICT ,
    CONSTRAINT `attendance_sheet_leave_fk` FOREIGN KEY (`leave_type_id`) REFERENCES `leave_types`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB;

/** Attendance Sheet Flags Table **/
CREATE TABLE IF NOT EXISTS `attendance_sheet_flags` (
    `attendance_sheet_id` CHAR(36) COLLATE utf8_unicode_ci  NOT NULL ,
    `attendance_flag_id` INT NOT NULL ,
    `total_count` INT NOT NULL ,
    `total_value` FLOAT NOT NULL ,
    PRIMARY KEY (`attendance_sheet_id`,`attendance_flag_id`),
    CONSTRAINT `attendance_sheet_sheet_flags_fk` FOREIGN KEY (`attendance_sheet_id`) REFERENCES `attendance_sheets`(`id`) ON DELETE CASCADE ON UPDATE RESTRICT ,
    CONSTRAINT `attendance_sheet_flag_fk` FOREIGN KEY (`attendance_flag_id`) REFERENCES `attendance_flags`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB;

/** Machines Tables **/
CREATE TABLE IF NOT EXISTS `machines` ( 
	`id` INT NOT NULL AUTO_INCREMENT , 
	`name` VARCHAR(50) NOT NULL ,
    `machine_type_key` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
	`serial_number` VARCHAR(50) NULL DEFAULT NULL ,
	`host` VARCHAR(50) NOT NULL , 
	`port` VARCHAR(50) NULL DEFAULT NULL , 
	`communication_key` VARCHAR(50) NULL DEFAULT NULL , 
	`status` INT NOT NULL DEFAULT '1' ,
    `last_pull_date` DATETIME NULL,
    `last_sign_date` DATETIME NULL,
	`deleted_at` TIMESTAMP NULL DEFAULT NULL ,
	`modified` TIMESTAMP on update CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , 
	`created` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP , 
	PRIMARY KEY (`id`),
	INDEX(`deleted_at`)
) ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `machine_employee_mappings` ( 
	`id` INT NOT NULL AUTO_INCREMENT ,
	UNIQUE (`id`) ,
	`machine_id` INT NOT NULL , 
	`staff_id` INT NOT NULL , 
	`staff_machine_id` INT NOT NULL ,
        PRIMARY KEY (`machine_id`,`staff_id`),
        INDEX(`staff_machine_id`),
	CONSTRAINT `machine_fk_hrm` FOREIGN KEY (`machine_id`) REFERENCES `machines`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT `staff_fk_hrm` FOREIGN KEY (`staff_id`) REFERENCES `staffs`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `allocated_shift_criteria`  (
    `id`                   INT NOT NULL auto_increment,
    `allocated_shift_id`   INT NOT NULL,
    `criteria_type`        VARCHAR(255) NOT NULL,
    `criteria_id`          INT NOT NULL,
    `created`              TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `modified`             TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    CONSTRAINT `allocated_shift_criteria_allocated_shift_id_constraint` FOREIGN KEY allocated_shift_criteria_allocated_shift_id_fk (`allocated_shift_id`) REFERENCES `allocated_shifts` (id)
) ENGINE = innodb;

CREATE TABLE IF NOT EXISTS `attendance_restrictions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `use_ip` tinyint(4) NOT NULL DEFAULT '0',
  `use_location` tinyint(4) NOT NULL DEFAULT '0',
  `use_camera` tinyint(4) NOT NULL DEFAULT '0',
  `location_lat` double DEFAULT NULL,
  `location_long` double DEFAULT NULL,
  `location_range` double DEFAULT NULL,
  `location_range_type` tinyint(1) DEFAULT NULL,
  `matching_ip_required` tinyint(1) DEFAULT '1',
  `matching_range_required` tinyint(1) DEFAULT NULL,
  `matching_camera_required` tinyint(1) DEFAULT NULL,
  `locations` json DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `attendance_restrictions_ips` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) NOT NULL,
  `attendance_restriction_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `attendance_restriction_constraint` (`attendance_restriction_id`),
  CONSTRAINT `attendance_restriction_constraint` FOREIGN KEY (`attendance_restriction_id`) REFERENCES `attendance_restrictions` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `attendance_restriction_logs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) DEFAULT NULL,
  `location_lat` double DEFAULT NULL,
  `location_long` double DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `attendance_restriction_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `attendance_restriction_logs_id_constraint` (`attendance_restriction_id`),
  CONSTRAINT `attendance_restriction_logs_id_constraint` FOREIGN KEY (`attendance_restriction_id`) REFERENCES `attendance_restrictions` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE IF NOT EXISTS `leave_applications` (  
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `date_from`  DATE NOT NULL,
  `date_to`  DATE NOT NULL,
  `days` int(11) NOT NULL DEFAULT 0,
  `type` ENUM('leave','delay','early', 'half_leave') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'leave',
  `late_time` INT(11) NULL,
  `early_time` INT(11) NULL,
  `leave_type_id` INT(11) NULL,
  `description` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `comment` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `status` ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
  `approved_by` int(11) DEFAULT NULL,
  `approval_data` json DEFAULT NULL,
  `approval_cycle_configuration_id` int(11) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `attendance_permission_id` INT,
  `shift_type` varchar(255) NULL DEFAULT NULL,
  `created` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `modified` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,


  INDEX (`approved_by`),
  INDEX (`shift_type`),
  INDEX ( `created_by` ),
  CONSTRAINT staff_leave_application_fk FOREIGN KEY staff_leave_application_index (`staff_id`) REFERENCES `staffs` (`id`),
  CONSTRAINT leave_application_type_fk FOREIGN KEY leave_application_type_index (`leave_type_id`) REFERENCES `leave_types` (`id`),
  CONSTRAINT leave_application_attendance_permission_fk FOREIGN KEY leave_application_attendance_permission_index (`attendance_permission_id`) REFERENCES `attendance_permissions` (`id`),

  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `settings` (`plugin_id`, `key`, `value`, `created`, `modified`,`branch_id`) VALUES
    (89, 'direct_managers_can_approve', true, NOW(), NOW(), 1);
INSERT INTO `settings` (`plugin_id`, `key`, `value`, `created`, `modified`,`branch_id`) VALUES
    (89, 'assigned_department_managers_can_approve', true, NOW(), NOW(), 1);

CREATE TABLE IF NOT EXISTS `staff_leave_type_credit` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `staff_id` int(11) NOT NULL DEFAULT '0',
    `leave_type_id` int(11) NOT NULL DEFAULT '0',
    `fiscal_year` YEAR NULL DEFAULT NULL,
    `credit` double NOT NULL DEFAULT '0',
    `note` VARCHAR(255) NULL DEFAULT NULL,
    `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT `staff_leave_type_credit_staff_fk` FOREIGN KEY (`staff_id`) REFERENCES `staffs`(`id`) ON DELETE CASCADE,
    CONSTRAINT `staff_leave_type_credit_leave_type_fk` FOREIGN KEY (`leave_type_id`) REFERENCES `leave_types`(`id`) ON DELETE CASCADE,

    INDEX (`staff_id`),
    INDEX (`leave_type_id`),
    INDEX (`fiscal_year`),
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
