<?php

use Izam\Attachment\Models\EntityAttachment;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Journal\Services\File\FileFacade;
use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;
use Izam\Recurring\Repositories\RecurringProfilesRepository;
use Izam\Daftra\Common\Utils\SettingsUtil;
use App\Services\AwsFileDriver;
use Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest;
use Izam\Daftra\Common\Utils\EntityFieldUtil;
use Izam\Daftra\Journal\Services\File\Drivers\CakeLocalDriver;
use Izam\Entity\Formatter\Formatter;
use Izam\Entity\Helper\EntityActivityLog;
use Izam\Entity\Helper\EntityHasLeCustomData;

class JournalsController extends AppController {

    var $name = 'Journals';

    /**
     * @var Journal
     */
    var $Journal;
    var $helpers = array('Html', 'Form');
    var $journals_js_labels = array(
        'You Have to Add at Least 2 Journal Transactions',
        'Total Debit Must be Equal to Total Credit',
        'Multiple',
        'None'
    );




    function owner_update_currency_rates()
    {
        $this->loadModel('JournalTransaction');
        $journals_id = $this->JournalTransaction->find('list',array('fields' => array('journal_id'),'conditions' => array('JournalTransaction.currency_rate' => 0)));
        debug($journals_id);
        $journals = $this->Journal->find('all',array('conditions' => array('Journal.id' => array_values($journals_id))));
        foreach($journals as $k => $journal)
        {
            $this->Journal->save_journal($journal);
        }
        die('');
    }

    //for testing model functions
    function owner_test($cat_id){
        foreach (null as $k)
        {

        }
//    \Rollbar\Rollbar::warning('asdsadsad', ['asdsad']);
//        $result = $this->Journal->update_account_parent_ids();
//        dd($result);
    }

    function check_auto_journal($entity_type,$entity_id)
    {
        //select entity_id from journals where entity_type = 'purchase_order' and entity_id not in (SELECT id FROM `purchase_orders` WHERE summary_total <> 0 AND purchase_orders.supplier_id <> 0 AND purchase_orders.supplier_id IS NOT NULL AND ( purchase_orders.draft IS NULL OR purchase_orders.draft = 0 ) AND ( purchase_orders.type IN (0) OR purchase_orders.type IS NULL ))
        //SELECT id FROM `purchase_orders` WHERE summary_total <> 0 AND purchase_orders.supplier_id <> 0 AND purchase_orders.supplier_id IS NOT NULL AND ( purchase_orders.draft IS NULL OR purchase_orders.draft = 0 ) AND ( purchase_orders.type IN (0) OR purchase_orders.type IS NULL ) and id not in (select entity_id from journals where entity_type = 'purchase_order')
        //select journals.id, currency_credit, journals.currency_code, purchase_orders.id , summary_total, purchase_orders.currency_code from journals left JOIN purchase_orders on purchase_orders.id = entity_id WHERE entity_type='purchase_order' and abs(summary_total) <> currency_credit

        $model=Journal::$auto_journals[$entity_type]['model'];
        $this->loadModel($model);
        $entity=$this->{$model}->findById($entity_id);
        $this->{$model}->update_journals($entity);
        $this->autoRender=false;
    }
    function update_on_all()
    {
        set_time_limit(3600000);
        //$this->loadModel('StockTransaction');
        //$this->StockTransaction->average_on_all();

        // $this->loadModel('Expense');
//             $this->Expense->update_journals_for_all(array('Expense.id'=>69));
        $this->loadModel('Journal');
        $this->Journal->update_auto_journals_for_all_models(true);

        //$this->loadModel('PurchaseOrder');
        //$this->PurchaseOrder->update_journals_for_all();
        //$this->autoRender=false;


    }

    function check_auto_journals()
    {

        $result=$this->Journal->compare_auto_journals();

    }



    function owner_index($fy_id=false) {
        if (!IS_REST) {
            if (EntityHasLeCustomData::check(EntityKeyTypesUtil::JOURNAL)) {
                return $this->redirect('/v2/owner/entity/journal/list?'. $this->buildEntityListingParams());
            }
        }
//		$this->Journal->recursive = 2;
        $conditions = $this->_filter_params();
        unset($conditions['JournalTransaction.journal_account_id']);//Replaced with the below query
        if(!empty($_GET['journal_account_id'])){
            $journal_account_id = urldecode($_GET['journal_account_id']);
            $conditions[] = 'Journal.id in(SELECT journal_id from journal_transactions where journal_transactions.journal_account_id = '.$journal_account_id.' )';
        }
		
        if(!empty($_GET['recurring_profile'])){
            $recurringProfileId = mysqli_real_escape_string( $this->Journal->getDataSource()->connection,$_GET['recurring_profile']);
            $conditions[] = 'Journal.id in(SELECT entity_id from recurring_items where recurring_items.profile_id = '.$recurringProfileId.' and recurring_items.entity_key = "journal" )';
        }
		if(isset($conditions['AND']))
		{
			$alter_conditions=[];
			foreach($conditions['AND'] as $cond)
			{
				foreach($cond as $k=>$val)
				{
					$k2=str_replace('description','alter_description',$k);
					$alter_conditions[]=[$k2=>$val];
				}
				
				
			}
			$conditions['OR']=[$conditions['AND'], $alter_conditions];
			unset($conditions['AND']);
		}
		
		if(isset($conditions["Journal.description LIKE"]))
		{
				$conditions['OR']=['Journal.description LIKE'=>$conditions["Journal.description LIKE"],'Journal.alter_description LIKE'=>$conditions["Journal.description LIKE"]];
				unset($conditions["Journal.description LIKE"]);
		}
			

        if ( isset ($_GET['date_selector']) &&$_GET['date_selector'] == "lastyear" ){
            $conditions[] =" YEAR(Journal.`date`) = YEAR(CURRENT_DATE - INTERVAL 1 Year) ";
        }else if ( isset ($_GET['date_selector']) &&$_GET['date_selector'] == "lastmonth" ){
            $conditions[] =" YEAR(Journal.`date`) = YEAR(CURRENT_DATE)
AND MONTH(Journal.`date`) = MONTH(CURRENT_DATE - INTERVAL 1 MONTH) ";
        }

        if (isset($_GET['entity_type']) && isset($_GET['entity_id'])){
            $conditions[] = ['Journal.entity_type' => $_GET['entity_type'] ,'Journal.entity_id' => $_GET['entity_id']];
        }

        if (isset($_GET['added_by']) && !empty($_GET['added_by'])){
            $conditions[] = ['Journal.staff_id' => $_GET['added_by']];
            unset($conditions['Journal.added_by']);
            $this->set('selected_staff_id', $_GET['added_by']); 
        }
 
//		$this->Journal->bindModel(array('hasOne' => array('JournalTransaction'=>array('foreignKey'=>'journal_id','className' => 'JournalTransaction'))), false);


        if (!empty($_GET['cost_center'])) {
            $costCenter = $_GET['cost_center'];
           if($costCenter == -1) {
                $conditions[] = 'Journal.id NOT IN (
                    SELECT cost_center_transactions.journal_id 
                    FROM cost_center_transactions
                )';
            }else{
                $conditions[] = 'Journal.id in(SELECT `cost_center_transactions.journal_id` FROM `cost_center_transactions` WHERE `cost_center_transactions.cost_center_id` = ' . $costCenter . ')';
            }
            unset($conditions['Journal.cost_center']);
        }
        if (!empty(settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_TAGS_IN_JOURNAL)) && $conditions['JournalTransaction.id']){
            $conditions[] = ' Journal.id in(SELECT `journal_transactions`.`journal_id` FROM `journal_transactions` WHERE `journal_transactions`.`id` IN (' . implode(',', $conditions['JournalTransaction.id']) . ') )';
            unset($conditions['JournalTransaction.id']);
        }
        $owner = getAuthOwner();
        if($owner['staff_id'] != 0)
        {
            $staff = $owner['staff_id'];

            if (!check_permission(VIEW_ALL_JOURNALS) && !check_permission(VIEW_OWN_JOURNALS)) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }

            if(check_permission(VIEW_OWN_JOURNALS) && !check_permission(VIEW_ALL_JOURNALS)){
                $conditions['Journal.staff_id'] = $staff;
            }
        }
        if(isset($conditions['Journal.entity_type']) && $conditions['Journal.entity_type'] === '0')
        {
            $conditions['Journal.entity_type'] = '';
        }

        $fy_cond=$this->Journal->get_financial_conditions($fy_id,'Journal');
        if(!empty($fy_cond))
            $conditions[] = $fy_cond;

        $this->paginate =
            array(
                
                'recursive' => 1,
                'order' => array(
                    'Journal.date' => 'DESC',
                    'Journal.id' => 'DESC',
                ),
//								'group' => 'Journal.id',
            'conditions'=>$conditions	,
//								'contain' => 'JournalTransaction'
        )
        ;
        $this->Journal->recursive = 2 ;

        $this->Journal->bindAttachmentRelation('journal');

        $journals = $this->paginate('Journal', $conditions);

        foreach($journals as $k => &$journal)
        {
            $journal['JournalTransaction'] = $journal['JournalTransaction']	;
            foreach($journal['JournalTransaction'] as $k => $transaction)
            {
                if(!is_numeric($k)){
                    unset($journal['JournalTransaction'][$k]);
                }
            }
        }

        $conditionsOfRecurring = [
            ['entity_key', EntityKeyTypesUtil::JOURNAL]            
        ];
        if (ifPluginActive(BranchesPlugin)) {
            $conditionsOfRecurring[] = ['branch_id', getCurrentBranchID()];
        }
        if (!check_permission(MANAGE_ALL_JOURNALS) && check_permission(MANAGE_OWN_JOURNALS)) {
            $conditionsOfRecurring[] = ['staff_id', getAuthStaff('id')];
        }
        $recurringProfiles = RecurringProfilesRepository::getRecurringProfilesByConditions($conditionsOfRecurring);
        if ($recurringProfiles && count($recurringProfiles)) {
            $this->set('listingRecurringButton', true);
        } else {
            $this->set('listingRecurringButton', false);
        }

        $this->set('title_for_layout',  __('Journal Entries',true) );
        $currency = $this->Journal->get_default_currency();
        if(!empty($journals)) {
            $mapped_accounts=$this->Journal->get_mapped_accounts($journals);
            $this->set('accounts', $mapped_accounts);
        }
        $this->set('journals', $journals);
        $this->set('currency', $currency);
        $this->loadModel('Staff');
	    $this->set('staffs', $this->Staff->getList());
	    if (ifPluginActive(BranchesPlugin)) {
		    $this->loadModel('Branch');
		    $this->set('branches', $this->Branch->getList(false));
	    }
        $this->setup_nav_data($journals);

        if(IS_REST){
            $this->set('rest_items', $journals);
            $this->set('rest_model_name', "Journal");
            $this->render("index");
        }
    }

    function owner_view($id = null) {

        if (!$id) {
            if(IS_REST){
                $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Journal', true))]);
            }else{
                $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Journal', true)),true));
                $this->redirect(array('action'=>'index'));
            }
        } 
        $this->Journal->recursive = 2;
        $this->loadModel('JournalTransaction');
        $this->loadModel('CostCenterTransaction');
        // Load Attachments . 
        $this->Journal->bindAttachmentRelation('journal');

        $journal = $this->Journal->find('first',array('applyBranchFind' => false,'conditions'=> array('Journal.id' => $id)));
        $owner = getAuthOwner();
        if($owner['staff_id'] != 0)
        {
            $staff = $owner['staff_id'];

            if (!check_permission(VIEW_ALL_JOURNALS) && (!(check_permission(VIEW_OWN_JOURNALS) && $staff == $journal['Journal']['staff_id']))) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        if(!$journal)
        {
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Product', true))));
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Journal', true)),true));
            $this->redirect(array('action'=>'index'));
        }
        $mapped_accounts=$this->Journal->get_mapped_accounts($journal);
        $this->set('mapped_accounts', $mapped_accounts);

        $journal['Journal']['local_currency_code'] = $this->Journal->get_default_currency();
        if($journal['Journal']['currency_code'] != $this->Journal->get_default_currency()){

            $journal['Journal']['show_loacal_currency'] = true ;
        }

        unset($journal['JournalTransaction']);
        if($journal['Journal']['is_automatic'])
        {
            $transactions = $this->Journal->JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $id) ,'order' => array('JournalTransaction.debit' => 'DESC', 'JournalTransaction.credit' => 'DESC') ));
        }else{
            $transactions = $this->Journal->JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $id) ,'order' => array('JournalTransaction.display_order' => 'ASC') ));
        }
        //Backward compatability for cost center new added display order
        if(!$transactions) {
            $transactions = $this->Journal->JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $id) ,'order' => array('JournalTransaction.created' => 'DESC') ));
        }
        foreach ($transactions as $k => $transaction)
            $journal['JournalTransaction'][] = $transaction['JournalTransaction'];

        $this->set('useNewCostCenterMode', true);
        if(!$this->CostCenterTransaction->applicableForNewMode($id)) {
            $journal['JournalTransaction'] = $this->collectJournalsUsingAccounts($journal['JournalTransaction']??[]);
            $this->set('useNewCostCenterMode', false);
        }
        $this->set('journal',$journal );
        if (low($this->params['url']['ext']) == 'pdf') {
            $this->owner_view_journal($id);
        }
        $this->set('title_for_layout',  sprintf(__('Journal No %s',true),$journal['Journal']['number']) );

        //get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('journal',true);

        $templateList = $this->PrintableTemplate->getPrintableTemplatesList('journal',true);
        // get default template otherwise take the first one
        // warning suppress
        $defaultTemplate = $templateList[0] ?? [];
        foreach ($templateList as $template) {
            if ($template['PrintableTemplate']['default_template']) {
                $defaultTemplate = $template;
            }
        }

        $this->set('has_templates', false);
        $this->set('defaultTemplate', $defaultTemplate);
        if (!empty($printableTemplates)) {
            $this->set('has_templates', true);
            $this->set(compact('printableTemplates'));
        }

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('journal');

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);

        $this->loadModel('CostCenterTransaction');
        $this->set('cost_centers', $this->CostCenterTransaction->CostCenter->find('list'));
        $recurringItem = RecurringProfilesRepository::getRecurringItemByEntityKeyAndEntityId(EntityKeyTypesUtil::JOURNAL, $id);
        if($recurringItem) {
            $this->set('recurringItem', $recurringItem);
        }

        if (isset($_SESSION['last_non_completed_id'])) {
            $this->set('last_non_completed_id', $_SESSION['last_non_completed_id']);
        }
        $recurringProfile = RecurringProfilesRepository::getRecurringProfileByEntityKeyAndEntityId(EntityKeyTypesUtil::JOURNAL, $id);
        $this->set('recurringProfile', $recurringProfile);
        $this->CostCenterTransaction->recursive = -1;
        $cost_transactions_count = $this->CostCenterTransaction->find('count',[ 'conditions' => ['CostCenterTransaction.journal_id' => $id] ]);
        $this->set('cost_transactions_count',$cost_transactions_count);
        $this->set('accounts', $this->JournalTransaction->JournalAccount->find('list'));
        $this->set('link',Journal::get_link($journal));
        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->set('owner', getAuthOwner());
        $this->loadModel('JournalLog');
        $logsCount = $this->JournalLog->find('count',['applyBranchFind' => false, 'conditions' => ['JournalLog.journal_id' => $id]]);
        $this->set('has_custom_data', EntityHasLeCustomData::check(EntityKeyTypesUtil::JOURNAL));
        $this->set('logsCount', $logsCount);
        $this->set('forms', $this->createAdditionalFieldsFormHandlerInstance()->show($id));

        if (ifPluginActive(BranchesPlugin)) {
            $this->loadModel('Branch');
            $this->set('branches', $this->Branch->getList(false));
        }
        $this->setup_nav_view($id);
        if(IS_REST){
            $this->set('rest_item', $journal);
            $this->set('rest_model_name', "Journal");
            $this->render("view");
        }
    }

    function owner_view_journal($id = false)
    {

        if (!$id)
        {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Journal', true)),true));
            $this->redirect(array('action'=>'index'));
        }
        $this->Journal->recursive = 2;

        $journal = $this->Journal->find('first',array('conditions'=> array('Journal.id' => $id)));

        $this->loadModel("CostCenterTransaction");
        $cost_transactions = $this->CostCenterTransaction->find('all',[ 'conditions' => ['CostCenterTransaction.journal_id' => $id] ]);
        $this->set('cost_transactions',$cost_transactions);


        $owner = getAuthOwner();
        if($owner['staff_id'] != 0)
        {
            $staff = $owner['staff_id'];

            if (!check_permission(VIEW_ALL_JOURNALS) && (!(check_permission(VIEW_OWN_JOURNALS) && $staff == $journal['Journal']['staff_id']))) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        $journal['Journal']['local_currency_code'] = $this->Journal->get_default_currency();
        if($journal['Journal']['currency_code'] != $this->Journal->get_default_currency()){

            $journal['Journal']['show_loacal_currency'] = true ;
        }

        unset($journal['JournalTransaction']);

        if($journal['Journal']['is_automatic'])
        {
            $transactions = $this->Journal->JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $id) ,'order' => array('JournalTransaction.debit' => 'DESC', 'JournalTransaction.credit' => 'DESC') ));
        }else{
            $transactions = $this->Journal->JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $id) ,'order' => array('JournalTransaction.id' => 'ASC') ));
        }

        foreach ($transactions as $k => $transaction){
            $journal['JournalTransaction'][] = $transaction['JournalTransaction'];
            $journal['JournalTransaction'][$k]['JournalAccount'] = $transaction['JournalAccount'];
        }

        if ($this->params['url']['ext'] == 'pdf') {
               $this->prepareJournalCostCenterSummary($cost_transactions);
               $this->prepareJournalTransactionsForTax($transactions, $journal);
        }

        $this->set('journal', $journal);
        $this->layout = 'box';


//        $this->add_actionline(ACTION_PRINT_PRESCRIPTION_CLIENT, array('primary_id' => $prescription['Prescription']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['email'], 'param4' => $client['Client']['client_number']));

        //$this->action = "owner_view_prescription";

        if ($this->action == 'owner_view_journal' && low($this->params['url']['ext']) == 'pdf') {

            $this->render('owner_view');

        }
    }

    function owner_get_accounts_ajax(){

        $this->loadModel("JournalAccount");
        $journal_accounts = $this->JournalAccount->find('list');
        $accounts_reversed = array();
        foreach($journal_accounts as $k => $v)
        {
            $accounts_reversed[$k] = "#{$k} - ".$v;
        }

        return $accounts_reversed;

        die(json_encode($accounts_reversed));

    }
    function owner_popup($id=false)
    {
        $this->owner_preview($id);
        $journal=  $this->viewVars['journal'];
        $this->initFileFacade();
        $files=   FileFacade::getJournalFiles( $journal['Journal']);
        // dd($this->viewVars);
        $journal_source=null;
        if($journal['Journal']['is_automatic']) {
            $journal_source = Journal::get_link($journal)['link'];
          }
        $this->set('journal_source', $journal_source);
        $this->set('files', $files);

        $this->setDefaultViewData();
        $this->view='izam';
        $this->render('journals/popup');

    }

    public function initFileFacade() {
        FileFacade::setConfig([
            'pdo' => getPdo(),
            'drivers' => [
                'cake_local' => [
                    'class_namespace' => CakeLocalDriver::class,
                    'subdomain' => $_SERVER['HTTP_HOST']
                ],
                'aws' => [
                    'handler' => new AwsFileDriver()
                ]
            ]
        ]);
        FileFacade::init();
    }
    function owner_preview($id = false) {

        if (!$id)
        {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Journal', true)),true));
            $this->redirect(array('action'=>'index'));
        }
        $CostCenterTransaction=ClassRegistry::init('CostCenterTransaction','Model');
        $cost_transactions = $CostCenterTransaction->find('all',[ 'conditions' => ['CostCenterTransaction.journal_id' => $id] ]);
        $this->prepareJournalCostCenterSummary($cost_transactions);
        $this->set('cost_transactions',$cost_transactions);
//		$this->Journal->recursive = 2;

        $journal = $this->Journal->find('first',array('applyBranchFind' => false, 'conditions'=> array('Journal.id' => $id)));

        if($journal && $journal['Journal']['entity_type'] == 'invoice'){
            $this->journalPlaceholders($journal['Journal']['entity_id']);
        }

        $owner = getAuthOwner();
        if($owner['staff_id'] != 0)
        {
            $staff = $owner['staff_id'];

            if (!check_permission(VIEW_ALL_JOURNALS) && (!(check_permission(VIEW_OWN_JOURNALS) && $staff == $journal['Journal']['staff_id']))) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                if(isset($_GET['box']) &&  $_GET['box']=1) {
                    return $this->render('../elements/not_allowed_error_message');
                }
                $this->redirect('/');
            }
        }

        unset($journal['JournalTransaction']);

        $this->loadModel('JournalTransaction');
        $this->JournalTransaction->recursive = 1;

        if($journal['Journal']['is_automatic'])
        {
            $order_by = array('CASE WHEN JournalTransaction.debit > 0 THEN 1  WHEN JournalTransaction.debit <= 0 THEN 2 END ' => 'ASC','JournalTransaction.id' => 'ASC');
        }else{
            $order_by = array('COALESCE(JournalTransaction.display_order, JournalTransaction.id)' => 'ASC');
        }
        $this->__setting();
        if (!empty($this->viewVars['enable_tags'])) {
            $this->loadModel('ItemsTag');
            $this->JournalTransaction->bindModel(
                ['hasAndBelongsToMany' =>
                    ['Tags' =>
                        [
                            'className' => 'Tag',
                            'joinTable' => 'items_tags',
                            'foreignKey' => 'item_id',
                            'associationForeignKey' => 'tag_id',
                            'conditions' => ['item_type' => ItemsTag::TAG_ITEM_TYPE_DEBIT_JOURNAL_TRANSACTION]
                        ]
                    ]
                ]
            );
        }
        $transactions = $this->JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $id) ,'order' => $order_by ));
        $this->prepareJournalTransactionsForTax($transactions, $journal);
        $journal['Journal']['local_currency_code'] = $this->Journal->get_default_currency();
        $journal['Journal']['id'] = $id;
        if($journal['Journal']['currency_code'] != $this->Journal->get_default_currency()){
            $journal['Journal']['show_loacal_currency'] = true ;
        }
   
        $this->set('journal', $journal);
    }


    function collectJournalsUsingAccounts($journalTransaction) {
        $journalAccounts=[];
        foreach($journalTransaction as $key => $transaction) {
            if(!isset($journalAccounts[$transaction['journal_account_id']])) {
                $journalAccounts[$transaction['journal_account_id']] = [];
            }
            $journalAccounts[$transaction['journal_account_id']][] = $transaction['journal_account_id'];
        }

        $journalAccounts = array_values(array_unique(array_map(function($item) {  
            return ($item[0]);
        }, array_filter($journalAccounts, function($item) {
            return count($item) > 1;
        }))));

        foreach($journalAccounts as $accId) {
            $toMerge = [];
            $mergedJournals = [];
            foreach($journalTransaction as $key => $transaction) {
                if($transaction['journal_account_id'] == $accId) {
                    $toMerge[$key] = $transaction;
                }
            }

            foreach($toMerge as $key => $transaction) {
               $currentType  = $transaction['debit'] > 0 ? 'debit' : 'credit';
               if(empty($mergedJournals[$currentType])) {
                $mergedJournals[$currentType] = $transaction;
               } else {
                $factor = $transaction['currency_debit'] ? 'currency_debit' : 'currency_credit';
                $mergedJournals[$currentType][$factor] += $transaction[$factor];
               } 
            }

            foreach($journalTransaction as $key => $transaction) {
                if($transaction['journal_account_id'] == $accId) { 
                  unset($journalTransaction[$key]); 
                }
            }

            foreach($mergedJournals as $transaction) {
              array_push($journalTransaction, $transaction);
            }
       
   
        }

        return array_values($journalTransaction); 
        
    }


    function processJournalTransaction($journalTransaction) {   
        $journalAccounts = [];
        foreach($journalTransaction as $key => $transaction) {
            if(!isset($journalAccounts[$transaction['journal_account_id']])) {
                $journalAccounts[$transaction['journal_account_id']] = [];
            }
            $journalAccounts[$transaction['journal_account_id']][] = $transaction['journal_account_id'];
        }

        $journalAccounts = array_values(array_unique(array_map(function($item) {  
            return ($item[0]);
        }, array_filter($journalAccounts, function($item) {
            return count($item) > 1;
        }))));


        foreach($journalAccounts as $accId) {
            $toMerge = [];
            $mergedJournals = [];
            foreach($journalTransaction as $key => $transaction) {
                if($transaction['journal_account_id'] == $accId) {
                    $toMerge[$key] = $transaction;
                }
            }
 
            foreach($toMerge as $key => $transaction) {
               if(empty($mergedJournals)) {
                $mergedJournals = $transaction;
               } else {
                $factor = $transaction['currency_debit'] ? 'currency_debit' : 'currency_credit';
                $mergedJournals[$factor] += $transaction[$factor];
               } 
            }
          
            foreach($journalTransaction as $key => $transaction) {
                if($transaction['journal_account_id'] == $accId) { 
                  unset($journalTransaction[$key]); 
                }
            }

     
            array_push($journalTransaction, $mergedJournals);
   
        }
 
        return array_values($journalTransaction); 
    }


    function getJournalTransactionTotalAmount($account_id, $type, $transactions) {
        $total = 0;
        foreach($transactions as $journal_transaction){
            $isCredit = $journal_transaction['currency_credit'] > $journal_transaction['currency_debit'];
            $currentType = $isCredit ? "credit" : "debit";
            $total_transaction_amount  = $isCredit ? $journal_transaction['currency_credit']  : ($journal_transaction['currency_debit'] ?: 0);
            if($account_id == $journal_transaction['journal_account_id'] && $type == $currentType){
                $total += (float) $total_transaction_amount;  
            }
        }
        return $total;
    }


    function getCostCenterTransactionList($data, $default_currency) {

        $costCenterTransactionList = [];  
        $cost_data=$data; 
        $alreadyAssigned = []; 
        $invalidCostCenters = [];
        $this->loadModel("JournalAccount");
        $this->loadModel('CostCenter');
         foreach($cost_data['JournalTransaction'] as $journal_transaction_key => $journal_transaction){   
            $journal = $this->Journal->get_journal($data['Journal']['id']);
            $account_id = $journal_transaction['journal_account_id'] == "default" ? "" : $journal_transaction['journal_account_id'];
            $journal_account = $this->JournalAccount->findById($account_id);  
            $cost_centers = $this->JournalAccount->get_cost_centers($journal_account);
            $cc_data = [];
            $cost_center_transactions = $this->data['CostCenterTransaction'];
   
            $isCredit = $journal_transaction['currency_credit'] > $journal_transaction['currency_debit'];
            $type = $isCredit ? "credit" : "debit";
             
            $total_transaction_amount  = $isCredit ? $journal_transaction['currency_credit']  : ($journal_transaction['currency_debit'] ?: 0);
 
            $total_transaction_amount = $this->getJournalTransactionTotalAmount($account_id, $type, $cost_data['JournalTransaction']);
 
            $getCostCentersByType = [];

            foreach($cost_center_transactions[$account_id] as  $_cost_center_transactions)
            {             
                foreach ($_cost_center_transactions as $cost_center_transaction) { 
                    
                    if ($type == $cost_center_transaction['type'] && !$alreadyAssigned[$account_id][$type][$cost_center_transaction['cost_center_id']]) { 
                        $alreadyAssigned[$account_id][$type][$cost_center_transaction['cost_center_id']] = true;
                        array_push($getCostCentersByType, $cost_center_transaction);
                    }
                }    
            }

            $to_delete = [];
            foreach($getCostCentersByType as $k => $cost_center_transaction)
            {         
                if($cost_center_transaction['percentage']==0){  
                    $to_delete[]=$cost_center_transaction['id'];
                    continue;
                }
                $type = $cost_center_transaction['type'];
                if($type == "debit"){   
                    $cost_center_transaction['debit'] = $cost_center_transaction['credit'];
                    $cost_center_transaction['credit'] = 0;
                }
                if($journal['Journal']['currency_code'] && $default_currency != $journal['Journal']['currency_code']){
                    $cost_center_transaction['debit'] *= $journal['Journal']['currency_rate'];
                    $cost_center_transaction['credit'] *= $journal['Journal']['currency_rate'];
                }
                $cc_data[$k]['CostCenterTransaction'] = $cost_center_transaction;
                $cc_data[$k]['CostCenterTransaction']['journal_account_id'] = $account_id;
                $cc_data[$k]['CostCenterTransaction']['is_auto'] = false;
                $cc_data[$k]['CostCenterTransaction']['currency_code'] = $default_currency;
                $cc_data[$k]['CostCenterTransaction']['rate'] = 1;
                
            }

            if(!empty($to_delete)) { 
                $this->CostCenter->CostCenterTransaction->deleteAll(['CostCenterTransaction.id' => $to_delete]);
            }

            $sum = array_reduce($getCostCentersByType, function ($sum, $item) {   
                if($item["CostCenterTransaction"]) {
                    $sum += $item["CostCenterTransaction"]['credit'] > 0 ? (float) $item["CostCenterTransaction"]['credit'] : (float) $item["CostCenterTransaction"]['debit'];
                } else {
                    $sum += (float) $item['credit'];
                }
                return $sum;
            },0);

            if($total_transaction_amount+0.009 < $sum) {  
                $this->flashMessage(__("The Total cost center amount should be less than or equal the total amount of the journal transaction", true));
                $validCostCenters = false;
                $invalidCostCenters[] = false;
            } else {
 
                foreach($cc_data as $costTransactionRecord) {
                    $costCenterTransactionList[] = $costTransactionRecord;
                }
            
                $validCostCenters = true;
            } 
 
        }  
 
       return [
         'validCostCenters'           => $validCostCenters,
         'costCenterTransactionList'  => $costCenterTransactionList,
         'invalidCostCenters'         => !empty($invalidCostCenters)
       ];

    }

    public function getCostCenterTransactionListV2($data, $default_currency) {
        $costCenterTransactionList = [];
        $cost_data=$data;
        $invalidCostCenters = [];
        $this->loadModel("JournalAccount");
        $this->loadModel('CostCenter');
        $cost_center_data = $originalCostCenterPerTransactions = [];
        $journalTransactions = array_filter($data['JournalTransaction'], function ($key) {
            return is_int($key);
        }, ARRAY_FILTER_USE_KEY);

         foreach($journalTransactions as $journal_transaction_key => $journal_transaction){
            $account_id = $journal_transaction['journal_account_id'] == "default" ? "" : $journal_transaction['journal_account_id'];
            $isCredit = $journal_transaction['currency_credit'] > $journal_transaction['currency_debit'];
            $total_transaction_amount  = $isCredit ? $journal_transaction['currency_credit']  : ($journal_transaction['currency_debit'] ?: 0);
            $to_delete = [];
            $original_transaction_key = $journal_transaction_key;
            $journal_transaction_key = isset($journal_transaction['id']) && is_numeric($journal_transaction['id'])  ? $journal_transaction['id'] : $journal_transaction_key;
            if(isset($data['CostCenterTransactionV2'][$journal_transaction_key])) {
                foreach($data['CostCenterTransactionV2'][$journal_transaction_key] as $k => $cost_center_transaction)
                {
                    if(!is_numeric($cost_center_transaction['cost_center_id'])){
                        continue;
                    }
                    
                    if($cost_center_transaction['percentage']==0){
                        $to_delete[]=$cost_center_transaction['id'];
                        continue;
                    }
                    $cost_center_transaction['currency_credit'] = $cost_center_transaction['credit'];
                    if(!$isCredit){
                        $cost_center_transaction['debit'] = $cost_center_transaction['credit'];
                        $cost_center_transaction['currency_debit'] = $cost_center_transaction['credit'];
                        $cost_center_transaction['credit'] = 0;
                        $cost_center_transaction['currency_credit'] = 0;
                    }
                    $cost_center_data[$journal_transaction_key][$k]['CostCenterTransaction'] = $cost_center_transaction;
                    $cost_center_data[$journal_transaction_key][$k]['CostCenterTransaction']['journal_account_id'] = $account_id;
                    $cost_center_data[$journal_transaction_key][$k]['CostCenterTransaction']['is_auto'] = false;
                    $cost_center_data[$journal_transaction_key][$k]['CostCenterTransaction']['currency_code'] = $default_currency;
                    $cost_center_data[$journal_transaction_key][$k]['CostCenterTransaction']['rate'] = 1;
                    $originalCostCenterPerTransactions[$original_transaction_key][$k] =$cost_center_data[$journal_transaction_key][$k];
                    if(!empty($to_delete)) {
                        $this->CostCenter->CostCenterTransaction->deleteAll(['CostCenterTransaction.id' => $to_delete]);
                    }

                }
            } elseif(is_numeric($journal_transaction['cost_center'])) {
                $specific_cost_center = [];
                $specific_cost_center['cost_center_id'] = $journal_transaction['cost_center'];
                if(!$isCredit){
                    $specific_cost_center['debit'] = $journal_transaction['currency_debit'];
                    $specific_cost_center['currency_debit'] = $journal_transaction['currency_debit'];
                    $specific_cost_center['credit'] = 0;
                    $specific_cost_center['currency_credit'] = 0;
                    $specific_cost_center['type'] = 'debit';

                } else {
                    $specific_cost_center['credit'] = $journal_transaction['currency_credit'];
                    $specific_cost_center['currency_credit'] = $journal_transaction['currency_credit'];
                    $specific_cost_center['debit'] = 0;
                    $specific_cost_center['currency_debit'] = 0;
                    $specific_cost_center['type'] = 'credit';
                }
                $specific_cost_center['percentage'] = 100;
                $specific_cost_center['journal_account_id'] = $account_id;
                $specific_cost_center['account_id'] = $account_id;
                $specific_cost_center['is_auto'] = false;
                $specific_cost_center['rate'] = 1;
                $specific_cost_center['currency_code'] = $default_currency;

                $cost_center_data[$journal_transaction_key][0]['CostCenterTransaction'] = $specific_cost_center;
                $originalCostCenterPerTransactions[$original_transaction_key][0]['CostCenterTransaction'] = $specific_cost_center;
            }

            $sum = array_reduce($cost_center_data, function ($sum, $item) {
                if($item["CostCenterTransaction"]) {
                    $sum += $item["CostCenterTransaction"]['credit'] > 0 ? (float) $item["CostCenterTransaction"]['credit'] : (float) $item["CostCenterTransaction"]['debit'];
                } else {
                    $sum += (float) $item['credit'];
                }
                return $sum;
            },0);

            if((float)$total_transaction_amount+0.009 < $sum) {
                $this->flashMessage(__("The Total cost center amount should be less than or equal the total amount of the journal transaction", true));
                $validCostCenters = false;
                $invalidCostCenters[] = false;
            } else {
                $costCenterTransactionList = $cost_center_data;
                $validCostCenters = true;
            }

        }
       return [
         'validCostCenters'           => $validCostCenters,
         'costCenterTransactionList'  => $costCenterTransactionList,
         'invalidCostCenters'         => !empty($invalidCostCenters),
         'journalsTransaction'        => $journalTransactions,
         'originalCostCenterPerTransactions' => $originalCostCenterPerTransactions
       ];
    }


    function collectCostCenterByTypes($structuredCostCenters) {
        $unifiedCostCenters = [];
        $accIdTypes = [];
        foreach($structuredCostCenters as $accId => $costCenterGroup) {
           foreach($costCenterGroup as $key => $costCenterList) { 
               foreach($costCenterList as $costCenterItem) {
                   $type = $costCenterItem['type'];
                   if(!isset($accIdTypes[$accId][$type][$costCenterItem['cost_center_id']])) {
                       $unifiedCostCenters[$accId][$type][$key][] = $costCenterItem; 
                       $accIdTypes[$accId][$type][$costCenterItem['cost_center_id']] = true;
                   }
               }
           }
        }
  
        return $unifiedCostCenters;
    }


    function restructureCostCenterTransactions($journalTransaction, $costCenterTransactions) {
       
        $restructuredCostCenters = [];
        $types = [
            'credit' => 1,
            'debit'  => 0
        ];  

        foreach($journalTransaction as $key => $transaction) {   
            if(!isset($transaction['debit'])) {
                $transaction['debit'] = $transaction['currency_debit'];
            }
            if(!isset($transaction['credit'])) {
                $transaction['credit'] = $transaction['currency_credit'];
            }
            $transactionType = $transaction['debit'] > 0 ? "debit" : "credit";
            foreach($costCenterTransactions as $costCenterTransaction) {  
                $costCenterTransaction  = $costCenterTransaction['CostCenterTransaction'];
                $type = $costCenterTransaction['debit'] > 0 ? "debit" : "credit";
                if($costCenterTransaction['journal_account_id'] == $transaction['journal_account_id'] && $transactionType == $type){
                    $costCenterData = [
                        'type'           => $type,
                        'account_id'     => $costCenterTransaction['journal_account_id'],
                        'journal_transaction_id' => $costCenterTransaction['journal_transaction_id'],
                        'cost_center_id' => $costCenterTransaction['cost_center_id'],
                        'percentage'     => $costCenterTransaction['percentage'],
                        'credit'         => $type == "debit" ? $costCenterTransaction['debit'] : $costCenterTransaction['credit']
                    ];  
                    $restructuredCostCenters[$costCenterData['account_id']][$key][] = $costCenterData;
                }
            }
        }

         return $this->collectCostCenterByTypes($restructuredCostCenters);
    }

    /*
        If for unknown reason the cost center is set to full percent so we make sure 
        that the percent is applied to cost center based on its transaction
    */
    private function setFullPercentCostCenterData($costCenter, $transaction) {

        if($costCenter['percentage'] == 100) {
            switch($costCenter['type']) {
                case 'debit':
                    $costCenter['debit']  = $transaction['debit'];
                    $costCenter['currency_debit']  = $transaction['currency_debit'];
                    break;
                case 'credit':
                    $costCenter['credit'] = $transaction['credit'];
                    $costCenter['currency_credit']  = $transaction['currency_credit'];
                    break;    
            }
        }

        return $costCenter;
    }

    function owner_add($id=false) {

        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();

        $this->loadModel('CostCenter');
        if (!check_permission(MANAGE_ALL_JOURNALS) && !check_permission(MANAGE_OWN_JOURNALS) && !check_permission(MANAGE_DRAFT_JOURNALS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->__setting();
        Journal::$auto_accounts['client']['callback_after_add']='adjust_and_pay';
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->journals_js_labels);
        $flushedTransactionList = $this->data['JournalTransaction'];
        if (!empty($this->data)) {
            $error = false;
            $total_debit = 0;
            $total_credit = 0;
            $updateJournalEntriesCurrencyRatesSetting = settings::getValue(AccountingPlugin, SettingsUtil::UPDATE_JOURNAL_ENTRIES_CURRENCY_RATES);
            $manualCurrencyRate = $this->data['Journal']['manual_currency_rate'] ?? null;
            $this->data['Journal']['manual_currency_rate'] = $updateJournalEntriesCurrencyRatesSetting ? $manualCurrencyRate : null;
            $this->set('assigned_cost_centers', $this->data['CostCenterTransaction']);
            $this->loadModel('JournalAccount');  
            $this->set('JournalAccount', $this->JournalAccount);
            //dd( $this->data );
            foreach ($this->data['JournalTransaction'] as $k => $transaction) {
                if(empty($transaction['currency_credit']) && empty($transaction['currency_debit'])) {
                    unset($this->data['JournalTransaction'][$k]);
                    continue;
                }
                if (empty($transaction['journal_account_id']) && (!empty($transaction['currency_credit']) || !empty($transaction['currency_debit']))) {

                    if (!empty($transaction['currency_credit'])) {
                        $side = __t('Credit');
                        $side_amount = $transaction['currency_credit'];
                    } else {
                        $side = __t('Debit');
                        $side_amount = $transaction['currency_debit'];
                    }
                
                    CustomValidationFlash([
                        sprintf(__t('The value %s cannot be added to the %s side without selecting an account.', true), $side_amount, $side)
                    ]);
                
                    $error = true;
                }

                if(empty($transaction['journal_account_id']) || !is_numeric($transaction['journal_account_id'])) {
                    unset($this->data['JournalTransaction'][$k]);
                }
            }
            $this->data = $this->setTransactionsTaxData($this->data);
            foreach($this->data['JournalTransaction'] as $k =>&$v){
//				dd($this->data);
                $total_debit += (float)$v['currency_debit'];
                $total_credit += (float)$v['currency_credit'];
                $v['currency_code'] = $this->data['Journal']['currency_code'];
            }
//			if($total_debit != $total_credit)
            if(!float_equal($total_debit, $total_credit))
            {
                if(IS_REST) $this->cakeError('error400', ["message"=>sprintf (__('The %s could not be saved. The total debit must be equal to the total credit', true), __('Journal',true))]);
                CustomValidationFlash([sprintf(__('The %s could not be saved. The total debit must be equal to the total credit', true), __('Journal',true))]);
                $error = true;
            }


            if(empty($this->data['JournalTransaction'])) {
                CustomValidationFlash([__("You can't add an empty journal",true)]);
                $error = true;
            }
            $this->data['Journal']['draft'] = $this->params['url']['send'] == 'draft';
//			dd('2s2');
            if(!$error && $additionalFieldsFormHandler->validate($this->data)){
                $data =  $this->data;
                //$data['JournalTransaction'] = $this->Journal->groupTransactionsData($this->data);

                $default_currency = $this->Journal->get_default_currency();
                $this->set('default_currency' , $default_currency);
                $costCenterData = $this->getCostCenterTransactionListV2($data, $default_currency);
                [$taxesList, $taxesPercentages, $includedTaxes] = $this->getTaxesData();
                foreach ($data['JournalTransaction'] as  $transaction) {
                    if (empty($transaction['journal_account_id'])) {
                        \Rollbar\Rollbar::log(\Rollbar\Payload\Level::ERROR, getCurrentSite('id'). " empty account  ".json_encode($this->data). " staff_id:".getAuthStaff("staff_id"));
                    }
                }
                if (!$costCenterData['invalidCostCenters'] && $journalId = $this->Journal->save_journal($data, true, true, true)) {
                    if (isset($data['non_completed_id'])) {
                        $_SESSION['last_non_completed_id'] = $data['non_completed_id'];
                    }
                    $journal = $this->Journal->getJournalWithSortedTransactions($journalId);
                    $this->setTaxTransactionId($journal);
                    $transactionsWithoutTaxes = $this->getJournalTransactionsWithoutTaxes($journal['JournalTransaction']);
                    $originalTransactionsKeys = array_keys($costCenterData['journalsTransaction']);
                    foreach(array_values($transactionsWithoutTaxes) as $transactionKey => $transaction){
                        $jtCostCenters = [];
                        if($costCenterData['costCenterTransactionList']) {
                            $costCenterTransactionList = $costCenterData['costCenterTransactionList'];
                            foreach ($costCenterTransactionList[$originalTransactionsKeys[$transactionKey]] as $key => $CostCenterTransaction) {
                                $jtCostCenters[$key] = $CostCenterTransaction;
                                if($transaction['tax_id'] && $includedTaxes[$transaction['tax_id']]) {
                                    $jtCostCenters[$key]['CostCenterTransaction']['debit'] = ($jtCostCenters[$key]['CostCenterTransaction']['percentage'] / 100) * $transaction['debit'];
                                    $jtCostCenters[$key]['CostCenterTransaction']['credit'] = ($jtCostCenters[$key]['CostCenterTransaction']['percentage'] / 100) * $transaction['credit'];
                                }
                                $jtCostCenters[$key]['CostCenterTransaction']['journal_transaction_id'] = $transaction['id'];
                                $jtCostCenters[$key]['CostCenterTransaction']['journal_id'] = $journalId;
                                $jtCostCenters[$key] = $this->setFullPercentCostCenterData($jtCostCenters[$key], $transaction);
                                $type = $jtCostCenters[$key]['CostCenterTransaction']['type'];
                                if(empty($jtCostCenters[$key]['CostCenterTransaction'][$type]) || $jtCostCenters[$key]['CostCenterTransaction'][$type] == 0 || trim($jtCostCenters[$key]['CostCenterTransaction'][$type]) == ""){
                                   $jtCostCenters[$key]['CostCenterTransaction'][$type] = $transaction[$type];
                                }
                                if($default_currency != $data['Journal']['currency_code']){
                                    $jtCostCenters[$key]['CostCenterTransaction']['debit'] *= $journal['Journal']['currency_rate'];
                                    $jtCostCenters[$key]['CostCenterTransaction']['credit'] *= $journal['Journal']['currency_rate'];
                                }
                            }
                            $this->CostCenter->save_manual_cost_transactions($jtCostCenters);
                        }
                    }
                    $entityId = $journalId;
                    $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL, $entityId, 1)->toArray();
                    $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::JOURNAL);
                    $activityLogRequestCreator = new EntityActivityLogRequestsCreator();
                    $requests = $activityLogRequestCreator->create($st, $newData, [], []);
                    $activityLogService =  new \App\Services\ActivityLogService();
                    foreach ($requests as $requestObj) {
                        $activityLogService->addActivity($requestObj);
                    }
                    $updateSystemRate = $this->data['update_system_rate'] ?? false;
                    $shouldAddLocalCurrencyRate = $updateJournalEntriesCurrencyRatesSetting && $updateSystemRate && isset($manualCurrencyRate) && (getCurrentSite('currency_code') != $this->data['Journal']['currency_code']);
                    if($shouldAddLocalCurrencyRate){
                        $LocalCurrencyRateModel = GetObjectOrLoadModel('LocalCurrencyRate');
                        $dateNow = new DateTime();
                        $timeNow = $dateNow->format('H:i:s');
                        $journalDate = $LocalCurrencyRateModel->formatDate($this->data['Journal']['date']);

                        $localCurrencyData = [
                            'LocalCurrencyRate' => [
                                'date_from' => $journalDate . " " . $timeNow,
                                'from_currency' => $this->data['Journal']['currency_code'],
                                'to_currency' => getCurrentSite('currency_code'),
                                'rate' => $manualCurrencyRate,
                                'updated_by_journal_id' => $journalId,
                            ]
                        ];
                        $result = $LocalCurrencyRateModel->addNewLocalCurrencyRate($localCurrencyData);
                        if(!$result['status']){
                            $this->flashMessage($result['error']);
                        }else{
                            $localCurrencyRateNewData = getRecordWithEntityStructure(EntityKeyTypesUtil::LOCAL_CURRENCY_RATE, $LocalCurrencyRateModel->id, 1)->toArray();
                            $this->addRelatedLocalCurrencyRateActivityLog($journalId, $localCurrencyRateNewData);
                        }
                    }
                }
                if(IS_REST){
                    $this->set('id', $journalId);
                    $this->render('created');
                    return;
                }


                if(!$costCenterData['invalidCostCenters'] && $journalId){
                    if ($this->data["Journal"]["redirect_type"] == "create_recurring") {
                        $this->izamFlashMessage(sprintf (__('The %s has been saved', true), __('Journal',true)));
                        $redirect = Router::url('/v2/owner/recurring-profile/create/journal/'. $journalId);
                        $this->redirect($redirect);
                    }

                    if (isset($this->data["Journal"]["entity_type"]) && isset($this->data["Journal"]["entity_id"])) {
                        if ($this->data["Journal"]["entity_type"] == 'work_order') {
                            $redirect = $this->getRedirectionUrl($this->data["Journal"]["entity_type"], $this->data["Journal"]["entity_id"]);
                            $this->redirect($redirect);
                        }
                    }

                    $additionalFieldsFormHandler->store($journalId, $this->data);

                    $this->flashMessage(sprintf (__('The %s has been saved', true), __('Journal',true)), 'Sucmessage');
                    $this->redirect(array('action'=>'view', $journalId));
                }

                } //else {
                    $this->logTransactionData($data, $transaction, 'Add');
                    $this->data['Journal']['date'] = !empty($this->data['Journal']['date']) ?  $this->Journal->formatDate($this->data['Journal']['date']) : date('Y-m-d');
                    // Used To get attachments details in case of Validation error occurred. 
                    if(!empty($this->data['Journal']['attachment'])){                       
                        $filesId = explode(',',$this->data['Journal']['attachment']);
                        $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                        $this->data['Attachments'] = $attachment;
                     }
                     if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=> array_merge($this->Journal->validationErrors, $additionalFieldsFormHandler->getValidationErrors() )]);
                    $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('Journal',true)));
                if (!empty( $this->Journal->validationErrors)) {
                    $this->flashMessage(implode("<br/>", $this->Journal->validationErrors), 'Errormessage', 'secondaryMessage');
                }
               // }
            
        }else{
            if ( !empty($id)) {
                $this->loadModel('CostCenterTransaction');
                $journal = $this->Journal->getJournalWithSortedTransactions($id);
                if(isset($_GET['reverse']) && $_GET['reverse']) {
                    $journal = $this->Journal->reverse_journal($journal);
                }
                if(!empty($journal))
                {
                    $this->data = $journal;
                    $cost_centers = $this->CostCenterTransaction->getJournalCostTransaction($id);
                    $assigned_cost_centers = [];
                    if (!empty($this->viewVars['enable_tags'])) {
                        $this->assignTagsToJournalTransaction($journal['JournalTransaction']);
                    }
                    foreach($journal['JournalTransaction'] as $key => $transaction) {
                        foreach($cost_centers as &$cost_center){
                            unset($cost_center['CostCenterTransaction']['journal_id'], $cost_center['CostCenterTransaction']['id']);
                            $cost_centers_list[] = $cost_center['CostCenter'];
                            if( $cost_center['CostCenterTransaction']['journal_transaction_id'] == $transaction['id'] || (is_null($cost_center['CostCenterTransaction']['journal_transaction_id']) && $cost_center['CostCenterTransaction']['journal_account_id'] == $transaction['journal_account_id'])) {
                                $assigned_cost_centers[$key][] = $cost_center['CostCenterTransaction'];
                            }
                        }
                    }
                    unset($this->data['Journal']['id']);
                    unset($this->data['Journal']['entity_type']);
                    unset($this->data['Journal']['entity_id']);
                    $this->data['Journal']['is_automatic']=0;
                    $this->data['Journal']['date']=date('Y-m-d');
                    foreach($this->data['JournalTransaction'] as $k=>$jt)
                    {
                        unset($this->data['JournalTransaction'][$k]['id']);
                        unset($this->data['JournalTransaction'][$k]['journal_id']);

                    }


                    $this->loadModel("JournalAccount");
                    $journal_accounts = $this->Journal->get_mapped_accounts($journal);
                    foreach($this->data['JournalTransaction']as $k => $transaction){
                        $this->data['JournalTransaction'][$k]['account_name'] = "#" .$journal_accounts[$this->data['JournalTransaction'][$k]['journal_account_id']]['code'] ." ". $journal_accounts[$this->data['JournalTransaction'][$k]['journal_account_id']]['name'];
                    }

                    $this->set('assigned_cost_centers_list', $cost_centers_list);
                    $this->set('assigned_cost_centers', $assigned_cost_centers);
                }
            }

            App::import('Vendor', 'AutoNumber');
            $number = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_JOURNAL);
            $this->data['Journal']['number'] = $number;
            $this->data['Journal']['hidden_number'] = $number;
            $this->data['Journal']['date'] = date('Y-m-d');
        }

        if(!empty($this->data['CostCenterTransactionV2'])){
            $assigned_cost_centers = $this->data['CostCenterTransactionV2'];
            $this->set('assigned_cost_centers', $assigned_cost_centers);
        }
        if(!empty($_GET['journal_account_id']))
            $this->data['JournalTransaction'][0]['journal_account_id'] = $_GET['journal_account_id'];

        $currencies = getCurrenciesList();
//		$journalAccounts = $this->Journal->JournalAccount->find('list');
        $types = $this->Journal->get_journal_types_list();
//        $accountNames = $this->owner_get_accounts_ajax();
        $this->loadModel('JournalAccount');
        $this->loadModel('JournalCat');
        $accounts_array = array();
        $this->JournalAccount->recursive = -1;
        $this->set('useNewCostCenterMode', true);
        $cost_centers_list = $this->CostCenter->get_secondary_cost_centers(true);
        $this->set('cost_centers_list', $cost_centers_list);
        if (isset($_GET["redirect_type"])) {
            $this->set('redirectType', $_GET["redirect_type"]);
        }
        // dd($_GET);

        $allowDraft = check_permission(MANAGE_DRAFT_JOURNALS);
        $allowNormalSave = check_permission(MANAGE_ALL_JOURNALS) || check_permission(MANAGE_OWN_JOURNALS);
        $this->set('allowNormalSave', $allowNormalSave);
        $this->set('allowDraft', $allowDraft);
        $this->set('is_clone', $id);
//		debug($catsList);

//		die(debug($accounts_array));
        //$accountNames = $this->owner_get_accounts_ajax();
        //$accountNames = [0=> __("Please Select", true) ] +$accountNames;
        $this->set('title_for_layout',  __('Add Journal',true) );
        $this->setTaxesData();
        $this->set(compact('currencies','types','catsList','accountNames', 'taxes'));
        if(!empty($flushedTransactionList)) {
            $this->data['JournalTransaction'] = $flushedTransactionList;
        }  
        $this->set('forms', $additionalFieldsFormHandler->getCreateForms($this->data));

    }

    function owner_save_auto_journal()
    {
        if(!empty($this->data))
        {
            $journal_id = $this->Journal->update_auto_journal($this->data, ifPluginActive(AccountingPlugin), ifPluginActive(AccountingPlugin));
            if($journal_id)
            {
                if(IS_REST){
                    $this->set('id', $journal_id);
                    $this->render('created');
                    return;
                }
            }else{
                if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Journal->validationErrors]);
            }
        }else{
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }


    }

    private function getJournalTransactionsWithoutTaxes($transactions){

        $transactionsWithoutTaxes = array_filter($transactions, function($transaction) {
            return !str_starts_with($transaction['subkey'], 'manual_income_tax_') && !str_starts_with($transaction['subkey'], 'manual_outcome_tax_');
        });

        return $transactionsWithoutTaxes;
    }


    function owner_edit($id = null) {
        $useNewCostCenterMode = true;
        Journal::$auto_accounts['client']['callback_after_add']='adjust_and_pay';
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->journals_js_labels);

        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();


        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.', 'journal',true)));
            $this->redirect(array('action'=>'index'));
        }

        // Load Attachments . 
        $this->Journal->bindAttachmentRelation('journal');

        $journal = $this->Journal->read(null, $id);
        if(empty($journal)&&IS_REST) $this->cakeError('error404', array('message' => __(sprintf ('Invalid %s', 'journal'),true)));
        if(empty($journal))
        {
            if(IS_REST) $this->cakeError('error404');
            $this->flashMessage(sprintf (__t('Invalid %s.'),__t('journal')));
            $this->redirect('/owner/journals/index');

        };
        $owner = getAuthOwner();
        if($owner['staff_id'] != 0)
        {
            $staff = $owner['staff_id'];

            if (!check_permission(MANAGE_ALL_JOURNALS) && (!(check_permission(MANAGE_OWN_JOURNALS) && $staff == $journal['Journal']['staff_id'])) && (($journal['Journal']['draft'] && !check_permission(MANAGE_DRAFT_JOURNALS)) || !$journal['Journal']['draft'])) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        $this->loadModel('JournalAccount');
        $this->loadModel('CostCenterTransaction');
        
        $default_currency = $this->Journal->get_default_currency();
		$cost_center_transactions = [];

        if($journal['Journal']['is_automatic']) {
          $journal_source = Journal::get_link($journal);
          $extraAttrs = '';
          if (isset($_GET['from_matching'])) {
            $extraAttrs .= " target='_blank' ";
          }
          $url = sprintf("<a href='%s' class='action-global-btn btn btn-secondary s2020' ".$extraAttrs.">%s</a>", $journal_source['link'], __('View Source', true));
          $this->flashMessage(sprintf(__("It is not possible to edit the values of the auto created journal entries which created via other transactions, you can edit the source of the journal %s", true), $url), "info");
        }
        $this->validate_open_day($journal['Journal']['date']);
        if (!empty($this->data)) {
            $oldData = EntityActivityLog::getRecordWithEntityStructureWithCustomFields(EntityKeyTypesUtil::JOURNAL, $id, 1);

            $error = false;
            // used in logging attachments .  
            $attachmentForLog = implode(',',izam_resolve(AttachmentsService::class)->getAttachmentIds('journal',$id));

            foreach ($this->data['JournalTransaction'] as $k => $transaction) {
                if(empty($transaction['currency_credit']) && empty($transaction['currency_debit'])) {
                    unset($this->data['JournalTransaction'][$k]);
                    continue;
                }

                if(empty($transaction['journal_account_id'])) {
                    unset($this->data['JournalTransaction'][$k]);
                    continue;
                }
            }
            $this->data = $this->setTransactionsTaxData($this->data, $journal);

            if(empty($this->data['JournalTransaction'])) {
                CustomValidationFlash([__("You can't add an empty journal",true)]);
                $error = true;
            }
            $total_debit = 0;
            $total_credit = 0;


            foreach($this->data['JournalTransaction'] as $k =>&$v){
                $total_debit += (float)$v['currency_debit'];
                $total_credit += (float)$v['currency_credit'];
                $v['currency_code'] = $this->data['Journal']['currency_code'];
                if(trim($v['alter_description']) == trim($v['description']))
                    unset($v['alter_description']);
                /**
                 * if the description is changed from the original description and is automatic
                 * then we need to save the alter description
                 */
                if($journal['Journal']['is_automatic'] && $v['description'] != $journal['JournalTransaction'][$k]['description']) {
                    if(!isset($v['alter_description'])){
                        $v['alter_description'] = $v['description'];
                    }
                }
            }
            if(!float_equal($total_debit, $total_credit))
            {
                if(IS_REST) $this->cakeError("error400", ["message"=>sprintf (__('The %s could not be saved. The total debit must be equal to the total credit', true), __('Journal',true))]);
                $this->flashMessage(sprintf (__('The %s could not be saved. The total debit must be equal to the total credit', true), __('Journal',true)));
                $error = true;
            }

            if(!$additionalFieldsFormHandler->validate($this->data)){
                $error = true;
            }

            if(trim($this->data['Journal']['alter_description']) == trim($this->data['Journal']['description']))
                unset($this->data['Journal']['alter_description']);

            $data =  $this->data;


            if(isset($data['CostCenterTransactionV2']) || !isset($data['CostCenterTransaction'])) {
                $costCenterData = $this->getCostCenterTransactionListV2($data, $default_currency);
                [$taxesList, $taxesPercentages, $includedTaxes] = $this->getTaxesData();
            } else {
                $costCenterData = $this->getCostCenterTransactionList($data, $default_currency);
            }
          if ($this->params['url']['send'] != 'draft' && (strpos($data['Journal']['number'], 'draft') !== false || $journal['Journal']['draft'] == 1)) {
                $data['Journal']['draft'] = 0;
                App::import('Vendor', 'AutoNumber');
                $number = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_JOURNAL);
                $data['Journal']['number'] = $number;
                $data['Journal']['hidden_number'] = $number;
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_JOURNAL);
            }
            //$data['JournalTransaction'] = $this->Journal->groupTransactionsData($this->data);
            // added to fix currency rate in calculate_total() . 
            $data['Journal']['date'] = $data['Journal']['date'] ?? $journal['Journal']['date'];
 
            if (!$error && $result =  $this->Journal->save_journal($data,true,true,true)) {

                $updateJournalEntriesCurrencyRatesSetting = settings::getValue(AccountingPlugin, SettingsUtil::UPDATE_JOURNAL_ENTRIES_CURRENCY_RATES);
                $updateSystemRate = $this->data['update_system_rate'] ?? false;
                $inputManualCurrencyRate = $this->data['Journal']['manual_currency_rate'] ?? null;
                $shouldAddLocalCurrencyRate = $updateJournalEntriesCurrencyRatesSetting && $updateSystemRate && isset($inputManualCurrencyRate) && (getCurrentSite('currency_code') != $this->data['Journal']['currency_code']);
                if($shouldAddLocalCurrencyRate){
                    $LocalCurrencyRateModel = GetObjectOrLoadModel('LocalCurrencyRate');
                    $localCurrencyUpdatedByJournal = $LocalCurrencyRateModel->find('first', [
                        'conditions' => [
                            'LocalCurrencyRate.updated_by_journal_id' => $journal['Journal']['id'],
                        ]
                    ]);
                    $nowDate = new DateTime();
                    $formattedInputDate = $LocalCurrencyRateModel->formatDate($data['Journal']['date']);
                    if($localCurrencyUpdatedByJournal){
                        $localCurrencyRateOldData = getRecordWithEntityStructure(EntityKeyTypesUtil::LOCAL_CURRENCY_RATE, $localCurrencyUpdatedByJournal['LocalCurrencyRate']['id'], 1)->toArray();
                        //update
                        $dataToUpdate = [];
                        $formattedLocalCurrencyDateFrom = $LocalCurrencyRateModel->formatDate($localCurrencyUpdatedByJournal['LocalCurrencyRate']['date_from']);
                        $smallestDate = $formattedLocalCurrencyDateFrom < $formattedInputDate ? $formattedLocalCurrencyDateFrom : $formattedInputDate;
                        if($formattedLocalCurrencyDateFrom != $formattedInputDate){
                            $dataToUpdate['date_from'] = Formatter::formatForDb($formattedInputDate . ' ' . $nowDate->format('H:i:s'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE);
                        }
                        if($localCurrencyUpdatedByJournal['LocalCurrencyRate']['from_currency'] != $this->data['Journal']['currency_code']){
                            $dataToUpdate['from_currency'] = $this->data['Journal']['currency_code'];
                        }
                        if($localCurrencyUpdatedByJournal['LocalCurrencyRate']['rate'] != $inputManualCurrencyRate){
                            $dataToUpdate['rate'] = $inputManualCurrencyRate;
                        }
                        if(!empty($dataToUpdate)){
                            $LocalCurrencyRateModel->update($localCurrencyUpdatedByJournal['LocalCurrencyRate']['id'], $dataToUpdate);
                            $LocalCurrencyRateModel->update_currency_transactions(
                                $this->data['Journal']['currency_code'],
                                getCurrentSite('currency_code'),
                                $smallestDate . ' ' . $nowDate->format('H:i:s')
                            );
                            $localCurrencyRateNewData = getRecordWithEntityStructure(EntityKeyTypesUtil::LOCAL_CURRENCY_RATE, $localCurrencyUpdatedByJournal['LocalCurrencyRate']['id'], 1)->toArray();
                            $this->addRelatedLocalCurrencyRateActivityLog($journal['Journal']['id'], $localCurrencyRateNewData, $localCurrencyRateOldData);
                            $this->add_actionline(ACTION_UPDATE_LOCAL_CURRENCY_RATE, ['primary_id' => $localCurrencyUpdatedByJournal['LocalCurrencyRate']['id']]);
                        }
                    }else{
                        //insert
                        $localCurrencyData = [
                            'LocalCurrencyRate' => [
                                'date_from' => Formatter::formatForDb($formattedInputDate . ' ' . $nowDate->format('H:i:s'), EntityFieldUtil::ENTITY_FIELD_TYPE_DATE),
                                'from_currency' => $this->data['Journal']['currency_code'],
                                'to_currency' => getCurrentSite('currency_code'),
                                'rate' => $inputManualCurrencyRate,
                                'updated_by_journal_id' => $journal['Journal']['id'],
                            ]
                        ];
                        $result = $LocalCurrencyRateModel->addNewLocalCurrencyRate($localCurrencyData);
                        if(!$result['status']){
                            $this->flashMessage($result['error']);
                        }else{
                            $localCurrencyRateNewData = getRecordWithEntityStructure(EntityKeyTypesUtil::LOCAL_CURRENCY_RATE, $LocalCurrencyRateModel->id, 1)->toArray();
                            $this->addRelatedLocalCurrencyRateActivityLog($journal['Journal']['id'], $localCurrencyRateNewData);
                        }
                    }
                }

                $journal = $this->Journal->getJournalWithSortedTransactions($id);
                $this->setTaxTransactionId($journal);
                if(IS_REST){
                    $this->render('success');
                    return;
                }
                $this->set('default_currency' , $default_currency);
                $costCenterTransactionList = [];  
				$cost_data=$data; 
                $alreadyAssigned = []; 
                $this->loadModel("JournalAccount");
                $this->loadModel('CostCenter');
                if(!empty($costCenterData['costCenterTransactionList'])) {
                    if(!isset($data['CostCenterTransaction'])) {
                        $journal = $this->Journal->getJournalWithSortedTransactions($id);
                        $transactionIds = [];
                        $transactionsWithoutTaxes = $this->getJournalTransactionsWithoutTaxes($journal['JournalTransaction']);
                        $originalTransactionsKeys = array_keys($costCenterData['journalsTransaction']);
                        foreach(array_values($transactionsWithoutTaxes) as $transactionKey => $transaction) {
                            $transactionIds[] = $transaction['id'];
                            $currentCostCenters = $this->CostCenterTransaction->getCostCenterTransactionsByJtransactionId($transaction['id']);
                            if(is_numeric($costCenterData['journalsTransaction'][$transaction['id']])) {
                                $jtCostCenters = [];
                                $updatedCostTransactions = [];
                                foreach($costCenterData['costCenterTransactionList'][$transaction['id']] as $transactionId => $CostCenterTransaction) {
                                    if(isset($CostCenterTransaction['CostCenterTransaction']['id'])) {
                                        $costCenterBuffer = [];
                                        $costCenterTransactionList = array_filter($costCenterData['costCenterTransactionList']);
                                        $updatedCostTransactions[$transaction['id']][] = isset($CostCenterTransaction['CostCenterTransaction']['id']) ? $CostCenterTransaction['CostCenterTransaction']['id'] : false;
                                        $costCenterBuffer = $CostCenterTransaction;
                                        if($transaction['tax_id'] && $includedTaxes[$transaction['tax_id']]) {
                                            $costCenterBuffer['CostCenterTransaction']['debit'] = ($costCenterBuffer['CostCenterTransaction']['percentage'] / 100) * $transaction['debit'];
                                            $costCenterBuffer['CostCenterTransaction']['credit'] = ($costCenterBuffer['CostCenterTransaction']['percentage'] / 100) * $transaction['credit'];
                                        }
                                        $costCenterBuffer['CostCenterTransaction']['journal_transaction_id'] = $transaction['id'];
                                        $costCenterBuffer['CostCenterTransaction']['journal_id'] = $id;
                                        $costCenterBuffer['CostCenterTransaction'] = $this->setFullPercentCostCenterData($costCenterBuffer['CostCenterTransaction'], $transaction);
                                        $type = $costCenterBuffer['CostCenterTransaction']['type'];
                                        if(!isset($costCenterBuffer['CostCenterTransaction'][$type]) || $costCenterBuffer['CostCenterTransaction'][$type] == 0 || trim($costCenterBuffer['CostCenterTransaction'][$type]) == ""){
                                            $costCenterBuffer['CostCenterTransaction'][$type] = $transaction[$type];
                                         }
                                        if($default_currency != $data['Journal']['currency_code']) {
                                            $costCenterBuffer['CostCenterTransaction']['debit']   = $costCenterBuffer['CostCenterTransaction']['currency_debit'] * $journal['Journal']['currency_rate'];
                                            $costCenterBuffer['CostCenterTransaction']['credit']  = $costCenterBuffer['CostCenterTransaction']['currency_credit'] * $journal['Journal']['currency_rate'];
                                        }
                                        array_push($jtCostCenters, $costCenterBuffer);        
                                    }
                                }
                                $this->CostCenter->save_manual_cost_transactions($jtCostCenters);

                            } else {
                                $costCenterTransactionList = $costCenterData['originalCostCenterPerTransactions'];
                                $jtCostCenters = [];
                                if(!is_null($costCenterTransactionList[$originalTransactionsKeys[$transactionKey]])){
                                    foreach ($costCenterTransactionList[$originalTransactionsKeys[$transactionKey]] as $key => $CostCenterTransaction) {
                                        $jtCostCenters[$key] = $CostCenterTransaction;
                                        $updatedCostTransactions[$transaction['id']][] = isset($CostCenterTransaction['CostCenterTransaction']['id']) ? $CostCenterTransaction['CostCenterTransaction']['id'] : false;
                                        if($transaction['tax_id'] && $includedTaxes[$transaction['tax_id']]) {
                                            $jtCostCenters[$key]['CostCenterTransaction']['debit'] = ($jtCostCenters[$key]['CostCenterTransaction']['percentage'] / 100) * $transaction['debit'];
                                            $jtCostCenters[$key]['CostCenterTransaction']['credit'] = ($jtCostCenters[$key]['CostCenterTransaction']['percentage'] / 100) * $transaction['credit'];
                                        }
                                        $jtCostCenters[$key]['CostCenterTransaction']['journal_transaction_id'] = $transaction['id'];
                                        $jtCostCenters[$key]['CostCenterTransaction']['journal_id'] = $id;
                                        $jtCostCenters[$key] = $this->setFullPercentCostCenterData($jtCostCenters[$key], $transaction);
                                        if($default_currency != $data['Journal']['currency_code']){
                                            $jtCostCenters[$key]['CostCenterTransaction']['debit'] *= $journal['Journal']['currency_rate'];
                                            $jtCostCenters[$key]['CostCenterTransaction']['credit'] *= $journal['Journal']['currency_rate'];
                                        }
                                    }

                                    $this->CostCenter->save_manual_cost_transactions($jtCostCenters);
                                } else {
                                    $this->CostCenterTransaction->deleteAll(['CostCenterTransaction.journal_transaction_id' => $transaction['id']]);
                                }

                            }

                            if($currentCostCenters && $updatedCostTransactions[$transaction['id']]) {
                                $deletedCostCenterTransaction = array_diff(array_values($currentCostCenters), array_values($updatedCostTransactions[$transaction['id']]));
                                $this->CostCenterTransaction->deleteAll(['CostCenterTransaction.id' => $deletedCostCenterTransaction]);
                            }



                        }
  
                    } else {
                        $costCenterTransactionList = array_filter($costCenterData['costCenterTransactionList']);
                        foreach ($costCenterTransactionList as $key => $CostCenterTransaction) {
                            $costCenterTransactionList[$key]['CostCenterTransaction']['journal_id'] = $id;
                        }
                        $this->CostCenter->save_cost_center_transaction($costCenterTransactionList, false, true, false);
                    }
                }
                if(empty($costCenterTransactionList)) {
                    $this->CostCenterTransaction->deleteAll(['CostCenterTransaction.journal_id' =>$id ]);
                }
                $additionalFieldsFormHandler->update($id, $this->data);
                
                $newData = EntityActivityLog::getRecordWithEntityStructureWithCustomFields(EntityKeyTypesUtil::JOURNAL, $id, 1);
                
                //   $newData = $data;
                // $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL, $id, 3)->toArray();
                $newData['attachment'] = (!empty($data['Journal']['attachment']))?$data['Journal']['attachment']:null; 
                $oldData['attachment'] = $attachmentForLog ;
           
                EntityActivityLog::activityLogUpdate(EntityKeyTypesUtil::JOURNAL, $newData, $oldData);
                
                // $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::JOURNAL);
                // $activityLogRequestCreator = new EntityActivityLogRequestsCreator();

                // $requests = $activityLogRequestCreator->create($st, $newData, $oldData);

               
                // $activityLogService =  new \App\Services\ActivityLogService();
                // foreach ($requests as $requestObj) {
                //     $activityLogService->addActivity($requestObj);
                // }

                $this->flashMessage(sprintf (__('The %s  has been saved', true), __('Journal',true)), 'Sucmessage');
                $this->redirect(array('action'=>'view', $id));
            } else {
                    /*
                        Will be removed later
                        Log cost center transactions
                    */
                    $this->logTransactionData($data, $transaction, 'Edit');
                    // Used To get attachments details in case of Validation error occurred.
                    if(!empty($this->data['Journal']['attachment'])){                       
                        $filesId = explode(',',$this->data['Journal']['attachment']);
                        $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                        $this->data['Attachments'] = $attachment;
                     }
                    $this->data['Journal']['date'] = !empty($data['Journal']['date']) ? $this->Journal->formatDate($data['Journal']['date']) : $this->Journal->formatDate($journal['Journal']['date']);
                if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=> array_merge($this->Journal->validationErrors,$additionalFieldsFormHandler->getValidationErrors())]);
                $this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('Journal',true)));
                if (!empty( $this->Journal->validationErrors)) {
                    $this->flashMessage(implode("<br/>", array_merge($this->Journal->validationErrors,$additionalFieldsFormHandler->getValidationErrors())), 'Errormessage', 'secondaryMessage');
                }
            }
            
        } 
        if (empty($this->data) && !$error) {
            if($journal['Journal']['is_automatic'])
            {
                $transactions = $this->Journal->JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $id) ,'order' => array('JournalTransaction.debit' => 'DESC', 'JournalTransaction.credit' => 'DESC') ));
            }else{
                $transactions = $this->Journal->JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $id) ,'order' => array('COALESCE(JournalTransaction.display_order, JournalTransaction.id)' => 'ASC') ));
            }

            //Backward compatability for cost center new added display order
            if(!$transactions) {
                $transactions = $this->Journal->JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $id) ,'order' => array('JournalTransaction.created' => 'DESC') ));
            }

            $journal['JournalTransaction'] = [];
            foreach ($transactions as $k => $transaction) {
                $journal['JournalTransaction'][] = $transaction['JournalTransaction'];
            }

            $this->data = $journal;
            $this->loadModel("JournalAccount");
            $journal_accounts = $this->Journal->get_mapped_accounts($journal);
            foreach($this->data['JournalTransaction']as $k => $transaction){
                $this->data['JournalTransaction'][$k]['account_name'] = "#" .$journal_accounts[$this->data['JournalTransaction'][$k]['journal_account_id']]['code'] ." ". $journal_accounts[$this->data['JournalTransaction'][$k]['journal_account_id']]['name'];
            }
        }
       
        if(empty($this->data) || empty($this->data['CostCenterTransaction'])) {
            $assigned_cost_centers = $this->CostCenterTransaction->getJournalCostTransaction($id);
            if(!empty($assigned_cost_centers) && is_null($assigned_cost_centers[0]['CostCenterTransaction']['journal_transaction_id'])) {
                $useNewCostCenterMode = false; 
                $filteredCostCenterList = [];
                array_map( function($cct) use(&$filteredCostCenterList) {
                    $filteredCostCenterList[$cct['CostCenter']['id']] = $cct['CostCenter']['name'] ;
                }, $assigned_cost_centers);

                $assigned_cost_centers = $this->restructureCostCenterTransactions($this->data['JournalTransaction'], $assigned_cost_centers);
                $this->set('filteredCostCenterList', $filteredCostCenterList);
            } else {
               $cost_centers = $assigned_cost_centers;
               $assigned_cost_centers = [];
               foreach($this->data['JournalTransaction'] as $transaction) {
                 foreach($cost_centers as $cost_center){
                   $cost_centers_list[] = $cost_center['CostCenter'];
                   if( $cost_center['CostCenterTransaction']['journal_transaction_id'] == $transaction['id']) {
                    $assigned_cost_centers[$transaction['id']][] = $cost_center['CostCenterTransaction'];
                   }
                 }
               }
            }
            $this->set('assigned_cost_centers_list', $cost_centers_list);
        } else {
            $assigned_cost_centers = $this->collectCostCenterByTypes($this->data['CostCenterTransaction']);
        }

        $this->set('assigned_cost_centers', $assigned_cost_centers);
        $this->set('useNewCostCenterMode', $useNewCostCenterMode);
        $this->loadModel('JournalAccount');  
        $this->set('JournalAccount', $this->JournalAccount);
        $currencies = getCurrenciesList();

        $allowDraft = check_permission(MANAGE_DRAFT_JOURNALS) && $this->data['Journal']['draft'];
        $allowNormalSave = check_permission(MANAGE_ALL_JOURNALS) || check_permission(MANAGE_OWN_JOURNALS);
        $this->set('allowNormalSave', $allowNormalSave);
        $this->set('allowDraft', $allowDraft);

        $types = $this->Journal->get_journal_types_list();
        $mapped_accounts= $this->Journal->get_mapped_accounts( $journal);
        $accountNames=[];
        foreach ($mapped_accounts as $k=>$account)
            $accountNames[$k]=''.$account['code'].' - '.$account['name'];

        //      $accountNames = $this->owner_get_accounts_ajax();
        $accountNames = [0=> __("Please Select", true) ] +$accountNames;

        $this->set(compact('currencies','types','accounts','catsList','accountNames'));
        $this->loadModel('CostCenter');
//        $cost_centers_list = $this->CostCenter->get_secondary_cost_centers(true);
        $cost_centers_list = []; // as in edit if the user has alot of cost centers and alot of lines in the journal the page will break
        $this->set('title_for_layout',  __('Edit Journal',true) );
        $this->set('cost_centers_list', $cost_centers_list);
        $allowDraft = check_permission(MANAGE_DRAFT_JOURNALS);
        $allowNormalSave = check_permission(MANAGE_ALL_JOURNALS) || check_permission(MANAGE_OWN_JOURNALS);
        $this->set('allowNormalSave', $allowNormalSave);
        $this->set('allowDraft', $allowDraft);
        $this->setTaxesData($journal);
        $this->__setting();
        if (!empty($this->viewVars['enable_tags'])) {
            $this->assignTagsToJournalTransaction($journal['JournalTransaction']);
        }
        $this->set('forms', $additionalFieldsFormHandler->getEditForms($id, $this->data));
        $this->render('owner_add');

    }

    private function addRelatedLocalCurrencyRateActivityLog($journalId, $newData, $oldData = []){
        $activityLogRequestCreator = new EntityActivityLogRequestsCreator();
        $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::LOCAL_CURRENCY_RATE);
        $requests = $activityLogRequestCreator->create($st, $newData, $oldData, [
            new ActivityLogRelationRequest($journalId, EntityKeyTypesUtil::JOURNAL)
        ]);
        $activityLogService =  new \App\Services\ActivityLogService();
        foreach ($requests as $requestObj) {
            $requestObj->setUrl('/owner/local_currency_rates/edit/' . $requestObj->getMainActivityLogRelation()->getEntityId());
            $activityLogService->addActivity($requestObj);
        }
    }

    protected function setTaxTransactionId($journal) {
        foreach ($journal['JournalTransaction'] as $transaction) {
            if($transaction['tax_id']) {
                $subkeyOne = "manual_income_tax_{$transaction['tax_id']}";
                $subkeyTwo = "manual_outcome_tax_{$transaction['tax_id']}";

                $journalId = $journal['Journal']['id'];
                $updateQuery = "UPDATE journal_transactions set tax_transaction_id = (select id from (select id from journal_transactions where journal_id = {$journalId} and (subkey =  ('$subkeyOne') or subkey =  ('$subkeyTwo'))   ) temp) where id = {$transaction['id']}";

                $this->Journal->query($updateQuery);
            }
        }
    }

    protected function setTransactionsTaxData($transactions, $oldData = null) {
        [$taxesList, $taxPercentages, $includedTaxes] = $this->getTaxesData($oldData);
        $descriptions = [];
        $currencyRound = 2;
        foreach($transactions['JournalTransaction'] as &$transaction) {
            if(strlen(substr(strrchr($transaction['currency_debit'], "."), 1)) > $currencyRound) {
                $currencyRound = strlen(substr(strrchr($transaction['currency_debit'], "."), 1));
            }
            if(strlen(substr(strrchr($transaction['currency_credit'], "."), 1)) > $currencyRound) {
                $currencyRound = strlen(substr(strrchr($transaction['currency_credit'], "."), 1));
            }
        }
        foreach ($transactions['JournalTransaction'] as &$transaction) {
            if($transaction['tax_id']) {
                $taxId = $transaction['tax_id'];
                $taxPercentage = $taxPercentages[$transaction['tax_id']];
                if($includedTaxes[$taxId]) {
                    $taxCredit = round($transaction['currency_credit'] / (1 + $taxPercentage / 100) * $taxPercentage / 100, 5);
                    $transaction['currency_credit'] -= $taxCredit;
                    $taxDebit = round($transaction['currency_debit'] / (1 + $taxPercentage / 100) * $taxPercentage / 100, 5);
                    $transaction['currency_debit'] -= $taxDebit;
                    $transaction['tax_included'] = 1;
                } else {
                    $transaction['tax_included'] = 0;
                    $taxCredit = $transaction['currency_credit'] * $taxPercentage / 100;
                    $taxDebit = $transaction['currency_debit'] * $taxPercentage / 100;
                }

                if($taxCredit) {
                    $entityType = 'income_tax';
                    $subkey = "manual_income_tax_$taxId";
                } else {
                    $entityType = 'outcome_tax';
                    $subkey = "manual_outcome_tax_$taxId";
                }
                $transaction['tax_percentage'] = $taxPercentage;

                if ((empty($oldData) || $oldData['Journal']['created'] > "2025-08-02" || $transactions['force_rounding']) && empty($includedTaxes[$taxId])) {
                    $taxDebit = round($taxDebit, $currencyRound);
                    $taxCredit = round($taxCredit, $currencyRound);
                    $transaction['currency_credit'] = round($transaction['currency_credit'], $currencyRound);
                    $transaction['currency_debit'] = round($transaction['currency_debit'], $currencyRound);
                }

                $transaction['tax_debit'] = $taxDebit;
                $transaction['tax_credit'] = $taxCredit;
                $journalId = $transaction['journal_id'];
                if(!isset($transactions['JournalTransaction'][$subkey])) {
                    $oldTaxTransaction = $this->Journal->JournalTransaction->find('first', ['conditions' => ['JournalTransaction.journal_id' => $journalId, 'JournalTransaction.subkey' => $subkey]]);
                    $descriptions[$subkey][] = $transaction['description'];
                    $transactions['JournalTransaction'][$subkey] = [
                        'id' => $oldTaxTransaction ? $oldTaxTransaction['JournalTransaction']['id'] : null,
                        'currency_credit' => 0,
                        'description' => $transaction['description'],
                        'currency_debit' => 0,
                        'subkey' => $subkey,
                        'auto_account' => [
                            'type' => 'dynamic',
                            'entity_type' => $entityType,
                            'entity_id' => $taxId
                        ]
                    ];
                } else {
                    $descriptions[$subkey][] = $transaction['description'];
                }
                $transactions['JournalTransaction'][$subkey]['currency_credit'] += $taxCredit;
                $transactions['JournalTransaction'][$subkey]['currency_debit'] += $taxDebit;
                $transactions['JournalTransaction'][$subkey]['description'] = implode(' / ', $descriptions[$subkey]);
            }
        }
        $transactions = $this->Journal->set_auto_account_ids($transactions); 
        return $transactions;
    }

    protected function setTaxesData($oldData = null) {
        if($oldData && $oldData['Journal']['is_automatic']) {
            return;
        }
        $displayTaxes = settings::getValue(AccountingPlugin, 'tax_in_journals');
        $this->set('display_taxes', $displayTaxes);
        [$taxesList, $taxesPercentages, $includedTaxes] = $this->getTaxesData($oldData);
        $this->set('taxesList', $taxesList);
        $this->set('taxesPercentages', $taxesPercentages);
        $this->set('includedTaxes', $includedTaxes);

    }

    protected $taxesData = [];

    protected function getTaxesData($oldData = null) {
        $this->loadModel('Tax');
        $taxesList = [];
        $taxPercentages = [];
        $includedTaxes = [];
        $taxes = $this->Tax->find('all', ['recursive' => -1]);
        $includedTaxes = [];
        foreach ($taxes as $tax) {
            $taxesList[$tax["Tax"]['id']] = $tax['Tax']['name'];
            $taxPercentages[$tax["Tax"]['id']] = $tax['Tax']['value'];
            $includedTaxes[$tax["Tax"]['id']] = $tax['Tax']['included'];
        }
        if($oldData) {
            foreach ($oldData['JournalTransaction'] as $transaction) {
                if($transaction['tax_id'] && !is_null($transaction['tax_percentage'])) {
                    $taxPercentages[$transaction['tax_id']] = $transaction['tax_percentage'];
                }
            }
        }
        if(empty($this->taxesData)) {
            $this->taxesData = [$taxesList, $taxPercentages, $includedTaxes];
        }
        return $this->taxesData;
    }

    function owner_delete($id = null,$force='') {
        if (!check_permission(DELETE_JOURNALS)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
            return;
        }
        $this->set('force_delete',$force);
        Journal::$auto_accounts['client']['callback_after_add']='adjust_and_pay';
        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }

        if (!$id && empty ($_POST)) {
            if(IS_REST){
                $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Journal', true))]);
            }else{
                $this->flashMessage(sprintf (__('Invalid id for %s', true), __('Journal',true)));
                $this->redirect(array('action'=>'index'));
            }
        }
        $owner = getAuthOwner();
        $staff = $owner['staff_id'];
        $module_name= __('Journal', true);
        if(is_array($id) && count($id) > 1){
            $module_name= __('journals', true);
        }
        $journals = $this->Journal->find('all',array('conditions'=>array('Journal.id'=>$id)));
        foreach($journals as $k => $journal)
        {

            if($owner['staff_id'] != 0)
            {
                if (!check_permission(MANAGE_ALL_JOURNALS) && (!(check_permission(MANAGE_OWN_JOURNALS) && $staff == $journal['Journal']['staff_id'])) && (($journal['Journal']['draft'] && !check_permission(MANAGE_DRAFT_JOURNALS)) || !$journal['Journal']['draft'])) {
                    if(IS_REST) $this->cakeError("error404", ["message"=> sprintf(__('%s Journal entries failed to be deleted as the user does not have the permission to delete the journal' , true), "(#{$journal['Journal']['number']}) " )]); 
                    $this->flashMessage(__('You are not allowed to view this page', TRUE));
                    $this->redirect('/');
                }
            }
            if(!isset($_POST['force_delete']) && !$force) {
                $this->validate_open_day($journal['Journal']['date'], ['action' => 'index']);
            }
        }
//		dd($journals );
        if (empty($journals)){
            if(IS_REST){
                $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), __($module_name,true))]);
            }else{
                $this->flashMessage(sprintf(__('%s not found', true), __($module_name,true)));
                $this->redirect($this->referer(array('action' => 'index'), true));
            }
        }

        else if($journals[0]['Journal']['is_automatic'] && ($this->Journal->get_related($journal))  &&($_POST['force_delete']=='' && $force==''))
        {
            if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('%s Journal entries failed to be deleted because their type is auto transactions' , true), "(#{$journal['Journal']['number']})" )]); 
            $this->flashMessage(sprintf(__('%s Is Automatic can\'t be deleted' , true), __($module_name,true)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        if(IS_REST){
            $_POST['submit_btn'] = 'yes';
            $_POST['ids'] = [$id];
        }
        if ($_POST['ids']) {
            $recurringProfiles = RecurringProfilesRepository::getRecurringProfilesByEntityIDs($_POST['ids']);
            if ($recurringProfiles && count($recurringProfiles)) {
                $recurringProfile = $recurringProfiles[0];
                $this->flashMessage(sprintf(__('You cannot delete the main journal of the recurring profile %s' , true), '<a href="/v2/owner/recurring-profile/' . $recurringProfile->id . '">#' . $recurringProfile->id . '</a>'));
                $this->redirect(array('action'=>'index'));
            }
        }
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $entitiesById = [];
            foreach($_POST['ids'] as $id) {
                $entitiesById[$id] = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL, $id, 1);  
            }
            if ($_POST['submit_btn'] == 'yes' && $this->Journal->delete_journal_multiple(array('Journal.id'=>$_POST['ids']))) {
                $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::JOURNAL);
                $activityLogRequestCreator = new EntityActivityLogRequestsCreator();
                foreach($entitiesById as $oldData) {
                    $requests = $activityLogRequestCreator->create($st, [], $oldData->toArray());
                    $activityLogService =  new \App\Services\ActivityLogService();
                    foreach ($requests as $requestObj) {
                        $activityLogService->addActivity($requestObj);
                    }
                }

                izam_resolve(AttachmentsService::class)->removeAttachmentsByEntityDetails('journal',$id);
                $additionalFieldsFormHandler->deleteRelatedCustomFieldRecords($id);

                if(IS_REST){
                    $this->set("message", sprintf(__('%s Journal entries deleted successfully', true), "(#{$journal['Journal']['number']})"));
                    $this->render("success");
                    return;
                } else {
                    $this->flashMessage(sprintf (__('%s deleted', true), __($module_name,true)), 'Sucmessage');
                    $this->redirect(array('action'=>'index'));
                }
            }
            else{
                if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect(array('action'=>'index'));
            }
        }
        $this->set('title_for_layout',  __('Delete Journal',true) );
        $this->set('journals',$journals);
    }

    /**
     * @param $siteId
     * @param $entityType
     * WIP
     */
    public function correct_create_auto_journals($siteId = 0, $onlyEntityType = null)
    {
        set_time_limit(0);
        ini_set("memory_limit", "10G");
        $this->loadModel('Site');
        $conditions = [];
        if($siteId) {
            $conditions['Site.id'] = $siteId;
        }
        $sites = $this->Site->find('all',['conditions' => $conditions]);
        App::import('Vendor', 'oilogger');
        foreach($sites as $site)
        {
            $logfile = 'journals_corecction_'.$site['Site']['id'];
            Oilogger::initSession('recurring' . $logfile . '.txt');
            Oilogger::log("correcting site {$site['Site']['id']} journals");
            $GLOBALS['site'] = $site['Site'];
            $config = json_decode($site['Site']['db_config'], true);
            ConnectionManager::getDataSource('default')->swtich_db($config);
            $this->Journal->getDataSource()->swtich_db($config);
            foreach(Journal::$auto_journals as $entityType => $entityData) {

                if($onlyEntityType !== null && $entityType !== $onlyEntityType) {
                    continue;
                }
                $Model = GetObjectOrLoadModel($entityData['model']);
                $entityJournalCorrector = \App\Domain\Correctors\JournalCorrectors\JournalCorrectorFactory::create($entityType, $this->Journal, $Model);
                $entitiesWithNoJournals = $entityJournalCorrector->getRecordsToCorrect();
                $missingJournals = count($entitiesWithNoJournals);
                if($entitiesWithNoJournals) {
                    Oilogger::log("entity $entityType number of records with no journals $missingJournals before correction");
                    debug("entity $entityType number of records with no journals $missingJournals before correction");
                    foreach($entitiesWithNoJournals as $entityWithNoJournal) {
                        $result = $entityJournalCorrector->correct($entityWithNoJournal);
                    }
                    $entitiesWithNoJournals = $entityJournalCorrector->getRecordsToCorrect();
                    $missingJournals = count($entitiesWithNoJournals);
                    Oilogger::log("entity $entityType number of records with no journals $missingJournals after correction");
                    debug("entity $entityType number of records with no journals $missingJournals before correction");
                }
            }
            Oilogger::log("finished correcting site {$site['Site']['id']} journals");
        }

        die('finished');
    }

    public static function calculateAccounts($accounts,$fy_id=false) {
        $JournalAccount = GetObjectOrLoadModel('JournalAccount');
        $Journal = GetObjectOrLoadModel('Journal');
        foreach ($accounts as &$account){
            $recaculateAccountTotal = $Journal->recaculate_account_total($account['JournalAccount']['id'], false, ['format_numbers' => false],$fy_id);
            $account['JournalAccount'] =is_array($account['JournalAccount']) ? array_merge($account['JournalAccount'], is_array($recaculateAccountTotal)?$recaculateAccountTotal:[]):null;
            $account['JournalAccount']['can_be_hidden'] = !$account['JournalAccount']['is_hidden'] && $JournalAccount->canBeHidden($account['JournalAccount']['id'], $fy_id);
            $account['JournalAccount']['deletable'] = $JournalAccount->deletAble($account['JournalAccount']['id'], true, false);
        }
        unset($account);
        return $accounts;
    }

    private function calculateCats($cats,$fy_id=false, $draftIds = null, $ignoreRecalculateAccounts = false) {
        foreach ($cats as &$cat){
            $cat = $this->calculateCat($cat, $draftIds, $ignoreRecalculateAccounts);
        }

        return $cats;
    }

    private function calculateCat($cat, $draftIds = null, $ignoreRecalculateAccounts = false) {
        $JournalCat = GetObjectOrLoadModel('JournalCat');
        $JournalAccount = GetObjectOrLoadModel('JournalAccount');
        $cat['JournalCat'] = array_merge($cat['JournalCat'], ($ignoreRecalculateAccounts ? [] : $this->Journal
            ->recaculate_cat_total($cat['JournalCat']['id'] ?? null, false, ['format_numbers' => false],null, $draftIds)));
        $cat['JournalCat']['deletable'] = $JournalCat->deletAble($cat['JournalCat']['id'] ?? null, false);
        $cat['JournalCat']['child_count'] = $JournalAccount->getJournalAccounts('count', ['conditions' => ['JournalAccount.journal_cat_id' => $cat['JournalCat']['id'] ?? null]]);
        $cat['JournalCat']['child_count'] += $JournalCat->getJournalCats('count', ['conditions' => ['JournalCat.journal_cat_id' => $cat['JournalCat']['id']]]);
        $cat['JournalCat']['can_be_hidden'] = !$cat['JournalCat']['is_hidden'] && $JournalCat->canBeHidden($cat['JournalCat']['id']) && !in_array($cat['JournalCat']['entity_type'] , ['assets', 'incomes', 'expenses', 'liabilities', 'owner_equity']);
        return $cat;
    }

    function owner_getChildAccounts($cat_id,$fy_id=false) {
        $JournalAccount = GetObjectOrLoadModel('JournalAccount');
        $JournalAccount->recursive = -1;
        $JournalCat = GetObjectOrLoadModel('JournalCat');
        $JournalCat->recursive = -1;

        /**
         * if branch is not set in session then set it to current branch to fix issue with branch transactions
         */
        if(empty(getCakeSession(BRANCH_TRANSACTIONS_KEY))) {
            if(ifPluginActive(BranchesPlugin)){
                $this->Session->write(BRANCH_TRANSACTIONS_KEY, getCurrentBranchID());
            }
        }

        $page = isset($_GET['page']) ? (int) $_GET['page'] : 1;
        $limit = \App\Utils\ChartOfAccountsUtil::$accountsLimit;
        $offset = ($page - 1) * $limit;
        $accountsCount = $JournalAccount->getJournalAccounts('count', ['conditions' => ['JournalAccount.journal_cat_id' => $cat_id]]);
        $pagesCount = ceil($accountsCount / $limit);
        $pagination = ['pagesCount' => $pagesCount, 'currentPage' => $page];
        set_time_limit(60);
        ini_set('memory_limit', '512m');

        $accounts = $JournalAccount->getJournalAccounts('all', [
            'offset' => $offset,
            'order' => ['JournalAccount.code ASC'],
            'conditions' => ['JournalAccount.journal_cat_id' => $cat_id],
            'limit' => $limit,
        ]);
        if($page === 1) {
            $cats = $JournalCat->getJournalCats('all', ['order' => ['JournalCat.code ASC'],'conditions' => ['JournalCat.journal_cat_id' => $cat_id]]);
        }
        $parentAccount = $JournalCat->getJournalCats('first', ['conditions' => ['JournalCat.id' => "$cat_id"]]);
        $parentAccount['JournalCat']['can_be_hidden'] = $JournalCat->canBeHidden($cat_id);
        $parentAccount['JournalCat']['child_count'] = $accountsCount;
        /*to handle loading the page on other account than -1*/
        $catsMappedById = [];
        $catIds = [];
        $accounts = array_map(function($account) {
            $account['JournalAccount']['name'] = __at($account['JournalAccount']['name'], true);
            return $account;
        }, self::calculateAccounts($accounts,$fy_id));

        $this->loadModel('Journal');

        $draftJournalIds = $this->Journal
            ->find('list', [
                'recursive' => -1,
                'conditions'=> ['Journal.draft' => 1],
                'applyBranchFind' => false
            ]);

        $ids = array_keys($draftJournalIds);

        $JournalTransaction = GetObjectOrLoadModel('JournalTransaction');
        $JournalTransactionCount =  $JournalTransaction->find('count', ['conditions' => [], 'recursive' => -1]);
        $ignoreRecalculateAccounts = $JournalTransactionCount >= 500000;
        $cats = $this->calculateCats($cats,$fy_id, $ids, $ignoreRecalculateAccounts);

        if($cat_id == -1) {
            $parentAccount['JournalCat'] = ['child_count' => 0, 'total_credit' => 0, 'total_debit' => 0, 'net' => 0, 'deletable' => false];
        } else {
            $parentAccount['JournalCat'] = array_merge($parentAccount['JournalCat'], ($ignoreRecalculateAccounts ? [] : $this->Journal->recaculate_cat_total($cat_id, false, ['format_numbers' => false],$fy_id, $ids)));
            $parentAccount = $this->calculateCat($parentAccount, $ids, $ignoreRecalculateAccounts);
            $parentAccount['JournalCat']['name'] = __at($parentAccount['JournalCat']['name'], true);
        }
        foreach ($cats as $k => $cat) {
            $cat['JournalCat']['name'] = __at($cat['JournalCat']['name'], true);
            $catsMappedById[$cat['JournalCat']['id']] = $cat;
            $catIds[$cat['JournalCat']['id']] = $cat['JournalCat']['id'];

        }
        if($catsMappedById) {
            $counts = $this->Journal->query("SELECT journal_cat_id, count(*) as Count from journal_accounts where journal_cat_id in (".implode(',', $catIds).") group by journal_cat_id ");
            foreach ($counts as $count) {
                if($count['0']['Count'] > 0){
                    $catsMappedById[$count['journal_accounts']['journal_cat_id']]['JournalCat']['has_children'] = true;
                }
            }
            $counts = $this->Journal->query("SELECT journal_cat_id, count(*) as Count from journal_cats where journal_cat_id in (".implode(',', $catIds).") group by journal_cat_id ");
            foreach ($counts as $count) {
                if($count['0']['Count'] > 0){
                    $catsMappedById[$count['journal_cats']['journal_cat_id']]['JournalCat']['has_children'] = true;
                }
            }
        }
        $response = ['status' => 200, 'cats' => array_values($catsMappedById), 'accounts' => $accounts, 'parent_account' => $parentAccount, 'pagination' => $pagination, 'ignoreCalculateAccounts' => $ignoreRecalculateAccounts];
        return die(json_encode($response));
    }

    function owner_getParents($fy_id=false) {
        $limit = \App\Utils\ChartOfAccountsUtil::$accountsLimit;
        $response = ['status' => 200, 'message' => 'success'];
        $JournalAccount = GetObjectOrLoadModel('JournalAccount');
        $JournalAccount->recursive = -1;
        $JournalCat = GetObjectOrLoadModel('JournalCat');
        $JournalCat->recursive = -1;
        $parentIds = $_GET['account_ids'];
        $accounts = [];
        $cats = [];
        foreach ($parentIds as $parentId) {
            $temp = $JournalAccount->getJournalAccounts('all', [
                'order' => ['JournalAccount.code'],
                'conditions' => ['JournalAccount.journal_cat_id' => $parentId],
                'limit' => $limit,
            ]);
            $temp = $this->calculateAccounts($temp,$fy_id);
      
            $accounts[$parentId] = array_values(array_map(function ($el) {
                $el['JournalAccount']['name'] = __at($el['JournalAccount']['name'], true);
                return $el['JournalAccount'];
            },$temp));
            $temp = $JournalCat->getJournalCats('all', ['conditions' => ['JournalCat.journal_cat_id' => $parentId]]);
            $temp = $this->calculateCats($temp,$fy_id);
            $cats[$parentId] = array_values(array_map(function ($el) {
                $el['JournalCat']['name'] = __at($el['JournalCat']['name'], true);
                $el['JournalCat']['has_children'] = $el['JournalCat']['child_count'] > 0;
                return $el['JournalCat'];
            },$temp));
        }
        $parents = [];
        foreach ($parentIds as $parentId) {
            $cat = $JournalCat->getJournalCats('all', ['conditions' => ['JournalCat.id' => $parentId]]);
            $cat = $this->calculateCats($cat,$fy_id);
            $cat = array_values(array_map(function ($el) {
                $el['JournalCat']['name'] = __at($el['JournalCat']['name'], true);
                $el['JournalCat']['has_children'] = true;
                return $el['JournalCat'];
            },$cat));
            $parents = array_merge($parents, $cat);
        }
        $response = ['status' => 200, 'cats' => $cats, 'accounts' => $accounts, 'parents' => $parents];
        return die(json_encode($response));
    }

    private function journalPlaceholders($invoice_id)
    {
        $customJournalDescriptionSettings = settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::InvoicesPlugin, 'custom_journal_description');
        if (isset($customJournalDescriptionSettings)) {
            $this->loadModel('Invoice');
            $invoice = $this->Invoice->find('first', ['conditions' => ['Invoice.id' => $invoice_id]]);
            $this->set('invoice', $invoice);
            $customJournalDescriptionSettings = array_filter(explode(' ', $customJournalDescriptionSettings));
            $this->set('customJournalDescriptionSettings', $customJournalDescriptionSettings);
        }
    }

    function owner_update_draft($id, $draft_status = 1) {
        App::import('Vendor', 'AutoNumber');
        $owner = getAuthOwner();
        $journal = $this->Journal->find('first',array('conditions'=> array('Journal.id' => $id)));
        if (!$journal) {
            $this->flashMessage(__('Journal not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(MANAGE_DRAFT_JOURNALS) && $journal['Journal']['staff_id'] != $staff) && !check_permission(MANAGE_ALL_JOURNALS)) {
                $this->flashMessage(__("You are not allowed to edit this journal", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }
        }
        $this->validate_open_day($journal['Journal']['date']);

        $this->data = $journal;
        $entityId = $this->data['Journal']['id'];
        $oldData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL, $entityId, 1);
        $this->data['Journal']['draft'] = $draft_status;
        $this->Journal->save_journal($this->data);
        $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL, $entityId, 1)->toArray();
        $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::JOURNAL);
        $oldData = $oldData->toArray();
        if(!$oldData['draft'] && $newData['draft']) {
            $this->Journal->saveField('number','draft-'.$entityId);
        } else {
            AutoNumber::set_validate(\AutoNumber::TYPE_JOURNAL);
            $this->Journal->saveField('number', AutoNumber::get_auto_serial(AutoNumber::TYPE_JOURNAL));
            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_JOURNAL);
        }
        $activityLogRequestCreator = new EntityActivityLogRequestsCreator();
        $requests = $activityLogRequestCreator->create($st, $newData, $oldData, []);

        $activityLogService =  new \App\Services\ActivityLogService();

        foreach ($requests as $requestObj) {
            $activityLogService->addActivity($requestObj);
        }

        $redirect = array('action' => 'owner_view', $id);
        $this->flashMessage(sprintf (__('The %s  has been saved', true), __('Journal',true)), 'Sucmessage');
        $this->redirect($redirect);

    }

    function owner_json_find_costCenter()
	{   
        $this->loadModel('CostCenter');
		if (!empty($_GET['q'])) {
			$value = mysql_escape_string($_GET['q']);
			$conditions = [];
            $costCenters = $this->CostCenter->find('list' , ['conditions' => [ "or" => ['CostCenter.name LIKE' => "%$value%" ,'CostCenter.code like ' =>"%$value%"] ]]);
	 
            $result = [];  
			foreach ($costCenters as $costId => $costCenter) {
				$result[] = [
					'name' => $costCenter,
					'id' => $costId,
					'details' => '',
				];
			}
			if (!empty($result))
				array_unshift($result, [
					'name' => __('Any cost center', true),
					'id' => '',
					'details' => '',
				]);
			echo json_encode($result);
			die();
		}
	}

    private function getRedirectionUrl($entityType, $entityId)
    {
        $redirect = '';
        if ($entityType == 'work_order') {
            $this->loadModel('WorkOrder');
            $work_order = $this->WorkOrder->findById($entityId);
            $redirect = Router::url('/owner/work_orders/view/' . $entityId . '?#WorkOrderJournals');
                if (!is_null($work_order['WorkOrder']['workflow_type_id'])) {
                    $redirect = Router::url('/owner/work_orders/workflow_view/' . $entityId . '?#WorkOrderJournals');
                }
        }
        return $redirect;
    }

	function owner_find_auto_account_json(){
        Configure::write('debug', 0);
        $this->autoRender = false;
        $this->RequestHandler->respondAs('json');
        $this->loadModel('CostCenter');
		if (!empty($_GET['entity_type']) && !empty($_GET['entity_id'])) {
			$accountEntity = mysql_escape_string($_GET['entity_type']);
			$accountEntityId = mysql_escape_string($_GET['entity_id']);
            $this->loadModel('JournalAccountRoute');
            $account = $this->Journal->get_auto_account(['entity_type' => $accountEntity, 'entity_id'=> $accountEntityId]);
            if(empty($this->JournalAccountRoute->getAllJournalAccountRoute($account['JournalAccount']['id']))){
                echo json_encode(['id' => $account['JournalAccount']['id'], 'code' => $account['JournalAccount']['code'], 'name' => $account['JournalAccount']['name']]);
            }else{

            }
			die();
		}
	}

    function owner_multi_pdf() {
        $ids = $_POST['ids'];
        $conditions = [];
        if($_POST['index_action_select_type'] === 'all') {
            $conditions = $this->getCachedPaginationConditions('Journal');
            if(isset($conditions['Journal.id'])){
                unset($conditions['Journal.id']);
            }
        } else {
            $conditions['Journal.id'] = $ids;
            if(is_countable($ids) && count($ids) > 200){
                $this->flashMessage(sprintf(__("Can't print more than %s %s", true), 200, __('Journal', true)));
                $this->redirect('index');
            }
        }
        $this->Journal->recursive = 2;
        $selected_journals = $this->Journal->find('all', ['conditions' => $conditions]);
        
        if(is_countable($selected_journals) && count($selected_journals) > 200){
            $this->flashMessage(sprintf(__("Can't print more than %s %s", true), 200, __('Journal', true)));
            $this->redirect('index');
        }
        $this->loadModel('Country');
        foreach ($selected_journals as $k => $selected_journal) {
            $selected_journals[$k] = $selected_journal;
            $selected_journals[$k]['clientCountry'] = $this->Country->field('country', array('Country.code' => $selected_journal['Journal']['country_code']));
        }
        $this->set('selected_journals', $selected_journals);
        $ownerCountry = $this->Country->get_country_code(getCurrentSite('country_code'));
        $this->set('ownerCountry', $ownerCountry);
        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
    }

    function owner_multi_delete()
    {
        $ids = $_POST['ids'];
        $conditions = [];
        if($_POST['index_action_select_type'] === 'all') {
            $conditions = $this->getCachedPaginationConditions($this->modelClass);
            $filterData = $this->{$this->modelClass}->find('all', ['conditions' => $conditions]);
            $ids = array_column(array_column($filterData, $this->modelClass), 'id');
        }
        $params = json_decode($_POST['bulk_data'], true);        
        $this->set('ids', $ids);
        $this->set('url', $params['target_url']);
        $this->set('actionType', $params['action_type']);
        $this->set('entityName', $params['entity_name']);
        $this->set('back_url', $params['back_url']);
        $this->set('action', $params['_method']);
        $this->set('title_for_layout', $params['title_for_layout']);
        $this->set('_PageBreadCrumbs', json_decode($params['breadcrumbs'], true));
		$this->setDefaultViewData();
		$this->view = 'izam';
		$this->render('bulk/bulk');       
    }

    private function __setting()
    {
        $displayCostCenters = settings::getValue(AccountingPlugin, 'display_cost_centers_in_journals');
        $enableTags = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_TAGS_IN_JOURNAL);
        $this->set('display_cost_centers_in_journals', $displayCostCenters);
        $this->set('enable_tags', $enableTags);
    }

    private function assignTagsToJournalTransaction($JournalTransactions = [])
    {
        $ItemsTag = GetObjectOrLoadModel('ItemsTag');
        foreach ($JournalTransactions as $key => $transaction) {
            $tags = $ItemsTag->get_items_tags($transaction['id'], $ItemsTag::TAG_ITEM_TYPE_DEBIT_JOURNAL_TRANSACTION);
            $this->data['ItemsTag'][$key]['tags'] = $tags[$transaction['id']];
        }
    }

    function filter_tags(){
        $tag_name = $this->params['url']['tag'] ;
        $tag_ids = $this->params['url']['tags'];
        $tag_type = $this->params['url']['tag_type'];
        $filterType = $this->params['url']['anyAllRadio'];

        if(($tag_name || $tag_ids) && $tag_type)
        {
            $this->loadModel('Tag');
            if(!$tag_ids){
                $tag = $this->Tag->get_tag_id($tag_name);
                $tag_ids = $tag['Tag']['id'] ;
            }
            $this->loadModel('ItemsTag');
            $items_ids = $this->ItemsTag->find('list',array('conditions' => array('ItemsTag.tag_id' => $tag_ids  , 'ItemsTag.item_type' => $tag_type), 'fields' => array('ItemsTag.item_id')));

            $countValues = array_count_values($items_ids);
            $tagsIdCount = count($tag_ids);

            if($filterType == 'any'){
                $result = array_keys($countValues);

            }else{
                $result = array_keys(array_filter($countValues, function($count) use($tagsIdCount) {
                    return $count === $tagsIdCount;
                }));
            }
            return [ItemsTag::tag_type_to_model_name($tag_type).'.id' =>$result];
        }
    }

    private function createAdditionalFieldsFormHandlerInstance()
    {
        return new \App\Services\LocalEntityForm\AdditionalFormsHandler(EntityKeyTypesUtil::JOURNAL);
    }


    public static function logTransactionData($data, $transaction, $action = 'Edit')
    {
        if (getCurrentSite('id') != 3975799) {
            return;
        }

        $directory = dirname(dirname(__FILE__)) . DS . 'webroot' . DS . 'files' . DS . 'manual_journals';

        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        $fileName = date('Y-m-d') . '.txt';
        $filePath = $directory . DS . $fileName;

        $log = '-----Time (Action : ' . $action . ') ' . date('H:i a') . ' ------' . "\r\n";
        $log .= print_r($data, true) . "\r\n";
        $log .= print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10), true);
        $log .= print_r($transaction, true);
        $log .= '-----End ------' . "\r\n";

        file_put_contents($filePath, $log, FILE_APPEND);
    }

    private function buildEntityListingParams(): string
    {
        $params = $this->params['url'];
        unset($params['url'], $params['ext']);
        $queryParams = [];
        foreach ($params as $param => $value) {
            $queryParams['filter'][$param] = $value;
        }
        if (!empty($params['box'])) {
            $queryParams['iframe'] = 1;
            $queryParams['hide_filters'] = 1;
            $queryParams['hide_page_header'] = 1;
            $queryParams['hide_actions'] = 1;
        }
        return http_build_query($queryParams);
    }

    function prepareJournalCostCenterSummary($cost_transactions)
    {

        $transactions_centers = [];
        foreach ($cost_transactions as $transaction) {
            if (!isset($transaction['CostCenterTransaction']['journal_transaction_id'])) {
                break;
            }
            if (!isset($transactions_centers[$transaction['CostCenterTransaction']['journal_transaction_id']][$transaction['CostCenter']['id']])) {
                $transactions_centers[$transaction['CostCenterTransaction']['journal_transaction_id']][$transaction['CostCenter']['id']] = $transaction['CostCenter'];
            }
            $transactions_centers[$transaction['CostCenterTransaction']['journal_transaction_id']][$transaction['CostCenter']['id']]['total_percentage'] += $transaction['CostCenterTransaction']['percentage'];
        }
        if (!empty($transactions_centers)) {
            $show_cost_centers_summary = true;
            foreach ($transactions_centers as $key => $transaction_centers) {
                if (count($transaction_centers) > 1 || reset($transaction_centers)['total_percentage'] < 100) {
                    $show_cost_centers_summary = false;
                    break;
                } else {
                    $transactions_centers[$key] = reset($transactions_centers[$key]);
                }
            }
        }

        $this->set(compact('transactions_centers', 'show_cost_centers_summary'));
    }

    
    function   prepareJournalTransactionsForTax($transactions, &$journal)
    {
        $i = 0;
        $isAuto = $journal['Journal']['is_automatic'];
        $hasTax = false;
        $hasTags = false;
        $taxTransactions = [];
        foreach ($transactions as $k => $transaction) {
            if ($transaction['JournalTransaction']['tax_id']) {
                $hasTax = true;
            }
            if (!empty($transaction['Tags'])) {
                $hasTags = true;
            }
            $journal['JournalTransaction'][$i] = $transaction['JournalTransaction'];
            $journal['JournalTransaction'][$i]['Tags'] = $transaction['Tags'];
            $transaction['JournalAccount']['name'] = __at($transaction['JournalAccount']['name'], true);
            $journal['JournalTransaction'][$i]['JournalAccount'] = $transaction['JournalAccount'];
            if (!$isAuto && strpos($transaction['JournalTransaction']['subkey'], 'tax')) {
                $taxTransactions[] = $transaction;
                unset($journal['JournalTransaction'][$i]);
            }
            $i++;
        }

        $this->set('has_tax', $hasTax);
        $this->set('has_tags', $hasTags);
        $this->loadModel('Tax');
        $this->set('taxes', $this->Tax->getTaxList());
        $this->set('tax_transactions', $taxTransactions);
        $this->setTaxesData($journal);
    }

}
