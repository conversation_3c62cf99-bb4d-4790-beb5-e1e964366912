<?php

use App\Expense\JournalTransactionCostCenterUpdater;
use App\Transformers\ServiceModelDataTransformer;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Aws\Aws;
use Izam\Daftra\Common\Queue\EventListenerMapper;
use Izam\Daftra\Common\Queue\EventTypeUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Expense\Services\ExpenseService;
	use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;
use Izam\Logging\Service\RollbarLogService;

App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
App::import('Vendor', 'settings');
/**
 * @property Expense $Expense
 * @property TempFile $TempFile
 *
 */
class ExpensesController extends AppController {

    var $uses = array('Expense', 'SavedReport');
    var $name = 'Expenses';
    var $helpers = array('Html', 'Form', 'Paginator');
    var $is_income = 0;
    var $is_mileage = 0;
    var $controller = 'expenses';
    var $multi_title = 'Expenses';
    var $single_title = 'Expense';
    var $permissions = array(
        'View_his_own' => View_his_own_expenses,
        'Add_New' => Add_New_Expenses,
        'Edit_delete_his_own' => Edit_delete_his_own_expenses,
        'Edit_Delete_all' => Edit_Delete_all_expenses,
        'View_All' => View_All_expenses,
        'Draft' => PermissionUtil::ADD_EDIT_DELETE_DRAFT_EXPENSE
    );
    var $components = ['SupplierValidation'];

    
    function beforeFilter() {


        parent::beforeFilter();
        $this->titleAlias = __('Expenses', true);



        if (!empty($this->params['is_income'])) {
            $this->is_income = true;
            $this->titleAlias = __('Incomes', true);
            $this->controller = 'incomes';
            $this->multi_title = 'Incomes';
            $this->single_title = 'Income';
            $this->permissions = array(
                'View_his_own' => View_his_own_incomes,
                'Add_New' => Add_New_Incomes,
                'Edit_delete_his_own' => Edit_delete_his_own_incomes,
                'Edit_Delete_all' => Edit_Delete_all_incomes,
                'View_All' => View_All_incomes,
                'Draft' => PermissionUtil::ADD_EDIT_DELETE_DRAFT_INCOME
            );
        }else{

            if(isset($this->params['url']['is_mileage']) && $this->params['url']['is_mileage'] == 1){
                    $this->is_mileage = true;
                    $this->single_title = 'Mileage';
                    $this->titleAlias = "Mileage";
            }
        }

        $this->set('permissions', $this->permissions);
        $this->set('is_income', $this->is_income);
        $this->set('is_mileage', $this->is_mileage);
        $this->set('controller', $this->controller);
        $this->set('multi_title', $this->multi_title);
        $this->set('single_title', $this->single_title);
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }

        if(($this->is_mileage && !ifPluginActive(MILEAGE_PLUGIN))){
            $this->flashMessage(__("Plugin not active", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
    }

    function owner_change_category() {
        $this->loadModel('ExpenseCategory');
        $ids = isset($_POST['ids']) ? $_POST['ids'] : explode(',', $this->data['Expense']['ids']);
        if (empty($ids[0])) {
            unset($ids[0]);
        }
        if (empty($ids)) {

            $this->flashMessage(sprintf(__('Invalid %s', true), __($this->controller, true)));
            if (!empty($this->params['url']['box'])) {
                $this->set('save', true);
            } else {
                $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
            }
        }
        $this->set('ids', $ids);

        $this->set('categories', $categories = $this->Expense->getCategoriesList($this->is_income, false));

        if (!empty($this->data) and !empty($this->data['Expense']['action'])) {

            $ids_array = explode(',', $this->data['Expense']['ids']);

            foreach ($ids_array as $id) {
                $expense_category_id=$this->ExpenseCategory->findOrCreate($this->data['Expense']['category'],$this->is_income==1?2:1);
                $this->Expense->id = $id;
                $this->Expense->saveField('category', $this->data['Expense']['category']);
                $this->Expense->saveField('expense_category_id', $expense_category_id);
                $row = $this->Expense->read(null, $id);
                $actionType = $this->is_income ? ACTION_UPDATE_INCOME : ($this->is_mileage? ACTION_UPDATE_MILEAGE:ACTION_UPDATE_EXPENSE);
                $this->add_actionline($actionType, array('primary_id' => $id, 'param1' => $row['Expense']['amount'], 'param2' => $row['Expense']['category'], 'param3' => $row['Expense']['vendor'],'param4' => $row['Expense']['expense_number'], 'param6' => $row['Expense']['date']));
            }


            //$this->Expense->updateAll(array('Expense.category' => "'" . $this->data['Expense']['category'] . "'"), array('Expense.id' => $ids_array));
            $this->flashMessage($this->multi_title . __(' has been saved', true), 'Sucmessage');

            if (!empty($this->params['url']['box'])) {
                $this->set('box', true);
                $this->set('save', true);
            } else {
                $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
            }
        }
    }

    function owner_change_vendor() {
        // bussiness said we don't need it anymore
        die();
        $ids = isset($_POST['ids']) ? $_POST['ids'] : explode(',', $this->data['Expense']['ids']);
        if (empty($ids[0])) {
            unset($ids[0]);
        }

        if (empty($ids)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __($this->controller, true)));
            if (!empty($this->params['url']['box'])) {
                $this->set('save', true);
            } else {
                $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
            }
        }

        $this->set('ids', $ids);
        $this->set('vendors', $vendors = $this->Expense->getVendorsList($this->is_income, false));
        if (!empty($this->data) and !empty($this->data['Expense']['action'])) {
            $ids_array = explode(',', $this->data['Expense']['ids']);
            foreach ($ids_array as $id) {

                $this->Expense->id = $id;
                $this->Expense->saveField('vendor', $this->data['Expense']['vendor']);
                $row = $this->Expense->read(null, $id);
                $actionType = $this->is_income ? ACTION_UPDATE_INCOME : ($this->is_mileage? ACTION_UPDATE_MILEAGE:ACTION_UPDATE_EXPENSE);
                $this->add_actionline($actionType, array('primary_id' => $id, 'param1' => $row['Expense']['amount'], 'param2' => $row['Expense']['category'], 'param3' => $row['Expense']['vendor'],'param4' => $row['Expense']['expense_number'], 'param6' => $row['Expense']['date']));
            }



            //  $this->Expense->updateAll(array('Expense.vendor' => "'" . $this->data['Expense']['vendor'] . "'"), array('Expense.id' => $ids_array));
            $this->flashMessage($this->multi_title . __(' has been saved', true), 'Sucmessage');
            if (!empty($this->params['url']['box'])) {
                $this->set('box', true);
                $this->set('save', true);
            } else {
                $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
            }
        }
    }

    private $rates_arr = array();

    function _get_total_amount($type, $default_currency, $days_before, $staff_condition = "1" ) {

        $this->rates_arr = array();
        $total = 0;
        $branchCondition = '1';
        if (ifPluginActive(BranchesPlugin)) {
            $branchCondition = 'branch_id = '.getCurrentBranchID();
        }
        $this->set('from_' . $days_before . '_date', format_date((date('Y-m-d', strtotime('-' . ($days_before - 1) . ' Days')))));
        $query = "SELECT 
            `currency_code` , sum(`amount`)
             as total_amount , NOW()
              FROM `expenses` 
              where `date`<= '" . date('Y-m-d 23:59:59') . "' AND  `date` >= '" . date('Y-m-d', strtotime('-' . ($days_before - 1) . ' Days')) . "' and is_income = $type and $staff_condition and $branchCondition group by `currency_code`
              ";
        $results = $this->Expense->query("SELECT 
            `currency_code` , sum(`amount`)
             as total_amount , NOW()
              FROM `expenses` 
              where `date`<= '" . date('Y-m-d 23:59:59') . "' AND  `date` >= '" . date('Y-m-d', strtotime('-' . ($days_before - 1) . ' Days')) . "' and is_income = $type and $staff_condition and $branchCondition group by `currency_code`
              ");

        foreach ($results as $key => $value) {
            // if different currency
            if ($default_currency != $value["expenses"]["currency_code"]) {

                // convert to user currency and push to $rates_arr;
                $rate_val = CurrencyConverter::index($value["expenses"]["currency_code"], $default_currency, date('Y-m-d'));
                $this->rates_arr[] = array("currency" => $value["expenses"]["currency_code"], "rate" => $rate_val);
                $total += ($rate_val * $value[0]["total_amount"]);
            } else {
                $total += $value[0]["total_amount"];
            }
        }

        return $total;
    }

    function _get_total_count($type) {
        $results = $this->Expense->query("SELECT 
            count(*) as results_count
            FROM `expenses` 
            where is_income = $type
        ");

        return $results;
    }
    
    function loadVehiclesImages($expenses) {
        $vehicle_ids = [];
        foreach ($expenses as $expense) {
            if (!empty($expense['Vehicle']['photo'])) {
                $vehicle_ids[] = $expense['Vehicle']['photo'];
            }
        }
    
        if (empty($vehicle_ids)) {
            return $expenses; 
        }
        $this->loadModel('FileModel');
    
        $vehicle_images = $this->FileModel->find("all", ['conditions' => ["id" => $vehicle_ids]]);
    
        $image_map = [];
        foreach ($vehicle_images as $image) {
            $image_map[$image['FileModel']['id']] = $image['FileModel'];
        }
    
        foreach ($expenses as $key => $expense) {
            $photo_id = $expense['Vehicle']['photo'] ?? null;
            if ($photo_id && isset($image_map[$photo_id])) {
                $expenses[$key]['Vehicle']['File'] = $image_map[$photo_id];
            }
        }
        return $expenses;
    }
    

    function owner_index() {
        $start = microtime(true);
      
        if(isset($this->params['url']['unit_id']) && isset($this->params['url']['box'])){
            $this->Expense->applyBranch['onFind'] = false;
        }
        //die();
        $expenses_hide_client=settings::getValue ( 0  ,"expenses_hide_client" );
        $this->set('expenses_hide_client',$expenses_hide_client);
        $site = getAuthOwner();
        // Start expenses and income block

        $this->set("default_currency", $site['currency_code']);
        if ($this->is_income == 0) {
            $this->set("report_url_type", "expenses");
        } else {
            $this->set("report_url_type", "incomes");
        }
        $ccc=$this->_get_total_count($this->is_income);
        $this->set("results_count", $ccc[0][0]["results_count"]);

        $this->set("current_date", format_date(date('Y-m-d')));
        $this->set("is_income", $this->is_income);

        $this->loadModel ( 'Treasury' ) ;
        $this->loadModel ( 'ItemPermission' ) ;
        $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW)  ) ;

        if ($site['staff_id'] != 0) {
            if (!check_permission($this->permissions['View_his_own']) && !check_permission($this->permissions['Add_New']) && !check_permission($this->permissions['Edit_delete_his_own'])) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $time_elapsed_secs = microtime(true) - $start;
        debug('1st measure ' . $time_elapsed_secs);
        $start = microtime(true);
        $conditions = array();
        $staff_condition = "1" ;
        if ($site['staff_id'] != 0) {
            if (!check_permission($this->permissions['View_All']) && !check_permission($this->permissions['Edit_Delete_all'])) {
                $conditions['Expense.staff_id'] = $site['staff_id'];
                $staff_condition = "staff_id = {$site['staff_id']}";
            }
        }
        // Expenses last 7 days
        $total = $this->_get_total_amount($this->is_income, $site['currency_code'], 7 , $staff_condition);
        $this->set("last_7_days_total", $total);
        $this->set("last_7_days_rates", $this->rates_arr);

        // Expenses last 30 days
        $total = $this->_get_total_amount($this->is_income, $site['currency_code'], 30, $staff_condition);
        $this->set("last_30_days_total", $total);
        $this->set("last_30_days_rates", $this->rates_arr);

        // Expenses last 365 days
        $total = $this->_get_total_amount($this->is_income, $site['currency_code'], 365, $staff_condition);
        $this->set("last_365_days_total", $total);
        $this->set("last_365_days_rates", $this->rates_arr);

        // End expenses and income block

        $conditions['Expense.is_income'] = $this->is_income;


        $this->set('vendors', $vendors = $this->Expense->getVendorsList($this->is_income, false));
        $this->set('expense_categories', $categories = $this->Expense->getCategoriesList($this->is_income, true,true));
        $this->set('file_settings', $this->Expense->getFileSettings());

        $this->Expense->filters = $this->Expense->getFilters($this->is_income);
        if (empty($vendors))
            unset($this->Expense->filters['vendor']);
        if (empty($categories))
            unset($this->Expense->filters['category']);

        if (ifPluginActive(StaffPlugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $staffs = $this->Staff->getList());
            if (count($staffs) > 0 && check_permission($this->permissions['View_All'])) {
                $this->Expense->filters['staff_id'] = array('label' => __('Added By', true), 'more-options' => true, 'empty' => __('Any Staff', true), 'option' => array('type' => 'select'));
            }
        }
        $time_elapsed_secs = microtime(true) - $start;
        debug('2nd measure ' . $time_elapsed_secs);
        $start = microtime(true);
        $this->Expense->recursive = 0;
        $conditions = $conditions + $this->_filter_params(false, $this->Expense->filters);
        if(isset($conditions['Expense.journal_account_id']) && !empty($conditions['Expense.journal_account_id']))
        {
            $journalAccountValue = $conditions['Expense.journal_account_id'];
            unset($conditions['Expense.journal_account_id']);
            $conditions += [
                'OR' => [
                    ['Expense.journal_account_id' => $journalAccountValue],
                    'AND' => [
                        ['Expense.accounts_data !=' => ''],
                        'OR' => [
                            ["JSON_SEARCH(Expense.accounts_data, \"all\", $journalAccountValue, NULL, \"$[*].journal_account_id\") IS NOT NULL"],
                            ["JSON_SEARCH(Expense.accounts_data, \"all\", $journalAccountValue, NULL, \"$.*.journal_account_id\") IS NOT NULL"],
                        ],
                    ],
                ],
            ];
        }
        // warning suppress
        if (array_key_exists('id', $_GET) && trim ($_GET['id'])!= "" ) {
            $conditions['Expense.id']= intval($_GET['id'] ) ;
        }


        if (!empty ( $this->params['url']['work_order_id'] ) )
        {
            $conditions['Expense.work_order_id'] = $this->params['url']['work_order_id']  ;
            debug ( $conditions ) ;
        }

        //filter the expenses according to the recurring id if isset
        if( isset( $_GET['recurring_expense_id'] ) && !empty( $_GET['recurring_expense_id'] ))
        {
            $conditions['Expense.recurring_expense_id'] = $this->params['url']['recurring_expense_id']  ;
        }

        if (isset($this->params['url']['unit_id']))
        {
            $conditions['Expense.source_id'] = $this->params['url']['unit_id'];
            $conditions['Expense.source_type'] = Expense::SOURCE_TYPE_UNIT;
            $this->set('unitIfram', true);
        }

        if ( $this->params['url']['status']) {
            $conditions['Expense.status'] = $this->params['url']['status'];
        }
        unset($conditions['Expense.status select']);
        if(!ifPluginActive(MILEAGE_PLUGIN) && ifPluginInstalled(MILEAGE_PLUGIN)){
            $conditions['Expense.is_mileage'] = 0;
        }

        $this->Expense->bindAttachmentRelation('expense');
        if(isset($this->params['url']['unit_id']) && isset($this->params['url']['box'])){
            $expenses = $this->Expense->find('all',['conditions' => $conditions]);
        }else{

            $this->paginate['Expense'] = array('conditions' => $conditions , 'recursive' => 1 );
            $this->setConditionsKey('expenses', $conditions);
            $expenses = $this->paginate('Expense', $conditions);
        }
        if(ifPluginActive(MILEAGE_PLUGIN)){
            $expenses = $this->loadVehiclesImages($expenses);
        }
        $time_elapsed_secs = microtime(true) - $start;
        debug('3rd measure ' . $time_elapsed_secs);

        $this->set('expenses', $expenses);
        $this->set('clients', $this->Expense->Client->find('list', array('fields' => 'id,business_name')));
        $this->set('content', $this->get_snippet($this->multi_title));
        $this->set('staff_id', $site['staff_id']);

        //get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        if($this->is_income == 0)
            $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('expense', false);
        else
            $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('income', false);

        if(!empty($printableTemplates)){
			foreach($printableTemplates as $k => $printable_template){
				if($printable_template['PrintableTemplate']['default_template'])
				{
					$this->set('default_template', $printable_template);
				}
			}
            $this->set(compact('printableTemplates'));
		}

        App::import('vendor','ExpenseV',['file'=>'Expenses/autoload.php']);
        $expenseSources = \ExpenseV\Sources\ExpenseSourceAbstract::getSourceTypeOptions();
        $expenseSourcesObjects = [];
        foreach(\ExpenseV\Sources\ExpenseSourceAbstract::$expenseSources as $expenseSource)
        {
            $expenseSourcesObjects[$expenseSource] = \ExpenseV\Sources\ExpenseSourceAbstract::CreateExpenseSource($expenseSource);
        }
        $this->set('expenseSources', $expenseSourcesObjects);

		// nav section
		$refer_url = $this->referer('/', true);
		$refer_url_params = Router::parse($refer_url);
		$this->setup_nav_data($expenses);
		//end nav section
        $this->set('title_for_layout',  h(__($this->multi_title, true)));
		if(IS_REST){
			if($this->is_income){
				foreach ($expenses as $key => $value) {
					$expenses[$key]["Income"] = $expenses[$key]["Expense"];
					unset($expenses[$key]["Expense"]);
				}
			}

            foreach ($expenses as $key => $expense) 
            {
                $expenses[$key]['Expense']['attachments'] = $this->prepareS3Attachments($expense['Attachments']);
            }
            
			$this->set('rest_items', $expenses);
			$this->set('rest_model_name', $this->single_title);
			$this->render("index");
		}
        $this->setup_nav_data($expenses);
    }

    function owner_download_file($id = null) {


        $expense = $this->Expense->find(array('Expense.id' => $id));
        //debug($expense);
        if (!$expense) {
            $this->flashMessage(sprintf(__("%s not found.", true), __($this->single_title, true)));
            $this->redirect($this->referer(array('controller' => $this->controller, 'action' => 'index')));
        }
        if (check_permission($this->permissions['View_All']) || check_permission($this->permissions['Edit_Delete_all']) || (getAuthOwner('staff_id') == $expense['Expense']['staff_id'])) {
            App::import('Vendor', 'Download');
            $file = $this->Expense->getFileAbsolutePath('file', $expense);
//            debug($file);
//            debug(realpath("/var/www/html/daftra/daftra-dev/webroot/files/db4715bd/documents/481a2_d.jpg"));
//            debug(realpath($file));
            if (realpath($file) == $file) {
                Download::download_file($file);
            } else {
                $this->flashMessage(sprintf(__("%s not found.", true), __($this->single_title, true)));
                $this->redirect($this->referer(array('controller' => $this->controller, 'action' => 'index')));
            }
        } else {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
    }

    function __init_form_data() {
       $expenses_hide_client=settings::getValue ( 0  ,"expenses_hide_client" );

        $this->set('expenses_hide_client',$expenses_hide_client);
		$inclusive_tax_mode= settings::getValue ( ExpensesPlugin  ,"expense_inclusive_tax_mode");
		$this->set('inclusive_tax_mode',$inclusive_tax_mode);


        foreach ($this->Expense->RecurringExpense->period_list as $key => $value) {
            $periods[$key] = ucfirst($key);
        }
        


        $this->set('periods', $periods);
        $this->set('currencies', getCurrenciesList([], true));
        $this->set('file_settings', $this->Expense->getFileSettings());
        $this->set('vendors_list', $this->Expense->getVendorsList($this->is_income));
        $this->set('categories_list', $this->Expense->getCategoriesList($this->is_income,true));

        $this->set('clients', $this->Expense->Client->find('list', array( 'fields' => 'id,business_name')));
        if(ifPluginActive(MILEAGE_PLUGIN)){
            $this->loadModel('Vehicle');
            $this->set('vehicles', $this->Vehicle->find('list', array('fields' => 'id,name')));
        }
        // Used To prevent edit attachments in case of user has old attachments . 
        $this->set('attachmentFlag', true);

        if (empty($this->data['Expense']['id'])) {
            $this->set('taxes', $this->Expense->getExpenseTaxList());
            $this->set('jsTaxes', $this->Expense->getJSONList());
        } else {
            $this->set('taxes', $this->Expense->getExpenseTaxList($this->data['Expense']['id']));
            $taxes = json_decode($this->Expense->getJSONList($this->data['Expense']['id']), true);
            $accounts =  json_decode($this->data['Expense']['accounts_data'], true);
            foreach ($accounts as $account) {
                if (isset($account['tax_id'])) {
                    $taxes[$account['tax_id']]['value'] = $account['percent'];
                    $taxes[$account['tax_id']]['included'] = $account['included'];
                }
            }
            $this->Set('jsTaxes', json_encode($taxes));
        }

        if (ifPluginActive(RENTAL_PLUGIN)) {
            $this->loadModel('RentalUnit');

            if(isset($this->params['url']['unit_id']) || $this->data['Expense']['unit_on_fly']){
                $source_id = $this->params['url']['unit_id']?? $this->data['Expense']['unit_id'];
                $this->data['Expense']['source_id'] = $source_id;
                $this->data['Expense']['unit_on_fly'] = true;
            }else{
                $this->data['Expense']['source_id'] = $this->data['Expense']['unit_id'];
                $source_id = $this->data['Expense']['unit_id'];
            }

            $condition = [];
            $temp = [];
            if (isset($source_id)) {
                $condition = ['RentalUnit.id' => $source_id];
                $unitData = $this->RentalUnit->find('all', array('fields' => 'id,name,RentalUnitType.name', 'conditions' => $condition));

               foreach ($unitData as  $value) {
                   $temp[$value['RentalUnit']['id']] =   $value['RentalUnit']['name'] . '(' . $value['RentalUnitType']['name'] . ')';
               }
            }
             $this->set('units', $temp);
        }
    }

    private function _submitCommon($data)
    {
        if(isset($data['Expense']['vendor']) && is_array($data['Expense']['vendor']))
        {
            $data['Expense']['vendor'] = $data['Expense']['vendor'][0];
        }
        if(empty($data['Expense']['tax1_id'])) {
            $data['Expense']['tax1_id'] = $data['Expense']['tax1_name'] = $data['Expense']['tax1_name'] = $data['Expense']['tax1_amount'] = $data['Expense']['tax1_value'] = $data['Expense']['tax1_included']  = null;
        }
        if(empty($data['Expense']['tax2_id'])) {
            $data['Expense']['tax2_id'] = $data['Expense']['tax2_name'] = $data['Expense']['tax2_name'] = $data['Expense']['tax2_amount'] = $data['Expense']['tax2_value'] = $data['Expense']['tax2_included']  = null;
        }

        return $data;
    }

    function add_clone_action_line($clone_id, $type) {
        if($clone_id) { 
            $expense = $this->Expense->find(array('Expense.id' => $clone_id));
            $new_expense = $this->Expense->find(array('Expense.id' =>  $this->Expense->id));
            $this->add_actionline($type, array('primary_id' => $this->Expense->id,'secondary_id' => $clone_id, 'param4' => $expense['Expense']['expense_number'], 'param6' => $new_expense['Expense']['expense_number'], 'param9' => $new_expense['Expense']['id']));
        }
    }

    private function saveExpenseAttachments() {
        // Upload Attachments.
        $attachments = $this->data['Expense']['attachment'];
        if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
        
        $imagesIds = explode(',',$attachments);
        if(!empty($imagesIds))
        {
            izam_resolve(AttachmentsService::class)->save('expense', $this->Expense->id, $imagesIds); 
        }
    }

    function owner_add($id=false) {
        $_REQUEST=modifyArray($_REQUEST);
        $this->data=modifyArray($this->data);
        $this->Expense->bindAttachmentRelation('expense');
        
        $this->loadModel('Country');
        $this->set('countries', $this->Country->getCountryList());
        App::import('Vendor', 'sites_local');
        $this->set('bnf',Localize::get_business_number_fields());

        $this->Expense->bindAttachmentRelation('expense');
        $this->loadModel('CostCenter');
        $owner = getAuthOwner();
        $formats = getDateFormats('std');
        $dateFormat = $formats[$owner['date_format']];
        $this->loadModel('ExpenseCategory');
        $this->loadModel('Currency');
        App::import('Vendor', 'AutoNumber');
        $type = $this->is_income ? 'income' : 'expense';

        $filterByType = $type == "expense" ? 'debit' : 'credit';
        if (!check_permission($this->permissions['Add_New']) && !check_permission(PermissionUtil::View_his_own_expenses) && !check_permission($this->permissions['Draft'])
            || ($this->is_mileage && !ifPluginActive(MILEAGE_PLUGIN))) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage PermissionMessage', 'secondaryMessage');
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
        $expensesIsMandatoryEnabled = settings::getValueEvenEmpty(ExpensesPlugin , 'selecting_expense_account_is_mandatory' , allowCache: false);
        $incomeIsMandatoryEnabled = settings::getValueEvenEmpty(ExpensesPlugin , 'selecting_income_account_is_mandatory' , allowCache: false);
        $this->set('expensesIsMandatoryEnabled',$expensesIsMandatoryEnabled);
        $this->set('incomeIsMandatoryEnabled',$incomeIsMandatoryEnabled);
        $autoNumberType = ($type == "income") ? \AutoNumber::TYPE_INCOME :
                            ( $this->is_mileage ? \AutoNumber::TYPE_MILEAGE :\AutoNumber::TYPE_EXPENSE);

        
        if(ifPluginActive(AccountingPlugin)){
			$this->loadModel('Journal');
			Journal::$auto_accounts['client']['callback_after_add']='adjust_and_pay';
			Journal::$auto_accounts['supplier']['callback_after_add']='adjust_and_pay';
			$this->set('multi_journal_accounts',Settings::getValue(AccountingPlugin, 'enable_multi_journal_accounts'));
        }
        
        if (!empty($this->data)) {
            if ((($expensesIsMandatoryEnabled && !$this->is_income) || ($incomeIsMandatoryEnabled && $this->is_income)) && (!IS_MOBILE_APPLICATION)) {
                if (empty($this->data['JournalTransaction'][0]['journal_account_id'])){
                    $this->Expense->validate['journal_account_id'][] = [
                        'rule' => 'notempty',
                        'message' => __('Required', true)
                    ];
                }
            }
            $this->data['Expense']['note'] = html_entity_decode($this->data['Expense']['note']);
            $is_draft = ($this->params['url']['send']?? null) == "draft" ? 1 : null;
            $this->SupplierValidation->validateSupplierSuspended($this->data['Expense']['supplier_id']);
            if(IS_MOBILE_APPLICATION){
                $this->data['Expense']['external_source'] = 'mobile';
            }else{
                $this->data['Expense']['external_source'] = 'web';
            }
            $this->data = $this->_submitCommon($this->data);
            \AutoNumber::set_validate($autoNumberType );
            setcookie('expense_default_action',$_POST['next_action'],time() + **********);
			//set number if new expense and he didn't change or number is empty
			if(empty($this->data['Expense']['expense_number'])||(empty($this->data['Expense']['id'])&&$this->data['Expense']['expense_number']==$this->data['Expense']['default_expense_number'])){
                $this->data['Expense']['default_expense_number'] = $this->data['Expense']['expense_number'] = \AutoNumber::get_auto_serial($autoNumberType );
				$generated_number = true;
			}
            if (empty($this->data['Expense']['currency_code'])) {
                $this->data['Expense']['currency_code'] = getAuthOwner('currency_code');
            }

            $this->Expense->create();
            if (IS_REST && empty($this->data['Expense']['treasury_id'])){
                unset($this->Expense->data['Expense']['treasury_id']);
            }
            $this->data['Expense']['is_income'] = $this->is_income;
            $this->data['Expense']['is_mileage'] = $this->is_mileage;
            $this->data['Expense']['staff_id'] = getAuthOwner('staff_id');


            $this->data['Expense']['date'] = $this->Expense->formatDate($this->data['Expense']['date']);

            $vendor = $this->data['Expense']['vendor'] ?? null;
            $category = $this->data['Expense']['category'] ?? null;
            if (!empty($vendor)) {
                $this->data['Expense']['vendor'] = is_array($vendor) ? array_map('html_entity_decode', $vendor) : html_entity_decode($vendor);
            }

            if (!empty($category)) {
                $this->data['Expense']['category'] = is_array($category) ? array_map('html_entity_decode', $category) : html_entity_decode($category);
            }

            if (is_array($this->data['Expense']['vendor'])){
                $this->data['Expense']['vendor'] = $this->data['Expense']['vendor'][0];
            }
            if (empty($this->data['Expense']['vendor'])){
                $this->data['Expense']['vendor'] = "";
            }
            if (is_array($this->data['Expense']['category'])){
                $this->data['Expense']['category'] = preg_replace('/\s+/', ' ', $this->data['Expense']['category'][0]);
                $this->data['Expense']['expense_category_id'] = $this->ExpenseCategory->findOrCreate($this->data['Expense']['category'],$this->is_income?2:1);
            } else if (is_numeric($this->data['Expense']['category'])) {
                $this->data['Expense']['expense_category_id'] = $this->data['Expense']['category'];
                $this->data['Expense']['category'] = $this->ExpenseCategory->find('first',['conditions' => ['ExpenseCategory.id' => $this->data['Expense']['expense_category_id']]])['ExpenseCategory']['name'];
            }

            if (empty($this->data['Expense']['category'])){
                $this->data['Expense']['category'] = "";
            }

            if (!empty($is_draft)) {
                $this->data['Expense']['status'] = 'draft';
            } else {
                $this->data['Expense']['status'] = 'issued';
            }

            if(!empty($this->data['Expense']['unit_id'])){
                $this->data['Expense']['source_type'] = Expense::SOURCE_TYPE_UNIT;
                $this->data['Expense']['source_id'] = $this->data['Expense']['unit_id'];
            }
            if($this->is_mileage){
                $this->data['Expense']['amount'] = floatval($this->data['Expense']['rate']) * floatval($this->data['Expense']['distance']);
            }

            if(!empty($this->data['Expense']['filename'])){
                $this->loadModel('TempFile');
                $row=$this->TempFile->readFile($this->data['Expense']['filename']);

                $file_settings = $this->Expense->getFileSettings();
                $from_file = $row['data']['file_name_system_full_path'];
                $to_file = WWW_ROOT.$file_settings['file']['folder'].basename($from_file);
                mkdir(WWW_ROOT.$file_settings['file']['folder'],0777);
                if(!empty($from_file) && copy($from_file, $to_file))
                {
                    $this->data['Expense']['file'] = basename($from_file);
                    $this->TempFile->deleteFile($this->data['Expense']['filename']);
                }
            }

            $clone_id = isset($this->data['Expense']['clone_id']) ? (int) $this->data['Expense']['clone_id'] : false;
            if ($this->data['Expense']['recurring'] == "" or $this->data['Expense']['recurring'] == 0) {
                unset($this->data['RecurringExpense']);
                $validCostCenters = $this->checkCostCentersValidity($this->data);
                if ($validCostCenters  && $this->Expense->save($this->data)) {

                    $expense = $this->data;
                    $expense['Expense']['id'] = $this->Expense->id;
                    $actionType = $this->is_income ? ACTION_CLONE_INCOME : ($this->is_mileage? ACTION_CLONE_MILEAGE: ACTION_CLONE_EXPENSE);
                    $this->add_clone_action_line($clone_id, $actionType);
                    $this->saveExpenseAttachments();
                    if (empty($is_draft))  {
                        izam_resolve(ExpenseService::class)->insert(ServiceModelDataTransformer::transform($expense, 'Expense'));
                        foreach ($this->data['JournalTransaction'] as $entity) {
                            $this->_recalcjournalbalance($entity['journal_account_id']);
                        }
                        $this->_recalcjournalbalance($this->data['Expense']['journal_account_id']);
                        if(!empty($generated_number)) {
                            \AutoNumber::update_auto_serial($autoNumberType);
                            // $this->is_income ? \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INCOME): \AutoNumber::update_auto_serial(\AutoNumber::TYPE_EXPENSE);
                        } elseif($this->data['Expense']['expense_number']!=$this->data['Expense']['default_expense_number'])
                        {
                            \AutoNumber::update_last_from_number($this->data['Expense']['expense_number'],$autoNumberType);
                            // $this->is_income ? \AutoNumber::update_last_from_number($this->data['Expense']['expense_number'],\AutoNumber::TYPE_INCOME) : \AutoNumber::update_last_from_number($this->data['Expense']['expense_number'],\AutoNumber::TYPE_EXPENSE);
                        }
                    } else {
                        $this->Expense->saveField('expense_number', 'Draft-'.$expense['Expense']['id']);
                        $this->data['Expense']['expense_number'] = 'Draft-'.$expense['Expense']['id'];
                    }
                    // ACTION_ADD_EXPENSE primary_id =>expense_id, p1=>amount, p2=> category, p3=> Vendor
                    $actionType = $this->is_income ? ACTION_ADD_INCOME : ($this->is_mileage ? ACTION_ADD_MILEAGE :ACTION_ADD_EXPENSE);
                    $this->add_actionline($actionType, array('primary_id' => $this->Expense->id, 'param1' => $this->data['Expense']['amount'], 'param2' => $this->data['Expense']['category'], 'param3' => $this->data['Expense']['vendor'],'param4' => $this->data['Expense']['expense_number'], 'param6' => $this->data['Expense']['date'] ,'param7' => $this->data['Expense']['attachment'] ));
                    $action = 'view';
                    if (!empty($_POST['next_action']) && $_POST['next_action'] == 'add_new')
                        $action = 'add';
                    if (!empty($_POST['next_action']) && $_POST['next_action'] == 'add_print')
                        $action = 'add_print';


					//cost transaction section
					if($this->data['CostCenterTransaction'])
					{
                        if (empty($is_draft)) {
                            $this->loadModel('CostCenter');
                            $this->data['Expense']['id'] = $this->Expense->id;
                            $this->data['CostCenterTransaction'] = array_map(function($costCenterTransaction) {
                                unset($costCenterTransaction['id']);
                                return $costCenterTransaction;
                            }, $this->data['CostCenterTransaction']);
                            $this->CostCenter->save_model_transaction($this->data,'Expense',['entity_type' => $this->is_income ? 'income' :  'expense']);
                        }
                        $this->Expense->saveField("accounts_data", json_encode($this->data['CostCenterTransaction']));
					}
					//end cost transaction section
                    //Save Cost center transaction per journal transaction
                    if(!empty($this->data['JournalTransaction']) && empty($is_draft)) {
                        $this->Expense->saveTransactionCostCenters($this->Expense->id, $this->data, $type, $filterByType);
                    }

					if(IS_REST){
						$this->set('id', $this->Expense->id);
                        $this->set('expense_number', $this->data['Expense']['expense_number']);
                        $this->render('created');
						return;
					}
                    $PT = GetObjectOrLoadModel('PrintableTemplate');
                    $default_templates = $PT->getDefaultTemplateForType(($this->is_income ? 'income' : 'expense'));
                    foreach($default_templates as $k => $default_template){
                        if($default_template['PrintableTemplate']['language'] == $this->lang){
                            $default_template = $default_template;
                            break;
                        }
                    }
                    if (!empty($this->params['url']['box'])) {
                        $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                        $redirect = array('controller' => $this->controller, 'action' => $action, '?' => array('box' => 1, 'success' => 1));
                    }else if ( !empty ($this->data['Expense']['back_to_wo'] ) && !empty ( $this->data['Expense']['work_order_id']) )
                    {
                        $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                        $redirect = Router::url( ['controller' => 'work_orders' , 'action' => 'view' ,$this->data['Expense']['work_order_id'].'#'.$this->single_title.'Block' ]);
                    } else if (!empty($this->data['Expense']['back_to_wf'])) {
                        $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                        $redirect = Router::url($this->data['Invoice']['back_to_wf'] . '#' . $this->single_title . 'Block');
                    }
                    if ( $action != 'add_print') {
                        $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                        $urlParams = $this->is_mileage? ["is_mileage"=>1]:[];
                        if ($action == "add") {
                            $redirect = array('controller' => $this->controller, 'action' => $action,"?"=>$urlParams);
                        } else {
                            $redirect = array('controller' => $this->controller, 'action' => $action, "?"=>$urlParams, $this->Expense->id);
                        }
                       
                    } else {
                        $this->redirect(array('controller' => 'printable_templates', 'action' => 'print_saved', $this->Expense->id, $default_template['PrintableTemplate']['id'], ($this->is_income ? 'income' : 'expense')));
                    }
                    if (isset($this->data['Expense']['is_box'])){
                        $this->set('expense_id', $this->Expense->id);
                        $this->layout = 'box';
                        $this->set('isBox', true);
                        $this->set('box', true);
                        return $this->render('owner_wait');
                    }

                    if($this->data['Expense']['source_type'] == Expense::SOURCE_TYPE_UNIT ){
                       $this->izamFlashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)));
                       $redirect = Router::url('/v2/owner/rental/units/'.$this->data['Expense']['source_id'].'#expenses', true);
                    }
                    return $this->redirect($redirect);
                }
                else {
                     // Used To get attachments details in case of Validation error occurred. 
                    if (!empty($this->data['Expense']['attachment'])) {
                        $filesId = explode(',', $this->data['Expense']['attachment']);
                        $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                        $this->data['Attachments'] = $attachment;
                    }

                if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Expense->validationErrors]);

                if (!empty($this->data['Expense']['vendor'])){
                    $this->data['Expense']['vendor'] = json_encode(array($this->data['Expense']['vendor']));
                }
                if (!empty($this->data['Expense']['category'])){
                    $this->data['Expense']['category'] = json_encode(array($this->data['Expense']['category']));
                }
                if (!empty( $this->data['ExpenseCategory']['name'])){
                    $this->data['ExpenseCategory']['name'] = json_encode(array( $this->data['ExpenseCategory']['name']));
                }
                debug($this->Expense->validationErrors);
               
                $this->data['Expense']['date'] = format_date($this->data['Expense']['date']);
                if (isset($this->data["source"]) && $this->data["source"] == "bank_transaction" && !empty($this->Expense->validationErrors)) {
                    CustomValidationFlash($this->Expense->validationErrors);
                } else {
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __($this->single_title, true)));
                }
                if (isset($this->data['Expense']['is_box'])){
                    $redirect = ['controller' => $this->controller, 'action' => 'add', '?' => ['box' => 1, 'success' => 1]];
                    $this->redirect($redirect);
                }
            }
            } else {
                $validCostCenters = $this->checkCostCentersValidity($this->data);
                if ($validCostCenters && $saved_expense=$this->Expense->save($this->data)) {
                    $expense_id=$saved_expense['Expense']['id'];
                    $actionType = $this->is_income ? ACTION_CLONE_INCOME : ($this->is_mileage? ACTION_CLONE_MILEAGE: ACTION_CLONE_EXPENSE);
                    $this->add_clone_action_line($clone_id, $actionType);
                    $this->saveExpenseAttachments();
                    if (empty($is_draft)) {
                        foreach ($this->data['JournalTransaction'] as $entity) {
                            $this->_recalcjournalbalance($entity['journal_account_id']);
                        }
                        $this->_recalcjournalbalance($this->data['Expense']['journal_account_id']);
                        if(!empty($generated_number)) {
                            \AutoNumber::update_auto_serial($autoNumberType );
                        }
                        elseif($this->data['Expense']['expense_number']!=$this->data['Expense']['default_expense_number'])
                        {
                            $this->is_income ? \AutoNumber::update_last_from_number($this->data['Expense']['expense_number'],\AutoNumber::TYPE_INCOME) : \AutoNumber::update_last_from_number($this->data['Expense']['expense_number'],\AutoNumber::TYPE_EXPENSE);
                        }
                    } else {
                        $this->Expense->saveField('expense_number', 'Draft-'.$saved_expense['Expense']['id']);
                        $this->data['Expense']['expense_number'] = 'Draft-'.$saved_expense['Expense']['id'];
                    }
                    $actionType = $this->is_income ? ACTION_ADD_INCOME : ($this->is_mileage ? ACTION_ADD_MILEAGE :ACTION_ADD_EXPENSE);
                    $this->add_actionline($actionType, array('primary_id' => $this->Expense->id, 'param1' => $this->data['Expense']['amount'], 'param2' => $this->data['Expense']['category'], 'param3' => $this->data['Expense']['vendor'],'param4' => $this->data['Expense']['expense_number'], 'param6' => $this->data['Expense']['date']));

                    $this->data['RecurringExpense']['last_generated_id'] = $expense_id;
                    $this->data['RecurringExpense']['last_generated_date'] = $this->data['Expense']['date'];
                    $this->data['RecurringExpense']['active'] = 1;
                    $this->data['RecurringExpense']['unit_name'] = $this->data['RecurringExpense']['unit_name'];
                    $this->data['RecurringExpense']['period_unit'] = $this->Expense->RecurringExpense->period_list[$this->data['RecurringExpense']['unit_name']]['unit'];
                    $this->data['RecurringExpense']['unit_count'] = $this->Expense->RecurringExpense->period_list[$this->data['RecurringExpense']['unit_name']]['unit_count'];
                    if ($this->data['RecurringExpense']['end_date'] != "") {

                        //$date = DateTime::createFromFormat($dateFormat, $this->data['RecurringExpense']['end_date']);
                        $this->data['RecurringExpense']['end_date'] = $this->Expense->formatDateTime($this->data['RecurringExpense']['end_date']);
                    }

                    $this->Expense->RecurringExpense->save($this->data['RecurringExpense']);
                    $this->Expense->saveField("recurring_expense_id", $this->Expense->RecurringExpense->id, false);

                    if($this->data['CostCenterTransaction'])
                    {
                        if (empty($is_draft)) {
                            $this->loadModel('CostCenter');
                            $this->data['Expense']['id'] = $expense_id;
                            $this->CostCenter->save_model_transaction($this->data,'Expense',['entity_type' => $this->is_income ? 'income' :  'expense']);
                        }
                        $this->Expense->saveField("accounts_data", json_encode($this->data['CostCenterTransaction']));
                    }
                    if(!empty($this->data['JournalTransaction']) && empty($is_draft)) {
                        $this->Expense->saveTransactionCostCenters($expense_id, $this->data, $type, $filterByType);
                    }

                    if ($this->data['Expense']['date'] < date('Y-m-d') && empty($is_draft)){
                        $this->cron(getCurrentSite('id'));
                    }
					if(IS_REST){
						$this->set('id', $this->Expense->id);
                        $this->set('expense_number', $this->data['Expense']['expense_number']);
						$this->render('created');
						return;
					}
                    $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                    $redirect = array('controller' => $this->controller, 'action' => 'view',$expense_id);
                    if (!empty($this->params['url']['box'])) {
                        $redirect = array('controller' => $this->controller, 'action' => 'add', '?' => array('box' => 1, 'success' => 1));
                    } else if ( !empty ($this->data['Expense']['back_to_wo'] ) && !empty ( $this->data['Expense']['work_order_id']) ){
                        $redirect = Router::url( ['controller' => 'work_orders' , 'action' => 'view' ,$this->data['Expense']['work_order_id'].'#'.$this->single_title.'Block' ]);
                    } else if (!empty($this->data['Expense']['back_to_wf'])) {
                        $redirect = Router::url($this->data['Invoice']['back_to_wf'] . '#' . $this->single_title . 'Block');
                    }

                    if (isset($this->data['Expense']['is_box'])) {
                        $this->set('expense_id', $this->Expense->id);
                        $this->layout = 'box';
                        $this->set('isBox', true);
                        $this->set('box', true);
                        return $this->render('owner_wait');
                    }
                    $this->redirect($redirect);
                } else {

                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __($this->single_title, true)));
                }
            }


        } else {                
			if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Expense->validationErrors]);
            if ( !empty($id)) {
                $expense = $this->Expense->find(array('Expense.id' => $id));
                if(!empty($expense))
                {
                    if($expense['Expense']['is_mileage'] && !$this->is_mileage)
                    {
                        $this->redirect(Router::url(['controller' => 'expenses',  'action' => 'add', $id ,'?' => ['is_mileage' => '1'] ]));
                    }
                    $this->set('clone_id', $id);
                    if (ifPluginActive(AccountingPlugin))
                    {
                      $this->loadModel('CostCenterTransaction');
                      $journal = $this->Expense->get_entity_journal($id,['entity_type' => $this->is_income ? 'income' : 'expense']);
                      $this->set('journal',$journal);
                      $journal_id = $journal['Journal']['id'];
                      $assigned_cost_centers = $this->CostCenterTransaction->getJournalCostTransaction($journal_id);
                      $cost_centers = $assigned_cost_centers;
                      $assigned_cost_centers = [];
                      $journalTransactions = JournalTransactionCostCenterUpdater::filterJournalTransactionsByType($journal['JournalTransaction'], $filterByType);
                      foreach($journalTransactions as $transaction) {
                      foreach($cost_centers as $cost_center){
                          $cost_centers_list[] = $cost_center['CostCenter'];
                          if( $cost_center['CostCenterTransaction']['journal_transaction_id'] == $transaction['id']) {
                          $assigned_cost_centers[$transaction['id']][] = $cost_center['CostCenterTransaction'];
                          }
                      }
      
                      }
                    $this->loadModel('CostCenter');
                    $cost_centers_list = array_column($this->CostCenter->find('all', ['conditions' => ['CostCenter.id' => array_column(json_decode($expense['Expense']['accounts_data'], true)??[], 'cost_center')]]), 'CostCenter');
                      $this->set('journal_transactions', $journalTransactions);
                      $this->set('assigned_cost_centers', $assigned_cost_centers);
                      $this->set('assigned_cost_centers_list', $cost_centers_list);
                    }
                    $this->set('has_cost_center_transaction',($this->Expense->hasConstCenterTransaction($id, $this->is_income ? 'income' : 'expense')|| !empty(json_decode($expense['Expense']['accounts_data'], true)[0]['cost_center_id'])));
                    $this->data = $expense;
                    if(!empty($this->data['Expense']['category'])){
    
                        $this->data['Expense']['category']=json_encode(array($this->data['Expense']['category']));
    
                    }
                    if(!empty($this->data['ExpenseCategory']['name'])){
    
                        $this->data['ExpenseCategory']['name']=json_encode(array($this->data['ExpenseCategory']['name']));
    
                    }
                    if(!empty($this->data['Expense']['vendor'])){
                        $this->data['Expense']['vendor']=json_encode(array($this->data['Expense']['vendor']));
    
                    }
    
                    $jsDateFormat = getDateFormats('std');
                    $this->data['Expense']['date'] = format_date(date("Y-m-d", strtotime($this->data['Expense']['date'])));
    
                    if ($this->data['RecurringExpense']['id'] != "" and $this->data['RecurringExpense']['active'] == 1) {
                        $this->data['Expense']['recurring'] = 1;
                        if ($this->data['RecurringExpense']['end_date'] != "0000-00-00") {
                            $this->data['RecurringExpense']['end_date'] = format_date(date("Y-m-d", strtotime($this->data['RecurringExpense']['end_date'])));
                        } else {
                            unset($this->data['RecurringExpense']['end_date']);
                        }
                    }
                    
                    unset($this->data['Expense']['id']);
                    unset($this->data['Expense']['expense_number']);
                    unset($this->data['Expense']['work_order_id']);
                }
            }
            $this->data['Expense']['date'] = format_date(date('Y-m-d'));
            $this->data['Expense']['currency_code'] = getAuthOwner('currency_code');
            $this->data['Attachmnets'] = []; //Clear attachments from the clonned expense
			$this->data['Expense']['expense_number'] = $this->data['Expense']['default_expense_number'] = \AutoNumber::get_auto_serial($autoNumberType );
        }
        
        if(isset($_GET['source']) && $_GET['source'] === 'bank_transaction') {
            $this->setBankTransactionData();
        }

        if ( ifPluginActive(WorkOrderPlugin) || ifPluginActive(WorkflowPlugin)) {
            $this->loadModel ( 'WorkOrder');
            $this->set ( 'work_order_ids' , ["" => '['.__("Choose from a list" , true ).']'] + $this->WorkOrder->get_work_orders ( ['WorkOrder.status'=>WorkOrder::STATUS_OPEN] ) ) ;
            if ( !empty ( $_GET['work_order_id'] )  ) {
                $this->data['Expense']['work_order_id'] =  $_GET['work_order_id'];
            }
        }


        if (ifPluginActive(ExpensesPlugin))
        {
            $this->loadModel ( 'ItemPermission');
            $this->loadModel ( 'Treasury');
            $permission = ($this->is_income ?ItemPermission::PERMISSION_DEPOSIT:ItemPermission::PERMISSION_WITHDRAW );
            $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,$permission)  ) ;

            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;

        }
		$this->set('canAddCategory', check_permission(ADD_EXPENSE_CATEGORY));
        $this->__init_form_data();
 
      if (ifPluginActive(RENTAL_PLUGIN) && $expense) {
        $this->loadModel('RentalUnit');

        $source_id = $expense['Expense']['source_id'];
        $condition = [];
        $temp = [];
        if (isset($source_id)) {
            $condition = ['RentalUnit.id' => $source_id];
            $unitData = $this->RentalUnit->find('all', array('fields' => 'id,name,RentalUnitType.name', 'conditions' => $condition));
            foreach ($unitData as  $value) {
                $temp[$value['RentalUnit']['id']] =   $value['RentalUnit']['name'] . '(' . $value['RentalUnitType']['name'] . ')';
            }
        }

            $this->set('units', $temp);
            $this->data['Expense']['source_id'] = $expense['Expense']['source_id'];
        }
        $this->set('cloned_id', $id ?? null);
        $this->set('title_for_layout',  __('Add ' . $this->single_title, true));

    }

    private function setBankTransactionData() {
        $this->loadModel('BankTransaction');
        $bankTransaction = $this->BankTransaction->findById($_GET['bank_transaction_id']);
        $this->data['Expense']['currency_code'] = $bankTransaction['Treasury']['currency_code'];
        $this->data['Expense']['amount'] = $_GET['amount'];
        $this->data['Expense']['note'] = $bankTransaction['BankTransaction']['description'];
        $this->data['Expense']['treasury_id'] = $bankTransaction['Treasury']['id'];
        $this->data['Expense']['date'] = format_date($bankTransaction['BankTransaction']['date']);
        $this->set('currencyReadOnly', true);
        $this->set('treasuryReadOnly', true);
    }


    private function resetUpdatedAccountBalance($expense, $newExpense) {

        if($newExpense['Expense']['journal_account_id'] != $expense['Expense']['journal_account_id'] && $expense['Expense']['journal_account_id'])
        {
            $row = $this->JournalAccount->find('first', array('recursive' => -1, 'conditions' => array('JournalAccount.id' => $expense['Expense']['journal_account_id'])));

            if ($row['JournalAccount']['entity_type'] == 'client') {
                $this->loadModel('Client');
                $this->Client->adjust_and_pay($row['JournalAccount']['entity_id']);

            }
            if ($row['JournalAccount']['entity_type'] == 'supplier') {
                $this->loadModel('Supplier');
                $this->Supplier->adjust_and_pay($row['JournalAccount']['entity_id']);

            }
        }

    }

    function owner_edit($id = null) {
        $_REQUEST=modifyArray($_REQUEST);
        $this->data=modifyArray($this->data);
        $this->Expense->bindAttachmentRelation('expense');
        $is_draft = ($this->params['url']['send']?? null) == "draft" ? 1 : null;
        $this->Expense->bindAttachmentRelation('expense');
        $this->loadModel('ExpenseCategory');
        $this->loadModel('Journal');
        $this->loadModel('CostCenterTransaction');
        $this->loadModel('CostCenter');
        $owner = getAuthOwner();
		App::import('Vendor', 'AutoNumber');
        $default_currency = $this->Journal->get_default_currency();
        $expense = $this->Expense->find(array('Expense.id' => $id));
        $cost_centers_list = [];
        if(Expense::hasSource($expense) && !isset($this->data['Expense']['source_type']))
        {
            if( $expense['Expense']['source_type'] !== Expense::SOURCE_TYPE_UNIT ){
            App::import('vendor','ExpenseV',['file'=>'Expenses/autoload.php']);
            $expenseSource = \ExpenseV\Sources\ExpenseSourceAbstract::getSourceTypeOptions($expense['Expense']['source_type']);
            $this->flashMessage(sprintf(__('%s cannot be edited as it has a related %s %s', true), $this->single_title,$expenseSource,$expense['Expense']['source_id']),'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer(array('controller' => $this->controller, 'action' => 'index'), true));
            }
        }
        if($expense['Expense']['is_income'] && !$this->is_income)
        {
            $this->redirect(Router::url(['controller' => 'incomes', 'edit', $id]));
        }else if(!$expense['Expense']['is_income'] && $this->is_income)
        {
            $this->redirect(Router::url(['controller' => 'expenses', 'edit', $id]));
        }
        if(!$expense['Expense']['is_income'] && $this->is_income)
        {
            $this->redirect(Router::url(['controller' => 'expenses', 'edit', $id]));
        }

        if($expense['Expense']['is_mileage'] && !$this->is_mileage && !IS_REST)
        {
            $this->redirect(Router::url(['controller' => 'expenses',  'action' => 'edit', $id ,'?' => ['is_mileage' => '1'] ]));
        }
        if (!$expense) {
			if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __($this->single_title, true))));
            $this->flashMessage(sprintf(__("%s not found.", true), __($this->single_title, true)));
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }

        // Validate If Expense is Being used in Expense Distribution
        if ($this->Expense->isExpenseUsedInExpenseDistribution($id)) {
            $requisition_data = $this->Expense->getExpenseDistributionRequisitionDetails($id);
            $requisition_url = $requisition_data['requisition_url'];
            $requisition_translation = $requisition_data['requisition_translation'];
            $requisition_number = $requisition_data['requisition_number'];
            $this->flashMessage(sprintf(__("You cannot edit in the expense that already selected in an expense distribution transaction %s", true),"<a href='$requisition_url'>$requisition_translation # $requisition_number</a>"));
            $this->redirect($this->referer(['action' => 'index']));
        }

		$this->validate_open_day($expense['Expense']['date']);
	    if(ifPluginActive(AccountingPlugin)){
			$this->loadModel('Journal');
             Journal::$auto_accounts['client']['callback_after_add']='adjust_and_pay';
             Journal::$auto_accounts['supplier']['callback_after_add']='adjust_and_pay';


	         $this->set('multi_journal_accounts',Settings::getValue(AccountingPlugin, 'enable_multi_journal_accounts'));
        }
        if ($owner['staff_id'] > 0) {

            $staff = $owner['staff_id'];
            if (
                (!check_permission($this->permissions['Edit_Delete_all']) && (!check_permission($this->permissions['Edit_delete_his_own']) or $expense['Expense']['staff_id'] != $staff )) &&
                (!check_permission($this->permissions['Draft'])  || $expense['Expense']['staff_id'] != $staff )
            )
            {
				if(IS_REST) $this->cakeError('error403', ['message' => __("You are not allowed to edit these ".$this->controller, true)]);
                $this->flashMessage(__("You are not allowed to edit these ".$this->controller, true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array( 'controller' => $this->controller, 'action' => 'index'));
            }

        }
        
		if(IS_REST) $this->data['Expense']['id'] = $id;
        if (!empty($this->data)) {
            $this->data['Expense']['note'] = html_entity_decode($this->data['Expense']['note']);
            $was_draft = $expense['Expense']['status'] == "draft" ? 1 :0;
            if (!empty($this->data['Expense']['unit_id'])) {
                $this->data['Expense']['source_type'] = Expense::SOURCE_TYPE_UNIT;
                $this->data['Expense']['source_id'] = $this->data['Expense']['unit_id'];
            }

            if(IS_REST && IS_MOBILE_APPLICATION && ($expense['Expense']['last_update_external_source'] == 'web' || $expense['Expense']['external_source'] == 'web')){
                $this->cakeError('error403', ["message" => __("You cannot update the expense as it has already been updated from the web browser.", true)]);
            }elseif(!IS_MOBILE_APPLICATION){
                $this->data['Expense']['last_update_external_source'] = 'web';
            }

            $vendor = $this->data['Expense']['vendor'] ?? null;
            $category = $this->data['Expense']['category'] ?? null;
            if (!empty($vendor)) {
                $this->data['Expense']['vendor'] = is_array($vendor) ? array_map('html_entity_decode', $vendor) : html_entity_decode($vendor);
            }
            
            if (!empty($category)) {
                $this->data['Expense']['category'] = is_array($category) ? array_map('html_entity_decode', $category) : html_entity_decode($category);
            }
            
            $this->data = $this->_submitCommon($this->data);

            $autoNumberType = ($this->is_income) ? \AutoNumber::TYPE_INCOME : 
            ( $this->is_mileage ? \AutoNumber::TYPE_MILEAGE :\AutoNumber::TYPE_EXPENSE);

            \AutoNumber::set_validate( $autoNumberType);
            //set number if new expense and he didn't change or number is empty

			if(empty($this->data['Expense']['expense_number'])||(empty($this->data['Expense']['id'])&&$this->data['Expense']['expense_number']==$this->data['Expense']['default_expense_number']) || $was_draft){
				$this->data['Expense']['default_expense_number'] = $this->data['Expense']['expense_number'] = \AutoNumber::get_auto_serial( $autoNumberType);
				$generated_number = true;
			}
            $this->data['Expense']['is_income'] = $this->is_income;
            $this->data['Expense']['date'] = $this->Expense->formatDate($this->data['Expense']['date']);
            if($this->is_mileage){
                $this->data['Expense']['amount'] = floatval($this->data['Expense']['rate']) * floatval($this->data['Expense']['distance']);
            }
            if (isset($this->data['']['vendor']) && is_array($this->data['']['vendor'])){
                $this->data['Expense']['vendor'] = $this->data['Expense']['vendor'][0];
            }
            if (empty($this->data['Expense']['vendor'])){
                $this->data['Expense']['vendor'] = "";
            }
            if (isset($this->data['Expense']['category']) && is_array($this->data['Expense']['category'])){
                $this->data['Expense']['category'] = preg_replace('/\s+/', ' ', $this->data['Expense']['category'][0]);
                $this->data['Expense']['expense_category_id'] = $this->ExpenseCategory->findOrCreate($this->data['Expense']['category'],$this->is_income?2:1);
            } else if (is_numeric($this->data['Expense']['category'])) {
                $this->data['Expense']['expense_category_id'] = $this->data['Expense']['category'];
                $this->data['Expense']['category'] = $this->ExpenseCategory->find('first',['conditions' => ['ExpenseCategory.id' => $this->data['Expense']['expense_category_id']]])['ExpenseCategory']['name'];
            }
            if (empty($this->data['Expense']['category'])){
                $this->data['Expense']['category'] = "";
            $this->data['Expense']['expense_category_id']="";
            }
            if (!empty($is_draft)) {
                $this->data['Expense']['status'] = 'draft';
            } else {
                $this->data['Expense']['status'] = 'issued';
            }

			if(!empty($this->data['Expense']['filename'])){

				$this->loadModel('TempFile');
				$row=$this->TempFile->readFile($this->data['Expense']['filename']);

				$file_settings = $this->Expense->getFileSettings();

				$from_file = $row['data']['file_name_system_full_path'];
				$to_file = WWW_ROOT.$file_settings['file']['folder'].basename($from_file);
				mkdir(WWW_ROOT.$file_settings['file']['folder'],0777);
				if(!empty($from_file) && copy($from_file, $to_file))
				{
					$this->data['Expense']['file'] = basename($from_file);
					$this->TempFile->deleteFile($this->data['Expense']['filename']);

				}
			}
        }

        $accounts = json_decode($expense['Expense']['accounts_data'], true);
        $type = $this->is_income ? 'income' : 'expense';
        $filterByType = $type == "expense" ? 'debit' : 'credit';
        $journal_id = $this->Expense->get_journal_id($id, $type);
        $journal = $this->Journal->read(null, $journal_id);
        if(IS_REST && $this->data['Expense']['is_mileage'] != 1){
            unset($this->data['Expense']['distance']) ;
            unset($this->data['Expense']['rate']) ;
            unset($this->data['Expense']['distance_unit']) ;
        }
        if ($this->data['Expense']['recurring'] == "" or $this->data['Expense']['recurring'] == 0) {
            unset($this->data['RecurringExpense']);
            if (!empty($this->data)) {
                $validCostCenters = $this->checkCostCentersValidity($this->data);

                if($this->data['CostCenterTransaction'] && $this->data['Expense']['manual_cost_centers'] && $this->data['Expense']['manual_cost_centers']!="0")
                {
                    unset($this->data['JournalTransaction']);
                }
                
                if ($validCostCenters && $this->Expense->save($this->data)) {

                        if (!empty($is_draft)) {
                            $this->Expense->delete_auto_journals($expense['Expense']['id']);
                        }
                        $this->data['Expense']['id'] = $id;
                        izam_resolve(ExpenseService::class)->update(ServiceModelDataTransformer::transform($this->data, 'Expense'));
                        // Edit Attachments.
                        $attachments = $this->data['Expense']['attachment'];
                        $imagesIds = is_string($attachments)? explode(',', $attachments):[];
                        if(!empty($imagesIds))
                        {
                           izam_resolve(AttachmentsService::class)->save('expense', $this->Expense->id, $imagesIds); 
                        }
                        if(ifPluginActive(AccountingPlugin)) {
                            foreach ($this->data['JournalTransaction'] as $entity) {
                                $this->_recalcjournalbalance($entity['journal_account_id']);
                            }
                            if (!empty($this->data['Expense']['journal_account_id'])) {
                                $this->loadModel('JournalAccount');
                                $row = $this->JournalAccount->find('first', array('recursive' => -1, 'conditions' => array('JournalAccount.id' => $this->data['Expense']['journal_account_id'])));

                                if ($row['JournalAccount']['entity_type'] == 'client') {
                                    $this->loadModel('Client');
                                    $this->Client->adjust_and_pay($row['JournalAccount']['entity_id']);

                                }
                                if ($row['JournalAccount']['entity_type'] == 'supplier') {
                                    $this->loadModel('Supplier');
                                    $this->Supplier->adjust_and_pay($row['JournalAccount']['entity_id']);

                                }

                                /**
                                 * This function is good when chaning the account of the income to update the old account balance
                                 */
                                $this->resetUpdatedAccountBalance($expense, $this->data);

                            }
                        }

                    $this->_recalcjournalbalance($this->data['Expense']['journal_account_id']);
                    if (empty($is_draft)) {
                        if (!empty($generated_number))  \AutoNumber::update_auto_serial($autoNumberType) ;
                        elseif ($this->data['Expense']['expense_number'] != $this->data['Expense']['default_expense_number']) {
                            \AutoNumber::update_last_from_number($this->data['Expense']['expense_number'],  $autoNumberType);
                        }
                    } else {
                        $this->Expense->saveField('expense_number', 'Draft-'.$expense['Expense']['id']);
                        $this->data['Expense']['expense_number'] = 'Draft-'.$expense['Expense']['id'];
                    }
                    if (empty($this->params['url']['stop_activity'])) {
                        $actionType = $this->is_income ? ACTION_UPDATE_INCOME : ($this->is_mileage? ACTION_UPDATE_MILEAGE:ACTION_UPDATE_EXPENSE);
                        $this->add_actionline($actionType , array('primary_id' => $id, 'param1' => $this->data['Expense']['amount'], 'param2' => $this->data['Expense']['category'], 'param3' => $this->data['Expense']['vendor'],'param4' => $this->data['Expense']['expense_number'], 'param6' => $this->data['Expense']['date'] , 'param7' =>  $this->data['Expense']['attachment'] ));
                    }
                    $this->Expense->RecurringExpense->id = $expense['Expense']['recurring_expense_id'];
                    $this->Expense->RecurringExpense->saveField("active", 0, false);
					//cost transaction section

                    if($this->data['CostCenterTransaction'] && $this->data['Expense']['manual_cost_centers'] && $this->data['Expense']['manual_cost_centers']!="0")
					{

						$this->loadModel('CostCenter');
						$this->data['Expense']['id'] = $this->Expense->id;
                        if (empty($is_draft)) {
                            $this->CostCenter->save_model_transaction($this->data, 'Expense', ['entity_type' => $this->is_income ? 'income' : 'expense'], true);
                        }
                        $this->Expense->saveField("accounts_data", json_encode($this->data['CostCenterTransaction']));
                    }elseif($this->data['JournalTransaction']){
                        $journal_id = $this->Expense->get_journal_id($id, $this->is_income ? 'income' : 'expense');
                        $allCostCenterTransaction = $this->CostCenterTransaction->find('list', array('fields' => 'id,id', 'conditions' => array('CostCenterTransaction.is_auto' => false,'CostCenterTransaction.journal_id' => $journal_id)));
                        foreach ($allCostCenterTransaction as $cid) {
                            $this->CostCenterTransaction->delete($cid);
                        }
                    }
					//end cost transaction section
                    if (!empty($this->data['JournalTransaction'])) {
                        $this->Expense->updateOrDeleteTransactionCostCenters($journal, $this->data, $filterByType, $accounts);
                    }


					if(IS_REST){
						$this->render('success');
						return;
					}

                    $action = 'view';
                    if (!empty($_POST['next_action']) && $_POST['next_action'] == 'add_new')
                        $action = 'add';
                    if (!empty($_POST['next_action']) && $_POST['next_action'] == 'add_print')
                        $action = 'add_print';

                    $PT = GetObjectOrLoadModel('PrintableTemplate');
                    $default_templates = $PT->getDefaultTemplateForType(($this->is_income ? 'income' : 'expense'));
                    foreach($default_templates as $k => $default_template){
                        if($default_template['PrintableTemplate']['language'] == $this->lang){
                            $default_template = $default_template;
                            break;
                        }
                    }
                    if (!empty($this->params['url']['box'])) {
                        $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                        $redirect = array('controller' => $this->controller, 'action' => $action, '?' => array('box' => 1, 'success' => 1));
                    }else if ( !empty ($this->data['Expense']['back_to_wo'] ) && !empty ( $this->data['Expense']['work_order_id']) )
                    {
                        $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                        $redirect = Router::url( ['controller' => 'work_orders' , 'action' => 'view' ,$this->data['Expense']['work_order_id'].'#'.$this->single_title.'Block' ]);
                    } else if (!empty($this->data['Expense']['back_to_wf'])) {
                        $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                        $redirect = Router::url($this->data['Invoice']['back_to_wf'] . '#' . $this->single_title . 'Block');
                    }
                    if ( $action != 'add_print') {
                        $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                        $redirect = array('controller' => $this->controller, 'action' => $action, $this->Expense->id);
                    } else {
                        $this->redirect(array('controller' => 'printable_templates', 'action' => 'print_saved', $this->Expense->id, $default_template['PrintableTemplate']['id'], ($this->is_income ? 'income' : 'expense')));
                    }
                    if (isset($this->data['Expense']['is_box'])){
                        $this->set('expense_id', $this->Expense->id);
                        $this->layout = 'box';
                        $this->set('isBox', true);
                        $this->set('box', true);
                        return $this->render('owner_wait');
                    }
                    return $this->redirect($redirect);

                    $this->flashMessage(sprintf(__('%s  has been saved', true), __($this->single_title, true)), 'Sucmessage');

                    $this->redirect(array('controller' => $this->controller, 'action' => $action, $id));
                } else {

                    // Used To get attachments details in case of Validation error occurred. 
                    if(!empty($this->data['Expense']['attachment'])){                       
                        $filesId = explode(',',$this->data['Expense']['attachment']);
                        $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                        $this->data['Attachments'] = $attachment;
                     }

                    if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->Expense->validationErrors]);
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __($this->single_title, true)));
                }
            } else {
                $this->data = $expense;
                if(!empty($this->data['Expense']['category'])){

                    $this->data['Expense']['category']=json_encode(array($this->data['Expense']['category']));

                }
                if(!empty($this->data['ExpenseCategory']['name'])){

                    $this->data['ExpenseCategory']['name']=json_encode(array($this->data['ExpenseCategory']['name']));

                }
                if(!empty($this->data['Expense']['vendor'])){
                    $this->data['Expense']['vendor']=json_encode(array($this->data['Expense']['vendor']));

                }

                $jsDateFormat = getDateFormats('std');
                $this->data['Expense']['date'] = format_date(date("Y-m-d", strtotime($this->data['Expense']['date'])));

                if ($this->data['RecurringExpense']['id'] != "" and $this->data['RecurringExpense']['active'] == 1) {
                    $this->data['Expense']['recurring'] = 1;
                    if ($this->data['RecurringExpense']['end_date'] != "0000-00-00") {
                        $this->data['RecurringExpense']['end_date'] = format_date(date("Y-m-d", strtotime($this->data['RecurringExpense']['end_date'])));
                    } else {
                        unset($this->data['RecurringExpense']['end_date']);
                    }
                }
            }
        } else {
            $validCostCenters = $this->checkCostCentersValidity($this->data);
            if (!empty($is_draft)) {
                $this->Expense->delete_auto_journals($expense['Expense']['id']);
            }
            $valid = true;
            //check if date is before last generated date
            if ($expense['RecurringExpense']['last_generated_date'] > $this->data['Expense']['date']) {
                $this->Expense->validationErrors['date'] = sprintf(__('Expense date can not be before last generated date %s', true), $expense['RecurringExpense']['last_generated_date']);
                $valid = false;
            }
            if ($valid && $validCostCenters && $this->Expense->save($this->data)) {
                foreach ($this->data['JournalTransaction'] as $entity) {
                    $this->_recalcjournalbalance($entity['journal_account_id']);
                }
                if (!empty($this->data['JournalTransaction'])) {
                    $this->Expense->updateOrDeleteTransactionCostCenters($journal, $this->data, $filterByType, $accounts);

                }
 
                 // Edit Attachments.
                 $attachments = $this->data['Expense']['attachment'];
                 $imagesIds = is_string($attachments)? explode(',', $attachments):[];
                 if(!empty($imagesIds))
                 {
                    izam_resolve(AttachmentsService::class)->save('expense', $this->Expense->id, $imagesIds); 
                 }
                 
                if($this->data['CostCenterTransaction'] && $this->data['Expense']['manual_cost_centers'] && $this->data['Expense']['manual_cost_centers']!="0")
                {
                    $this->loadModel('CostCenter');
                    $this->data['Expense']['id'] = $this->Expense->id;
                    if (empty($is_draft)) {
                        $this->CostCenter->save_model_transaction($this->data,'Expense',['entity_type' => $this->is_income ? 'income' :  'expense'], true);
                    }
                    $this->Expense->saveField("accounts_data", json_encode($this->data['CostCenterTransaction']));
                }else{

                if(empty($this->data['JournalTransaction'])) {
                    $journal_id = $this->Expense->get_journal_id($id, $this->is_income ? 'income' : 'expense');
                    $this->loadModel('CostCenterTransaction');
                    $allCostCenterTransaction = $this->CostCenterTransaction->find('list', array('fields' => 'id,id', 'conditions' => array('CostCenterTransaction.journal_id' => $journal_id)));
                    foreach ($allCostCenterTransaction as $cid) {
                        $this->CostCenterTransaction->delete($cid);
                    }
                }

                }
                $this->_recalcjournalbalance($this->data['Expense']['journal_account_id']);
                if (empty($is_draft)) {
                    if(!empty($generated_number)) \AutoNumber::update_auto_serial( $autoNumberType);
                    elseif($this->data['Expense']['expense_number']!=$this->data['Expense']['default_expense_number'])
                    {
                        \AutoNumber::update_last_from_number($this->data['Expense']['expense_number'], $autoNumberType) ;
                    }
                } else {
                    $this->Expense->saveField('expense_number', 'Draft-'.$expense['Expense']['id']);
                    $this->data['Expense']['expense_number'] = 'Draft-'.$expense['Expense']['id'];
                }
                if (empty($this->params['url']['stop_activity'])) {
                    $actionType = $this->is_income ? ACTION_UPDATE_INCOME : ($this->is_mileage? ACTION_UPDATE_MILEAGE:ACTION_UPDATE_EXPENSE);
                    $this->add_actionline($actionType, array('primary_id' => $id, 'param1' => $this->data['Expense']['amount'], 'param2' => $this->data['Expense']['category'], 'param3' => $this->data['Expense']['vendor'],'param4' => $this->data['Expense']['expense_number'], 'param6' => $this->data['Expense']['date']));
                }
                $this->data['RecurringExpense']['active'] = 1;
                $this->data['RecurringExpense']['period_unit'] = $this->Expense->RecurringExpense->period_list[$this->data['RecurringExpense']['unit_name']]['unit'];
                $this->data['RecurringExpense']['unit_count'] = $this->Expense->RecurringExpense->period_list[$this->data['RecurringExpense']['unit_name']]['unit_count'];
                if ($this->data['RecurringExpense']['end_date'] != "") {


                    $this->data['RecurringExpense']['end_date'] = $this->Expense->formatDate($this->data['RecurringExpense']['end_date']);
                }

                $this->Expense->RecurringExpense->save($this->data['RecurringExpense']);

               if(empty($this->Expense->RecurringExpense->id)) $this->Expense->RecurringExpense->id=$this->data['RecurringExpense']['id'];
               $this->Expense->saveField("recurring_expense_id", $this->Expense->RecurringExpense->id, false);
                $this->Expense->update_last_recurring_date($this->Expense->RecurringExpense->id);
				if(IS_REST){
					$this->render('success');
					return;
				}
                $this->flashMessage(sprintf(__('%s has been saved', true), __($this->single_title, true)), 'Sucmessage');
                $redirect = array('controller' => $this->controller, 'action' => 'view', $id);
                if (!empty($this->params['url']['box'])) {
                    $redirect = array('controller' => $this->controller, 'action' => 'add', '?' => array('box' => 1, 'success' => 1));
                }
                $this->redirect($redirect);
            } else {
				if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Expense->validationErrors]);
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __($this->single_title, true)));
            }
        }
        if ( ifPluginActive(WorkOrderPlugin) ) {
            $this->loadModel ( 'WorkOrder');
            $this->set ( 'work_order_ids' ,["" => '['.__("Choose from a list" , true ).']'] + $this->WorkOrder->get_work_orders ( ['WorkOrder.status'=>WorkOrder::STATUS_OPEN] ) ) ;
        }
        if (ifPluginActive(ExpensesPlugin))
        {
            $this->loadModel ( 'ItemPermission');
            $this->loadModel ( 'Treasury');
            $permission = ($this->is_income ?ItemPermission::PERMISSION_DEPOSIT:ItemPermission::PERMISSION_WITHDRAW );
            $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,$permission)  ) ;

            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
        if (ifPluginActive(AccountingPlugin))
        {
         $this->set('journal',$this->Expense->get_entity_journal($id,['entity_type' => $this->is_income ? 'income' : 'expense']));
        }
        $this->set('has_cost_center_transaction', ($this->Expense->hasConstCenterTransaction($id, $this->is_income ? 'income' : 'expense') || !empty(json_decode($expense['Expense']['accounts_data'], true)[0]['cost_center_id'])));
        //print_pre($this->data['Expense']);
        $this->set('title_for_layout',  h(sprintf(__('Edit ' . $this->single_title, true))));
        $this->set('canAddCategory', check_permission(ADD_EXPENSE_CATEGORY));
        $this->__init_form_data();

        if( $expense['Expense']['source_type'] == Expense::SOURCE_TYPE_UNIT )
        {
            if ($expense['Expense']['source_id']) {
                $condition = ['RentalUnit.id' => $expense['Expense']['source_id']];
            }
             $this->loadModel('RentalUnit');
             $units = $this->RentalUnit->find('all', array('fields' => 'id,name,RentalUnitType.name', 'conditions' => $condition));

             $temp = [];
             foreach ($units as  $value) {
                 $temp[$value['RentalUnit']['id']] =   $value['RentalUnit']['name'] . '(' . $value['RentalUnitType']['name'] . ')';
             }
              $this->set('units', $temp);
              $this->data['Expense']['source_id'] = $expense['Expense']['source_id'];

         }
         $this->loadModel('CostCenterTransaction');
         $journal = $this->Expense->get_journal($id, $this->is_income ? 'income' : 'expense');
         $journal_id = $journal['Journal']['id'];
         $assigned_cost_centers = $this->CostCenterTransaction->getJournalCostTransaction($journal_id);
         $cost_centers = $assigned_cost_centers;
         $assigned_cost_centers = [];
         $journalTransactions = JournalTransactionCostCenterUpdater::filterJournalTransactionsByType($journal['JournalTransaction'], $filterByType);
         foreach($journalTransactions as $transaction) {
           foreach($cost_centers as $cost_center){
             $cost_centers_list[] = $cost_center['CostCenter'];
             if( $cost_center['CostCenterTransaction']['journal_transaction_id'] == $transaction['id']) {
              $assigned_cost_centers[$transaction['id']][] = $cost_center['CostCenterTransaction'];
             }
           }

        }
        $this->loadModel('CostCenter');
        $accounts_data = json_decode($expense['Expense']['accounts_data'], true);
        if(!is_null($accounts_data)) {
            $cost_centers_list = array_column($this->CostCenter->find('all', ['conditions' => ['CostCenter.id' => array_column($accounts_data, 'cost_center')]]), 'CostCenter');
        }
         $this->set('journal_transactions', $journalTransactions);
         $this->set('assigned_cost_centers', $assigned_cost_centers);
         $this->set('assigned_cost_centers_list', $cost_centers_list);
         $this->set('cloned_id', $id);
        $this->render('owner_add');
        
    }


    function owner_update_draft($id, $value)
    {
        $title = "expense";
        if ($this->is_income) {
            $title = "income";
        }
            
        $staff = getAuthStaff();
        $this->loadModel('Expense');
        $expense = $this->Expense->find('first', ['conditions' => ['Expense.id' => $id]]);
        if (!check_permission([$this->permissions['Edit_Delete_all'], $this->permissions['Draft'], $this->permissions['Add_New']]) && (!check_permission($this->permissions['Edit_delete_his_own']) or $expense['Expense']['staff_id'] != $staff )) {
            $message = sprintf(__t(ucfirst($title)." %s failed to be issued as the user does not have the permission to issue the {$title}", true), "<a target='_blank' href='/owner/{$title}s/view/".$expense['Expense']['id']."'><b>#".$expense['Expense']['expense_number']."</b></a>");
            if(IS_REST) $this->cakeError('error403', ['message' => $message]);
            $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

        if ($value == 0 && $expense['Expense']['status'] == "issued") {
            $message = sprintf(__(ucfirst($title)." %s failed to be Issued as it has already been issued before", true), "<a target='_blank' href='/owner/{$title}s/view/".$expense['Expense']['id']."'><b>#".$expense['Expense']['expense_number']."</b></a>");
            if(IS_REST) $this->cakeError('error403', ['message' => $message]);
            $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        } else if ($value == 1 && $expense['Expense']['status'] == "draft") {
            if(IS_REST) $this->cakeError('error403', ['message' => __("You can't update status of  this  {$title}", true)]);
            $this->flashMessage(__("You can't update status of  this {$title}", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $hasError = 0;
        $message = '';
        App::import('Component', 'ApiRequestsComponent');
        $apiRequests = new ApiRequestsComponent();
        $data['Expense'] = $expense['Expense'];
        $data['Expense']['date'] =  format_date(date("Y-m-d", strtotime($expense['Expense']['date'])));
        unset($data['Expense']['status']);
        if (!empty($expense['RecurringExpense']['id'])) {
            $data['RecurringExpense'] = array_intersect_key($expense['RecurringExpense'], ['id' => true, 'unit_name' => true, 'end_date' => true, "active" => true]);
            $data['Expense']['recurring'] = 1;
        }
        $accounts = json_decode($expense['Expense']['accounts_data'], true);
        if ($value == 0) {
            if (!empty($accounts[0]['amount'])) {
                $data['JournalTransaction']  = array_map(function ($item) {
                    $item['id'] = null;
                    return $item;
                }, $accounts);
            }
            if (!empty($accounts[0]["percentage"])) {
                $data['CostCenterTransaction']  = array_map(function ($item) {
                    $item['id'] = null;
                    return $item;
                }, $accounts);
                $data["Expense"]["manual_cost_centers"] = 1;
            }
            $res = $apiRequests->request("/api2/{$title}s/".$expense["Expense"]['id'].'?stop_activity=1&request_branch_id='.getCurrentBranchID(),true,'POST', $data);
        } else {
            $this->loadModel('CostCenterTransaction');
            $journal = $this->Expense->get_journal($id, $expense['Expense']['is_income'] ? 'income' : 'expense');
            $costCenters = $this->CostCenterTransaction->getJournalCostTransaction($journal['Journal']['id']);
            foreach ($costCenters as $costCenter) {
                $data["CostCenterTransaction"][] = array_intersect_key($costCenter['CostCenterTransaction'], ["cost_center_id" => true, "percentage" => true, "debit" => true, "credit" => true]);
            }
            if (empty($data["CostCenterTransaction"]) && $accounts) {
                $data['JournalTransaction']  = array_map(function ($item) {
                    $item['id'] = null;
                    return $item;
                }, $accounts);
                $data["Expense"]["manual_cost_centers"] = 1;
            }
            $res = $apiRequests->request("/api2/{$title}s/".$expense["Expense"]['id'].'?send=draft&stop_activity=1&request_branch_id='.getCurrentBranchID(),true,'POST',$data);
        }
        if ($res['code'] == 200) {
            $expense = $this->Expense->find('first', ['conditions' => ['Expense.id' => $id]]);
            $message = sprintf(__(ucfirst($title)." %s has been Issued with the following Number %s", true), "<b>#Draft-".$expense['Expense']['id']."</b>", "<a target='_blank' href='/owner/{$title}s/view/".$expense['Expense']['id']."'><b>#".$expense['Expense']['expense_number']."</b></a>");
        } else {
            $hasError = true;
            $message = $res['message'];
        }
        if (IS_REST) {
            if ($hasError) {
                return $this->cakeError('error403', ['message' => $hasError]);
            }
            echo json_encode( ["result"=>"successful", "code"=>200, 'message' => $message]);
            die;
        } else {
            if ($hasError) {
                $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
            } else {
                $this->flashMessage($message, 'Sucmessage');
            }
            $this->redirect($this->referer());
        }
    }

    function owner_create_invoice() {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission($this->permissions['View_All'])) {
                $conditions['Expense.staff_id'] = $site['staff_id'];
            }
        }
        if (!is_countable($_POST['ids']) || count($_POST['ids']) == 0) {
            $this->flashMessage(__("please select expenses", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        $conditions['Expense.id'] = $_POST['ids'];
        $count = $this->Expense->find('all', array('fields' => 'Expense.client_id', 'group' => 'Expense.client_id', 'conditions' => $conditions));
        if (count($count) > 1) {

            $this->flashMessage(__("please select expenses of one client", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
            die();
        }
        $expenses = $this->Expense->find('all', array('conditions' => $conditions));

        if (count($expenses) == 0) {
            $this->flashMessage(__("please select expenses", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        foreach ($expenses as $key => $expense) {
            $dateFormats = getDateFormats('std');
            $time_date = format_date(date("Y-m-d", strtotime($expense['Expense']['date'])));
            $Data['InvoiceItem'][$key]['item'] = $time_date;
            $Data['InvoiceItem'][$key]['description'] = trim($expense['Expense']['note']) ?: trim($expense['Expense']['vendor'] . ' ' . $expense['Expense']['category']);
            $Data['InvoiceItem'][$key]['quantity'] = 1;
            $Data['InvoiceItem'][$key]['unit_price'] = $expense['Expense']['amount'];
            $Data['InvoiceItem'][$key]['tax1'] = $expense['Expense']['tax1_id'];
            $Data['InvoiceItem'][$key]['tax2'] = $expense['Expense']['tax2_id'];
        }
        $this->Session->write('invoice_items', $Data);
        $this->redirect(array('controller' => 'invoices', 'action' => 'add'));
        die();
    }

    function owner_delete($id = null) {
        $owner = getAuthOwner();
        if (!check_permission($this->permissions['Edit_Delete_all']) && !check_permission($this->permissions['Edit_delete_his_own'])) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to delete expenses", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }
		 if(ifPluginActive(AccountingPlugin)){
			$this->loadModel('Journal');
         Journal::$auto_accounts['client']['callback_after_add']='adjust_and_pay';
         Journal::$auto_accounts['supplier']['callback_after_add']='adjust_and_pay';
         }
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        $this->setTimeOut($id);
        if (!$id && empty($_POST)) {
			if(IS_REST){
				$this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true),__($this->multi_title, true))]);
			}else{
				$this->flashMessage(sprintf(__('Invalid %s', true), __($this->multi_title, true)));
				$this->redirect(array('controller' => $this->controller, 'action' => 'index'));
			}
        }
        if($this->is_income){
        $model_real_names='incomes';
        }else{
            $model_real_names='expenses';
        }
        $module_name = __($model_real_names, true);
        $verb = __('has', true);
        if (is_countable($id) && count($id) > 1) {
            $verb = __('have', true);
            $module_name = __($model_real_names, true);
        }

        $conditions = array();

        $conditions['Expense.id'] = $id;
        $conditions['Expense.is_income'] = $this->is_income;
        $this->set('is_income',$this->is_income);
        if ($owner['staff_id'] != 0 && !check_permission($this->permissions['Edit_Delete_all'])) {
            $conditions['Expense.staff_id'] = $owner['staff_id'];
        }
        $expensesEntities = [];
        $expenses = $this->Expense->find('all', array('conditions' => $conditions));
            foreach($expenses as $k => $expense){
				$this->validate_open_day($expense['Expense']['date'], ['action' => 'index']);

                $expensesEntities[$expense['Expense']['id']] = [];
                if (EventListenerMapper::hasEventListener(EventTypeUtil::EXPENSE_DELETED)) {
                    $expensesEntities[$expense['Expense']['id']] = getRecordWithEntityStructure('expense',$expense['Expense']['id'],2)->toArray();
                }

                if(!Expense::removable(null, $expense))
                {
                if ($expense['Expense']['source_type'] !== Expense::SOURCE_TYPE_UNIT) {
                    App::import('vendor', 'ExpenseV', ['file' => 'Expenses/autoload.php']);
                    $expenseSource = \ExpenseV\Sources\ExpenseSourceAbstract::getSourceTypeOptions($expense['Expense']['source_type']);
                    $message = sprintf(__('%s cannot be deleted as it has a related %s %s', true), __($this->single_title, true), __($expenseSource, true), $expense['Expense']['source_id']);
                    if (IS_REST) $this->cakeError(['message' => $message]);
                    $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
                    $this->redirect($this->referer(array('controller' => $this->controller, 'action' => 'index'), true));
                }
                }
                // Validate If Expense is Being used in Expense Distribution
                if ($this->Expense->isExpenseUsedInExpenseDistribution($expense['Expense']['id'])) {
                    $requisition_data = $this->Expense->getExpenseDistributionRequisitionDetails($expense['Expense']['id']);
                    $requisition_url = $requisition_data['requisition_url'];
                    $requisition_translation = $requisition_data['requisition_translation'];
                    $requisition_number = $requisition_data['requisition_number'];
                    $this->flashMessage(sprintf(__("You cannot delete in the expense that already selected in an expense distribution transaction %s", true),"<a href='$requisition_url'>$requisition_translation # $requisition_number</a>"));
                    $this->redirect($this->referer(['action' => 'index']));
                }
		}
        if (empty($expenses)) {
			if(IS_REST){
				$this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), __($this->single_title,true))]);
			}else{
				$this->flashMessage(sprintf(__('%s not found', true), __($this->single_title,true)));
				$this->redirect($this->referer(array('controller' => $this->controller, 'action' => 'index'), true));
			}
        }
		if(IS_REST){
			$_POST['submit_btn'] = 'yes';
			$_POST['ids'] = [$id];
		}
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {

            if ($_POST['submit_btn'] == 'yes' && $this->Expense->deleteAll($conditions)) {
                $this->loadModel('Journal');
                foreach ($expenses as $expense) {
                    $JournalRow = $this->Journal->find('first', array( 'conditions' => array('Journal.entity_type'=>($expense['Expense']['is_income']?'income':'expense'),'Journal.entity_id' => $expense['Expense']['id'])));

                    if (EventListenerMapper::hasEventListener(EventTypeUtil::EXPENSE_DELETED)) {
                        izam_resolve(ExpenseService::class)->delete($expensesEntities[$expense['Expense']['id']]);
                    }

                    $this->Expense->delete_auto_journals($expense['Expense']['id']);
                    if(ifPluginActive(AccountingPlugin)) {
                        // Delete Auto Journals Before Adjusting To Fix Paid/Unpaid Statuses Of Entities
                        if (!empty($expense['Expense']['journal_account_id'])) {

                            $this->loadModel('JournalAccount');
                            $row = $this->JournalAccount->find('first', array('recursive' => -1, 'conditions' => array('JournalAccount.id' => $expense['Expense']['journal_account_id'])));

                            if ($row['JournalAccount']['entity_type'] == 'client') {
                                $this->loadModel('Client');
                                if(settings::getValue(InvoicesPlugin, 'automatic_pay_invoices')) {
                                    $this->Client->adjust_and_pay($row['JournalAccount']['entity_id']);
                                } else {
                                    $this->Client->adjust_balance($row['JournalAccount']['entity_id'],$JournalRow['Journal']['currency_code']);
                                }

                            }
                            if ($row['JournalAccount']['entity_type'] == 'supplier') {
                                $this->loadModel('Supplier');
                                $this->Supplier->adjust_and_pay($row['JournalAccount']['entity_id']);

                            }

                        }
                    }
                    foreach ($JournalRow['JournalTransaction'] as $entity) {
                        $this->_recalcjournalbalance($entity['journal_account_id']);
                    }
                if ($this->is_income == true) {
                    $action = ACTION_DELETE_INCOME;
                } else {
                    if($expense['Expense']['is_mileage']){
                        $action = ACTION_DELETE_MILEAGE;
                    }else
                        $action = ACTION_DELETE_EXPENSE;
                }
                    $this->add_actionline($action, array('primary_id' => $expense['Expense']['id'], 'param1' => $expense['Expense']['amount'], 'param2' => $expense['Expense']['category'], 'param3' => $expense['Expense']['vendor'], 'param4' => $expense['Expense']['expense_number'], 'param6' => $expense['Expense']['date'] , 'param7' => $expense['Expense']['source_type'] . '#' . $expense['Expense']['source_id'] ));

                }
                
                izam_resolve(AttachmentsService::class)->removeAttachmentsByEntityDetails('expense',$id);

				if(IS_REST){
					$this->set("message", sprintf(__('%s %s been deleted', true), __($this->single_title,true), $verb));
					$this->render("success");
					return;
				} else {
					$this->flashMessage(sprintf(__('%s %s been deleted', true), __($this->single_title,true), $verb), 'Sucmessage');
					$this->redirect(array('controller' => $this->controller, 'action' => 'index'));
				}
            } else {
				if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
            }
        }
        $title_for_layout =  h(__('Delete expenses', true));
        if (isset($id) && !is_countable($id) && isset($expenses[0]) &&$expenses[0]['Expense']['is_mileage']) {
            $module_name = "mileage";
            $title_for_layout = "Delete mileage";
            $this->set('is_mileage',1);
        }

        $this->set('title_for_layout', $title_for_layout);
        $this->set('expenses', $expenses);
        $this->set('module_name', $module_name);
    }

    function owner_test_recurring($recurring_id)
    {
        $RecurringExpenses = $this->Expense->RecurringExpense->findById($recurring_id);
        $expense = $this->Expense->findById($RecurringExpenses['RecurringExpense']['last_generated_id']);
        unset($expense['Expense']['id']);
        unset($expense['Expense']['created']);
        unset($expense['Expense']['modified']);
        $this->Expense->create();
        $this->Expense->save($expense);

    }

    function cron($site_id = null) {

        $this->autoRender = $this->autoLayout = false;
        App::import('Vendor', 'AutoNumber');
        App::import('Vendor', 'Recurring');
        set_time_limit(360000);
        $this->autoRender = $this->autoLayout = false;
        $siteModel = ClassRegistry::init(array('class' => 'Site', 'ds' => 'portal'));

		$conditions = array('Site.status'=>SITE_STATUS_ACTIVE);

		if ($site_id) {
            $conditions['Site.id'] = $site_id;
        }
		$sites = $siteModel->find('all', array('conditions' => $conditions));

        $this->loadModel('Timezone');
        $this->loadModel('Expense');
        debug($sites);
	    IzamDatabaseServiceProvider::boot(getPDO(),getPortalConfig());
        foreach ($sites as $site) {
            $GLOBALS['site'] = $site['Site'];
            $config = json_decode($site['Site']['db_config'], true);
            try {
                ConnectionManager::getDataSource('default')->swtich_db($config);
                $this->Expense->getDataSource()->swtich_db($config);

	            IzamDatabaseServiceProvider::setSiteConnection(getPdo());
	            removePdo();

                $this->loadModel('Timezone');
                $zone = $this->Timezone->field('zone_name', array("Timezone.id" => $site['Site']['timezone']));
                date_default_timezone_set($zone);
                $currDate = date('Y-m-d');

                $RecurringExpenses = $this->Expense->RecurringExpense->find('all', array('conditions' => array('RecurringExpense.active' => 1, 'OR' => array('RecurringExpense.end_date' => '0000-00-00', 'RecurringExpense.end_date >= ' => $currDate, 'RecurringExpense.is_ended' => 0))));
                debug($RecurringExpenses);
                foreach ($RecurringExpenses as $RecurringExpense) {
                    if(isset($RecurringExpense['RecurringExpense']['branch_id'])){
                        setRequestCurrentBranch($RecurringExpense['RecurringExpense']['branch_id']);
                    }
                    $id = $RecurringExpense['RecurringExpense']['id'];
                    //   print_r($RecurringExpense);
                    $period = $RecurringExpense['RecurringExpense']['period_unit'];
                    $period_count = $RecurringExpense['RecurringExpense']['unit_count'];
                    $last_generated_date = $RecurringExpense['RecurringExpense']['last_generated_date'];

                    if (!strtotime($last_generated_date) or strtotime($last_generated_date) < 0) {
                        $Find = $this->Expense->query("SELECT max(date) as max_date  FROM `expenses` AS `Expense` WHERE `Expense`.`recurring_expense_id` = $id ORDER BY `Expense`.`id` DESC LIMIT 1", false);

                        $last_generated_date = $Find[0]['max_date'];
                    }
                    $this->Expense->RecurringExpense->id = $id;
                    if (!strtotime($last_generated_date)) {
                        $this->Expense->RecurringExpense->saveField("is_ended", 1, false);
                        continue;
                    }


                    $previous_dates = $this->Expense->find('list', array('fields' => array('Expense.id', 'Expense.date'), 'conditions' => array('Expense.recurring_expense_id' => $id)));


                    $fromDate = $last_generated_date;
                    $previous_dates[] = $fromDate;

                    $fromDate = Recurring::nextDate($period_count, $period, $fromDate, $previous_dates);

                    $count = 0;


                    if (strtotime($fromDate) > 0) {

                        while ($fromDate <= $currDate && (!strtotime($RecurringExpense['RecurringExpense']['end_date']) || strtotime($RecurringExpense['RecurringExpense']['end_date']) < 0 || $fromDate <= $RecurringExpense['RecurringExpense']['end_date'])) {

                            // echo $fromDate . "\n";

                            //  echo "\n";
                            $count++;
                            if ($count > 50) {
                                //echo "over 50 ...";

                                break;
                            }

                            $Find = $this->Expense->query("SELECT *  FROM `expenses` AS `Expense` WHERE `Expense`.`recurring_expense_id` = $id ORDER BY `Expense`.`id` DESC LIMIT 1", false);
                            debug($Find);
                            if (!$Find) {
                                break;
                            }

                            $Find[0]['Expense']['date'] = $fromDate;

                            $accountsData = json_decode($Find[0]['Expense']['accounts_data'],true);

                            if (!$Find[0]['Expense']['journal_account_id']) {
                                $Find[0]['Expense']['journal_account_id'] = $accountsData[0]['journal_account_id']??null;
                            }

                            $Find[0]['CostCenterTransaction'] = $accountsData;
                            $Find[0]['JournalTransaction'] = $accountsData;

                            $main_expense_number = $Find[0]['Expense']['expense_number'];
                            $Find[0]['Expense']['expense_number'] = \AutoNumber::get_auto_serial($Find[0]['Expense']['is_income'] ?  \AutoNumber::TYPE_INCOME : \AutoNumber::TYPE_EXPENSE);
                            $lastId = $Find[0]['Expense']['id'];
                            unset($Find[0]['Expense']['id']);
                            unset($Find[0]['Expense']['created']);
                            unset($Find[0]['Expense']['modified']);
                            App::import('Vendor', 'AutoNumber');
                            \AutoNumber::set_validate($Find[0]['Expense']['is_income'] ?  \AutoNumber::TYPE_INCOME : \AutoNumber::TYPE_EXPENSE);

                            $Find[0]['Expense']['default_expense_number'] = $Find[0]['Expense']['expense_number'] = \AutoNumber::get_auto_serial($Find[0]['Expense']['is_income'] ?  \AutoNumber::TYPE_INCOME : \AutoNumber::TYPE_EXPENSE);


                            $this->Expense->create();
                            $Find[0]['Expense']['is_income'] ? \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INCOME) : \AutoNumber::update_auto_serial(\AutoNumber::TYPE_EXPENSE);
                            // Disable mileage-related validation if the plugin is inactive 
                            // or the current expense is not marked as mileage
                            if (!ifPluginActive(MILEAGE_PLUGIN) || empty($Find[0]['Expense']['is_mileage'])) {
                                unset($this->Expense->validate['distance']);
                                unset($this->Expense->validate['rate']);
                                unset($this->Expense->validate['distance_unit']);
                            }
                            if(!$this->Expense->save($Find[0])){
                              //  break;
                                debug('save fail');
                                \Rollbar\Rollbar::log(\Rollbar\Payload\Level::WARNING, 'error in create RecurringExpense ', [
                                    'data' => $Find[0]['Expense'],
                                    'message' => $this->Expense->validationErrors,
                                    'site_id' => $site['Site']['id']
                                ]);
                                break;
                            }
                            $Find[0]['Expense']['id'] = $lastId;
                            $this->saveCostCenters($Find, $this->Expense->id);
                            if(!empty($Find[0]['JournalTransaction'])) {
                                $type = $Find[0]['Expense']['is_income'] ? 'income' : 'expense';
                                $filterByType = $type == "expense" ? 'debit' : 'credit';
                                $this->Expense->saveTransactionCostCenters($Find[0]['Expense']['id'], $Find[0], $type, $filterByType);
                            }
                            $this->saveExpenseAttachments();
                            $this->_recalcjournalbalance($Find[0]['Expense']['journal_account_id']);
                            $this->add_actionline($Find[0]['Expense']['is_income'] ? ACTION_ADD_RECURRING_INCOME : ACTION_ADD_RECURRING_EXPENSE, array('primary_id' => $this->Expense->id, 'param1' => $Find[0]['Expense']['amount'], 'param2' => $Find[0]['Expense']['category'], 'param3' => $Find[0]['Expense']['vendor'], 'param4' => $main_expense_number, 'param6' => $Find[0]['Expense']['date'], 'staff_id' => -2));
                            $this->Expense->RecurringExpense->saveField("last_generated_date", $fromDate, false);
                            $this->Expense->RecurringExpense->saveField("last_generated_id", $this->Expense->id, false);
                            $previous_dates[] = $fromDate;
                            $fromDate = Recurring::nextDate($period_count, $period, $fromDate, $previous_dates);
                        }
                    }
                    if (strtotime($RecurringExpense['RecurringExpense']['end_date']) > 0 && $fromDate >= $RecurringExpense['RecurringExpense']['end_date'])
                        $this->Expense->RecurringExpense->saveField("is_ended", 1, false);
                }
                Configure::delete('cron_branch_id');
            } catch (Throwable $exception) {
                Configure::delete('cron_branch_id');
                continue;
            }
        }
    }

    private function saveCostCenters($Find, $newId)
    {
        $type = $Find[0]['Expense']['is_income'] ? 'income' : 'expense';
        $filterByType = $type == "expense" ? 'debit' : 'credit';

        $this->loadModel('CostCenterTransaction');
        $journal = $this->Expense->get_entity_journal($Find[0]['Expense']['id'], ['entity_type' => $type]);
        $journal_id = $journal['Journal']['id'];

        $assigned_cost_centers = $this->CostCenterTransaction->getJournalCostTransaction($journal_id);
        $cost_centers = $assigned_cost_centers;
        $assigned_cost_centers = [];
        $journalTransactions = JournalTransactionCostCenterUpdater::filterJournalTransactionsByType($journal['JournalTransaction'], $filterByType);
        foreach ($journalTransactions as $transaction) {
            foreach ($cost_centers as $cost_center) {
                $cost_centers_list[] = $cost_center['CostCenter'];
                if ($cost_center['CostCenterTransaction']['journal_transaction_id'] == $transaction['id']) {
                    $assigned_cost_centers[$transaction['id']][] = $cost_center['CostCenterTransaction'];
                }
            }
        }

        if (empty($is_draft) && count($assigned_cost_centers)) {
            $this->loadModel('CostCenter');
            $Find[0]['Expense']['id'] = $newId;
            $Find[0]['CostCenterTransaction'] = array_map(function ($costCenterTransaction) {
                unset($costCenterTransaction['id']);
                return $costCenterTransaction;
            }, $assigned_cost_centers[$transaction['id']]);
            $this->CostCenter->save_model_transaction($Find[0], 'Expense', ['entity_type' => $Find[0]['Expense']['is_income'] ? 'income' :  'expense']);
        }
    }

    function owner_expenses_ajax() {
        require_once APP . 'vendors' . DS . 'Report.php';
        $owner = getAuthOwner();
        $dateFormats = getDateFormats('std');
        $format = $dateFormats[$owner['date_format']];

        if ($this->params['url']['default']) {

            $report = new Report('Expense', 'daily', $params = array('date_from' => format_date(date("Y-m-d", strtotime('-1 Month')))));
            die(json_encode($report->jsonAdapter()));
        }
    }

//    function owner_report() {
//
//        $owner = getAuthOwner();
//        $formats = getDateFormats('std');
//        $dateFormat = $formats[$owner['date_format']];
//        $this->set('owner', $owner);
//        $this->loadModel('Staff');
//        $staffs = $this->Staff->find('list');
//        $this->set('staffs', $staffs);
//        $this->set('clients', $this->Expense->Client->find('list', array('fields' => 'id,business_name')));
//        $url_params = $this->params['url'];
//
//        $conditions = array();
//        if (isset($url_params['staff_id']) and intval($url_params['staff_id']) != 0) {
//            $conditions['Expense.staff_id'] = $url_params['staff_id'];
//        }
//        if (isset($url_params['client_id']) and intval($url_params['client_id']) != 0) {
//            $conditions['Expense.client_id'] = $url_params['client_id'];
//        }
//        if (isset($url_params['date_from']) and $url_params['date_from'] != "") {
//            $date = DateTime::createFromFormat($dateFormat, $url_params['date_from']);
//            $conditions['Expense.date >= '] = $date->format('Y-m-d');
//        }
//        if (isset($url_params['date_to']) and $url_params['date_to'] != "") {
//            $date = DateTime::createFromFormat($dateFormat, $url_params['date_to']);
//            $conditions['Expense.date <= '] = $date->format('Y-m-d');
//        }
//        debug($conditions);
//        $group = array();
//        if (isset($url_params['group'])) {
//
//            switch ($url_params['group']) {
//                case 'staff':
//                    $group = "Expense.staff_id";
//                    break;
//                case 'project':
//                    $group = "Expense.vendor";
//                    break;
//                case 'date':
//                    $group = "Expense.category";
//                    break;
//                case 'client':
//                    $group = "Expense.client_id";
//                    break;
//            }
//        }
//
//        $Expenses = $this->Expense->find('all', array('fields' => 'Expense.client_id,Expense.staff_id,Expense.vendor,Expense.category,Expense.date,Expense.amount,Expense.note', 'conditions' => $conditions));
//
//        //   debug($Expenses);
//
//        if (isset($url_params['group'])) {
//            switch ($url_params['group']) {
//                case 'staff':
//                    foreach ($Expenses as $Expense) {
//
//                        $NewList[$Expense['Expense']['staff_id']][] = $Expense['Expense'];
//                    }
//                    debug($NewList);
//                    $this->set('data', $NewList);
//                    $this->render('owner_report_staff');
//                    break;
//                case 'category':
//                    foreach ($Expenses as $Expense) {
//
//                        $NewList[$Expense['Expense']['category']][] = $Expense['Expense'];
//                    }
//                    debug($NewList);
//                    $this->set('data', $NewList);
//                    $this->render('owner_report_category');
//                    break;
//                case 'vendor':
//                    foreach ($Expenses as $Expense) {
//
//                        $NewList[$Expense['Expense']['vendor']][] = $Expense['Expense'];
//                    }
//                    debug($NewList);
//                    $this->set('data', $NewList);
//                    $this->render('owner_report_vendor');
//                    break;
//                case 'client':
//                    foreach ($Expenses as $Expense) {
//
//                        $NewList[$Expense['Expense']['client_id']][] = $Expense['Expense'];
//                    }
//                    debug($NewList);
//                    $this->set('data', $NewList);
//                    $this->render('owner_report_client');
//                    break;
//            }
//        }
//    }


    function owner_report() {

        $this->reportType = 2;
        $owner = getAuthOwner();

        if ($this->RequestHandler->isAjax() || $this->RequestHandler->isFlash()) {
            $this->_owner_payments_ajax();
        }

        $this->loadModel('Expense');
        $treasuries = $this->Expense->Treasury->find('list');
        $this->set('treasuries', $treasuries);
        $this->set('vendors', $vendors = $this->Expense->getVendorsList($this->is_income));
        $categories = $this->Expense->getCategoriesList($this->is_income,false,true);

		$time_to_expense = settings::getValue ( 0  ,"time_to_expense" ) ;
		if( $time_to_expense ) {
			$this->loadModel ( 'Project');
			$projects = $this->Project->getList () ;
			$categories = array_merge ( $categories , $projects ) ;
		}


        $categories[] = "purchase_order" ;
        $this->set('categories', $categories);

        $this->_ownerSave();

        $this->set(array('type' => '', 'reportType' => ''));
        if (!empty($_GET['type'])) {
            $type = 'category';
            if (in_array($_GET['type'], array('category', 'expense', 'vendor', 'client', 'daily', 'weekly', 'monthly', 'yearly', 'lastmonth', 'lastyear', 'staff'))) {
                $type = $_GET['type'];
            }
            require_once APP . 'vendors' . DS . 'Report.php';
//            debug($type);
            $report = new Report('Expense', $type);
            $dataArray = $report->getDataArray();
            if ( $this->params['url']['ext'] == 'json'){
                echo json_encode ( $dataArray);
                die ;
            }
            $this->set($dataArray, false);

            $this->set('jsonParams', $report->jsonAdapter(), false);
        }
        $this->__init_form_data();
        $this->_settings();



        $this->set('clients', $this->Expense->Client->getClientsList($owner['id'], array('Client.id in (SELECT DISTINCT client_id FROM expenses WHERE ' . $this->is_income . ')')));
        $plugin = get_plugin_array();
        if (ifPluginActive(StaffPlugin, $plugin)) {
            $this->loadModel('Staff');
            $this->set('staffs', $this->Staff->getList());
        }

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true);
        $this->set('payment_methods', $paymentMethods);

        // start show one chart to display all currencies
        // for export ul items url
        $request_url = "owner/expenses/report";
        if ($this->is_income == 1) {
            $request_url = "owner/incomes/report";
        }
        $this->set("request_url", $request_url);

        if (isset($_GET['currency']) && $_GET['currency'] == -1) {

            $arr1 = array();

            $arr_rates = array();
            foreach ($this->viewVars['currencies'] as $key1 => $value1) {
                $arr_rates[$key1] = CurrencyConverter::index($key1, $owner["currency_code"],date('Y-m-d'));

            }

            if ($this->is_income == 0 && $_GET['type'] == "expense") {
            debug($this->viewVars['reportData']);
                foreach ($this->viewVars['reportData'] as $key1 => $value1) {

                    foreach ($value1 as $key2 => $value2) {

                        foreach ($value2 as $key3 => $value3) {

                            $value3["amount"] = ($value3["amount"] * $arr_rates[$value3["currency_code"]]);
                           if (trim($key2) != "") {
                                $arr1[$owner['currency_code']][$key2][] = $value3;
                           }
                        }
                        if (count($value2) == 0) {
                            $newarr["amount"] = 0;
                            $newarr["currency_code"] = $owner["currency_code"];
                            $arr1[$owner["currency_code"]][$key2][0] = $newarr;
                        }
                    }
                }
                debug($arr_rates);
                $this->set("reportCurrencies", array($owner['currency_code'] => $owner['currency_code']));
                $this->set("type", "expense");
                $this->set("currency_rates", $arr_rates);
                $this->set("reportData", $arr1);
            }
//                elseif ($this->is_income == 1 || $_GET['type'] != "expense")
            elseif ($this->is_income == 1 || $_GET['type'] == "expense") {
                foreach ($this->viewVars['reportData'] as $key1 => $value1) {

                    foreach ($value1 as $key2 => $value2) {
//                            debug($key2);
                        foreach ($value2 as $key3 => $value3) {
                            $value3["amount"] = ($value3["amount"] * $arr_rates[$value3["currency_code"]]);
                            $value3["currency_code"] = $owner['currency_code'];
                            $index_counter = 0;
                            if (isset($arr1[$owner['currency_code']][$key2])) {
                                $index_counter = count($arr1[$owner['currency_code']][$key2]);
                            }
                            $arr1[$owner['currency_code']][$key2][$index_counter] = $value3;
                            $index_counter++;
                        }

                        if (count($value2) == 0) {
                            $newarr["amount"] = 0;
                            $newarr["currency_code"] = $owner["currency_code"];
                            $arr[$owner["currency_code"]][$key2][0] = $newarr;
                        }
                    }
                }
                //debug($arr1);
                $this->set("reportCurrencies", array($owner['currency_code'] => $owner['currency_code']));

                $this->set("reportData", $arr1);
                $this->set("currency_rates", $arr_rates);
                //debug($arr1);
            }

            // set report title
            if ($type == "daily" || $type == "weekly" || $type == "monthly" || $type == "yearly") {
                $this->set("report_title", __(ucfirst($type) . ' ' . $this->multi_title, true));
            } elseif ($type == "category" || $type == "vendor" || $type == "client" || $type == "staff") {
                $this->set("report_title", __($this->multi_title . ' by ' . ucfirst($type), true));
            }
        }


        if (ifPluginActive(WorkOrderPlugin) ){
            $this->loadModel ( 'WorkOrder');
            $this->set ( 'work_order_ids' , array_merge (['' => __("All" , true ) ] , $this->WorkOrder->get_work_orders (['WorkOrder.status' => [WorkOrder::STATUS_OPEN , WorkOrder::STATUS_CLOSED ]]) ));
        }

        // end show one chart to display all currencies

        $this->set('title_for_layout',  __($this->multi_title, true) . ' - ' . __('Reports', true));

    }

    function _ownerSave() {
        if (!empty($this->data)) {


            $params = json_decode($this->data['SavedReport']['data'], true);
            if (!empty($params['date_from'])) {
                $params['date_from'] = $this->SavedReport->formatDate($params['date_from'], getCurrentSite('date_format'));
            }
            if (!empty($params['date_to'])) {
                $params['date_to'] = $this->SavedReport->formatDate($params['date_to'], getCurrentSite('date_format'));
            }
            $this->data['SavedReport']['data'] = json_encode($params);
            if ($this->SavedReport->save($this->data)) {

                $this->flashMessage(__('Report has been saved', true), 'Sucmessage');
                $this->redirect($_SERVER['REQUEST_URI']);
            } else {
                $this->flashMessage(__('Could not save report', true));
            }
        }

        $this->set('savedReports', $this->SavedReport->getSavedReportsList($this->reportType));
    }

    function owner_index_categories()
    {
        //get all expenses from the expenses table
        $expenses = $this->Expense->query("SELECT DISTINCT category,category as id from expenses as Expense WHERE category != \"\" ");

        $this->set('expenses', $expenses);

    }

    function owner_edit_categories($name)
    {
        if( ! empty($this->data) )
        {
            //die(debug($this->data));
            $original = $this->data['Categories']['original_category_name'];
            $replacement = $this->data['Categories']['Category'];
            $expenses = $this->Expense->find('all', array('conditions'=>array('Expense.category'=>$original)));
            foreach($expenses as $exp)
            {
                $exp['Expense']['category'] = $replacement;
                $this->Expense->save($exp);
            }
            $this->flashMessage(__('Category have been saved', true), 'Sucmessage');
            $this->redirect(array('controller' => $this->controller, 'action' => 'index_categories'));
        }

        $this->set("category_name", $name);
    }

    function owner_delete_categories($id = null)
    {
        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty ($_POST)) {
            $this->flashMessage(sprintf (__('Invalid id for %s', true), __('category',true)));
            $this->redirect(array('controller'=> 'expenses', 'action'=>'index'));
        }

        $categories = $this->Expense->find('all',
            array(
                'conditions'=>array('Expense.category'=>$id ) ,
                'fields' => array('DISTINCT Expense.category')
            ));

        if(!empty($_POST['submit_btn']) && !empty($_POST['ids']))
        {
            if($_POST['submit_btn'] == 'yes' )
            {
                $expenses = $this->Expense->find('all', array('conditions'=>array('Expense.category'=>$id)));
                foreach($expenses as $exp)
                {
                    $exp['Expense']['category'] = "";
                    $this->Expense->save($exp);
                }
                $this->flashMessage(__('Category have been removed successfully', true), 'Sucmessage');
                $this->redirect(array('controller' => $this->controller, 'action' => 'index_categories'));
            }
            else if($_POST['submit_btn'] == 'no')
            {
                $this->redirect(array('controller' => $this->controller, 'action' => 'index_categories'));
            }
        }

        $this->set('categories' , $categories);

    }
        function _settings() {

        $this->loadModel('Invoice');
        $owner = getAuthOwner();
        $Currencylist = $this->Invoice->getCurrencylist();

        $this->loadModel('Expense');
        foreach ($this->Expense->getCurrencylist() as $ExpenseCurrency) {
            if(!in_array($ExpenseCurrency, $Currencylist)){
            $Currencylist[] = $ExpenseCurrency;
            }
        }

        $this->loadModel('PurchaseOrder');
        foreach ($this->PurchaseOrder->getCurrencylist() as $PurchaseOrderCurrency) {
            if(!in_array($PurchaseOrderCurrency, $Currencylist)){
            $Currencylist[] = $PurchaseOrderCurrency;
            }
        }

//        debug($Currencylist);
        if (count($Currencylist) > 0) {
            $currencies = getCurrenciesList(array('Currency.code' => $Currencylist));
        } else {
            $currencies = array();
        }
        $this->set('currencies', $currencies);
        $this->set('owner', $owner);
    }

	function owner_upload_file() {
        if (!empty($this->data)) {

			$ext=strtolower(end(explode('.', $this->data['Expense']['file']['name'])));

			$filename = uniqid().'.'.$ext;
            $file_path= sys_get_temp_dir().DS. $filename;
            $this->loadModel('TempFile');
            $result=$this->TempFile->createFile($this->data['Expense']['file']);
            if($result['status']==true){
                $ext=strtolower(end(explode('.', $result['data']['file_name'])));
                $file_size=$result['data']['file_size'];
                $file_name=$result['data']['file_name'];
                $file_path=$result['data']['file_name_system_full_path'];
                $temp_id=$result['data']['id'];
            }else{
                die(json_encode(array()));
            }

				//move_uploaded_file($this->data['Expense']['file']["tmp_name"], $file_path);

                                if($file_size>1048576){
                                 $file_content=null;
                                }else{
                                $file_content=base64_encode(file_get_contents($file_path));
                                }
                    die(json_encode(array('id'=>$temp_id,'ext'=>$ext,'size'=> round($file_size/1024,2), 'status' => true, 'filename' =>$file_name,'file_content' => $file_content) ));


        }else{
            die(json_encode(array()));
        }

    }

    /**
     * gets the expenses related to current recurring_id
     */
    function getRecurringExpenses($RecurringID = null)
    {
        $expenses = $this->Expense->find('all',['conditions'=>['Expense.recurring_expense_id'=>$RecurringID],'orders'=>['Expense.date ASC']]);
        die( json_encode( $expenses ) ) ;
    }

    /**
     * reverse the value of active in the passed recurring
     */
    function owner_deactivate_recurring($recurringID)
    {
        $this->LoadModel('RecurringExpense');
        $RecurringExpense = $this->RecurringExpense->find(array('RecurringExpense.id' => $recurringID) ) ;

        $controller_name = ( $RecurringExpense['Expense'][0]['is_income'] ? 'incomes' : 'expenses' );


        $status = $RecurringExpense['RecurringExpense']['active']=="1"?'Recurring  has been deactivated':'Recurring  has been active';
        $RecurringExpense['RecurringExpense']['active'] = $RecurringExpense['RecurringExpense']['active']=="1"?0:1;


        $this->RecurringExpense->save( $RecurringExpense );

        $this->flashMessage(sprintf(__('%s', true), __($status,true)), 'Sucmessage');
        $this->redirect(array('controller'=>$controller_name,'owner'=>true,'action' => 'index','?'=>[ 'recurring_expense_id' => $recurringID ] ));
    }

    function total_formeted($amount)
    {
        die( format_price($amount) ) ;
    }
 

    
	function owner_view($id=null)
    {
        $this->Expense->bindAttachmentRelation('expense');
        $site = getAuthOwner();
        $this->set("default_currency", $site['currency_code']);
        $this->set('is_income', $this->is_income);
        $conditions = ['Expense.is_income' => $this->is_income, 'Expense.id' => $id];
        $alias = $this->is_income ? 'Income' : ($this->is_mileage?"Mileage":'Expense');
        $this->pageTitle= $this->is_income ? __('Incomes',true) : __('Expenses',true);
        $this->set('alias', $alias);
        if ($site['staff_id'] != 0) {
            if (!check_permission($this->permissions['View_his_own'])  && !check_permission($this->permissions['Edit_delete_his_own']) && !check_permission($this->permissions['View_All'])) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
            if (!check_permission($this->permissions['View_All']) && !check_permission($this->permissions['Edit_Delete_all'])) {
                $conditions['Expense.staff_id'] = $site['staff_id'];
            }
        }
        
        $this->Expense->bindModel(['belongsTo' => ['Supplier' => ['className' => 'Supplier', 'recursive' => -1]]]);
        $expnese = $this->Expense->find('first', ['conditions' => $conditions]);
        if($expnese['Expense']['is_income'] && !$this->is_income)
        {
            $this->redirect(Router::url(['controller' => 'incomes', 'action' => 'view', $id]));
        }else if(!$expnese['Expense']['is_income'] && $this->is_income)
        {
            $this->redirect(Router::url(['controller' => 'expenses', 'action' => 'view', $id]));

        }else if($expnese['Expense']['is_mileage'] && !$this->is_mileage)
        {
            $this->redirect(Router::url(['controller' => 'expenses', 'action' => 'view', $id,'?' => ['is_mileage' => '1']]));
        }
        if (!$expnese) {
            $this->flashMessage(sprintf(__('%s not found', true), __($alias, true)));
           // $this->redirect($this->referer(array('action' => 'index'), true));
            $this->redirect(array('controller' => $this->controller, 'action' => 'index'));
        }

       
        if(!empty($expnese['Expense']['source_id']))
        {

            App::import('vendor','ExpenseV',['file'=>'Expenses/autoload.php']);
            $expenseSource = \ExpenseV\Sources\ExpenseSourceAbstract::CreateExpenseSource($expnese['Expense']['source_type'], $expnese['Expense']['source_id']);
            $this->set('expenseSource', $expenseSource);
        }

        if (!empty($expnese['RecurringExpense']['id'])) {
            $firstRecurring = $this->Expense->getFirstRecurring($expnese['RecurringExpense']['id']);
            $this->set('firstRecurring', $firstRecurring);
            $lastRecurring = $this->Expense->getLastRecurring($expnese['RecurringExpense']['id']);
            $this->set('lastRecurring', $lastRecurring);
            $recurringCount = $this->Expense->getRecurringGeneratedCount($expnese['RecurringExpense']['id']);
            $this->set('recurringCount', $recurringCount);
        }

        $accounts = json_decode($expnese['Expense']['accounts_data'], true);
        $accounts = array_filter($accounts ?? [], function ($item) {
            return !empty($item['amount']);
        });
        $this->loadModel('JournalAccount');
        foreach ($accounts as &$account) {
            if ( empty($account["included"])) {
                $account['tax_value']= ( (float) $account['percent'] / 100) * (float)  $account['amount'];
            } else {
                $account['tax_value'] = ( (float) $account['amount'] / (100 + (float) $account['percent'])) * (float) $account['percent'];
            }
            $account['journal_account'] = $this->JournalAccount->find(['JournalAccount.id' => $account['journal_account_id']]);
        }
        $expnese['Expense']['accounts_data'] = json_encode($accounts);
//        dump($expnese);
        $expnese=modifyArray($expnese);
//        dd($expnese);
        if(isset($expnese['Vehicle']['photo'])){
            $this->loadModel("FileModel");
            $expnese['Vehicle']['File'] = $this->FileModel->find("first", ['conditions' => ["id" => $expnese['Vehicle']['photo']]])['FileModel'];
        }
        $this->set('expense', $expnese);
        $PT = GetObjectOrLoadModel('PrintableTemplate');
        $default_templates = $PT->getDefaultTemplateForType(($this->is_income ? 'income' : 'expense'));
        foreach ($default_templates as $k => $default_template) {
            if ($default_template['PrintableTemplate']['language'] == $this->lang) {
                $this->set('default_template', $default_template);
            }
        }

        $this->set_journal($id,$this->is_income ? 'income' : 'expense');
        $this->set('file_settings', $this->Expense->getFileSettings());

        //timeline area
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Expense', array('primary_id' => $id));
        $this->loadModel('ActionLine');
        if ($this->is_income)
            $action_list = $timeline->getIncomeActionsList();
        else
            {
                if($this->is_mileage){
                    $action_list = $timeline->getMileageActionsList();

                }else{
                    $action_list = $timeline->getExpenseActionsList();
                }
            }

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $expnese_actions[$key] = $action;
            }
        }
        $this->set('expense_id', $id);
        $this->set('actions', $expnese_actions);


        //printableTemplatesSection
        //get the list of printable templates for this expenses
        $this->loadModel('PrintableTemplate');
        if ($this->is_income == 0) {
            $entityKey = 'expense';
            $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('expense', false);
        } else {
            $entityKey = 'income';
            $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('income', false);
        }

        $this->set('has_templates', false);

        if (!empty($printableTemplates)) {
            $this->set('has_templates', true);
            $this->set(compact('printableTemplates'));

            if (empty($default_templates)) {
                foreach ($printableTemplates as $k => $printable_template) {
                    if ($printable_template['PrintableTemplate']['default_template']) {
                        $this->set('default_template', $printable_template);
                    }
                }
            }else{
                foreach ($default_templates as $k => $printable_template) {
                    if ($printable_template[$k]['PrintableTemplate']['default_template']) {
                        $this->set('default_template', $printable_template[$k]);
                    }
                }
            }
        }

        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates($entityKey);

        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('title_for_layout',  sprintf(__("$alias #%s", true), $expnese['Expense']['expense_number']));
        $this->set('taxes', $this->Expense->getExpenseTaxes());
        $this->set('view_templates', $viewTemplates);
    }

    public function getPrintableTemplates()
    {
        //load template in the same way in PrintableTemplates index controller , to get active templates
        // daly  and ashraf said get only active template in index
        $type = $this->is_income == 1 ? 'income' : 'expense';
        $this->loadModel('PrintableTemplate');
        $allTemplates=[];
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesForDashboard($type);
        foreach ($printableTemplates as $template) {
            if ($template['PrintableTemplate']['for_galary_only'] == 0) {
                $allTemplates[] = $template;
            }
        }

        return $allTemplates;
    }

	public function api_view($id = null) {
		$site = getAuthOwner();
		$conditions = array("{$this->single_title}.id" => $id, "{$this->single_title}.is_income"=>$this->is_income);
        if ($site['staff_id'] != 0) {
            if (!check_permission($this->permissions['View_his_own']) && !check_permission($this->permissions['Add_New']) && !check_permission($this->permissions['Edit_delete_his_own'])) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
			if (!check_permission($this->permissions['View_All']) && !check_permission($this->permissions['Edit_Delete_all'])) {
                $conditions['Expense.staff_id'] = $site['staff_id'];
            }
        }
		$this->Expense->alias = $this->single_title;
		$expense = $this->Expense->find("first", ["conditions" => $conditions] );
		$expense['Expense']['attachmnets'] = $this->prepareS3Attachments($expense['Attachments']);
        
        if(empty($expense)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __($this->single_title, true))));
        else $this->set('rest_item', $expense);
		$this->set('rest_model_name', $this->single_title);
		$this->render("view");
	}

		function _recalcjournalbalance($id = null){
				$this->loadModel('JournalAccount');
				$ja=$this->JournalAccount->find(array('JournalAccount.id'=>$id));
				if($ja['JournalAccount']['entity_type']=="client"){
				$this->loadModel('Client');
				$this->Client->adjust_and_pay($ja['JournalAccount']['entity_id']);
				}elseif($ja['JournalAccount']['entity_type']=="supplier"){
				$this->loadModel('Supplier');
				$this->Supplier->adjust_and_pay($ja['JournalAccount']['entity_id']);
				}
	}

    function owner_timeline($id = false, $is_income = false) {
        $this->set('expense_id', $id);
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }


        $expense = $this->Expense->findById($id);
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Expense', array('primary_id' => $id));
        if ($expense['Expense']['is_income'] ) {
            $action_list = $timeline->getIncomeActionsList();
        } else {
            {
                if($expense['Expense']['is_mileage']){
                    $action_list = $timeline->getMileageActionsList();
                }else{
                    $action_list = $timeline->getExpenseActionsList();
                }
            }
        }
        $timeline->init(array('primary_id' => $id), $action_list);

        $this->set('data', $timeline->getDataArray());

        $this->loadModel('ActionLine');
        $itemActions = [];
        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $itemActions[$key] = $action;
            }
        }
        $this->set('actions', $itemActions);

//$this->set('jsonParams', $timeline->jsonAdapter(), false);
    }

    function owner_timeline_row($id = null) {
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Expense', array('primary_id' => $id));
        $this->set('data', $timeline->getDataArray());
        echo $timeline->view_action($id);
        die();
    }

    function checkCostCentersValidity($data)
    {
        if(isset($data['CostCenterTransaction']))
        {
            //dont judge me its all fucked up they didnt allow me to fix it
            $percentage = 0;
            foreach ($data['CostCenterTransaction'] as $transaction)
            {
                // Cat percentage to double in case its string type php8 type error
                $percentage += (double) $transaction['percentage']; 
                if($percentage > (double) 100.0 + 0.009)
                {
                    $this->flashMessage(__("Cost Center percent is more then 100%", true), 'Errormessage', 'secondaryMessage');
                    return false;
                }
            }
        }
        return true;
    }

    private function prepareS3Attachments($attachments)
    {
        $start = microtime(true);
        $tmpAttachments = [];
        foreach ($attachments as $attachment) {
            $attachment['site_id'] = getCurrentSite('id'); 
            RollbarLogService::logTempFilesToRollbar($attachment['is_temp'],$attachment);
            $awsService = new Aws;
            $tmpAttachments[] = $awsService->getProxyUrl($attachment['path']);
        }
        $time_elapsed_secs = microtime(true) - $start;
        debug('s3 measure ' . $time_elapsed_secs);
        return $tmpAttachments;
    }

    function owner_import()
    {
        $breadCrumbs = [];
        if ($this->is_income) {
            $breadCrumbs[] = [
                'title' => __("Incomes", true), 'link' => '/owner/incomes/index'
            ];
        } else {
            $breadCrumbs[] = [
                'title' => __("Expenses", true), 'link' => '/owner/expenses/index'
            ];
        }
        $sampleFile = ['name' => ($this->is_income? 'Incomes' : 'Expenses') . '-sample.csv', 'path' => '/samples/' . ($this->is_income? 'incomes' : 'expenses') . '-sample.csv'];
        $this->set('sample_file', $sampleFile);
        $breadCrumbs[] = ['title' => __("Import", true), 'link' => '#'];
        $this->set('_PageBreadCrumbs', $breadCrumbs);
        return parent::owner_import();
    }

}
