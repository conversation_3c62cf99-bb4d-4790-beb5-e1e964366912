<?php

namespace ReportV2;

class InventoryRequistion extends Base
{

    function __construct($name, $params = null)
    {
        if (is_array($_GET['client']) && count($_GET['client']) == 1 && empty($_GET['client'][0])) {
            unset($_GET['client']);
        }
        GetObjectOrLoadModel('Category');
        GetObjectOrLoadModel('Product');
        $this->report = [
            "modelName" => "clients",
            "title" => "Inventory Consumption for Invoices and Returns",
            "table" => "clients C",
            "alias" => "C",
            "fields" => [
                "id" => [
                    "query" => "P.id",
                    "type" => ""
                ],
                "product_code" => [
                    "query" => "P.product_code",
                    "type" => ""
                ],
                "name" => [
                    "query" => "P.name",
                    "type" => ""
                ],
                "business_name" => [
                    "query" => "C.business_name",
                    "type" => ""
                ],
                "inv_total" => [
                    "query" => "COALESCE(INV.inv_total, 0) AS inv_total",
                    "type" => ""
                ],
                "req_total" => [
                    "query" => "COALESCE(REQ.req_total, 0) AS req_total",
                    "type" => ""
                ],
                "inv_refund" => [
                    "query" => "COALESCE(INV.inv_refund, 0) AS inv_refund",
                    "type" => ""
                ],
                "req_refund_total" => [
                    "query" => "COALESCE(REQ.req_refund_total, 0) AS req_refund_total",
                    "type" => ""
                ],
            ],
            "display_fields" => [
                "product_code" => "Product Code",
                "name" => "Product Name",
                "business_name" => "Client Name",
                "inv_total" => "Invoices Total",
                "req_total" => "Requisitions Total",
                "diff_total" => "Diffrence",
                "inv_refund" => "Refunded Invoices Total",
                "req_refund_total" => "Refunded Requisitions Total",
                "diff_refund_total" => "Diffrence",
                "dif_all" => "Total Diffrence"
            ],
            "allow_removed" => false,
            "removed_text" => "[REMOVED]",
            "group_fields" => [
                "inv_total" => "SUM",
                "req_total" => "SUM",
                "diff_total" => "SUM",
                "inv_refund" => "SUM",
                "req_refund_total" => "SUM",
                "diff_refund_total" => "SUM",
                "dif_all" => "SUM"
            ],
            // "total_fields" => [
            //     "inv_total" => "SUM",
            //     "req_total" => "SUM",
            //     "diff_total" => "SUM",
            //     "inv_refund" => "SUM",
            //     "req_refund_total" => "SUM",
            //     "diff_refund_total" => "SUM",
            //     "dif_all" => "SUM"
            // ],
            "filters" => [
                'product' => [
                    'type' => 'select',
                    'input' => [
                        'id' => 'product',
                        'name' => 'product',
                        'type' => 'select',
                        'label' => __('Product', true),
                        'class' => 'input-select form-control',
                        'data-live-search' => true,
                        'div' => 'form-group col-md-3',
                        'multiple' => 'multiple',
                        'dynamic_options' => [
                            'model' => 'Product',
                            'fields' => [
                                'Product.id',
                                'Product.name'
                            ]
                        ]
                    ],
                    'condition' => "P.id IN (\$product)"
                ],
                "product_code" => [
                    "type" => "text",
                    "input" => [
                        "name" => "product_code",
                        "type" => "text",
                        "label" => "Product Code",
                        "class" => "form-control",
                        "div" => "col-md-3"
                    ],
                    "condition" => "P.product_code LIKE '%\$product_code%'"
                ],
                'category' => [
                    'type' => 'select',
                    'input' => [
                        'label' => 'Category',
                        'name' => 'category',
                        'multiple' => 'multiple',
                        'data-live-search' => true,
                        'class' => 'form-control input-select',
                        'div' => 'form-group col-md-3',
                        'dynamic_options' => ['model' => 'Category', 'conditions' => ['Category.category_type' => \Category::CATEGORY_TYPE_PRODUCT], 'fields' => ['Category.id', 'Category.name']]
                    ],
                    'condition' => "P.id in(select item_id from items_categories where category_id in (\$category) and item_type=" . \Category::CATEGORY_TYPE_PRODUCT . ")"
                ],
                'client' => [
                    'type' => 'element',
                    'element' => [
                        'name' => 'advanced_client',
                        'options' => [
                            'multiple' => 'multiple',
                            'label' => __('Client', true),
                            'empty' => __('All', true),
                            'width' => '100%',
                            'input_name' => 'client',
                            'class' => 'form-control input-select',
                            'div' => 'form-group col-md-3',
                            'selected_client_id' => $_GET['client'],
                        ]
                    ],
                    'condition' => !empty($_GET['client']) ? "C.id IN (\$client)" : "",
                ],
            ],
            "joins" => [
                "LEFT JOIN (SELECT I.client_id, II.product_id, SUM(CASE WHEN I.type = 0 THEN II.quantity ELSE 0 END) AS inv_total, SUM(CASE WHEN I.type IN (5,6) THEN II.quantity ELSE 0 END) AS inv_refund FROM invoices I JOIN invoice_items II ON I.id = II.invoice_id GROUP BY I.client_id, II.product_id) AS INV ON INV.client_id = C.id",
                "LEFT JOIN (SELECT I.client_id, RI.product_id, SUM(CASE WHEN R.type = 2 AND R.status = 3 THEN RI.quantity ELSE 0 END) AS req_total, SUM(CASE WHEN R.type = 1 AND R.status = 3 THEN RI.quantity ELSE 0 END) AS req_refund_total FROM requisitions R JOIN requisition_items RI ON R.id = RI.requisition_id JOIN invoices I ON R.order_id = I.id AND R.order_type IN (3, 4, 5) GROUP BY I.client_id, RI.product_id) AS REQ ON REQ.client_id = C.id AND REQ.product_id = INV.product_id",
                "LEFT JOIN products P ON P.id = INV.product_id OR P.id = REQ.product_id"
            ],
            "conditions" => [
                "P.type != " . \Product::SERVICE_TYPE,
            ],
            "group_by" => [
                "Product" => "C.id, P.id"
            ],
            "has_summary" => false,
            "order_by" => [],
            "default_group_by" => "C.id, P.id"
        ];
        parent::__construct($name, $params);
    }


    protected function get_fields($group_by = null)
    {
        $fields = [];
        foreach ($this->report['fields'] as $k => $v) {
            if (is_array($v)) {
                if (!empty($v['query'])) $fields[] = $v['query'];
            } else {
                $fields[] = $v;
            }
        }

        $fields[] = 'P.name as group_by_field';

        return implode(',', $fields);
    }

    function process_query_data($query_data)
    {
        foreach ($query_data as &$row) {
            $row[0]['diff_total'] = $row[0]['inv_total'] - $row[0]['req_total'];
            $row[0]['diff_refund_total'] = $row[0]['inv_refund'] - $row[0]['req_refund_total'];
            $row[0]['dif_all'] = $row[0]['diff_total'] - $row[0]['diff_refund_total'];
        }
        return $query_data;
    }
}
