<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 05/11/19
 * Time: 11:21 ص
 */

namespace ReportV2;

use Izam\Daftra\Common\Utils\InvoiceSourceTypesUtil;
use Izam\Daftra\Common\Utils\PosShiftStatusUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Daftra\Common\Utils\StockTransactionSourceTyeUtil;
use Izam\Daftra\Common\Utils\StockTransactionUtil;
use Izam\Daftra\Common\Utils\StoreStatusUtil;
use Izam\Daftra\Common\Utils\ProductStatusUtil;

\App::import('Vendor', 'Settings', array('file' => 'settings.php'));

class WareHouseStockBalance extends Base
{
    private $Product;
    private bool $showPendingStock;

    public function __construct($name, $params = null)
    {
        $this->Product = GetObjectOrLoadModel('Product');
        GetObjectOrLoadModel('Category');
        GetObjectOrLoadModel('StockTransaction');
        $productCategoryType = \Category::CATEGORY_TYPE_PRODUCT;
        $this->showOnHandAndAvailableStock();

        $productFilterCondition = "P.id= \$product ";
        $groupBy = 'Product'; 
        if (substr($_GET['product']??'', 0, 1) === 'g') {// if the product is item group record
            $itemGroupId = intval(str_replace("g","",$_GET['product']));
            $productFilterCondition = "P.item_group_id= ".$itemGroupId;
            $groupBy = 'Item Group'; 
        }

        $storeFilterCondition = "";

        if(isset($_GET['warehouse']) && !empty($_GET['warehouse'])){
            $storeCondition = implode(',', $_GET['warehouse']);
            $storeFilterCondition = " AND stock_transactions.store_id in({$storeCondition}) ";
        }

        if(getAuthOwner("staff_id") != 0 && !($params['warehouse']))
        {
            $storeFilterCondition = ' AND stock_transactions.store_id IN (' . implode(', ', array_keys($this->getWarehouses())) . ')';
        }


        $this->report = [
            'modelName' => 'StockTransaction',
            'title' => 'Summary Of Stock Balance',
            'table' => 'products P',
            'fields' => [
                'product_code' => ['query' => '`P`.`product_code`'],
                'status' => ['query' => 'P.status'],
                'track_stock' => ['query' => 'P.track_stock'],
                'low_stock_thershold' => ['query' => 'P.low_stock_thershold'],
                'id' => ['query' => '`P`.`id`'],
                'name' => ['query' => '`P`.`name`'],
                'brand' => ['query' => '`P`.`brand`'],
                'quantity' => ['query' => 'IF(P.track_stock = 0, 0, COALESCE(ST.quantity, 0)) AS quantity'],
                'category' => ['query' => '(SELECT GROUP_CONCAT(name) as name FROM categories CAT LEFT JOIN items_categories I ON I.category_id = CAT.id WHERE I.item_id = P.id AND I.item_type = 1) category'],
                'unit_small_name' => ['query' => '`UT`.`unit_small_name`'],
                'unit_factor' => ['query' => '`ST`.`unit_factor`'],
                'store_name' => ['query' => '`S`.`name` store_name'],
                'product_balance' => ['query' => 'product_balance.total_quantity'],
                
            ],
            'display_fields' => [
                'product_code' => 'Code',
                'name' => "Name",
                'category' => 'Category',
                'brand' => 'Brand',
            ],
            'allow_removed' => true,
            'removed_text' => ' ',
            'filters' => [
                'product' => [
                    'type' => 'element',
                    'element' => [
                        'name' => 'advanced_product',
                        'options' => [
                            'label' => __('Product', true) ,
                            'empty' => __('All', true),
                            'width' => '100%',
                            'input_name' => 'product',
                            'class' => 'form-control input-select',
                            'div' => 'form-group col-md-3',
                            'selected_product_id' => $_GET['product'],
                            'include_item_groups' => 1
                        ]
                    ],
                    'condition' => $productFilterCondition,
                ],
                'brand_id' => [
                    'type' => 'element',
                    'element' => [
                        'name' => 'products/product_brand',
                        'options' => [
                            'input_name' => 'brand',
                            'multiple' => false,
                            'empty' => sprintf(__('Select %s', true), __('brand', true)),
                            'label' => 'Brand' ,
                            'name' => 'brand',
                        ]
                    ],
                    'condition' => "P.brand_id ='\$brand_id' "
                ],


                'category' => [
                    'type' => 'element',
                    'element' => [
                        'name' => 'ajax_search/categories',
                        'options' => [
                            'options' => [
                                'input_name' => 'category',
                                'multiple' => false,
                                'empty' => sprintf(__('Select %s', true), __('Category', true)),
                                'label' => 'Category' ,
                                'name' => 'category',
                                'div' => 'form-group col-flex-md-3',
                            ],
                            'value' => $_GET['category'] ?? null,
                        ]
                    ],
                    'condition' => "P.id in(select item_id from items_categories where category_id = \$category and item_type=$productCategoryType)"
                ],
                'warehouse' => [ 'type' => 'select',
                    'input' => [
                        'multiple' => 'multiple',
//                        'empty' => sprintf(__('Select %s', true), __('Warehouse', true)),
                        'label' => 'Warehouse' ,
                        'name' => 'warehouse',
                        'data-live-search' => true,
                        'class' => 'form-control input-select',
                        'div' => 'form-group col-md-3',
                        'options' =>  $this->getWarehouses()],
                        //'dynamic_options' => ['model' => 'Store', 'conditions' => ['Store.active '=>[StoreStatusUtil::ACTIVE,StoreStatusUtil::SUSPEND]], 'fields' => ['Store.id', 'Store.name']]],
                    'condition' => "S.id in(\$warehouse) " ],
                'status' => [ 'type' => 'select',
                    'input' => [
                        'label' => 'Status' ,
                        'name' => 'status',
                        'class' => 'form-control input-select',
                        'div' => 'form-group col-md-3',
                        'options' => ['' => __('All', true), 1 => __('Available', true), 2 => __('Low Stock', true), 3 => __('Out of Stock', true), 4 => __('Inactive', true)],
                        'condition' => "" ]
                    ]
            ],
            'joins' => [
                'LEFT JOIN (SELECT
                        product_id,
                        store_id,
                        IF(ROUND(SUM(`quantity`), 6) = 0, 
                                            ABS(ROUND(SUM(`quantity`), 2)), 
                                            ROUND(SUM(`quantity`), 2)) AS quantity,
                        unit_factor,
                        ignored,
                        status
                    FROM
                        stock_transactions
                    WHERE
                        (status ='.\StockTransaction::STATUS_PROCESSED. ( $this->showPendingStock ? $this->appendPendingTransactionsQuery('conditions')['status']  : '') . ' OR status IS NULL )
                        AND (
                            ignored = 0
                            OR ignored IS NULL
                        )
                        '.$storeFilterCondition.'
                    GROUP BY
                        product_id,
                        store_id
                ) AS ST ON P.id = ST.product_id',
                'LEFT join stores S on S.id = ST.store_id',
                'LEFT join items_categories IC on IC.item_id = P.id and IC.item_type = 1 ',
                'LEFT join categories C on C.id = IC.category_id',
                'LEFT join unit_templates UT on UT.id = P.unit_template_id',
                'LEFT join (SELECT
                    product_id,
                    store_id,
                    SUM(quantity) as total_quantity
                    FROM
                        stock_transactions
                    where 
                         ( stock_transactions.status ='.\StockTransaction::STATUS_PROCESSED.' OR stock_transactions.status IS NULL ) 
                      and (stock_transactions.ignored = 0 OR stock_transactions.ignored is null)
                      '.$storeFilterCondition.'
                        group by product_id,store_id
                    ) 
                    
                    AS product_balance  ON
                    product_balance.product_id = P.id AND product_balance.store_id = S.id
                    
                    '
                    
                    
                    
            ],
            'group_by' => [
                $groupBy => 'NULL',
            ],
            'default_group_by' => 'P.id,S.name',
            'has_summary' => false,
            'has_details' => true,
            'order_by' => [
            ],
            'graphs' => [],
            'conditions' => [
                'P.type <> 2',
            ]
        ];
        if(ifPluginActive(BranchesPlugin) && !\settings::getValue(BranchesPlugin, 'share_products')) {
            $this->report['conditions'][] = 'branches.status !=2';
            $this->report['joins'][] = 'LEFT join branches  on P.branch_id = branches.id ';
        }
        if ($this->showPendingStock) {
            $this->report['fields'] = array_merge($this->report['fields'], $this->appendPendingTransactionsQuery('fields'));
            $this->report['joins'] = array_merge($this->report['joins'], $this->appendPendingTransactionsQuery('joins'));
        }
        parent::__construct($name, $params);
    }

    function get_conditions()
    {
        $conditions = parent::get_conditions(); // TODO: Change the autogenerated stub
        if(isset($this->params['status'])) {
            $status = $this->params['status'];
            if ($status == 1){
                $conditions['where'][] = " ((P.track_stock IS NULL OR P.track_stock = 0) OR (product_balance.total_quantity > 0 ) and (P.status IS NULL OR P.status != ". ProductStatusUtil::STATUS_INACTIVE ." )) ";
                $conditions['having'][] = "  quantity <> 0 ";
            }else if ($status == 2){
                $conditions['where'][] = '( (P.track_stock = 1 AND  P.track_stock  IS NOT NULL  AND  product_balance.total_quantity  > 0 AND  product_balance.total_quantity  <= P.low_stock_thershold AND  P.low_stock_thershold IS NOT NULL ) and product_balance.total_quantity  <= P.low_stock_thershold  and (P.status IS NULL OR P.status != '. ProductStatusUtil::STATUS_INACTIVE ." )) ";
            }else if ($status == 3){
                $conditions['where'][] = '(product_balance.total_quantity <= 0 and ( P.status != '. ProductStatusUtil::STATUS_INACTIVE .'  OR `P`.`status` IS NULL ) )';
            }else if ($status == 4){
                $conditions['where'][] = '(P.status = '. ProductStatusUtil::STATUS_INACTIVE .'  AND P.status  IS NOT NULL )';
            }
        }
        return $conditions;
    }

    function process_query_data($query_data)
    {
        $productGroupedData = [];
        $storesArray = [];
        if($this->params['warehouse'])
        {
            $Store = GetObjectOrLoadModel('Store');
            $selectedStores = $Store->find('list', ['fields' => ['Store.name', 'Store.name'],'conditions' => ['Store.id' => $this->params['warehouse']]]);
            $storesArray = [];
            foreach ($selectedStores as $store)
            {
                $storesArray[$store] = 0;
            }
        }

        $stores = $selectedStores;
        foreach ($query_data as $row)
        {
            $productId = $row['P']['id'];
            $storeName = $row['S']['store_name'];
            if(empty($productId))
            {
                continue;
            }
            if(!isset($productGroupedData[$productId]))
            {
                $productGroupedData[$productId] = $row;
                $productGroupedData[$productId]['P']['total']  = 0;
                $productGroupedData[$productId]['P'] = array_merge($productGroupedData[$productId]['P'], $storesArray);
            }
            if ($this->showPendingStock) {
                $this->assignPendingTransactionsData($row, $productGroupedData);
            }else{
                $this->assignTransactionsData($row, $productGroupedData);
            }
            $stores[$storeName] = $storeName;
        }
        $this->totalNumberFormat($productGroupedData);
        if(empty($this->params['warehouse'])){
            $stores = [];
        }

        if ($this->showPendingStock) {
            $this->report['display_fields'] = array_merge($this->report['display_fields'], $this->getStoresName($stores));
        }else{
            $this->report['display_fields'] = array_merge($this->report['display_fields'],$stores, ['total' => 'Total']);
        }
        return $productGroupedData;

    }

    public function process_header_filters($filters)
    {
        $filters = parent::process_header_filters($filters);
        if($filters['product'])
        {
            $ProductModel = GetObjectOrLoadModel('Product');
            $product = $ProductModel->findById($filters['product']);
            $filters['product'] = $product['Product']['name'];
        }
        $this->report['conditions'][] = "(IC.category_id = (SELECT category_id FROM items_categories WHERE items_categories.item_type = 1 AND P.id = items_categories.item_id LIMIT 1) OR IC.category_id IS NULL)";
	    $this->report['conditions'][] = "(S.active = 1 OR S.active IS NULL)";
        return $filters;
    }

    private function assignTransactionsData($row, &$productGroupedData): void
    {
        $productId = $row['P']['id'];
        $storeQty = $row['0']['quantity'];
        $storeName = $row['S']['store_name'];
        $productGroupedData[$productId]['P'][$storeName] = $storeQty;
        $productGroupedData[$productId]['P']['total'] += $storeQty;
    }

    private function showOnHandAndAvailableStock(): void
    {
        $this->showPendingStock = (bool)\settings::getValue(InventoryPlugin, SettingsUtil::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS);
    }

    private function appendPendingTransactionsQuery($field): array|string
    {
        $data = [
            'fields' => ['product_pending' => ['query' => 'product_pending.total_pending_quantity'],],
            'conditions' => [
                'status' => ' OR status ='.\StockTransaction::STATUS_PENDING,
            ],
            'joins' => ['LEFT join (
                        SELECT
                            product_id,
                            store_id,
                            SUM(quantity) AS total_pending_quantity
                        FROM
                            (
                            SELECT
                                product_id,
                                store_id,
                                SUM(quantity) AS quantity
                            FROM
                                stock_transactions
                            WHERE
                                (stock_transactions.status = ' . StockTransactionUtil::STATUS_PENDING . ' OR stock_transactions.status IS NULL) AND (ignored = 0 OR ignored IS NULL)
                              AND source_type IN  ( ' . implode(', ', StockTransactionSourceTyeUtil::getPendingStockSourceType()) . ' )
                            GROUP BY 1, 2 '. (!$this->hasAccountingSystemPerInvoice() && ifPluginActive(PosPlugin)  ? $this->getPendingStockFromInvoiceItems() : '').'
                               ) AS subquery
                        GROUP BY
                            product_id,
                            store_id
                        )  AS product_pending  ON
                        product_pending.product_id = P.id  AND product_pending.store_id =  S.id
                        '],
        ];
        return $data[$field];
    }

    private function assignPendingTransactionsData($row, &$productGroupedData): void
    {
        $productId = $row['P']['id'];
        $storeName = $row['S']['store_name'];
        $storeOnHandQty = $row['product_balance']['total_quantity'];
        $storePendingQty = $row['product_pending']['total_pending_quantity'];

        $productGroupedData[$productId]['P']["$storeName On Hand"] = $storeOnHandQty;
        $productGroupedData[$productId]['P']["$storeName Pending"] = $storePendingQty;
        $productGroupedData[$productId]['P']["$storeName Available"] = ($storeOnHandQty + $storePendingQty);

        $productGroupedData[$productId]['P']['Total On Hand'] += $storeOnHandQty;
        $productGroupedData[$productId]['P']['Total Pending'] += $storePendingQty;
        $productGroupedData[$productId]['P']['Total Available'] += ($storeOnHandQty + $storePendingQty);
    }

    private function getStoresName($stores): array
    {
        $stockTypes = ['On Hand', 'Pending', 'Available'];
        $newStores = [];
        foreach ($stores as $store) {
            foreach ($stockTypes as $stockType) {
                $newStores[$store . ' ' . $stockType] = sprintf(__t("%s $stockType Stock"),$store);
            }
        }
        return array_merge($newStores, ['Total On Hand' => 'Total On Hand', 'Total Pending' => 'Total Pending', 'Total Available' => 'Total Available']);
    }

    private function totalNumberFormat(&$productGroupedData): void
    {
        if ($this->showPendingStock) {
            foreach ($productGroupedData as $productId => $product) {
                $unitSmallName = $product['UT']['unit_small_name'];
                $productGroupedData[$productId]['P']['Total On Hand'] = number_format($productGroupedData[$productId]['P']['Total On Hand'], 2) . " $unitSmallName";
                $productGroupedData[$productId]['P']['Total Pending'] = number_format($productGroupedData[$productId]['P']['Total Pending'], 2) . " $unitSmallName";
                $productGroupedData[$productId]['P']['Total Available'] = number_format($productGroupedData[$productId]['P']['Total Available'], 2) . " $unitSmallName";
            }
            return;
        }
        $total_key=$this->showPendingStock?'Total':'total';
        foreach ($productGroupedData as $productId => $product) {
            $unitSmallName = $product['UT']['unit_small_name'];
            $productGroupedData[$productId]['P'][$total_key] = number_format($productGroupedData[$productId]['P'][$total_key], 2). " $unitSmallName";
        }
    }

    private function hasAccountingSystemPerInvoice(): bool
    {
        if (\settings::getValue(PosPlugin, 'pos_accounting_settings')) {
            return true;
        }
        return false;
    }

    private function getPendingStockFromInvoiceItems():string
    {   
        return ' UNION ALL
                    SELECT
                        invoice_items.product_id,
                        invoice_items.store_id,
                        SUM(
                                CASE
                                    WHEN invoices.type = ' . InvoiceSourceTypesUtil::Invoice . ' THEN - invoice_items.quantity
                                    ELSE invoice_items.quantity
                                    END
                        ) AS quantity
                    FROM
                        `pos_shifts`
                            JOIN invoices ON invoices.pos_shift_id = pos_shifts.id
                            JOIN invoice_items ON invoice_items.invoice_id = invoices.id
                    WHERE
                        pos_shifts.status <> ' . PosShiftStatusUtil::STATUS_VALIDATED . '
                    GROUP BY 1,2';
    }

    private function getWarehouses(){
        $ItemPermission = GetObjectOrLoadModel('ItemPermission');
        return $ItemPermission->getAuthenticatedList(\ItemPermission::ITEM_TYPE_STORE,\ItemPermission::PERMISSION_VIEW, null, true);
    }
}
