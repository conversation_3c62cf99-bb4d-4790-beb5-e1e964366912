<?php
namespace ReportV2;

use App\Utils\ReportLimitTrait;

\App::import('Vendor','CurrencyConverter', array('file' => 'CurrencyConverter.php'));

abstract class Base
{
    use ReportLimitTrait;
    
	CONST FIELD_TYPE_DATE = 'DATE';
	CONST FIELD_TYPE_DATE_TIME = 'DATE_TIME';
	CONST FIELD_TYPE_TIME = 'TIME';
	CONST FIELD_TYPE_PRICE = 'PRICE';
	const FIELD_TYPE_NUMBER = 'NUMBER';
	const FIELD_TYPE_CURRENCY = 'CURRENCY';
	const FIELD_TYPE_MAP_JSON = 'MAP_JSON';

	CONST CURRENCY_OPTION_ALL_IN_DEFAULT = 'All';
	CONST CURRENCY_OPTION_ALL_SPERATED = 'Sperated';

	CONST AGGREGATE_FUNCTION_SUM = 'SUM';
	CONST AGGREGATE_FUNCTION_COUNT = 'COUNT';
	CONST AGGREGATE_FUNCTION_ABSOLUTE_COUNT = 'ABSOLUTE_COUNT';
	CONST AGGREGATE_FUNCTION_IGNORE = 'IGNORE';
	CONST AGGREGATE_FUNCTION_SUM_WITH_TOTAL_COUNT = 'SUM_WITH_TOTAL_COUNT';

	public $currencies = [];
	public $currencyRates = [];
	protected $default_currency = null;
    public $data  = [];
	protected $Model = false;
	public $report = false;
	protected $params = false;
    protected $currency = null;
    public $groupByName = null;
    public $responseType = null;
    public $isPerodic = false;
	protected $Session;
	static $reports = [
	    'attendance_sheets' => [
	        'className' => 'AttendanceSheet',
            'permissions' => [
                VIEW_ATTENDANCE_REPORT
            ],
            'plugin' => HRM_ATTENDANCE_PLUGIN
        ],
		//report
		'bundle' => [
			'className' => 'Bundle',
		],
		'supplier' => [

            'modelName' => 'Supplier',
			'className' => 'Supplier',
		],
		'clients' => [
            'modelName' => 'Client',
			'className' => 'Client',
        ],
		'supplier_balance' => [
			'className' => 'SupplierBalance',
		],
		'client_balance' => [
			'className' => 'ClientBalance',
		],
        'client_aged_ledger' => [
            'className' => 'ClientAgedLedger',
        ],
        'supplier_aged_ledger' => [
            'className' => 'SupplierAgedLedger',
        ],
		'purchases' => [
			'className' => 'Purchase',
		],
		'clients_sales' => [
			'className' => 'ClientSale',
		],
		'clients_payments' => [
			'className' => 'ClientPayment',
		],
        'invoice_payments' => [
            'className' => 'InvoicePayment',
        ],
		'paid_purchases' => [
			'className' => 'PaidPurchase',
		],
		'stock_transactions' => [
			'className' => 'StockTransaction',
		],
        'journal_transactions' => [
			'className' => 'JournalTransaction',
		],
        'journals' => [
			'className' => 'Journal',
		],
        'incomes' => [
			'className' => 'Income',
		],
        'fusion' => [
            'className' => 'Fusion'
        ],
        'expenses' => [
            'className' => 'Expense',
        ],
        'appointment' => [
            'className' => 'Appointment',
        ],
        'work_order_appointment' => [
            'className' => 'WorkOrderAppointment',
        ],
        'workflow_appointment' => [
            'className' => 'WorkflowAppointment',
        ],
        'work_order_profit_details' => [
            'className' => 'WorkOrderProfitDetails',
        ],
        'workflow_profit_details' => [
            'className' => 'WorkflowProfitDetails',
        ],
		'work_order_profit_summary' => [
            'className' => 'WorkOrderProfitSummary',
        ],
        'workflow_profit_summary' => [
            'className' => 'WorkflowProfitSummary',
        ],
        'workflows' => [
            'className' => 'Workflow'
        ],
		//NEW REPORT
        'PosCategories' => [
            'className' => 'PosCategories',
        ],
        'PosProducts' => [
            'className' => 'PosProducts',
        ],
        'PosSales' => [
            'className' => 'PosSales',
        ],
        'PosTransactions' => [
            'className' => 'PosTransactions',
        ],
        'PosProfits' => [
            'className' => 'PosProfits',
        ],
        'PosCategoriesProfits' => [
            'className' => 'PosCategoriesProfits',
        ],
        'PosProductsProfits' => [
            'className' => 'PosProductsProfits',
        ],
        'product_purchases' => [
            'className' => 'ProductPurchase'
        ],
        'payslips' => [
            "className" => 'Payslip'
        ],
        'warehouse_stock_balance' => [
            "className" => 'WareHouseStockBalance'
        ],
        'product_average_cost' => [
            "className" => 'ProductAverageCost'
        ],
        'price_list' => [
            "className" => 'ProductPrice'
        ],
        "tracking_products_by_lot_and_expiry_date" => [
            'className'=>'TrackingProductsByLotAndExpiryDate'
		],
		'tracking_products_by_serial'=>[
			'className'=>'SerialTracking'
		],
		'tracking_products_by_lot'=>[
			'className'=>'LotTracking'
        ],
        'tracking_products_by_expiry_date' => [
            'className' => 'TrackingProductsByExpiryDate',
        ],
        'credit_charge_details'=>[
            'className'=>'CreditChargeDetails'
        ],
        "credit_usages" => [
            "className" => "CreditUsages"
        ],
        "expired_memberships" => [
            "className" => "ExpiredMemberships"
        ],
        'new_membership'=>[
            'className'=>'NewMembership'
        ],
        "memberships_subscriptions" => [
            'className'=>'MembershipsSubscriptions'
        ],
        'installments' => [
            'className'=>'Installment'
        ],
        'payable_cheques' => [
            'className' => 'PayableCheque'
        ],
        'receivable_cheques' => [
            'className' => 'ReceivableCheque'
        ],
        'chart_of_accounts' => [
            'className' => 'ChartOfAccountsDirectory'
        ],
        'rental_reports' => [
            'className' => 'RentalUnits'
        ],
        'assets' => [
            'className' => 'Asset'
        ],
        'employees_residency_status' => [
            'className' => 'EmployeesResidencyStatus'
        ],
        'detailed_stock_transactions' => [
            'className' => 'DetailedStockTransactions'
        ],
        'loans' => [
            'className' => 'EmployeesLoans'
        ],
        'contracts' =>[
            'className' => 'EmployeesContracts'
        ],
        'accounts_balance' => [
            'className' => 'AccountsBalance'
        ],
        'manufacturing_order_costs' => [
            'className' => 'ManufacturingOrderCosts',
            'plugin' => MANUFACTURING_PLUGIN
        ],
        'inventory_requistion' => [
            'className' => 'InventoryRequistion'
        ],
        'purchase_requistion' => [
            'className' => 'PurchaseRequistion'
        ],
        'detailed_attendance_sheets' => [
            'className' => 'AttendanceSheets',
            'permissions' => [
                VIEW_ATTENDANCE_REPORT
            ],
            'plugin' => HRM_ATTENDANCE_PLUGIN
        ],
        'contract_installments' => [
            'className' => 'ContractInstallment',
            'permissions' => [
                VIEW_RESERVATION_ORDERS
            ],
            'plugin' => LEASE_CONTRACT_PLUGIN
        ],
        'lease_contract_income_and_expenses' => [
            'className' => 'LeaseContractIncomeAndExpenses',
            'permissions' => [
                VIEW_RESERVATION_ORDERS
            ],
            'plugin' => LEASE_CONTRACT_PLUGIN
        ],
	];
 
	public $dateFormat ;

	function isValidReport()
    {
        if(isset($this->report['permissions']) && !empty($this->report['permissions']))
        {
            $permissions=is_array($this->report['permissions'])?$this->report['permissions']:[$this->report['permissions']];
            $result = check_permission($permissions);
            if(!$result)
                return $result;
        }
        if(isset($this->report['plugin']) && !empty($this->report['plugin']))
        {
            $result = ifPluginActive($this->report['plugin']);
            if(!$result)
                return $result;
        }
        return true;


    }


    public function setResponseType($responseType)
    {
        $this->responseType = $responseType;
    }


    protected function getResponseType()
    {
        return $this->responseType;
    }

    protected function isCsv()
    {
        return $this->getResponseType() === 'csv';
    }

    protected function isXlsx()
    {
        return $this->getResponseType() === 'xlsx';
    }


	function __construct($name ,$params = null) {
        $this->report['joins'] = $this->report['joins'] ?? [];
	    $this->Session = new \cakeSession;
		$this->params = $params;
		if (!$this->report) {
            $this->report = self::$reports[$name];
        }

        $model = isset($this->report['modelName']) ? $this->report['modelName'] : 'Allfile';
		$this->Model = GetObjectOrLoadModel($model);
		$this->default_currency = $this->Model->get_default_currency();


		$this->currencies = ['All' => sprintf(__('All In (%s)',true),$this->default_currency)];

        if (isset($this->Model->applyBranch) && $this->Model->applyBranch['onFind']) {
            if (ifPluginActive(BranchesPlugin))
                $this->applyBranches();
        }

        if(isset($this->report['custom_table'])&&$this->report['custom_table'])
        {
            $CustomForm = GetObjectOrLoadModel('CustomForm');
            $result = $CustomForm->get_display_form_fields($this->report['modelName']);
            if(!empty($result['fields']))
            {
                $this->setCustomFields($result);
                $this->setCustomFieldsFilters();
                $this->report['joins'][] = 'LEFT JOIN '.$result['customDataTableName'].' CustomData ON CustomData.'.$result['customDataForeignKey'] .' = '.$this->report['alias'].'.id' ;
            }

        }
	}

	function setCustomFields($customFormFields)
    {
        $customDisplayFields = [];
        foreach ($customFormFields['fields'] as $k => $field)
        {
            if($field['CustomFormField']['is_listing']){
                $this->report['fields'][] = 'CustomData.field_'.$field['CustomFormField']['id'];
                $customDisplayFields[ 'field_'.$field['CustomFormField']['id']] = $field['CustomFormField']['label'];
                $this->report['group_by'][$field['CustomFormField']['label']] = 'CustomData.field_'.$field['CustomFormField']['id'];

            }
        }
        $newDisplayFields = [];
        foreach($this->report['display_fields'] as $k => $display_field)
        {
            if($k !== 'custom_fields')
            {
                $newDisplayFields[$k] = $display_field;
            }else{
                foreach($customDisplayFields as $k2 => $customDisplayField)
                {
                    $newDisplayFields[$k2] = $customDisplayField;
                }
            }
        }
        $this->report['display_fields'] = $newDisplayFields;

    }

    function setCustomFieldsFilters($model = false)
    {
        if($model == false){
            $customFilters = $this->Model->get_custom_fields_filters();
        }else{
            $customFilters = $model->get_custom_fields_filters();
        }


        if (!empty($customFilters)) {
            $this->report['custom_filters'] = $customFilters;
            $customFieldsParams = $this->params;
            foreach ($this->report['custom_filters'] as $k => $filter_field) {
                $filter_field = $filter_field['myval'];;
                $customFilters[$k] = $filter_field;
                $param = 'field_'.$filter_field['CustomFormField']['id'];
                $this->report['custom_filters'][$k]['myval']['CustomFormField']['name'] = 'custom_filters.'.$param;
                if (!empty($customFieldsParams[$param]) || (isset($customFieldsParams[$param]) && strval($customFieldsParams[$param]) === '0')) {
                    if (isset($customFieldsParams['type_' . $param]) && $customFieldsParams['type_' . $param] == "date") {
                        $this->report['conditions'][] =  "$param  =  '{$this->Model->formatDate($customFieldsParams[$param])}'";
                    }else if (isset($customFieldsParams['type_custom_filters_' . $param]) && $customFieldsParams['type_custom_filters_' . $param] == "date"){
                        $this->report['conditions'][] =  "CustomData. $param  =  '{$this->Model->formatDate($customFieldsParams[$param])}'";
                    } else if (isset($customFieldsParams['type_' . $param]) && $customFieldsParams['type_' . $param] == "datetime") {
                        $this->report['conditions'][] =  "$param  =  '{$this->Model->formatDateTime($customFieldsParams[$param])}'";
                    } else if (isset($customFieldsParams['type_custom_filters_' . $param]) && $customFieldsParams['type_custom_filters_' . $param] == "date_range") {
                        $this->report['conditions'][] = "CustomData. $param  =  '{$this->Model->formatDateTime($customFieldsParams[$param])}'";
                    } else {

                        if (is_array($customFieldsParams[$param])) {
                            $orCond = [];
                            foreach ($customFieldsParams[$param] as $k => $par) {
                                if(!empty($par)){
                                    $orCond[] = "FIND_IN_SET('{$par}',CustomData.$param)";
                                }
                            }
                            if(!empty($orCond)){
                                $this->report['conditions'][] = ' ( '.implode(' OR ', $orCond)  .' ) ';
                            }
                        } else {
                            $this->report['conditions'][] = $param . " LIKE '%{$customFieldsParams[$param]}%'";
                        }
                    }
                }
            }
        }
    }

    function additionalContent()
    {
        return '';
    }

    function show_report()
    {
        $this->data = $this->data ?: [];
        return (
            isset($this->params['group_by']) &&
            $this->params['group_by'] &&
            !$this->report['empty'] &&
            (!empty(end($this->data)['data']) || !empty($_GET['show_report']))
        ) ;
    }

    function process_final_row($final_data_row){
        return $final_data_row;
    }

    function process_query_data($query_data) {
        return $query_data;
    }

    function process_order_by($order_by) {
        return $order_by;
    }

    function process_filters($filters) {
        return $filters;
    }

    function process_filter_conditions($filters) {
        return $filters;
    }

    function process_header_filters($filters) {
        return $filters;
    }

    function process_meta_data() {

    }

    function process_group_data($grouped_data) {
        return $grouped_data;
    }

    function process_final_data($final_data) {
        return $final_data;
    }
	
	function proccess_raw_data($raw_data) {
        return $raw_data;
    }

    protected function processQueryRow($row)
    {
        return $row;
    }

    public function get_report(){
        return $this->report;
    }
	
	function get_report_meta_data(){
		$this->process_meta_data();
		return $this->report;
	}

	function get_query()
    {
        $group_by = $this->get_group_by();

        $fields = $this->get_fields($group_by);

        $joins = $this->get_joins();
        $order_by = $this->get_order_by();

        $conditions = $this->get_conditions();

        $query = 'SELECT '
            . $fields . ' FROM '
            .$this->report['table'] ;

        if($joins){
            $query .= ' '.$joins;
        }

        if(count($conditions['where'])){
            $query .= ' WHERE '
                . implode(' AND ',$conditions['where']);
        }


        if($this->report['default_group_by']){
            $query .= ' GROUP BY '
                .$this->report['default_group_by'];
        }

        if(is_array($conditions['having']) && count($conditions['having'])){
            $query .= ' HAVING '
                .implode(' AND ',$conditions['having']);
        }

        if($order_by){
            $query .=
                ' ORDER BY '
                .$order_by ;
        }
        return $query;
    }
	
	function get_report_data(){
		if($this->report ) {
            $query=$this->get_query();
            if(!isset($this->params['threshold_limit'])){
                $result = $this->Model->query($query);
            }
            if(!$this->params['is_summary'] && !isset($this->params['threshold_limit'])){
            $params = func_get_args();
            $count = false;

            if (!empty($params[0]) && is_array($params[0]) 
                && isset($params[0]['action']) 
                && $params[0]['action'] === 'owner_report') {
                //Lower the threshold limit for owner reports to 3k    
                $count = 3000;
            }
              $this->displayLimitThreshold($result, $count, 'v2');
            }
			$result = $this->process_query_data($result);
            $data = $this->process_result($result);
            $this->data = $data;
            return $data;
		}
	}


	protected function extract_result_data($result){
		$data = [];
		foreach($result as $index => $v){
			$temp = [];
			foreach(array_keys($v) as $key_index => $key){
				$temp = array_merge($temp, $v[$key]);
			}
			$data[] = $temp;
		}
        return $data;
	}
	
	protected function group_data($data){
		$grouped_data = [];


		$currency_rates = [];
        $minDate = $this->Model->formatDate($this->params['from_date']);
        $maxDate = $this->Model->formatDate($this->params['to_date']);
		$default_currency = $this->default_currency;
		foreach($data as $k => $v){
            $v = $this->processQueryRow($v);

			//TODO add dynamic fields
			$group_by_field = trim($v['group_by_field']);
			if($group_by_field == '' && $this->report['allow_removed'])
			{
				$group_by_field = $this->report['removed_text'] ?:__("Uncategorized",true);
			}else if($group_by_field ==''){
				continue;
			}
			$currency_name = isset($this->report['currency']['field']) ? $v[$this->report['currency']['field']] : $default_currency;

			
			unset($v['group_by_field']);
			$defaultCurrencyDate = date('Y-m-d');
			if(isset($this->params['currency']) && $this->params['currency'] == self::CURRENCY_OPTION_ALL_IN_DEFAULT){
				if($currency_name)
				{
				    if(isset($this->report['currency']['date_field']) && !empty($this->report['currency']['convert_fields'])){
				        $currencyDate = $v[$this->report['currency']['date_field']];
                    }else {
				        $currencyDate = $defaultCurrencyDate;
                    }
                    $reportConvertedFields = array_unique($this->report['currency']['convert_fields']);
					foreach($reportConvertedFields as $i => $convert_field_name){
						if(!$currency_rates[$currency_name][$currencyDate])
                        {
                            $currency_rates[$currency_name][$currencyDate] = \CurrencyConverter::index($currency_name,$default_currency, $currencyDate);
                        }
                        $v[$convert_field_name] = (float) $v[$convert_field_name];
						$v[$convert_field_name]*= $currency_rates[$currency_name][$currencyDate];
						
					}
                    $currency_name = $this->default_currency;

                }
			}
            $this->currencyRates = $currency_rates;
			if($this->isPerodic)
            {
                //calculate max date and min date
                if($group_by_field < $minDate)
                {
                    $minDate = $group_by_field;
                }else if($group_by_field > $maxDate)
                {
                    $maxDate = $maxDate;
                }
            }
            if(!isset($emptyRecord))
            $emptyRecord = array_fill_keys(array_keys($v), null);
			$emptyRecord['empty_record'] = true;
            $grouped_data[$currency_name][$group_by_field][] = $v;
            unset($data[$k]);
        }

        if($this->isPerodic){
		    //set periods list
            $periodsList = $this->getPeriodsList($minDate, $maxDate, $this->groupByName);
        }

		foreach($grouped_data as $currency => &$currency_grouped_data)
		{

			if($this->isPerodic)
            {
                //apply periods list
                foreach($periodsList as $k => $periodLabel){

                    if(!isset($currency_grouped_data[$periodLabel] ) && $periodLabel < $maxDate && $periodLabel > $minDate)
                    {

                        $currency_grouped_data[$periodLabel][] = $emptyRecord;
                    }
                }
            }
            ksort($currency_grouped_data);

            $currency_grouped_data = $this->process_group_data($currency_grouped_data);

        }

		return $grouped_data;
	}

	protected function add_group_fields($grouped_data){
		foreach($this->report['group_fields'] as $k => $group_operation){
			foreach($grouped_data as $group_name => $group)
			{


				$counter = 0;
				$group_fields = [];

                foreach($group as $group_index => $group_data){

                    if($group_index === 'group_fields' || isset($group_data['empty_record']) && $group_data['empty_record'] === true)
                    {
                        continue;
                    }

                    if($group_operation == self::AGGREGATE_FUNCTION_IGNORE)
                    {
                        $group_fields[$k] = '';
                        break;
                    }
                    $counter = $counter + 1;
//					$group_fields['count'] = $counter;
					
					if(isset($group_data[$k])){
					    switch ($group_operation){
                            case self::AGGREGATE_FUNCTION_SUM:
                            case self::AGGREGATE_FUNCTION_SUM_WITH_TOTAL_COUNT:
                                $group_fields[$k] += $group_data[$k];
                                break;
						    case self::AGGREGATE_FUNCTION_ABSOLUTE_COUNT:
						    case self::AGGREGATE_FUNCTION_COUNT:
                                if($group_data[$k])
                                {
                                    $group_fields[$k] += 1;
                                }
                                break;
					    }

					}
				}
				if($group_operation == self::AGGREGATE_FUNCTION_COUNT || $group_operation == self::AGGREGATE_FUNCTION_SUM_WITH_TOTAL_COUNT)
                {
                    $grouped_data[$group_name]['group_fields'][$k] = ($group_fields[$k] ?:0).'/'.$counter  ;
                    $grouped_data[$group_name]['group_fields_raw_values'][$k] =$grouped_data[$group_name]['group_fields'][$k] ;
                }else{
                    $grouped_data[$group_name]['group_fields'][$k] = $group_fields[$k];
                    $grouped_data[$group_name]['group_fields_raw_values'][$k] =  $grouped_data[$group_name]['group_fields'][$k];

                }

//				$grouped_data[$group_name]['group_fields'] = $group_fields;
				}
			

		}

		return $grouped_data;
		
	}	
	
	protected function add_total_fields($grouped_data){
		$total_fields = [];
		foreach($this->report['total_fields'] as $k => $group_operation){
            if($group_operation == self::AGGREGATE_FUNCTION_IGNORE)
            {
                $total_fields[$k] = '';
                continue;
            }
			foreach($grouped_data as $group_name => $group)
			{
					if(is_array($group['group_fields']) && key_exists($k,$group['group_fields'])){
                        switch ($group_operation){
                            case self::AGGREGATE_FUNCTION_SUM:
                                if(!isset($total_fields[$k]))
                                    $total_fields[$k]=0;
                                if(!empty($group['group_fields'][$k]))
                                $total_fields[$k] +=  $group['group_fields'][$k];

                                break;
                            case self::AGGREGATE_FUNCTION_COUNT:
                            case self::AGGREGATE_FUNCTION_SUM_WITH_TOTAL_COUNT:
                                if($group['group_fields'][$k] )
                                {
                                    $totalParts = explode('/',$total_fields[$k]);
                                
                                    $countParts = explode('/', $group['group_fields'][$k]);
                                   
                                    $totalParts = array_map('floatval', $totalParts); //php8 fix
                                    $countParts = array_map('floatval', $countParts); //php8 fix

                                    
                                    $total_fields[$k] =  ($totalParts[0] + $countParts[0]) . '/' . ($totalParts[1] + $countParts[1]);
                                }
                                break;
                            case self::AGGREGATE_FUNCTION_ABSOLUTE_COUNT:
                                if($group['group_fields'][$k])
                                {
                                    $totalParts = explode('/',$total_fields[$k]);
                                    $countParts = explode('/', $group['group_fields'][$k]);

                                    $totalParts = array_map('floatval', $totalParts);
                                    $countParts = array_map('floatval', $countParts);

                                    $total_fields[$k] =  ($totalParts[0] + $countParts[0]); //php8 fix
                                }
                                break;
                        }

					}
			}
			
		}
        
		return $total_fields;

	}

  
	protected function applyBranches() {
	    //Add Fields
        $this->report['fields']['branch'] = ['query' => '`Branch`.`name` AS branch','type' => ''];

        //Add Display Field
        $reportDisplayFields = &$this->report['display_fields'];
        if (isset($this->report['group_fields']) || isset($this->report['total_fields'])) {
            $insetAtOffset = count($reportDisplayFields) - max(count($this->report['group_fields']),count($this->report['group_fields']));
            $newArr = [];
            $i = 0;
            foreach ($reportDisplayFields as $key => $value) {
                if ($i++ == $insetAtOffset)
                    $newArr['branch'] = 'Branch';
                $newArr[$key] = $value;
            }
            $reportDisplayFields = $newArr;
        } else {
            $reportDisplayFields['branch'] = 'Branch';
        }

        //Add Joins
        $this->report['joins'][] = 'LEFT JOIN branches Branch ON '.$this->getBranchConditionAlias().'.branch_id = Branch.id';

        //Add Filters
        $modelName = isset($this->report['modelName']) ? $this->report['modelName'] : 'Allfile';
        if (!isModelSharable($modelName)) {
            $this->report['filters']['branch_id'] = [
                'type' => 'select',
                'input' => [
                    'multiple' => 'multiple',
                    'label' => __('Branch', true),
                    'name' => 'branch_id',
                    'data-live-search' => true,
                    'class' => 'form-control input-select',
                    'div' => 'form-group col-md-3',
                    'options' => getStaffBranchesSuspended()
                ],
                'condition' => $this->getBranchesCondition(),
                'allowedValues' => getStaffBranchesIDsSuspended()
            ];
        }

        //Add GroupBY
        $this->report['group_by']['Branch'] = 'Branch.name';
    }

	protected function set_currencies()
	{
		$this->currencies[self::CURRENCY_OPTION_ALL_IN_DEFAULT] = sprintf(__('All In (%s)',true), $this->default_currency);
		if($currency_field = $this->report['currency']['field'])
		{
		    $table = $this->report['currency']['table'] ?: $this->report['table'];
			$sql = 'SELECT DISTINCT('.$currency_field.') from '.$table;
			
			$result = $this->Model->query($sql);
			if($result)
			{
				$this->currencies[self::CURRENCY_OPTION_ALL_SPERATED] = __('All Sperated',true);
				foreach($result as $k => $v)
				{
					foreach($v as $k2 => $v2)
					{
						if($v2[$currency_field])
						$this->currencies[$v2[$currency_field]] = $v2[$currency_field];

					}
				}
			}
			
		}

	}

	protected function process_result($result){
		$this->set_currencies();
		$data = $this->extract_result_data($result);
		$grouped_data = $this->group_data($data);
        unset($data);
        foreach($grouped_data as $currency => &$currency_grouped_data)
		{
            $this->currency = $currency;
			$currency_grouped_data = $this->proccess_raw_data($currency_grouped_data);
			$currency_grouped_data = $this->add_group_fields($currency_grouped_data);
            $final_data[$currency]['total_fields'] = $this->add_total_fields($currency_grouped_data);
            $final_data[$currency]['raw_values'] = $final_data[$currency]['total_fields'];
            $final_data[$currency]['data'] = $currency_grouped_data;
			$graph_data[$currency] = $this->get_graph_data($final_data[$currency]);
			$final_data[$currency]['graphs'] = $graph_data[$currency];
            $final_data[$currency] = $this->format_data($final_data[$currency]);
            $final_data[$currency]  = $this->process_final_data($final_data[$currency]);
        }
		return $final_data;
	}

	/**
	 * 
	 * @param type $data
	 * @return type data grouped by currency 
	 */
	protected function group_by_currency($data){
		
		\App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
//		require_once '';
		$currency_grouped_data = [];
		$currency_field = $this->report['currency']['field'];
		
		$fields_to_convert = $this->report['currency']['fields_to_convert'];
		$default_currency = $this->Model->get_default_currency();
		$curr_rates = [];
		$currencies = [];
		foreach($data['data'] as $group_name => $group_data)
		{
			foreach($group_data as $k => $v)
			{
				$record_currency = $v[$currency_field]; 
				$currencies[$record_currency] = $record_currency;
				if($record_currency && ($record_currency != $default_currency))
				{
					foreach($fields_to_convert as $i => $convert_field)
					{
						if(empty($curr_rates[$record_currency])){
							$curr_rates[$record_currency] = \CurrencyConverter::index($record_currency, $default_currency);
						}
						$v[$convert_field] = $v[$convert_field] * $curr_rates[$record_currency];
					}
				}
				
				$currency_grouped_data[$record_currency][$group_name][] = $v;
			}
		}
		$data['data'] = $currency_grouped_data;
		$data['currencies'] = $currencies;
		return $data;
	}

	protected function getDataFormatter()
    {
        switch ($this->getResponseType())
        {
            case 'csv':
            case 'xlsx':
                return new Formatters\CsvDataFormatter($this->report['fields']);
                break;
            default:
                return new Formatters\DefaultDataFormatter($this->report['fields']);
                break;
        }
    }

	protected function format_data($final_data)
	{
        $formatter = $this->getDataFormatter();
		foreach($final_data['data'] as $group_name => &$group)
		{
			foreach($group as $group_index => &$group_data)
			{
                if ($group_index =='group_fields_raw_values') continue;
                $group_data = $formatter->formatData($group_data);
                $group_data = $this->process_final_row($group_data);
            }

		}
		
		foreach($final_data['total_fields'] as $field_name => &$field_value)
		{
			$field_data = $this->report['fields'][$field_name];
            
					SWITCH($field_data['type']){
						case self::FIELD_TYPE_DATE:
							$field_value = format_date($field_value);
							break;
                        case self::FIELD_TYPE_DATE_TIME:
                            $field_value = format_date($field_value,true, true);
                            break;
                        case self::FIELD_TYPE_TIME:
                            $field_value = date('H:i',strtotime($field_value));
                            break;
						case self::FIELD_TYPE_PRICE:
							if($field_data['colored']) $field_value = $this->format_price_colored($field_value);
							else	$field_value = format_price($field_value,$this->currency);
							break;
                        case self::FIELD_TYPE_NUMBER:
                            $field_value = format_number($field_value, false, 2);
                            break;
						default:
							
					}
					
					
		}
		return $final_data;
	}
	
	protected function get_graph_data($reportCurrencyData){
		$graph_data = [];
			foreach($this->report['graphs'] as $k => $graph){
			$graph['y_title'] = __($graph['y_title'],true);
			$graph['x_title'] = __($this->params['group_by'],true);
			$graph_meta = $graph;
			if($graph['type'] == 'area' || $graph['type'] == 'bar'){
				$result = ['data' => $this->get_area_graph_data($graph,$reportCurrencyData), 'meta' => $graph_meta ];
				if($graph['type'] == 'bar' && count($result['data']['values']) > 30)
                {
                    continue;
                }
				
			}else if($graph['type'] == 'pie'){
				$result = ['data' => $this->get_pie_graph_data($graph,$reportCurrencyData), 'meta' => $graph_meta];
			}
			$graph_data[] = array_merge($result, ['columns' => $graph['columns']]);
			}
		return $graph_data;
		
	}
	
	protected function get_first_row_fields($grouped_data){

        if (is_array($grouped_data[array_keys($grouped_data)[0]][0])) { //php8 fix
            return array_keys($grouped_data[array_keys($grouped_data)[0]][0]);
        }
        return array_keys($grouped_data[array_keys($grouped_data)[0]]);
	}

	protected function get_area_graph_data($graph,$grouped_data){
		$first_row_fields = $this->get_first_row_fields($grouped_data);
		foreach($graph['group_fields'] as $k => &$v)
			$v = __($v,true);
		$graph_data['titles'] = array_merge([$this->params['group_by']],array_values($graph['group_fields']));
		foreach($grouped_data['data'] as $group_name => $group){
			$graph_item = [$group_name];
			foreach($graph['group_fields'] as $k => $group_field){
				$graph_item[] = floatval($group['group_fields'][$k])?:0;
			}
			$graph_data['values'][] = $graph_item;
		}

		return $graph_data;
	}
	
	protected function get_pie_graph_data($graph,$grouped_data){
		$first_row_fields = $this->get_first_row_fields($grouped_data);
		if(!empty($graph['group_fields']))
        {
            $graph_data['titles'] = array_merge([$this->params['group_by']],$graph['group_fields']);
            foreach($grouped_data['data'] as $group_name => $group){
                foreach($graph['group_fields'] as $k => $group_field)
                {
                    $graph_item = [$group_name,$group['group_fields'][$group_field]];
                }
                $graph_data['values'][] = $graph_item;

            }
        }else if(!empty($graph['total_fields']))
        {
            $graph_data['titles'] = [$graph['x_title'], $graph['y_title']];
            foreach ($graph['total_fields'] as $totalField => $totalFieldName)
            {
                $graph_data['values'][] = [$totalFieldName, floatval($grouped_data['total_fields'][$totalField])];
            }
        }
		return $graph_data;
	}
	
	protected function get_fields($group_by = null){
		$fields = [];
		foreach($this->report['fields'] as $k => $v)
		{
			if(is_array($v))
			{
				if(!empty($v['query'])) $fields[] = $v['query'];
			}else{
				$fields[] = $v;
			}
		}
		if($group_by){
			$fields[] = $group_by .' as group_by_field';
		}
		return implode(',',$fields);
	}
	
	public function get_filter_inputs(){
		$filters = $this->report['filters'];
		foreach($filters as $filter_name => $filter){
		    if(isset($this->params[$filter_name]))
            {
                $filters[$filter_name]['input']['value'] = $this->params[$filter_name];
            }
			if(isset($filter['input']['dynamic_options'])){
				$filters[$filter_name]['input']['options'] = $this->get_dynamic_options($filter['input']['dynamic_options']);
			}
		}
		$filters = $this->process_filters($filters);
        return $filters;
	}

	public function getBranchesCondition() {
	    $tableAlias = $this->getBranchConditionAlias();
        return $tableAlias.'.branch_id IN ($branch_id)';
    }

    public function getBranchConditionAlias() {
	    if (isset($this->report['branchesConditionAlias']))
	        return $this->report['branchesConditionAlias'];
	    $baseTableParts = explode(' ',$this->report['table']);
	    return count($baseTableParts) > 1 ? $baseTableParts[1] : $baseTableParts[0];
    }

    protected function get_dynamic_options($dynamic_option_data){
        $model_params = $dynamic_option_data['model'];
        if(str_starts_with($dynamic_option_data['model'], 'le_')){
            $model_params = ['class' => $dynamic_option_data['model'], 'table' => $dynamic_option_data['model']];
        }
        $Model = GetObjectOrLoadModel($model_params);
        $limit = 500;
        if(isset($dynamic_option_data['limit'])){
            $limit = $dynamic_option_data['limit'];
        }
        // We Limit Options Here to not cause browser to become unresponsive trying to render a lot of select options, instead use ajax select
        $options = $Model->find('list', ['limit' => $limit,'conditions' => $dynamic_option_data['conditions'], 'fields' =>$dynamic_option_data['fields'] ?? null, 'group' =>$dynamic_option_data['group'] ?? null,'callbacks' => false ]);
        $options = $options;
        if(isset($dynamic_option_data['more_options'])){
            $options=$options+    $dynamic_option_data['more_options'];
        }

        return $options;
    }
	
	protected function get_filters_conditions(){
		$filter_conditions = ['where' => [], 'having' => []];
		foreach($this->report['filters'] as $filter_name => $filter){
		    if(isset($filter['name']))
            {
                $filter_name = $filter['name'];
            }
			if(isset($filter['condition']) && $filter['condition']) {
                $filterValues = [];
                if((isset($this->params[$filter_name])) && ($this->params[$filter_name] !== '' )){
                    $filterValues = $this->params[$filter_name];
                    if (isset($filter['allowedValues'])) {
                        if(!is_array($filterValues)) {
                            $filterValues = [$filterValues];
                        }
                        foreach ($filterValues as $key => $value) {
                            if (!in_array($value,$filter['allowedValues'])) {
                                unset($filterValues[$key]);
                            }
                        }
                    }
                } else if (isset($filter['allowedValues'])) {
                    $filterValues = $filter['allowedValues'];
                }
                if (!empty($filterValues) || $filterValues === "0" || $filterValues === 0) {
                    if (isset($filter['having']) && $filter['having']) {
                        $filter_conditions['having'][] = str_replace("\$" . $filter_name, $filterValues, $filter['condition']);
                    } else {

                        if(!is_array($filterValues) && (strpos($filter['condition'],"IN (") || strpos($filter['condition'],"IN(")) && $filter['type'] != 'date'){
                            if(!is_numeric($filterValues)) {
                                $filter_value = "'" . $filterValues . "'";
                            }else{
                                $filter_value=$filterValues;
                            }
                        }else{
                            $filter_value = $filterValues;
                        }
                        if(
                            is_array($filterValues) &&
                            1 === count($filterValues) &&
                            empty($filterValues[0]) &&
                            $filter['type'] == "element" &&
                            $filter['element']['name'] == "advanced_staff"
                        ) {
                            continue;
                        }
                        if (is_array($filter_value)) {
//                            $filter_value = array_map(function($n){
//                                return $n;
//
//                            }, $filter_value);

                            $filter_value = "'". implode("','", $filter_value) ."'";
                        } else if ($filter['type'] == 'date') {
                            $filter_value = $this->Model->formatDate($filter_value);
                        }
                        $filter_conditions['where'][$filter_name] = str_replace("\$" . $filter_name, $filter_value, $filter['condition']);
                    }
                }
			}
		}
		return $filter_conditions;
	}

	protected function get_conditions(){
		$conditions  = isset($this->report['conditions'])?$this->report['conditions']:[];
		$filter_conditions = $this->get_filters_conditions();
		if(is_array($this->params['currency']))
		{
			$temp = [];
			foreach($this->params['currency'] as $k => $currency)
			{
				if($this->params['currency'] && ($this->params['currency'] != self::CURRENCY_OPTION_ALL_IN_DEFAULT && $this->params['currency'] != self::CURRENCY_OPTION_ALL_SPERATED ))
				{	
					$temp[] = $this->report['currency']['filter_field'] .'="'.$currency.'"';
				}
			}
			$filter_conditions['where'][] = implode(' OR ' , $temp);
		}else{
			if($this->params['currency'] && ($this->params['currency'] != self::CURRENCY_OPTION_ALL_IN_DEFAULT && $this->params['currency'] != self::CURRENCY_OPTION_ALL_SPERATED )) {
				$filter_conditions['where'][] = $this->report['currency']['filter_field'] .'="'.$this->params['currency'].'"';
			}
			
		}

        $filter_conditions = $this->process_filter_conditions($filter_conditions);
		$conditions['where'] = array_merge($filter_conditions['where'],$conditions);
		$conditions['having'] = $filter_conditions['having'];
		return $conditions;
		
	}
	
	protected function get_group_by(){
		$group_by = [];
		if(is_countable($this->report['group_by']) && count($this->report['group_by']) == 1)
		{
			//there is only 1 group by so we will always group by it
			$first_group_by = array_keys($this->report['group_by'])[0];
			$group_by[] = $this->report['group_by'][$first_group_by];
			$this->params['group_by'] = $first_group_by;
		}else{
			$group_by[] = $this->report['group_by'][$this->params['group_by']];
		}
		$this->groupByName = $this->params['group_by'];
//		die($this->groupByName );
		if($this->groupByName == 'Daily' || $this->groupByName == 'Weekly' || $this->groupByName == 'Monthly' || $this->groupByName == 'Yearly' ){
            $this->isPerodic = true;
        }
		$group_by = implode(' , ', $group_by);
		return $group_by;
	}
	
	protected function get_joins(){
		$joins = $this->report['joins'];
		$joins = implode(' ', $joins);
		return $joins;
	}
	
	protected function get_order_by(){
		$order_by = [];
		if(!empty($this->report['order_by'])&&is_string($this->report['order_by']) && strlen($this->report['order_by']) >= 1)
		{
			$order_by = $this->report['order_by'];
		}else{
			if(!empty($this->report['order_by'])&&is_array($this->params['order_by']))
			{
				foreach($this->params['order_by'] as $k => $v)
				{
					$order_by[] = $this->report['order_by'][$v];
				}
				
			}else{
				$order_by[] = $this->report['order_by'][$this->params['order_by']];
			}
			
			
		}
		
		$order_by = $this->process_order_by($order_by);
        if (is_array($order_by)) {
            $order_by = implode(',',$order_by);
        }
        $order_by = trim(preg_replace("/\t+|\r|\n/", "", $order_by));
		return $order_by;
	}
	
	 function get_filters_key_value($filter_inputs)
	{
	    $filter_values = $this->params;
		$result = [];
		unset($filter_values['no_graph'],$filter_values['summary'],$filter_values['show_report'],$filter_values['ext'],$filter_values['url'],$filter_values['group_by'],$filter_values['debug'],$filter_values['order_by'],$filter_values['is_sum']);
		 foreach($filter_values as $filter_name => $v)
		 {
			if($filter_name == 'is_sum')
            {
                continue;
            }

			if( (isset($filter_inputs[$filter_name]['type']) && $filter_inputs[$filter_name]['type'] == 'hidden')
                || (isset($filter_inputs[$filter_name]['hide_from_header'])
                    && $filter_inputs[$filter_name]['hide_from_header']
                )
            )
            {
                continue;

            }
			if(isset($filter_inputs[$filter_name]['type']) && $filter_inputs[$filter_name]['type'] == 'select')
			{

                $values = $filter_inputs[$filter_name]['input']['options'];
				if(is_array($v))
				{
					//in case you can select multiple option in the same filter
					$filter_value = [];
					foreach($v as $k2 => $value2)
					{
						array_push($filter_value, $values[$value2]);
					}
					$filter_value = '(' . implode(',',$filter_value) . ')';
				}else{
					$filter_value = $values[$v];
				}
			}
			else if(isset($filter_inputs[$filter_name]['type']) && $filter_inputs[$filter_name]['type'] == 'date')
			{
				$filter_value = $v;
			}
			else if(isset($filter_inputs[$filter_name]['type']) && $filter_inputs[$filter_name]['type'] == 'element' && $v) {
			    if ($filter_inputs[$filter_name]['element']['name'] == 'advanced_client') {
                    $clientModel = GetObjectOrLoadModel('Client');
                    $clientData = $clientModel->find(
                        'list',
                        array(
                            'limit' => 1,
                            'conditions' => array('Client.id' => $v),
                            'fields' => "Client.client_number, Client.business_name, Client.id",
                            'recursive' => -1
                        ));
                    if(!is_array($v) &&isset($clientData[$v])) {
                        $code = key($clientData[$v]);
                        $displayedClientData = $clientData[$v][$code] . ' #' . $code;
                        $filter_value = $displayedClientData;
                    }
                }
                
			}
			else{
				$filter_value = $v;
			}
			if(empty($filter_value))
				continue;
			
			if(isset($filter_inputs[$filter_name]['input']['label']) && $filter_inputs[$filter_name]['input']['label'])
			{
				$filter_name = __($filter_inputs[$filter_name]['input']['label'],true);
			} else if (isset($filter_inputs[$filter_name]['element'])) {
                $filter_name = __($filter_inputs[$filter_name]['element']['options']['label'],true);
            }
			$result[$filter_name] = $filter_value;
		 }
		 $result = $this->process_header_filters($result);
		 return $result;
	}
	
	function get_params()
	{
		return $this->params;
	}
	
	private function format_price_colored($value){
		
		if($value >= 0){
			$value = format_price($value,$this->currency);
			return '<span class="">'.$value.'</span>';
		}else{
			$value = format_price($value,$this->currency);
			return '<span class="text-danger">'.$value.'</span>';
		}
	}

	private function format_price_simple_colored($value){
		if($value >= 0){
			$value = format_price_simple($value, $this->currency);
			return '<span class="">'.$value.'</span>';
		}else{
			$value = format_price_simple($value, $this->currency);
			return '<span class="text-danger">'.$value.'</span>';
		}
	}

    protected function getPeriodsList($stard_date, $end_date,$period='monthly')
    {
        $period_labels=array();
        $periods=array(
            'Yearly'=>array('completion'=>'-01-01',  'format' => 'Y',  'step' => '+1 Year'),
            'Monthly'=>array('completion'=>'-01',  'format' => 'Y-m',  'step' => '+1 Month'),
            'Weekly'=>array('completion'=>'',  'format' => 'Y-m-d',  'step' => '+7 Days'),
            'Daily'=>array('completion'=>'',  'format' => 'Y-m-d',  'step' => '+1 Day'),
        );
        $period_labels[]=$stard_date;


        while($stard_date<$end_date)
        {
            $stard_date=date($periods[$period]['format'],strtotime($periods[$period]['step'],strtotime($stard_date.$periods[$period]['completion'])));
            $period_labels[]=$stard_date;
        }

        return $period_labels;
    }

    public function isSummary()
    {
        return $this->params['summary']  && $this->report['has_summary'];
    }

    protected function getUserName()
    {
        $user = getAuthOwner();
        if (!empty($user['first_name']) || !empty($user['last_name'])) {
            $actorName = "{$user['first_name']} {$user['last_name']}";
        } else if (!empty($user['business_name'])) {
            $actorName = $user['business_name'];
        } else {
            $actorName = $user['name'];
        }
        return isset($actorName) ? $actorName : '';
    }


}
?>
