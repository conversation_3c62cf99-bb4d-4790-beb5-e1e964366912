<?php
\App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
require_once dirname(dirname(__FILE__)) . DS . 'AgedDebtor.php';

class Report_AgedDebtor_AgedDebtor extends Report_AgedDebtor {

    public function getDataArray() {
        $owner = getAuthOwner();

        $group_by_fields = array(
//            'daily' => 'date(J.`date`)',
//            'weekly' => 'CONCAT_WS("-", YEAR(J.`date`), LPAD(MONTH(DATE_ADD(J.`date`, INTERVAL (0 - WEEKDAY(J.`date`)) DAY)), 2, 0), LPAD(DAY(DATE_ADD(J.`date`, INTERVAL (0 - WEEKDAY(J.`date`)) DAY)), 2, 0))',
//            'monthly' => 'CONCAT(YEAR(J.`date`), "-", LPAD(MONTH(J.`date`), 2, 0))',
            'client' => 'I.client_id',

        );



  $group_by_title_params = array(
//            'daily' => array('column_title' => __('Day', true), 'report_title' => __('Daily Cost Center Transactions', true), 'graph_title' => __('Daily Cost Center Transactions', true)),
//            'weekly' => array('column_title' => __('Week', true), 'report_title' => __('Weekly Cost Center Transactions', true), 'graph_title' => __('Weekly Cost Center Transactions', true)),
//            'monthly' => array('column_title' => __('Month', true), 'report_title' => __('Monthly Cost Center Transactions', true), 'graph_title' => __('Monthly Cost Center Transactions', true)),
//            'yearly' => array('column_title' => __('Year', true), 'report_title' => __('Yearly Cost Center Transactions', true), 'graph_title' => __('Yearly Cost Center Transactions', true)),
            'client' => array('column_title' => __('Aged Debtors', true), 'report_title' => __('Aged Debtors', true), 'graph_title' => __('Aged Debtors', true)),

        );

		$period_length = 30;
		$periods_count = 4;
        $clients_limit = 2000;
        $group_by = 'client';
		debug($this->params['group_by']);
		debug($group_by_fields[$this->params['group_by']]);
        if (!empty($this->params['group_by']) && !empty($group_by_fields[$this->params['group_by']])){
            $group_by = $this->params['group_by'];
		}

        $this->params['group_by'] = $group_by;
        $is_periodic = in_array($group_by, array('daily', 'weekly', 'monthly', 'yearly'));
        $titles = $group_by_title_params[$group_by];

        $this->params['is_summary'] = empty($this->params['is_summary']) ? 0 : 1;

        $query = "SELECT (datediff(CURDATE(), I.date)),sum(JT.currency_rate * I.summary_unpaid) total_unpaid,I.client_id, IF((datediff(CURDATE(), I.date))<=0,-30,(((datediff(CURDATE(), I.date) DIV {$period_length}) + 1) * {$period_length})) as timegroup," . $group_by_fields[$group_by] . " as `subtotal_field`" ;
//		if($is_periodic)
//			$query .= ', count(IT.tag_id) as count' ;
		$query .= "  FROM invoices as I"
				. " LEFT JOIN journals J on J.entity_id = I.id and J.entity_type in( 'invoice','debit_note' )" //
				. " LEFT JOIN (select currency_rate,journal_id from journal_transactions group by journal_id) JT on JT.journal_id = J.id" //
				. "" ;

        if (!empty($this->params['date_from'])) { //after return to ashraf ->result no need for date_from  in this report 
            unset($this->params['date_from']);
        }
        $whereParts = array_merge(['(I.payment_status = 0 OR '. 'I.payment_status = '.INVOICE_STATUS_PARTIAL_PAID.')', 'I.date IS NOT NULL', "I.date != '0000-00-00'",'I.summary_unpaid > 0' ], $this->getWhereParts());
// die(debug($whereParts));
        if(count($whereParts) > 0)
        $query .= ' WHERE ' . implode(' AND ', $whereParts);
        $query .= ' GROUP BY subtotal_field, timegroup';
        $query .= ' ORDER BY subtotal_field asc';

        $queryData = [];

        if(!isset($this->params['threshold_limit'])){
            $queryData = $this->Invoice->query($query);
        }

        foreach ($queryData as $row) {
            if (!isset($row[0]))
                $row[0] = array();
            $flatData[] = array_merge($row[0], $row['I']);
        }
        $queryData = $flatData;



        foreach ($queryData as $row) {
            if (!$is_periodic)
                $groups[] = $row['subtotal_field'];
        }
        $reportData = [];
        $clientIds = [];
		foreach ($queryData as $row) {
            $group = $row['subtotal_field'];
            $clientIds[] = $group;
            $reportData[$group][] = $row;
//            $reportData[$currency]['methods'][$row['payment_method']]+=$row['amount'];
        }
        foreach($reportData as $group => $group_data){
            foreach($group_data as $k => $data)
            {

                for($i = -1 ; $i <= $periods_count  ; $i++)
                {
                $pl=($i) * $period_length;
               if($pl>=30) {
                   $pl = $pl - 30;
               }
                   // print_pre($pl);
//                    if (($data['timegroup'] == -1)) {
//
//                        $reportData[$group][0]['unpaids']['-1 ' . __('Day',true)] = $data['total_unpaid'];
//                    }else
                    if (($data['timegroup'] == $i * $period_length)) {
                        $reportData[$group][0]['unpaids'][$pl . ' ' . __('Day',true)] = $data['total_unpaid'];

                    } else if(!isset($reportData[$group][0]['unpaids'][$pl . ' ' . __('Day',true)])){


                        $reportData[$group][0]['unpaids'][$pl. ' ' . __('Day',true)] = 0;
                    }
                    //the output in case period_length == 30
//				$data['timegroup'] == 30 ? $reportData[$group][0]['unpaids']['30'] = $data['total_unpaid'] : $reportData[$group][0]['unpaids']['30'] = 0;
//			$data['timegroup'] == 60 ? $reportData[$group][0]['unpaids']['60'] = $data['total_unpaid'] : $reportData[$group][0]['unpaids']['60'] =  0;
//			$data['timegroup'] == 90 ? $reportData[$group][0]['unpaids']['90'] = $data['total_unpaid'] : $reportData[$group][0]['unpaids']['90'] =  0;
//
                }

                $i--;
                if($data['timegroup'] > ($i * $period_length)) {

                    $reportData[$group][0]['unpaids'][$i * $period_length . '+' . ' ' . __('Day',true)] += $data['total_unpaid'];
                }else{

                    $reportData[$group][0]['unpaids'][$i * $period_length . '+' . ' ' . __('Day',true)] += 0;
                }


                //				$reportData[$group][0]['unpaids']['90+'] += $data['total_unpaid'];
                if($k > 0)
                {
                    unset($reportData[$group][$k]);
                }
            }
        }
        $mergedClientIds = $clientIds;
        if (count($mergedClientIds) < $clients_limit && ($clientIds && empty($this->params['client_id']) || ($clientIds && !empty($this->params['client_id']) && !empty($this->params['client_category'])))) {
            $Client = GetObjectOrLoadModel('Client');
            $Client->recursive = -1;
            $conditions = ['Client.id NOT IN ('.implode(",", $clientIds).')'];
            if (!empty($this->params['client_category'])) {
                $conditions []= ['Client.id  IN ('.$this->params['client_id'].')'];
            }
            if(isset($this->params['data']['branch_id'])) {
                $conditions['Client.branch_id'] = $this->params['data']['branch_id'];
            }
            $notIncludedClients = $Client->find('list', ['fields' => ['id', 'id'],'applyBranchFind' => false,'conditions' => $conditions, 'limit' => $clients_limit]);
            $sample = end($reportData)[0];
            foreach ($sample['unpaids'] as  $k => $v) {
                $sample['unpaids'][$k] = 0;
            }
            foreach ($notIncludedClients as $notIncludedClient) {
                $mergedClientIds[] = $notIncludedClient;
                $reportData[$notIncludedClient][0] = array_merge(
                    $sample,[
                    "total_unpaid" => 0,
                    "client_id" => $notIncludedClient,
                    "subtotal_field" => $notIncludedClient,
                ]);
            }
        }
        if (empty($clientIds) ) {
            $Client = GetObjectOrLoadModel('Client');
            $Client->recursive = -1;
            $conditions=[];
            if (!empty($this->params['client_id'])) {
                $conditions = ['Client.id = ' . $this->params['client_id']];
            }
            if (isset($this->params['data']['branch_id'])) {
                $conditions['Client.branch_id'] = $this->params['data']['branch_id'];
            }
            $notIncludedClients = $Client->find('list', ['fields' => ['id', 'id'], 'applyBranchFind' => false, 'conditions' => $conditions, 'limit' => $clients_limit]);
            $sample = [];
            for ($i = -1; $i <= $periods_count; $i++) {
                $pl = ($i) * $period_length;
                if ($pl >= 30) {
                    $pl = $pl - 30;
                }
                $sample['unpaids'][$pl . ' ' . __('Day', true)] = 0;
            }
            foreach ($notIncludedClients as $notIncludedClient) {
                $mergedClientIds[] = $notIncludedClient;
                $reportData[$notIncludedClient][0] = array_merge(
                    $sample,
                    [
                        "total_unpaid" => 0,
                        "client_id" => $notIncludedClient,
                        "subtotal_field" => $notIncludedClient,
                    ]
                );
            }
        }
        if ($is_periodic)
                foreach ($groups as $group)
                    if (!isset($reportData[$group])){
                        $reportData[$group] = array();
//
					}
                ksort($reportData);

        if($mergedClientIds) {
            $Client = GetObjectOrLoadModel('Client');
            $balances = $Client->clients_credit_local_currency($mergedClientIds);
            foreach ($reportData as $clientId => $item) {

                $balance = $balances[$clientId];
                if($balance < 0) {
                    $reportData[$clientId][0]['unpaids']['120+ '.__('Day',true)] +=  abs($balance);
                }
                $accountTotal = array_sum($reportData[$clientId][0]['unpaids']);
                if($accountTotal == 0 && !empty($this->params['data']['hide_zero_values'])) {
                    unset($reportData[$clientId]);
                }
            }
        }

        $report_title = $titles['report_title'];
        $params = $this->params;
        $this->set(compact('periods_count','period_length','reportData', 'params', 'groups', 'titles', 'is_periodic', 'report_title'), false);
        $this->set('type','transactions');


        return $this->viewVars;
    }

    public function getDataJSON() {
        parent::getDataJSON();

        $reportData = $this->viewVars['reportData'];
        $reportCurrencies = $this->viewVars['reportCurrencies'];

        $labels = array();
        $max = 0;
        $dateFormats = getDateFormats('std');
        $ownerFormat = $dateFormats[getAuthOwner('date_format')];
        foreach ($reportCurrencies as $curr) {
            $i = 0;
            $values = array();
            foreach ($reportData as $day => $row) {

                $labels[$i] = date('Y-m-d', strtotime($day));
                //$labels[$i] = date($ownerFormat, strtotime($day));
                if (isset($row[$curr])) {
                    $values[] = round($row[$curr][0]['total_amount'], 2);
                } else {
                    $values[] = 0;
                }
                ++$i;
            }

            $vmax = max($values);
            if ($vmax > $max) {
                $max = $vmax;
            }

            $rows[$curr] = $values;
        }


        $jsonData = $jsonData = array('title' => __('Daily Payments', true), 'values' => $rows, 'yMax' => ceil($max), 'xLabels' => $labels, 'group' => 'daily', 'chartType' => 'line');
        return $jsonData;
    }

}

