<?php
/**
 * Description of Client
 *
 * <AUTHOR>
 */
require_once dirname(dirname(__FILE__)) . DS . 'Revenue.php';

class Report_Revenue_Invoice extends Report_Revenue {

    public function getDataArray() {

        $filtered_fields = $this->get_filtered_fields();
		$subtotal_fields=array(
			'daily'=>'I.`date`',
			'weekly'=>'DATE_ADD(I.`date`, INTERVAL (0 - WEEKDAY(I.`date`)) DAY)',
			'monthly'=>'CONCAT(YEAR(I.`date`), "-", LPAD(MONTH(I.`date`), 2, 0))',
			'yearly'=>'YEAR(I.`date`)',
			'client'=>'C.business_name',
			'staff'=>'I.staff_id',
            'sales_person' => 'SP_IS.staff_id',
		);
		
		$title_params=array(
			'daily'=>array('column_title'=>__('Day',true), 'report_title'=>__('Detailed Daily Revenue',true),'graph_title'=>__('Daily Revenue',true)),
			'weekly'=>array('column_title'=>__('Week',true), 'report_title'=>__('Detailed Weekly Revenue',true),'graph_title'=>__('Weekly Revenue',true)),
			'monthly'=>array('column_title'=>__('Month',true), 'report_title'=>__('Detailed Monthly Revenue',true),'graph_title'=>__('Monthly Revenue',true)),
			'yearly'=>array('column_title'=>__('Year',true), 'report_title'=>__('Detailed Yearly Revenue',true),'graph_title'=>__('Yearly Revenue',true)),
			'client'=>array('column_title'=>__('Client',true), 'report_title'=>__('Detailed Revenue by Client',true),'graph_title'=>__('Revenue By Client',true)),
			'staff'=>array('column_title'=>__('Staff',true), 'report_title'=>__('Detailed Revenue by Staff Member',true),'graph_title'=>__('Revenue By Staff',true)),
            'sales_person' => array('column_title' => __('Sales Person', true), 'report_title' => __('Detailed Revenue by Sales Person', true), 'graph_title' => __('Revenue by Staff', true)),
		);
		$group_by='monthly';
		

		if (!empty($this->params['group_by'])&&!empty($subtotal_fields[$this->params['group_by']]))
		$group_by=$this->params['group_by'];
		
		$this->params['group_by']=$group_by;
		
		$is_periodic= in_array($group_by,array('daily','weekly','monthly','yearly'));
		
		$titles=$title_params[$group_by];


        if(count($filtered_fields) > 0)
        {
            foreach($filtered_fields as $k => $filter_field){
                $custom_field_query .= ', CD.field_'.$filter_field['CustomFormField']['id'] . ' AS `' .$filter_field['CustomFormField']['label'].'` ';
            }
        }
        $owner = getAuthOwner();
       $without_taxes= !empty($this->params['show_not_taxed']);
       $summary_taxes_select = $summary_taxes_join = $summary_taxes_select_sales_person = '';
       if ($without_taxes) {
                // Columns to include in SELECT when tax data is needed
                $summary_taxes_select = "
                    , IItem.totalTax1
                    , IItem.totalTax2
                    , IItem.totalSubtotal
                    , IItem.totalWithoutTax
                ";
            
                // Placeholder nulls for use when joining other contexts (e.g., sales person summary without detailed tax)
                $summary_taxes_select_sales_person = "
                    , NULL AS totalTax1
                    , NULL AS totalTax2
                    , NULL AS totalSubtotal
                    , NULL AS totalWithoutTax
                ";
            
                // Join clause to bring in aggregated tax/subtotal values from invoice_items
                $summary_taxes_join = "
                    LEFT JOIN (
                        SELECT  
                            invoice_id,
                            SUM(summary_tax1) AS totalTax1,
                            SUM(summary_tax2) AS totalTax2,
                            SUM(subtotal) AS totalSubtotal,
                            SUM(subtotal) - SUM(summary_tax1 + summary_tax2) AS totalWithoutTax
                        FROM invoice_items
                        GROUP BY invoice_id
                    ) AS IItem ON IItem.invoice_id = I.id
                ";
        }
        $query = "SELECT SP_IS.staff_id as sales_person,Journal.id as journal_id, Journal.currency_rate as currency_rate,C.bn1,C.bn1_label,C.bn2,C.bn2_label,`I`.`type`,I.`no`, I.`date`, I.summary_total, I.summary_paid, I.summary_unpaid, I.summary_refund, I.currency_code, I.client_id, IF(I.client_business_name =  '', C.business_name, I.client_business_name) as client_business_name, I.client_first_name, I.client_last_name, I.staff_id, ".$subtotal_fields[$group_by]." as `subtotal_field` {$custom_field_query} {$summary_taxes_select} FROM invoices as I LEFT JOIN (clients as C) ON (I.client_id = C.id) LEFT JOIN (staffs as S) ON (I.staff_id = S.id)";
        $query .= ' LEFT JOIN item_staffs SP_IS ON SP_IS.item_id = I.id AND SP_IS.item_type = ' . ItemStaff::SALES_ITEM_TYPE;
        $query .= " LEFT JOIN (invoices as SubscriptionInvoice) ON (I.subscription_id = SubscriptionInvoice.id)";
        $query .= " LEFT JOIN (journals as Journal) ON (I.id = Journal.entity_id AND Journal.entity_type = 'invoice')"; 
        $custom_table_name = "invoices_custom_data";
        if(count($filtered_fields) > 0)
        {
            $query.= " LEFT JOIN ($custom_table_name as CD) ON (CD.invoice_id = I.id) " ;
        }
        $query.=$summary_taxes_join;  
        $whereParts = array_merge(array('I.`type` in('.Invoice::DEBIT_NOTE.','.Invoice::Invoice.','.Invoice::Credit_Note.','.Invoice::Refund_Receipt.')'), $this->getWhereParts());
        $whereParts[] = "(  SubscriptionInvoice.type is null or SubscriptionInvoice.type not in (". Invoice::ADVANCE_PAYMENT .") )";
        if (isset($this->params['staff_id']) && is_array($this->params['staff_id']) && !empty($this->params['staff_id']) && (!empty($this->params['staff_id'][0]) || $this->params['staff_id'][0] === '0')) {
            if ($group_by != 'sales_person') {
                $whereParts[] = 'I.staff_id IN( ' . implode(",", $this->params['staff_id']) . ')';
            }else {
                $whereParts[] = '('.'SP_IS.staff_id IN( ' . implode(",", $this->params['staff_id']) . ')' . (in_array(0, $this->params['staff_id'])? ' OR (I.sales_person_id IS NULL)': '') . ')' ;
            }
        }
        if($group_by == 'sales_person'){
            $whereParts[] = '(I.sales_person_id != ' . Invoice::MULTIPLE_SALES_PERSONS.' OR I.sales_person_id IS NULL)';
        }
        // unexpected results
        // if (!check_permission(Invoices_View_All_Invoices)) {
        //     $whereParts[] = 'I.staff_id = ' . intval($owner['staff_id']);
        // }
        $query .= ' WHERE ' . implode(' AND ', $whereParts);
        /**
         * Sort the invoices based on the auto number in case prefix format is set
         */
        $invoiceAutoNumber = AutoNumber::get_rule(\AutoNumber::TYPE_INVOICE);
        $refundAutoNumber = AutoNumber::get_rule(\AutoNumber::TYPE_REFUND_RECEIPT);
        $creditNoteAutoNumber = AutoNumber::get_rule(\AutoNumber::TYPE_CREDIT_NOTE);
 
        // supporting multiple sales_person
        if($group_by == 'sales_person'){

            // We need to remove the SubscriptionInvoice.type condition. Duplicating this condition in the wrong place causes the report to throw an error.
            foreach ($whereParts as $key => $wherePart) {
                if (strpos($wherePart, 'SubscriptionInvoice.type') !== false) {
                    unset($whereParts[$key]);
                }
            }

            $query .= " UNION SELECT SP_IS.staff_id as sales_person,Journal.id as journal_id, Journal.currency_rate as currency_rate,C.bn1,C.bn1_label,C.bn2,C.bn2_label,`I`.`type`,I.`no`, I.`date`, SUM(II.subtotal) as summary_total, SUM((I.summary_paid / I.summary_total) * II.subtotal) as summary_paid, SUM((I.summary_unpaid / I.summary_total) * II.subtotal) as summary_unpaid, SUM((I.summary_refund / I.summary_total) * II.subtotal) as summary_refund, I.currency_code, I.client_id, IF(I.client_business_name =  '', C.business_name, I.client_business_name) as client_business_name, I.client_first_name, I.client_last_name, I.staff_id, ".$subtotal_fields[$group_by]." as `subtotal_field` {$custom_field_query}  {$summary_taxes_select_sales_person} FROM invoices as I JOIN (invoice_items as II) ON (I.id = II.invoice_id) LEFT JOIN (clients as C) ON (I.client_id = C.id) LEFT JOIN (staffs as S) ON (I.staff_id = S.id)";
            $query .= ' JOIN item_staffs SP_IS ON SP_IS.item_id = II.id AND SP_IS.item_type = ' . ItemStaff::INVOICE_ITEM_SALES_TYPE;
            if(count($filtered_fields) > 0)
            {
                $query.= " LEFT JOIN ($custom_table_name as CD) ON (CD.invoice_id = I.id) " ;
            }
            $query .= " LEFT JOIN (journals as Journal) ON (I.id = Journal.entity_id AND Journal.entity_type = 'invoice')";
            array_pop($whereParts);
            $whereParts[] = " I.sales_person_id = " . Invoice::MULTIPLE_SALES_PERSONS . " AND II.sales_person_id IS NOT NULL AND II.sales_person_id = SP_IS.staff_id";
            $query .= ' WHERE ' . implode(' AND ', $whereParts);
            $query .= " GROUP BY SP_IS.staff_id, SP_IS.group_id";
        }

        $orderByNo = false;
        if($invoiceAutoNumber['prefix_format']) {
            $count = strlen($invoiceAutoNumber['prefix_format']) + 1;
            $orderByNo = 'WHEN I.type = 0 THEN  (CAST(SUBSTRING(I.`no`, '. $count.') AS UNSIGNED) + 0)';
        }
        if($refundAutoNumber['prefix_format']) {
            $count = strlen($refundAutoNumber['prefix_format']) + 1;
            $orderByNo .= 'WHEN I.type = 6 THEN  (CAST(SUBSTRING(I.`no`, '. $count.') AS UNSIGNED) + 0)';
        }
        if($creditNoteAutoNumber['prefix_format']) {
            $count = strlen($creditNoteAutoNumber['prefix_format']) + 1;
            $orderByNo .= 'WHEN I.type = 5 THEN  (CAST(SUBSTRING(I.`no`, '. $count.') AS UNSIGNED) + 0)';
        }

        if(!$orderByNo) {
            $orderByNo = '(I.`no` + 0)';
        } else {
            $orderByNo = 'CASE '.$orderByNo.' END;';
        }
        if($group_by == 'sales_person'){
            $query .= ' ORDER BY subtotal_field, `date`, `no` ASC';
        }else{
            $query .= ' ORDER BY subtotal_field,I.`date` ASC, '.$orderByNo;
        }
    	//debug($query);
        $queryData = [];
        if(!isset($this->params['threshold_limit'])){
            $queryData = $this->Invoice->query($query);
        }

        if(!$this->params['is_summary'] && !isset($this->params['threshold_limit'])){
            $this->displayLimitThreshold($queryData, false, 'v2');
        }

		$flatData=array();
		$totalSales = ['paid' => 0, 'unpaid' => 0, 'total' => 0, 'refund' => 0];
		$totalRefund = ['paid' => 0, 'unpaid' => 0, 'total' => 0];
		$totalCredit = ['paid' => 0, 'unpaid' => 0, 'total' => 0];

		foreach($queryData as $row)
		{
            if($group_by == 'sales_person'){
                // the union remove the cake model schema so i had to remap the array
                $rowData = $row;
                $row = [
                    0 => ['client_business_name' => $row[0]['client_business_name'], 'subtotal_field' => $row[0]['subtotal_field']],
                    'I' => ['type' => $row[0]['type'], 'no' => $row[0]['no'], 'date' => $row[0]['date'], 'summary_total' => $row[0]['summary_total'], 'summary_paid' => $row[0]['summary_paid'], 'summary_unpaid' => $row[0]['summary_unpaid'], 'summary_refund' => $row[0]['summary_refund'], 'currency_code' => $row[0]['currency_code'], 'client_id' => $row[0]['client_id'], 'client_first_name' => $row[0]['client_first_name'], 'client_last_name' => $row[0]['client_last_name'], 'staff_id' => $row[0]['staff_id']],
                    'C' => ['bn1' => $row[0]['bn1'], 'bn1_label' => $row[0]['bn1_label'], 'bn2' => $row[0]['bn2'], 'bn2_label' => $row[0]['bn2_label']],
                    'SP_IS' => ['sales_person' => $row[0]['sales_person']],
                    'Journal' => ['id' => $row[0]['journal_id'], 'currency_rate' => $row[0]['currency_rate']]
                ];

                if ($without_taxes) {
                    $row['IItem'] =['totalTax1' => $rowData[0]['totalTax1'], 'totalTax2' => $rowData[0]['totalTax2'],'totalSubtotal' => $rowData[0]['totalSubtotal'],'totalWithoutTax' => $rowData[0]['totalWithoutTax']];
                }
            }
            if($row['I']['summary_paid']+$row['I']['summary_refund']+$row['I']['summary_unpaid'] > $row['I']['summary_total']){
                $row['I']['summary_unpaid'] = $row['I']['summary_total'] - $row['I']['summary_paid'] - $row['I']['summary_refund'];
            }
            if($row['I']['summary_unpaid'] != 0 && $row['I']['summary_refund'] != 0){
                if($row['I']['summary_unpaid'] > 0 && $row['I']['summary_refund'] < 0){
                    if($row['I']['summary_unpaid'] + $row['I']['summary_refund'] > 0){
                        $row['I']['summary_unpaid'] = $row['I']['summary_unpaid'] + $row['I']['summary_refund'];
                        $row['I']['summary_refund'] = 0;
                    }else{
                        $row['I']['summary_refund'] = $row['I']['summary_unpaid'] + $row['I']['summary_refund'];
                        $row['I']['summary_unpaid'] = 0;
                    }
                }elseif($row['I']['summary_unpaid'] < 0 && $row['I']['summary_refund'] > 0){
                    if($row['I']['summary_unpaid'] + $row['I']['summary_refund'] < 0){
                        $row['I']['summary_unpaid'] = $row['I']['summary_unpaid'] + $row['I']['summary_refund'];
                        $row['I']['summary_refund'] = 0;
                    }else if($row['I']['summary_unpaid'] > 0){ //case client pay full invoice and refund part of it without pay refund   summary_unpaid < 0
                        $row['I']['summary_refund'] = $row['I']['summary_unpaid']  > 0 ? $row['I']['summary_unpaid'] + $row['I']['summary_refund'] : $row['I']['summary_refund'];
                        $row['I']['summary_unpaid'] = 0;
                    }
                }
            }
            switch ($row['I']['type']) {
                case Invoice::Refund_Receipt:
                    $totalRefund['paid']+= $row['I']['summary_paid'];
                    $totalRefund['unpaid']+= $row['I']['summary_unpaid'];
                    $totalRefund['total']+= $row['I']['summary_total'];
                    $row['I']['summary_refund'] = $row['I']['summary_paid'] * -1;
                    $row['I']['no'] = $row['I']['no'] . ' ' . __('Refund Receipt', true);
                    $row['I']['summary_total'] = $row['I']['summary_total'] * -1;
                    $row['I']['summary_paid'] = 0;
                    $row['I']['summary_unpaid'] = $row['I']['summary_unpaid'] * -1;
                    if ($without_taxes){
                     $row['IItem']['totalWithoutTax'] = $row['IItem']['totalWithoutTax'] * -1;
                    }
                    break;
                case Invoice::Credit_Note:
                    $totalCredit['paid']+= $row['I']['summary_paid'];
                    $totalCredit['unpaid']+= $row['I']['summary_unpaid'];
                    $totalCredit['total']+= $row['I']['summary_total'];
                    $row['I']['no'] = $row['I']['no'] . ' ' . __('Credit Note', true);
                    $row['I']['summary_total'] = $row['I']['summary_total'] * -1;
                    $row['I']['summary_paid'] = 0;
                    $row['I']['summary_unpaid'] = $row['I']['summary_unpaid'] * -1;
                    if ($without_taxes){
                        $row['IItem']['totalWithoutTax'] = $row['IItem']['totalWithoutTax'] * -1;
                    }
                    break;
                case Invoice::DEBIT_NOTE:
                    $row['I']['no'] = $row['I']['no'] . ' ' . __('Debit Note', true);
                    $totalSales['paid']+= $row['I']['summary_paid'];
                    $totalSales['refund']+= $row['I']['summary_refund'] ?: 0;
                    $totalSales['unpaid']+= $row['I']['summary_unpaid'];
                    $totalSales['total']+= $row['I']['summary_total'];
                default:
                    $totalSales['paid']+= $row['I']['summary_paid'];
                    $totalSales['refund']+= $row['I']['summary_refund'] ?: 0;
                    $totalSales['unpaid']+= $row['I']['summary_unpaid'];
                    $totalSales['total']+= $row['I']['summary_total'];

            }
            $row['I']['summary_refund'] = $row['I']['summary_refund'] ?: '0';
			if(!isset($row[0])) $row[0]=array();
			$flatData[]=array_merge($row[0],$row['I'],$row['C'],$row['SP_IS'],$row['IItem']?? [] , $row['Journal'] );
		}
		$queryData=$flatData;
		$groups=array();
		if($is_periodic)
		$groups=$this->getPeriodsList($queryData[0]['subtotal_field'],$queryData[count($queryData)-1]['subtotal_field'],$group_by);
		
		
		foreach ($queryData as $row)
		{
		//print_pre($queryData);
                if(!$is_periodic)
			$groups[]=$row['subtotal_field'];
		}
		
		

        $reportCurrencies = $reportData = array();
        foreach ($queryData as $row) {
			
            $currency = $row['currency_code'];
			$group = $row['subtotal_field'];
            if (!isset($reportCurrencies[$currency])) {
                $reportCurrencies[$row['currency_code']] = $currency;
            }
            $reportData[$currency][$group][] = $row;
        }
		
		if($is_periodic)
		foreach( $reportData as $currency=>$groupData )
		{
			foreach($groups as $group)
				if(!isset($reportData[$currency][$group])) $reportData[$currency][$group]=array();
			  ksort($reportData[$currency]);
		}
		$report_title=$titles['report_title'];
        ksort($reportCurrencies);
		$params=$this->params;
        $this->set(compact('reportCurrencies', 'reportData','params','groups','titles','is_periodic','report_title'), false);
        $this->set('reportType', 'invoice'); 
        return $this->viewVars;
    }
    public function getDataJSON() {
        parent::getDataJSON();

        $reportData = $this->viewVars['reportData'];
        $reportCurrencies = $this->viewVars['reportCurrencies'];
		//debug($reportData);
        $totals = array('total' => array(), 'paid' => array(), 'unpaid' => array(), 'refund' => array());
        $labels = array();
        foreach ($reportData as $currency => $invoices) {
            $total = 0;
            $paid = 0;
            $unpaid = 0;
            $refund = 0;
            foreach ($invoices as $invoice) {
        		// $total += $invoice['summary_total'];
          //       $paid += $invoice['summary_paid'];
          //       $unpaid += $invoice['summary_unpaid'];

            	if (count($invoice)) {
            		$total += $invoice[0]['summary_total'];
	                $paid += $invoice[0]['summary_paid'];
	                $unpaid += $invoice[0]['summary_unpaid'];
                    $refund += $invoice[0]['summary_refund'];
            	}
                
            }

            $labels[] = $currency;
            $totals['total'][] = $total;
            $totals['paid'][] = $paid;
            $totals['unpaid'][] = $unpaid;
            $totals['refund'][] = $refund;
        }

        $rows[__('Total', true)] = $totals['total'];
        $rows[__('Paid', true)] = $totals['paid'];
        $rows[__('Unpaid', true)] = $totals['unpaid'];
        $rows[__('Refund', true)] = $totals['refund'];

        if (is_array($totals['total']) && count($totals['total']) > 0){
            $max = ceil(max($totals['total']));
        } else {
            $max = 0;
        }

        $jsonData = array('title' => __('Invoices Revenue', true), 'chartType' => 'bar', 'yMax' => $max, 'xLabels' => $labels, 'values' => $rows);
        return $jsonData;
    }

}
