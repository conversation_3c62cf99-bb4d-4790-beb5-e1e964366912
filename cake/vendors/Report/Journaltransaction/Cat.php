<?php

/**
 * Description of Client
 *
 * <AUTHOR>
 */

use App\Facades\Branch;
use App\Facades\Plugins;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Journal\Services\JournalAccountsBalanceCalculator;
use Izam\Daftra\Journal\Utils\AccountDisplayUtil;

require_once dirname(dirname(__FILE__)) . DS . 'JournalTransaction.php';

class Report_Journaltransaction_Cat extends Report_JournalTransaction {

    public function getDataArray() {
        $type = $_GET['type'] ?? '';
        $trialNet = $type == "trial-net";
        $showNet = $_GET['show_net'];
        $flag = $this->params['before_balance_display_type'] ?? 'balances_only';
        $this->set('flag', $flag);
        if(isset($this->params['data']['date_to']) && !isset($this->params['date_to'])) {
            $this->params['date_to'] = $this->params['data']['date_to'];
        }
        //THIS PART TO subtract the financial year from the incomes and outcomes
        $FY = GetObjectOrLoadModel('FinancialYear');
        $financialYear = $FY->get_last_FY_before_date($FY->formatDate($this->params['date_from']));
        if(!$financialYear['FinancialYear']['is_closed']) {
            $financialYear = false;
        }
        if($financialYear) {
            $expensesAndIncomesAccountsList = [];
            $expensesAndIncomesCatsList = [];
            $JournalCat = GetObjectOrLoadModel('JournalCat');
            $journalCats = $JournalCat->find('all',['conditions' => ['JournalCat.entity_type' => ['incomes', 'expenses']]]);
            foreach ($journalCats as $journalCat) {
                $expensesAndIncomesAccountsList[] = $journalCat['JournalCat']['id'];
                $expensesAndIncomesCatsList[] = $journalCat['JournalCat']['id'];
                $accountsIdsList = $this->Journal->get_all_accounts_recursive($journalCat['JournalCat']['id']);
                $catsIdList = $this->Journal->get_all_cats_recursive($journalCat['JournalCat']['id']);
                $expensesAndIncomesAccountsList = array_merge($expensesAndIncomesAccountsList, $accountsIdsList);
                $expensesAndIncomesCatsList = array_merge($expensesAndIncomesCatsList, $catsIdList);
            }
        }
        $this->getWhereParts();
        $cat_id = $this->params['cat_id'] ? $this->params['cat_id'] : $this->params['journal_cat_id'];
        $level = $this->params['level']??null;
        if($trialNet) {
            $level = false;
        }
        if(!empty($this->params['journal_account_id'])){
            $JA = GetObjectOrLoadModel('JournalAccount');
            $journal_account_ids = $this->params['journal_account_id'];
            if(!is_array($journal_account_ids)) {
                $journal_account_ids = [$journal_account_ids];
            }
            $records = [];
            foreach ($journal_account_ids as $journal_account_id) {
                $journalAccountRecord = $JA->findById($journal_account_id);
                $records[] = ['id' => $journalAccountRecord['JournalAccount']['id'], 'code' => $journalAccountRecord['JournalAccount']['code']];
            }
            $journal_account_ids = $records;

        }
        // i just called it to get set view variables
        $query = "SELECT JC.id,JC.name,JC.code from journal_cats JC ";
        if (((!isset($cat_id) || empty($cat_id)) && empty($journal_account_ids)) && !$level)
            $cat_id = -1;

        $whereParts = [];
        if(!empty($cat_id)) {
            $whereParts[] = "JC.journal_cat_id = $cat_id";
        }
        if(ifPluginActive(BranchesPlugin) && settings::getValue(BranchesPlugin,'specify_accounts_branches')){
//            $itemPermission = GetObjectOrLoadModel('ItemPermission');
//            $accountIds = $itemPermission->getBranchAccountsIds(ItemPermission::ITEM_TYPE_JOURNAL_CAT);
            $JournalCat = GetObjectOrLoadModel('JournalCat');
            $accountIds = $JournalCat->getPermittedCatIds();

            if($accountIds)
            {
                $whereParts[] = 'JC.id in ('.implode(',', $accountIds).')';
            }
        }
        $accountsDisplay = isset($this->params['accounts_display']) ? $this->params['accounts_display'] : null;
        if($accountsDisplay) {
            $accountsDisplayValue = $this->params['accounts_display'];
            switch ($accountsDisplayValue) {
                case \Izam\Daftra\Journal\Utils\AccountDisplayUtil::DISPLAY_ACCOUNTS_WITH_TRANSACTIONS:
                    $whereParts[] = Journal::getJournalCatWithTransactionsCondition('JC.id');
                    break;
            }
        }

        if($level) {
            $whereParts[]= "JC.level = {$this->params['level']}";
        }
        $whereParts[] = "JC.is_hidden = 0";
        $query .= 'where '.implode(' AND ',$whereParts);
        $query .= ' order by code ASC';
        $balanceOnly = (isset($_GET['balance_only']) && $_GET['balance_only'] == 1)?true:false;
        $this->set('balanceOnly', $balanceOnly);
        $reportCurrencies = array();
        if($cat_id || $level){
            $queryData = $this->JournalTransaction->query($query);
            foreach ($queryData as $k => &$v) {
                $this->params['fy_id'] = $_GET['fy_id'];
                $v[] = $this->Journal->report_caculate_cat_total($v['JC']['id'], $this->params);
                if($this->params['date_from']){
                    $fy_id = $_GET['fy_id'] ?? -1;
                    $beforeConditions = array('currency' => $currency,'date_to' => $this->params['date_from'], 'fy_id' => $fy_id, 'cost_center_id' => $this->params['cost_center_id']);
                    $includeOpening = !empty($this->params['fy_id']) && is_array($this->params['fy_id']) && count($this->params['fy_id']) > 1;
                    $v['before'] = $this->Journal->report_caculate_cat_total($v['JC']['id'],$beforeConditions ,false, $includeOpening);
                }
                if($accountsDisplay === AccountDisplayUtil::HIDE_ZERO_ACCOUNTS && abs($v[0]['total_credit'] - $v[0]['total_debit']) < 0.01) {
                    unset($queryData[$k]);
                    continue;
                }
                $v['after']['total_credit'] = $v[0]['total_credit'] + $v['before']['total_credit'] ;
                $v['after']['total_debit'] = $v[0]['total_debit'] + $v['before']['total_debit'] ;
                if($_GET['show_net'])
                {
                    if(($_GET['date_to'] && $_GET['date_from']) || $balanceOnly)
                    {
                        $report_title = __('Trial Balance & Nets Report', true);
                        if($v['after']['total_credit'] > $v['after']['total_debit'])
                            $v['net']['credit'] = $v['after']['total_credit'] - $v['after']['total_debit'];
                        else
                            $v['net']['debit'] = $v['after']['total_debit'] - $v['after']['total_credit'] ;
                    }
                    else if($_GET['date_from'] ){
                        $report_title = __('Trial Nets Report', true);
                        if($v[0]['total_credit'] > $v[0]['total_debit'])
                            $v['net']['credit'] = $v[0]['total_credit'] - $v[0]['total_debit'];
                        else
                            $v['net']['debit'] = $v[0]['total_debit'] - $v[0]['total_credit'] ;
                    }
                }else{
                    $report_title = __('Trial Balance Report', true);
                }
            }
            $reportData = array();
            $reportCurrencies = array($currency);
            foreach ($queryData as $row) {
                if (!isset($reportCurrencies[$row['JT']['currency_code']]) && !empty($reportCurrencies[$row['JT']['currency_code']])) {
                    $reportCurrencies[$row['JT']['currency_code']] = $row['JT']['currency_code'];
                }
                $name = __at($row['JC']['name'])/* .  (!empty($row['C']['first_name'])? ' ('.$row['C']['first_name'] . ' ' . $row['C']['last_name'].')':'') */;
                if (empty($name))
                    $name = '[' . strtoupper(__('Removed', true)) . ']';

                $reportData[$row['JC']['id']][$currency] = $row;
                $reportData[$row['JC']['id']]['name'] = $name;
                $reportData[$row['JC']['id']][$currency]['JC']['has_children'] = $this->Journal->cat_has_children($row['JC']['id']);
                $reportData[$row['JC']['id']][$currency]['JT']['currency_code'] = $currency;
                $reportData[$row['JC']['id']]['code'] = $row['JC']['code'];
                $reportData[$row['JC']['id']]['acc_id'] = $row['JC']['id'];
                
            }

        }
        $JournalAccount = GetObjectOrLoadModel('JournalAccount');
        if(empty($journal_account_ids)) {
            $conditions = [];
            if(!empty($cat_id)){
                $conditions['JournalAccount.journal_cat_id'] = $cat_id;
            }else if(!empty ($account_ids)){
                $conditions['JournalAccount.id'] = $account_ids;
            }
            if($accountsDisplay === AccountDisplayUtil::DISPLAY_ACCOUNTS_WITH_TRANSACTIONS) {
                $conditions[] = Journal::getJournalAccountsWithTransactionsCondition();
            }
            if($level) {
                $conditions[] = "JournalAccount.level <= $level";
            }
            $whereConditionToGetUnhiddenAccounts = $JournalAccount->buildJournalAccountUnHiddenCondition("JournalAccount", ['date_from' => $JournalAccount->formatDate($this->params['date_from']), 'date_to' => $JournalAccount->formatDate($this->params['date_to'])]);
            $conditions[] = [$whereConditionToGetUnhiddenAccounts];

            $accounts = $JournalAccount->getJournalAccounts('all', [ 'conditions' => $conditions ]);
            $i = 0;
            $account_ids = [];
            //push accounts to the ids
            foreach ($accounts as $account){
                $account_ids[$i]['id'] = $account['JournalAccount']['id'];
                $account_ids[$i]['code'] = $account['JournalAccount']['code'];
                $account_ids[$i]['name'] = $account['JournalAccount']['name'];
                $i++;
            }
            $journal_account_ids = $account_ids;
        }
        $accountsBalanceRequestCalculator = new \Izam\Daftra\Journal\Services\JournalAccountBalanceCalculatorRequest(array_column($journal_account_ids, 'id'));
        $accountsBalanceRequestCalculator->setDateFrom($JournalAccount->formatDate($this->params['date_from']))
            ->setDateTo($JournalAccount->formatDate($this->params['date_to']))
            ->setDateToEqual(true)
            ->setDateFromEqual(true)
            ->setCostCenterIds(!empty($this->params['cost_center_id']) ? $this->params['cost_center_id'] : [])
            ->setStaffId($_GET['staff_id'])
            ->setFinancialYearId($_GET['fy_id']);
        if (ifPluginActive(BranchesPlugin)) {

            $branchId = ""; 
            if(isset($this->params['branch_id'])){
                $branchId = $this->params['branch_id'];
            }else if(isset($this->params['branch_transactions']) && $this->params['branch_transactions'] != '-1'){
                $branchId = $this->params['branch_transactions'];
            }

            $accountsBalanceRequestCalculator->setBranchId($branchId);
        }
        $calculator = new JournalAccountsBalanceCalculator(getPDO());
        $isCostCenter = isset($_GET['cost_center_id']) && !empty($_GET['cost_center_id']);

        $accountsBalance = $isCostCenter
            ? $calculator->calculateCostCenters($accountsBalanceRequestCalculator)
            : $calculator->calculate($accountsBalanceRequestCalculator);

        $accountsBalanceRequestCalculator->setDateTo($JournalAccount->formatDate($this->params['date_from']));
        $accountsBalanceRequestCalculator->setDateToEqual(false);
        $accountsBalanceRequestCalculator->setDateFrom(false);

        $accountsBalanceBefore = $isCostCenter
            ? $calculator->calculateCostCenters($accountsBalanceRequestCalculator)
            : $calculator->calculate($accountsBalanceRequestCalculator);

        if(!empty($journal_account_ids)){

            foreach ($journal_account_ids as $journal_account_id) {
                $account_unique_key = $cat_id."-".$journal_account_id['id'];
                $accountTotals = $accountsBalance[$journal_account_id['id']];
                $reportData[$account_unique_key][$currency][] = $accountTotals;
                if($accountsDisplay === AccountDisplayUtil::HIDE_ZERO_ACCOUNTS && abs($reportData[$account_unique_key][$currency][0]['total_credit'] - $reportData[$account_unique_key][$currency][0]['total_debit']) < 0.01) {
                    unset($reportData[$account_unique_key][$currency]);
                    continue;
                }

                if($this->params['date_from']){
                    $beforeConditions = array('currency' => $this->params['currency'],'date_to' => $this->params['date_from']);
//                    if($financialYear && in_array($journal_account_id['id'], $expensesAndIncomesAccountsList)) {
//                        $beforeConditions['date_from'] = $financialYear['FinancialYear']['end_date'];
//                    }
                    $beforeConditions['fy_id']=false;
                    if(isset($this->params['fy_id']))  $beforeConditions['fy_id']=$this->params['fy_id'];

                    $before = $accountsBalanceBefore[$journal_account_id['id']];
                    if($flag == "balances_only"){
                        if($before['total_credit'] > $before['total_debit']) {
                            $before['total_credit'] = $before['total_credit'] - $before['total_debit'];
                            $before['total_debit'] = 0;
                        } else {
                            // when 1939.705 -1037 = 902.705    but  in   php has bug 1939.705 -1037 =902.************* so we need to round result
                            $before['total_debit'] = round($before['total_debit'] - $before['total_credit'],7) ;
                            $before['total_credit'] = 0;
                        }
                    }

                    $reportData[$account_unique_key][$currency]['before'] = $before;
                }
                $reportData[$account_unique_key][$currency]['after']['total_credit'] = $reportData[$account_unique_key][$currency]['before']['total_credit'] + $reportData[$account_unique_key][$currency][0]['total_credit'];
                $reportData[$account_unique_key][$currency]['after']['total_debit'] = $reportData[$account_unique_key][$currency]['before']['total_debit'] + $reportData[$account_unique_key][$currency][0]['total_debit'];
                // this block is to show the net column
                if($_GET['show_net'])
                {
                    // if both date from and date to are set then the net_credit = after_credit - after_debit equation are respectively
                    if(($_GET['date_from']  && $_GET['date_to']) || $balanceOnly)
                    {
                        if($reportData[$account_unique_key][$currency]['after']['total_credit'] > $reportData[$account_unique_key][$currency]['after']['total_debit'])
                            $reportData[$account_unique_key][$currency]['net']['credit'] = $reportData[$account_unique_key][$currency]['after']['total_credit'] - $reportData[$account_unique_key][$currency]['after']['total_debit'];
                        else
                            $reportData[$account_unique_key][$currency]['net']['debit'] = $reportData[$account_unique_key][$currency]['after']['total_debit'] - $reportData[$account_unique_key][$currency]['after']['total_credit'];
                    }
                    // else if only date_to then the net = credit - debit all equation are respectively
                    else if($_GET['date_from'])
                    {
                        if($reportData[$account_unique_key][$currency][0]['total_credit'] > $reportData[$account_unique_key][$currency][0]['total_debit'])
                            $reportData[$account_unique_key][$currency]['net']['credit'] = $reportData[$account_unique_key][$currency][0]['total_credit'] - $reportData[$account_unique_key][$currency][0]['total_debit'];
                        else
                            $reportData[$account_unique_key][$currency]['net']['debit'] = $reportData[$account_unique_key][$currency][0]['total_debit'] - $reportData[$account_unique_key][$currency][0]['total_credit'];
                    }

                    if($accountsDisplay === AccountDisplayUtil::HIDE_ZERO_BALANCES && abs($reportData[$account_unique_key][$currency]['net']['credit'] - $reportData[$account_unique_key][$currency]['net']['debit']) < 0.01) {
                        unset($reportData[$account_unique_key][$currency]);
                        continue;
                    }
                }

                $accountDetails = $JournalAccount->findById($journal_account_id['id']);

                $reportData[$account_unique_key][$currency]['JA'] = true;
                $reportData[$account_unique_key][$currency]['JT']['currency_code'] = $currency;
                $reportData[$account_unique_key][$currency]['code']= $journal_account_id['code'];
                $reportData[$account_unique_key]['code'] = $journal_account_id['code'];
                $reportData[$account_unique_key]['name']= __at($journal_account_id['name'])??$accountDetails['JournalAccount']['name'];
                $reportData[$account_unique_key]['acc_id'] = $journal_account_id['id'];
                

                //  print_pre($reportData[$account_unique_key]);
//                if( !empty($_GET['hide_zero'])&& $_GET['hide_zero']==1
//                    &&(empty($reportData[$account_unique_key][$currency]['before']['total_credit'])||$reportData[$account_unique_key][$currency]['before']['total_credit']==0)
//                    &&(empty($reportData[$account_unique_key][$currency]['before']['total_debit'])||$reportData[$account_unique_key][$currency]['before']['total_debit']==0)
//                    &&(empty($reportData[$account_unique_key][$currency]['after']['total_credit'])||$reportData[$account_unique_key][$currency]['after']['total_credit']==0)
//                    &&(empty($reportData[$account_unique_key][$currency]['after']['total_debit'])||$reportData[$account_unique_key][$currency]['after']['total_debit']==0)
//                    &&(empty($reportData[$account_unique_key][$currency][0]['total_credit'])||$reportData[$account_unique_key][$currency][0]['total_credit']==0)
//                    &&(empty($reportData[$account_unique_key][$currency][0]['total_debit'])||$reportData[$account_unique_key][$currency][0]['total_debit']==0)
//                 ) unset($reportData[$account_unique_key]);
             //   print_pre($reportData[$account_unique_key]);

            }
         //   die();
      //  //  print_pre($reportData);die();


        }
        $currency = mysql_escape_string($_GET['currency'] ? $_GET['currency']  : $this->Journal->get_default_currency() );
        $show_net = $_GET['show_net'];

        $this->set('show_net', $show_net);
        if($balanceOnly || $type == "trial-net")
        {
            $report_title = __('Trial Nets Report', true);
        }
        else if($type == 'trial-balance-nets')
        {
            $report_title = __('Trial Balance & Nets Report', true);
        }else if($type == 'trial-net' ){
            $report_title = __('Trial Nets Report', true);
        }else if ($type == 'trial-balance'){
            $report_title = __('Trial Balance Report', true);
        }
        ksort($reportCurrencies);
        $maxLevelQuery = 'select max(accounts_cats_max_level.max_level) as max_level from (select max(level) as max_level from journal_accounts UNION select max(level) as max_level from journal_cats) as accounts_cats_max_level;';
        $maxLevel = $JournalAccount->query($maxLevelQuery);
        $maxLevel = $maxLevel[0][0]['max_level'];
        if (count($reportData)) {
            $reportData = sortByKey($reportData, 'code', 'asc', SORT_STRING);
        }
        $this->set(compact('maxLevel', 'reportCurrencies', 'reportData', 'date_from', 'date_to', 'currency', 'report_title'), false);
        $this->set('reportType', 'cat');
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' && !(isset($_GET["currency"]) && $_GET["currency"] == -1)) {
            die(json_encode($reportData, JSON_UNESCAPED_UNICODE));
        }
        return $this->viewVars;
    }

    public function getDataJSON() {
        parent::getDataJSON();
        $jsonData = array('chartType' => 'line', 'title' => __('Journal Transactions By Account', true));

        $reportCurrencies = $this->viewVars['reportCurrencies'];
        $reportData = $this->viewVars['reportData'];
        $max = 0;
        $labels = array();
        $rows = array();
        foreach ($reportCurrencies as $curr) {
            $values = $credit = $debit = array();
            foreach ($reportData as $account => $row) {
                $labels[$account] = $row['name'];
                if (isset($row[$curr])) {
                    $credit[] = round($row[$curr][0]['total_debit'], 2);
                    $debit[] = round($row[$curr][0]['total_credit'], 2);

                    $values[] = $value;
                    if ($value > $max) {
                        $max = $value;
                    }
                } else {
                    $values[] = $credit[] = $debit[] = 0;
                }
            }

            $rows[$curr] = $values; // $paid, $unpaid);
        }

        $jsonData['values'] = $rows;
        $jsonData['xLabels'] = array_values($labels);
        $jsonData['yMax'] = ceil($max);
        $jsonData['chartType'] = 'bar';
        return $jsonData;
    }

}
