<?php

require_once dirname(dirname(__FILE__)) . DS . 'CostCenter.php';

class Report_CostCenter_CostCenter extends Report_CostCenter {

    public function getDataArray() {
        $owner = getAuthOwner();



        $group_by_fields = array(
            'daily' => 'date(J.`date`)',
            'weekly' => 'CONCAT_WS("-", YEAR(J.`date`), LPAD(MONTH(DATE_ADD(J.`date`, INTERVAL (0 - WEEKDAY(J.`date`)) DAY)), 2, 0), LPAD(DAY(DATE_ADD(J.`date`, INTERVAL (0 - WEEKDAY(J.`date`)) DAY)), 2, 0))',
            'monthly' => 'CONCAT(YEAR(J.`date`), "-", LPAD(MONTH(J.`date`), 2, 0))',
            'yearly' => 'YEAR(J.`date`)',
            'cost_center' => 'T.cost_center_id',
            'journal_account' => 'T.journal_account_id',
        );


  $group_by_title_params = array(
            'daily' => array('column_title' => __('Day', true), 'report_title' => __('Daily Cost Center Transactions', true), 'graph_title' => __('Daily Cost Center Transactions', true)),
            'weekly' => array('column_title' => __('Week', true), 'report_title' => __('Weekly Cost Center Transactions', true), 'graph_title' => __('Weekly Cost Center Transactions', true)),
            'monthly' => array('column_title' => __('Month', true), 'report_title' => __('Monthly Cost Center Transactions', true), 'graph_title' => __('Monthly Cost Center Transactions', true)),
            'yearly' => array('column_title' => __('Year', true), 'report_title' => __('Yearly Cost Center Transactions', true), 'graph_title' => __('Yearly Cost Center Transactions', true)),
            'cost_center' => array('column_title' => __('Cost Centers', true), 'report_title' => __('Cost Center Transactions', true), 'graph_title' => __('Cost Centers', true)),
            'journal_account' => array('column_title' => __('Cost Centers Transactions By Journal Account', true), 'report_title' => __('Cost Centers Transactions By Journal Account', true), 'graph_title' => __('Cost Centers Transactions By Journal Account', true)),

        );
  
  

        $group_by = 'monthly';
        if (!empty($this->params['group_by']) && !empty($group_by_fields[$this->params['group_by']])){
            $group_by = $this->params['group_by'];
		}


        $this->params['group_by'] = $group_by;
        $is_periodic = in_array($group_by, array('daily', 'weekly', 'monthly', 'yearly'));
        $titles = $group_by_title_params[$group_by];
		
        $this->params['is_summary'] = empty($this->params['is_summary']) ? 0 : 1;
        $query = "SELECT J.number,T.*,J.date,IF(JT_V2.id, JT_V2.description, GROUP_CONCAT(JT.description SEPARATOR ' / ')) AS description,JT_V2.alter_description,JT.alter_description,CC.name cost_center_name,CC.code cost_center_code,JA.name journal_account_name,JA.code journal_account_code, " . $group_by_fields[$group_by] . " as `subtotal_field`" ;
//		if($is_periodic)
//			$query .= ', count(IT.tag_id) as count' ;
		$query .= "  FROM cost_center_transactions as T"
				. " JOIN (journals as J) ON (T.journal_id = J.id) " // for invoice,credit_not,and refund reciept
				. " LEFT JOIN (journal_transactions as JT) ON (JT.journal_account_id = T.journal_account_id AND JT.journal_id = J.id AND T.journal_transaction_id IS NULL) " // for invoice,credit_not,and refund reciept
                . " LEFT JOIN (journal_transactions as JT_V2) ON (T.journal_transaction_id IS NOT NULL AND JT_V2.id = T.journal_transaction_id) "
                . " JOIN (journal_accounts as JA) ON (JA.id = T.journal_account_id) "
				. " JOIN (cost_centers as CC) ON (CC.id = T.cost_center_id) "
				. "" ;
        $whereParts = array_merge(array(), $this->getWhereParts());

        if (ifPluginActive(BranchesPlugin) && !empty($this->params['data']['branch_id'])) {
            $branchConditions = [
                'daily' => 'J.`branch_id`',
                'weekly' => 'J.`branch_id`',
                'monthly' => 'J.`branch_id`',
                'yearly' => 'J.`branch_id`',
                'cost_center' => 'J.`branch_id`',
                'journal_account' => 'J.`branch_id`',
            ];
            $branchCondition = $branchConditions[$this->params['group_by']];
            if($branchCondition) {
                $whereParts[] = $branchCondition.' IN (' . implode(',',$this->params['data']['branch_id']) . ')';
            }
        }

        if(count($whereParts) > 0)
        $query .= ' WHERE ' . implode(' AND ', $whereParts);

//        if($group_by === 'cost_center') {
        $query .= ' GROUP BY T.id ';
//        }
		if($is_periodic)
        $query .= ' ORDER BY subtotal_field, J.`date` asc ';

		if(!$is_periodic) {
			$query .= 'ORDER BY CC.`code` ASC, J.`date` ASC';
		}

        if(!isset($this->params['threshold_limit'])){
            $queryData = $this->CostCenterTransaction->query($query);
        }


        foreach ($queryData as $row) {
            if (!isset($row[0]))
                $row[0] = array();
            $flatData[] = array_merge($row[0],$row['JT'], $row['T'], $row['J'], $row['JA'], $row['CC'],$row['JT_V2']??[]);
        }
        $queryData = $flatData;

        if(!$this->params['is_summary'] && !isset($this->params['threshold_limit'])){ 
            $this->displayLimitThreshold($flatData, false, 'v2');
        }
            
       if (low($this->params['group_by']) == 'yearly') {
        $dateFormat = 'Y';
       } elseif (low($this->params['group_by']) == 'daily') {
       $dateFormat = 'Y-m-d';    
       } elseif (low($this->params['group_by']) == 'weekly') {
       $dateFormat = 'Y-m-d';       
       } elseif (low($this->params['group_by']) == 'lastyear'||low($this->params['group_by']) == 'monthly') {
       $dateFormat = 'Y-m';       
       }else{
        $dateFormat = 'Y-m-d';           
       }
        
        $startDate = !empty($this->viewVars['date_from']) ? date($dateFormat, strtotime($this->viewVars['date_from'])) : $dateToStart;
        $endDate = !empty($this->viewVars['date_to']) ? date($dateFormat, strtotime($this->viewVars['date_to'])) : $dateToEnd;
        $groups = array();
        if ($is_periodic){
            $groups = $this->getPeriodsList($startDate, $endDate, $group_by);
        }
        
        foreach ($queryData as $row) {
            if (!$is_periodic)
                $groups[] = $row['subtotal_field'];
        }

        $reportData = [];
		foreach ($queryData as $row) {
            $group = $row['subtotal_field'];
            
            $reportData[$group][] = $row;
//            $reportData[$currency]['methods'][$row['payment_method']]+=$row['amount'];
        }
        if ($is_periodic) {
            foreach ($groups as $group) {
                if($group) {
                    if (!isset($reportData[$group])){
                        $reportData[$group] = array();
//
                    }
                }

            }
            ksort($reportData);
        }

				
        $report_title = $titles['report_title'];
        $params = $this->params;

        $this->set(compact('reportData', 'params', 'groups', 'titles', 'is_periodic', 'report_title'), false);
        $this->set('type','transactions');


        return $this->viewVars;
    }

    public function getDataJSON() {
        parent::getDataJSON();

        $reportData = $this->viewVars['reportData'];
        $reportCurrencies = $this->viewVars['reportCurrencies'];

        $labels = array();
        $max = 0;
        $dateFormats = getDateFormats('std');
        $ownerFormat = $dateFormats[getAuthOwner('date_format')];
        foreach ($reportCurrencies as $curr) {
            $i = 0;
            $values = array();
            foreach ($reportData as $day => $row) {

                $labels[$i] = date('Y-m-d', strtotime($day));
                //$labels[$i] = date($ownerFormat, strtotime($day));
                if (isset($row[$curr])) {
                    $values[] = round($row[$curr][0]['total_amount'], 2);
                } else {
                    $values[] = 0;
                }
                ++$i;
            }

            $vmax = max($values);
            if ($vmax > $max) {
                $max = $vmax;
            }

            $rows[$curr] = $values;
        }


        $jsonData = $jsonData = array('title' => __('Daily Payments', true), 'values' => $rows, 'yMax' => ceil($max), 'xLabels' => $labels, 'group' => 'daily', 'chartType' => 'line');
        return $jsonData;
    }

}

