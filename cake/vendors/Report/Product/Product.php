<?php

require_once dirname(dirname(__FILE__)) . DS . 'Product.php';

class Report_Product_Product extends Report_Product {

    public function getDataArray() {
        $owner = getAuthOwner();

        $subtotal_fields = array(
            'daily' => 'date(Invoice.`date`)',
            'weekly' => 'CONCAT_WS("-", YEAR(Invoice.`date`), LPAD(MONTH(DATE_ADD(Invoice.`date`, INTERVAL (0 - WEEKDAY(Invoice.`date`)) DAY)), 2, 0), LPAD(DAY(DATE_ADD(Invoice.`date`, INTERVAL (0 - WEEKDAY(Invoice.`date`)) DAY)), 2, 0))',
            'monthly' => 'CONCAT(YEAR(Invoice.`date`), "-", LPAD(MONTH(Invoice.`date`), 2, 0))',
            'yearly' => 'YEAR(Invoice.`date`)',
            'staff' => 'Invoice.staff_id',
            'sales_person' => 'SP_IS.staff_id',
            'collected_by' => 'IP.staff_id',
            'client' => 'Client.id',
            'product' => 'I.product_id',
            'category' => 'GROUP_CONCAT(IC.category_id)',
            'brand' => 'IP.brand',
        );


        $title_params = array(
            'daily' => array('column_title' => __('Day', true), 'report_title' => __('Daily Product Sales', true), 'graph_title' => __('Daily Products', true)),
            'weekly' => array('column_title' => __('Week', true), 'report_title' => __('Weekly Product Sales', true), 'graph_title' => __('Weekly Products', true)),
            'monthly' => array('column_title' => __('Month', true), 'report_title' => __('Monthly Product Sales', true), 'graph_title' => __('Monthly Products', true)),
            'yearly' => array('column_title' => __('Year', true), 'report_title' => __('Yearly Product Sales', true), 'graph_title' => __('Yearly Products', true)),
            'staff' => array('column_title' => __('Staff', true), 'report_title' => __('Item Sales by Staff', true), 'graph_title' => __('Item Sales by Staff', true)),
            'sales_person' => array('column_title' => __('Sales Person', true), 'report_title' => __('Item Sales by Sales Person', true), 'graph_title' => __('Item Sales by Sales Person', true)),
            'product' => array('column_title' => __('Product', true), 'report_title' => __('Item Sales by Item', true), 'graph_title' => __('Item Sales by Item', true)),
            'client' => array('column_title' => __('Client', true), 'report_title' => __('Item Sales by Client', true), 'graph_title' => __('Item Sales by Client', true)),
            'category' => array('column_title' => __('Category', true), 'report_title' => __('Item Sales by Category', true), 'graph_title' => __('Item Sales by Category', true)),
            'brand' => array('column_title' => __('Brand', true), 'report_title' => __('Item Sales by Brand', true), 'graph_title' => __('Item Sales by Brand', true)),
        );

        $group_by = 'monthly';
        if (!empty($this->params['group_by']) && !empty($subtotal_fields[$this->params['group_by']]))
            $group_by = $this->params['group_by'];
        $this->params['group_by'] = $group_by;
        $is_periodic = in_array($group_by, array( 'daily', 'weekly', 'monthly', 'yearly'));
        $is_category = $group_by == "category" ; 
        $titles = $title_params[$group_by];
        
        $this->params['is_summary'] = empty($this->params['is_summary']) ? 0 : 1;

        $query = "
                    SELECT SP_IS.staff_id as sales_person, SP_IS2.staff_id as sales_person,I.calculated_discount,Client.business_name , Client.client_number ,Client.id as client_id,Invoice.type,Invoice.discount_amount,Invoice.discount,Invoice.summary_subtotal, Invoice.no as invoice_number , IP.id, 
                    ( CASE  WHEN IP.name IS NOT NULL AND IP.name != '' THEN IP.name  ELSE (case when I.description is not null and I.description != '' then I.description else I.item end)  END) as name, IP.product_code,I.quantity, 
                    
                    (CASE
                    WHEN IT1.included = 1 AND IT2.included = 1 THEN I.unit_price - COALESCE(I.summary_tax1/I.quantity, 0) - COALESCE(I.summary_tax2/I.quantity, 0)
                    WHEN IT1.included = 1 THEN I.unit_price - COALESCE(I.summary_tax1/I.quantity, 0)
                    WHEN IT2.included = 1 THEN I.unit_price - COALESCE(I.summary_tax2/I.quantity, 0)
                    ELSE I.unit_price END) AS unit_price, 
                    
                    I.invoice_id,Invoice.date ,I.quantity, I.summary_tax1 , I.summary_tax2 ,Invoice.currency_code,Invoice.staff_id as `collected_by`, Invoice.staff_id, " . $subtotal_fields[$group_by] . " as `subtotal_field`  
                    FROM invoices as Invoice 
                    LEFT JOIN (invoice_items as I) ON (Invoice.id = I.invoice_id) 
                    LEFT JOIN (clients as Client ) ON ( Client.id = Invoice.client_id )
                    LEFT JOIN (products as IP) ON (I.product_id=IP.id) 
                    LEFT JOIN (items_categories as IC) ON (IP.id=IC.item_id AND IC.item_type = ".ItemsCategory::ITEM_TYPE_PRODUCT.")
                    LEFT JOIN (invoice_taxes AS IT1) ON (I.invoice_id = IT1.invoice_id AND I.tax1 = IT1.tax_id)
                    LEFT JOIN (invoice_taxes AS IT2) ON (I.invoice_id = IT2.invoice_id AND I.tax2 = IT2.tax_id)
                    ";
        $query .= ' LEFT JOIN item_staffs SP_IS ON SP_IS.item_id = Invoice.id AND SP_IS.item_type = ' . ItemStaff::SALES_ITEM_TYPE;
        // supporting multiple sales_person
        $query .= ' LEFT JOIN item_staffs SP_IS2 ON SP_IS2.item_id = I.id AND SP_IS2.item_type = ' . ItemStaff::INVOICE_ITEM_SALES_TYPE;
        $whereParts = array_merge(array('Invoice.date > \'1969-12-31\''), $this->getWhereParts());
        if (!empty($this->params['product_id'])) {
            if (substr($this->params['product_id']??'', 0, 1) === 'g') {// if the product is item group record
                $itemGroupId = intval(str_replace("g","",$this->params['product_id']));
                $whereParts[] = 'IP.item_group_id = ' . $itemGroupId;
            }else{
                $whereParts[] = 'IP.id = ' . intval($this->params['product_id']);
            }
        }
        if (!empty($this->params['category'])) {
            $Category = GetObjectOrLoadModel('Category');
            $whereParts[] = 'IC.category_id IN ('. implode(',',$Category->getCategoryChildren($this->params['category'])).")";
        }
        if (!empty($this->params['brand_id'])) {
            $whereParts[] = 'IP.brand_id = ' ."'". addslashes(strtolower($this->params['brand_id']))."'";
        }
        if (isset($this->params['staff_id']) && is_array($this->params['staff_id']) && !empty($this->params['staff_id'])) {
            if ($this->params['group_by'] != 'sales_person'){
                $whereParts[] = 'Invoice.staff_id IN( ' . implode(",",$this->params['staff_id']) . ')';
            } else{
                $whereParts[] = '(SP_IS.staff_id IN( ' . implode(",",$this->params['staff_id']) . ') OR SP_IS2.staff_id IN( ' . implode(",",$this->params['staff_id']) . '))';
            }
        }
		if (isset($this->params['invoice_type']) and !empty($this->params['invoice_type']) || $this->params['invoice_type'] === '0') {
            $whereParts[] = 'Invoice.type = ' . intval($this->params['invoice_type']);
		}else{
            $typeBooking = ($this->params['group_by'] == 'product') ? $typeBooking = ',' .Invoice::BOOKING : '';
            $whereParts[] = 'Invoice.type in ('.Invoice::DEBIT_NOTE.','.Invoice::Credit_Note.','.Invoice::Refund_Receipt.','.Invoice::Invoice.$typeBooking.') ';
		}
		if (isset($this->params['invoice_status']) and !empty($this->params['invoice_status'])) {
            $whereParts[] = 'Invoice.follow_up_status = ' . intval($this->params['invoice_status']);
		}
        if (!empty($this->params['data']['order_source_id'])) {
			$whereParts[] = 'Invoice.`order_source_id` = ' . $this->params['data']['order_source_id'];
		}
        if (ifPluginActive(InventoryPlugin)) {
            if (isset($this->params['store']) and !empty($this->params['store'])) {
                $whereParts[] = "( I.store_id IS NULL AND Invoice.store_id =". intval($this->params['store'])." OR I.store_id =". intval($this->params['store']).")";
            } else {
                $Store = GetObjectOrLoadModel('Store');
                $inActiveStoreIds = $Store->getInActiveStoreIds();
                if (!empty($inActiveStoreIds)) {
                    $whereParts[] = ' I.store_id not in (' . implode(',', $inActiveStoreIds) . ')';
                }
            }
        }
        //no need to check product name caz in debit note there is no product name
        $query .= ' WHERE   ' . implode(' AND ', $whereParts);
        $query .= ' GROUP BY Invoice.id,I.id';
        if($this->params['group_by'] == 'sales_person'){
            $query .= ' ORDER BY Invoice.`date`, Invoice.id ';
        }else{
            $query .= ' ORDER BY subtotal_field, Invoice.`date` ';
        }

        if ( $is_category)
        {
            $all_categories = $this->ItemsCategory->Category->find('list' ) ;
//            $items_categories = $this->ItemsCategory->find('list' , ['conditions' => [] ]);
        }
        $queryData = [];
        if(!isset($this->params['threshold_limit'])){
            $queryData = $this->Invoice->InvoicePayment->query($query);
        }



        foreach ($queryData as $row) {
			if($row['Invoice']['type']==Invoice::Refund_Receipt || $row['Invoice']['type']==Invoice::Credit_Note){
			$row['I']['quantity']=($row['I']['quantity'])*-1;
			}
            if($this->params['group_by'] == 'sales_person' && empty($row['SP_IS']['sales_person']) && !empty($row['SP_IS2']['sales_person'])){
                $row['SP_IS']['subtotal_field'] = $row['SP_IS2']['sales_person'];
            }
            if (!isset($row[0]))
                $row[0] = array();
            $flatData[] = array_merge($row[0], $row['IP'], $row['I'], $row['Invoice'],$row['Client'],$row['SP_IS']);
            
        }
        $queryData = $flatData;

		
        if (low($this->params['group_by']) == 'yearly') {
        $dateFormat = 'Y';
       } elseif (low($this->params['group_by']) == 'daily') {
       $dateFormat = 'Y-m-d';    
       } elseif (low($this->params['group_by']) == 'weekly') {
       $dateFormat = 'Y-m-d';       
       } elseif (low($this->params['group_by']) == 'lastyear'||low($this->params['group_by']) == 'monthly') {
       $dateFormat = 'Y-m';       
       }else{
        $dateFormat = 'Y-m-d';           
       }
        
        $startDate = !empty($this->viewVars['date_from']) ? date($dateFormat, strtotime($this->viewVars['date_from'])) : $dateToStart;
        $endDate = !empty($this->viewVars['date_to']) ? date($dateFormat, strtotime($this->viewVars['date_to'])) : $dateToEnd;
		$groups = array();
        if ($is_periodic){
            $groups = $this->getPeriodsList($startDate, $endDate, $group_by);
        }
//        debug ( $queryData ) ;
        foreach ($queryData as &$row) {
            if($is_category){
                $product_categories = explode (',', $row['subtotal_field']);
                arsort($product_categories );
                $row['subtotal_field'] = $all_categories[array_values($product_categories)[0]] ?? null ;
                $groups[] =  $row['subtotal_field'];
            }
           else if (!$is_periodic)
                $groups[] = $row['subtotal_field'];
           unset($row);
        }

        if(!$this->params['is_summary'] && !$this->params['threshold_limit']){
            $this->displayLimitThreshold($queryData,false, 'v2');
        }

        $reportCurrencies = $reportData = array();
        foreach ($queryData as $row) {
            $currency = $row['currency_code'];
            $group = $row['subtotal_field'];
            if (!isset($reportCurrencies[$currency])) {
                $reportCurrencies[$row['currency_code']] = $currency;
            }
            $reportData[$currency][$group][] = $row;
            $reportData['Cash'] = $reportData['Cash'] ?? 0;
            $reportData['Cash']+=$row['quantity'] ?? 0;
        }
        
        
        if ($is_periodic)
            foreach ($reportData as $currency => $groupData) {
                foreach ($groups as $group){
                    if (!is_array($reportData[$currency])) $reportData[$currency] = [];
                    if (!isset($reportData[$currency][$group])) {
                        $reportData[$currency][$group] = [];
                    }
                }
                ksort($reportData[$currency]);
            }
//        debug($reportData);
//                die(debug($reportData));
        $report_title = $titles['report_title'];
        ksort($reportCurrencies);
        $params = $this->params;

        $this->set(compact('reportCurrencies', 'reportData', 'params', 'groups', 'titles', 'is_periodic', 'report_title'), false);
        $this->set('type', 'product');


        return $this->viewVars;
    }

    public function getDataJSON() {
        parent::getDataJSON();

        $reportData = $this->viewVars['reportData'];
        $reportCurrencies = $this->viewVars['reportCurrencies'];

        $labels = array();
        $max = 0;
        $dateFormats = getDateFormats('std');
        $ownerFormat = $dateFormats[getAuthOwner('date_format')];
        foreach ($reportCurrencies as $curr) {
            $i = 0;
            $values = array();
            foreach ($reportData as $day => $row) {

                $labels[$i] = date('Y-m-d', strtotime($day));
                //$labels[$i] = date($ownerFormat, strtotime($day));
                if (isset($row[$curr])) {
                    $values[] = round($row[$curr][0]['total_amount'], 2);
                } else {
                    $values[] = 0;
                }
                ++$i;
            }

            $vmax = max($values);
            if ($vmax > $max) {
                $max = $vmax;
            }

            $rows[$curr] = $values;
        }


        $jsonData = $jsonData = array('title' => __('Daily Payments', true), 'values' => $rows, 'yMax' => ceil($max), 'xLabels' => $labels, 'group' => 'daily', 'chartType' => 'line');
        return $jsonData;
    }

}

