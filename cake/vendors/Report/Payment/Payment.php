<?php

require_once dirname(dirname(__FILE__)) . DS . 'Payment.php';

class Report_Payment_Payment extends Report_Payment {

    public function getDataArray() {
        $owner = getAuthOwner();



        $subtotal_fields = array(
            'daily' => 'date(IP.`date`)',
            'weekly' => 'DATE_FORMAT(DATE_ADD(IP.`date`, INTERVAL -WEEKDAY(IP.`date`) DAY), "%Y-%m-%d")',
            'monthly' => 'CONCAT(YEAR(IP.`date`), "-", LPAD(MONTH(IP.`date`), 2, 0))',
            'yearly' => 'YEAR(IP.`date`)',
            'client' => 'CONCAT(COALESCE(IP_clients.business_name,I.client_business_name)," #", COALESCE(IP_clients.client_number,I_clients.client_number, I.client_id))',
            'staff' => 'COALESCE(I.staff_id,IP.staff_id)',
            'collected_by' => 'IP.staff_id',
            'payment_method' => 'IP.payment_method',
        );



        $title_params = array(
            'daily' => array('column_title' => __('Day', true), 'report_title' => __('Daily Payments', true), 'graph_title' => __('Daily Payments', true)),
            'weekly' => array('column_title' => __('Week', true), 'report_title' => __('Weekly Payments', true), 'graph_title' => __('Weekly Payments', true)),
            'monthly' => array('column_title' => __('Month', true), 'report_title' => __('Monthly Payments', true), 'graph_title' => __('Monthly Payments', true)),
            'yearly' => array('column_title' => __('Year', true), 'report_title' => __('Yearly Payments', true), 'graph_title' => __('Yearly Payments', true)),
            'client' => array('column_title' => __('Client', true), 'report_title' => __('Payments by Client', true), 'graph_title' => __('Payments By Client', true)),
            'staff' => array('column_title' => __('Invoiced By', true), 'report_title' => __('Payments by Staff', true), 'graph_title' => __('Payments By Staff (Invoiced)', true)),
            'collected_by' => array('column_title' => __('Collected By', true), 'report_title' => __('Payments by Staff', true), 'graph_title' => __('Payments By Staff (Collected)', true)),
            'payment_method' => array('column_title' => __('Method', true), 'report_title' => __('Payments by Method', true), 'graph_title' => __('Payments By Staff', true)),
        );

        $group_by = 'monthly';
        if (!empty($this->params['group_by']) && !empty($subtotal_fields[$this->params['group_by']]))
            $group_by = $this->params['group_by'];
        $this->params['group_by'] = $group_by;
        $is_periodic = in_array($group_by, array('daily', 'weekly', 'monthly', 'yearly'));
        $titles = $title_params[$group_by];

        $this->params['is_summary'] = empty($this->params['is_summary']) ? 0 : 1;
        
        $query = "SELECT IP.id, IP.transaction_id, Journal.id as journal_id, Journal.currency_rate as currency_rate, I.no,I.type as invoice_type, IP.invoice_id, COALESCE(I_clients.business_name,I.client_business_name,IP_clients.business_name) as client_business_name, COALESCE(I.currency_code,IP.currency_code) as currency_code , IP.payment_method, IP.date ,  IP.amount, IP.staff_id as `collected_by`, I.staff_id, " . $subtotal_fields[$group_by] . " as `subtotal_field` ,IP.code as code
        FROM invoice_payments as IP LEFT JOIN (invoices as I) ON (IP.invoice_id = I.id)
        LEFT JOIN clients as IP_clients on IP_clients.id = IP.client_id
        LEFT JOIN clients as I_clients on I_clients.id = I.client_id
        LEFT JOIN (journals as Journal) ON (IP.id = Journal.entity_id AND Journal.entity_type = 'invoice_payment')
         ";
        $whereParts = array_merge(array("(I.id is null OR (I.draft <> 1))",'IP.status = 1', 'IP.date > \'1970-01-01\''), $this->getWhereParts());
        unset($whereParts['draft_condition']); //Replacing the draft <> 1 in the OR function because we are getting the client payments as well.

        if (getAuthStaff('id') !== 0 && !check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::Invoices_View_All_Invoices)) {
            $staff_id = getAuthStaff('id');
            $whereParts[] = " (I.staff_id = $staff_id OR IP.staff_id = $staff_id ) " ;
        }

        if (!empty($this->params['client_id'])) {
            $client_id = intval($this->params['client_id']);
            $whereParts[] = " (I.client_id = $client_id OR IP.client_id = $client_id ) " ;
        }

        //IF the payment method is client credit or not select set payment method not to be client credit
        if(!isset($this->params['payment_method']) || $this->params['payment_method'] != 'client_credit' && (empty($this->params['staff_id']))) { 
            $whereParts[] = "IP.payment_method <> 'client_credit'";
        }

        // warning suppress
        if (isset($this->params['staff_id']) && (!empty($this->params['staff_id']) || $this->params['staff_id'] === '0')) {
            $whereParts[] = 'I.staff_id = ' . intval($this->params['staff_id']);
        }

        if (isset($this->params['collected_by']) && (!empty($this->params['collected_by']) || $this->params['collected_by'] === '0')) {
            $whereParts[] = 'IP.staff_id = ' . intval($this->params['collected_by']);
        }

        if (!empty($this->params['payment_method'])) {
            $whereParts[] = 'IP.payment_method = \'' . mysql_escape_string($this->params['payment_method']) . '\'';
        }

        $advancePaymentsEnabled = settings::getValue(SalesPlugin, settings::ENABLE_ADVANCE_PAYMENT);
        if ($advancePaymentsEnabled) {
            $whereParts[] = '(I.type IS NULL OR I.type <> ' . Invoice::ADVANCE_PAYMENT .')';
        }

        $query .= ' WHERE ' . implode(' AND ', $whereParts);

        if  (!empty($this->params['group_by']) && $this->params['group_by']=='client') {
            $query .= ' ORDER BY IP.`date` desc ,  subtotal_field, IP.`id` asc ';
        }else{
            $query .= ' ORDER BY subtotal_field, IP.`id` asc ';

        }

        $queryData = [];
        if(!isset($this->params['threshold_limit'])){
            $queryData = $this->Invoice->InvoicePayment->query($query);
        }

        if ($advancePaymentsEnabled) {
            $advancePaymentsResult = $this->getAdvancePayments($whereParts, $subtotal_fields, $group_by);
            $creditNotesResult = $this->getAdvancePaymentCreditNotes($whereParts, $subtotal_fields, $group_by);
            $queryData = array_merge($queryData, $advancePaymentsResult, $creditNotesResult);
        }

        if(!$this->params['is_summary'] && !isset($this->params['threshold_limit'])){ 
            $this->displayLimitThreshold($queryData, false, 'v2');
        }

        debug ( $query  ) ;
        foreach ($queryData as $row) {
            if (!isset($row[0]))
                $row[0] = array();
            $flatData[] = array_merge($row[0], $row['IP'], $row['I'], $row['Journal']??[]);
        }
        $queryData = $flatData;
//        debug ( $queryData ) ;


        if ($is_periodic){
            
         //   $groups = $this->getPeriodsList($this->viewVars['date_from'], $queryData[count($queryData) - 1]['subtotal_field'], $group_by);
        //$groups = $this->getPeriodsList($queryData[0]['subtotal_field'], $queryData[count($queryData) - 1]['subtotal_field'], $group_by);            
        }
            
       if (low($this->params['group_by']) == 'yearly') {
        $dateFormat = 'Y';
       } elseif (low($this->params['group_by']) == 'daily') {
       $dateFormat = 'Y-m-d';    
       } elseif (low($this->params['group_by']) == 'weekly') {
       $dateFormat = 'Y-m-d';       
       } elseif (low($this->params['group_by']) == 'lastyear'||low($this->params['group_by']) == 'monthly') {
       $dateFormat = 'Y-m';       
       }else{
        $dateFormat = 'Y-m-d';           
       }
        
        $startDate = !empty($this->viewVars['date_from']) ? date($dateFormat, strtotime($this->viewVars['date_from'])) : $dateToStart;
        $endDate = !empty($this->viewVars['date_to']) ? date($dateFormat, strtotime($this->viewVars['date_to'])) : $dateToEnd;
        $groups = array();
        if ($is_periodic){
            $groups = $this->getPeriodsList($startDate, $endDate, $group_by);
        }
        
        foreach ($queryData as $row) {
            if (!$is_periodic)
                $groups[] = $row['subtotal_field'];
        }
        debug($groups);
        $reportCurrencies = $reportData = array();
        foreach ($queryData as $row) {
            $currency = $row['currency_code'];
            // warning suppress
            $reportData[$currency]['methods'][$row['payment_method']] = $reportData[$currency]['methods'][$row['payment_method']] ?? null;
            $group = $row['subtotal_field'];
            if (!isset($reportCurrencies[$currency])) {
                $reportCurrencies[$row['currency_code']] = $currency;
            }
            $reportData[$currency][$group][] = $row;
            $reportData[$currency]['methods'][$row['payment_method']]+=$row['amount'];
        }
        if ($is_periodic)
            foreach ($reportData as $currency => $groupData) {
                foreach ($groups as $group)
                    if (!isset($reportData[$currency][$group]))
                        $reportData[$currency][$group] = array();
                ksort($reportData[$currency]);
            }

        $report_title = $titles['report_title'];
        ksort($reportCurrencies);
        $params = $this->params;
        $SitePaymentGateway = ClassRegistry::init('SitePaymentGateway');
        $paymentMethods = $SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, false);
		$paymentMethods['client_credit']=__('Client Credit',true);
        $this->set(compact('reportCurrencies', 'reportData', 'params', 'groups', 'titles', 'is_periodic', 'report_title', 'paymentMethods'), false);
        $this->set('type', 'payment');


        return $this->viewVars;
    }

    private function getAdvancePayments($whereParts, $subtotal_fields, $group_by): array
    {
        $query = '
                SELECT IP.id,
                       IP.transaction_id,
                       concat("Advance Payment #", I.no)                                                   as `no`,
                       I.type                                                                              as invoice_type,
                       IP.invoice_id,
                       COALESCE(I_clients.business_name, I.client_business_name, IP_clients.business_name) as client_business_name,
                       COALESCE(I.currency_code, IP.currency_code)                                         as currency_code,
                       IP.payment_method,
                       IP.date,
                       IP.amount,
                       IP.staff_id                                                                         as `collected_by`,
                       I.staff_id,
                              '. $subtotal_fields[$group_by] .'                                            as `subtotal_field`,
                       IP.code                                                                             as code
                FROM invoices as I
                         LEFT JOIN (invoice_payments as IP) ON (IP.invoice_id = I.id)
                         LEFT JOIN clients as IP_clients on IP_clients.id = IP.client_id
                         LEFT JOIN clients as I_clients on I_clients.id = I.client_id

            ';
        $whereParts = array_filter($whereParts, fn($wherePart) => !str_contains($wherePart, 'I.type'));
        $whereParts[] = 'I.type = ' . Invoice::ADVANCE_PAYMENT;
        $query .= ' WHERE ' . implode(' AND ', $whereParts);
        if  (!empty($this->params['group_by']) && $this->params['group_by']=='client') {
            $query .= ' ORDER BY IP.`date` desc ,  subtotal_field, IP.`id` asc ';
        }else{
            $query .= ' ORDER BY subtotal_field, IP.`id` asc ';

        }
        $result = $this->Invoice->query($query);
        return array_map(function ($row) {
            $row['I']['no'] = $row[0]['no'];
            unset($row[0]['no']);
            return $row;
        }, $result);
    }

    private function getAdvancePaymentCreditNotes($whereParts, $subtotal_fields, $group_by): array
    {
        $subtotal_fields = array_map(fn ($field) => $field === 'IP.payment_method' ? $field :  str_replace('IP.', 'I.', $field), $subtotal_fields);
        $query = '
            SELECT I.id,
                   I.id ,
                   concat("Advance Payment Credit Note #", I.no)                                       as `no`,
                   I.type                                                                              as invoice_type,
                   I.id                                                                                as invoice_id,
                   COALESCE(I_clients.business_name, I.client_business_name, IP_clients.business_name) as client_business_name,
                   COALESCE(I.currency_code, IP.currency_code)                                         as currency_code,
                   IP.payment_method,
                   IP.date,
                   I.summary_total,
                   I.staff_id                                                                          as `collected_by`,
                   I.staff_id,
                   ' . $subtotal_fields[$group_by] . '                                                 as `subtotal_field`,
                   IP.code                                                                             as code
            FROM invoices as I
                     LEFT JOIN (invoice_payments as IP) ON (IP.invoice_id = I.id)
                     LEFT JOIN clients as IP_clients on IP_clients.id = IP.client_id
                     LEFT JOIN clients as I_clients on I_clients.id = I.client_id
                     LEFT JOIN invoices as SourceInvoice ON SourceInvoice.id = I.subscription_id
        ';
        $whereParts = array_filter($whereParts, fn($wherePart) => !str_contains($wherePart, 'I.type') && !str_contains($wherePart, 'IP.payment_method'));
        $whereParts = array_map(fn($wherePart) => str_replace(['IP.date', 'IP.`date`', 'IP.status = 1', 'IP.`branch_id`'], ['I.`date`', 'I.`date`', 'I.payment_status = 0', 'I.`branch_id`'], $wherePart), $whereParts);
        $whereParts[] = 'I.type = ' . Invoice::Credit_Note;
        $whereParts[] = 'SourceInvoice.type = ' . Invoice::ADVANCE_PAYMENT;
        $query .= ' WHERE ' . implode(' AND ', $whereParts);
        if (!empty($this->params['group_by']) && $this->params['group_by'] == 'client') {
            $query .= ' ORDER BY IP.`date` desc ,  subtotal_field, IP.`id` asc ';
        } else {
            $query .= ' ORDER BY subtotal_field, IP.`id` asc ';
        }
        $result = $this->Invoice->query($query);
        return array_map(function ($row) {
            $row['I']['no'] = $row[0]['no'];
            $row['IP']['invoice_id'] = $row['I']['invoice_id'];
            $row['IP']['amount'] = $row['I']['summary_total'] * -1;
            unset($row[0]['no']);
            unset($row['I']['invoice_id']);
            return $row;
        }, $result);
    }

    public function getDataJSON() {
        parent::getDataJSON();

        $reportData = $this->viewVars['reportData'];
        $reportCurrencies = $this->viewVars['reportCurrencies'];

        $labels = array();
        $max = 0;
        $dateFormats = getDateFormats('std');
        $ownerFormat = $dateFormats[getAuthOwner('date_format')];
        foreach ($reportCurrencies as $curr) {
            $i = 0;
            $values = array();
            foreach ($reportData as $day => $row) {

                $labels[$i] = date('Y-m-d', strtotime($day));
                //$labels[$i] = date($ownerFormat, strtotime($day));
                if (isset($row[$curr])) {
                    $values[] = round($row[$curr][0]['total_amount'], 2);
                } else {
                    $values[] = 0;
                }
                ++$i;
            }

            $vmax = max($values);
            if ($vmax > $max) {
                $max = $vmax;
            }

            $rows[$curr] = $values;
        }


        $jsonData = $jsonData = array('title' => __('Daily Payments', true), 'values' => $rows, 'yMax' => ceil($max), 'xLabels' => $labels, 'group' => 'daily', 'chartType' => 'line');
        return $jsonData;
    }

}

