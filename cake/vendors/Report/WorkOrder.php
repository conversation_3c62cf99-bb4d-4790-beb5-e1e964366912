<?php

/**
 * <AUTHOR>
 */
require_once dirname(dirname(__FILE__)) . DS . 'Report.php';

class Report_WorkOrder extends Report {

    protected $params = array();
    protected $dataArray = array();
    protected $WorkOrder;
	protected $CustomFormField;
	protected $CustomForm;


	protected $viewVars = array();
    protected $primaryColors = array(
        'B9D1E5', '7C3DD9', '0A5277', '91A51E', '704129', '8D2E03', 'DDD1F6', 'DF03A8', '6F7742'
    );
    var $is_income = 0;
    var $controller = 'work_orders';
    var $multi_title = 'Work Orders';
    var $single_title = 'Work Orders';
//    var $permissions = array(
//        'View_his_own' => View_his_own_expenses,
//        'Add_New' => Add_New_Expenses,
//        'Edit_delete_his_own' => Edit_delete_his_own_expenses,
//        'Edit_Delete_all' => Edit_Delete_all_expenses,
//        'View_All' => View_All_expenses
//    );

    public function __construct($params) {
        $this->params = $params;
        $params = Router::getParams();
//        if (!empty($params['is_income'])) {
//            $this->is_income = true;
//            $this->controller = 'incomes';
//            $this->multi_title = 'Incomes';
//            $this->single_title = 'Income';
//            $this->permissions = array(
//                'View_his_own' => View_his_own_incomes,
//                'Add_New' => Add_New_Incomes,
//                'Edit_delete_his_own' => Edit_delete_his_own_incomes,
//                'Edit_Delete_all' => Edit_Delete_all_incomes,
//                'View_All' => View_All_incomes);
//        }


        $this->WorkOrder = ClassRegistry::init('WorkOrder');
        $this->CustomFormField = ClassRegistry::init('CustomFormField');
        $this->CustomForm = ClassRegistry::init('CustomForm');
    }

    function get_stock_requests_count($work_order_id) {
        $this->loadModel('StockRequest');
        return $this->StockRequest->find('count' , ['recursive' => -1 ,'conditions' => ['work_order_id' => $work_order_id] ]) ;        
    }


    public function getDataArray() {
        
    }

    public function getDataJSON() {
        if (empty($this->viewVars['reportData'])) {
            $this->getDataArray();
        }
    }

	public function get_filtered_fields()
	{
		$reread_form = $this->CustomForm->findByTableName("work_orders");
        $filterd_fields = array();
        if (!empty($reread_form)) {
            $form_fields = $this->WorkOrder->get_custom_fields_filters();
            foreach ($form_fields as $key => $value) {
                if ($value['myval']["CustomFormField"]["is_filtered"] == 1) {
                    $filterd_fields[] = $value['myval'];
                }
            }
        }
		return $filterd_fields;
	}

    protected function getWhereParts($date_field = 'start_date') {

        
		
        App::import('Core', 'Sanitize');
        $owner = getAuthOwner();
        $whereParts = array();


       
//	
		$params = $this->params;
		
		$filtered_fields = $this->get_filtered_fields();
		foreach($filtered_fields as $k => $filter_field)
		{
		    $customFieldsParams = $params['data'];
            $param = 'field_' . $filter_field['CustomFormField']['id'];
            if (!empty($params[$param]) || (isset($params[$param]) && strval($params[$param]) === '0')) {
                if (isset($params['type_' . $param]) && $params['type_' . $param] == "date") {
                    $whereParts["CD.$field"] = $this->WorkORder->formatDate($params[$param]);
                } else if (isset($params['type_' . $param]) && $params['type_' . $param] == "datetime") {
                    $whereParts["CD.$field"] = $this->WorkORder->formatDateTime($params[$param]);
                } else {

                    if(is_array($params[$param])){
                        $orConCustom=[];
                        foreach($params[$param] as $k => $par)
                        {
                            if (!empty($par)) {
                                $orConCustom[] = "FIND_IN_SET('{$par}',CD.$param)";
                            }
                        }
                        if(!empty($orConCustom)){
                            $whereParts[] = '(' .implode (' OR ', $orConCustom) .')';//to handle multi select field make or in selected field only not on all conditions 
                        }
                    }else{
                        $whereParts[] = "CD.".$param . " LIKE '%{$params[$param]}%'";
                    }
                }
            }
        }
		if($whereParts['OR'])
			$whereParts[] = '' .implode (' OR ', $whereParts['OR']) .'';
		
		unset($whereParts['OR']);
        if (!empty($this->params['date_from'])) {
            $date_from = $this->WorkOrder->formatDate($this->params['date_from'], $owner['date_format']);
            $whereParts[] = 'W.`'.$date_field.'` >= "' . $date_from . '"';
        }

        $date_to = '';
        if (!empty($this->params['date_to'])) {
            $date_to = $this->WorkOrder->formatDate($this->params['date_to'], $owner['date_format']);
            $whereParts[] = 'W.`'.$date_field.'` <= "' . $date_to . '"';
        }
		
		if (!empty($this->params['client_id']) && $this->params['client_id'] != -1) {
            $whereParts[] = 'W.client_id= ' . intval($this->params['client_id']);
        }
		if (!empty($this->params['staff_id']) && $this->params['staff_id'] != -1) {
            $whereParts[] = '(`IS`.staff_id= ' . intval($this->params['staff_id']).' or W.staff_id = '. intval($this->params['staff_id']) .')';
        }
		if (!empty($this->params['status']) && $this->params['status'] != -1) {
            $whereParts[] = 'W.follow_up_status_id= ' . intval($this->params['status']);
        }
		
		if (!empty($this->params['item_type']) && $this->params['item_type'] != -1) {
            $whereParts[] = 'IT.item_type= ' . intval($this->params['item_type']);
        }

        if (ifPluginActive(BranchesPlugin) && !empty($this->params['data']['branch_id'])) {
            $whereParts[] = 'W.`branch_id` IN (' . implode(',',$this->params['data']['branch_id']) . ')';
        }
        if(ifPluginInstalled(WorkflowPlugin)) {
            $whereParts[] = 'W.workflow_type_id IS NULL';
        }
        $this->set(compact('date_from', 'date_to'));
        return $whereParts;
    }

    protected function set($param1, $param2 = null) {
        if (is_array($param1)) {
            $this->viewVars = array_merge($this->viewVars, $param1);
        } else {
            $this->viewVars[$param1] = $param2;
        }
    }

}

