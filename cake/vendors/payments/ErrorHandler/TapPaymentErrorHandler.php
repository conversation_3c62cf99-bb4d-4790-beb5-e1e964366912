<?php

namespace App\vendors\payments\ErrorHandler;

use <PERSON><PERSON>r<PERSON><PERSON><PERSON>;
use Mockery\Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;

class TapPaymentErrorHandler implements ErrorHandlerInterface
{

    protected ?PaymentError $error = null;

    public function setError($data)
    {
        if ($data instanceof BadResponseException) {
            $this->error = new PaymentError();
            $this->error->setCode($data->getCode());
            $this->error->setMessage(json_decode($data->getResponse()->getBody()->getContents(),true));
        }
        return $this->error;
    }

    public function getError()
    {
        $message = '';
        $error = $this->error->getMessage();
        debug($error);
        $message = $error['errors'][0]['description'];
        if($message == 'Authorization Required'){
            $message = 'Client Authentication failed, Please Verify your credentials in payment methods';
        }
        return __($message, true);
    }

    public function hasError(){
        return isset($this->error);
    }
}