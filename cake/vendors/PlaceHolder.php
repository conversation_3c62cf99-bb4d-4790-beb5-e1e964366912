<?php

use App\Services\LocalEntityForm\CustomFieldsFormService;
use App\Utils\TrackStockUtil;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Cache\PortalCache;
use Izam\Daftra\Common\Utils\BusinessNumberCommonFieldsUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Logging\Service\RollbarLogService;
use Rollbar\Payload\Level;
use Izam\Aws\Aws;
use Izam\Daftra\Common\Utils\InvoiceSourceTypesUtil;

App::import('Vendor', 'OIBarcode', array('file' => 'OIBarcode/OIBarcode.php'));
App::import('Controller', 'Invoices');

Class PlaceHolder {

    public function __construct()
    {
        $this->CustomForm = GetObjectOrLoadModel('CustomForm');
        $this->CustomFormField = GetObjectOrLoadModel('CustomFormField');
    }

    public static function replace($text, $placeholder, $replace,$replace_not_match_with_empty=true) {
        $matches = array();
        $matches2 = array();
        preg_match_all('/{%custom_(.*?)}/', $text, $matches);
        foreach ($matches[0] as $value) {
            preg_match('/(\d+)\D*\z/', $value, $matches2);
            $text = str_replace($value, '{%field_' . $matches2[1] . '%}', $text);
        }
        $text = str_replace($placeholder, $replace, $text);
        preg_match_all('/{%(.*?)}/', $text, $matches2);
        if($replace_not_match_with_empty) {
            $text = str_replace(array_values($matches2[0]), '', $text);
        }

        return $text;
    }

    public static function get_custom_place_holder($name) {

        $rrr = explode("_", $name);
        return $rrr[count($rrr) - 2] . '_' . $rrr[count($rrr) - 1];
    }

    public static function datetime_place_holder ( ){
        return ['{%current_date%}' => format_date(date('Y-m-d')) ,'{%current_datetime%}' => format_datetime(date('Y-m-d H:i:s')) ];
    }

    public static function datetime_place_holder_list ( ){
        return ['{%current_date%}' => __("Current local date" , true) ,'{%current_datetime%}' => __("Current local time" , true) ];
    }
    public static function wo_client_place_holder($wo_id)
    {
        $WorkOrder = ClassRegistry::init ('WorkOrder' , 'Model');
        $Client = ClassRegistry::init ('Client' , 'Model');
        $wo = $WorkOrder->findById ( $wo_id ) ;
        $client = [] ;
        if ( empty ($wo['WorkOrder']['client_data'] ) && !empty ($wo['WorkOrder']['client_id'] ) )
        {
            $client = $Client->findById ( $wo['WorkOrder']['client_id'] ) ;
        }else if ( !empty ( $wo['WorkOrder']['client_data'] ) ){
            $client['Client'] = json_decode ( $wo['WorkOrder']['client_data'] , true ) ;
        }
        return self::client_place_holder($client);
    }
	/**
	 *
	 * @param type $table_name_or_table_id
	 * @param type $record_id
	 * @return type
	 */
	private static function get_custom_place_holders($table_name_or_table_id,$record_id)
	{
		$placeholder = [];
		$CustomHolder = array();
		$CustomForm = GetObjectOrLoadModel('CustomForm');
		$CustomFormField = GetObjectOrLoadModel('CustomFormField');
		$form = $CustomForm->get_custom_form_table_name($table_name_or_table_id);
		if($form)
		{
			$form_fields = $CustomFormField->get_form_fields($form['CustomForm']['id']);
			 foreach ($form_fields as $v) {
                $v ['CustomFormField']['is_required'] = 0;
                $holdername = slug('field_' . $v['CustomFormField']['id'], '_');
                $CustomHolder[$holdername]['name'] = $v['CustomFormField']['label'];
                $CustomHolder[$holdername]['type'] = $v['CustomFormField']['type'];
                if (!empty($v['CustomFormField']['belongs_to_tables']))
                {
                    $CustomHolder[$holdername]["s_belongs"] = $v['CustomFormField']['belongs_to_tables'];
                }
            }
		}
		if($form['CustomForm']['table_name'])
		{
			$CustomFormDataModel = $CustomForm->get_custom_form_model($form);
			$conditions = [Inflector::singularize($form['CustomForm']['table_name']).'_id' => $record_id];
			$custom_record = $CustomFormDataModel->find('first', ['conditions' => $conditions]);
			$custom_record[$CustomFormDataModel->alias] = $CustomForm->formatCustomRecordData($custom_record, $form_fields);
		}
		$CustomRecordAlias  = $CustomFormDataModel->alias;
        // several warning suppress
        foreach ($CustomHolder as $key => $value) {
                $placeHolderValue = $custom_record[$CustomRecordAlias][$key]??null;
                $placeholder['{%' . $key . '%}'] = $placeHolderValue;
                $belongs = json_decode ( $value['s_belongs'] ??'' , true );
                if ( !empty($value["s_belongs"]) && !empty($belongs['table_name']))
                {
					$Belongs = GetObjectOrLoadModel($belongs['model_name']);
                    $val = $Belongs->find('first' , ['conditions' => [$belongs[ 'model_name'].'.id' => $custom_record[$CustomRecordAlias][$key]??null ] ]);
                    $placeholder['{%' . $key . '%}'] = $val[$Belongs->alias][$belongs['field_name']];
                }else if($value['type'] == CustomForm::HASMANY_FIELD_TYPE)
                {
                    $placeholder['{%' . $key . '%}'] = CustomForm::getHasManyTable($placeHolderValue);
                    $placeholder['{%loop_'. $key .'%}'] = $placeHolderValue;
                }
            }
		return $placeholder;
	}
	/**
	 *
	 * @param type $table_name_or_table_id
     * @param type $options
	 * @return string
	 */
	private static function get_custom_place_holders_list($table_name_or_table_id, $options = ['native' => false])
	{
	    $isNative = $options['native'];
		$placeholder = [];
		$CustomHolder = array();
		$CustomForm = GetObjectOrLoadModel('CustomForm');
		$CustomFormField = GetObjectOrLoadModel('CustomFormField');
		$form = $CustomForm->get_custom_form_table_name($table_name_or_table_id);
		if($form)
		{
			$form_fields = $CustomFormField->get_form_fields($form['CustomForm']['id']);
			 foreach ($form_fields as $v) {
                $v ['CustomFormField']['is_required'] = 0;
                 $holdername = self::getCustomFieldPlaceHolder($v, $options);
                $CustomHolder[$holdername] = $v['CustomFormField'];
            }
            foreach ($CustomHolder as $placeHolderKey => $customFormField) {
                if($isNative || (isset($customFormField['type']) && $customFormField['type'] == CustomForm::HASMANY_FIELD_TYPE))
                {
                    $placeholder[$placeHolderKey] = $customFormField['label'];
                }else{
                    $placeholder['{%custom_' . $placeHolderKey . '%}'] = $customFormField['label'].' - '.__(Inflector::classify($form['CustomForm']['table_name']),true);
                }

            }


        }
		return $placeholder;
	}


    /**
     * @param $customField
     * @param array $options
     * @return string
     */
	private static function getCustomFieldPlaceHolder($customField, $options = ['native' => false])
    {
        $isNative = $options['native'];
        $holdername = '';

            if($customField['CustomFormField']['type'] == CustomForm::HASMANY_FIELD_TYPE)
            {
                //get sub forms table
                $hasManyData = json_decode($customField['CustomFormField']['has_many'], true);
                $hasManyPlaceHolders = self::get_custom_place_holders_list($hasManyData['table_name'], ['native' => true]);
                if(!empty($hasManyPlaceHolders))
                {
                    $haManyColumns = array_values($hasManyPlaceHolders);
                    $holdername .= '<table>';
                    $holdername .= '<tr>';
                    foreach($haManyColumns as $k => $column)
                    {
                        $holdername .= '<th>'.$column.'</th>';
                    }
                    $holdername .= '</tr>';
                    $holdername .= '<!-- {{#loop_field_'.$customField['CustomFormField']['id'] .' }} -->';
                    $holdername .= '<tr>';
                    foreach($hasManyPlaceHolders as $placeHolderKey => $placeHolderValue)
                    {
                        $holdername .= '<td>{{'.$placeHolderValue.'}}</td>';
                    }
                    $holdername .= '</tr>';
                    $holdername .= '<!-- {{/loop_field_'.$customField['CustomFormField']['id'] .' }} -->';
                    $holdername .= '</table>';
                }

        }else {
                if (!$isNative) {
                    $holdername = slug($customField['CustomFormField']['label'], '_') . '_' . 'field_' . $customField['CustomFormField']['id'];
                } else {
                    $holdername = 'field_' . $customField['CustomFormField']['id'];
                }
            }

        return $holdername;
    }

    public static function work_order_place_holder( $work_order ,$extra_fields=[], $no_view = false) {
        if ( is_array ( $work_order ) && empty ( $work_order['WorkOrder'] )){
                $work_order['WorkOrder'] = $work_order ;
        }
        $WorkOrderModel = ClassRegistry::init('WorkOrder', 'Model');
        $work_order_status_list = $WorkOrderModel->get_status_list();
        $FollowUpStatusModel = ClassRegistry::init('FollowUpStatus', 'Model');
        $FollowUpStatus_list = $FollowUpStatusModel->find('list');
        $income_expense=$WorkOrderModel->get_financial_list_table($work_order['WorkOrder']['id'],100000000,true);
        $budget_calculation = settings::getValue(WorkOrderPlugin, 'budget_calculation');
        if ( $budget_calculation == 'invoices') {
            $spent = $WorkOrderModel->get_spent($work_order['WorkOrder']['id']);
        }else {
            $spent = $income_expense['previous_expense'] * -1;
        }
        if ($work_order['WorkOrder']['budget'] > 0) {
            $percentage = round(($spent / $work_order['WorkOrder']['budget']) * 100);
        } else {
            $percentage = INF;
        }

        $ItemStaff = GetObjectOrLoadModel('ItemStaff');
        $assignedStaffs = $ItemStaff->getAssignedStaff(ItemStaff::WORK_ORDER_ITEM_TYPE, $work_order['WorkOrder']['id']);
        $assignedStaffsNames = implode(',',array_map(function ($item){return $item['Staff']['name'];},$assignedStaffs));
        $assignedStaffsMobileNumber = implode(',',array_map(function ($item){return $item['Staff']['mobile'];},$assignedStaffs));
        $assignedStaffsPhoneNumber = implode(',',array_map(function ($item){return $item['Staff']['home_phone'];},$assignedStaffs));
        // warning suppress
        $FollowUpStatus_list[$work_order['WorkOrder']['follow_up_status_id']] = $FollowUpStatus_list[$work_order['WorkOrder']['follow_up_status_id']] ?? null;
        $placeholder = [
          '{%work_order_number%}' => $work_order['WorkOrder']['number']  ,
          '{%work_order_title%}' => $work_order['WorkOrder']['title']  ,
          '{%work_order_start_date%}' => format_date ($work_order['WorkOrder']['start_date'] ) ,
          '{%work_order_delivery_date%}' =>format_date ($work_order['WorkOrder']['delivery_date'] ),
          '{%work_order_description%}' =>$work_order['WorkOrder']['description']  ,
          '{%work_order_budget%}' => format_price ( $work_order['WorkOrder']['budget']  , $work_order['WorkOrder']['budget_currency']  ) ,
          '{%work_order_budget_currency%}' => $work_order['WorkOrder']['budget_currency'],//__("Work Order Budget Currency" , true)  ,
          '{%work_order_status%}' => $work_order_status_list [$work_order['WorkOrder']['status']],
          '{%work_order_type_name%}' => (!empty($work_order['WorkOrder']['workflow_type_id']) && !empty($work_order ['WorkflowType'])) ? $work_order ['WorkflowType']['singular_title']: __('Work Order', true),
          '{%work_order_follow_up_status%}' => $FollowUpStatus_list[$work_order['WorkOrder']['follow_up_status_id']],
          '{%work_order_spent%}' => format_price ( $spent , $work_order['WorkOrder']['budget_currency'] ) ,
          '{%work_order_budget_progress%}' => (!empty($work_order['WorkOrder']['budget'])? '<div class="work_order_progress m-b-20"><div style="width: '.(($percentage > 100 )?100:$percentage).'%;" class="work_order_in_progress left"></div></div>':""),
          '{%work_order_transaction_list%}' => $WorkOrderModel->get_transaction_list_table($work_order['WorkOrder']['id'], 100000000, $no_view),
          '{%work_order_transaction_list_title%}' => __("Transaction List till" , true ),
          '{%work_order_financial_list%}' => $WorkOrderModel->get_financial_list_table($work_order['WorkOrder']['id']),
          '{%work_order_financial_list_title%}' => __("Financial List till" , true ),
          '{%work_order_serialized_invoice_products%}' => serialize($WorkOrderModel->get_invoiced_products($work_order['WorkOrder']['id'])),
          '{%work_order_invoiced_products%}' => $WorkOrderModel->get_invoiced_products_table($work_order['WorkOrder']['id'],$extra_fields),
          '{%work_order_assigned_staffs%}' => $assignedStaffsNames,
          '{%work_order_assigned_staffs_mobile_number%}' => $assignedStaffsMobileNumber,
          '{%work_order_assigned_staffs_phone_number%}' => $assignedStaffsPhoneNumber,
        ];


        $placeholder += self::datetime_place_holder();
		$placeholder  +=self::get_custom_place_holders('work_orders',$work_order['WorkOrder']['id']);

        //get the client
        $Client = ClassRegistry::init('Client', 'Model');
        $ClientRow = $Client ->find('first', array('conditions' => array('Client.id' => $work_order['WorkOrder']['client_id'])));
        $placeholder += self::site_place_holder(getCurrentSite());
        $placeholder += self::staff_place_holder($work_order['WorkOrder']['staff_id'],'work_order');
        $placeholder += self::client_place_holder($ClientRow);

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholder += self::branches_place_holder($work_order['WorkOrder']['branch_id'], 'work_order');
        }
        return $placeholder ;
    }

    public static function appointments_place_holder( $appointment) {
	    $FollowUpReminder = GetObjectOrLoadModel('FollowUpReminder');

        $FollowUpAction = ClassRegistry::init ('FollowUpAction' , 'Model');
        $action = $FollowUpAction->find ( 'first' , ['conditions'=> ['FollowUpAction.id' => $appointment['FollowUpReminder']['action_id']]]);
        $status_list = $FollowUpReminder->getStatuses();
        $entityModelName = $entityModel = $placeHolderFunctionName = '';
        switch ($appointment['FollowUpReminder']['item_type']){
            case FollowUpReminder::CLIENT_TYPE:
                $entityModel = ClassRegistry::init ('Client','Model');
                $entityModelName = 'Client';
                $placeHolderFunctionName = 'client_place_holder';
                break;
            case FollowUpReminder::INVOICE_TYPE:
                $entityModel = ClassRegistry::init ('Invoice','Model');
                $entityModelName = 'Invoice';
                $placeHolderFunctionName = 'invoice_place_holder';
                break;
            case FollowUpReminder::ESTIMATE_TYPE:
                $entityModel = ClassRegistry::init ('Invoice','Model');
                $entityModelName = 'Invoice';
                $placeHolderFunctionName = 'estimate_place_holder';
                break;
            case FollowUpReminder::PO_TYPE:
                $entityModel = ClassRegistry::init ('PurchaseOrder','Model');
                $entityModelName = 'PurchaseOrder';
                $placeHolderFunctionName = 'purchaseorders_place_holder';
                break;
            case FollowUpReminder::STAFF_TYPE:
                $entityModel = ClassRegistry::init ('Staff','Model');
                $entityModelName = 'Staff';
                $placeHolderFunctionName = 'staff_place_holder';
                break;
            case FollowUpReminder::SUPPLIER_TYPE:
                $entityModel = ClassRegistry::init ('Supplier','Model');
                $entityModelName = 'Supplier';
                $placeHolderFunctionName = 'supplier_place_holder';
                break;
            case FollowUpReminder::PRODUCT_TYPE:
                $entityModel = ClassRegistry::init ('Product','Model');
                $entityModelName = 'Product';
                $placeHolderFunctionName = 'product_place_holder';
                break;
            case FollowUpReminder::WORK_ORDER_TYPE:
                $entityModel = ClassRegistry::init ('WorkOrder','Model');
                $entityModelName = 'WorkOrder';
                $placeHolderFunctionName = 'work_order_place_holder';
                break;
        }

        $itemPlaceHolders = [];
        if ($entityModelName) {
            $item = $entityModel->find('first', ['conditions' => [$entityModelName . '.id' => $appointment['FollowUpReminder']['item_id']]]);
            $itemPlaceHolders = self::{$placeHolderFunctionName}($item);
        }

        if(php_sapi_name() == 'cli' || ($entityModelName == 'WorkOrder' && $appointment['FollowUpReminder']['item_id'] == 96))
        {  
            RollbarLogService::log(Level::INFO,'inside appointments_place_holder() after $entityModel->find()',[$item, $entityModel->getLastQuery()],true);
        } 

        $placeholder = [
            '{%appointments_id%}' => $appointment['FollowUpReminder']['id']  ,
            '{%appointments_body%}' => $appointment['FollowUpReminder']['body']  ,
            '{%appointments_date%}' => $appointment['FollowUpReminder']['date']  ,
            '{%appointments_start_date%}' => $appointment['FollowUpReminder']['start_date']  ,
            '{%appointments_start_time%}' => $appointment['FollowUpReminder']['start_time'],
            '{%appointments_end_time%}' => $appointment['FollowUpReminder']['end_time'],
            '{%appointments_status%}' => $status_list[$appointment['FollowUpReminder']['status']]  ,
            '{%appointment_action%}' => $action['FollowUpAction']['name'],

        ];
        $placeholder += self::site_place_holder();
        $placeholder += self::staff_place_holder($appointment['Staff']['id']);
        $placeholder += self::datetime_place_holder();
        $assignedStaffIds = $appointment['AssignedStaff']['staff_id'];
        if(empty($assignedStaffIds))
        {
            $ItemStaff = GetObjectOrLoadModel('ItemStaff');
            $assignedStaffIds = $ItemStaff->getAssignedStaff(ItemStaff::APPOINTMENT_ITEM_TYPE, $appointment['FollowUpReminder']['id'])[0]['ItemStaff']['staff_id'];
        }
        if(!empty($assignedStaffIds) || (int)$assignedStaffIds == 0)
        {
            $placeholder += self::assigned_staff_place_holder($assignedStaffIds);
        }

        //get the client
        if($appointment['FollowUpReminder']['partner_type'] == FollowUpReminder::CLIENT_PARTNER_TYPE)
        {
            $Client = GetObjectOrLoadModel('Client');
            $ClientRow = $Client ->find('first', array('conditions' => array('Client.id' => $appointment['FollowUpReminder']['partner_id'])));
            $appointment['Partner'] = $ClientRow['Client'];
            $appointment = $FollowUpReminder->setPartnerRaltedData($appointment, $appointment['FollowUpReminder']['partner_type']);
            $placeholder += [
                '{%partner_no%}' => $appointment['Partner']['no']  ,
                '{%partner_name%}' => $appointment['Partner']['name'] ,
                '{%partner_phone%}' => $appointment['Partner']['phone'] ,
                '{%partner_mobile%}' => $appointment['Partner']['mobile'] ,
                '{%partner_email%}' => $appointment['Partner']['email'] ,
                '{%partner_address1%}' => $appointment['Partner']['address1']  ,

            ];

            App::import('Vendor', 'settings');
            $client_settings = settings::getPluginValues(ClientsPlugin);
            if ($client_settings['national_id'] == "1") {
                $placeholder += ["{%partner_national_id%}" => $appointment['Partner']['national_id']];
            }

        }

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholder += self::branches_place_holder($appointment['FollowUpReminder']['branch_id'],'appointment');
        }
        $placeholder +=$itemPlaceHolders;
        return $placeholder ;
    }

    static function appointments_place_holder_list(){
        $placeholder = [
            '{%appointments_id%}' => __("Number" , true)  ,
            '{%appointments_body%}' => __("Descripton" , true)  ,
            '{%appointments_start_date%}' => __("Date" , true)  ,
            '{%appointments_start_time%}' => __("Start Time" , true)  ,
            '{%appointments_end_time%}' => __("End Time" , true)  ,
            '{%appointments_status%}' => __("Status" , true)  ,
            '{%partner_no%}' => __("Partner Number" , true)  ,
            '{%partner_name%}' => __("Partner Name" , true)  ,
            '{%partner_phone%}' => __("Partner Phone" , true)  ,
            '{%partner_email%}' => __("Partner Email" , true)  ,
            '{%partner_address1%}' => __("Partner Address" , true)  ,
            '{%appointment_action%}' => __('Appointment Action', true),
        ];

        App::import('Vendor', 'settings');
        $client_settings = settings::getPluginValues(ClientsPlugin);
        if ($client_settings['national_id'] == "1") {
            $placeholder += ["{%partner_national_id%}" => __("Partner National ID", true)];
        }

        $placeholder += self::staff_place_holder_list();
        $placeholder += self::site_place_holder_list();
        $placeholder += self::datetime_place_holder_list();
        $placeholder += self::assigned_staff_place_holder_list();
        return $placeholder + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('appointment') : []);
    }


    public static function bookings_place_holder( $appointment) {
        $FollowUpReminder = GetObjectOrLoadModel('FollowUpReminder');
        App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);
        $booking =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, null, []);
        $bookingStatuses = $booking->getStatuses();
        $bookingStatus = $bookingStatuses[$appointment['Invoice']['payment_status']];
        $FollowUpAction = ClassRegistry::init ('FollowUpAction' , 'Model');
        $action = $FollowUpAction->find ( 'first' , ['conditions'=> ['FollowUpAction.id' => $appointment['FollowUpReminder']['action_id']]]);
        $status_list = $FollowUpReminder->getStatuses();
        $placeholder = [
            '{%appointment_id%}' => $appointment['FollowUpReminder']['id']  ,
            '{%appointments_id%}' => $appointment['FollowUpReminder']['id']  ,
            '{%appointments_body%}' => $appointment['FollowUpReminder']['body']  ,
            '{%appointments_date%}' => format_date($appointment['FollowUpReminder']['date'])  ,
            '{%appointments_start_date%}' => format_date($appointment['FollowUpReminder']['start_date'])  ,
            '{%appointments_start_time%}' => $appointment['FollowUpReminder']['start_time'],
            '{%appointments_end_time%}' => $appointment['FollowUpReminder']['end_time'],
            '{%appointments_status%}' => $status_list[$appointment['FollowUpReminder']['status']]  ,
            '{%booking_status%}' => __($bookingStatus, true) ,
            '{%appointment_action%}' => $action['FollowUpAction']['name'],
            '{%booking_services%}' => __('Booking Services', true),
        ];
        $controller = new InvoicesController();

        $view = new View($controller, false);
        $table = $view->element("placeholders/services_table",['booking' => $appointment]);
        $placeholder['{%booking_services%}'] = $table;
        $placeholder += self::site_place_holder();
        $placeholder += self::staff_place_holder($appointment['invoice']['staff_id']);
        $placeholder += self::assigned_staff_place_holder($appointment['AssignedStaff']['staff_id']);
        $placeholder += self::datetime_place_holder();

        //get the client
        if($appointment['FollowUpReminder']['partner_type'] == FollowUpReminder::CLIENT_PARTNER_TYPE)
        {
            $Client = GetObjectOrLoadModel('Client');
            $ClientRow = $Client ->find('first', array('conditions' => array('Client.id' => $appointment['FollowUpReminder']['partner_id'])));
            $appointment['Partner'] = $ClientRow['Client'];
            $appointment = $FollowUpReminder->setPartnerRaltedData($appointment, $appointment['FollowUpReminder']['partner_type']);
            $placeholder += [
                '{%partner_no%}' => $appointment['Partner']['no']  ,
                '{%partner_name%}' => $appointment['Partner']['name'] ,
                '{%partner_phone%}' => $appointment['Partner']['phone'] ,
                '{%partner_mobile%}' => $appointment['Partner']['mobile'] ,
                '{%partner_email%}' => $appointment['Partner']['email'] ,
                '{%partner_address1%}' => $appointment['Partner']['address1']  ,

            ];

            App::import('Vendor', 'settings');
            $client_settings = settings::getPluginValues(ClientsPlugin);
            if ($client_settings['national_id'] == "1") {
                $placeholder += ["{%partner_national_id%}" => $appointment['Partner']['national_id']];
            }
        }
//        die(debug($placeholder));

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholder += self::branches_place_holder($appointment['FollowUpReminder']['branch_id'], 'booking');
        }


        return $placeholder ;
    }

    static function bookings_place_holder_list(){
        $placeholder = [
            '{%appointments_id%}' => __("Number" , true)  ,
            '{%appointments_body%}' => __("Descripton" , true)  ,
            '{%appointments_start_date%}' => __("Date" , true)  ,
            '{%appointments_start_time%}' => __("Start Time" , true)  ,
            '{%appointments_end_time%}' => __("End Time" , true)  ,
            '{%appointments_status%}' => __("Status" , true)  ,
            '{%partner_no%}' => __("Partner Number" , true)  ,
            '{%partner_name%}' => __("Partner Name" , true)  ,
            '{%partner_phone%}' => __("Partner Phone" , true)  ,
            '{%partner_mobile%}' => __("Partner Mobile" , true)  ,
            '{%partner_email%}' => __("Partner Email" , true)  ,
            '{%partner_address1%}' => __("Partner Address" , true)  ,
            '{%booking_status%}' => __("Booking Status" , true)  ,
            '{%appointment_action%}' => __('Appointment Action', true),
            '{%booking_services%}' => __('Booking Services', true),
        ];

        App::import('Vendor', 'settings');
        $client_settings = settings::getPluginValues(ClientsPlugin);
        if ($client_settings['national_id'] == "1") {
            $placeholder += ["{%partner_national_id%}" => __("Partner National ID", true)];
        }

        $placeholder += self::staff_place_holder_list();
        $placeholder += self::assigned_staff_place_holder_list();
        $placeholder += self::site_place_holder_list();
        $placeholder += self::datetime_place_holder_list();

        return $placeholder + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('booking') : []);
    }

    public static function work_order_place_holder_list($work_order_only=false) {
        $placeholder = [
          '{%work_order_number%}' => __("Work Order Number" , true)  ,
          '{%work_order_title%}' => __("Work Order Title" , true)  ,
          '{%work_order_start_date%}' => __("Work Order Start Date" , true)  ,
          '{%work_order_delivery_date%}' => __("Work Order Delivery Date" , true)  ,
          '{%work_order_description%}' => __("Work Order Description" , true)  ,
          '{%work_order_budget%}' => __("Work Order Budget" , true)  ,
          '{%work_order_budget_currency%}' => __("Work Order Budget Currency" , true)  ,
          '{%work_order_status%}' => __("Work Order Status" , true)  ,
          '{%work_order_follow_up_status%}' => __("Work Order Follow up status" , true)  ,
          '{%work_order_spent%}' => __("Work Order Total Spent" , true ),
          '{%work_order_budget_progress%}' => __("Work Order Budget Progress" , true ),
          '{%work_order_transaction_list%}' => __('Work Order Transaction List' , true ) ,
          '{%work_order_transaction_list_title%}' => __('Work Order Transaction List' , true ) ,
          '{%work_order_financial_list%}' => __('Work Order Financial List' , true ) ,
          '{%work_order_financial_list_title%}' => __('Work Order Financial List' , true ) ,
//          '{%work_order_serialized_invoice_products%}' => __("Invoiced Products" , true ),
          '{%work_order_invoiced_products%}' => __("Invoiced Products" , true ),
          '{%work_order_assigned_staffs%}' => __("Work Order Assigned Staffs" , true ),
          '{%work_order_assigned_staffs_mobile_number%}' => __("Work Order Assigned Staffs Mobile Number" , true ),
          '{%work_order_assigned_staffs_phone_number%}' => __("Work Order Assigned Staffs Phone Number" , true ),
        ];
		$placeholder += self::datetime_place_holder_list();
        $placeholder += self::get_custom_place_holders_list('work_orders');
		if($work_order_only){
			return $placeholder;
		}

        $placeholder += self::site_place_holder_list();
        $placeholder += self::staff_place_holder_list();
        $placeholder += self::client_place_holder_list();
        $placeholder += self::branches_place_holder_list('work_order');
        return $placeholder ;
    }
    public static function client_place_holder($client) {
//        dd($client);
        if (!isset($client['Client'])) {
            $client['Client'] = $client;
        }
        $controller = new InvoicesController();

        $view = new View($controller, false);
        //get location lat,lng,zoom
        $location = explode(',',$client['Client']['map_location']);

        //get followUpStatuses data
        $FollowUpStatus = \ClassRegistry::init('FollowUpStatus', 'Model');
        $Post = \ClassRegistry::init('Post', 'Model');
        $FollowUpStatus = $FollowUpStatus->getLisWithColorList($Post::CLIENT_TYPE);
        $barcode_image_link = "https://". getCurrentSite("subdomain") . "/printable_templates/get_barcode_image/". CLIENT_BARCODE . "/". $client['Client']['id'];
        $imgUrl = resolve(AttachmentsService::class)->resolveClientImage((object)$client['Client']);
        $placeholder = array(
            '{%client_number%}' => $client['Client']['client_number'],
            '{%client_business_name%}' => $client['Client']['business_name'] ?? sprintf('%s %s',$client['Client']['first_name'],$client['Client']['last_name']),
            '{%client_name%}' => trim($client['Client']['first_name'] . ' ' . $client['Client']['last_name']) != ''
                ? $client['Client']['first_name'] . ' ' . $client['Client']['last_name']
                : $client['Client']['business_name'],
            '{%client_first_name%}' => $client['Client']['first_name'] ?? null,
            '{%client_last_name%}' => $client['Client']['last_name'] ?? null,
            '{%client_phone%}' => $client['Client']['phone1'] ?? null,
            '{%client_mobile%}' => $client['Client']['phone2'] ?? null,
            '{%client_email%}' => $client['Client']['email'] ?? null,
            '{%client_full_address%}' => @$view->element('format_address_html', $client['Client']) ?: '',
            '{%client_address%}' => $client['Client']['address1'] . (empty($client['Client']['address2']) ? '' : '<br/>' . $client['Client']['address2']),
            '{%client_postcode%}' => $client['Client']['postal_code'],
            '{%client_postal_code%}' => $client['Client']['postal_code'],
            '{%client_city%}' => $client['Client']['city'],
            '{%client_state%}' => $client['Client']['state'],
            '{%client_country%}' => get_country_code($client['Client']['country_code']),
            '{%client_secondary_name%}' => $client['Client']['secondary_name'],
            '{%client_secondary_address%}' => $client['Client']['secondary_address1'] . (empty($client['Client']['secondary_address2']) ? '' : '<br/>' . $client['Client']['secondary_address2']),
            '{%client_secondary_address2%}' => $client['Client']['secondary_address2'],
            '{%client_secondary_postcode%}' => $client['Client']['secondary_postal_code'],
            '{%client_secondary_city%}' => $client['Client']['secondary_city'],
            '{%client_secondary_state%}' => $client['Client']['secondary_state'],
            '{%client_secondary_country%}' => get_country_code($client['Client']['secondary_country_code']),
            '{%client_bn1%}' => $client['Client']['bn1'],
			'{%client_bn2%}' => $client['Client']['bn2'],
			'{%client_business_registration_info1%}' => !empty($client['Client']['bn1'])?__($client['Client']['bn1_label'],true).': '.$client['Client']['bn1']:'',
            '{%client_business_registration_info2%}' => !empty($client['Client']['bn2'])?__($client['Client']['bn2_label'],true).': '.$client['Client']['bn2']:'',
            '{%client_barcode%}' => "<img src='{$barcode_image_link}'>", //OIBarcode::getBarcodeImage(OIBarcode::getAutoBarcode(CLIENT_BARCODE, $client['Client']['id']), ['style'=>['width'=>'20%','height'=>'30px']]),
            "{%client_barcode_number%}" => OIBarcode::getAutoBarcode(CLIENT_BARCODE, $client['Client']['id']),
            "{%client_photo%}" => self::getImageHtmlTag($imgUrl),
            "{%client_status_name%}" => $FollowUpStatus[$client['Client']['follow_up_status']]['name'] ?? null,
            "{%client_category%}" => $client['Client']['category'],
            "{%client_credit_limit%}" => $client['Client']['credit_limit'],
            "{%client_credit_period%}" => $client['Client']['credit_period'],
        );

        App::import('Vendor', 'settings');
        $client_settings = settings::getPluginValues(ClientsPlugin);

        if($client_settings['map_location'] == '1')
        {
            $placeholder  += [ "{%client_location%}" => self::getMapAsImage($location[1] ?? null,$location[0]?? null,$location[2]??null, 600 , 400 ) ];
        }

        if($client_settings['starting_balance'] == "1")
        {
            $placeholder  += [ "{%client_starting_balance%}" => format_price($client['Client']['starting_balance']) ];
        }

        if($client_settings['gender'] == "1")
        {
            $placeholder  += [ "{%client_gender%}" =>  ( !isset($client['Client']['gender']) ? __("Not Selected",true) : ( $client['Client']['gender'] == 1 ? __("Male", true) : __("Female", true) ) ) ];
        }

        if($client_settings['birth_date'] == "1")
        {
            $placeholder  += [ "{%client_birth_date%}" => format_date($client['Client']['birth_date']) ];
        }
        if($client_settings['birth_date'] == "1")
        {

        }
        if ($client_settings['national_id'] == "1") {
            $placeholder += ["{%client_national_id%}" =>$client['Client']['national_id']];
        }

        if ($client_settings['link'] == "1") {
            $placeholder += ["{%link%}" =>$client['Client']['link']];
        }

        foreach ($placeholder as $key => $holder) {
            $placeholder[str_replace('_', '-', $key)] = $holder;
        }
		$placeholder  +=self::get_custom_place_holders('clients',$client['Client']['id']);
        if (isset($client['Client']['id'])) {
            $placeholder  += self::get_additional_fields_place_holders_values(EntityKeyTypesUtil::CLIENT_ENTITY_KEY, $client['Client']['id']);
        }
        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholder += self::branches_place_holder($client['Client']['branch_id'], 'client');
        }
        // check if membership plugin installed
        if (ifPluginActive(MEMBERSHIP_PLUGIN)) {
            $membershipModel = GetObjectOrLoadModel('Membership');
            $membershipData = $membershipModel->find('first', array(
                'recursive' => 1,
                'conditions' => [
                    'Membership.client_id' => $client['Client']['id'],
                    'deleted_at' => NULL
                ]
            ));
            if (isset($membershipData['Membership'])) {
                $packageModel = GetObjectOrLoadModel('Package');
                $packageData = $packageModel->findById($membershipData['Membership']['package_id']);
                $membership = $membershipData['Membership'];
                $package = $packageData['Package'];
                $placeholder += [
                    "{%client_membership_id%}" => $membership['id'],
                    "{%client_membership_expiry_date%}" => format_date($membership['expiry_date']),
                    "{%client_membership_join_date%}" => format_date($membership['join_date']),
                    "{%client_membership_current_package%}" => $package['name'],
                ];
            }
        }

        return $placeholder;
    }

    public static function client_custom_place_holder($client) {
        $ClientModel = ClassRegistry::init('Client', 'Model');
        $ClientModel->recursive = -1;
        $row = $ClientModel->read(null, $client);
        return self::client_place_holder($row);
    }

    public static function client_place_holder_list() {

        $placeholder = array(
            "{%client_business_name%}" => __('Client Business Name', true),
            "{%client_number%}" => __('Client Number', true),
            "{%client_first_name%}" => __("Client first name", true),
            "{%client_last_name%}" => __("Client last name", true),
            "{%client_phone%}" => __("Client Phone", true),
            "{%client_mobile%}" => __("Client Mobile", true),
            "{%client_email%}" => __("Client Email", true),
            "{%client_full_address%}" => __('Client Full Address', true),
            "{%client_address%}" => __("Client Street Address", true),
            "{%client_postcode%}" => __("Postcode", true),
            "{%client_city%}" => __("City", true),
            "{%client_state%}" => __("State", true),
            "{%client_country%}" => __("Country", true),
            "{%client_secondary_name%}" => __("Client Secondary Name", true),
            "{%client_secondary_address%}" => __("Secondary Street Address", true),
            "{%client_secondary_postcode%}" => __("Client Secondary Postcode", true),
            "{%client_secondary_city%}" => __("Client Secondary City", true),
            "{%client_secondary_state%}" => __("Client Secondary State", true),
            "{%client_secondary_country%}" => __("Client Secondary Country", true),
            "{%client_bn1%}" => __("Client Business Number 1", true),
			"{%client_bn2%}" => __("Client Business Number 2", true),
			'{%client_business_registration_info1%}' => __("Client Business Info 1", true),
			'{%client_business_registration_info2%}' =>  __("Client Business Info 2", true),
            "{%client_barcode%}" => __("Client barcode", true),
            "{%client_barcode_number%}" => __("Client barcode number", true),
            "{%client_photo%}" => __("Client photo", true),
            "{%client_status_name%}" => __("Client Status Name", true),
            "{%client_category%}" => __("Client Category", true),
            "{%client_credit_limit%}" => __("Credit Limit", true),
            "{%client_credit_period%}" => __("Credit Period", true),
        );

        App::import('Vendor', 'settings');
        $client_settings = settings::getPluginValues(ClientsPlugin);

        if ($client_settings['map_location'] == '1') {
            $placeholder += ["{%client_location%}" => __("Client Location", true)];
        }

        if ($client_settings['starting_balance'] == "1") {
            $placeholder += ["{%client_starting_balance%}" => __("Client Starting Balance", true)];
        }

        if ($client_settings['gender'] == "1") {
            $placeholder += ["{%client_gender%}" => __("Client Gender", true)];
        }

        if ($client_settings['birth_date'] == "1") {
            $placeholder += ["{%client_birth_date%}" => __("Client Birth Date", true)];
        }
        if ($client_settings['national_id'] == "1") {
            $placeholder += ["{%client_national_id%}" => __("Client National ID", true)];
        }
        if ($client_settings['link'] == "1") {
            $placeholder += ["{%link%}" => __("Link", true)];
        }

                App::import('Vendor', 'sites_local');
              $csite=  getCurrentSite('country_code');

              $Localize = BusinessNumberCommonFieldsUtil::getBusinessNumberFields();
              if(count($Localize[$csite] ?? [])==1){
              $placeholder['{%client_bn1%}']=__($Localize[$csite]['bn1']['label'],true);
              }else{
                if(isset($Localize[$csite])) {
                    $placeholder['{%client_bn1%}']=__($Localize[$csite]['bn1']['label'],true);
                    $placeholder['{%client_bn2%}']=__($Localize[$csite]['bn2']['label'],true);
                }
              }
		$placeholder += self::get_custom_place_holders_list('clients');
		$placeholder += self::get_additional_fields_place_holders_list(EntityKeyTypesUtil::CLIENT_ENTITY_KEY);

        $placeholder += (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('client') : []);
        if (ifPluginActive(MEMBERSHIP_PLUGIN)) {
            $placeholder += [
                "{%client_membership_id%}" => __("Membership ID", true),
                "{%client_membership_expiry_date%}" => __("Membership Expiry date", true),
                "{%client_membership_join_date%}" => __("Membership Join date", true),
                "{%client_membership_current_package%}" => __("Membership Current Package", true),
            ];
        }
        return $placeholder;
    }

    public static function staff_place_holder($staff_id,$prefix = '') {
        if(strlen($prefix)){
            $prefix .= '_';
        }
        if ($staff_id == 0) {
            $placeholders['{%'.$prefix.'staff_member_name%}'] = getCurrentSite('first_name') . ' ' . getCurrentSite('last_name');
            $placeholders['{%'.$prefix.'staff_member_email%}'] = getCurrentSite('email');
            $placeholders['{%'.$prefix.'staff_member_phone%}'] = getCurrentSite('phone1');
            $placeholders['{%'.$prefix.'staff_member_mobile%}'] = getCurrentSite('phone2');
            $placeholders['{%'.$prefix.'staff_member_address1%}'] = getCurrentSite('address1');
            $placeholders['{%'.$prefix.'staff_member_address2%}'] = getCurrentSite('address2');
            $placeholders['{%'.$prefix.'staff_member_city%}'] = getCurrentSite('city');
            $placeholders['{%'.$prefix.'staff_member_state%}'] = getCurrentSite('state');
            $placeholders['{%'.$prefix.'staff_member_postalcode%}'] = getCurrentSite('postal_code');
            $placeholders['{%'.$prefix.'staff_member_country%}'] = get_country_code(getCurrentSite('country_code'));
        } else {
            $Staff = ClassRegistry::init('Staff', 'Model');
            $StaffRow = $Staff->find('first', array('conditions' => array('Staff.id' => $staff_id)));

            // warning suppress
            if (!$StaffRow) {
                $StaffRow = ['Staff' => [
                    'name' => null,
                    'email_address' => null,
                    'home_phone' => null,
                    'mobile' => null,
                    'address1' => null,
                    'address2' => null,
                    'city' => null,
                    'state' => null,
                    'postal_code' => null,
                ]];
            }
            // end warning suppress
            $placeholders['{%'.$prefix.'staff_member_name%}'] = $StaffRow['Staff']['name'];
            $placeholders['{%'.$prefix.'staff_member_email%}'] = $StaffRow['Staff']['email_address'];
            $placeholders['{%'.$prefix.'staff_member_phone%}'] = $StaffRow['Staff']['home_phone'];
            $placeholders['{%'.$prefix.'staff_member_mobile%}'] = $StaffRow['Staff']['mobile'];
            $placeholders['{%'.$prefix.'staff_member_address1%}'] = $StaffRow['Staff']['address1'];
            $placeholders['{%'.$prefix.'staff_member_address2%}'] = $StaffRow['Staff']['address2'];
            $placeholders['{%'.$prefix.'staff_member_city%}'] = $StaffRow['Staff']['city'];
            $placeholders['{%'.$prefix.'staff_member_state%}'] = $StaffRow['Staff']['state'];
            $placeholders['{%'.$prefix.'staff_member_postalcode%}'] = $StaffRow['Staff']['postal_code'];
            $placeholders['{%'.$prefix.'staff_member_country%}'] = get_country_code($StaffRow['Staff']['country_code'] ?? null);
        }
        foreach ($placeholders as $key => $holder) {
            $placeholders[str_replace('_', '-', $key)] = $holder;
        }
        foreach ($placeholders as $key => $holder) {
            $placeholders[str_replace('_member_', '-', $key)] = $holder;
        }

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $staffBranchId = $StaffRow['Staff']['branch_id'] ?? null;
            $placeholders += self::branches_place_holder($staff_id != 0 ? $staffBranchId : getMainBranch('id'), 'staff');
        }

        return $placeholders;
    }


    public static function staff_place_holder_list() {

        $placeholders['{%staff_member_name%}'] = __("Staff member name", true);
        $placeholders['{%staff_member_email%}'] = __("Staff member email", true);
        $placeholders['{%staff_member_phone%}'] = __("Staff member telephone", true);
        $placeholders['{%staff_member_mobile%}'] = __("Staff member mobile", true);
        $placeholders['{%staff_member_address1%}'] = __("Staff member address 1", true);
        $placeholders['{%staff_member_address2%}'] = __("Staff member address 2", true);
        $placeholders['{%staff_member_city%}'] = __("Staff member city", true);
        $placeholders['{%staff_member_state%}'] = __("Staff member state", true);
        $placeholders['{%staff_member_postalcode%}'] = __("Staff member postal code", true);
        $placeholders['{%staff_member_country%}'] = __("Staff member country", true);
        return $placeholders + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('staff') : []);
    }


    public static function assigned_staff_place_holder($staff_id) {

        if ($staff_id == 0) {
            $staff = getCurrentSite();
            $placeholders['{%assigned_staff_member_name%}'] = $staff['first_name'] . ' ' . $staff['last_name'];
            $placeholders['{%assigned_staff_member_email%}'] = $staff['email'];
            $placeholders['{%assigned_staff_member_phone%}'] = $staff['phone1'];
            $placeholders['{%assigned_staff_member_mobile%}'] = $staff['phone2'];
            $placeholders['{%assigned_staff_member_address1%}'] = $staff['address1'];
            $placeholders['{%assigned_staff_member_address2%}'] = $staff['address2'];
            $placeholders['{%assigned_staff_member_city%}'] = $staff['city'];
            $placeholders['{%assigned_staff_member_state%}'] = $staff['state'];
            $placeholders['{%assigned_staff_member_postalcode%}'] = $staff['postal_code'];
            $placeholders['{%assigned_staff_member_country%}'] = get_country_code($staff['country_code']);
        } else {
            $Staff = ClassRegistry::init('Staff', 'Model');
            $StaffRow = $Staff->find('first', array('conditions' => array('Staff.id' => $staff_id)));

            $placeholders['{%assigned_staff_member_name%}'] = $StaffRow['Staff']['name'];
            $placeholders['{%assigned_staff_member_email%}'] = $StaffRow['Staff']['email_address'];
            $placeholders['{%assigned_staff_member_phone%}'] = $StaffRow['Staff']['home_phone'];
            $placeholders['{%assigned_staff_member_mobile%}'] = $StaffRow['Staff']['mobile'];
            $placeholders['{%assigned_staff_member_address1%}'] = $StaffRow['Staff']['address1'];
            $placeholders['{%assigned_staff_member_address2%}'] = $StaffRow['Staff']['address2'];
            $placeholders['{%assigned_staff_member_city%}'] = $StaffRow['Staff']['city'];
            $placeholders['{%assigned_staff_member_state%}'] = $StaffRow['Staff']['state'];
            $placeholders['{%assigned_staff_member_postalcode%}'] = $StaffRow['Staff']['postal_code'];
            $placeholders['{%assigned_staff_member_country%}'] = get_country_code($StaffRow['Staff']['country_code']);
        }
        foreach ($placeholders as $key => $holder) {
            $placeholders[str_replace('_', '-', $key)] = $holder;
        }
        foreach ($placeholders as $key => $holder) {
            $placeholders[str_replace('_member_', '-', $key)] = $holder;
        }

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholders += self::branches_place_holder($staff_id != 0 ? $StaffRow['Staff']['branch_id'] : getMainBranch('id'),'assigned_staff');
        }

        return $placeholders;
    }

    public static function assigned_staff_place_holder_list() {

        $placeholders['{%assigned_staff_member_name%}'] = __("Assigned Staff member name", true);
        $placeholders['{%assigned_staff_member_email%}'] = __("Assigned Staff member email", true);
        $placeholders['{%assigned_staff_member_phone%}'] = __("Assigned Staff member telephone", true);
        $placeholders['{%assigned_staff_member_mobile%}'] = __("Assigned Staff member mobile", true);
        $placeholders['{%assigned_staff_member_address1%}'] = __("Assigned Staff member address 1", true);
        $placeholders['{%assigned_staff_member_address2%}'] = __("Assigned Staff member address 2", true);
        $placeholders['{%assigned_staff_member_city%}'] = __("Assigned Staff member city", true);
        $placeholders['{%assigned_staff_member_state%}'] = __("Assigned Staff member state", true);
        $placeholders['{%assigned_staff_member_postalcode%}'] = __("Assigned Staff member postal code", true);
        $placeholders['{%assigned_staff_member_country%}'] = __("Assigned Staff member country", true);
        return $placeholders + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('assigned_staff') : []);
    }

    public static function shop_front_place_holder() {

        $placeholders['{%business-name% }'] = __("Business Name", true);
        $placeholders['{%client-name%}'] = __("Client Name", true);
        $placeholders['{%invoice-no%}'] = __("Invoice Number", true);
        $placeholders['{%view-link%}'] = __("View Link", true);
        $placeholders['{%invoice-total%}'] = __("Invoice Total", true);
        $placeholders['{%unpaid%}'] = __("Un paid", true);
        $placeholders['{%address1%}'] = __("First Address", true);
        $placeholders['{%address2%}'] = __("Second Address", true);
        $placeholders['{%postal-code%}'] = __("Postal code", true);
        $placeholders['{%state%}'] = __("State", true);
        $placeholders['{%City%}'] = __("City", true);
        $placeholders['{%view-link%}'] = __("View Link", true);
        $placeholders['{%telephone%}'] = __("Telephone", true);
        return $placeholders;
    }

    public static function invoice_place_holder_list($site=null) {
        $placeholders =  array(
            '{%client_business_name%}' => __('Client business name', true),
            '{%client_name%}' => __('Client Name', true),
            '{%client_first_name%}' => __('Client first name', true),
            '{%client_last_name%}' => __('Client last name', true),
            '{%client_address%}' => __('Client address', true),
            '{%client_city%}' => __('Client city', true),
            '{%client_state%}' => __('Client State', true),
            '{%client_postal_code%}' => __('Client postal code', true),
            '{%client_country%}' => __('Client country', true),
            '{%client_number%}' => __('Client Number',true),
            '{%invoice_no%}' => __('Invoice No', true),
            '{%invoice_order_no%}' => __('Invoice Order No', true),
            '{%invoice_date%}' => __('Invoice date', true),
            '{%manual_status%}' => __('Invoice Manual Status', true),
            '{%due_date%}' => __('Due date', true),
            '{%issue_date%}' => __('Issue Date', true),
            '{%invoice_subtotal%}' => __('Invoice subtotal', true),
            '{%invoice_total%}' => __('Invoice total', true),
            '{%deposit%}' => __('Amount to be paid ', true),
            '{%paid%}' => __('Amount paid', true),
            '{%unpaid%}' => __('Unpaid Amount', true),
            '{%discount%}' => __('Discount', true),
            '{%tax-total%}' => __('Tax Total', true),
            '{%item-discount-total%}' => __('Item discount Total', true),
            '{%payment_link%}' => __('Payment link', true),
            '{%view_link%}' => __('View link', true),
            '{%short-link%}' => __('Short Link', true),
            '{%invoice_notes%}' => __('Invoice Notes', true),
            '{%invoice_barcode%}' => __('Invoice barcode', true),
            '{%invoice_barcode_number%}' => __('Invoice barcode number', true),
            '{%invoice_currency_code%}' => __('Invoice currency code', true),
            '{%created_date%}' => __('Created date', true),
            '{%created_time%}' => __('Created time', true),
            '{%client_view_link%}' => __('Client view link',true),
            '{%qr_code_link%}' => __('QR Code Link',true),
            '{%qr_code_image%}' => __('QR Code Image',true),
            '{%owner_view_link%}' => __('Owner view link',true),
			'{%ware_house%}' => __('Warehouse', true),
			'{%sales_person%}' => __('Sales person', true),
            '{%invoice_item_count%}' => __('Invoice Item Count',true),
            '{%invoice_item_qty%}' => __('Invoice quantity count',true),
            '{%invoice_payment_status%}' =>__('Invoice payment status',true) ,
            '{%extra_details.client_balance%}' => __('Client Balance',true),
            '{%invoice_advanced_client_balance%}' => __('Advanced Client balance',true),
            '{%invoice_payment_table%}' => __('Invoice Payment List',true),
            '{%order_source%}' => __('Order Source',true),
            '{%invoice_type%}' => __('Invoice Type',true),
        );

        if(ifPluginActive(EINVOICE_SA_PLUGIN))
            {
                $placeholders +=
                    [
                        '{%sa_qr_code_link%}' => __('QR Code Link',true),
                        '{%sa_qr_code_image%}' => __('QR Code Image',true),
                        '{%sa_auto_invoice_title%}' => __('Auto Invoice Title',true),
                    ];
            }


        if(ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::JORDAN_EINVOICE_PLUGIN))
        {
            $placeholders +=
                [
                    '{%ja_qr_code_link%}' => __('QR Code Link',true),
                    '{%ja_qr_code_image%}' => __('QR Code Image',true),
                    '{%ja_auto_invoice_title%}' => __('Auto Invoice Title',true),
                ];
        }

        //get the tafketed value of amount
        $placeholders += [
            '{%invoice_total_spelled%}' => __('Invoice total Spelled', true),
            '{%deposit_spelled%}' => __('Amount to be paid Spelled', true),
            '{%paid_spelled%}' => __('Amount paid Spelled', true),
            '{%unpaid_spelled%}' => __('Unpaid Amount Spelled', true),
            '{%discount_spelled%}' => __('Discount Spelled', true)
        ];

        $placeholders += self::get_custom_place_holders_list('clients');
		$placeholders += self::get_additional_fields_place_holders_list(EntityKeyTypesUtil::CLIENT_ENTITY_KEY);
        return $placeholders +self::get_custom_place_holders_list('invoices')+ (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('invoice') : []);
    }

    public static function payment_place_holder_list() {
        return array(
            '{%business_name%}' => __('Business name', true),
            '{%business_email%}' => __('Business email', true),
            '{%address1%}' => __('Business address 1', true),
            '{%address2%}' => __('Business address 2', true),
            '{%city%}' => __('Business city', true),
            '{%state%}' => __('Business state', true),
            '{%postal_code%}' => __('Business postal code', true),
            '{%telephone%}' => __('Business Telephone', true),
            '{%mobile%}' => __('Business Mobile', true),
            '{%client_business-name%}' => __('Client business name', true),
            '{%client_name%}' => __('Client name', true),
            '{%client_first-name%}' => __('Client first name', true),
            '{%client_last-name%}' => __('Client last name', true),
            '{%payment_no%}' => __('Payment No', true),
            '{%transaction_id%}' => __('Transaction ID', true),
            '{%payment_amount%}' => __('Payment amount', true),
            '{%invoice_no%}' => __('Invoice No', true),
            '{%payment_date%}' => __('Payment date', true),
            '{%payment_method%}' => __('Payment method', true),
            '{%payment_message%}' => __('Payment method message', true),
            '{%payment_currency%}' => __('Currency', true),
            '{%invoice_paid%}' => __('Invoice\'s amount paid', true),
            '{%invoice_unpaid%}' => __('Invoice\'s amount due', true),
            '{%view_link%}' => __('Online invoice link', true),
        ) + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('payment') : []);
    }

    public static function invoice_payment_place_holder_list($payment_only=false) {
        $array = array(
            '{%invoice_payment_No%}' => __('Payment No',true),
            '{%payment_method%}' => __('Payment method', true),
			'{%payment_method_display%}' => __('Payment method display name',true),
            '{%amount%}' => __('Amount', true),
            '{%currency_code%}' => __('Currency', true),
            '{%added_by%}' => __('Added by', true),
            '{%date%}' => __('Date', true),
            '{%issue_date%}' => __('Issue Date', true),
            '{%email%}' => __('Email', true),
            '{%status%}' => __('Status', true),
            '{%payment_status%}' => __('Payment Status', true),
            '{%notes%}' => __('Notes', true),
            '{%response_code%}' => __('Response code', true),
            '{%response_message%}' => __('Response message', true),
            '{%first_name%}' => __('First name', true),
            '{%last_name%}' => __('Last name', true),
            '{%address1%}' => __('Address 1', true),
            '{%address2%}' => __('Address 2', true),
            '{%city%}' => __('City', true),
            '{%state%}' => __('State', true),
            '{%postal_code%}' => __('Postal code', true),
            '{%country_code%}' => __('Country code', true),
            '{%phone1%}' => __('Phone 1', true),
            '{%phone2%}' => __('Phone 2', true),
            '{%receipt_notes%}' => __('Receipt notes', true),
            '{%invoice_payment_attachment%}' => __('Attachment', true),
            '{%transaction_id%}' => __("Transaction ID", true),
            '{%treasury_name%}'=> __("Treasury",true),
            '{%spelled_amount%}' => __("Spelled Amount",true),
        );
		if($payment_only){
			return $array;
		}
        $array += self::invoice_place_holder_list();
        $array += self::client_place_holder_list();
        $array += self::staff_place_holder_list();
        $array += self::site_place_holder_list();
        return $array + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('invoice_payment') : []);
    }

    public static function purchase_order_payment_place_holder_list($payment_only=false) {
        $array = array(
            '{%purchase_order_payment_No%}' => __('Payment No',true),
            '{%payment_method%}' => __('Payment method', true),
			'{%payment_method_display%}' => __('Payment method display name',true),
            '{%amount%}' => __('Amount', true),
            '{%currency_code%}' => __('Currency', true),
            '{%added_by%}' => __('Added by', true),
            '{%date%}' => __('Date', true),
            '{%issue_date%}' => __('Issue Date', true),
            '{%email%}' => __('Email', true),
            '{%status%}' => __('Status', true),
            '{%payment_status%}' => __('Payment Status', true),
            '{%notes%}' => __('Notes', true),
            '{%response_code%}' => __('Response code', true),
            '{%response_message%}' => __('Response message', true),
            '{%first_name%}' => __('First name', true),
            '{%last_name%}' => __('Last name', true),
            '{%address1%}' => __('Address 1', true),
            '{%address2%}' => __('Address 2', true),
            '{%city%}' => __('City', true),
            '{%state%}' => __('State', true),
            '{%postal_code%}' => __('Postal code', true),
            '{%country_code%}' => __('Country code', true),
            '{%phone1%}' => __('Phone 1', true),
            '{%phone2%}' => __('Phone 2', true),
            '{%receipt_notes%}' => __('Receipt notes', true),
            '{%invoice_payment_attachment%}' => __('Attachment', true),
            '{%transaction_id%}' => __("Transaction ID", true),
            '{%treasury_name%}'=> __("Treasury",true),
            '{%spelled_amount%}' => __("Spelled Amount",true),
        );
		if($payment_only){
			return $array;
		}
        $array += self::purchaseorders_place_holder_list();
        $array += self::supplier_place_holder_list();
        $array += self::staff_place_holder_list();
        $array += self::site_place_holder_list();
        return $array + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('purchase_order_payment') : []);
    }
	/**
	* Get invoice payment place holder
	* @param array $payment payment details
	* @param boolean $only_payment if true will return payment place holder only
	* @return array of placeholders
	*/
    public static function invoice_payment_place_holder($payment,$only_payment=false) {
		$InvoicePayment=GetObjectOrLoadModel('InvoicePayment');
		$pamyent_method_name=$InvoicePayment::getPaymentMethods();
        $payment_status_name=$InvoicePayment::getPaymentStatus();
        if(isset($payment['InvoicePayment']))
        {
            $payment = $payment['InvoicePayment'];
        }
        //render the attachement as image and image from pdf
        $file_type = pathinfo($payment['attachment_full_path'], PATHINFO_EXTENSION);
        if(in_array(strtolower($file_type), ['jpg','jpeg','bmp','png','gif'])) //image type render as image tag
        {
            $attachment_file = self::getImageTag($payment['attachment_full_path']);
        }
        else if( strtolower($file_type) == 'pdf' ) //image type render as pdf convert pdf to image and
        {
            $attachment_file = "<a href=\"https://".getCurrentSite('subdomain')."/" . $payment['attachment_full_path'] ."\">Downlaod Attachment</a>";
        }
  
        //show the tafketed number
        $amount_Tafketed = self::tafketWithChange($payment['amount'], $payment['currency_code']);
        $staff_placeholders = self::staff_place_holder($payment['staff_id']);
//        dd($payment);

        $Treasury= ClassRegistry::init('Treasury', 'Model');
        $treasury_name=$Treasury->find(array('id'=>$payment['treasury_id']));
        $treasury_name = $treasury_name['Treasury']['name'];
        if ('client_credit' == $payment['payment_method']){
            $treasury_name = __('Client Credit', true);
        }

        $SitePaymentGateway= ClassRegistry::init( 'SitePaymentGateway');
        $SitePaymentGatewayRow=$SitePaymentGateway->find(array('SitePaymentGateway.payment_gateway'=>$payment['payment_method']));

        $array = array(
            '{%invoice_payment_No%}' => $payment['code'] ?? $payment['id'],
            '{%payment_method_display%}' => empty($SitePaymentGatewayRow['SitePaymentGateway']['label'])?__($pamyent_method_name[$payment['payment_method']],true):__($SitePaymentGatewayRow['SitePaymentGateway']['label'],true),
            '{%payment_method%}' => empty($SitePaymentGatewayRow['SitePaymentGateway']['label'])?__($pamyent_method_name[$payment['payment_method']],true):__($SitePaymentGatewayRow['SitePaymentGateway']['label'],true),
            '{%amount%}' => format_price_simple($payment['amount'],false),
            '{%currency_code%}' => $payment['currency_code'],
            '{%added_by%}' => $staff_placeholders['{%staff_member_name%}'],
            '{%date%}' => format_date($payment['date']),
            '{%email%}' => $payment['email'],
            '{%status%}' => $payment_status_name[$payment['status']],
            '{%payment_status%}' => $payment_status_name[$payment['status']],
            '{%notes%}' => $payment['notes'],
            '{%response_code%}' => $payment['response_code'],
            '{%response_message%}' => $payment['response_message'],
            '{%first_name%}' => $payment['first_name'],
            '{%last_name%}' => $payment['last_name'],
            '{%address1%}' => $payment['address1'],
            '{%address2%}' => $payment['address2'],
            '{%city%}' => $payment['city'],
            '{%state%}' => $payment['state'],
            '{%postal_code%}' => $payment['postal_code'],
            '{%country_code%}' => $payment['country_code'],
            '{%phone1%}' => $payment['phone1'],
            '{%phone2%}' => $payment['phone2'],
            '{%receipt_notes%}' => $payment['receipt_notes'],
            '{%invoice_payment_attachment%}' => $attachment_file,
            '{%transaction_id%}' => $payment['transaction_id'],
            '{%spelled_amount%}' => $amount_Tafketed,
            '{%treasury_name%}'=>$treasury_name,
        );
		if($only_payment === true){
		return $array;
		}
        $Site = ClassRegistry::init('Site', 'Model');
        $site = $Site->read(null, getCurrentSite('id'));

        $site_placeholders = self::site_place_holder($site['Site']);
        $array += $site_placeholders ;

        if( !empty($payment['invoice_id']) )
        {
            $Invoice = ClassRegistry::init('Invoice', 'Model');
            $invoice = $Invoice->read(null, $payment['invoice_id']);

            $invoice_placeholders = self::invoice_get_all_place_holders($invoice['Invoice']);
            $array += $invoice_placeholders;
        }

        if( !empty($payment['client_id']) )
        {
            $Client = ClassRegistry::init('Client', 'Model');
            $client = $Client->read(null, $payment['client_id']);

            $client_placeholders = self::client_place_holder($client['Client']);
            $array += $client_placeholders;
        }



            $array += $staff_placeholders ;


        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $array += self::branches_place_holder($payment['branch_id'],'invoice_payment');
        }

        
        /**
         * Processes the 'client_balance' value from the payment's extra details and updates the placeholder array.
         * 
         * - Decodes the 'extra_details' JSON field into an associative array.
         * - Checks if 'client_balance' exists within the decoded extra details.
         * - Removes any existing placeholder entry for 'client_balance' in the target array.
         * - Reassigns the formatted client balance to the corresponding placeholder key 
         *   using the correct currency format.
         */
        $extra_details = json_decode($payment['extra_details'], true);
        if (isset($extra_details['client_balance'])){
            unset($array["{%extra_details.client_balance%}"]);
            $array["{%extra_details.client_balance%}"] = format_price($extra_details['client_balance'], $payment['currency_code']);
            if (empty($array['{%invoice_advanced_client_balance%}'])) {
                $array['{%invoice_advanced_client_balance%}'] = format_price($extra_details['client_balance']-$payment['amount'], $payment['currency_code']);
            }
        }
        return $array;


    }

    public static function purchase_order_payment_place_holder($payment,$only_payment=false) {
		$PurchaseOrderPayment=GetObjectOrLoadModel('PurchaseOrderPayment');
		$pamyent_method_name=$PurchaseOrderPayment::getPaymentMethods();
        $pamyent_method_name['supplier_credit']=__('Supplier Credit',true);
        $payment_status_name=$PurchaseOrderPayment::getPaymentStatus();
        if(isset($payment['PurchaseOrderPayment']))
        {
            $payment = $payment['PurchaseOrderPayment'];
        }
        //render the attachement as image and image from pdf
        $file_type = pathinfo($payment['attachment_full_path'], PATHINFO_EXTENSION);
        if(in_array(strtolower($file_type), ['jpg','jpeg','bmp','png','gif'])) //image type render as image tag
        {
            $attachment_file = self::getImageTag($payment['attachment_full_path']);
        }
        else if( strtolower($file_type) == 'pdf' ) //image type render as pdf convert pdf to image and
        {
            $attachment_file = "<a href=\"https://".getCurrentSite('subdomain')."/" . $payment['attachment_full_path'] ."\">Downlaod Attachment</a>";
        }

        //show the tafketed number
        $amount_Tafketed = self::tafketWithChange($payment['amount'], $payment['currency_code']);
        $staff_placeholders = self::staff_place_holder($payment['staff_id']);

        $Treasury= ClassRegistry::init('Treasury', 'Model');
        $treasury_name=$Treasury->find(array('id'=>$payment['treasury_id']));

        $SitePaymentGateway= ClassRegistry::init( 'SitePaymentGateway');
        $SitePaymentGatewayRow=$SitePaymentGateway->find(array('SitePaymentGateway.payment_gateway'=>$payment['payment_method']));

        $treasury_name = $treasury_name['Treasury']['name'];
        if ('supplier_credit' == $payment['payment_method']){
            $treasury_name = __('Supplier Credit', true);
        }
        $array = array(
            '{%purchase_order_payment_No%}' => $payment['code'] ?? $payment['id'],
            '{%payment_method_display%}' => empty($SitePaymentGatewayRow['SitePaymentGateway']['label'])?__($pamyent_method_name[$payment['payment_method']],true):__($SitePaymentGatewayRow['SitePaymentGateway']['label'],true),
            '{%payment_method%}' => empty($SitePaymentGatewayRow['SitePaymentGateway']['label'])?__($pamyent_method_name[$payment['payment_method']],true):__($SitePaymentGatewayRow['SitePaymentGateway']['label'],true),
            '{%amount%}' => format_price_simple($payment['amount'],false),
            '{%currency_code%}' => $payment['currency_code'],
            '{%added_by%}' => $staff_placeholders['{%staff_member_name%}'],
            '{%date%}' => format_date($payment['date']),
            '{%email%}' => $payment['email'],
            '{%status%}' => $payment_status_name[$payment['status']],
            '{%payment_status%}' => $payment_status_name[$payment['status']],
            '{%notes%}' => $payment['notes'],
            '{%response_code%}' => $payment['response_code'],
            '{%response_message%}' => $payment['response_message'],
            '{%first_name%}' => $payment['first_name'],
            '{%last_name%}' => $payment['last_name'],
            '{%address1%}' => $payment['address1'],
            '{%address2%}' => $payment['address2'],
            '{%city%}' => $payment['city'],
            '{%state%}' => $payment['state'],
            '{%postal_code%}' => $payment['postal_code'],
            '{%country_code%}' => $payment['country_code'],
            '{%phone1%}' => $payment['phone1'],
            '{%phone2%}' => $payment['phone2'],
            '{%receipt_notes%}' => $payment['receipt_notes'],
            '{%invoice_payment_attachment%}' => $attachment_file,
            '{%transaction_id%}' => $payment['transaction_id'],
            '{%spelled_amount%}' => $amount_Tafketed,
            '{%treasury_name%}'=>$treasury_name,
        );

		if($only_payment === true){
            return $array;
		}

        $Site = ClassRegistry::init('Site', 'Model');
        $site = $Site->read(null, getCurrentSite('id'));

        $site_placeholders = self::site_place_holder($site['Site']);
        $array += $site_placeholders ;

        if( !empty($payment['purchase_order_id']) )
        {
            $PurchaseOrder = ClassRegistry::init('PurchaseOrder', 'Model');
            $purchase_order = $PurchaseOrder->read(null, $payment['purchase_order_id']);

            $purchase_order_placeholders = self::purchaseorders_get_all_place_holders($purchase_order['PurchaseOrder']);
            $array += $purchase_order_placeholders;
        }

        if( !empty($payment['supplier_id']) )
        {
            $Supplier = ClassRegistry::init('Supplier', 'Model');
            $supplier = $Supplier->read(null, $payment['supplier_id']);

            $supplier_placeholders = self::supplier_place_holder($supplier['Supplier']);
            $array += $supplier_placeholders;
        }

        $array += $staff_placeholders ;

        if (ifPluginInstalled(BranchesPlugin)) {
            $array += self::branches_place_holder($payment['branch_id'],'invoice_payment');
        }
        return $array;
    }
	/**
	* Get invoice payment place holder
	* @param array $payment payment details
	* @param boolean $only_payment if true will return payment place holder only
	* @return array of placeholders
	*/
    public static function last_invoice_payment_place_holder($payment,$only_payment=false) {
        
		$InvoicePayment=GetObjectOrLoadModel('InvoicePayment');
		$pamyent_method_name=$InvoicePayment::getPaymentMethods();
		$payment_status_name=$InvoicePayment::getPaymentStatus();
        // warning suppress
        if (!$payment) {
            $payment = [
                'InvoicePayment' => [
                    'id' => null,
                    'payment_method' => null,
                    'amount' => null,
                    'currency_code' => null,
                    'added_by' => null,
                    'date' => null,
                    'email' => null,
                    'status' => null,
                    'notes' => null,
                    'response_code' => null,
                    'response_message' => null,
                    'first_name' => null,
                    'last_name' => null,
                    'address1' => null,
                    'address2' => null,
                    'city' => null,
                    'state' => null,
                    'postal_code' => null,
                    'country_code' => null,
                    'phone1' => null,
                    'phone2' => null,
                    'receipt_notes' => null,
                    'attachment_full_path' => null,
                    'transaction_id' => null,
                ]
            ];
        }
        if(isset($payment['InvoicePayment']))
        {
            $payment = $payment['InvoicePayment'];
        }
        //render the attachement as image and image from pdf
        // warning suppress
        $file_type = pathinfo($payment['attachment_full_path'] ?? null, PATHINFO_EXTENSION);
        $attachment_file = null;
        if(in_array(strtolower($file_type), ['jpg','jpeg','bmp','png','gif'])) //image type render as image tag
        {
            $attachment_file = self::getImageTag($payment['attachment_full_path']);
        }
        else if( strtolower($file_type) == 'pdf' ) //image type render as pdf convert pdf to image and
        {
            $attachment_file = "<a href=\"https://".getCurrentSite('subdomain')."/" . $payment['attachment_full_path'] ."\">Downlaod Attachment</a>";
        }



//        dd($payment);
        $SitePaymentGateway= ClassRegistry::init('SitePaymentGateway' );
        $SitePaymentGatewayRow=$SitePaymentGateway->find(array('SitePaymentGateway.payment_gateway'=>$payment['payment_method']));

        $paymentMethodName = $pamyent_method_name[$payment['payment_method']] ?? null;
        $array = array(
            '{%invoice_payment_No%}' => $payment['id'],
            '{%payment_method_display%}' => empty($SitePaymentGatewayRow['SitePaymentGateway']['label'])?__($paymentMethodName,true):__($SitePaymentGatewayRow['SitePaymentGateway']['label'],true),
            '{%payment_method%}'         => empty($SitePaymentGatewayRow['SitePaymentGateway']['label'])?__($paymentMethodName,true):__($SitePaymentGatewayRow['SitePaymentGateway']['label'],true),
            '{%payment_amount%}' => $payment['amount'],
            '{%payment_currency_code%}' => $payment['currency_code'],
            '{%payment_added_by%}' => $payment['added_by'],
            '{%payment_date%}' => format_date($payment['date']),
            '{%payment_email%}' => $payment['email'],
            '{%payment_status%}' => $payment_status_name[$payment['status']]??null,
            '{%payment_notes%}' => $payment['notes'],
            '{%payment_response_code%}' => $payment['response_code'],
            '{%payment_response_message%}' => $payment['response_message'],
            '{%payment_first_name%}' => $payment['first_name'],
            '{%payment_last_name%}' => $payment['last_name'],
            '{%payment_address1%}' => $payment['address1'],
            '{%payment_address2%}' => $payment['address2'],
            '{%payment_city%}' => $payment['city'],
            '{%payment_state%}' => $payment['state'],
            '{%payment_postal_code%}' => $payment['postal_code'],
            '{%payment_country_code%}' => $payment['country_code'],
            '{%payment_phone1%}' => $payment['phone1'],
            '{%payment_phone2%}' => $payment['phone2'],
            '{%payment_receipt_notes%}' => $payment['receipt_notes'],
            '{%payment_invoice_payment_attachment%}' => $attachment_file,

        );
        $array =$array+ array(
            '{%amount%}' => $payment['amount'],
            '{%currency_code%}' => $payment['currency_code'],
            '{%added_by%}' => $payment['added_by'],
            '{%date%}' => format_date($payment['date']),
            '{%email%}' => $payment['email'],
            '{%status%}' => $payment['status'],
            '{%notes%}' => $payment['notes'],
            '{%response_code%}' => $payment['response_code'],
            '{%response_message%}' => $payment['response_message'],
            '{%first_name%}' => $payment['first_name'],
            '{%last_name%}' => $payment['last_name'],
            '{%address1%}' => $payment['address1'],
            '{%address2%}' => $payment['address2'],
            '{%city%}' => $payment['city'],
            '{%state%}' => $payment['state'],
            '{%postal_code%}' => $payment['postal_code'],
            '{%country_code%}' => $payment['country_code'],
            '{%phone1%}' => $payment['phone1'],
            '{%phone2%}' => $payment['phone2'],
            '{%receipt_notes%}' => $payment['receipt_notes'],
            '{%invoice_payment_attachment%}' => $attachment_file,
            '{%transaction_id%}' => $payment['transaction_id']

        );

		if($only_payment){
		return $array;
		}
        $Site = ClassRegistry::init('Site', 'Model');
        $site = $Site->read(null, getCurrentSite('id'));

        $site_placeholders = self::site_place_holder($site['Site']);
        $array += $site_placeholders ;

        if( !empty($payment['invoice_id']) )
        {
            $Invoice = ClassRegistry::init('Invoice', 'Model');
            $invoice = $Invoice->read(null, $payment['invoice_id']);

            $invoice_placeholders = self::invoice_get_all_place_holders($invoice['Invoice']);
            $array += $invoice_placeholders;
        }

        if( !empty($payment['client_id']) )
        {
            $Client = ClassRegistry::init('Client', 'Model');
            $client = $Client->read(null, $payment['client_id']);

            $client_placeholders = self::client_place_holder($client['Client']);
            $array += $client_placeholders;
        }

        if( !empty($payment['staff_id']) )
        {
            $Staff = ClassRegistry::init('Staff', 'Model');
            $staff = $Staff->read(null, $payment['staff_id']);

            $staff_placeholders = self::staff_place_holder($staff['Staff']['id']);
            $array += $staff_placeholders ;
        }

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $array += self::branches_place_holder($payment['branch_id'], 'payment');
        }

        return $array;


    }

    public static function credit_payment_place_holder_list() {
        return array(
            '{%business_name%}' => __('Business name', true),
            '{%business_email%}' => __('Business email', true),
            '{%address1%}' => __('Business address 1', true),
            '{%address2%}' => __('Business address 2', true),
            '{%city%}' => __('Business city', true),
            '{%state%}' => __('Business state', true),
            '{%postal_code%}' => __('Business postal code', true),
            '{%telephone%}' => __('Business Telephone', true),
            '{%mobile%}' => __('Business Mobile', true),
            '{%client_business-name%}' => __('Client business name', true),
            '{%client_name%}' => __('Client name', true),
            '{%client_first-name%}' => __('Client first name', true),
            '{%client_last-name%}' => __('Client last name', true),
            '{%payment_no%}' => __('Payment No', true),
            '{%transaction_id%}' => __('Transaction ID', true),
            '{%payment_amount%}' => __('Payment amount', true),
            '{%payment_date%}' => __('Payment date', true),
            '{%payment_method%}' => __('Payment method', true),
            '{%payment_message%}' => __('Payment method message', true),
            '{%payment_currency%}' => __('Currency', true),
        ) + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('credit_payment') : []);
    }

    public static function hasShortUrlPlaceholder($layout_id) {
        if(IS_REST || (defined("IS_API") && IS_API)){
            $invoice_layout = GetObjectOrLoadModel('InvoiceLayout')->find('first', ['recursive' => -1, 'conditions' => ['InvoiceLayout.id' => $layout_id]]);
            if ($invoice_layout) {
                $short_link_placeholder = '{%short-link%}';
                return
                    str_contains($invoice_layout['InvoiceLayout']['html'], $short_link_placeholder) ||
                    str_contains($invoice_layout['InvoiceLayout']['sticky_header'], $short_link_placeholder) ||
                    str_contains($invoice_layout['InvoiceLayout']['sticky_footer'], $short_link_placeholder) ||
                    str_contains($invoice_layout['InvoiceLayout']['business_info'], $short_link_placeholder) ||
                    str_contains($invoice_layout['InvoiceLayout']['client_info'], $short_link_placeholder) ||
                    str_contains($invoice_layout['InvoiceLayout']['footer'], $short_link_placeholder);
            }   
        }
        return true;
    }

    public static function invoice_place_holder($invoice) {
        $invoiceTaxes = $invoice['InvoiceTax'] ?? [];
        $client = $invoice['Client'] ?? [];
        if(!empty($invoice['client_id']) && empty($client)){
            $clientModel = GetObjectOrLoadModel('Client');
            $clientData=$clientModel->find('first',['recursive' => -1,'conditions'=>array('Client.id'=>$invoice['client_id'])]);
            $client = $clientData['Client'] ?? [];
        }
        $InvoicePayment = GetObjectOrLoadModel('InvoicePayment');
        $controller = new InvoicesController();
        $site= getCurrentSite();
        if(isset($invoice['Invoice']))
        {
            $invoice = $invoice['Invoice'];
        }
        $InvoiceTax = GetObjectOrLoadModel('InvoiceTax');

        $tax_total=0;
        $taxes=$InvoiceTax->find('all',array('conditions'=>array('InvoiceTax.invoice_id'=>$invoice['id']),'fields'=>'InvoiceTax.name,InvoiceTax.value,InvoiceTax.invoice_value'));


        $FollowUpStatus = GetObjectOrLoadModel('FollowUpStatus');
        $Post = GetObjectOrLoadModel('Post');
        $FollowUpStatusList = $FollowUpStatus->getList(Post::INVOICE_TYPE);
        $InvoiceItem = GetObjectOrLoadModel('InvoiceItem');
        $invoice_payment_name=Invoice::getPaymentStatuses();

            /** @var Client $clientModel */
        $clientModel = GetObjectOrLoadModel('Client');
        if ($client == []) {
            $Client = GetObjectOrLoadModel('Client');
            $Client = $Client->find(array('Client.id'=>$invoice['client_id']));
        } else {
            $Client['Client'] = $client['Client'] ?? $client;
        }

        // This behavior is made because the customer wants the invoice submition to be faster and doesn't care about the client balance calculations
        if (settings::getValue(0, 'disable_client_unpaid_balance_in_extra_details')) {
            $extraDetails = json_decode($invoice['extra_details'], true);
            $extraDetails_client_balance = $extraDetails['client_balance'] ?? 0;
            $extraDetails_client_balance = $extraDetails_client_balance + $invoice['summary_unpaid'];
        } else {
//            $clientUnpaid = $clientModel->getUnpaid($invoice['client_id'], $invoice['currency_code'])[strtoupper($invoice['currency_code'])];
        }

        $view = new View($controller, false);
        $InvoicePayment->alias = 'InvoicePayment';
        $payment_list=$InvoicePayment->find('all',['fields'=>['InvoicePayment.amount,InvoicePayment.payment_method,InvoicePayment.currency_code'],'conditions'=>['invoice_id'=>$invoice['id']]]);
        foreach ($payment_list as $paymentKey => $payment) {
            $payment_list[$paymentKey]['InvoicePayment']['amount'] = format_price($payment['InvoicePayment']['amount'], $payment['InvoicePayment']['currency_code']);
        }
        $payment_list_element=$view->element('invoices/payment_list',['payments'=>$payment_list,'pm'=>$InvoicePayment::getAllPaymentMethods()]);
        $InvoiceModel = GetObjectOrLoadModel('Invoice');
        $hash = $InvoiceModel->getHash($invoice);
        $ViewList[Invoice::Invoice]="view";
        $ViewList[Invoice::Estimate]="view_estimate";
        $ViewList[Invoice::Credit_Note]="view_creditnote";
        $ViewList[Invoice::Refund_Receipt]="view_refund";
        $client_view_link = 'https://' . $site['subdomain'] .'/invoices/'.(isset($ViewList[$invoice['type']])?$ViewList[$invoice['type']]:'view').'/' . $invoice['id'] . '?hash=' . $hash;

        $sa_qr_code_image='';
        $sa_qr_code_link='';

        $invoiceType =  match((int)$invoice['type']){
            InvoiceSourceTypesUtil::Invoice => __('Invoice', true),
            InvoiceSourceTypesUtil::Estimate => __('Estimate', true),
            InvoiceSourceTypesUtil::SALES_ORDER => __('Sales Order', true),
            InvoiceSourceTypesUtil::Credit_Note => __('Credit Note', true),
            InvoiceSourceTypesUtil::Refund_Receipt => __('Refund Receipt', true),
            default => __('Invoice',true)
        };

        $sa_auto_invoice_title= $invoiceType;
        
        if(in_array($invoice['type'],[Invoice::Invoice,Invoice::DEBIT_NOTE,Invoice::Credit_Note,Invoice::Refund_Receipt,Invoice::ADVANCE_PAYMENT]))
        {
            if(ifPluginActive(EINVOICE_SA_PLUGIN))
            {
                $inv['Invoice']=$invoice;
                foreach ($taxes as $tax) {
                    $inv['InvoiceTax'][]=$tax['InvoiceTax'];
                }

                $data=Invoice::get_kas_invoice_qr_data($inv);
                if(empty($invoice['id']))
                    $qr_code_link='https://' . $site['subdomain'] .'/qr/dummy.jpg' ;
                    else
                $qr_code_link='https://' . $site['subdomain'] .'/qr/?d64=' .$data;
                $qr_code_image = '<img src="'.$qr_code_link.'" /><br/>';

                $sa_qr_code_link=$qr_code_link;
                $sa_qr_code_image=$qr_code_image ;
                $invoiceTitles = [
                    Invoice::Invoice => [
                        'simplified'      => __('Simplified Tax Invoice', true),
                        'standard'        => __('Tax Invoice', true)
                    ],
                    Invoice::Refund_Receipt => [
                        'simplified'      => __('Tax Refund Receipt Simplified', true),
                        'standard'        => __('Tax Refund Receipt', true)
                    ]

                ];
                $sa_auto_invoice_title= isset($invoiceTitles[$invoice['type']]) ? $invoiceTitles[$invoice['type']]['simplified'] : $invoiceTitles[Invoice::Invoice]['simplified'];
                if($Client['Client']['type']!='2')
                {
                  $sa_auto_invoice_title= isset($invoiceTitles[$invoice['type']]) ? $invoiceTitles[$invoice['type']]['standard'] : $invoiceTitles[Invoice::Invoice]['standard'];
                }
  
            } else if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::JORDAN_EINVOICE_PLUGIN)) {

                $inv['Invoice']=$invoice;
                foreach ($taxes as $tax) {
                    $inv['InvoiceTax'][]=$tax['InvoiceTax'];
                }

                $appEntityData = GetObjectOrLoadModel('EntityAppData');
                $inv["EntityAppData"][] = $appEntityData->find('all',
                    [
                        'conditions' =>
                        [
                            "EntityAppData.app_key" => \Izam\Daftra\Common\Utils\EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE,
                             "EntityAppData.action_key" => \Izam\Daftra\Common\Utils\ElectronicInvoicesActionsUtil::GET_DOCUMENT,
                            "EntityAppData.entity_id" => $invoice["id"]
                        ]
                    ]
                )[0]["EntityAppData"] ?? [];

                $imagePath = Invoice::get_jordan_invoice_qr_data($inv);

                if (empty($imagePath)){
                    $sa_qr_code_link='';
                    $sa_qr_code_image='' ;
                } else {
                    $qr_code_link= 'data:image/png;base64,'.$imagePath;
                    $qr_code_image = '<img src="data:image/png;base64,'.$imagePath.'" /><br/>';

                    $sa_qr_code_link=$qr_code_link;
                    $sa_qr_code_image=$qr_code_image ;
                }

                $sa_auto_invoice_title=__('Tax Invoice',true);
                //print_pre_die($Client['Client']);
            } else {
                $data=base64_encode($client_view_link);
                $qr_code_link='https://' . $site['subdomain'] .'/qr/?d64=' .$data;

                $qr_code_image = '<img class="qr_code_image" src="'.$qr_code_link.'" /><br class="qr_code_br"/>';
            }



        }
        else
        {
            $qr_code_link='';
            $qr_code_image = '';
        }




        $shipping_name = $invoice['client_secondary_name'];
        $shipping_address = $invoice['client_secondary_address1'] . (empty($invoice['client_secondary_address2']) ? '' : '<br/>' . $invoice['client_secondary_address2']);
        $shipping_city = $invoice['client_secondary_city'];
        $shipping_state = $invoice['client_secondary_state'];
        $shipping_postal_code = $invoice['client_secondary_postal_code'];
        $shipping_country_code = $invoice['client_secondary_country_code'];
        $shippingCountry =  get_country_code($invoice['client_secondary_country_code']);

		$storeModel = GetObjectOrLoadModel('Store');
		$store=$storeModel->find('first',['conditions'=>['Store.id'=>$invoice['store_id']]]);

        if ($invoice['shipping_options'] == 2 || (empty($invoice['shipping_options']) && !empty($layout['InvoiceLayout']['show_ship']) && (empty($invoice['client_secondary_name']) && empty($invoice['client_secondary_address1'])))) {

            $shipping_name = $invoice['client_business_name'];
            $shipping_address = $invoice['client_address1'] . (empty($invoice['client_address2']) ? '' : '<br/>' . $invoice['client_address2']);
            $shipping_city = $invoice['client_city'];
            $shipping_state = $invoice['client_state'];
            $shipping_postal_code = $invoice['client_postal_code'];
            $shipping_country_code = $invoice['client_country_code'];
            $shippingCountry =  get_country_code($shipping_country_code);
        } else if (
                (empty($invoice['shipping_options']) && !empty($layout['InvoiceLayout']['show_ship']) && (!empty($invoice['client_secondary_name']) || !empty($invoice['client_secondary_address1']))) || $invoice['shipping_options'] == 3) {

        }

        $invoice_total_spelled = self::tafketWithChange($invoice['summary_total'], $invoice['currency_code']);
        $deposit_spelled = self::tafketWithChange($invoice['summary_deposit'], $invoice['currency_code']);
        $paid_spelled = self::tafketWithChange($invoice['summary_paid'], $invoice['currency_code']);
        $unpaid_spelled = self::tafketWithChange($invoice['summary_unpaid'], $invoice['currency_code']);
        $discount_spelled = self::tafketWithChange($invoice['summary_discount'], $invoice['currency_code']);

        $extraDetails = json_decode($invoice['extra_details'], true);
        $extraDetails_client_balance = $extraDetails['client_balance'] ?? 0;

        if ($invoice['order_source_id']) {
            $OrderSource = GetObjectOrLoadModel('OrderSource');
            $OrderSource = $OrderSource->find(array('OrderSource.id' => $invoice['order_source_id']));
        }
        $barcode_image_link = "https://". getCurrentSite("subdomain") . "/printable_templates/get_barcode_image/". INVOICE_BARCODE . "/". $invoice['id'];
        $ItemStaff = GetObjectOrLoadModel('ItemStaff');
        if(!empty($invoice['sales_person_id']) && empty($invoice['id'])) {
            $Staff = GetObjectOrLoadModel('Staff');
            $salesPerson = $Staff->findById($invoice['sales_person_id']);
            $salesPersonNames = !empty($salesPerson['Staff']['full_name']) ? $salesPerson['Staff']['full_name'] : $salesPerson['Staff']['name'] . ' ' . $salesPerson['Staff']['last_name'];
        } else {
            if($invoice['id']!=""){
            $salesPersons = $ItemStaff->getAssignedStaff(ItemStaff::SALES_ITEM_TYPE, $invoice['id']);
            $salesPersonNames = implode(',',array_map(function ($item){return !empty($item['Staff']['full_name'])? $item['Staff']['full_name'] : $item['Staff']['name'];},$salesPersons));
            }else{
                $salesPersonNames="";
            }
        }
        $totalAmount = $InvoicePayment->query("SELECT SUM(amount) as sum_credit FROM `invoice_payments` WHERE invoice_id = {$invoice['id']} AND `payment_method` = 'client_credit';",false);
        $sum_credit= (isset($totalAmount[0][0]['sum_credit'])) ?(float) $totalAmount[0][0]['sum_credit']:0;
        $invoice_item_qty= $InvoiceItem->find('all',array('fields'=>'sum(`InvoiceItem`.`quantity`) as qty','conditions'=>array('invoice_id'=>$invoice['id']))) ;
        $ShortUrlGenerator = new \Izam\ShortUrl\ShortUrl(getPDO());

        $placeholders = array(
            '{%client_business_name%}' => $invoice['client_business_name'],
            '{%client_last_name%}' => $invoice['client_last_name'] ? $invoice['client_last_name'] : $Client['Client']['last_name'],
            '{%client_first_name%}' => $invoice['client_first_name'] ? $invoice['client_first_name'] : $Client['Client']['first_name'],
            '{%client_name%}' => $client_name = (empty($invoice['client_first_name']) && empty($invoice['client_last_name']) ? $invoice['client_business_name'] : $invoice['client_first_name'] . ' ' . $invoice['client_last_name']),
            '{%client_address%}' => $invoice['client_address1'] . ife($invoice['client_address2'], '<br />' . $invoice['client_address2']),
            '{%client_city%}' => $invoice['client_city'],
            '{%client_state%}' => $invoice['client_state'],
            '{%client_postal_code%}' => $invoice['client_postal_code'],
            '{%client_postcode%}' => $invoice['client_postal_code'],
            '{%full_client_address%}' => $view->element('format_address_html', array('address1' => $invoice['client_address1'], 'address2' => $invoice['client_address2'], 'city' => $invoice['client_city'], 'state' => $invoice['client_state'], 'postal_code' => $invoice['client_postal_code'], 'is_inline' => true)),
            '{%client_full_address%}' => $view->element('format_address_html', array('address1' => $invoice['client_address1'], 'address2' => $invoice['client_address2'], 'city' => $invoice['client_city'], 'state' => $invoice['client_state'], 'postal_code' => $invoice['client_postal_code'], 'is_inline' => true)),
            '{%client_secondary_name%}' => $shipping_name,
            '{%client_secondary_address%}' => $shipping_address,
            '{%client_secondary_city%}' => $shipping_city,
            '{%client_secondary_state%}' => $shipping_state,
            '{%client_secondary_country%}' => $shippingCountry,
            '{%client_secondary_postcode%}' => $shipping_postal_code,
            '{%client_secondary_postal_code%}' => $shipping_postal_code,
            '{%client_country%}' => get_country_code($invoice['client_country_code']),
            '{%invoice_no%}' => $invoice['no'],
            '{%invoice_payment_status%}' => $invoice_payment_name[$invoice['payment_status']],
            '{%invoice_shipping_amount%}' => $invoice['shipping_amount'],
            '{%invoice_adjustment_value%}' => $invoice['adjustment_value'],
            '{%manual_status%}' => $FollowUpStatusList[$invoice['follow_up_status']] ?? null,
            '{%invoice_date%}' => $invoice['date'],  // no need for formatting  caz it formatted before
            '{%due_date%}' => ($invoice['due_date']),
            '{%issue_date%}' => ($invoice['issue_date']),
            '{%subscription_name%}' => $invoice['name'],
            '{%subscription_id%}' => $invoice['id'],
            '{%invoice_subtotal%}' => format_price($invoice['summary_subtotal'], $invoice['currency_code']),
            '{%invoice_order_no%}' => $invoice['order_number'],
            '{%invoice_total%}' => format_price($invoice['summary_total'], $invoice['currency_code']),
            '{%deposit%}' => format_price($invoice['summary_deposit'], $invoice['currency_code']),
            '{%paid%}' => format_price($invoice['summary_paid'], $invoice['currency_code']),
            '{%unpaid%}' => format_price($invoice['summary_unpaid'], $invoice['currency_code']),
//            '{%invoice_advanced_client_balance%}' => format_price($invoice['summary_unpaid'], $invoice['currency_code']),
            '{%invoice_payment_table%}'=>$payment_list_element,
            '{%discount%}' => format_price($invoice['summary_discount'], $invoice['currency_code']),
            '{%invoice_total_spelled%}' => ucfirst(trim($invoice_total_spelled)),
            '{%deposit_spelled%}' => $deposit_spelled  ,
            '{%paid_spelled%}' => $paid_spelled ,
            '{%unpaid_spelled%}' => $unpaid_spelled ,
            '{%invoice_item_count%}' => $InvoiceItem->find('count',array('conditions'=>array('invoice_id'=>$invoice['id']))) ,
            '{%invoice_item_qty%}' =>  round($invoice_item_qty[0][0]['qty'],4),
            '{%discount_spelled%}' => $discount_spelled ,
            '{%invoice_barcode%}' =>  "<img src='{$barcode_image_link}'>",//OIBarcode::getBarcodeImage(OIBarcode::getAutoBarcode(INVOICE_BARCODE, $invoice['id']), [ 'style'=> ['width'=>'20%','height'=>'30px'] ] ),
            '{%invoice_barcode_number%}' => OIBarcode::getAutoBarcode(INVOICE_BARCODE, $invoice['id']),
			'{%invoice_currency_code%}' => $invoice['currency_code'],
            '{%created_date%}' => format_date($invoice['created']),
            '{%created_time%}' => date("H:i:s",($invoice['created']) ? strtotime($invoice['created']) : time()),
            '{%client_view_link%}' => $client_view_link,
            '{%view_link%}' => $client_view_link,
            '{%owner_view_link%}' => $owner_view_link ?? null,
			'{%currency_code%}' => $invoice['currency_code'],
			'{%currencies%}' => json_encode(include APP . 'config' . DS . 'currencies.php'),
			'{%number_formats%}' => json_encode($number_formats),
			'{%ware_house%}' => $store['Store']['name'],
			'{%invoice_notes%}' => $invoice['html_notes'],
            '{%qr_code_link%}' => $qr_code_link,
            '{%qr_code_image%}' => $qr_code_image,
            '{%sa_qr_code_link%}' => $sa_qr_code_link,
            '{%sa_qr_code_image%}' => $sa_qr_code_image,
            '{%sa_auto_invoice_title%}'=>$sa_auto_invoice_title,
            '{%sales_person%}' => $salesPersonNames,
            '{%client_number%}' => $Client['Client']['client_number'],
            '{%invoice_advanced_client_balance%}' => format_price(in_array($invoice['type'], [Invoice::Refund_Receipt, Invoice::Credit_Note]) ? (float) $extraDetails_client_balance - $invoice['summary_unpaid'] : ( (float) $extraDetails_client_balance +( $invoice['summary_unpaid']+$sum_credit)), $invoice['currency_code']),
            '{%client_bn1%}' => !empty($Client['Client']['bn1']) ? $Client['Client']['bn1'] : '',
            '{%client_bn2%}' => !empty($Client['Client']['bn2']) ? $Client['Client']['bn2'] : '',
            '{%refund_invoice_number_condition%}' => 'style="display: none"',
            '{%refund_invoice_no_title%}' => '',
            '{%refund_invoice_number%}' => '',
            '{%order_source%}' => isset($OrderSource)? $OrderSource['OrderSource']['name'] : '',
            '{%invoice_type%}' => $invoiceType,
            '{%payment_link%}' => ' http://' . $site['subdomain'] . '/client/invoices/pay/' . $invoice['id'] . '?hash=' . $hash ,

        );

        $placeholders += self::client_place_holder($client);
        if (self::hasShortUrlPlaceholder($invoice['invoice_layout_id'])) {
            $placeholders['{%short_link%}'] = $ShortUrlGenerator->generateShortUrl($client_view_link);
        }

        if($invoice['type'] == Invoice::Refund_Receipt || $invoice['type'] == Invoice::Credit_Note) {
            $orgInvoice = $InvoiceModel->find('first', ['conditions' => ['Invoice.id' => $invoice['subscription_id']]]);
            if($orgInvoice) {
                $placeholders['{%refund_invoice_number_condition%}'] = "";
                $placeholders['{%refund_invoice_number%}'] = "{$orgInvoice['Invoice']['no']}";
                $placeholders['{%refund_invoice_no_title%}'] = "";
            }
        }
        
        if(!$invoice['draft']) {
            $placeholders += [
                '{%qr_code_link%}' => $qr_code_link,
                '{%qr_code_image%}' => $qr_code_image,
                '{%sa_qr_code_link%}' => $sa_qr_code_link,
                '{%sa_qr_code_image%}' => $sa_qr_code_image,
            ];
        }
  
        if(!empty($invoice['work_order_id'] ) && ifPluginActive(WorkOrderPlugin)){
		$WorkOrder=GetObjectOrLoadModel('WorkOrder');
		$work_order = $WorkOrder->findById (  $invoice['work_order_id'] ) ;
        if (!isset($work_order['WorkOrder']['client_id'])) $work_order['WorkOrder']['client_id'] = $invoice['client_id'];
		$placeholders +=self::work_order_place_holder($work_order);
		}
         //Invoice Custom Field as placeholder

        foreach ($placeholders as $key => $holder) {
            $placeholders[str_replace('_', '-', $key)] = $holder;
        }

        if ($invoiceTaxes == []) {
            /** @todo refactor this to get only taxes instead of get full object */
            $RealTaxes = GetObjectOrLoadModel('Invoice')->getInvoice($invoice['id']);
        } else {
            $RealTaxes['InvoiceTax'] = $invoiceTaxes;
        }

        foreach ($RealTaxes['InvoiceTax'] as $value) {
            $key = $value['tax_id']; 
            $placeholders["{%tax{$key}_name%}"] = $value['name'].' ('.$value['value'].'%)';
            $placeholders["{%tax{$key}_value%}"] = $value['invoice_value'];
            $tax_total=$tax_total+$value['invoice_value'];
        }

        $placeholders['{%tax-total%}']=format_price($tax_total, $invoice['currency_code']);
        $placeholders['{%item-discount-total%}']=format_price($invoice['item_discount_amount'], $invoice['currency_code']);
        //TOdo Add Placeholder name in invoice placeholder list function
        $placeholders['{%invoice-total-with-discount%}']=format_price($invoice['summary_subtotal']+$invoice['item_discount_amount'],$invoice['currency_code']);

        ///
        $extra_details=json_decode($invoice['extra_details'],true);

        if (isset($extra_details))
        foreach ($extra_details as $key => $value) {

            $placeholders["{%extra_details.{$key}%}"] = $value;
        }

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholders += self::branches_place_holder($invoice['branch_id'],'invoice');
        }

        $placeholders  +=self::get_custom_place_holders('invoices',$invoice['id']);

        // check if membership plugin installed
        if (ifPluginActive(MEMBERSHIP_PLUGIN)) {
            $membershipModel = GetObjectOrLoadModel('Membership');
            $membershipModel->bindModel(['hasOne' => ['MembershipRenewals' => ['className' => 'MembershipRenewals', 'recursive' => 1, 'foreignKey' => 'membership_id', 'conditions' => ['MembershipRenewals.invoice_id' => $invoice['id']]]]]);
            $membershipData = $membershipModel->find('first', array(
                'recursive' => 1,
                'conditions' => [
                    'Membership.client_id' => $invoice['client_id'],
                    'deleted_at' => NULL
                ]
            ));
            if (!empty($membershipData['MembershipRenewals'])) {
                $packageModel = GetObjectOrLoadModel('Package');
                $packageData = $packageModel->findById($membershipData['Membership']['package_id']);
                $membership = $membershipData['MembershipRenewals'];
                $package = $packageData['Package'];
                $placeholders += [
                    "{%client_membership_id%}" => $membership['id'],
                    "{%client_membership_expiry_date%}" => format_date($membership['end_date']),
                    "{%client_membership_join_date%}" => format_date($membership['start_date']),
                    "{%client_membership_current_package%}" => $package['name'],
                ];
            }
        }

        $placeholders += self::get_custom_place_holders('clients', $Client['Client']['id']);
        if (isset($Client['Client']['id'])) {
            $placeholders += self::get_additional_fields_place_holders_values(EntityKeyTypesUtil::CLIENT_ENTITY_KEY, $Client['Client']['id']);
        }
        return $placeholders;
    }

    public static function invoice_custom_field_place_holder($invoice)
    {
        $placeholders = array();
        //print_pre($invoice);
        //die();
        //$placeholders=[];
        //$InvoiceCustomField = ClassRegistry::init('InvoiceCustomField', 'Model');
        //$InvoiceCustomFieldLists=$InvoiceCustomField->find('list',array('fields'=>'label,value','conditions'=>array('InvoiceCustomField.invoice_id'=>$invoice['id'])));
        if (isset($invoice['InvoiceCustomField'])) {
            $InvoiceCustomFieldLists = $invoice['InvoiceCustomField'];
            foreach ($InvoiceCustomFieldLists as $value) {

                if(!preg_match('/[^0-9أ-يa-zA-Z ]/',$value['label']) || getCurrentSite('id')=="2090315")
                {
                    $placeholders["{%{$value['label']}%}"]=$value['real-value'];
                }

            }
        }
        return $placeholders;
    }


    public static function estimate_place_holder_list() {
        return array(
            '{%business_name%}' => __('Business name', true),
            '{%business_email%}' => __('Business email', true),
            '{%address1%}' => __('Business address 1', true),
            '{%address2%}' => __('Business address 2', true),
            '{%city%}' => __('Business city', true),
            '{%state%}' => __('Business state', true),
            '{%postal_code%}' => __('Business postal code', true),
            '{%telephone%}' => __('Business telephone', true),
            '{%mobile%}' => __('Business mobile', true),
            '{%client_business_name%}' => __('Client business name', true),
            '{%client_name%}' => __('Client name', true),
            '{%client_address%}' => __('Client address', true),
            '{%client_first_name%}' => __('Client first name', true),
            '{%client_last_name%}' => __('Client last name', true),
            '{%client_city%}' => __('Client city', true),
            '{%client_state%}' => __('Client state', true),
            '{%client_postal_code%}' => __('Client postal code', true),
            '{%client_country%}' => __('Client country', true),
            '{%estimate_no%}' => __('Estimate No', true),
            '{%estimate_total%}' => __('Estimate total', true),
            '{%discount%}' => __('Discount', true),
            '{%tax-total%}' => __('Tax total', true),
            '{%view_link%}' => __('View link', true),
            '{%short-link%}' => __('Short link', true),
            '{%accept_link%}' => __('Accept link', true),
            '{%accept_url%}' => __('Accept Url', true),
        ) + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('estimate') : []);
    }

    public static function estimate_place_holder($estimate) {
        $hash = ClassRegistry::init('Invoice', 'Model')->getHash($estimate);
        $viewLink = Router::url('/invoices/view_estimate/' . $estimate['id'] . '?hash=' . $hash, true);
        $acceptLink = Router::url('/invoices/accept/' . $estimate['id'] . '?hash=' . $hash, true);
        $controller = new InvoicesController();

        $view = new View($controller, false);
        $ShortUrlGenerator = new \Izam\ShortUrl\ShortUrl(getPDO());
        $placeholders = [
            '{%client_business_name%}' => $estimate['client_business_name'],
            '{%client_name%}' => $client_name = (empty($estimate['client_first_name']) && empty($estimate['client_last_name']) ? $estimate['client_business_name'] : $estimate['client_first_name'] . ' ' . $estimate['client_last_name']),
            '{%client_address%}' => $estimate['client_address1'] . ife($estimate['address2'], '<br />' . $estimate['address2']),
            '{%client_last_name%}' => $estimate['client_last_name'],
            '{%client_first_name%}' => $estimate['client_first_name'],
            '{%client_city%}' => $estimate['client_city'],
            '{%client_state%}' => $estimate['client_state'],
            '{%client_postal_code%}' => $estimate['client_postal_code'],
            '{%client_postcode%}' => $estimate['client_postal_code'],
            '{%full_client_address%}' => $view->element('format_address_html', ['address1' => $estimate['client_address1'], 'address2' => $estimate['client_address2'], 'city' => $estimate['client_city'], 'state' => $estimate['client_state'], 'postal_code' => $estimate['client_postal_code'], 'is_inline' => true]),
            '{%client_country%}' => get_country_code($estimate['client_country_code']),
            '{%estimate_no%}' => $estimate['no'],
            '{%estimate_total%}' => format_price($estimate['summary_total'], $estimate['currency_code']),
            '{%deposit%}' => format_price($estimate['summary_deposit'], $estimate['currency_code']),
            '{%discount%}' => format_price($estimate['summary_discount'], $estimate['currency_code']),
            '{%view_link%}' => '<a href="' . $viewLink . '">' . $viewLink . '</a>',
            '{%view_url%}' => $viewLink,
            '{%accept_link%}' => '<a href="' . $acceptLink . '">' . $acceptLink . '</a>',
            '{%accept_url%}' => $acceptLink,
        ];

        if ((isset($estimate['type']) && $estimate['type'] == 3 /*estimate type*/) && self::hasShortUrlPlaceholder($estimate['invoice_layout_id'])) {
            $placeholders['{%short_link%}'] = '<a href="' . $ShortUrlGenerator->generateShortUrl($viewLink) . '">' . $ShortUrlGenerator->generateShortUrl($viewLink) . '</a>';
        }

        foreach ($placeholders as $key => $holder) {
            $placeholders[str_replace('_', '-', $key)] = $holder;
        }

        ///
        $extra_details=json_decode($estimate['extra_details'],true);

        foreach ($extra_details as $key => $value) {

            $placeholders["{%extra_details.{$key}%}"] = $value;
        }

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholders += self::branches_place_holder($estimate['branch_id'],'estimate');
        }

        return $placeholders;
    }
    public static function sales_order_place_holder($sales_order) {
        $hash = ClassRegistry::init('Invoice', 'Model')->getHash($sales_order);
        $Country = ClassRegistry::init('Country', 'Model');
        $viewLink = Router::url('/invoices/view_sales_order/' . $sales_order['id'] . '?hash=' . $hash, true);
        $acceptLink = Router::url('/invoices/accept/' . $sales_order['id'] . '?hash=' . $hash, true);
        $controller = new InvoicesController();

        $view = new View($controller, false);
        $ShortUrlGenerator = new \Izam\ShortUrl\ShortUrl(getPDO());
        $placeholders = [
            '{%client_business_name%}' => $sales_order['client_business_name'],
            '{%client_name%}' => $client_name = (empty($sales_order['client_first_name']) && empty($sales_order['client_last_name']) ? $sales_order['client_business_name'] : $sales_order['client_first_name'] . ' ' . $sales_order['client_last_name']),
            '{%client_address%}' => $sales_order['client_address1'] . ife($sales_order['address2'], '<br />' . $sales_order['address2']),
            '{%client_last_name%}' => $sales_order['client_last_name'],
            '{%client_first_name%}' => $sales_order['client_first_name'],
            '{%client_city%}' => $sales_order['client_city'],
            '{%client_state%}' => $sales_order['client_state'],
            '{%client_postal_code%}' => $sales_order['client_postal_code'],
            '{%client_postcode%}' => $sales_order['client_postal_code'],
            '{%full_client_address%}' => $view->element('format_address_html', ['address1' => $sales_order['client_address1'], 'address2' => $sales_order['client_address2'], 'city' => $sales_order['client_city'], 'state' => $sales_order['client_state'], 'postal_code' => $sales_order['client_postal_code'], 'is_inline' => true]),
            '{%client_country%}' => $Country->get_country_code($sales_order['client_country_code']),
            '{%sales_order_no%}' => $sales_order['no'],
            '{%sales_order_total%}' => format_price($sales_order['summary_total'], $sales_order['currency_code']),
            '{%deposit%}' => format_price($sales_order['summary_deposit'], $sales_order['currency_code']),
            '{%discount%}' => format_price($sales_order['summary_discount'], $sales_order['currency_code']),
            '{%view_link%}' => '<a href="' . $viewLink . '">' . $viewLink . '</a>',
            '{%view_url%}' => $viewLink,
            '{%accept_link%}' => '<a href="' . $acceptLink . '">' . $acceptLink . '</a>',
            '{%accept_url%}' => $acceptLink,
        ];

        if (self::hasShortUrlPlaceholder($sales_order['invoice_layout_id'])) {
            $placeholders['{%short_link%}'] = '<a href="' . $ShortUrlGenerator->generateShortUrl($viewLink) . '">' . $ShortUrlGenerator->generateShortUrl($viewLink) . '</a>';
        }

        foreach ($placeholders as $key => $holder) {
            $placeholders[str_replace('_', '-', $key)] = $holder;
        }

        ///
        $extra_details=json_decode($sales_order['extra_details'],true);

        foreach ($extra_details as $key => $value) {

            $placeholders["{%extra_details.{$key}%}"] = $value;
        }

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholders += self::branches_place_holder($sales_order['branch_id'],'sales_order');
        }

        return $placeholders;
    }
    public static function sales_order_place_holder_list()
    {
        return array(
                '{%business_name%}' => __('Business name', true),
                '{%business_email%}' => __('Business email', true),
                '{%address1%}' => __('Business address 1', true),
                '{%address2%}' => __('Business address 2', true),
                '{%city%}' => __('Business city', true),
                '{%state%}' => __('Business state', true),
                '{%postal_code%}' => __('Business postal code', true),
                '{%telephone%}' => __('Business telephone', true),
                '{%mobile%}' => __('Business mobile', true),
                '{%client_business_name%}' => __('Client business name', true),
                '{%client_name%}' => __('Client name', true),
                '{%client_address%}' => __('Client address', true),
                '{%client_first_name%}' => __('Client first name', true),
                '{%client_last_name%}' => __('Client last name', true),
                '{%client_city%}' => __('Client city', true),
                '{%client_state%}' => __('Client state', true),
                '{%client_postal_code%}' => __('Client postal code', true),
                '{%client_country%}' => __('Client country', true),
                '{%sales_order_no%}' => __('Sales Order No', true),
                '{%sales_order_total%}' => __('Sales Order total', true),
                '{%discount%}' => __('Discount', true),
                '{%tax-total%}' => __('Tax total', true),
                '{%view_link%}' => __('View link', true),
                '{%short-link%}' => __('Short link', true),
                '{%accept_link%}' => __('Accept link', true),
                '{%accept_url%}' => __('Accept Url', true),
            ) + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('sales_order') : []);
    }

    public static function purchaseorders_place_holder_list($short=false) {
        $array = array(
            '{%business_name%}' => __('Business name', true),
            '{%business_email%}' => __('Business email', true),
            '{%address1%}' => __('Business address 1', true),
            '{%address2%}' => __('Business address 2', true),
            '{%city%}' => __('Business city', true),
            '{%state%}' => __('Business state', true),
            '{%postal_code%}' => __('Postal code', true),
            '{%telephone%}' => __('Business telephone', true),
            '{%mobile%}' => __('Business Mobile', true),
            '{%supplier_business_name%}' => __('Supplier business name', true),
            '{%supplier_name%}' => __('Supplier Name', true),
            '{%supplier_address%}' => __('Supplier address', true),
            '{%supplier_city%}' => __('Supplier city', true),
            '{%supplier_state%}' => __('Supplier State', true),
            '{%supplier_postal_code%}' => __('Supplier postal code', true),
            '{%supplier_country%}' => __('Supplier country', true),
            '{%purchaseorders_no%}' => __('PurchaseOrder No', true),
            '{%purchaseorders_date%}' => __('PurchaseOrder date', true),
            '{%purchaseorders_total%}' => __('PurchaseOrder total', true),
            '{%po_note%}' => __('PurchaseOrder note', true),
            '{%ware_house%}' => __('Warehouse', true),
            '{%po_item_qty%}' => __('Purchase Invoice quantity count', true),
            '{%discount%}' => __('Discount', true),
            '{%item-discount-total%}' => __('Item discount Total', true),
            '{%purchaseorder_tax_total%}' => __('Purchase Invoice Tax total', true),
            '{%purchaseorder_subtotal%}' => __('Purchase Invoice subtotal', true),
            '{%tax-total%}' => __('Tax Total', true),
            '{%extra_details.supplier_balance%}' => __('Supplier Balance',true),
            '{%purchase_order_advanced_supplier_balance%}' => __('Advanced Supplier balance',true),
            '{%purchase_order_no%}' => __('Purchase Invoice No',true),
        );

            $array += [
                '{%purchaseorder_total_spelled%}' => __('Purchase Invoice total Spelled', true),
                '{%purchaseorder_total_tax_spelled%}' => __('Purchase Invoice total tax Spelled', true),
            ];

        $array += self::get_additional_fields_place_holders_list(EntityKeyTypesUtil::PURCHASE_INVOICE);

		if($short==false){
        $array = array_merge($array, self::supplier_place_holder_list() );
        $array = array_merge($array, self::staff_place_holder_list() );
		}
        return $array + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('purchase_order') : []);
    }
    public static function supplier_place_holder_list() {
      $placeholder = array(
            "{%supplier_business_name%}" => __('Supplier Business Name', true),
            "{%supplier_number%}" => __('Supplier Number', true),
            "{%supplier_first_name%}" => __("Supplier first name", true),
            "{%supplier_last_name%}" => __("Supplier last name", true),
            "{%supplier_phone%}" => __("Supplier Phone", true),
            "{%supplier_mobile%}" => __("Supplier Mobile", true),
            "{%supplier_email%}" => __("Supplier Email", true),
            "{%supplier_full_address%}" => __('Supplier Full Address', true),
            "{%supplier_address%}" => __("Supplier Street Address", true),
            "{%supplier_postcode%}" => __("Postcode", true),
            "{%supplier_city%}" => __("City", true),
            "{%supplier_state%}" => __("State", true),
            "{%supplier_country%}" => __("Country", true),
            "{%supplier_secondary_name%}" => __("Supplier Secondary Name", true),
            "{%supplier_secondary_address%}" => __("Secondary Street Address", true),
            "{%supplier_secondary_postcode%}" => __("Supplier Secondary Postcode", true),
            "{%supplier_secondary_city%}" => __("Supplier Secondary City", true),
            "{%supplier_secondary_state%}" => __("Supplier Secondary State", true),
            "{%supplier_secondary_country%}" => __("Supplier Secondary Country", true),
			"{%supplier_bn1%}" => __("Supplier Business Number 1", true),
			"{%supplier_bn2%}" => __("Supplier Business Number 2", true),
			'{%supplier_business_registration_info1%}' => __("Supplier Business Info 1", true),
			'{%supplier_business_registration_info2%}' =>  __("Supplier Business Info 2", true),
        );
			App::import('Vendor', 'sites_local');
              $csite=  getCurrentSite('country_code');

              $Localize = BusinessNumberCommonFieldsUtil::getBusinessNumberFields();

			  if(count($Localize[$csite] ?? [])>0){
              if(count($Localize[$csite] ?? [])==1){
              $placeholder['{%supplier_bn1%}']=__($Localize[$csite]['bn1']['label'],true);
              }else{
              $placeholder['{%supplier_bn1%}']=__($Localize[$csite]['bn1']['label'],true);
              $placeholder['{%supplier_bn2%}']=__($Localize[$csite]['bn2']['label'],true);
              }
			  }

        $placeholder += self::get_custom_place_holders_list('suppliers');
        $placeholder += self::get_additional_fields_place_holders_list(EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY);

        return $placeholder + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('supplier') : []);
    }


     public static function supplier_place_holder($supplier) {
        if (!isset($supplier['Supplier'])) {
            $supplier['Supplier'] = $supplier;
        }
         $controller = new InvoicesController();

         $view = new View($controller, false);
         $placeholder = array(
            '{%supplier_number%}' => $supplier['Supplier']['supplier_number'],
            '{%supplier_business_name%}' => $supplier['Supplier']['business_name'],
            '{%supplier_name%}' => !trim($supplier['Supplier']['first_name'] . ' ' . $supplier['Supplier']['last_name'])
                ? $supplier['Supplier']['business_name']
                : $supplier['Supplier']['first_name'] . ' ' . $supplier['Supplier']['last_name'],
            '{%supplier_first_name%}' => $supplier['Supplier']['first_name'],
            '{%supplier_last_name%}' => $supplier['Supplier']['last_name'],
            '{%supplier_phone%}' => $supplier['Supplier']['phone1'],
            '{%supplier_mobile%}' => $supplier['Supplier']['phone2'],
            '{%supplier_email%}' => $supplier['Supplier']['email'],
            '{%supplier_full_address%}' => $view->element('format_address_html', $supplier['Supplier']),
            '{%supplier_address%}' => $supplier['Supplier']['address1'] . (empty($supplier['Supplier']['address2']) ? '' : '<br/>' . $supplier['Supplier']['address2']),
            '{%supplier_postcode%}' => $supplier['Supplier']['postal_code'],
            '{%supplier_postal_code%}' => $supplier['Supplier']['postal_code'],
            '{%supplier_city%}' => $supplier['Supplier']['city'],
            '{%supplier_state%}' => $supplier['Supplier']['state'],
            '{%supplier_country%}' => get_country_code($supplier['Supplier']['country_code']),
            '{%supplier_secondary_name%}' => $supplier['Supplier']['secondary_name'],
            '{%supplier_secondary_address%}' => $supplier['Supplier']['secondary_address1'] . (empty($supplier['Supplier']['secondary_address2']) ? '' : '<br/>' . $supplier['Supplier']['secondary_address2']),
            '{%supplier_secondary_address2%}' => $supplier['Supplier']['secondary_address2'],
            '{%supplier_secondary_postcode%}' => $supplier['Supplier']['secondary_postal_code'],
            '{%supplier_secondary_city%}' => $supplier['Supplier']['secondary_city'],
            '{%supplier_secondary_state%}' => $supplier['Supplier']['secondary_state'],
            '{%supplier_secondary_country%}' => get_country_code($supplier['Supplier']['secondary_country_code']),
            '{%supplier_bn1%}' => $supplier['Supplier']['bn1'],
			'{%supplier_bn2%}' => $supplier['Supplier']['bn2'],
			'{%supplier_business_registration_info1%}' => !empty($supplier['Supplier']['bn1'])?__($supplier['Supplier']['bn1_label'],true).': '.$supplier['Supplier']['bn1']:'',
			'{%supplier_business_registration_info2%}' => !empty($supplier['Supplier']['bn2'])?__($supplier['Supplier']['bn2_label'],true).': '.$supplier['Supplier']['bn2']:'',
        );

         //Branches
         if (ifPluginInstalled(BranchesPlugin)) {
             $placeholder += self::branches_place_holder($supplier['Supplier']['branch_id'],'supplier');
         }
         $placeholder  +=self::get_custom_place_holders('suppliers',$supplier['Supplier']['id']);
        if (isset($supplier['Supplier']['id'])) {
            $placeholder  += self::get_additional_fields_place_holders_values(EntityKeyTypesUtil::SUPPLIER_ENTITY_KEY, $supplier['Supplier']['id']);
        }
        return $placeholder;
     }



    public static function purchaseorders_custom_field_place_holder($purchase_order)
    {

        $placeholders = array();
        //print_pre($invoice);
        //die();
        //$placeholders=[];
        $PurchaseOrderCustomFieldM = ClassRegistry::init('PurchaseOrderCustomField', 'Model');
        $PurchaseOrderCustomField=$PurchaseOrderCustomFieldM->find('all',array('recursive'=>-1,'conditions'=>array('PurchaseOrderCustomField.purchase_order_id'=>$purchase_order['PurchaseOrder']['id'])));

            foreach ($PurchaseOrderCustomField as $value) {

                if(!preg_match('/[^0-9أ-يa-zA-Z ]/',$value['PurchaseOrderCustomField']['label']))
                {
                    $placeholders["{%{$value['PurchaseOrderCustomField']['label']}%}"]=$value['PurchaseOrderCustomField']['value'];
                }

            }

        return $placeholders;
    }

    public static function purchaseorders_place_holder($purchase_order) {
        if(isset($purchase_order['PurchaseOrder']))
        {
            $purchase_order = $purchase_order['PurchaseOrder'];
        }
		$invoice_total_spelled = self::tafketWithChange($purchase_order['summary_total'], $purchase_order['currency_code']);
        $deposit_spelled = self::tafketWithChange($purchase_order['summary_deposit'], $purchase_order['currency_code']);
        $paid_spelled = self::tafketWithChange($purchase_order['summary_paid'], $purchase_order['currency_code']);
        $unpaid_spelled = self::tafketWithChange($purchase_order['summary_unpaid'], $purchase_order['currency_code']);
        $discount_spelled = self::tafketWithChange($purchase_order['summary_discount'], $purchase_order['currency_code']);
        $extraDetails = json_decode($purchase_order['extra_details'], true);
        $extraDetails_supplier_balance = $extraDetails['supplier_balance'] ?? 0;
		$storeModel = GetObjectOrLoadModel('Store');
		$store=$storeModel->find('first',['conditions'=>['Store.id'=>$purchase_order['store_id']]]);

        $purchaseOrderItemModel = GetObjectOrLoadModel('PurchaseOrderItem');
        $po_item_qty= $purchaseOrderItemModel->find('all',array('fields'=>'sum(`PurchaseOrderItem`.`quantity`) as qty','conditions'=>array('purchase_order_id'=>$purchase_order['id']))) ;
        $totalAmount = $purchaseOrderItemModel->query("SELECT SUM(amount) as sum_credit FROM `purchase_order_payments` WHERE purchase_order_id = {$purchase_order['id']} AND `payment_method` = 'supplier_credit';",false);
        $sum_credit= (isset($totalAmount[0][0]['sum_credit'])) ?(float) $totalAmount[0][0]['sum_credit']:0;

        $placeholders = array(
            '{%supplier_business_name%}' => $purchase_order['supplier_business_name'],
            '{%supplier_name%}' => $supplier_name = (empty($purchase_order['supplier_first_name']) && empty($purchase_order['supplier_last_name']) ? $purchase_order['supplier_business_name'] : $purchase_order['supplier_first_name'] . ' ' . $purchase_order['supplier_last_name']),
            '{%supplier_address%}' => $purchase_order['supplier_address1'] . ife($purchase_order['supplier_address2'], '<br />' . $purchase_order['supplier_address2']),
            '{%supplier_city%}' => $purchase_order['supplier_city'],
            '{%supplier_state%}' => $purchase_order['supplier_state'],
            '{%supplier_postal_code%}' => $purchase_order['supplier_postal_code'],
            '{%supplier_country%}' => get_country_code($purchase_order['supplier_country_code']),
            '{%purchaseorders_no%}' => $purchase_order['no'],
            '{%purchaseorders_date%}' => $purchase_order['date'],
            '{%due_date%}' => $purchase_order['due_date'],
            '{%purchaseorders_total%}' => format_price($purchase_order['summary_total'], $purchase_order['currency_code']),
            '{%deposit%}' => format_price($purchase_order['summary_deposit'], $purchase_order['currency_code']),
            '{%paid%}' => format_price($purchase_order['summary_paid'], $purchase_order['currency_code']),
            '{%unpaid%}' => format_price($purchase_order['summary_unpaid'], $purchase_order['currency_code']),
            '{%discount%}' => format_price($purchase_order['summary_discount'], $purchase_order['currency_code']),
			'{%invoice_total_spelled%}' => $invoice_total_spelled ,
			'{%purchaseorder_total_spelled%}' => $invoice_total_spelled ,
            '{%purchaseorder_subtotal%}' => format_price($purchase_order['summary_subtotal'], $purchase_order['currency_code']),
			 '{%unpaid_spelled%}' => $unpaid_spelled ,
			'{%discount_spelled%}' => $discount_spelled ,
			'{%paid_spelled%}' => $paid_spelled ,
			'{%deposit_spelled%}' => $deposit_spelled ,
			'{%ware_house%}' => $store['Store']['name'],
            '{%po_item_qty%}' => $po_item_qty[0][0]['qty'],
            '{%po_note%}'=>$purchase_order['html_notes'],
            '{%purchase_order_advanced_supplier_balance%}' => format_price(in_array($purchase_order['type'], [PurchaseOrder::Purchase_Refund, PurchaseOrder::CREDIT_NOTE]) ? (float) $extraDetails_supplier_balance - $purchase_order['summary_unpaid'] : ( (float) $extraDetails_supplier_balance +( $purchase_order['summary_unpaid']+$sum_credit)), $purchase_order['currency_code']),
            '{%extra_details.supplier_balance%}' => format_price($extraDetails_supplier_balance, $purchase_order['currency_code']),
            '{%purchase_order_no%}' => $purchase_order['no'],
        );
        $extraDetails = json_decode($purchase_order['extra_details'], true);
        $extraDetails_supplier_balance = $extraDetails['supplier_balance'] ?? 0;
        $extraDetails_supplier_balance = $extraDetails_supplier_balance + $purchase_order['summary_unpaid'];


        $RealTaxes=GetObjectOrLoadModel('PurchaseOrder')->getPurchaseOrder($purchase_order['id']);
        $tax_total=0;
        foreach ($RealTaxes['PurchaseOrderTax'] as $key => $value) {
            $placeholders["{%tax{$key}_name%}"] = $value['name'].' ('.$value['value'].'%)';
            $placeholders["{%tax{$key}_value%}"] = $value['invoice_value'];
            $tax_total=$tax_total+$value['invoice_value'];
        }

        $placeholders['{%purchaseorder_tax_total%}']=format_price($tax_total, $purchase_order['currency_code']);
        $placeholders['{%item-discount-total%}']=format_price($purchase_order['item_discount_amount'], $purchase_order['currency_code']);
        //TOdo Add Placeholder name in invoice placeholder list function
        $placeholders['{%invoice-total-with-discount%}']=format_price($purchase_order['summary_subtotal']+$purchase_order['item_discount_amount'],$purchase_order['currency_code']);
        $placeholders['{%purchaseorder_total_tax_spelled%}']=self::tafketWithChange($tax_total, $purchase_order['currency_code']);
        if( !empty($purchase_order['supplier_id']) )
        {
            $Supplier = ClassRegistry::init('Supplier', 'Model');
            $supplier = $Supplier->read(null, $purchase_order['supplier_id']);

            $supplier_placeholders = self::supplier_place_holder($supplier['Supplier']);
            $placeholders = array_merge($placeholders, $supplier_placeholders);
        }

        if( !empty($purchase_order['staff_id']) )
        {
            $staff_placeholders = self::staff_place_holder($purchase_order['staff_id']);
            $placeholders = array_merge($placeholders, $staff_placeholders);
        }



        foreach ($placeholders as $key => $holder) {
            $placeholders[str_replace('_', '-', $key)] = $holder;
        }

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholders += self::branches_place_holder($purchase_order['branch_id'],'purchase_order');
        }
        if (isset($purchase_order['id']) && $purchase_order['id'] != '') {
            $placeholders += self::get_additional_fields_place_holders_values(EntityKeyTypesUtil::PURCHASE_INVOICE, $purchase_order['id']);
        } else {
            if(isset($_POST['data']) && isset($_POST['data']['le_custom_data_purchase_order'])) {
                $customDataPlaceholders = [];
                foreach ($_POST['data']['le_custom_data_purchase_order'] as $prefix => $field) {
                    $templateKey = "{%le_custom_data_purchase_order_{$prefix}%}";
                    $customDataPlaceholders[$templateKey] =  $field;
                }

                $placeholders += $customDataPlaceholders;
            }

        }

        return $placeholders;
    }

    public static function site_place_holder_list() {
		$current_site=getCurrentSite();
        $placeholder =  array(
            '{%site_first_name%}' => __('Business first name', true),
            '{%site_last_name%}' => __('Business last name', true),
            '{%subdomain%}' => __('Business domain', true),
            '{%site_business_name%}' => __('Business name', true),
            '{%site_business_email%}' => __('Business email', true),
            '{%site_address1%}' => __('Business address 1', true),
            '{%site_address2%}' => __('Business address 2', true),
            '{%site_city%}' => __('Business city', true),
            '{%site_state%}' => __('Business state', true),
            '{%site_postal_code%}' => __('Business postal code', true),
            '{%site_telephone%}' => __('Business telephone', true),
            '{%site_mobile%}' => __('Business mobile', true),
            '{%current_time%}' => __('Current Time', true),
			'{%site_logo%}' => __('Site Logo', true),
            '{%full_business_address%}' => __('Business Full Address', true),
            '{%domain_name_only%}' => __('Domain Name Only', true),

        );

        if($current_site['bn1'] != NULL)
        {
            $placeholder += [ '{%site_bn1%}' => __("bn1 Code",true) ];
            $placeholder += [ '{%site_bn1_label%}' => __(getCurrentSite('bn1_label'),true) ];
        }

        if(getCurrentSite('bn2') != NULL)
        {
            $placeholder += [ '{%site_bn2%}' => __('bn2 Code',true) ];
            $placeholder += [ '{%site_bn2_label%}' => __(getCurrentSite('bn2_label'),true) ];
        }

//        Configure::write('debug', 2);
//        dd($placeholder );
        return $placeholder;
    }

    public static function site_place_holder($site = array()) {
        if( $site == null  || !isset($site['timezone'])){
            $site= getCurrentSite();
		}
        $controller = new InvoicesController();

        $view = new View($controller, false);
        try {
            $cacheName = "timezone_{$site['timezone']}";
            $timezone = PortalCache::get($cacheName, function () use ($site) {
                $TimeZone = GetObjectOrLoadModel('Timezone');
                $timeZone = $TimeZone->findById($site['timezone']);
                return $timeZone['Timezone'];
            });
            $date = new DateTime('now', new DateTimeZone($timezone['zone_name']));
        } catch (Exception $exception) {
            $TimeZone = ClassRegistry::init('TimeZone', 'Model');
            $timezone = $TimeZone->read(null, $site['timezone'] );
            $date = new DateTime('now', new DateTimeZone($timezone['TimeZone']['zone_name']));
        }
        $localtime = $date->format('h:i:s');
        $placeholder =  array(
            '{%first_name%}' => $site['first_name'],
            '{%last_name%}' => $site['last_name'],
            '{%subdomain%}' => $site['subdomain'],
            '{%business_name%}' => $site['business_name'],
            '{%business-email%}' => $site['email'],
            '{%business_email%}' => $site['email'],
            '{%address1%}' => $site['address1'],
            '{%address2%}' => $site['address2'],
            '{%city%}' => $site['city'],
            '{%state%}' => $site['state'],
            '{%postal-code%}' => $site['postal_code'],
            '{%postal_code%}' => $site['postal_code'],
            '{%telephone%}' => $site['phone1'],
            '{%mobile%}' => $site['phone2'],
            '{%full_business_address%}' => $view->element('format_address_html', $site + array('is_inline' => true)),
            '{%full-business-address%}' => $view->element('format_address_html', $site + array('is_inline' => true)),
            '{%site_bn1%}' => $site['bn1'],
            '{%site_bn2%}' => $site['bn2'],
            '{%site_bn1_label%}' => $site['bn1_label'],
            '{%site_bn2_label%}' => $site['bn2_label'],
            '{%site_first_name%}' => $site['first_name'],
            '{%site_last_name%}' => $site['last_name'],
            '{%site_business_name%}' => $site['business_name'],
            '{%site_business_email%}' => $site['email'],
            '{%site_address1%}' => $site['address1'],
            '{%site_address2%}' => $site['address2'],
            '{%site_city%}' => $site['city'],
            '{%site_state%}' => $site['state'],
            '{%site_postal_code%}' => $site['postal_code'],
            '{%site_telephone%}' => $site['phone1'],
            '{%site_mobile%}' => $site['phone2'],
            '{%current_time%}' => $localtime,
            '{%site_logo%}' => ( empty($site['site_logo']) ? "" : "<img src='".'https://'.$site['subdomain']."/files/images/site-logos/" . $site['site_logo'] . "?w=200&h=100' >"),
            '{%domain_name_only%}' => Domain_Name_Only == 'enerpize'?__('Enerpize', true) :__('Daftra', true),

        );

        foreach ($placeholder as $key => $holder) {
            $placeholder[str_replace('_', '-', $key)] = $holder;
        }

        return $placeholder;
    }

    public static function barcode_place_holder_list() {
        return array(
            '{%item_name%}' => __("Product name",true),
            '{%barcode_number%}' => __("Barcode number",true),
            '{%barcode_image%}' => __("Barcode image",true),
            '{%barcode_width%}' => __("Barcode width",true),
            '{%barcode_height%}' => __("Barcode height",true),
        );
    }

    public static function barcode_place_holder($barcode)
    {
        $placeholder = array(
            '{%item_name%}' => $barcode['item_name'],
            '{%barcode_number%}' => $barcode['barcode_number'],
            '{%barcode_image%}' => $barcode['barcode_image'],
            '{%barcode_width%}' => $barcode['barcode_width'],
            '{%barcode_height%}' => $barcode['barcode_height'],
        );

        return $placeholder;
    }

    /* ----------Expense Placeholders---------- */
    public static function expense_place_holder_list()
    {
        $array = array(
            '{%date%}' => __("Expense Date",true),
            '{%expense_id%}' => __("Expense ID",true),
            '{%amount%}' => __("Expense Amount",true),
            '{%note%}' => __("Note",true),
            '{%treasury%}' => __("Treasury",true),
            //tax_1
            '{%tax1_id%}' => __("Tax_1 Id",true),
            '{%tax1_name%}' => __("Tax_1 Name",true),
            '{%tax1_value%}' => __("Tax_1 Value",true),
            '{%tax1_amount%}' => __("Tax_1 Amount",true),
            '{%tax1_included%}' => __("Tax_1 Included",true),

            //tax_2
            '{%tax2_id%}' => __("Tax_2 Id",true),
            '{%tax2_name%}' => __("Tax_2 Name",true),
            '{%tax2_value%}' => __("Tax_2 Value",true),
            '{%tax2_amount%}' => __("Tax_2 Amount",true),
            '{%tax2_included%}' => __("Tax_2 Included",true),

            '{%vendor%}' => __("Vendor",true),
            '{%spelled_amount%}' => __("Spelled Amount",true),
            '{%client_bussiness_name_or_journal_name%}' => __("Client Or Journal", true),
            '{%expense_attachement%}' => __("Expense Attachement", true),
        );
		$array += self::get_custom_place_holders_list('expenses');

        $array += self::expense_category_place_holder_list();

        //enable_multi_journal_accounts is true then journals are comming from accounts_data

            $array['{%journal_account_table%}'] =  __("Journal Accounts Table", true);


        $array += self::journal_account_place_holder_list();
        $array += self::treasury_place_holder_list();
        $array += self::staff_place_holder_list();
        $array += self::client_place_holder_list();
        $array += self::site_place_holder_list();

        return $array + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('expense') : []);
    }

    public static function expense_place_holder($expense, $site = 'daftra.com')
    {
        if(isset($expense['Expense']))
        {
            $expense = $expense['Expense'];
        }
        $condition['Expense.id'] = $expense['id'];
//        dd($expense);
        $Expense = ClassRegistry::init('Expense', 'Model');
        $ExpenseRow = $Expense->find('first' , array('conditions' => $condition));

        $Currency = ClassRegistry::init('Currency', 'Model');

        //show the tafketed number
        $amount_Tafketed = self::tafketWithChange($expense['amount'], $expense['currency_code'], $site);

        $client_bussiness_name_or_journal_name = '...............................';
//        $expense['journal_account_id'] = 439;
        $clientPlaceHolder=[];
        if( ! empty( $expense['client_id']) )
        {
            $Client = ClassRegistry::init('Client', 'Model');
            $client = $Client->find( 'first', [ 'conditions' => [ 'Client.id' => $expense['client_id'] ] ] );
            $clientPlaceHolder=self::client_place_holder($client['Client']);
            $client_bussiness_name_or_journal_name = $client['Client']['business_name'];
        }else if( ! empty( $expense['journal_account_id']) )
        {
            $JournalAccount = ClassRegistry::init('JournalAccount', 'Model');
            $Journal = $JournalAccount->find( 'first', [ 'conditions' => [ 'JournalAccount.id' => $expense['journal_account_id'] ] ] );

            if($Journal['JournalAccount']['entity_type']=='client') {
                $Client = ClassRegistry::init('Client', 'Model');
                $client = $Client->find('first', ['conditions' => ['Client.id' => $Journal['JournalAccount']['entity_id']]]);
                $clientPlaceHolder = self::client_place_holder($client['Client']);
            }
            $client_bussiness_name_or_journal_name = $Journal['JournalAccount']['name'];
        } else {
            $accounts = json_decode($expense['accounts_data'],true);
            if (is_countable($accounts) && count($accounts)) {
                $JournalAccount = ClassRegistry::init('JournalAccount', 'Model');
                $Journal = $JournalAccount->find( 'first', [ 'conditions' => [ 'JournalAccount.id' => $accounts[0]['journal_account_id'] ] ] );
                $client_bussiness_name_or_journal_name = $Journal['JournalAccount']['name'];
            }
        }

        //render the attachement as image and image from pdf
        $file_type = pathinfo($expense['file_full_path'], PATHINFO_EXTENSION);
        if(in_array(strtolower($file_type), ['jpg','jpeg','bmp','png'])) //image type render as image tag
        {
            $expense_attachement = self::getImageTag($expense['file_full_path']);
        }
        else if( strtolower($file_type) == 'pdf' ) //image type render as pdf convert pdf to image and
        {
            $expense_attachement = "<a href=\"https://".getCurrentSite('subdomain')."/" . $expense['file_full_path'] ."\">Downlaod Attachment</a>";
        }
        if (!empty($ExpenseRow['Attachments'])) {
            $expense_attachement = self::get_aws_file_path($ExpenseRow['Attachments']);
        }
        $Treasury= ClassRegistry::init('Treasury', 'Model');
		$treasury_name=$Treasury->find(array('id'=>$expense['treasury_id']));


//        $expense_attachement = pathinfo($path, PATHINFO_EXTENSION);
        // replace all the expenses main fields
        $array = array(
            '{%date%}' => format_date($expense['date']),
            '{%expense_id%}' => !empty($expense['expense_number'])?$expense['expense_number']:$expense['id'],
            '{%amount%}' => format_price($expense['amount'],$expense['currency_code'] ),
            '{%note%}' => str_replace([ "\r\n" ], '<br />', $expense['note'] ),
			'{%treasury%}'=>$treasury_name['Treasury']['name'],
            //tax_1
            '{%tax1_id%}' => $expense['tax1_id'],
            '{%tax1_name%}' => $expense['tax1_name'],
            '{%tax1_value%}' => format_price($expense['tax1_value']),
            '{%tax1_amount%}' => format_number($expense['tax1_amount']),
            '{%tax1_included%}' => $expense['tax1_included'],

            //tax_2
            '{%tax2_id%}' => $expense['tax2_id'],
            '{%tax2_name%}' => $expense['tax2_name'],
            '{%tax2_value%}' => format_price($expense['tax2_value']),
            '{%tax2_amount%}' => format_number($expense['tax2_amount']),
            '{%tax2_included%}' => $expense['tax2_included'],

            '{%vendor%}' => $expense['vendor'],
            '{%spelled_amount%}' => $amount_Tafketed,
            '{%client_bussiness_name_or_journal_name%}' => $client_bussiness_name_or_journal_name,
            '{%expense_attachement%}' => $expense_attachement,
        );

//        Configure::write('debug', 2);
//        $t = __($Currency->getCurrencyList($Currency->getCurrencyList(['code'=>$expense['currency_code']])[$expense['currency_code']]), true);
//        dd($t);

        //load the Site place holders
        $array += self::site_place_holder(getCurrentSite());

        $array += $clientPlaceHolder;
        //load the category place holders instead of the loading the category id
        $array += self::expense_category_place_holder($ExpenseRow['ExpenseCategory']);
        //enable_multi_journal_accounts is true then journals are comming from accounts_data

        if( !empty($journal_account = json_decode($expense['accounts_data'],true)) && !empty($journal_account[0]['journal_account_id']))
        {
            $JournalAccount = ClassRegistry::init('JournalAccount');

            foreach($journal_account as $index => $account)
            {
                $journal_account[$index]['journal_account_name'] = $JournalAccount->find('first',['conditions'=>['JournalAccount.id'=>$account['journal_account_id']]])['JournalAccount']['name'];
            }

            $controller = new InvoicesController();

            $view = new View($controller, false);
            $table = $view->element("journal_accounts_table",['journal_account' => $journal_account]);
            $array['{%journal_account_table%}'] = $table;
        }else{ // load the journal from the journal_account_id
            $JournalAccount = ClassRegistry::init('JournalAccount');
            $journal_account = $JournalAccount->read(null, $ExpenseRow['Expense']['journal_account_id']);
            $array += self::journal_account_place_holder($journal_account['JournalAccount']);
        }

        //load the treasury placeholders instead of loading the
        $array += self::treasury_place_holder($ExpenseRow['Treasury']);

        //load the place holders of staff instead of loading the staff_id
        $array += self::staff_place_holder($ExpenseRow['Staff']['id']);

        //load the place holders of client instead of loading the client_id
        $array += self::client_place_holder($ExpenseRow['Client']);

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $array += self::branches_place_holder($expense['branch_id'],'expense');
        }

        return $array;
    }

    /* ----------Expense Category Placeholders---------- */
    public static function expense_category_place_holder_list()
    {
        return array(
            '{%category_name%}' => __("Category Name",true),
            '{%category_type%}' => __("Category Type",true),
            '{%category_description%}' => __("Category Description",true),
        );
    }

    public static function expense_category_place_holder($expense_category)
    {
        //replace $category placeholders
        return array(
            '{%category_name%}' => $expense_category['name'],
            '{%category_type%}' => $expense_category['type'],
            '{%category_description%}' => $expense_category['description'],
        );
    }

    /* ----------Journal Account Placeholders---------- */
    public static function journal_account_place_holder_list()
    {
        return array(
            '{%journal_id%}' => __("Journal Account Id", true),
            '{%journal_account_code%}' => __("Code",true),
            '{%journal_account_entity_id%}' => __("Entity ID",true),
            '{%journal_account_entity_type%}' => __("Entity Type",true),
            '{%journal_account_is_recalculated%}' => __("Is Recalculated",true),
            '{%journal_account_last_transaction_update%}' => __("LastTransactionUpdate",true),
            '{%journal_account_name%}' => __("Name",true),
            '{%journal_account_total_credit%}' => __("Total Credit",true),
            '{%journal_account_total_debit%}' => __("Total Debit",true),
            '{%journal_account_total_balance%}' => __("Total Balance",true),
        );
    }

    public static function journal_account_place_holder($journal_account)
    {
        //replace $category placeholders
        return array(
            '{%journal_id%}' => $journal_account['id'],
            '{%journal_account_code%}' => $journal_account['code'],
            '{%journal_account_entity_id%}' => $journal_account['entity_id'],
            '{%journal_account_entity_type%}' => $journal_account['entity_type'],
            '{%journal_account_is_recalculated%}' => $journal_account['is_recalculated'],
            '{%journal_account_last_transaction_update%}' => $journal_account['last_transaction_update'],
            '{%journal_account_name%}' => $journal_account['name'],
            '{%journal_account_total_credit%}' => $journal_account['total_credit'],
            '{%journal_account_total_debit%}' => $journal_account['total_debit'],
            '{%journal_account_total_balance%}' => (string)($journal_account['total_debit'] - $journal_account['total_credit']),
        );
    }

    /* ----------Journals Placeholders---------- */
    public static function journal_place_holder_list()
    {
        $placeholders =  array(
            '{%journal_id%}' => __("Id",true),
            '{%journal_number%}' => __("Number",true),
            '{%journal_date%}' => __("Date",true),
            '{%journal_created_by%}' => __("Created By",true),
            '{%journal_creation_date%}' => __("Creation Date ",true),
            '{%journal_entity_type%}' => __("Entity type",true),
            '{%journal_is_automatic%}' => __("Is automatic",true),
            '{%journal_description%}' => __("description",true),
            '{%journal_total_debit%}' => __("Total debit",true),
            '{%journal_total_credit%}' => __("Total credit",true),
            '{%journal_total_debit_without_symbol%}' =>  __("Total debit without currency symbol",true),
            '{%journal_total_credit_without_symbol%}' => __("Total credit without currency symbol",true),
            '{%journal_total_debit_without_tax%}' => __("Total debit without taxes", true),
            '{%journal_total_credit_without_tax%}' => __("Total credit without taxes", true),
            '{%journal_currency_code%}' => __("Currency code",true),
            '{%site_currency_symbol%}' => __("Site Currency Symbol", true),
            '{%journal_currency_credit%}' => __("Currency credit",true),
            '{%journal_currency_debit%}' => __("Currency debit",true),
            '{%journal_currency_rate%}' => __("Currency rate",true),
            '{%journal_transactions_table%}' => __("Journal Transactions Table",true),
            '{%journal_transactions_table_2%}' => __("Journal Transactions Table",true),
            '{%journal_transactions_table_without_taxes%}' => __("Journal Transactions Table Without Taxes",true),
            '{%journal_transactions_table_without_taxes_2%}' => __("Journal Transactions Table Without Taxes",true),
            '{%tax_transactions_table%}' => __("Taxes Transactions Table",true),
            '{%tax_transactions_table2%}' => __("Taxes Transactions Table",true),
            '{%cost_center_table%}' => __("Cost Center Table",true),
        );

        $placeholders += self::site_place_holder_list(getCurrentSite());
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholders += self::branches_place_holder_list('journal');
        }
        $placeholders += self::get_additional_fields_place_holders_list(EntityKeyTypesUtil::JOURNAL);
        return $placeholders;
    }

    public static function journal_place_holder( $journal )
    {
        $Journal = ClassRegistry::init('Journal', 'Model');
        $Journal->recursive = 2;
        $JournalTransaction = ClassRegistry::init('JournalTransaction', 'Model');
        $journal_transactions = $Journal->find('first',['conditions'=>['Journal.id'=>$journal['Journal']['id']]]);
        $CostCenterTransaction=ClassRegistry::init('CostCenterTransaction','Model');
        $ItemsTag = ClassRegistry::init('ItemsTag');
        $cost_transactions = $CostCenterTransaction->find('all',[ 'conditions' => ['CostCenterTransaction.journal_id' => $journal['Journal']['id']] ]);

        unset($journal_transactions['JournalTransaction']);

        if($journal['Journal']['is_automatic']) {
            $order_by = array('CASE WHEN JournalTransaction.debit > 0 THEN 1  WHEN JournalTransaction.debit <= 0 THEN 2 END ' => 'ASC', 'JournalTransaction.id' => 'ASC');
        }else{
//            $order_by = array('CASE WHEN JournalTransaction.debit > 0 THEN 1  WHEN JournalTransaction.debit <= 0 THEN 2 END ' => 'DESC', 'JournalTransaction.id' => 'ASC');
            $order_by = array('COALESCE(JournalTransaction.display_order, JournalTransaction.id)' => 'ASC');
        }
        $transactions = $JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $journal['Journal']['id']) ,'order' => $order_by ));
        //Backward compatability for cost center new added display order
        if(!$transactions) {
            $order_by = array('JournalTransaction.id' => 'ASC');
            $transactions = $JournalTransaction->find('all',array('conditions'=> array('JournalTransaction.journal_id' => $journal['Journal']['id']) ,'order' => $order_by )); 
        }

        foreach ($transactions as $k => $transaction){
            $journal_transactions['JournalTransaction'][$k] = $transaction['JournalTransaction'];
            $journal_transactions['JournalTransaction'][$k]['JournalAccount'] = $transaction['JournalAccount'];
        }
        $total_debit = 0;
        $total_credit = 0;

        $debitWithoutTax = 0;
        $creditWithoutTax = 0;
        $isAuto = $journal['Journal']['is_automatic'];
        $hasTax = false;
        $taxTransactions = ['JournalTransaction' => []];
        $taxModel = GetObjectOrLoadModel('Tax');
        $taxes = $taxModel->getTaxList();
        $transactions_centers = [];
        foreach($cost_transactions as $costTransaction){
            if(!isset($costTransaction['CostCenterTransaction']['journal_transaction_id'])){
                break;
            }
            if(!isset($transactions_centers[$costTransaction['CostCenterTransaction']['journal_transaction_id']])){
                $transactions_centers[$costTransaction['CostCenterTransaction']['journal_transaction_id']] = $costTransaction['CostCenter'];
            }
            $transactions_centers[$costTransaction['CostCenterTransaction']['journal_transaction_id']]['total_percentage'] += $costTransaction['CostCenterTransaction']['percentage'];
            $transactions_centers[$costTransaction['CostCenterTransaction']['journal_transaction_id']]['currency_debit'] += $costTransaction['CostCenterTransaction']['currency_debit'];
            $transactions_centers[$costTransaction['CostCenterTransaction']['journal_transaction_id']]['currency_credit'] += $costTransaction['CostCenterTransaction']['currency_credit'];
        }
        foreach($journal_transactions['JournalTransaction'] as &$transaction)
        {
            $total_debit += $transaction['currency_debit'];
            $total_credit += $transaction['currency_credit'];
            if ($transaction['tax_id']) {
                $hasTax = true;
                $taxName = $transaction['tax_id'] ? $taxes[$transaction['tax_id']] : '-';
                $transaction['tax_name'] = $taxName;
            }
            if (isset($transactions_centers[$transaction['id']])) {
                $transaction['cost_center_name'] = $transactions_centers[$transaction['id']]['name'];
                $transaction['cost_center_currency_debit'] = (string)$transactions_centers[$transaction['id']]['currency_debit'];
                $transaction['cost_center_currency_credit'] = (string)$transactions_centers[$transaction['id']]['currency_credit'];
            } else {
                $transaction['cost_center_name'] = '';
                $transaction['cost_center_currency_debit'] = (string)0;
                $transaction['cost_center_currency_credit'] = (string)0;
            }
            if (!$isAuto && strpos($transaction['subkey'], 'tax')) {
                $taxTransactions['JournalTransaction'][] = &$transaction;
                $debitWithoutTax -= $transaction['currency_debit'];
                $creditWithoutTax -= $transaction['currency_credit'];
                $transaction['tax_name'] = $taxes[$transaction['JournalAccount']['entity_id']];
            }
            $debitWithoutTax += $transaction['currency_debit'];
            $creditWithoutTax += $transaction['currency_credit'];
            $transaction['currency_debit'] = format_price_simple($transaction['currency_debit'], $transaction['currency_code']);
            $transaction['debit'] = format_price_simple($transaction['debit'], $transaction['currency_code']);
            $transaction['currency_credit'] = format_price_simple($transaction['currency_credit'], $transaction['currency_code']);
            $transaction['credit'] = format_price_simple($transaction['credit'], $transaction['currency_code']);
            $transaction['description'] = !empty($transaction['alter_description'])? $transaction['alter_description'] : $transaction['description'];
            $tags = $ItemsTag->get_item_tags($transaction['id'], ItemsTag::TAG_ITEM_TYPE_DEBIT_JOURNAL_TRANSACTION, true);
            $transaction['tags'] = implode(',', $tags);
        }
        $journal_transactions['total_debit'] = format_price_simple($total_debit,$journal_transactions['JournalTransaction'][0]['currency_code']) . " " . $journal_transactions['JournalTransaction'][0]['currency_code'];
        $journal_transactions['total_credit'] = format_price_simple($total_credit,$journal_transactions['JournalTransaction'][0]['currency_code']) . " " . $journal_transactions['JournalTransaction'][0]['currency_code'];
        $journal_transactions['total_debit_without_tax'] = format_price_simple($debitWithoutTax,$journal_transactions['JournalTransaction'][0]['currency_code']) . " " . $journal_transactions['JournalTransaction'][0]['currency_code'];
        $journal_transactions['total_credit_without_tax'] = format_price_simple($creditWithoutTax,$journal_transactions['JournalTransaction'][0]['currency_code']) . " " . $journal_transactions['JournalTransaction'][0]['currency_code'];
        $journal_transactions['JournalTransaction'] = is_array($journal_transactions['JournalTransaction']) ? array_values($journal_transactions['JournalTransaction']) : [];

        $journal_transactions_without_taxes = $journal_transactions;
        $journal_transactions_without_taxes['Journal']['currency_debit'] = $debitWithoutTax;
        $journal_transactions_without_taxes['Journal']['currency_credit'] = $creditWithoutTax;
        foreach ($journal_transactions_without_taxes['JournalTransaction'] as $i => &$transaction) {
            if (!$isAuto && strpos($transaction['subkey'], 'tax')) {
                unset($journal_transactions_without_taxes['JournalTransaction'][$i]);
            }
        }

        $journal_transactions_without_taxes['JournalTransaction'] = array_values($journal_transactions_without_taxes['JournalTransaction']);

        $taxesDebit = $total_debit - $debitWithoutTax;
        $taxesCredit = $total_credit - $creditWithoutTax;
        $taxTransactions['Journal']['currency_debit'] = format_price_simple($taxesDebit,$journal_transactions['JournalTransaction'][0]['currency_code']) . " " . $journal_transactions['JournalTransaction'][0]['currency_code'];
        $taxTransactions['Journal']['currency_credit'] = format_price_simple($taxesCredit,$journal_transactions['JournalTransaction'][0]['currency_code']) . " " . $journal_transactions['JournalTransaction'][0]['currency_code'];
//        dd($journal);
//        dd($journal['JournalTransaction']);
//        $view = new View( $this, false );
//        $transactions_table = str_replace("\n",'', $view->element( 'journal_transactions', [ 'journal' => $journal ] ));
            $journal = $journal['Journal'];

        $staff_id = $journal['staff_id'];
        $staffName = getCurrentSite('first_name') . ' ' . getCurrentSite('last_name');
        if ($staff_id != 0) {
            $Staff = ClassRegistry::init('Staff', 'Model');
            $staffRow = $Staff->find('first', array('conditions' => array('Staff.id' => $staff_id)));
            $staffName = $staffRow['Staff']['name'];
        }
        if ($journal['is_automatic']) {
            $staffName = __('System', true);
        }

        $placeholders =  array(
            '{%journal_id%}' => !empty($journal['number'])?$journal['number']:$journal['id'],
            '{%journal_date%}' => format_date($journal['date']),
            '{%journal_created_by%}' => $staffName,
            '{%journal_creation_date%}' => format_date($journal['created']),
            '{%journal_entity_type%}' => $journal['entity_type'],
            '{%journal_is_automatic%}' => $journal['is_automatic'],
            '{%journal_description%}' => !empty($journal['alter_description'])?$journal['alter_description']:$journal['description'],
            '{%journal_total_debit%}' => format_price($journal['total_debit'], $journal['currency_code']),
            '{%journal_total_credit%}' => format_price($journal['total_credit'], $journal['currency_code']),
            '{%journal_total_debit_without_symbol%}' => format_price_simple($journal['total_debit'], $journal['currency_code']),
            '{%journal_total_credit_without_symbol%}' => format_price_simple($journal['total_credit'], $journal['currency_code']),
            '{%journal_total_debit_without_tax%}' => $journal_transactions['total_debit_without_tax'],
            '{%journal_total_credit_without_tax%}' => $journal_transactions['total_credit_without_tax'],
            '{%journal_currency_code%}' => $journal['currency_code'],
            '{%site_currency_symbol%}' => get_currency_symbol(getCurrentSite('currency_code')),
            '{%journal_currency_rate%}' => (string) round($journal['currency_rate'], 5),
            '{%journal_currency_credit%}' => format_price($journal['currency_credit'], $journal['currency_code']),
            '{%journal_currency_debit%}' => format_price($journal['currency_debit'], $journal['currency_code']),
            '{%journal_transactions_table%}' =>$journal_transactions,
            '{%journal_transactions_table_2%}' => self::getJournalTransactionsTable($journal_transactions),
            '{%journal_transactions_table_without_taxes%}' =>$journal_transactions_without_taxes,
            '{%journal_transactions_table_without_taxes_2%}' => self::getJournalTransactionsTable($journal_transactions_without_taxes),
            '{%has_tax%}' => $hasTax,
            '{%tax_transactions_table%}' => $taxTransactions,
            '{%tax_transactions_table2%}' => self::getJournalTransactionsTable($taxTransactions),
            '{%cost_center_table%}' => self::getCostCenterTable($cost_transactions),
        );

        $placeholders += self::site_place_holder(getCurrentSite());
        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholders += self::branches_place_holder($journal['branch_id'],'journal');
        }
  
        if (isset($journal['id']) && $journal['id'] != '') {
            $placeholders += self::get_additional_fields_place_holders_values(EntityKeyTypesUtil::JOURNAL, $journal['id']);
        }
        return $placeholders;

    }

    static function getCostCenterTable($cost_transactions)
    {
        if(empty($cost_transactions)){
            return "";
        }
        $grouped_transactions = [];
        $costCenterTransaction = ClassRegistry::init('CostCenterTransaction');
        $journalId = $cost_transactions[0]['Journal']['id'];
        foreach($cost_transactions as $k => $cost_transaction)
        {  
            if($costCenterTransaction->applicableForNewMode($journalId)) {
                //group transactions by accounts
                $grouped_transactions[$cost_transaction['CostCenterTransaction']['journal_transaction_id']][] = $cost_transaction;
            } else {
            //group transactions by accounts
                $grouped_transactions[$cost_transaction['CostCenterTransaction']['journal_account_id']][] = $cost_transaction;
            }

        }
        
 
        $view = new View($controller, false);
        return $view->element('cost_center_transactions/table', ['placeholder'=>true,'cost_center_transactions'=>$grouped_transactions]);
    }

    static function getJournalTransactionsTable($journal)
    {

        $return = '<table id="listing_table" class="t_table">
                    <tbody>
                    <tr class="bold" bgcolor="#e5e5e5">
                    <th colspan="2">Account</th>
                    <th>Description</th>
                    <th>Debit</th>
                    <th>Credit</th>
                    </tr>';
        foreach($journal['JournalTransaction'] as $k => $journalTransaction)
        {

            $return .= "<tr>
                    <td>{$journalTransaction['JournalAccount']['code']}</td>
                    <td>{$journalTransaction['JournalAccount']['name']}</td>
                    <td>{$journalTransaction['description']}</td>
                    <td>{$journalTransaction['currency_debit']}</td>
                    <td>{$journalTransaction['currency_credit']}</td>
                    </tr>";
        }
        $return .= '<tr class="bold" bgcolor="#e5e5e5">
                    <th colspan="3">Total</th>
                    <th style="word-wrap: break-word;"><span class="mceNonEditable">'.$journal['Journal']['currency_debit'].'</span></th>
                    <th style="word-wrap: break-word;" colspan="1"><span class="mceNonEditable">'.$journal['Journal']['currency_credit'].'</span></th>
                    </tr>
                    </tbody>
                    </table>' ;

        return $return;
    }



    /* ----------Treasury Placeholders---------- */
    public static function treasury_place_holder_list()
    {
        return array(
            '{%treasury_name%}' => __("name",true),
            '{%treasury_description%}' => __("Description",true),
            '{%treasury_is_primary%}' => __("Is Primary",true),
            '{%treasury_type%}' => __("type",true),
            '{%treasury_type_name%}' => __("type",true),
            '{%treasury_type_account_number%}' => __("type",true),
            '{%treasury_active%}' => __("active",true)
        );
    }

    public static function treasury_place_holder($treasury)
    {
        //replace $category placeholders
        return array(
            '{%treasury_name%}' => $treasury['name'],
            '{%treasury_description%}' => $treasury['description'],
            '{%treasury_is_primary%}' => $treasury['is_primary'],
            '{%treasury_type%}' => $treasury['type'],
            '{%treasury_type_name%}' => $treasury['type_name'],
            '{%treasury_type_account_number%}' => $treasury['type_account_number'],
            '{%treasury_active%}' => $treasury['active']
        );
    }



    public static function receipt_place_holder_list() {
        return array(
            '{%item_name%}' => __("Product name",true),
            '{%barcode_number%}' => __("Barcode number",true),
            '{%barcode_width%}' => __("Barcode width",true)
        );
    }

    public static function receipt_place_holder($barcode)
    {
        $placeholder = array(
            '{%item_name%}' => $barcode['item_name'],
            '{%barcode_number%}' => $barcode['barcode_number'],
            '{%barcode_width%}' => $barcode['barcode_width']
        );

        return $placeholder;
    }

    public static function product_place_holder_list() {
        $placeholders =  array(
            '{%product_name%}' => __("Product name",true),
            '{%product_barcode_image%}' => __('Barcode Image', true),
            '{%product_image%}' => __('Product Image', true),
            '{%product_image_url%}' => __('Product Image Url', true),
            '{%product_description%}' => __("Product description",true),
            '{%product_unit_price%}' => __("Product unit price",true),
            '{%product_unit_price_with_tax%}' => __("Product unit price with taxes",true),
            '{%product_default_quantity%}' => __("Product default quantity",true),
            '{%product_category%}' => __("Product category",true),
            '{%product_buy_price%}' => __("Product buy price",true),
            '{%product_buy_price_with_tax%}' => __("Product buy price with taxes",true),
            '{%product_code%}' => __("Product Code",true),
            '{%product_supplier_code%}' => __("Product supplier code",true),
            '{%product_track_stock%}' => __("Product track stock",true),
            '{%product_stock_balance%}' => __("Product stock balance",true),
            '{%product_low_stock_thershold%}' => __("Product low stock threshold",true),
            '{%product_barcode%}' => __("Product barcode",true),
            '{%product_barcode_number%}' => __("Product barcode number",true),
            '{%product_average_price%}' => __("Product average price",true),
            '{%product_type%}' => __("Product type",true),
            '{%product_brand_name%}' => __("Product brand name",true),
            '{%item_group_name%}' => __("Item group name",true),
            '{%Product-Attribute1-Label%}'=> __("Product-Attribute1-Label",true),
            '{%Product-Attribute2-Label%}'=> __("Product-Attribute2-Label",true),
            '{%Product-Attribute3-Label%}'=> __("Product-Attribute3-Label",true),
            '{%Product-Attribute1-Value%}'=> __("Product-Attribute1-Value",true),
            '{%Product-Attribute2-Value%}'=> __("Product-Attribute2-Value",true),
            '{%Product-Attribute3-Value%}'=> __("Product-Attribute3-Value",true)

        );

        if(ifPluginActive(PRODUCT_TRACKING_PLUGIN)) {
            $placeholders += ['{%tracking_details%}' => __("Product tracking details",true)];
        }

        $placeholders += self::get_custom_place_holders_list('products');

        $placeholders += self::site_place_holder_list();
        $placeholders += self::staff_place_holder_list();
        $placeholders += self::datetime_place_holder_list();


        return $placeholders + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('product') : []);
    }

    public static function product_place_holder($product, $additionalData = [])
    {
        $productModel = GetObjectOrLoadModel('Product');
        if (isset($product['Product'])) {
            $product = $product['Product'];
        }

        $productCategories = $productModel->getProductCategories($product['id']);
        $itemGroup = $productModel->getItemGroup($product['item_group_id']);
        $attributes = $productModel->getAttributes($product['id']);
        $itemGroupName = $itemGroup['ItemGroup']['name']??'';
        $product['category'] = implode(' , ', is_array($productCategories) ? $productCategories : []);

        if(!empty($product['barcode']))
        {
            $barcode_type = settings::getValue(ProductsPlugin, 'barcode_type');



            if(!isset($additionalData['unit']) || $additionalData['unit'] == 0) {
                $barcode_type = settings::getValue(ProductsPlugin, 'barcode_type');
                $src = "https://".getCurrentSite('subdomain')."/barcode.php?type={$barcode_type}&content={$product['barcode']}";
                $image = "<img width=\"90%\" style=\"height:0.8cm;\"  src=\"" . $src . "\" />";
                $product_barcode_number = $product['barcode'];
            } else {
                $itemBarCode = GetObjectOrLoadModel('ItemBarcode');
                $data_fields = $itemBarCode->read(null , $additionalData['unit']);
                $product_barcode_number = $data_fields['ItemBarcode']['barcode'];
                $barcode_type = settings::getValue(ProductsPlugin, 'barcode_type');
                $src = "https://".getCurrentSite('subdomain')."/barcode.php?type={$barcode_type}&content={$product_barcode_number}";
                $image = "<img width=\"90%\" style=\"height:0.8cm;\"  src=\"" . $src . "\" />";
            }

        }
        else
        {
            $image  = "";
            $product_barcode_number = "";
        }
//        Configure::write("debug",2);
//        dd($im);



//        Configure::write("debug",2);
//        dd($image );
        $taxModel= GetObjectOrLoadModel('Tax');
        $ProductImage= GetObjectOrLoadModel('ProductImage');
        $tax1_value = $tax2_value = 0;
        if(!empty($product['tax1'])){
            $tax_row = $taxModel->find('first',['recursive'=>-1,'conditions'=>['id'=>$product['tax1']]]);
            if($tax_row['Tax']['included']!=1) {
                $tax1_value = $tax_row['Tax']['value'];
            }
        }
        if(!empty($product['tax2'])){
            $tax_row = $taxModel->find('first',['recursive'=>-1,'conditions'=>['id'=>$product['tax2']]]);
            if($tax_row['Tax']['included']!=1) {
                $tax2_value = $tax_row['Tax']['value'];
            }
        }
        $unit_price_tax_values = ($tax1_value*$product['unit_price']/100) + ($tax2_value*$product['unit_price']/100) ;
        $buy_price_tax_values = ($tax1_value*$product['buy_price']/100) + ($tax2_value*$product['buy_price']/100) ;
        $ProductImageRow=$ProductImage->find('first',['conditions'=>['ProductImage.product_id'=>$product['id'],'ProductImage.default'=>1]]);
        $masterImage = resolve(AttachmentsService::class)->getDefault('product' ,$product['id']);

        $productMasterImage = '';
        if(!empty($ProductImageRow['ProductImage']['file_full_path'])){
            $productMasterImage = ("https://".getCurrentSite('subdomain').'/'.$ProductImageRow['ProductImage']['file_full_path']);
        }elseif(isset($masterImage[0]->files)){
            $productMasterImage = ($masterImage[0]->files->path);
        }
        $product_master_image = "<img   src=\"" . $productMasterImage . "\" />";
        $placeholder = array(
            '{%product_name%}' => $product['name'],
            '{%product_barcode_image%}' => $image ,
            '{%product_image%}' => $product_master_image ,
            '{%product_image_url%}' => ($productMasterImage) ,
            '{%product_description%}' => $product['description'],
            '{%product_unit_price%}' => format_price($product['unit_price'],getCurrentSite('currency_code')),
            '{%product_unit_price_with_tax%}' => format_price($product['unit_price']+$unit_price_tax_values,getCurrentSite('currency_code')),
            '{%product_default_quantity%}' => $product['default_quantity'],
            '{%product_category%}' => $product['category'],
            '{%product_buy_price%}' => $product['buy_price'],
            '{%product_buy_price_with_tax%}' => format_price($product['buy_price']+$buy_price_tax_values,getCurrentSite('currency_code')),
            '{%product_code%}' => $product['product_code'],
            '{%product_supplier_code%}' => $product['supplier_code'],
            '{%product_track_stock%}' => $product['track_stock'],
            '{%product_stock_balance%}' => $product['stock_balance'],
            '{%product_low_stock_thershold%}' => $product['low_stock_thershold'],
            '{%product_barcode%}' => $product['barcode'],
            '{%product_barcode_number%}' => $product_barcode_number,
            '{%product_average_price%}' => $product['average_price'],
            '{%product_type%}' => $product['type'],
            '{%product_brand_name%}' => $product['brand'],
            '{%img/dummy_barcode.jpg%}' => $file,
            '{%product_barcode_image_link%}' => "https://".getCurrentSite('subdomain')."/barcode.php?type={$barcode_type}&content={$product_barcode_number}",
            '{%tracking_details%}' => self::tracking_data_placeholder_generator($product),
            '{%item_group_name%}' => $itemGroupName,
            '{%Product-Attribute1-Label%}'=> $attributes[0]['ProductAttributeOption']['attribute']?? '',
            '{%Product-Attribute2-Label%}'=> $attributes[1]['ProductAttributeOption']['attribute']?? '',
            '{%Product-Attribute3-Label%}'=> $attributes[2]['ProductAttributeOption']['attribute']?? '',
            '{%Product-Attribute1-Value%}'=> $attributes[0]['ProductAttributeOption']['option']?? '',
            '{%Product-Attribute2-Value%}'=> $attributes[1]['ProductAttributeOption']['option']?? '',
            '{%Product-Attribute3-Value%}'=> $attributes[2]['ProductAttributeOption']['option']?? '',

        );
        $placeholder  +=self::get_custom_place_holders('products',$product['id']);
        $placeholder += self::site_place_holder(getCurrentSite());
        $placeholder += self::staff_place_holder($product['staff_id']);

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholder += self::branches_place_holder($product['branch_id'],'product');
        }

        $placeholder +=self::datetime_place_holder();
        return $placeholder;
    }

    public static function tracking_data_placeholder_generator($product) {
        
        switch($product['tracking_type']){
            case TrackStockUtil::TYPE_SERIAL:
            case TrackStockUtil::TYPE_LOT:       
            case TrackStockUtil::TYPE_EXPIRY:      
                return $product['tracking_data'];
                break;
            case TrackStockUtil::TYPE_LOT_EXPIRY:   
                return ''.__('Lot', true).' : '.$product['tracking_data'][0]. ' '.__('Expiry', true).' : '. $product['tracking_data'][1];
                break;
        }

    }


    public static function barcode_place_holder_generator($barcode)
    {
        $productModel = GetObjectOrLoadModel('Product');
        if (isset($barcode['ItemBarcode']['product_id'])) {
            $product_id = $barcode['ItemBarcode']['product_id'];
            $product = $productModel->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $product_id]])['Product'];
        }

        $unitTemplate = GetObjectOrLoadModel('UnitTemplate');
        $unitFactor = GetObjectOrLoadModel('UnitFactor');
        $factors = $unitFactor->find('all' , ['recursive' => -1]);
        $factors_list = []  ;
    
        foreach ( $factors as $k => $f ) {
            $factors_list[$f['UnitFactor']['unit_template_id']][$f['UnitFactor']['id']] = $f['UnitFactor'];
        }
        $unit_templates = $unitTemplate->find('all' , ['recursive' => -1 ]);
        foreach ( $unit_templates as  $t ){
            $factors_list[$t['UnitTemplate']['id']][0] = ['id' => 0 , 'factor_name' => $t['UnitTemplate']['main_unit_name'], 'factor' => 1 ];
        }

        $unit_price = round($barcode['ItemBarcode']['unit_price'] * $factors_list[$product['unit_template_id']][$barcode['ItemBarcode']['unit_factor_id']]['factor'], 6);

        $productCategories = $productModel->getProductCategories($product['id']);

        $productCategories = is_array($productCategories) ? $productCategories : [];

        $product['category'] = implode(' , ', $productCategories);
   
        if(!empty($barcode['ItemBarcode']['barcode']))
        {
            $barcode_type = settings::getValue(ProductsPlugin, 'barcode_type');
            $src = "https://".getCurrentSite('subdomain')."/barcode.php?type={$barcode_type}&content={$barcode['ItemBarcode']['barcode']}";
            $image = "<img width=\"90%\" style=\"height:0.8cm;\"  src=\"" . $src . "\" />";
            $product_barcode_number = $barcode['ItemBarcode']['barcode'];
        }
        else
        {
            $image  = "";
            $product_barcode_number = "";
        }
//        Configure::write("debug",2);
//        dd($im);



//        Configure::write("debug",2);
//        dd($image );
        $taxModel= GetObjectOrLoadModel('Tax');
        $ProductImage= GetObjectOrLoadModel('ProductImage');
        $tax1_value = $tax2_value = 0;
        if(!empty($product['tax1'])){
            $tax_row = $taxModel->find('first',['recursive'=>-1,'conditions'=>['id'=>$product['tax1']]]);
            if($tax_row['Tax']['included']!=1) {
                $tax1_value = $tax_row['Tax']['value'];
            }
        }
        if(!empty($product['tax2'])){
            $tax_row = $taxModel->find('first',['recursive'=>-1,'conditions'=>['id'=>$product['tax2']]]);
            if($tax_row['Tax']['included']!=1) {
                $tax2_value = $tax_row['Tax']['value'];
            }
        }
        $unit_price_tax_values = ($tax1_value*$unit_price/100) + ($tax2_value*$unit_price/100) ;
        $buy_price_tax_values = ($tax1_value*$product['buy_price']/100) + ($tax2_value*$product['buy_price']/100) ;
        $ProductImageRow=$ProductImage->find('first',['conditions'=>['ProductImage.product_id'=>$product['id'],'ProductImage.default'=>1]]);
        $product_master_image = "<img   src=\"" . ("https://".getCurrentSite('subdomain').'/'.$ProductImageRow['ProductImage']['file_full_path']) . "\" />";
        $placeholder = array(
            '{%product_name%}' => $product['name'],
            '{%product_barcode_image%}' => $image ,
            '{%product_image%}' => $product_master_image ,
            '{%product_image_url%}' => ("https://".getCurrentSite('subdomain').'/'.$ProductImageRow['ProductImage']['file_full_path']) ,
            '{%product_description%}' => $product['description'],
            '{%product_unit_price%}' => format_price(round($barcode['ItemBarcode']['unit_price'] * $factors_list[$product['unit_template_id']][$barcode['ItemBarcode']['unit_factor_id']]['factor'], 6),getCurrentSite('currency_code')),
            '{%product_unit_price_with_tax%}' => format_price($unit_price+$unit_price_tax_values,getCurrentSite('currency_code')),
            '{%product_default_quantity%}' => $product['default_quantity'],
            '{%product_category%}' => $product['category'],
            '{%product_buy_price%}' => $product['buy_price'],
            '{%product_buy_price_with_tax%}' => format_price($product['buy_price']+$buy_price_tax_values,getCurrentSite('currency_code')),
            '{%product_code%}' => $product['product_code'],
            '{%product_supplier_code%}' => $product['supplier_code'],
            '{%product_track_stock%}' => $product['track_stock'],
            '{%product_stock_balance%}' => $product['stock_balance'],
            '{%product_low_stock_thershold%}' => $product['low_stock_thershold'],
            '{%product_barcode%}' => $product_barcode_number,
            '{%product_barcode_number%}' => $product_barcode_number,
            '{%product_average_price%}' => $product['average_price'],
            '{%product_type%}' => $product['type'],
            '{%product_brand_name%}' => $product['brand'],
            '{%product_barcode_image_link%}' => $src
        );

        $placeholder += self::site_place_holder(getCurrentSite());
        $placeholder += self::staff_place_holder($product['staff_id']);

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholder += self::branches_place_holder($product['branch_id'],'product');
        }

        return $placeholder;
    }

    public static function prescription_place_holder_list()
    {
        $placeholders = array(
            '{%prescription_date%}' => __("Prescription Date",true),
            '{%medications_table%}' => __("Medications Table", true)
        );

        $placeholders += self::staff_place_holder_list();
        $placeholders += self::client_place_holder_list();

        return $placeholders ;
    }

    public static function prescription_place_holder($prescription)
    {
        $medications = $prescription['Medication'];

        $controller = new InvoicesController();

        $view = new View($controller, false);
        $medications_table = $view->element('printable_templates_prescription_medications_table', ['medications'=>$medications]);

        $prescription = $prescription["Prescription"];
        $placeholder = array(
            '{%prescription_date%}' => $prescription['date'],
            '{%medications_table%}' => $medications_table
        );

        $placeholder += self::staff_place_holder($prescription['staff_id']);
        $placeholder += self::client_place_holder($prescription['client_id']);
        return $placeholder;
    }

    public static function invoice_get_all_place_holders($invoice) {
        $array = array();
        $array += self::site_place_holder(getCurrentSite());
        $array += self::invoice_place_holder($invoice);

        $array += self::client_custom_place_holder($invoice['client_id']);
        $array += self::staff_place_holder($invoice['staff_id']??$invoice['Invoice']['staff_id']);
 
        return $array;
    }
    public static function invoice_get_all_place_holder_list() {
        $array = array();
        $array += self::site_place_holder_list();
        $array += self::invoice_place_holder_list();
        $array += self::client_place_holder_list();
        $array += self::staff_place_holder_list();
        return $array;
    }
    public static function purchaseorders_get_all_place_holders($po) {

        $array = array();
        $array += self::site_place_holder(getCurrentSite());
        $array += self::purchaseorders_place_holder($po);
        $array += self::purchaseorders_custom_field_place_holder($po);
        $array += self::supplier_place_holder($po['Supplier']);
        $array += self::staff_place_holder($po['staff_id']);
        return $array;
    }

    public static function estimate_get_all_place_holders($estimate) {
        $array = array();
        $array += self::site_place_holder(getCurrentSite());
        $array += self::estimate_place_holder($estimate);
        $array += self::client_custom_place_holder($estimate['client_id']);
        $array += self::staff_place_holder($estimate['staff_id']);
        return $array;
    }

    public static function sales_order_get_all_place_holders($salesOrder) {
        $array = array();
        $array += self::site_place_holder(getCurrentSite());
        $array += self::sales_order_place_holder($salesOrder);
        $array += self::client_custom_place_holder($salesOrder['client_id']);
        $array += self::staff_place_holder($salesOrder['staff_id']);
        return $array;
    }

    public static function client_get_all_place_holders($client) {
        $array = array();
        $array += self::site_place_holder(getCurrentSite());
        $array += self::client_place_holder($client);
        $array += self::staff_place_holder($client['staff_id']);
        return $array;
    }

    static function convertNumberToWord($num = false)
    {
        $num = str_replace(array(',', ' '), '' , trim($num));
        if(! $num) {
            return false;
        }
        $num = (int) $num;
        $words = array();
        $list1 = array('', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'eleven',
            'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'
        );
        $list2 = array('', 'ten', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety', 'hundred');
        $list3 = array('', 'thousand', 'million', 'billion', 'trillion', 'quadrillion', 'quintillion', 'sextillion', 'septillion',
            'octillion', 'nonillion', 'decillion', 'undecillion', 'duodecillion', 'tredecillion', 'quattuordecillion',
            'quindecillion', 'sexdecillion', 'septendecillion', 'octodecillion', 'novemdecillion', 'vigintillion'
        );
        $num_length = strlen($num);
        $levels = (int) (($num_length + 2) / 3);
        $max_length = $levels * 3;
        $num = substr('00' . $num, -$max_length);
        $num_levels = str_split($num, 3);
        for ($i = 0; $i < count($num_levels); $i++) {
            $levels--;
            $hundreds = (int) ($num_levels[$i] / 100);
            $hundreds = ($hundreds ? ' ' . $list1[$hundreds] . ' hundred' . ' ' : '');
            $tens = (int) ($num_levels[$i] % 100);
            $singles = '';
            if ( $tens < 20 ) {
                $tens = ($tens ? ' ' . $list1[$tens] . ' ' : '' );
            } else {
                $tens = (int)($tens / 10);
                $tens = ' ' . $list2[$tens] . ' ';
                $singles = (int) ($num_levels[$i] % 10);
                $singles = ' ' . $list1[$singles] . ' ';
            }
            $words[] = $hundreds . $tens . $singles . ( ( $levels && ( int ) ( $num_levels[$i] ) ) ? ' ' . $list3[$levels] . ' ' : '' );
        } //end for loop
        $commas = count($words);
        if ($commas > 1) {
            $commas = $commas - 1;
        }
        return implode(' ', $words);
    }

    function extractFieldFromPlaceHolder($placeholder)
    {
        preg_match_all('/{%(.*?)%}/',$placeholder, $matches, PREG_SET_ORDER, 0);
        return $matches[0][1];
    }

    public static function testingg() {
        return array(
            '{%value%}' => "Ahmed Khaled",
            '{%values%}' => [["name"=>'ahmed'],["name"=>'mohamed'],["name"=>'ali']]
        );
    }

    /**
     * this function will get your the arabic and english Tafket for any number
     * TODO handle the change for the english Tafket as in arabic
     * @param $floatNumber => the number you want to get it Tafketed
     * @param $currency_code => the currency code that the floatNumber wanted to be tafketed on
     * @param string $site => default value is daftra the site it will check to process with the function
     * @return string => numbe tafketed with the change
     */
    public static function tafketWithChange($floatNumber, $currency_code, $site='daftra.com')
    {

        App::import('Vendor', 'Tafket', array('file' => 'Tafket/Arabic.php'));
       $Currency = ClassRegistry::init('Currency', 'Model');

        //get the tafketed value of amount
        if( ($site == 'daftra.com' || $site='daftra.local') && CurrentSiteLang() == 'ara' )
        {
            if(
                $currency_code == "KWD" ||
                $currency_code == 'BHD' ||
                $currency_code == 'IQD' ||
                $currency_code == 'LYD' ||
                $currency_code == 'OMR' ||
                $currency_code == "JOD"
            )
            {
                $precision=3;
                $number = explode(".", round($floatNumber,3))[1];
            }
            else{
                $precision=2;
                $number = explode(".", round($floatNumber,2))[1];
            }

            $obj = new I18N_Arabic('Numbers');
            $currency = getCurrenciesList(['code'=>$currency_code],2)[$currency_code];
            $numberValue = intval(round($floatNumber,$precision));
            if (($numberValue > 2 && $numberValue < 11) ) {
                $currency = getCurrencyArabicPlural($currency);
            }
            $amount_Tafketed = $obj->int2str($numberValue). " " . $currency;
            if(intval(explode(".", $floatNumber)[1]) > 0)
            {

                $digits = floor(log($number , 10) + 1);
                $actual_digits = strlen($number);

                if(
                    $currency_code == "KWD" ||
                    $currency_code == 'BHD' ||
                    $currency_code == 'IQD' ||
                    $currency_code == 'LYD' ||
                    $currency_code == 'OMR' ||
                    $currency_code == 'JOD'
                )
                {
                    if($actual_digits == 1)
                    {
                        $number.="00";
                    }
                    else if($actual_digits == 2)
                    {
                        $number.="0";
                    }
                    else if($actual_digits == 3)
                    {
                        $number.="";
                    }

                    $number = intval($number);
                }
                else
                {
                    $number .= ( $actual_digits == 1 ? "0" : '');
                    $number = intval($number);
                }

                $amount_Tafketed .= " ". __("And",true) . " " . $obj->int2str($number) . " " .getCurrencyMinorUnitList(['code'=>$currency_code],2)[$currency_code];
            }
        }
        else
        {
            if(
                $currency_code == "KWD" ||
                $currency_code == 'BHD' ||
                $currency_code == 'IQD' ||
                $currency_code == 'LYD' ||
                $currency_code == 'OMR' ||
                $currency_code == "JOD"
            )
            {
                $precision=3;
                $number = explode(".", round($floatNumber,3))[1] ?? null;
            }
            else{
                $precision=2;
                $number = explode(".", round($floatNumber,2))[1] ?? null;
            }

            $currency = getCurrenciesList(['code'=>$currency_code],2)[$currency_code];
            if (intval(round($floatNumber,$precision)) > 2) {
                $currency = getCurrencyEnglishPlural($currency);
            }
            $amount_Tafketed = self::convertNumberToWord(round($floatNumber,$precision)) . " " . $currency;
            if(intval(explode(".", $floatNumber)[1]) > 0)
            {
                $digits = floor(log($number , 10) + 1);
                $actual_digits = strlen($number);
                if(
                    $currency_code == "KWD" ||
                    $currency_code == 'BHD' ||
                    $currency_code == 'IQD' ||
                    $currency_code == 'LYD' ||
                    $currency_code == 'OMR' ||
                    $currency_code == 'JOD'
                )
                {
                    if($actual_digits == 1)
                    {
                        $number.="00";
                    }
                    else if($actual_digits == 2)
                    {
                        $number.="0";
                    }
                    else if($actual_digits == 3)
                    {
                        $number.="";
                    }

                    $number = intval($number);
                }
                else
                {
                    $number .= ( $actual_digits == 1 ? "0" : '');
                    $number = intval($number);
                }

                $amount_Tafketed .= " ". __("And",true) . " " . self::convertNumberToWord(round($number,$precision)) . " " .getCurrencyMinorUnitList(['code'=>$currency_code],2)[$currency_code];
            }
        }


        return $amount_Tafketed;
    }
    static function appointment_place_holder ( $appointment , $staff_id = null ) {
        return self::appointments_place_holder($appointment);
//        $FollowUpReminder = ClassRegistry::init ('FollowUpReminder' , 'Model');
//        if ( !is_array ( $appointment ) ){
//           $appointment =  $FollowUpReminder->find ( 'first' , ['conditions' =>['FollowUpReminder.id' =>$appointment] ]);
//        }
//        $entityModelName = $entityModel = $placeHolderFunctionName = '';
//        switch ($appointment['FollowUpReminder']['item_type']){
//            case FollowUpReminder::CLIENT_TYPE:
//                $entityModel = ClassRegistry::init ('Client','Model');
//                $entityModelName = 'Client';
//                $placeHolderFunctionName = 'client_place_holder';
//                break;
//            case FollowUpReminder::INVOICE_TYPE:
//                $entityModel = ClassRegistry::init ('Invoice','Model');
//                $entityModelName = 'Invoice';
//                $placeHolderFunctionName = 'invoice_place_holder';
//                break;
//            case FollowUpReminder::ESTIMATE_TYPE:
//                $entityModel = ClassRegistry::init ('Invoice','Model');
//                $entityModelName = 'Invoice';
//                $placeHolderFunctionName = 'estimate_place_holder';
//                break;
//            case FollowUpReminder::PO_TYPE:
//                $entityModel = ClassRegistry::init ('PurchaseOrder','Model');
//                $entityModelName = 'PurchaseOrder';
//                $placeHolderFunctionName = 'purchaseorders_place_holder';
//                break;
//            case FollowUpReminder::STAFF_TYPE:
//                $entityModel = ClassRegistry::init ('Staff','Model');
//                $entityModelName = 'Staff';
//                $placeHolderFunctionName = 'staff_place_holder';
//                break;
//            case FollowUpReminder::SUPPLIER_TYPE:
//                $entityModel = ClassRegistry::init ('Supplier','Model');
//                $entityModelName = 'Supplier';
//                $placeHolderFunctionName = 'supplier_place_holder';
//                break;
//            case FollowUpReminder::PRODUCT_TYPE:
//                $entityModel = ClassRegistry::init ('Product','Model');
//                $entityModelName = 'Product';
//                $placeHolderFunctionName = 'product_place_holder';
//                break;
//            case FollowUpReminder::WORK_ORDER_TYPE:
//                $entityModel = ClassRegistry::init ('WorkOrder','Model');
//                $entityModelName = 'WorkOrder';
//                $placeHolderFunctionName = 'work_order_place_holder';
//                break;
//        }
//        $itemPlaceHolders = [];
//        if ($entityModelName) {
//            $item = $entityModel->find('first', ['conditions' => [$entityModelName . '.id' => $appointment['FollowUpReminder']['item_id']]]);
//            $itemPlaceHolders = self::{$placeHolderFunctionName}($item);
//        }
//        $Post = ClassRegistry::init ('Post' , 'Model');
//
//        $number = $FollowUpReminder->get_type_number ($appointment);
//        $FollowUpAction = ClassRegistry::init ('FollowUpAction' , 'Model');
//        $action = $FollowUpAction->find ( 'first' , ['conditions'=> ['FollowUpAction.id' => $appointment['FollowUpReminder']['action_id']]]);
//
//        $partner = $FollowUpReminder->get_type_contact ($appointment ,false);
//        $staffs = $FollowUpReminder->get_type_staff_contacts ( $appointment['FollowUpReminder']['id'] );
//        foreach ($staffs as &$staff) {
//            if(!empty($staff)&&!empty($staff['phone'])) {
//                $phones = $staff['phone'];
//                $staff['phone'] = [];
//                foreach ($phones as $phone) {
//                    if(!empty($phone)) {
//                        $staff['phone'][] = $phone;
//                    }
//                }
//                $staff['phone'] = implode (',',$staff['phone']);
//            }
//        }
//
//        //Branches
//        $branchesPlaceholders = [];
//        if (ifPluginInstalled(BranchesPlugin)) {
//            $branchesPlaceholders = self::branches_place_holder($appointment['FollowUpReminder']['branch_id'],'appointment');
//        }
//
//        return [
//            '{%assigned_staff%}' => implode (',',array_column ( $staffs , 'name')),
//            '{%assigned_staff_mobile%}' => implode (',',array_column ( $staffs , 'phone')),
//            '{%partner_name%}' => $partner['name'],
//            '{%partner_address%}' => $partner['address'],
//            '{%item_number%}' => $number,
//            '{%item_id%}' => $appointment['FollowUpReminder']['item_id'],
//            '{%item_type%}' => $appointment['FollowUpReminder']['item_type'],
//            '{%item_type_name%}' => Post::getItemTypes()[$appointment['FollowUpReminder']['item_type']]['name'],
//            '{%appointment_date%}' => format_date ( $appointment['FollowUpReminder']['date'] ) ,
//            '{%appointment_body%}' => $appointment['FollowUpReminder']['body'],
//            '{%appointment_action%}' => $action['FollowUpAction']['name'],
//        ] + $itemPlaceHolders + self::appointments_place_holder($appointment) + $branchesPlaceholders;
    }
    static function appointment_place_holder_list (  ) {
        return self::appointments_place_holder_list();
//        return array(
//            '{%assigned_staff%}' => __("Assigned Staff Name",true),
//            '{%assigned_staff_mobile%}' => __("Assigned Staff Mobile",true),
//            '{%partner_name%}' => __("Partner name",true),
//            '{%partner_address%}' => __("Partner address",true),
//            '{%item_number%}' => __("Item number",true),
//            '{%item_id%}' => __("Item Id",true),
//            '{%item_type_name%}' => __("Item Type name",true),
//            '{%appointment_date%}' => __("Appointment Date",true),
//            '{%appointment_body%}' => __("Appointment Body",true),
//            '{%appointment_action%}' => __("Appointment Action",true),
//        ) + self::appointments_place_holder_list() + (ifPluginInstalled(BranchesPlugin) ? self::branches_place_holder_list('appointment') : []);

    }

    /**
     * @param $image_link => the src of the image tag
     * @param array $options => customized style for the image
     * @return string => the image tag after renderring with the parameters
     */
    public static function getImageTag($image_link , $options=array())
    {
        $src = "https://".getCurrentSite('subdomain').'/' . ( empty($image_link) ? "css/images/businessman.png" : $image_link);
        return self::getImageHtmlTag($src, $options);
    }

    public static function getImageHtmlTag($src, $options = [])
    {
        $opt = '';
        foreach ($options as $key => $value) {
            if ($key != 'style') {
                continue;
            }
            $opt .= 'style="';
            foreach ($value as $attr => $val) {
                $opt .= $attr . ":" . $val . ';';
            }
            $opt .= '"';
        }
        return "<img " . $opt . " src=\"" . $src . "\" />";
    }


    /**
     * @param $lat => map latitude
     * @param $lng => map longitude
     * @param $zoom => map zoom
     * @param $width => map width
     * @param $height => map height
     * @param array $options => style of the image tag
     * @return string => return the image tag after rendering from the link and the params
     */
    public static function getMapAsImage( $lat, $lng, $zoom , $width, $height, $options=array())
    {
        $src = "https://maps.googleapis.com/maps/api/staticmap?center=".$lat.",".$lng."&markers=color:red%7C".$lat.",".$lng."&zoom=".$zoom ."&size=".$width."x".$height;

        $opt='';
        foreach( $options as $key => $value )
        {
            if( $key == 'style' )
            {
                $opt .= 'style="';
                foreach($value as $attr => $val)
                {
                    $opt .= $attr . ":" . $val . ';';
                }
                $opt .= '"';
            }
        }


        $image = "<img " . $opt . " src=\"" . $src . "\" />";

        return $image;

    }
    static function requisition_place_holder($requisition = [])
    {
        $requisitionModel = GetObjectOrLoadModel('Requisition');
        $journalAccountModel = GetObjectOrLoadModel('JournalAccount');
        $storeModel = GetObjectOrLoadModel('Store');
        $staffModel = GetObjectOrLoadModel('Staff');
        $rqItem = GetObjectOrLoadModel('RequisitionItem');

        $invoice_item_qty= $rqItem->find('all',array('fields'=>'sum(`RequisitionItem`.`quantity`) as qty','conditions'=>array('requisition_id'=>$requisition['Requisition']['id']))) ;

        $allStores = $storeModel->find('list') ;

        if (!empty($requisition)) {
            $placeholder = [
                '{%requisition_number%}' => '#' . $requisition['Requisition']['number'],
                '{%requisition_date%}' => format_date($requisition['Requisition']['date']),
                '{%requisition_store%}' => $allStores[$requisition['Requisition']['store_id']],
                '{%requisition_to_store%}' => $allStores[$requisition['Requisition']['to_store_id']],
                '{%requisition_status%}' => __(Requisition::$requisition_statuses [$requisition['Requisition']['status']]['label'], true),
                '{%requisition_type%}' => __(Requisition::$requisition_types[$requisition['Requisition']['type']]['label'], true),
                '{%requisition_order_type%}' => __(Requisition::$requisition_order_types[$requisition['Requisition']['order_type']]['label'], true),
                '{%requisition_journal_account%}' => $journalAccountModel->find('first', ['recursive' => -1, 'conditions' => ['id' => $requisition['Requisition']['journal_account_id']]])['JournalAccount']['name'],
                '{%requisition_product_list%}' => $requisitionModel->productListHtml($requisition['Requisition']['id']),
                '{%requisition_order_number%}' => '#' . $requisition['Requisition']['order_number'],
                '{%requisition_notes%}' => nl2br($requisition['Requisition']['notes']),
                '{%requisition_staff%}' => $staffModel->getList(1)[$requisition['Requisition']['staff_id']],
                '{%requisition_item_count%}' => $rqItem->find('count', array('conditions' => array('requisition_id' => $requisition['Requisition']['id']))),
                '{%requisition_item_qty%}' => $invoice_item_qty[0][0]['qty'],
            ];
        } else {
            $placeholder = [
                '{%requisition_number%}' => null,
                '{%requisition_date%}' => null,
                '{%requisition_store%}' => null,
                '{%requisition_to_store%}' => null,
                '{%requisition_status%}' => null,
                '{%requisition_type%}' => null,
                '{%requisition_order_type%}' => null,
                '{%requisition_journal_account%}' => null,
                '{%requisition_product_list%}' => null,
                '{%requisition_order_number%}' => null,
                '{%requisition_notes%}' => null,
                '{%requisition_staff%}' => null,
                '{%requisition_item_count%}' => null,
                '{%requisition_item_qty%}' => null,
            ];
        }

        //Branches
        if (ifPluginInstalled(BranchesPlugin) && isset($requisition['Requisition']['branch_id'])) {
            $placeholder += self::branches_place_holder($requisition['Requisition']['branch_id'],'requisition');
        }
        if ($requisition['Requisition']['order_type']==$requisitionModel::ORDER_TYPE_INVOICE) {
            $InvoiceM = GetObjectOrLoadModel('Invoice');
            $invoice=$InvoiceM->getInvoice($requisition['Requisition']['order_id']);
            $placeholder +=self::invoice_place_holder($invoice);
            $placeholder +=self::client_place_holder($invoice['Client']);
        }elseif (!empty($requisition['Client'])) {
            $placeholder +=self::client_place_holder($requisition['Client']);
        }
        return $placeholder ;
    }
    static function requisition_place_holder_list(){
        $placeholder = [
          '{%requisition_number%}' => __("Requisition Number" , true)  ,
          '{%requisition_date%}' => __("Requisition Date" , true)  ,
          '{%requisition_store%}' => __("Requisition Store" , true)  ,
          '{%requisition_to_store%}' => __("Requisition To Store" , true)  ,
          '{%requisition_status%}' => __("Requisition Status" , true)  ,
          '{%requisition_type%}' => __("Requisition Type" , true)  ,
          '{%requisition_order_type%}' => __("Requisition Order Type" , true)  ,
          '{%requisition_journal_account%}' => __("Requisition Journal Account" , true)  ,
          '{%requisition_product_list%}' => __("Requisition Product List" , true)  ,
          '{%requisition_order_number%}' => __("Order Number" , true)  ,
            '{%requisition_item_count%}' => __('Requisition Item Count',true),
            '{%requisition_item_qty%}' => __('Requisition Quantity count',true),
          '{%requisition_notes%}' => __("Notes" , true)  ,
          '{%requisition_staff%}' => __("Staff" , true)  ,
        ];

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholder += self::branches_place_holder_list('requisition');
        }

        return $placeholder ;
    }
    static function stocktaking_place_holder($stocktaking)
    {
        $stocktakingModel = GetObjectOrLoadModel('Stocktaking');
        $storeModel = GetObjectOrLoadModel('Store');
        $staffModel = GetObjectOrLoadModel('Staff');
        $allStores = $storeModel->find('list') ;
        $placeholder = [
          '{%stocktaking_number%}' => '#'.$stocktaking['Stocktaking']['number'] ,
          '{%stocktaking_date%}' => format_date($stocktaking['Stocktaking']['date']) ,
          '{%stocktaking_store%}' => $allStores[$stocktaking['Stocktaking']['store_id']]  ,
          '{%stocktaking_status%}' => __(Stocktaking::$statuses [$stocktaking['Stocktaking']['status']]['label'],true)  ,
          '{%stocktaking_product_decrease_list%}' =>  $stocktakingModel->productListHtml($stocktaking['Stocktaking']['id'],'decrease'),
          '{%stocktaking_product_increase_list%}' => $stocktakingModel->productListHtml($stocktaking['Stocktaking']['id'],'increase'),
          '{%stocktaking_product_all_list%}' => $stocktakingModel->productListHtml($stocktaking['Stocktaking']['id']),
          '{%stocktaking_notes%}' => nl2br($stocktaking['Stocktaking']['notes']),
          '{%stocktaking_staff%}' => $staffModel->getList(1)[$stocktaking['Stocktaking']['staff_id'] ],
        ];

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholder += self::branches_place_holder($stocktaking['Stocktaking']['branch_id'],'stocktaking');
        }

        return $placeholder ;
    }
    static function stocktaking_place_holder_list(){
        $placeholder = [
          '{%stocktaking_number%}' => __("Number" , true)  ,
          '{%stocktaking_date%}' => __("Date" , true)  ,
          '{%stocktaking_store%}' => __("Store" , true)  ,
          '{%stocktaking_status%}' => __("Status" , true)  ,
          '{%stocktaking_product_decrease_list%}' => __("Products Decrease List" , true)  ,
          '{%stocktaking_product_increase_list%}' => __("Products Increase List" , true)  ,
          '{%stocktaking_product_all_list%}' => __("All Products List" , true)  ,
          '{%stocktaking_notes%}' => __("Notes" , true)  ,
          '{%stocktaking_staff%}' => __("Staff" , true)
        ];

        //Branches
        if (ifPluginInstalled(BranchesPlugin)) {
            $placeholder += self::branches_place_holder_list('stocktaking');
        }

        return $placeholder ;
    }

    static function branches_place_holder_list($prefix = "") {
        $prefix = $prefix ? $prefix."_" : '';
        $placeholder = [
            "{%{$prefix}branch_id%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch ID" , true),
            "{%{$prefix}branch_code%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch Code" , true),
            "{%{$prefix}branch_name%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch Name" , true),
            "{%{$prefix}branch_phone1%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch Phone1" , true),
            "{%{$prefix}branch_phone2%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch Phone2" , true),
            "{%{$prefix}branch_address1%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch Address1" , true),
            "{%{$prefix}branch_address2%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch Address2" , true),
            "{%{$prefix}branch_city%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch City" , true),
            "{%{$prefix}branch_state%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch State" , true),
            "{%{$prefix}branch_country%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch Country" , true),
            "{%{$prefix}branch_map_location%}" => __(Inflector::humanize($prefix),true) . ' - ' . __("Branch Location" , true),
        ];
        return $placeholder ;
    }

    public static function branches_place_holder($branch_id, $prefix = "") {
        if (!ifPluginInstalled(BranchesPlugin)) {
            return [];
        }
        $BranchesModel = GetObjectOrLoadModel('Branch');
        $branch = $BranchesModel->find('first',['conditions' => ['Branch.id' => $branch_id]])['Branch'] ?? null;
        $location = !is_null($branch) ? explode(',',$branch['map_location']) : [];
        $prefix = $prefix ? $prefix."_" : '';
        // warning suppress
        if (!$branch) {
            $branch = [
                'id' => null,
                'code' => null,
                'name' => null,
                'phone1' => null,
                'phone2' => null,
                'address1' => null,
                'address2' => null,
                'city' => null,
                'state' => null,
                'country_code' => null,
            ];
        }



        $placeholder = [
            "{%{$prefix}branch_id%}" => $branch['id'],
            "{%{$prefix}branch_code%}" => '#' . $branch['code'],
            "{%{$prefix}branch_name%}" => $branch['name'],
            "{%{$prefix}branch_phone1%}" => $branch['phone1'],
            "{%{$prefix}branch_phone2%}" => $branch['phone2'],
            "{%{$prefix}branch_address1%}" => $branch['address1'],
            "{%{$prefix}branch_address2%}" => $branch['address2'],
            "{%{$prefix}branch_city%}" => $branch['city'],
            "{%{$prefix}branch_state%}" => $branch['state'],
            "{%{$prefix}branch_country%}" => get_country_code($branch['country_code']),
            "{%{$prefix}branch_map_location%}" => $location && count($location) >= 3 ? self::getMapAsImage($location[1],$location[0],$location[2], 600 , 400 ) : '',
            "{%{$prefix}alter_branch_business_name%}" => settings::getValue(0, 'branch_business_name_'.$branch_id),
            "{%{$prefix}alter_branch_bn1%}" => settings::getValue(0, 'branch_bn1_'.$branch_id),
        ];


        return $placeholder ;
    }

    /**
     * This function to add Custom model to the current placeholder attributes
     */
    public static function invoice_custom_field_value_adder($invoice) {
        $placeholders = array();
        if (!empty($invoice['Invoice']['id'])){
            return [];
        }
        foreach($invoice['CustomModel'] as $fieldKey => $fieldVal) {
            $placeholders["{%{$fieldKey}%}"] = $fieldVal;
        }
        return $placeholders;
    }

    private static function get_additional_fields_place_holders_list(string $entityKey) : array
    {
        return (new CustomFieldsFormService($entityKey))->getPlaceholdersFields();
    }

    private static function get_additional_fields_place_holders_values(string $entityKey, int $id) : array
    {
        return (new CustomFieldsFormService($entityKey))->getPlaceholdersValues($id);
    }

    private static function get_aws_file_path($awsAttachments): string
    {
        $image = '';
        $imageMimeTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'svg'];
        foreach ($awsAttachments as $attachment) {
            if (!in_array($attachment['mime_type'], $imageMimeTypes)) {
                continue;
            }
            $imgUrl = Aws::getPermanentUrl($attachment['path']);
            $image .= " <img src='$imgUrl' alt='{$attachment["name"]}' height='100' width='100'> ";
        }
        return $image;
    }


}

?>
