<?php

require_once dirname(dirname(__FILE__)) . DS . 'Timeline.php';

class Timeline_Invoice extends Timeline {

    public $invoice_actions_list = array(
        ACTION_ADD_INVOICE,
        //tested
        ACTION_UPDATE_INVOICE,
        //tested
        ACTION_SEND_INVOICE,
        ACTION_PRINT_INVOICE,
        ACTION_ADD_INVOICE_PAYMENT,
        ACTION_UPDATE_INVOICE_PAYMENT,
        ACTION_DELETE_INVOICE_PAYMENT,
        ACTION_PROCCESS_INVOICE_PAYMENT,
        ACTION_CLIENT_VIEW_INVIOCE,
        ACTION_CLIENT_PRINT_INVIOCE,
        ACTION_CLIENT_READ_INVOICE_EMAIL,
        ACTION_CLIENT_PAY,
        ACTION_ADD_INVOICE_FROM_ESTIMATE,
        ACTION_RECURRING_ADD_INVOICE,
    );
    public $estimate_actions_list = array(
        ACTION_ADD_ESTIMATE,
        ACTION_UPDATE_ESTIMATE,
        ACTION_DELETE_ESTIMATE,
        ACTION_SEND_ESTIMATE,
        ACTION_PRINT_ESTIMATE,
        ACTION_CLIENT_VIEW_ESTIMATE,
        ACTION_CLIENT_PRINT_ESTIMATE,
        ACTION_CLIENT_READ_INVOICE_EMAIL,
        ACTION_CLIENT_ACCEPT_ESTIMATE,
        ACTION_CONVERT_ESTIMATE_TO_INVOICE,
        ACTION_CONVERT_ESTIMATE_TO_SALES_ORDER,
        
        ACTION_ADD_NEW_POST_INVOICE,
        ACTION_UPDATE_THE_POST_INVOICE,
        ACTION_REMOVE_THE_POST_INVOICE,
        
        ACTION_UPDATE_ESTIMATE_STATUS,
        
        ACTION_ADD_ESTIMATE_APPOINTMENT,
        ACTION_DELETE_ESTIMATE_APPOINTMENT,
        ACTION_EDIT_ESTIMATE_APPOINTMENT,
    );

    public $sales_order_actions_list = array(
        ACTION_ADD_SALES_ORDER,
        ACTION_UPDATE_SALES_ORDER,
        ACTION_ADD_SALES_ORDER_FROM_ESTIMATE,
        ACTION_CLIENT_VIEW_SALES_ORDER,
        ACTION_PRINT_SALES_ORDER,
        ACTION_CLIENT_PRINT_SALES_ORDER,

        ACTION_ADD_NEW_POST_INVOICE,
        ACTION_UPDATE_THE_POST_INVOICE,
        ACTION_REMOVE_THE_POST_INVOICE,
    );

    public $debit_note_actions_list = [
            ACTION_ADD_DEBITNOTE,
            ACTION_UPDATE_DEBITNOTE,
            ACTION_DELETE_DEBITNOTE,
            ACTION_SEND_DEBITNOTE,
            ACTION_PRINT_DEBITNOTE
    ];

    public $advance_payment_actions_list = [
        ACTION_ADD_ADVANCE_PAYMENT,
        ACTION_UPDATE_ADVANCE_PAYMENT,
        ACTION_DELETE_ADVANCE_PAYMENT,
        ACTION_UPDATE_ADVANCE_PAYMENT_DISTRIBUTION,
        ACTION_DELETE_ADVANCE_PAYMENT_DISTRIBUTION,
    ];

    public function getInvoiceActionsList() {
        $action_list = $this->invoice_actions_list;
        if (ifPluginActive(InventoryPlugin)) {
        $action_list[]=ACTION_TRANSACTION_INVOICE_UPDATED;    
        $action_list[]=ACTION_TRANSACTION_INVOICE_DELETED;    
        $action_list[]=ACTION_TRANSACTION_INVOICE_ADDED;    
        
        $action_list[]=ACTION_ADD_NEW_POST_INVOICE;    
        $action_list[]=ACTION_UPDATE_THE_POST_INVOICE;    
        $action_list[]=ACTION_REMOVE_THE_POST_INVOICE;   
        
        $action_list[]=ACTION_UPDATE_INVOICE_STATUS;    
        
        $action_list[]=ACTION_ADD_INVOICE_APPOINTMENT;    
        $action_list[]=ACTION_DELETE_INVOICE_APPOINTMENT;    
        $action_list[]=ACTION_EDIT_INVOICE_APPOINTMENT;
        $action_list[]=ACTION_UPDATE_ADVANCE_PAYMENT_DISTRIBUTION;
        $action_list[]=ACTION_DELETE_ADVANCE_PAYMENT;
        $action_list[]=ACTION_DELETE_ADVANCE_PAYMENT_DISTRIBUTION;
        
        $action_list[]=ACTION_REQUISITION_ADD;
        $action_list[]=ACTION_REQUISITION_UPDATE;
        $action_list[]=ACTION_REQUISITION_DELETE;
//
//        $action_list[]=ACTION_REMOVE_THE_POST;    
//        $action_list[]=ACTION_REMOVE_THE_POST;    
//        $action_list[]=ACTION_REMOVE_THE_POST;    
        
        
        }

        if(ifPluginActive(MANUFACTURING_PLUGIN)) {
            $action_list[]=ACTION_CONVERT_INVOICE_TO_PRODUCTION_PLAN;
        }


        return $action_list;
    }

    public function getEstimateActionsList() {
        return $this->estimate_actions_list;
    }

    public function getSalesOrderActionsList(): array
    {
        $list = $this->sales_order_actions_list;
        if(ifPluginActive(MANUFACTURING_PLUGIN)) {
            $list[]=ACTION_CONVERT_SALES_ORDER_TO_PRODUCTION_PLAN;
        }

        return $list;
    }

    public function getDebitNoteActionsList() {
        return $this->debit_note_actions_list;
    }

    public function getAdvancePaymentActionsList() {
        return $this->advance_payment_actions_list;
    }

    public function __construct($params = array(), $actions_group = false) {
        if ($actions_group != false)
            $this->ActionsGroup = $actions_group;
        parent::init($params, $actions_group);
    }

}

?>
