<?php

use App\Utils\TrackStockUtil;
use Izam\Daftra\Common\Utils\BranchStatusUtil;

class Timeline {

    public $params = array();
    public $report = null;
    public $Staff;
    public $currentStaff;
    public $ActionLine;
    public $ActionKeys;
    public $Slist;
    public $Invoice;
    public $Client;
    public $InvoicePayment;
    public $Params;
    public $Journal;
    public $PaymentStatuses;
    public $IPS;
    public $PaymentMethods;
    public $ActionsGroup = false;
    public $FilterParams = array('staff_id');
    public $PurchaseOrder = false;
    public $PoStatuses = false;
    public $JournalAccount;
    protected $actionLinesData;

    public function __construct($type, $params = array(), $actions_group = false) {

        $classArr = array('Timeline', ucfirst(strtolower($type)));
        $classPath = dirname(__FILE__) . DS . implode(DS, $classArr) . '.php';
//        debug ( $classPath ) ;
        require_once ($classPath);
        $className = implode('_', $classArr);
        $this->report = new $className($params, $actions_group);
        if ($actions_group != false)
            $this->ActionsGroup = $actions_group;
        $this->init($params, $actions_group);
    }

    public function view_action($id) {
        $conditions = ['ActionLine.id' => $id];
        if(ifPluginActive(BranchesPlugin)) {
            $conditions['ActionLine.branch_id'] = getStaffBranchesIDs();
        }
        $action = $this->ActionLine->find('first', ['conditions' => $conditions]);
        return $this->longaction2string($action);
    }

    public function loadPOStatuses() {
        $this->PurchaseOrder = GetObjectOrLoadModel('PurchaseOrder');
        $this->PoStatuses = $this->PurchaseOrder->getStatuses();
    }

    public function getFilterData() {
        $conditions = $this->Params;

        if ($this->ActionsGroup != false)
            $conditions['action_key'] = $this->ActionsGroup;

        $this->ActionLine->unbindModel(array('belongsTo' => array('Staff')));
        $staff = array();
        if (false && $this->actionLinesData) {
            $staffIds = array_unique(array_map(function ($item) {
                return $item['ActionLine']['staff_id'];
            }, $this->actionLinesData));

            foreach ($staffIds as $id) {
                $staff[$id] = $this->getStaffName($id, false);
            }
            ksort($staff);
        } else {
            $all_staff = $this->ActionLine->find('all', array(
                    'conditions' => $conditions,
                    'fields' => array('DISTINCT ActionLine.staff_id')
                )
            );
            foreach ($all_staff as $staff_member) {
                $staff[$staff_member['ActionLine']['staff_id']] = $this->getStaffName($staff_member['ActionLine']['staff_id'], false);
            }
        }
        return array('staff' => $staff);
    }

    public function getDataArray($options = array()) {
        $per_page = 300;
        if (!empty($options['per_page']))
            $per_page = $options['per_page'];
        $last_action_key = 0;
        $last_action_key_date = "01-01-1970";
        $DataArray = array();

        $conditions = $this->Params;


        if ($this->ActionsGroup != false)
            $conditions['action_key'] = $this->ActionsGroup;



        if (!empty($_GET['action'])) {
            $action = explode(',', $_GET['action']);
            if (empty($conditions['action_key']))
                $conditions['action_key'] = $action;
            else
                $conditions['action_key'] = array_intersect($action, $conditions['action_key']);
        }
        //warning suppress
        if (array_key_exists('staff', $_GET) && (!empty($_GET['staff']) || $_GET['staff']=='0')) {

            $staff = explode(',', $_GET['staff']);
            if (empty($conditions['ActionLine.staff_id']))
                $conditions['ActionLine.staff_id'] = $staff;
        }

        if(!isLoggedAsAdmin()) {
            $conditions[] = "ActionLine.logged_as_admin IS NULL";
        }

        if (!check_permission(View_All_Activity_Logs) && !check_permission(Track_Inventory)) {
            $conditions['ActionLine.staff_id'] = getAuthOwner('staff_id');
        }


        if (!empty($_GET['date_from'])) {
            $conditions['ActionLine.created >='] = $this->ActionLine->formatDate($_GET['date_from']);
        }

        if (!empty($_GET['date_to']) ) {

            $conditions['ActionLine.created <='] = $this->ActionLine->formatDate($_GET['date_to']) . ' 23:59:59';
        }


        if (isset($_GET['order']) && $_GET['order'] == "1") {
            $order = "ActionLine.id asc";
        } else {
            $order = "ActionLine.id desc";
        }

        if (!empty($_GET['keyword'])) {
            $keyworld_f=mysqli_real_escape_string( $this->ActionLine->getDataSource()->connection,$_GET['keyword']);
            $conditions['OR']['match(ip,param1,param2,param3,param4,param5,param6,param7,param8,param9) AGAINST(\'' . $keyworld_f. '\' IN BOOLEAN MODE)'] = true;
            $conditions['OR']["FIND_IN_SET('$keyworld_f',CONCAT_WS(',', `action_key`, `staff_id`, `primary_id`, `secondary_id`, `param1`, `param2`, `param3`, `param4`, `param5`, `param6`, `param7`, `param8`, `param9`, `param_date`, `ip`)) >"]=0;$conditions['OR']["FIND_IN_SET('$keyworld_f',CONCAT_WS(',', `action_key`, `staff_id`, `primary_id`, `secondary_id`, `param1`, `param2`, `param3`, `param4`, `param5`, `param6`, `param7`, `param8`, `param9`, `param_date`, `ip`)) >"]=0;$conditions['OR']["FIND_IN_SET('$keyworld_f',CONCAT_WS(',', `action_key`, `staff_id`, `primary_id`, `secondary_id`, `param1`, `param2`, `param3`, `param4`, `param5`, `param6`, `param7`, `param8`, `param9`, `param_date`, `ip`)) >"]=0;$conditions['OR']["FIND_IN_SET('$keyworld_f',CONCAT_WS(',', `action_key`, `staff_id`, `primary_id`, `secondary_id`, `param1`, `param2`, `param3`, `param4`, `param5`, `param6`, `param7`, `param8`, `param9`, `param_date`, `ip`)) >"]=0;
            $conditions['OR']["CONCAT_WS(' ', `action_key`, `staff_id`, `primary_id`, `secondary_id`, `param1`, `param2`, `param3`, `param4`, `param5`, `param6`, `param7`, `param8`, `param9`, `param_date`, `ip`) like "]="%$keyworld_f%";

        }
        if (!empty($_GET['keyword']) and is_numeric($_GET['keyword'])) {
//          $conditions['OR']['primary_id'] = ($_GET['keyword']);
  //        $conditions['OR']['secondary_id'] = ($_GET['keyword']);
        }



        if (empty($_GET['page'])) {
            $page = 1;
        } else {
            $page = intval($_GET['page']) == 0 ? 1 : $_GET['page'];
        }
        if(ifPluginActive(BranchesPlugin) && !isset($conditions['branch_id']) && !isset($conditions['ActionLine.branch_id'])) {
            $conditions['ActionLine.branch_id'] = getStaffBranchesIDs();
        }
        $this->ActionLine->unbindModel(array('belongsTo' => array('Staff')));
        $this->actionLinesData = $actions = $this->ActionLine->find('all', array('conditions' => $conditions, 'offset' => ($page - 1) * $per_page, 'limit' => $per_page, 'order' => $order));
        $this->ActionLine->unbindModel(array('belongsTo' => array('Staff')));
        $DataArray['pagination']['total'] = $this->ActionLine->find('count', array('conditions' => $conditions, 'order' => $order));
        $DataArray['pagination']['total_pages'] = ceil($DataArray['pagination']['total'] / $per_page);
        foreach ($actions as $action) {
            if (($this->ActionKeys[$action['ActionLine']['action_key']]['transparent'] == FALSE) || $last_action_key != $action['ActionLine']['action_key'] || ($last_action['param1'] != $action['ActionLine']['param1']) || ($last_action['param2'] != $action['ActionLine']['param2']) || $last_action_key_date != date("Y-m-d H:i:s", strtotime($action['ActionLine']['created']))) {
                $last_action = $action['ActionLine'];
                $last_action_key = $action['ActionLine']['action_key'];
                $last_action_key_date = date("Y-m-d H:i:s", strtotime($action['ActionLine']['created']));
                $month = date("Y-m-d", strtotime($action['ActionLine']['created']));

                if (empty($_GET['view']) || $_GET['view'] == "1") {

                    $source = json_decode($action['ActionLine']['source'], true);
                    
                    $DataArray['data'][$month][] = [
                        'class' => $this->ActionKeys[$action['ActionLine']['action_key']]['class'],
                        'staff_name' => $this->Slist[$action['ActionLine']['staff_id']] ?? null, // warning suppress
                        'action_key' => $action['ActionLine']['action_key'],
                        'id' => $action['ActionLine']['id'],
                        'date' => $action['ActionLine']['created'],
                        'amount' => $action['ActionLine']['param1'],
                        'text' => $this->longaction2string($action),
                        'branch_id' => $action['ActionLine']['branch_id'],
                        'action-id' => $action['ActionLine']['id'],
                        'ip' => $action['ActionLine']['ip'],
                        'logged_as_admin' => $action['ActionLine']['logged_as_admin']
                    ];
                } else {
                    $DataArray['data'][]['ActionLine'] = array('logged_as_admin' => $action['ActionLine']['logged_as_admin'], 'client_id' => $action['ActionLine']['secondary_id'], 'invoice_id' => $action['ActionLine']['primary_id'], 'name' => $this->shortaction2string($action), 'staff_name' => $this->Slist[$action['ActionLine']['staff_id']], 'action_key' => $action['ActionLine']['action_key'], 'id' => $action['ActionLine']['id'], 'date' => $action['ActionLine']['created'], 'amount' => $action['ActionLine']['param1'], 'branch_id' => $action['ActionLine']['branch_id'], 'action-id' => $action['ActionLine']['id'], 'ip' => $action['ActionLine']['ip']);
                }
            }
        }

        return $DataArray;
    }

    public function getConditions() {
        $conditions = $this->Params;


        if (!empty($_GET['action'])) {
            $action = explode(',', $_GET['action']);
            $conditions['action_key'] = $action;
        }

        return $conditions;
    }

    public function init($params = array(), $actions_group = false) {

        $this->Staff = GetObjectOrLoadModel('Staff');
        $this->Slist = $this->Staff->getList(true, [], false, true);
		$this->JournalAccount = GetObjectOrLoadModel('JournalAccount') ;
		$this->Journal = GetObjectOrLoadModel('Journal') ;
        $this->Slist[-1] = __('The client', true);
        $this->Slist[-2] = __('The system', true);
        $this->Slist[-4] = __('The supplier', true);
        $this->ActionLine = GetObjectOrLoadModel('ActionLine');

        $this->ActionKeys = array(
            ACTION_ADD_INVOICE => array('name' => __('Create Invoice', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_UPDATE_INVOICE => array('name' => __('Update Invoice', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False),
            ACTION_SEND_INVOICE => array('name' => __('Send Invoice', true), 'class' => 'timeline_invoice timeline_send_invoice', 'transparent' => false),
            ACTION_PRINT_PURCHASE_ORDER => array('name' => __('Print Purchase Order', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => true),
            ACTION_PRINT_INVOICE => array('name' => __('Print Invoice', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => true),
            ACTION_DELETE_INVOICE => array('name' => __('Delete Invoice', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_PREVIEW_INVOICE => array('name' => __('Preview Invoice', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => false),
            ACTION_DELETE_INVOICE_PAYMENT => array('name' => __('Delete Invoice Payment', true), 'class' => 'timeline_invoice timeline_delete_invoice_payment', 'transparent' => False),
            ACTION_CLIENT_VIEW_INVIOCE => array('name' => __('Client Views Invoice', true), 'class' => 'timeline_invoice timeline_client_view_invoice', 'transparent' => true),
            ACTION_CLIENT_PRINT_INVIOCE => array('name' => __('Client Prints Invoice', true), 'class' => 'timeline_invoice timeline_client_print_invoice', 'transparent' => true),
            ACTION_ADD_INVOICE_PAYMENT => array('name' => __('Add Invoice Payment', true), 'class' => 'timeline_invoice timeline_add_invoice_payment', 'transparent' => False),
            ACTION_UPDATE_INVOICE_PAYMENT => array('name' => __('Update Invoice Payment', true), 'class' => 'timeline_invoice timeline_update_invoice_payment', 'transparent' => False),
            ACTION_PROCCESS_INVOICE_PAYMENT => array('name' => __('Process Invoice Payment', true), 'class' => 'timeline_invoice timeline_update_invoice_payment', 'transparent' => False),
            ACTION_CLIENT_PAY => array('name' => __('Client Makes a Payment', true), 'class' => 'timeline_invoice timeline_client_pay', 'transparent' => False),
            ACTION_ADD_INVOICE_FROM_ESTIMATE => array('name' => __('Convert Estimate to Invoice', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_RECURRING_ADD_INVOICE => array('name' => __('New Recurring Invoice', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_ADD_ESTIMATE => array('name' => __('Create Estimate', true), 'class' => 'timeline_estimate timeline_add_estimate', 'transparent' => False),
            ACTION_UPDATE_ESTIMATE => array('name' => __('Update Estimate', true), 'class' => 'timeline_estimate timeline_update_estimate', 'transparent' => False),
            ACTION_DELETE_ESTIMATE => array('name' => __('Delete Estimate', true), 'class' => 'timeline_estimate timeline_delete_invoice', 'transparent' => False),
            ACTION_DELETE_SALES_ORDER => ['name' => __('Delete Sales Order', true), 'class' => 'timeline_estimate timeline_delete_invoice', 'transparent' => False],
            ACTION_SEND_ESTIMATE => array('name' => __('Send Estimate', true), 'class' => 'timeline_estimate timeline_send_estimate', 'transparent' => False),
            ACTION_PRINT_ESTIMATE => array('name' => __('Print Estimate', true), 'class' => 'timeline_estimate timeline_print_estimate', 'transparent' => true),
            ACTION_CLIENT_VIEW_ESTIMATE => array('name' => __('Client Views Estimate', true), 'class' => 'timeline_estimate timeline_client_view_estimate', 'transparent' => true),
            ACTION_CLIENT_PRINT_ESTIMATE => array('name' => __('Client Prints Estimate', true), 'class' => 'timeline_estimate timeline_client_print_estimate', 'transparent' => true),
            ACTION_CLIENT_READ_ESTIMATE_EMAIL => array('name' => __('Client Reads Estimate Email', true), 'class' => 'timeline_estimate timeline_client_read_estimate_email', 'transparent' => true),
            ACTION_CLIENT_ACCEPT_ESTIMATE => array('name' => __('Client Accepts Estimate', true), 'class' => 'timeline_estimate timeline_client_read_estimate_email', 'transparent' => false),
            ACTION_CONVERT_ESTIMATE_TO_INVOICE => array('name' => __('Convert Estimate to Invoice', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_CLIENT_READ_INVOICE_EMAIL => array('name' => __('Read Email', true), 'class' => 'timeline_invoice timeline_client_read_invoice_email', 'transparent' => true),
            ACTION_ADD_EXPENSE => array('name' => __('Add Expense', true), 'class' => 'timeline_expense timeline_add_expense', 'transparent' => false),
            ACTION_UPDATE_EXPENSE => array('name' => __('Update Expense', true), 'class' => 'timeline_expense timeline_update_expense', 'transparent' => false),
            ACTION_ADD_MILEAGE => array('name' => __('Add Mileage', true), 'class' => 'timeline_expense timeline_add_expense', 'transparent' => false),
            ACTION_UPDATE_MILEAGE => array('name' => __('Update Mileage', true), 'class' => 'timeline_expense timeline_update_expense', 'transparent' => false),
            
            ACTION_DELETE_EXPENSE => array('name' => __('Delete Expense', true), 'class' => 'timeline_expense timeline_delete_expense', 'transparent' => false),
            ACTION_DELETE_MILEAGE => array('name' => __('Delete Mileage', true), 'class' => 'timeline_expense timeline_delete_expense', 'transparent' => false),
            ACTION_ADD_EXPENSECATEGORY => array('name' =>   __('Add Expense Category', true), 'class' => 'timeline_expense timeline_add_expense', 'transparent' => false),
            ACTION_UPDATE_EXPENSECATEGORY => array('name' => __('Update Expense Category', true), 'class' => 'timeline_expense timeline_update_expense', 'transparent' => false),
            ACTION_DELETE_EXPENSECATEGORY => array('name' => __('Delete Expense Category', true), 'class' => 'timeline_expense timeline_delete_expense', 'transparent' => false),
//            ACTION_DELETE_EXPENSE => array('name' => __('Delete Expense', true), 'class' => 'timeline_expense timeline_delete_expense', 'transparent' => false),
            ACTION_ADD_RECURRING_EXPENSE => array('name' => __('Recurring Expense Generated', true), 'class' => 'timeline_expense timeline_add_expense', 'transparent' => false),
            ACTION_ADD_INCOME => array('name' => __('Add Income', true), 'class' => 'timeline_income timeline_add_income', 'transparent' => false),
            ACTION_UPDATE_INCOME => array('name' => __('Update Income', true), 'class' => 'timeline_income timeline_update_income', 'transparent' => false),
            ACTION_DELETE_INCOME => array('name' => __('Delete Income', true), 'class' => 'timeline_income timeline_delete_income', 'transparent' => false),
            ACTION_ADD_RECURRING_INCOME => array('name' => __('Recurring Income Generated', true), 'class' => 'timeline_income timeline_add_income', 'transparent' => false),
            ACTION_ADD_TIME => array('name' => __('Add Timesheet-Record', true), 'class' => 'timeline_time timeline_add_time', 'transparent' => false),
            ACTION_EDIT_TIME => array('name' => __('Edit Timesheet-Record', true), 'class' => 'timeline_time timeline_update_time', 'transparent' => false),
            ACTION_DELETE_TIME => array('name' => __('Delete Timesheet-Record', true), 'class' => 'timeline_time timeline_delete_time', 'transparent' => false),
            ACTION_ADD_CLIENT => array('name' => __('Add Client', true), 'class' => 'timeline_time timeline_add_client', 'transparent' => false),
            ACTION_ADD_WORK_ORDER => array('name' => sprintf(__('Add %s', true) , __("Work Order",true)), 'class' => 'timeline_time timeline_add_client', 'transparent' => false),
            ACTION_UPDATE_WORK_ORDER_STATUS => array('name' => sprintf(__('Update Work Order Status', true) , __("Work Order",true)), 'class' => 'timeline_time timeline_edit_client', 'transparent' => false),
            ACTION_UPDATE_CLIENT => array('name' => __('Update Client', true), 'class' => 'timeline_time timeline_edit_client', 'transparent' => false),
            ACTION_UPDATE_CLIENT_GROUPPRICE => array('name' => __('Update Client Group Price', true), 'class' => 'timeline_time timeline_edit_client', 'transparent' => false),
            ACTION_EDIT_WORK_ORDER => array('name' => sprintf(__('Edit %s', true) , __("Work Order",true)), 'class' => 'timeline_time timeline_edit_client', 'transparent' => false),
            ACTION_SEND_LOGIN_DETAILS => array('name' => __('Send Login Details', true), 'class' => 'timeline_time timeline_send_login', 'transparent' => false),
            ACTION_DELETE_CLIENT => array('name' => __('Delete Client', true), 'class' => 'timeline_time timeline_delete_client', 'transparent' => false),
            ACTION_DELETE_WORK_ORDER => array('name' => __('Delete',true).' ' .__('Work Order', true), 'class' => 'timeline_time timeline_delete_client', 'transparent' => false),
            ACTION_CLIENT_LOGIN => array('name' => __('Client Login', true), 'class' => 'timeline_time timeline_login', 'transparent' => false),
            ACTION_ADD_RECURRING_PROFILE => array('name' => __('Add Recurring Profile', true), 'class' => 'timeline_time timeline_add_recurring_invoice', 'transparent' => false),
            ACTION_UPDATE_RECURRING_PROFILE => array('name' => __('Update Recurring Profile', true), 'class' => 'timeline_time timeline_edit_recurring_invoice', 'transparent' => false),
            ACTION_DELETE_RECURRING_PROFILE => array('name' => __('Delete Recurring Profile', true), 'class' => 'timeline_time timeline_delete_recurring_invoice', 'transparent' => false),
            ACTION_ADD_PROJECT => array('name' => __('Add Project', true), 'class' => 'timeline_time timeline_add_project', 'transparent' => false),
            ACTION_EDIT_PROJECT => array('name' => __('Edit Project', true), 'class' => 'timeline_time timeline_edit_project', 'transparent' => false),
            ACTION_DELETE_PROJECT => array('name' => __('Delete Project', true), 'class' => 'timeline_time timeline_delete_project', 'transparent' => false),
            ACTION_ADD_GROUPPRICE => array('name' => __('Add Price Group', true), 'class' => 'timeline_time timeline_expense timeline_add_expense', 'transparent' => false),
            ACTION_EDIT_GROUPPRICE => array('name' => __('Edit Price Group', true), 'class' => 'timeline_time timeline_expense timeline_update_expense', 'transparent' => false),
            ACTION_DELETE_GROUPPRICE => array('name' => __('Delete Price Group', true), 'class' => 'timeline_timetimeline_expense timeline_delete_expense', 'transparent' => false),
            ACTION_ADD_ACTIVITY => array('name' => __('Add Activity', true), 'class' => 'timeline_time timeline_add_activity', 'transparent' => false),
            ACTION_EDIT_ACTIVITY => array('name' => __('Edit Activity', true), 'class' => 'timeline_time timeline_edit_activity', 'transparent' => false),
            ACTION_DELETE_ACTIVITY => array('name' => __('Delete Activity', true), 'class' => 'timeline_time timeline_delete_activity', 'transparent' => false),
            ACTION_ADD_PRODUCT => array('name' => __('Add Product', true), 'class' => 'timeline_time timeline_add_product', 'transparent' => false),
            ACTION_EDIT_PRODUCT => array('name' => __('Edit Product', true), 'class' => 'timeline_time timeline_edit_product', 'transparent' => false),
            ACTION_DELETE_PRODUCT => array('name' => __('Delete Product', true), 'class' => 'timeline_time timeline_delete_product', 'transparent' => false),
            ACTION_ADD_STAFF => array('name' => __('Add Staff Member', true), 'class' => 'timeline_time timeline_add_staff', 'transparent' => false),
            ACTION_EDIT_STAFF => array('name' => __('Edit Staff Member', true), 'class' => 'timeline_time timeline_edit_staff', 'transparent' => false),
            ACTION_DELETE_STAFF => array('name' => __('Delete Staff Member', true), 'class' => 'timeline_time timeline_delete_staff', 'transparent' => false),
            ACTION_ADD_ROLE => array('name' => __('Add Role', true), 'class' => 'timeline_time timeline_add_role', 'transparent' => false),
            ACTION_EDIT_ROLE => array('name' => __('Edit Role', true), 'class' => 'timeline_time timeline_edit_role', 'transparent' => false),
            ACTION_DELETE_ROLE => array('name' => __('Delete Role', true), 'class' => 'timeline_time timeline_delete_role', 'transparent' => false),
            ACTION_IMPORT_DATA => array('name' => __('Import Data', true), 'class' => 'timeline_time timeline_import_data', 'transparent' => false),
            ACTION_UPDATE_SMTP_SETTINGS => array('name' => __('Update SMTP Settings', true), 'class' => 'timeline_time timeline_settings', 'transparent' => false),
            ACTION_UPDATE_SETTINGS => array('name' => __('Update System Settings', true), 'class' => 'timeline_time timeline_settings', 'transparent' => false),
            ACTION_UPDATED_PLUGIN => array('name' => __('Update System Plugins', true), 'class' => 'timeline_time timeline_settings', 'transparent' => false),
            ACTION_CHNAGE_PASSWORD => array('name' => __('Change Password', true), 'class' => 'timeline_time timeline_change_password', 'transparent' => false),
            ACTION_CHNAGE_EMAIL => array('name' => __('Changed Email', true), 'class' => 'timeline_time timeline_change_email', 'transparent' => false),
            ACTION_UPDATE_SYSTEM_LOGO_COLOR => array('name' => __('Update System Logo/Color', true), 'class' => 'timeline_time timeline_settings', 'transparent' => false),
            ACTION_ADD_EMAIL_TEMPLATE => array('name' => __('Add Email Template', true), 'class' => 'timeline_time timeline_add_email_template', 'transparent' => false),
            ACTION_UPDATE_EMAIL_TEMPLATE => array('name' => __('Update Email Template', true), 'class' => 'timeline_time timeline_edit_email_template', 'transparent' => false),
            ACTION_DELETE_EMAIL_TEMPLATE => array('name' => __('Delete Email Template', true), 'class' => 'timeline_time timeline_delete_email_template', 'transparent' => false),
            ACTION_ADD_INVOICE_LAYOUT => array('name' => __('Add Invoice Layout', true), 'class' => 'timeline_time timeline_add_layout', 'transparent' => false),
            ACTION_UPDATE_INVOICE_LAYOUT => array('name' => __('Update Invoice Layout', true), 'class' => 'timeline_time timeline_edit_layout', 'transparent' => false),
            ACTION_DELETE_INVOICE_LAYOUT => array('name' => __('Delete Invoice Layout', true), 'class' => 'timeline_time timeline_delete_layout', 'transparent' => false),
            ACTION_LOGIN => array('name' => __('Login', true), 'class' => 'timeline_time timeline_login', 'transparent' => false),
            ACTION_LOGIN_AS_CLIENT => array('name' => __('Login as Client', true), 'class' => 'timeline_time timeline_login', 'transparent' => false),
            ACTION_LOGIN_AS_STAFF => array('name' => __('Login as Staff Member', true), 'class' => 'timeline_time timeline_login', 'transparent' => false),
            ACTION_CREATED_THE_ACCOUNT => array('name' => 'CREATED THE ACCOUNT', 'class' => 'timeline_register', 'transparent' => False),
            ACTION_ADD_PR => array('name' => __('Create Purchase Refund', true), 'class' => 'timeline_add_po', 'transparent' => False),
            ACTION_EDIT_PR => array('name' => __('Update Purchase Refund', true), 'class' => 'timeline_edit_po', 'transparent' => False),
            ACTION_DELETE_PR => array('name' => __('Delete Purchase Refund', true), 'class' => 'timeline_delete_po timeline_update_estimate', 'transparent' => False),
            ACTION_ADD_PO => array('name' => __('Create Purchase Invoice', true), 'class' => 'timeline_add_po', 'transparent' => False),
            ACTION_UPDATE_PO => array('name' => __('Update Purchase Invoice', true), 'class' => 'timeline_edit_po', 'transparent' => False),
            ACTION_ADD_PQ => array('name' => __('Create Purchase Quotation', true), 'class' => 'timeline_add_po', 'transparent' => False),
            ACTION_UPDATE_PQ => array('name' => __('Update Purchase Quotation', true), 'class' => 'timeline_edit_po', 'transparent' => False),
            ACTION_DELETE_PO => array('name' => __('Delete Purchase Invoice', true), 'class' => 'timeline_delete_po timeline_update_estimate', 'transparent' => False),
            ACTION_SEND_PO => array('name' => __('Send Purchase Invoice', true), 'class' => 'timeline_estimate timeline_send_estimate', 'transparent' => False),
            ACTION_READ_PO_EMAIL => array('name' => __('Reads Purchase Invoice Email', true), 'class' => 'timeline_estimate timeline_client_read_estimate_email', 'transparent' => true),
            ACTION_SUPPLIER_PRINT_PO => array('name' => __('Supplier Prints Purchase Invoice', true), 'class' => 'timeline_estimate timeline_client_read_estimate_email', 'transparent' => true),
            ACTION_SUPPLIER_VIEW_PO => array('name' => __('Supplier Views Purchase Invoice', true), 'class' => 'timeline_estimate timeline_client_read_estimate_email', 'transparent' => true),
            ACTION_TRANSACTION_PO_ADDED => array('name' => __('Add Purchased Stock', true), 'class' => 'timeline_received_stock', 'transparent' => False),
            ACTION_TRANSACTION_PO_UPDATED => array('name' => __('Update Purchased Stock', true), 'class' => 'timeline_received_stock_updated', 'transparent' => False),
            ACTION_TRANSACTION_PO_DELETED => array('name' => __('Remove Purchased Stock', true), 'class' => 'timeline_received_stock_deleted', 'transparent' => False),
            ACTION_TRANSACTION_PR_ADDED => array('name' => __('Add Purchased Refund Stock', true), 'class' => 'timeline_received_stock', 'transparent' => False),
            ACTION_TRANSACTION_PR_UPDATED => array('name' => __('Update Purchased Refund Stock', true), 'class' => 'timeline_received_stock_updated', 'transparent' => False),
            ACTION_TRANSACTION_PR_DELETED => array('name' => __('Remove Purchased Refund Stock', true), 'class' => 'timeline_received_stock_deleted', 'transparent' => False),
            ACTION_TRANSACTION_INVOICE_ADDED => array('name' => __('Add Sold Stock', true), 'class' => 'timeline_sold_stock', 'transparent' => False),
            ACTION_TRANSACTION_INVOICE_UPDATED => array('name' => __('Update Sold Stock', true), 'class' => 'timeline_sold_stock_updated', 'transparent' => False),
            ACTION_TRANSACTION_INVOICE_DELETED => array('name' => __('Remove Sold Stock', true), 'class' => 'timeline_sold_stock_deleted', 'transparent' => False),
            ACTION_TRANSACTION_MANUAL_ADDED => array('name' => __('Manual Stock Adjustment', true), 'class' => 'stock_adjustment', 'transparent' => False),
            ACTION_TRANSACTION_MANUAL_UPDATED => array('name' => __('Manual Stock Adjustment Updated', true), 'class' => 'stock_adjustment_updated', 'transparent' => False),
            ACTION_TRANSACTION_MANUAL_DELETED => array('name' => __('Manual Stock Adjustment Removed', true), 'class' => 'stock_adjustment_deleted', 'transparent' => False),
            ACTION_SEND_EMAIL_TO_CLIENT => array('name' => __('Send Email To Client', true), 'class' => 'timeline_time timeline_send_invoice', 'transparent' => false),
            ACTION_SEND_SMS_TO_CLIENT => array('name' => __('Send SMS To Client', true), 'class' => 'timeline_time timeline_send_invoice', 'transparent' => false),
            ACTION_SEND_SMS_TO_INVOICE => array('name' => __('Send SMS To Invoice Client', true), 'class' => 'timeline_time timeline_send_invoice', 'transparent' => false),
            ACTION_SEND_SMS_TO_APPOINTMENT => array('name' => __('Send SMS To Appointment Partener', true), 'class' => 'timeline_time timeline_send_invoice', 'transparent' => false),
            ACTION_SEND_SMS_TO_APPOINTMENT_STAFF => array('name' => __('Send SMS To Appointment Staff', true), 'class' => 'timeline_time timeline_send_invoice', 'transparent' => false),
            ACTION_SEND_EMAIL_TO_APPOINTMENT => array('name' => __('Send Email To Appointment Partener', true), 'class' => 'timeline_time timeline_send_invoice', 'transparent' => false),
            ACTION_SEND_EMAIL_TO_APPOINTMENT_STAFF => array('name' => __('Send Email To Appointment Staff', true), 'class' => 'timeline_time timeline_send_invoice', 'transparent' => false),
            ACTION_ASSIGN_STAFF_TO_CLIENT => array('name' => __('Assign Staff To Client', true), 'class' => 'timeline_time timeline_edit_client', 'transparent' => false),
            ACTION_UNASSIGN_STAFF_FROM_CLIENT => array('name' => __('Unassign Staff Member From Client', true), 'class' => 'timeline_time timeline_remove_client', 'transparent' => false),
            STAFF_DOWNLOAD_FILE => array('name' => __('Staff Member Downloads File', true), 'class' => 'timeline_time timeline_download_client', 'transparent' => false),
            CLIENT_DOWNLOAD_FILE => array('name' => __('Client Downloads File', true), 'class' => 'timeline_time timeline_download_client', 'transparent' => false),
            STAFF_DOWNLOAD_INVOICE_DOCUMENT => array('name' => __('Staff Member Download Invoice Document', true), 'class' => 'timeline_time timeline_download_client', 'transparent' => false),
            CLIENT_DOWNLOAD_INVOICE_DOCUMENT => array('name' => __('Client Download Invoice Document', true), 'class' => 'timeline_time timeline_download_client', 'transparent' => false),
            ACTION_ADD_NEW_POST => array('name' => __('Add Notes/Attachments To Client', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => false),
            ACTION_UPDATE_THE_POST => array('name' => __('Update Client Notes/Attachments', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => false),
            ACTION_ADD_NEW_WORK_ORDER_POST => array('name' => __('Add Notes/Attachments To Work Order', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => false),
            ACTION_UPDATE_WORK_ORDER_POST => array('name' => __('Update Work Order Notes/Attachments', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => false),
            ACTION_REMOVE_THE_POST => array('name' => __('Delete Client Notes/Attachments', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => false),
            
            ACTION_ADD_NEW_POST_INVOICE => array('name' => __('Add Notes/Attachments', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => false),
            ACTION_UPDATE_THE_POST_INVOICE => array('name' => __('Update Client Notes/Attachments', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => false),
            ACTION_REMOVE_THE_POST_INVOICE => array('name' => __('Delete Client Notes/Attachments', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => false),
            
            ACTION_ADD_NEW_POST_PURCHASE_INVOICE => array('name' => __('Add Notes/Attachments', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => false),
            ACTION_UPDATE_THE_POST_PURCHASE_INVOICE => array('name' => __('Update Client Notes/Attachments', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => false),
            ACTION_REMOVE_THE_POST_PURCHASE_INVOICE => array('name' => __('Delete Client Notes/Attachments', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => false),

            ACTION_EDIT_CLIENT_APPOINTMENT => array('name' => __('Update Client Appointment', true), 'class' => 'timeline_invoice timeline_edit_appointment', 'transparent' => false),
            ACTION_DELETE_CLIENT_APPOINTMENT => array('name' => __('Delete Client Appointment', true), 'class' => 'timeline_invoice timeline_delete_appointment', 'transparent' => false),
            ACTION_ADD_CLIENT_APPOINTMENT => array('name' => __('Add Appointment For Client', true), 'class' => 'timeline_invoice  timeline_add_appointment', 'transparent' => false),
            
            ACTION_EDIT_INVOICE_APPOINTMENT => array('name' => __('Update Invoice Appointment', true), 'class' => 'timeline_invoice timeline_edit_appointment', 'transparent' => false),
            ACTION_DELETE_INVOICE_APPOINTMENT => array('name' => __('Delete Invoice Appointment', true), 'class' => 'timeline_invoice timeline_delete_appointment', 'transparent' => false),
            ACTION_ADD_INVOICE_APPOINTMENT => array('name' => __('Add Appointment For Invoice', true), 'class' => 'timeline_invoice  timeline_add_appointment', 'transparent' => false),
            
            ACTION_EDIT_ESTIMATE_APPOINTMENT => array('name' => __('Update Estimate Appointment', true), 'class' => 'timeline_invoice timeline_edit_appointment', 'transparent' => false),
            ACTION_DELETE_ESTIMATE_APPOINTMENT => array('name' => __('Delete Estimate Appointment', true), 'class' => 'timeline_invoice timeline_delete_appointment', 'transparent' => false),
            ACTION_ADD_ESTIMATE_APPOINTMENT => array('name' => __('Add Appointment For Estimate', true), 'class' => 'timeline_invoice  timeline_add_appointment', 'transparent' => false),

            ACTION_EDIT_WORK_ORDER_APPOINTMENT  => array('name' => __('Update Work Order Appointment', true), 'class' => 'timeline_invoice timeline_edit_appointment', 'transparent' => false),
            ACTION_DELETE_WORK_ORDER_APPOINTMENT => array('name' => __('Delete Work Order Appointment', true), 'class' => 'timeline_invoice timeline_delete_appointment', 'transparent' => false),
            ACTION_ADD_WORK_ORDER_APPOINTMENT=> array('name' => __('Add Appointment For Work Order', true), 'class' => 'timeline_invoice  timeline_add_appointment', 'transparent' => false),

            ACTION_UPDATED_AVG_PRICE=> array('name' => __('Average Price Changed', true), 'class' => 'timeline_invoice  timeline_add_appointment', 'transparent' => false),
            
            ACTION_UPDATE_CLIENT_STATUS => array('name' => __('Update Client Status', true), 'class' => 'timeline_time timeline_edit_client', 'transparent' => false),
            ACTION_UPDATE_INVOICE_STATUS => array('name' => __('Update Invoice Status', true), 'class' => 'timeline_time timeline_edit_client', 'transparent' => false),
            ACTION_UPDATE_ESTIMATE_STATUS => array('name' => __('Update Estimate Status', true), 'class' => 'timeline_time timeline_edit_client', 'transparent' => false),
            ACTION_UPDATE_CLIENT_CATEGORY => array('name' => __('Update Client Category', true), 'class' => 'timeline_time timeline_edit_client', 'transparent' => false),
            ACTION_CLIENT_VIEW_POST => array('name' => __('Client Views Note', true), 'class' => 'timeline_invoice timeline_client_view_invoice', 'transparent' => true),
            ACTION_PRINT_RECEIPT => array('name' => __('Staff Member Receipt', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => false),
            ACTION_DOWNLOAD_RECEIPT => array('name' => __('Staff Member Download Receipt', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => false),
            ACTION_ADD_SUPPLIER => array('name' => __('Add', true) . ' ' . __('Supplier', true), 'class' => 'timeline_client timeline_add_client', 'transparent' => false),
            ACTION_UPDATE_SUPPLIER => array('name' => __('Update', true) . ' ' . __('Supplier', true), 'class' => 'timeline_client timeline_edit_client', 'transparent' => false),
            ACTION_DELETE_SUPPLIER => array('name' => __('Delete', true) . ' ' . __('Supplier', true), 'class' => 'timeline_client timeline_delete_client', 'transparent' => false),
            ACTION_DELETE_PO_PAYMENT => array('name' => __('Delete Purchase Invoice Payment', true), 'class' => 'timeline_invoice timeline_delete_invoice_payment', 'transparent' => False),
            ACTION_ADD_PO_PAYMENT => array('name' => __('Add Purchase Invoice Payment', true), 'class' => 'timeline_invoice timeline_add_invoice_payment', 'transparent' => False),
            ACTION_UPDATE_PCN_PAYMENT => array('name' => __('Update Credit Note Payment', true), 'class' => 'timeline_invoice timeline_update_invoice_payment', 'transparent' => False),
            ACTION_DELETE_PCN_PAYMENT => array('name' => __('Delete Credit Note Payment', true), 'class' => 'timeline_invoice timeline_delete_invoice_payment', 'transparent' => False),
            ACTION_ADD_PCN_PAYMENT => array('name' => __('Add Purchase Credit Note payment', true), 'class' => 'timeline_invoice timeline_add_invoice_payment', 'transparent' => False),
            ACTION_UPDATE_PO_PAYMENT => array('name' => __('Update Purchase Invoice Payment', true), 'class' => 'timeline_invoice timeline_update_invoice_payment', 'transparent' => False),
            ACTION_ADD_CREDITNOTE => array('name' => __('Create Credit Note', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_ATTACH_CREDIT_NOTE_TO_ADVANCE_PAYMENT_ON_CREATION => array('name' => __('Attach Credit Note to Advance Payment', true), 'class' => 'timeline_invoice timeline_update_invoice_payment', 'transparent' => False),
            ACTION_UPDATE_CREDITNOTE => array('name' => __('Update Credit Note', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False),
            ACTION_DELETE_CREDITNOTE => array('name' => __('Delete Credit Note', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_ADD_ADVANCE_PAYMENT => array('name' => __('Create Advance Payment', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_UPDATE_ADVANCE_PAYMENT => array('name' => __('Update Advance Payment', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False),
            ACTION_DELETE_ADVANCE_PAYMENT => array('name' => __('Delete Advance Payment', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_SEND_CREDITNOTE => array('name' => __('Send Credit Note', true), 'class' => 'timeline_invoice timeline_send_invoice', 'transparent' => false),
            ACTION_UPDATE_ADVANCE_PAYMENT_DISTRIBUTION => array('name' => __('Update Advance Payment Distribution', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False),
            ACTION_DELETE_ADVANCE_PAYMENT_DISTRIBUTION => array('name' => __('Delete Advance Payment Distribution', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False),
            ACTION_PRINT_CREDITNOTE => array('name' => __('Print Credit Note', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => true),
            ACTION_ADD_DEBITNOTE => array('name' => __('Create Debit Note', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_UPDATE_DEBITNOTE => array('name' => __('Update Debit Note', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False),
            ACTION_DELETE_DEBITNOTE => array('name' => __('Delete Debit Note', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_SEND_DEBITNOTE => array('name' => __('Send Debit Note', true), 'class' => 'timeline_invoice timeline_send_invoice', 'transparent' => false),
            ACTION_PRINT_DEBITNOTE => array('name' => __('Print Debit Note', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => true),
            ACTION_TRANSACTION_CREDITNOTE_ADDED => array('name' => __('Add Credit Note Stock', true), 'class' => 'timeline_sold_stock', 'transparent' => False),
            ACTION_TRANSACTION_CREDITNOTE_UPDATED => array('name' => __('Update Credit Note Stock', true), 'class' => 'timeline_sold_stock_updated', 'transparent' => False),
            ACTION_TRANSACTION_CREDITNOTE_DELETED => array('name' => __('Remove Credit Note Stock', true), 'class' => 'timeline_sold_stock_deleted', 'transparent' => False),
            ACTION_ADD_REFUND => array('name' => __('Create Refund Receipt', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_UPDATE_REFUND => array('name' => __('Update Refund Receipt', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False),
            ACTION_DELETE_REFUND => array('name' => __('Delete Refund Receipt', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_SEND_REFUND => array('name' => __('Send Refund Receipt', true), 'class' => 'timeline_invoice timeline_send_invoice', 'transparent' => false),
            ACTION_PRINT_REFUND => array('name' => __('Print Refund Receipt', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => true),
            ACTION_TRANSACTION_REFUND_ADDED => array('name' => __('Add Refund Receipt Stock', true), 'class' => 'timeline_sold_stock', 'transparent' => False),
            ACTION_TRANSACTION_REFUND_UPDATED => array('name' => __('Update Refund Receipt Stock', true), 'class' => 'timeline_sold_stock_updated', 'transparent' => False),
            ACTION_TRANSACTION_REFUND_DELETED => array('name' => __('Remove Refund Receipt Stock', true), 'class' => 'timeline_sold_stock_deleted', 'transparent' => False),
            ACTION_ADD_CLIENT_PAYMENT => array('name' => __('Add Payment Credit', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_ADD_SUPPLIER_CREDIT => array('name' => __('Add Payment Credit', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_CLIENT_ADD_CLIENT_PAYMENT => array('name' => __('Client Payment Credit', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_DELETE_CLIENT_PAYMENT => array('name' => __('Delete Client Payment Credit', true), 'class' => 'timeline_invoice timeline_delete_invoice_payment', 'transparent' => False),
            ACTION_ADD_PRESCRIPTION_CLIENT => array('name' => __('Add Client Prescription', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_EDIT_PRESCRIPTION_CLIENT => array('name' => __('Edit Client Prescription', true), 'class' => 'timeline_invoice timeline_edit_appointment', 'transparent' => False),
            ACTION_DELETE_PRESCRIPTION_CLIENT => array('name' => __('Delete Client Prescription', true), 'class' => 'timeline_invoice timeline_delete_invoice_payment', 'transparent' => False),
            ACTION_PRINT_PRESCRIPTION_CLIENT => array('name' => __('Print Client Prescription', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => False),
            ACTION_EMAIL_SENT_PRESCRIPTION_CLIENT => array('name' => __('Prescription Sent By Email', true), 'class' => 'timeline_invoice timeline_send_invoice', 'transparent' => False),
            ACTION_JOURNAL_ACCOUNT_ADD => array('name' => __('Add Journal Account', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_JOURNAL_ACCOUNT_EDIT => array('name' => __('Edit Journal Account', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_JOURNAL_ACCOUNT_DELETE => array('name' => __('Delete Journal Account', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),

            ACTION_ASSET_ADD => array('name' => sprintf ( __('Add %s', true) , __("Asset" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_ASSET_MODIFY => array('name' => sprintf ( __('Edit %s', true) , __("Asset" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_ASSET_DELETE => array('name' => sprintf ( __('Delete %s', true) , __("Asset" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            
            ACTION_ASSET_DEPRECATION_DELETE => array('name' => sprintf ( __('Delete %s', true) , __("Asset Depreciation" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_ASSET_DEPRECATION_ADD => array('name' => sprintf ( __('Add %s', true) , __("Asset Depreciation" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_ASSET_DEPRECATION_MODIFY => array('name' => sprintf ( __('Edit %s', true) , __("Asset Depreciation" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            
            ACTION_ASSET_SOLD => array('name' => __('Delete Journal Account', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_ASSET_WRITE_OFF => array('name' => __('Delete Journal Account', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_ASSET_RE_EVALUATE => array('name' => __('Delete Journal Account', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_ADD_CLIENT_OPENING_BALANCE => array('name' => __('Add Client Opening Balance', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_EDIT_OPENING_BALANCE => array('name' => __('Edit Client Opening Balance', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_DELETE_CLIENT_OPENING_BALANCE => array('name' => __('Delete Client Opening Balance', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_ADD_SUPPLIER_OPENING_BALANCE => array('name' => __('Add Supplier Opening Balance', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_EDIT_SUPPLIER_OPENING_BALANCE => array('name' => __('Edit Supplier Opening Balance', true), 'class' => 'timeline_invoice timeline_edit_invoice', 'transparent' => False),
            ACTION_DELETE_SUPPLIER_OPENING_BALANCE => array('name' => __('Delete Supplier Opening Balance', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),

            ACTION_EDIT_CLIENT_CREDIT => array('name' => __('Edit Client Credit', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),


			//CLOSED PERIODS
            ACTION_CLOSED_PERIOD_ADD => array('name' => sprintf ( __('Add %s', true) , __("Closed Period" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_CLOSED_PERIOD_EDIT => array('name' => sprintf ( __('Edit %s', true) , __("Closed Period" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_CLOSED_PERIOD_DELETE => array('name' => sprintf ( __('Delete %s', true) , __("Closed Period" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),        
			//end CLOSED PERIODSs

			//cost centers
			ACTION_COST_CENTER_ADD => array('name' => sprintf ( __('Add %s', true) , __("Cost Center" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_COST_CENTER_EDIT => array('name' => sprintf ( __('Edit %s', true) , __("Cost Center" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_COST_CENTER_DELETE => array('name' => sprintf ( __('Delete %s', true) , __("Cost Center" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
			//end cost centers
			
			
			//journal accounts cost centers
			ACTION_JOURNAL_ACCOUNT_COST_CENTER_ADD => array('name' => sprintf ( __('Add %s', true) , __("Cost Center for Journal Account" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_JOURNAL_ACCOUNT_COST_CENTER_EDIT => array('name' => sprintf ( __('Edit %s', true) , __("Cost Center for Journal Account" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_JOURNAL_ACCOUNT_COST_CENTER_DELETE => array('name' => sprintf ( __('Delete %s', true) , __("Cost Center for Journal Account" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
			//end journal accounts cost centers
			
			//journal accounts cost centers
			ACTION_COST_CENTER_TRANSACTION_ADD=> array('name' => sprintf ( __('Add %s', true) , __("Cost Center Transaction" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_COST_CENTER_TRANSACTION_EDIT => array('name' => sprintf ( __('Edit %s', true) , __("Cost Center Transaction" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_COST_CENTER_TRANSACTION_DELETE => array('name' => sprintf ( __('Delete %s', true) , __("Cost Center Transaction" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
			//end journal accounts cost centers
			
			//financial years
			ACTION_FINANCIAL_YEAR_ADD => array('name' => sprintf ( __('Add %s', true) , __("Financial Year" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_FINANCIAL_YEAR_EDIT => array('name' => sprintf ( __('Edit %s', true) , __("Financial Year" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_FINANCIAL_YEAR_DELETE => array('name' => sprintf ( __('Delete %s', true) , __("Financial Year" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
			//end financial years

			//POS
			ACTION_POS_SESSION_START => array('name' => sprintf ( __('Add %s', true) , __("POS Session" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_POS_SESSION_OPEN => array('name' => sprintf ( __('Open %s', true) , __("POS Session" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_POS_SESSION_CLOSE => array('name' => sprintf ( __('Close %s', true) , __("POS Session" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_POS_SESSION_REMOVE => array('name' => sprintf ( __('Remove %s', true) , __("POS Session" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_POS_SESSION_VALIDATE => array('name' => sprintf ( __('Validate %s', true) , __("POS Session" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_POS_SESSION_TRANSACTION_ADD => array('name' => sprintf ( __('Add %s', true) , __("Transaction" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
			//end POS
            //Requisition
            ACTION_REQUISITION_ADD => array('name' => sprintf ( __('Add %s', true) , __("Requisition" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_REQUISITION_UPDATE => array('name' => sprintf ( __('Edit %s', true) , __("Requisition" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_REQUISITION_CHANGE_STORE => array('name' => sprintf ( __('Edit %s store' , true) , __("Requisition" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_REQUISITION_DELETE => array('name' => sprintf ( __('Delete %s', true) , __("Requisition" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
			//end Requisition
            //STOCKTAKING
            ACTION_STOCKTAKING_ADD => array('name' => sprintf ( __('Add %s', true) , __("Stocktaking" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_STOCKTAKING_UPDATE => array('name' => sprintf ( __('Edit %s', true) , __("Stocktaking" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_STOCKTAKING_DELETE => array('name' => sprintf ( __('Delete %s', true) , __("Stocktaking" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            //end STOCKTAKING
            //Beta
            ACTION_MOVE_TO_BETA => array('name' => __('Moved to Beta', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_MOVE_TO_LIVE => array('name' => __('Moved to Live', true), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_UPDATE_INVENTORY_SETTINGS => array('name' => __('Update Inventory Settings', true), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_UPDATE_BRANCH_SETTINGS => array('name' => __('Update Branch Settings', true), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_UPDATE_BRANCH => array('name' => __('Update Branch Status', true), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_ADD_WAREHOUSE => array('name' => sprintf ( __('Add %s', true) , __("Warehouse" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_EDIT_WAREHOUSE => array('name' => sprintf ( __('Edit %s', true) , __("Warehouse" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_DELETE_WAREHOUSE => array('name' => sprintf ( __('Delete %s', true) , __("Warehouse" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
			//end Beta

            ACTION_DELETE_BOOKING => ['name' => sprintf ( __('Delete %s', true) , __("Booking" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False],
            ACTION_UPDATE_BOOKING_STATUS => ['name' => __('Update Booking Status', true) , 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False],
            ACTION_ADD_BOOKING_CLIENT => ['name' => sprintf ( __('Add Booking By Client', true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False],
            ACTION_CONVERT_BOOKING_TO_INVOICE => ['name' => __('Convert Booking To Invoice', true) , 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False],
            ACTION_ADD_BOOKING => ['name' => sprintf ( __('Add %s', true) , __("Booking" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False],
            ACTION_UPDATE_BOOKING => ['name' => sprintf ( __('Edit %s', true) , __("Booking" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False],
            ACTION_ADD_TREASURY => ['name' => sprintf ( __('Add %s', true) , __("Treasury" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False],
            ACTION_EDIT_TREASURY => ['name' => sprintf ( __('Edit %s', true) , __("Treasury" , true) ), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_DELETE_TREASURY => ['name' => sprintf ( __('Delete %s', true) , __("Treasury" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False],
            ACTION_INVOICE_PAYMENT_FAIL => ['name' => __('Invoice Payment Fail', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False],
            ACTION_UPDATE_POS_ACCOUNTING_SETTINGS => ['name' => __('Pos Accounting Setting changed', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_UPDATE_POS_PARTIAL_PAYMENT => ['name' => __('Pos Accounting Partial Payment changed', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_UPDATE_ROUTING_SETTINGS => ['name' => __('Routing Settings Changed', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_UPDATE_AUTONUMBER_SETTINGS => ['name' => __('Autonumber Settings Changed', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_ADD_LOCAL_CURRENCY_RATE => ['name' => __('Local currency rate added', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False],
            ACTION_UPDATE_LOCAL_CURRENCY_RATE => ['name' => __('Local currency rate updated', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_DELETE_LOCAL_CURRENCY_RATE => ['name' => __('Local currency rate deleted', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False],
            ACTION_UPDATE_AUTONUMBER_SETTINGS_UNIQUE_STATUS => ['name' => __('Autonumber Unique Settings Changed', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_UPDATE_AUTONUMBER_SETTINGS_PATTERN => ['name' => __('Autonumber Pattern Changed', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_UPDATE_AUTONUMBER_SETTINGS_PREFIX => ['name' => __('Autonumber Prefix Changed', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_UPDATE_AUTONUMBER_SETTINGS_NUMBER_OF_DIGITS => ['name' => __('Autonumber Number of digits Changed', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_UPDATE_AUTONUMBER_SETTINGS_NUMBER_OF_CHARACTERS => ['name' => __('Autonumber Number of characters Changed', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False],
            ACTION_ADD_UNIT_TEMPLATE => array('name' => sprintf ( __('Add %s', true) , __("Unit template" , true) ), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_UPDATE_UNIT_TEMPLATE => array('name' => sprintf ( __('Edit %s', true) , __("Unit template" , true) ), 'class' => 'timeline_invoice  timeline_update_invoice', 'transparent' => False),
            ACTION_DELETE_UNIT_TEMPLATE => array('name' => sprintf ( __('Delete %s', true) , __("Unit template" , true) ), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_CLONE_EXPENSE => ['name' => __('Clonned Expense', true) , 'class' => 'timeline_expense timeline_add_expense', 'transparent' => False],
            ACTION_CLONE_MILEAGE => ['name' => __('Clonned Mileage', true) , 'class' => 'timeline_expense timeline_add_expense', 'transparent' => False],
            ACTION_CLONE_INCOME => ['name' => __('Clonned Income', true) , 'class' => 'timeline_income timeline_add_income', 'transparent' => False],
            ACTION_DELETE_SUPPLIER_PAYMENT => array('name' => __('Delete Supplier Payment Credit', true), 'class' => 'timeline_invoice timeline_delete_invoice_payment', 'transparent' => False),
            ACTION_ADD_SALES_ORDER => array('name' => __('Create Sales Order', true), 'class' => 'timeline_estimate timeline_add_estimate', 'transparent' => False),
            ACTION_ADD_SALES_ORDER_FROM_ESTIMATE => array('name' => __('Convert Estimate to Sales Order', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_CONVERT_ESTIMATE_TO_SALES_ORDER => array('name' => __('Convert Estimate to Sales Order', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_UPDATE_SALES_ORDER => array('name' => __('Update Sales Order', true), 'class' => 'timeline_estimate timeline_update_estimate', 'transparent' => False),
            ACTION_CLIENT_VIEW_SALES_ORDER => array('name' => __('Client Views Sales order', true), 'class' => 'timeline_estimate timeline_client_view_estimate', 'transparent' => true),
            ACTION_PRINT_SALES_ORDER => array('name' => __('Print Sales Order', true), 'class' => 'timeline_estimate timeline_print_estimate', 'transparent' => true),
            ACTION_CLIENT_PRINT_SALES_ORDER => array('name' => __('Client Prints Sales Order', true), 'class' => 'timeline_estimate timeline_client_print_estimate', 'transparent' => true),
            ACTION_CONVERT_INVOICE_TO_PRODUCTION_PLAN => array('name' => __('Convert Invoice To Production Plan', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => false),
            ACTION_CONVERT_SALES_ORDER_TO_PRODUCTION_PLAN => array('name' => __('Convert Sales Order To Production Plan', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => false),
          //purchase debit Note
           ACTION_PURCHASE_ADD_DEBIT_NOTE => array('name' => __('Create Purchase Debit Note', true), 'class' => 'timeline_add_po', 'transparent' => False),
           ACTION_PURCHASE_UPDATE_DEBIT_NOTE => array('name' => __('Update Purchase Debit Note', true), 'class' => 'timeline_edit_po', 'transparent' => False),
           ACTION_PURCHASE_DELETE_DEBIT_NOTE => array('name' => __('Delete Purchase Debit Note', true), 'class' => 'timeline_delete_po timeline_update_estimate', 'transparent' => False),
            ACTION_ADD_PURCHASE_CREDIT_NOTE => array('name' => __('Create Purchase Credit Note', true), 'class' => 'timeline_invoice timeline_add_invoice', 'transparent' => False),
            ACTION_UPDATE_PURCHASE_CREDIT_NOTE => array('name' => __('Update Purchase Credit Note', true), 'class' => 'timeline_invoice timeline_update_invoice', 'transparent' => False),
            ACTION_DELETE_PURCHASE_CREDIT_NOTE => array('name' => __('Delete Purchase Credit Note', true), 'class' => 'timeline_invoice timeline_delete_invoice', 'transparent' => False),
            ACTION_SEND_PURCHASE_CREDIT_NOTE => array('name' => __('Send Purchase Credit Note', true), 'class' => 'timeline_invoice timeline_send_invoice', 'transparent' => false),
            ACTION_PRINT_PURCHASE_CREDIT_NOTE => array('name' => __('Print Purchase Credit Note', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => true),
            ACTION_CHANGE_FOLLOW_UP_STATUS => array('name' => __('Change Follow Up Status', true), 'class' => 'timeline_follow_up timeline_follow_up_change', 'transparent' => true),
            ACTION_DOWNLOAD_PO_PAYMENT => array('name' => __('Staff Member Download Receipt', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => false),
            ACTION_PRINT_PO_PAYMENT => array('name' => __('Staff Member Receipt', true), 'class' => 'timeline_invoice timeline_print_invoice', 'transparent' => false),

        
        
        );



        $this->currentStaff = getAuthOwner('staff_id');
        $this->Invoice = GetObjectOrLoadModel('Invoice');
        $this->Client = GetObjectOrLoadModel('Client');
        $this->InvoicePayment = GetObjectOrLoadModel('InvoicePayment');
        $this->Params = $params;
        $this->PaymentStatuses = $this->Invoice->getPaymentStatuses();
        $this->ActiveNotActive = array('1'=>__('Active',true),0=>__('Not Active',true),'2'=>__t('Suspended'));
        $this->YesNo = array('1'=>__('Yes',true),0=>__('No',true));
        $this->IPS = $this->InvoicePayment->getPaymentStatus();
        $this->EstimateStatuses = Invoice::getEstimateStatuses();
        $this->SalesOrderStatuses = Invoice::getSalesOrderStatuses();

        $this->ActionsGroup = $actions_group;

        $this->PaymentMethods = InvoicePayment::getPaymentMethods();
    }

    private function showAvgPrice($action)
    {
        return !empty(json_decode($action['ActionLine']['param9'])[2]) && check_permission(Add_New_Purchase_Orders);
    }

    function shortaction2string($action) {
        $pr = GetObjectOrLoadModel('Product');
        $default_currency = $pr->get_default_currency ( ) ;
        App::import('Helper', 'Html');
        $html = new HtmlHelper();
        $dateFormats = getDateFormats('std');
        $ownerFormat = $dateFormats[getCurrentSite('date_format')];
        $staff_name = $this->getStaffName($action['ActionLine']['staff_id']);
        $text = $this->ActionKeys[$action['ActionLine']['action_key']]['desc'];
        switch ($action['ActionLine']['action_key']) {
            case ACTION_TRANSACTION_INVOICE_UPDATED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s updated the sold stock (Transaction ID: %s) of %s in invoice %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($invoice_link), strongStr(format_number(-1 * $action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_CREDITNOTE_UPDATED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s updated the sold stock (Transaction ID: %s) of %s in  credit note %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($invoice_link), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_CREDITNOTE_DELETED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s removed the %s refunded items of %s in credit note %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(1* $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_REFUND_ADDED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s received %s new items of %s in refund receipt %s, price per unit is: %s, stock balance now become: %s (Transaction ID: %s)", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2']));
                break;
            case ACTION_TRANSACTION_REFUND_UPDATED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s updated the sold stock (Transaction ID: %s) of %s in refund receipt %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($invoice_link), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_REFUND_DELETED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s removed the %s sold items of %s in refund receipt %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_INVOICE_ADDED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                $text = __("%s sold %s new items of %s in invoice %s, price per unit is: %s, stock balance now become: %s (Transaction ID: %s)", true).$extra;
                return sprintf($text, strongStr($staff_name), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2']));
                break;
            case ACTION_TRANSACTION_CREDITNOTE_ADDED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s sold %s new items of %s in credit note %s, price per unit is: %s, stock balance now become: %s (Transaction ID: %s)", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2'])).$extra;
                break;
            case ACTION_TRANSACTION_INVOICE_DELETED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s removed the %s sold items of %s in invoice %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(-1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_MANUAL_ADDED:

                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));

                if ($action['ActionLine']['param1'] >= 0) {
                    //$text = __("%s added %s items of %s to the stock manually (Transaction ID: %s), price per unit is: %s, stock balance now become: %s", true);
                    $text = __("%s added %s items of %s to the stock manually (Transaction ID: %s), price per unit is: %s, stock balance now become: %s and store %s has balance: %s and store %s has balance: %s", true);
                    $qty = $action['ActionLine']['param1'];
                } else {
                    //$text = __("%s deducted %s items of %s from the stock manually (Transaction ID: %s), price per unit is: %s, stock balance now become: %s", true);
                    $text = __("%s deducted %s items of %s from the stock manually (Transaction ID: %s), price per unit is: %s, stock balance now become: %s and store %s has balance: %s", true);
                    $qty = $action['ActionLine']['param1'] * -1;
                }
                debug($action['ActionLine']);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                // php8 fix: if else added to memic old php behaviod
                if (substr_count($text, '%s') > 8)
                    return false;
                else
                    return sprintf(
                            $text,
                            strongStr($staff_name),
                            strongStr(format_number($qty)),
                            strongStr($product_link),
                            strongStr('#' . $action['ActionLine']['param2']),
                            strongStr(format_price($action['ActionLine']['param6'])),
                            strongStr(format_number($action['ActionLine']['param8'])),
                            strongStr(format_number(json_decode($action['ActionLine']['param9'], true)[1])),
                            strongStr(format_number(json_decode($action['ActionLine']['param9'], true)[0]))) . $extra;
                break;
            case ACTION_TRANSACTION_MANUAL_UPDATED:

                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s updated the manual stock adjustment (Transaction ID: %s) of %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr(format_number($action['ActionLine']['param1'])), strongStr(format_number($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))). ", ".__("Store",true).": ". strongStr(json_decode($action['ActionLine']['param9'], true)[1]).$extra;
                break;
            case ACTION_TRANSACTION_MANUAL_DELETED:

                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s deleted the %s manually adjusted stock of %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_INVOICE_ADDED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s sold %s new items of %s in invoice %s, price per unit is: %s, stock balance now become: %s (Transaction ID: %s)", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(-1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2'])).$extra;
                break;
            case ACTION_TRANSACTION_PO_ADDED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s received %s new items of %s in purchase invoice %s, price per unit is: %s, stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr($po_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2'])).$extra;
                break;
            case ACTION_TRANSACTION_PO_UPDATED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s updated the received stock (Transaction ID: %s) of %s in purchase invoice %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".format_price(json_decode ($action['ActionLine']['param9'])[2] );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($po_link), strongStr(format_number($action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_PO_DELETED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s removed the  %s received items of %s in purchase invoice %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr($po_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_PR_ADDED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s returned %s items of %s in purchase refund %s, price per unit is: %s, stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr($po_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2'])).$extra;
                break;
            case ACTION_TRANSACTION_PR_UPDATED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s updated the returned stock (Transaction ID: %s) of %s in purchase refund %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".format_price(json_decode ($action['ActionLine']['param9'])[2] );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($po_link), strongStr(format_number($action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_PR_DELETED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s removed the  %s returned items of %s in purchase refund %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr($po_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_STORE_TRANSFER:

                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s transfered %s %s from %s to %s", true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['param2'], strongStr($product_link), strongStr($action['ActionLine']['param6']), strongStr($action['ActionLine']['param8']));
                break;
            case CLIENT_DOWNLOAD_FILE:
                $file_link = $html->link($action['ActionLine']['param3'], array('owner' => false, 'controller' => "allfiles", 'action' => "download", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('%s has started downloading %s attached with %s', TRUE);
                return sprintf($text, strongStr($client_link), strongStr($file_link), strongStr('Note #' . $action['ActionLine']['param5']));
                break;
            case ACTION_ADD_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));

                $text = __('Added a new client %s', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_ADD_WORK_ORDER:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param3'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                
                $text = __('Added a new work order %s', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_UPDATE_WORK_ORDER_STATUS:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param3'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                
                $text = __('%s updated the work order %s status into %s', true);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param1']));
                break;
            case ACTION_ADD_SUPPLIER:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));

                $text = __('Added a new supplier %s', true);
                return sprintf($text, $client_link);
                break;
            case STAFF_DOWNLOAD_FILE:
                $file_link = $html->link($action['ActionLine']['param3'], array('owner' => false, 'controller' => "allfiles", 'action' => "download", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "staffs", 'action' => "edit", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('%s has started downloading %s attached with %s', TRUE);
                return sprintf($text, strongStr($staff_name), $file_link, strongStr('Note #' . $action['ActionLine']['param5']));
                break;
            case STAFF_DOWNLOAD_INVOICE_DOCUMENT:
                $file_link = $html->link($action['ActionLine']['param3'], array('owner' => false, 'controller' => "allfiles", 'action' => "download", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "staffs", 'action' => "edit", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('%s has started downloading %s attached with %s', TRUE);
                return sprintf($text, strongStr($staff_name), $file_link, strongStr('Note #' . $action['ActionLine']['param5']));
                break;
            case CLIENT_DOWNLOAD_INVOICE_DOCUMENT:
                $file_link = $html->link($action['ActionLine']['param3'], array('owner' => false, 'controller' => "allfiles", 'action' => "download", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "staffs", 'action' => "edit", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('%s has started downloading %s attached with %s', TRUE);
                return sprintf($text, strongStr($staff_name), $file_link, strongStr('Note #' . $action['ActionLine']['param5']));
                break;
            case ACTION_SEND_EMAIL_TO_CLIENT:
                $message_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for client %s", true);
                return sprintf($text, strongStr($staff_name), $message_link, strongStr(h($action['ActionLine']['param3'])), strongStr($client_link));
                break;
            case ACTION_SEND_SMS_TO_CLIENT:
                $message_link = $html->link($action['ActionLine']['param6'], array('controller' => "sms_campaigns", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __("%s sent a message %s to phone number %s for client %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param3'])), strongStr($client_link));
                break;
            case ACTION_SEND_SMS_TO_INVOICE:
                $message_link = $html->link($action['ActionLine']['param6'], array('controller' => "sms_campaigns", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param7'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __("%s sent a message %s to phone number %s for client %s for invoice %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param3'])), strongStr($client_link), strongStr($invoice_link));
                break;
            case ACTION_SEND_SMS_TO_APPOINTMENT:
                $message_link = $html->link($action['ActionLine']['param6'], array('controller' => "sms_campaigns", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s sent a message %s to phone number %s for client %s for appointment %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param3'])), strongStr($client_link), $action['ActionLine']['primary_id']);
                break;
            case ACTION_SEND_SMS_TO_APPOINTMENT_STAFF:
                $message_link = $html->link($action['ActionLine']['param6'], array('controller' => "sms_campaigns", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $text = __("%s sent a message %s to phone number %s for staff %s for appointment %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param3'])), $action['ActionLine']['param2'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_SEND_EMAIL_TO_APPOINTMENT:
                $message = $action['ActionLine']['param6'];
                $client_link = $html->link($action['ActionLine']['param2'], array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s sent an message %s to email %s for client %s for appointment %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message), strongStr(h($action['ActionLine']['param3'])), strongStr($client_link), $action['ActionLine']['primary_id']);
                break;
            case ACTION_SEND_EMAIL_TO_APPOINTMENT_STAFF:
                $message = $action['ActionLine']['param6'];
                $text = __("%s sent a message %s to email %s for staff %s for appointment %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message), strongStr(h($action['ActionLine']['param3'])), $action['ActionLine']['param2'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_ADD_NEW_POST:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], '?' => array('note_id' => $action['ActionLine']['primary_id']), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s added a new Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));

                return  implode(', ', $texts);
                break;
            case ACTION_UPDATE_THE_POST:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], '?' => array('note_id' => $action['ActionLine']['primary_id']), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s updated the Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s with the client', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));
                return  implode(', ', $texts);
                break;
            case ACTION_ADD_NEW_WORK_ORDER_POST:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], '?' => array('note_id' => $action['ActionLine']['primary_id']), '#' => 'NotesBlock'), array('target' => '_blank'));
                $link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s added a new Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $link, strongStr($date));
                return  implode(', ', $texts);
                break;
            case ACTION_UPDATE_WORK_ORDER_POST:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], '?' => array('note_id' => $action['ActionLine']['primary_id']), '#' => 'NotesBlock'), array('target' => '_blank'));
                $link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s updated the Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s with the work order', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));
                return  implode(', ', $texts);
                break;
            case ACTION_REMOVE_THE_POST:
                $text = __('%s deleted the Note #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']));
                break;
            
            
            case ACTION_ADD_NEW_POST_INVOICE:
                $followup =  GetObjectOrLoadModel('FollowUpStatus'); $types_diff = $followup->get_types_diff(); $types_att = $types_diff[$action['ActionLine']['param2']];debug ( $types_att ) ;
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['secondary_id'], array('controller' => "invoices", 'action' =>(($types_att['name'] == "Estimate")? "view_estimate": ($types_att['name'] == "Sales Order"? "view_sales_order" : "view") ), $action['ActionLine']['primary_id'], '?' => array('note_id' => $action['ActionLine']['secondary_id']), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link(sprintf ( __($types_att['name'].' %s', true), '#' . $action['ActionLine']['param1'] ), array('controller' => "invoices", 'action' => (($types_att['name'] == "Estimate")? "view_estimate": ($types_att['name'] == "Sales Order"? "view_sales_order" : "view") ), $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s added a new Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));

                return  implode(', ', $texts);
                break;
            case ACTION_UPDATE_THE_POST_INVOICE:
                $followup =  GetObjectOrLoadModel('FollowUpStatus'); $types_diff = $followup->get_types_diff(); $types_att = $types_diff[$action['ActionLine']['param2']];
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['secondary_id'], array('controller' => "invoices", 'action' =>(($types_att['name'] == "Estimate")? "view_estimate": ($types_att['name'] == "Sales Order"? "view_sales_order" : "view") ), $action['ActionLine']['primary_id'], '?' => array('note_id' => $action['ActionLine']['secondary_id']), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link(sprintf ( __($types_att['name'].' %s', true), '#' . $action['ActionLine']['param1'] ), array('controller' => "invoices", 'action' => (($types_att['name'] == "Estimate")? "view_estimate": ($types_att['name'] == "Sales Order"? "view_sales_order" : "view") ), $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s updated the Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));
                return  implode(', ', $texts);
                break;
            case ACTION_REMOVE_THE_POST_INVOICE:
                $text = __('%s deleted the Note #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']));
                break;
            
            case ACTION_ADD_NEW_POST_PURCHASE_INVOICE:
                $followup =  GetObjectOrLoadModel('FollowUpStatus'); $types_diff = $followup->get_types_diff(); $types_att = $types_diff[$action['ActionLine']['param2']];debug ( $types_att ) ;
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['secondary_id'], array('controller' => "purchase_invoices", 'action' =>(($types_att['name'] == "Estimate")? "view_estimate": "view" ), $action['ActionLine']['primary_id'], '?' => array('note_id' => $action['ActionLine']['secondary_id']), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link(sprintf ( __($types_att['name'].' %s', true), '#' . $action['ActionLine']['param1'] ), array('controller' => "purchase_invoices", 'action' => (($types_att['name'] == "Estimate")? "view_estimate": "view" ), $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s added a new Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));

                return  implode(', ', $texts);
                break;
            case ACTION_UPDATE_THE_POST_PURCHASE_INVOICE:
                $followup =  GetObjectOrLoadModel('FollowUpStatus'); $types_diff = $followup->get_types_diff(); $types_att = $types_diff[$action['ActionLine']['param2']];
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['secondary_id'], array('controller' => "purchase_invoices", 'action' => "view", $action['ActionLine']['primary_id'], '?' => array('note_id' => $action['ActionLine']['secondary_id']), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link(sprintf ( __($types_att['name'].' %s', true), '#' . $action['ActionLine']['param1'] ), array('controller' => "purchase_invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s updated the Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));
                return  implode(', ', $texts);
                break;
            case ACTION_REMOVE_THE_POST_PURCHASE_INVOICE:
                $text = __('%s deleted the Note #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']));
                break;
            
            case ACTION_ADD_CLIENT_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('%s scheduled a new %s appointment %s for client %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['primary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_EDIT_CLIENT_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('%s updated the %s appointment %s for client %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['primary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_CLIENT_APPOINTMENT:
                $text = __('%s deleted the Appointment (%s)', true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['primary_id']);
                break;
            
            case ACTION_ADD_INVOICE_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s scheduled a new %s appointment %s for invoice %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_EDIT_INVOICE_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s updated the %s appointment %s for invoice %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_INVOICE_APPOINTMENT:
                $text = __('%s deleted the Appointment (%s)', true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['secondary_id']);
                break;
            
            
            case ACTION_ADD_ESTIMATE_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s scheduled a new %s appointment %s for estimate %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_EDIT_ESTIMATE_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s updated the %s appointment %s for estimate %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_ESTIMATE_APPOINTMENT:
                $text = __('%s deleted the Appointment (%s)', true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['secondary_id']);
                break;

              case ACTION_ADD_WORK_ORDER_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'] ), array('target' => '_blank'));
                $text = __('%s scheduled a new %s appointment %s for work order %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_EDIT_WORK_ORDER_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'] ), array('target' => '_blank'));
                $text = __('%s updated the %s appointment %s for work order %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_WORK_ORDER_APPOINTMENT:
                $text = __('%s deleted the Appointment (%s)', true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['secondary_id']);
                break;

            
            case ACTION_UPDATE_CLIENT_STATUS:
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('%s updated the client %s status into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_UPDATE_INVOICE_STATUS:
                $client_link = $html->link( '#' . $action['ActionLine']['param1'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s updated the invoice %s status into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_UPDATE_ESTIMATE_STATUS:
                $client_link = $html->link( '#' . $action['ActionLine']['param1'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s updated the estimate %s status into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_UPDATE_CLIENT_CATEGORY:
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('%s updated the client %s category into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_ASSIGN_STAFF_TO_CLIENT:
                $staff_ids = explode(',', $action['ActionLine']['param5']);
                $staff_names = explode(',', $action['ActionLine']['param6']);
                foreach ($staff_ids as $key => $staff_id) {
                    $staff_links[] = $html->link($staff_names[$key], array('controller' => "staffs", 'action' => "edit", $staff_id), array('target' => '_blank'));
                }

                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __("%s assigned the client %s to the staff members %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link), strongStr(implode(',', $staff_links)));
                break;
            case ACTION_UNASSIGN_STAFF_FROM_CLIENT:
                $staff_ids = explode(',', $action['ActionLine']['param5']);
                $staff_names = explode(',', $action['ActionLine']['param6']);
                foreach ($staff_ids as $key => $staff_id) {
                    $staff_links[] = $html->link($staff_names[$key], array('controller' => "staffs", 'action' => "edit", $staff_id), array('target' => '_blank'));
                }
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __("%s unassigned the client %s from the staff member %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link), strongStr(implode(',', $staff_links)));
                break;

            case ACTION_IMPORT_DATA:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Import Data #%s', TRUE);
                return sprintf($text, $po_link);
                break;
            case ACTION_ADD_INVOICE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Created a new invoice #%s for client %s', true);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']));
                break;
            case ACTION_ADD_CREDITNOTE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Created a new credit note #%s for client %s', true);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']));
                break;
            case ACTION_ATTACH_CREDIT_NOTE_TO_ADVANCE_PAYMENT_ON_CREATION:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param8'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['param8']), array('target' => '_blank'));
                $text = __('Attached a credit note #%s to advance payment for client #%s with amount %s %s', true);
                return sprintf(
                    $text,
                    $invoice_link,
                    $client_link,
                    format_price_simple($action['ActionLine']['param1']),
                    $action['ActionLine']['param7']
                );
                break;

            case ACTION_ADD_ADVANCE_PAYMENT:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_advance_payment", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Created a new Advance Payment #%s for client %s', true);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']));
                break;

            case ACTION_UPDATE_ADVANCE_PAYMENT_DISTRIBUTION:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_advance_payment", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s Updated Invoice #%s Advance Payment Distribution for client %s', true);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']));
                break;
            case ACTION_DELETE_ADVANCE_PAYMENT_DISTRIBUTION:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_advance_payment", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s Deleted invoice #%s Advance Payment Distribution with amount %s for client %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($client_link) );
                break;
            case ACTION_ADD_DEBITNOTE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Updated Advance Payment #%s Distribution for client %s', true);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']));
                break;
            case ACTION_ADD_REFUND:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Created a new refund receipt  #%s for client %s', true);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']));
                break;

            case ACTION_RECURRING_ADD_INVOICE:

                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $subscription_link = $html->link('#' . $action['ActionLine']['param9'], array('controller' => "invoices", 'action' => "view_subscription", $action['ActionLine']['param9']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Generated a new invoice #%s from subscription %s', true);
                return sprintf($text, $invoice_link, $subscription_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']));
                break;
            case ACTION_ADD_INVOICE_FROM_ESTIMATE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $estimate_link = $html->link('#' . $action['ActionLine']['param8'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['param9']), array('target' => '_blank'));
                $text = __('Converted estimate #%s to invoice #%s', true);
                return sprintf($text, $estimate_link, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']));
                break;

            case ACTION_UPDATE_INVOICE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Updated invoice #%s', TRUE);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']), $this->PaymentStatuses[$action['ActionLine']['param2']]);
                break;
            case ACTION_UPDATE_CREDITNOTE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Updated credit note #%s', TRUE);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']), $this->PaymentStatuses[$action['ActionLine']['param2']]);
                break;
            case ACTION_UPDATE_DEBITNOTE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Updated debit note #%s', TRUE);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']), $this->PaymentStatuses[$action['ActionLine']['param2']]);
                break;
            case ACTION_UPDATE_REFUND:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view_refund", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Updated refund receipt #%s', TRUE);
                return sprintf($text, $invoice_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']), $this->PaymentStatuses[$action['ActionLine']['param2']]);
                break;

            case ACTION_SEND_INVOICE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Sent invoice #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_SEND_REFUND:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Sent invoice #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_SEND_CREDITNOTE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Sent credit note #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_SEND_DEBITNOTE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_debitnote", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Sent debit note #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_PRINT_PURCHASE_ORDER:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "purchase_orders", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed invoice #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_PRINT_INVOICE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed invoice #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_PREVIEW_INVOICE:
                $text = __('Preview Invoice', TRUE).' #'.$action['ActionLine']['param4'];
                return $text;
                break;
            case ACTION_DELETE_INVOICE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Deleted Invoice #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_DELETE_REFUND:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Deleted refund receipt #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_DELETE_CREDITNOTE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Deleted credit notes #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_DELETE_DEBITNOTE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Deleted debit notes #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_DELETE_ESTIMATE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Deleted Invoice Estimate #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_CLIENT_PAY:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $payment_link = $html->link($action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $text = __('Made a payment #%s for invoice #%s', true);
                return sprintf($text, $payment_link, $invoice_link);
                break;

            case ACTION_ADD_INVOICE_PAYMENT:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $payment_link = $html->link($action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $text = __('Added payment #%s for invoice #%s', true);
                return sprintf($text, $payment_link, $invoice_link);
                break;
            case ACTION_ADD_PO_PAYMENT:

                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $payment_link = $html->link($action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $text = __('Added payment #%s for invoice #%s', true);
                return sprintf($text, $payment_link, $invoice_link);
                break;
            case ACTION_UPDATE_INVOICE_PAYMENT:
                $payment_link = $html->link($action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $text = __('Updated invoice payment #%s', true);
                return sprintf($text, $payment_link);
                break;
            case ACTION_UPDATE_PO_PAYMENT:
                $payment_link = $html->link($action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $text = __('Updated invoice payment #%s', true);
                return sprintf($text, $payment_link);
                break;
            case ACTION_PROCCESS_INVOICE_PAYMENT:
                $payment_link = $html->link($action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $text = __('Processed invoice payment #%s', true);
                return sprintf($text, $payment_link);
                break;
            case ACTION_DELETE_INVOICE_PAYMENT:
                $text = __('Deleted invoice payment #%s', TRUE);
                return sprintf($text, $action['ActionLine']['param5']);
                break;
            case ACTION_DELETE_CLIENT_PAYMENT:
                $text = __('Deleted Client payment credit #%s', TRUE);
                return sprintf($text, $action['ActionLine']['param5']);
                break;
            case ACTION_CLIENT_VIEW_INVIOCE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Viewed invoice #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_CLIENT_VIEW_POST:

                $text = __('Viewed Note #%s', TRUE);
                return sprintf($text, $action['ActionLine']['primary_id']);
                break;
            case ACTION_CLIENT_PRINT_INVIOCE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed invoice #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;


            case ACTION_CLIENT_READ_INVOICE_EMAIL:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $message_link = $html->link($action['ActionLine']['param8'], array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8']), array('target' => '_blank'));
                $text = __('Email #%s was read', true);
                return sprintf($text, $message_link, $invoice_link);
                break;

            case ACTION_ADD_ESTIMATE:
                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Created a new estimate #%s for client %s', true);
                return sprintf($text, $estimate_link, $client_link);
                break;
            case ACTION_UPDATE_ESTIMATE:
                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Updated estimate #%s for client #%s ', true);
                return sprintf($text, $estimate_link, $client_link);
                break;
            case ACTION_SEND_ESTIMATE:
                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $message_link = $html->link($action['ActionLine']['param8'] . '', array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8']), array('target' => '_blank'));
                $text = __('Sent message #%s for estimate #%s', true);
                return sprintf($text, $message_link, $estimate_link);
            case ACTION_PRINT_ESTIMATE:

                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed estimate #%s', true);
                return sprintf($text, $estimate_link);
                break;
            case ACTION_PRINT_SALES_ORDER:
                $sales_order_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed sales order #%s', true);
                return sprintf($text, $sales_order_link);
                break;
            case ACTION_ADD_SALES_ORDER:
                $sales_order_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Created a new sales order #%s for client %s', true);
                return sprintf($text, $sales_order_link, $client_link);
                break;
            case ACTION_UPDATE_SALES_ORDER:
                $sales_order_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Updated sales order #%s for client #%s ', true);
                return sprintf($text, $sales_order_link, $client_link);
                break;
            case ACTION_ADD_SALES_ORDER_FROM_ESTIMATE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $sales_order_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $estimate_link = $html->link('#' . $action['ActionLine']['param8'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['param9']), array('target' => '_blank'));
                $text = __('Converted estimate #%s to sales order #%s', true);
                return sprintf($text, $estimate_link, $sales_order_link, $client_link, format_price_simple($action['ActionLine']['param1']), format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3']));
                break;
            case ACTION_CONVERT_ESTIMATE_TO_SALES_ORDER:
                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $sales_order_link = $html->link($action['ActionLine']['param6'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['param9']), array('target' => '_blank'));
                $text = __('Converted estimate #%s to sales order #%s', true);
                return sprintf($text, $estimate_link, $sales_order_link);
                break;
            case ACTION_PRINT_CREDITNOTE:

                $creditnote_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed credit note #%s', true);
                return sprintf($text, $creditnote_link);
                break;
            case ACTION_PRINT_DEBITNOTE:
                $debitnote_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_debitnote", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed debit note #%s', true);
                return sprintf($text, $debitnote_link);
                break;
            case ACTION_PRINT_REFUND:

                $refund_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed refund Receipt #%s', true);
                return sprintf($text, $refund_link);
                break;
            case ACTION_CLIENT_VIEW_ESTIMATE:
                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Client viewed estimate #%s', true);
                return sprintf($text, $estimate_link);
                break;
            case ACTION_CLIENT_VIEW_SALES_ORDER:
                $sales_order_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Client viewed sales order #%s', true);
                return sprintf($text, $sales_order_link);
                break;
            case ACTION_CLIENT_PRINT_ESTIMATE:
                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Client printed estimate #%s', true);
                return sprintf($text, $estimate_link);
                break;
            case ACTION_CLIENT_PRINT_SALES_ORDER:
                $sales_order_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Client printed sales order #%s', true);
                return sprintf($text, $sales_order_link);
                break;
            case ACTION_CLIENT_ACCEPT_ESTIMATE:
                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Client accepted estimate #%s', true);
                return sprintf($text, $estimate_link);
                break;
            case ACTION_CONVERT_ESTIMATE_TO_INVOICE:
                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param6'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['param9']), array('target' => '_blank'));
                $text = __('Converted estimate #%s to invoice #%s', true);
                return sprintf($text, $estimate_link, $invoice_link);
                break;

            case ACTION_ADD_EXPENSE:
                $text = __('Added a new expense record #%s in %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_ADD_MILEAGE:
                $text = __('Added a new mileage record #%s in %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;

            case ACTION_UPDATE_EXPENSE:
                $text = __('Updated the expense record #%s in %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_UPDATE_MILEAGE:
                $text = __('Updated the mileage record #%s in %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_ADD_EXPENSECATEGORY:
                $text = __('Added a new expense category record #%s in %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_UPDATE_EXPENSECATEGORY:
                $text = __('Updated the expense category record #%s in %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_EXPENSE:
                $text = __('Deleted the expense record #%s from %s', true);
                return sprintf($text, $action['ActionLine']['param4'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_MILEAGE:
                $text = __('Deleted the mileage record #%s from %s', true);
                return sprintf($text, $action['ActionLine']['param4'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_EXPENSECATEGORY:
                $text = __('Deleted the expenses category record #%s', true);
                return sprintf($text, $action['ActionLine']['primary_id']);
                break;
            case ACTION_ADD_RECURRING_EXPENSE:
                $text = __('a new recurring expense record #%s in %s is generated', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_ADD_INCOME:
                $text = __('Added a new income record #%s in %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_UPDATE_INCOME:
                $text = __('Updated the income record #%s in %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_INCOME:
                $text = __('Deleted the income record #%s from %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_ADD_RECURRING_INCOME:
                $text = __('a new recurring income record #%s in %s is generated', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;

            case ACTION_ADD_TIME:
                $text = __('Entered record #%s in timesheet date %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_EDIT_TIME:
                $text = __('Updated record #%s in timesheet date %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_TIME:
                $text = __('Deleted record #%s from timesheet date %s', true);
                return sprintf($text, $action['ActionLine']['primary_id'], format_date($action['ActionLine']['param6']));
                break;
                break;

            case ACTION_ADD_PROJECT:
                $text = __('Added a new project "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param1'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_EDIT_PROJECT:
                $text = __('Updated the project "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param1'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_DELETE_PROJECT:
                $text = __('Deleted the project "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param1'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_ADD_GROUPPRICE:
                $text = __('Added a new prices group "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param1'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_EDIT_GROUPPRICE:
                $text = __('Updated the prices group "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param1'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_DELETE_GROUPPRICE:
                $text = __('Deleted the prices group "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param1'], $action['ActionLine']['primary_id']);
                break;

            case ACTION_ADD_ACTIVITY:
                $text = __('Added a new time-activity "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param4'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_EDIT_ACTIVITY:
                $text = __('Updated the time-activity "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param4'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_DELETE_ACTIVITY:
                $text = __('Deleted the time-activity "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param4'], $action['ActionLine']['primary_id']);
                break;

            case ACTION_ADD_PRODUCT:
                $text = __('Added a new product "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param4'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_EDIT_PRODUCT:
                $text = __('Updated the product "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param4'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_DELETE_PRODUCT:
                $text = __('Deleted the product "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param4'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_ADD_STAFF:
                $text = __('Added a new staff member "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param5'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_EDIT_STAFF:
                $text = __('Updated the staff member "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param5'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_DELETE_STAFF:
                $text = __('Deleted the staff member "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param5'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_ADD_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));

                $text = __('Added a new client %s', true);
                return sprintf($text, $client_link);
                break;
            
            case ACTION_UPDATE_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));

                $text = __('Updated the client %s', true);
                return sprintf($text, $client_link);
                break;

            case ACTION_UPDATE_CLIENT_GROUPPRICE:
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('%s updated the client %s group price into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_EDIT_WORK_ORDER:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param3'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));

                $text = __('Updated the work order %s', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_UPDATE_SUPPLIER:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));

                $text = __('Updated the supplier %s', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_DELETE_CLIENT:
                $client_link = $action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')';
                $text = __('Deleted the client %s', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_DELETE_WORK_ORDER:
                $client_link = $action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param3'] . ')';
                $text = __('Deleted the work order %s', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_DELETE_SUPPLIER:
                $client_link = $action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')';
                $text = __('Deleted the supplier %s', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_SEND_LOGIN_DETAILS:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Sent the login details to the client %s', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_CLIENT_LOGIN:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('The client %s logged in the system', true);
                return sprintf($text, $client_link);
                break;

            case ACTION_LOGIN:
                $text = __('%s logged in the system', true);
                return sprintf($text, $staff_name, $client_link);
                break;

            case ACTION_CREATED_THE_ACCOUNT:
                $text = __('%s created this account, Welcome!', true);
                return sprintf($text, $staff_name);
                break;


            case ACTION_LOGIN_AS_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Logged in as the client: %s', true);
                return sprintf($text, $client_link);
                break;

            case ACTION_LOGIN_AS_STAFF:
                $text = __('Logged in as the staff member: %s #%s ', true);
                return sprintf($text, $action['ActionLine']['param2'], $action['ActionLine']['primary_id']);
                break;

            case ACTION_ADD_RECURRING_PROFILE:
                $profile_link = $html->link('#' . $action['ActionLine']['primary_id'] . ' ' . $action['ActionLine']['param2'], array('controller' => "invoices", 'action' => "view_subscription", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("Created a recurring invoices generator %s for %s", true);
                return sprintf($text, ($profile_link), ($client_link));

                break;
            case ACTION_UPDATE_RECURRING_PROFILE:
                $profile_link = $html->link('#' . $action['ActionLine']['primary_id'] . ' ' . $action['ActionLine']['param2'], array('controller' => "invoices", 'action' => "view_subscription", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("Updated recurring invoices generator %s for %s", true);
                return sprintf($text, ($profile_link), ($client_link));

                break;
            case ACTION_DELETE_RECURRING_PROFILE:
                $profile_link = $html->link('#' . $action['ActionLine']['primary_id'] . ' ' . $action['ActionLine']['param2'], array('controller' => "invoices", 'action' => "view_subscription", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("Deleted recurring invoices generator %s for %s", true);
                return sprintf($text, ($profile_link), ($client_link));
                break;
            case ACTION_UPDATE_SMTP_SETTINGS:
                $enabled = $action['ActionLine']['param3'] ? __('Enabled', true) : __('Disabled', true);
                $text = __('%s the SMTP option', true);
                return sprintf($text, ($enabled));
                break;
            case ACTION_UPDATE_SETTINGS:
                $text = __('Updated the system settings', true);
                return sprintf($text);
                break;
                case ACTION_UPDATE_INVENTORY_SETTINGS:
                $text = __('%s update inventory settings changed %s from %s to %s', true);
                return sprintf($text,strongStr($staff_name), $action['ActionLine']['param1'],$action['ActionLine']['param2'],$action['ActionLine']['param3']);
                break;
            case ACTION_UPDATED_PLUGIN:
                $text = __('Updated the system plugin', true);
                return sprintf($text);
                break;
            case ACTION_CHNAGE_PASSWORD:
                $text = __('Updated the password', true);
                return sprintf($text, ($client_link));
                break;
            case ACTION_CHNAGE_EMAIL:
                $text = __('%s updated the email address from %s to %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), strongStr($action['ActionLine']['param5']));
                break;
            case ACTION_UPDATE_SYSTEM_LOGO_COLOR:
                $text = __('Updated the system logo/colors', true);
                return sprintf($text);
                break;
            case ACTION_CLIENT_UPDATE_PROFILE:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s updated his details', true);
                return sprintf($text, ($client_link));
                break;
            case ACTION_CLIENT_CHNAGE_PASSWORD:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s changed his password', true);
                return sprintf($text, ($client_link));
                break;
            case ACTION_CLIENT_CHNAGE_EMAIL:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('%s changed his email address from %s to %s', true);
                return sprintf($text, ($client_link), ($action['ActionLine']['param3']), ($action['ActionLine']['param5']));
                break;

            case ACTION_ADD_EMAIL_TEMPLATE:
                $text = __('Added a new email templates "%s" #%s', true);
                return sprintf($text, ($action['ActionLine']['param3']), ($action['ActionLine']['primary_id']));
                break;
            case ACTION_UPDATE_EMAIL_TEMPLATE:
                $text = __('Updated the email template "%s" #%s', true);
                return sprintf($text, $action['ActionLine']['param3'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_DELETE_EMAIL_TEMPLATE:
                $text = __('Deleted the email template "%s" #%s', true);
                return sprintf($text, ($action['ActionLine']['param3']), ($action['ActionLine']['primary_id']));
                break;
            case ACTION_ADD_INVOICE_LAYOUT:
                $text = __('Added a new invoice layout "%s" #%s', true);
                return sprintf($text, ($action['ActionLine']['param3']), ($action['ActionLine']['primary_id']));
                break;
            case ACTION_UPDATE_INVOICE_LAYOUT:
                $text = __('Updated the invoice layout "%s" #%s', true);
                return sprintf($text, ($action['ActionLine']['param3']), ($action['ActionLine']['primary_id']));
                break;
            case ACTION_DELETE_INVOICE_LAYOUT:
                $text = __('Deleted the invoice layout "%s" #%s', true);
                return sprintf($text, ($action['ActionLine']['param3']), ($action['ActionLine']['primary_id']));
                break;

            case ACTION_CREATED_THE_ACCOUNT:
                $text = __('You created your account, Welcome!', true);
                return sprintf($text);
                break;
            case ACTION_ADD_PR:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Created new purchase refund #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;
                
            case ACTION_EDIT_PR:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Update purchase refund #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;

            case ACTION_DELETE_PR:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Deleted purchase refund #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;

            case ACTION_PURCHASE_ADD_DEBIT_NOTE:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Created new purchase debit note #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;

            case ACTION_PURCHASE_UPDATE_DEBIT_NOTE:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Update purchase debit note #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;

            case ACTION_PURCHASE_DELETE_DEBIT_NOTE:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Deleted purchase debit note #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;
          
            case ACTION_ADD_PO:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Created new purchase invoice #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;

            case ACTION_UPDATE_PO:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Update purchase invoice #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;


            case ACTION_ADD_PQ:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => "purchase_quotations", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Created new purchase quotation #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;

            case ACTION_UPDATE_PQ:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => "purchase_quotations", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Update purchase quotation #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;
     
            case ACTION_DELETE_PO:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Deleted purchase invoice #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;
            case ACTION_SEND_PO:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Sent purchase invoice #%s', TRUE);
                return sprintf($text, $po_link);
                break;

            case ACTION_READ_PO_EMAIL:
                $message_link = $html->link($action['ActionLine']['param8'], array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8']), array('target' => '_blank'));
                $text = __('Email #%s was read', true);
                return sprintf($text, $message_link);
                break;
            case ACTION_SUPPLIER_VIEW_PO:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Viewed purchase invoice #%s', TRUE);
                return sprintf($text, $po_link);
                break;
            case ACTION_SUPPLIER_PRINT_PO:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed purchase invoice #%s', TRUE);
                return sprintf($text, $po_link);
                break;
            case ACTION_DELETE_CLIENT_APPOINTMENT:
                $text = __('%s deleted the Appointment (%s)', true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['primary_id']);
                break;
            case ACTION_ADD_ROLE:
                $text = __('%s added a new staff role %s (#%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_EDIT_ROLE:
                $text = __('%s updated the staff role %s (#%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_DELETE_ROLE:
                $text = __('%s deleted the staff role %s (#%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_PRINT_RECEIPT:
                $invoice_link = $html->link($action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed receipt  #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_DOWNLOAD_RECEIPT:
                $invoice_link = $html->link($action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Download receipt #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;

            //Prescription Action lines
            case ACTION_ADD_PRESCRIPTION_CLIENT:
                $text = __('Added a new Prescription', true);
                return sprintf($text, ($action['ActionLine']['param3']), ($action['ActionLine']['primary_id']));
                break;
            case ACTION_EDIT_PRESCRIPTION_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Updated the Prescription', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_DELETE_PRESCRIPTION_CLIENT:
                $client_link = $action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')';
                $text = __('Deleted the Prescription %s', true);
                return sprintf($text, $client_link);
                break;
            case ACTION_PRINT_PRESCRIPTION_CLIENT:
                $estimate_link = $html->link($action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed Prescription #%s', true);
                return sprintf($text, $estimate_link);
                break;
            case ACTION_EMAIL_SENT_PRESCRIPTION_CLIENT:
                $message_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param5']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for client %s", true);
                return sprintf($text, strongStr($staff_name), $message_link, strongStr(h($action['ActionLine']['param3'])), strongStr($client_link));
                break;
			case ACTION_JOURNAL_ACCOUNT_ADD:
				//type 0 => cat 1 => account
				if($action['ActionLine']['param1'] == JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT ){
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => '/owner/journal_accounts/list_transactions/'.$action['ActionLine']['primary_id'].'/page:0/?ASC&date_from=&date_to=&currency_code='), array('target' => '_blank'));
				}else{
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => 'folderid='.$action['ActionLine']['primary_id']), array('target' => '_blank'));
				}
                $parent_link = $html->link(' (' . $action['ActionLine']['param5'] . ')', array('controller' => "journal_cats", 'action' => "tree",'#' => 'folderid='.$action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s Added a Journal Account %s to the Account %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($account_link), strongStr($parent_link));
                break;
			case ACTION_JOURNAL_ACCOUNT_EDIT:
				//type 0 => cat 1 => account
				
				if($action['ActionLine']['param1'] == JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT ){
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => '/owner/journal_accounts/list_transactions/'.$action['ActionLine']['primary_id'].'/page:0/?ASC&date_from=&date_to=&currency_code='), array('target' => '_blank'));
				}else{
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => 'folderid='.$action['ActionLine']['primary_id']), array('target' => '_blank'));
				}
                $parent_link = $html->link(' (' . $action['ActionLine']['param5'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => 'folderid='.$action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s Edited a Journal Account %s Under the Account %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($account_link), strongStr($parent_link));
                break;
			case ACTION_JOURNAL_ACCOUNT_DELETE:
				//type 0 => cat 1 => account
				if($action['ActionLine']['param1'] == JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT ){
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => '/owner/journal_accounts/list_transactions/'.$action['ActionLine']['primary_id'].'/page:0/?ASC&date_from=&date_to=&currency_code='), array('target' => '_blank'));
				}else{
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree",  '#' => 'folderid='.$action['ActionLine']['primary_id']), array('target' => '_blank'));
				}
                $parent_link = $html->link(' (' . $action['ActionLine']['param5'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => 'folderid='.$action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __("%s Deleted a Journal Account %s From the Account %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($account_link), strongStr($parent_link));
                break;
                
            case ACTION_ASSET_DEPRECATION_ADD: 
                $text = __("%s added a new Depreciation #%s for asset %s #%s with value %s, asset current value is %s" ,true ) ;
                $extra = '' ;
                $asset_link = $html->link($action['ActionLine']['secondary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
                $asset_deprecation_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'asset_deprecations' , 'action' => 'edit' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name),$asset_deprecation_link ,strongStr($action['ActionLine']['param2']) ,$asset_link ,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param3']) ).$extra;
            case ACTION_ASSET_DEPRECATION_MODIFY: 
                $text = __("%s modified Depreciation #%s for asset %s #%s with value %s, asset current value is %s" ,true ) ;
                $extra = '' ;
                $asset_link = $html->link($action['ActionLine']['secondary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $asset_deprecation_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'asset_deprecations' , 'action' => 'edit' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name),$asset_deprecation_link ,strongStr($action['ActionLine']['param2']) ,$asset_link ,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param3']) ).$extra;
                
            case ACTION_ASSET_DEPRECATION_DELETE: 
                $text = __("%s deleted Depreciation #%s for asset %s #%s with value %s, asset current value is %s" ,true ) ;
                $extra = '' ;
                $asset_link = $html->link($action['ActionLine']['secondary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
//                $asset_deprecation_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'asset_deprecations' , 'action' => 'edit' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name),strongStr($action['ActionLine']['primary_id']) ,strongStr($action['ActionLine']['param2']) ,$asset_link ,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param3']) ).$extra;
                
            case ACTION_ASSET_ADD: 
                $text = __("%s added a new asset # %s under %s category, purchased value is %s, from %s account, asset current value is %s" ,true ) ;
                $extra = '' ;
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                if ( !empty ( $action['ActionLine']['param5']))
                {
                    $extra = sprintf(__(", with automatic %s Depreciation every %s",true) , strongStr($action['ActionLine']['param5']) ,strongStr($action['ActionLine']['param6'])  );
                }
                return sprintf($text, strongStr($staff_name),$asset_link ,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']),strongStr($action['ActionLine']['param4']) ).$extra;
            case ACTION_ASSET_DEPRECATION_ADD: 
                $text = __("%s added a new Depreciation for asset %s #%s with value %s, asset current value is %s" ,true ) ;
                $extra = '' ;
                $asset_link = $html->link($action['ActionLine']['secondary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                
                return sprintf($text, strongStr($staff_name) ,$action['ActionLine']['param2'] ,$asset_link ,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param3']) ).$extra;
            case ACTION_ASSET_MODIFY: 
                $text = __("%s updated the asset # %s under %s category, purchased value is %s, from %s account, asset current value is %s" ,true ) ;
                $extra = '' ;
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                if ( !empty ( $action['ActionLine']['param5']))
                {
                    $extra = sprintf(__(", with automatic %s Depreciation every %s",true) , strongStr($action['ActionLine']['param5']) ,strongStr($action['ActionLine']['param6'])  );
                }
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']),strongStr($action['ActionLine']['param4']) ).$extra;
            case ACTION_ASSET_DELETE: 
                $text = __("%s deleted the asset # %s under %s category, purchased value is %s, from %s account, asset current value is %s" ,true ) ;
                $extra = '' ;
                if ( !empty ( $action['ActionLine']['param5']))
                {
                    $extra = sprintf(__(", with automatic %s Depreciation every %s",true) , strongStr($action['ActionLine']['param5']) ,strongStr($action['ActionLine']['param6'])  );
                }
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']),strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']),strongStr($action['ActionLine']['param4']) ).$extra;
            case ACTION_ASSET_RE_EVALUATE: 
                $text = __("%s re-evaluated the asset #%s under %s category with value: %s" ,true ) ;
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
            case ACTION_ASSET_EDIT_RE_EVALUATE: 
                $text = __("%s edited the re-evaluation for the asset #%s under %s category with value: %s" ,true ) ;
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
            case ACTION_ASSET_DELETE_RE_EVALUATE: 
                $text = __("%s deleted the re-evaluation for the asset #%s under %s category with value: %s" ,true ) ;
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
                
            case ACTION_ASSET_WRITE_OFF: 
                $text = __("%s written of the asset #%s under %s category with value: %s" ,true ) ;
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name),$asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
           case ACTION_ASSET_DELETE_WRITE_OFF: 
                $text = __("%s deleted the write-off of the asset #%s under %s category with value: %s" ,true ) ;
               $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
            
            case ACTION_ASSET_SOLD: 
                $text = __("%s sold the asset #%s under %s category with value: %s to %s" ,true ) ;
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']));
            case ACTION_ASSET_EDIT_SOLD: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s edited the selling of the asset #%s under %s category with value: %s to %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']));
            case ACTION_ASSET_DELETE_SOLD: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s deleted the selling of the asset #%s under %s category with value: %s to %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']));

			case ACTION_CLOSED_PERIOD_ADD: 
                $closed_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'closed_periods' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s added a new %s closed period #%s from %s to %s" ,true ) ;
				$active = $action['ActionLine']['param3'] ? __('Active',true) : __('Not Active',true) ;
                return sprintf($text, strongStr($staff_name), $active, $closed_link, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
			case ACTION_CLOSED_PERIOD_EDIT: 
                $closed_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'closed_periods' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s updated the closed period #%s to be %s from %s to %s" ,true ) ;
				$active = $action['ActionLine']['param3'] ? __('Active',true) : __('Not Active',true) ;
                return sprintf($text, strongStr($staff_name), $closed_link, $active,format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
			case ACTION_CLOSED_PERIOD_DELETE: 
                $closed_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'closed_periods' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s deleted the %s closed period #%s from %s to %s" ,true ) ;
				$active = $action['ActionLine']['param3'] ? __('Active',true) : __('Not Active',true) ;
                return sprintf($text, strongStr($staff_name), $active, $closed_link, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));

            case ACTION_COST_CENTER_ADD: 
                $cost_center_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				$type_text = $action['ActionLine']['param1'] ? __('Primary',true) : __('Secondary', true);
				if($action['ActionLine']['secondary_id']){
					$parent_link = $html->link($action['ActionLine']['param4'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
					  $text = __("%s added a new %s cost center %s #%s under %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] ,$parent_link,$action['ActionLine']['param5']);
				}
                $text = __("%s added a new %s cost center %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] );
            case ACTION_COST_CENTER_EDIT: 
                $cost_center_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				$type_text = $action['ActionLine']['param1'] ? __('Primary',true) : __('Secondary', true);
				if($action['ActionLine']['secondary_id']){
					$parent_link = $html->link($action['ActionLine']['param4'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
					  $text = __("%s updated the %s cost center %s #%s under %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] ,$parent_link,$action['ActionLine']['param5']);
				}
                $text = __("%s updated the %s cost center %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] );
            case ACTION_COST_CENTER_DELETE: 
                $cost_center_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				$type_text = $action['ActionLine']['param1'] ? __('Primary',true) : __('Secondary', true);
				if($action['ActionLine']['secondary_id']){
					$parent_link = $html->link($action['ActionLine']['param4'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
					  $text = __("%s deleted the %s cost center %s #%s under %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] ,$parent_link,$action['ActionLine']['param5']);
				}
                $text = __("%s deleted the %s cost center %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] );
			case ACTION_JOURNAL_ACCOUNT_COST_CENTER_ADD: 
                $cost_center_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param6'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				
                $text = __("%s added a new %s cost center %s #%s %s%% for %s #%s account" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, $cost_center_link, $action['ActionLine']['param4'],$action['ActionLine']['param5']  ,$journal_account_link,   $action['ActionLine']['param2']);
               
            case ACTION_JOURNAL_ACCOUNT_COST_CENTER_EDIT: 
                $cost_center_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param6'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				
                $text = __("%s updated the %s cost center %s #%s %s%% for %s #%s account" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, $cost_center_link, $action['ActionLine']['param4'],$action['ActionLine']['param5'] , $journal_account_link,   $action['ActionLine']['param2']);
            case ACTION_JOURNAL_ACCOUNT_COST_CENTER_DELETE: 
                $cost_center_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param6'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				
                $text = __("%s deleted the %s cost center %s #%s %s%% for %s #%s account" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, $cost_center_link, $action['ActionLine']['param4'],$action['ActionLine']['param5'] , $journal_account_link,   $action['ActionLine']['param2']);
            case ACTION_COST_CENTER_TRANSACTION_ADD: 
                $cost_center_link = $html->link($action['ActionLine']['param5'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
                $journal_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'journals' , 'action' => 'view' ,$action['ActionLine']['param1'] ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param7'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				
                $text = __("%s added a new %s cost center transaction (amount: %s) for journal #%s to cost center %s #%s to account %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, format_price($action['ActionLine']['param8'],$default_currency),$journal_link,$cost_center_link, $action['ActionLine']['param6'], $journal_account_link,$action['ActionLine']['param4']);
            case ACTION_COST_CENTER_TRANSACTION_EDIT: 
                $cost_center_link = $html->link($action['ActionLine']['param5'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
                $journal_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'journals' , 'action' => 'view' ,$action['ActionLine']['param1'] ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param7'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				
                $text = __("%s updated %s cost center transaction (amount: %s) for journal #%s to cost center %s #%s to account %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, format_price($action['ActionLine']['param8'],$default_currency),$journal_link,$cost_center_link, $action['ActionLine']['param6'], $journal_account_link,$action['ActionLine']['param4']);
            case ACTION_COST_CENTER_TRANSACTION_DELETE: 
                $cost_center_link = $html->link($action['ActionLine']['param5'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'] ], array('target' => '_blank'));
                $journal_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'journals' , 'action' => 'view' ,$action['ActionLine']['param1'] ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param7'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				
                $text = __("%s deleted %s cost center transaction (amount: %s) for journal #%s to cost center %s #%s to account %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, format_price($action['ActionLine']['param8'],$default_currency),$journal_link,$cost_center_link, $action['ActionLine']['param6'], $journal_account_link,$action['ActionLine']['param4']);
            case ACTION_FINANCIAL_YEAR_ADD: 
                $financial_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'cost_centers' , 'action' => 'edit' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				$closed_text = $action['ActionLine']['param3'] ? __('Opened',true) :  __('Closed',true);
                $text = __("%s Added %s Financial Year from %s to %s" ,true ) ;
			return sprintf($text, strongStr($staff_name), $closed_text, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
            case ACTION_FINANCIAL_YEAR_EDIT: 
                $financial_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'cost_centers' , 'action' => 'edit' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				$closed_text = $action['ActionLine']['param3'] ? __('Opened',true) :  __('Closed',true);
                $text = __("%s Updated %s Financial Year from %s to %s" ,true ) ;
			return sprintf($text, strongStr($staff_name), $closed_text, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
            case ACTION_FINANCIAL_YEAR_DELETE: 
                $financial_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'cost_centers' , 'action' => 'edit' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
				$closed_text = $action['ActionLine']['param3'] ? __('Opened',true) :  __('Closed',true);
                $text = __("%s Deleted %s Financial Year from %s to %s" ,true ) ;
			return sprintf($text, strongStr($staff_name), $closed_text, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));

            //POS Actions
            case ACTION_POS_SESSION_START:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Started Session no. %s from %s at the %s" ,true );
                return sprintf($text, strongStr($staff_name), $session_link, $action['ActionLine']['param2'],$action['ActionLine']['param3']);
            
            case ACTION_POS_SESSION_OPEN:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Opened Session no. %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $session_link);

            case ACTION_POS_SESSION_CLOSE:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Closed Session no. %s from %s at the %s" ,true );
                return sprintf($text, strongStr($staff_name), $session_link, $action['ActionLine']['param2'],$action['ActionLine']['param3']);

            case ACTION_POS_SESSION_VALIDATE:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Validated Session no. %s closure with %s counted money and %s difference" ,true );
                return sprintf($text, strongStr($staff_name), $session_link, format_price($action['ActionLine']['param2'],$action['ActionLine']['param4']),format_price($action['ActionLine']['param3'],$action['ActionLine']['param4']));

            case ACTION_POS_SESSION_TRANSACTION_ADD:
                $text = __("%s made %s transaction with %s %s %s" ,true );
                return sprintf($text, strongStr($staff_name), __($action['ActionLine']['param3'],true), format_price($action['ActionLine']['param1'],$action['ActionLine']['param2']), __($action['ActionLine']['param4'],true),$action['ActionLine']['param5']);
            //POS Actions End
            //Requisition START 
            case ACTION_REQUISITION_ADD:
                $requistion_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'requisitions' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Added %s #%s" ,true );$extra_text = "";
                if ( !empty($action['ActionLine']['param2']))
                {
                    $extra = __("for %s",true);
                    $view_link = (json_decode($action['ActionLine']['param5'],true)?:[]);
                    $order_link = $html->link(__($action['ActionLine']['param4'],true). ' #'.$action['ActionLine']['param3'],$view_link+[$action['ActionLine']['param2']], array('target' => '_blank'));
                    $extra_text = sprintf($extra , $order_link );
                }
                return sprintf($text, strongStr($staff_name),__($action['ActionLine']['param6'],true), $requistion_link).' '.$extra_text;
            case ACTION_REQUISITION_UPDATE:
                $requistion_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'requisitions' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Updated %s #%s" ,true );$extra_text = "";
                if ( !empty($action['ActionLine']['param2']))
                {
                    $extra = __("for %s",true);
                    $view_link = json_decode($action['ActionLine']['param5'],true);
                    $order_link = $html->link(__($action['ActionLine']['param4'],true). ' #'.$action['ActionLine']['param3'],$view_link+[$action['ActionLine']['param2']], array('target' => '_blank'));
                    $extra_text = sprintf($extra , $order_link );
                }
                return sprintf($text, strongStr($staff_name),__($action['ActionLine']['param6'],true), $requistion_link).' '.$extra_text;
            case ACTION_REQUISITION_CHANGE_STORE:
                $requistion_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'requisitions' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Updated %s #%s" ,true ).$action['ActionLine']['param9'];$extra_text = "";
                if ( !empty($action['ActionLine']['param2']))
                {
                    $extra = __("for %s",true);
                    $view_link = json_decode($action['ActionLine']['param5'],true);
                    $order_link = $html->link(__($action['ActionLine']['param4'],true). ' #'.$action['ActionLine']['param3'],$view_link+[$action['ActionLine']['param2']], array('target' => '_blank'));
                    $extra_text = sprintf($extra , $order_link );
                }
                return sprintf($text, strongStr($staff_name),__($action['ActionLine']['param6'],true), $requistion_link).' '.$extra_text;
                
                
            case ACTION_REQUISITION_DELETE:
                $requistion_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'requisitions' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Deleted %s #%s" ,true );$extra_text = "";
                if ( !empty($action['ActionLine']['param2']))
                {
                    $extra = __("for %s",true);
                    $view_link = json_decode($action['ActionLine']['param5'],true);
                    $order_link = $html->link(__($action['ActionLine']['param4'],true). ' #'.$action['ActionLine']['param3'],$view_link+[$action['ActionLine']['param2']], array('target' => '_blank'));
                    $extra_text = sprintf($extra , $order_link );
                }
                return sprintf($text, strongStr($staff_name),__($action['ActionLine']['param6'],true), $requistion_link).' '.$extra_text;
            //Requisitions END
            //Stocktaking START
            case ACTION_STOCKTAKING_ADD:
                $stocktaking_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'stocktakings' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Added %s #%s" ,true );$extra_text = sprintf(__(" with status %s",true),$action['ActionLine']['param2']);
                return sprintf($text, strongStr($staff_name),__("Stocktaking",true), $stocktaking_link).' '.$extra_text;
            case ACTION_STOCKTAKING_UPDATE:
                $stocktaking_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'stocktakings' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Updated %s #%s" ,true );$extra_text = sprintf(__(" with status %s",true),$action['ActionLine']['param2']);
                return sprintf($text, strongStr($staff_name),__("Stocktaking",true), $stocktaking_link).' '.$extra_text;
            case ACTION_STOCKTAKING_DELETE:
               $stocktaking_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'stocktakings' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Deleted %s #%s" ,true );$extra_text = sprintf(__(" with status %s",true),$action['ActionLine']['param2']);
                return sprintf($text, strongStr($staff_name),__("Stocktaking",true), $stocktaking_link).' '.$extra_text;
            //Stocktaking END
            //Beta
            case ACTION_MOVE_TO_BETA:
                $text = __("%s Moved the site to Beta" ,true ) ;
                return sprintf($text, strongStr($staff_name));
            case ACTION_MOVE_TO_LIVE:
                $text = __("%s Moved the site to Live" ,true ) ;
                return sprintf($text, strongStr($staff_name));
            //Beta END
            case ACTION_ADD_PURCHASE_CREDIT_NOTE:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view_credit_note", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Created new purchase Credit Note #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;
            case ACTION_UPDATE_PURCHASE_CREDIT_NOTE:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view_credit_note", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Update purchase credit note #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;
            case ACTION_DELETE_PURCHASE_CREDIT_NOTE:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view_credit_note", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("Deleted purchase credit note #%s for supplier %s", true);
                return sprintf($text, $po_link, $supplier_link);
                break;
            case ACTION_SEND_PURCHASE_CREDIT_NOTE:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view_credit_note", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id']), array('target' => '_blank'));
                $text = __('Sent purchase credit note #%s', TRUE);
                return sprintf($text, $po_link);
                break;
            case ACTION_PRINT_PURCHASE_CREDIT_NOTE:
                $invoice_link = $html->link($action['ActionLine']['param4'], array('controller' => "purchase_invoices", 'action' => "view_credit_note", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed purchase credit note #%s', TRUE);
                return sprintf($text, $invoice_link);
                break;
            case ACTION_CHANGE_FOLLOW_UP_STATUS:
                $code = $action['ActionLine']['param4'] ?? $action['ActionLine']['primary_id'];
                $from_id = $action['ActionLine']['secondary_id'];
                $to_id = $action['ActionLine']['param1'];
                $type = __($action['ActionLine']['param2'], true);
                $url_array = json_decode($action['ActionLine']['param3'], true);
                $anchorLink = $html->link($type . " #$code", $url_array, array('target' => '_blank'));
                $FollowUpStatusModel = GetObjectOrLoadModel('FollowUpStatus');
                $followUpStatuses = $FollowUpStatusModel->find('all', ['conditions' => ['FollowUpStatus.id' => [$from_id, $to_id]]]);
                $from = $from_id;
                $to = $to_id;
                if($followUpStatuses){
                    foreach($followUpStatuses as $followUpStatus){
                        if($followUpStatus['FollowUpStatus']['id'] == $from_id){
                            $from = $followUpStatus['FollowUpStatus']['name'];
                        }
                        if($followUpStatus['FollowUpStatus']['id'] == $to_id){
                            $to = $followUpStatus['FollowUpStatus']['name'];
                        }
                    }
                }
                return sprintf(__('%s Changed Follow Up Status For %s, from %s to %s', true), '', $anchorLink, $from, $to);
            case ACTION_DOWNLOAD_PO_PAYMENT:
                $purchase_invoice_link = $html->link($action['ActionLine']['param3'], array('controller' => "purchase_invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Download receipt #%s', TRUE);
                return sprintf($text, $purchase_invoice_link);
                break;
            case ACTION_CONVERT_INVOICE_TO_PRODUCTION_PLAN:
            case ACTION_CONVERT_SALES_ORDER_TO_PRODUCTION_PLAN:
                return sprintf(
                    "Production Plan %s",
                    sprintf('<a href="/v2/owner/entity/production_plan/%s/show" target="_blank">%s</a>', $action['ActionLine']['secondary_id'], $action['ActionLine']['param5'])
                );
            case ACTION_PRINT_PO_PAYMENT:
                $purchase_invoice_link = $html->link($action['ActionLine']['param3'], array('controller' => "purchase_invoices", 'action' => "view", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $text = __('Printed receipt  #%s', TRUE);
                return sprintf($text, $purchase_invoice_link);
                break;
			default :
                return $action['ActionLine']['action_key'];
        }
    }

    public function getStaffName($staff_id, $relative = true) {
        if ($staff_id == -1)
            return __('The client', true);
        if ($staff_id == -2)
            return __('The system', true);
        if ($staff_id == -3)
            return __('The API', true);

        if ($relative && $staff_id == $this->currentStaff)
            return __('You', true);

        // warning suppress
        return $this->Slist[$staff_id] ?? null;
    }

    function longaction2string($action) {
        foreach($action['ActionLine'] as &$field){
            $matches = [];
            preg_match_all('/<script[^<]+<\/script\s*>/', $field, $matches);
            if(!empty($matches[0])){
                foreach($matches[0] as $match){
                    $field = str_replace($match, htmlentities($match), $field);
                }
            }
        }
        App::import('Helper', 'Html');
        $html = new HtmlHelper();
        $staff_name = $this->getStaffName($action['ActionLine']['staff_id']);
        $source = json_decode($action['ActionLine']['source'], true);
        if (isset($source['app_name'])) {
            $staff_name = $source['app_name'];
        }

        $dateFormats = getDateFormats('std');
        $ownerFormat = $dateFormats[getCurrentSite('date_format')];
        // warning suppress
        $extra = '';
        $default_currency = '';
        switch ($action['ActionLine']['action_key']) {
            case ACTION_POS_SESSION_REMOVE:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => 'true' ], array('target' => '_blank'));
                $text = __("%s Removed Session no. %s from %s at the %s" ,true );
                return sprintf($text, strongStr($staff_name), $session_link, $action['ActionLine']['param2'],$action['ActionLine']['param3']);

            case ACTION_UPDATE_INVENTORY_SETTINGS:
                $text = __('%s update inventory settings changed %s from %s to %s', true);
                return sprintf($text,strongStr($staff_name),__($action['ActionLine']['param1'],true),strongStr(__($action['ActionLine']['param2'], true)),strongStr(__($action['ActionLine']['param3'], true)));
                break;
            case ACTION_UPDATE_BRANCH_SETTINGS:
                $text = __(' %s Update Branch settings changed %s from %s to %s', true);
                return sprintf($text,strongStr($staff_name),__($action['ActionLine']['param1'],true),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_UPDATE_BRANCH:
                $text = __('%s Update %s Branch status to %s', true);
                $branchName = $action['ActionLine']['param1'];
                $status = $action['ActionLine']['param2'];
                return sprintf($text, strongStr($staff_name), strongStr($branchName), BranchStatusUtil::getStatusList()[$status]);
                break;
            case ACTION_ADD_WAREHOUSE:
                    $store_link = $html->link( $action['ActionLine']['param1'], array('controller' => "stores", 'action' => "edit", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s created a new warehouse with name %s Status %s Primary %s', true);
                return sprintf($text,strongStr($staff_name),strongStr($store_link),strongStr($this->ActiveNotActive[$action['ActionLine']['param3']]),strongStr($this->YesNo[$action['ActionLine']['param2']]));
                break;
            case ACTION_EDIT_WAREHOUSE:
                $store_link = $html->link( $action['ActionLine']['param1'], array('controller' => "stores", 'action' => "edit", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s update the warehouse with name %s Status %s Primary %s', true);
                return sprintf($text,strongStr($staff_name),strongStr($store_link),strongStr($this->ActiveNotActive[$action['ActionLine']['param3']]),strongStr($this->YesNo[$action['ActionLine']['param2']]));
                break;
                case ACTION_DELETE_WAREHOUSE:
                $store_link = $html->link( $action['ActionLine']['param1'], array('controller' => "stores", 'action' => "edit", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s deleted the warehouse with name %s Status %s Primary %s', true);
                return sprintf($text,strongStr($staff_name),strongStr($store_link),strongStr($this->ActiveNotActive[$action['ActionLine']['param3']]),strongStr($this->YesNo[$action['ActionLine']['param2']]));
                break;
            case ACTION_ADD_INVOICE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s created a new %s invoice  %s for %s totalling %s, and balance due: %s", true);
                if($action['ActionLine']['param7']){
                    $session_name = $action['ActionLine']['param7'];
                    $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['param8'], 'owner' => 'true' ], array('target' => '_blank'));
                    $text .= sprintf(__(", during POS Session no. %s",true),$session_link);
                }

                if($action['ActionLine']['param9']){
                    $salesOrderNo = $action['ActionLine']['param9'];
                    $salesOrderId = $action['ActionLine']['param8'];
                    $salesOrderLink = $html->link(
                        $salesOrderNo , [
                            'controller' => 'invoices' ,
                            'action' => 'view_sales_order',
                            $salesOrderId
                        ],
                        ['target' => '_blank']
                    );
                    $text .= sprintf(__(", from sales order %s",true), $salesOrderLink);
                }

                return sprintf($text, strongStr($staff_name), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple(($action['ActionLine']['param1'] ?? 0) - ($action['ActionLine']['param3'] ?? 0))));

                break;
            case ACTION_ADD_CREDITNOTE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s created a new credit note %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])));

                break;
            case ACTION_ADD_DEBITNOTE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s created a new debit note %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])));

                break;

            case ACTION_ATTACH_CREDIT_NOTE_TO_ADVANCE_PAYMENT_ON_CREATION:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link($action['ActionLine']['param8'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('Attached a credit note #%s to advance payment for client #%s with amount %s %s', true);
                return sprintf(
                    $text,
                    $invoice_link,
                    $client_link,
                    format_price_simple($action['ActionLine']['param1']),
                    $action['ActionLine']['param7']
                );
                break;

            case ACTION_ADD_ADVANCE_PAYMENT:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_advance_payment", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s created a new advance payment %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])));

                break;

            case ACTION_UPDATE_ADVANCE_PAYMENT_DISTRIBUTION:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s Updated Invoice #%s Advance Payment Distribution for client %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])));

                break;

            case ACTION_DELETE_ADVANCE_PAYMENT_DISTRIBUTION:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s Deleted invoice #%s Advance Payment Distribution with amount %s for client %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($client_link) );
                break;
            case ACTION_ADD_REFUND:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s created a new  refund Receipt  %s for %s totalling %s", true);
                if($action['ActionLine']['param7']){
                    $session_name = $action['ActionLine']['param7'];
                    $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['param8'] ], array('target' => '_blank'));
                    $text .= sprintf(__(", during POS Session no. %s",true),$session_link);
                }
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])));

                break;

            case ACTION_RECURRING_ADD_INVOICE:

                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $subscription_link = $html->link('#' . $action['ActionLine']['param9'], array('controller' => "invoices", 'action' => "view_subscription", $action['ActionLine']['param9'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s generated a new recurring %s invoice %s from subscription %s for %s totalling %s, and balance due: %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]), strongStr($invoice_link), strongStr($subscription_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])));
                break;
            case ACTION_ADD_INVOICE_FROM_ESTIMATE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param8'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['param9'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s converted estimate %s to invoice %s for %s totalling %s, and balance due: %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($estimate_link), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])));
                break;

            case ACTION_UPDATE_INVOICE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the invoice %s for %s totalling %s, balance due: %s, and status: %s", true);
                if(!empty($action['ActionLine']['param7'])){
                    $text .= sprintf(__(", old number: %s", true), $action['ActionLine']['param7']);
                }
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_UPDATE_CREDITNOTE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the credit note %s for %s totalling %s", true);
                if(!empty($action['ActionLine']['param7'])){
                    $text .= sprintf(__(", old number: %s", true), $action['ActionLine']['param7']);
                }
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_UPDATE_DEBITNOTE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_debitnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the debit note %s for %s totalling %s", true);
                if(!empty($action['ActionLine']['param7'])){
                    $text .= sprintf(__(", old number: %s", true), $action['ActionLine']['param7']);
                }
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_UPDATE_REFUND:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the refund receipt %s for %s totalling %s", true);
                if(!empty($action['ActionLine']['param7'])){
                    $text .= sprintf(__(", old number: %s", true), $action['ActionLine']['param7']);
                }
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_SEND_INVOICE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $message_link = $html->link('(#' . $action['ActionLine']['param8'] . ')', array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for invoice %s ", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param2'])), strongStr($invoice_link));
                break;
            case ACTION_SEND_REFUND:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $message_link = $html->link('(#' . $action['ActionLine']['param8'] . ')', array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for refund receipt  %s ", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param2'])), strongStr($invoice_link));
                break;
            case ACTION_SEND_CREDITNOTE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $message_link = $html->link('(#' . $action['ActionLine']['param8'] . ')', array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for credit note  %s ", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param2'])), strongStr($invoice_link));
                break;
            case ACTION_SEND_DEBITNOTE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_debitnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $message_link = $html->link('(#' . $action['ActionLine']['param8'] . ')', array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for debit note %s ", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param2'])), strongStr($invoice_link));
                break;
            case ACTION_PRINT_PURCHASE_ORDER:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "purchase_orders", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s printed the invoice %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link));
            case ACTION_PRINT_INVOICE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s printed the invoice %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link));
            case ACTION_PREVIEW_INVOICE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = strongStr($staff_name).' '.__('Previewed Invoice',true).' #'.$action['ActionLine']['param4'].', '.__('Client',true).': '.$client_link.', ' .__('Total',true).': '.format_price_simple($action['ActionLine']['param1']);
                return $text;
                break;

            case ACTION_DELETE_INVOICE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s deleted the invoice %s for %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link));
                break;
            case ACTION_DELETE_REFUND:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s deleted the refund receipt %s for %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link));
                break;
            case ACTION_DELETE_CREDITNOTE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s deleted the credit note %s for %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link));
                break;
            case ACTION_DELETE_DEBITNOTE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_debitnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s deleted the debit note %s for %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link));
                break;

            case ACTION_DELETE_ESTIMATE:
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s deleted the invoice estimate %s for %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link), strongStr($client_link));
                break;

            case ACTION_DELETE_SALES_ORDER:
                $clientLink = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $salesOrderLink = $html->link('#' . $action['ActionLine']['param4'], [
                    'controller' => "invoices",
                    'action' => "view_sales_order",
                    $action['ActionLine']['primary_id']
                ], ['target' => '_blank']
                );
                $text = __("%s deleted the sales order %s for %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($salesOrderLink), strongStr($clientLink));
                break;

            case ACTION_CLIENT_PAY:
                $payment_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The client made a new payment %s (amount: %s, method: %s%s and status: %s) for the invoice %s, invoice total is: %s, balance due now is: %s, and invoice status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr($invoice_link), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_ADD_INVOICE_PAYMENT:
                
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $payment_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                if($action['ActionLine']['param8']=='client_credit' and $action['ActionLine']['staff_id']=="-2"){
                $text = __("The system has updated the invoice #%s to: %s, Invoice Total: %s, Balance Due: %s", true);                    
                return sprintf($text,$invoice_link,strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]),strongStr(format_price_simple($action['ActionLine']['param6'])),strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])));                                
                }else{
                $text = __("%s added a new payment %s  (amount: %s, method: %s%s and status: %s) to the invoice %s, invoice total is: %s, balance due now is: %s ,and invoice status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(__($this->PaymentMethods[$action['ActionLine']['param8']],true)), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr($invoice_link), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));                
                }

                break;
            case ACTION_ADD_CLIENT_PAYMENT:
                
                $payment_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param3'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s added a new payment %s  (amount: %s, method: %s%s and status: %s) to the client %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), __(strongStr($this->PaymentMethods[$action['ActionLine']['param4']]),true), $extra, strongStr($this->IPS[$action['ActionLine']['param2']]), strongStr($invoice_link), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_ADD_SUPPLIER_CREDIT:

                $payment_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "purchase_order_payments", 'action' => "view", $action['ActionLine']['param3'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s added a new payment %s  (amount: %s, method: %s%s and status: %s) to the supplier %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), __(strongStr($this->PaymentMethods[$action['ActionLine']['param4']]),true), $extra, strongStr($this->IPS[$action['ActionLine']['param2']]), strongStr($invoice_link), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_CLIENT_ADD_CLIENT_PAYMENT:
                $payment_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param3'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s added a new payment %s (amount: %s, method: %s%s and status: %s)", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param4']]), $extra, strongStr($this->IPS[$action['ActionLine']['param2']]), strongStr($invoice_link), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_EDIT_CLIENT_CREDIT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));                
                $payment_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s edit payment %s (amount: %s, method: %s and status: %s) for client %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param5']]), strongStr($this->IPS[$action['ActionLine']['param6']]),$client_link);
                break;
            case ACTION_EDIT_SUPPLIER_CREDIT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $payment_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "purchase_order_payments", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s edit payment %s (amount: %s, method: %s and status: %s) for supplier %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param5']]), strongStr($this->IPS[$action['ActionLine']['param6']]),$client_link);
                break;

            case ACTION_ADD_CLIENT_OPENING_BALANCE:

                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s add opening balance #%s (amount: %s) for client %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(($action['ActionLine']['param1'])), strongStr($client_link));
                break;
                case ACTION_ADD_SUPPLIER_OPENING_BALANCE:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s add opening balance #%s (amount: %s) for supplier %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(($action['ActionLine']['param1'])), strongStr($client_link));
                break;
            case ACTION_EDIT_OPENING_BALANCE:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s edit opening balance #%s (amount: %s) for client %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(($action['ActionLine']['param1'])), strongStr($client_link));
                break;
            case ACTION_DELETE_CLIENT_OPENING_BALANCE:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s delete opening balance #%s (amount: %s) for client %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(($action['ActionLine']['param1'])), strongStr($client_link));
                break;
                case ACTION_DELETE_SUPPLIER_OPENING_BALANCE:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s delete opening balance #%s (amount: %s) for supplier %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(($action['ActionLine']['param1'])), strongStr($client_link));
                break;
                case ACTION_EDIT_SUPPLIER_OPENING_BALANCE:

                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s edit opening balance #%s (amount: %s) for supplier %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(($action['ActionLine']['param1'])), strongStr($client_link));
                break;
            case ACTION_UPDATE_INVOICE_PAYMENT:
                $payment_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the payment %s (amount: %s, method: %s%s and status: %s), invoice total is: %s, balance due now is: %s , and invoice status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';

                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(__($this->PaymentMethods[$action['ActionLine']['param8']],true)), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_PROCCESS_INVOICE_PAYMENT:
                $payment_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The payment gateway processed the payment %s (amount: %s, method: %s%s and status: %s), invoice total is: %s, balance due now is: %s , and invoice status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';

                return sprintf($text, strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_DELETE_INVOICE_PAYMENT:
                                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $payment_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "invoice_payments", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                if($action['ActionLine']['param8']=='client_credit' and $action['ActionLine']['staff_id']=="-2"){
                $text = __("The system has updated the invoice #%s to: %s, Invoice Total: %s, Balance Due: %s", true);                    
                return sprintf($text,$invoice_link,strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]),strongStr(format_price_simple($action['ActionLine']['param6'])),strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])));                                                
                }else{
                $text = __("%s deleted the payment %s  (amount: %s, method: %s%s and status: %s), invoice total is: %s, balance due now is: %s , and invoice status is: %s", true);
                }
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';

                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param5']), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_DELETE_CLIENT_PAYMENT:
                $text = __("%s deleted the payment %s  (amount: %s, method: %s%s and status: %s)", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';

                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param5']), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_DELETE_SUPPLIER_PAYMENT:
                $text = __("%s deleted the payment %s  (amount: %s, method: %s%s and status: %s)", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';

                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param5']), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;


            case ACTION_ADD_PO_PAYMENT:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "purchase_orders", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $payment_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "purchase_order_payments", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s added a new payment %s  (amount: %s, method: %s%s and status: %s) to the purchase invoice %s, invoice total is: %s, balance due now is: %s ,and purchase invoice status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr($invoice_link), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_UPDATE_PO_PAYMENT:
                $payment_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "purchase_order_payments", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the payment %s (amount: %s, method: %s%s and status: %s), purchase invoice total is: %s, balance due now is: %s , and purchase invoice status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_DELETE_PO_PAYMENT:
                $text = __("%s deleted the payment %s  (amount: %s, method: %s%s and status: %s), purchase invoice total is: %s, balance due now is: %s , and purchase invoice status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';

                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param5']), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_ADD_PCN_PAYMENT:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "purchase_orders", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $payment_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "purchase_order_payments", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s added a new payment %s  (amount: %s, method: %s%s and status: %s) to the purchase Credit Note %s,  total is: %s, balance due now is: %s ,and purchase Credit Note status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr($invoice_link), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_UPDATE_PCN_PAYMENT:
                $payment_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "purchase_order_payments", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the payment %s (amount: %s, method: %s%s and status: %s),  total is: %s, balance due now is: %s , and purchase Credit Note status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';
                return sprintf($text, strongStr($staff_name), strongStr($payment_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_DELETE_PCN_PAYMENT:
                $text = __("%s deleted the payment %s  (amount: %s, method: %s%s and status: %s),  total is: %s, balance due now is: %s , and purchase Credit Note status is: %s", true);
                $extra = '';
                if (!empty($action['ActionLine']['param9']))
                    $extra = ', ' . __('ref', true) . ': ' . strongStr($action['ActionLine']['param9']) . '';

                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param5']), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PaymentMethods[$action['ActionLine']['param8']]), $extra, strongStr($this->IPS[$action['ActionLine']['param7']]), strongStr(format_price_simple($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param6'] - $action['ActionLine']['param3'])), strongStr($this->PaymentStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_CLIENT_VIEW_INVIOCE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The client viewed the invoice %s on the system", true);
                return sprintf($text, strongStr($invoice_link));
                break;
            case ACTION_CLIENT_VIEW_POST:

                $note_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], '?' => array('note_id' => $action['ActionLine']['primary_id'], 'owner' => 'true'), '#' => 'NotesBlock'), array('target' => '_blank'));
                $text = __("The client viewed the Note %s on the system", true);
                return sprintf($text, $note_link);
                break;
            case ACTION_CLIENT_PRINT_INVIOCE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The client printed the invoice %s on the system", true);
                return sprintf($text, strongStr($invoice_link));
                break;


            case ACTION_CLIENT_READ_INVOICE_EMAIL:
                $message_link = $html->link('#' . $action['ActionLine']['param8'], array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The email message %s was read", true);
                return sprintf($text, strongStr($message_link));
                break;

            case ACTION_ADD_ESTIMATE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s created a new %s estimate %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($this->EstimateStatuses[$action['ActionLine']['param2']]), strongStr($estimate_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;
            case ACTION_UPDATE_ESTIMATE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the estimate %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($estimate_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;
            case ACTION_SEND_ESTIMATE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $message_link = $html->link('(#' . $action['ActionLine']['param8'] . ')', array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for estimate %s ", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param2'])), strongStr($estimate_link));
            case ACTION_PRINT_ESTIMATE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s printed the estimate %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($estimate_link));
                break;
            case ACTION_PRINT_SALES_ORDER:
                $sales_order_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s printed the sales order %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($sales_order_link));
                break;
            case ACTION_ADD_SALES_ORDER:
                $sales_order_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s created a new %s sales order %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($this->SalesOrderStatuses[$action['ActionLine']['param2']]), strongStr($sales_order_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;
            case ACTION_UPDATE_SALES_ORDER:
                $sales_order_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the sales order %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($sales_order_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;
            case ACTION_ADD_SALES_ORDER_FROM_ESTIMATE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param8'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['param9'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $sales_order_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s converted estimate %s to sales order %s for %s totalling %s, and balance due: %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($estimate_link), strongStr($sales_order_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr(format_price_simple($action['ActionLine']['param1'] - $action['ActionLine']['param3'])));
                break;
            case ACTION_CONVERT_ESTIMATE_TO_SALES_ORDER:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $sales_order_link = $html->link($action['ActionLine']['param6'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['param9'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s converted the estimate %s to an sales order %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($estimate_link), strongStr($sales_order_link));
                break;
            case ACTION_PRINT_CREDITNOTE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s printed the credit note %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($estimate_link));
                break;
            case ACTION_PRINT_DEBITNOTE:
                $debitnote_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_debitnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s printed the debit note %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($debitnote_link));
                break;
            case ACTION_PRINT_REFUND:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s printed the refund receipt %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($estimate_link));
                break;
            case ACTION_CLIENT_VIEW_ESTIMATE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The client viewed the estimate %s on the system", true);
                return sprintf($text, strongStr($estimate_link));
                break;
            case ACTION_CLIENT_VIEW_SALES_ORDER:
                $sales_order_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The client viewed the sales order %s on the system", true);
                return sprintf($text, strongStr($sales_order_link));
                break;
            case ACTION_CLIENT_PRINT_ESTIMATE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The client printed the estimate %s on the system", true);
                return sprintf($text, strongStr($estimate_link));
                break;
            case ACTION_CLIENT_PRINT_SALES_ORDER:
                $sales_order_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The client printed the sales order %s on the system", true);
                return sprintf($text, strongStr($sales_order_link));
                break;
            case ACTION_CLIENT_ACCEPT_ESTIMATE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The client accepted the estimate %s", true);
                return sprintf($text, strongStr($estimate_link));
                break;

            case ACTION_CONVERT_ESTIMATE_TO_INVOICE:
                $estimate_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param6'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['param9'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s converted the estimate %s to an invoice %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($estimate_link), strongStr($invoice_link));
                break;

            case ACTION_ADD_EXPENSE:
                $expense_link = $html->link($action['ActionLine']['primary_id'], array('controller' => "expenses", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';
                if (!empty($action['ActionLine']['param2']))
                    $extra.=', ' . __('Category', true) . ': ' . strongStr($action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Vendor', true) . ': ' . strongStr($action['ActionLine']['param3']);

                $text = __('%s added a new expense record #%s (Date: %s, Amount: %s%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($expense_link), strongStr(format_date($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param1'])), $extra);
                break;
            case ACTION_ADD_MILEAGE:
                $expense_link = $html->link($action['ActionLine']['primary_id'], array('controller' => "expenses", 'action' => "view", $action['ActionLine']['primary_id'],"?"=>['is_mileage'=>"1"], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';
                if (!empty($action['ActionLine']['param2']))
                    $extra.=', ' . __('Category', true) . ': ' . strongStr($action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Vendor', true) . ': ' . strongStr($action['ActionLine']['param3']);

                $text = __('%s added a new mileage record #%s (Date: %s, Amount: %s%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($expense_link), strongStr(format_date($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param1'])), $extra);
                break;

            case ACTION_UPDATE_EXPENSE:
                $expense_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "expenses", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';
                if (!empty($action['ActionLine']['param2']))
                    $extra.=', ' . __('Category', true) . ': ' . strongStr($action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Vendor', true) . ': ' . strongStr($action['ActionLine']['param3']);

                $text = __('%s updated the expense record #%s (Date: %s, Amount: %s%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($expense_link), strongStr(format_date($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param1'])), $extra);
                break;
            case ACTION_UPDATE_MILEAGE:
                $expense_link = $html->link( $action['ActionLine']['primary_id'], array('controller' => "expenses", 'action' => "view", $action['ActionLine']['primary_id'],"?"=>['is_mileage'=>"1"], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';
                if (!empty($action['ActionLine']['param2']))
                    $extra.=', ' . __('Category', true) . ': ' . strongStr($action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Vendor', true) . ': ' . strongStr($action['ActionLine']['param3']);

                $text = __('%s updated the mileage record #%s (Date: %s, Amount: %s%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($expense_link), strongStr(format_date($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param1'])), $extra);
                break;

                
            case ACTION_ADD_EXPENSECATEGORY:
       
                $text = __('%s added a new expense category record #%s ', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']));

                break;
            case ACTION_UPDATE_EXPENSECATEGORY:
                $text = __('%s updated the expense category record #%s Name %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(($action['ActionLine']['param1'])));
                break;
            case ACTION_DELETE_EXPENSE:

                if($staff_name == 'The API' && !empty($action['ActionLine']['param7'])){
                    $text = __("%s deleted the expense record #%s from %s , Source : %s", true);
                    return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param4']), strongStr(format_date($action['ActionLine']['param6']) , true), $action['ActionLine']['param7']);
                    break;
                }else{
                    $text = __('%s deleted the expense record #%s from %s', true);
                    return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param4']), strongStr(format_date($action['ActionLine']['param6'])));
                    break;
                }

            case ACTION_DELETE_MILEAGE:
                $text = __('%s deleted the mileage record #%s from %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param4']), strongStr(format_date($action['ActionLine']['param6'])));
                break;
            case ACTION_DELETE_EXPENSECATEGORY:
                $text = __('%s deleted the expenses category record #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_ADD_RECURRING_EXPENSE:
                $extra = '';
                if (!empty($action['ActionLine']['param2']))
                    $extra.=', ' . __('Category', true) . ': ' . strongStr($action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Vendor', true) . ': ' . strongStr($action['ActionLine']['param3']);

                $text = __('%s generated a new recurring expense record #%s (Date: %s, Amount: %s%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(format_date($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param1'])), $extra);
                break;
            case ACTION_ADD_INCOME:
                $expense_link = $html->link($action['ActionLine']['primary_id'], array('controller' => "incomes", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';
                if (!empty($action['ActionLine']['param2']))
                    $extra.=', ' . __('Category', true) . ': ' . strongStr($action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Vendor', true) . ': ' . strongStr($action['ActionLine']['param3']);

                $text = __('%s added a new income record #%s (Date: %s, Amount: %s%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($expense_link), strongStr(format_date($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param1'])), $extra);
                break;
            case ACTION_UPDATE_INCOME:
                $expense_link = $html->link($action['ActionLine']['primary_id'], array('controller' => "incomes", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';
                if (!empty($action['ActionLine']['param2']))
                    $extra.=', ' . __('Category', true) . ': ' . strongStr($action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Vendor', true) . ': ' . strongStr($action['ActionLine']['param3']);

                $text = __('%s updated the income record #%s (Date: %s, Amount: %s%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($expense_link), strongStr(format_date($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param1'])), $extra);
                break;
            case ACTION_DELETE_INCOME:
                $text = __('%s deleted the income record #%s from %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(format_date($action['ActionLine']['param6'])));
                break;
            case ACTION_ADD_RECURRING_INCOME:
                $extra = '';
                if (!empty($action['ActionLine']['param2']))
                    $extra.=', ' . __('Category', true) . ': ' . strongStr($action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Vendor', true) . ': ' . strongStr($action['ActionLine']['param3']);

                $text = __('%s generated a new recurring income record #%s (Date: %s, Amount: %s%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr(format_date($action['ActionLine']['param6'])), strongStr(format_price_simple($action['ActionLine']['param1'])), $extra);
                break;


            case ACTION_ADD_TIME:
                $extra = '';
                if (!empty($action['ActionLine']['param4']))
                    $extra.=', ' . __('Project', true) . ': ' . strongStr($action['ActionLine']['param4'] . ' #' . $action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param5']))
                    $extra.=', ' . __('Activity', true) . ': ' . strongStr($action['ActionLine']['param5'] . ' #' . $action['ActionLine']['param3']);
                $text = __('%s entered %s hours in the timesheet (Record: %s, Date: %s%s) ', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr('#' . $action['ActionLine']['primary_id']), strongStr(format_date($action['ActionLine']['param6'])), $extra);
                break;
            case ACTION_EDIT_TIME:
                $extra = '';
                if (!empty($action['ActionLine']['param4']))
                    $extra.=', ' . __('Project', true) . ': ' . strongStr($action['ActionLine']['param4'] . ' #' . $action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param5']))
                    $extra.=', ' . __('Activity', true) . ': ' . strongStr($action['ActionLine']['param5'] . ' #' . $action['ActionLine']['param3']);
                $text = __('%s updated timesheet record %s (Hours: %s, Date: %s%s) ', true);
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['primary_id']), strongStr($action['ActionLine']['param1']), strongStr(format_date($action['ActionLine']['param6'])), $extra);
                break;
            case ACTION_DELETE_TIME:
                $extra = '';
                if (!empty($action['ActionLine']['param4']))
                    $extra.=', ' . __('Project', true) . ': ' . strongStr($action['ActionLine']['param4'] . ' #' . $action['ActionLine']['param2']);
                if (!empty($action['ActionLine']['param5']))
                    $extra.=', ' . __('Activity', true) . ': ' . strongStr($action['ActionLine']['param5'] . ' #' . $action['ActionLine']['param3']);
                $text = __('%s deleted timesheet record %s (Hours: %s, Date: %s%s) ', true);
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['primary_id']), strongStr($action['ActionLine']['param1']), strongStr(format_date($action['ActionLine']['param6'])), $extra);
                break;

            case ACTION_ADD_PROJECT:
                $text = __('%s added a new project "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_EDIT_PROJECT:
                $text = __('%s updated the project "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_DELETE_PROJECT:
                $text = __('%s deleted the project "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_ADD_GROUPPRICE:
                $text = __('%s added a new prices group "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_EDIT_GROUPPRICE:
                $text = __('%s updated the prices group "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_DELETE_GROUPPRICE:
                $text = __('%s deleted the prices group "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;

            case ACTION_ADD_ACTIVITY:
                $text = __('%s added a new time-activity "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param4']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_EDIT_ACTIVITY:
                $text = __('%s updated the time-activity "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param4']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_DELETE_ACTIVITY:
                $text = __('%s deleted the time-activity "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param4']), strongStr($action['ActionLine']['primary_id']));
                break;

            case ACTION_ADD_PRODUCT:
                if ( empty ( $action['ActionLine']['param2']) ){
                    $text = __('Added a new product "%s" #%s', true);
                    return sprintf($text, $action['ActionLine']['param4'], $action['ActionLine']['primary_id']);
                }else {
                    $text = __('%s added a new product "%s" #%s, default price: %s', true);
                    return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param4']), strongStr($action['ActionLine']['primary_id']), strongStr($action['ActionLine']['param2']));
                }
                
                break;
            case ACTION_EDIT_PRODUCT:
                if ( empty ( $action['ActionLine']['param2']) ){
                    $text = __('Updated the product "%s" #%s', true);
                    return sprintf($text, $action['ActionLine']['param4'], $action['ActionLine']['primary_id']);
                }
                else if ( !empty ( $action['ActionLine']['param9']) ){
                    $text = __('Updated the product #%s tracking type from : %s to : %s', true);
                    $trackingTypes = TrackStockUtil::getTrackingTypes();
                    return sprintf($text, $action['ActionLine']['primary_id'],strongStr($trackingTypes[$action['ActionLine']['param8']]), strongStr($trackingTypes[$action['ActionLine']['param9']]));
                }
                else if ( !empty ( $action['ActionLine']['param7']) ){
                    $text = __('Updated the product #%s Unit template from : %s to : %s', true);
                    $oldUnitTemplate = $action['ActionLine']['param7'];
                    $newUnitTemplate = $action['ActionLine']['param8'];
                    return sprintf($text, $action['ActionLine']['primary_id'],strongStr($oldUnitTemplate), strongStr($newUnitTemplate));
                }
                else {
                    $text = __('%s  updated the product "%s" #%s, default price: %s', true);
                    return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param4']), strongStr($action['ActionLine']['primary_id']), strongStr($action['ActionLine']['param2']));
                }
                
                break;
            case ACTION_DELETE_PRODUCT:
                $text = __('%s  deleted the product "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param4']), strongStr($action['ActionLine']['primary_id']));
                break;

            case ACTION_ADD_STAFF:
                $text = __('%s added a new staff member "%s" #%s, email address: %s, role: %s #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param5']), strongStr($action['ActionLine']['primary_id']), strongStr($action['ActionLine']['param2']), strongStr($action['ActionLine']['param4']), strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_EDIT_STAFF:
                $text = __('%s Edit the staff member "%s" #%s, email address: %s, role: %s #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param5']), strongStr($action['ActionLine']['primary_id']), strongStr($action['ActionLine']['param2']), strongStr($action['ActionLine']['param4']), strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_DELETE_STAFF:
                $text = __('%s deleted the staff member "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param5']), strongStr($action['ActionLine']['primary_id']), strongStr($action['ActionLine']['param2']), strongStr($action['ActionLine']['param4']), strongStr($action['ActionLine']['param3']));
                break;
            //Nour Mod
            case ACTION_DELETE_SUPPLIER:
                $client_link = $action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')';
                $text = __('%s deleted the supplier %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;
            case ACTION_UPDATE_SUPPLIER:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Email', true) . ': ' . strongStr($action['ActionLine']['param3']) . '';
                if (!empty(trim($action['ActionLine']['param5'])))
                    $extra.=', ' . __('Phone', true) . ': ' . strongStr($action['ActionLine']['param5']) . '';
                if (!empty(trim($action['ActionLine']['param6']))) {
                    $updates = json_decode($action['ActionLine']['param6'], true);
                    foreach ($updates as $update) {
                        $extra .= ', ' . __('Edit', true) . ': ' .  strongStr(__t($update['key'], true)) . ' ' . (!empty($update['old_value'])? __('From', true) . ': <s>' . strongStr($update['old_value']) . '</s> ' : '') . __('To', true) . ': ' . strongStr($update['new_value']) . '';
                    }

                }
                $text = __('%s updated the supplier %s %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link), $extra);
                break;
            case ACTION_ADD_SUPPLIER:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';

                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Email', true) . ': ' . strongStr($action['ActionLine']['param3']) . '';
                if (!empty(trim($action['ActionLine']['param5'])))
                    $extra.=', ' . __('Phone', true) . ': ' . strongStr($action['ActionLine']['param5']) . '';

                $text = __('%s added a new supplier %s %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link), $extra);
                break;
            //$this->add_actionline(ACTION_ADD_CLIENT, array('primary_id'=>$client_id,'secondary_id'=>$client_id,'param2'=>$this->data['Client']['business_name'],'param3'=>$this->data['Client']['email'],'param4'=>$this->data['Client']['client_number'],'param5'=>$this->data['Client']['phone1'].' '.$this->data['Client']['phone2']));	
            case ACTION_ADD_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';

                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Email', true) . ': ' . strongStr($action['ActionLine']['param3']) . '';
                if (!empty(trim($action['ActionLine']['param5'])))
                    $extra.=', ' . __('Phone', true) . ': ' . strongStr($action['ActionLine']['param5']) . '';

                $text = __('%s added a new client %s %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link), $extra);
                break;
            case ACTION_ADD_WORK_ORDER:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param3'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';
                $text = __('%s added a new work order %s %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link), $extra);
                break;
            case ACTION_UPDATE_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $extra = '';
                if (!empty($action['ActionLine']['param3']))
                    $extra.=', ' . __('Email', true) . ': ' . strongStr($action['ActionLine']['param3']) . '';
                if (!empty(trim($action['ActionLine']['param5'])))
                    $extra.=', ' . __('Phone', true) . ': ' . strongStr($action['ActionLine']['param5']) . '';
                $text = __('%s updated the client %s %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link), $extra);
                break;
            case ACTION_UPDATE_CLIENT_GROUPPRICE:
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the client %s group price into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_EDIT_WORK_ORDER:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param3'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the work order %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;
            case ACTION_DELETE_CLIENT:
                $client_link = $action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')';
                $text = __('%s deleted the client %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;
            case ACTION_DELETE_WORK_ORDER:
                $client_link = $action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param3'] . ')';
                $text = __('%s deleted the work order %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;
            case ACTION_SEND_LOGIN_DETAILS:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s sent the login details to the client %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;
            case ACTION_CLIENT_LOGIN:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('The client %s logged in the system', true);
                return sprintf($text, strongStr($client_link));
                break;

            case ACTION_LOGIN:
                $text = __('%s logged in the system', true);
                return sprintf($text, strongStr($staff_name));
                break;
            case ACTION_CREATED_THE_ACCOUNT:
                $text = __('You created this account, Welcome!', true);
                return sprintf($text, strongStr($staff_name));
                break;

            case ACTION_LOGIN_AS_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s logged in as client: %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;

            case ACTION_LOGIN_AS_STAFF:
                $text = __('%s logged in as staff member: %s #%s ', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param2']), strongStr($action['ActionLine']['primary_id']));
                break;


            //$arry = array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'],'param1' => $re_read_invoice['Invoice']['summary_total'],'param2' => $re_read_invoice['Invoice']['name'],'param3' => $re_read_invoice['Invoice']['subscription_unit'].' '.$re_read_invoice['Invoice']['subscription_period'],'param4' => $re_read_invoice['Invoice']['subscription_max_repeat'],'param5' => $re_read_invoice['Client']['business_name'],'param6' => $re_read_invoice['Client']['client_number'],'param7' => $re_read_invoice['Invoice']['date'],'param8' => $re_read_invoice['Invoice']['subscription_issue_before'],'param9' => $re_read_invoice['Invoice']['active']);
            case ACTION_ADD_RECURRING_PROFILE:
                $profile_link = $html->link('#' . $action['ActionLine']['primary_id'] . ' ' . $action['ActionLine']['param2'], array('controller' => "invoices", 'action' => "view_subscription", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $status = $action['ActionLine']['param9'] ? __('Active', true) : __('Disabled', true);
                $text = __("%s created a new %s recurring invoices generator %s for %s totalling %s to be generated every %s %s and the first invoice date is %s, to be issued %s days before that", true);
                $every = explode(' ', $action['ActionLine']['param3']); //php8 fix


                $every = $every[1] . ' ' . __($every[0], true);
                $for = ($action['ActionLine']['param4'] ? sprintf(__('for %s times', true), $action['ActionLine']['param4']) : __('forever', true));
                return sprintf($text, strongStr($staff_name), strongStr($status), strongStr($profile_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($every), strongStr($for), strongStr(format_date($action['ActionLine']['param7'])), strongStr($action['ActionLine']['param8']));

                break;
            case ACTION_UPDATE_RECURRING_PROFILE:
                $profile_link = $html->link('#' . $action['ActionLine']['primary_id'] . ' ' . $action['ActionLine']['param2'], array('controller' => "invoices", 'action' => "view_subscription", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $status = $action['ActionLine']['param9'] ? __('Active', true) : __('Disabled', true);
                $text = __("%s updated %s recurring invoices generator %s for %s totalling %s to be generated every %s %s and the first invoice date is %s, to be issued %s days before that", true);
                $every = explode(' ', $action['ActionLine']['param3']); //php8 fix


                $every = $every[1] . ' ' . __($every[0], true);
                $for = ($action['ActionLine']['param4'] ? sprintf(__('for %s times', true), $action['ActionLine']['param4']) : __('forever', true));
                return sprintf($text, strongStr($staff_name), strongStr($status), strongStr($profile_link), strongStr($client_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($every), strongStr($for), strongStr(format_date($action['ActionLine']['param7'])), strongStr($action['ActionLine']['param8']));

                break;
            case ACTION_DELETE_RECURRING_PROFILE:
                $profile_link = $html->link('#' . $action['ActionLine']['primary_id'] . ' ' . $action['ActionLine']['param2'], array('controller' => "invoices", 'action' => "view_subscription", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param5'] . ' (#' . $action['ActionLine']['param6'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s deleted the recurring invoices generator %s for %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($profile_link), strongStr($client_link));
                break;

            case ACTION_UPDATE_SMTP_SETTINGS:
                $enabled = $action['ActionLine']['param3'] ? __('Enabled', true) : __('Disabled', true);
                $text = __('%s %s the SMTP option', true);
                return sprintf($text, strongStr($staff_name), strongStr($enabled));
                break;
            case ACTION_UPDATE_SETTINGS:
                $text = __('%s updated the system settings', true);
                return sprintf($text, strongStr($staff_name));
                break;
            case ACTION_UPDATED_PLUGIN:
                    if (!empty(trim($action['ActionLine']['param5']))){
                    $extra.=' '.__('Active',true).' '.$action['ActionLine']['param5'];           
                    }
                    if (!empty(trim($action['ActionLine']['param4']))){
                    $extra.=' '.__('In-Active',true).' '.$action['ActionLine']['param4'];           
                    }
                
                
                $text = __('%s updated the system plugins %s', true);
                return sprintf($text, strongStr($staff_name),$extra);
                break;
            case ACTION_CHNAGE_PASSWORD:
                $text = __('%s updated the password', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;
            case ACTION_CHNAGE_EMAIL:
                $text = __('%s updated email address from %s to %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), strongStr($action['ActionLine']['param4']));
                break;
            case ACTION_UPDATE_SYSTEM_LOGO_COLOR:
                $text = __('%s updated the system logo/colors', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;
            case ACTION_CLIENT_UPDATE_PROFILE:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated his details', true);
                return sprintf($text, strongStr($client_link));
                break;
            case ACTION_CLIENT_CHNAGE_PASSWORD:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('The client %s changed his password', true);
                return sprintf($text, strongStr($client_link));
                break;
            case ACTION_OWNER_CHNAGE_CLIENT_PASSWORD:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s changed %s password', true);
                return sprintf($text,strongStr($staff_name), strongStr($client_link));
                break;
            case ACTION_OWNER_CHNAGE_STAFF_PASSWORD:
                $staff_link = $html->link($action['ActionLine']['param2'], array('controller' => "staffs", 'action' => "edit", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s changed staff %s password', true);
                return sprintf($text,strongStr($staff_name), strongStr($staff_link));
                break;
            case ACTION_CLIENT_CHNAGE_EMAIL:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s changed his email address from %s to %s', true);
                return sprintf($text, strongStr($client_link), strongStr($action['ActionLine']['param3']), strongStr($action['ActionLine']['param5']));
                break;


            case ACTION_ADD_EMAIL_TEMPLATE:
                $text = __('%s added a new email templates "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_UPDATE_EMAIL_TEMPLATE:
                $text = __('%s updated the email template "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_DELETE_EMAIL_TEMPLATE:
                $text = __('%s deleted the email template "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_ADD_INVOICE_LAYOUT:
                $text = __('%s added a new invoice layout "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_UPDATE_INVOICE_LAYOUT:
                $text = __('%s updated the invoice layout "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_DELETE_INVOICE_LAYOUT:
                $text = __('%s deleted the invoice layout "%s" #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_CREATED_THE_ACCOUNT:
                $text = __('You created your account, Welcome!', true);
                return sprintf($text);
                break;
            case ACTION_ADD_PR:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s created a new purchase refund %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name),  strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;

            case ACTION_EDIT_PR:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s updated the purchase refund %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PoStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_DELETE_PR:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s deleted the purchase refund %s for %s totalling %s, and status was %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PoStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_PURCHASE_ADD_DEBIT_NOTE:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s created a new purchase debit note %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name),  strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;

            case ACTION_PURCHASE_UPDATE_DEBIT_NOTE:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s updated the purchase debit note %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PoStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_PURCHASE_DELETE_DEBIT_NOTE:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s deleted the purchase debit note %s for %s totalling %s, and status was %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PoStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_ADD_PO:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s created a new %s and %s purchase invoice %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($this->PoStatuses[$action['ActionLine']['param2']]), strongStr($this->PaymentStatuses[$action['ActionLine']['param7']]), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;

            case ACTION_UPDATE_PO:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s updated the purchase invoice %s for %s totalling %s, and status: %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PoStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_ADD_PQ:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "purchase_quotations", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s created a new %s purchase quotation %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($this->PaymentStatuses[$action['ActionLine']['param7']]), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;

            case ACTION_UPDATE_PQ:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "purchase_quotations", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the purchase quotation %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;
    

            case ACTION_DELETE_PO:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s deleted the purchase invoice %s for %s totalling %s, and status was %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PoStatuses[$action['ActionLine']['param2']]));
                break;

            case ACTION_SEND_PO:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $message_link = $html->link('#' . $action['ActionLine']['param8'], array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for purchase invoice %s ", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param2'])), strongStr($po_link));
                break;

            case ACTION_READ_PO_EMAIL:
                $message_link = $html->link('#' . $action['ActionLine']['param8'], array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The email message %s was read", true);
                return sprintf($text, strongStr($message_link));
                break;
            case ACTION_SEND_EMAIL_TO_CLIENT:
                $message_link = $html->link('#' . $action['ActionLine']['param5'], array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for client %s", true);
                return sprintf($text, strongStr($staff_name), $message_link, strongStr(h($action['ActionLine']['param3'])), strongStr($client_link));
                break;
            case ACTION_SEND_SMS_TO_CLIENT:
                $message_link = $html->link($action['ActionLine']['param6'], array('controller' => "sms_campaigns", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to phone number %s for client %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param3'])), strongStr($client_link));
				break;
            case ACTION_SEND_SMS_TO_INVOICE:
                $message_link = $html->link($action['ActionLine']['param6'], array('controller' => "sms_campaigns", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $invoice_link = $html->link('#' . $action['ActionLine']['param7'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to phone number %s for client %s for invoice %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param3'])), strongStr($client_link), strongStr($invoice_link));
                break;
            case ACTION_SEND_SMS_TO_APPOINTMENT:
                $message_link = $html->link($action['ActionLine']['param6'], array('controller' => "sms_campaigns", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to phone number %s for client %s for appointment %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param3'])), strongStr($client_link), $action['ActionLine']['primary_id']);
                break;
            case ACTION_SEND_SMS_TO_APPOINTMENT_STAFF:
                $message_link = $html->link($action['ActionLine']['param6'], array('controller' => "sms_campaigns", 'action' => "view", $action['ActionLine']['param5'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to phone number %s for staff %s for appointment %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param3'])), $action['ActionLine']['param2'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_SEND_EMAIL_TO_APPOINTMENT:
                $message = $action['ActionLine']['param6'];
                $client_link = $html->link($action['ActionLine']['param2'], array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent an message %s to email %s for client %s for appointment %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message), strongStr(h($action['ActionLine']['param3'])), strongStr($client_link), $action['ActionLine']['primary_id']);
                break;
            case ACTION_SEND_EMAIL_TO_APPOINTMENT_STAFF:
                $message = $action['ActionLine']['param6'];
                $text = __("%s sent a message %s to email %s for staff %s for appointment %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($message), strongStr(h($action['ActionLine']['param3'])), $action['ActionLine']['param2'], $action['ActionLine']['primary_id']);
                break;
            case ACTION_ASSIGN_STAFF_TO_CLIENT:
                $staff_ids = explode(',', $action['ActionLine']['param5']);
                $staff_names = explode(',', $action['ActionLine']['param6']);
                foreach ($staff_ids as $key => $staff_id) {
                    $staff_links[] = $html->link($staff_names[$key], array('controller' => "staffs", 'action' => "edit", $staff_id, 'owner' => 'true'), array('target' => '_blank'));
                }

                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s assigned the client %s to the staff members %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link), strongStr(implode(',', $staff_links)));
                break;
            case ACTION_UNASSIGN_STAFF_FROM_CLIENT:
                $staff_ids = explode(',', $action['ActionLine']['param5']);
                $staff_names = explode(',', $action['ActionLine']['param6']);
                foreach ($staff_ids as $key => $staff_id) {
                    $staff_links[] = $html->link($staff_names[$key], array('controller' => "staffs", 'action' => "edit", $staff_id, 'owner' => 'true'), array('target' => '_blank'));
                }
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s unassigned the client %s from the the staff member %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link), strongStr(implode(',', $staff_links)));
                break;
            case ACTION_SUPPLIER_VIEW_PO:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The supplier viewed the purchase invoice %s on the system", true);
                return sprintf($text, strongStr($po_link));
                break;
            case ACTION_SUPPLIER_PRINT_PO:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("The supplier printed the purchase invoice %s on the system", true);
                return sprintf($text, strongStr($po_link));
                break;

            //array('primary_id' => $new['StockTransaction']['order_id'], 'secondary_id' => $new['StockTransaction']['product_id'], 'param1' => $new['StockTransaction']['quantity'], 'param2' => $new['StockTransaction']['id'], 'param8' => $product_count, 'param3' => $invoice['Invoice']['no'], 'param4' => $transaction['Product']['product_code'], 'param5' => $transaction['Product']['name']

            case ACTION_TRANSACTION_PO_ADDED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s received %s new items of %s in purchase invoice %s, price per unit is: %s, stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr($po_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2'])).$extra;
                break;
            case ACTION_TRANSACTION_PO_UPDATED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the received stock (Transaction ID: %s) of %s in purchase invoice %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($po_link), strongStr(format_number($action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_PO_DELETED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s removed the  %s received items of %s in purchase invoice %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr($po_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_PR_ADDED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s returned %s items of %s in purchase refund %s, price per unit is: %s, stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr($po_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2'])).$extra;
                break;
            case ACTION_TRANSACTION_PR_UPDATED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the returned stock (Transaction ID: %s) of %s in purchase refund %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($po_link), strongStr(format_number($action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_PR_DELETED:
                $po_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s removed the  %s returned items of %s in purchase refund %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr($po_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;

            case ACTION_TRANSACTION_INVOICE_ADDED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sold %s new items of %s in invoice %s, price per unit is: %s, stock balance now become: %s (Transaction ID: %s)", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__('Average Price' , true ).": ".strongStr(format_price(json_decode($action['ActionLine']['param9'])[2], $default_currency ?? null));
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(-1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2'])).$extra;
                break;
            case ACTION_TRANSACTION_INVOICE_UPDATED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the sold stock (Transaction ID: %s) of %s in invoice %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ;
                // warning suppress
                $default_currency = $default_currency ?? null;
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($invoice_link), strongStr(format_number(-1 * $action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_INVOICE_DELETED:
                $invoice_link = $html->link('#' . (!empty($action['ActionLine']['param3'])?$action['ActionLine']['param3']:$action['ActionLine']['primary_id']), array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s removed the %s sold items of %s in invoice %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(-1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_CREDITNOTE_ADDED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s received %s new items of %s in credit note %s, price per unit is: %s, stock balance now become: %s (Transaction ID: %s)", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2'])).$extra;
                break;
            case ACTION_TRANSACTION_CREDITNOTE_UPDATED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the refunded stock (Transaction ID: %s) of %s in credit note %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($invoice_link), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_CREDITNOTE_DELETED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_creditnote", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s removed the %s refunded items of %s in credit note %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(1* $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_REFUND_ADDED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s received %s new items of %s in refund receipt %s, price per unit is: %s, stock balance now become: %s (Transaction ID: %s)", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8'])), strongStr('#' . $action['ActionLine']['param2']));
                break;
            case ACTION_TRANSACTION_REFUND_UPDATED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the sold stock (Transaction ID: %s) of %s in refund receipt %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr($invoice_link), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_TRANSACTION_REFUND_DELETED:
                $invoice_link = $html->link('#' . $action['ActionLine']['param3'], array('controller' => "invoices", 'action' => "view_refund", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s removed the %s sold items of %s in refund receipt %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number(1 * $action['ActionLine']['param1'])), strongStr($product_link), strongStr($invoice_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;

            case ACTION_TRANSACTION_MANUAL_ADDED:

                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));

                if ($action['ActionLine']['param1'] >= 0) {
                    $text = __("%s added %s items of %s to the stock manually (Transaction ID: %s), price per unit is: %s, stock balance now become: %s and store %s has balance: %s", true);
                    $qty = $action['ActionLine']['param1'];
                } else {
                    $text = __("%s deducted %s items of %s from the stock manually (Transaction ID: %s), price per unit is: %s, stock balance now become: %s and store %s has balance: %s", true);
                    $qty = $action['ActionLine']['param1'] * -1;
                }
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                $currencyCode = $action['ActionLine']['param7'];
                return sprintf($text, strongStr($staff_name), strongStr(format_number($qty)), strongStr($product_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'],$currencyCode)), strongStr(format_number($action['ActionLine']['param8'])), strongStr(json_decode($action['ActionLine']['param9'], true)[1]), strongStr(json_decode($action['ActionLine']['param9'], true)[0])).$extra;
                break;
            case ACTION_TRANSACTION_MANUAL_UPDATED:

                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s updated the manual stock adjustment (Transaction ID: %s) of %s to %s items, price per unit is: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr('#' . $action['ActionLine']['param2']), strongStr($product_link), strongStr(format_number($action['ActionLine']['param1'])), strongStr(format_number($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))). ", ".__("Store",true).": ". strongStr(json_decode($action['ActionLine']['param9'], true)[1]).$extra;
                break;
            case ACTION_TRANSACTION_MANUAL_DELETED:

                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s deleted the %s manually adjusted stock of %s (Transaction ID: %s), price per unit was: %s and stock balance now become: %s", true);
                $extra = '' ; 
                if ($this->showAvgPrice($action)) {
                    $extra = ', '.__("Average Price" , true ).": ".strongStr(format_price (json_decode ($action['ActionLine']['param9'])[2] , $default_currency ) );
                }
                return sprintf($text, strongStr($staff_name), strongStr(format_number($action['ActionLine']['param1'])), strongStr($product_link), strongStr('#' . $action['ActionLine']['param2']), strongStr(format_price($action['ActionLine']['param6'])), strongStr(format_number($action['ActionLine']['param8']))).$extra;
                break;
            case ACTION_STORE_TRANSFER:

                $product_link = $html->link('#' . (empty($action['ActionLine']['param4']) ? $action['ActionLine']['secondary_id'] : $action['ActionLine']['param4']) . ' (' . $action['ActionLine']['param5'] . ')', array('controller' => "products", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s transfered %s %s from %s to %s", true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['param2'], strongStr($product_link), strongStr($action['ActionLine']['param6']), strongStr($action['ActionLine']['param8']));
                break;
            case ACTION_IMPORT_DATA:
                $po_link = $html->link($action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s imported a new  %s CSV file, %s new records were added, and %s records were updated, and found errors in %s records', TRUE);
                return sprintf($text, strongStr($staff_name), strongStr(Inflector::humanize($action['ActionLine']['param4'])), strongStr(Inflector::humanize($action['ActionLine']['param2'])), strongStr(Inflector::humanize($action['ActionLine']['param3'])), strongStr(Inflector::humanize($action['ActionLine']['param5'])));
                break;
            case STAFF_DOWNLOAD_FILE:
                $file_link = $html->link($action['ActionLine']['param3'], array('owner' => false, 'controller' => "allfiles", 'action' => "download", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "staffs", 'action' => "edit", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s has started downloading %s attached with %s', TRUE);
                return sprintf($text, strongStr($staff_name), $file_link, strongStr('Note #' . $action['ActionLine']['param5']));
                break;
            case STAFF_DOWNLOAD_INVOICE_DOCUMENT:
                $invoice_link = $html->link($action['ActionLine']['param6'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $file_link = $html->link($action['ActionLine']['param3'], array('owner' => false, 'controller' => "documents", 'action' => "download", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "staffs", 'action' => "edit", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if(!empty($action['ActionLine']['param6'])){
                $text = __('%s has started downloading %s attached with Invoice #%s', TRUE);
                return sprintf($text, strongStr($staff_name), $file_link, $invoice_link);
                }else{
                $file_link = $html->link($action['ActionLine']['param3'], array('owner' => false, 'controller' => "documents", 'action' => "download_doc", $action['ActionLine']['primary_id']), array('target' => '_blank'));                    
                $text = __('%s has started downloading %s', TRUE);
                return sprintf($text, strongStr($staff_name), $file_link);    
                }
                break;
            case CLIENT_DOWNLOAD_INVOICE_DOCUMENT:
                $invoice_link = $html->link($action['ActionLine']['param6'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $file_link = $html->link($action['ActionLine']['param3'], array('owner' => false, 'controller' => "documents", 'action' => "download", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s has started downloading %s attached with Invoice #%s', TRUE);
                return sprintf($text, $client_link, $file_link, $invoice_link);
                break;
            case CLIENT_DOWNLOAD_FILE:

                $file_link = $html->link($action['ActionLine']['param3'], array('owner' => false, 'controller' => "allfiles", 'action' => "download", $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s has started downloading %s attached with %s', TRUE);
                return sprintf($text, strongStr($client_link), strongStr($file_link), strongStr('Note #' . $action['ActionLine']['param5']));
                break;
            case ACTION_ADD_NEW_POST:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], '?' => array('note_id' => $action['ActionLine']['primary_id'], 'owner' => 'true'), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));



                $texts = array();
                $texts[] = sprintf(__('%s added a new Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s with client', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));


                return  implode(', ', $texts);
                break;
            case ACTION_UPDATE_THE_POST:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], '?' => array('note_id' => $action['ActionLine']['primary_id'], 'owner' => 'true'), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s updated the Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s with client', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));

                return  implode(', ', $texts);
                break;
            case ACTION_ADD_NEW_WORK_ORDER_POST:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'], '?' => array('note_id' => $action['ActionLine']['primary_id'], 'owner' => 'true'), '#' => 'NotesBlock'), array('target' => '_blank'));
                $link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s added a new Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s with client', true), strongStr(($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true))));
                return implode(', ',$texts);
                break;
            case ACTION_UPDATE_WORK_ORDER_POST:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'], '?' => array('note_id' => $action['ActionLine']['primary_id'], 'owner' => 'true'), '#' => 'NotesBlock'), array('target' => '_blank'));
                $link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s updated the Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s with client', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));

                return  implode(', ', $texts);
                break;
            case ACTION_REMOVE_THE_POST:
                $text = __('%s deleted the Note #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']));
                break;
            
            
            case ACTION_ADD_NEW_POST_INVOICE:
                $followup =  GetObjectOrLoadModel('FollowUpStatus'); $types_diff = $followup->get_types_diff(); $types_att = $types_diff[$action['ActionLine']['param2']];
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['secondary_id'], array('controller' => $types_att['controller'] ?? 'invoices', 'action' =>(($types_att['name'] == "Estimate")? "view_estimate": ($types_att['name'] == "Sales Order"? "view_sales_order" : "view") ), $action['ActionLine']['primary_id'], '?' => array('note_id' => $action['ActionLine']['secondary_id'], 'owner' => 'true'), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link(sprintf ( __($types_att['name'].' %s', true), '#' . $action['ActionLine']['param1'] ), array('owner' => true, 'controller' => $types_att['controller'] ?? 'invoices', 'action' => (($types_att['name'] == "Estimate")? "view_estimate": ($types_att['name'] == "Sales Order"? "view_sales_order" : "view") ), $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s added a new Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));
                return implode(', ',$texts);
                break;
            case ACTION_UPDATE_THE_POST_INVOICE:
                $followup =  GetObjectOrLoadModel('FollowUpStatus'); $types_diff = $followup->get_types_diff(); $types_att = $types_diff[$action['ActionLine']['param2']];debug ( $types_att ) ;
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['secondary_id'], array('controller' => $types_att['controller'] ?? "invoices", 'action' =>(($types_att['name'] == "Estimate")? "view_estimate": ($types_att['name'] == "Sales Order"? "view_sales_order" : "view") ), $action['ActionLine']['primary_id'], '?' => array('note_id' => $action['ActionLine']['secondary_id'], 'owner' => 'true'), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link(sprintf ( __($types_att['name'].' %s', true), '#' . $action['ActionLine']['param1'] ), array('owner' => true, 'controller' => $types_att['controller'] ?? "invoices", 'action' => (($types_att['name'] == "Estimate")? "view_estimate": ($types_att['name'] == "Sales Order"? "view_sales_order" : "view") ), $action['ActionLine']['primary_id']), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s updated the Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));
                return  implode(', ', $texts);
                break;
            case ACTION_REMOVE_THE_POST_INVOICE:
                $text = __('%s deleted the Note #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']));
                break;
            
            case ACTION_ADD_NEW_POST_PURCHASE_INVOICE:
                $followup =  GetObjectOrLoadModel('FollowUpStatus'); $types_diff = $followup->get_types_diff(); $types_att = $types_diff[$action['ActionLine']['param2']];
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['secondary_id'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], '?' => array('note_id' => $action['ActionLine']['secondary_id'], 'owner' => 'true'), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link(sprintf ( __($types_att['name'].' %s', true), '#' . $action['ActionLine']['param1'] ), array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s added a new Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));
                return implode(', ',$texts);
                break;
            case ACTION_UPDATE_THE_POST_PURCHASE_INVOICE:
                $followup =  GetObjectOrLoadModel('FollowUpStatus'); $types_diff = $followup->get_types_diff(); $types_att = $types_diff[$action['ActionLine']['param2']];debug ( $types_att ) ;
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $file_link = $html->link('#' . $action['ActionLine']['secondary_id'], array('controller' => 'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], '?' => array('note_id' => $action['ActionLine']['secondary_id'], 'owner' => 'true'), '#' => 'NotesBlock'), array('target' => '_blank'));
                $client_link = $html->link(sprintf ( __($types_att['name'].' %s', true), '#' . $action['ActionLine']['param1'] ), array('controller' =>'purchase_invoices', 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => true), array('target' => '_blank'));
                $texts = array();
                $texts[] = sprintf(__('%s updated the Note %s for %s in %s', true), strongStr($staff_name), strongStr($file_link), $client_link, strongStr($date));
                if (!empty($action['ActionLine']['param5']))
                    $texts[] = sprintf(__('The action performed is: %s', TRUE), strongStr($action['ActionLine']['param5']));
                if (!empty($action['ActionLine']['param6'])) {
                    $texts[] = sprintf(__('With attached files (%s)', true), strongStr($action['ActionLine']['param6']));
                }
                $texts[] = sprintf(__('And it is %s', true), strongStr($action['ActionLine']['param3'] != 1 ? __('Not Shared', true) : __('Shared', true)));
                return  implode(', ', $texts);
                break;
            case ACTION_REMOVE_THE_POST_PURCHASE_INVOICE:
                $text = __('%s deleted the Note #%s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']));
                break;
            
            case ACTION_UPDATE_CLIENT_STATUS:
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the client %s status into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_UPDATE_INVOICE_STATUS:
                $client_link = $html->link( '#' . $action['ActionLine']['param1'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the invoice %s status into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_UPDATE_ESTIMATE_STATUS:
                $client_link = $html->link( '#' . $action['ActionLine']['param1'], array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the estimate %s status into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;
            case ACTION_UPDATE_WORK_ORDER_STATUS:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param3'] . ')', array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                
                $text = __('%s updated the work order %s status into %s', true);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param1']));
                break;
            case ACTION_UPDATE_CLIENT_CATEGORY:
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the client %s category into %s', TRUE);
                return sprintf($text, $staff_name, $client_link, strongStr($action['ActionLine']['param3']));
                break;

            case ACTION_ADD_CLIENT_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s scheduled a new %s appointment %s for client %s at %s, Appointment status is: %s ', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['primary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_EDIT_CLIENT_APPOINTMENT:
                $date = format_date(explode(" ",$action['ActionLine']['param4'])[0]) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link($action['ActionLine']['param1'] . ' (#' . $action['ActionLine']['param2'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the %s appointment %s for client %s at %s, Appointment status is: %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['primary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_CLIENT_APPOINTMENT:
                $text = __('%s deleted the Appointment (%s)', true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['primary_id']);
                break;
            
            case ACTION_ADD_INVOICE_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s scheduled a new %s appointment %s for invoice %s at %s, Appointment status is: %s ', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_EDIT_INVOICE_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the %s appointment %s for invoice %s at %s, Appointment status is: %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_INVOICE_APPOINTMENT:
                $text = __('%s deleted the Appointment (%s)', true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['secondary_id']);
                break;
            
            
            case ACTION_ADD_ESTIMATE_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s scheduled a new %s appointment %s for estimate %s at %s, Appointment status is: %s ', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_EDIT_ESTIMATE_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $client_link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "invoices", 'action' => "view_estimate", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the %s appointment %s for estimate %s at %s, Appointment status is: %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $client_link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_ESTIMATE_APPOINTMENT:
                $text = __('%s deleted the Appointment (%s)', true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['secondary_id']);
                break;

            case ACTION_ADD_WORK_ORDER_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'] , 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s scheduled a new %s appointment %s for work order %s at %s, Appointment status is: %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_EDIT_WORK_ORDER_APPOINTMENT:
                $date = format_date($action['ActionLine']['param4']) . date(' H:i:s', strtotime($action['ActionLine']['param4']));
                $link = $html->link( ' #' . $action['ActionLine']['param1'] , array('controller' => "work_orders", 'action' => "view", $action['ActionLine']['secondary_id'] , 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s updated the %s appointment %s for work order %s at %s, Appointment status is: %s', TRUE);
                return sprintf($text, strongStr($staff_name), !empty($action['ActionLine']['param5']) ? '"' . strongStr($action['ActionLine']['param5']) . '"' : '', strongStr('(#' . $action['ActionLine']['secondary_id'] . ')'), $link, strongStr($date), strongStr($action['ActionLine']['param6']));
                break;
            case ACTION_DELETE_WORK_ORDER_APPOINTMENT:
                $text = __('%s deleted the Appointment (%s)', true);
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['secondary_id']);
                break;
            
            
            case ACTION_ADD_ROLE:
                $text = __('%s added a new staff role %s (#%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_EDIT_ROLE:
                $text = __('%s updated the staff role %s (#%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_DELETE_ROLE:
                $text = __('%s deleted the staff role %s (#%s)', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param1']), strongStr($action['ActionLine']['primary_id']));
                break;
            case ACTION_PRINT_RECEIPT:
                $invoice_link = $html->link($action['ActionLine']['param2'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s printed the receipt #%s of the invoice #%s', TRUE);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), $invoice_link);
                break;
            case ACTION_DOWNLOAD_RECEIPT:
                $invoice_link = $html->link($action['ActionLine']['param2'], array('controller' => "invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s downloaded PDF for the receipt #%s of the invoice #%s', TRUE);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), $invoice_link);
                // return implode(",", array_keys($action['ActionLine'])).implode(",", $action['ActionLine']);
                break;

            //Prescription longActions
            case ACTION_ADD_PRESCRIPTION_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("Add %s a new Prescription for %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;

            case ACTION_EDIT_PRESCRIPTION_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));

                $text = __('updated %s the Prescription %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($client_link));
                break;

            case ACTION_DELETE_PRESCRIPTION_CLIENT:
                $client_link = $action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')';
                $text = __('%s deleted the prescription %s for client %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']), strongStr($client_link));
                break;

            case ACTION_PRINT_PRESCRIPTION_CLIENT:
                $prescription_link = $html->link('#' . $action['ActionLine']['primary_id'], array('controller' => "prescriptions", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s printed the prescription %s', true);
                return sprintf($text, strongStr($staff_name), strongStr($prescription_link));
                break;

            case ACTION_EMAIL_SENT_PRESCRIPTION_CLIENT:
                $client_link = $html->link($action['ActionLine']['param2'] . ' (#' . $action['ActionLine']['param4'] . ')', array('controller' => "clients", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $prescription_link = $html->link(' (#' . $action['ActionLine']['primary_id'] . ')', array('controller' => "prescriptions", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a prescription %s to the client %s , Email Address %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($prescription_link), strongStr($client_link), strongStr(h($action['ActionLine']['param3'])));
                break;
			
			case ACTION_JOURNAL_ACCOUNT_ADD:
				//type 0 => cat 1 => account
				if($action['ActionLine']['param1'] == JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT ){
					$account_type = __('Sub',true);
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => '/owner/journal_accounts/list_transactions/'.$action['ActionLine']['primary_id'].'/page:0/?ASC&date_from=&date_to=&currency_code=', 'owner' => 'true'), array('target' => '_blank'));
				}else{
					$account_type = __('Main',true);
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => 'folderid='.$action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
				}
				
				if($action['ActionLine']['param4'] == Journal::JOURNAL_TYPE_CREEDITOR)
				{
					$type = __('Credit',true);
				}else
				{
					$type = __('Debit',true);
				}
				
                $parent_link = $html->link(' (' . $action['ActionLine']['param5'] . ')', array('controller' => "journal_cats", 'action' => "tree",'#' => 'folderid='.$action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s Added a %s %s Journal Account %s to the Account %s", true);
                return sprintf($text, strongStr($staff_name),$account_type , $type, strongStr($account_link), strongStr($parent_link));
                break;
			case ACTION_JOURNAL_ACCOUNT_EDIT:
				//type 0 => cat 1 => account
				
				if($action['ActionLine']['param1'] == JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT ){
					$account_type = __('Sub',true);
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => '/owner/journal_accounts/list_transactions/'.$action['ActionLine']['primary_id'].'/page:0/?ASC&date_from=&date_to=&currency_code=', 'owner' => 'true'), array('target' => '_blank'));
				}else{
					$account_type = __('Main',true);
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => 'folderid='.$action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
				}
				
				if($action['ActionLine']['param4'] == Journal::JOURNAL_TYPE_CREEDITOR)
				{
					$type = __('Credit',true);
				}else
				{
					$type = __('Debit',true);
				}
				
                $parent_link = $html->link(' (' . $action['ActionLine']['param5'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => 'folderid='.$action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s Edited a %s %s Journal Account %s Under the Account %s", true);
                return sprintf($text, strongStr($staff_name),$account_type , $type , strongStr($account_link), strongStr($parent_link));
                break;
			case ACTION_JOURNAL_ACCOUNT_DELETE:
				//type 0 => cat 1 => account
				if($action['ActionLine']['param1'] == JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT ){
					$account_type = __('Sub',true);
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => '/owner/journal_accounts/list_transactions/'.$action['ActionLine']['primary_id'].'/page:0/?ASC&date_from=&date_to=&currency_code=', 'owner' => 'true'), array('target' => '_blank'));
				}else{
					$account_type = __('Main',true);
					$account_link = $html->link(' (' . $action['ActionLine']['param3'] . ')', array('controller' => "journal_cats", 'action' => "tree",  '#' => 'folderid='.$action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
				}
				
				if($action['ActionLine']['param4'] == Journal::JOURNAL_TYPE_CREEDITOR)
				{
					$type = __('Credit',true);
				}else
				{
					$type = __('Debit',true);
				}
				
				
                $parent_link = $html->link(' (' . $action['ActionLine']['param5'] . ')', array('controller' => "journal_cats", 'action' => "tree", '#' => 'folderid='.$action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s Deleted a %s %s Journal Account %s From the Account %s", true);
                return sprintf($text, strongStr($staff_name),$account_type , $type ,strongStr($account_link), strongStr($parent_link));
                break;
            case ACTION_UPDATED_AVG_PRICE: 
                $text = __("The average unit cost has been changed for transaction #%s with received date %s from %s to %s and average unit cost for the product became %s" , true );
		return sprintf($text , strongStr($action['ActionLine']['param1']) , strongStr($action['ActionLine']['param5']) , strongStr($action['ActionLine']['param3']) , strongStr($action['ActionLine']['param4']) ,strongStr($action['ActionLine']['param4']) )	;
            case ACTION_ASSET_DEPRECATION_ADD: 
                $text = __("%s added a new Depreciation #%s for asset %s #%s with value %s, asset current value is %s" ,true ) ;
                $extra = '' ;
                $asset_link = $html->link($action['ActionLine']['secondary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $asset_deprecation_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'asset_deprecations' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name),$asset_deprecation_link ,strongStr($action['ActionLine']['param2']) ,$asset_link ,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param3']) ).$extra;
            case ACTION_ASSET_DEPRECATION_MODIFY: 
                $text = __("%s modified Depreciation #%s for asset %s #%s with value %s, asset current value is %s" ,true ) ;
                $extra = '' ;
                $asset_link = $html->link($action['ActionLine']['secondary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $asset_deprecation_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'asset_deprecations' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name),$asset_deprecation_link ,strongStr($action['ActionLine']['param2']) ,$asset_link ,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param3']) ).$extra;
                
            case ACTION_ASSET_DEPRECATION_DELETE: 
                $text = __("%s deleted Depreciation #%s for asset %s #%s with value %s, asset current value is %s" ,true ) ;
                $extra = '' ;
                $asset_link = $html->link($action['ActionLine']['secondary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                return sprintf($text, strongStr($staff_name),strongStr($action['ActionLine']['primary_id']) ,strongStr($action['ActionLine']['param2']) ,$asset_link ,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param3']) ).$extra;
                
            case ACTION_ASSET_ADD: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s added a new asset # %s under %s category, purchased value is %s, from %s account, asset current value is %s" ,true ) ;
                $extra = '' ;
                if ( !empty ( $action['ActionLine']['param5']))
                {
                    $extra = sprintf(__(", with automatic %s Depreciation every %s",true) , strongStr($action['ActionLine']['param5']) ,strongStr($action['ActionLine']['param6'])  );
                }
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']),strongStr($action['ActionLine']['param4']) ).$extra;
                       
                
            case ACTION_ASSET_MODIFY: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s updated the asset # %s under %s category, purchased value is %s, from %s account, asset current value is %s" ,true ) ;
                $extra = '' ;
                if ( !empty ( $action['ActionLine']['param5']))
                {
                    $extra = sprintf(__(", with automatic %s Depreciation every %s",true) , strongStr($action['ActionLine']['param5']) ,strongStr($action['ActionLine']['param6'])  );
                }
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']),strongStr($action['ActionLine']['param4']) ).$extra;
            case ACTION_ASSET_DELETE: 
                
                $text = __("%s deleted the asset # %s under %s category, purchased value is %s, from %s account, asset current value is %s" ,true ) ;
                $extra = '' ;
                if ( !empty ( $action['ActionLine']['param5']))
                {
                    $extra = sprintf(__(", with automatic %s Depreciation every %s",true) , strongStr($action['ActionLine']['param5']) ,strongStr($action['ActionLine']['param6'])  );
                }
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['primary_id']),strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']),strongStr($action['ActionLine']['param4']) ).$extra;
                
            case ACTION_ASSET_RE_EVALUATE: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s re-evaluated the asset #%s under %s category with value: %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
            case ACTION_ASSET_EDIT_RE_EVALUATE: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s edited the re-evaluation for the asset #%s under %s category with value: %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
            case ACTION_ASSET_DELETE_RE_EVALUATE: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s deleted the re-evaluation for the asset #%s under %s category with value: %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
                
            case ACTION_ASSET_WRITE_OFF: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s written of the asset #%s under %s category with value: %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
           case ACTION_ASSET_DELETE_WRITE_OFF: 
               $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s deleted the write-off of the asset #%s under %s category with value: %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']));
            
            case ACTION_ASSET_SOLD: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s sold the asset #%s under %s category with value: %s to %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']));
            case ACTION_ASSET_EDIT_SOLD: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s edited the selling of the asset #%s under %s category with value: %s to %s" ,true ) ;
                return sprintf($text, strongStr($staff_name),$asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']));
            case ACTION_ASSET_DELETE_SOLD: 
                $asset_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'assets' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s deleted the selling of the asset #%s under %s category with value: %s to %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $asset_link,strongStr($action['ActionLine']['param1']),strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']));

            case ACTION_CLOSED_PERIOD_ADD: 
                $closed_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'closed_periods' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s added a new %s closed period #%s from %s to %s" ,true ) ;
				$active = $action['ActionLine']['param3'] ? __('Active',true) : __('Not Active',true) ;
                return sprintf($text, strongStr($staff_name), $active, $closed_link, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
            case ACTION_CLOSED_PERIOD_EDIT: 
                $closed_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'closed_periods' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s updated the closed period #%s to be %s from %s to %s" ,true ) ;
				$active = $action['ActionLine']['param3'] ? __('Active',true) : __('Not Active',true) ;
                return sprintf($text, strongStr($staff_name), $closed_link, $active,format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
			case ACTION_CLOSED_PERIOD_DELETE: 
                $closed_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'closed_periods' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s deleted the %s closed period #%s from %s to %s" ,true ) ;
				$active = $action['ActionLine']['param3'] ? __('Active',true) : __('Not Active',true) ;
                return sprintf($text, strongStr($staff_name), $active, $closed_link, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
               
            case ACTION_COST_CENTER_ADD: 
				
                $cost_center_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				$type_text = $action['ActionLine']['param1'] ? __('Primary',true) : __('Secondary', true);
				if($action['ActionLine']['secondary_id']){
					$parent_link = $html->link($action['ActionLine']['param4'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
					  $text = __("%s added a new %s cost center %s #%s under %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] ,$parent_link,$action['ActionLine']['param5']);
				}
                $text = __("%s added a new %s cost center %s #%s" ,true ) ;

				return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] );
            case ACTION_COST_CENTER_EDIT: 
                $cost_center_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				$type_text = $action['ActionLine']['param1'] ? __('Primary',true) : __('Secondary', true);
				if($action['ActionLine']['secondary_id']){
					$parent_link = $html->link($action['ActionLine']['param4'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
					  $text = __("%s updated the %s cost center %s #%s under %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] ,$parent_link,$action['ActionLine']['param5']);
				}
                $text = __("%s updated the %s cost center %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] );
            case ACTION_COST_CENTER_DELETE: 
                $cost_center_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				$type_text = $action['ActionLine']['param1'] ? __('Primary',true) : __('Secondary', true);
				if($action['ActionLine']['secondary_id']){
					$parent_link = $html->link($action['ActionLine']['param4'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
					  $text = __("%s deleted the %s cost center %s #%s under %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] ,$parent_link,$action['ActionLine']['param5']);
				}
                $text = __("%s deleted the %s cost center %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $type_text, $cost_center_link, $action['ActionLine']['param3'] );
            case ACTION_JOURNAL_ACCOUNT_COST_CENTER_ADD: 
                $cost_center_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param6'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				
                $text = __("%s added a new %s cost center %s #%s %s%% for %s #%s account" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, $cost_center_link, $action['ActionLine']['param4'],$action['ActionLine']['param5']  ,$journal_account_link,   $action['ActionLine']['param2']);
               
            case ACTION_JOURNAL_ACCOUNT_COST_CENTER_EDIT: 
                $cost_center_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param6'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				
                $text = __("%s updated the %s cost center %s #%s %s%% for %s #%s account" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, $cost_center_link, $action['ActionLine']['param4'],$action['ActionLine']['param5'] , $journal_account_link,   $action['ActionLine']['param2']);
            case ACTION_JOURNAL_ACCOUNT_COST_CENTER_DELETE: 
                $cost_center_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param6'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				
                $text = __("%s deleted the %s cost center %s #%s %s%% for %s #%s account" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, $cost_center_link, $action['ActionLine']['param4'],$action['ActionLine']['param5'] , $journal_account_link,   $action['ActionLine']['param2']);
			 case ACTION_COST_CENTER_TRANSACTION_ADD: 
                $cost_center_link = $html->link($action['ActionLine']['param5'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $journal_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'journals' , 'action' => 'view' ,$action['ActionLine']['param1'], 'owner' => true ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param7'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				
                $text = __("%s added a new %s cost center transaction (amount: %s) for journal #%s to cost center %s #%s to account %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, format_price($action['ActionLine']['param8'],$default_currency),$journal_link,$cost_center_link, $action['ActionLine']['param6'], $journal_account_link,$action['ActionLine']['param4']);
            case ACTION_COST_CENTER_TRANSACTION_EDIT: 
                $cost_center_link = $html->link($action['ActionLine']['param5'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $journal_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'journals' , 'action' => 'view' ,$action['ActionLine']['param1'], 'owner' => true ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param7'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				
                $text = __("%s updated %s cost center transaction (amount: %s) for journal #%s to cost center %s #%s to account %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, format_price($action['ActionLine']['param8'],$default_currency),$journal_link,$cost_center_link, $action['ActionLine']['param6'], $journal_account_link,$action['ActionLine']['param4']);
            case ACTION_COST_CENTER_TRANSACTION_DELETE: 
                $cost_center_link = $html->link($action['ActionLine']['param5'] , ['controller' => 'cost_centers' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $journal_link = $html->link($action['ActionLine']['param2'] , ['controller' => 'journals' , 'action' => 'view' ,$action['ActionLine']['param1'], 'owner' => true ], array('target' => '_blank'));
				$auto_text = $action['ActionLine']['param7'] ? __('Auto',true) : '';
				$journal_account_link = $html->link($action['ActionLine']['param3'] , ['controller' => 'cost_centers' , 'action' => 'assign_cost_centers' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				
                $text = __("%s deleted %s cost center transaction (amount: %s) for journal #%s to cost center %s #%s to account %s #%s" ,true ) ;
					   return sprintf($text, strongStr($staff_name), $auto_text, format_price($action['ActionLine']['param8'],$default_currency),$journal_link,$cost_center_link, $action['ActionLine']['param6'], $journal_account_link,$action['ActionLine']['param4']);
            
            case ACTION_FINANCIAL_YEAR_ADD: 
                $financial_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'cost_centers' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				$closed_text = $action['ActionLine']['param3'] ? __('Opened',true) :  __('Closed',true);
                $text = __("%s Added %s Financial Year from %s to %s" ,true ) ;
			return sprintf($text, strongStr($staff_name), $closed_text, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
            case ACTION_FINANCIAL_YEAR_EDIT: 
                $financial_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'cost_centers' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				$closed_text = $action['ActionLine']['param3'] ? __('Opened',true) :  __('Closed',true);
                $text = __("%s Updated %s Financial Year from %s to %s" ,true ) ;
			return sprintf($text, strongStr($staff_name), $closed_text, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
            case ACTION_FINANCIAL_YEAR_DELETE: 
                $financial_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'cost_centers' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
				$closed_text = $action['ActionLine']['param3'] ? __('Opened',true) :  __('Closed',true);
                $text = __("%s Deleted %s Financial Year from %s to %s" ,true ) ;
			return sprintf($text, strongStr($staff_name), $closed_text, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));            

			//POS Actions
            case ACTION_POS_SESSION_START:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Started Session no. %s from %s at the %s" ,true );
                return sprintf($text, strongStr($staff_name), $session_link, strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']));

            case ACTION_POS_SESSION_OPEN:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Opened Session no. %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $session_link);

            case ACTION_POS_SESSION_CLOSE:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Closed Session no. %s from %s at the %s" ,true );
                return sprintf($text, strongStr($staff_name), strongStr($session_link), strongStr($action['ActionLine']['param2']),strongStr($action['ActionLine']['param3']));

            case ACTION_POS_SESSION_VALIDATE:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Validated Session no. %s closure with %s counted money and %s difference" ,true );
                return sprintf($text, strongStr($staff_name), $session_link, strongStr(format_price($action['ActionLine']['param2'],$action['ActionLine']['param4'])),strongStr(format_price($action['ActionLine']['param3'],$action['ActionLine']['param4'])));

            case ACTION_POS_SESSION_TRANSACTION_ADD:
                $text = __("%s made %s transaction with %s %s %s" ,true );
                return sprintf($text, strongStr($staff_name), strongStr(__($action['ActionLine']['param3'],true)), strongStr(format_price($action['ActionLine']['param1'],$action['ActionLine']['param2'])), __($action['ActionLine']['param4'],true),strongStr($action['ActionLine']['param5']));

            case ACTION_POS_SESSION_UN_VALIDATE:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Unvalidated Session no. %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $session_link);

            case ACTION_POS_SESSION_REOPEN:
                $session_name = $action['ActionLine']['param1'];
                $session_link = $html->link($session_name , ['controller' => 'pos_shifts' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s reopened Session no. %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $session_link);
            //POS Actions End
            //Requisition START 
            case ACTION_REQUISITION_ADD:
                $requistion_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'requisitions' , 'action' => 'view' ,$action['ActionLine']['primary_id'] , 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Added %s #%s" ,true );$extra_text = "";
                if ( !empty($action['ActionLine']['param2']))
                {
                    $extra = __("for %s",true);
                    $view_link = (json_decode($action['ActionLine']['param5'],true)?:[]);
                    if(!empty($view_link)) {
                        $view_link['owner'] = true;
                    }
                    $order_link = $html->link(__($action['ActionLine']['param4'],true). ' #'.$action['ActionLine']['param3'],$view_link+[$action['ActionLine']['param2']], array('target' => '_blank'));
                    $extra_text = sprintf($extra , $order_link );
                }
                return sprintf($text, strongStr($staff_name),__($action['ActionLine']['param6'],true), $requistion_link).' '.$extra_text;
            case ACTION_REQUISITION_UPDATE:
                $requistion_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'requisitions' , 'action' => 'view' ,$action['ActionLine']['primary_id'] , 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Updated %s #%s" ,true );$extra_text = "";
                if ( !empty($action['ActionLine']['param2']))
                {
                    $extra = __("for %s",true);
                    $view_link = json_decode($action['ActionLine']['param5'],true) ?? [];
                    if(!empty($view_link)) {
                        $view_link['owner'] = true;
                    }
                    $order_link = $html->link(__($action['ActionLine']['param4'],true). ' #'.$action['ActionLine']['param3'],$view_link+[$action['ActionLine']['param2']], array('target' => '_blank'));
                    $extra_text = sprintf($extra , $order_link );
                }
                return sprintf($text, strongStr($staff_name),__($action['ActionLine']['param6'],true), $requistion_link).' '.$extra_text;
            case ACTION_REQUISITION_CHANGE_STORE:
                $requistion_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'requisitions' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Updated %s #%s" ,true ).$action['ActionLine']['param9'];$extra_text = "";
                if ( !empty($action['ActionLine']['param2']))
                {
                    $extra = __("for %s",true);
                    $view_link = json_decode($action['ActionLine']['param5'],true) ?? [];
                    if(!empty($view_link)) {
                        $view_link['owner'] = true;
                    }
                    $order_link = $html->link(__($action['ActionLine']['param4'],true). ' #'.$action['ActionLine']['param3'],$view_link+[$action['ActionLine']['param2']], array('target' => '_blank'));
                    $extra_text = sprintf($extra , $order_link );
                }
                return sprintf($text, strongStr($staff_name),__($action['ActionLine']['param6'],true), $requistion_link ,$action['ActionLine']['param9']).' '.$extra_text;
            
                
            case ACTION_REQUISITION_DELETE:
                $requistion_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'requisitions' , 'action' => 'view' ,$action['ActionLine']['primary_id'] ], array('target' => '_blank'));
                $text = __("%s Deleted %s #%s" ,true );$extra_text = "";
                if ( !empty($action['ActionLine']['param2']))
                {
                    $extra = __("for %s",true);
                    $view_link = json_decode($action['ActionLine']['param5'],true) ?? [];
                    if(!empty($view_link)) {
                        $view_link['owner'] = true;
                    }
                    $order_link = $html->link(__($action['ActionLine']['param4'],true). ' #'.$action['ActionLine']['param3'],$view_link+[$action['ActionLine']['param2']], array('target' => '_blank'));
                    $extra_text = sprintf($extra , $order_link );
                }
                return sprintf($text, strongStr($staff_name),__($action['ActionLine']['param6'],true), $requistion_link).' '.$extra_text;
            //Requisitions END
                //Stocktaking START
            case ACTION_STOCKTAKING_ADD:
                $stocktaking_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'stocktakings' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Added %s #%s" ,true );$extra_text = sprintf(__(" with status %s",true),$action['ActionLine']['param2']);
                return sprintf($text, strongStr($staff_name),__("Stocktaking",true), $stocktaking_link).' '.$extra_text;
            case ACTION_STOCKTAKING_UPDATE:
                $stocktaking_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'stocktakings' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Updated %s #%s" ,true );$extra_text = sprintf(__(" with status %s",true),$action['ActionLine']['param2']);
                return sprintf($text, strongStr($staff_name),__("Stocktaking",true), $stocktaking_link).' '.$extra_text;
            case ACTION_STOCKTAKING_DELETE:
               $stocktaking_link = $html->link($action['ActionLine']['param1'] , ['controller' => 'stocktakings' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Deleted %s #%s" ,true );$extra_text = sprintf(__(" with status %s",true),$action['ActionLine']['param2']);
                return sprintf($text, strongStr($staff_name),__("Stocktaking",true), $stocktaking_link).' '.$extra_text;
            //Stocktaking END
            case ACTION_MOVE_TO_BETA:
                $text = __("%s Moved the site to Beta" ,true ) ;
                return sprintf($text, strongStr($staff_name));
           case ACTION_INVOICE_PAYMENT_FAIL:
                $text = __("Fail to Add payment to invoice %s" ,true ) ;
                return sprintf($text, $action['ActionLine']['param2']);
            case ACTION_UPDATE_POS_ACCOUNTING_SETTINGS:
                if($action['ActionLine']['param1']) {
                    $text = __("Pos Accounting Settings Per Invoice Activated" ,true ) ;
                } else {
                    $text = __("Pos Accounting Settings Per Invoice Deactivated" ,true ) ;
                }
                return $text;
            case ACTION_UPDATE_POS_PARTIAL_PAYMENT:
                if($action['ActionLine']['param1']) {
                    $text = __("Pos Accounting Settings For  Partial Payment Activated" ,true ) ;
                } else {
                    $text = __("Pos Accounting Settings For Partial Payment Deactivated" ,true ) ;
                }
                return $text;
            case ACTION_UPDATE_ROUTING_SETTINGS:
                $texts = [];
                $texts[] = sprintf(__("%s routing updated to be %s",true), $action['ActionLine']['param2'], $action['ActionLine']['param1']);
                if($action['ActionLine']['secondary_id']) {
                    $Ja = GetObjectOrLoadModel('JournalAccount');
                    $account = $Ja->findById($action['ActionLine']['secondary_id']);
                    if($account) {
                        $texts[] = sprintf(__("Selected Account %s",true), $account['JournalAccount']['name']);
                    }
                }
                return implode(", ", $texts);
            case ACTION_UPDATE_AUTONUMBER_SETTINGS:
                $texts = [];
                $texts[] = __('Autonumber Settings Changed', true).sprintf(__(" %s (%s) updated to be %s",true), __($action['ActionLine']['param3'], true),$action['ActionLine']['param2'], $action['ActionLine']['param1']);
                if (isset($action['ActionLine']['param4']) && $action['ActionLine']['param4']) {
                    $texts[] = __('(Manual change to auto-number)', true);
                }
                return implode(" ", $texts);
            case ACTION_UPDATE_AUTONUMBER_SETTINGS_UNIQUE_STATUS:
                $texts = [];
                $texts[] = __('Autonumber Unique Settings Changed', true).sprintf(__(" %s (%s) updated to be %s",true), __($action['ActionLine']['param3'], true),$this->YesNo[$action['ActionLine']['param2']], $this->YesNo[$action['ActionLine']['param1']]);
                return implode(", ", $texts);
            case ACTION_UPDATE_AUTONUMBER_SETTINGS_PATTERN:
                $texts = [];
                $texts[] = __('Autonumber Pattern Settings Changed', true).sprintf(__(" %s (%s) updated to be %s",true), __($action['ActionLine']['param3'], true),$action['ActionLine']['param2'], $action['ActionLine']['param1']);
                return implode(", ", $texts);
            case ACTION_UPDATE_AUTONUMBER_SETTINGS_PREFIX:
                $texts = [];
                $texts[] = __('Autonumber Prefix Settings Changed', true).sprintf(__(" %s (%s) updated to be %s",true), __($action['ActionLine']['param3'], true),$action['ActionLine']['param2'], $action['ActionLine']['param1']);
                return implode(", ", $texts);
            case ACTION_UPDATE_AUTONUMBER_SETTINGS_NUMBER_OF_DIGITS:
                $texts = [];
                $texts[] = __('Autonumber Number of digits Changed', true).sprintf(__(" %s (%s) updated to be %s",true), __($action['ActionLine']['param3'], true),$action['ActionLine']['param2'], $action['ActionLine']['param1']);
                return implode(", ", $texts);
            case ACTION_UPDATE_AUTONUMBER_SETTINGS_NUMBER_OF_CHARACTERS:
                $texts = [];
                $texts[] = __('Autonumber Number of characters Changed', true).sprintf(__(" %s (%s) updated to be %s",true), __($action['ActionLine']['param3'], true),$action['ActionLine']['param2'], $action['ActionLine']['param1']);
                return implode(", ", $texts);
            case ACTION_MOVE_TO_LIVE:
                $text = __("%s Moved the site to Live" ,true ) ;
                return sprintf($text, strongStr($staff_name));
            case ACTION_ADD_BOOKING:
                $bookingLink = $html->link($action['ActionLine']['param6'] , ['controller' => 'bookings' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $bookingDate = format_date($action['ActionLine']['param1']);
                $clientLink = $html->link($action['ActionLine']['param2'] , ['controller' => 'clients' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s add booking #%s %s for %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $bookingLink, $bookingDate, $clientLink);
            case ACTION_ADD_TREASURY:
                $t=GetObjectOrLoadModel('Treasury');
                $type=$t->get_types();
                $link = $html->link($action['ActionLine']['param1'] , ['controller' => 'treasuries' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s add %s with name %s status %s primary %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), strongStr($type[$action['ActionLine']['param2']]),$link,strongStr($this->ActiveNotActive[$action['ActionLine']['param3']]),strongStr($this->YesNo[$action['ActionLine']['param4']]));
            case ACTION_EDIT_TREASURY:
                $t=GetObjectOrLoadModel('Treasury');
                $type=$t->get_types();
                $link = $html->link($action['ActionLine']['param1'] , ['controller' => 'treasuries' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s edit %s with name %s status %s primary %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), strongStr($type[$action['ActionLine']['param2']]),$link,strongStr($this->ActiveNotActive[$action['ActionLine']['param3']]),strongStr($this->YesNo[$action['ActionLine']['param4']]));
                case ACTION_DELETE_TREASURY:
                $t=GetObjectOrLoadModel('Treasury');
                $type=$t->get_types();
                $link = $html->link($action['ActionLine']['param1'] , ['controller' => 'treasuries' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s delete %s with name %s status %s primary %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), strongStr($type[$action['ActionLine']['param2']]),$link,strongStr($this->ActiveNotActive[$action['ActionLine']['param3']]),strongStr($this->YesNo[$action['ActionLine']['param4']]));
            case ACTION_ADD_BOOKING_CLIENT:
                $bookingLink = $html->link($action['ActionLine']['param6'] , ['controller' => 'bookings' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $bookingDate = format_date($action['ActionLine']['param1']);
                $clientLink = $html->link($action['ActionLine']['param2'] , ['controller' => 'clients' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s add booking #%s %s" ,true ) ;
                return sprintf($text, strongStr($clientLink), $bookingLink, $bookingDate);
            case ACTION_UPDATE_BOOKING:
                $bookingLink = $html->link($action['ActionLine']['param6'] , ['controller' => 'bookings' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $bookingDate = format_date($action['ActionLine']['param1']);
                $clientLink = $html->link($action['ActionLine']['param2'] , ['controller' => 'clients' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s update booking #%s %s for %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $bookingLink, $bookingDate, $clientLink);
            case ACTION_UPDATE_BOOKING_STATUS:
                $bookingLink = $html->link($action['ActionLine']['param6'] , ['controller' => 'bookings' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $bookingDate = format_date($action['ActionLine']['param1']);
                $clientLink = $html->link($action['ActionLine']['param2'] , ['controller' => 'clients' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s update booking #%s status from %s to %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $bookingLink, $action['ActionLine']['param3'], $action['ActionLine']['param4']);
            case ACTION_CLIENT_CANCEL_BOOKING:
                $bookingLink = $html->link($action['ActionLine']['param6'] , ['controller' => 'bookings' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $bookingDate = format_date($action['ActionLine']['param1']);
                $clientLink = $html->link($action['ActionLine']['param2'] , ['controller' => 'clients' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s update booking #%s status from %s to %s" ,true ) ;
                return sprintf($text, strongStr($clientLink), $bookingLink, $action['ActionLine']['param3'], $action['ActionLine']['param4']);
            case ACTION_DELETE_BOOKING:
                $bookingDate = format_date($action['ActionLine']['param1']);
                $clientLink = $html->link($action['ActionLine']['param2'] , ['controller' => 'clients' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s delete booking #%s %s for %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $action['ActionLine']['primary_id'], $bookingDate, $clientLink);
            case ACTION_CONVERT_BOOKING_TO_INVOICE:
                $bookingLink = $html->link($action['ActionLine']['param6'] , ['controller' => 'bookings' , 'action' => 'view' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $invoiceLink = $html->link($action['ActionLine']['param5'] , ['controller' => 'invoices' , 'action' => 'view' ,$action['ActionLine']['param5'], 'owner' => true ], array('target' => '_blank'));
                $bookingDate = format_date($action['ActionLine']['param1']);
                $clientLink = $html->link($action['ActionLine']['param2'] , ['controller' => 'clients' , 'action' => 'view' ,$action['ActionLine']['secondary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s create new invoice #%s to booking #%s for %s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $invoiceLink, $bookingLink, $bookingDate, $clientLink);

            case ACTION_ADD_LOCAL_CURRENCY_RATE:
                $currencyRateLink = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'local_currency_rates' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Created new Local currency rate #%s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $currencyRateLink);
            case ACTION_UPDATE_LOCAL_CURRENCY_RATE:
                $currencyRateLink = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'local_currency_rates' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Edited Local currency rate #%s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $currencyRateLink);
            case ACTION_DELETE_LOCAL_CURRENCY_RATE:
                $currencyRateLink = $action['ActionLine']['primary_id'];
                $text = __("%s Deleted Local currency rate #%s" ,true ) ;
                return sprintf($text, strongStr($staff_name), $currencyRateLink);
            case ACTION_CLONE_EXPENSE:
                $clonned_expense = $html->link($action['ActionLine']['param4'], array('controller' => "expenses", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $expense = $html->link($action['ActionLine']['param6'], array('controller' => "expenses", 'action' => "view", $action['ActionLine']['param9'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('Clonned new expense #%s from main expense #%s', true);
                return sprintf($text, $expense, $clonned_expense);
            case ACTION_CLONE_MILEAGE:
                $clonned_expense = $html->link($action['ActionLine']['param4'], array('controller' => "expenses", 'action' => "view", $action['ActionLine']['secondary_id'],"?"=>['is_mileage'=>"1"], 'owner' => 'true'), array('target' => '_blank'));
                $expense = $html->link($action['ActionLine']['param6'], array('controller' => "expenses", 'action' => "view", $action['ActionLine']['param9'],"?"=>['is_mileage'=>"1"], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('Clonned new mileage #%s from main mileage #%s', true);
                return sprintf($text, $expense, $clonned_expense);
            case ACTION_CLONE_INCOME:
                $clonned_expense = $html->link($action['ActionLine']['param4'], array('controller' => "incomes", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $expense = $html->link($action['ActionLine']['param6'], array('controller' => "incomes", 'action' => "view", $action['ActionLine']['param9'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('Clonned new income #%s from main income #%s', true);
                return sprintf($text, $expense, $clonned_expense);
            case ACTION_UPDATE_EMAIL:
                $text = __("update email from %s to %s", true);
                return sprintf($text,$action['ActionLine']['param2'], $action['ActionLine']['param3']);
                break;

            case ACTION_DELETE_SUPPLIER_PAYMENT:
                $text = __('Deleted Supplier payment credit #%s', TRUE);
                return sprintf($text, $action['ActionLine']['param5']);
                break;
            case ACTION_ADD_UNIT_TEMPLATE:
                $unit_template = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'unit_templates' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $text = __("%s Created new Unit Template #%s" ,true ) ;
            return sprintf($text, strongStr($staff_name), $unit_template);
            case ACTION_UPDATE_UNIT_TEMPLATE:
                $financial_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'cost_centers' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $closed_text = $action['ActionLine']['param3'] ? __('Opened',true) :  __('Closed',true);
                $text = __("%s Updated %s Financial Year from %s to %s" ,true ) ;
            return sprintf($text, strongStr($staff_name), $closed_text, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
            case ACTION_DELETE_UNIT_TEMPLATE:
                $financial_link = $html->link($action['ActionLine']['primary_id'] , ['controller' => 'cost_centers' , 'action' => 'edit' ,$action['ActionLine']['primary_id'], 'owner' => true ], array('target' => '_blank'));
                $closed_text = $action['ActionLine']['param3'] ? __('Opened',true) :  __('Closed',true);
                $text = __("%s Deleted %s Financial Year from %s to %s" ,true ) ;
            return sprintf($text, strongStr($staff_name), $closed_text, format_date($action['ActionLine']['param1']),format_date($action['ActionLine']['param2']));
            case ACTION_ADD_PURCHASE_CREDIT_NOTE:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view_credit_note", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s created a new %s and %s purchase Credit Note %s for %s totalling %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($this->PoStatuses[$action['ActionLine']['param2']]), strongStr($this->PaymentStatuses[$action['ActionLine']['param7']]), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])));
                break;
            case ACTION_UPDATE_PURCHASE_CREDIT_NOTE:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view_credit_note", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s updated the purchase credit note %s for %s totalling %s, and status: %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PoStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_DELETE_PURCHASE_CREDIT_NOTE:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view_credit_note", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                if ($this->PoStatuses == false)
                    $this->loadPOStatuses();
                $text = __("%s deleted the purchase credit note %s for %s totalling %s, and status was %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($po_link), strongStr($supplier_link), strongStr(format_price_simple($action['ActionLine']['param1'])), strongStr($this->PoStatuses[$action['ActionLine']['param2']]));
                break;
            case ACTION_SEND_PURCHASE_CREDIT_NOTE:
                $po_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => 'purchase_invoices', 'action' => "view_credit_note", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $supplier_link = $html->link($action['ActionLine']['param5'], array('controller' => "suppliers", 'action' => "view", $action['ActionLine']['secondary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $message_link = $html->link('#' . $action['ActionLine']['param8'], array('controller' => "email_logs", 'action' => "view", $action['ActionLine']['param8'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s sent a message %s to the email address %s for purchase credit note %s ", true);
                return sprintf($text, strongStr($staff_name), strongStr($message_link), strongStr(h($action['ActionLine']['param2'])), strongStr($po_link));
                break;
            case ACTION_PRINT_PURCHASE_CREDIT_NOTE:
                $invoice_link = $html->link('#' . $action['ActionLine']['param4'], array('controller' => "purchase_invoices", 'action' => "view_credit_note", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __("%s printed the purchase credit note %s", true);
                return sprintf($text, strongStr($staff_name), strongStr($invoice_link));
                break;
            case ACTION_CHANGE_FOLLOW_UP_STATUS:
                $code = $action['ActionLine']['param4'] ?? $action['ActionLine']['primary_id'];
                $from_id = $action['ActionLine']['secondary_id'];
                $to_id = $action['ActionLine']['param1'];
                $type = __($action['ActionLine']['param2'], true);
                $url_array = json_decode($action['ActionLine']['param3'], true);
                if(!empty($url_array)) {
                    $url_array['owner'] = true;
                }
                $anchorLink = $html->link($type . " #$code", $url_array, array('target' => '_blank'));
                $FollowUpStatusModel = GetObjectOrLoadModel('FollowUpStatus');
                $followUpStatuses = $FollowUpStatusModel->find('all', ['conditions' => ['FollowUpStatus.id' => [$from_id, $to_id]]]);
                $from = $from_id;
                $to = $to_id;
                if($followUpStatuses){
                    foreach($followUpStatuses as $followUpStatus){
                        if($followUpStatus['FollowUpStatus']['id'] == $from_id){
                            $from = $followUpStatus['FollowUpStatus']['name'];
                        }
                        if($followUpStatus['FollowUpStatus']['id'] == $to_id){
                            $to = $followUpStatus['FollowUpStatus']['name'];
                        }
                    }
                }
                return sprintf(__('%s Changed Follow Up Status For %s, from %s to %s', true), strongStr($staff_name), strongStr($anchorLink), strongStr($from), strongStr($to));
            case ACTION_DOWNLOAD_PO_PAYMENT:
                $purchase_order_link = $html->link($action['ActionLine']['param2'], array('controller' => "purchase_invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s downloaded PDF for the receipt #%s of the purchase invoice #%s', TRUE);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), $purchase_order_link);
                break;
            case ACTION_PRINT_PO_PAYMENT:
                $purchase_order_link = $html->link($action['ActionLine']['param2'], array('controller' => "purchase_invoices", 'action' => "view", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                $text = __('%s printed the receipt #%s of the purchase invoice #%s', TRUE);
                return sprintf($text, strongStr($staff_name), strongStr($action['ActionLine']['param3']), $purchase_order_link);
                break;
            case ACTION_CONVERT_INVOICE_TO_PRODUCTION_PLAN:
                $view_invoice_link = $html->link('#' . $action['ActionLine']['primary_id'],
                    ['controller' => 'invoices', 'action' => 'view', $action['ActionLine']['primary_id'], 'owner' => true],
                    ['target' => '_blank']
                );
                return sprintf(
                    "%s converted Invoice %s to Production Plan %s",
                    strongStr($staff_name),
                    $view_invoice_link,
                    sprintf('<a href="/v2/owner/entity/production_plan/%s/show" target="_blank">%s</a>', $action['ActionLine']['secondary_id'], $action['ActionLine']['param5'])
                );
            case ACTION_CONVERT_SALES_ORDER_TO_PRODUCTION_PLAN:
                $sales_order_link = $html->link('#'. $action['ActionLine']['primary_id'], array('controller' => "invoices", 'action' => "view_sales_order", $action['ActionLine']['primary_id'], 'owner' => 'true'), array('target' => '_blank'));
                return sprintf(
                    "%s converted Sales Order %s to Production Plan %s",
                    strongStr($staff_name),
                    $sales_order_link,
                    sprintf('<a href="/v2/owner/entity/production_plan/%s/show" target="_blank">%s</a>', $action['ActionLine']['secondary_id'], $action['ActionLine']['param5'])
                );

            case ACTION_ASSIGN_WORK_ORDER_TRANSACTON:
                $transaction_link = $action['ActionLine']['secondary_id'];
                /** @todo continue other types */
                if ($action['ActionLine']['param1'] == 'invoice') {
                    $transaction_link = $html->link('#' . $action['ActionLine']['secondary_id'],
                        ['controller' => 'invoices', 'action' => 'view', $action['ActionLine']['secondary_id'], 'owner' => true],
                        ['target' => '_blank']
                    );
                }

                $is_workflow = $action['ActionLine']['param2'] == 1;

                $work_order_link = $html->link('#' . $action['ActionLine']['param3'], [
                    'controller' => 'work_orders',
                    'action' => $is_workflow ? 'workflow_view' : 'view',
                    $action['ActionLine']['primary_id'],
                    'owner' => true
                ], ['target' => '_blank']);

                return sprintf(
                    __('%s Assigned a new transaction %s of type %s to %s %s', true),
                    strongStr($staff_name),
                    strongStr($transaction_link),
                    strongStr(__t($action['ActionLine']['param1'])),
                    $is_workflow ? __t('Workflow') : __t('Work Order'),
                    strongStr($work_order_link)
                );
            case ACTION_UNASSIGN_WORK_ORDER_TRANSACTON:
                $transaction_link = $action['ActionLine']['secondary_id'];
                /** @todo continue other types */
                if ($action['ActionLine']['param1'] == 'invoice') {
                    $transaction_link = $html->link('#' . $action['ActionLine']['secondary_id'],
                        ['controller' => 'invoices', 'action' => 'view', $action['ActionLine']['secondary_id'], 'owner' => true],
                        ['target' => '_blank']
                    );
                }

                $is_workflow = $action['ActionLine']['param2'] == 1;

                $work_order_link = $html->link('#' . $action['ActionLine']['param3'], [
                    'controller' => 'work_orders',
                    'action' => $is_workflow ? 'workflow_view' : 'view',
                    $action['ActionLine']['primary_id'],
                    'owner' => true
                ], ['target' => '_blank']);

                return sprintf(
                    __('%s Unassigned a transaction %s of type %s from %s %s', true),
                    strongStr($staff_name),
                    strongStr($transaction_link),
                    strongStr(__t($action['ActionLine']['param1'])),
                    $is_workflow ? __t('Workflow') : __t('Work Order'),
                    strongStr($work_order_link)
                );
        }


    }

    public function __call($name, $arguments) {
        $class = new ReflectionClass($this->report);
        try {
            if ($class->getMethod($name)->isPublic()) {
                return call_user_func_array([$this->report, $name], $arguments);
//                return call_user_method_array($name, $this->report, $arguments);
            }
        } catch (Exception $e) {
            return false;
        }

        return false;
    }

}

?>
