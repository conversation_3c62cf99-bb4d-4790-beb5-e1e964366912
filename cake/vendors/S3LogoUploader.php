<?php
putenv("AWS_SUPPRESS_PHP_DEPRECATION_WARNING=true");
use Aws\Exception\AwsException;
use Aws\S3\S3Client;
class S3LogoUploader
{
    protected static $s3Client;
    public static function createS3Client()
    {
        if(!empty(self::$s3Client)) {
            return self::$s3Client;
        }

        $env = APP_ENV;

        $config = [
            'region' => AWS_DEFAULT_REGION,
            'bucket' => AWS_LOGO_BUCKET,
            'version' => 'latest',
        ];

        if (in_array($env, ['local'])) {
            $config = [
                'region' => AWS_DEFAULT_REGION,
                'version' => 'latest',
                'credentials' => array(
                    'key' => AWS_ACCESS_KEY_ID,
                    'secret'  => AWS_SECRET_ACCESS_KEY,
                )
            ];
        }

        if (in_array($env, ['enterprize'])) {
            $config = [
                'region' => AWS_DEFAULT_REGION,
                'bucket' => AWS_LOGO_BUCKET,
                'version' => 'latest',
                'endpoint' => AWS_ENDPOINT,
                'use_path_style_endpoint' => true,
                'credentials' => array(
                    'key' => AWS_ACCESS_KEY_ID,
                    'secret'  => AWS_SECRET_ACCESS_KEY,
                )
            ];
        }

        return self::$s3Client =  new S3Client($config);
    }

    public static function uploadToS3($key , $fileContents)
    {
        $s3 = self::createS3Client();

        try {
            $s3->putObject([
                'Bucket' => AWS_LOGO_BUCKET,
                'Key'    => $key,
                'Body'   => $fileContents,
                'ACL'    => 'private',
            ]);

            return ['status'=>true,'file'=>$key];
        } catch (\Exception $e) {
            return ['status'=>false,'message'=>$e->getMessage()];
        }
    }

    public static function downloadLogo($key,$saveAs)
    {
        $s3 = self::createS3Client();

        try {
            $result = $s3->getObject([
                'Bucket' => AWS_LOGO_BUCKET,
                'Key'    => $key,
                'SaveAs' => $saveAs
            ]);
        } catch (AwsException $e) {
            @unlink($saveAs);
            return false;
        }
        return $saveAs;
    }
}