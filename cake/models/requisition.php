<?php

App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
use App\Services\TrackStock\TrackingItemConverterAbstract;
use App\Events\Queue\UpdateForOrderRequested;
use App\Utils\TrackStockUtil;
use Izam\Daftra\Common\Utils\RequisitionOrderTypeUtil;
use Izam\ManufacturingOrder\Events\MoMaterialsAveragePriceUpdated;

/**
 * DON'T ADD/UPDATE/DELETE Requisitions without using addRequisition/updateRequisition/delete_with_related
 */
 class Requisition extends AppModel {
    var $applyBranch = ['onFind' => true, 'onSave' => true];
    var $name = 'Requisition';
    var $actsAs = array(
		'journal' => array(
                    'is_journal'=>true,
                    'entity_type'=>array('requisition'),
                    'disable_auto_update'=>true,
                 ),
		
    );

    const REQUISITION = 'Requisition';
    //Delivery Status
    const DELIVERY_STATUS_RECEIVED = 1;
    const DELIVERY_STATUS_REJECTED = 2;
    const DELIVERY_STATUS_PENDING = 3;
    const DELIVERY_STATUS_PARTIALLY_RECEIVED = 4;
    const DELIVERY_STATUS_PARTIALLY_REJECTED = 5;
    //Types
    const TYPE_INBOUND = 1;
    const TYPE_OUTBOUND = 2;
    const TYPE_NOEFFECT = 3;
    //Statuses
    const STATUS_PENDING = 1;//Pending first time to create invoice
//    const STATUS_NOT_ALL_AVAILABLE = 2;//All STOCK ARE NOT AVAILABLE - no stock transaction
    const STATUS_ACCEPTED = 3;//STOCK TRANSACTION OCCURS
    const STATUS_CANCELLED = 4;//CANCELLED - no stock transaction
    const STATUS_DRAFT = 5;//NOTUSED
    const STATUS_MODIFIED = 6;//STOCK TRANSACTION OCCURS + creates new requisition

    //Order types
    //IF ADD NEW TYPE ADD IT TO STOCK TRANSACTION MODEL (const => SOURCE_RQ_** and function getSourceRequisition())
    const ORDER_TYPE_MANUAL_INBOUND = 1;
    const ORDER_TYPE_MANUAL_OUTBOUND = 2;
    const ORDER_TYPE_INVOICE = 3;
    const ORDER_TYPE_INVOICE_REFUND = 4;
    const ORDER_TYPE_INVOICE_CREDIT_NOTE = 5;
    const ORDER_TYPE_PURCHASE_ORDER = 6;
    const ORDER_TYPE_PURCHASE_REFUND = 7;
    const ORDER_TYPE_PURCHASE_DEBIT_NOTE = 15;
    const ORDER_TYPE_TRANSFER_REQUISITION = 8;
    const ORDER_TYPE_TRANSFER_INBOUND = 9;
    const ORDER_TYPE_TRANSFER_OUTBOUND = 10;
    const ORDER_TYPE_POS_INBOUND = 11;
    const ORDER_TYPE_POS_OUTBOUND = 12;
    const ORDER_TYPE_STOCKTAKING_OUT = 13;
    const ORDER_TYPE_STOCKTAKING_IN = 14;
    const ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND = 16;
    const ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL = 18;
    const ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP = 21;
    const ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT = 22;
    const ORDER_TYPE_MANUAL_IMPORT_INBOUND = 23;
    const ORDER_TYPE_MANUAL_IMPORT_OUTBOUND = 24;

    //IF ADD NEW TYPE ADD IT TO STOCK TRANSACTION MODEL (const => SOURCE_RQ_** and function getSourceRequisition())

    public $stocktransaction_sources = [] ; //filled in constructor to load StockTransaction model
    public static $requisition_statuses = [
        self::STATUS_PENDING => ['label' => "Under Delivery" ,'background-color' => 'yellow','color' => 'black'] ,
//        self::STATUS_NOT_ALL_AVAILABLE => ['label' => "Not All Available", 'color' => 'red'] ,
        self::STATUS_ACCEPTED => ['label' => "Accepted", 'background-color' => 'green','color' => 'white'] ,
        self::STATUS_CANCELLED => ['label' => "Rejected", 'background-color' => 'red','color' => 'white'] ,
    ] ;
    public static $requisition_types = [
        self::TYPE_INBOUND => ['label' => "Inbound Requisition" , 'icon' => 'fa-arrow-down' ,'created_controller' => 'purchase_invoices', 'number_field' => 'no', 'created' => 'PurchaseOrder' ,'created_item' => 'PurchaseOrderItem','created_order_type' => self::ORDER_TYPE_PURCHASE_ORDER] ,
        self::TYPE_OUTBOUND => ['label' => "Outbound Requisition" , 'icon' => 'fa-arrow-up','created_controller' => 'invoices', 'number_field' => 'no', 'created' => 'Invoice' ,'created_item' => 'InvoiceItem','created_order_type' => self::ORDER_TYPE_INVOICE] ,
        self::TYPE_NOEFFECT=> ['label' => "Manual Transfer" , 'icon' => 'fa-arrows-v', 'number_field' => '']
    ] ; 
    public static $requisition_order_types = [
        self::ORDER_TYPE_MANUAL_INBOUND => ['viewWhenDisabled' => true, 'journal_account' => 'other_credit' , 'label' => 'Inbound Requisition', 'type' => self::TYPE_INBOUND, 'is_sub' => false],
        self::ORDER_TYPE_MANUAL_OUTBOUND => ['viewWhenDisabled' => true,'journal_account' => 'other_debit' ,'label' => 'Outbound Requisition', 'type' => self::TYPE_OUTBOUND, 'is_sub' => false],

        self::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT => ['viewWhenDisabled' => false, 'source_model' => 'ManufacturingOrder', 'number_field' => 'code', 'label' => 'Manufacturing Order Main Product Inbound Requisition', 'type' => self::TYPE_INBOUND, 'is_sub' => false],
        self::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP => ['viewWhenDisabled' => false, 'source_model' => 'ManufacturingOrder', 'number_field' => 'code', 'label' => 'Manufacturing Order Scrap Items Inbound Requisition', 'type' => self::TYPE_INBOUND, 'is_sub' => false],
        self::ORDER_TYPE_STOCKTAKING_OUT => ['viewWhenDisabled' => true,'quantity_field' => 'shortage_value', 'number_field' => 'number','journal_account' => 'sales_cost' ,'view_url_array' => ['controller' => 'stocktakings' , 'action' => 'view'] , 'item_id' => 'stocktaking_id' , 'label' => 'Stocktaking Outbound', 'type' => self::TYPE_OUTBOUND, 'is_sub' => true, 'source_model' => "Stocktaking", 'item_source_model' => "StocktakingRecord"],
        self::ORDER_TYPE_STOCKTAKING_IN => ['viewWhenDisabled' => true,'quantity_field' => 'shortage_value',  'number_field' => 'number','journal_account' => 'sales_cost' ,'view_url_array' => ['controller' => 'stocktakings' , 'action' => 'view'] , 'item_id' => 'stocktaking_id' , 'label' => 'Stocktaking Inbound', 'type' => self::TYPE_INBOUND, 'is_sub' => true, 'source_model' => "Stocktaking", 'item_source_model' => "StocktakingRecord"],
        
        self::ORDER_TYPE_INVOICE => ['taxModelName' => 'InvoiceTax','taxForeignKey' => 'invoice_id', 'viewWhenDisabled' => false,'provider_class' => 'Client','provider_id' => 'client_id' , 'provider_url' => ['controller' => 'clients' , 'action' => 'view'] , 'number_field' => 'no','journal_account' => 'sales_cost' ,'view_url_array' => ['controller' => 'invoices' , 'action' => 'view'], 'number_field' => 'no' , 'item_id' => 'invoice_id' , 'label' => 'Invoice', 'type' => self::TYPE_OUTBOUND, 'is_sub' => true, 'source_model' => "Invoice", 'item_source_model' => "InvoiceItem", 'conditions' => ['Invoice.type' => 0]],
        self::ORDER_TYPE_INVOICE_REFUND => ['taxModelName' => 'InvoiceTax','taxForeignKey' => 'invoice_id', 'viewWhenDisabled' => false,'provider_class' => 'Client','provider_id' => 'client_id', 'provider_url' => ['controller' => 'clients' , 'action' => 'view'] ,'number_field' => 'no','journal_account' => 'sales_cost' ,'view_url_array' => ['controller' => 'invoices' , 'action' => 'view_refund'],'number_field' => 'no' ,'item_id' => 'invoice_id' ,'label' => 'Refund Receipt', 'type' => self::TYPE_INBOUND, 'is_sub' => true, 'source_model' => "Invoice", 'item_source_model' => "InvoiceItem", 'conditions' => ['Invoice.type' => 6]],
        self::ORDER_TYPE_INVOICE_CREDIT_NOTE => ['taxModelName' => 'InvoiceTax','taxForeignKey' => 'invoice_id', 'viewWhenDisabled' => false,'provider_class' => 'Client','provider_id' => 'client_id' , 'provider_url' => ['controller' => 'clients' , 'action' => 'view'],'number_field' => 'no','journal_account' => 'sales_cost' ,'view_url_array' => ['controller' => 'invoices' , 'action' => 'view_creditnote'],'number_field' => 'no' ,'item_id' => 'invoice_id' ,'label' => 'Credit Note', 'type' => self::TYPE_INBOUND, 'is_sub' => true, 'source_model' => "Invoice", 'item_source_model' => "InvoiceItem", 'conditions' => ['Invoice.type' => 5]],
//        self::ORDER_TYPE_CREATED_PURCHASE_ORDER => ['provider_class' => 'Supplier','provider_id' => 'supplier_id', 'provider_url' => ['controller' => 'suppliers' , 'action' => 'view'] ,'number_field' => 'no','journal_account' => 'purchases' ,'view_url_array' => ['controller' => 'purchase_invoices' , 'action' => 'view'],'number_field' => 'no' ,'item_id' => 'purchase_order_id' ,'label' => 'Purchase Order', 'type' => self::TYPE_INBOUND, 'is_sub' => true, 'source_model' => "PurchaseOrder", 'item_source_model' => "PurchaseOrderItem", 'conditions' => ['PurchaseOrder.type' => 0]],
        self::ORDER_TYPE_PURCHASE_ORDER => ['taxModelName' => 'PurchaseOrderTax', 'taxForeignKey' => 'purchase_order_id', 'viewWhenDisabled' => false,'getJournalsCallback' => 'getPOJournalAccountId',  'provider_class' => 'Supplier','provider_id' => 'supplier_id', 'provider_url' => ['controller' => 'suppliers' , 'action' => 'view'] ,'number_field' => 'no','journal_account' => 'purchases' ,'view_url_array' => ['controller' => 'purchase_invoices' , 'action' => 'view'],'number_field' => 'no' ,'item_id' => 'purchase_order_id' ,'label' => 'Purchase Invoice', 'type' => self::TYPE_INBOUND, 'is_sub' => true, 'source_model' => "PurchaseOrder", 'item_source_model' => "PurchaseOrderItem", 'conditions' => ['PurchaseOrder.type' => 0]],
        self::ORDER_TYPE_PURCHASE_REFUND => ['taxModelName' => 'PurchaseOrderTax', 'taxForeignKey' => 'purchase_order_id', 'viewWhenDisabled' => false,'getJournalsCallback' => 'getPORefundJournalAccountId','provider_class' => 'Supplier','provider_id' => 'supplier_id', 'provider_url' => ['controller' => 'suppliers' , 'action' => 'view'] ,'number_field' => 'no','journal_account' => 'purchases' ,'view_url_array' => ['controller' => 'purchase_invoices' , 'action' => 'view_refund'],'number_field' => 'no' ,'item_id' => 'purchase_order_id' ,'label' => 'Purchase Refund', 'type' => self::TYPE_OUTBOUND, 'is_sub' => true, 'source_model' => "PurchaseOrder", 'item_source_model' => "PurchaseOrderItem", 'conditions' => ['PurchaseOrder.type' => 6]],
        self::ORDER_TYPE_PURCHASE_DEBIT_NOTE => ['taxModelName' => 'PurchaseOrderTax', 'taxForeignKey' => 'purchase_order_id', 'viewWhenDisabled' => false,'getJournalsCallback' => 'getPODebitJournalAccountId','provider_class' => 'Supplier','provider_id' => 'supplier_id', 'provider_url' => ['controller' => 'suppliers' , 'action' => 'view'] ,'number_field' => 'no','journal_account' => 'purchases' ,'view_url_array' => ['controller' => 'purchase_invoices' , 'action' => 'view_debit_note'],'number_field' => 'no' ,'item_id' => 'purchase_order_id' ,'label' => 'Purchase Debit Note', 'type' => self::TYPE_OUTBOUND, 'is_sub' => true, 'source_model' => "PurchaseOrder", 'item_source_model' => "PurchaseOrderItem", 'conditions' => ['PurchaseOrder.type' => 14]],
        self::ORDER_TYPE_TRANSFER_REQUISITION => ['viewWhenDisabled' => false,'number_field' => 'number', 'source_model' => 'Requisition','item_source_model' => "RequisitionItem", 'label' => 'Manual Transfer', 'type' => self::TYPE_NOEFFECT, 'is_sub' => false],
//        self::ORDER_TYPE_TRANSFER_INBOUND => ['number_field' => 'number' ,'number_field' => 'number' ,'label' => 'Transfer Inbound', 'type' => self::TYPE_INBOUND, 'is_sub' => true, 'source_model' => "Requisition", 'conditions' => ['Requisition.order_type' => self::ORDER_TYPE_TRANSFER_REQUISITION]],
//        self::ORDER_TYPE_TRANSFER_OUTBOUND => ['number_field' => 'number' ,'label' => 'Transfer Outbound', 'type' => self::TYPE_OUTBOUND, 'is_sub' => true, 'source_model' => "Requisition", 'conditions' => ['Requisition.order_type' => self::ORDER_TYPE_TRANSFER_REQUISITION]],
        self::ORDER_TYPE_POS_OUTBOUND => ['viewWhenDisabled' => false,'journal_account' => 'sales_cost' ,'label' => 'POS Shift Outbound', 'type' => self::TYPE_OUTBOUND, 'is_sub' => false ,'view_url_array' => ['controller' => 'pos_shifts' , 'action' => 'view'],'source_model' => 'PosShift'],
        self::ORDER_TYPE_POS_INBOUND => ['viewWhenDisabled' => false,'journal_account' => 'sales_cost' ,'label' => 'POS Shift Inbound', 'type' => self::TYPE_INBOUND, 'is_sub' => false ,'view_url_array' => ['controller' => 'pos_shifts' , 'action' => 'view'],'source_model' => 'PosShift'],
        self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND => ['viewWhenDisabled' => false,'source_model' => 'ManufacturingOrder'  ,'item_source_model' => "ManufacturingOrderMaterial" ,'label' => 'Manufacturing Order Returned Materials', 'type' => self::TYPE_INBOUND, 'is_sub' => false],
        self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL => ['viewWhenDisabled' => false,'journal_account' => 'sales_cost' ,'item_source_model' => "ManufacturingOrderMaterial",'label' => 'Outbound manufacturing order Material', 'type' => self::TYPE_OUTBOUND, 'is_sub' => false ,'view_url_array' => ['controller' => 'manufacturing_orders' , 'action' => 'view'],'source_model' => 'ManufacturingOrder'],
        self::ORDER_TYPE_MANUAL_IMPORT_INBOUND => ['viewWhenDisabled' => false, 'journal_account' => 'other_credit' ,'label' => 'Manual Import Inbound', 'type' => self::TYPE_INBOUND, 'is_sub' => false ,'view_url_array' => ['controller' => 'requisitions' , 'action' => 'view']],
        self::ORDER_TYPE_MANUAL_IMPORT_OUTBOUND => ['viewWhenDisabled' => false ,'journal_account' => 'other_debit' , 'label' => 'Manual Import Outbound', 'type' => self::TYPE_OUTBOUND, 'is_sub' => false ,'view_url_array' => ['controller' => 'requisitions' , 'action' => 'view']],

       ];
    public $hasMany = array(
        'RequisitionItem' => array('order' => 'RequisitionItem.display_order', 'dependant' => true),
    );
 
	var $belongsTo = array(
        'Client' => array('className' => 'Client', 'foreignKey' => 'client_id'),
        'Supplier' => array('className' => 'Supplier', 'foreignKey' => 'supplier_id'),
        'Store' => array('className' => 'Store', 'foreignKey' => 'store_id'),

    );
    public static $delivery_status_list = [
        self::DELIVERY_STATUS_PARTIALLY_RECEIVED => [ 'background-color' => 'green','color' => 'white', 'label' => "Partially Received"],
        self::DELIVERY_STATUS_PARTIALLY_REJECTED => ['background-color' => 'red','color' => 'white','label' => "Partially Rejected"],
        self::DELIVERY_STATUS_PENDING => ['background-color' => 'yellow','color' => 'black','label' => "Under Delivery"],
        self::DELIVERY_STATUS_RECEIVED => ['background-color' => 'green','color' => 'white','label' =>"Received"],
        self::DELIVERY_STATUS_REJECTED => ['background-color' => 'red','color' => 'white','label' => "Rejected"],
    ];

    function __construct($id = false, $table = null, $ds = null) {
        $this->validate = array_merge($this->validate, [
            'store_id' => array(
                'rule' => 'isStoreActive',
                'message' => __t('You cannot add the transaction through a suspended warehouse')
            ),
            'to_store_id' => array(
                'rule' => 'isStoreToActive',
                'message' => __t('You cannot add the transaction through a suspended warehouse')
            ),
            'branch_id' => array(
                'rule' => 'checkBranchActive',
                'message' => __t('You cannot add a transaction in a suspended branch')
            ),
        ]);
        parent::__construct($id, $table, $ds);
        $this->loadModel('StockTransaction');
        $this->stocktransaction_sources = [
            self::ORDER_TYPE_MANUAL_INBOUND => ['StockTransactionSource' =>StockTransaction::SOURCE_MANUAL ],
            self::ORDER_TYPE_MANUAL_OUTBOUND => ['StockTransactionSource' =>StockTransaction::SOURCE_MANUAL ],
            self::ORDER_TYPE_STOCKTAKING_IN => ['StockTransactionSource' =>StockTransaction::SOURCE_MANUAL ],
            self::ORDER_TYPE_STOCKTAKING_OUT => ['StockTransactionSource' =>StockTransaction::SOURCE_MANUAL ],
            self::ORDER_TYPE_POS_OUTBOUND => ['StockTransactionSource' =>StockTransaction::SOURCE_INVOICE ],
            self::ORDER_TYPE_POS_INBOUND => ['StockTransactionSource' =>StockTransaction::SOURCE_RR ],
            
            self::ORDER_TYPE_INVOICE => ['StockTransactionSource' =>StockTransaction::SOURCE_INVOICE ],
            self::ORDER_TYPE_INVOICE_REFUND => ['StockTransactionSource' =>StockTransaction::SOURCE_RR ],
            self::ORDER_TYPE_INVOICE_CREDIT_NOTE => ['StockTransactionSource' =>StockTransaction::SOURCE_CN ],
            self::ORDER_TYPE_PURCHASE_ORDER => ['StockTransactionSource' =>StockTransaction::SOURCE_PO ],
            self::ORDER_TYPE_PURCHASE_REFUND => ['StockTransactionSource' =>StockTransaction::SOURCE_PR ],
            self::ORDER_TYPE_PURCHASE_DEBIT_NOTE => ['StockTransactionSource' =>StockTransaction::SOURCE_PDN ],
            self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND => ['StockTransactionSource' => StockTransaction::SOURCE_MANUAL],
            self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL => ['StockTransactionSource' =>StockTransaction::SOURCE_RQ_MANUFACTURE_ORDER_MATERIAL_OUTBOUND ],
            self::ORDER_TYPE_MANUAL_IMPORT_INBOUND => ['StockTransactionSource' => StockTransaction::SOURCE_MANUAL],
            self::ORDER_TYPE_MANUAL_IMPORT_OUTBOUND => ['StockTransactionSource' => StockTransaction::SOURCE_MANUAL],
        ];
        foreach ( $this->stocktransaction_sources as $k => $v){
            self::$requisition_order_types[$k] = array_merge($v,self::$requisition_order_types[$k]);
        }
    }
    static function getDeliveryStatus(){
    $DeliveryStatus=array();
    foreach(self::$delivery_status_list as $key=>$value){
        $DeliveryStatus[$key]=__($value['label'], true);
    }

  
    return $DeliveryStatus;
}
    function beforeSave($options = array()) {
        parent::beforeSave($options);
        if (empty($this->data[$this->alias]['id']) && empty($this->data[$this->alias]['staff_id'])) {
            $this->data[$this->alias]['staff_id'] = getAuthOwner('staff_id');
        }
        return true;
    }
    /**
     * Saves new requisitions / updates old pending requisitions
     * @param INT|array $order order_id or order data 
     * @param INT $order_type
     * @param boolean $get_pending adds pending to the statuses list to search for when comparing the order with existing requisitions
     * @param boolean $get_cancelled adds cancelled to the statuses list to search for when comparing the order with existing requisitions
     * @return boolean|array false on (requisitions have more than order ) , true on (requisitions same as order data ) , new requisitions as a difference between order and existing requisitions
     */
    function updateForOrder($order , $order_type , $get_pending= false , $get_cancelled = true ,$created_status = 1 , $action = null){
	    // For Now we Only want to run this Function in the Background Queue only for API/Rest Requests
	    $applyNewWayForCommunication = IS_REST;
	    if (!isRunningInQueue() && $applyNewWayForCommunication) {
		    dispatch_event_action(new UpdateForOrderRequested([
			    'order' => $order,
			    'order_type' => $order_type,
			    'get_pending' => $get_pending,
			    'get_cancelled' => $get_cancelled,
			    'created_status' => $created_status,
		    ]));
		    return true;
	    }
        $logData = [];
        $model = Requisition::$requisition_order_types[$order_type]['source_model']  ;
        $item_model = Requisition::$requisition_order_types[$order_type]['item_source_model']  ;
        $stockTransactionSource = Requisition::$requisition_order_types[$order_type]['StockTransactionSource'];
        if (is_numeric($order)){
            $order = $this->getOrder($order , $order_type);
        }

	    if (empty($model) || empty($item_model) || ($order_type == self::ORDER_TYPE_INVOICE && $order['Invoice']['type'] == Invoice::TEMPINVOICE)) {
		    return false;
	    }
        // Should Only Enter this Incase the $order was sent as array and is missing $order[$model] (Incase of Requisition based Source Type)
        if (!isset($order[$model])) {
            $order[$model] = $this->getOrder($order['Requisition']['order_id'], $order_type)[$model];
        }

        if ($model == "Invoice" || $model == "PurchaseOrder") {
            $this->create();
            $originalRequisition = $this->find('first', ['conditions' => ['Requisition.order_id' => $order[$model]['id'], 'Requisition.order_type' => $order_type]]);
            $this->id = $originalRequisition["Requisition"]["id"];
            $this->saveField('order_number', $order[$model]["no"]);
        }
        if(in_array($order_type, [RequisitionOrderTypeUtil::ORDER_TYPE_INVOICE, RequisitionOrderTypeUtil::ORDER_TYPE_INVOICE_REFUND, RequisitionOrderTypeUtil::ORDER_TYPE_INVOICE_CREDIT_NOTE])){
            $this->id = $originalRequisition["Requisition"]["id"];
            $this->saveField('client_id', $order['Client']["id"]);
        }
        if ( !empty($stockTransactionSource))
        {
            if($model == "Invoice") {
                GetObjectOrLoadModel('Invoice')->update_cost_journals($order[$model]['id']);
            }
            $sts = $this->StockTransaction->find('all' , ['recursive' => -1, 'conditions' => ['source_type' => $stockTransactionSource , 'order_id' => $order[$model]['id']] ]);
            if($model !== 'Stocktaking') {
                //this section handles delete of the stock transactions of an order
                //before the requisition was activated
                //but in case of stocktaking it will have not stock transactions
                //so we don't need to delete it as well as that the source of stocktaking transactions
                //is manual so it may cause an error that it will delete manual transactions not the stock taking transactions
                foreach($sts as $s){
                    StockTransaction::removeTransaction($s['StockTransaction']);
                }
            }

        }
        // if $order not found stop update
        if(empty($order)){
            return ;
        }
        //For order items that doesn't have store_id
        foreach ( $order[$item_model] as &$item){
            if ( empty($item['store_id'])) $item['store_id'] = $order[$model]['store_id'];
            unset($item); //for the foreach with reference
        }
        $deleted_conditions = [ 'Requisition.order_id' =>$order[$model]['id'] , 'Requisition.order_type' => $order_type ] ;
        if ( empty($order[$model]['draft']))
        {
            $new_requisitions = $this->compare_requisitions($order, $order_type, $get_pending , $get_cancelled,$created_status) ;
            if ( empty($new_requisitions)){
                /**
                 * here we now that the user didn't change the order items
                 * so we update just the requisition delivery status
                 */
                $this->loadModel($model);
                $this->{$model}->id = $order[$model]['id'] ;
                if (!empty($this->{$model}->id) && $this->{$model}->exists(true)) {
                    $this->{$model}->saveField('requisition_delivery_status',$this->updateOrderStatus($order,$order_type));
                } else {
                	// This Is used to delete requisitions that it's source was removed (hopefully won't have side effects)
	                $deleted_requisitions = $this->find('all' , ['recursive' => -1 , 'conditions' => ['Requisition.order_id' => $this->{$model}->id]]) ;
	                foreach ( $deleted_requisitions as $r  ){
		                $this->delete_with_related($r['Requisition']['id']) ;
	                }
                }
                return false ;
            }

            $ids = [] ;
            $logData['requisitionAddUpdate'] = [];
            $logData['$new_requisitions'] = $new_requisitions;
            $isDeleteRequisitionRelatedToStockRequest = $action == 'delete' && !empty($order['Requisition']) && $this->checkIfStockRequest($order);
            foreach ($new_requisitions as $r){
                if($isDeleteRequisitionRelatedToStockRequest){
                    break;
                }
                $existing = $this->find('first' , ['recursive' => 1 , 'conditions' => ['Requisition.store_id' =>$r['Requisition']['store_id'] ,  'Requisition.status' => Requisition::STATUS_PENDING ,    'Requisition.order_id' => $order[$model]['id'] , 'Requisition.order_type' => $order_type] ]);
                if ( $existing ){
                    $r['Requisition']['id'] = $existing['Requisition']['id'] ;
                    unset($r['Requisition']['number']) ;
                    $logData['requisitionAddUpdate'][] = 'update';
                    $saved_requisition = $this->updateRequisition($r);
                    if (isset($saved_requisition['status']) && $saved_requisition['status'] == false) {
                        $ids[] = $existing['Requisition']['id']; //in case error happen while save $new_requisitions(validation error or else)  don't delete old requisitions
                    }

                }else {
                    $logData['requisitionAddUpdate'][] = 'add';
                    $saved_requisition = $this->addRequisition($r);
                }
                $ids[]  = $saved_requisition['data']['Requisition']['id'] ;
            }
            $deleted_conditions['Requisition.status'] = Requisition::STATUS_PENDING;
            if (! empty($ids) && count($ids) > 1)
            {
                $deleted_conditions['Requisition.id not '] = $ids;
            }else if (count($ids) == 1)
            {
                $deleted_conditions['Requisition.id <>'] = $ids[0];
            }
        }
        $deleted_requisitions = $this->find('all' , ['recursive' => -1 , 'conditions' => $deleted_conditions ] ) ;
        foreach ( $deleted_requisitions as $r  ){
            $this->delete_with_related($r['Requisition']['id']) ;
        }
        $this->loadModel($model);
        $this->{$model}->id = $order[$model]['id'];
        if (empty($order[$model]['draft']))
        {
            if (!empty($this->{$model}->id) && $this->{$model}->exists(true)) {
                $requisition_delivery_status = $this->updateOrderStatus($order,$order_type);
                $this->{$model}->saveField('requisition_delivery_status', $requisition_delivery_status);
            }
            if ( $order[$model]['staff_id'] != getAuthOwner('staff_id'))
            {
                App::import('Vendor', 'notification_2');
                $trigger = NotificationV2::get_trigger();
                NotificationV2::add_notification(
                    NotificationV2::NOTI_ORDER_DELIVERY_UPDATE,
                    $order[$model]['id'],//Reference/item id
                    $order[$model]['staff_id'], //staff to be notified ( can be array )
                    NotificationV2::NOTI_USER_TYPE_STAFF,
                    $trigger['trigger_type'],
                    $trigger['trigger_id'],
                    NotificationV2::NOTI_ACTION_UPDATE,
                    [
                        'order' =>__(Requisition::$requisition_order_types[$order_type]['label'],true).' #'.$order[$model][Requisition::$requisition_order_types[$order_type]['number_field']] ,
                    ]);
            }
        } else {
            if ($this->{$model}->exists(true)){
	            $this->{$model}->saveField('requisition_delivery_status',NULL);
            }
        }
        if(in_array($order_type, [RequisitionOrderTypeUtil::ORDER_TYPE_PURCHASE_ORDER, RequisitionOrderTypeUtil::ORDER_TYPE_PURCHASE_REFUND, RequisitionOrderTypeUtil::ORDER_TYPE_PURCHASE_DEBIT_NOTE])){
            $originalRequisition = $this->find('first', ['conditions' => ['Requisition.order_id' => $order['PurchaseOrder']['id'], 'Requisition.order_type' => $order_type]]);
            $this->id = $originalRequisition["Requisition"]["id"];
            $this->saveField('supplier_id', $order['PurchaseOrder']['supplier_id']);
        }
        return ($new_requisitions ?: true);
    }
    /**
     * Gets the order associated 
     * @param INT $order_id
     * @param INT $order_type
     */
    function getOrder ($order_id , $order_type ){
        $source_model = Requisition::$requisition_order_types[$order_type]['source_model'] ;
        if ( !empty($source_model) ){
            $this->loadModel($source_model);
            return $this->{$source_model}->find('first' , ['recursive' => 2 , 'conditions' => ["$source_model.id" => $order_id] ]) ;
        }
	    return false ;
    }

    /**
     * Compares requisitions with the order data
     * @param INT|array $order order_id or order data
     * @param INT $order_type
     * @param boolean $get_pending adds pending to the statuses list to search for when comparing the order with existing requisitions
     * @param bool $get_cancelled
     * @return boolean|array false on (requisitions have more than order ) , true on (requisitions same as order data ) , new requisitions as a difference between order and existing requisitions
     */
    function compare_requisitions ( $order , $order_type , $get_pending = false , $get_cancelled = true ,$created_status = 1  ) {
        App::import('Vendor', 'AutoNumber');
        $item_model = Requisition::$requisition_order_types[$order_type]['item_source_model']  ;
        $model = Requisition::$requisition_order_types[$order_type]['source_model']  ;
        $item_id = Requisition::$requisition_order_types[$order_type]['item_id']  ;
        $requisition_type = Requisition::$requisition_order_types[$order_type]['type']  ;
        $quantity_field = (Requisition::$requisition_order_types[$order_type]['quantity_field']?:'quantity');
        if ( is_numeric($order)){
            $this->loadModel($model);
            $order = $this->{$model}->find('first' ,['recursive' => -1 ,'conditions' =>['id' => $order]]);
            $order[$model]['date'] = format_datetime($order[$model]['date']);
        }
        if ( !empty($order[$model]['draft']) )
        {
            return false ;
        }
        if ( empty($order[$item_model]))
        {
            $this->loadModel($item_model);
            $items = $this->{$item_model}->find('all' , ['recursive' => 1 , 'conditions' => [$item_id => $order[$model]['id']] ]);
            foreach ( $items as $i ){
                $order[$item_model][] = $i[$item_model] ; 
            }
        }
        $item_orders = $order[$item_model] ; //We are changing this because the items need to be manipulated first
        $order[$item_model] = [] ;
        $original_item_orders = $item_orders;
        /**
         * here we are calculating each product total quantity in each store
         * and its average price in the order
         */
        foreach ( $item_orders as &$item){
            if ( empty($item['store_id'])){ $item['store_id'] = $order[$model]['store_id'];};

            $is_tracking=isset($item['Product']) && $item['Product']['tracking_type'] != TrackStockUtil::QUANTITY_ONLY && isset($item[$item['Product']['tracking_type']]) ;
            if (isset($order[$item_model][$item['store_id']][$item['product_id']]) && !$is_tracking) {
                if($order[$item_model][$item['store_id']][$item['product_id']]['quantity'] == ''){
                    $order[$item_model][$item['store_id']][$item['product_id']]['quantity'] = 0;
                }
                if($item['quantity'] == ''){
                    $item['quantity'] = 0;
                }
                $oldQuantity = $order[$item_model][$item['store_id']][$item['product_id']]['quantity'];
                $newQuantity = $item['quantity'];
                $oldUnitPrice = (float)$order[$item_model][$item['store_id']][$item['product_id']]['unit_price'];
                $newUnitPrice = (float)$item['unit_price'];
                if(($oldQuantity + $newQuantity) != 0){
                    $avgUnitPrice = ($oldQuantity * $oldUnitPrice + $newQuantity * $newUnitPrice) / ($oldQuantity + $newQuantity);
                } else {
                    $avgUnitPrice = false;
                }
                $tax1 = $this->isPurchaseOrderTaxInclusive($order[$item_model][$item['store_id']][$item['product_id']]['tax1'], $order[$model]['id']) ? $order[$item_model][$item['store_id']][$item['product_id']]['summary_tax1'] : 0;
                $tax2 = $this->isPurchaseOrderTaxInclusive($order[$item_model][$item['store_id']][$item['product_id']]['tax2'], $order[$model]['id']) ? $order[$item_model][$item['store_id']][$item['product_id']]['summary_tax2'] : 0;

                $item['summary_tax1'] += $tax1;
                $item['summary_tax2'] += $tax2;
                $item['quantity'] += $order[$item_model][$item['store_id']][$item['product_id']]['quantity'];
                $item['count'] += ($order[$item_model][$item['store_id']][$item['product_id']]['count'] ?: 1);
                $item['unit_price'] = $avgUnitPrice;

//                $item['unit_price'] = (($order[$item_model][$item['store_id']][$item['product_id']]['unit_price'] + $item['unit_price']) / $item['count']);
                if ($order[$item_model][$item['store_id']][$item['product_id']]['unit_name'] != $item['unit_name']) {
                    unset($item['unit_name']);
                    unset($item['unit_small_name']);
                    unset($item['unit_factor']);
                    unset($item['unit_factor_id']);
                }
            } else {
                $tax1 = $this->isPurchaseOrderTaxInclusive($item['tax1'], $order[$model]['id']) ? $item['summary_tax1'] : 0;
                $tax2 = $this->isPurchaseOrderTaxInclusive($item['tax2'], $order[$model]['id']) ? $item['summary_tax2'] : 0;
    
                $item['summary_tax1'] = $tax1;
                $item['summary_tax2'] = $tax2;
            }
            if ($is_tracking) {
                $order[$item_model][$item['store_id']][]=$item;
            }else{
                $order[$item_model][$item['store_id']][$item['product_id']] = $item;
            }
            debug($order[$item_model][$item['store_id']][$item['product_id']]);
            unset($item);
        }
        $this->loadModel('RequisitionItem');
        $new_requisitions = [] ; 
        $requisition_statuses = [Requisition::STATUS_ACCEPTED,Requisition::STATUS_MODIFIED] ;
        if ( $get_pending ){
            $requisition_statuses[] = Requisition::STATUS_PENDING ; 
        }
        if ( $get_cancelled)
        {
            $requisition_statuses[] =  Requisition::STATUS_CANCELLED;
        }
        $this->loadModel('Product');
        /**
         * in this loop we compare each product quantity in the order
         * with the product quantity in its requisition items
         * so if the order product qty > requisition items qty then we create a new requisition
         * else if order product qty < requisition items qty then there is an error bcs we can't receive more than  the order qty
         *
         */
        $purchase_order_data = null;
        foreach ( $order[$item_model] as $store_id => $products) {
            foreach ($products as $k => $v) {
                if ($order_type  ==self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL){
                    $this->Product->disableBranchFind();
                }
                if (empty($v['Product'])) {
                    $order[$item_model][$k]['Product'] = $v['Product'] = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $v['product_id']]])['Product'];
                }
                if (empty($v['Product']['track_stock'])) {
                    continue;
                }
                $store_id = ($v['store_id'] ?: $order[$model]['store_id']);
                /**
                 * here we got the total quantity of this pro
                 */
                $conditions = ['Requisition.status' => $requisition_statuses, 'RequisitionItem.product_id' => $v['product_id'], 'Requisition.order_id' => $order[$model]['id'], 'Requisition.order_type' => $order_type];
                if ($order_type !== self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL){
                    $conditions['Requisition.store_id']=$store_id;
                }
                if ($order_type == self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL){
                    $conditions['Requisition.order_type'] = [
                        self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL,
                        self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND
                    ];
                    $requisition_items = $this->RequisitionItem->find('first', ['fields' => 'MIN(Requisition.id)as earliest_requisition_id , SUM(CASE WHEN Requisition.order_type = 18 THEN RequisitionItem.quantity WHEN Requisition.order_type = 16 THEN -RequisitionItem.quantity ELSE 0 END ) as sum_quantity', 'recursive' => 1,
                        'conditions' => $conditions
                    ]);
                }else{
                    $requisition_items = $this->RequisitionItem->find('first', ['fields' => 'MIN(Requisition.id)as earliest_requisition_id , SUM(RequisitionItem.quantity) as sum_quantity', 'recursive' => 1,
                        'conditions' => $conditions
                    ]);
                    // Here we unset the store_id to get the total quantity of this product in all stores (to prevent creating a new requisition if the product quantity in requisitions >= the total quantity in the order)
                    unset($conditions['Requisition.store_id']);
                    $requisition_items_all_stores = $this->RequisitionItem->find('first', ['fields' => 'MIN(Requisition.id)as earliest_requisition_id , SUM(RequisitionItem.quantity) as sum_quantity', 'recursive' => 1,
                        'conditions' => $conditions
                    ]);

                    $this->loadModel($item_model);
                    $product_sum_all_stores = $this->{$item_model}->find('first', [
                        'recursive' => -1,
                        'fields' => ['SUM(quantity) as total_quantity'],
                        'conditions' => [$item_id => $order[$model]['id'], $item_model . '.product_id' => $v['product_id']]
                    ])['0']['total_quantity'] ?? 0;
                }
                // check if order item product qty < requisition product qty
                if (!empty($requisition_items[0]['sum_quantity']) && round($v[$quantity_field], 5) < round($requisition_items[0]['sum_quantity'], 5)) {
                    if ($order_type !== self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL){
                        return false;
                    }
                }

                $requisitionQuantity = round($requisition_items_all_stores[0]['sum_quantity'] ?? 0, 5);
                $productSum = round($product_sum_all_stores ?? 0, 5);
                if ($requisitionQuantity >= $productSum) {
                    if (in_array($order_type, [
                        self::ORDER_TYPE_INVOICE,
                        self::ORDER_TYPE_INVOICE_REFUND,
                        self::ORDER_TYPE_INVOICE_CREDIT_NOTE,
                        self::ORDER_TYPE_PURCHASE_ORDER,
                        self::ORDER_TYPE_PURCHASE_REFUND])) {
                        if ($requisitionQuantity === $productSum) {
                            continue;
                        }
                        return false; // Requisition quantity is greater than product sum so we return false to rollback for the validation "Amount more than order"
                    }
                }
                //check if order product qty > requisition product qty
                if (empty($requisition_items[0]['sum_quantity']) || ($requisition_items[0]['sum_quantity'] !== "0" && round($v[$quantity_field], 5) > round($requisition_items[0]['sum_quantity'], 5))) {
                    $new_quantity = $v[$quantity_field];
                    if (!empty($requisition_items[0]['sum_quantity'])) {
                        $new_quantity = $v[$quantity_field] - $requisition_items[0]['sum_quantity'];
                    }
                    if (empty($new_quantity)) {
                        continue;
                    }
                    $currency = $this->get_default_currency();
                    $dateString = $this->formatDateTime($order[$model]['date']);
                    $dateTime = DateTime::createFromFormat('Y-m-d H:i:s', $dateString);
                    $new_requisitions[$store_id]['Requisition'] = [
                        'store_id' => $store_id,
                        'status' => $created_status,
                        'type' => $requisition_type,
                        'date' => $dateTime !== false ? $dateString : $this->formatDateTime($dateString . ' ' . date('H:i:s', strtotime($order[$model]['created']))),
                        'order_id' => $order[$model]['id'],
                        'order_type' => $order_type,
                        'currency_code' => $currency,
                        'parent_requisition_id' => $requisition_items[0]['earliest_requisition_id'],
                    ];
                    if(in_array($order_type, [RequisitionOrderTypeUtil::ORDER_TYPE_INVOICE, RequisitionOrderTypeUtil::ORDER_TYPE_INVOICE_REFUND, RequisitionOrderTypeUtil::ORDER_TYPE_INVOICE_CREDIT_NOTE])){
                        $new_requisitions[$store_id]['Requisition']['client_id'] = $order['Client']['id'];
                    }
                    if(isset($order[$model]['work_order_id'])){
                        $new_requisitions[$store_id]['Requisition']['work_order_id'] = $order[$model]['work_order_id'];
                    }
                    $calculated_item_taxes = 0;
                    $orderQuantity = $v[$quantity_field];
                    $receivedQuantity = $requisition_items[0]['sum_quantity'];
                    $unreceivedQuantity = $orderQuantity - $receivedQuantity;
                    if (in_array($order_type, [self::ORDER_TYPE_PURCHASE_ORDER, self::ORDER_TYPE_PURCHASE_REFUND])) {
                        if ($purchase_order_data == null) {
                            $purchase_order_data = GetObjectOrLoadModel('PurchaseOrder')->getPurchaseOrder($order[$model]['id']);
                        }
                        $inclusiveTaxForUnreceivedQuantity = \App\Helpers\TaxesHelper::calculatePartialTax($v['summary_tax1'], $orderQuantity, $unreceivedQuantity);
                        $inclusiveTaxForUnreceivedQuantity2 = \App\Helpers\TaxesHelper::calculatePartialTax($v['summary_tax2'], $orderQuantity, $unreceivedQuantity);
                        $tax1 = $this->isPurchaseOrderTaxInclusive($v['tax1'], $v['purchase_order_id']) ? $inclusiveTaxForUnreceivedQuantity : 0;
                        $tax2 = $this->isPurchaseOrderTaxInclusive($v['tax2'], $v['purchase_order_id']) ? $inclusiveTaxForUnreceivedQuantity2 : 0;
	                    $calculated_item_taxes = ($tax1 + $tax2) / $new_quantity;
                        $local_item_discount = 0;
                        if (!empty($v['discount']) && $v['discount_type'] == InvoiceItem::DISCOUNT_AMOUNT) {
                            $local_item_discount = $v['discount'];
                        }
                        else if (!empty($v['discount']) && $v['discount_type'] == InvoiceItem::DISCOUNT_PERCENTAGE) {
                            $local_item_discount = $v['discount'] * $v['unit_price'] / 100;
                        }
                        if ($order_type==self::ORDER_TYPE_PURCHASE_ORDER) {
                          $local_item_discount= $this->calculatePurchaseItemDiscount($original_item_orders,$v);
                        }

                        if($purchase_order_data[$model]['summary_subtotal']){
                            $global_item_discount = ($purchase_order_data[$model]['summary_discount'] / $purchase_order_data[$model]['summary_subtotal']) * ($v['unit_price'] - $calculated_item_taxes - $local_item_discount);
                        }
                    }
                    $currency_rate = $this->getSourceCurrencyRateFromJournal($order, $model,$order_type);
                   
                
                   $requisitionItem  = [
                        'quantity' => $new_quantity,
                        'requested_quantity' => $new_quantity,
                        'unit_price' => $this->getAverageOrUnitPriceForRequisition($v, $currency_rate, $order_type, $calculated_item_taxes, $local_item_discount, $global_item_discount),
                        'description' => $v['description'],
                        'item' => $v['item']??$v['Product']['name'],
                        'product_id' => $v['product_id'],
                        'unit_name' => $v['unit_name'],
                        'unit_small_name' => $v['unit_small_name'],
                        'unit_factor' => $v['unit_factor'],
                        'unit_factor_id' => $v['unit_factor_id'],
                    ];

                    $tracking_type = null;
                    if (isset($v['Product']) && $v['Product']['tracking_type'] != TrackStockUtil::QUANTITY_ONLY) {
                        $tracking_type = $v['Product']['tracking_type'];
                        if (isset($v[$tracking_type])&& $tracking_type != TrackStockUtil::TYPE_LOT_EXPIRY) {
                            $requisitionItem[$tracking_type] = $v[$tracking_type] ?? null;
                        } elseif ($tracking_type == TrackStockUtil::TYPE_LOT_EXPIRY) {
                            $requisitionItem['lot'] = $v['lot'] ?? null;
                            $requisitionItem['expiry_date'] = $v['expiry_date'] ?? null;
                        }
                    }
                    $new_requisitions[$store_id]['RequisitionItem'][]=$requisitionItem;
                }
            }
        }
        /**
         * here we check if there is a removed product from the order
         * that has been received if so we return false as you can't receive
         * anything that is not in the order
         */
        $old_confirmed_requisition_items = $this->RequisitionItem->find('all' ,[  'recursive' => 1 , 
                'conditions' => ['Requisition.status' => $requisition_statuses,  'Requisition.order_id' => $order[$model]['id'] , 'Requisition.order_type' => $order_type] 
                    ]);
        $old_confirmed_requisition_items = $this->handleDeletedSourceItems($old_confirmed_requisition_items, $order[$item_model],$order_type);
//        foreach ($old_confirmed_requisition_items as $i){
//            $found = false ;
//            foreach ($order[$item_model] as $store_id => $storeProducts) {
//                foreach ($storeProducts as $oi) {
//                    if (($store_id == $i['Requisition']['store_id'] || $order_type == self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL  ) && $oi['product_id'] == $i['RequisitionItem']['product_id']) {
//                        $found = true;
//                        break;
//                    }
//                }
//            }
//            if ( !$found ){
//                return false ;
//            }
//        }
        return ($new_requisitions?:true) ;
    }

    public function calculatePurchaseItemDiscount($invoice_items, $item)
    {
        //handle discount in case item duplicated in order
        $this->loadModel('PurchaseOrder');
        if ($item['quantity'] == 0) return false;
        $total_product_discount = 0;
        $productQty = 0;
        foreach($invoice_items as $invoice_item){
            if ($invoice_item['product_id'] == $item['product_id']){
                $productQty += $invoice_item['quantity'];
                $total_product_discount += $this->PurchaseOrder->calculate_item_discount($invoice_item, ((float)$invoice_item['unit_price'] * (float)$invoice_item['quantity']));
            }
        }

        $currency_rate = 1;
        $sourceModelData = $this->source_model_name . 'Data';
        // The Source Might be in Different Currency so we have to make sure that we also multiply the discount with the currency_rate here as well
        if ($this->$sourceModelData[$this->source_model_name]['currency_code'] != getCurrentSite('currency_code')) {
            $currency_rate = CurrencyConverter::index($this->$sourceModelData[$this->source_model_name]['currency_code'], getCurrentSite('currency_code'), date('Y-m-d', strtotime($this->fixReceivedDate($this->$sourceModelData[$this->source_model_name]['date']))));
        }
        $total_product_discount *= $currency_rate;
        $discountPerItem = 0;
        if($productQty) {
            $discountPerItem = $total_product_discount / $productQty;
        }
        return $discountPerItem;
    }

    /**
     * This Function is used to check for inclusive taxes to substract it from the unit_price before creating the requisition
     */
    function isPurchaseOrderTaxInclusive($tax_id, $purchase_order_id){
		$PurchaseOrderTaxModel = GetObjectOrLoadModel('PurchaseOrderTax');
		$tax = $PurchaseOrderTaxModel->find('first', ['conditions' => ['tax_id' => $tax_id, 'PurchaseOrderTax.purchase_order_id' => $purchase_order_id]]);
		return isset($tax['PurchaseOrderTax']['included']) ? $tax['PurchaseOrderTax']['included'] : 0;
	}
    
    /**
     * Deletes the requisition with related data 
     * @param INT $id id of the requisition to be deleted
     * @return boolean 
     */
    function delete_with_related($id, $keepExpenseDistribution = false)
    {
        $this->loadModel('StockTransaction');
        $this->loadModel('RequisitionItem');
        $this->loadModel('ExpenseDistribution');
        $this->applyBranch['onFind'] = false;

        $requisitions = $this->find('all' , ['applyBranchFind' => false, 'recursive' => -1 , 'conditions' => ['Requisition.id' => $id]]);
        $keyed_requisitions = [] ;
        foreach ( $requisitions as $k => $l){
            $keyed_requisitions[$l['Requisition']['id']] = $l;
        }
        $items = $this->RequisitionItem->find('list', ['applyBranchFind' => false, 'recursive' => -1, 'conditions' => ['requisition_id' => $id]]);

        if (!empty($items)) {
            $uf_keys = array_keys($items);
            $this->RequisitionItem->delete($uf_keys);
        }

        //If Requisition has a bundle generated journals delete it
        $this->deleteBundleOutboundStockJournalForRequisition($id);

        $transactions = $this->StockTransaction->find('all' , ['applyBranchFind' => false,'conditions' => ['StockTransaction.order_id' => $id , 'StockTransaction.source_type' => StockTransaction::getSourceRequisition()]]);
        foreach ( $transactions as $t ){
            StockTransaction::removeTransaction($t['StockTransaction']['id']) ;
        }

        if(!$keepExpenseDistribution) {
            $distributions = $this->ExpenseDistribution->find('list', ['conditions' => ['ExpenseDistribution.requisition_id' => $id]]);
            if (!empty($distributions)) {
                $dist_ids = array_keys($distributions);
                foreach ($dist_ids as $dist_id) {
                    if ($this->ExpenseDistribution->delete_with_related($dist_id)) {
                        $this->ExpenseDistribution->delete($dist_id);
                    }
                }
            }
        }

        if (is_array($id)) {
            foreach ($id as $k) {
                if ($this->delete($k)) {
                    $this->add_action(ACTION_REQUISITION_DELETE, $keyed_requisitions[$k]);
                }
            }
        } else {
            if ($this->delete($id)) {
                $this->add_action(ACTION_REQUISITION_DELETE, $requisitions[0]);
            }
        }

        $this->delete_auto_journals($id);
        return TRUE;
    }

    /**
     * Used in validation 
     * @param array $param parameters for the field to be checked
     * @return boolean 
     */
    function isFieldUnique($param) {


        [$field, $value] =[key($param), current($param)];
        $conditions = array();
        if (!empty($this->data[$this->alias]['id'])) {
            $conditions[$this->alias . '.id <>'] = $this->data[$this->alias]['id'];
        }

        $conditions[$this->alias . ".{$field}"] = $value;
        return !($this->hasAny($conditions));
    }
    /**
     * Adds requisition with all related operations
     * @param array $data requisition to be added 
     * @return array requisition data 
     */
    function addRequisition($data) {
        $this->create();
        $this->set($data);
        $status = false;


        if (!empty($data['Requisition']['date'])) {
            $data['Requisition']['date'] = convertDateToDateTime($this->formatDateTime($data['Requisition']['date']));
        }else {
            $data['Requisition']['date'] = $this->formatDateTime(date('Y-m-d H:i:s'));
        }
        if (empty($data['Requisition']['currency_code'])) {
            $data['Requisition']['currency_code'] = getCurrentSite('currency_code');
        }
        if (empty($data['Requisition']['status']))
        {
            $data['Requisition']['status'] = Requisition::STATUS_ACCEPTED;
        }
        $order = $this->getOrder($data['Requisition']['order_id'] , $data['Requisition']['order_type']) ;
        $model = Requisition::$requisition_order_types[$data['Requisition']['order_type']]['source_model'] ;
        $number_field = Requisition::$requisition_order_types[$data['Requisition']['order_type']]['number_field'] ;
        //Calculate totals was here ( same way as Invoice
        $data_temp['InvoiceItem'] = $data['RequisitionItem'];
        $this->loadModel('Invoice');
        $totals = $this->Invoice->calculateTotals($data_temp);
        $data['RequisitionItem'] = $totals['InvoiceItems'];
        if (ifPluginActive(PRODUCT_TRACKING_PLUGIN)) {
            foreach ($data['RequisitionItem'] as &$item) {
                $item['tracking_data'] = json_encode(TrackingItemConverterAbstract::getItemTrackingDataForSave($item));
            }
        }
        // CALM DOWN It's Safe !
//        $data['RequisitionItem'] = $this->spreadSerialItems($data['RequisitionItem'], $data['Requisition']);
        $site = getAuthOwner();
        if (!$data['Requisition']['staff_id']) {
            $data['Requisition']['staff_id'] = $site['staff_id'];
        }
        App::import('Vendor', 'AutoNumber');
        $autonumber_type =\AutoNumber::mapRequisitionTypeToAutoNumberType($data['Requisition']['type']);
        \AutoNumber::set_validate($autonumber_type);
        if(empty($data[$this->alias]['number'])||((empty($data[$this->alias]['id'])&&$data[$this->alias]['number']==$data[$this->alias]['hidden_number']&& !isset($data['Requisition']['is_stock_request'])) )){
            $data[$this->alias]['hidden_number'] = $data[$this->alias]['number'] = \AutoNumber::get_auto_serial($autonumber_type);
            $generated_number = true;
        }
        if (!isset($data['Requisition']['is_stock_request']) && !isset($data['Requisition']['is_manufacturing_order'])){
            $data['Requisition']['order_number'] = ($order[$model][$number_field]?:$data['Requisition']['number'] );
        }
        if(isset($order[$model]['branch_id'])){
            $data[$this->alias]['branch_id']=$order[$model]['branch_id'];
        } else {
            $data[$this->alias]['branch_id'] = getCurrentBranchID();
        }
        /**
         * this code section is a work around for the bug of duplicate autonumber
         * as per my conversation with eng <NAME_EMAIL>
         */
        $this->set($data);
        if(!$this->validates(['fieldList' => ['number']])){
            $loopCounter = 0;
            while(!$this->validates(['fieldList' => ['number']]) && $loopCounter < 1000) {
                \AutoNumber::update_auto_serial($autonumber_type);
                $data['Requisition']['number'] = \AutoNumber::get_auto_serial($autonumber_type);
                $this->set($data);
                $loopCounter++;
            }
            if($loopCounter == 1000) {
                $data['Requisition']['number'] = date('m/d/Y h:i:s a', time());
            }
        }
        /**
         *
         */
        if ($this->saveAll($data, array('validate' => 'first')) == 1) {
            if(!empty($generated_number)) \AutoNumber::update_auto_serial($autonumber_type);
            elseif($data[$this->alias]['number']!=$data[$this->alias]['hidden_number']) \AutoNumber::update_last_from_number($data[$this->alias]['number'],$autonumber_type);
            if (check_permission(Edit_General_Settings)) {
                App::import('Vendor', 'settings');
                settings::setValue(InvoicesPlugin, 'initial_invoice_custom_fields', json_encode($data['InvoiceCustomField']));
            }
            if (empty($data['Requisition']['id'])) {
                $data['Requisition']['id'] = $this->getLastInsertID();
            }
            $status = true;
        }
        if (ifPluginActive(InventoryPlugin)) {
            $this->loadModel('StockTransaction');
            $data = $this->getRequisition($data['Requisition']['id']); // To get the product and units 
            StockTransaction::updateForRequisition($data);
        }
        App::import('Vendor', 'notification_2');
        $this->add_action ( ACTION_REQUISITION_ADD , $data);
        $this->notifyRequisitionHolders($data, NotificationV2::NOTI_ADD_REQUISITION,NotificationV2::NOTI_ACTION_ADD);
        $this->update_journals($data , false );
        return compact('status', 'data', 'templateStatus', 'message');
    }

    public function spreadSerialItems($items, $requisition)
    {
        $new_items = [];
        $productModel = GetObjectOrLoadModel('Product');
        foreach ($items as $item) {
            $productTrackingType = $productModel->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $item['product_id']]])['Product']['tracking_type'];
            if ($productTrackingType === TrackStockUtil::TYPE_SERIAL && $item['quantity'] > 1 && $this->shouldSpreadToItems($requisition['order_type'])) {
                $serial_item = $item;
                for ($i = 0; $i < $item['quantity']; $i++) {
                    $serial_item['quantity'] = 1;
                    $serial_item['requested_quantity'] = 1;
                    $new_items[] = $serial_item;
                }
            } else if(
                in_array($productTrackingType, [TrackStockUtil::TYPE_EXPIRY, TrackStockUtil::TYPE_LOT, TrackStockUtil::TYPE_LOT_EXPIRY]) &&
                $this->shouldSpreadToItems($requisition['order_type']) &&
                $requisition['status'] == Requisition::STATUS_PENDING
            ){
                $InvoiceM = GetObjectOrLoadModel('Invoice');
                $RequisitionM = GetObjectOrLoadModel('Requisition');
                $invoice = $InvoiceM->getInvoice($requisition['order_id']);
                if (empty($invoice)) {
                    $new_items[] = $item;
                    continue;
                }
                foreach ($invoice['InvoiceItem'] as $invoiceItem) {
                    if($invoiceItem['product_id'] == $item['product_id']) {
                        $item['quantity'] = $invoiceItem['quantity'];
                        $item['requested_quantity'] = $invoiceItem['quantity'];
                        $new_items[] = $item;
                    }
                }
            } else {
                $new_items[] = $item;
            }
        }
        return $new_items;
    }

    public function shouldSpreadToItems($type){
        return $type == Requisition::ORDER_TYPE_INVOICE;
    }
    /**
     * 
     * @param array|mixed $requisition
     * @param INT $notification
     * @param INT $notification_type
     */
    function notifyRequisitionHolders($requisition,$notification,$notification_type){
        App::import('Vendor', 'notification_2');
        $this->loadModel('ItemPermission');
        $trigger = NotificationV2::get_trigger();
        $assignedStaff = $this->ItemPermission->getAuthenticatedStaff(ItemPermission::ITEM_TYPE_STORE,$requisition['Requisition']['store_id'], ItemPermission::PERMISSION_STOCK_UPDATING);
        foreach ( $assignedStaff as $k => $as){
            if ( !check_permission(REQUISITION_MODIFY , $as)){
                unset($assignedStaff[$k]);
            }
        }
        NotificationV2::add_notification(
				$notification,
				$requisition['Requisition']['id'],//Reference/item id
				$assignedStaff, //staff to be notified ( can be array ) 
				NotificationV2::NOTI_USER_TYPE_STAFF,
				$trigger['trigger_type'],
				$trigger['trigger_id'],
				$notification_type,
				[
                                    'requisition_number' => '#'.$requisition['Requisition']['number'],
                                    'order' =>__(Requisition::$requisition_order_types[$requisition['Requisition']['order_type']]['label'],true).' #'.$requisition['Requisition']['order_id'] ,
                                    'status' => __(Requisition::$requisition_statuses[$requisition['Requisition']['status']]['label'],true)  
                                ]);
    }
    /**
     * Gets requisition with all related data . 
     * @param INT $id
     * @param array $otherConditions
     * @param boolean $replace_units
     * @return boolean|array false on failure , requisition data on success 
     */
    function getRequisition($id = null, $otherConditions = array(), $replace_units = false, $applyBranch = true) {
        $this->loadModel('Product');

        if (empty($id) && empty($otherConditions)) {
            return null;
        }
        $conditions = $otherConditions;
        if ($id !== null) {
            $conditions['Requisition.id'] = $id;
        }
        if (PHP_SAPI != 'cli') {
            $site_id = getAuthOwner('id');
            if (!$site_id) {
                $site_id = getCurrentSite('id');
            }
        }
        $requisition = $this->find('first', ['applyBranchFind' => $applyBranch, 'conditions' => $conditions, 'recursive' => 1]);

        if (!$requisition) {
            return null;
        }

        $Product = ClassRegistry::init('Product');
        $Product->recursive = -1;
        $productIds = array_map(function($item) {
            return $item['product_id'];
        },$requisition['RequisitionItem']);
        $Product->applyBranch['onFind'] = false;
        $products = $Product->find('all', ['conditions' => ['Product.id' => $productIds]]);
        $Product->applyBranch['onFind'] = true;
        //map products by id
        $products = array_combine(array_map(function($item) {
            return $item['Product']['id'];
        }, $products), $products);
        if (!$requisition) {
            return false;
        }
        $enable_multi_units = settings::getValue(InventoryPlugin, 'enable_multi_units');
        if (ifPluginActive(InventoryPlugin)) {
            foreach ($requisition['RequisitionItem'] as $i => $item) {
                if ($replace_units && $enable_multi_units && !empty($item['unit_factor']) && !empty($item['unit_name'])) {
                    debug('prodigy');
                    $requisition['RequisitionItem'][$i]['quantity_written'] = ($item['quantity'] / $item['unit_factor']) . ' <span class="unit_factor_name" >' . $item['unit_small_name'] . '</span>';
                } else {
                    $requisition['RequisitionItem'][$i]['quantity_written'] = format_number($item['quantity']);
                }

                if (!empty($item['product_id'])) {
                    if(isset($products[$item['product_id']])) {
                        $pp[0] = $products[$item['product_id']]['Product'];
                    } else {
                        $prod = $Product->read(null, $item['product_id']);
                        $pp[0] = $prod['Product'];
                    }
                    if ($enable_multi_units) {
                        $this->Product->add_multi_units($pp, true);
                    }
                    $requisition['RequisitionItem'][$i]['Product'] = $pp[0];
                }
            }
        }

        $this->loadModel('ExpenseDistribution');
        $expenseDistribution = $this->ExpenseDistribution->find('first', ['conditions' => ['ExpenseDistribution.requisition_id' => $id], 'recursive' => 1]);
        if ($expenseDistribution)
            $requisition = array_merge($requisition, $expenseDistribution);

        return $requisition;
    }
    /**
     * Updates requisition with all related operations
     * @param array $data requisition to be updated
     * @return array returns array of requisition
     */
    function updateRequisition($data) {
        $requisition = $this->getRequisition($data['Requisition']['id']);
        if (!$requisition) {
            return array('status' => false);
        }
        if (empty($data['Requisition']['currency_code'])) {
            $data['Requisition']['currency_code'] = getCurrentSite('currency_code');
        }
        $data_temp['InvoiceItem'] = $data['RequisitionItem'];
        $this->loadModel('Invoice');
        $totals = $this->Invoice->calculateTotals($data_temp);
        $data['RequisitionItem'] = $totals['InvoiceItems'];
        $data['InvoiceTax'] = $totals['taxes'];
        $this->set($data);
        $dateParts = explode(' ',$data['Requisition']['date']);
        if(count($dateParts) < 2) {
            $data['Requisition']['date'] = $this->formatDateTime($data['Requisition']['date'] . ' ' . date('H:i:s', strtotime($requisition['Requisition']['created'])));
        } else {
            $data['Requisition']['date'] = $this->formatDateTime($data['Requisition']['date']);
        }
        if (ifPluginActive(PRODUCT_TRACKING_PLUGIN)) {
            foreach ($data['RequisitionItem'] as &$item) {
                if (empty($item['tracking_data'])) {
                    $item['tracking_data'] = json_encode(TrackingItemConverterAbstract::getItemTrackingDataForSave($item));
                }
            }
        }
//	    $data['RequisitionItem'] = $this->spreadSerialItems($data['RequisitionItem'], $data['Requisition']);
	    $hasErrors = false;
        foreach (array('RequisitionItem') as $subModel) {
            $errors = array();
            if (!empty($data[$subModel])) {
                foreach ($data[$subModel] as $key => $subItem) {
                    $this->{$subModel}->set(array($subModel => $subItem));

                    if (!$this->{$subModel}->validates()) {
                        $errors[$key] = $this->{$subModel}->validationErrors;
                        $hasErrors = true;
                    }
                }
                $this->{$subModel}->validationErrors = $errors;
            }
        }

        if (!$this->validates() || $hasErrors) {
            return array('status' => false);
        }

        if ($this->save($data, array('validate' => false))) {
            foreach (array('RequisitionItem') as $subModel) {
                $ids = array();
                $counter = 1;
                debug($subModel);
                if (!empty($data[$subModel])) {
                    foreach ($data[$subModel] as $key => &$subItem) {

                        $subItem['requisition_id'] = $data['Requisition']['id'];
                        $subItem['display_order'] = $counter;
                        if (empty($subItem['id'])) {
                            $this->{$subModel}->create();
                        }
                        if ($this->{$subModel}->save(array($subModel => $subItem), array('validate' => false))) {
                            if (!empty($subItem['id'])) {
                                $ids[] = $subItem['id'];
                            } else {
                                $ids[] = $this->{$subModel}->getLastInsertID();
                            }
                        }
                        ++$counter;
                    }
                    if (!empty($ids)) {
                        $this->{$subModel}->deleteAll(array("$subModel.requisition_id" => $data['Requisition']['id'], 'not' => array("$subModel.id" => $ids)));
                    }
                } else {
                    $this->{$subModel}->deleteAll(array("$subModel.requisition_id" => $data['Requisition']['id']));
                }
            }
            if (ifPluginActive(InventoryPlugin)) {
                $this->loadModel('StockTransaction');
                $data = $this->getRequisition($data['Requisition']['id']); // To get the product and units 
                StockTransaction::updateForRequisition($data);
            }
            App::import('Vendor', 'notification_2');
            $this->notifyRequisitionHolders($data, NotificationV2::NOTI_UPDATE_REQUISITION,NotificationV2::NOTI_ACTION_UPDATE);
            $this->update_journals($data , false );
            $this->data = $data;
           
            $action =ACTION_REQUISITION_UPDATE;
            if($data['Requisition']['store_id'] != $requisition['Requisition']['store_id']){
                $action =ACTION_REQUISITION_CHANGE_STORE;
                $this->loadModel('Store');
                $newStore = $this->Store->find('first' , ['conditions' => ['Store.id' => $data['Requisition']['store_id']] ]);
                $oldStore = $this->Store->find('first' , ['conditions' => ['Store.id' => $requisition['Requisition']['store_id']] ]);

                $data['Requisition']['old_store'] = $oldStore['Store']['name'];
                $data['Requisition']['new_store'] = $newStore['Store']['name'];
            }
           
            $this->add_action ($action, $data);
            return array('status' => true, 'data' => $data);
        } else {

            return array('status' => false);
        }
    }
    /**
     * Updates the cost journals for requisitions
     * @param array $stock_transaction stock transaction array without StockTransaction key 
     * @param double $new_avg the new average to be updated
     */
    public function updateCostJournals($stock_transaction , $new_avg) {
	    $this->loadModel('RequisitionItem');
	    $this->RequisitionItem->id = $stock_transaction['ref_id'] ;
        $this->RequisitionItem->saveField('unit_price' ,$new_avg );
        $this->RequisitionItem->saveField('subtotal' ,$new_avg * abs($stock_transaction['quantity'] ));
        $requisition = $this->find('first' , ['conditions' => ['Requisition.id' => $stock_transaction['order_id']]]);
        $this->update_journals($requisition , NULL);
//        $this->updateManufactureResultRequisition($requisition);
    }
    function getPOJournalAccountId($purchase_order_id){
        $this->loadModel('JournalTransaction');
        return $this->JournalTransaction->find('first' , ['recursive' => 1, 'conditions' => ['Journal.entity_type' => 'purchase_order' , 'Journal.entity_id' => $purchase_order_id ,'JournalTransaction.subkey' => 'purchases' ] ])['JournalTransaction']['journal_account_id'];
    }

    function getPORefundJournalAccountId($purchase_order_id){
        $this->loadModel('JournalTransaction');
        return $this->JournalTransaction->find('first' , ['recursive' => 1, 'conditions' => ['Journal.entity_type' => 'purchase_refund' , 'Journal.entity_id' => $purchase_order_id ,'JournalTransaction.subkey' => 'purchases' ] ])['JournalTransaction']['journal_account_id'];
    }

    function getPODebitJournalAccountId($purchase_order_id){
        $this->loadModel('JournalTransaction');
        return $this->JournalTransaction->find('first' , ['recursive' => 1, 'conditions' => ['Journal.entity_type' => 'purchase_debit_note' , 'Journal.entity_id' => $purchase_order_id ,'JournalTransaction.subkey' => 'purchases' ] ])['JournalTransaction']['journal_account_id'];
    }

    /**
     * function for journal behavior 
     * @param array $data requisition data 
     * @return boolean|array false on failure , array of joural/journal transactions otherwise 
     */
    public function get_journals($data)
    {
        if (empty($data[$this->alias]['id']))
            $data[$this->alias]['id'] = $this->id;

        $this->loadModel('RequisitionItem');
        $orderItem = $this->getOrderItem($data['Requisition']['order_type'], $data['Requisition']['order_id']);
        $d = $this->RequisitionItem->find('first', [
            'fields' => 'sum(RequisitionItem.quantity*RequisitionItem.unit_price) as sum_all ,  sum(RequisitionItem.quantity)as sum_q ',
            'conditions' => ['RequisitionItem.requisition_id' => $data[$this->alias]['id']]
        ]);
       
        $quantity = $d[0]['sum_q'] * ($data['Requisition']['type'] == Requisition::TYPE_OUTBOUND ? -1 : 1);
        if (empty($quantity)) {
            return false;
        }

        if (!empty($data[$this->alias]['id']) && !isset($data[$this->alias]['source_type']))
            $data = $this->findById($data[$this->alias]['id']);


        if ($data['Requisition']['type'] == Requisition::TYPE_INBOUND) {
            $credit = 'credit';
            $debit = 'debit';
            $debit_count = 0;
            $credit_count = 1;
            $journal_account = 'other_credit';
        } else {
            //TRANSFER and OUTBOUND
            $credit = 'debit';
            $debit = 'credit';
            $debit_count = 1;
            $credit_count = 0;
            $journal_account = 'other_debit';
        }

        if ($orderItem) {
            $orderItemTypeData = self::$requisition_order_types[$data['Requisition']['order_type']];
            $orderItemNumber = $orderItem[$orderItemTypeData['source_model']][$orderItemTypeData['number_field']];
            $description = sprintf('%s #%s', __(self::$requisition_types[$data['Requisition']['type']]['label'], true), $data['Requisition']['number']) . ' - ' . sprintf('%s #%s', __($orderItemTypeData['label'], true), $orderItemNumber);
        } else {
            $description = __(self::$requisition_types[$data['Requisition']['type']]['label'], true) . ' - ' . __(self::$requisition_order_types[$data['Requisition']['order_type']]['label'], true) . ' #' . $data['Requisition']['number'];
        }

        if (empty($data[$this->alias]['store_id'])) {
            $this->loadModel('Store');
            $data[$this->alias]['store_id'] = $this->Store->getPrimaryStore();
        }

        if (empty($data[$this->alias]['currency_code'])) {
            $data[$this->alias]['currency_code'] = $this->get_default_currency();
        }

	    $currency_code = $data[$this->alias]['currency_code'];
	    $amount = abs($d[0]['sum_all']);
        debug($amount);
        $journal['Journal']['date'] = $data[$this->alias]['date'];
        $journal['Journal']['entity_type'] = 'requisition';
        $journal['Journal']['description'] = $description . "\r\n" . $data[$this->alias]['notes'];
        $journal['Journal']['entity_id'] = $data[$this->alias]['id'];
        $journal['Journal']['currency_code'] = $currency_code;
        $journal['Journal']['branch_id'] = $data[$this->alias]['branch_id'];
        if (!in_array($data['Requisition']['status'], [Requisition::STATUS_ACCEPTED, Requisition::STATUS_MODIFIED])) {
            return $journal;
        }
        
        $this->loadModel('ExpenseDistribution');
        $expense_distribution = $this->ExpenseDistribution->find('first', ['conditions' => ['ExpenseDistribution.requisition_id' => $data['Requisition']['id']], 'recursive' => 1]);
        //Delete The Old Journal in Case:
        if (empty ($expense_distribution) && (empty($amount) || $amount == "0"))
        return $journal;
    
        $journal['JournalTransaction'][$debit_count] = array(
            'subkey' => 'store',
            'currency_' . $debit => $amount,
            'currency_code' => $currency_code,
            'description' => $description,
            'auto_account' => array('type' => 'dynamic', 'entity_type' => 'store', 'entity_id' => $data[$this->alias]['store_id'])
        );

        if (!empty($data[$this->alias]['to_store_id'])) {
            $journal['JournalTransaction'][$credit_count] = array(
                'subkey' => 'to_store',
                'currency_' . $credit => $amount,
                'currency_code' => $currency_code,
                'description' => $description,
                'auto_account' => array('type' => 'dynamic', 'entity_type' => 'store', 'entity_id' => $data[$this->alias]['to_store_id'])
            );
        } else {
            $journal['JournalTransaction'][$credit_count] = array(
                'subkey' => 'account',
                'currency_' . $credit => $amount,
                'currency_code' => $currency_code,
                'description' => $description,
            );
            debug($journal);
            $settings_default_account_id = settings::getValue(InvoicesPlugin, 'requisition_journal_' . $data[$this->alias]['order_type'],null,false);

            if (!empty(Requisition::$requisition_order_types[$data[$this->alias]['order_type']]['getJournalsCallback']) && empty($settings_default_account_id)) {
                $default_account_id = $this->{Requisition::$requisition_order_types[$data[$this->alias]['order_type']]['getJournalsCallback']}($data[$this->alias]['order_id']);
            } else {
                $default_account_id = $settings_default_account_id;
            }
         
            if (!empty($data[$this->alias]['journal_account_id'])) {
                $journal['JournalTransaction'][$credit_count]['journal_account_id'] = $data[$this->alias]['journal_account_id'];
            } else if (!empty($default_account_id)) {
                $journal['JournalTransaction'][$credit_count]['journal_account_id'] = $default_account_id;
                $this->saveOrderJournalAccount($data, $default_account_id);
            } else {
                $this->loadModel('JournalAccount');
                $this->loadModel('Product');
                $product_sales_routing = settings::getValue(AccountingPlugin,'product_sales_accounts_routing');
               
                foreach ($data['RequisitionItem'] as $key => $item) {

                    $account_id = $this->Product->getSalesCostAccount($item['product_id']);
                    $journalAccount = $this->JournalAccount->find('first', ['conditions' => ['JournalAccount.id' => $account_id], 'applyBranchFind' => false]);
                   
                    
                    if(!empty($account_id) && !empty($journalAccount) && (!empty($product_sales_routing) && $product_sales_routing == settings::MANUAL_ACCOUNTS_ROUTING)){
                        $subkey = 'account_' . $item['product_id'] . "_$account_id";
                        $journal['JournalTransaction'][] = array(
                            'subkey' => $subkey,
                            'currency_' . $credit =>  $item['quantity'] * $item['unit_price'],
                            'currency_code' => $currency_code,
                            'description' => $description,
                            'journal_account_id' => $account_id
                        );
                        $journal['JournalTransaction'][$credit_count][ 'currency_' . $credit ] -=  $item['quantity'] * $item['unit_price'];
                    }else{
                        $journal_account_entity = Requisition::$requisition_order_types[$data[$this->alias]['order_type']]['journal_account'];
                        $journal['JournalTransaction'][$credit_count]['auto_account'] = array('type' => 'fixed', 'entity_type' => $journal_account_entity, 'entity_id' => 0);
                        $this->saveOrderJournalAccount($data, null, $journal_account_entity);
                    }   
                }
            }
        }
        if ($journal['JournalTransaction'][$credit_count][ 'currency_' . $credit ] <= 0.00001){
            $journal['JournalTransaction'][$credit_count][ 'currency_' . $credit ] = 0;
        }
        debug($journal);
        if ($expense_distribution) {
//            $expenseRate = 1;
//            if($expense_distribution['ExpenseDistributionItem'][0]['expense_currency'] != $currency_code) {
//                $expenseRate = CurrencyConverter::index($expense_distribution['ExpenseDistributionItem'][0]['expense_currency'], $currency_code, $data[$this->alias]['date']);
//            }
            $this->loadModel('Expense');
            $this->loadModel('JournalTransaction');
            $expense_ids = array_column($expense_distribution['ExpenseDistributionItem'], 'expense_id');
            $transactionIds = array_column($expense_distribution['ExpenseDistributionItem'], 'transaction_id');
            $transactions = $this->JournalTransaction->find('all', ['conditions' => ['JournalTransaction.id' => $transactionIds]]);
            $transactionsMappedByAccountId = [];
            foreach ($transactions as $transaction) {
                if(!isset($transactionsMappedByAccountId[$transaction['JournalTransaction']['journal_account_id']])) {
                    $transactionsMappedByAccountId[$transaction['JournalTransaction']['journal_account_id']] = [];
                }
                $transactionsMappedByAccountId[$transaction['JournalTransaction']['journal_account_id']][] = $transaction;
            }
            $expenses_matrix = $this->Expense->getExpensesJournalTransactionMatrix($expense_ids);

            $distribution_items = [];
            $transactionDistributionItems = [];
            foreach ($expense_distribution['ExpenseDistributionItem'] as $dist_item) {
                if($dist_item['type'] === \Izam\Daftra\Common\Utils\ExpenseDistributionItemTypeUtil::EXPENSE) {
                    $distribution_items[$dist_item['expense_id']] = $dist_item;
                } else {
                    $transactionDistributionItems[$dist_item['transaction_id']] = $dist_item;
                }
            }
            $count = 1;
            $total_transaction_amount = 0;
            foreach ($expenses_matrix as $journal_account_id => $transaction_arr) {
                $transaction_amount = 0;
                foreach ($transaction_arr as $expense_id => $percentage) {
                    $transaction_amount += $percentage * $distribution_items[$expense_id]['expense_amount'];
                }

                $journal['JournalTransaction'][$credit_count + $count] = [
                    'subkey' => 'account_' . $journal_account_id,
                    'currency_' . $credit => $transaction_amount,
                    'currency_code' => $currency_code,
                    'description' => $description . ' - Expense Distribution',
                    'journal_account_id' => $journal_account_id
                ];

                $total_transaction_amount += $transaction_amount;
                $count++;
            }

            /**
             * handle expense distribution of journal transaction
             */
            foreach ($transactionsMappedByAccountId as $accountId => $accountTransactions) {
                $transaction_amount = 0;
                foreach ($accountTransactions as $transaction) {
                    $transaction_amount += $transactionDistributionItems[$transaction['JournalTransaction']['id']]['expense_amount'];
                }
                $journal['JournalTransaction'][$credit_count + $count] = array(
                    'subkey' => 'account_expense_distribution_journal' . $accountId,
                    'currency_' . $credit => $transaction_amount,
                    'currency_code' => $currency_code,
                    'description' => $description . ' - Expense Distribution',
                    'journal_account_id' => $accountId
                );

                $total_transaction_amount += $transaction_amount;
                $count++;
            }

            $journal['JournalTransaction'][$debit_count]['currency_' . $debit] += $total_transaction_amount;
        }
        return $journal;
    }

    /**
     * Saves the order journal account to the requisition created 
     * @param mixed $requisition
     * @param INT $journal_account_id
     * @param String $journal_entity_type
     */
    function saveOrderJournalAccount($requisition,$journal_account_id = null , $journal_entity_type = null ){
        if ( empty($journal_account_id)&&empty($journal_entity_type))
        {
            return false;
        }else if ( !empty($journal_account_id))
        {
            $this->id = $requisition['Requisition']['id'];
            $this->saveField( 'journal_account_id' ,$journal_account_id);
        }else if ( !empty($journal_entity_type) )
        {
            $this->loadModel('Journal');
            $account = $this->Journal->get_auto_account(['entity_type'=>$journal_entity_type,'entity_id' => 0]);
            $this->id = $requisition['Requisition']['id'];
            $this->saveField( 'journal_account_id' ,$account['JournalAccount']['id']);
        }
    }
    /**
     * returns requisition types list
     * @return array containing type list translated 
     */
    function getTypeList(){
        $return = [ ] ; 
        foreach (Requisition::$requisition_types as $k => $v )
        {
            $return[$k] = __($v['label'] , true );
        }
        return $return ; 
    }
    /**
     * returns requisition order types list
     * @return array containing order type list translated 
     */
    function getOrderTypeList(){
        $return = [ ] ; 
        foreach (Requisition::$requisition_order_types as $k => $v )
        {
            $return[$k] = __($v['label'] , true );
        }
        return $return ; 
    }
    /**
     * returns requisition status list
     * @return array containing status list translated 
     */
    function getStatusList(){
        $return = [ ] ; 
        foreach (Requisition::$requisition_statuses as $k => $v )
        {
            $return[$k] = __($v['label'] , true );
        }
        return $return ; 
    }
    /**
     * gets filter array to be used in controller 
     * @return array filter array 
     */
    function getFilters() {
        $this->loadModel('ItemPermission');
        $this->loadModel('Staff');

        $order_types = $this->getOrderTypeList () ;
        $statuses = $this->getStatusList () ; 
        $staffs = $this->Staff->getList();
        $stores_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE , ItemPermission::PERMISSION_VIEW,null,false,true);
		if (ifPluginActive(BranchesPlugin)) {
			$this->loadModel('Branch');
			$branches = $this->Branch->getList();
			$filters['branch_id'] = array('div_class' => 'full-width','options' => ['options' => $branches ], 'more-options' => false, 'empty' => __('All', true), 'label' => __('Branch', true));
		}
        $filters['number'] = array('type' => 'like','div_class' => 'full-width', 'more-options' => false, 'label' => __('Requisition Number', true));
        $filters['order_type'] = array('div_class' => 'full-width','options' => ['options' => $order_types ], 'more-options' => false, 'empty' => __('All', true), 'label' => __('Requisition Source', true));
        $filters['order_number'] = array('div_class' => 'full-width', 'more-options' => false, 'label' => __('Reference No', true));
        $filters['store_id'] = array('div_class' => 'full-width','options' => ['options' => $stores_list ], 'more-options' => false, 'empty' => __('All', true), 'label' => __('Warehouse', true));
        
        $filters['order_id'] = array('type' => 'hidden');
        $filters['work_order_id'] = array('type' => 'hidden');
        
        $filters['status'] = array('more-options'=>true, 'div_class' => 'full-width','options' => ['options' => $statuses ],  'empty' => __('All', true), 'label' => __('Status', true));
        $filters['staff_id'] =  array('input_type' => 'multiselect', 'more-options'=>true,'div_class' => 'full-width', 'options' => array('options' => $staffs  ),'empty' => __('Any Staff', true),'label' => __("Added By",true));
        $filters['RequisitionItem.product_id'] = ['more-options'=>true,'div_class' => 'full-width', 'input_type' => 'advanced', 'label' =>  __('Product',true),'advanced_controller' =>'products','advanced_model' =>'Product' , 'advanced_action' => 'json_find'];
        $filters['date'] = array('more-options' => true,'label' =>  __('Date',true), 'type' => 'date_range', 'to_label' => __('Date To', true), 'from_label' => __('Date From', true));
        $filters['client_id'] = array('div_class' => 'full-width', 'input_type' => 'advanced_client', 'more-options' => false, 'empty' => __('Any Client', true), 'label' => __('Client', true));
        $filters['supplier_id'] = array('div_class' => 'full-width', 'input_type' => 'advanced_supplier', 'more-options' => false, 'empty' => __('Any Supplier', true), 'label' => __('Supplier', true));

        return $filters;
    }
    /**
     * Saves new requisitions / updates old pending requisitions
     * @param INT|array $pos_shift_id order_id or order data
     * @param boolean $get_pending adds pending to the statuses list to search for when comparing the order with existing requisitions
     * @return boolean|array false on (requisitions have more than order ) , true on (requisitions same as order data ) , new requisitions as a difference between order and existing requisitions
     */
    function updateForPOS($pos_shift_id, $get_pending= false) {
	    // This is for customers that re-open their sessions and save/delete more than 1000+ Invoices at the same time
    	set_time_limit(1800);
    	ini_set('memory_limit', '2G');
        if (ifPluginActive(PosPlugin)){
            $this->loadModel('PosShift');
            if ($this->PosShift->checkPosShiftIdIsCalculatedPerInvoice($pos_shift_id)) {
                $deleted_conditions = ['Requisition.order_id' =>$pos_shift_id , 'Requisition.order_type' => [self::ORDER_TYPE_POS_OUTBOUND,self::ORDER_TYPE_POS_INBOUND] ] ;
                $deleted_requisitions = $this->find('all' , ['recursive' => -1 , 'conditions' => $deleted_conditions ] ) ;
                foreach ( $deleted_requisitions as $r  ){
                    $this->delete_with_related($r['Requisition']['id']) ;
                }
                return false;
            }
        }
        $this->loadModel('Invoice');
        $this->loadModel('StockTransaction');
        $invoices = $this->Invoice->find('all', ['conditions' => ['Invoice.pos_shift_id' => $pos_shift_id]]);
        foreach ($invoices as $invoice) {
            $stockTransactions = $this->StockTransaction->find('all', ['conditions' => [
                'StockTransaction.order_id' => $invoice['Invoice']['id'],
                'StockTransaction.source_type' => [StockTransaction::SOURCE_INVOICE, StockTransaction::SOURCE_RR, StockTransaction::SOURCE_RR]
            ]]);
            foreach ($stockTransactions as $transaction) {
                StockTransaction::removeTransaction($transaction['StockTransaction']);
            }
        }
        $new_sales_requisitions = $this->compare_pos_requisitions($pos_shift_id, self::ORDER_TYPE_POS_OUTBOUND, $get_pending) ;
        $new_refund_requisitions = $this->compare_pos_requisitions($pos_shift_id, self::ORDER_TYPE_POS_INBOUND, $get_pending) ;
        if ($new_sales_requisitions === [] && $new_refund_requisitions === []) {
            $deleted_conditions = ['Requisition.order_id' =>$pos_shift_id , 'Requisition.order_type' => [self::ORDER_TYPE_POS_OUTBOUND,self::ORDER_TYPE_POS_INBOUND] ] ;
            $deleted_requisitions = $this->find('all' , ['recursive' => -1 , 'conditions' => $deleted_conditions ] ) ;
            foreach ( $deleted_requisitions as $r  ){
                $this->delete_with_related($r['Requisition']['id']) ;
            }
            return [];
        }
        if ( empty($new_sales_requisitions) && empty($new_refund_requisitions) ){
            return false ;
        }
        $ids = [] ;
        foreach ($new_sales_requisitions as $r){
            $existing = $this->find('first' , ['recursive' => 1 , 'conditions' => ['Requisition.status' => Requisition::STATUS_ACCEPTED ,    'Requisition.order_id' => $pos_shift_id , 'Requisition.order_type' => self::ORDER_TYPE_POS_OUTBOUND] ]);
            if ( $existing ){
                $r['Requisition']['id'] = $existing['Requisition']['id'] ;
                unset($r['Requisition']['number']) ;
                $saved_requisition = $this->updateRequisition($r);
            }else {
                $saved_requisition = $this->addRequisition($r);
            }
            $ids[]  = $saved_requisition['data']['Requisition']['id'] ;
        }
        $deleted_conditions = ['Requisition.status' => Requisition::STATUS_ACCEPTED, 'Requisition.order_id' =>$pos_shift_id , 'Requisition.order_type' => self::ORDER_TYPE_POS_OUTBOUND ] ;
        if (! empty($ids) && count($ids) > 1)
        {
            $deleted_conditions['Requisition.id not '] = $ids;
        }else if (count($ids) == 1)
        {
            $deleted_conditions['Requisition.id <>'] = $ids[0];
        }
        $deleted_requisitions = $this->find('all' , ['recursive' => -1 , 'conditions' => $deleted_conditions ] ) ;
        foreach ( $deleted_requisitions as $r  ){
            $this->delete_with_related($r['Requisition']['id']) ;
        }
        $ids = [] ;
        foreach ($new_refund_requisitions as $r){
            $existing = $this->find('first' , ['recursive' => 1 , 'conditions' => ['Requisition.status' => Requisition::STATUS_ACCEPTED ,    'Requisition.order_id' => $pos_shift_id , 'Requisition.order_type' => self::ORDER_TYPE_POS_INBOUND] ]);
            if ( $existing ){
                $r['Requisition']['id'] = $existing['Requisition']['id'] ;
                unset($r['Requisition']['number']) ;
                $saved_requisition = $this->updateRequisition($r);
            }else {
                $saved_requisition = $this->addRequisition($r);
            }
            $ids[]  = $saved_requisition['data']['Requisition']['id'] ;
        }
        $deleted_conditions = ['Requisition.status' => Requisition::STATUS_ACCEPTED, 'Requisition.order_id' =>$pos_shift_id , 'Requisition.order_type' => self::ORDER_TYPE_POS_INBOUND ] ;
        if (! empty($ids) && count($ids) > 1)
        {
            $deleted_conditions['Requisition.id not '] = $ids;
        }else if (count($ids) == 1)
        {
            $deleted_conditions['Requisition.id <>'] = $ids[0];
        }
        $deleted_requisitions = $this->find('all' , ['recursive' => -1 , 'conditions' => $deleted_conditions ] ) ;
        foreach ( $deleted_requisitions as $r  ){
            $this->delete_with_related($r['Requisition']['id']) ;
        }
        $new_requisitions = (is_array($new_refund_requisitions) ? $new_refund_requisitions : []) + (is_array($new_sales_requisitions) ? $new_sales_requisitions : []);
        return ($new_requisitions?:true) ;
    }

    /**
     * Compares requisitions with the order data
     * @param INT|array $pos_shift_id order_id or order data
     * @param INT $order_type the type of the requisition (ORDER_TYPE_POS_INBOUND || ORDER_TYPE_POS_OUTBOUND
     * @return boolean|array false on (requisitions have more than order ) , true on (requisitions same as order data ) , new requisitions as a difference between order and existing requisitions
     */
    function compare_pos_requisitions ( $pos_shift_id , $order_type ) {
        App::import('Vendor', 'AutoNumber');
        $this->loadModel('PosShift');
        $this->PosShift->recursive = -1;
        $invoiceItemsData = $this->PosShift->getSessionItemsData($pos_shift_id, $order_type == self::ORDER_TYPE_POS_OUTBOUND);
        $shift = $this->PosShift->read(null,$pos_shift_id);
        if ($shift['PosShift']['status'] != PosShift::STATUS_VALIDATED)
            return [];
        $this->loadModel('RequisitionItem');
        $new_requisitions = [] ;
        $requisition_statuses = [Requisition::STATUS_ACCEPTED] ;
        $store_id = $shift['PosShift']['warehouse_id'];
        $this->loadModel('Product');
        foreach ( $invoiceItemsData as $invoiceItem){
            $requisition_items = $this->RequisitionItem->find('first' ,['fields'=>'MIN(Requisition.id)as earliest_requisition_id , SUM(RequisitionItem.quantity) as sum_quantity' ,  'recursive' => 1 ,
                'conditions' => ['Requisition.status' => $requisition_statuses ,  'RequisitionItem.product_id' => $invoiceItem['iv']['product_id'],  'Requisition.order_id' => $pos_shift_id , 'Requisition.order_type' => $order_type]
            ]);
            if (!empty($requisition_items[0]['sum_quantity']) && $invoiceItem[0]['quantity'] <  $requisition_items[0]['sum_quantity'])
            {
                return false ;
            }
            if ( empty($requisition_items[0]['sum_quantity']) || ($requisition_items[0]['sum_quantity'] !== "0" && $invoiceItem[0]['quantity'] >  $requisition_items[0]['sum_quantity']))
            {
                $new_quantity = $invoiceItem[0]['quantity'] ;
                if(!empty($requisition_items[0]['sum_quantity']))
                {
                    $new_quantity = $invoiceItem[0]['quantity'] - $requisition_items[0]['sum_quantity'];
                }
                if ( empty($new_quantity )){
                    continue ;
                }
                if ( empty($invoiceItem['Product'])){
                    $invoiceItem['Product'] = $this->Product->find('first' , ['recursive' => -1 , 'conditions' => ['id' => $invoiceItem['iv']['product_id']] ])['Product'];
                }
                if (empty($invoiceItem['Product']['track_stock']))
                    continue;
                $new_requisitions[$store_id]['Requisition'] = [
                    'store_id' => $store_id,
                    'status' => Requisition::STATUS_PENDING,
                    'type' => Requisition::$requisition_order_types[$order_type]['type'],
                    'date' => $shift['PosShift']['close_time'],
                    'order_id' => $pos_shift_id,
                    'order_type' => $order_type,
                    'currency_code' => $shift['PosShift']['currency_code'],
                    'parent_requisition_id' => $requisition_items[0]['earliest_requisition_id'],
                ];
                $new_requisitions[$store_id]['RequisitionItem'][] = [
                    'quantity' => $new_quantity,//$v['quantity'],
                    'requested_quantity' => $new_quantity,
                    'unit_price' => $invoiceItem['Product']['average_price'],
                    'description' => $invoiceItem['iv']['description'],
                    'item' => $invoiceItem['iv']['item'],
                    'product_id' => $invoiceItem['iv']['product_id'],
                ];
            }

        }

        return ($new_requisitions?:true) ;
    }

    /**
     * Updates Order status 
     * @param INT|Array $order
     * @param INT $order_type
     * @return INT Delivery status
     */
    public function updateOrderStatus($order , $order_type)
    {
        if (is_numeric($order)){
            $order = $this->getOrder($order, $order_type) ; 
        }
        $model = Requisition::$requisition_order_types[$order_type]['source_model'];
        $requisions_count = $this->find('count', ['applyBranchFind' => false, 'recursive' => -1, 'conditions' => ['Requisition.order_id' => $order[$model]['id'], 'Requisition.order_type' => $order_type]]);
        $received_count = $this->find('count', ['applyBranchFind' => false, 'recursive' => -1, 'conditions' => ['Requisition.status' => [Requisition::STATUS_ACCEPTED, Requisition::STATUS_MODIFIED], 'Requisition.order_id' => $order[$model]['id'], 'Requisition.order_type' => $order_type]]);
        $rejected_count = $this->find('count', ['applyBranchFind' => false, 'recursive' => -1, 'conditions' => ['Requisition.status' => Requisition::STATUS_CANCELLED, 'Requisition.order_id' => $order[$model]['id'], 'Requisition.order_type' => $order_type]]);
        $pending_count = $this->find('count', ['applyBranchFind' => false, 'recursive' => -1, 'conditions' => ['Requisition.status' => Requisition::STATUS_PENDING, 'Requisition.order_id' => $order[$model]['id'], 'Requisition.order_type' => $order_type]]);

        if($requisions_count == 0){
            return null;
        }

        if (!empty($this->compare_requisitions($order, $order_type, false, false))  && ($received_count == $requisions_count) )
            return $this::DELIVERY_STATUS_RECEIVED;
        else if($received_count>0) return  $this::DELIVERY_STATUS_PARTIALLY_RECEIVED;
        else if ($rejected_count > 0 && $pending_count > 0 ) return $this::DELIVERY_STATUS_PARTIALLY_REJECTED ;
        else if($rejected_count > 0)  return $this::DELIVERY_STATUS_REJECTED;
        else  return $this::DELIVERY_STATUS_PENDING;


    }
    /**
     * Generates the product list view to be used in the placeholder
     * @param $requisition_id
     * @return String the products list html view replaced with requisition data
     */
    public function productListHtml($requisition_id)
    {
        $requisition = $this->getRequisition($requisition_id);
        $view = new View ( $this , true ) ; 
        $show_price = false;
        $type = $requisition['Requisition']['type'] ;
        if ($type == Requisition::TYPE_INBOUND) {
            if (check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
                $show_price = true;
            }
        } else if (in_array($type , [Requisition::TYPE_OUTBOUND,Requisition::TYPE_NOEFFECT]) && check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
            $show_price = true;
        }
        $view->set('show_price', $show_price);
        
        $view->set('requisition' , $requisition  ) ;
        return str_replace ( array ( "\n" , "\r" , "\r\n" ) , '' , $view->element ( 'requisitions/products_list_table.ctp' ) );
    }
    public function createOrderFromRequisitions($requisition_ids,&$type){
    	$this->loadModel('Product');
        $requisitions = [] ;
        foreach ( $requisition_ids as $id){
            $requisitions[] = $this->getRequisition($id);
        }
        $work_order_id = $requisitions[0]['Requisition']['work_order_id'] ;
        $store_id = $requisitions[0]['Requisition']['store_id'] ;
        $current_type = $requisitions[0]['Requisition']['type'] ;
        $currency = $requisitions[0]['Requisition']['currency_code'] ;
        $type = Requisition::$requisition_types[$current_type] ;
        if ( empty($type['created'] ) )
        {
            return false ;
        }
        //Checking Requisitions sent of the same type
        foreach ( $requisitions as $r  )
        {
            if ( $current_type != $r['Requisition']['type'])
            {
                return false ;
            }
            if ( !in_array($r['Requisition']['order_type'] , [Requisition::ORDER_TYPE_MANUAL_INBOUND,Requisition::ORDER_TYPE_MANUAL_OUTBOUND] ))
            {
                return false ;
            }
        }
        $current_date = date('Y-m-d');
        $returned_order[$type['created']] = [
            'staff_id' => getAuthOwner('staff_id'),
            'type' => 0,
            'is_offline' => 1,
            'currency_code' => $currency,
            'date' => $current_date,
            'draft' => 0,
            'deposit' => 0,
            'deposit_type' => 1,
            'issue_date' => $current_date,
            'work_order_id' => $work_order_id,
            'store_id' =>$store_id,
        ] ;
        if( $r['Requisition']['order_type'] == Requisition::ORDER_TYPE_MANUAL_OUTBOUND )
        {
            $returned_order[$type['created']]['client_id'] = $r['Requisition']['client_id'];
            $returned_order[$type['created']]['from_manual_outbound_requisition'] = true;
        }elseif( $r['Requisition']['order_type'] == Requisition::ORDER_TYPE_MANUAL_INBOUND )
        {
            $returned_order[$type['created']]['supplier_id'] = $r['Requisition']['supplier_id'];
            $returned_order[$type['created']]['from_manual_inbound_requisition'] = true;
        }
        foreach ( $requisitions as $r ){
            foreach ( $r['RequisitionItem'] as $item){
          
            	if ($r['Requisition']['type'] == Requisition::TYPE_OUTBOUND) {
		            $item['unit_price'] = $this->Product->getProductSellPrice($item['product_id']);
	            }
                $orderData = [
	                'store_id' => $r['Requisition']['store_id'],
	                'item' => $item['Product']['name'],
                    'product_code' => $item['Product']['product_code'],
	                'description' => $item['Product']['description'],
	                'unit_price' => $item['unit_price'],
	                'quantity' => $item['quantity'],
	                'product_id' => $item['product_id'],
	                'unit_name' => $item['unit_name'],
	                'unit_small_name' => $item['unit_small_name'],
	                'unit_factor' => $item['unit_factor'],
	                'unit_factor_id' => $item['unit_factor_id'],
	                'tax1' => $item['Product']['tax1'],
	                'tax2' => $item['Product']['tax2'],
                    'Product' => $item['Product'],
                ];

                if (in_array($r['Requisition']['type'], [Requisition::TYPE_OUTBOUND, Requisition::TYPE_INBOUND])) {
                    $product = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $item['product_id']]]);
                    if (!$product) {
                        continue;
                    }
                    $productData = $product['Product'];
                    $isOutbound = $r['Requisition']['type'] == Requisition::TYPE_OUTBOUND;
                    $isInbound = $r['Requisition']['type'] == Requisition::TYPE_INBOUND;
                    if ($isOutbound) {
                        if (!empty($productData['tax1'])) {
                            $orderData['tax1'] = $productData['tax1'];
                        }
                        if (!empty($productData['tax2'])) {
                            $orderData['tax2'] = $productData['tax2'];
                        }
                    }
                    if ($isInbound) {
                        if (!empty($productData['purchasing_tax1'])) {
                            $orderData['tax1'] = $productData['purchasing_tax1'];
                        }
                        if (!empty($productData['purchasing_tax2'])) {
                            $orderData['tax2'] = $productData['purchasing_tax2'];
                        }
                    }
                }
                if (isset($returned_order[$type['created_item']])) {
                    // Find the index of a matching item
                    $key = array_search(true, array_map(function ($item) use ($orderData) {
                        return (
                            $item['store_id'] == $orderData['store_id'] &&
                            $item['unit_price'] == $orderData['unit_price'] &&
                            $item['product_id'] == $orderData['product_id'] &&
                            $item['unit_factor_id'] == $orderData['unit_factor_id']
                        );
                    }, $returned_order[$type['created_item']]), true);
                
                    if ($key !== false) {
                        // Update the quantity of the matched item
                        $returned_order[$type['created_item']][$key]['quantity'] += $orderData['quantity'];
                    } else {
                        // Add the new order item if no match was found
                        $returned_order[$type['created_item']][] = $orderData;
                    }
                } else {
                    // Add the first order item if the key doesn't exist
                    $returned_order[$type['created_item']] = [$orderData];
                }
            }
        }
        return $returned_order ;
    }
    private function add_action ( $action , $requisition) {
        if ( is_numeric ( $requisition ) ) {
            $requisition = $this->find ('first' , ['recursive' => -1 ,'Requisition.id' => $requisition]  ) ;
        }
        if ( empty ( $requisition ) || empty ( $action ))
        {
            return false ; 
        }

        $isStockReq = $this->checkIfStockRequest($requisition);
        $param4 = $isStockReq ? "Stock Request"
            : Requisition::$requisition_order_types[$requisition['Requisition']['order_type']]['label'];
        $param5 = $isStockReq ? json_encode(['controller' => 'stock_request' , 'action' => 'view'])
            : json_encode(Requisition::$requisition_order_types [$requisition['Requisition']['order_type']]['view_url_array']);

        $action_line =[
                    'primary_id'=>$requisition['Requisition']['id'],
                    'secondary_id'=>$requisition['Requisition']['store_id'],
                    'param1' => $requisition['Requisition']['number'] , 
                    'param2' => $requisition['Requisition']['order_id'],
                    'param3' => $requisition['Requisition']['order_number'],
                    'param4' => $param4,
                    'param5' => $param5,
                    'param6' => Requisition::$requisition_types [$requisition['Requisition']['type']]['label'],
                        ];
                        if($action == ACTION_REQUISITION_CHANGE_STORE){
                            $action_line['param9'] = " from ".$requisition['Requisition']['old_store']. ' to '. $requisition['Requisition']['new_store']. ' ';
                        }
        $this->add_actionline($action ,$action_line );
    
        
    }
    function getActionLineData( $id ){
        $this->loadModel('ActionLine');
        $all_actions_lists = [ACTION_REQUISITION_ADD,ACTION_REQUISITION_UPDATE,ACTION_REQUISITION_DELETE,ACTION_REQUISITION_CHANGE_STORE];
        $action_ids_time = $this->ActionLine->find( 'list' ,['fields' => 'ActionLine.id','applyBranchFind' => false, 'conditions' => ['primary_id' =>$id , 'action_key' => $all_actions_lists  ] ] );
        $all_action_ids = $action_ids_time;
        return compact('all_actions_lists' , 'all_action_ids');
    }

    /**
     * @param $orderItemType
     * @param $orderId
     * @return bool
     */
    function getOrderItem($orderItemType, $orderId)
    {
        $orderItemModel = self::$requisition_order_types[$orderItemType]['source_model'];
        $orderItem = false;
        if($orderItemModel && $orderId)
        {
            $this->loadModel($orderItemModel);
            $recursive = $this->{$orderItemModel}->recursive;
            $this->{$orderItemModel}->recursive = -1;
            $orderItem = $this->{$orderItemModel}->findById($orderId);
            $this->{$orderItemModel}->recursive = $recursive;
        }
        return $orderItem;
    }

    function convertRequisitionsToInvoice($requisitionIDs) {
        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions');
        if ( !$enable_requisitions ) {
            CustomValidationFlash([__('You need to activate requisition per invoice setting',true)]);
            return false;
        }
        $requisitions = $this->find('all', ['conditions' => ['Requisition.id' => $requisitionIDs, 'Requisition.work_order_id IS NOT NULL', 'Requisition.status !=' => Requisition::STATUS_DRAFT]]);
        if (empty($requisitions)) {
            CustomValidationFlash([__('Make sure to select right requisitions not draft with work order',true)]);
            return false;
        }
        $invoiceItems = [];
        $workOrderID = null;
        foreach ( $requisitions as $r ){
            if ($workOrderID && $workOrderID != $r['Requisition']['work_order_id']) {
                CustomValidationFlash([__('All requisitions must belong to the same work order',true)]);
                return false;
            }
            $workOrderID = $r['Requisition']['work_order_id'];
            foreach ( $r['RequisitionItem'] as $item){
                if (!isset($invoiceItems[$item['product_id']][$r['Requisition']['store_id']])) {
					// We use Product->getProductSellPrice since it's going to be an invoice and the product might have a sell price so let's use that instead of the average price used in the requisition
                    $invoiceItems[$item['product_id']][$r['Requisition']['store_id']] = [
                        'unit_price' => GetObjectOrLoadModel('Product')->getProductSellPrice($item['product_id']) ?: $item['unit_price'],
                        'quantity' => $item['quantity'],
                        'product_id' => $item['product_id'],
                        'store_id' => $r['Requisition']['store_id'],
                        'unit_name' => $item['unit_name'],
                        'unit_small_name' => $item['unit_small_name'],
                        'unit_factor' => $item['unit_factor'],
                        'unit_factor_id' => $item['unit_factor_id'],
                        'item' => $item['item'],
                        'description' => $item['item'],
                    ];
                } else {
                    $invoiceItems[$item['product_id']][$r['Requisition']['store_id']]['quantity'] += $item['quantity'];
                }
            }
        }
        $this->loadModel('WorkOrder');
        $this->loadModel('Invoice');
        $workOrder = $this->WorkOrder->find('first', ['conditions' => ['WorkOrder.id' => $workOrderID]]);
        if (empty($workOrder['WorkOrder']['client_id'])) {
            CustomValidationFlash([__('This work order do not have client',true)]);
            return false;
        }
        $invoiceData = [
            'Invoice' => [
                'client_id' => $workOrder['WorkOrder']['client_id'],
                'work_order_id' => $workOrderID,
                'type' => Invoice::Invoice,
                'is_offline' => 1,
                'draft' => 1
            ],
            'InvoiceItem' => []
        ];
        foreach ($invoiceItems as $item) {
            foreach ($item as $storeItem) {
                $invoiceData['InvoiceItem'][] = $storeItem;
            }
        }
        $addInvoiceResult = $this->Invoice->addInvoice($invoiceData, false, true);
        if ($addInvoiceResult['status']) {
            $invoice = $addInvoiceResult['data'];
            $invoiceID = $invoice['Invoice']['id'];
            $this->updateAll(['Requisition.order_type' => Requisition::ORDER_TYPE_INVOICE, 'Requisition.order_id' => $invoiceID, 'Requisition.order_number' => $invoice['Invoice']['no']], ['Requisition.id' => $requisitionIDs]);
            if(!ifPluginActive(EINVOICE_SA_PLUGIN)){
                $this->Invoice->update_draft($invoiceID, 0);
            }
            return $invoiceID;
        } else {
            CustomValidationFlash([__('Adding invoice failed',true)] + $addInvoiceResult);
            return false;
        }
    }

    function getSortFields($sorted_by = null)
    {
        return [
            [
                'title' => __('Requisition Number',true),
                'field' => 'Requisition.number'
            ],
            [
                'title' => __('Created Date',true),
                'field' => 'Requisition.created'
            ],
            'default_order' => $sorted_by
        ];
    }

		/**
		 * @param $item
		 * @param $order_currency_code
		 * @return mixed - returns the correct average or unit price for requsition depends on the order_type and currency of the order (source)
		 */
		private function getAverageOrUnitPriceForRequisition($item, $currency_rate, $order_type, $calculated_item_taxes = false, $local_item_discount = 0, $global_item_discount = 0)
		{
            if (in_array($order_type, [Requisition::ORDER_TYPE_PURCHASE_ORDER, Requisition::ORDER_TYPE_PURCHASE_REFUND, Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE]) && GetObjectOrLoadModel('PurchaseOrder')->is_discount_received) {
                $local_item_discount = 0;
                $global_item_discount = 0;
            }
			$average_price_mode = $this->getAveragePriceOrderTypes($order_type);
			$requisitionUnitPrice = (float) $item['unit_price'] - $calculated_item_taxes - $local_item_discount - $global_item_discount;
			if ($average_price_mode) {
				return $item['Product']['average_price'];
			}
			return $requisitionUnitPrice * $currency_rate;
		}

    /**
     * Delete Bundle stock transaction generated journal when requisition is deleted
     * @param mixed $requisition_id
     * @return void
     */
    private function deleteBundleOutboundStockJournalForRequisition($requisition_id) {
        $this->loadModel('Journal');
        $linkedJournals = $this->Journal->find('all', [
            'recursive'=>-1,
            'fields' => ['stock_transactions.order_id', 'Journal.*'],
            'joins' => [['table' => 'stock_transactions', 'type' => 'left', 'conditions' => ['stock_transactions.id = Journal.entity_id']]],
            'conditions' => ['entity_type' => 'pack_bundle_outbound_stock_transaction', 'stock_transactions.id IS NOT NULL', 'stock_transactions.order_id' => $requisition_id]]);
        if(!empty($linkedJournals)) {
            foreach($linkedJournals as $journal) {
                $this->Journal->delete_journal($journal['Journal']['id'], false);
            }
        }
    }

    /**
     * here we are calculating each product total quantity in each store
     * and its average price in the order
     */
    public function calculateOrderTotals($item_orders, $orgStoreId) {
        $order = [];
        foreach ($item_orders as &$item){
            if ( empty($item['store_id'])){ $item['store_id'] = $orgStoreId;};
            if (isset($order[$item['store_id']][$item['product_id']])) {
                if($order[$item['store_id']][$item['product_id']]['quantity'] == ''){
                    $order[$item['store_id']][$item['product_id']]['quantity'] = 0;
                }
                if($item['quantity'] == ''){
                    $item['quantity'] = 0;
                }
                $oldQuantity = $order[$item['store_id']][$item['product_id']]['quantity'];
                $newQuantity = $item['quantity'];
                $oldUnitPrice = (float)$order[$item['store_id']][$item['product_id']]['unit_price'];
                $newUnitPrice = (float)$item['unit_price'];
                if(($oldQuantity + $newQuantity) != 0){
                    $avgUnitPrice = ($oldQuantity * $oldUnitPrice + $newQuantity * $newUnitPrice) / ($oldQuantity + $newQuantity);
                } else {
                    $avgUnitPrice = false;
                }
                $item['quantity'] += $order[$item['store_id']][$item['product_id']]['quantity'];
                $item['count'] += ($order[$item['store_id']][$item['product_id']]['count'] ?: 1);
                $item['unit_price'] = $avgUnitPrice;

                if ($order[$item['store_id']][$item['product_id']]['unit_name'] != $item['unit_name']) {
                    unset($item['unit_name']);
                    unset($item['unit_small_name']);
                    unset($item['unit_factor']);
                    unset($item['unit_factor_id']);
                }
            }
            $order[$item['store_id']][$item['product_id']] = $item;
            unset($item);
        }
        return $order;
    }

    /**
     * @param $orderType
     * @param $order
     * @return array
     */
    public function getStandardOrder($orderType, $order) {
        $item_model = Requisition::$requisition_order_types[$orderType]['item_source_model']  ;
        $model = Requisition::$requisition_order_types[$orderType]['source_model']  ;
        $item_id = Requisition::$requisition_order_types[$orderType]['item_id']  ;
        if ( is_numeric($order)){
            $this->loadModel($model);
            $order = $this->{$model}->find('first' ,['recursive' => -1 ,'conditions' =>['id' => $order]]);
        }
        if ( empty($order[$item_model]))
        {
            $this->loadModel($item_model);
            $items = $this->{$item_model}->find('all' , ['recursive' => 1 , 'conditions' => [$item_id => $order[$model]['id']] ]);
            foreach ( $items as $i ){
                $order[$item_model][] = $i[$item_model] ;
            }
        }
        $standardOrder = ["Order" => $order[$model], "OrderItems" => $order[$item_model]];
        return $standardOrder;
    }

    /**
     * @param $conditions
     * @return ["requisitions_qty", "first_requisition_id"]
     * get the requisitions quantity of a product in order
     */
    public function getOrderProductRequisitionsQty($conditions) {
        $this->loadModel('RequisitionItem');
        $result = $this->RequisitionItem->find('first', [
            'fields' => 'MIN(Requisition.id) as earliest_requisition_id , SUM(RequisitionItem.quantity) as sum_quantity',
            'recursive' => 1,
            'conditions' => $conditions
        ]);
        return [
            "requisitions_qty" => $result[0]['sum_quantity']?:0,
            "first_requisition_id" => $result[0]['earliest_requisition_id']?:null
        ];
    }

    public function recalculateRequisition($requisition) {
        App::import('Vendor', 'AutoNumber');
        $order_type = $requisition['Requisition']['order_type'];
        $order = $requisition['Requisition']['order_id'];
        $requisition_type = Requisition::$requisition_order_types[$order_type]['type']  ;
        $quantity_field = (Requisition::$requisition_order_types[$order_type]['quantity_field']?:'quantity');
        $order = $this->getStandardOrder($order_type, $order);
        $item_model = "OrderItems"  ;
        $model = "Order";
        $item_orders = $order[$item_model] ; //We are changing this because the items need to be manipulated first

        /**
         * here we are calculating each product total quantity in each store
         * and its average price in the order
         */
        $order[$item_model] = $this->calculateOrderTotals($item_orders, $order[$model]['store_id']);

        $this->loadModel('RequisitionItem');
        $new_requisitions = [] ;
        $requisition_statuses = [Requisition::STATUS_ACCEPTED, Requisition::STATUS_MODIFIED, Requisition::STATUS_PENDING] ;

        $this->loadModel('Product');
        $purchase_order_data = null;
        /**
         * in this loop we compare each product quantity in the order
         * with the product quantity in its requisition items
         * so if the order product qty > requisition items qty then we create a new requisition
         * else if order product qty < requisition items qty then there is an error bcs we can't receive more than  the order qty
         *
         */
        foreach ( $order[$item_model] as $store_id => $products) {
            foreach ($products as $k => $storeOrderItem) {
                $item_model = "OrderItems";
                $model ="Order";

                if (empty($storeOrderItem['Product'])) {
                    $order[$item_model][$k]['Product'] = $storeOrderItem['Product'] = $this->Product->find('first', ['recursive' => -1, 'conditions' => ['id' => $storeOrderItem['product_id']]])['Product'];
                }
                if (empty($storeOrderItem['Product']['track_stock'])) {
                    continue;
                }
                $store_id = ($storeOrderItem['store_id'] ?: $order[$model]['store_id']);
                /**
                 * here we got the total quantity of this pro
                 */
                $requisition_items = $this->getOrderProductRequisitionsQty([
                    'Not' => [
                        'Requisition.id' => $requisition['Requisition']['id']
                    ],
                    'Requisition.store_id' => $store_id,
                    'Requisition.status' => $requisition_statuses,
                    'RequisitionItem.product_id' => $storeOrderItem['product_id'],
                    'Requisition.order_id' => $order[$model]['id'],
                    'Requisition.order_type' => $order_type
                ]);
                $orderReceivedQty = $requisition_items["requisitions_qty"];
                $productOrderQty = $storeOrderItem[$quantity_field];
//              //check if order product qty < requisition product qty
                if (!empty($orderReceivedQty) && $productOrderQty < (int)$orderReceivedQty) {
                    return false;
                }
                $this->loadModel("RequisitionItem");

                //check if order product qty > requisition product qty
                if (empty($orderReceivedQty) || ($orderReceivedQty !== "0" && $productOrderQty > $orderReceivedQty)) {
                    $new_quantity = $productOrderQty;
                    if (!empty($orderReceivedQty)) {
                        $new_quantity = $productOrderQty - $orderReceivedQty;
                    }
                    if (empty($new_quantity)) {
                        continue;
                    }
	                $currency = $this->get_default_currency();
                    $new_requisitions[$store_id]['Requisition'] = [
                        'id' => $requisition['Requisition']['id'],
                        'store_id' => $store_id,
                        'status' => $requisition['Requisition']['status'],
                        'type' => $requisition_type,
                        'date' => $requisition['Requisition']['date'],
                        'order_id' => $order[$model]['id'],
                        'order_type' => $order_type,
                        'currency_code' => $currency,
                        'parent_requisition_id' => $requisition['Requisition']['parent_requisition_id'],
                    ];
                    if (!empty($requisition['Requisition']['number'])) {
                        $new_requisitions[$store_id]['Requisition']['number'] = $requisition['Requisition']['number'];
                    }
                    if (!empty($requisition['Requisition']['order_number'])) {
                        $new_requisitions[$store_id]['Requisition']['order_number'] = $requisition['Requisition']['order_number'];
                    }
                    $calculated_item_taxes = 0;
                    $orderQuantity = $storeOrderItem[$quantity_field];
                    $receivedQuantity = $requisition_items["requisitions_qty"];
                    $unreceivedQuantity = $orderQuantity - $receivedQuantity;
                    if (in_array($order_type, [self::ORDER_TYPE_PURCHASE_ORDER, self::ORDER_TYPE_PURCHASE_REFUND])) {
                        if ($purchase_order_data == null) {
                            $purchase_order_data = GetObjectOrLoadModel('PurchaseOrder')->getPurchaseOrder($order[$model]['id']);
                            $item_model = Requisition::$requisition_order_types[$order_type]['item_source_model'];
                            $model = Requisition::$requisition_order_types[$order_type]['source_model'] ;
                        }
                        $inclusiveTaxForUnreceivedQuantity = \App\Helpers\TaxesHelper::calculatePartialTax($storeOrderItem['summary_tax1'], $orderQuantity, $unreceivedQuantity);
                        $inclusiveTaxForUnreceivedQuantity2 = \App\Helpers\TaxesHelper::calculatePartialTax($storeOrderItem['summary_tax2'], $orderQuantity, $unreceivedQuantity) ;
                        $tax1 = $this->isPurchaseOrderTaxInclusive($storeOrderItem['tax1'], $storeOrderItem['purchase_order_id']) ? $inclusiveTaxForUnreceivedQuantity : 0;
                        $tax2 = $this->isPurchaseOrderTaxInclusive($storeOrderItem['tax2'], $storeOrderItem['purchase_order_id']) ? $inclusiveTaxForUnreceivedQuantity2 : 0;
                        $calculated_item_taxes = ($tax1 + $tax2) / $new_quantity;
                        $local_item_discount = 0;
                        if (!empty($storeOrderItem['discount']) && $storeOrderItem['discount_type'] == InvoiceItem::DISCOUNT_AMOUNT) {
                            $local_item_discount = $storeOrderItem['discount'];
                        }
                        else if (!empty($storeOrderItem['discount']) && $storeOrderItem['discount_type'] == InvoiceItem::DISCOUNT_PERCENTAGE) {
                            $local_item_discount = $storeOrderItem['discount'] * $storeOrderItem['unit_price'] / 100;
                        }
                        $global_item_discount =$purchase_order_data[$model]['summary_subtotal'] ==0?0:  ($purchase_order_data[$model]['summary_discount'] / $purchase_order_data[$model]['summary_subtotal']) * ($storeOrderItem['unit_price'] - $local_item_discount);
                    }
                    $currency_rate = $this->getSourceCurrencyRateFromJournal($order, $model, $order_type);
                    $trackingData = null;
                    if($storeOrderItem['Product']['tracking_type'] != TrackStockUtil::QUANTITY_ONLY) {
                        $oldReqItem = $this->RequisitionItem->find('first', ['conditions' => [
                                'RequisitionItem.requisition_id' => $requisition['Requisition']['id'],
                                "RequisitionItem.product_id" => $storeOrderItem['product_id'],
                            ]
                        ]);
                        if($oldReqItem) {
                            $trackingData = $oldReqItem['RequisitionItem']['tracking_data'];
                        }
                    }
                    $new_requisitions[$store_id]['RequisitionItem'][] = [
                        'quantity' => $new_quantity,
                        'requested_quantity' => $new_quantity,
	                    'unit_price' => $this->getAverageOrUnitPriceForRequisition($storeOrderItem, $currency_rate, $order_type, $calculated_item_taxes, $local_item_discount, $global_item_discount),
	                    'description' => $storeOrderItem['description'],
                        'item' => $storeOrderItem['item'],
                        'product_id' => $storeOrderItem['product_id'],
                        'unit_name' => $storeOrderItem['unit_name'],
                        'unit_small_name' => $storeOrderItem['unit_small_name'],
                        'unit_factor' => $storeOrderItem['unit_factor'],
                        'unit_factor_id' => $storeOrderItem['unit_factor_id'],
                        'tracking_data' => $trackingData
                    ];
                }
            }
        }
        return ($new_requisitions?:true);
    }

	public function getPosShiftRequisitions($pos_shift_id) {
		$isCalculatedPerInvoice = GetObjectOrLoadModel('PosShift')->checkPosShiftIsCalculatedPerInvoice(GetObjectOrLoadModel('PosShift')->read(null, $_GET['pos_shift_id']));

		if ($isCalculatedPerInvoice) {
			return $this->getPosShiftInvoices($pos_shift_id);
		}

		$requisitions = $this->find('all', ['conditions' => [
			'Requisition.order_id' => $pos_shift_id,
			'Requisition.order_type' => [Requisition::ORDER_TYPE_POS_INBOUND, Requisition::ORDER_TYPE_POS_OUTBOUND]
		]]);

		if (empty($requisitions)) {
			return [];
		}
		return $requisitions;
	}

	private function getPosShiftInvoices($pos_shift_id) {
		$pos_shift_invoices = GetObjectOrLoadModel('Invoice')->find('all', ['recursive' => -1, 'conditions' => ['Invoice.pos_shift_id' => (int) $pos_shift_id]]);
		$pos_shift_invoice_ids = [];
		foreach ($pos_shift_invoices as $pos_shift_invoice) {
			$pos_shift_invoice_ids[] = $pos_shift_invoice['Invoice']['id'];
		}
		if (!empty($pos_shift_invoice_ids)) {
			return $this->find('all', ['conditions' => [
				'Requisition.order_id' => $pos_shift_invoice_ids,
				'Requisition.order_type' => [Requisition::ORDER_TYPE_INVOICE, Requisition::ORDER_TYPE_INVOICE_REFUND]
			]]);
		}
		return [];
	}

    function isStoreToActive($param)
    {
            $param['store_id'] = $param['to_store_id'];
            return   $this->isStoreActive($param);
    }

    function getSourceCurrencyRateFromJournal($order, $model, $order_type) {
        $this->loadModel('Journal');
        $this->loadModel('JournalTransaction');
        $mapping = [
            Requisition::ORDER_TYPE_INVOICE => 'invoice',
            Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE => 'credit_note',
            Requisition::ORDER_TYPE_INVOICE_REFUND => 'refund_receipt',
            Requisition::ORDER_TYPE_PURCHASE_ORDER => 'purchase_order',
            Requisition::ORDER_TYPE_PURCHASE_REFUND => 'purchase_refund'
        ];
        $entity_type = $mapping[$order_type] ?? null;
        if (empty($entity_type)) {
            return 1;
        }
        $journal_id = $this->Journal->find('first', ['recursive' => -1, 'fields' => ['id'], 'conditions' => ['Journal.entity_id' => $order[$model]['id'], 'Journal.entity_type' => $entity_type]])['Journal']['id'] ?? null;
        if (empty($journal_id)) {
            return 1;
        }
        $currency_rate = $this->JournalTransaction->find('first', ['recursive' => -1, 'fields' => ['id', 'currency_rate'], 'conditions' => ['JournalTransaction.journal_id' => $journal_id]])['JournalTransaction']['currency_rate'] ?? null;
        if (empty($currency_rate)) {
            return 1;
        }
            return $currency_rate;
    }

		/**
		 * @param $order_type
		 * @return bool
		 */
		private function getAveragePriceOrderTypes($order_type): bool {
			return in_array($order_type, [Requisition::ORDER_TYPE_STOCKTAKING_IN, Requisition::ORDER_TYPE_STOCKTAKING_OUT, Requisition::ORDER_TYPE_INVOICE_REFUND, Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE, Requisition::ORDER_TYPE_INVOICE, Requisition::ORDER_TYPE_PURCHASE_REFUND, Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE]);
		}

    public function checkIfStockRequest($requisition) : bool
    {
        $stockRequestOrderTypes =[
            Requisition::TYPE_INBOUND,
            Requisition::TYPE_OUTBOUND,
            Requisition::ORDER_TYPE_TRANSFER_REQUISITION,
        ];
        $orderType = $requisition['Requisition']['order_type'];
        return  $requisition['Requisition']['order_id'] != 0 &&  in_array($orderType , $stockRequestOrderTypes);
    }


    function groupRequisitionsItemWithQuantity($requisition_ids)
    {
        $requisitions = $this->find('all', ['conditions' => ['Requisition.id' => $requisition_ids], 'recursive' => 1]);
        $product_quantities = [];
        foreach ($requisitions as $requisition) {
            foreach ($requisition['RequisitionItem'] as $item) {
                $product_id = $item['product_id'];
                if (!isset($product_quantities[$product_id])) {
                    $product_quantities[$product_id] = 0;
                }
                $product_quantities[$product_id] += $item['quantity'];
            }
        }
        return $product_quantities;
    }


    private function handleDeletedSourceItems(&$old_confirmed_requisition_items, array $source_items, int $order_type)
    {
        if ($order_type === self::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL){
            $source_ids = array_map(function ($item){
                if (isset($item["Product"])){
                    return $item["Product"]["id"];
                }
                return null;
            } , $source_items);
            $source_ids = array_filter($source_ids);
            foreach ($old_confirmed_requisition_items as $idx => $confirmed_requisition_item){
                if (!in_array($confirmed_requisition_item["RequisitionItem"]["product_id"] , $source_ids) ){
                    unset($old_confirmed_requisition_items[$idx]);
                }
            }
        }
        return $old_confirmed_requisition_items;
    }

    public function updateItemsPrices(&$oldConfirmedRequisitionItems, $orderItems)
    {
        foreach ($orderItems as $orderItem) {
            $key = array_search(true, array_map(function ($oldConfirmedRequisitionItem) use ($orderItem) {
                return (
                    $oldConfirmedRequisitionItem['product_id'] == $orderItem['product_id'] &&
                    $oldConfirmedRequisitionItem['unit_factor_id'] == $orderItem['unit_factor_id']
                );
            }, $oldConfirmedRequisitionItems), true);
        
            if ($key !== false) {
                $oldConfirmedRequisitionItems[$key]['unit_price'] = $orderItem['unit_price'];
            }
        }
    }


   function addRequisitionItemMultiUnits($requisitionItems)
    {
        $Product = ClassRegistry::init('Product');
        $Product->recursive = -1;
        $productIds = array_map(function($item) {
            return $item['product_id'];
        },$requisitionItems);
        $Product->applyBranch['onFind'] = false;
        $products = $Product->find('all', ['conditions' => ['Product.id' => $productIds]]);
        //map products by id
        $products = array_combine(array_map(function($item) {
            return $item['Product']['id'];
        }, $products), $products);
        $enable_multi_units = settings::getValue(InventoryPlugin, 'enable_multi_units');
        if (ifPluginActive(InventoryPlugin)) {
            foreach ($requisitionItems as $i => $item) {
               
                $requisitionItems[$i]['quantity_written'] = format_number($item['quantity']);

                if (!empty($item['product_id'])) {
                    if(isset($products[$item['product_id']])) {
                        $pp[0] = $products[$item['product_id']]['Product'];
                    } else {
                        $prod = $Product->read(null, $item['product_id']);
                        $pp[0] = $prod['Product'];
                    }
                    if ($enable_multi_units) {
                        $Product->add_multi_units($pp, true);
                    }
                    $requisitionItems[$i]['Product'] = $pp[0];

                }
            }
        }

        return $requisitionItems;
    }

    /**
     * @param $order_type
     * @return void
     */
    public function updateManufactureResultRequisition($requisition): void
    {
        if ($requisition['Requisition']['order_type'] == Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL) {
            dispatch_event_action(new MoMaterialsAveragePriceUpdated([
                'use_outbound_materials' => true,
                'date_from' => $requisition['Requisition']['date'],
            ]));
        }
    }


}
