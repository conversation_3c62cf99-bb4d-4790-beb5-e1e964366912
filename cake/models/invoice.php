<?php

require_once dirname(dirname(__DIR__)) . '/cake/vendors/payments/TabbyPayment.php';

use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;
use App\Helpers\Common\PurchaseOrderAndInvoiceHelper;
use App\Repositories\CreditChargeRepository;
use App\Repositories\CreditUsageRepository;
use App\Services\Communicator\CommunicatorFacade;
use App\Transformers\ServiceModelDataTransformer;
use App\Utils\InvoiceTypeUtil;
use App\Validators\CreditNote\AdvancePaymentCreditNoteValidator;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Common\Utils\ElectronicInvoicesActionsUtil;
use Izam\Daftra\Common\Utils\ElectronicInvoicesStatus;
use Izam\Daftra\Common\Utils\EntityAppDataKeysUtil;
use Izam\Daftra\Common\Utils\ErrorTypes;
use Izam\Daftra\Common\Utils\InvoiceExternalSourceUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\ProductionPlanSourceTypeUtil;
use Izam\Daftra\Invoice\Repositories\InvoiceRepository;
use Izam\Daftra\Invoice\Services\AdvancePaymentService;
use Izam\Daftra\Invoice\Services\InvoicePaymentService;
use Izam\Limitation\Utils\LimitationUtil;
use Izam\ScoreCalculator\Services\SiteScoreCalculator;
use Rollbar\Payload\Level;
use Rollbar\Rollbar;
use App\Helpers\CurrencyHelper;

use App\Services\PaymentGateways\Tamara\RefundService;
use Izam\Daftra\Common\Utils\SettingsUtil;

use App\Services\PaymentGateways\Gateways\Tabby\RefundService as TabbyRefundService;
use Izam\Daftra\Common\Utils\InvoiceSourceTypesUtil;

define('INVOICE_STATUS_UNPAID', 0);
define('INVOICE_STATUS_PARTIAL_PAID', 1);
define('INVOICE_STATUS_PAID', 2);
define('INVOICE_STATUS_REFUNDED', 3);
define('INVOICE_STATUS_PARTIAL_REFUND', 5);
define('INVOICE_STATUS_OVERPID', 4);
define('INVOICE_STATUS_DRAFT', -1);
define('INVOICE_STATUS_DUE', -2);
define('INVOICE_STATUS_OverDue', -3);

define("ESTIMATE_STATUS_OPEN", 0);
define('ESTIMATE_STATUS_SENT', 1);
define('ESTIMATED_STATUS_VIEWED', 2);
define('ESTIMATE_STATUS_REPLIED', 3);
define('ESTIMATE_STATUS_ACCEPTED', 4);
define('ESTIMATE_STATUS_INVOICED', 5);
define('ESTIMATE_STATUS_SALES_ORDERED', 6);

define("SALES_ORDER_STATUS_PENDING", 0);
define('SALES_ORDER_STATUS_PARTIALLY_INVOICED', 1);
define('SALES_ORDER_STATUS_INVOICED', 2);
/**
 * @property Site $Site
 * @property InvoiceItem $InvoiceItem
 */
class Invoice extends AppModel {
    var $applyBranch = ['onFind' => true, 'onSave' => true];
    
    const TEMP_BOOKING = 13;
    const INSURANCE_INVOICE = 10;
    const INSURANCE_REFUND = 11;
    const TEMPINVOICE = 9;
    const BOOKING = 8;
    const Resellers = 7;
    const Refund_Receipt = 6;
    const Credit_Note = 5;
    const Estimate = 3;
    const Template = 1;
    const Invoice = 0;
    const Subscription = 2;
    const DISCOUNT_TYPE_PERCENTAGE = 1;
    const DISCOUNT_TYPE_VALUE = 2;
    const DEPOSIT_TYPE_UNPAID = 1;
    const DEPOSIT_TYPE_PAID = 2;
    const RESERVATION_ORDER = 14;
    const SHOP_FRONT = 15;
    const SALES_ORDER = 12;
    const DEBIT_NOTE = 16;
    const ADVANCE_PAYMENT = 19;

    const MULTIPLE_SALES_PERSONS = -2;
    const CONTRACT_INSTALLMENT = 17;
    const LEASE_CONTRACT = 18;

    public $types = [
		self::Invoice => 'Invoice',
		self::Subscription => 'Subscription',
		self::Estimate => 'Estimate',
		self::Credit_Note => 'CreditNote',
		self::Refund_Receipt => 'RefundReceipt',
		self::Resellers => 'BNR',
		self::BOOKING => 'Booking',
		self::TEMPINVOICE => 'Temp Invoice',
		self::INSURANCE_INVOICE => 'Insurance Invoice',
		self::SALES_ORDER => 'Sales Order',
		self::DEBIT_NOTE => 'DebitNote',
        self::CONTRACT_INSTALLMENT => 'Contract Installment',
		self::LEASE_CONTRACT => 'Lease Contract',
        self::ADVANCE_PAYMENT => 'Advance Payment',
	];

    public static $labels = [
        self::Invoice => 'Invoice',
        self::Subscription => 'Subscription',
        self::Estimate => 'Estimate',
        self::Credit_Note => 'CreditNote',
        self::Refund_Receipt => 'RefundReceipt',
        self::Resellers => 'BNR',
        self::BOOKING => 'Booking',
        self::TEMPINVOICE => 'Temp Invoice',
        self::INSURANCE_INVOICE => 'Insurance Invoice',
        self::SALES_ORDER => 'Sales Order',
        self::DEBIT_NOTE => 'DebitNote',
        self::CONTRACT_INSTALLMENT => 'Contract Installment',
        self::LEASE_CONTRACT => 'Lease Contract',
        self::ADVANCE_PAYMENT => 'Advance Payment',
    ];
    //EntityAppData , SubmitEntityAppData , ErrorEntityAppData ,SentEntityAppData should be removed and set for country invoice as for now its binded incorrectly
	public $full_relations = [
		'table' => 'invoices',
		'relations' => [
			'Client' => ['table' => 'clients', 'type' => 'belongsTo', 'relation_key' => 'client_id'],
			'Country' => ['type' => 'belongsTo', 'relation_key' => 'client_country_code', 'foreign_key' => 'code', 'table' => 'countries'],
			'Staff' => ['table' => 'staffs', 'type' => 'belongsTo', 'relation_key' => 'staff_id'],
			'SalesPerson' => ['table' => 'item_staffs', 'type' => 'belongsTo', 'foreign_key' => 'item_id', 'conditions' => 'SalesPerson.item_type = 12'],
            'EntityAppData' => ['table' => 'entity_app_data', 'type' => 'hasMany', 'foreign_key' => 'EntityAppData.entity_id', 'conditions' => 'EntityAppData.entity_key = "invoice" and EntityAppData.action_key = "'. ElectronicInvoicesActionsUtil::GET_DOCUMENT.'"', 'order' => 'EntityAppData.id DESC'],
            'SubmitEntityAppData' => ['table' => 'entity_app_data', 'type' => 'hasMany', 'foreign_key' => 'SubmitEntityAppData.entity_id', 'conditions' => 'SubmitEntityAppData.entity_key = "invoice" and SubmitEntityAppData.app_key = "'. EntityAppDataKeysUtil::ELECTRONIC_INVOICE.'" and SubmitEntityAppData.action_key = "'. ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT.'"', 'order' => 'SubmitEntityAppData.id DESC'],
            'ErrorEntityAppData' => ['table' => 'entity_app_data', 'type' => 'hasMany', 'foreign_key' => 'ErrorEntityAppData.entity_id', 'conditions' => 'ErrorEntityAppData.entity_key = "invoice"  and ErrorEntityAppData.action_key = "'. ElectronicInvoicesActionsUtil::ERROR.'"', 'order' => 'ErrorEntityAppData.id DESC'],
            'SentEntityAppData' => ['table' => 'entity_app_data', 'type' => 'hasMany', 'foreign_key' => 'SentEntityAppData.entity_id', 'conditions' => 'SentEntityAppData.entity_key = "invoice" and SentEntityAppData.app_key = "'. EntityAppDataKeysUtil::ELECTRONIC_INVOICE.'" and SentEntityAppData.action_key = "'. ElectronicInvoicesActionsUtil::SENT_DOCUMENT.'"', 'order' => 'SentEntityAppData.id DESC'],
			'InvoiceItem' => ['table' => 'invoice_items', 'type' => 'hasMany', 'foreign_key' => 'invoice_id', 'order' => '`InvoiceItem`.`display_order`',
				'relations' => [
					'Product' => ['type' => 'belongsTo', 'relation_key' => 'product_id', 'table' => 'products', 'is_sub' => true, 'hide_empty' => true],
					'ProductMasterImage' => ['type' => 'belongsTo', 'relation_key' => 'product_id', 'foreign_key' => 'product_id', 'table' => 'product_images', 'is_sub' => true, 'conditions' => 'ProductMasterImage.default = 1'],
				],
			],
			'InvoiceReminder' => ['table' => 'invoice_reminders', 'type' => 'hasMany', 'foreign_key' => 'invoice_id', 'order' => '`InvoiceReminder`.`display_order`', 'hide_empty' => true],
			'InvoiceCustomField' => ['table' => 'invoice_custom_fields', 'type' => 'hasMany', 'foreign_key' => 'invoice_id', 'order' => '`InvoiceCustomField`.`display_order`', 'hide_empty' => true],
			'InvoicePayment' => ['table' => 'invoice_payments', 'type' => 'hasMany', 'foreign_key' => 'invoice_id', 'order' => 'InvoicePayment.id DESC', 'hide_empty' => true],
			'EmailLog' => ['table' => 'email_logs', 'type' => 'hasMany', 'foreign_key' => 'invoice_id', 'order' => '`EmailLog`.`sent_date` DESC', 'hide_empty' => true],
			'InvoiceTax' => ['table' => 'invoice_taxes', 'type' => 'hasMany', 'foreign_key' => 'invoice_id', 'order' => '`InvoiceTax`.`id`', 'hide_empty' => true],
			'InvoiceDocument' => ['table' => 'invoice_documents', 'type' => 'hasMany', 'foreign_key' => 'invoice_id', 'order' => ' `InvoiceDocument`.`id` ASC', 'hide_empty' => true],
            'RelatedInvoice' => ['table' => 'related_invoices', 'type' => 'hasMany', 'foreign_key' => 'invoice_id', 'order' => ' `RelatedInvoice`.`id` ASC', 'hide_empty' => true],
        ],
	];


    public $InvoiceTypesMetaData = [
        self::Invoice => ['url' => ['controller' => 'invoices', 'view_action' => 'view', 'add_action' => 'add', 'edit_action' => 'edit', 'name_action' => 'Invoice']],
        self::Subscription =>  ['url' => ['controller' => 'invoices', 'view_action' => 'view_subscription', 'add_action' => 'add_subscription', 'edit_action' => 'edit_subscription', 'name_action' => 'Subscription']],
        self::Estimate =>  ['url' => ['controller' => 'invoices', 'view_action' => 'view_estimate', 'add_action' => 'add_estimate', 'edit_action' => 'edit_estimate', 'name_action' => 'Estimate']],
        self::Credit_Note =>  ['url' => ['controller' => 'invoices', 'view_action' => 'view_creditnote', 'add_action' => 'add_creditnote', 'edit_action' => 'edit_creditnote',  'name_action' => 'Credit Note']],
        self::Refund_Receipt =>  ['url' => ['controller' => 'invoices', 'view_action' => 'view_refund', 'add_action' => 'add_refund', 'edit_action' => 'edit_refund', 'name_action' => 'Refund Receipt']],
        self::Resellers =>  ['url' => ['controller' => 'resellers', 'view_action' => 'view', 'add_action' => 'add', 'edit_action' => 'edit', 'name_action' => 'Resellers']],
        self::BOOKING =>  ['url' => ['controller' => 'bookings', 'view_action' => 'view', 'add_action' => 'add', 'edit_action' => 'edit', 'name_action' => 'Booking']],
        self::TEMPINVOICE =>  ['url' => ['controller' => 'bookings', 'view_action' => 'view', 'add_action' => 'add', 'edit_action' => 'edit', 'name_action' => 'Booking']],
        self::INSURANCE_INVOICE => ['url' => ['controller' => 'invoices', 'view_action' => 'view', 'add_action' => 'add', 'edit_action' => 'edit', 'name_action' => 'Insurance Invoice']],
        self::SALES_ORDER =>  ['url' => ['controller' => 'invoices', 'view_action' => 'view_sales_order', 'add_action' => 'add_sales_order', 'edit_action' => 'edit_sales_order', 'name_action' => 'Sales Order']],
        self::DEBIT_NOTE =>  ['url' => ['controller' => 'invoices', 'view_action' => 'view_debitnote', 'add_action' => 'add_debitnote', 'edit_action' => 'edit_debitnote', 'name_action' => 'Debit Note']],
        self::ADVANCE_PAYMENT =>  ['url' => ['controller' => 'invoices', 'view_action' => 'view_advance_payment', 'add_action' => 'add_advance_payment', 'edit_action' => 'edit_advance_payment', 'name_action' => 'Advance Payment']],
    ];

    public $name = 'Invoice';
    public $belongsTo = array('Site', 'Client', 'Staff' => [
        'className' => 'Staff',
        'foreignKey' => 'staff_id',
        'conditions' => ['Invoice.staff_id != -3']
    ]);

	public $hasOne = [
		'SalesPerson' => ['className' => 'ItemStaff', 'foreignKey' => 'item_id', 'conditions' => ['SalesPerson.item_type' => 12]],
	];

    public $journal_settings=array(
        'level'=>'journals',
        'entity_type'=>array('invoice','refund_receipt','credit_note','debit_note'),
    );
    
    var $actsAs = array(
		'tag' => array(),
		'journal' => array(
                    'is_journal'=>true,
                    'entity_type'=>array('invoice','refund_receipt','credit_note','debit_note', 'advance_payment'),
                 ),
        'customform' => [
            'custom_model_name' => 'InvoicesCustomData',
            'custom_data_table_name' => 'invoices_custom_data',
            'callbacks' => [
                'afterSave' => 'afterCustomSaveCallback'
            ]
        ],
		
    );

    /**
	 * 
	 * @param type $invoice
	 * @return type the url to redirect to
	 */
    function getSourceRedirect($invoice)
	{
		$sourceType = $invoice['Invoice']['source_type'];
		$sourceRedirects = [
		self::BOOKING => Router::url(['controller' => 'bookings','action'=> 'view', $invoice['Invoice']['source_id']])
		];
		return $sourceRedirects[$sourceType];
	}

    function hasCreditCharge ($client_id = null )
    {
        if(is_null($client_id)){
            $client_id = $this->id;
        }
        if(ifPluginActive(CREDIT_PLUGIN)){
            $creditCharge = GetObjectOrLoadModel ('CreditCharge') ;
            return $creditCharge->find ( 'count' , ['recursive' => -1 , 'conditions' => ['CreditCharge.invoice_id' => $client_id ,'CreditCharge.deleted_at IS NULL'] ]) > 0;
        }else{
            return false;
        }
    }


    function hasCreditUsage ($client_id = null )
    {
        if(is_null($client_id)){
            $client_id = $this->id;
        }
        if(ifPluginActive(CREDIT_PLUGIN)){
            $creditUsage = GetObjectOrLoadModel ('CreditUsage') ;
            return $creditUsage->find ( 'count' , ['recursive' => -1 , 'conditions' => ['CreditUsage.invoice_id' => $client_id ,'CreditUsage.deleted_at IS NULL'] ]) > 0;
        }else{
            return false;
        }
    }

    public $hasMany = array(
        'InvoiceItem' => array('order' => 'InvoiceItem.display_order', 'dependant' => true),
        'InvoiceReminder' => array('order' => 'InvoiceReminder.display_order', 'dependant' => true),
        'InvoiceCustomField' => array('order' => 'InvoiceCustomField.display_order', 'dependant' => true),
        'InvoicePayment' => array('order' => 'InvoicePayment.id DESC'),
        'EmailLog' => array('order' => 'EmailLog.sent_date DESC'),
        'InvoiceTax' => array('order' => 'InvoiceTax.id', 'dependant' => true),
        'InvoiceDocument' => array('order' => 'InvoiceDocument.id', 'dependant' => false),
        'RelatedInvoice' => array('order' => 'RelatedInvoice.id', 'dependant' => false),
        'RelatedInvoiceReverse' => array(
            'className'  => 'RelatedInvoice',
            'foreignKey' => 'related_invoice_id'
        )
       // 'EntityAppData' => [ 'dependant' => true, 'className' => 'EntityAppData', 'foreignKey' => 'entity_id', 'conditions' => ['EntityAppData.entity_key' => 'invoice', 'EntityAppData.app_key' => EntityAppDataKeysUtil::ELECTRONIC_INVOICE, 'EntityAppData.action_key' => ElectronicInvoicesActionsUtil::GET_DOCUMENT], 'order' => 'EntityAppData.id DESC', 'limit' => 1],

    );
    public static $taxes = array();
//$conditions['InvoicePayment.payment_method <>']='client_credit';
    /**
     * @var Client
     */
    public $Client;

    /**
     *
     * @var InvoicePayment
     */
    public $InvoicePayment;

    /**
     * @var InvoiceTax
     */
    public $InvoiceTax;

    /**
     * @var InvoiceDocument
     */
    public $InvoiceDocument;

	public $order ;
	
	function getSortFields($sorted_by = null) {
		
        return array(
		array('title' => __('Client',true),'field' => 'client_business_name' ),
		array('title' => __('Created Date',true),'field' => 'created' ,'default' => 'desc'),
		array('title' => __('Number',true),'field' => 'no' ),
		array('title' => __('Payment Status',true),'field' => 'payment_status' ),
		array('title' => __('Date',true),'field' => 'date','default' => 'desc' ),
		'default_order' => $sorted_by
		);
    }
	
    function __construct($id = false, $table = null, $ds = null) {
		
        parent::__construct($id, $table, $ds);
        $positive = [['rule' => 'numeric', 'message' => __('Valid number required', true), 'allowEmpty' => true], ['rule' => ['comparison', '>=', 0], 'message' => __('Positive number required', true)]];
	    $this->validate = [
		    'client_id' => [['rule' => 'notEmpty', 'message' => __('required', true)], ['rule' => 'checkClientExists', 'message' => __('Client not found', true)]],
		    'currency_code' => ['rule' => 'checkCurrencyExists', 'message' => __('Currency not supported', true), 'allowEmpty' => true],
		    'discount' =>
			    [
				    ['rule' => 'numeric', 'message' => __('Valid number required', true), 'allowEmpty' => true],
				    ['rule' => ['comparison', '>=', 0], 'message' => __('Positive number required', true)],
				    ['rule' => ['comparison', '<', 101], 'message' => __('Discount cannot be more than 100%', true)],
				    ['rule' => ['validate_maximum_discount'], 'message' => __("Discount exceeded maximum", true), 'allowEmpty' => true]
			    ],
		    'discount_amount' => [
			    ['rule' => ['validate_maximum_discount'], 'message' => __("Discount exceeded maximum", true), 'allowEmpty' => true]
		    ],
		    'despoit' => am($positive, ['rule' => 'checkDeposit', 'message' => __('Deposit must be less than unpaid value', true)]),
		    'date' => [
			    'checkDateFormat' => ['rule' => 'checkDateFormat', 'message' => __('Invalid date format', true)],
			    'validateIsOpenedPeriod' => ['rule' => 'validateIsOpenedPeriod', 'message' => __('You can not add, edit, or delete a transaction in this date %s within a closed period', true)],
                ['rule' => 'checkInvoiceDate', 'message' => __('You cannot change the date of the invoice', true)],
		    ],
		    'issue_date' => [
			    ['rule' => 'checkDateFormat', 'message' => __('Invalid date. Date must match date format', true)],
                ['rule' => 'checkInvoiceIssueDate', 'message' => __('You cannot change the issue date of the invoice', true)],
		    ],
		    'due_after' => $positive,
		    'notes' => ['rule' => ['maxLength', 80000], 'message' => __('The maximum number of characters allowed is 8000', true)],
		    'terms' => ['rule' => ['maxLength', 80000], 'message' => __('The maximum number of characters allowed is 8000', true)],
		    'client_email' => [
			    ['rule' => 'isRequiredEmail', 'message' => __('The Email Address is required when the invoicing method for the user is sending it via email', true)],
			    ['rule' => 'email', 'allowEmpty' => true, 'message' => __('Invalid Email', true)],
		    ],
		    'client_currency_code' => ['rule' => 'checkCurrency', 'allowEmpty' => true, 'message' => __('Invalid currency', true)],
		    'name' => ['rule' => 'checkInvoiceName', 'allowEmpty' => true, 'message' => __('Required', true)],
            'branch_id' => array(
                'rule' => 'checkBranchActive',
                'message' => __t('You cannot add a transaction in a suspended branch')
            ),
            'summary_total' => [
                ['rule' => 'checkUnpaidPosInvoice', 'message' => __t("You can't submit this invoice because it is not paid")],
                ['rule' => 'checkOverPaidPosRefund', 'message' => __t("You can't refund more than the amount paid in the original invoice")],
            ]
	    ];

        $orderSourceRequired = settings::getValue(InvoicesPlugin, 'is_order_source_required');
        if ($orderSourceRequired) {
            $this->validate['order_source_id'] = [
                [
                    'rule' => 'notEmpty',
                    'message' => __('Order Soruce is required', true)
                ]
            ];
        }

        if(ifPluginActive(PluginUtil::JORDAN_EINVOICE_PLUGIN)){
            unset($this->full_relations['relations']['SubmitEntityAppData'],$this->full_relations['relations']['SentEntityAppData'],$this->full_relations['relations']['EntityAppData'],$this->full_relations['relations']['ErrorEntityAppData']);
            $this->full_relations['relations']['SubmitEntityAppData'] = ['table' => 'entity_app_data', 'type' => 'hasMany', 'foreign_key' => 'SubmitEntityAppData.entity_id', 'conditions' => 'SubmitEntityAppData.entity_key = "invoice" and SubmitEntityAppData.app_key = "'. EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE.'" and SubmitEntityAppData.action_key = "'. ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT.'"', 'order' => 'SubmitEntityAppData.id DESC'];
            $this->full_relations['relations']['SentEntityAppData'] = ['table' => 'entity_app_data', 'type' => 'hasMany', 'foreign_key' => 'SentEntityAppData.entity_id', 'conditions' => 'SentEntityAppData.entity_key = "invoice" and SentEntityAppData.app_key = "'. EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE.'" and SentEntityAppData.action_key = "'. ElectronicInvoicesActionsUtil::SENT_DOCUMENT.'"', 'order' => 'SentEntityAppData.id DESC'];
        }

        //@todo  do the same with invoices_custom_data
        if(ifPluginActive(PluginUtil::MANUFACTURING_PLUGIN)){
            $this->full_relations['relations']['ProductionPlan'] = ['table' => 'production_plans', 'type' => 'hasMany', 'foreign_key' => 'source_id', 'order' => ' `ProductionPlan`.`id` ASC', 'hide_empty' => true];
        }
    }

    function checkInvoiceDate() {
        $compareWith = date('Y-m-d');

        if($this->data['Invoice']['id']){
            $invoice = $this->findById($this->data['Invoice']['id']);
            $compareWith = $invoice['Invoice']['date'];
        }

        return $this->validateInvoicesDate($this->data['Invoice']['date'] , $compareWith);
    }

    function checkInvoiceIssueDate() {
        $compareWith = date('Y-m-d');
        if($this->data['Invoice']['id']){
            $invoice = $this->findById($this->data['Invoice']['id']);

            if(!empty($invoice['Invoice']['issue_date'])) {
                $compareWith = $invoice['Invoice']['issue_date'];
            }
        }

        return $this->validateInvoicesDate($this->data['Invoice']['issue_date'] , $compareWith);
    }

    function validateInvoicesDate($date , $compareWith) {

        if(in_array($this->data['Invoice']['external_source'], [InvoiceExternalSourceUtil::SOURCE_DESKTOP_POS , InvoiceExternalSourceUtil::SOURCE_MOBILE_POS])){
            return true;
        }
        // For invoices being copied in subscriptions_cron with an older date than today we need to allow this
        if (IS_CRON) {
            return true;
        }
        //this types dont need to check permission INVOICES_EDIT_DATE  user can add booking in future date
        if(in_array($this->data['Invoice']['type'],[self::BOOKING])){
            return true;
        }
        if(isOwner()) return true;
        if(check_permission(INVOICES_EDIT_DATE)) return true;

        $date = strtotime($this->formatDate($date));
        $currentTimestamp = strtotime($compareWith);

        if (abs($currentTimestamp - $date) > (12 * 3600)) {
            return false;
        }
        return true;
    }

    // validates invoices from pos and prevents unpaid or partially paid invoices from being submitted due to logic/js errors from POS angular project
    function checkUnpaidPosInvoice() {
        // Check if POS Plugin is Active
        if (!ifPluginActive(PosPlugin)) {
            return true;
        }
        // Check if pos_partial_payment setting is enabled and yes I don't trust the cache for now
        if (settings::getValue(PosPlugin, 'pos_partial_payment', null, false)) {
            return true;
        }
        // Check if the invoice source is from POS Invoice
        if (!$this->isInvoiceSourcePos($this->data)) {
            return true;
        }
        // Check if It's an Invoice
        if ($this->data['Invoice']['type'] != self::Invoice) {
            return true;
        }
        // Check if Invoice and InvoicePayment exists in the request
        if (!isset($this->data['Invoice']) || !isset($this->data['Payment'])) {
            return false;
        }
        // Check if the payment amount is not equal to the invoice total
        $invoice = $this->data;
        $invoice_total = round($invoice['Invoice']['summary_total'], getAllowedNumberOfDigitsAfterFraction($invoice['Invoice']['currency_code']));
        $payment_total = round($this->sumInvoicePayments($invoice['Payment']), getAllowedNumberOfDigitsAfterFraction($invoice['Invoice']['currency_code']));
        if ($invoice_total - $payment_total > 0.001) {
            return false;
        }
        // Return true as there are no more checks
        return true;
    }

    function checkOverPaidPosRefund() {
        // Check if POS Plugin is Active
        if (!ifPluginActive(PosPlugin)) {
            return true;
        }
        $invoice = $this->data;
        if ($invoice['Invoice']['type'] != self::Refund_Receipt) {
            return true;
        }
        if (empty($invoice['Invoice']['pos_shift_id'])){
            return true;
        }
        $payment_total = round($this->sumInvoicePayments($invoice['Payment']), getAllowedNumberOfDigitsAfterFraction($invoice['Invoice']['currency_code']));
        $original_invoice = $this->getInvoice($invoice['Invoice']['subscription_id'], [], false, -1);
        $original_invoice_paid_amount = round($original_invoice['Invoice']['summary_paid'], getAllowedNumberOfDigitsAfterFraction($invoice['Invoice']['currency_code']));
        if ($payment_total > $original_invoice_paid_amount) {
            return false;
        }
        return true;
    }

    function validate_maximum_discount()
    {
        App::import('Vendor', 'settings');
        $this->loadModel('Staff');
	    $enable_maximum_discount= settings::getValue(InventoryPlugin, "enable_maximum_discount");
	    if (!$enable_maximum_discount) return true;
	    if (isSuperAdmin() || isOwner()) {
		    $maximum_discount = (float) settings::getValue(InventoryPlugin, "maximum_discount_admin");
	    }
	    else {
		    $maximum_discount = (float) getAuthStaff('maximum_general_discount');
	    }
        // Validate Only if there is $maximum_discount
        if (empty($maximum_discount)) {
        	return true;
        }
        // Validate Invoice Items Against $maximum_discount
		if (!$this->validateMaximumDiscountForInvoiceItems($maximum_discount)) {
			return false;
		}
	    // Validate Invoice Total Against $maximum_discount
	    if (!$this->validateMaximumDiscountForInvoice($maximum_discount)) {
		    return false;
	    }
	    return true;
    }

	function validate_invoice_not_negative() {
		if (round($this->data['Invoice']['summary_total'], 5) < 0) return false;
		return true;
	}

    private function validateMaximumDiscountForInvoice($maximum_discount) {
		$invoice_calculated_discount = $this->calculateDiscountPercentage($this->getInvoiceTotalPrice($this->data), $this->getInvoiceTotalPrice($this->data, true));
		return round($maximum_discount, 2) - round($invoice_calculated_discount, 2) >= 0;
    }

    private function validateMaximumDiscountForInvoiceItems($maximum_discount) {
	    foreach ($this->data['InvoiceItem'] as $invoiceItem) {
		    if (isset($invoiceItem['offer_id'])) {
			    continue;
		    }
		    $total_item_price_without_discount = (float)$invoiceItem['unit_price'] * (float)$invoiceItem['quantity'];
		    $total_item_price_with_discount = $total_item_price_without_discount - (float)$invoiceItem['calculated_discount'];
			$discount_percentage_calculated = $this->calculateDiscountPercentage($total_item_price_without_discount, $total_item_price_with_discount);
		    if (round($maximum_discount, 2) - round($discount_percentage_calculated, 2) < 0) {
			    return false;
		    }
	    }
	    return true;
    }

    public function calculateDiscountPercentage($total_price_without_discount, $total_price_with_discount) {
        if ($total_price_without_discount == 0) return false; // return false instead of division by zero to match the same behavior as php5.6
        return (($total_price_without_discount - $total_price_with_discount) / ($total_price_without_discount) * 100);
    }

    private function getInvoiceTotalPrice($invoice_data, $minusDiscount = false) {
		$total = 0;
        $offerDiscount = 0;
		foreach ($invoice_data['InvoiceItem'] as $invoiceItem) {
			if (isset($invoiceItem['offer_id'])) {
				$offerDiscount = $invoiceItem['discount_value'];
                $total += $invoiceItem['item_subtotal'];
			} else {
                $total += (float)$invoiceItem['unit_price'] * (float)$invoiceItem['quantity'];
            }
			if ($minusDiscount) {
				$total -= $invoiceItem['calculated_discount'] - $offerDiscount;
			}
		}
		return $total;
    }

    function getLastClientAction($invoice) {
        if (!is_array($invoice) && is_numeric($invoice))
            $invoice = $this->read(null, $invoice);
        $invoice_actions = array(
            ACTION_ADD_INVOICE_FROM_ESTIMATE => array('title' => __('Created', true), 'priority' => 10),
            ACTION_RECURRING_ADD_INVOICE => array('title' => __('Created', true), 'priority' => 10),
            ACTION_ADD_INVOICE => array('title' => __('Created', true), 'priority' => 10),
            ACTION_ADD_ESTIMATE => array('title' => __('Created', true), 'priority' => 10),
            ACTION_PRINT_INVOICE => array('title' => __('Printed', true), 'priority' => 20),
            ACTION_PRINT_PURCHASE_ORDER => array('title' => __('Printed', true), 'priority' => 20),
            ACTION_PRINT_ESTIMATE => array('title' => __('Printed', true), 'priority' => 20),
            ACTION_SEND_INVOICE => array('title' => __('Sent', true), 'priority' => 30),
            ACTION_SEND_ESTIMATE => array('title' => __('Sent', true), 'priority' => 30),
            ACTION_CLIENT_VIEW_INVIOCE => array('title' => __('Viewed', true), 'priority' => 30),
            ACTION_CLIENT_VIEW_ESTIMATE => array('title' => __('Viewed', true), 'priority' => 30),
            ACTION_CLIENT_PRINT_INVIOCE => array('title' => __('Printed', true), 'priority' => 40),
            ACTION_CLIENT_PRINT_ESTIMATE => array('title' => __('Printed', true), 'priority' => 40),
            //for priority -1 , show bigger date
            ACTION_UPDATE_INVOICE_PAYMENT => array('title' => __('Updated Payment', true), 'priority' => -1),
            ACTION_ADD_INVOICE_PAYMENT => array('title' => __('Added Payment', true), 'priority' => -1),
            ACTION_DELETE_INVOICE_PAYMENT => array('title' => __('Delete Payment', true), 'priority' => -1),
            ACTION_CLIENT_PAY => array('title' => __('Added Payment', true), 'priority' => 50),
            //Sales Orders
            ACTION_ADD_SALES_ORDER => array('title' => __('Created', true), 'priority' => 10),
            ACTION_ADD_SALES_ORDER_FROM_ESTIMATE => array('title' => __('Created', true), 'priority' => 10),
            ACTION_CLIENT_VIEW_SALES_ORDER => array('title' => __('Viewed', true), 'priority' => 30),
            ACTION_PRINT_SALES_ORDER => array('title' => __('Printed', true), 'priority' => 20),
            ACTION_CLIENT_PRINT_SALES_ORDER => array('title' => __('Printed', true), 'priority' => 40),

        );



        $conditions = array('ActionLine.primary_id' => $invoice['Invoice']['id'], 'ActionLine.action_key' => array_keys($invoice_actions));
        $ActionLine = GetObjectOrLoadModel('ActionLine');
        $action_lines = $ActionLine->find('all', array('fields' => array('DISTINCT action_key', 'created', 'staff_id'), 'conditions' => $conditions, 'ORDER' => array('ActionLine.id' => 'DESC')));
        if (empty($action_lines)) {
            $tmp_action = $invoice_actions[ACTION_ADD_INVOICE];
            $tmp_action['ActionLine'] = array('created' => $invoice['Invoice']['created'], 'staff_id' => $invoice['Invoice']['staff_id'], 'action_key' => ACTION_ADD_INVOICE);
            $action_lines[] = $tmp_action;

            if (!empty($invoice['Invoice']['payment_status'])) {

                $tmp_action = $invoice_actions[ACTION_ADD_INVOICE_PAYMENT];
                $tmp_action['ActionLine'] = array('created' => $this->InvoicePayment->field('max(InvoicePayment.modified)', array('InvoicePayment.invoice_id' => $invoice['Invoice']['id'])), 'staff_id' => -10, 'action_key' => ACTION_ADD_INVOICE_PAYMENT);
                $action_lines[] = $tmp_action;
            }

            if (!empty($invoice['Invoice']['last_sent'])) {
                $tmp_action = $invoice_actions[ACTION_SEND_INVOICE];
                $tmp_action['ActionLine'] = array('created' => $invoice['Invoice']['last_sent'], 'staff_id' => -10, 'action_key' => ACTION_SEND_INVOICE);
                $action_lines[] = $tmp_action;
            }
        }
        $actions_list = array();
        $last_action = array('priority' => -2, 'ActionLine' => array('created' => '0000-00-00'));
        foreach ($action_lines as $line) {
            if ($invoice_actions[$line['ActionLine']['action_key']]['priority'] > $last_action['priority'] && $last_action['priority'] != -1) {
                $last_action = $invoice_actions[$line['ActionLine']['action_key']];
                $last_action['ActionLine'] = $line['ActionLine'];
            } else if (($invoice_actions[$line['ActionLine']['action_key']]['priority'] == -1 || $last_action['priority'] == -1 || $invoice_actions[$line['ActionLine']['action_key']]['priority'] == $last_action['priority']) && $line['ActionLine']['created'] >= $last_action['ActionLine']['created']) {
                $last_action = $invoice_actions[$line['ActionLine']['action_key']];
                $last_action['ActionLine'] = $line['ActionLine'];
            }
        }

        return $last_action;
    }

    function isRequiredEmail() {

        if (empty($this->data[$this->alias]['is_offline']) && empty($this->data[$this->alias]['client_email'])) {
            return false;
        }
        return true;
    }

    public static function getMethods() {
        return array(
            0 => __('Send via Email', true),
            1 => __('Print (Offline)', true),
        );
    }

    public static function getPaymentStatuses() {
        return array(
            INVOICE_STATUS_UNPAID => __('Unpaid', true),
            INVOICE_STATUS_PARTIAL_PAID => __('Partially Paid', true),
            INVOICE_STATUS_PAID => __('Paid', true),
            INVOICE_STATUS_REFUNDED => __('Refunded', true),
            INVOICE_STATUS_PARTIAL_REFUND => __('Partially Refunded', true),
            INVOICE_STATUS_OVERPID => __('Overpaid', true),
            INVOICE_STATUS_DRAFT => __('Draft', true),
            INVOICE_STATUS_DUE => __('Due', true),
            INVOICE_STATUS_OverDue => __('OverDue', true),
        );
    }

    public static function getTaxesList() {
        return array(
            0 => __('No Taxes', true),
            1 => __('Tax 1', true),
            2 => __('Tax 2', true),
            3 => __('Both', true),
        );
    }
    public static function getInvoiceTypeList($pluralize=false) {
        if($pluralize==false){
        return array(
            self::Credit_Note => __('Credit Note', true),
            self::Refund_Receipt => __('Refund Receipt', true),
            self::Invoice => __('Invoice', true),
            self::Estimate => __('Estimate', true),
            self::BOOKING => __('Booking', true),
            self::SALES_ORDER => __('Sales Order', true),
            self::DEBIT_NOTE => __('Debit Note', true),
            self::ADVANCE_PAYMENT => __('Invoice', true),
        );
        }else{
        return array(
            self::Credit_Note => __('Credit notes', true),
            self::Refund_Receipt => __('Refund Receipts', true),
            self::Invoice => __('Invoices', true),
            self::Estimate => __('Estimates', true),
            self::BOOKING => __('Bookings', true),
            self::SALES_ORDER => __('Sales Order', true),
            self::DEBIT_NOTE => __('Debit notes', true),
            self::ADVANCE_PAYMENT => __('Invoices', true),
        );
        }
    }
    public static function getInvoiceActionList() {
        return array(
            self::Credit_Note => 'creditnotes',
            self::Refund_Receipt => 'refund',
            self::Invoice =>'index',
            self::ADVANCE_PAYMENT =>'index',
            self::Estimate => 'estimates',
            self::BOOKING => 'Bookings',
            self::SALES_ORDER => 'sales_orders',
            self::DEBIT_NOTE => 'index',
        );
    }
    public static function getInvoiceViewActionList() {
        return array(
            self::Credit_Note => 'view_creditnote',
            self::Refund_Receipt => 'view_refund',
            self::Invoice =>'view',
            self::Estimate => 'view_estimate',
            self::BOOKING => 'view_booking',
            self::Subscription => 'view_subscription',
            self::SALES_ORDER => 'view_sales_order',
            self::DEBIT_NOTE => 'view_debitnote',
            self::ADVANCE_PAYMENT => 'view_advance_payment',
        );
    }

    public static function getInvoiceEditActionList() {
        return array(
            self::Credit_Note => 'edit_creditnote',
            self::Refund_Receipt => 'edit_refund',
            self::Invoice =>'edit',
            self::Estimate => 'edit_estimate',
            self::BOOKING => 'edit_booking',
            self::Subscription => 'edit_subscription',
            self::SALES_ORDER => 'edit_sales_order',
            self::DEBIT_NOTE => 'edit_debitnote',
        );
    }

    public static function getRemindersWhenList() {
        return array(
            0 => __('On issue date', true),
            1 => __('On invoice date', true),
            2 => __('On due date', true),
            3 => __('Before invoice date (in days)', true),
            4 => __('After invoice date (in days)', true),
        );
    }

    public static function getSubscriptionUnits() {
        return array(
            'days' => __('Days', true),
            'weeks' => __('Weeks', true),
            'months' => __('Months', true),
            'years' => __('Years', true),
        );
    }

    public function getNextInvoiceNo($site_id = 0) {
        if (!$site_id) {
            $site_id = getAuthOwner('id');
        }
//		$lastInvoice = $this->find('first', array('order' => 'Invoice.no + 0 DESC', 'fields' => 'Invoice.no', 'recursive' => -1, 'conditions' => array('Invoice.site_id' => intval($site_id), 'Invoice.type' => 0)));
//		return intval($lastInvoice['Invoice']['no']) + 1;

        $dbSite = $this->Site->find('first', array('conditions' => array('Site.id' => $site_id), 'recursive' => -1, 'callbacks' => false, 'fields' => 'next_invoice_number'));
		
        return $dbSite['Site']['next_invoice_number'];
    }

    public function getNextEstimateNo($site_id = 0) {
        if (!$site_id) {
            $site_id = getAuthOwner('id');
        }
        $lastInvoice = $this->find('first', array('order' => 'Invoice.no + 0 DESC', 'fields' => 'Invoice.no', 'recursive' => -1, 'conditions' => array('Invoice.site_id' => intval($site_id), 'Invoice.type' => 3)));
        $invoiceNo = $this->getNextInvoiceNo($site_id);
        $pad = strlen($invoiceNo);
        return sprintf("%0{$pad}d", intval($lastInvoice['Invoice']['no']) + 1);
    }

    public function getNextSalesOrderNo($site_id = 0) {
        if (!$site_id) {
            $site_id = getAuthOwner('id');
        }
        $lastInvoice = $this->find('first', array('order' => 'Invoice.no + 0 DESC', 'fields' => 'Invoice.no', 'recursive' => -1, 'conditions' => array('Invoice.site_id' => intval($site_id), 'Invoice.type' => 12)));
        $invoiceNo = $this->getNextInvoiceNo($site_id);
        $pad = strlen($invoiceNo);
        return sprintf("%0{$pad}d", intval($lastInvoice['Invoice']['no']) + 1);
    }

    function getHash($invoice) {
        if (isset($invoice['Invoice'])) {
            $invoice = $invoice['Invoice'];
            $invoice['site_id'] = $invoice['site_id'] ?? null;
            $invoice['client_id'] = $invoice['client_id'] ?? null;
            $invoice['id'] = $invoice['id'] ?? null;
        }
        return md5($invoice['site_id'] . '#' . $invoice['client_id'] . '#' . $invoice['id']);
    }

    function getClientLink($invoice)
    {
        if(!is_array($invoice)&&is_numeric($invoice))
            $invoice= $this->findById($invoice);
        if(isset($invoice['Invoice']))  $invoice=$invoice['Invoice'];
        if(empty($invoice)) return false;
        $hash =$this->getHash($invoice);
        $site=getCurrentSite();
        $client_view_link = 'https://' . $site['subdomain'] .'/invoices/view/' . $invoice['id'] . '?hash=' . $hash;
        return $client_view_link;
    }

		/**
		 * Returns the QR Code Image URL for the specified invoice or false on failure
		 * @param $invoice - the invoice object from getInvoice
		 * @return false|string
		 */
		public static function getInvoiceQrCodeImageUrl($host, $invoice) {
		if (!isset($invoice['Invoice']) && empty($invoice)) {
			return false;
		}
		if (empty($invoice)) {
			return false;
		}
		$data = Invoice::get_kas_invoice_qr_data($invoice);
		if (empty($data)) {
			return false;
		}
		return "https://" . $host . "/qr/?d64=$data";
	}
		/**
		 * Returns the Client Invoice Preview URL or false on failure
		 * @param $invoice - the invoice object from getInvoice
		 * @return false|string
		 */
	public static function getInvoiceClientPreviewUrl($host, $invoice) {
		$hash = GetObjectOrLoadModel('Invoice')->getHash($invoice);
		$invoice_id = $invoice['Invoice']['id'] ?? null;
		return "https://$host/invoices/preview/$invoice_id?hash=$hash";
	}

	/**
	 * Returns the Client Invoice PDF URL or false on failure
	 * @param $invoice - the invoice object from getInvoice
	 * @return false|string
	 */
	public static function getInvoiceClientPdfUrl($host, $invoice) {
		$hash = GetObjectOrLoadModel('Invoice')->getHash($invoice);
		$invoice_id = $invoice['Invoice']['id'] ?? null;
		return "https://$host/invoices/view/$invoice_id.pdf?hash=$hash";
	}

    public static function get_kas_invoice_qr_data($invoice)
    {
        if(isset($invoice['Invoice']['draft']) &&$invoice['Invoice']['draft']) {
            return;
        }
        $InvoiceTax=GetObjectOrLoadModel('InvoiceTax');
        $site=getCurrentSite();
        $invoice_taxes=[];
        if (!isset($invoice['InvoiceTax']) && !empty($invoice['Invoice']['id'])) {
            $invoice_taxes = $InvoiceTax->find('all', array('recursive' => -1, 'conditions' => array('InvoiceTax.invoice_id' => $invoice['Invoice']['id'])));
            foreach ($invoice_taxes as $invoice_tax) {
                $invoice['InvoiceTax'][] = $invoice_tax['InvoiceTax'];
            }
        }
        $invoice['Invoice']['date'] = $invoice['Invoice']['date'] ?? null;
        $invoice['Invoice']['created'] = $invoice['Invoice']['created'] ?? null;
        $invoice['Invoice']['summary_total'] = $invoice['Invoice']['summary_total'] ?? null;
        $tax_id=$site['bn1'];

        $invoiceDate = (empty($invoice['Invoice']['original_date']) ? $invoice['Invoice']['date'] : $invoice['Invoice']['original_date']);
        $invoiceTime = date('H:i:s',strtotime($invoice['Invoice']['created']));
        $invoiceDateTime = self::generateDateTimeInZuluFormat($invoiceDate, $invoiceTime);
        $business_name=$site['business_name'];
        if(ifPluginActive(BranchesPlugin))
        {
            $Branch = GetObjectOrLoadModel('Branch');
            $count = $Branch->find('count', ['conditions' => ['Branch.status' => 1]]);
            if ($count >= 2 && settings::getValue(PluginUtil::EINVOICE_PLUGIN, 'use_branch_name')) {
                $business_name = getCurrentBranch('name');
            }
            $alter_business_name = settings::getValue(0, 'branch_business_name_'.$invoice['Invoice']['branch_id']);
            $alter_tax_id= settings::getValue(0, 'branch_bn1_'.$invoice['Invoice']['branch_id']);
            if(!empty($alter_business_name)) $business_name=$alter_business_name;
            if(!empty($alter_tax_id)) $tax_id=$alter_tax_id;
        }
        $tags=
            [
                1=>$business_name,
                2=> $tax_id,
                3=>$invoiceDateTime,
                4=>round($invoice['Invoice']['summary_total'],2),
                5=>0,
            ];
        // warning suppress
    if (isset($invoice['InvoiceTax'])) {
        foreach ( $invoice['InvoiceTax'] as $tax)
        {
            $tags[5]+=$tax['invoice_value'];
        }
    }
    if(!empty($invoice['Invoice']['currency_code'])&&$invoice['Invoice']['currency_code']!='SAR')
    {
        App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
        $rate= CurrencyConverter::index($invoice['Invoice']['currency_code'], 'SAR',$invoice['Invoice']['date']);
        $tags[4]=$rate*$tags[4];
        $tags[5]=$rate*$tags[5];

    }
        $tags[4]=str_replace(',', '', number_format($tags[4], 2));
        $tags[5]=round($tags[5],2);
        $AppData = GetObjectOrLoadModel('EntityAppData');
        $entityAppdata = $AppData->find('first', ['conditions' => [
            'EntityAppData.entity_id' => $invoice['Invoice']['id'],
            'EntityAppData.entity_key' => 'invoice',
            'EntityAppData.action_key' => ElectronicInvoicesActionsUtil::SIGNED_INVOICE


        ]]);

        $signedInvoice = json_decode($entityAppdata["EntityAppData"]["data"], true)["signed_invoice"] ?? null;
        if (!empty($signedInvoice)) {
            $re = '/<ds:DigestValue>(.*?)<\/ds:DigestValue>/m';
            preg_match_all($re, $signedInvoice, $matches, PREG_SET_ORDER, 0);
            $tags[6] = $matches[1][1];
            $tags[7] = $matches[0][1];
            $re = '/ <cbc:UUID>(.+?)<\/cbc:UUID>/m';
            preg_match($re, $signedInvoice, $matches);
            $tags[8] = $matches[1];
        }
        return  self::toTLV($tags);
    }

    private static function generateDateTimeInZuluFormat($date, $time)
    {
        $dateTime = $date . ' ' . $time;
        $tz = new DateTimeZone('Asia/Riyadh');

        try {
            $originalDatetime = new DateTime($dateTime, $tz);
        } catch (Exception $e) {
            /* in preview invoice */
            $dataDateFormat = getCurrentSite('date_format');
            $dateFormats = getDateFormats('std');
            $format = $dateFormats[$dataDateFormat] . ' ' . 'H:i:s';

            $originalDatetime = DateTime::createFromFormat($format, $dateTime, $tz);

            if (!$originalDatetime) {
                $format = 'Y-m-d H:i:s';
                $originalDatetime = DateTime::createFromFormat($format, date($format));
            }
        }

        // Convert the DateTime object to UTC
//        $originalDatetime->setTimezone(new DateTimeZone('UTC'));

        // Format the DateTime object as a Zulu ISO8601 datetime string
        return $originalDatetime->format('Y-m-d\TH:i:s\Z');
    }

    private function raw($tags)
    {
        $data = '';

        foreach( $tags as $value) {
            $data .= " $value";
        }

        return base64_encode($data);
    }

    private static function toTLV($tags)
    {
        $data = '';

        foreach ($tags as $b => $value) {
            $value = (string) $value;
            $data .= self::toHex($b) . self::toHex(strlen($value)).($value);
        }

        return base64_encode(base64_encode($data));
    }

    private static function toHex($value)
    {
        return pack('H*', sprintf('%02X', $value));
    }

    public function delete_related_items($invoice_id = false) {
        if (empty($invoice_id)) {
            return false;
        }

        $ids = $invoice_id;

        $this->InvoiceItem->deleteAll(array('InvoiceItem.invoice_id' => $ids));
        $payments = $this->InvoicePayment->find('list', array('conditions' => array("InvoicePayment.invoice_id" => $ids)));
        foreach($payments as $payment)
        {
            $this->InvoicePayment->delete_auto_journals($payment);
        }
        $this->InvoicePayment->deleteAll(array('InvoicePayment.invoice_id' => $ids));
        $this->InvoiceReminder->deleteAll(array('InvoiceReminder.invoice_id' => $ids));
        $this->InvoiceTax->deleteAll(array('InvoiceTax.invoice_id' => $ids));
        $this->InvoiceCustomField->deleteAll(array('InvoiceCustomField.invoice_id' => $ids));
        $this->delete_auto_journals($invoice_id);
        $this->delete_cost_journals($invoice_id);
        $this->delete_with_related($ids);
        if (ifPluginActive(COMMISSION_PLUGIN) || ifPluginInstalled(COMMISSION_PLUGIN)) {
            $commissionsToDelete = [];
            foreach ($ids as $invoiceID) {
                $commissions = $this->hasSalesCommissions($invoiceID);
                foreach($commissions as $commission){
                    $commissionsToDelete [] = $commission['Commission']['id'];
                }
            }
            $this->deleteRelatedSalesCommissions($commissionsToDelete);
        }
	    if (ifPluginActive(CREDIT_PLUGIN)) {
		    $this->suspend_invoice_credit_charges($ids);
	    }

	    return true;
    }


    public function delete_related_invoices($invoice)
    {
        if (empty($invoice)) {
            return false;
        }
        if ($invoice['Invoice']['type'] == Invoice::Invoice) {
            $this->RelatedInvoice->deleteAll(array('RelatedInvoice.invoice_id' => $invoice['Invoice']['id']));
        } elseif ($invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT) {
            $this->RelatedInvoice->deleteAll(array('RelatedInvoice.related_invoice_id' => $invoice['Invoice']['id']));
        }
    }

	/**
	 * @param $invoice_ids
	 */
	public function suspend_invoice_credit_charges($invoice_ids) {
		$this->loadModel('CreditCharge');
		$credit_charges = $this->CreditCharge->getChargesByInvoiceID($invoice_ids);

		$url = '/v2/api/credit_charges/suspend';
		App::import('Component', 'ApiRequestsComponent');
		$apiRequests = new ApiRequestsComponent();
		$apiRequests->request($url,false,'POST', ['ids' => array_keys($credit_charges), 'isAutomatic' => true]);

		$statusCode = $apiRequests->getStatusCode();
		if ($statusCode < 200 || $statusCode >= 400){
			$data = [];
			foreach ($credit_charges as $key => $value){
				$data[] = [
					'CreditCharge' => [
						'id' => $key,
						'status' => CreditCharge::STATUS_SUSPENDED,
						'suspension_type' => CreditCharge::SUSPENSION_TYPE_AUTO
					]
				];
			}
			$this->CreditCharge->saveAll($data);
		}
    }

    /**
     * @param $invoiceData
     * @param $invoiceTotals
     * @return bool|mixed
     * checks if the clinet has credit if he has it returns true else it returns error message
     */
    function checkInvoiceClientCredit($invoiceData, $invoiceTotals, $oldInvoice = null)
    {
        $data = $invoiceData;
        $client_settings = settings::getPluginValues(ClientsPlugin);
        $result = true;
        $isPosShiftNotPaid = !isset($data['Payment'][0]['pos_shift_id']);
        $isInvoicePaid = !empty($data['Payment']['is_paid']);
        $invoice_total = round($data['Invoice']['summary_total'], getAllowedNumberOfDigitsAfterFraction($data['Invoice']['currency_code']));
        $payment_total = $isInvoicePaid ? $invoice_total : round($this->sumInvoicePayments($data['Payment']), getAllowedNumberOfDigitsAfterFraction($data['Invoice']['currency_code']));
        $isInvoice = $data['Invoice']['type'] == Invoice::Invoice;
        $isEnableCredit = $client_settings['enable_client_credit_limit'] == 1;
        $isPartialPaidInvoice = $invoice_total > $payment_total;
        $isPaidAndNotPos = $data['Payment']['is_paid'] == 0 && $isPosShiftNotPaid;
        if($isInvoice && $isEnableCredit && ($isPartialPaidInvoice || $isPaidAndNotPos )){
            $credit_amount = $invoice_total - $payment_total;
            $oldAmount = $oldInvoice ? $oldInvoice['Invoice']['summary_unpaid'] : 0;
            $this->loadModel('Journal');
            if($this->Journal->is_client_over_credited($data['Invoice']['client_id'],$credit_amount, $data['Invoice']['currency_code'], false, $oldAmount))
            {
                $result = __('Client passed the credit limit',true);
            }
        }
        if(!empty($data))
        {
            if($isInvoice && $client_settings['client_credit_period'] == 1 && $data['Payment']['is_paid'] == 0){

                $invoice_date = $data['Invoice']['date'];
                $this->loadModel('Journal');
                if($this->Journal->is_client_over_credit_period($data['Invoice']['client_id'],$invoice_date, false, ($oldInvoice ? $oldInvoice['Invoice']['id'] : null)))
                {
                    $result = __('Client passed the credit period',true);

                }
            }
        }
        return $result;

    }


    function initLoyaltyPoints($data) {
        if (
            ifPluginActive(CLIENT_LOYALTY_PLUGIN) && 
            check_permission(REDEEM_LOYALTY_POINTS) && 
            $data['Invoice']['type'] == self::Invoice && 
            !empty(Settings::getValue(CLIENT_LOYALTY_PLUGIN, "client_loyalty_credit_type")) &&
            $data['Invoice']['draft'] == false
        ) {
            $points = $data['Invoice']['points'];
            $client_id = $data['Invoice']['client_id'];
            $date = strtotime($this->formatDate($data['Invoice']['date'], getCurrentSite('date_format')));
            $credit_type_id = Settings::getValue(CLIENT_LOYALTY_PLUGIN, "client_loyalty_credit_type");
            $minimum_redemption_points = Settings::getValue(CLIENT_LOYALTY_PLUGIN, "loyalty_minimum_redemption_points");
            $client_loyalty_conversion_factor = Settings::getValue(CLIENT_LOYALTY_PLUGIN, 'client_loyalty_conversion_factor');

            $creditChargeRepo = new CreditChargeRepository();
            $credit_usage_repo = new CreditUsageRepository();

            $chargeUsages = [];
            if(!empty($data['Invoice']['id'])){
                $chargeUsagesByInvoiceId= $credit_usage_repo->getChargeUsagesByInvoiceId($data['Invoice']['id']);
                $chargeUsages = array_map(function($charge){
                    return $charge['charge_usages']['id'];
                },$chargeUsagesByInvoiceId);
            }

            $creditCharges = $creditChargeRepo->getClientCreditCharges($client_id, $credit_type_id, date("Y:m:d H:i:s", $date), $chargeUsages);
        
            $creditChargesMeta = $credit_usage_repo->creditChargesMeta($creditCharges, $minimum_redemption_points);

            if(isset($data['Invoice']['points']) && $data['Invoice']['points'] > 0) {
                $actual_points = isset($data['Invoice']['actual_points']) ? $data['Invoice']['actual_points'] : $data['Invoice']['points'];
                $discount_amount = $credit_usage_repo->calculateDiscountAmount($actual_points, $client_loyalty_conversion_factor, $this->data['Invoice']['client_default_currency_code'], date("Y:m:d H:i:s", $date));
                $data['Invoice']['discount_amount'] = $discount_amount;
            }
  
            $hasLoyaltyPointDiscount = true;
            unset($data['Invoice']['points']);

            return [
               'hasLoyaltyPointDiscount' => $hasLoyaltyPointDiscount,
               'creditChargeRepo' => $creditChargeRepo,
               'credit_usage_repo' => $credit_usage_repo,
               'creditChargesMeta' => $creditChargesMeta,
               'credit_type_id' => $credit_type_id,
               'discount_amount' => $discount_amount,
               'data' => $data,
               'points' => $points,
               'date' => $date
            ];
        }

        return false;

    }


    function updateInvoice($data, $type = false,$orginal_invoice=null, $save_payment_only = false) {

        $errors = [];
        /**
         * Remove the stored columns in case they exist to be moved later ISA
         */
        if (isset($data['Invoice']['stored_due_date'])) {
			unset($data['Invoice']['stored_due_date']);
		}
        if ($data['Invoice']['invoice_layout_id'] == 0) {
            if (check_permission(Edit_General_Settings)) {
                App::import('Vendor', 'settings');
                settings::setValue(InvoicesPlugin, 'initial_invoice_custom_fields', json_encode($data['InvoiceCustomField']));
            }
        }
		if (empty($data['Invoice']['shipping_amount'])) {
			unset($data['Invoice']['tax_shipping_id']);
		}
        if($orginal_invoice==null) {
            $invoice = $this->getInvoice($data['Invoice']['id']);
        }else{
            $invoice=$orginal_invoice;
        }
        if (!$invoice) {
            return array('status' => false);
        }
        if (IS_REST && !empty($data['Invoice']['branch_id'])) {
            setRequestCurrentBranch($data['Invoice']['branch_id']);
        }
        if (empty($data['Invoice']['currency_code'])) {
            $data['Invoice']['currency_code'] = getCurrentSite('currency_code');
        }
        if ($invoice['Invoice']['type'] == self::Subscription) {
            $this->validate['name'] = array('rule' => 'notEmpty', 'message' => __('Required', true));
            $this->validate['subscription_period'] = array(array('rule' => 'numeric', 'message' => __('Valid number required', true), 'allowEmpty' => false), array('rule' => array('comparison', '>=', 0), 'message' => __('Positive number required', true)));
            $this->validate['subscription_unit'] = array('rule' => array('inList', array_keys(self::getSubscriptionUnits()), 'message' => __('Invalid unit', true), 'allowEmpty' => false));
            $this->validate['subscription_max_repeat'] = array(array('rule' => 'numeric', 'message' => __('Valid number required', true), 'allowEmpty' => true), array('rule' => array('comparison', '>=', 0), 'message' => __('Positive number required', true), 'allowEmpty' => true));
            unset($this->validate['no']);
            $data['Invoice']['type'] = self::Subscription;
            $data['Invoice']['no'] = '';
        } elseif ($invoice['Invoice']['type'] == Invoice::Credit_Note) {
            $data['Invoice']['type'] = self::Credit_Note;
        }
        elseif ($invoice['Invoice']['type'] == Invoice::DEBIT_NOTE) {
            $data['Invoice']['type'] = self::DEBIT_NOTE;
        }
        elseif ($invoice['Invoice']['type'] == Invoice::Resellers) {
            $data['Invoice']['type'] = self::Resellers;
        } elseif ($invoice['Invoice']['type'] == Invoice::Refund_Receipt) {
            $data['Invoice']['type'] = self::Refund_Receipt;
        } elseif ($type == 1 || $type == 'template') {
            $this->validate['name'] = array('rule' => 'notEmpty', 'message' => __('Required', true));

            unset($this->validate['no'], $this->validate['client_id'], $this->validate['client_first_name'], $this->validate['client_last_name'], $this->validate['client_email'], $this->validate['client_country_code'], $this->validate['client_currency_code']);
            $data['Invoice']['type'] = self::Template;
            $data['Invoice']['no'] = '';
        } elseif ($type == self::Estimate) {
            unset($this->validate['issue_date']);
            unset($this->validate['date'], $this->validate['deposit'], $this->validate['due_after'], $this->validate['terms']);
            $data['Invoice']['type'] = self::Estimate;
//			$data['Invoice']['draft'] = 1;
        } elseif ($type == self::SALES_ORDER) {
            unset($this->validate['issue_date']);
            unset($this->validate['date'], $this->validate['deposit'], $this->validate['due_after'], $this->validate['terms']);
            $data['Invoice']['type'] = self::SALES_ORDER;
        } elseif ($type === Invoice::INSURANCE_INVOICE || $subscription === Invoice::INSURANCE_REFUND) {
            $data['Invoice']['type'] = $type;
            unset($this->validate['client_id'], $this->validate['client_business_name'], $this->validate['client_first_name']);
            unset($this->validate['client_last_name'], $this->validate['client_email'], $this->validate['issue_date']);
        }elseif ($type == self::DEBIT_NOTE){
            $data['Invoice']['type'] = self::DEBIT_NOTE;
        } elseif ($invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT) {
            $data['Invoice']['type'] = Invoice::ADVANCE_PAYMENT;
        } else if(empty($data['Invoice']['type'])){
            $data['Invoice']['type'] = $type;
        }

        if (empty($data['Invoice']['due_after'])) {
            $data['Invoice']['due_after'] = 0;
        }

        if (!$data['Client']['client_from_data']) {
            $data['Client'] = $this->Client->find(array('Client.id' => $data['Invoice']['client_id']));
        }

        if ($data['Invoice']['shipping_options'] == "3") {
            $row = $this->Client->find(array('Client.id' => $data['Invoice']['client_id']));
            $data['Invoice']['secondary_address1'] = $row['Client']['secondary_address1'];
            $data['Invoice']['secondary_address1'] = $row['Client']['secondary_address2'];
            $data['Invoice']['secondary_city'] = $row['Client']['secondary_city'];
            $data['Invoice']['secondary_state'] = $row['Client']['secondary_state'];
            $data['Invoice']['secondary_postal_code'] = $row['Client']['secondary_postal_code'];
            $data['Invoice']['secondary_country_code'] = $row['Client']['secondary_country_code'];
        }
        if (!empty($data['Client'])) {
	        foreach ($data['Client'] as $key => $value) {
		        if ($key == 'id') {
			        continue;
		        }
		        // This conditions prevents Address of the client from being overwritten incase it's an invoice from POS (We send address data with POS now)
		        if (!empty($data['Invoice']['pos_shift_id']) && in_array($key, ['address1', 'address2', 'state', 'city', 'postal_code', 'address_id', 'country_code'])) {
			        continue;
		        }
		        $data['Invoice']['client_' . $key] = $value;
	        }
        }

        if (!empty($data['Client']['id'])) {
            unset($data['Client']);
        }
        $client_id = $data['Invoice']['client_id'];
        ///This is to update the invoice item columns in case there is a new invoice layout assigned to the invoice
        $InvoiceLayout=GetObjectOrLoadModel('InvoiceLayout');
        if($invoice['Invoice']['invoice_layout_id'] != $data['Invoice']['invoice_layout_id'] || empty($data['Invoice']['item_columns'])) {
            $invoiceLayoutsColumns = $InvoiceLayout->find('first', array('conditions'=>array('InvoiceLayout.id'=>$data['Invoice']['invoice_layout_id'])));
            $data['Invoice']['item_columns'] = $invoiceLayoutsColumns['InvoiceLayout']['item_columns'];
        }

        $loyaltyPointsResult = $this->initLoyaltyPoints($data);

        if($loyaltyPointsResult) {
            $data = $loyaltyPointsResult['data'];
            $creditChargesMeta = $loyaltyPointsResult['creditChargesMeta'];
            $hasLoyaltyPointDiscount = $loyaltyPointsResult['hasLoyaltyPointDiscount'];
            $credit_usage_repo = $loyaltyPointsResult['credit_usage_repo'];
            $points = $loyaltyPointsResult['points'];
            $credit_type_id = $loyaltyPointsResult['credit_type_id'];
        }

        if($data['Invoice']['type'] == self::Refund_Receipt && ifPluginInstalled(PluginUtil::CLIENT_LOYALTY)) {
            $credit_usage_repo = new CreditUsageRepository();
            $creditUsageData = $credit_usage_repo->getCreditUsageByInvoice($data['Invoice']['subscription_id']);
            if (!empty($creditUsageData["CreditUsage"]["id"])) {
                $this->applyRefundDiscountForLoyalty($data);
            }
        }

        if(strtotime($invoice['Invoice']['created']) > strtotime("-10 minutes")) {
            $data   = $this->commonSaveOperations($data);
        }

        $totals = $this->calculateTotals($data);

        if(round( $totals['invoiceTotal'],4 )< 0) {
            $errors['invoiceTotal'] =  __('You cannot save the transaction with negative total amount', true);
            $this->validationErrors = array_merge($this->validationErrors, $errors);
        }

        $data['Invoice']['item_discount_amount'] = $totals['total_item_discount'];
        if ($data['Invoice']['deposit_type'] == self::DEPOSIT_TYPE_PAID) {
            $data['Invoice']['deposit_type'] = self::DEPOSIT_TYPE_UNPAID;
            $data['Invoice']['deposit'] = $totals['invoiceDeposit'];
        }


        $data['InvoiceItem'] = $totals['InvoiceItems'];

        $data['InvoiceTax'] = $totals['taxes'];
        $data['Invoice']['summary_discount'] = $totals['invoiceDiscount'];

        $data['Invoice']['summary_total'] = $totals['invoiceTotal'];

        $data['Invoice']['summary_subtotal'] = $totals['invoiceSubtotal'];

            $refunds=$this->get_refund_total($invoice['Invoice']['id']);   
            
          
        $newUnpaid = $totals['invoiceTotal'] - $invoice['Invoice']['summary_paid']-$refunds;
       
        if ($newUnpaid < 0 && $totals['invoiceTotal'] >= 0) {

            $data['Invoice']['status'] = 2;
        } elseif ($newUnpaid == 0) {
            
            $data['Invoice']['status'] = 2;
        }
        // Calc refunds and save them ....
        $data['Invoice']['summary_refund']=$refunds;
        $data['Invoice']['summary_unpaid'] = $newUnpaid;
        
        if (isset($totals['invoicePaid']) && $totals['invoicePaid'] > 0.01) {
            $data['Invoice']['summary_deposit'] = $newUnpaid;
        } else {
            $data['Invoice']['summary_deposit'] = $totals['invoiceDeposit'];
        }
		App::import('Vendor', 'AutoNumber');
        if(!$data['Invoice']['draft']) {
            $updateAutoNumber = true;
			// We edited this to get new invoice no for old invoices which had "draft" in their invoice no but is not draft anymore
            if (empty($data['Invoice']['no']) || (!empty($invoice['Invoice']['no']) && ($invoice['Invoice']['draft'] || strpos($invoice['Invoice']['no'], 'draft') !== false))){
                switch ($data['Invoice']['type']) {
                    case Invoice::Invoice:
                        \AutoNumber::set_validate(\AutoNumber::TYPE_INVOICE);
                        $data['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE);
                        break;
                    case Invoice::Estimate:
                        \AutoNumber::set_validate(\AutoNumber::TYPE_ESTIMATE);
                        $data['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_ESTIMATE);
                        break;
                    case Invoice::SALES_ORDER:
                        \AutoNumber::set_validate(\AutoNumber::TYPE_SALES_ORDER);
                        $data['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_SALES_ORDER);
                        break;
                    case Invoice::BOOKING:
                        \AutoNumber::set_validate(\AutoNumber::TYPE_BOOKING);
                        $data['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_BOOKING);
                        break;
                    case Invoice::Credit_Note:
                        \AutoNumber::set_validate(\AutoNumber::TYPE_CREDIT_NOTE);
                        $data['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_CREDIT_NOTE);
                        break;
                    case Invoice::DEBIT_NOTE:
                        \AutoNumber::set_validate(\AutoNumber::TYPE_SALES_DEBIT_NOTE);
                        $data['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_SALES_DEBIT_NOTE);
                        break;
                    case Invoice::Refund_Receipt:
                        \AutoNumber::set_validate(\AutoNumber::TYPE_REFUND_RECEIPT);
                        $data['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_REFUND_RECEIPT);
                        break;
                    case Invoice::ADVANCE_PAYMENT:
                        \AutoNumber::set_validate(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
                        $data['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
                        break;
                    default:
                        break;
                }
                $generated_number = true;
            } else {
				// This is needed since the updateInvoice is something called twice with the same $this->data without updating it  after saving for normal invoice and another one for CustomForms?
                // and also we will take the number only if user not change the autonumber of invoice, if he changed  it we need to take the new value
                if ($data['Invoice']['no'] == $data['Invoice']['hidden_no']) {
                    $data['Invoice']['no'] = $invoice['Invoice']['no'];
                }
				$data['Invoice']['draft'] = $invoice['Invoice']['draft'];
            }
            switch ($data['Invoice']['type']) {
                case Invoice::Invoice:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_INVOICE);
                    break;
                case Invoice::Estimate:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_ESTIMATE);
                    break;
                case Invoice::SALES_ORDER:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_SALES_ORDER);
                    break;
                case Invoice::BOOKING:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_BOOKING);
                    break;
                case Invoice::Credit_Note:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_CREDIT_NOTE);
                    break;
                case Invoice::DEBIT_NOTE:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_SALES_DEBIT_NOTE);
                    break;
                case Invoice::Refund_Receipt:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_REFUND_RECEIPT);
                    break;
                case Invoice::ADVANCE_PAYMENT:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
                    break;
                default:
                    break;
            }
        } else {
            $updateAutoNumber = false;
            if(($data['Invoice']['draft'] && !str_contains('draft-',$data['Invoice']['no']))){
                $data['Invoice']['no'] = $data['Invoice']['no'];
            }else{
                $data['Invoice']['no'] = 'draft-'.$data['Invoice']['id'];
            }
        }
        $this->set($data);
            
        $dateFormats = getDateFormats('mysql');
        $format = $dateFormats[getCurrentSite('date_format')];
        $data['Invoice']['date'] = $this->formatDate($data['Invoice']['date'], $format);
        if (!empty($data['Invoice']['from']))
            $data['Invoice']['from'] = $this->formatDate($data['Invoice']['from']);
        if (!empty($data['Invoice']['to']))
            $data['Invoice']['to'] = $this->formatDate($data['Invoice']['to']);

		
        if (!in_array($invoice['Invoice']['type'], [self::Estimate, self::SALES_ORDER])) {
            $data['Invoice']['issue_date'] = $this->formatDate($data['Invoice']['issue_date']);
        }

        if (!empty($data['InvoiceReminder'])) {
            foreach ($data['InvoiceReminder'] as &$reminder) {
                switch ($reminder['send_when']) {
                    case 0:
                        $date = strtotime($this->formatDate($data['Invoice']['date'], getCurrentSite('date_format')));
                        $issueDate = strtotime($this->formatDate($data['Invoice']['issue_date'], getCurrentSite('date_format')));
                        $diff = ($date - $issueDate) / 86400;
                        $reminder['days'] = -$diff;
                        break;
                    case 1:
                        $reminder['days'] = 0;
                        break;
                    case 2:
                        $reminder['days'] = $data['Invoice']['due_after'];
                        break;
                }
            }
            unset($reminder);
        }
    
        $hasErrors = false;

        foreach (array('InvoiceItem', 'InvoiceReminder', 'InvoiceCustomField') as $subModel) {
			
            $errors = array();
            if (!empty($data[$subModel])) {
				
                foreach ($data[$subModel] as $key => $subItem) {
                    $this->{$subModel}->set(array($subModel => $subItem));

                    if (!$this->{$subModel}->validates()) {
                        $errors[$key] = $this->{$subModel}->validationErrors;
                        $hasErrors = true;	
                        $this->validationErrors[$subModel] =$errors;
                    }
                }
                $this->{$subModel}->validationErrors = $errors;
            }
        }
        if(!$this->validateMultipleGateways($data)){
            $hasErrors = true;
        }
        if($data['Invoice']['type'] == self::DEBIT_NOTE){
            $this->ignoreCustomForm = true;
        }
        if (!$this->validates() || $hasErrors) {
            return array('status' => false, 'errors' => $this->validationErrors);
        }

        $data['Invoice']['site_id'] = $invoice['Invoice']['site_id'];
        if (!empty($data['Invoice']['terms_id'])) {
            $TermsModel = GetObjectOrLoadModel('Term');
            $data['Invoice']['terms'] = $TermsModel->field('content', array('Term.id' => $data['Invoice']['terms_id'], 'Term.site_id' => $data['Invoice']['site_id']));
        } else {
            $data['Invoice']['terms'] = '';
        }

        $client_settings = settings::getPluginValues(ClientsPlugin);
        $creditResult = $this->checkInvoiceClientCredit($data, $totals, $invoice);
		if($creditResult !== true)
        {
            $errors['client_id'] = $creditResult;
            $this->validationErrors = array_merge($this->validationErrors, $errors);
        }

        if (!empty($data['Invoice']['save_as_template'])) {
            $template = $this->copyInvoice($data['Invoice']['id'], array('type' => 1, 'name' => $data['Invoice']['name']), true);
            $templateStatus = $template['Invoice']['saved'];
        }

        if($hasLoyaltyPointDiscount) {

            if($points && !$credit_usage_repo->validate($points, $creditChargesMeta)) {
              $errors['loyalty_points'] = __('No Loyalty Points available in the Invoice Date that You enter for the selected Client', true);
            }

            if($points && !$credit_usage_repo->exceedMinimumRedemption($creditChargesMeta)) {
                $errors['loyalty_points'] = __('The total points should exceed the minimum redemption points', true);
            }
  

            $loyaltyPoints = $credit_usage_repo->getUsedLoyaltyCreditByInvoiceId($this->data['Invoice']['id']);

            if($loyaltyPoints && $data['Invoice']['client_id'] != $invoice['Invoice']['client_id']){
                $errors['cannot_change_client'] = __('Invoice client cannot be changed because the invoice has related credit usages', true);
            }
          
        }

        $this->validationErrors = $errors;
        if (!empty($this->validationErrors)) {
            return array('status' => false, 'errors' => $errors);
        }
        $old_client_balance = json_decode($invoice['Invoice']['extra_details'], true)['client_balance'];
        $extra_details = json_decode($data['Invoice']['extra_details'], true);
        $extra_details['client_balance'] = $old_client_balance;
        $data['Invoice']['extra_details'] = json_encode($extra_details);
        if (count($errors) == 0 && $this->save($data, array('validate' => false))) {

            if ($hasLoyaltyPointDiscount) {
                $credit_usage_repo->restoreByInvoiceId($invoice['Invoice']['id']);
                $result = checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::LOYALTY_POINTS);
                if($result['status']) {
                    $credit_usage_repo->add($creditChargesMeta, $data['Invoice']['id'], $client_id, $data['Invoice']['date'], $credit_type_id, $points, $loyaltyPointsResult['discount_amount'], $data['Invoice']['currency_code']);
                }
            }
            if($updateAutoNumber) {
                if (!empty($generated_number)){
                    switch ($data['Invoice']['type']) {
                        case Invoice::Invoice:
                            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE);
                            break;
                        case Invoice::Estimate:
                            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_ESTIMATE);
                            break;
                        case Invoice::SALES_ORDER:
                            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_SALES_ORDER);
                            break;
                        case Invoice::BOOKING:
                            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_BOOKING);
                            break;
                        case Invoice::Credit_Note:
                            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_CREDIT_NOTE);
                            break;
                        case Invoice::DEBIT_NOTE:
                            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_SALES_DEBIT_NOTE);
                            break;
                        case Invoice::Refund_Receipt:
                            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_REFUND_RECEIPT);
                            break;
                        case Invoice::ADVANCE_PAYMENT:
                            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
                            break;
                        default:
                            break;
                    }
                } elseif($data['Invoice']['hidden_no'] && $data['Invoice']['hidden_no']!=$data['Invoice']['no']) {
                    switch ($data['Invoice']['type']) {
                        case Invoice::Invoice:
                            \AutoNumber::update_last_from_number($data['Invoice']['no'], \AutoNumber::TYPE_INVOICE);
                            break;
                        case Invoice::Estimate:
                            \AutoNumber::update_last_from_number($data['Invoice']['no'], \AutoNumber::TYPE_ESTIMATE);
                            break;
                        case Invoice::SALES_ORDER:
                            \AutoNumber::update_last_from_number($data['Invoice']['no'], \AutoNumber::TYPE_SALES_ORDER);
                            break;
                        case Invoice::BOOKING:
                            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_BOOKING);
                            break;
                        case Invoice::Credit_Note:
                            \AutoNumber::update_last_from_number($data['Invoice']['no'], \AutoNumber::TYPE_CREDIT_NOTE);
                            break;
                        case Invoice::DEBIT_NOTE:
                            \AutoNumber::update_last_from_number($data['Invoice']['no'], \AutoNumber::TYPE_SALES_DEBIT_NOTE);
                            break;
                        case Invoice::Refund_Receipt:
                            \AutoNumber::update_last_from_number($data['Invoice']['no'], \AutoNumber::TYPE_REFUND_RECEIPT);
                            break;
                        case Invoice::ADVANCE_PAYMENT:
                            \AutoNumber::update_last_from_number($data['Invoice']['no'], \AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
                            break;
                        default:
                            break;
                    }
                }
            }
            foreach (array('InvoiceItem', 'InvoiceReminder', 'InvoiceCustomField', 'InvoiceTax', 'InvoiceDocument') as $subModel) {
                // Here we save time because invoice was saved before.
				if ($save_payment_only) {
					break;
                }
				$ids = array();
                $counter = 1;
                if (!empty($data[$subModel])) {
                    foreach ($data[$subModel] as $key => &$subItem) {
                        $subItem['invoice_id'] = $data['Invoice']['id'];
                        $subItem['display_order'] = $counter;
                        if (empty($subItem['id'])) {
                            $this->{$subModel}->create();
                        }
                        if ($this->{$subModel}->save(array($subModel => $subItem), array('validate' => false))) {
                            if (!empty($subItem['id'])) {
                                $ids[] = $subItem['id'];
                            } else {
                                $ids[] = $this->{$subModel}->getLastInsertID();
                            }
                        }
                        ++$counter;
                    }
                    if (!empty($ids)) {
                        $this->{$subModel}->deleteAll(array("$subModel.invoice_id" => $data['Invoice']['id'], 'not' => array("$subModel.id" => $ids)));
                    }
                } else {
                    $this->{$subModel}->deleteAll(array("$subModel.invoice_id" => $data['Invoice']['id']));
                }
            }

            $this->data = $data;
            if ($data['Invoice']['type']!=self::Estimate) { //estimate doesn't has payment  
                $this->updateInvoicePayments($data['Invoice']['id']);
            }

            // This condition added to ignore update invoice attachment when add payment , because this method called when add payment . 
            if(!isset($data['InvoicePayment']))
            {
                // Start Save Attachments To S3 .
                $attachmentsId = explode(',',$data['invoiceDocuments']);
                izam_resolve(AttachmentsService::class)->save('invoice', $data['Invoice']['id'], $attachmentsId);
            }
            $this->autoReminderFindMatches(self::getEntityType((int)$data['Invoice']['type']));

            return array('status' => true, 'data' => $data, 'templateStatus' => isset($templateStatus) ? $templateStatus : null);
        } else {
            return array('status' => false);
        }
    }

    static function getEntityType($invoiceTypeNumber)
    {
        return match ((int)$invoiceTypeNumber) {
            self::Refund_Receipt => 'refund_receipt',
            self::Credit_Note => 'credit_note',
            self::DEBIT_NOTE => 'debit_note',
            self::Estimate => 'estimate',
            default => 'invoices',
        };
    }

    function calculate_item_discount($item , $subtotal) {
        return PurchaseOrderAndInvoiceHelper::calculate_item_discount($item, $subtotal);
    }

    function calculateTotals($invoice)
    {

        if (empty($invoice['Invoice']['currency_code'])) {
            $invoice['Invoice']['currency_code'] = getCurrentSite('currency_code');
        }
        [$currency_fractions, $cent] = PurchaseOrderAndInvoiceHelper::getCurrencyFraction($invoice['Invoice']["currency_code"]);

        $site_id = getAuthOwner('id');
        $invoiceTotal = $invoiceTax1 = $invoiceTax2 = $invoiceDiscount = $invoiceSubtotal = 0;
        $is_discount_amount = false;
        if (!empty($invoice['Invoice']['discount_amount']) && (float)$invoice['Invoice']['discount_amount'] != 0) {
            $is_discount_amount = true;
            $allSub = 0;
            foreach ($invoice['InvoiceItem'] as $c => $item) {
                $sub = (float)$item['unit_price'] * (float)$item['quantity'];
                $discount_val = $this->calculate_item_discount($item, $sub);
                $allSub += ($sub - $discount_val);

            }
            $discount=0;
            if($allSub){
                $discount = $invoice['Invoice']['discount_amount'] / $allSub * 100;

            }
        } else
            $discount = floatval($invoice['Invoice']['discount']);
        $items = array();
        $counter = 1;

        $invoice_id = false;
        if (!empty($invoice['Invoice']['id'])) {
            $invoice_id = $invoice['Invoice']['id'];
        } elseif (!empty($invoice['Invoice']['subscription_id'])) {
            $invoice_id = $invoice['Invoice']['subscription_id'];
        }

        // warning suppress
        $invoiceTaxes = $this->InvoiceTax->getInvoiceTaxes($invoice_id,false,$invoice['Invoice']['tax_changed'] ?? null);

		
        $taxes = array();
        $show_discount = false ;
        $total_item_discount = 0 ;
        $total_item = is_countable($invoice['InvoiceItem']) ? count($invoice['InvoiceItem']) : 0;
		$current_item=0;
        $copaymentTotal = 0;
        $extra_details=[];
        if(!empty($invoice['Invoice']['extra_details']) && is_string($invoice['Invoice']['extra_details']))
        $extra_details=json_decode($invoice['Invoice']['extra_details'],true);

        foreach ($invoice['InvoiceItem'] as $c => &$item) {
            //Cast item to array to avoid php scalar errors 
            //Next logic expects an array
            if(!is_array($item)) {
                $item = [];
            }
            if (ifPluginActive(INSURANCE_PLUGIN)) {
                if (is_string($item['extra_details'])) $item['copayment'] = json_decode($item['extra_details'], true)['copayment'];
                $copaymentTotal += $item['copayment'] ?? null;
            }
            // Incase user doesn't have permission to see price then set it to product average price.
            if(is_array($item) && !isset($item['unit_price'])){
                $this->loadModel('Product');
                $item_db = $this->Product->find('first', ['conditions' => ['Product.id' => $item['product_id']]]);
                if($item_db){
                    $item['unit_price'] = $item_db['Product']['average_price'];
                }
            }
            $itemDiscount = 0;
			$current_item++;
            // todo recheck this
            $subtotal = (float) $item['unit_price'] * (float)$item['quantity'];
            $discount_val = 0 ; 
            if ( !empty($item['discount']) /*&& $invoice['Invoice']['type'] == Invoice::Invoice*/)
            {
                $show_discount = true ;
                $discount_val = $this->calculate_item_discount($item, $subtotal);
                $subtotal -= $discount_val ;
                $item['discount_value'] = $discount_val ;  
                $total_item_discount += $discount_val ;  
                $item['discount_string'] = ($item['discount_type'] == Invoice::DISCOUNT_TYPE_PERCENTAGE? $item['discount']."%":format_price_simple($item['discount'],false,false));
            }
            $item['item_subtotal'] = $subtotal ; 
            $invoiceSubtotal += $subtotal;

            if ($discount) {
                $itemDiscount = $subtotal * $discount / 100;
                $invoiceDiscount += round($itemDiscount, $currency_fractions);
				if($total_item==$current_item){
                while ($is_discount_amount && round($invoiceDiscount - $invoice['Invoice']['discount_amount'], $currency_fractions) >= $cent) {
                    $itemDiscount-=$cent;
                    $invoiceDiscount-=$cent;
                } 
				while ($is_discount_amount && round($invoiceDiscount - $invoice['Invoice']['discount_amount'], $currency_fractions) <= -1 * $cent) {
                    $itemDiscount+=$cent;
                    $invoiceDiscount+=$cent;
                }
               
            }
            if ((string)$subtotal == (string)round($itemDiscount, $currency_fractions)) {
                $subtotal = 0;
            } else {
                $subtotal -= round($itemDiscount, $currency_fractions);
            }

			}
            $item['calculated_discount'] = $itemDiscount + $discount_val;
            $subtotal_without = $subtotal;
            $inc_tax = 0;
            if (isset($item['tax1']) && $item['tax1'] && $invoiceTaxes[$item['tax1']]['included'])
                $inc_tax+=$invoiceTaxes[$item['tax1']]['value'];

            if ($item['tax2'] && $invoiceTaxes[$item['tax2']]['included'])
                $inc_tax+=$invoiceTaxes[$item['tax2']]['value'];

            if ($inc_tax > 0) {
                $inc_tax_value = ($inc_tax / (100 + $inc_tax)) * $subtotal;
                $subtotal_without = $subtotal_without - $inc_tax_value;
            }
 
            $itemTax1 = $itemTax2 = 0;
            if ($item['tax1']) {

                $tax1 = $invoiceTaxes[$item['tax1']];
                $itemTax1 = $subtotal_without * $tax1['value'] / 100;

                if (!isset($tax1['tax_id'])) {
                    $tax1['tax_id'] = $tax1['id'];
                    unset($tax1['id']);
                }
                if (!isset($taxes[$item['tax1']])) {
                    $taxes[$item['tax1']] = $tax1;
                    $taxes[$item['tax1']]['invoice_value'] = 0;
                    if (!empty($invoice['Invoice']['id'])) {

                        $taxes[$item['tax1']]['invoice_id'] = $invoice['Invoice']['id'];
                    } else {
                        unset($taxes[$item['tax1']]['invoice_id']);
                        unset($taxes[$item['tax1']]['id']);
                    }
                }
                $taxes[$item['tax1']]['invoice_value'] += $itemTax1;
            }

            if ($item['tax2']) {

                $tax2 = $invoiceTaxes[$item['tax2']];
                $itemTax2 = $subtotal_without * $tax2['value'] / 100;

                if (!isset($tax2['tax_id'])) {
                    $tax2['tax_id'] = $tax2['id'];
                    unset($tax2['id']);
                }

                if (!isset($taxes[$item['tax2']])) {
                    $taxes[$item['tax2']] = $tax2;
                    $taxes[$item['tax2']]['invoice_value'] = 0;
                    if (!empty($invoice['Invoice']['id'])) {
                        $taxes[$item['tax2']]['invoice_id'] = $invoice['Invoice']['id'];
                    } else {
                        unset($taxes[$item['tax2']]['invoice_id']);
                        unset($taxes[$item['tax2']]['id']);
                    }
                }
                $taxes[$item['tax2']]['invoice_value'] += $itemTax2;
            }



            if (empty($taxes[$item['tax1']]['included'])) {

                $subtotal += $itemTax1;

            } else {
                $invoiceSubtotal -= $itemTax1;

            }
            if (empty($taxes[$item['tax2']]['included']))
                $subtotal += $itemTax2;
            else
                $invoiceSubtotal -= $itemTax2;

             
            $invoiceTotal += $subtotal;

            $item['subtotal'] = $subtotal;

            $item['summary_tax1'] = $itemTax1;
            $item['summary_tax2'] = $itemTax2;
            $item['site_id'] = $site_id;
            $item['display_order'] = $c;
            $item['store_id'] = ($item['store_id']?:$invoice['Invoice']['store_id'] );
            if (!empty($invoice['Invoice']['id'])) {
                $item['invoice_id'] = $invoice['Invoice']['id'];
            }
            ++$counter;
        }
        
        if (isset($invoice['Invoice']['shipping_amount'])) {
            $invoiceTotal += round((float)$invoice['Invoice']['shipping_amount'], 3);
        }

        if (!empty($invoice['Invoice']['adjustment_value'])) {
            $invoiceTotal += round((float)$invoice['Invoice']['adjustment_value'], 3);
            $invoiceSubtotal += round((float)$invoice['Invoice']['adjustment_value'], 3);
        }

        if(!empty($extra_details['invoice_accounts'])&&is_array($extra_details['invoice_accounts']))
        {
            foreach($extra_details['invoice_accounts'] as $invoice_account)
                if(!empty($invoice_account['value']))
                {
                    $invoiceTotal += round((float)$invoice_account['value'], 3);
                }
        }

		if (empty($invoice['Invoice']['shipping_amount'])) {
			 unset($invoice['Invoice']['shipping_tax_id']);
		}

	    if (!empty($invoice['Invoice']['shipping_tax_id'])) {
			// Check for the shipping tax if it's included or not
		    // If it's not included then add it here again in the invoice Total
		    $TaxObject = GetObjectOrLoadModel('Tax');
			$shipping_tax_id = $invoice['Invoice']['shipping_tax_id'];
			$tax = $TaxObject->find('first', ['conditions' => ['Tax.id' => $invoice['Invoice']['shipping_tax_id']]]);
		    if (!$tax['Tax']['included']) {
			    $invoiceTotal += $TaxObject->getTaxAmount($tax, $invoice['Invoice']['shipping_amount']);
		    }

		    if (!isset($taxes[$shipping_tax_id])) {
			    $taxes[$shipping_tax_id] = $invoiceTaxes[$shipping_tax_id];
			    $taxes[$shipping_tax_id]['invoice_value'] = 0;
			    if (!empty($invoice['Invoice']['id'])) {
				    $taxes[$shipping_tax_id]['invoice_id'] = $invoice['Invoice']['id'];
			    } else {
                    unset($taxes[$shipping_tax_id]['invoice_id']);
                    unset($taxes[$shipping_tax_id]['id']);
                }
                unset($taxes[$shipping_tax_id]['id']);
		    }
            $taxes[$shipping_tax_id]['tax_id'] = $shipping_tax_id;
		    $taxes[$shipping_tax_id]['invoice_value'] += $TaxObject->getTaxAmount($tax, $invoice['Invoice']['shipping_amount']);
	    }


        $results['invoicePaid'] = 0;
        $results = array('InvoiceItems' => $invoice['InvoiceItem'], 'taxes' => $taxes, 'invoiceSubtotal' => $invoiceSubtotal, 'invoiceTotal' => $invoiceTotal, 'invoiceDiscount' => $invoiceDiscount);
        $results['show_discount'] = $show_discount ; 
        $results['total_item_discount'] = $total_item_discount ;
        $results['invoiceDeposit'] = 0;


        if (!empty($invoice['Invoice']['deposit'])) {
            if ($invoice['Invoice']['deposit_type'] == self::DEPOSIT_TYPE_UNPAID) {
                $results['invoiceDeposit'] = $invoice['Invoice']['deposit'];
            } else {
                $results['invoiceDeposit'] = $invoiceTotal * (float) $invoice['Invoice']['deposit'] / 100;
            }

            if (!empty($invoice['Deposit']) && !empty($invoice['Deposit']['is_paid'])) {

                $results['invoicePaid'] = $results['invoiceDeposit'];
                $results['invoiceDeposit'] = 0;

            }
        } /*else if (!empty($invoice['Invoice']['id'])) {
            $unpaid = $this->field('summary_unpaid', array('Invoice.id' => $invoice['Invoice']['id']));
            $paid = $this->field('summary_paid', array('Invoice.id' => $invoice['Invoice']['id']));
            if ($paid > 0)
                $results['invoiceDeposit'] = $unpaid;
        }*/
        if (!empty($invoice['Payment']) && !empty($invoice['Payment']['is_paid'])) {

            $results['invoicePaid'] = $invoiceTotal;
            $results['invoiceDeposit'] = 0;
        } else if (!empty($invoice['Invoice']['id'])) {
            $results['invoicePaid'] = $results['invoicePaid'] = $this->field('summary_paid', array('Invoice.id' => $invoice['Invoice']['id']));
        } else {
            
        }
        $results['invoiceUnpaid']= $results['invoiceTotal']- $results['invoicePaid'];
        $results['invoiceCopaymentTotal'] = $copaymentTotal;
        if(Settings::getValue(0, 'round_invoices', null, false, false)){
            $round_to = CurrencyHelper::getFraction($invoice['Invoice']['currency_code']);
            $results['invoiceSubtotal'] = round($results['invoiceSubtotal'], $round_to);
            $results['invoiceTotal'] = round($results['invoiceTotal'], $round_to);
            $results['invoiceDiscount'] = round($results['invoiceDiscount'], $round_to);
            $results['invoicePaid'] = round($results['invoicePaid'], $round_to);
            $results['invoiceUnpaid'] = round($results['invoiceUnpaid'], $round_to);
            $results['invoiceCopaymentTotal'] = round($results['invoiceCopaymentTotal'], $round_to);
        }

        return $results;
    }

    function fillInvoice($data, $forceFill = false)
    {
        if (!IS_REST && !$forceFill) {
            return $data;
        }
        $data['Invoice']['date'] = $data['Invoice']['date'] ? $data['Invoice']['date'] : date('Y-m-d');
        $data['InvoiceItem'] = InvoiceItem::prepareForSave($data['InvoiceItem'], $data);
        if (!isset($data['InvoiceCustomField']) && isset($data['Invoice']['invoice_layout_id'])) {
            $InvoiceLayoutCustomField = GetObjectOrLoadModel('InvoiceLayoutCustomField');
            $InvoiceLayoutCustomFieldRows = $InvoiceLayoutCustomField->find('all', array('conditions' => array('InvoiceLayoutCustomField.invoice_layout_id' => $data['Invoice']['invoice_layout_id'])));
            $i = 0;
            foreach ($InvoiceLayoutCustomFieldRows as $row) {
                $ilcf = $row['InvoiceLayoutCustomField'];
                $data['InvoiceCustomField'][] = ['display_order' => $i++, 'placeholder' => $ilcf['placeholder'], 'value' => $ilcf['value'], 'label' => $ilcf['label']];
            }
        }
        return $data;
    }

    private function getProductIdOrCreate($item)
    {
        $search = $item['product_id'];

        $product = $this->Product->find('first', [
            'conditions' => [
                'OR' => [
                    'id' => $search,
                    'name' => $search,
                    'barcode' => $search,
                    'product_code' => $search,
                ]
            ]
        ]);

        if ($product) {
            return $product['Product']['id'];
        }

        $this->Product->create();
        $result = $this->Product->save(['Product' => [
            'name' => $search
        ]]);

        if ($result) {
            return $this->Product->id;
        }

        return $item['product_id'];
    }

    function addInvoice($data, $subscription = false, $forceFill = false, $alreadyIn = false) {

        if (!empty($data['Invoice']['id'])) {
            $logMsg = "Invoice id is set in request " . $data['Invoice']['id']." On Site With id ".getCurrentSite('id');
            unset($data['Invoice']['id']);
            \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , $logMsg, $data);
        }

        $this->loadModel('Product');
        $headers = getallheaders();

        foreach ($data['InvoiceItem'] as $i => $item) {
            if (isset($headers['user-agent']) && $headers['user-agent'] == 'Zapier' && isset($item['product_id'])) {
                $data['InvoiceItem'][$i]['product_id'] = $this->getProductIdOrCreate($item);
            }

            /* remove item id from invoice while sync in desktop to prevent collision */
            unset($data['InvoiceItem'][$i]['id'], $data['InvoiceItem'][$i]['invoice_id']);
        }

        if(empty($data['Client'])){
            unset($data['Client']);
        }
	    if (empty($data['Invoice']['shipping_amount'])) {
		    unset($data['Invoice']['tax_shipping_id']);
	    }
        $this->set($data);
        $status = false;
		$errors = [];
        $site_id = getAuthOwner('id');
        $data['Invoice']['site_id'] = $site_id;

        $this->loadModel('RecordUniqueId');
        if(empty($data['Invoice']['client_id']) && $data["Invoice"]['type'] == Invoice::Invoice){
            $siteId = getCurrentSite('id');
            \Rollbar\Rollbar::log(
                \Rollbar\Payload\Level::ERROR ,
                "invoice with empty client: $siteId",
                ['data' => $data, 'session' => $_SESSION]
            );
        }


        if (ifPluginActive(PosPlugin) && $this->isInvoiceSourcePos($data) && !defined('IGNORE_POS_CHECK')) {
	        $this->loadModel('PosShift');
            $this->PosShift->recursive = -1;
            $posShift = $this->PosShift->read(null, $data['Invoice']['pos_shift_id']);

            if ($posShift['PosShift']['status'] != PosShift::STATUS_OPENED) {
                $this->validationErrors['Invoice'] = $errors['pos_shift'] = 'This session is closed and you can\'t add new invoices to it';

                $this->RecordUniqueId->deleteAll([
                    'RecordUniqueId.unique_id' => $data['Invoice']['unique_id'],
                    'entity_key' => 'invoice'
                ]);

                return ['status' => false];
            }

            if (isset($data['Invoice']['unique_id']) && !empty($data['Invoice']['unique_id']) && $data['Invoice']['unique_id'] !== 'undefined') {
                $this->RecordUniqueId->clear();
                $this->RecordUniqueId->create(false);
                $uniqueIdSaved = $this->RecordUniqueId->save([
                    /** @todo add mapping to all types */
                    'entity_key' => 'invoice',
                    'unique_id' => $data['Invoice']['unique_id'],
                ]);

                if (!$uniqueIdSaved) {
//                    $logMsg = date('Y-m-d H:i:s') . ": potential duplicate invoice in pos " . $data['Invoice']['unique_id'];
//                    \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , $logMsg, $data);
                    $this->validationErrors['Invoice'] = __('This Invoice is duplicated please refresh and try again', true);
                    $invoice = $this->find('first', ['conditions' => ['Invoice.extra_details LIKE' => "%".$data['Invoice']['unique_id']."%"]]);

                    return [
                        'status' => false,
                        "error_type" => ErrorTypes::DUPLICATED,
                        'extra_data' => [
                            'id' => $invoice['Invoice']['id'],
                            'summary_total' => $invoice['Invoice']['summary_total'],
                            'order_no' => $invoice['Invoice']['order_number'],
                            'no' => $invoice['Invoice']['no'],
                        ]
                    ];
                }

            }else{
                $this->validationErrors['Invoice'] = __('This Invoice is duplicated please refresh and try again', true);
                return ['status' => false, "error_type" =>  ErrorTypes::DUPLICATED];
            }
        //for desktop
        } elseif (isset($data['Invoice']['unique_id']) && !empty($data['Invoice']['unique_id']) && $data['Invoice']['unique_id'] !== 'undefined') {
            $uniqueIdSaved = $this->RecordUniqueId->save([
                /** @todo add mapping to all types */
                'entity_key' => 'invoice',
                'unique_id' => $data['Invoice']['unique_id'],
            ]);

            if (!$uniqueIdSaved) {
//                $logMsg = date('Y-m-d H:i:s') . ": potential duplicate invoice in desktop " . $data['Invoice']['unique_id'];
//                \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , $logMsg, $data);
                $this->validationErrors['Invoice'] = __('This Invoice is duplicated please refresh and try again', true);
                $invoice = $this->find('first', ['conditions' => ['Invoice.extra_details LIKE' => "%".$data['Invoice']['unique_id']."%"]]);

                return [
                    'status' => false,
                    "error_type" => ErrorTypes::DUPLICATED,
                    'extra_data' => [
                        'id' => $invoice['Invoice']['id'],
                        'summary_total' => $invoice['Invoice']['summary_total'],
                        'order_no' => $invoice['Invoice']['order_number'],
                        'no' => $invoice['Invoice']['no'],
                    ]
                ];
            }
        }
        if ($subscription === true) {
            $this->validate['name'] = array('rule' => 'notEmpty', 'message' => __('Required', true));
            $this->validate['subscription_period'] = array(array('rule' => 'numeric', 'message' => __('Valid number required', true), 'allowEmpty' => false), array('rule' => array('comparison', '>=', 0), 'message' => __('Positive number required', true)));
            $this->validate['subscription_unit'] = array('rule' => array('inList', array_keys(self::getSubscriptionUnits()), 'message' => __('Invalid unit', true), 'allowEmpty' => false));
            $this->validate['subscription_max_repeat'] = array(array('rule' => 'numeric', 'message' => __('Valid number required', true), 'allowEmpty' => true), array('rule' => array('comparison', '>=', 0), 'message' => __('Positive number required', true), 'allowEmpty' => true));
            unset($this->validate['no']);
            $data['Invoice']['no'] = '';
            $data['Invoice']['type'] = 2;
        } elseif ($subscription === 'template') {
            $this->validate['name'] = array('rule' => 'notEmpty', 'message' => __('Required', true));
            unset($this->validate['no'], $this->validate['date']);
            unset($this->validate['client_id'], $this->validate['client_business_name'], $this->validate['client_first_name']);
            unset($this->validate['client_last_name'], $this->validate['client_email'], $this->validate['issue_date']);

            foreach ($data['InvoiceItem'] as $k => $item) {
                if (empty($item['item'])&&empty($item['description']))
                    unset($data['InvoiceItem'][$k]);
            }
            $data['Invoice']['no'] = '';
        } elseif ($subscription === 'estimate' || $subscription == self::Estimate) {
            unset($this->validate['deposit'], $this->validate['due_after'], $this->validate['terms'], $this->validate['issue_date']);
            $data['Invoice']['type'] = self::Estimate;
            //$data['Invoice']['draft'] = 1;
        } elseif ($subscription === 'sales_order' || $subscription == self::SALES_ORDER) {
            unset($this->validate['deposit'], $this->validate['due_after'], $this->validate['terms'], $this->validate['issue_date']);
            $data['Invoice']['type'] = self::SALES_ORDER;
            //$data['Invoice']['draft'] = 1;
        } elseif ($subscription === Invoice::Credit_Note) {

            $data['Invoice']['type'] = Invoice::Credit_Note;
            //$data['Invoice']['draft'] = 1;
        }
        elseif ($subscription === Invoice::DEBIT_NOTE) {

            $data['Invoice']['type'] = Invoice::DEBIT_NOTE;
        }
        elseif ($subscription === Invoice::Refund_Receipt) {

            $data['Invoice']['type'] = Invoice::Refund_Receipt;
            //$data['Invoice']['draft'] = 1;

        } elseif ($subscription === Invoice::TEMPINVOICE) {

            $data['Invoice']['type'] = Invoice::TEMPINVOICE;
            //$data['Invoice']['draft'] = 1;

        } elseif($subscription == Invoice::BOOKING)
		{
			$data['Invoice']['type'] = Invoice::BOOKING;
            unset($this->validate['order_source_id']);
		} elseif ($subscription === Invoice::Resellers) {

            $data['Invoice']['type'] = Invoice::Resellers;
            //$data['Invoice']['draft'] = 1;
        } elseif ($subscription === Invoice::INSURANCE_INVOICE || $subscription === Invoice::INSURANCE_REFUND) {
            $data['Invoice']['type'] = $subscription;
            unset($this->validate['client_id'], $this->validate['client_business_name'], $this->validate['client_first_name']);
            unset($this->validate['client_last_name'], $this->validate['client_email'], $this->validate['issue_date']);
        } elseif ($subscription === Invoice::DEBIT_NOTE){
            $data['Invoice']['type'] = Invoice::DEBIT_NOTE;
        }  elseif ($subscription === Invoice::ADVANCE_PAYMENT){
            $data['Invoice']['type'] = Invoice::ADVANCE_PAYMENT;
        } else {
            $data['Invoice']['type'] = 0;
        }
        if (empty($data['Invoice']['branch_id'])) {
            $data['Invoice']['branch_id'] = getCurrentBranchID();
        }
        if(IS_REST) {
            if (!empty($data['Invoice']['branch_id'])) {
                setRequestCurrentBranch($data['Invoice']['branch_id']);
            }
            $this->validate['client_id'] = [['rule' => 'notEmpty', 'message' => __('required', true)], ['rule' => 'checkClientExists', 'message' => __('Client not found', true)]];
        }

        if ($data['Invoice']['shipping_options'] == "3") {
            $row = $this->Client->find(array('Client.id' => $data['Invoice']['client_id']));
            $data['Invoice']['secondary_address1'] = $row['Client']['secondary_address1'];
            $data['Invoice']['secondary_address2'] = $row['Client']['secondary_address2'];
            $data['Invoice']['secondary_city'] = $row['Client']['secondary_city'];
            $data['Invoice']['secondary_state'] = $row['Client']['secondary_state'];
            $data['Invoice']['secondary_postal_code'] = $row['Client']['secondary_postal_code'];
            $data['Invoice']['secondary_country_code'] = $row['Client']['secondary_country_code'];
        }

        if (empty($data['Invoice']['currency_code'])) {
            $data['Invoice']['currency_code'] = getCurrentSite('currency_code');
        }
	
		// Read client from form data
        if (!$data['Client']['client_from_data']) {
            $this->Client->applyBranch=['onFind'=>false];
            $row = $this->Client->find('first',array('recursive'=>-1,'conditions'=>array('Client.id' => $data['Invoice']['client_id'])));

            if (!empty($row)) {
                $data['Client']=$row['Client'];
            }
        }

        if (!empty($data['Client'])) {
	
            foreach ($data['Client'] as $key => $value) {
				if ($key == 'id') {
					continue;
				}
				// This conditions prevents Address of the client from being overwritten incase it's an invoice from POS (We send address data with POS now)
                if (isset($data['Invoice']['client_' . $key]) || (
                        !empty($data['Invoice']['pos_shift_id']) && in_array($key, ['address1', 'address2', 'state', 'city', 'postal_code', 'address_id', 'country_code'])
                    )) {
                    continue;
                }
				$data['Invoice']['client_' . $key] = $value;
            }
        }
	
        if (!empty($data['Client']['id'])) {
            unset($data['Client']);
        }
	
        if (empty($data['Invoice']['due_after'])) {
            $data['Invoice']['due_after'] = 0;
        }
		//copy template item_columns to invoice item_columns
		$InvoiceLayout=GetObjectOrLoadModel('InvoiceLayout');
        $invoiceLayoutsColumns = $InvoiceLayout->find('first', array('conditions'=>array('InvoiceLayout.id'=>$data['Invoice']['invoice_layout_id'])));
        $data['Invoice']['item_columns'] = $invoiceLayoutsColumns['InvoiceLayout']['item_columns'];
        if($data['Invoice']['type'] == self::Refund_Receipt) {
            $this->applyDiscountForRefund($data);
        }
        //Fill Invoice With Missing Data
		$data=$this->fillInvoice($data, $forceFill);

        $client_id = $data['Invoice']['client_id'];

        $loyaltyPointsResult = $this->initLoyaltyPoints($data);

        if($loyaltyPointsResult) {
            $data = $loyaltyPointsResult['data'];
            $creditChargesMeta = $loyaltyPointsResult['creditChargesMeta'];
            $hasLoyaltyPointDiscount = $loyaltyPointsResult['hasLoyaltyPointDiscount'];
            $credit_usage_repo = $loyaltyPointsResult['credit_usage_repo'];
            $points = $loyaltyPointsResult['points'];
            $credit_type_id = $loyaltyPointsResult['credit_type_id'];
            $date = $loyaltyPointsResult['date'];
            unset($data["Invoice"]['points']);
        }

        if($data['Invoice']['type'] == self::Refund_Receipt && ifPluginInstalled(PluginUtil::CLIENT_LOYALTY)) {
            $credit_usage_repo = new CreditUsageRepository();
            $creditUsageData = $credit_usage_repo->getCreditUsageByInvoice($data['Invoice']['subscription_id']);
            if (!empty($creditUsageData["CreditUsage"]["id"])) {
                $this->applyRefundDiscountForLoyalty($data);
            }
        }
		$data = $this->commonSaveOperations($data);
		//Calculate totals was here
        $totals = $this->calculateTotals($data);

        if(round($totals['invoiceTotal'], getAllowedNumberOfDigitsAfterFraction($data['Invoice']['currency_code']))< 0) {
            $errors['invoiceTotal'] =  __('You cannot save the transaction with negative total amount', true);
            CustomValidationFlash(['invoiceTotal' => __('You cannot save the transaction with negative total amount', true)]);
        }


        $data['Invoice']['deposit_type'] = self::DEPOSIT_TYPE_UNPAID;
        $data['Invoice']['item_discount_amount'] = $totals['total_item_discount'];
		

        $data['InvoiceItem'] = $totals['InvoiceItems'];

        $data['InvoiceTax'] = $totals['taxes'];
        $data['Invoice']['summary_discount'] = $totals['invoiceDiscount'];
        $data['Invoice']['summary_total'] = $totals['invoiceTotal'];

        $data['Invoice']['summary_subtotal'] = $totals['invoiceSubtotal'];

        $data['Invoice']['summary_paid'] = 0;
        $data['Invoice']['summary_unpaid'] = $data['Invoice']['summary_total'];

        $data['Invoice']['deposit'] = $totals['invoiceDeposit'];
        $data['Invoice']['summary_deposit'] = $totals['invoiceDeposit'];
        // Mark Invoice as paid if Summary_unpaid less than or eqal 0;
        if($data['Invoice']['summary_unpaid']<=0 && $data['Invoice']['type'] != Invoice::SALES_ORDER){
         $data['Invoice']['payment_status']=INVOICE_STATUS_PAID;   
        }

        if (!empty($data['Invoice']['terms_id'])) {
            $TermsModel = GetObjectOrLoadModel('Term');
            $data['Invoice']['terms'] = $TermsModel->field('content', array('Term.id' => $data['Invoice']['terms_id'], 'Term.site_id' => $data['Invoice']['site_id']));
        } else {
            $data['Invoice']['terms'] = '';
        }
		
        if (!empty($data['InvoiceReminder'])) {

            foreach ($data['InvoiceReminder'] as &$reminder) {
                switch ($reminder['send_when']) {
                    case 0:
                        $date = strtotime($this->formatDate($data['Invoice']['date'], getCurrentSite('date_format')));
                        $issueDate = strtotime($this->formatDate($data['Invoice']['issue_date'], getCurrentSite('date_format')));
						
                        $diff = ($date - $issueDate) / 86400;
                        //  $reminder['days'] = -$diff;
                        break;
                    case 1:
                        //     $reminder['days'] = 0;
                        break;
                    case 2:
                        //     $reminder['days'] = $data['Invoice']['due_after'];
                        break;
                }
            }
            unset($reminder);
        }
        $site = getAuthOwner();
        //Nour Mod
        if (!isset($data['Invoice']['staff_id'])) {
            $data['Invoice']['staff_id'] = $site['staff_id'];
        }
        if ((!isset($data['Invoice']['sales_person_id']) || $data['Invoice']['sales_person_id'] === null)&&!empty($site['staff_id'])) {
            $data['Invoice']['sales_person_id'] = $site['staff_id'];
        }

        $baseInvoice = $this->find('first', ['conditions' => ['Invoice.id' => $data['Invoice']['subscription_id']]]);
        if (
            $data['Invoice']['type'] == Invoice::Credit_Note &&
            isset($baseInvoice) &&
            $baseInvoice['Invoice']['type'] == self::ADVANCE_PAYMENT
        ) {
            $validation = AdvancePaymentCreditNoteValidator::validate($data['Invoice'], $totals, $baseInvoice['Invoice']);
            if (!$validation['isValid'])  {
                foreach ($validation['errors'] as $key => $error) {
                    $this->validationErrors['Invoice'] = $error;
                    return ['status' => false,  "error_type" =>  ErrorTypes::PAYMENT_ERROR];
                }
            }
        }


        if ($data['Invoice']['type'] == Invoice::Refund_Receipt || (!empty($data['Invoice']['subscription_id']) &&$data['Invoice']['type'] == Invoice::Credit_Note )) {
            $validateResult = $this->validateRefundedItems($data);
            if ($validateResult !== true) {
                $errors = array_merge($validateResult,$errors);
            }
        }


         if($subscription=='template'){
             if(empty($data['Client']['id'])){
                 unset($data['Client']['id']);
             }
             if($data['Client']['client_from_data']=="1"){
             unset($data['Client']['client_from_data']) ;
             if(empty($data['Client'])){
                 unset($data['Client']);
             }else{
                 $data['Client']['client_from_data']=1;
             }
             }
        
         }
		$client_settings = settings::getPluginValues(ClientsPlugin);
        $creditResult = $this->checkInvoiceClientCredit($data, $totals);
        if($creditResult !== true)
        {
            $errors['client_id'] = $creditResult;
            $this->validationErrors = array_merge($this->validationErrors, $errors);
        }

//		dd($data);
	// Unset invalid post data
  foreach (array('InvoiceReminder', 'InvoiceCustomField') as $subModel) {
	if(!is_array($data[$subModel])){
		unset($data[$subModel]);
	}
  }
  if(empty($data['InvoiceTax'])){
		unset($data['InvoiceTax']);
		}
		   $client_settings = settings::getPluginValues(ClientsPlugin);
        $creditResult = $this->checkInvoiceClientCredit($data, $totals);
        if($creditResult !== true)
        {
            $errors['client_id'] = $creditResult;
            $this->validationErrors = array_merge($this->validationErrors, $errors);
        }

//		dd($data);
	// Unset invalid post data	
  foreach (array('InvoiceReminder', 'InvoiceCustomField') as $subModel) {
	if(!is_array($data[$subModel])){
		unset($data[$subModel]);
	}
  }
  if(empty($data['InvoiceTax'])){
		unset($data['InvoiceTax']);
		}
        $this->set($data);
        if($this->checkAgentHeader()){
            $this->ignoreCustomForm = true;
        }
        $this->validates();
        foreach ($data['InvoiceItem'] as &$itemItemDummy) {
            if (isset($itemItemDummy['extra_details']) && is_array($itemItemDummy['extra_details'])) {
                $itemItemDummy['extra_details'] = json_encode($itemItemDummy['extra_details']);
            }
        }

        foreach ($data['InvoiceTax'] as $key=>$item) {
          if(empty($item['tax_id'])){
              unset($data['InvoiceTax'][$key]);
          }
        }

        foreach ($data['InvoiceDocument'] as $key=>$item) {
            if(empty($item['document_id'])){
                unset($data['InvoiceDocument'][$key]);
            }
        }

        if(empty($data['InvoiceDocument'])){
            unset($data['InvoiceDocument']);
        }

        if (isset($data['Invoice']['extra_details']) && is_array($data['Invoice']['extra_details'])) {
            $data['Invoice']['extra_details'] = json_encode($data['Invoice']['extra_details']);
        }
	    if (isset($data['Invoice']['tab_id']) && !empty($data['Invoice']['tab_id'])) {
		    $tempJSON = json_decode($data['Invoice']['extra_details'], true);
		    $tempJSON['tab_id'] = $data['Invoice']['tab_id'];
			$data['Invoice']['extra_details'] = json_encode($tempJSON);
	    }
	    if (isset($data['Invoice']['unique_id']) && !empty($data['Invoice']['unique_id'])) {
		    $tempJSON = json_decode($data['Invoice']['extra_details'], true);
			$tempJSON['unique_id'] = $data['Invoice']['unique_id'];
		    $data['Invoice']['extra_details'] = json_encode($tempJSON);
	    }

        if(!$this->isInvoiceSourcePos($data) && settings::getValue(PosPlugin, 'pos_default_client') != $data["Invoice"]['client_id']) {
            $tempJSON = json_decode($data['Invoice']['extra_details'], true);
            if (empty($tempJSON['client_balance']) && !settings::getValue(0, 'disable_client_unpaid_balance_in_extra_details')) {
                $OpenInvoices = $this->Client->getUnpaid($data["Invoice"]['client_id'], $data['Invoice']['currency_code']);
                $Creditlist = $this->Client->getAllCredit($data["Invoice"]['client_id'], $data['Invoice']['currency_code']);
                $tempJSON['client_balance'] = array_values($OpenInvoices)[0] - array_values($Creditlist)[0];
                $data['Invoice']['extra_details'] = json_encode($tempJSON);
            }
        }

        //@this line was made because the previous function changed the invoice global applyBranch to false
        $this->applyBranch = ['onFind' => true, 'onSave' => true];

        $tempJSON = json_decode($data['Invoice']['extra_details'], true);
        $tempJSON['invoice_version'] = 2;

        $data['Invoice']['extra_details'] = json_encode($tempJSON);

        if($hasLoyaltyPointDiscount && $points) {
            if(!$credit_usage_repo->validate($points, $creditChargesMeta)) {
              $errors['loyalty_points'] = __('No Loyalty Points available in the Invoice Date that You enter for the selected Client', true);
            }

            if(!$credit_usage_repo->exceedMinimumRedemption($creditChargesMeta)) {
                $errors['loyalty_points'] = __('The total points should exceed the minimum redemption points', true);
            }
          }

        if(!$this->validateMultipleGateways($data)){
            $errors['multiple_gateways'] = '';
        }

        if (isset($data['_Token']['key'])) {
            $uniqueIdSaved = $this->RecordUniqueId->save([
                /** @todo add mapping to all types */
                'entity_key' => 'invoice',
                'unique_id' => $data['_Token']['key'],
            ]);

            if (!$uniqueIdSaved) {
//                $logMsg = date('Y-m-d H:i:s') . ": potential duplicate invoice in web " . $data['_Token']['key'];
//                \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , $logMsg, $data);
                $this->validationErrors['Invoice'] = __('This Invoice is duplicated please refresh and try again', true);
                $invoice = $this->find('first', ['condition' => ['Invoice.extra_details LIKE' => "%".$data['_Token']['key']."%"]]);
                return ['status' => false, "error_type" =>  ErrorTypes::DUPLICATED, 'extra_data' => ['id' => $invoice['Invoice']['id']]];
            }
        }


        $code = $this->data["Invoice"]["currency_code"];
        $round_number = Cache::read('number_formats')[$code][0] ?? 3;
        if (
            $this->data['Invoice']['type'] == Invoice::ADVANCE_PAYMENT &&
            empty($this->data["Payment"]["payment_method"]) &&
            (
                empty($this->data['MultiplePayment']) ||
                (!empty($this->data['MultiplePayment']) && abs(round(array_sum( array_column($this->data['MultiplePayment'], 'amount')), $round_number)  -  round($this->data["Invoice"]["summary_total"], $round_number)) > 0.01)
            )
        ) {
            $this->validationErrors['Payment'] =  __("The total amount across all selected payment gateways should be equal to the total amount of the invoice", true);
            return ['status' => false,  "error_type" =>  ErrorTypes::PAYMENT_ERROR, ];
        }


        if (!empty($this->data['Payment']['payment_method'])) {
            $payments = [$this->data['Payment']];
        } else if (!empty($this->data['Payment']) && !isset($this->data['Payment']['payment_method']) && empty($this->data['MultiplePayment'])) {
            $payments = $this->data['Payment'];
        } else if (!empty($this->data['MultiplePayment'])) {
            $payments = $this->data['MultiplePayment'];
        } else {
            $payments = [];
        }
        $this->loadModel("SitePaymentGateway");
        foreach ($payments as $payment) {
            if (empty($payment)) {
                continue;
            }
            $invoicePayment = $this->SitePaymentGateway->find('first', ['conditions' => ['active' => 1, 'payment_gateway' => $payment['payment_method']]]);
            if (empty($invoicePayment['SitePaymentGateway'])) {
                $this->validationErrors["payment"] = __("Wrong Payment Method or inactive", true);
                return ['status' => false,  "error_type" =>  ErrorTypes::PAYMENT_ERROR, ];
            }
        }

        App::import('Vendor', 'AutoNumber');

        if (!$data['Invoice']['draft'] && $subscription !== true) {
            $this->query('START TRANSACTION;');
            $data['Invoice']['no'] = $this->getAndUpdateAutoNumber($data);
            if(empty(trim($data['Invoice']['no'])) && $subscription !='template'){
                $errors['no'] = __("The invoice number cannot accept spaces.", true);
            }

        } else {
            if($data['Invoice']['no'] == $data['Invoice']['hidden_no']){
                //will be updated after save to draft-$id
                $data['Invoice']['no'] = 'draft';
            }
             
        }
        $data = $this->prepareForAdd($data);
        
        if (count($errors) == 0 && $this->saveAll($data, ['validate' => 'only'])) {
            $save = true;
            $this->query('COMMIT;', false);
        } else {
            $save = false;
            $this->query('ROLLBACK;', false);
        }
        // assign date  value changed in validate to sql date
        $data['Invoice']['date'] = $this->data['Invoice']['date'];
        $data['Invoice']['issue_date'] = $this->data['Invoice']['issue_date'];

        if ($save && $this->saveAll($data, ['validate' => false]) == 1) {
			if (isset($data['non_completed_id'])) {
				$_SESSION['last_non_completed_id'] = $data['non_completed_id'];
			}
            if ($hasLoyaltyPointDiscount && $points > 0) {
                $result = checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::LOYALTY_POINTS);
                if($result['status']) {
                  $credit_usage_repo->add($creditChargesMeta, $this->getLastInsertID(), $client_id, date("Y:m:d H:i:s", $date), $credit_type_id, $points, $loyaltyPointsResult['discount_amount'], $data['Invoice']['currency_code']);
                }
            }
            $this->invoiceSavedCallback($data, true);

            $isDraft = $data['Invoice']['no'] == 'draft';

            if (!$isDraft) {
                /* @todo check if invoiceSavedCallback should be called again here */
                $this->invoiceSavedCallback($data, true);
            } else {
                $this->saveField('no','draft-'.$this->id);
            }

			if (check_permission(Edit_General_Settings)) {
				App::import('Vendor', 'settings');
				settings::setValue(InvoicesPlugin, 'initial_invoice_custom_fields', json_encode($data['InvoiceCustomField']));
			}
			if (empty($data['Invoice']['id'])) {
				$data['Invoice']['id'] = $this->getLastInsertID();
			}

			if (!empty($data['Invoice']['save_as_template'])) {
				$template = $this->copyInvoice($data['Invoice']['id'], array('type' => 1, 'name' => $data['Invoice']['name']), true);
				$templateStatus = $template['Invoice']['saved'];
			}

            //If the invoice will be handled from the controller ,  Some clients have rules that trigger an email for unpaid or partially paid invoices. Previously, when a client created an invoice, is_paid was sent, causing even fully paid invoices to trigger these rules, as the payment processing occurs later in the controller.
                if($data['Invoice']['type'] != self::Invoice)
                {
                    $this->autoReminderFindMatches(self::getEntityType((int)$data['Invoice']['type']));
                }


			$status = true;

            if (ifPluginActive(INSURANCE_PLUGIN) && !$alreadyIn) {
	            $this->loadModel('InsuranceAgentClass');
                if (isset($data['appliedInsurance']) && $data['Invoice']['type'] == self::Invoice) {
                    $newInvoice = $this->InsuranceAgentClass->generateInsuranceInvoice($data, self::INSURANCE_INVOICE);
                    //Sometimes newinvoice is set to false so don't call addInvoice to avoid calling empty insurance invoices
                    if($newInvoice) {
                        $this->addInvoice($newInvoice, self::INSURANCE_INVOICE, false, true);
                    }

                } else if (isset($data['appliedInsurance']) && $data['Invoice']['type'] == self::Refund_Receipt) {
                    $newInvoice = $this->InsuranceAgentClass->generateInsuranceInvoice($data, self::INSURANCE_REFUND);
                    //Sometimes newinvoice is set to false so don't call addInvoice to avoid calling empty insurance invoices
                    if($newInvoice) {
                        $this->addInvoice($newInvoice, self::INSURANCE_REFUND, false, true);
                    }
                }
            }
            Cache::delete('statement_data_' . $client_id . '_' . getCurrentSite('id'));

             // Start Save Attachments To S3 .
             $attachmentsId = explode(',',$data['invoiceDocuments']);
             izam_resolve(AttachmentsService::class)->save('invoice', $data['Invoice']['id'], $attachmentsId);
        }else{
            if (isset($uniqueIdSaved) && $uniqueIdSaved !== false) {
                $this->RecordUniqueId->deleteAll([
                    'RecordUniqueId.unique_id' => $uniqueIdSaved['RecordUniqueId']['unique_id'],
                    'entity_key' => $uniqueIdSaved['RecordUniqueId']['entity_key']
                ]);
            }
			$this->validationErrors = array_merge($this->validationErrors,$errors);
		}


        return compact('status', 'data', 'templateStatus', 'message');
    }

    private function getAndUpdateAutoNumber($data)
    {
        // generated
        if (empty($data['Invoice']['no'])||(empty($data['Invoice']['id']) && $data['Invoice']['hidden_no'] == $data['Invoice']['no'])) {
            switch ($data['Invoice']['type']) {
                case Invoice::Invoice:
                case Invoice::INSURANCE_INVOICE:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_INVOICE);
                    $no = $this->getAndUpdateAutoNumberFromAutoSerial(\AutoNumber::TYPE_INVOICE, Invoice::Invoice);
                    break;
                case Invoice::Estimate:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_ESTIMATE);
                    $no = $this->getAndUpdateAutoNumberFromAutoSerial(\AutoNumber::TYPE_ESTIMATE, Invoice::Estimate);
                    break;
                case Invoice::SALES_ORDER:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_SALES_ORDER);
                    $no = $this->getAndUpdateAutoNumberFromAutoSerial(\AutoNumber::TYPE_SALES_ORDER, Invoice::SALES_ORDER);
                    break;
                case Invoice::BOOKING:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_BOOKING);
                    $no = $this->getAndUpdateAutoNumberFromAutoSerial(\AutoNumber::TYPE_BOOKING, Invoice::BOOKING);
                    break;
                case Invoice::TEMPINVOICE:
//                    \AutoNumber::set_validate(\AutoNumber::TYPE_TEMPINVOICE);
                    $no = $this->getAndUpdateAutoNumberFromAutoSerial(\AutoNumber::TYPE_TEMPINVOICE, Invoice::TEMPINVOICE);
                    break;
                case Invoice::Credit_Note:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_CREDIT_NOTE);
                    $no = $this->getAndUpdateAutoNumberFromAutoSerial(\AutoNumber::TYPE_CREDIT_NOTE, Invoice::Credit_Note);
                    break;
                case Invoice::DEBIT_NOTE:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_SALES_DEBIT_NOTE);
                    $no = $this->getAndUpdateAutoNumberFromAutoSerial(\AutoNumber::TYPE_SALES_DEBIT_NOTE, Invoice::DEBIT_NOTE);
                    break;
                case Invoice::ADVANCE_PAYMENT:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
                    $no = $this->getAndUpdateAutoNumberFromAutoSerial(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE, Invoice::ADVANCE_PAYMENT);
                    break;
                case Invoice::Refund_Receipt:
                case Invoice::INSURANCE_REFUND:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_REFUND_RECEIPT);
                    $no = $this->getAndUpdateAutoNumberFromAutoSerial(\AutoNumber::TYPE_REFUND_RECEIPT, Invoice::Refund_Receipt);
                    break;
                default:
                    $no = $data['Invoice']['no'];
                    break;
            }
        } else {
            // inserted
            switch ($data['Invoice']['type']) {
                case Invoice::Invoice:
                case Invoice::INSURANCE_INVOICE:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_INVOICE);
                    $no = $this->getAndUpdateAutoNumberFromNumber(\AutoNumber::TYPE_INVOICE, $data['Invoice']['no']);
                    break;
                case Invoice::Estimate:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_ESTIMATE);
                    $no = $this->getAndUpdateAutoNumberFromNumber(\AutoNumber::TYPE_ESTIMATE, $data['Invoice']['no']);
                    break;
                case Invoice::SALES_ORDER:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_SALES_ORDER);
                    $no = $this->getAndUpdateAutoNumberFromNumber(\AutoNumber::TYPE_SALES_ORDER, $data['Invoice']['no']);
                    break;
                case Invoice::BOOKING:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_BOOKING);
                    $no = $this->getAndUpdateAutoNumberFromNumber(\AutoNumber::TYPE_BOOKING, $data['Invoice']['no']);
                    break;
                case Invoice::TEMPINVOICE:
//                    \AutoNumber::set_validate(\AutoNumber::TYPE_TEMPINVOICE);
                    $no = $this->getAndUpdateAutoNumberFromNumber(\AutoNumber::TYPE_TEMPINVOICE, $data['Invoice']['no']);
                    break;
                case Invoice::Credit_Note:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_CREDIT_NOTE);
                    $no = $this->getAndUpdateAutoNumberFromNumber(\AutoNumber::TYPE_CREDIT_NOTE, $data['Invoice']['no']);
                    break;
                case Invoice::DEBIT_NOTE:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_SALES_DEBIT_NOTE);
                    $no = $this->getAndUpdateAutoNumberFromNumber(\AutoNumber::TYPE_SALES_DEBIT_NOTE, $data['Invoice']['no']);
                    break;
                case Invoice::ADVANCE_PAYMENT:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
                    $no = $this->getAndUpdateAutoNumberFromNumber(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE, $data['Invoice']['no']);
                    break;
                case Invoice::Refund_Receipt:
                case Invoice::INSURANCE_REFUND:
                    \AutoNumber::set_validate(\AutoNumber::TYPE_REFUND_RECEIPT);
                    $no = $this->getAndUpdateAutoNumberFromNumber(\AutoNumber::TYPE_REFUND_RECEIPT, $data['Invoice']['no']);
                    break;
                default:
                    $no = $data['Invoice']['no'];
                    break;
            }
        }

        return $no;
    }

    private function getAndUpdateAutoNumberFromAutoSerial($type, $invoiceType)
    {
        $key = "{$type}-0";

        $autonumber_rows = $this->query("select * from settings where `key`='$key' for update;", false);

        $no = (in_array($invoiceType, [Invoice::Invoice, Invoice::Refund_Receipt])) ?
            $this->getNextAutoNumber($type, $invoiceType) :
            \AutoNumber::get_auto_serial($type);

        $updatedNo = \AutoNumber::update_auto_serial($type);

        $siteId = getCurrentSite('id');

        if (!$autonumber_rows) {
            $data = [
                'locked rows'=> $autonumber_rows,
                'invoice no'=> $no,
                'updated result'=> $updatedNo,
                'data' => json_encode($this->data)
            ];

            $dataSource = $this->getDataSource();
            $error = $dataSource->error;

            \Rollbar\Rollbar::log(
                \Rollbar\Payload\Level::INFO ,
                "log for potential duplicate invoice no for site: $siteId . $error",
                $data
            );
        }

        return $no;
    }

    private function getAndUpdateAutoNumberFromNumber($type, $no)
    {
        $key = "{$type}-0";

        $this->query("select * from settings where `key`='$key' for update;", false);

        \AutoNumber::update_last_from_number($no, $type);

        return $no;
    }

    function autoReminderFindMatches($invoiceType = 'invoices')
    {
        App::import('vendor','AutoReminder',['file'=>'AutoReminder/autoload.php']);
        $this->loadModel('AutoReminderRule');
        $this->loadModel('ReminderMessage');

        $rules = $this->AutoReminderRule->find('all', ["recursive" => -1, "conditions" => ["entity_type" => $invoiceType, "active" => 1]]);

        foreach ($rules as $rule) {
            $autoReminderRule = \AutoReminder\AutoReminderFactory::create($rule);
            $autoReminderRule->addMessages($autoReminderRule->findMatches());
        }
    }
    private function invoiceSavedCallback($invoiceData, $isCreate = true) {
        // Added to ignore create commision for sales orders.
        if($invoiceData['Invoice']['type'] == self::SALES_ORDER){
            return;
        }
        //make it installed only because we want update commission event it's not activated
        if (ifPluginInstalled(COMMISSION_PLUGIN) && $invoiceData['Invoice']['type'] != self::ADVANCE_PAYMENT) {
			$data = $this->checkForCommissionSheetInPeriod($invoiceData);
        	if ($data){
                CustomValidationFlash([__('The Sales commission for this transaction will not be calculated as the transaction date already included in a sheet with a status paid or Rejected or Approved and if you need to include the commission of this transaction so you need to undo the sheet to be opened then edit and save the transaction.', true)]);
        		return;
	        }
        	// No Sales Person In Invoice (No Commission)
	        if (empty($invoiceData['Invoice']['sales_person_id'])) {
		        return;
	        }
        	$CommissionRuleStaffsModel = GetObjectOrLoadModel('CommissionRuleStaff');
        	$found_rules_for_sales_person = $CommissionRuleStaffsModel->find('first', ['conditions' => ['CommissionRuleStaff.staff_id' => $invoiceData['Invoice']['sales_person_id']]]);
			$CommissionModel = GetObjectOrLoadModel('Commission');
        	$found_commission_for_invoice_id = $CommissionModel->find('first', ['conditions' => ['Commission.invoice_id' => $invoiceData['Invoice']['id']]]);
        	if (empty($found_rules_for_sales_person) && empty($found_commission_for_invoice_id) && $invoiceData['Invoice']['sales_person_id'] != self::MULTIPLE_SALES_PERSONS) {
				 // No Sales Person Found for any Commission Rule ! & No Commission was made for this invoice before
		        return;
	        }
        	// Call Laravel Via API and Calculate the Commssion !
        	CommunicatorFacade::getInstance()->calculateInvoiceCommission($this->id);
        }
        if($invoiceData['Invoice']['source_type'] == self::BOOKING) {
            $this->handleBookingPayment($invoiceData);
        }
    }

    private function handleBookingPayment($invoiceData) {
        //update TEMP BOOKING DATA
        $tempBooking = $this->find('first', ['conditions' => ['Invoice.id' => $invoiceData['Invoice']['source_id'], 'Invoice.type' => self::TEMP_BOOKING]]);
        if($tempBooking && $tempBooking['Invoice']['payment_status'] == INVOICE_STATUS_PAID) {
            $this->updateInvoiceType($tempBooking['Invoice']['id'], self::BOOKING, true);
            $FollowUpReminder = GetObjectOrLoadModel('FollowUpReminder');
            $tempFollowUpReminder = $FollowUpReminder->find('first', ['conditions' => [
                'FollowUpReminder.item_type' => FollowUpReminder::TEMP_BOOKING,
                'FollowUpReminder.item_id' => $tempBooking['Invoice']['id']
            ]]);
            if($tempFollowUpReminder) {
                $FollowUpReminder->id = $tempFollowUpReminder['FollowUpReminder']['id'];
                $FollowUpReminder->save(['status' => FollowUpReminder::Status_Scheduled, 'item_type' => FollowUpReminder::BOOKING_TYPE], ['callbacks' => false]);
            }
        }
    }

    /**
     * This function gets the next auto number if the rule is unique
     * This function works recursively until it finds the next available autonumber to prevent
     * Autonumber exsits exception
     */
    private function getNextAutoNumber($autoNumberType, $invoiceType) {

        $rule = AutoNumber::get_rule($autoNumberType);

        $number = \AutoNumber::get_auto_serial($autoNumberType);

        if (!isset($rule['require_unique']) || $rule['require_unique'] != '1') {
            return $number;
        }

        $invoice = $this->find('first', ['conditions' => ['no' => $number, 'Invoice.type' => $invoiceType]]);
        while ($invoice) {
            \AutoNumber::update_auto_serial($autoNumberType);
            $number = \AutoNumber::get_auto_serial($autoNumberType);
            $invoice = $this->find('first', ['conditions' => ['no' => $number, 'Invoice.type' => $invoiceType]]);
        }

        return $number;
    }

    public function isInvoiceSourcePos($invoice){
    	return !empty($invoice['Invoice']['pos_shift_id']);
    }

    public function validateIfStaffHaveAccessToPosShift($invoice) {
        if (ifPluginActive(PosPlugin) && $this->isInvoiceSourcePos($invoice) && !check_permission(OPEN_ALL_POS_SESSIONS)) {
            $posShift = GetObjectOrLoadModel('PosShift')->find('first', ['recursive' => -1, 'conditions' => ['PosShift.id' => $invoice['Invoice']['pos_shift_id']]]);
            if (empty($posShift) || $posShift['PosShift']['staff_id'] != getAuthOwner('staff_id')) {
                $data = [
                    'debug_back_trace' => debug_backtrace(),
                    'staff_id' => getAuthOwner('staff_id'),
                    'pos_shift_id' => $invoice['Invoice']['pos_shift_id'],
                    'pos_shift_staff_id' => $posShift['PosShift']['staff_id'] ?? null,
                    'get' => $_GET,
                    'invoice' => $invoice,
                    'post' => $_POST,
                ];
                \Rollbar\Rollbar::log(Level::INFO, "invoice add request by a staff to a pos shift that he can not access", ['debug_data' => $data]);
                return false;
            }
        }
        return true;
    }

    public function validateIfStaffCanAccessCurrentBranch($invoice) {
        if (!ifPluginActive(BranchesPlugin)) {
            return true;
        }
        if (isOwner()) {
            return true;
        }
        $current_request_branch_id = getCurrentBranchID();
        $accessible_branch_ids_for_staff = getStaffBranchesIDs(getAuthOwner('staff_id'));
        $invoice_data_branch_id = $invoice['Invoice']['branch_id'] ?? null;
        $current_request_condition = in_array($current_request_branch_id, $accessible_branch_ids_for_staff);
        $invoice_data_condition = empty($invoice_data_branch_id) || in_array($invoice_data_branch_id, $accessible_branch_ids_for_staff);
        if ($current_request_condition && $invoice_data_condition) {
            return true;
        }
        $data = [
            'debug_back_trace' => debug_backtrace(),
            'staff_id' => getAuthOwner('staff_id'),
            'current_request_branch_id' => $current_request_branch_id,
            'accessible_branch_ids_for_staff' => $accessible_branch_ids_for_staff,
            'invoice_data_branch_id' => $invoice_data_branch_id,
            'invoice_data' => $invoice,
            'get' => $_GET,
            'post' => $_POST,
        ];
        \Rollbar\Rollbar::log(Level::INFO, "invoice add request by a staff to a branch that he can not access", ['debug_data' => $data]);
        return false;
    }

    function sumInvoicePayments($invoicePayments) {
        $sum = 0;
        foreach ($invoicePayments as $payment) {
            // we must check if empty value we shoult put 0 because it was returning empty string
            $amount = empty($payment['amount']) ? 0 :  (float)  $payment['amount'];
            $amount = abs($amount);
            if ($amount === 0) {
                $amount = 0;
            }
            $sum += $amount;
        }
        return $sum;
    }

    private function checkForCommissionSheetInPeriod($invoiceData){
	    $this->loadModel('CommissionSheet');
	    $invoice_date = $this->formatDateTime($invoiceData['Invoice']['date']);
        if($invoiceData['Invoice']['sales_person_id'] != self::MULTIPLE_SALES_PERSONS){
            return $this->CommissionSheet->find('first', [
                'conditions' => [
                        'CommissionSheet.from_date <=' => $invoice_date,
                        'CommissionSheet.to_date >=' => $invoice_date,
                        'CommissionSheet.status <>' => 'open',
                        'CommissionSheet.staff_id' => $invoiceData['Invoice']['sales_person_id']
                ]
            ]);
        }else{
            foreach($invoiceData['InvoiceItem'] as $item){
                $commission_sheet = $this->CommissionSheet->find('first', [
                    'conditions' => [
                            'CommissionSheet.from_date <=' => $invoice_date,
                            'CommissionSheet.to_date >=' => $invoice_date,
                            'CommissionSheet.status <>' => 'open',
                            'CommissionSheet.staff_id' => $item['sales_person_id']
                    ]
                ]);
                if($commission_sheet){
                    return $commission_sheet;
                }
            }
            return false;
        }
    }

	private function commonSaveOperations($data)
	{
        $appliedInsurance = false;
        if (ifPluginActive(INSURANCE_PLUGIN) && ($data['Invoice']['type'] != self::INSURANCE_INVOICE || $data['Invoice']['type'] != self::INSURANCE_REFUND)) {
            foreach ($data['InvoiceItem'] as $invoiceItem) {
                if (isset($invoiceItem['extra_details']) && !empty($invoiceItem['extra_details'])) {
                    $data['appliedInsurance'] = true;
                    return $data;
                }
            }
        }
		if (!$appliedInsurance && ifPluginActive(OffersPlugin) && in_array($data['Invoice']['type'] ,[ self::Invoice , self::Estimate])  ) {
			$this->loadModel('Offer');
			try {
				$data = $this->Offer->applyOffers($data);
			} catch (Exception $e) {
				App::import('Vendor', 'oilogger');
				Oilogger::log('Error in applying offer');
			}
		}
        $data = $this->prepareInvoiceSalesPerson($data);
		return $data;
	}
	
    /**
     * this function validates the refund receipt data by checking if any item is added without being in the original invoice
     * and also check if any item is being refunded with amount that exceeds the original amount minus the previously refunded amounts
     * @param $data Invoice the refund receipt data submitted by the user
     * @return array|bool return true if the data is valid and can be refunded or array of errors
     */
    function validateRefundedItems($data) {
        $mainInvoiceId = $data['Invoice']['subscription_id'];
        $refundsQuery = <<<SQL
SELECT II.product_id,SUM(II.quantity) - COALESCE(R.refunded_quantity,0) as quantity
                        FROM invoice_items II JOIN invoices I ON I.id = II.invoice_id LEFT JOIN (SELECT II.product_id,
                            sum(II.quantity) as refunded_quantity,R.subscription_id FROM invoice_items II JOIN invoices R ON R.id = II.invoice_id 
                            WHERE R.subscription_id = $mainInvoiceId GROUP BY II.product_id) R ON R.product_id = II.product_id 
                        WHERE I.id = $mainInvoiceId GROUP BY II.product_id
SQL;
        $refundsData = $this->flat_query_results($refundsQuery);
        $itemsAvailableQuantity = [];
        foreach ($refundsData as $item){
            $itemsAvailableQuantity[$item['product_id']] = $item['quantity'];
        }
        $errors = [];
        foreach ($data['InvoiceItem'] as $index => $item){
            if(empty($item['product_id'])){
                continue;
            }
            $item['quantity']=(float)$item['quantity'];
            if (!in_array($item['product_id'],array_keys($itemsAvailableQuantity))) {
                $errors['InvoiceItem'][$index]["item"] = __('This item didn\'t exist in the original invoice',true);
            } else if (round($item['quantity'], 6) > round($itemsAvailableQuantity[$item['product_id']], 6) ) {
                $errors['InvoiceItem'][$index]["item"] = __('This item exceeds the amount that can be refunded',true);
            } else {
                $itemsAvailableQuantity[$item['product_id']] -= $item['quantity'];
            }
        }
        return $errors ?: true;
    }

    function checkDateFormat($param) {

        $dataDateFromat = getCurrentSite('date_format');
        $dateFormats = getDateFormats('mysql');

        $myDateFormat = $dateFormats[$dataDateFromat];

        [$key, $value] = [key($param), current($param)];
        if (empty($value))
            return true;
        $date = strtotime($this->formatDate($value, $myDateFormat));
        if ($date) {
            $this->data['Invoice'][$key] = date('Y-m-d', $date);
            return true;
        }


        return false;
    }

    function checkDeposit($param) {
        return $param['deposit'] <= $this->data['Invoice']['summary_unpaid'];
    }

    function checkCurrencyExists($param) {
        $currenciesList = getCurrenciesList();
        return isset($currenciesList[$param['currency_code']]);
    }

    function checkClientExists($param) {
        $check=$this->Client->hasAny(array('Client.id' => $param['client_id']));
        return $check ;
    }
    

    //-----------------------------------------
    function getFilters($action = 'index') {
					$this->loadModel('ItemsTag');

        $filters = array();
        if ($action == 'index') {
            $filters = array();
            if (getAuthOwner('enable_po')) {
                $filters['po_number'] = array('more-options' => true, 'label' => __('PO Number', true));
            }
            $advancePaymentCount = $this->find('count', ['conditions' => ['Invoice.type' => Invoice::ADVANCE_PAYMENT]]);

            $filters = $filters + array(
                'id' => array('div' => false, 'input_type' => 'hidden', 'more-options' => false,  'label' => false),
                'client_id' => array('div_class' => 'full-width', 'input_type' => 'advanced_client', 'more-options' => false, 'empty' => __('Any Client', true), 'label' => __('Client', true)),
                'tags' => array('div_class' => 'full-width', 'input_type' => 'tags-multiselect', 'more-options' => true, 'empty' => __('Any tags', true), 'label' => __('Tag', true), 'tag_type' => ItemsTag::model_name_to_tag_type($this->name)),
                'no' => array('type' => 'like','more-options' => false, 'label' => __('Invoice No.', true)),
                'item' => array('more-options' => true, 'label' => __('Contains Item', true)),
                'currency_code' => array('label' => __('Currency', true), 'empty' => __('Any', true), 'more-options' => true),
                'summary_total' => array('more-options' => true,'label' =>  __('Summary Total',true) ,'from_label' => __('Total more than', true), 'to_label' => __('Total less than', true), 'type' => 'number_range'),
                'payment_status' => array('more-options' => true,'label' =>  __('Payment Status',true) ,'empty' => __('Any', true)),
                'date' => array('more-options' => true,'label' =>  __('Date',true), 'type' => 'date_range', 'to_label' => __('Date To', true), 'from_label' => __('Date From', true)),
                'due_date' => array('more-options' => true,'label' => __('Due Date',true), 'type' => 'date_range', 'from_label' => __('Due after', true), 'to_label' => __('Due before', true)),
                'external_source' => array('label' => __('Source', true),'div_class' => 'full-width','more-options' => true, 'empty'=>__('All',true),'input_type' => 'multiselect','div' => 'form-group left', 'empty' => __('All', true), 'options' => InvoiceExternalSourceUtil::getSourceList()),
                'custom_field' => array('more-options' => true, 'label' => __('Custom Field', true)),
                'created' => array('more-options' => true,'label' =>  __('Created Date',true), 'type' => 'date_range', 'to_label' => __('Date To', true), 'from_label' => __('Date From', true)),
                'type' => array('div_class' => 'full-width ', 'options' => array('class' => 'selectpicker', 'options' => InvoiceTypeUtil::getSource($advancePaymentCount)), 'more-options' => true, 'empty' => __('All', true), 'label' => __('Invoice Type', true), 'div' => 'form-group left',)
            );

            $showForJordanianEInvoice = self::checkIfJordanEInvoiceEnabled();
            if (ifPluginActive(PluginUtil::ETA_PLUGIN) || (ifPluginActive(PluginUtil::EINVOICE_PLUGIN) && !empty(settings::getValueEvenEmpty(PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_binarySecurityToken'))) || $showForJordanianEInvoice) {
                $filters['einvoice_status'] = array('type' => 'select','options' => ['options' => self::getEInvoiceStatus()],'more-options' => true,'label' =>  __('Electronic Invoice Status',true) ,'empty' => __('Any', true));
            }

            $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions');
            if(!empty($enable_requisitions)) {

                $this->loadModel('Requisition');

                $filters['requisition_delivery_status'] =array('label' => __('Requisition Delivery Status', true),'div_class' => 'full-width','more-options' => true, 'empty'=>__('Select status',true),'input_type' => 'multiselect','div' => 'form-group left', 'empty' => __('All', true), 'options' => Requisition::getDeliveryStatus());
            }



            if (ifPluginActive(StaffPlugin) && check_permission(Invoices_View_All_Invoices)) {
                $filters['staff_id'] = array('input_type' => 'multiselect','more-options' => true, 'empty' => __('Any Staff', true), 'label' => __('Added By', true));
                $filters['sales_person_id'] = array('input_type' => 'multiselect', 'more-options' => true, 'empty' => __('Any Sales Person', true), 'label' => __('Sales Person', true), 'options' => ['options' => $this->Staff->getList()]);
            }
            if (ifPluginActive(PosPlugin) && check_permission(Invoices_View_All_Invoices) || check_permission(Invoices_View_Invoices_Details)) {
                $filters['pos_shift_id'] = array('input_type' => 'text', 'more-options' => true, 'empty' => __('Any POS Shift', true), 'label' => __('Pos Shift', true));
            }
        } elseif ($action == 'templates') {
            $filters = array(
                'name' => array('label' => __('Client', true)),
            );
        } elseif ($action == 'client') {
            $filters = array(
                'no' => array('type' => 'like','label' => __('Invoice No.', true)),
                'currency_code' => array('label' => __('Currency', true), 'empty' => __('Any', true), 'more-options' => true),
                'summary_total' => array('more-options' => true, 'from_label' => __('Total more than', true), 'to_label' => __('Total less than', true), 'type' => 'number_range'),
                'payment_status' => array('more-options' => true, 'empty' => __('Any', true)),
                'date' => array('more-options' => true, 'type' => 'date_range'),
                'due_date' => array('more-options' => true, 'type' => 'date_range', 'from_label' => __('Due after', true), 'to_label' => __('Due before', true)),
            );
        } elseif ($action == 'estimates') {
            $filters = array(
                'client_id' => array('input_type' => 'advanced_client', 'empty' => __('Any Client', true), 'label' => __('Client', true)),
                'no' => array('type' => 'like','label' => __('Estimate No.', true)),
                'currency_code' => array('label' => __('Currency', true), 'empty' => __('Any', true), 'more-options' => true),
                'summary_total' => array('more-options' => true, 'from_label' => __('Total more than', true), 'to_label' => __('Total less than', true), 'type' => 'number_range'),
                'payment_status' => array('more-options' => true, 'empty' => __('Any', true), 'label' => __('Status', true)),
                'date' => array('more-options' => true,'label' =>  __('Date',true), 'type' => 'date_range', 'to_label' => __('Date To', true), 'from_label' => __('Date From', true)),
				'tags' => array('div_class' => 'full-width', 'input_type' => 'tags-multiselect', 'more-options' => true, 'empty' => __('Any tags', true), 'label' => __('Tag', true), 'tag_type' => ItemsTag::model_name_to_tag_type('Estimate')),
                'created' => array('more-options' => true,'label' =>  __('Created Date',true), 'type' => 'date_range', 'to_label' => __('Date To', true), 'from_label' => __('Date From', true)),
                'item' => array('more-options' => true, 'label' => __('Contains Item', true)),

            );
            $this->loadModel('OrderSource');
            $orderSourceOptions = $this->OrderSource->getList([], false);
            $filters['order_source_id'] = [
                'options' => array('class' => 'selectpicker', 'options' => $orderSourceOptions),
                'more-options' => true,
                'empty' => __('All', true),
                'div_class' => 'full-width',
                'input_type' => 'advanced',
                'label' =>  __('Order Source', true),
                'advanced_controller' => 'order_sources',
                'advanced_model' => 'OrderSource',
                'advanced_action' => 'json_find',
            ];
            if (ifPluginActive(StaffPlugin) && check_permission(ESTIMATES_VIEW_ALL_ESTIMATES)) {
                $staffList = $this->Staff->getList();
                $filters['staff_id'] = array('input_type' => 'multiselect', 'more-options' => true, 'empty' => __('Any Staff', true), 'label' => __('Added By', true), 'options' => ['options' => $staffList]);
                $filters['sales_person_id'] = array('input_type' => 'multiselect', 'more-options' => true, 'empty' => __('Any Sales Person', true), 'label' => __('Sales Person', true), 'options' => ['options' => $staffList]);

            }
        } elseif ($action == 'estimates-client') {
            $filters = array(
                'no' => array('type' => 'like','label' => __('Estimate No.', true)),
                'currency_code' => array('label' => __('Currency', true), 'empty' => __('Any', true), 'more-options' => true),
                'summary_total' => array('more-options' => true, 'from_label' => __('Total more than', true), 'to_label' => __('Total less than', true), 'type' => 'number_range'),
                'payment_status' => array('more-options' => true, 'empty' => __('Any', true), 'label' => __('Status', true))
            );
        } elseif ($action == 'subscriptions') {
            $filters = array(
                'name' => array('type' => 'LIKE', 'label' => __('Subscription name', true)),
                'client_id' => array('input_type' => 'advanced_client', 'empty' => __('Any Client', true), 'label' => __('Client', true)),
                'date' => array('more-options' => true, 'type' => 'date_range', 'label' => __('Next invoice date', true)),
//				'due_date' => array('more-options' => true, 'type' => 'date_range', 'from_label' => __('Due after', true), 'to_label' => __('Due before', true)),
                'summary_total' => array('more-options' => true, 'from_label' => __('Total more than', true), 'to_label' => __('Total less than', true), 'type' => 'number_range'),
//				'payment_status' => array('more-options' => true, 'empty' => __('Any', true))
            );
        }else if($action == 'creditnotes'){
//			$filters = array(
//				'tags' => array('div_class' => 'full-width', 'input_type' => 'tags-multiselect', 'more-options' => false, 'empty' => __('Any tags', true), 'label' => __('Tag', true), 'tag_type' => ItemsTag::model_name_to_tag_type('Refund')),
//
//            );
		}else if($action == 'refund'){
					$filters = array(
				'tags' => array('div_class' => 'full-width', 'input_type' => 'tags-multiselect', 'more-options' => true, 'empty' => __('Any tags', true), 'label' => __('Tag', true), 'tag_type' => ItemsTag::model_name_to_tag_type('Refund')),
            );
		}
        
        return $filters;
    }


    //-----------------------------------------
    function getInvoice($id = null, $otherConditions = array() , $replace_units = false , $recursive = 1, $oldGetInvoice = false, $applyBranch = true) {
	    if (empty($otherConditions) &&  !$replace_units && $recursive == 1 && !$oldGetInvoice) {
            $inv=$this->getInvoiceQuick($id);
            if(!empty($inv['Invoice']['id'])) return $inv;
	    }

        $this->loadModel('Product');
        
        if (empty($id) && empty($otherConditions)) {
            return null;
        }

        $conditions = $otherConditions;
        if ($id !== null) {
            $conditions[$this->alias . '.id'] = $id;
        }

        if (self::checkIfJordanEInvoiceEnabled()){
            $this->bindModel(
                ['hasMany' => [
                    'EntityAppData' => [
                        'dependant' => true, 'className' => 'EntityAppData', 'foreignKey' => 'entity_id', 'conditions' => ['EntityAppData.entity_key' => 'invoice', 'EntityAppData.app_key' => EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE, 'EntityAppData.action_key' => ElectronicInvoicesActionsUtil::GET_DOCUMENT], 'order' => 'EntityAppData.id DESC', 'limit' => 1
                    ],
                    'ErrorEntityAppData' => [
                        'dependant' => true, 'className' => 'ErrorEntityAppData', 'foreignKey' => 'entity_id', 'conditions' => ['ErrorEntityAppData.entity_key' => 'invoice', 'ErrorEntityAppData.app_key' => EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE, 'ErrorEntityAppData.action_key' => ElectronicInvoicesActionsUtil::ERROR], 'order' => 'ErrorEntityAppData.id DESC', 'limit' => 1
                    ],
                    'SentEntityAppData' => [
                        'dependant' => true, 'className' => 'SentEntityAppData', 'foreignKey' => 'entity_id', 'conditions' => ['SentEntityAppData.entity_key' => 'invoice', 'SentEntityAppData.app_key' => EntityAppDataKeysUtil::JORDAN_ELECTRONIC_INVOICE, 'SentEntityAppData.action_key' => ElectronicInvoicesActionsUtil::SENT_DOCUMENT], 'order' => 'SentEntityAppData.id DESC', 'limit' => 1
                    ],
                ]]
            );
        }

        $invoice = $this->find(
            'first', [
                'conditions' => $conditions,
                'recursive' => $recursive,
                'applyBranchFind' => $applyBranch
            ]);

        if (ifPluginActive(InventoryPlugin)) {
            $Product = ClassRegistry::init('Product');
            $Product->recursive = -1;
            if (isset($invoice['InvoiceItem']))
            foreach ($invoice['InvoiceItem'] as $i => $item) {
                if (!empty($item['product_id'])) {
                    $prod = $Product->read(null, $item['product_id']);
                    $pp[0] = $prod['Product'];
                    
                    $invoice['InvoiceItem'][$i]['Product'] = $pp[0];
                    
                }
            }
        }

        if (!$invoice) {
            return false;
        }

//        $siteModel = getSiteModel();
//        $site = $siteModel->find(array('Site.id' => $invoice['Invoice']['site_id']));

        // warning suppress
        $invoice['Site'] = getCurrentSite();


		//$host = getCurrentSite('subdomain');
	    if (IS_REST && ifPluginActive(EINVOICE_SA_PLUGIN)) {
		    $invoice['Invoice']['qr_code_url'] = Invoice::getInvoiceQrCodeImageUrl($host, $invoice);
	    }
	    $invoice['Invoice']['invoice_html_url'] = Invoice::getInvoiceClientPreviewUrl($host, $invoice);
		$invoice['Invoice']['invoice_pdf_url'] = Invoice::getInvoiceClientPdfUrl($host, $invoice);
        return $this->prepareForView($invoice);
    }

	function getInvoiceQuick($id)
	{
        //@todo : find a better approach , causing an invalid query
		$custom_exist = $this->select_all('SHOW TABLES LIKE \'invoices_custom_data\'');
		if (count($custom_exist))
			$this->full_relations['relations']['CustomModel'] = ['table' => 'invoices_custom_data', 'type' => 'belongsTo', 'foreign_key' => 'invoice_id'];
		$invoice = $this->quick_find_with_relations('Invoice', $id, $this->full_relations);
		if (isset($invoice['Invoice']['date']) && isset($invoice['Invoice']['due_after'])) {
			$invoice['Invoice']['due_date'] = date('Y-m-d', strtotime('+' . $invoice['Invoice']['due_after'] . ' days', strtotime($invoice['Invoice']['date'])));
		}
		$enable_multi_units = settings::getValue(InventoryPlugin, 'enable_multi_units');
		if ($enable_multi_units) {
			$this->loadModel('Product');
			if (!isset($invoice['InvoiceTax'])) $invoice['InvoiceTax'] = [];
			foreach ($invoice['InvoiceItem'] as $i => $item) {
				if (!empty($item['product_id'])) {
					$pp[0] = $item['Product'];
					$this->Product->add_multi_units_quick($pp);
					$invoice['InvoiceItem'][$i]['Product'] = $pp[0];
				}
			}
		}
		foreach ($invoice['InvoiceItem'] as $i => $item) {
			if (!empty($item['ProductMasterImage']['file'])) {
				$invoice['InvoiceItem'][$i]['ProductMasterImage']['file_full_path'] = "/files/" . SITE_HASH . "/product-images/" . $item['ProductMasterImage']['file'];
			}
		}
		$invoice['Site'] = getCurrentSite();
		if (IS_REST) return $invoice;
		else return $this->prepareForView($invoice);
	}


    function copyInvoice($id, $extra, $save = false) {
		$this->recursive=1;
        $original = $this->find('first',array('conditions'=>array('Invoice.id'=>$id)));
        $siteId=getCurrentSite('id');

		unset($original['SalesPerson']);
		unset($original['Client']);
		
        if (!$original) {

            $result['Invoice']['id']=$id;
            $result['Invoice']['las_errors']=$this->query("SHOW ERRORS");
            $result['Invoice']['last_WARNINGS']=$this->query("SHOW WARNINGS");
            $result['Invoice']['saved']=false;
            return $result;
        }
        //Set
        $newInvoice = $original;
        $newInvoice['Invoice'] = array_merge($original['Invoice'], $extra);
//		$newInvoice['Invoice']['date'] = $original['Invoice']['original_date'];
        //Reset
        unset($newInvoice['Invoice']['summary_refund']);
        unset($newInvoice['Invoice']['id']);
        // Unset belongsTo
        unset($newInvoice['Staff']);
        unset($newInvoice['Country']);
        unset($newInvoice['ShippingOption']);

//		$newInvoice['Invoice']['draft'] = 1;
        $newInvoice['Invoice']['last_sent'] = '';
        $newInvoice['Invoice']['payment_status'] = 0;
        $newInvoice['Invoice']['summary_paid'] = 0;
        $newInvoice['Invoice']['summary_unpaid'] = $original['Invoice']['summary_total'];

        App::import('Vendor', 'AutoNumber');
		$extra['type']=isset($extra['type'])?$extra['type']:$newInvoice['Invoice']['type'];
		
			
		switch ($extra['type']) {
			case Invoice::Invoice:
				$newInvoice['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE);
				break;
			case Invoice::Estimate:
				$newInvoice['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_ESTIMATE);
				break;
            case Invoice::SALES_ORDER:
                $newInvoice['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_SALES_ORDER);
                break;
			case Invoice::BOOKING:
				$newInvoice['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_BOOKING);
				break;
            case Invoice::ADVANCE_PAYMENT:
                $newInvoice['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
                break;
			default:
				$newInvoice['Invoice']['no'] = '';
				break;
		}
		
        foreach ($newInvoice['InvoiceItem'] as $key => &$item) {
            unset($item['id'], $item['invoice_id']);
        }
        unset($item);

        foreach ($newInvoice['InvoiceReminder'] as $key => &$reminder) {
            unset($reminder['id'], $reminder['invoice_id']);
        }
        unset($reminder);

        foreach ($newInvoice['InvoiceCustomField'] as $key => &$field) {
            unset($field['id'], $field['invoice_id']);
        }
        unset($field);
        //Calculate totals was here
        if ($save) {
            if (!empty($extra['invoice_layout_id'])&&$extra['invoice_layout_id']==$original['Invoice']['invoice_layout_id']&&!empty($extra['estimate_id']))
            {
                //don't do any thing
            }
            else
            {
                $newInvoice=$this->fillInvoice($newInvoice, true);
            }

            $totals = $this->calculateTotals($newInvoice);
            $newInvoice['Invoice']['deposit_type'] = self::DEPOSIT_TYPE_UNPAID;
            $newInvoice['Invoice']['item_discount_amount'] = $totals['total_item_discount'];


            $newInvoice['InvoiceItem'] = $totals['InvoiceItems'];

            $newInvoice['InvoiceTax'] = $totals['taxes'];
            $newInvoice['Invoice']['summary_discount'] = $totals['invoiceDiscount'];
            $newInvoice['Invoice']['summary_total'] = $totals['invoiceTotal'];

            $newInvoice['Invoice']['summary_subtotal'] = $totals['invoiceSubtotal'];

            $newInvoice['Invoice']['summary_paid'] = 0;
            $newInvoice['Invoice']['summary_unpaid'] = $newInvoice['Invoice']['summary_total'];


            $newInvoice['Invoice']['deposit'] = $totals['invoiceDeposit'];
            $newInvoice['Invoice']['summary_deposit'] = $totals['invoiceDeposit'];

            // Mark Invoice as paid if Summary_unpaid less than or eqal 0;
            if ($newInvoice['Invoice']['summary_unpaid'] <= 0) {
                $newInvoice['Invoice']['payment_status'] = INVOICE_STATUS_PAID;
            }
        }

        $this->loadModel('Tax');
        foreach ($newInvoice['InvoiceTax'] as $key => &$tax) {
            $tax['name']=$this->Tax->read('name',$tax['tax_id'])['Tax']['name'];


            unset($tax['id'], $tax['invoice_id']);
        }
        unset($tax);

        foreach ($newInvoice['InvoiceDocument'] as $key => &$doc) {
            unset($doc['id'], $doc['invoice_id']);
        }
        unset($doc);
        unset($newInvoice['Site'], $newInvoice['Client'], $newInvoice['EmailLog'], $newInvoice['InvoicePayment'], $newInvoice['Invoice']['created'], $newInvoice['Invoice']['modified']);
		
		if(empty($newInvoice['InvoiceCustomField'])){
			unset($newInvoice['InvoiceCustomField']);
		}
		if(empty($newInvoice['InvoiceDocument'])){
			unset($newInvoice['InvoiceDocument']);
		}
		if(empty($newInvoice['InvoiceReminder'])){
			unset($newInvoice['InvoiceReminder']);
		}
		if(empty($newInvoice['InvoiceTax'])){
			unset($newInvoice['InvoiceTax']);
		}
        if ($save) {
            $this->create();
            unset($newInvoice['Invoice']['created'])   ;
            unset($newInvoice['Invoice']['modified'])   ;
			if ($this->saveAll($newInvoice, array('validate' => 'first'))) {

				switch ($newInvoice['Invoice']['type']) {
					case Invoice::Invoice:
						\AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE);
						break;
					case Invoice::Estimate:
						\AutoNumber::update_auto_serial(\AutoNumber::TYPE_ESTIMATE);
						break;
                    case Invoice::SALES_ORDER:
                        \AutoNumber::update_auto_serial(\AutoNumber::TYPE_SALES_ORDER);
                        break;
					case Invoice::BOOKING:
						\AutoNumber::update_auto_serial(\AutoNumber::TYPE_BOOKING);
						break;
                    case Invoice::ADVANCE_PAYMENT:
                        \AutoNumber::update_auto_serial(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
                        break;
				}
				$newInvoice['Invoice']['saved'] = true;
				$newInvoice['Invoice']['id'] = $this->getLastInsertID();

                $this->loadModel('JournalAccountRoute');

                $journalAccountRoute = $this->JournalAccountRoute->find('first', [
                    'conditions' => [
                        'entity_type' => Journal::SALES_ACCOUNT_ENTITY_TYPE,
                        'entity_id' => $id,
                    ]
                ]);
                
                if (!empty($journalAccountRoute)) {
                    unset($journalAccountRoute['JournalAccountRoute']['id']);
                    unset($journalAccountRoute['JournalAccountRoute']['entity_id']);

                    RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_INVOICE)
                        ->save($journalAccountRoute['JournalAccountRoute'], $newInvoice['Invoice']['id']);
                }

			} else {
	
				$newInvoice['Invoice']['saved'] = false;
			}
        }

        return $newInvoice;
    }

    function checkInvoiceName($param) {
        if (!empty($this->data['Invoice']['save_as_template']) || !empty($this->data['Invoice']['type'])) {
            return !empty($param['name']);
        } else {
            return true;
        }
    }

    function getTemplate($id, $is_estimate = false) {
        $conditions = array(); //array('Invoice.type' => array(0,1));
        $template = $this->getInvoice($id, $conditions);
		// Added $is_estimate to not bother checking for products when it's just an estimate
        if (!$is_estimate && !check_permission(INVOICE_ALL_PRODUCTS))
        {
            $this->loadModel('Product');
            $staff_id = getAuthOwner('staff_id');
            foreach ( $template['InvoiceItem'] as $k => $ii ) {
                $p = $this->Product->find ('all', ['conditions' => ['Product.staff_id' =>$staff_id, 'id' => $ii['product_id'] ] ]);
                if ( empty ( $p)){
                    unset($template['InvoiceItem'][$k]);//NOT permitted 
                }
            }
            
        }

        if($template['Invoice']['date']=="01/01/1970"){
            $template['Invoice']['date']= format_date(date("Y-m-d"));
        }
        if($template['Invoice']['issue_date']=="01/01/1970"){
            $template['Invoice']['issue_date']= format_date(date("Y-m-d"));
        }
        unset($template['Invoice']['id'], $template['Invoice']['name'], $template['Invoice']['no'], $template['Invoice']['type'],$template['Invoice']['summary_refund']);
        
        return $template;
    }

    function prepareForView($invoice) {

        $smallest_fraction = pow(10, CurrencyHelper::getFraction($invoice['Invoice']['currency_code']) * -1);
        $dateFormats = getDateFormats('keys');
        $invoice['Invoice']['str_date_format'] = $dateFormats[getCurrentSite('date_format')];
        if (empty($invoice['Invoice']['date'])) {
            $invoice['Invoice']['date'] = date('Y-m-d');
        }
        $invoiceTime = strtotime($this->formatDate($invoice['Invoice']['date']));
        $invoiceDueTime = strtotime('+' . intval($invoice['Invoice']['due_after']) . ' Days', $invoiceTime);
        $curTime = time();

        $results = $this->calculateTotals($invoice);
        $invoice['Invoice']['deposit_type'] = self::DEPOSIT_TYPE_UNPAID;


        $invoice['show_discount'] = $results['show_discount'];


//		if (empty($invoice['Invoice']['summary_total'])) {
        $invoice['InvoiceTax'] = $results['taxes'];

        $invoice['InvoiceItem'] = InvoiceItem::prepareForView($results['InvoiceItems'] );

        $invoice['Invoice']['summary_discount'] =  $results['invoiceDiscount'] ?? $invoice['Invoice']['summary_discount'];
        $invoice['Invoice']['summary_total'] = $results['invoiceTotal'] ?? $invoice['Invoice']['summary_total'];
        $invoice['Invoice']['summary_subtotal'] =  $results['invoiceSubtotal'] ?? $invoice['Invoice']['summary_subtotal'];
        if (ifPluginActive(INSURANCE_PLUGIN)) {
            $invoice['Invoice']['summary_copayment'] = $results['invoiceCopaymentTotal'];
        }
        if (!empty($results['invoiceDeposit'])){
            $invoice['Invoice']['summary_deposit'] = $results['invoiceDeposit'];
        }
        if (!empty($results['invoiceDeposit'])){
            $invoice['Invoice']['deposit'] = $results['invoiceDeposit'];
        }

        if (!in_array($invoice['Invoice']['type'], [3, 12])) {
            $invoice['Invoice']['summary_paid'] = $results['invoicePaid'];
            $refunds=$this->get_refund_total( $invoice['Invoice']['id']);
            $newUnpaid = $results['invoiceTotal'] - $invoice['Invoice']['summary_paid']-$refunds;
            


            $invoice['Invoice']['payment_status'] = 0;
            if (($invoice['Invoice']['summary_total'] - $invoice['Invoice']['summary_paid']) == 0 && !$invoice['Invoice']['draft']) {
                $invoice['Invoice']['payment_status'] = 2;
            } elseif ($invoice['Invoice']['summary_paid'] == 0) {
                $invoice['Invoice']['payment_status'] = 0;
            } elseif (($newUnpaid <= $smallest_fraction && $invoice['Invoice']['summary_total'] >= 0 ) || (($newUnpaid >= -$smallest_fraction && $invoice['Invoice']['summary_total'] < 0 ))) {
                $invoice['Invoice']['payment_status'] = 2;
            } else if (($invoice['Invoice']['summary_paid'] > 0 && $invoice['Invoice']['summary_total'] >= 0 ) || ($invoice['Invoice']['summary_paid'] < 0 && $invoice['Invoice']['summary_total'] < 0 )) {
                $invoice['Invoice']['payment_status'] = 1;
            }
            $invoice['Invoice']['summary_unpaid'] = $newUnpaid;
        }
        if ($invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT) {
            $invoiceRepo = new InvoiceRepository();
            $invoice['Invoice']['unsettledAmount'] = $invoiceRepo->getAdvanceInvoiceData([$invoice['Invoice']['id']])[0]['unSettledAmount'];
        }

        if($invoice['Invoice']['summary_refund']>0 && $invoice['Invoice']['summary_refund']>=($invoice['Invoice']['summary_total'] - 0.02)){
                $invoice['Invoice']['payment_status'] = INVOICE_STATUS_REFUNDED;
        }

		//}
        if ($invoice['Invoice']['type'] != 3) {
            if (empty($invoice['Invoice']['issue_date']))
                $invoice['Invoice']['issue_date'] = $invoice['Invoice']['date'];
            $issueTime = strtotime($this->formatDate($invoice['Invoice']['issue_date']));
            $invoice['Invoice']['issue_date'] = date('Y-m-d', $issueTime);
            if (!empty($invoice['Invoice']['from']) && strtotime($invoice['Invoice']['from']) > 0)
                $invoice['Invoice']['from'] = date('Y-m-d', strtotime($invoice['Invoice']['from']));

            if (!empty($invoice['Invoice']['to']) && strtotime($invoice['Invoice']['to']) > 0)
                $invoice['Invoice']['to'] = date('Y-m-d', strtotime($invoice['Invoice']['to']));

            if ($invoice['Invoice']['type'] == 2) {
                $invoice['Invoice']['from'] = date('Y-m-d', strtotime($invoice['Invoice']['date']));

                $invoice['Invoice']['to'] = date('Y-m-d', strtotime($invoice['Invoice']['date'] . ' -1 Day +' . $invoice['Invoice']['subscription_period'] . ' ' . $invoice['Invoice']['subscription_unit']));
            }

            $invoice['Invoice']['issued'] = ($issueTime <= $curTime);

            $dueTime = strtotime('+' . intval($invoice['Invoice']['due_after']) . ' days', $invoiceTime);
            
            $invoice['Invoice']['due_date'] = date('Y-m-d', $dueTime);
            $invoice['Invoice']['overdue'] = ($dueTime <= $curTime);

            $invoice['Invoice']['cur_date'] = date('Y-m-d', $curTime);
        }
        if (ifPluginActive(WorkOrderPlugin)&&!empty($invoice['Invoice']['work_order_id']))
        {
            $this->loadModel( 'WorkOrder');
            $this->WorkOrder->recursive = -1 ; 
            $work_order = $this->WorkOrder->find ('first' , ['fields' => 'number,title' , 'conditions' => ['WorkOrder.id'=>$invoice['Invoice']['work_order_id'] ] ] ) ;
            $work_order_text = $work_order['WorkOrder']['title'].' #' .$work_order['WorkOrder']['number'];
            $invoice['Invoice']['work_order_text'] = $work_order_text;
        }


        if (!empty($invoice['InvoiceCustomField'])) {
            $noEstimate = array('%due-date%', '%due-days%', '%paid-amount%', '%unpaid-amount%', '%deposit%');
            foreach ($invoice['InvoiceCustomField'] as &$customField) {
                if ($invoice['Invoice']['type'] == 3 && in_array($customField['placeholder'], $noEstimate)) {
                    continue;
                }
                if (!empty($customField['placeholder'])) {
                    switch (low($customField['placeholder'])) {
                        case '%work_order_number%' : 
                            if ( !empty ( $invoice['Invoice']['work_order_id']))
                            {
                                $customField['real-value'] =$work_order_text;
                                $customField['real-label'] = __("Work Order" , true );
                            }else {
                                $customField['real-value'] ='';
                                $customField['real-label'] = '';
                            }
                            
                            
                            break;
                        case '%total%':
                            $customField['real-value'] = format_price($invoice['Invoice']['summary_total'], $invoice['Invoice']['currency_code']);
                            $customField['real-label'] = __('Total', true);
                            break;
                        case '%due-date%':
                            $customField['real-value']= format_date(date("Y-m-d",$dueTime));
                            //$customField['real-value'] = format_date(strftime($invoice['Invoice']['str_date_format'], $dueTime));
                            
                            // echo strftime($invoice['Invoice']['str_date_format'], $dueTime);
                            $customField['real-label'] = __('Due Date', true);
                            break;
                        case '%due-days%':
                            $customField['real-value'] = sprintf(__('%s days', true), $invoice['Invoice']['due_after']);
                            $customField['real-label'] = __('Due After', true);
                            break;
                        case '%discount%':
                            $customField['real-value'] = '-' . format_price($invoice['Invoice']['summary_discount'], $invoice['Invoice']['currency_code']);
                            $customField['real-label'] = sprintf(__('Discount (%s%%)', true), $invoice['Invoice']['discount']);
                            break;
//						case '%tax1%':
//							$customField['real-value'] = format_price($invoice['Invoice']['summary_tax1'], $invoice['Invoice']['currency_code']);
//							$customField['real-label'] = sprintf(__('Tax1 (%s%%)', true), $invoice['Invoice']['tax1']);
//							break;
//						case '%tax2%':
//							$customField['real-value'] = format_price($invoice['Invoice']['summary_tax2'], $invoice['Invoice']['currency_code']);
//							$customField['real-label'] = sprintf(__('Tax2 (%s%%)', true), $invoice['Invoice']['tax2']);
//							break;
                        case '%deposit%':
                            $customField['real-value'] = format_price($invoice['Invoice']['deposit'], $invoice['Invoice']['currency_code']);
                            $customField['real-label'] = __('Advance payment', true);
                            break;
                        case '%paid-amount%':
                            $customField['real-value'] = '-' . format_price($invoice['Invoice']['summary_paid'], $invoice['Invoice']['currency_code']);
                            $customField['real-label'] = __('Paid', true);
                            break;
                        case '%unpaid-amount%':
                            $customField['real-value'] = format_price($invoice['Invoice']['summary_unpaid'], $invoice['Invoice']['currency_code']);
                            $customField['real-label'] = __('Unpaid', true);
                            break;
                        case '%status%':
                            $statuses = self::getPaymentStatuses();
                            $customField['real-value'] = $statuses[intval($invoice['Invoice']['payment_status'])];
                            $customField['real-label'] = __('Status', true);
                            break;
                        case '%from%':
                            $customField['real-value'] = '';
                            $fromTime = strtotime($this->formatDate($invoice['Invoice']['from']));
                            if (!empty($fromTime)) {
                                $customField['real-value'] = strftime($invoice['Invoice']['str_date_format'], strtotime($this->formatDate($invoice['Invoice']['from'])));
                            }
                            $customField['real-label'] = __('From', true);
                            break;
                        case '%to%':
                            $customField['real-value'] = '';
                            $toTime = strtotime($this->formatDate($invoice['Invoice']['to']));
                            if (!empty($toTime)) {
                                $customField['real-value'] = strftime($invoice['Invoice']['str_date_format'], strtotime($this->formatDate($invoice['Invoice']['to'])));
                            }
                            $customField['real-label'] = __('To', true);
                            break;
                        case '%order-source%':
                            $orderSource = __('None', true);
                            if (!empty($invoice['Invoice']['order_source_id'])) {
                                $OrderSourceModel = GetObjectOrLoadModel('OrderSource');
                                $orderSourceObj = $OrderSourceModel->find(array('OrderSource.id' => $invoice['Invoice']['order_source_id']));
                                $orderSource = $orderSourceObj['OrderSource']['name'];
                            }
                            $customField['real-value'] = $orderSource;
                            $customField['real-label'] = __('Order Source', true);
                            break;
                        default:
                            $customField['real-value'] = $customField['placeholder'];
                            $customField['real-label'] = $customField['label'];
                    }
                } else {

                    $customField['real-value'] = $customField['value'];
                    $customField['real-label'] = $customField['label'];
                }
            }
        }


        if (!empty($invoice['InvoiceReminder'])) {
            foreach ($invoice['InvoiceReminder'] as &$reminder) {
                switch ($reminder['send_when']) {
                    case 0:
                        $reminderTime = $issueTime;
                        break;
                    case 1:
                        $reminderTime = $invoiceTime;
                        break;
                    case 2:
                        $reminderTime = strtotime('+' . intval($invoice['Invoice']['due_after']) . ' days', strtotime($invoice['Invoice']['date']));
                        break;
                    case 3:
                        $days = intval($reminder['days']);
                        $reminderTime = strtotime('-' . $days . ' days', $invoiceTime);
                        break;
                    case 4:
                        $days = intval($reminder['days']);
                        $reminderTime = strtotime('+' . $days . ' days', $invoiceTime);
                        break;
                }
                $reminder['date'] = date('Y-m-d', $reminderTime);
                $reminder['date_formatted'] = strftime($invoice['Invoice']['str_date_format'], $reminderTime);
            }
        }
        $invoice['Invoice']['original_date'] = $invoice['Invoice']['date'];
        $invoice['Invoice']['date'] = date('Y-m-d', $invoiceTime);
        $invoice['Invoice']['due_date'] = date('Y-m-d', $invoiceDueTime);

        if ((!empty($invoice['Client']['client_from_data']) || empty($invoice['Client'])) && !empty($invoice['Invoice']['client_id'])) {
            $client = ClassRegistry::init('Client')->find('first', ['conditions' => ['Client.id' => $invoice['Invoice']['client_id']]]);
            $invoice['Client'] = $client['Client'];
        }

        if (empty($invoice['Invoice']['client_business_name'])) {
            foreach ($invoice['Client'] as $field => $value) {
                $invoice['Invoice']['client_' . $field] = $value;
            }
        }

        if (empty($invoice['InvoiceTax']) && !empty($invoice['Invoice']['id'])) {
            $invoice_taxs = $this->InvoiceTax->find('all', array('conditions' => array('InvoiceTax.invoice_id' => $invoice['Invoice']['id'])));

            foreach ($invoice_taxs as $invoice_tax) {
                $invoice['InvoiceTax'][] = $invoice_tax['InvoiceTax'];
            }
        }



        if (!isset($invoice['Invoice']['invoice_layout_id'])) {
            $invoice['Invoice']['invoice_layout_id'] = 0;
        }

        $tmp = getCurrentSite('invoice_default_title');

        if (!empty($tmp) and $invoice['Invoice']['type'] == 0) {
             $invoice['invoice_default_title'] = $tmp;
        } elseif ($invoice['Invoice']['type'] == Invoice::Credit_Note) {
             $invoice['invoice_default_title'] = __('Credit Note', true);
        } elseif ($invoice['Invoice']['type'] == Invoice::Refund_Receipt) {
           $invoice['invoice_default_title'] = __('Refund Receipt', true);
        } elseif ($invoice['Invoice']['type'] == Invoice::DEBIT_NOTE){
            $invoice['invoice_default_title'] = __('Debit Note', true);
        } elseif ($invoice['Invoice']['type'] == Invoice::SALES_ORDER){
            $invoice['invoice_default_title'] = __('Sales Order', true);
        } elseif ($invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT){
            $invoice['invoice_default_title'] =__('Advance Payment', true);
        } else {
            $invoice['invoice_default_title'] = __('Invoice', true);
        }

        $invoice['Invoice']['date'] = format_date($invoice['Invoice']['date']);
        if($invoice['Invoice']['issue_date'] === '0000-00-00'){
            $invoice['Invoice']['issue_date'] = null ; 
        }
        $invoice['Invoice']['issue_date'] = format_date($invoice['Invoice']['issue_date']);
        $invoice['Invoice']['due_date'] = format_date($invoice['Invoice']['due_date']);

        if($invoice['show_discount'])
            $invoice['Invoice']['summary_total_discount']=$this->calculate_total_discount($invoice);

        if (!empty($invoice['Invoice']['from'])) {
            $invoice['Invoice']['from'] = format_date($invoice['Invoice']['from']);
        }
        if (!empty($invoice['Invoice']['to'])) {
            $invoice['Invoice']['to'] = format_date(date('Y-m-d',strtotime($invoice['Invoice']['to'])));
        }

        if(abs($invoice['Invoice']['summary_unpaid']) < $smallest_fraction) {
            $invoice['Invoice']['summary_unpaid'] = 0;
        }

        if($invoice['Invoice']['type']==0){
            $invoice['Invoice']['summary_refund']=$this->get_refund_total($invoice['Invoice']['id']);
        }

        if (ifPluginActive(InventoryPlugin)) {
            $Product = ClassRegistry::init('Product');
            $Product->recursive = -1;
            if (isset($invoice['InvoiceItem']))
            foreach ($invoice['InvoiceItem'] as $i => $item) {
                if (!empty($item['product_id'])) {
                    $prod = $Product->read(null, $item['product_id']);
                    $prod['Product']['unit_factors'] = $item['Product']['unit_factors']??[];
                    $prod['Product']['factor'] = $item['Product']['factor']??[];
                    $pp[0] = $prod['Product'];
                    $invoice['InvoiceItem'][$i]['Product'] = $pp[0];
                    
                }
            }
        }


        return $invoice;


    }

    function updateInvoicePayments($id=null) {
        if($id==null){
            return array('status' => false);
        }

        App::import('Vendor', 'AutoNumber');
        $this->applyBranch['onFind'] = false;
        $invoice = $this->find('first', array('conditions' => array('Invoice.id' => $id), 'recursive' => -1));

        if(empty($invoice)){
            return array('status' => false);
        }

        $this->InvoicePayment->alias = 'Payment';
        $this->InvoicePayment->applyBranch['onFind'] = false;
        unset($this->InvoicePayment->virtualFields['amount_currency']);
        $payments = $this->InvoicePayment->find('all', array('conditions' => array('Payment.invoice_id' => $id, 'Payment.status' => 1), 'applyBranchFind' => false, 'recursive' => -1));
        $isDraft = false;
        if($invoice['Invoice']['draft']) {
            $isDraft = true;
        }
        if (is_countable($payments) && count($payments) > 0) {
	        $invoice['Invoice']['draft'] = 0;
	        $invoice['Invoice']['issued'] = 1;
            if($isDraft && strpos($invoice['Invoice']['no'], 'draft') !== false && $invoice['Invoice']['type'] == self::Invoice) {
                $invoice['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE);
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE);
            }
        }
        $totalPaid = 0;
        foreach ($payments as $payment) {

            if($payment['Payment']['amount']<0){
                $payment['Payment']['amount']=$payment['Payment']['amount']*-1;
            }
            $totalPaid += $payment['Payment']['amount'];
        }
        if (settings::getValue(SalesPlugin, 'enable_advance_payment')) {
            $invoiceRepo = new InvoiceRepository();
            $advancePaymentService = new AdvancePaymentService($invoiceRepo);
            $advancePayments = $advancePaymentService->getInvoiceWithAdvancePayments($id);
            foreach ($advancePayments['relatedInvoices'] as $advancePayment) {
                $totalPaid += $advancePayment['salesAmount'];
            }
        }
        $invoice['Invoice']['summary_paid'] = $totalPaid;
        $refunds=$this->get_refund_total($id);
        
        $invoice['Invoice']['summary_unpaid'] = $invoice['Invoice']['summary_total'] - $totalPaid-$refunds;
        $invoice['Invoice']['summary_refund'] = $refunds;
        
        

        $smallest_fraction = pow(10, CurrencyHelper::getFraction($invoice['Invoice']['currency_code']) * -1);
        if ($invoice['Invoice']['type'] != self::SALES_ORDER) {
            if ($invoice['Invoice']['summary_unpaid'] <= $smallest_fraction) {
                $invoice['Invoice']['summary_unpaid'] = 0;
                $invoice['Invoice']['payment_status'] = INVOICE_STATUS_PAID;

                if ($invoice['Invoice']['summary_total'] <= $invoice['Invoice']['summary_refund'] && $invoice['Invoice']['summary_refund'] > 0) {
                    $invoice['Invoice']['payment_status'] = INVOICE_STATUS_REFUNDED;
                }


            } elseif ($totalPaid > 0) {
                $invoice['Invoice']['payment_status'] = INVOICE_STATUS_PARTIAL_PAID;
            } else if ($totalPaid == 0 and $invoice['Invoice']['summary_unpaid'] > 0) {
                $invoice['Invoice']['payment_status'] = INVOICE_STATUS_UNPAID;
            } else if ($invoice['Invoice']['summary_unpaid'] == 0) {
                $invoice['Invoice']['payment_status'] = INVOICE_STATUS_PAID;

            }
        }



        if (empty($this->data['Invoice']['deposit']) && $invoice['Invoice']['summary_paid'] > 0.01) {

          //  $invoice['Invoice']['summary_deposit'] = $invoice['Invoice']['summary_unpaid'];
        } else if (empty($this->data['Invoice']['deposit']) && $totalPaid <= 0.01) {
            $invoice['Invoice']['summary_deposit'] = 0;
        } else if (!empty($this->data['Invoice']['deposit']) && $invoice['Invoice']['summary_unpaid'] < $this->data['Invoice']['deposit']) {
            $invoice['Invoice']['summary_deposit'] = $invoice['Invoice']['summary_unpaid'];
        }
        

        if ($invoice['Invoice']['deposit'] > $invoice['Invoice']['summary_unpaid'])
            $invoice['Invoice']['deposit'] = $invoice['Invoice']['summary_unpaid'];
		$this->InvoicePayment->alias = 'Payment';
	    $paymentsCount = $this->InvoicePayment->find('count', array('conditions' => array('Payment.invoice_id' => $id, 'Payment.status' => [PAYMENT_STATUS_COMPLETED, PAYMENT_STATUS_PENDING])));
	    if ($invoice['Invoice']['type'] == Invoice::TEMPINVOICE && $paymentsCount > 0) {
		    $this->updateInvoiceType($id, Invoice::Invoice, true, true);
//		    $this->updateNextInvoiceNo($id, Invoice::Invoice);
		    unset($invoice['Invoice']['type']);
		    //unset($invoice['Invoice']['no']);
		    unset($invoice['Invoice']['requisition_delivery_status']);
	    }
//		$invoice['Invoice']['deposit']=0;
//		$invoice['Invoice']['draft']=0;

        // stored_due_date caused an error when edit invoice . 
        if(array_key_exists('stored_due_date' , $invoice['Invoice'])){
            unset($invoice['Invoice']['stored_due_date']);
        }
        if(Settings::getValue(0, 'round_invoices', null, false, false)){
            $round_to = CurrencyHelper::getFraction($invoice['Invoice']['currency_code']);
            $invoice['Invoice']['summary_unpaid'] = round($invoice['Invoice']['summary_unpaid'], $round_to);
            $invoice['Invoice']['summary_paid'] = round($invoice['Invoice']['summary_paid'], $round_to);
            $invoice['Invoice']['deposit'] = round($invoice['Invoice']['deposit'], $round_to);
            $invoice['Invoice']['summary_refund'] = round($invoice['Invoice']['summary_refund'], $round_to);
        }

        if ($this->save($invoice, array('validate' => false, 'callbacks' => false))) {
            $this->invoiceSavedCallback($invoice);
            // create credit Charges
            /**
             * ################## CAUTION ################################
             */
            // will reflect in memebership and renwals
            if (ifPluginActive(CREDIT_PLUGIN)) {
                $timeout = 60;
                set_time_limit($timeout);
                $this->loadModel('Product');
                $invoiceData = $this->getInvoice($id, [] ,false ,2);
                $invoiceItems = $invoiceData['InvoiceItem'];
                $isContainProductsWithPackageAsSourceType = false;
                foreach ($invoiceItems as $invoiceItem) {
                    if (isset($invoiceItem['Product']) && $invoiceItem['Product']['source_type'] == Product::PACKAGE_SOURCE) {
                        $isContainProductsWithPackageAsSourceType = true;
                        break;
                    }
                }
                $invoiceStatus = $invoiceData['Invoice']['payment_status'];
                if ($isContainProductsWithPackageAsSourceType) {
                    App::import('Component', 'ApiRequestsComponent');
                    $apiRequests = new ApiRequestsComponent();
                    $invoiceId = $invoiceData['Invoice']['id'];
                    if ($invoiceStatus == INVOICE_STATUS_PARTIAL_PAID || $invoiceStatus == INVOICE_STATUS_PAID) {
                        $apiRequests->request("/v2/api/invoice/{$invoiceId}/generate-charges",false,'POST', [], [], $timeout);
                    } else if ($invoiceStatus == INVOICE_STATUS_UNPAID) {
                        $apiRequests->request("/v2/api/invoice/{$invoiceId}/suspend-charges",false,'POST', [], [], $timeout);
                    }
                }
            }
            if (ifPluginActive(INSTALLMENT_AGREEMENT_PLUGIN)) {
	            $timeout = 60;
	            $this->loadModel('InvoiceInstallmentAgreement');
                $agreement = $this->InvoiceInstallmentAgreement->find('first', [
                    'conditions' => [
                        'invoice_id' => $id,
                        'deleted_at' => null
                    ],
                    'recursive' => -1
                ]);

                if ($agreement) {
                    App::import('Component', 'ApiRequestsComponent');
                    $apiRequests = new ApiRequestsComponent();
                    $apiRequests->request("/v2/api/invoice/{$id}/pay-agreement-installment",false,'POST', [], [], $timeout);
                }
            }
            if(ifPluginActive(LEASE_CONTRACT_PLUGIN) && $invoice['Invoice']['source_type'] == InvoiceSourceTypesUtil::CONTRACT_INSTALLMENT){
                $leaseContractInstallmentsHandler = new \Izam\Rental\Services\LeaseContractInstallmentsHandler();
                $leaseContractInstallmentsHandler->payInstallments($invoice['Invoice']['source_id'] );
            }

            foreach ($payments as $payment) {
                $this->InvoicePayment->update_journals($payment);
            }
            App::import('Vendor', 'notification_2');
            if (!empty($invoice['Invoice']['subscription_id']) && $invoice['Invoice']['payment_status'] == 2)
            NotificationV2::delete_notificationbyref($id, NotificationV2::RECURRING_INVOICE_GENERATED);

            return array('status' => true, 'invoice' => $invoice['Invoice']);
        }


        return array('status' => false);
    }

    function ownerAddPayment($data, $options = []) {
        $options = array_merge([
            'check_client_credit' => true
        ],$options);
        $invoice = $this->find('first', array('applyBranchFind' => false, 'conditions' => array('Invoice.id' => $data['InvoicePayment']['invoice_id']), 'recursive' => 1 ));

        if ((empty($data['InvoicePayment']['amount']) and $data['InvoicePayment']['amount']!=0) || !is_numeric($data['InvoicePayment']['amount'])) {
            $this->InvoicePayment->validationErrors['amount'] = __('Invalid amount', true);
            //because if not set it make errors in import without adding value in payment
            $data['InvoicePayment']['amount'] = 0;
        }

		if (empty($data['InvoicePayment']['date'])) {
			$data['InvoicePayment']['date'] = $invoice['Invoice']['date'] ?: date('Y-m-d');
		}

        if (isset($data['InvoicePayment']['status']) && $data['InvoicePayment']['status'] == '') {
            $this->InvoicePayment->validationErrors['status'] = __('Required', true);
        } elseif (isset($data['InvoicePayment']['status']) && $data['InvoicePayment']['status'] != '') {
            $statuses = InvoicePayment::getPaymentStatus();
            if (!isset($statuses[$data['InvoicePayment']['status']])) {
                $this->InvoicePayment->validationErrors['status'] = __('Invalid status', true);
            }
        }

        if (ifPluginActive(BranchesPlugin) && empty($data['InvoicePayment']['branch_id']) && isset($invoice['Invoice']['branch_id'])) {
            /**
             * some time it pay InvoicePayment  by function adjust_and_pay to pay from  client credit and be in different branch than invoice
             * this fix invoice payment has different branch than invoice
             */
            $data['InvoicePayment']['branch_id'] = $invoice['Invoice']['branch_id'];
        }

        if (empty($data['InvoicePayment']['payment_method'])) {
            $this->InvoicePayment->validationErrors['payment_method'] = __('Required', true);
        }
        if(!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices')){
        if ($data['InvoicePayment']['payment_method']=='client_credit') {
            $this->loadModel('Invoice');
            $this->Invoice->applyBranch['onFind'] = false;
            $this->loadModel('InvoicePayment');
            $this->InvoicePayment->applyBranch['onFind'] = false;

        $client_credit = $this->Client->client_credit($invoice['Invoice']['client_id'], $invoice['Invoice']['currency_code']);
        if ($options['check_client_credit'] && ($client_credit < $data['InvoicePayment']['amount'] && abs($client_credit - $data['InvoicePayment']['amount']) > 0.001)) {
            $this->InvoicePayment->validationErrors['amount'] = sprintf(__('Value must be less or equal to %s', true), $client_credit);
        }

        }
        }

        if (strlen($data['InvoicePayment']['notes']) > 500) {
            $this->InvoicePayment->validationErrors['notes'] = __('Only 500 characters allowed', true);
        }

		$empty_date_message = $this->validateIsOpenedPeriod($data['InvoicePayment']['date']);
        if ( $empty_date_message !== true) {
            $this->InvoicePayment->validationErrors['date'] = $empty_date_message;
        }

        $fields = array('first_name', 'last_name', 'address1', 'address2', 'city', 'state', 'postal_code', 'country_code');

        foreach ($fields as $field) {
            $data['InvoicePayment'][$field] = $invoice['Invoice']['client_' . $field];
        }

        $data['InvoicePayment']['added_by'] = 1;
        $data['InvoicePayment']['currency_code'] = $invoice['Invoice']['currency_code'];
        if (empty($data['InvoicePayment']['date'])) {
            $data['InvoicePayment']['date'] = date('Y-m-d');
        } else {
            $data['InvoicePayment']['date'] = $this->formatDate($data['InvoicePayment']['date']);
        }
        
        if (empty($data['InvoicePayment']['ip'])) {
            $data['InvoicePayment']['ip'] = get_real_ip();
        }
        App::import('Vendor', 'AutoNumber');
        if (in_array($invoice['Invoice']['type'], [Invoice::Invoice, Invoice::DEBIT_NOTE])){
            $data['InvoicePayment']['code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
        }elseif($invoice['Invoice']['type'] == Invoice::Refund_Receipt){
            $data['InvoicePayment']['code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_REFUND_PAYMENT);
        }

        // to correct when there is a very small diff between amount and invoice total
        $abs_diff = abs($invoice['Invoice']['summary_total'] - $invoice['Invoice']['summary_paid'] - (float) $data['InvoicePayment']['amount'] - $invoice['Invoice']['summary_refund']);
        $smallest_fraction = pow(10, CurrencyHelper::getFraction($invoice['Invoice']['currency_code']) * -1);
        if ($data['InvoicePayment']['status'] == 1 & $abs_diff > 0 && $abs_diff < $smallest_fraction) {
            $data['InvoicePayment']['amount'] += $invoice['Invoice']['summary_total'] - $invoice['Invoice']['summary_paid'] - (float) $data['InvoicePayment']['amount'] - $invoice['Invoice']['summary_refund'];
        }

        if ($data['InvoicePayment']['manual_payment'] != "1" && empty($this->InvoicePayment->validationErrors) && $data['InvoicePayment']['payment_method'] == "paytabs") {
		    App::import('Vendor', 'PayTabsPayment', array('file' => 'payments/PayTabsPayment.php'));
		    $PaymentGateway = ClassRegistry::init('SitePaymentGateway');
		    $pg = $PaymentGateway->find(array('payment_gateway' => $data['InvoicePayment']['payment_method']), null, null, -1);
		    $userOptions = $pg['SitePaymentGateway'];

		    $paymentData = $data['InvoicePayment'];
		    $invoice = $this->find('first', array('conditions' => array('Invoice.id' => $paymentData['invoice_id']), 'recursive' => 1));

		    $paymentData['status'] = 0;
		    $paymentData['email'] = $invoice['Client']['email'];
		    $paymentData['source'] = 'owner_add_payment';
		    $this->InvoicePayment->create();
		    $this->InvoicePayment->alias = 'InvoicePayment';
		    $this->InvoicePayment->save($paymentData, array('validate' => false, 'fieldList' => null));
            if (in_array($invoice['Invoice']['type'], [Invoice::Invoice, Invoice::DEBIT_NOTE])){
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
            }elseif($invoice['Invoice']['type'] == Invoice::Refund_Receipt){
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_REFUND_PAYMENT);
            }
		    $paymentData['id'] = $this->InvoicePayment->id;
		    $paymentData['invoice'] = $invoice['Invoice'];
		    $paymentData['invoiceItems'] = $invoice['InvoiceItem'];
		    $paymentData['client'] = $invoice['Client'];

		    $paymentMethod = new PayTabsPayment();
		    $url = $paymentMethod->getPaymentURL($userOptions, $paymentData);
		    if ($url) {
			    return array('out' => true, 'url' => $url);
		    } else {
                if (!empty($paymentMethod->rejectedFields))
                    $errorMessage = "Please update client information before proceeding to payment. (" . implode(',', $paymentMethod->rejectedFields) . ")";
                else
                    $errorMessage = "Error Creating Paytabs Payment Page. " . $paymentMethod->errorMsg;

			    return array('status' => false, 'error_message' => $errorMessage);
		    }
	    }

        if ($data['InvoicePayment']['manual_payment'] != "1" && empty($this->InvoicePayment->validationErrors) && $data['InvoicePayment']['payment_method'] == "tamara") {

            // Handle Refund payment
            if (Invoice::Refund_Receipt == $invoice['Invoice']['type']) {
                $gateway = ClassRegistry::init('SitePaymentGateway');
                $gatewayOptions = $gateway->find('first', [
                    'conditions' => ['payment_gateway' => 'tamara']
                ]);
                $refundService = new RefundService(
                    $gatewayOptions['SitePaymentGateway']['username'],
                    $gatewayOptions['SitePaymentGateway']['option1'],
                );

                $refundService->setComment(
                    'Refund process for #' . $invoice['Invoice']['id']
                );

                $refundService->setTotalAmount(
                    $data['InvoicePayment']['amount'] * -1
                );

                $refundService->setCurrency(
                    $data['InvoicePayment']['currency_code']
                );

                $refundService->setOrderId(
                    $_POST['tamara_payments_id']
                ); // transaction id from tamara

                $refundResponse = $refundService->processService();

                if (
                    isset($refundResponse['order_id'])
                    && $refundResponse['order_id'] == $_POST['tamara_payments_id']
                ) {
                    $paymentData = $data['InvoicePayment'];
                    $paymentData['transaction_id'] = $refundResponse['order_id'];
                    $paymentData['status'] = PAYMENT_STATUS_COMPLETED;
                    $paymentData['email'] = $invoice['Client']['email'];
                    $paymentData['source'] = 'owner_add_payment';
                    $paymentData['extra_details'] = json_encode($refundResponse);
                    $this->InvoicePayment->create();
                    $this->InvoicePayment->alias = 'InvoicePayment';
                    $this->InvoicePayment->save($paymentData, [
                        'validate' => false,
                        'fieldList' => null
                    ]);

                    return [
                        'status' => true,
                        'out' => false,
                        'payment_id' => $refundResponse['order_id']
                    ];
                } else if (
                    isset($refundResponse['status'])
                    && $refundResponse['status'] == 'ERR'
                ) {
                    return [
                        'status' => false,
                        'error_message' => $refundResponse['error']['message']
                    ];
                } else {
                    return [
                        'status' => false,
                        'error_message' => 'Unexpected error'
                    ];
                }
            }

            $instoreService = new \App\Services\PaymentGateways\Tamara\TamaraInStoreService();
            $phoneNumber = $_POST['tamaraPhone'];
            $client_data = $this->Client->find("first", ['conditions' =>['Client.id'=>$invoice["Invoice"]["client_id"]]]);
            $data['Invoice'] = $invoice['Invoice'];
            $data['InvoiceItem'] = $invoice['InvoiceItem'];
            $response =  json_decode($instoreService->createCheckout($client_data, $data, $phoneNumber), true);
            $paymentData = $data['InvoicePayment'];

             if ($response['order_id']){
                $paymentData['transaction_id'] = $response['order_id'];
                $paymentData['status'] = PAYMENT_STATUS_PENDING;
                $paymentData['email'] = $invoice['Client']['email'];
                $paymentData['source'] = 'owner_add_payment';
                $paymentData['extra_details'] = json_encode(['checkout-id' => $response['checkout_id']]);
                $this->InvoicePayment->create();
                $this->InvoicePayment->alias = 'InvoicePayment';
                $this->InvoicePayment->save($paymentData, array('validate' => false, 'fieldList' => null));
                if (in_array($invoice['Invoice']['type'], [Invoice::Invoice, Invoice::DEBIT_NOTE])){
                    \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
                }elseif($invoice['Invoice']['type'] == Invoice::Refund_Receipt){
                    \AutoNumber::update_auto_serial(\AutoNumber::TYPE_REFUND_PAYMENT);
                }
                return array('status' => true, 'out' => false, 'payment_id' => $paymentData['id']);
            } else {
                 return array('status' => false, 'out' => false, 'error_message' => $response['error_message']);
             }
        }

        if ($data['InvoicePayment']['manual_payment'] != "1" && empty($this->InvoicePayment->validationErrors) && $data['InvoicePayment']['payment_method'] == "tabby") {
            // Handle Refund payment
            if (Invoice::Refund_Receipt == $invoice['Invoice']['type']) {
                $gateway = ClassRegistry::init('SitePaymentGateway');
                $gatewayOptions = $gateway->find('first', [
                    'conditions' => ['payment_gateway' => 'tabby']
                ]);
                $refundService = new TabbyRefundService(
                    $gatewayOptions['SitePaymentGateway']['username'],
                    $gatewayOptions['SitePaymentGateway']['option1'],
                    getenv('TABBY_ENVIRONMENT', 'sandbox')
                );

                $refundService->setComment(
                    'Refund process for #' . $invoice['Invoice']['id']
                );

                $refundService->setRefundAmount(
                    $data['InvoicePayment']['amount'] * -1
                );

                $refundService->setCurrency(
                    $data['InvoicePayment']['currency_code']
                );

                $refundService->setOrderId(
                    $_POST['tabby_payments_id']
                ); // transaction id from tamara

                $refundResponse = $refundService->processService();

                if (
                    isset($refundResponse['data']['id'])
                    && $refundResponse['data']['id'] == $_POST['tabby_payments_id']
                ) {
                    $paymentData = $data['InvoicePayment'];
                    $paymentData['transaction_id'] = $refundResponse['data']['id'];
                    $paymentData['status'] = PAYMENT_STATUS_COMPLETED;
                    $paymentData['email'] = $invoice['Client']['email'];
                    $paymentData['source'] = 'owner_add_payment';
                    $paymentData['extra_details'] = json_encode($refundResponse);
                    $this->InvoicePayment->create();
                    $this->InvoicePayment->alias = 'InvoicePayment';
                    $this->InvoicePayment->save($paymentData, [
                        'validate' => false,
                        'fieldList' => null
                    ]);

                    return [
                        'status' => true,
                        'out' => false,
                        'payment_id' => $refundResponse['data']['id']
                    ];
                } else if (
                    isset($refundResponse['status'])
                    && $refundResponse['status'] == 'ERR'
                ) {
                    return [
                        'status' => false,
                        'error_message' => $refundResponse['errors'][0]
                    ];
                } else {
                    return [
                        'status' => false,
                        'error_message' => 'Unexpected error'
                    ];
                }
            }
            $phoneNumber = $_POST['tabbyPhone'];
            $this->loadModel('SitePaymentGateway');
            $instoreService = new TabbyPayment();
            $userOptions = $this->SitePaymentGateway->find('first', [
                'conditions' => [
                    'SitePaymentGateway.payment_gateway' => 'tabby'
                ]
            ]);
            $data['client_phone_number'] = $phoneNumber;
            $data['source'] = "POS";
             $this->InvoicePayment->save($data['InvoicePayment'], [
                'validate' => false,
                'fieldList' => null
            ]);
            $data['InvoicePayment']['id'] = $this->InvoicePayment->getLastInsertID();

            $response =  $instoreService->getPaymentURL($userOptions['SitePaymentGateway'], array_merge($invoice, $data));
            $paymentData = $data['InvoicePayment'];

            if ($response['web_url']){
                $paymentData['transaction_id'] = $response['transaction_id'];
                $paymentData['status'] = PAYMENT_STATUS_PENDING;
                $paymentData['email'] = $invoice['Client']['email'];
                $paymentData['source'] = 'owner_add_payment';
                $paymentData['extra_details'] = json_encode(['checkout-id' => $response['checkout_id']]);
                $this->InvoicePayment->create();
                $this->InvoicePayment->alias = 'InvoicePayment';
                $this->InvoicePayment->save($paymentData, array('validate' => false, 'fieldList' => null));
                $expires_at = str_replace('T', ' ', $response['expires_at']);
                $expires_at = str_replace('Z', '', $expires_at);
                return array('status' => true, 'out' => false, 'payment_id' => $paymentData['id'], 'web_url' => $response['web_url'], 'expires_at' => $expires_at);
            } else {
                return array('status' => false, 'out' => false, 'error_message' => $response['errorMsg']);
            }
        }

        if($data['InvoicePayment']['manual_payment'] != "1" && empty($this->InvoicePayment->validationErrors) && in_array($data['InvoicePayment']['payment_method'],['paytabs2','paymob','securepay','tap','paypalV2', 'square', 'paymob2', 'stripe'])){
            $paymentData = $data['InvoicePayment'];
            $invoice = $this->find('first', array('conditions' => array('Invoice.id' => $paymentData['invoice_id']), 'recursive' => 1));

            $paymentData['status'] = 0;
            $paymentData['email'] = $invoice['Client']['email'];
            // $paymentData['client_id'] = $invoice['Client']['id'];
            $paymentData['source'] = 'owner_add_payment';
            $this->InvoicePayment->create();
            $this->InvoicePayment->alias = 'InvoicePayment';
            $this->InvoicePayment->save($paymentData, array('validate' => false, 'fieldList' => null));
            if (in_array($invoice['Invoice']['type'], [Invoice::Invoice, Invoice::DEBIT_NOTE])){
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
            }elseif($invoice['Invoice']['type'] == Invoice::Refund_Receipt){
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_REFUND_PAYMENT);
            }
            $paymentData['id'] = $this->InvoicePayment->id;
            $paymentData['invoice'] = $invoice['Invoice'];
		    $paymentData['invoiceItems'] = $invoice['InvoiceItem'];
            $paymentData['client'] = $invoice['Client'];
            $paymentData['invoicePayment'] = $this->InvoicePayment->read(null, $this->InvoicePayment->id)['InvoicePayment'];

            $paymentClass = getPaymentClass($data['InvoicePayment']['payment_method']);
            $paymentClassString = $paymentClass;

            $PaymentGateway = ClassRegistry::init('SitePaymentGateway');
            $pg = $PaymentGateway->find(array('payment_gateway' => $data['InvoicePayment']['payment_method']), null, null, -1);
            if (!empty($pg['SitePaymentGateway']['manually_added']))
            $paymentClass = getPaymentClass('offline');
            else if (!class_exists($paymentClass))
                $paymentClass = getPaymentClass('offline');

            $paymentMethod = new $paymentClass;
            if ($paymentMethod->isOut()) {
                $userOptions = $pg['SitePaymentGateway'];
                $url = $paymentMethod->getPaymentURL($userOptions, $paymentData);
                if (method_exists($paymentMethod, 'getTransactionRef')) { // todo implement in all payment methods using interface
                    $this->InvoicePayment->save(['transaction_id' => $paymentMethod->getTransactionRef()], array('validate' => false, 'fieldList' => null));
                }
                if ($url) {
                    return array('status' => true, 'url' => $url, 'out' => true);
                }
                return array('status' => false, 'error_message' => $paymentMethod->errorMsg);
            } else {
                $result = $paymentMethod->processResult($paymentData);
                $paymentData['status'] = $result['data']['status'];
                if (empty($paymentData['date'])) {
                    $paymentData['date'] = date('Y-m-d');
                } else {
                    $paymentData['date'] = $this->formatDate($paymentData['date']);
                }
                if (!empty($result['data']['transaction_id'])) {
                    $paymentData['transaction_id'] = $result['data']['transaction_id'];
                }
                $this->InvoicePayment->save($paymentData, false, array('id', 'status', 'transaction_id'));
                if (!$result['data']['status']) {
                    return array('status' => false, 'out' => false, 'error_message' => $paymentMethod->errorMsg);
                }
                if($data['InvoicePayment']['payment_method'] == 'stripe' && $result['data']['status'] == PAYMENT_STATUS_PENDING){
                    return array('status' => true, 'out' => false, 'client_secret' => $result['data']['client_secret'], 'public_key' => $result['data']['public_key']);
                }
                return array('status' => true, 'out' => false);
            }
        }

        if (empty($this->InvoicePayment->validationErrors)) {
            $this->InvoicePayment->create();

            // append client phone1 , phone2
            $this->loadModel("Client");
            $client_data = $this->Client->find("first", ['conditions' =>['Client.id'=>$invoice["Invoice"]["client_id"]]]);
            $data["InvoicePayment"]["phone1"] = $client_data["Client"]["phone1"];
            $data["InvoicePayment"]["phone2"] = $client_data["Client"]["phone2"];
            if (isset($data["treasury_id"])) {
                $data["InvoicePayment"]["treasury_id"] = $data["treasury_id"];
            }
            $this->InvoicePayment->alias='InvoicePayment';
            $this->InvoicePayment->create(false);
            if ($row = $this->InvoicePayment->save($data, array('validate' => false,'fieldList'=>null))) {
                if (in_array($invoice['Invoice']['type'], [Invoice::Invoice, Invoice::DEBIT_NOTE])){
                    \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
                }elseif($invoice['Invoice']['type'] == Invoice::Refund_Receipt){
                    \AutoNumber::update_auto_serial(\AutoNumber::TYPE_REFUND_PAYMENT);
                }
                $data['InvoicePayment']['id'] = $this->InvoicePayment->id;
                izam_resolve(InvoicePaymentService::class)->insert(ServiceModelDataTransformer::transform($data, 'InvoicePayment'));
                $result = $this->updateInvoicePayments($invoice['Invoice']['id']);
                $this->InvoicePayment->alias = 'InvoicePayment';
                if ($result['status']) {
                    return array('payment_id' => $this->InvoicePayment->id, 'status' => true, 'invoice' => $result['invoice']);
                }
            }
        }

        return array('status' => false, 'here' => $this->InvoicePayment->validationErrors);
    }

    //------------------------------
    function getTotalInvoices($conditions = array()) {
        return 0;
        $invoices = $this->find('all', array('conditions' => $conditions, 'fields' => 'SUM(Invoice.summary_total) AS summary_total,Invoice.currency_code', 'group' => 'Invoice.currency_code'));

        $currencies = array();
        foreach ($invoices as $invoice) {
            $currencies[$invoice['Invoice']['currency_code']] = $invoice[0]['summary_total'];
        }
        return $currencies;
    }

    //------------------------------
    
    

    function saveClientMessage($invoice, $data) {
        $config = parse_ini_file(WWW_ROOT . '../app_config.ini');
        $EmailLog = ClassRegistry::init('EmailLog');
        $template = array();
        $template['EmailTemplate']['body'] = $data['EmailLog']['body'];
        $template['EmailTemplate']['subject'] = $data['EmailLog']['subject'];
        $template['EmailTemplate']['attachment'] = $data['EmailLog']['attachments'];
        $template['EmailTemplate']['attachment_full_path'] = 'files/temp/' . $data['EmailLog']['attachments'];
        $template['EmailTemplate']['send_invoice'] = $data['EmailLog']['send_invoice'];
        $template['EmailTemplate']['send_documents'] = !empty($data['EmailLog']['send_documents']);


        $result = ClassRegistry::init('EmailTemplate')->getInvoiceMessage($template, $invoice);
        $message = $data['EmailLog']['body'];
        $subject = $data['EmailLog']['subject'];

        $message_data = array();
        $message_data = $data;
        $data['EmailLog']['body'] = $result['body'];
        $data['EmailLog']['invoice_id'] = $invoice['Invoice']['id'];
        $data['EmailLog']['client_id'] = $invoice['Invoice']['client_id'];
        $data['EmailLog']['site_id'] = $invoice['Invoice']['site_id'];
        $data['EmailLog']['send_date'] = date('Y-m-d H:i:s');
        $data['EmailLog']['subject'] = $result['subject'];
        $data['EmailLog']['send_to'] = $invoice['Client']['email'];
        $data['EmailLog']['send_from'] = getAuthOwner('email');
        $data['EmailLog']['sent_date'] = date('Y-m-d H:i:s');


        if (($EmailLog->save($data)) != false) {
            return array('message' => $message, 'subject' => $subject);
        }
        return false;
    }

    function clientAddPayment($data) {
        $this->set($data);
        if (isset($_GET['source']) && $_GET['source'] == 'website_front'){
            $data['InvoicePayment']['source'] = 'website_front';
        } elseif (!empty($_GET['webfront_reservation'])) {
            $data['InvoicePayment']['source'] =  $paymentData['source'] = 'webfront_reservation';
        } else {
            $data['InvoicePayment']['source'] = 'client_add_payment';
        }
        App::import('Vendor', 'AutoNumber');
        $data['InvoicePayment']['code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
        if ($this->InvoicePayment->save($data)) {
            \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE_PAYMENT);
            $order_id = $this->InvoicePayment->getLastInsertID();
            $data['InvoicePayment']['id'] = $data['InvoicePayment']['order_id'] = $order_id;



            $paymentClass = getPaymentClass($data['InvoicePayment']['payment_method']);
            $paymentClassString = $paymentClass;
//			App::import('Vendor', 'PaypalPayment', array('file' => 'payments/PaypalPayment.php'));
            $PaymentGateway = ClassRegistry::init('SitePaymentGateway');
            $pg = $PaymentGateway->find(array('payment_gateway' => $data['InvoicePayment']['payment_method']), null, null, -1);
            if (!empty($pg['SitePaymentGateway']['manually_added']))
                $paymentClass = getPaymentClass('offline');
            else if (!class_exists($paymentClass))
                $paymentClass = getPaymentClass('offline');

            $paymentMethod = new $paymentClass;
            if ($paymentMethod->isOut()) {
                /* @var $PaymentGateway SitePaymentGateway */
                $userOptions = $pg['SitePaymentGateway'];
                $paymentData = $data['InvoicePayment'];

                if (isset($_GET['source']) && $_GET['source'] == 'website_front') {
                    $paymentData['source'] = 'website_front';
                } elseif (!empty($_GET['webfront_reservation'])) {
                    $paymentData['source'] = 'webfront_reservation';
                } else {
                    $paymentData['source'] = 'client_add_payment';
                }
                $invoice = $this->find('first', ['conditions' => ['Invoice.id' => $paymentData['invoice_id']], 'recursive' => 1]);
	            $paymentData['invoice'] = $invoice['Invoice'];
	            $paymentData['invoiceItems'] = $invoice['InvoiceItem'];
	            $paymentData['client'] = $invoice['Client'];

                $paymentData['return'] = Router::url(array('controller' => 'invoice_payments', 'action' => 'view', $order_id), true);
                $paymentData['notify_url'] = Router::url("/invoice_payments/payment_ipn/$order_id", true);
                if (!empty($paymentData['payment_method']) && in_array($paymentData['payment_method'],['tabby','tamara'])   ) {
                    $paymentData['source'] = "client";
                }

                if (isset($data['_Token']['key'])) {
                    $paymentData['_token_key'] = $data['_Token']['key'];
                }

                $url = $paymentMethod->getPaymentURL($userOptions, $paymentData);
                if ($url && !(isset($url['status']))) {
                    if (method_exists($paymentMethod, 'getTransactionRef')) { // todo implement in all payment methods using interface

                        $updatedData = ['transaction_id' => $paymentMethod->getTransactionRef()];

                        if ($data['InvoicePayment']['payment_method'] == 'tamara') {
                            $updatedData['status'] = 2;
                        }

                        $this->InvoicePayment->save($updatedData, ['validate' => false, 'fieldList' => null]);
                    }
                    return array('status' => true, 'url' => $url, 'data' => $data, 'out' => true);
                } elseif (in_array($paymentClassString,['PayTabsPayment2','PayPalPaymentV2', 'TapPayment', 'PaymobPayment', "PayTabsPayment"])) {
	                $data['errorMsg'] = $paymentMethod->errorMsg ?? '' . (!empty($paymentMethod->rejectedFields)  ?'rejected fields: '.implode(', ', $paymentMethod->rejectedFields) : '');
                }
                if (isset($paymentMethod->errorMsg) && !isset($data['errorMsg'])){
                    $data['errorMsg'] = $paymentMethod->errorMsg;
                }
	            return array('status' => false, 'url' => false, 'data' => $data,'validationErrors'=>$this->InvoicePayment->validationErrors);
            } else {
                $result = $paymentMethod->processResult($data);
                $data['InvoicePayment']['status'] = $result['data']['status'];
                if (empty($data['InvoicePayment']['date'])) {
                    $data['InvoicePayment']['date'] = date('Y-m-d');
                } else {
                    $data['InvoicePayment']['date'] = $this->formatDate($data['InvoicePayment']['date']);
                }
                if(!empty($result['data']['transaction_id'])){
                    $data['InvoicePayment']['transaction_id'] = $result['data']['transaction_id'];
                }
                unset($data['InvoicePayment']['attachment']);
                $this->InvoicePayment->save($data, false, array('id', 'status', 'transaction_id'));
                if(!$result['data']['status']){
                    return array('status' => false, 'out' => false, 'data' => $result);
                }
                if($data['InvoicePayment']['payment_method'] == 'stripe' && $result['data']['status'] == PAYMENT_STATUS_PENDING){
                    return array('status' => true, 'out' => false, 'client_secret' => $result['data']['client_secret'], 'public_key' => $result['data']['public_key']);
                }
                return array('status' => true, 'out' => false, 'data' => $data);
            }
        }
        return array('status' => false, 'url' => false, 'data' => $data,'validationErrors'=>$this->InvoicePayment->validationErrors);
    }

    function GetDays($sStartDate, $sEndDate) {
        if ($sStartDate > $sEndDate) {
            return 0;
        }
        $gd_a = getdate($sStartDate);
        $gd_b = getdate($sEndDate);

        $a_new = mktime(12, 0, 0, $gd_a['mon'], $gd_a['mday'], $gd_a['year']);
        $b_new = mktime(12, 0, 0, $gd_b['mon'], $gd_b['mday'], $gd_b['year']);

        return round(abs($a_new - $b_new) / 86400);
    }

    function isNumberUnique($param) {
        $site_id = getAuthOwner('id');
        $conditions = array('Invoice.site_id' => $site_id, 'Invoice.no' => $param['no'], 'Invoice.type' => $this->data['Invoice']['type']);
        if (!empty($this->data['Invoice']['id'])) {
            $conditions['Invoice.id !='] = $this->data['Invoice']['id'];
        }
        return (!$this->hasAny($conditions));
    }

    public function afterFind($results, $primary = false) {
        $host=getCurrentSite('subdomain');
        parent::afterFind($results, $primary);
        if (empty($results)) {
            return $results;
        }
        foreach ($results as &$result) {
	        if (isset($result['Invoice']) && !empty($result['Invoice'])) {
		        if (isset($getQrCode) && $getQrCode) {
			        $result['Invoice']['qr_code_url'] = Invoice::getInvoiceQrCodeImageUrl($host, $result);
		        }
		        $result['Invoice']['invoice_html_url'] = Invoice::getInvoiceClientPreviewUrl($host, $result);
				$result['Invoice']['invoice_pdf_url'] = Invoice::getInvoiceClientPdfUrl($host, $result);
	        }

            if (isset($result[$this->alias]['date']) && isset($result[$this->alias]['due_after'])) {
                $result[$this->alias]['due_date'] = date('Y-m-d', strtotime('+' . $result[$this->alias]['due_after'] . ' days', strtotime($result[$this->alias]['date'])));
            }
            if (isset($result[$this->alias]['client_country_code']) && !empty($result[$this->alias]['client_country_code'])) {
                $result['Country'] = $this->FindCountry($result[$this->alias]['client_country_code']);
            }
        }
        return $results;
    }


    function checkIssueDate($params) {
        return $this->formatDate($params['issue_date'], getCurrentSite('date_format')) <= $this->data[$this->alias]['date'];
    }

    function getUsedTaxList() {
        $query = "SELECT DISTINCT CONCAT(UPPER(`name`), `value`) as idx, tax_id, `name`, `value` FROM invoice_taxes as ITax WHERE invoice_id in (SELECT id FROM invoices WHERE site_id = " . getAuthOwner('id') . ")";
        $results = $this->query($query);
        $taxes = array();
        foreach ($results as $result) {
            $taxes[$result[0]['idx']] = $result['ITax']['name'] . ' (' . $result['ITax']['value'] . '%)';
        }
        return $taxes;
    }

    function getTaxList() {
        $this->loadModel('Tax');
        $taxes = $this->Tax->find('list', array('order' => 'name'));

        return $taxes;
    }

    public static function getEstimateStatuses() {
        return array(
            "" => __('Open', true),
            null => __('Open', true),
            ESTIMATE_STATUS_OPEN => __('Open', true),
            ESTIMATE_STATUS_SENT => __('Sent', true),
            ESTIMATED_STATUS_VIEWED => __('Viewed', true),
            ESTIMATE_STATUS_REPLIED => __('Replied', true),
            ESTIMATE_STATUS_ACCEPTED => __('Accepted', true),
            ESTIMATE_STATUS_INVOICED => __('Invoiced', true),
            ESTIMATE_STATUS_SALES_ORDERED => __('Ordered', true),
            -1 => __('Draft', true),
        );
    }

    public static function getSalesOrderStatuses() {
        return array(
            "" => __('Open', true),
            null => __('Open', true),
            SALES_ORDER_STATUS_PENDING => __('Open', true),
            SALES_ORDER_STATUS_PARTIALLY_INVOICED => __('Partially Invoiced', true),
            SALES_ORDER_STATUS_INVOICED => __('Invoiced', true),
            -1 => __('Draft', true),
        );
    }

    public function setEntityAppData(&$invoices)
    {
        if ((ifPluginActive(ETA_PLUGIN) || ifPluginActive(PluginUtil::EINVOICE_PLUGIN)|| ifPluginActive(PluginUtil::JORDAN_EINVOICE_PLUGIN)) && !empty($invoices)) {
            $this->loadModel('EntityAppData');
            //here loading all app entity data with action get
            $maxAppEntityData = $this->EntityAppData->find('all', ['conditions' =>[
                'entity_id' => array_column(array_column($invoices, 'Invoice'), 'id'),
                'EntityAppData.entity_key' => 'invoice',
                //'EntityAppData.app_key' => EntityAppDataKeysUtil::ELECTRONIC_INVOICE,
                'EntityAppData.action_key' => ElectronicInvoicesActionsUtil::GET_DOCUMENT,
            ], 'group' => 'EntityAppData.entity_id', 'fields' => 'max(id) as id, entity_id']);
            //get ids of get entity data
            $maxAppEntityDataIds = array_column(array_column($maxAppEntityData, 0), 'id');
            //get entities ids for all get entity data
            $entitiesId =  array_column(array_column($maxAppEntityData, 'EntityAppData'), 'entity_id');

            //get all submitted data and no get invoice data
            $maxAppEntityDataSubmitOnly = $this->EntityAppData->find('all', ['conditions' =>[
                'EntityAppData.entity_key' => 'invoice', // 'EntityAppData.app_key' => EntityAppDataKeysUtil::ELECTRONIC_INVOICE,
                'EntityAppData.action_key' => ElectronicInvoicesActionsUtil::SUBMIT_DOCUMENT,
                "EntityAppData.entity_id" => array_diff( array_column(array_column($invoices, 'Invoice'), 'id'), $entitiesId),
            ], 'group' => 'EntityAppData.entity_id', 'fields' => 'max(id) as id, entity_id']);


            //get all sent data and no get invoice data
            $errorEntity = $this->EntityAppData->find('all', ['conditions' =>[
                'EntityAppData.entity_key' => 'invoice', 'EntityAppData.app_key' => EntityAppDataKeysUtil::SAUDI_ELECTRONIC_INVOICE,
                'EntityAppData.action_key' => ElectronicInvoicesActionsUtil::ERROR,
                "EntityAppData.entity_id" => array_column(array_column($invoices, 'Invoice'), 'id'),
            ], 'group' => 'EntityAppData.entity_id', 'fields' => 'max(id) as id, entity_id']);


            $maxAppEntityDataSentOnly = $this->EntityAppData->find('all', ['conditions' =>[
                'EntityAppData.entity_key' => 'invoice', // 'EntityAppData.app_key' => EntityAppDataKeysUtil::ELECTRONIC_INVOICE,
                'EntityAppData.action_key' => ElectronicInvoicesActionsUtil::SENT_DOCUMENT,
                "EntityAppData.entity_id" => array_column(array_column($invoices, 'Invoice'), 'id'),
            ], 'group' => 'EntityAppData.entity_id', 'fields' => 'max(id) as id, entity_id']);

            $appEntityData = $this->EntityAppData->find('all', ['conditions' =>[
                'id' => $maxAppEntityDataIds,
            ]]);

            foreach ($appEntityData as $datum) {
                $index = $this->searchArray($invoices, $datum['EntityAppData']['entity_id']);
                $invoices[$index]["EntityAppData"] = [$datum["EntityAppData"]];

            }

            foreach ($maxAppEntityDataSubmitOnly as $datum) {
                $index = $this->searchArray($invoices, $datum['EntityAppData']['entity_id']);
                $invoices[$index]["is_submitted"] = true;
            }

            foreach ($maxAppEntityDataSentOnly as $datum) {
                $index = $this->searchArray($invoices, $datum['EntityAppData']['entity_id']);
                $invoices[$index]["is_sent"] = true;
            }

            foreach ($errorEntity as $datum) {
                $index = $this->searchArray($invoices, $datum['EntityAppData']['entity_id']);
                $invoices[$index]["have_error"] = true;
            }
        }
        $advancePaymentsEnabled = 0;
        if (!empty($invoices)){
            $advancePaymentsEnabled = count(array_filter($invoices, function($item) {
                return $item['Invoice']['type'] == Invoice::ADVANCE_PAYMENT;
            }));
        }
        if ($advancePaymentsEnabled) {
            $invoiceIds = array_column(array_column($invoices, 'Invoice'), 'id');
            $invoiceRepo = new InvoiceRepository();
            $advancePayment = $invoiceRepo->getAdvanceInvoiceData($invoiceIds);
            foreach ($invoices as $index => $invoice) {
                $adv = array_filter($advancePayment, function($item) use ($invoice) {
                    return $item['id'] == $invoice['Invoice']['id'];
                });
                if ($adv) {
                    $adv = end($adv);
                    $invoices[$index]['Invoice']['unSettledAmount'] = $adv['unSettledAmount'];
                }
            }
        }
    }

    private function searchArray($data, $id)
    {
        foreach ($data as $key => $item) {
            if ($item["Invoice"]["id"] == $id) {
                return  $key;
            }
        }
        return -1;
    }

    public function updateInvoiceNo($site_id) {
        $no = $this->getNextInvoiceNo($site_id);
        $padding = strlen($no);
        ++$no;
        if ($this->hasAny(array('Invoice.site_id' => $site_id, '(`Invoice`.`no` + 0) = ' . intval($no, 10), 'Invoice.type' => 0))) {
            $lastInvoice = $this->find('first', array('order' => 'Invoice.no + 0 DESC', 'fields' => 'Invoice.no', 'recursive' => -1, 'conditions' => array('Invoice.site_id' => intval($site_id), 'Invoice.type' => 0)));
            $no = $lastInvoice['Invoice']['no'] + 1;
        }
        $no = sprintf('%0' . $padding . 'd', $no);
        $this->Site->query('UPDATE sites SET next_invoice_number = "' . $no . '" WHERE id = ' . $site_id);
    }

    public function getCurrencylist($conditions = array()) {

        $Currencylist = array();
        $rows = $this->find('all', array('applyBranchFind'=>false,'fields' => 'DISTINCT currency_code', 'conditions' => $conditions, 'recursive' => -1));
        foreach ($rows as $row) {
            $Currencylist[] = $row['Invoice']['currency_code'];
        }
        
        return $Currencylist;
    }

    function check_deletion ( $id ) {
        $staff_id = getAuthOwner ( 'staff_id' );
        $this->loadModel ( 'FollowUpReminder' );
        $this->loadModel ( 'Post' );
        $this->loadModel ( 'InvoicePayment' );
        
        $this->FollowUpReminder->recursive = -1 ; 
        $this->Post->recursive = -1 ; 
        $this->InvoicePayment->recursive = -1 ; 
        //payments
        if (!check_permission(Invoices_Edit_All_Invoices) || !check_permission(Invoices_Add_Payments_to_All))
        {
            $payment_count = $this->InvoicePayment->find ( 'count' , ['conditions' => ['staff_id <> ' => $staff_id, 'invoice_id' => $id ]] );
            if ( $payment_count > 0 ){
                return false ;
            }
            
        }
        //appointments and notes
        if (!check_permission(Edit_Delete_All_Notes_Attachments))
        {
            //appointments
            $reminder_count = $this->FollowUpReminder->find ( 'count' , ['conditions' => ['staff_id <> ' => $staff_id, 'item_id' => $id, 'item_type' => Post::INVOICE_TYPE ]] );
            if ( $reminder_count > 0 ){
                return false ;
            }
            //notes
            $post_count = $this->Post->find ( 'count' , ['conditions' => ['staff_id <> ' => $staff_id, 'item_id' => $id, 'item_type' => Post::INVOICE_TYPE ]] );
            if ( $post_count > 0 ){
                return false ;
            }
        }
        
        return true ;
    }
    function delete_with_related ( $id , $run = false ) 
    {
//        if ( $run)
//        {
//            $transaction = ["START TRANSACTION;"] ;
//        }else {
//            $transaction = [] ;
//        }
        $this->loadModel('StockTransaction');
        $stock_transactions_list = $this->StockTransaction->find('all',['conditions' => ['StockTransaction.source_type' => [StockTransaction::SOURCE_INVOICE, StockTransaction::SOURCE_CN,StockTransaction::SOURCE_RR],'order_id' => $id ]] );
        foreach ( $stock_transactions_list as $k => $l){
            StockTransaction::removeTransaction($l['StockTransaction']);
        }
        
        //payments
        $this->loadModel ( 'InvoicePayment' );
        //$this->InvoicePayment->recursive =-1 ;
        $payments = $this->InvoicePayment->find ( 'list' ,[  'conditions' => ['InvoicePayment.invoice_id' => $id ] ]);
        if ( !empty ( $payments)){
            $payments_keys = array_keys ( $payments ) ;

            foreach ( $payments_keys as $k ){
                $this->InvoicePayment->delete ($k ) ;
            }
            
//            $transaction[]= "DELETE FROM invoice_payments where id in ( ".implode(',', $payments_keys)." );" ;
        }
        
        //notes
        $this->loadModel ( 'Post' );
        //$this->Post->recursive =-1 ;
        $posts = $this->Post->find ( 'list' ,['recursive'=> -1,'conditions' => ['item_id' => $id , 'item_type' => Post::INVOICE_TYPE ] ]);
        if ( !empty ( $posts)){
            $posts_keys = array_keys ( $posts ) ;
            foreach ( $posts_keys as $k ){
                $this->Post->delete ($k ) ;
            }
//            $this->Post->deleteAll (['Post.id' => $posts_keys ] ) ;
//            $transaction[] = "DELETE FROM posts where id in ( ".implode(',', $posts_keys)." );" ;
        }
        
        //appointments
        $this->loadModel ( 'FollowUpReminder' );
        //$this->FollowUpReminder->recursive =-1 ;
        $reminders = $this->FollowUpReminder->find ( 'list' ,['recursive'=> -1,'conditions' => ['item_id' => $id , 'item_type' => Post::INVOICE_TYPE ] ]);
        if ( !empty ( $reminders)){
            $reminders_keys = array_keys ( $reminders ) ;
            foreach ( $reminders_keys as $k ){
                $this->FollowUpReminder->delete ($k ) ;
            }
//            $this->FollowUpReminder->deleteAll (['FollowUpReminder.id' => $reminders_keys ] ) ;
//            $transaction[] = "DELETE FROM follow_up_reminders where id in ( ".implode(',', $reminders_keys)." );" ;
        }
//        if ( is_array($id)){
//            $where = " in (".implode( ',' , $id ).")" ;
//        }else {
//            $where = " = ".intval($id ) ;
//        }
        if ( is_array($id)){
            foreach ( $id as $k   ){
                $this->delete_cost_journals ( $k ) ;
                $this->delete ( $k ) ;
            }
        }else {
            $this->delete_cost_journals ( $id ) ;
            $this->delete ( $id ) ;
        }
//        $this->deleteAll ( ['Invoice.id' => $id ]) ;
        return true ; 
//        $transaction[] = "DELETE FROM invoices where id ".$where.' ;';
//        if ( $run){
//            $transaction[] =" COMMIT;";
//            foreach ( $transaction as $q  ) {
//                $temp = $this->query($q ) ;
//            }
//            return $temp; 
//        }else {
//            return $transaction;
//        }
    }
    
    function get_refund_total($id,$field='summary_unpaid'){
        if(empty($id)){
            return 0;    
        }
        $total=0;
        $old_recursive=$this->recursive;
        $this->recursive=-1;
        $this->applyBranch['onFind'] = false;
        $rows=$this->find('all',array('fields'=>$field,'conditions'=>array('Invoice.type'=>Invoice::Refund_Receipt,'Invoice.draft <>1','Invoice.subscription_id'=>$id)));
      
        foreach($rows as $row){
        $total +=($row['Invoice'][$field]) ;
        }
       
        $this->recursive=$old_recursive;

        return $total;
    }
    function get_refund_count($id,$show_draft=false){
        if(empty($id)){
            return 0;    
        }
        $total=0;
        $old_recursive=$this->recursive;
        $this->recursive=-1;
           if($show_draft==false){
        $conditions[]='Invoice.draft <>1';
        }
        
        $conditions['Invoice.type']=Invoice::Refund_Receipt;
        $conditions['Invoice.subscription_id']=$id;
        $this->applyBranch['onFind'] = false;
        $rows=$this->find('count',array('fields'=>'summary_unpaid','conditions'=>$conditions));
        $this->recursive=$old_recursive;
        
        return $rows;
    }
    function get_refund_list($id,$show_draft=false){
        if(empty($id)){
            return 0;    
        }
        $total=0;
        $old_recursive=$this->recursive;
        $this->recursive=-1;
        if($show_draft==false){
        $conditions[]='Invoice.draft <>1';
        }
        $conditions['Invoice.type']=Invoice::Refund_Receipt;
        $conditions['Invoice.subscription_id']=$id;
        $this->applyBranch['onFind'] = false;
        $rows=$this->find('list',array('fields'=>'Invoice.id,Invoice.no','conditions'=>$conditions));
        $this->recursive=$old_recursive;
        
        return $rows;
    }

    function get_credit_note_count($id,$show_draft=false){
        if(empty($id)){
            return 0;
        }
        $total=0;
        $old_recursive=$this->recursive;
        $this->recursive=-1;
        if($show_draft==false){
            $conditions[]='Invoice.draft <>1';
        }

        $conditions['Invoice.type']=Invoice::Credit_Note;
        $conditions['Invoice.subscription_id']=$id;
        $rows=$this->find('count',array('fields'=>'summary_unpaid','conditions'=>$conditions));
        $this->recursive=$old_recursive;

        return $rows;
    }
    function get_credit_note_list($id,$show_draft=false){
        if(empty($id)){
            return 0;
        }
        $total=0;
        $old_recursive=$this->recursive;
        $this->recursive=-1;
        if($show_draft==false){
            $conditions[]='Invoice.draft <>1';
        }
        $conditions['Invoice.type']=Invoice::Credit_Note;
        $conditions['Invoice.subscription_id']=$id;
        $rows=$this->find('list',array('fields'=>'Invoice.id,Invoice.no','conditions'=>$conditions));
        $this->recursive=$old_recursive;

        return $rows;
    }

    function get_debit_note_count($id, $show_draft = false)
    {
        if (empty($id)) {
            return 0;
        }
        $total = 0;
        $old_recursive = $this->recursive;
        $this->recursive = -1;
        if ($show_draft == false) {
            $conditions[] = 'Invoice.draft <>1';
        }

        $conditions['Invoice.type'] = Invoice::DEBIT_NOTE;
        $conditions['Invoice.subscription_id'] = $id;
        $rows = $this->find('count', array('fields' => 'summary_unpaid', 'conditions' => $conditions));
        $this->recursive = $old_recursive;

        return $rows;
    }

    function get_debit_note_list($id, $show_draft = false)
    {
        if (empty($id)) {
            return 0;
        }
        $total = 0;
        $old_recursive = $this->recursive;
        $this->recursive = -1;
        if ($show_draft == false) {
            $conditions[] = 'Invoice.draft <>1';
        }
        $conditions['Invoice.type'] = Invoice::DEBIT_NOTE;
        $conditions['Invoice.subscription_id'] = $id;
        $rows = $this->find('list', array('fields' => 'Invoice.id,Invoice.no', 'conditions' => $conditions));
        $this->recursive = $old_recursive;

        return $rows;
    }


    function FindCountry($code = "") {
        return getCountry($code);
    }    
    
    function update_draft($id,$draft_status){
         $old_recursive=$this->recursive;
        $invoice = $this->getInvoice($id);
        $data = $invoice;
        $data['Invoice']['draft'] = $draft_status;
        $result = $this->updateInvoice($data, $invoice['Invoice']['type']);
        if($result)
        {
            if($draft_status==1)
                $this->delete_payment_journals($id);
            else
                $this->update_payment_journals($id);
        }
        $this->recursive= $old_recursive;
      return $result;

    }

		function calculate_taxes_amount($data)
		{
			if (!empty($data['Invoice']['id'])) {
				$data['InvoiceTax'] = $this->calculateTotals($data)['taxes'];
				if (empty($data['InvoiceTax'])) {
					// Let's try to fix it with Another Solution
					$data['InvoiceTax'] = $this->calculateTotals($this->getInvoice($data['Invoice']['id']))['taxes'];
				}
			}
			foreach ($data['InvoiceTax'] as $j => $tax) {
				unset($data['InvoiceTax'][$j]['amount']);
			}
			foreach ($data['InvoiceItem'] as $item) {
				for ($i = 1; $i <= 2; $i++) {
					if (!empty($item['tax' . $i]) && !empty($item['summary_tax' . $i])) {
						if (isset($data['InvoiceTax'][$item['tax' . $i]])) {
							if (!isset($data['InvoiceTax'][$item['tax' . $i]]['amount'])) {
								$data['InvoiceTax'][$item['tax' . $i]]['amount'] = 0;
							}
							$data['InvoiceTax'][$item['tax' . $i]]['amount'] += $item['summary_tax' . $i];
						}
					}
				}
			}
			if (!empty($data['Invoice']['shipping_amount']) && !empty($data['Invoice']['shipping_tax_id'])) {
				$data['InvoiceTax'][$data['Invoice']['shipping_tax_id']]['amount'] = $data['InvoiceTax'][$data['Invoice']['shipping_tax_id']]['invoice_value'];
			}
			return $data;
		}
    
    public function delete_cost_journals($invoice_id)
    {
       $this->delete_auto_journals($invoice_id,array('entity_type'=>'invoice_sales_cost' ));
    }
    
    public function update_payment_journals($id)
    {
        $payments=$this->InvoicePayment->find('all',array('conditions'=>array('Invoice.id'=>$id)));
        foreach($payments as $payment)
        {
            $this->InvoicePayment->update_journals($payment);
        }
    }
    
    public function delete_payment_journals($id)
    {
        $payments=$this->InvoicePayment->find('list',array('conditions'=>array($this->InvoicePayment->alias.'.invoice_id'=>$id)));
        foreach($payments as $payment_id)
        {
            $this->InvoicePayment->delete_auto_journals($payment_id);
        }
    }
    
    
    public function update_cost_journals($invoice,$stock_transactions=false)
    {
		$disable_invoice_auto_journals = settings::getValue(AccountingPlugin, "disable_invoice_auto_journal"  );
		if($disable_invoice_auto_journals){
			return false;
		}


       if(!is_array($invoice)&& is_numeric($invoice))
           $invoice=$this->findById($invoice);
       if(empty($invoice)) return false;
       
       if(empty($invoice['Invoice']['id'])) $invoice['Invoice']['id']=$this->id;
       if(empty($invoice['Invoice']['id'])) return false;

        $is_open_period = $this->validateIsOpenedPeriod($invoice['Invoice']['date']);

        if ( $is_open_period !== true) {
            //Log for specific site in hopes of finding out the issue source
            if(in_array(getCurrentSite('id'), [2611261]) ) {
                $directory = dirname(dirname(__FILE__)) . DS . 'webroot' . DS . 'files' . DS . getCurrentSite('id') . DS  . 'sales-cost-journals';
                if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
                }
                $fileName = date('Y-m-d').'.txt';
                file_put_contents($directory.DS.$fileName, '-----Time '.date('H:i a').' ------'."\r\n", FILE_APPEND);
                file_put_contents($directory.DS.$fileName, print_r($invoice, true)."\r\n", FILE_APPEND);
                file_put_contents($directory.DS.$fileName, print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10), true), FILE_APPEND);
                file_put_contents($directory.DS.$fileName, print_r($stock_transactions, true), FILE_APPEND);
                file_put_contents($directory.DS.$fileName, '-----End ------'."\r\n", FILE_APPEND);
            }

            return false;
        }

        if($stock_transactions==false)
        {
            $this->loadModel('StockTransaction');
            $stock_transactions= $this->StockTransaction->find('all',array('conditions'=>array('StockTransaction.status != '=> StockTransaction::STATUS_DRAFT,'StockTransaction.order_id'=>$invoice['Invoice']['id'],'StockTransaction.source_type'=> array(StockTransaction::SOURCE_INVOICE,StockTransaction::SOURCE_RR,StockTransaction::SOURCE_CN))));
        }

        if((empty($stock_transactions)&&!empty($invoice['Invoice']['id']))||!empty($invoice['Invoice']['draft']))
            return $this->delete_cost_journals($invoice['Invoice']['id']);
        
        $debit='debit';
        $credit='credit';
        if($invoice['Invoice']['type']==Invoice::Invoice||empty($invoice['Invoice']['type']))
        {
           $item_type='Invoice';
        } else if($invoice['Invoice']['type']==Invoice::Refund_Receipt)
        {
            $item_type='Refund Receipt';
        } else if($invoice['Invoice']['type']==Invoice::Credit_Note)
        {
            $item_type='Credit Note';
        }else if($invoice['Invoice']['type']==Invoice::DEBIT_NOTE)
        {
            $item_type='Debit Note';
        } else return false;
        
        $currecny= $this->get_default_currency();

        $journal['Journal']['date']=$invoice['Invoice']['date'];
        $journal['Journal']['staff_id']=$invoice['Invoice']['staff_id'];
        $journal['Journal']['entity_type']='invoice_sales_cost';
        $journal['Journal']['description']=__('Sales Cost',true).' - '.__($item_type,true).' #'.$invoice['Invoice']['no'];
        $journal['Journal']['entity_id']=$invoice['Invoice']['id'];
        $journal['Journal']['currency_code']=$currecny;

        if (ifPluginActive(PosPlugin)) {
            $pos_accounting_settings = $this->checkInvoicePosShiftCalculatedPerInvoice($invoice['Invoice']['pos_shift_id']);
            $invoice = $this->getInvoice($invoice['Invoice']['id']);
            if (empty($pos_accounting_settings) && !empty($invoice['Invoice']['pos_shift_id'])) {
                return $this->update_journals(false,$journal);
            }
        }

        $product_sales_routing = settings::getValue(AccountingPlugin,'product_sales_accounts_routing');

        $stores=array();
         $journal['JournalTransaction'][0]['currency_'.$debit]=0;
         $journal_total=0;
         ini_set('memory_limit', '8G');
         set_time_limit(1200);
        $isRefundTransaction = in_array($invoice['Invoice']['type'], [self::Refund_Receipt, self::Credit_Note]);
        foreach($stock_transactions as $transaction)
        {
            
            //$extra_description=' - '.__('Product',true).' #'.$transaction['StockTransaction']['product_id'];
			$extra_description='';
            // Extract the default calculation as base value
            $defaultTransTotal = $transaction['StockTransaction']['purchase_price'] * -1 * $transaction['StockTransaction']['quantity'];
            if ($isRefundTransaction && calculateRefundWithSellingPrice()) {
                // We don't use the price as it can be changed from the user himself
                $originalCost = $this->StockTransaction->getOriginalCostPriceForRefund($transaction['StockTransaction']);
                $trans_total = ($originalCost > 0)
                    ? $originalCost * -1 * $transaction['StockTransaction']['quantity']
                    : $defaultTransTotal;
            } else {
                $trans_total = $defaultTransTotal;
            }

            if ($transaction['StockTransaction']['currency_code'] != $data['Invoice']['currency_code']) {
                $currency_rate = CurrencyConverter::index($transaction['StockTransaction']['currency_code'], $data['Invoice']['currency_code'], date('Y-m-d'));
                $trans_total = round($currency_rate * $trans_total, 4);
            }
            $journal_total+=abs($trans_total);
            $this->loadModel('Product');
            $this->loadModel('JournalAccount');
            $account_id = $this->Product->getSalesCostAccount($transaction['Product']['id']);
            $this->loadModel('JournalAccount');
            $journalAccount = $this->JournalAccount->find('first', ['conditions' => ['JournalAccount.id' => $account_id], 'applyBranchFind' => false]);
            if(!empty($account_id) && !empty($journalAccount) && (!empty($product_sales_routing) && $product_sales_routing == settings::MANUAL_ACCOUNTS_ROUTING)){

                $journal['JournalTransaction'][]=
                array(
                        'subkey'=>'product_sales_cost_'.$transaction['Product']['name'].'_'.$transaction['Product']['id'],
                        'currency_'.$debit => $trans_total,
                        'currency_code' => $currecny,
                        'description' => $journal['Journal']['description'].$extra_description,
                        'journal_account_id'=>$account_id
                    );
            }else{

                if(empty($journal['JournalTransaction'][0]['description']))
                    $journal['JournalTransaction'][0]['description']=$journal['Journal']['description'];

                $journal['JournalTransaction'][0]=
                array(
                        'subkey'=>'sales_cost',
                        'currency_'.$debit=>$journal['JournalTransaction'][0]['currency_'.$debit]+$trans_total,
                        'currency_code'=>$currecny,
                        'description'=>$journal['JournalTransaction'][0]['description'].$extra_description,
                        'auto_account'=>array('type'=>'fixed' , 'entity_type'=> 'sales_cost','entity_id'=>0)
                    );
            }

            $key=count( $journal['JournalTransaction']);
             if(isset($stores[$transaction['StockTransaction']['store_id']]))
                        $key= $stores[$transaction['StockTransaction']['store_id']];
                    else 
                        $stores[$transaction['StockTransaction']['store_id']]=$key;
            
            if(empty($journal['JournalTransaction'][$key]['description']))
                $journal['JournalTransaction'][$key]['description']=$journal['Journal']['description'];
            
            $journal['JournalTransaction'][$key]=
            array(
                    'subkey'=>'store_'.$transaction['StockTransaction']['store_id'],
                    'currency_'.$credit=>(isset($journal['JournalTransaction'][$key]['currency_'.$credit])?$journal['JournalTransaction'][$key]['currency_'.$credit]+ $trans_total: $trans_total),
                    'currency_code'=>$currecny,
                    'description'=>$journal['JournalTransaction'][$key]['description'].=$extra_description,
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'store','entity_id'=>$transaction['StockTransaction']['store_id'])
            );
        }
        if($journal_total==0) unset($journal['JournalTransaction']);
        return $this->update_journals(false,$journal);
    }

    function calculate_total_discount($data)
    {
        if(!is_array($data)&&is_int($data))
        {
            $data=$this->findById($data);
        }
        $total_discount=$data['Invoice']['summary_discount'];

        foreach ($data['InvoiceItem'] as $item)
        {

            if(!empty($item['discount'])&&!empty($item['discount_type'])) {
                $item_discount = $this->calculate_item_discount($item, $item['subtotal']);
                $total_discount+=$item_discount;

            }


        }

        return $total_discount;
    }
    
    public function get_journals($data)
    {
	
		$disable_invoice_auto_journals = settings::getValue(AccountingPlugin, "disable_invoice_auto_journal"  );


        $taxAccount = [];

        if($data['Invoice']['type']==Invoice::Invoice||empty($data['Invoice']['type']))
        {
             $item_type='Invoice';
             $debit='debit';
             $credit='credit';
             $account='sales';
             $entity_type='invoice';
            $acountRoutingType = settings::getValue(AccountingPlugin, 'sales_accounts_routing');
            if($disable_invoice_auto_journals || ($acountRoutingType == Settings::CANCEL_ACCOUNTS_ROUTING)){

                return false;
            }
        } else if($data['Invoice']['type']==Invoice::Refund_Receipt)
        {
            $item_type='Refund Receipt';
            $debit='credit';
            $credit='debit';
            $account='returns';
            $entity_type='refund_receipt';
            $acountRoutingType = settings::getValue(AccountingPlugin, 'returns_accounts_routing');
            if(($acountRoutingType == Settings::CANCEL_ACCOUNTS_ROUTING)){

                return false;
            }
        } else if($data['Invoice']['type']==Invoice::Credit_Note)
        {
            $item_type='Credit Note';
            $debit='credit';
            $credit='debit';
            $account='returns';
            $entity_type='credit_note';
            $accountRoutingType = settings::getValue(AccountingPlugin, 'returns_accounts_routing');
            if(($accountRoutingType == Settings::CANCEL_ACCOUNTS_ROUTING)){
                return false;
            }
        }else if($data['Invoice']['type']==Invoice::Subscription)
        {
            $journalAccountRoute = GetObjectOrLoadModel('JournalAccountRoute');
            $journalAccountRoute->getAutoJournalAccountAccountRoute(['entity_type' => 'sales', 'entity_id' => 0, 'type' => 'fixed'], $data);
            return false;
        }else if($data['Invoice']['type']==Invoice::DEBIT_NOTE)
        {
            $acountRoutingType = settings::getValue(AccountingPlugin, 'sales_accounts_routing');
            if($disable_invoice_auto_journals || ($acountRoutingType == Settings::CANCEL_ACCOUNTS_ROUTING)){
                return false;
            }
            $item_type='Debit Note';
            $debit='debit';
            $credit='credit';
            $account='sales';
            $entity_type='debit_note';
        } else if ($data["Invoice"]["type"] == Invoice::ADVANCE_PAYMENT) {
            $item_type = "Advance Payment";
            $debit = "debit";
            $credit = "credit";
            $account = "clients_advance_payment";
            $entity_type = "advance_payment";
        }
        else  return false;
        
        if(!isset($data['Invoice']['summary_total'])||!isset($data['Invoice']['client_id'])) return false;

        $data=$this->calculate_taxes_amount($data);

        $custom_journal_description=settings::getValue(InvoicesPlugin, 'custom_journal_description');

        if(empty($custom_journal_description)) {
            $description = __($item_type, true) . ' #' . $data['Invoice']['no'];
        }else{

            App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));

            $text=$custom_journal_description;
            $placeholders = PlaceHolder::invoice_place_holder($data);
            for ($i = 1; $i <= 10; $i++) {
                $text = PlaceHolder::replace($text, array_keys($placeholders), array_values($placeholders),false);
            }
            $text = PlaceHolder::replace($text, array_keys($placeholders), array_values($placeholders),true);
            $description = $text;
        }

        $journal['Journal']['date']=$data['Invoice']['date'];
        $journal['Journal']['entity_type']=$entity_type;
        $journal['Journal']['description']=$description;
        $journal['Journal']['entity_id']=!empty($data['Invoice']['id'])?$data['Invoice']['id']:$this->id;
        if (!empty($data[$this->alias]['branch_id'])) { //if branch plugin not active will save journal branch to null so must check if data has branch id or not
            $journal['Journal']['branch_id'] = $data[$this->alias]['branch_id'];
        }
        if (ifPluginActive(PosPlugin)) {
            $pos_accounting_settings = $this->checkInvoicePosShiftCalculatedPerInvoice($data['Invoice']['pos_shift_id']);
            $invoice = $this->getInvoice($data['Invoice']['id']);
            if (empty($pos_accounting_settings) && !empty($invoice['Invoice']['pos_shift_id'])) {
                return $journal;
            }
        }

        if(!empty($data['Invoice']['draft'])) return $journal;
        $invoice_total=$data['Invoice']['summary_total'];
       if(!empty($data['Invoice']['draft']))
       {
           return  $journal;
       }
        $insuranceAgentID = null;
        if (ifPluginActive(INSURANCE_PLUGIN)) {
            foreach ($data['InvoiceItem'] as $invoiceItem) {
                if (isset($invoiceItem['extra_details'])) {
                    $extraDetails = json_decode($invoiceItem['extra_details'], true);
                    if (isset($extraDetails['copayment'])) {
                        $invoice_total += $extraDetails['copayment'];
                        $insuranceAgentID = $extraDetails['insurance_agent'];
                    }
                }
            }
        }
        $journal['JournalTransaction'][0]=
            array(
                    'subkey'=>'sales', 
                    'currency_'.$credit=>$invoice_total,
                    'currency_code'=>$data['Invoice']['currency_code'],
                    'description'=> (!empty($custom_journal_description)) ? $description : $description. ' '.__($account,true),
                    'auto_account'=>array('type'=>'fixed' , 'entity_type'=> $account,'entity_id'=>0)
                );


        if($data['Invoice']['source_type'] == InvoiceSourceTypesUtil::CONTRACT_INSTALLMENT && ifPluginActive(LEASE_CONTRACT_PLUGIN)) {
            $this->loadModel('LeaseContract');
            $contract = $this->LeaseContract->find("first",$this->data['Invoice']['source_type']);
            $journal['JournalTransaction'][0]['journal_account_id'] = $contract['JournalAccount']['id'] ;
        }
        $journal['JournalTransaction'][]=
            array(
                'subkey'=>'client',
                'currency_'.$debit=>$data['Invoice']['summary_total'],
                'currency_code'=>$data['Invoice']['currency_code'],
                'description'=>$description,
                'auto_account'=>array('type'=>'dynamic', 'entity_type'=>'client','entity_id'=>$data['Invoice']['client_id'])
            );

        $is_discount_allowed = settings::getValue(AccountingPlugin , "discount_allowed_accounts_routing") ;
        $is_discount_allowed = (!empty($is_discount_allowed) && $is_discount_allowed!=Settings::CANCEL_ACCOUNTS_ROUTING);
        if($is_discount_allowed)
        {
            $total_discount=$this->calculate_total_discount($data);
            $journal['JournalTransaction'][]=
                array(
                    'subkey'=>'discount_allowed',
                    'currency_'.$debit=>$total_discount,
                    'currency_code'=>$data['Invoice']['currency_code'],
                    'description'=>$description,
                    'auto_account'=>array('type'=>'dynamic', 'entity_type'=>'discount_allowed','entity_id'=>0)
                );
            $allowed_discount_index=count($journal['JournalTransaction'])-1;
            $journal['JournalTransaction'][0]['currency_'.$credit]+=$total_discount;
        }

        $adjustment_routing = settings::getValue(AccountingPlugin , "adjustment_accounts_routing") ;
        $is_adjustment_allowed = (!empty($adjustment_routing) && $adjustment_routing!=Settings::CANCEL_ACCOUNTS_ROUTING);

        if($is_adjustment_allowed)
        {
            $adjustment= $data['Invoice']['adjustment_value'];
            if($adjustment) {
                $this->loadModel('Journal');

                $adjustmentTransaction = array(
                    'subkey'=>Journal::ADJUSTMENT_ACCOUNT_ENTITY_TYPE,
                    'currency_'.$credit=> $adjustment,
                    'currency_code'=>$data['Invoice']['currency_code'],
                    'description'=>$description
                );

                if(isset($this->data['JournalAccountAdjustmentRoute']['account_id'])) {
                    if(!empty($this->data['JournalAccountAdjustmentRoute']['account_id'])) {
                        $adjustmentTransaction['journal_account_id'] = $this->data['JournalAccountAdjustmentRoute']['account_id'];
                    } else {
                        $defaultAdjustmentAccount =  $this->Journal->get_auto_account(['entity_type' => 'discount_allowed', 'entity_id' => 0]);
                        if(!$defaultAdjustmentAccount) {
                            $defaultAdjustmentAccount['JournalAccount']['id'] = $this->Journal->create_auto_account(['entity_type' => 'discount_allowed', 'entity_id' => 0]);
                        }
                        $adjustmentTransaction['journal_account_id'] = $defaultAdjustmentAccount['JournalAccount']['id'];
                    }

                } else {
                    $adjustmentTransaction['auto_account'] =array('type'=>'fixed', 'entity_type'=>'adjustment','entity_id'=>0);
                }
                $journal['JournalTransaction'][]= $adjustmentTransaction;
                $journal['JournalTransaction'][0]['currency_'.$credit]-=$adjustment;
            }

        }

        $extra_details=json_decode($data['Invoice']['extra_details'],true);
        if(!empty($extra_details)&&!empty($extra_details['invoice_accounts']))
        {
            foreach($extra_details['invoice_accounts'] as $account_id => $invoice_account)
            {
                if(!empty($invoice_account['value'])&&abs($invoice_account['value'])>0)
                    $journal['JournalTransaction'][]  = array(
                    'subkey'=>'invoice_accounts_'.$account_id,
                    'currency_'.$credit=> $invoice_account['value'],
                    'currency_code'=>$data['Invoice']['currency_code'],
                    'journal_account_id'=>$account_id,
                    'description'=>$description
                );
                $journal['JournalTransaction'][0]['currency_'.$credit]-=$invoice_account['value'];
            }
        }


        //Handel Sales Account for each product Case
        $product_sales_routing = settings::getValue(AccountingPlugin,'product_sales_accounts_routing');
        $this->loadModel('JournalAccount');
        if(!empty($product_sales_routing) && $product_sales_routing == settings::MANUAL_ACCOUNTS_ROUTING) {
            $this->loadModel('Product');
            foreach($data['InvoiceItem'] as $item)
            {
                if(!empty($item['product_id']))
                {
                    $account_id = $this->Product->getSalesAccount($item['product_id']);
                    $journalAccount = $this->JournalAccount->find('first', ['conditions' => ['JournalAccount.id' => $account_id], 'applyBranchFind' => false]);
                    if(!empty($account_id) && !empty($journalAccount))
                    {
                        $product_sales_amount=$item['subtotal'];
                        //Deduct taxes from subtotla if it is applied
                        if(!empty($item['summary_tax1']))
                            $product_sales_amount-=$item['summary_tax1'];

                        if(!empty($item['summary_tax2']))
                            $product_sales_amount-=$item['summary_tax2'];

                        if($is_discount_allowed)
                            $product_sales_amount+=$item['calculated_discount'];



                        $journal['JournalTransaction'][]=
                            array(
                                'subkey'=>'product_sales_'.$item['product_id'].'_'.$item['id'],
                                'currency_'.$credit=> $product_sales_amount,
                                'currency_code'=>$data['Invoice']['currency_code'],
                                'description'=>$description.' - '.__('Sales',true).' - '.$item['item'],
                                'journal_account_id'=>$account_id
                            );

                        $journal['JournalTransaction'][0]['currency_'.$credit]-= $product_sales_amount;
                    }
                }
            }

        }

            //This was to support insurance with its taxes in invoice journals
            $insuranceTotal = $invoice_total - $data['Invoice']['summary_total'];
            $insuranceTaxValue = 0;

            if(!empty($data['InvoiceTax']))
            {
                $TaxObject = GetObjectOrLoadModel('Tax');
                foreach($data['InvoiceTax'] as $tax)
                {

                    if(!empty($tax['name'])&&!empty($tax['amount']))
                    {
                        if($insuranceAgentID) {
                            $currentinsuranceTaxValue = $TaxObject->getTaxAmount($tax['tax_id'], $insuranceTotal);
                            $insuranceTaxValue += $currentinsuranceTaxValue;
                        } 
                        //Deduct the tax from the sales account, and add to the tax account
                        $journal['JournalTransaction'][0]['currency_'.$credit]-=$tax['amount'];
                        $taxAccount[]= $journal['JournalTransaction'][]=
                         [
                                'subkey'=>$tax['tax_id'].'-'.$tax['name'],
                                'currency_'.$credit=> $tax['amount'] + $currentinsuranceTaxValue,
                                'currency_code'=>$data['Invoice']['currency_code'],
                                'description'=>$description.' '.$tax['name'].' '.__('Tax',true),
                                'auto_account'=> ['type'=>'dynamic' , 'entity_type'=>'income_tax','entity_id'=>$tax['tax_id']]
                         ];

                    }
                }
            }
            if(!empty($data['Invoice']['shipping_amount'])&&$data['Invoice']['shipping_amount']!=0)
            {
                $data['Invoice']['shipping_amount'] = (float)$data['Invoice']['shipping_amount'];
                $shippingOptionId = $data['Invoice']['shipping_option_id'];
                $ShippingOptionModel = GetObjectOrLoadModel('ShippingOption');
                $shippingOption = $ShippingOptionModel->find('first', ['conditions' => ['ShippingOption.id' => $shippingOptionId]]);
                $shippingAccount = $shippingOption['ShippingOption']['account_id'];
                //Deduct the tax from the sales account, and add to the tax account
                $shippingTax = !empty($data['InvoiceTax'][$data['Invoice']['shipping_tax_id']]) ? $data['InvoiceTax'][$data['Invoice']['shipping_tax_id']]: [];
                $journal['JournalTransaction'][0]['currency_'.$credit]-=$data['Invoice']['shipping_amount'] - $this->getInclusiveTaxAmount($data['Invoice']['shipping_amount'], $shippingTax);
				$journal['JournalTransaction'][]=
                 [
                        'subkey'=>'shipping', 
                        'currency_'.$credit=>$data['Invoice']['shipping_amount'] - $this->getInclusiveTaxAmount($data['Invoice']['shipping_amount'], $shippingTax),
                        'currency_code'=>$data['Invoice']['currency_code'],
                        'description'=>$description.' '.__('Shipping',true),
                        'journal_account_id' => $shippingAccount,
                        'auto_account'=> ['type'=>'static' , 'entity_type'=>'sales_shipping','entity_id'=>0]
                 ];
            }

            if ($insuranceAgentID) {
                $journal['JournalTransaction'][] =
                    [
                        'subkey' => 'insurance_agent',
                        'currency_' . $debit => ($insuranceTotal + $insuranceTaxValue),
                        'currency_code' => $data['Invoice']['currency_code'],
                        'description' => $description,
                        'auto_account' => ['type' => 'dynamic', 'entity_type' => 'insurance_agent', 'entity_id' => $insuranceAgentID]
                    ];
            }
        if(abs($journal['JournalTransaction'][0]['currency_'.$credit]) < 0.00001) {
            $journal['JournalTransaction'][0]['currency_'.$credit] = 0;
        }

        // advance payment journals will be added here
        if ($entity_type == 'advance_payment' && $account == 'clients_advance_payment') {
            $journal['JournalTransaction'] = [];

            if (empty($data["Payment"]['treasury_id']) && empty($data["MultiplePayment"]) && count($data["InvoicePayment"]) == 1) {
               $treasuries = [$data["InvoicePayment"][0]["treasury_id"] => $invoice_total];
            } else if (!empty($data["Payment"]['treasury_id'])) {
                $treasuries = [$data["Payment"]['treasury_id'] => $invoice_total];

            } else if (!empty($data["MultiplePayment"])) {
                $treasuries = [];
                foreach ($data["MultiplePayment"] as $payment) {
                    $treasuries[$payment["treasury_id"]]+= $payment["amount"];
                }
            } else if (!empty($data["InvoicePayment"]) && count($data["InvoicePayment"]) > 1) {
                $treasuries = [];
                foreach ($data["InvoicePayment"] as $payment) {
                    $treasuries[$payment["treasury_id"]]+= $payment["amount"];
                }
            }

            $journal['JournalTransaction'][] = array(
                'subkey' => 'prepaid_tax',
                'currency_' . $debit => array_sum( array_column($taxAccount??[], "currency_".$credit)),
                'currency_code' => $data['Invoice']['currency_code'],
                'description' =>  "",
                'auto_account' => array('type' => 'fixed', 'entity_type' => 'prepaid_tax', 'entity_id' =>  0)
            );

            foreach ($taxAccount as $tax) {
                $journal['JournalTransaction'][] = $tax;

            }

            $journal['JournalTransaction'][] = array(

                'subkey' => 'clients_advance_payment',
                'currency_' . $credit => $invoice_total,
                'currency_code' => $data['Invoice']['currency_code'],
                'description' => $description . ' ' . __('Advance Payment', true),
                'auto_account' => array('type' => 'dynamic', 'entity_type' => 'clients_advance_payment', 'entity_id' =>  $data['Invoice']['client_id'])
            );

            foreach ($treasuries as $treasury_id => $treasury_amount) {
                $journal['JournalTransaction'][]=  [
                    'subkey'=>'treasury_'.$treasury_id,
                    'currency_debit'=> $treasury_amount,
                    'currency_code'=> $data["Invoice"]['currency_code'],
                    'description'=>"",
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'treasury','entity_id'=> $treasury_id)
                ];
            }


        }

        if (!empty($data['Invoice']['subscription_id'])) {
            $baseInvoice = $this->getInvoice($data['Invoice']['subscription_id']);
        }

        $enableAdvance = settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::SalesPlugin, SettingsUtil::ENABLE_ADVANCE_PAYMENT);
        if (
            $entity_type == 'credit_note' &&
            isset($baseInvoice) &&
            $baseInvoice["Invoice"]["type"] == Invoice::ADVANCE_PAYMENT &&
            $enableAdvance
        ) {
            $journal['JournalTransaction'] = [];

            $journal['JournalTransaction'][] = array(
                'subkey'             => 'prepaid_tax',
                'currency_' . $debit => array_sum(array_column($taxAccount ?? [], "currency_" . $credit)),
                'currency_code'      => $data['Invoice']['currency_code'],
                'description'        => "",
                'auto_account'       => array(
                    'type' => 'fixed',
                    'entity_type' => 'prepaid_tax',
                    'entity_id' => 0
                )
            );

            foreach ($taxAccount as $tax) {
                $journal['JournalTransaction'][] = $tax;
            }

            $journal['JournalTransaction'][] = array(
                'subkey'              => 'clients_advance_payment',
               'currency_' . $credit => $invoice_total,
                 'currency_code'       => $data['Invoice']['currency_code'],
                'description'         => $description . ' ' . __('Advance Payment', true),
                'auto_account'        => array(
                    'type'        => 'dynamic',
                    'entity_type' => 'clients_advance_payment',
                    'entity_id'   => $data['Invoice']['client_id']
                )
            );

            $journal['JournalTransaction'][] =
                array(
                    'subkey'             => 'client',
                    'currency_' . $debit => $data['Invoice']['summary_total'],
                    'currency_code'      => $data['Invoice']['currency_code'],
                    'description'        => $description,
                    'auto_account'       => array(
                        'type'        => 'dynamic',
                        'entity_type' => 'client',
                        'entity_id'   => $data['Invoice']['client_id']
                    )
                );
        }

        if ($enableAdvance) {
            $advancePaymentService = resolve(\Izam\Daftra\Invoice\Services\AdvancePaymentService::class);
            $relatedInvoicesData = $advancePaymentService->getInvoiceWithAdvancePayments($data["Invoice"]["id"]);
        }


        if (!empty($relatedInvoicesData) && $relatedInvoicesData["relatedInvoices"]) {
            $transactions = array_filter($journal['JournalTransaction'], function ($item) {
               return $item['auto_account']['entity_type'] != "income_tax";
            });

            $totalAdvancePayment = $relatedInvoicesData['totalAdvancePayment'];

            $invoiceNo = $data['Invoice']['no'];
            $journal['JournalTransaction'] = $transactions;
            foreach ($relatedInvoicesData['relatedInvoices'] as $key => $related) {
                $advancePaymentNo = $related['no'];
                $advancePaymentAmount = $related['salesAmount'];
                $advanceDescription = sprintf(__('Advance Payment invoice #%s for Invoice #%s', true), $advancePaymentNo, $invoiceNo);
                $journal['JournalTransaction'][] = array(
                    'subkey'                => ($key + 1) . '-clients_advance_payment',
                    'currency_' . $debit    => $advancePaymentAmount,
                    'currency_code'         => $data['Invoice']['currency_code'],
                    'description'           => $advanceDescription,
                    'auto_account'          => array(
                        'type'          => 'dynamic',
                        'entity_type'   => 'clients_advance_payment',
                        'entity_id'     => $data['Invoice']['client_id']
                    )
                );

                $journal['JournalTransaction'][] =
                    array(
                        'subkey'                => ($key + 1) . '-client-1',
                        'currency_' . $credit   => $advancePaymentAmount,
                        'currency_code'         => $data['Invoice']['currency_code'],
                        'description'           => $advanceDescription,
                        'auto_account'          => array(
                            'type'          => 'dynamic',
                            'entity_type'   => 'client',
                            'entity_id'     => $data['Invoice']['client_id']
                        )
                    );
            }



                $taxTotals = array_sum( array_column($taxAccount??[], "currency_".$credit));

                $prePaidTax = 0;
                foreach ($relatedInvoicesData['relatedInvoices'] as $related) {
                    $prePaidTax+= $related['taxes']['total'];
                }
                if ($taxTotals) {
                    $journal['JournalTransaction'][] = array(
                        'subkey' => (3+count($taxAccount)).'-prepaid_tax',
                        'currency_' . $credit => $prePaidTax,
                        'currency_code' => $data['Invoice']['currency_code'],
                        'description' =>  "",
                        'auto_account' => array('type' => 'fixed', 'entity_type' => 'prepaid_tax', 'entity_id' =>  0)
                    );
                }

                $lastTaxAmount = $taxTotals - $prePaidTax;

                $index = 2;
                $code = getCurrentSite('currency_code');
                if (isset($data['Invoice']['currency_code'])) {
                    $code = $data['Invoice']['currency_code'];
                }
                $round_number = Cache::read('number_formats')[$code][0] ?? 2;
                if (round($taxTotals, $round_number) != round($prePaidTax, $round_number)) {
                    foreach ($taxAccount as $account) {
                        $percentage = $account['currency_'.$credit]/ $taxTotals;
                         $account["currency_".$credit] = $percentage * $lastTaxAmount;
                        $account["subkey"] = $index."-".$account["subkey"];
                        $journal["JournalTransaction"][] = $account;
                        $index++;
                    }
                }
        }

        $journal['JournalTransaction']=array_reverse($journal['JournalTransaction']);
        return $journal;
    }
	
	function get_assigned_staff($invoice_id,$get_staff_id = false)
	{
		$invoice = $this->find('first',array('callbacks' => false,'recursive'=>-1,'conditions'=>array('Invoice.id'=>$invoice_id)));
		$this->loadModel('Client');
		$assigned = $this->Client->get_assigned_staff($invoice['Invoice']['client_id']);
		if(!$assigned && $get_staff_id)
			$assigned = $invoice['Invoice']['staff_id'];
		return $assigned;
		
	}
        
  function fix_overpaid($invoice_id, $automatic_payments_only=true,$convert_to_client_payment=false){
   
      $i=0;
        $invoice=$this->getInvoice($invoice_id);
	$overpaid= $invoice['Invoice']['summary_paid']-$invoice['Invoice']['summary_total'];

        $InvoicePaymentConditions['InvoicePayment.invoice_id']=$invoice_id;
        if($automatic_payments_only){
        $InvoicePaymentConditions['InvoicePayment.payment_method']='client_credit';    
        }

        while($overpaid>MINOVERPAID)
        {
     //InvoicePayment.amount
       $this->InvoicePayment->alias = 'InvoicePayment';
        // Get Invoice Payment order by Client Credit
        // get new payment first
        $last_payment = $this->InvoicePayment->find('first', array(
            'recursive' => -1,
            'order' => array(
                'FIELD(`payment_method`, "client_credit") DESC', 
                'date' => 'DESC',
                'FIELD(`amount`, "' . $overpaid . '")',
            ),
            'conditions' => $InvoicePaymentConditions
        ));
         
        $payment_amount_before_update=$last_payment['InvoicePayment']['amount'];
        // subtract overpaid amount from Payment & save payment
        $last_payment['InvoicePayment']['amount']=($last_payment['InvoicePayment']['amount']-$overpaid);
        
        if($last_payment['InvoicePayment']['amount']>0){
            // unset validate for payment_method client_credit
            if($last_payment['InvoicePayment']['payment_method']=="client_credit"){
            unset($this->InvoicePayment->validate['payment_method']);
            }
            
        if($convert_to_client_payment==true and $last_payment['InvoicePayment']['payment_method']!="client_credit"){
            $last_payment['InvoicePayment']['amount'] = $overpaid;
            $last_payment['InvoicePayment']['invoice_id']=null;    
            $last_payment['InvoicePayment']['client_id']=$invoice['Invoice']['client_id'];  

            $data = array('InvoicePayment' => array());
            $data['InvoicePayment']['invoice_id'] = $invoice_id;
            $data['InvoicePayment']['staff_id'] = getAuthOwner('staff_id');
            $data['InvoicePayment']['status'] = PAYMENT_STATUS_COMPLETED;
            $data['InvoicePayment']['payment_method'] = $last_payment['InvoicePayment']['payment_method'];
            $data['InvoicePayment']['date'] = $last_payment['InvoicePayment']['date'] ?? (date("Y-m-d"));
            $data['InvoicePayment']['amount'] = $payment_amount_before_update - $overpaid;
            $data['InvoicePayment']['currency_code'] = $invoice['Invoice']['currency_code'];
            $data['InvoicePayment']['added_by'] = 1;
            $data['InvoicePayment']['treasury_id'] = $last_payment['InvoicePayment']['treasury_id'];
            $data['InvoicePayment']['branch_id'] = $last_payment['InvoicePayment']['branch_id'];
            $data['InvoicePayment']['extra_details'] = $last_payment['InvoicePayment']['extra_details'];
            $data['InvoicePayment']['source'] = $last_payment['InvoicePayment']['source'];
            GetObjectOrLoadModel('Invoice')->ownerAddPayment($data);

            // Convert Invoice Payment to Client Payment
            $this->InvoicePayment->ConvertPayment($last_payment,$invoice_id);
        }else{
            $this->InvoicePayment->editPayment($last_payment);
            if($last_payment['InvoicePayment']['payment_method']!="client_credit"){
                $invoice=$this->getInvoice($invoice_id);     
                $this->add_actionline(ACTION_DELETE_INVOICE_PAYMENT, array('staff'=>-1,'primary_id' => $invoice['Invoice']['id'], 'secondary_id' => $invoice['Invoice']['client_id'], 'param1' => $last_payment['InvoicePayment']['amount'], 'param2' => $invoice['Invoice']['payment_status'], 'param3' => $invoice['Invoice']['summary_paid'], 'param4' => $invoice['Invoice']['no'], 'param5' => $last_payment['InvoicePayment']['id'], 'param6' => $invoice['Invoice']['summary_total'], 'param7' => $last_payment['InvoicePayment']['status'], 'param8' => $last_payment['InvoicePayment']['payment_method'], 'param9' => $last_payment['InvoicePayment']['transaction_id']));
            }
        }
        $this->updateInvoicePayments($invoice_id);
        // Pay Invoice From Credit
        // $this->Client->pay_invoice_from_credit($invoice['Invoice']['id'],true);
        //Pay Current Invoice From Credit
        $this->Client->pay_invoice_from_credit($invoice['Invoice']['id'],true);

        }else{
        if($convert_to_client_payment==true and $last_payment['InvoicePayment']['payment_method']!="client_credit"){
              
        $last_payment['InvoicePayment']['amount']=$payment_amount_before_update;   
        $last_payment['InvoicePayment']['invoice_id']=null;    
        $last_payment['InvoicePayment']['client_id']=$invoice['Invoice']['client_id'];  
      
        // Convert Invoice Payment to Client Payment
        $this->InvoicePayment->ConvertPayment($last_payment,$invoice_id);      
         $this->Client->pay_invoice_from_credit($invoice['Invoice']['id'],true);
            $this->updateInvoicePayments($invoice['Invoice']['id']);
   }else{
          $this->InvoicePayment->delete($last_payment['InvoicePayment']['id']);
            $this->InvoicePayment->delete_auto_journals($last_payment['InvoicePayment']['id']);
   }

          if($last_payment['InvoicePayment']['payment_method']!="client_credit"){
          $invoice=$this->getInvoice($invoice_id);               
          $this->add_actionline(ACTION_DELETE_INVOICE_PAYMENT, array('staff'=>-1,'primary_id' => $invoice['Invoice']['id'], 'secondary_id' => $invoice['Invoice']['client_id'], 'param1' => $payment_amount_before_update, 'param2' => $invoice['Invoice']['payment_status'], 'param3' => $invoice['Invoice']['summary_paid'], 'param4' => $invoice['Invoice']['no'], 'param5' => $last_payment['InvoicePayment']['id'], 'param6' => $invoice['Invoice']['summary_total'], 'param7' => $last_payment['InvoicePayment']['status'], 'param8' => $last_payment['InvoicePayment']['payment_method'], 'param9' => $last_payment['InvoicePayment']['transaction_id']));
          }
        }
        // Update Invoice payment 
        $result = $this->updateInvoicePayments($invoice_id);
        // reread invoice and calculate over paid amount again
        $invoice=$this->getInvoice($invoice_id); 
        
        $overpaid= $invoice['Invoice']['summary_paid']-$invoice['Invoice']['summary_total'];
        $i++;
        if($i>5) break;
        }
        
  }
  function check_minimum_price ( $invoice_items , &$product_name , &$minimum_price ) {
      $advanced_pricing = settings::getValue(InventoryPlugin , "advanced_pricing_options") ;
      if ( $advanced_pricing ) {
        if (check_permission(ALLOW_MINIMUM_PRICE))
        {
            return true ; 
        }
        $this->loadModel('Product');
        foreach ( $invoice_items as $i  ){
            if ( !empty($i['product_id']))
            {
                $product = $this->Product->find('first' ,['recursive' => -1 ,  'conditions' => ['id' => $i['product_id']] ] );

                $i['subtotal'] = (float)$i['subtotal'];
                $i['quantity'] = (float)$i['quantity'];
                if ($product['Product']['minimum_price'] && Settings::getValue(PluginUtil::InventoryPlugin, 'advanced_pricing_options') && in_array(Settings::getValue(PluginUtil::SalesPlugin, 'minimum_price_calculation'), ["tax", null, "", 0]) && ($i['subtotal'] / (float)$i['quantity']) < $product['Product']['minimum_price']) {
                    $product_name = $product['Product']['name'];
                    $minimum_price = $product['Product']['minimum_price'];
                    return false;
                }
                $price = round((((double) $i['subtotal'] - (double) $i['summary_tax1'] - (double) $i['summary_tax2']) / (double) $i['quantity']), 3);
                if ($product['Product']['minimum_price'] && Settings::getValue(PluginUtil::InventoryPlugin, 'advanced_pricing_options') && Settings::getValue(PluginUtil::SalesPlugin, 'minimum_price_calculation') == "without" && $price < round($product['Product']['minimum_price'], 3)) {
                    $product_name = $product['Product']['name'];
                    $minimum_price = $product['Product']['minimum_price'];
                    return false;
                }
            }
        }
        return true ; 
      }else {
          return true ; 
      }
      
  }
  function getDiscountTypes ( ) {
      return [
          Invoice::DISCOUNT_TYPE_PERCENTAGE => __("%" , true),
          Invoice::DISCOUNT_TYPE_VALUE => __("$" , true),
      ];
  }

  function isPosOpenedOrNotPos($pos_shift_id) {
	  
	    if (empty($pos_shift_id))
	        return true;
	    $this->loadModel('PosShift');
	    $this->PosShift->recursive = -1;
	    $status = $this->PosShift->read(['status'], $pos_shift_id)['PosShift']['status'];
	    return $status == PosShift::STATUS_OPENED;
  }
  
  function afterSave($created)
  {
	$this->addSalesPerson($created);
	return true;
  }

    private function addSalesPerson($created)
    {
        $this->loadModel('ItemStaff');
        if($this->data['Invoice']['sales_person_id'] != self::MULTIPLE_SALES_PERSONS){
            if(isset($this->data['Invoice']['sales_person_id']) && $this->data['Invoice']['sales_person_id'] != -1)
            {
                $salesPersonId = $this->data['Invoice']['sales_person_id'];
            }else if($created && empty($this->data['Invoice']['sales_person_id']))
            {
                $salesPersonId = $this->data['Invoice']['staff_id'];
            }else if(!isset ($this->data['Invoice']['sales_person_id'])){
                $salesPersonId = false;
            }else if($this->data['Invoice']['sales_person_id'] == -1)
            {
                $salesPersonId = null;
            }else if($this->data['Invoice']['sales_person_id'] == null)
            {
                $salesPersonId = null;
            }

            if($salesPersonId !== false && $salesPersonId!==null){
                $this->ItemStaff->assign_staff_members([$salesPersonId], $this->id, ItemStaff::SALES_ITEM_TYPE);
            }
        }else{
            if(!$created && !isset($this->data['InvoiceItem'][0]['id'])){
                $this->ItemStaff->deleteItemStaffsByGroup($this->id, ItemStaff::INVOICE_ITEM_SALES_TYPE);
            }
        }
    }

public function updateInvoiceType($id = null, $newtype = null, $forceUpdateInvoiceNo = false, $forceUpdateStock = false)
{
	$this->id = $id;
	if ($id == null) {
		return false;
	}
	
	if ($newtype === null) {
		return false;
	}
	
	if (!$this->exists($id)) {
		return false;
	}
	
	$this->saveField('type', $newtype);
	
	if ($forceUpdateInvoiceNo)
		$this->updateNextInvoiceNo($id, Invoice::Invoice);
	
	$invoice = $this->find('first', array('conditions' => array('Invoice.id' => $id)));
	if ($forceUpdateStock)
		$this->updateInvoiceStock($invoice);
	
	$this->update_journals($invoice);
}

public function updateInvoiceStock($invoice)
{
	$enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions', null, false);
	if ($enable_requisitions) {
		$createRequisition = true;
		if (ifPluginActive(PosPlugin)) {
            $pos_accounting_settings = $this->checkInvoicePosShiftCalculatedPerInvoice($invoice['Invoice']['pos_shift_id']);
			if (empty($pos_accounting_settings) && !empty($invoice['Invoice']['pos_shift_id'])) {
				$createRequisition = false;
			}
		}
		if ($createRequisition) {
			$this->loadModel('Requisition');
			$this->Requisition->updateForOrder($invoice, Requisition::ORDER_TYPE_INVOICE, false, false);
		}
	} else if (ifPluginActive(InventoryPlugin)) {
		$this->loadModel('StockTransaction');
		StockTransaction::updateForInvoice($invoice);
	}
}
    
public function updateNextInvoiceNo($id = null, $type=null) {
    App::import('Vendor', 'AutoNumber');
    $this->id = $id;
    if ($id == null) {
        return false;
    }

    if ($type === null) {
        return false;
    }

    if (!$this->exists($id)) {
        return false;
    }
    switch ($type) {
        case Invoice::Invoice:
            \AutoNumber::set_validate(\AutoNumber::TYPE_INVOICE);
            $no = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE);
            break;
        case Invoice::Estimate:
            \AutoNumber::set_validate(\AutoNumber::TYPE_ESTIMATE);
            $no = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_ESTIMATE);
            break;
        case Invoice::SALES_ORDER:
            \AutoNumber::set_validate(\AutoNumber::TYPE_SALES_ORDER);
            $no = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_SALES_ORDER);
            break;
        case Invoice::BOOKING:
            \AutoNumber::set_validate(\AutoNumber::TYPE_BOOKING);
            $no = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_BOOKING);
            break;
        case Invoice::Credit_Note:
            \AutoNumber::set_validate(\AutoNumber::TYPE_CREDIT_NOTE);
            $no = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_CREDIT_NOTE);
            break;
        case Invoice::Refund_Receipt:
            \AutoNumber::set_validate(\AutoNumber::TYPE_REFUND_RECEIPT);
            $no = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_REFUND_RECEIPT);
            break;
        case Invoice::ADVANCE_PAYMENT:
            \AutoNumber::set_validate(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
            $no = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
            break;
        default:
            break;
    }
    $check = $this->saveField('no', $no);
    
switch ($type) {
        case Invoice::Invoice:
            $no = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_INVOICE);
            break;
        case Invoice::Estimate:
            $no = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_ESTIMATE);
            break;
        case Invoice::SALES_ORDER:
            $no = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_SALES_ORDER);
            break;
        case Invoice::BOOKING:
            $no = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_BOOKING);
            break;
        case Invoice::Credit_Note:
            $no = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_CREDIT_NOTE);
            break;
        case Invoice::Refund_Receipt:
            $no = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_REFUND_RECEIPT);
            break;
    case Invoice::ADVANCE_PAYMENT:
        $no = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_ADVANCE_PAYMENT_INVOICE);
        break;
        default:
            break;
    }       
    
}

  public function getDefaultInvoiceLayout()
  {
      $this->loadModel('InvoiceLayout');
      $layout = $this->InvoiceLayout->find(array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_Invoice,'InvoiceLayout.default' => 1));
      return $layout;
  }

	public function hasRenewals($id)
	{
	    $this->loadModel("MembershipRenewals");
	    return $this->MembershipRenewals->hasAny(["MembershipRenewals.invoice_id" => $id]);
	}

	public function updateClientInfo($invoice_id, $clientUpdateData)
	{
        $invoice = $this->getInvoice($invoice_id, array('Invoice.type' => array(Invoice::Invoice,Invoice::TEMPINVOICE)));
		$clientUpdateData = array_merge(array('id' => $invoice['Client']['id']), $clientUpdateData);
		$this->Client->save(['Client' => $clientUpdateData]);
        unset($clientUpdateData['id']);
        $invoiceClientUpdateData = [];
        foreach ($clientUpdateData as $key => $value) {
            $invoiceClientUpdateData["client_".$key] = $value;
        }
        $invoiceClientUpdateData['id'] = $invoice_id;
        $this->save(['Invoice' => $invoiceClientUpdateData]);

	}


    public function getAgreementInstallmentsCount($id)
    {
        if (!is_numeric($id)) return 0;

        $query = "SELECT COUNT(`agreement_installments`.`id`) as count
                    FROM invoices
                    left join `invoice_installment_agreements` on `invoice_installment_agreements`.`invoice_id` = `invoices`.`id`
                    left join `agreement_installments` on `agreement_installments`.`installment_agreement_id`  =`invoice_installment_agreements`.`id`
                    where invoices.id = $id
                    and `agreement_installments`.`deleted_at` is NULL
                    and `invoice_installment_agreements`.`deleted_at` is NULL";
        $result = $this->query($query);

        if (!isset($result[0][0]['count'])) return 0;

        return $result[0][0]['count'];
    }

    public function hasAgreementInstallments($id)
    {
        return !!$this->getAgreementInstallmentsCount($id);
    }

    public function deleteRelatedAgreementInstallments($id)
    {
        $ids = implode(",", $id);
        $query = "UPDATE `agreement_installments`
                    left join `invoice_installment_agreements` on `invoice_installment_agreements`.`id` = `agreement_installments`.`installment_agreement_id`
                    SET `agreement_installments`.`deleted_at` = CURRENT_TIMESTAMP
                    where `invoice_installment_agreements`.`invoice_id` in ($ids)";
        $this->query($query);
    }

    public function deleteRelatedAgreement($id)
    {
	    $url = '/v2/api/invoice_installment_agreements/deleteInvoiceAgreementForInvoice';
	    App::import('Component', 'ApiRequestsComponent');
	    $apiRequests = new ApiRequestsComponent();
	    $apiRequests->request($url,false,'POST', ['ids' => $id]);

	    $statusCode = $apiRequests->getStatusCode();
	    if ($statusCode < 200 || $statusCode >= 400){
            if(is_array($id)) {
                $ids = implode(",", $id);
            }else{
                $ids=$id;
            }
		    $query = "UPDATE `invoice_installment_agreements` SET `deleted_at`= CURRENT_TIMESTAMP WHERE `invoice_id` in ($ids)";
		    $this->query($query);

		    $query = "UPDATE `agreement_installments`
                    left join `invoice_installment_agreements` on `invoice_installment_agreements`.`id` = `agreement_installments`.`installment_agreement_id`
                    SET `agreement_installments`.`deleted_at` = CURRENT_TIMESTAMP
                    where `invoice_installment_agreements`.`invoice_id` in ($ids)";
		    $this->query($query);
	    }
    }

    public function hasInstallmentAgreement($id)
    {
        return !!$this->getInstallmentAgreement($id);
    }

    public function getInstallmentAgreement($id)
    {
        $query = "SELECT * FROM `invoice_installment_agreements` WHERE `invoice_id` = $id AND `deleted_at` is Null LIMIT 1";
        $result = $this->query($query);
        if (isset($result[0]['invoice_installment_agreements'])) {
            return $result[0]['invoice_installment_agreements'];
        }
        return null;
    }

    /**
     * @param $id
     * @return bool
     */
    public function hasSalesCommissions($id)
    {
        $Commission = GetObjectOrLoadModel('Commission');
        $result = $Commission->find('all', ['conditions' => ['Commission.invoice_id' => $id]]);
        if (isset($result)) {
            return $result;
        }
        return false;
    }

    /**
     * @param $salesCommissionsIds
     * @return mixed
     */
    public function deleteRelatedSalesCommissions($salesCommissionsIds)
    {
        if (!empty($salesCommissionsIds)) {
            \App\Services\Communicator\CommunicatorFacade::getInstance()->deleteCommissions($salesCommissionsIds);
        }
    }

    public function hasSalesCommissionsWithNonOpenStatus($id)
    {
        if (is_array($id))
            $invoice_id_condition = "`commissions`.`invoice_id` IN ('" . implode("','", $id) . "')";
        else
            $invoice_id_condition = "`commissions`.`invoice_id` = $id";

        $query = "Select count(*) as count from `commission_sheets`
                LEFT JOIN `commissions` on `commissions`.`commission_sheet_id` = `commission_sheets`.`id`
                where `commission_sheets`.`status` in ('approved','rejected','paid') and $invoice_id_condition";

        $result = $this->query($query);
        if (isset($result[0][0]["count"])) {
            return $result[0][0]["count"];
        }
        return false;
    }

    public function getInvoiceClientLink($invoice, $type) {
        $hash = $this->getHash($invoice);
        switch ($type) {
            case 'invoice':
                $site = getCurrentSite('subdomain');
                $client_view_link = 'https://' . $site .'/invoices/view/' . $invoice['Invoice']['id'] . '?hash=' . $hash;
                break;
            case 'estimate':
                $site = getCurrentSite('subdomain');
                $client_view_link = 'https://' . $site .'/invoices/view_estimate/' . $invoice['Invoice']['id'] . '?hash=' . $hash;
                break;
            case 'sales_order':
                $site = getCurrentSite('subdomain');
                $client_view_link = 'https://' . $site .'/invoices/view_sales_order/' . $invoice['Invoice']['id'] . '?hash=' . $hash;
                break;
        }

        return $client_view_link;
    }

//    public function getWhatsappMessage($invoice, $type) {
//        $link = $this->getInvoiceClientLink($invoice, $type);
//        switch ($type) {
//            case 'invoice':
//                $text = sprintf(__("Invoice #%s \n %s",true), $invoice['Invoice']['no'], $link);
//                break;
//            case 'estimate':
//                $text = sprintf(__("Estimate #%s \n %s",true), $invoice['Invoice']['no'], $link);
//                break;
//        }
//        return $text;
//    }

    public function getWhatsappMessage($invoice, $type)
    {
        App::import('Vendor', 'settings');
        $text = settings::getValue(InvoicesPlugin, 'whatsapp_text_' . $type);
        if (!empty($text)) {
            App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));
            $placeholders = PlaceHolder::invoice_place_holder($invoice);
            $text = PlaceHolder::replace($text, array_keys($placeholders), $placeholders);
        } else {

            $link = $this->getInvoiceClientLink($invoice, $type);
            switch ($type) {
                case 'invoice':
                    $text = sprintf(__("Invoice #%s \n %s", true), $invoice['Invoice']['no'], $link);
                    break;
                case 'estimate':
                    $text = sprintf(__("Estimate #%s \n %s", true), $invoice['Invoice']['no'], $link);
                    break;
                case 'sales_order':
                    $text = sprintf(__("Sales Order #%s \n %s", true), $invoice['Invoice']['no'], $link);
                    break;
            }
        }
        return $text;
    }


    public function getInvoiceClientWhattsapLink($invoice, $type)
    {
        $message = $this->getWhatsappMessage($invoice, $type);
        $client_number = $invoice['Client']['phone2'];
      //whats app doesn't accept number that  start with 00 so remove 00 from the beginning of the client number
        if (substr($client_number, 0, 2) == '00') {
            $client_number = substr($client_number, 2);
        }

        return 'https://wa.me/' . $client_number . '?text=' . urlencode($message);
    }

    public function afterCustomSaveCallback($data) {
        $invoiceRecord = $data['record'];
        if($invoiceRecord['Invoice']['type'] == self::Invoice) {
            $custom_journal_description=settings::getValue(InvoicesPlugin, 'custom_journal_description');
            if($custom_journal_description) {
                $journal = $this->getJournal($invoiceRecord['Invoice']['id'], 'invoice');
                App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));
                $placeholders = PlaceHolder::invoice_place_holder($data['record']);
                $text = PlaceHolder::replace($custom_journal_description, array_keys($placeholders),array_values($placeholders));
                $description = $text;
                if($journal && $journal['Journal']['description'] = $description) {
                    GetObjectOrLoadModel('Journal')->update($journal['Journal']['id'], ['description' => $description]);
                }
                $this->loadModel('JournalTransaction');
                foreach ($journal['JournalTransaction'] as $JournalTransaction) {
                    $this->JournalTransaction->read(null, $JournalTransaction['id']);
                    $this->JournalTransaction->saveField('description', $description);
                }
            }
        }
    }

    function getEInvoiceStatus(){
        $status =  [
            ElectronicInvoicesStatus::NOT_SUBMITTED => __('Not Submitted',true),
            ElectronicInvoicesStatus::SENT => __('Pending On ETA',true),
            ElectronicInvoicesStatus::SUBMITTED => __('Submitted',true),
            ElectronicInvoicesStatus::VALID => __('Valid',true),
            ElectronicInvoicesStatus::INVALID => __('Invalid',true),
            ElectronicInvoicesStatus::REJECTED => __('Rejected',true),
            ElectronicInvoicesStatus::CANCELLED => __('Cancelled',true),
            ElectronicInvoicesStatus::IN_PROGRESS => __('In Progress', true)
        ];

        if (ifPluginActive(PluginUtil::EINVOICE_PLUGIN) || ifPluginActive(PluginUtil::JORDAN_EINVOICE_PLUGIN)) {
            unset(
                $status[ElectronicInvoicesStatus::IN_PROGRESS], $status[ElectronicInvoicesStatus::CANCELLED],
                $status[ElectronicInvoicesStatus::REJECTED], $status[ElectronicInvoicesStatus::SENT],
                $status[ElectronicInvoicesStatus::SUBMITTED]
            );
        }
        return $status;
    }

    private function applyDiscountForRefund(&$data)
    {
        return;
        $invoice = $this->findById($data["Invoice"]["subscription_id"]);
        $data["Invoice"]['discount'] =\App\Services\InvoiceProfitCalculator\InvoiceProfitCalculator::calculateInvoiceDiscount($invoice)*100;
        $data["Invoice"]['discount_amount'] = null;
    }

    private function applyRefundDiscountForLoyalty(&$data)
    {
        $invoice = $this->findById($data["Invoice"]["subscription_id"]);
        $data["Invoice"]['discount'] =\App\Services\InvoiceProfitCalculator\InvoiceProfitCalculator::calculateInvoiceDiscount($invoice)*100;
        $data["Invoice"]['discount_amount'] = null;
        unset($data['Invoice']['points']);
    }

	public function getInclusiveTaxAmount($amount, $tax) {
		if (empty($tax)) return 0;
		if (empty($amount)) return 0;
		if (!$tax['included']) return 0;
		return $amount * $tax['value'] / (100 + $tax['value']);
	}

	public function getInvoiceCustomForm($invoice,$data) {
        $this->loadModel('CustomForm');
        $custom_data = $this->CustomForm->sperate_custom_form_data_and_custom_form($invoice, $data)['custom_form'];
        $custom_form = $this->CustomForm->findByTableName('invoices');
        $result = $this->CustomForm->validate_custom_form_data($custom_form, $custom_data);
        return $result;
	}

    public function checkInvoicePosShiftCalculatedPerInvoice($posShiftId) {
        if(empty($posShiftId)) {
            return true;
        }

        $this->loadModel('PosShift');
        $posShift = $this->PosShift->find('first', ['conditions' => ['PosShift.id' => $posShiftId], 'applyBranchFind' => false]);
        if(!$posShift) {
            return true;
        }
        return $this->PosShift->checkPosShiftIsCalculatedPerInvoice($posShift);
    }

    function paginateCount($conditions = null, $recursive = 0, $extra = array())
    {
        $conditionsString = json_encode($conditions);
        preg_match_all('/`?([A-Za-z]+)`?\.`?[A-Za-z]+`?/', $conditionsString, $m);
        $recursive = -1;
        foreach ($m[1] as $modelName) {
            if($modelName != 'Invoice') {
                $recursive = 1;
            }
        }
        return $this->find('count', ['conditions' => $conditions , 'extra' => $extra , 'recursive' => $recursive]);
    }

    public function updateUnitBookings($invoiceId)
    {
        $this->loadModel('RentalUnitBooking');

        $this->RentalUnitBooking->updateAll(
            ['RentalUnitBooking.invoice_id' => null],
            ['RentalUnitBooking.invoice_id' => $invoiceId]
        );
    }

    function prepare_custom_fields($Model = null, $has_many = [])
    {
        if (is_null($Model)) {
            $data = parent::prepare_custom_fields([]);
        } else {
            $data = parent::prepare_custom_fields($has_many);
        }
        $invoice = $this->read(null, $this->id);
        if (!$invoice['Invoice']['external_source']) {
            return $data; 
        }
     
        return $data;
    }

    public function getLastSelectedOrderSource()
    {
        return $this->find('first', [
            'fields' => ['order_source_id'],
            'conditions' => ['Invoice.order_source_id IS NOT NULL'],
            'order' => 'Invoice.id DESC',
        ]);
    }

    public function getClientName($invoice) {
        if (!empty($invoice['Invoice']['client_business_name'])) {
            return $invoice['Invoice']['client_business_name'];
        }
        return $invoice['Invoice']['client_first_name'] . ' ' . $invoice['Invoice']['client_last_name'];
    }

    public function getItemsCountForPos($invoice) {
        return array_reduce($invoice['InvoiceItem'], function ($carry, $item) {
            return $carry + $item['quantity'] / ($item['unit_factor'] ?? 1);
        }, 0);
    }

    public function getInvoicePaymentStatus($invoice) {
        $summary_paid = round($invoice['Invoice']['summary_paid'], getAllowedNumberOfDigitsAfterFraction($invoice['Invoice']['currency_code']));
        $summary_total = round($invoice['Invoice']['summary_total'], getAllowedNumberOfDigitsAfterFraction($invoice['Invoice']['currency_code']));

        if ($summary_total <= 0) {
            return INVOICE_STATUS_PAID;
        } elseif ($invoice['Invoice']['summary_paid'] == 0) {
            return INVOICE_STATUS_UNPAID;
        } else if ($summary_paid < $summary_total) {
            return INVOICE_STATUS_PARTIAL_PAID;
        } else if ($summary_paid > $summary_total) {
            return INVOICE_STATUS_OVERPID;
        } else {
            return INVOICE_STATUS_PAID;
        }
    }


    public function getInvoiceRefundStatusForPos($invoice) {
        // get refunded invoices
        $refund_invoice_ids = Set::extract('{n}.Invoice.id', $this->find('all', ['fields' => ['id'], 'recursive' => -1, 'conditions' => ['Invoice.subscription_id' => $invoice['Invoice']['id'], 'Invoice.type' => [self::Refund_Receipt, self::Credit_Note]]]));
        if (empty($refund_invoice_ids)) {
            return 0;
        }

       $refund_total = $this->get_refund_total($invoice['Invoice']['id'],'summary_total');
       $invoice_currency_code = $invoice['Invoice']['currency_code'];
       $invoice_summary_total = round($invoice['Invoice']['summary_total'], getAllowedNumberOfDigitsAfterFraction($invoice_currency_code));
       $refunds_summary_total = round($refund_total, getAllowedNumberOfDigitsAfterFraction($invoice_currency_code));
       if ($refunds_summary_total == $invoice_summary_total) {
           return INVOICE_STATUS_REFUNDED;
       } else if ($refunds_summary_total > 0) {
           return INVOICE_STATUS_PARTIAL_REFUND;
       } else {
           return 0;
       }
    }

    public function getInvoicePaymentsForPos($invoice) {
        $payments = [];
        $payment_after_deductions = [];
        $refund_payments = [];
        $refund_invoice_ids = Set::extract('{n}.Invoice.id', $this->find('all', ['fields' => ['id'], 'recursive' => -1, 'conditions' => ['Invoice.subscription_id' => $invoice['Invoice']['id'], 'Invoice.type' => [self::Refund_Receipt, self::Credit_Note]]]));
        $invoice_payments = Set::extract('{n}.InvoicePayment', $this->InvoicePayment->find('all', ['fields' => ['id', 'amount', 'payment_method', 'transaction_id'],'recursive' => -1, 'conditions' => ['InvoicePayment.invoice_id' => $invoice['Invoice']['id']]]));
        if (!empty($refund_invoice_ids)) {
            $refund_payments = Set::extract('{n}.InvoicePayment', $this->InvoicePayment->find('all', ['fields' => ['id', 'amount', 'payment_method'],'recursive' => -1, 'conditions' => ['InvoicePayment.invoice_id' => $refund_invoice_ids]]));
        }
        foreach ($invoice_payments as $payment) {
            if ($payment['payment_method'] == 'cash' && array_key_exists('cash', $payments)) {
                $payments['cash'][0]['amount'] += $payment['amount'];
            } else {
                $payments[$payment['payment_method']][] = $payment;
            }
        }
        foreach ($refund_payments as $payment) {
            if (array_key_exists($payment['payment_method'], $payments)) {
                $payments['cash'][0]['amount'] += $payment['amount'];
            } else {
                $payments[$payment['payment_method']][] = $payment;
            }
        }
        foreach ($payments as $payment_method => $payment) {
            $payment_after_deductions[] = [
                'id' => $payment[0]['id'],
                'amount' => $payment[0]['amount'],
                'payment_method' => $payment[0]['payment_method'],
                'transaction_id' => $payment[0]['transaction_id'],
            ];
        }
        return $payment_after_deductions ?? [];
    }

    public function getItemsAvailableForRefund($invoice) {
        $allowed_refund_invoice_types = implode(',' , [self::Refund_Receipt, self::Credit_Note]);
        $mainInvoiceId = $invoice['Invoice']['id'];
        $total_items = Set::extract('${n}[0].invoice_items', $this->query("SELECT item as `name`,unit_factor,unit_name,unit_small_name, product_id, unit_price, quantity FROM invoice_items WHERE invoice_id = $mainInvoiceId;", false));
        $total_refunds = Set::extract('${n}[0].invoice_items', $this->query("SELECT item as `name`,unit_factor,unit_name,unit_small_name, product_id, unit_price, quantity FROM invoice_items WHERE invoice_id in (SELECT id FROM invoices WHERE subscription_id = $mainInvoiceId and type in ($allowed_refund_invoice_types));", false));
        foreach ($total_refunds as $refund) {
            $total_items = $this->getTotalItemsAfterSubstractingRefund($total_items, $refund);
        }
        return $total_items;
    }

    public function getInvoiceRefundsForPos($invoice) {
        $refunds = $this->find('all', ['fields' => ['id', 'no', 'summary_total', 'summary_discount'], 'recursive' => -1, 'conditions' => ['Invoice.subscription_id' => $invoice['Invoice']['id'], 'Invoice.type' => [self::Refund_Receipt, self::Credit_Note]]]);
        $data = [];
        foreach ($refunds as $refund) {
            $data[] = [
                'id' => $refund['Invoice']['id'],
                'no' => $refund['Invoice']['no'],
                'summary_total' => $refund['Invoice']['summary_total'],
                'summary_discount' => $refund['Invoice']['summary_discount'],
            ];
        }
        return $data;
    }

    public function getOriginalInvoiceForPos($invoice) {
        $originalInvoice = [];
        if (!in_array($invoice['Invoice']['type'], [self::Refund_Receipt, self::Credit_Note])) {
            return null;
        }
        $invoice = $this->find('first', ['fields' => ['id', 'no'], 'recursive' => -1, 'conditions' => ['Invoice.id' => $invoice['Invoice']['subscription_id'], 'Invoice.type' => self::Invoice]]);
        $originalInvoice['id'] = $invoice['Invoice']['id'];
        $originalInvoice['no'] = $invoice['Invoice']['no'];
        return $originalInvoice;
    }


    /**
     * @param $total_items
     * @param $refund
     * @param $unit_price_matching
     * @return mixed
     */
    private function getTotalItemsAfterSubstractingRefund($total_items, $refund, $unit_price_matching = true) {
        foreach ($total_items as &$item) {
            if ($item['quantity'] == 0) continue;
            if ($item['product_id'] != $refund['product_id']) continue;
            if ($item['unit_price'] != $refund['unit_price'] && $unit_price_matching) continue;
            if ($item['quantity'] - $refund['quantity'] < 0) {
                $refund['quantity'] -= $item['quantity'];
                $item['quantity'] = 0;
                return $this->getTotalItemsAfterSubstractingRefund($total_items, $refund, false);
            } else {
                $item['quantity'] -= $refund['quantity'];
                return $total_items;
            }
        }
        return $this->getTotalItemsAfterSubstractingRefund($total_items, $refund, false);
    }

    public function getInvoiceItemsForPos($invoice) {
        $items = Set::extract('InvoiceItem', $invoice);
        return $items ?? [];
    }

    public function getInvoiceRealTotalAfterRefunds($invoice, $refunds) {
        $summary_total = $invoice['Invoice']['summary_total'] + $invoice['Invoice']['summary_discount'];
        foreach ($refunds as $refund) {
            $summary_total -= ($refund['summary_total'] + $refund['summary_discount']);
        }
        return $summary_total;
    }

    public function getInvoiceRemainingDiscount($invoice, $refunds) {
        $remaining_discount = $invoice['Invoice']['summary_discount'];
        foreach ($refunds as $refund) {
            $remaining_discount -= $refund['summary_discount'];
        }
        return $remaining_discount;
    }

    function getSentEstimates(){
        $EmailLog = GetObjectOrLoadModel('EmailLog');
        $result = $EmailLog->query("SELECT DISTINCT(invoice_id) from email_logs el
        LEFT JOIN invoices i on i.id = el.invoice_id AND i.`type` = '".Invoice::Estimate."' 
        where invoice_id is not null AND invoice_id <> '' AND i.id NOT IN(SELECT primary_id from action_lines where action_key = " . ACTION_CLIENT_VIEW_ESTIMATE . ")");
        $resultArray= [];
        foreach($result as $k => $v){
            $resultArray[] = $v['el']['invoice_id'];
        }
        return $resultArray;
    }

    function validateMultipleGateways($data)
    {
        /**
         * Handle validation for multiple invoice payments
         */

        /**
         * Don't validate in case is paid option is unchecked
         */
        if ($data['Payment']['is_paid'] == 0) {
            return true;
        }

        if (isset($data['MultiplePayment'])) {
            $sum = array_reduce($data['MultiplePayment'], function ($sum, $item) {
                $sum += (float) $item['amount'];
                return $sum;
            }, 0);
            if (settings::getValue(SalesPlugin, settings::ENABLE_ADVANCE_PAYMENT) && isset($data['AdvancePaymentInvoices'])) {
                $advanceSum = array_reduce($data['AdvancePaymentInvoices'], function ($sum, $item) {
                    $sum += (float) $item['amount'];
                    return $sum;
                }, 0);
                $sum += $advanceSum;
            }

            $code = getCurrentSite('currency_code');
            if (isset($data['Invoice']['currency_code'])) {
                $code = $data['Invoice']['currency_code'];
            }
            $round_number = Cache::read('number_formats')[$code][0] ?? 2;
            if (abs(round($data['Invoice']['summary_total'], $round_number) - round($sum, $round_number)) > 0.001) {
                $this->validationErrors['MultiplePayment'][0] = ['amount' => __("The total amount across all selected payment gateways should be equal to the total amount of the invoice", true)];
                return false;
            }
        }
        return true;
    }

    private function getProductName($itemId = null, $itemName = null)
    {
        if ($itemName) {
            return $itemName;
        }
        $Product = GetObjectOrLoadModel('Product');
        $product = $Product->find('first', ['conditions' => ['Product.id' => $itemId]]);
        return $product['Product']['name'];
    }

    private function prepareForAdd($invoice){
       
        if (ifPluginActive(WorkOrderPlugin)&&!empty($invoice['Invoice']['work_order_id']))
        {
            $this->loadModel( 'WorkOrder');
            $this->WorkOrder->recursive = -1 ; 
            $work_order = $this->WorkOrder->find ('first' , ['fields' => 'number,title' , 'conditions' => ['WorkOrder.id'=>$invoice['Invoice']['work_order_id'] ] ] ) ;
            $work_order_text = $work_order['WorkOrder']['title'].' #' .$work_order['WorkOrder']['number'];
            $invoice['Invoice']['work_order_text'] = $work_order_text;
        }


        if (!empty($invoice['InvoiceCustomField'])) {
            $noEstimate = array('%due-date%', '%due-days%', '%paid-amount%', '%unpaid-amount%', '%deposit%');
            foreach ($invoice['InvoiceCustomField'] as &$customField) {
                if ($invoice['Invoice']['type'] == 3 && in_array($customField['placeholder'], $noEstimate)) {
                    continue;
                }
                if (!empty($customField['placeholder'])) {
                    switch (low($customField['placeholder'])) {
                        case '%work_order_number%':
                            if (!empty($invoice['Invoice']['work_order_id'])) {
                                $customField['value'] = $work_order_text;
                             } else {
                                $customField['value'] = '';
                             }
                            break;
                    }
                }
            }
        }

        /**
         * To be used in invoice item validation
         */
        foreach($invoice['InvoiceItem'] as &$item) {
           $item['invoice_currency_code'] = $invoice['Invoice']['currency_code'];
        }
      
      
        return $invoice;
    }
    
    private function checkAgentHeader() {
        $headers = getallheaders();
        if (array_key_exists('X-App-Id', $headers)) {
            return in_array($headers['X-App-Id'],['com.izam.daftraPos','com.izam.enerpize.pos'])?true :false;
        }
        if (array_key_exists('X-App-Os', $headers)) {
            return in_array($headers['X-App-Os'], ['desktop']) ? true : false;
        }
        return false;
    }

    public function beforeSave($options = array()) {
        parent::beforeSave ($options );
        if (isset($this->data[$this->alias]['stored_due_date'])) {
            unset($this->data[$this->alias]['stored_due_date']);
        }
        $this->debug_ostar_invoices_updated_to_different_branch();//TO DO delete this method
        // Debugging issue https://izam.atlassian.net/browse/DAFTRA-85704 (empty invoices being saved bypassing all validations)
        if ($this->debug_empty_invoices()){
            return false;
        }
        return true;
    }

    function debug_empty_invoices() {
        $isInvoiceIdValid = !empty($this->data[$this->alias]['id']) && is_numeric($this->data[$this->alias]['id']) && $this->data[$this->alias]['id'] > 0;
        if (getCurrentSite('id') == '3738217' && !$isInvoiceIdValid && empty($this->data[$this->alias]['client_id'])) {
            $data = [
                'is_cli' => php_sapi_name() == 'cli',
                'site_id' => getCurrentSite('id'),
                'subdomain' => getCurrentSite('subdomain'),
                'data' => $this->data,
                'debug_back_trace' => debug_backtrace(),
            ];
            \Rollbar\Rollbar::log(Level::INFO, "invoice add request using empty data (client_id)", ['debug_data' => $data]);
            return true;
        }
        return false;
    }


    function debug_ostar_invoices_updated_to_different_branch()
    { //TO DO delete this method
        try {
            if (getCurrentSite('id') == 2697741) {//https://ostar.daftara.com/

                if (!empty($this->data[$this->alias]['id']) && !empty($this->data[$this->alias]['branch_id'])) { //in edit data
                    $id = $this->data[$this->alias]['id'];
                    $create_action_line = $this->query("SELECT * FROM `action_lines` WHERE `action_key` = 1 AND `primary_id` = {$id} limit 1");

                    if (isset($create_action_line[0]['action_lines']['branch_id']) && $this->data[$this->alias]['branch_id'] != $create_action_line[0]['action_lines']['branch_id']) {
                        $new_branch_id = $this->data[$this->alias]['branch_id'];
                        unset($this->data[$this->alias]['branch_id']);

                        \Rollbar\Rollbar::error('error ostar invoices updated to diffrent branch', [
                            'invoice_id' => $id,
                            'previous_branch_id' => $create_action_line[0]['action_lines']['branch_id'],
                            'new_branch_id' => $new_branch_id ?? 'none',
                            'site_in_branch_current_branch_id' => getCurrentBranchID(),
                            'debug_backtrace' => debug_backtrace(),
                        ]);
                    }
                }
            }
        } catch (\Throwable $th) {
            debug('debug_ostar_invoices_updated_to_diffrent_branch');
            debug($th);
        }
    }

    public function updateEstimate($invoice) {
        // revert the estimate to accepted if all related invoices are deleted
        $estimate_id = $invoice['estimate_id'];
        if (!$estimate_id) {
            return;
        }
        $related_invoices = $this->find('all', ['conditions' => ['Invoice.estimate_id' => $estimate_id, 'Invoice.type' => Invoice::Invoice]]);
        if (count($related_invoices) == 0) {
            $this->updateAll(['Invoice.payment_status' => ESTIMATE_STATUS_ACCEPTED], ['Invoice.id' => $estimate_id]);
        }
    }
    public function hasProductionPlan($invoiceId, $sourceType = ProductionPlanSourceTypeUtil::INVOICE)
    {
        $this->loadModel('ProductionPlan');
        $productionPlan = $this->ProductionPlan->find('first', ['conditions' => [
            'ProductionPlan.source_id' => $invoiceId,
            'ProductionPlan.source_type' => $sourceType
        ]]);
        return $productionPlan;
    }

    public function hasRelatedInvoice($invoiceId)
    {
        $this->loadModel('RelatedInvoice');
        $relatedInvoice = $this->RelatedInvoice->find('first', ['conditions' => [
            'RelatedInvoice.related_invoice_id' => $invoiceId,
        ]]);
        return $relatedInvoice;
    }

    public static function checkIfJordanEInvoiceEnabled()
    {
        $jordanEInvoicePlugin= ifPluginActive(PluginUtil::JORDAN_EINVOICE_PLUGIN);
        $jordanEInvoiceClientId = settings::getValue(PluginUtil::JORDAN_EINVOICE_PLUGIN, 'jordanian_einvoice_client_id');
        $jordanEInvoiceClientSecret = settings::getValue(PluginUtil::JORDAN_EINVOICE_PLUGIN, 'jordanian_einvoice_client_secret');
        $sourceSequence = settings::getValue(PluginUtil::JORDAN_EINVOICE_PLUGIN, 'jordanian_einvoice_income_source_sequence');
        $invoiceType = settings::getValue(PluginUtil::JORDAN_EINVOICE_PLUGIN, 'jordanian_einvoice_user_invoice_type');
        return (!empty($jordanEInvoiceClientId) && !empty($jordanEInvoiceClientSecret)&& !empty($sourceSequence)&& !empty($invoiceType) && $jordanEInvoicePlugin);
    }

    public static function get_jordan_invoice_qr_data($invoice)
    {
        if (empty($invoice["EntityAppData"][0])) return '';
        if ($invoice["EntityAppData"][0]['id'] == null) return '';
        $data = json_decode($invoice["EntityAppData"][0]['data'] ?? "" , true);
        if (empty($data) || !isset($data['response']['EINV_QR'])) return '';
        $result = \Endroid\QrCode\Builder\Builder::create()
            ->writer(new \Endroid\QrCode\Writer\PngWriter())
            ->data($data['response']['EINV_QR'])
            ->size(130)
            ->build();
        return  base64_encode($result->getString());
    }

    private function prepareInvoiceSalesPerson($invoice)
    {
        $sales_persons = [$invoice['Invoice']['sales_person_id']];
        foreach ($invoice['InvoiceItem'] as &$item) {
            if ($invoice['Invoice']['sales_person_id'] == self::MULTIPLE_SALES_PERSONS) {
                $item['sales_person_id'] = $item['sales_person_id'] ?? getAuthStaff('id');
            } elseif ($invoice['Invoice']['sales_person_id'] != self::MULTIPLE_SALES_PERSONS && $this->isInvoiceSourcePos($invoice)) {
                if (empty($item['sales_person_id'])) {
                    $item['sales_person_id'] = $invoice['Invoice']['sales_person_id'];
                }
            } else {
                $item['sales_person_id'] = null;
            }
            $sales_persons[] = $item['sales_person_id'];
        }
        $sales_persons = array_filter(array_unique($sales_persons));
        if (count($sales_persons) > 1) {
            $invoice['Invoice']['sales_person_id'] = self::MULTIPLE_SALES_PERSONS;
        } else if (isset($sales_persons[0])) {
            $invoice['Invoice']['sales_person_id'] = $sales_persons[0];
        }
        return $invoice;
    }
}
