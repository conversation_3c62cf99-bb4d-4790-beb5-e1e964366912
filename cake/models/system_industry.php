<?php

use Izam\Daftra\Portal\Services\SitePluginService;
use Izam\Daftra\Portal\Services\SiteService;

class SystemIndustry extends AppModel {

    var $name = 'SystemIndustry';
    var $filters = array();
    var $useDbConfig = 'portal';
    var $actsAs = array(
        'image' => array('image' => array('width' => 0, 'height' => 0, 'aspect_required' => false,'extensions' => array('jpg', 'png', 'gif','svg'), 'folder' => 'industry_img/'))
    );
    function __construct($id = false, $table = null, $ds = null) {
        parent::__construct($id, $table, $ds);
        $this->validate = array();
    }

    function getFilters() {
        return array(); //set filters here
    }

    function InstallActions($action_type, $text) {
        $actions = explode("\n", $text);
     //   print_r($actions);
        $this->loadModel('FollowUpAction');
        foreach ($actions as $action) {
             $data=array();
            $data['FollowUpAction']['name'] = $action;
            $data['FollowUpAction']['item_type'] = $action_type;
            $data['FollowUpAction']['staff_id'] = 0;
            $this->FollowUpAction->create();
            $row = $this->FollowUpAction->save($data);
            unset($data);
        }
    }

    function update_stats()
    {

        $query='
        
        UPDATE  system_industries
        set current_uses_count= (select count(*) from '.Portal_Site_DB.'.sites as s1 WHERE s1.system_industry_id = system_industries.id),
        last_month_uses_count=(select count(*) from '.Portal_Site_DB.'.sites as s2 WHERE s2.system_industry_id = system_industries.id AND s2.created > DATE_SUB(NOW(), INTERVAL 1 MONTH) )
        ';

        $this->query($query);
    }

    function InstallStatuses($action_type, $text) {
        $actions = explode("\n", $text);
      //  print_r($actions);
        $this->loadModel('FollowUpStatus');
        foreach ($actions as $action) {
            $data=array();
            $data['FollowUpStatus']['name'] = $action;
            $data['FollowUpStatus']['item_type'] = $action_type;
            $data['FollowUpStatus']['staff_id'] = 0;
            $this->FollowUpStatus->create();
            $row = $this->FollowUpStatus->save($data);
            unset($data);
        }
    }

    function InstallProducts($ids) {
        $ids = explode(",", $ids);
        $this->loadModel('Product');
        $ProductModel = ClassRegistry::init(array('class' => 'DummyProduct', 'alias' => 'Product', 'type' => 'Model', 'table' => 'products', 'ds' => 'template'));
        $ProductsToCopy = $ProductModel->find('all', array('conditions' => array('Product.id' => $ids)));
        foreach ($ProductsToCopy as $Product) {
            unset($Product['Product']['id']);
            unset($Product['Product']['staff_id']);
            unset($Product['Product']['site_id']);
            unset($Product['Product']['created']);
            unset($Product['Product']['modified']);
            $this->Product->create();
            $row = $this->Product->save($Product);
            // print_r($row);
        }
    }

    function InstallSitePlugin($ids) {
        $ids = explode(",", $ids);
        $this->loadModel('SitePlugin');
        $this->loadModel('Plugin');
        foreach ($ids as $id) {
        $prow=$this->Plugin->read(null,$id);
      //  echo "<pre>";
       // print_r($prow['Plugin']['is_external']);
        
            $data=array();
            $data['SitePlugin']['plugin_id'] = $id;
            $data['SitePlugin']['site_id'] = getCurrentSite('id');
            $data['SitePlugin']['active'] = 1;
            $data['SitePlugin']['installed'] = 1;
            $row = SitePluginService::create($data['SitePlugin']);
            if($row){
           if($prow['Plugin']['is_external']=="1"){
               $db=  json_decode(getCurrentSite('db_config'),true);
             //  print_r($prow['Plugin']);
               $config=$db;
               
               shell_exec("mysql -u{$config['login']} -p{$config['password']} -h{$config['host']} {$config['database']} < " . APP.'plugins'.DS.strtolower($prow['Plugin']['name']).DS . strtolower($prow['Plugin']['name']).'.sql');
           }     
            }
        }
    }

    function InstallRoles($ids) {
        $ids = explode(",", $ids);
        $this->loadModel('Role');
        $this->loadModel('RolesPermission');
        $RoleModel = ClassRegistry::init(array('class' => 'DummyRole', 'alias' => 'Role', 'type' => 'Model', 'table' => 'roles', 'ds' => 'template'));
        $PermissionModel = ClassRegistry::init(array('class' => 'DummyRolesPermission', 'alias' => 'RolesPermission', 'type' => 'Model', 'table' => 'roles_permissions', 'ds' => 'template'));
        $Permissions = $PermissionModel->find('all');

        $RolesToCopy = $RoleModel->find('all');

        foreach ($RolesToCopy as $Role) {
            $old_id = $Role['Role']['id'];
            unset($Role['Role']['id']);
            $this->Role->create();
            $row = $this->Role->save($Role);
            $id = $this->Role->id;
            foreach ($Permissions as $Permission) {
                if ($Permission['RolesPermission']['role_id'] == $old_id) {
                    unset($Permission['RolesPermission']['id']);
                    $Permission['RolesPermission']['role_id'] = $id;
                    $this->RolesPermission->create();
                    $this->RolesPermission->save($Permission);
                }
            }
            //  print_r($row);
        }
    }

    function InstallCustomForm($id) {
        $this->loadModel('CustomForm');   
        $this->loadModel('CustomFormField');   
        $CustomFormModel = ClassRegistry::init(array('class' => 'DummyCustomForm', 'alias' => 'CustomForm', 'type' => 'Model', 'table' => 'custom_forms', 'ds' => 'template'));
        $CustomFormFieldModel = ClassRegistry::init(array('class' => 'DummyCustomFormField', 'alias' => 'CustomFormField', 'type' => 'Model', 'table' => 'custom_form_fields', 'ds' => 'template'));
                
        $CustomFormToCopy = $CustomFormModel->find('first', array('conditions' => array('CustomForm.id' => $id)));
        $CustomFormFieldToCopy = $CustomFormFieldModel->find('all', array('conditions' => array('CustomFormField.custom_form_id' => $CustomFormToCopy['CustomForm']['id'])));
        if(!$CustomFormFieldToCopy){
            return false;
        }
        unset($CustomFormToCopy['CustomForm']['id']);
        $CustomFormToCopy['CustomForm']['client_id']=  getCurrentSite('id');
        
        
        $this->CustomForm->create();
        
        $row=$this->CustomForm->save($CustomFormToCopy);
        $NewCustomFormID=$this->CustomForm->id;
        foreach($CustomFormFieldToCopy as &$cff){
        $cff['CustomFormField']['custom_form_id']=$NewCustomFormID;    
        //$cff['CustomFormField']['id']=null;
        }
        $this->CustomFormField->create();
        $this->CustomFormField->saveAll($CustomFormFieldToCopy);
        
        $row = $CustomFormToCopy['CustomForm'];
        
        if ($row['table_name'] == "clients") {
        $table="clients_custom_data" ;   
        }else if($row['table_name'] == "products") {
        $table="products_custom_data" ;      
        }else if($row['table_name'] == "work_orders") {
        $table="work_orders_custom_data" ;      
        }
        if($table!=""){
        
        $TableToInsert = $CustomFormFieldModel->query("SHOW CREATE TABLE `" . Template_DB . "`.`$table`");
        $this->CustomForm->query($TableToInsert[0][0]['Create Table']);
       // print_r($TableToInsert[0][0]['Create Table']);
       // echo "\n\n";
        }
    }
    
function CloneTemplate($id){
        $this->add_stats(STATUS_SELECT_INDUSTRY,[]);
        App::import('Vendor', 'settings');
        $this->loadModel('SystemIndustry');
            $this->loadModel('Industry');
            $this->loadModel('Site');
            $this->loadModel('SitePlugin');
            $this->loadModel('Invoice');
            $this->loadModel('Plugin');
            
           // $row = $this->Industry->read(null, $id);
            $siterow=$this->Site->find(array('Site.id'=>  getCurrentSite('id')));
            $site=$siterow['Site'];
            $si = $this->SystemIndustry->find('first', array('conditions' => array('SystemIndustry.id' => $id)));
            $sitetoclone=$this->Site->find(array('Site.id'=>$si['SystemIndustry']['subdomain']));
            if(!$sitetoclone){
                return;
            }
    // debug template database config not available
    $sitetoclone_config = json_decode($sitetoclone['Site']['db_config'], true);
    $sql = "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '" . $sitetoclone_config['database'] . "'";
    exec("mysql -A --init-command=\"SET SESSION FOREIGN_KEY_CHECKS=0;\" -u{$sitetoclone_config['login']} -p{$sitetoclone_config['password']} -h{$sitetoclone_config['host']} --default-character-set=utf8 {$sitetoclone_config['database']} -e \"{$sql}\" ", $output);
    if (empty($output)) {
        \Rollbar\Rollbar::error('Clone template: site template database not found', $sitetoclone_config);
        return;
    }
    // end debug template database config not available

             $this->Site->id=$site['id'];
//            $this->Site->saveField('version', 2, false);
            SiteService::updateById($site['id'], [
                'system_industry_id' => $si['SystemIndustry']['id'],
                'last_sql_update_id' => $sitetoclone['Site']['last_sql_update_id'],
                'shop_front_template_id' => $sitetoclone['Site']['shop_front_template_id'],
            ]);

            
            // Clone Template Site Plugin To New Site
            SitePluginService::deleteWhere(['id' => getCurrentSite('id')]);
            $plist=$this->SitePlugin->find('all',array('conditions' => array('SitePlugin.site_id' => $sitetoclone['Site']['id'])));
            foreach($plist as $p){
            unset($p['SitePlugin']['id']);
            $p['SitePlugin']['site_id']=$site['id'];
            SitePluginService::create($p['SitePlugin']);
            }
            //Clone Tempalte Website Database to new database
            $site_config=  json_decode($site['db_config'],true);


                $t="system_sql_".time();
                $t_file=TMP.DS.$t;
                $t_error=TMP.DS."error_system_sql_".time();
                $try=0;
                while($try<5){
                    @touch($t_error);
                    $cmd = "mysqldump --column-statistics=0 --quick --single-transaction --no-tablespaces -u{$sitetoclone_config['login']} -p{$sitetoclone_config['password']} -h{$sitetoclone_config['host']} --default-character-set=utf8 {$sitetoclone_config['database']} 2>{$t_error} >{$t_file}";
                    exec($cmd, $output, $status);
                    if ($status != 0) {
                        \Rollbar\Rollbar::log(\Rollbar\Payload\Level::CRITICAL, new \Exception("System Industry Extract Error In Line ".__LINE__." \n\r $output \n\r".@file_get_contents($t_error)));
                        notify_admin_fetal_error("System Industry Extract Error In Line ".__LINE__." \n\r ".print_r($output,true).@file_get_contents($t_error), "System Industry clone Error");

                        $headers = "MIME-Version: 1.0" . "\r\nContent-type:text/html;charset=UTF-8\r\nFrom: <<EMAIL>>\r\n";
                        $debug_cmd=str_replace($sitetoclone_config['password'],"",$cmd);
                        mail('<EMAIL>', 'DF: FETAL ERROR ', "System Industry Extract Error In Line ".__LINE__." $t_file <br> $debug_cmd  \n\r ".print_r($output,true).@file_get_contents($t_error), $headers);

                    }else{
                        break;
                    }
                    $try++;
                }
                $try=0;

    while ($try < 5) {
        $this->Invoice->query("drop database ".$site_config['database'],true);
        $this->Invoice->query("create database ".$site_config['database'],true);
        exec($cmd="mysql -A --init-command=\"SET SESSION FOREIGN_KEY_CHECKS=0;\" -u{$site_config['login']} -p{$site_config['password']} -h{$site_config['host']} --default-character-set=utf8 {$site_config['database']} 2>{$t_error} <{$t_file}", $output, $status);
        if ($status != 0) {
            \Rollbar\Rollbar::log(\Rollbar\Payload\Level::CRITICAL, new \Exception("System Industry Restore  Error In Line ".__LINE__." \n\r ".print_r($output,true).@file_get_contents($t_error)));
//            notify_admin_fetal_error("System Industry Restore  Error In Line ".__LINE__." \n\r ".print_r($output,true), "System Industry clone Error");
            $headers = "MIME-Version: 1.0" . "\r\nContent-type:text/html;charset=UTF-8\r\nFrom: <<EMAIL>>\r\n";
            $debug_cmd=str_replace($sitetoclone_config['password'],"",$cmd);
            mail('<EMAIL>', 'System Industry Restore Error ', "System Industry Restore Error In Line ".__LINE__." $t_file <br> $debug_cmd \n\r ".print_r($output,true).@file_get_contents($t_error), $headers);


        } else {
            break;
        }
        $try++;
    }
//                $ret=shell_exec("mysql -A --init-command=\"SET SESSION FOREIGN_KEY_CHECKS=0;\" -u{$site_config['login']} -p{$site_config['password']} -h{$site_config['host']} --default-character-set=utf8 {$site_config['database']}</tmp/{$t}");
            //unlink($t_file);
            $source_folder=dirname(dirname(__FILE__)).DS.'webroot'.DS.'files'.DS.hashSite($si['SystemIndustry']['subdomain']).DS;
            $dst_folder=dirname(dirname(__FILE__)).DS.'webroot'.DS.'files'.DS.hashSite(getCurrentSite('id')).DS;
            IF(is_readable($source_folder)){
             //   recurse_copy($source_folder,$dst_folder);
            }

//            file_put_contents(APP.'tmp'.DS.'clone_template_database_log', $ret,FILE_APPEND);
            $this->Invoice->query("use ".$site_config['database']);
            // Delete Template Action lines
            $this->Invoice->query("TRUNCATE `action_lines`;");
            $this->Invoice->query("TRUNCATE `activity_log`;");
            $this->Invoice->query("TRUNCATE `activity_log_relation`;");
            $this->Invoice->query("TRUNCATE `journal_logs`;");
            $this->Invoice->query("delete from `settings` where `key`='journals_local_currency_code';");
	    // Delete Template Taxes and install Country Tax
            $this->Invoice->query("TRUNCATE `taxes`;");
            $this->Site->install_tax($site);            
             $this->Site->reload_session();
            Cache::delete('current_plugin_' . getCurrentSite('id'));
            
            Cache::delete(getCurrentSite('id'));
            Cache::delete(getCurrentSite('id').'_0');
            $allp=$this->Plugin->find('list');
            foreach($allp as $key=>$pp){
                unset($pp);
             Cache::delete(getCurrentSite('id').'_'.$key);
            }
            getCurrentPlugin();
            delete_menus();

            if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::WebsiteFrontPlugin)) {
                $this->Plugin->plugin_callback_79();
            }

            Cache::delete("cake_model_default_".$site_config['database']."_list");
            foreach ($_SESSION['cache_keys'] as $key => $value) {
                Cache::delete($key, $value['config']);
            }
            if ($_GET['cache_debug'] == "2") {
                echo "<pre>";
                print_r($_SESSION['cache_keys']);
                die();
            }
            $tempFileName = APP."tmp".DS."cache".DS."models".DS."cake_model_default_".$site_config['database']."_list";
            unlink($tempFileName);
}

}

?>
