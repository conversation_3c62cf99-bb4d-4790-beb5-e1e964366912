<?php

use Rollbar\Rollbar;

class JournalTransaction extends AppModel {
    var $name = 'JournalTransaction';
    
    var $belongsTo = array(
		'Journal' => array('className' => 'Journal', 'foreignKey' => 'journal_id'),
		'JournalAccount' => array('className' => 'JournalAccount', 'foreignKey' => 'journal_account_id')
	);

    function beforeSave($options = [])
    {
        if(empty($this->data['JournalTransaction']['journal_account_id']))
        {
            $journalAccount = false;
            if(
                $this->data['JournalTransaction']['currency_credit'] > $this->data['JournalTransaction']['currency_debit'] ||
                $this->data['JournalTransaction']['credit'] > $this->data['JournalTransaction']['debit']
            ){
                $journalAccount = $this->Journal->get_auto_account(['entity_type' => 'other_credit', 'entity_id' => 0]);
            }else{
                $journalAccount = $this->Journal->get_auto_account(['entity_type' => 'other_debit', 'entity_id' => 0]);
            }
            if($journalAccount){
                $this->data['JournalTransaction']['journal_account_id'] = $journalAccount['JournalAccount']['id'];
                }else{
                notify_admin_fetal_error('journal_transaction without journal account'. print_r($this->data,true));
            }
        }
        return true;
    }

    function afterSave($created = false) {
        if(!$created) {
            $record = $this->findById($this->data['JournalTransaction']['id']);
            if($record['JournalTransaction']['bank_transaction_id']) {
                $this->loadModel('BankTransaction');
                $this->BankTransaction->unmatch($record['JournalTransaction']['bank_transaction_id']);
            }
        }
        return parent::afterSave($created);
    }

    function beforeDelete($caseCade = true)
    {
        $record  = $this->findById($this->id);
        if($record['JournalTransaction']['bank_transaction_id']) {
            $this->loadModel('BankTransaction');
            $this->BankTransaction->unmatch($this->data['JournalTransaction']['bank_transaction_id']);
        }
        return parent::beforeDelete($caseCade); // TODO: Change the autogenerated stub
    }
}