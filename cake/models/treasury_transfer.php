<?php

class TreasuryTransfer extends AppModel {


    var $applyBranch = ['onFind' => true, 'onSave' => true];
    var $name = 'TreasuryTransfer';
    var $filters = array();
//    var $belongsTo = [];
//    var $belongsTo = array(
//        'ToTreasury' => array('className' => 'Treasury', 'foreignKey' => 'from_treasury_id', 'order' => 'ToTreasury.id asc', 'dependant' => false),
//         'FromTreasury' => array('className' => 'Treasury', 'foreignKey' => 'to_treasury_id', 'order' => 'FromTreasury.id asc', 'dependant' => false),
//    );
    var $hasOne = [] ;  
    var $validate = [];
    
     var $actsAs = array(
        'journal' => array(
            'is_journal'=>true,
            //if no currency change, it will use first one, otherwise it will use the second and third one
            'entity_type'=>array('treasury_transfer','treasury_transfer_from','treasury_transfer_to')
        ),
    );

     
    function __construct($id = false, $table = null, $ds = null) {
        parent::__construct($id, $table, $ds);
    }
    
    public function get_journals($data)
    {
        
        if(empty($data[$this->alias]['id'])) $data[$this->alias]['id']= $this->id;
        
        if(!isset($data[$this->alias]['id'])||empty($data[$this->alias]['from_treasury_id'])||empty($data[$this->alias]['to_treasury_id']))
            return false;
        
        if(!isset($data[$this->alias]['currency_rate'])||!isset($data[$this->alias]['balance'])) return false;

        $description=__('Money Transfer',true).' #'.$data[$this->alias]['id'];
        
        $journal[0]['Journal']['entity_type']='treasury_transfer';
        $journal[1]['Journal']['entity_type']='treasury_transfer_from';
        $journal[2]['Journal']['entity_type']='treasury_transfer_to';
        
        $journal[0]['Journal']['entity_id']=$journal[1]['Journal']['entity_id']=$journal[2]['Journal']['entity_id']=$data[$this->alias]['id'];
        $journal[0]['Journal']['date']=$journal[1]['Journal']['date']=$journal[2]['Journal']['date']=$data[$this->alias]['transfer_date'];
        $journal[0]['Journal']['description']=$journal[1]['Journal']['description']=$journal[2]['Journal']['description']=$description."\r\n".$data[$this->alias]['notes'];

        if($data[$this->alias]['from_currency_code']==$data[$this->alias]['to_currency_code'])
        {
            $journal[0]['Journal']['currency_code']=$data[$this->alias]['from_currency_code'];
            $journal[0]['JournalTransaction'][]=
            array(
                    'subkey'=>'treasury_from', 
                    'currency_credit'=>$data[$this->alias]['balance'],
                    'currency_code'=>$data[$this->alias]['from_currency_code'],
                    'description'=>$description." ".$data[$this->alias]['notes'],
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'treasury','entity_id'=>$data[$this->alias]['from_treasury_id'])
                );  
            $journal[0]['JournalTransaction'][]=
            array(
                    'subkey'=>'treasury_to', 
                    'currency_debit'=>$data[$this->alias]['balance'],
                    'currency_code'=>$data[$this->alias]['from_currency_code'],
                    'description'=>$description." ".$data[$this->alias]['notes'],
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'treasury','entity_id'=>$data[$this->alias]['to_treasury_id'])
                ); 
        } else
        {
            $journal[1]['Journal']['currency_code']=$data[$this->alias]['from_currency_code'];
            $journal[1]['Journal']['manual_currency_rate']=$data[$this->alias]['currency_rate'];
            $journal[1]['JournalTransaction'][]=
            array(
                    'subkey'=>'treasury_from', 
                    'currency_credit'=>$data[$this->alias]['balance'],
                    'currency_code'=>$data[$this->alias]['from_currency_code'],
                    'description'=>$description." ".$data[$this->alias]['notes'],
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'treasury','entity_id'=>$data[$this->alias]['from_treasury_id'])
                );  
            $journal[1]['JournalTransaction'][]=
            array(
                    'subkey'=>'currency_exchange', 
                    'currency_debit'=>$data[$this->alias]['balance'],
                    'currency_code'=>$data[$this->alias]['from_currency_code'],
                    'description'=>$description." ".$data[$this->alias]['notes'],
                    'auto_account'=>array('type'=>'fixed' , 'entity_type'=> 'currency_exchange','entity_id'=>0)
                ); 
            $journal[2]['Journal']['currency_code']=$data[$this->alias]['to_currency_code'];
            $journal[2]['JournalTransaction'][]=
            array(
                    'subkey'=>'currency_exchange', 
                    'currency_credit'=>$data[$this->alias]['balance']*$data[$this->alias]['currency_rate'],
                    'currency_code'=>$data[$this->alias]['to_currency_code'],
                    'description'=>$description." ".$data[$this->alias]['notes'],
                    'auto_account'=>array('type'=>'fixed' , 'entity_type'=> 'currency_exchange','entity_id'=>0)
                );  
            $journal[2]['JournalTransaction'][]=
            array(
                    'subkey'=>'treasury_to', 
                    'currency_debit'=>$data[$this->alias]['balance']*$data[$this->alias]['currency_rate'],
                    'currency_code'=>$data[$this->alias]['to_currency_code'],
                    'description'=>$description." ".$data[$this->alias]['notes'],
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'treasury','entity_id'=>$data[$this->alias]['to_treasury_id'])
                ); 
        }
        
        return $journal;


    }

    function getFilters()
    {
        return ['date' => array('more-options' => true,'label' =>  __('Date',true), 'type' => 'date_range', 'to_label' => __('Date To', true), 'from_label' => __('Date From', true))];
    }
    
}

