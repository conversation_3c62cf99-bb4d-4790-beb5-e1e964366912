<?php
//0.11636363636
use App\Utils\TrackStockUtil;
use Izam\Daftra\ActivityLog\DetailsEntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Attachment\Service\AttachmentsService;
use Izam\ManufacturingOrder\Services\BomService;
use Izam\ScoreCalculator\Services\SiteScoreCalculator;

if (!defined('VALID_PRICE')) {
    define('VALID_PRICE', '/^[0-9]{0,7}(\.?[0-9]{0,})?$/');
}

class Product extends AppModel {
    var $applyBranch = ['onFind' => true, 'onSave' => true];
    const PRODUCT_TYPE = 1;
    const SERVICE_TYPE = 2;
    const BUNDLE_TYPE = 3;

    // list source types will invoice related to
    const PACKAGE_SOURCE = 'package';

    var $name = 'Product';
    var $filters = array();
    var $import = array(
//		'id' => array('unique_list'=>true,'is_unique' => true, 'required' => false, 'title' => 'ID', 'match' => array('id')),
        'name' => array('unique_list' => true, 'required' => true, 'title' => 'Name', 'match' => array('Name','name', 'nome')),
        'description' => array('title' => 'Description', 'match' => array('description', 'desc')),
        'product_code' => array('unique_list'=>true,'is_unique' => true, 'required' => false, 'title' => 'Product Code', 'match' => array('productcode', 'product_code', 'sku', 'product code')),
        'barcode' => array('unique_list'=>true,'is_unique' => true, 'required' => false, 'title' => 'Barcode', 'match' => array('code', 'barcode', 'qrcode')),
        'type' => array('format' => 'trim', 'title' => 'Type', 'match' => array('product_type', 'type')),
        'unit_price' => array('format' => 'float', 'title' => 'Retail Price', 'match' => array('unitprice', 'price', 'unit_price', 'reatailprice', 'reatail_price')),
        'buy_price' => array('format' => 'float', 'title' => 'Buy Price', 'match' => array('buyprice', 'price', 'buy_price')),
        'minimum_price' => array('format' => 'float', 'title' => 'Minimum Price', 'match' => array('minimum_price', 'price')),
        'tax1' => array('format' => 'trim', 'title' => 'Tax1', 'match' => array('tax1', 'tax_one')),
        'tax2' => array('format' => 'trim', 'title' => 'Tax2', 'match' => array('tax2', 'tax_two')),
//        'default_quantity' => array('title' => 'Default Quantity', 'match' => array('defaultquantity', 'default_quantity', 'quantity', 'total')),
        'category' => array('format' => 'trim', 'title' => 'Category', 'match' => array('category', 'cat')),
        'brand' => array('format' => 'trim', 'title' => 'Brand', 'match' => array('brand')),
        'store_id' => array('title' => 'Warehouse', 'match' => array('warehouse', 'store')),
        'status' => array('title' => 'Status', 'match' => array('status'))
    );

    var $belongsTo = array('Supplier',
        'UnitTemplate' => array('className' => 'UnitTemplate', 'foreignKey' => 'unit_template_id'),
        'ItemGroup'
    );
	 var $actsAs = array(
		'tag' => array(

                 ),
				 'customform' => [
					 'custom_model_name' => 'ProductsCustomData',
					 'custom_data_table_name' => 'products_custom_data'
				 ]

    );

	 const DISCOUNT_TYPE_PERCENTAGE = 1;
	 const DISCOUNT_TYPE_VALUE = 2;

    public $hasOne = [
		'ProductMasterImage' => ['conditions'=>array('ProductMasterImage.default'=>1),'className'=>'ProductImage'],
	//	'ProductBalance' => ['conditions'=>array(),'className'=>'ProductBalance']
	];

    public $hasMany = array(
//        'ProductMasterImage'=>array(
//            'conditions'=>array('ProductMasterImage.default'=>1),'className'=>'ProductImage','order'=>'ProductMasterImage.default desc'
//        ),
        'ProductImage'=>array('order'=>'ProductImage.default desc')
        ,'ProductPrice'
        ,'ProductAttributeOption');

//    public $hasAndBelongsToMany = array(
//        'Category' => array('className' => 'ItemsCategory', 'joinTable' => 'items_categories', 'foreignKey' => 'item_id', 'associationForeignKey' => 'category_id',
//            'unique' => true)
//    );

    function getDiscountTypes ( ) {
        return [
            Product::DISCOUNT_TYPE_PERCENTAGE => __("%" , true),
            Product::DISCOUNT_TYPE_VALUE => __("$" , true),
        ];
    }

    public function getTrackingTypes(){
        return \App\Utils\TrackStockUtil::getTrackingTypes();
    }

    function get_bundle_final_cost($product_id)
    {
        $this->loadModel('ProductBundle') ;
        $bundle_products  = $this->ProductBundle->find('all' , ['recursive' => -1 ,  'conditions' =>['bundle_product_id' =>$product_id ]  ]) ;
        $bp = [] ;
        foreach ( $bundle_products as $b){
            $bp['ProductBundle'][] = $b['ProductBundle'] ;
        }
        return  $this->get_bundle_cost($bp);


    }

    function getProductCategories($product_id) {
	    $this->loadModel('ItemsCategory');
	    $this->bindModel(
		    ['hasAndBelongsToMany' =>
			    [ 'Category' =>
				    [
					    'className' => 'Category',
					    'joinTable' => 'items_categories',
					    'foreignKey' => 'item_id',
					    'associationForeignKey' => 'category_id',
					    'conditions' => [ 'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT, 'category_type' => Category::CATEGORY_TYPE_PRODUCT]
				    ]
			    ]
		    ]
	    );
	    return Set::extract('Category.{n}.name', $this->find('first', ['conditions' => ['Product.id' => $product_id]]));
    }

	function getSortFields($sorted_by = null) {

		return array(
            array('title' => __('Name',true), 'field' => 'name'),
			array('title' => __('Created Date',true),'field' => 'created', 'default' => 'desc'),
		//	array('title' => __('Stock Balance',true),'field' => 'ProductBalance.balance'),
			array('title' => __('Code',true),'field' => 'product_code'),
			'default_order' => $sorted_by

		);

    }

    function __construct($id = false, $table = null, $ds = null) {
        parent::__construct($id, $table, $ds);
        $this->import['product_code']['is_unique'] = json_decode(settings::getValue(AutoNumberPlugin, "18-0"), true)['require_unique'];
        $this->validate = array(
            'name' => array('rule' => 'notempty', 'message' => __('Required', true)),
            'description' => array('rule' => array('maxlength', 100000), 'message' => __('Only 100000 characters allowed', true)),
            'unit_price' => array('rule' => VALID_PRICE, 'message' => __('You must enter a correct number without any letters or symbols', true), 'allowEmpty' => true),
            'default_quantity' => array('rule' => 'numeric', 'message' => __('You must enter a correct number without any letters or symbols', true), 'allowEmpty' => true),
            'default_tax' => array('rule' => 'valid_tax', 'message' => __('Invalid Tax', true), 'allowEmpty' => true),
            'tax1' => array('rule' => 'same_tax', 'message' => __('You cannot select the same tax in Tax 1 and Tax 2 Inputs', true), 'allowEmpty' => true),
//            'product_code' => [
//                'notEmpty' =>[
//                    'rule' => 'notempty',
//                    'message' => __('Required', true)
//                ],
//                'isUniqueForReal' => [
//                    'rule' => 'isFieldUnique2',
//                    'message' => __('This Product SKU has been already used', true)
//                ]
//            ],
            'barcode' => [
                ['rule' => 'isFieldUnique2', 'message' => __('This barcode already used', true)],
                ['rule' => array('minLength', 5), 'allowEmpty' => true, 'message' => __('The barcode must be at least 5 digits long', true)]
            ],
            'store_id' => array(
                'rule' => 'isStoreActive',
                'message' => __t('You cannot add the transaction through a suspended warehouse')
            )
//            'unit_template_id' => ['rule' => 'unitTemplateNotEmpty', 'message' => __('You must choose unit template when tracking type is quantity only', true)]
        );

        if (ifPluginActive(InventoryPlugin)) {

            $this->import['supplier'] = array('title' => __('Supplier', true), 'match' => array('supplier'));

            if(settings::getValue(InventoryPlugin,'enable_multi_units')) {
                $this->import['unit_template_id'] = array('format'=>'unit_template','title' => __('Unit Templates', true), 'match' => array('unit_template_id','unit_template','unit template','UnitTemplate'));
                $this->import['default_buy_factor_id'] = array('title' => __('Default Buy Factor Unit', true), 'match' => array('default_buy_factor','buy_factor'));
                $this->import['default_retail_factor_id'] = array('title' => __('Default Retail Factor Unit', true), 'match' => array('default_retail_factor_id','retail_factor'));
            }

            $this->import['track_stock'] = array('title' => __('Track Stock', true), 'match' => array('trackstock', 'track_stock', 'track stock'));
            $this->import['stock_balance'] = array('title' => __('Stock Level', true), 'match' => array('stockbalance', 'stock_balance', 'balance', 'stock_level', 'stock', 'stock level'));
            $check=settings::getValue(InventoryPlugin, 'disable_overdraft') && ifPluginActive(InventoryPlugin);
            if($check) {
                $this->validate['initial_stock_level'] = ['rule' => array('comparison','>=',0),'message' => __('Amount not sufficient',true)];
            }
        }
        if(ifPluginActive(PluginUtil::PRODUCT_TRACKING_PLUGIN)) {
            $this->import['tracking_type'] = ['title' => 'Tracking Type', 'match' => ['tracking_type', 'trackingtype', 'tracking type']];
        }
    }

//    function unitTemplateNotEmpty($param)
//    {
//        $tracking_type = !empty($this->data['Product']['tracking_type_hidden']) ? $this->data['Product']['tracking_type_hidden'] : $this->data['Product']['tracking_type'];
//        list(, $value) = each($param);
//        return !(empty($value) && $tracking_type == 'quantity_only');
//    }

    function get_import_data () {

		$new_import= $this->import ;
		$custom_fields = $this->_load_custom_fields (null);
		foreach ( $custom_fields['fields'] as $f ){
			$new_import["field_{$f['CustomFormField']['id']}"] = array('is_unique' => false, 'required' => false, 'title' => $f['CustomFormField']['label'], 'match' => array($f['CustomFormField']['label'], strtolower($f['CustomFormField']['label']), str_replace (' ','' , $f['CustomFormField']['label'] ) , strtolower (str_replace (' ','' , $f['CustomFormField']['label'] ) ) ));
		}

        $appEntitiesFormHandler =
            new \Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\AppEntitiesFormErrorHandlerDecorator(
                \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::PRODUCT_ENTITY_KEY
            );
  
        $new_import = array_merge($new_import, $appEntitiesFormHandler->getImportFields());

        $this->loadModel('GroupPrice');
        $priceList = $this->GroupPrice->find('all');
        foreach ($priceList as $list) {
            $new_import["fields.pricelist_".$list['GroupPrice']['id']] = [
                'unique_list' => false,
                'required' => false,
                'multiple' => true,
                'positive' => true,
               // 'format' => "float",
                'title' => $list['GroupPrice']['name'],
                'match' => [$list['GroupPrice']['name'], "#".$list["GroupPrice"]["id"]." ".$list["GroupPrice"]["name"]]
            ];
        }
		return $new_import ;
	}
    public function getSoldQuantity($id, $from = false, $to = false , $stores_list = [] ) {
        if ( empty($stores_list )){
            $this->loadModel("ItemPermission");
            $stores_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW, null, false, true);
        }
        $conditions = array();
        $product = $this->read(null, $id);
        if (ifPluginActive(InventoryPlugin) && !empty($product['Product']['track_stock'])) {
            $conditions['StockTransaction.store_id']= array_keys($stores_list);
            $StockTransaction = ClassRegistry::init('StockTransaction');
            if ($from)
                $conditions['StockTransaction.received_date >= '] = $from;
            if ($to)
                $conditions['StockTransaction.received_date <= '] = $to;
            return $StockTransaction->getSoldProductQuantity($id, $conditions);
        }
        else {

            if ($from)
                $conditions[] = ' Invoice.date >= "' . $from . '"';
            if ($to)
                $conditions[] = ' Invoice.date <= "' . $to . '"';

            $result = $this->query('SELECT SUM(quantity) as total_quantity FROM invoice_items as InvoiceItem, invoices as Invoice WHERE InvoiceItem.product_id = ' . $product['Product']['id'] . ' AND InvoiceItem.invoice_id = Invoice.id AND Invoice.draft <> 1 AND Invoice.type <> 3 AND Invoice.type <> 12 ' . (!empty($conditions) ? (' AND ' . implode(' AND ', $conditions)) : ''));

            return $result[0][0]['total_quantity'];
        }
    }

    public  function getSoldTotal($product, $from = false, $to = false) {

        if (is_numeric($product))
            $product = $this->read(null, $product);
        if (ifPluginActive(InventoryPlugin) && !empty($product['Product']['track_stock'])) {
            $StockTransaction = ClassRegistry::init('StockTransaction');
            if ($from)
                $conditions['StockTransaction.received_date >= '] = $from;
            if ($to)
                $conditions['StockTransaction.received_date <= '] = $to;
            return $StockTransaction->getSoldProductQuantity($product['Product']['id'], $conditions);
        }
        else {

            if ($from)
                $conditions[] = ' Invoice.date >= "' . $from . '"';
            if ($to)
                $conditions[] = ' Invoice.date <= "' . $to . '"';

            $result = $this->query('SELECT SUM(quantity) as total_quantity FROM invoice_items as InvoiceItem, invoices as Invoice WHERE InvoiceItem.product_id = ' . $product['Product']['id'] . ' AND InvoiceItem.invoice_id = Invoice.id AND Invoice.draft <> 1 AND Invoice.type <> 3 AND  Invoice.payment_status = ' . INVOICE_STATUS_PAID . ' ' . (!empty($conditions) ? (' AND ' . implode(' AND ', $conditions)) : ''));

            return $result[0][0]['total_quantity'];
        }
    }

    function valid_tax($param) {
        ClassRegistry::init('Invoice');
        $taxes = Invoice::getTaxesList();
        return isset($taxes[$param['default_tax']]);
    }
    function same_tax($param) {
        $ProductData=$this->data[$this->alias];
        if(!empty($ProductData['tax1']) && $ProductData['tax1']==$ProductData['tax2']){
            return false;
        }
        return  true;
    }
    function getProductList($otherConditions = array(), $includeItemGroups = false, $selectedProductId = null) {
        if($includeItemGroups){
            $this->loadModel('ItemGroup') ;
            $results = [];
            if(is_array($selectedProductId)){// check if product filter is multiple, then id's are mixed (products and item groups)
                // seprate item groups and products
                $itemGroupsIds = [];
                $productsIds = [];
                foreach($selectedProductId as $pId){
                    if (substr($pId??'', 0, 1) === 'g') {
                        $itemGroupsIds[] = str_replace("g","",$pId);
                    }else{
                        $productsIds[] = $pId;
                    }
                }   
                
                $resultItemGroups = [];
                $resultProducts = [];
                if($itemGroupsIds){// find item groups
                    $itemGroups = $this->ItemGroup->find('list' ,  ['recursive' => -1,'conditions' =>['id' => $itemGroupsIds]]);
                    foreach($itemGroups as $id => $itemGroup){
                        $resultItemGroups['g'.$id] = $itemGroup . ' ('.__t('Item Group').')';
                    }
                } 
             
                if($productsIds){// find products
                    $resultProducts = $this->find('list', ['applyBranchFind' => false, 'recursive' => -1, 'conditions'=> ['id'=> $productsIds],'order' => 'Product.id DESC']);
                }

                $results = $resultItemGroups + $resultProducts;

                return $results;
            }else{
                if (substr($selectedProductId??'', 0, 1) === 'g') {
                    $itemGroups = $this->ItemGroup->find('list' ,  ['recursive' => -1, 'conditions' =>['id' => str_replace("g","",$selectedProductId)]]);
                    $result = [];
                    foreach($itemGroups as $id => $itemGroup){
                        $result['g'.$id] = $itemGroup . ' ('.__t('Item Group').')';
                    }
                    return $result;
                }else{
                    $products = $this->find('list', ['applyBranchFind' => false, 'recursive' => -1, 'conditions'=>$otherConditions,'order' => 'Product.id DESC']);
                }
            }
        }else{
            $products = $this->find('list', ['applyBranchFind' => false, 'recursive' => -1, 'conditions'=>$otherConditions,'order' => 'Product.id DESC']);
        }
        return $products;
    }

    function getInvoiceProductList($id = false,$extra_conditions = [] , $hasLimit = true, $oldData = [], $limit = 11, $type = 'invoice') {
        $other_dbproducts=[];
          $this->loadModel ('ItemsCategory');
          $this->loadModel ('Category');
          $this->loadModel ('CustomForm');
        $this->loadModel ('CustomFormField');
        if (ifPluginActive(ProductTracking)) {
            $this->bindModel(['hasMany' => array('TrackingNumber' => array('className' =>'TrackingNumber', 'foreignKey' => 'product_id'))],false);
        }
        $conditions = array();
        if ($type == 'requisition') {
            $conditions['Product.track_stock'] = 1;
        }
        if (!empty($id)){
            $conditions['Product.id'] = $id;
	        $limit = count($id);
        }

        if ($type == "invoice" && !check_permission ( INVOICE_ALL_PRODUCTS )){
            $conditions['Product.staff_id'] = getAuthOwner('staff_id');
        }
        if ( !empty($extra_conditions)&&is_array($extra_conditions)){
            $conditions += $extra_conditions;
	        $limit = count($extra_conditions['Product.id'] ?: []) + count($conditions['Product.id'] ?: []);
        }
        $options = ['conditions' => $conditions, 'order' => 'Product.id DESC', 'recursive' => 1];
        if ($hasLimit)
            $options['limit'] = $limit;

        $dbproducts = $this->find('all', $options);
        if ($hasLimit && !empty($id) && count($dbproducts)<$limit){
            unset($conditions['Product.id']);
            if(!empty(implode('', $id)))
            {
                $conditions[]="Product.id not in (".  implode(',', $id).")";
            }
	        $other_dbproducts = [];
            if (count($dbproducts) < 11){
	            $other_dbproducts = $this->find('all', ['conditions' => $conditions, 'limit' => 11 - count($dbproducts), 'order' => 'Product.id DESC', 'recursive' => 1]);
            }


         $dbproducts=array_merge_recursive($dbproducts,$other_dbproducts);
        }
        $products = [];
//		dd($dbproducts);
		$reread_form = $this->CustomForm->findByTableName ("products");
		if ( !empty ( $reread_form ) ){
			$this->_modelSettings ("products_custom_data" , [] , 0 ) ;
		}

        foreach ($dbproducts as $prod) {

            $prod['Product']['prices']=$prod['ProductPrice'];
            // warning suppress
            $prod['Product']['file'] = $prod['ProductMasterImage']['file_full_path'] ?? null;
            $prod = $this->handleTrackingNumber( $prod, $oldData);
            $products[$prod['Product']['id']] = $prod['Product'];

        if ( !empty ( $reread_form ) ){


                $custom_client_data = $this->CustomTable->findByProductId($prod['Product']['id']);
            // warning suppress
            $custom_data=$custom_client_data['products_custom_data']??null;
                unset($custom_data['id']);
                unset($custom_data['product_id']);
                unset($custom_data['created']);
                unset($custom_data['modified']);
                unset($custom_data['staff_id']);
                // warning suppress
                if (isset($custom_data))
                foreach($custom_data as $key=>$v){
                $products[$prod['Product']['id']][$key]=$v;
                }


        }
        $categories = $this->ItemsCategory->find('list' , ['fields' => 'category_id' , 'recursive' => -1 , 'conditions' => ['item_id' =>$prod['Product']['id'],'item_type'=>ItemsCategory::ITEM_TYPE_PRODUCT ] ]);
        if (!empty($categories)){
            $cats = $this->Category->find('list' , ['recursive' => -1 ,'conditions'  => ['id' => $categories] ] );
            $products[$prod['Product']['id']]['category'] = implode(",",$cats);
        }
            $this->getStoreBalanceForProductList($prod,$products);
            if (!empty(settings::getValue(InventoryPlugin, SettingsUtil::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS))) {
                $this->getStoreAvailableForProductList($prod, $products);
            }
        }
        $this->add_multi_units ( $products , true, false) ;
		foreach($products as &$row){
		if(empty($row['product_code']))	{
		$row['product_code']=$row['id'];
		}
		}
        return $products;

    }
    function getStoreBalanceForProductList($prod,&$products){
        if(empty($this->warehouseService))
        {
            App::import('vendor','WarehouseService' , ['file' => 'WarehouseService.php']);
            $this->warehouseService = new WarehouseService();
        }
        $pStock = $this->warehouseService->getStaffProductStock(1, $prod['Product']['id'] , getAuthOwner('staff_id'));
        $products[$prod['Product']['id']]['store_balance'] = $pStock;
        $products[$prod['Product']['id']]['stock_balance'] = array_sum($pStock);
        $pendingStock = $this->warehouseService->getStaffProductStock(1, $prod['Product']['id'] , getAuthOwner('staff_id'), null, false, 2);
        $products[$prod['Product']['id']]['store_pending'] = $pendingStock;
        $products[$prod['Product']['id']]['stock_pending'] = array_sum($pendingStock);
    }
    function getStoreAvailableForProductList($prod,&$products){
        if(empty($this->invoiceItemService))
        {
            $this->invoiceItemService = new App\vendors\Services\InvoiceItemService();
        }
        $this->invoiceItemService->stockPendingQTY = $products[$prod['Product']['id']]['store_pending'];
        $availableStock = $this->invoiceItemService->getStaffProductAvailableStock($products[$prod['Product']['id']]['store_balance'],1, $prod['Product']['id'] , getAuthOwner('staff_id'));
        $products[$prod['Product']['id']]['store_available'] = $availableStock;
        $products[$prod['Product']['id']]['stock_available'] = array_sum($availableStock);
    }
    function getPurchaseOrderProductList($id = false , $extra_conditions = [], $limit = 11 ) {
        $site_id = getAuthOwner('id');
        $conditions = array();
        if (!empty($id))
            $conditions['Product.id'] = $id;
        if ( !check_permission ( INVOICE_ALL_PRODUCTS ) ){
            $conditions['Product.staff_id'] = getAuthOwner('staff_id');
        }
        if ( !empty($extra_conditions)&&is_array($extra_conditions)){
            $conditions += $extra_conditions ;
        }
        if (ifPluginActive(ProductTracking)) {
            $this->bindModel(['hasMany' => array('TrackingNumber' => array('className' =>'TrackingNumber', 'foreignKey' => 'product_id'))],false);
        }
        $dbproducts = $this->find('all', array('conditions' => $conditions, 'limit' => $limit, 'order' => 'Product.id DESC', 'recursive' => 0));
        $products = array();
        foreach ($dbproducts as $prod) {
            $prod['Product']['file'] = $prod['ProductMasterImage']['file_full_path'];
            $prod = $this->handleTrackingNumber($prod);
            $products[$prod['Product']['id']] = $prod['Product'];
            $this->getStoreBalanceForProductList($prod,$products);
        }
//				dd($products);

        $this->add_multi_units ($products, true, false);

		foreach($products as &$row){
		if(empty($row['product_code']))	{
		$row['product_code']=$row['id'];
		}
		}

        return $products;
    }

	function add_multi_units_quick ( &$products)
    {
        $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
        if ( $enable_multi_units ) {
            $this->loadModel('UnitFactor');
            $unit_factors = [] ;
            $unit_templates
                = [] ;
            foreach ( $products as &$p ) {
                if ( !empty ( $p['unit_template_id']) ){
                    if ( empty ( $unit_templates[$p['unit_template_id']]))
                    {

                        $unit_templates[$p['unit_template_id']] = $this->quick_find_with_relations('UnitTemplate',$p['unit_template_id'],['table'=>'unit_templates'])['UnitTemplate'];
                    }
                    if ( empty ($unit_factors[$p['unit_template_id']] ) ) {

                        $factors = $this->UnitFactor->find('all' , [ 'recursive'=>-1, 'conditions' => ['unit_template_id' =>$p['unit_template_id'] ] ]);
                        $te = $unit_templates[$p['unit_template_id']];
                        $unit_factors[$p['unit_template_id']][] = [
                            'id' => 0 ,
                            'small_name' => ($te['unit_small_name']? $te['unit_small_name']: "" ),
                            'factor_name' =>$te['main_unit_name'],
                            'factor' => 1];
                        foreach ( $factors as $f ) {
                            $unit_factors[$p['unit_template_id']][] = $f['UnitFactor'] ;
                        }
                    }
                    $p['unit_factors'] = $unit_factors[$p ['unit_template_id']];
                    $p['factor'] = $this->get_default_factor(['Product' => $p]);
                }
                unset ( $p  ) ;
            }
        }
    }


    function add_multi_units ( &$products, $withoutSpan = false, $addProductCode = true)
    {
        $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
        if ( $enable_multi_units ) {
            $this->loadModel('UnitFactor');
            $this->loadModel('UnitTemplate');
            $unit_factors = [] ;
            $unit_templates
                = [] ;
            foreach ( $products as &$p ) {
                if ($addProductCode) {
                    if ($withoutSpan) {
                        $p['name']=$p['name'].' #'.$p['product_code'];
                    } else {
                        $p['name']=$p['name'].' #<span dir="ltr">'.$p['product_code'].'</span>';
                    }
                }
                if ( !empty ( $p['unit_template_id']) ){
                    if ( empty ( $unit_templates[$p['unit_template_id']]))
                    {
                        $unit_templates[$p['unit_template_id']] = $this->UnitTemplate->findById($p['unit_template_id'])['UnitTemplate'];
                    }
                    if ( empty ($unit_factors[$p['unit_template_id']] ) ) {

                        $factors = $this->UnitFactor->find('all' , [ 'recursive'=>-1, 'conditions' => ['unit_template_id' =>$p['unit_template_id'] ] ]);
                        $te = $unit_templates[$p['unit_template_id']];
                        $unit_factors[$p['unit_template_id']][] = [
                            'id' => 0 ,
                            'small_name' => ($te['unit_small_name']? $te['unit_small_name']: "" ),
                            'factor_name' =>$te['main_unit_name'],
                            'factor' => 1];
                        foreach ( $factors as $f ) {
                            $unit_factors[$p['unit_template_id']][] = $f['UnitFactor'] ;
                        }
                    }
                    $p['unit_factors'] = $unit_factors[$p ['unit_template_id']];
                    $p['factor'] = $this->get_default_factor(['Product' => $p]);//$p ['unit_template_id'];
                }
                unset ( $p  ) ;
            }
        }
    }
    //-----------------------------------------
    function getFilters($action = 'index') {

		$this->loadModel('ItemsTag');
        $filters = array(
            'id' => array('div' => false, 'input_type' => 'hidden', 'more-options' => false,  'label' => false),
            'keywords' => array('type' => 'like', 'options' => array('label' => __('Search Keywords', true), 'placeholder' => __('Enter name or code', true))),
            'category' => array('input_type'=>'element', 'element_name' => 'products/product_category', 'element_options' => ['selected_category' => $_GET['category'] ?: null,'empty' => __('[Any Category]', true), 'label' => __('Category', true)], 'more-options' => false),
			'created' => array('more-options' => true, 'type' => 'date_range', 'to_label' => __('Date To', true), 'from_label' => __('Date From', true)),
			'tags' => array('div_class' => 'full-width', 'input_type' => 'tags-multiselect', 'more-options' => true, 'empty' => __('Any tags', true), 'label' => __('Tag', true), 'tag_type' => ItemsTag::model_name_to_tag_type($this->name)),
        );

        App::import('Vendor', 'settings');
        $you_sell = settings::getValue(InvoicesPlugin, 'sold_item_type');

        if ($you_sell != settings::OPTION_SOLD_TYPE_SERVICES && ifPluginActive(InventoryPlugin)) {
	        $filters['brand_id'] = ['input_type'=>'element', 'element_name' => 'products/product_brand', 'element_options' => ['selected_brand' => $_GET['brand_id'] ?? null,'empty' => __('[Any Brand]', true), 'label' => __('Brand', true)], 'more-options' => false];
        }
        if (ifPluginActive(InventoryPlugin))
            $filters['status'] = array('options' => array('type' => 'select', 'div' => 'form-group text title col-md-3 col-sm-4 col-xs-6 primary-actions', 'empty' => __('All', true), 'options' => array(1 => __('Available', true), 2 => __('Low Stock', true), 3 => __('Out of Stock', true), 4 => __('Inactive', true), 5 => __('Suspended', true))));

        if(ifPluginActive(PRODUCT_TRACKING_PLUGIN))
        {
            $filters['tracking_type'] = array('type' => '=', 'options' => array('options' =>  \App\Utils\TrackStockUtil::getTrackingTypes(),'type' => 'select', 'div' => 'form-group text title col-md-3 col-sm-4 col-xs-6 primary-actions', 'class' => 'selectpicker INPUT form-control', 'data-live-search' => 'true'));
        }

        $filters['barcode'] = array('type' => 'like','more-options' => false, 'options' => array( 'div' => ' form-group ', 'class' => 'selectpicker INPUT form-control'));
        $filters['product_code'] = array('type' => '=','more-options' => true, 'options' => array( 'div' => ' form-group ', 'class' => 'selectpicker INPUT form-control'));
        $this->loadModel('ProductAttributeOption');
        $attributes = $this->ProductAttributeOption->find('all',
            [
                'group' => 'ProductAttributeOption.attribute',
                'recursive' => -1,
                'fields' => 'attribute',
                'conditions' => array(
                    'ProductAttributeOption.attribute  !=' => ''
                )
            ]
        );
        if (is_countable($attributes) && count($attributes) <= 10){
            foreach ($attributes as $index =>$attribute){
                $option = $_GET['attributes'][$index]['option'] ?: null;
                $filters['attributes['. $index. '][option]']=array('input_type'=>'element', 'element_name' => 'products/attribute_option', 'element_options' => ['attributes'=> ['option'=>$option], 'selectedOption' => $option?: null,'empty' => __('[Any Attribute]', true), 'label' => __('Attribute', true) , 'attribute'=> $attribute['ProductAttributeOption']['attribute'] , 'filter_index'=>$index], 'more-options' => $option ? false : true);
            }
        }else{
            $options = $_GET['multiple-attributes'] ?: [];
            $filters['multiple-attributes']=array('input_type'=>'element', 'element_name' => 'products/product_attribute', 'element_options' => ['options'=> $options,'empty' => __('[Any Attribute]', true), 'label' => __('Attribute', true) ], 'more-options' => $option ? false : true);
        }
        return $filters;
    }

    function getCategoriesListOld($numeric_index = true) {
        $ret = array();
        $ret2 = array();
        $list = $this->query('SELECT  DISTINCT category FROM products as Product WHERE category IS NOT NULL AND category <> \'\' ORDER BY category');
        foreach ($list as $l)
            if (!empty($l['Product']['category']) && !in_array($l['Product']['category'], $ret)) {
                $ret[] = $l['Product']['category'];
                $ret2[$l['Product']['category']] = $l['Product']['category'];
            }
        if ($numeric_index)
            return $ret;
        return $ret2;
    }
    function getCategoriesList($numeric_index = true) {
        $this->loadModel('Category');
        $cats = $this->Category->find('list' , ['conditions' => ['Category.category_type' => Category::CATEGORY_TYPE_PRODUCT]]);
        if ( $numeric_index ) {
            return array_values ( $cats ) ;
        }else {
            return $cats  ;
        }

    }



    function getBrandsList($numeric_index = true) {
        $ret = array();
        $ret2 = array();
        $this->loadModel('Brand');
        $list = $this->Brand->find('all', ['order' => ['Brand.name' => 'ASC']]);
        foreach ($list as $l)
            if (!empty($l['Brand']['name']) && !in_array($l['Brand']['name'], $ret)) {
                $ret[] = $l['Brand']['name'];
                $ret2[$l['Brand']['name']] = $l['Brand']['name'];
            }
        if ($numeric_index)
            return $ret;
        return $ret2;
    }
	function getFilteredBrandsList($brand = false) {
		$all_brands = "SELECT DISTINCT brand FROM products as Product WHERE brand IS NOT NULL AND brand <> '' ORDER BY brand";
		$filter_brands = "SELECT DISTINCT brand FROM products as Product WHERE brand IS NOT NULL AND brand <> '' and brand like '%$brand%' ORDER BY brand";
		if ($brand) {
			return Set::extract('{n}.Product.brand', $this->query($filter_brands, false));
		}
		return Set::extract('{n}.Product.brand', $this->query($all_brands, false));
	}

    function getTagsList($numeric_index = true) {
        $ret = array();
        $ret2 = array();
        $list = $this->query('SELECT  tags FROM products as Product WHERE tags IS NOT NULL AND tags <> \'\' ORDER BY tags');
        foreach ($list as $l)
            if (!empty($l['Product']['tags'])) {
                $brand_list = explode(",", $l['Product']['tags']);
                foreach ($brand_list as $brand_item) {
                    if (!in_array($brand_item, $ret)) {
                        $ret[] = $brand_item;
                        $ret2[$l['Product']['tags']] = $brand_item;
                    }
                }
            }

        if ($numeric_index)
            return $ret;
        return $ret2;
    }

		function check_import($data) {
            

            if (isset($data['Product']['store_id'])) {
                $this->loadModel('Store');
                $store_id = is_numeric($data['Product']['store_id']) ? $data['Product']['store_id'] : $this->Store->getStoreIdByName($data['Product']['store_id']);
                $store = $this->Store->find('first', ['conditions' => ['Store.id' => $store_id]]); 
                if (empty($store)) {
                    $return['status'] = false;
                    $return['message'] = __('Warehouse not found', true);
                    return $return;
                }
            }
            if (isset($data['Product']['type']) && $data['Product']['type']==self::SERVICE_TYPE) {
                if(isset($data['Product']['tracking_type'])||$data['Product']['track_stock']) {
                    $return['status'] = false;
                    $return['message'] = __t('you can\'t add service with tracking data or warehouse data');
                    return $return;
                }
            } 

			if (isset($mydata['Product']['track_stock']) and !in_array($mydata['Product']['track_stock'], ['0', '1', 'yes', 'no'])) {
				$return['status'] = false;
				$return['message'] = __('You should enter valid data in the tracking stock', true);
				return $return;
			}
            if(ifPluginActive(PluginUtil::PRODUCT_TRACKING_PLUGIN)) {
                if(isset($data['Product']['tracking_type'])) {
                    if(!empty($data['Product']['tracking_type']) && !in_array($data['Product']['tracking_type'], array_keys(TrackStockUtil::getTrackingTypes()))) {
                        $return['status'] = false;
                        $return['message'] = __('No tracking type matched the selected one', true);
                        return $return;
                    }

                    if(!empty($data['Product']['tracking_type']) && $data['Product']['tracking_type'] !== TrackStockUtil::QUANTITY_ONLY && !empty($data['Product']['new_stock_balance'])) {
                        $return['status'] = false;
                        $return['message'] = __('You cannot enter an initial stock for any item tracked by serial ,lot ,expiry or lot and expiry date', true);
                        return $return;
                    }

                    if($data['Product']['id']) {
                        $product = $this->findById($data['Product']['id']);
                        if($product['Product']['tracking_type'] != $data['Product']['tracking_type']) {
                            //tracking type changed
                            $allow_negative_tracking = settings::getValue(ProductTracking, 'allow_negative_tracking');
                            $this->loadModel('StockTransaction');
                            $record = $this->StockTransaction->find('first', ['conditions' => ['StockTransaction.product_id' => $data['Product']['id']]]);
                            if($record && !$allow_negative_tracking) {
                                $return['status'] = false;
                                $url = Router::url(['controller' => 'settings', 'action' => 'inventory']);
                                $return['message'] = __('You cannot update the tracking type as there’s a stock transactions created for the item', true).' '.sprintf(__('If you need to change the tracking type so that go to general inventory settings and enable %s', true), '<a href="'.$url.'" target="_blank">'.__("Allow negative tracking items", true).'</a>');
                                return $return;
                            }
                        }
                    }
                }
            }
			if (!empty($data['Product']['unit_template_id'])) {
				$UnitTemplate = ClassRegistry::init('UnitTemplate');
				$row = $UnitTemplate->find('first', ['conditions' => ['OR' => ['UnitTemplate.id' => $data['Product']['unit_template_id'], 'UnitTemplate.template_name' => $data['Product']['unit_template_id']]]]);
				if (!isset($row['UnitTemplate']['id'])) {
					$return['status'] = false;
					$return['message'] = __('Unit Template not found', true);
					return $return;
				}
				if (!empty($data['Product']['default_buy_factor_id'])) {
					$UnitTemplate = ClassRegistry::init('UnitTemplate');
					$row = $UnitTemplate->find('first', ['conditions' => ['OR' => ['UnitTemplate.id' => $data['Product']['default_buy_factor_id'], 'UnitTemplate.template_name' => $data['Product']['default_buy_factor_id']]]]);
                    if (!isset($row['UnitTemplate']['id'])) {
						$UnitFactor = ClassRegistry::init('UnitFactor');
						$row = $UnitFactor->find('first', ['conditions' => ['OR' => ['UnitFactor.id' => $data['Product']['default_buy_factor_id'], 'UnitFactor.factor_name' => $data['Product']['default_buy_factor_id']]]]);
						if (!isset($row['UnitFactor']['id'])) {
							$return['status'] = false;
							$return['message'] = __('Default Buy Factor Unit not found', true);
							return $return;
						}
					}
				}
				if (!empty($data['Product']['default_retail_factor_id'])) {
					$UnitTemplate = ClassRegistry::init('UnitTemplate');
                    $UnitFactor = ClassRegistry::init('UnitFactor');
                    $row = $UnitFactor->find('first', ['conditions' => ['OR' => ['UnitFactor.id' => $data['Product']['default_retail_factor_id'],'unit_template_id' => $row['UnitTemplate']['id'], 'UnitFactor.factor_name' => $data['Product']['default_retail_factor_id']]]]);
                    if (!isset($row['UnitFactor']['id'])) {
                        $return['status'] = false;
                        $return['message'] = __('Default Retail Factor Unit not found', true);
                        return $return;
                    }
				}
			}
            $conditions = [];
			// Barcode Unique Check
            if(isset($data['Product']['barcode'])){
                if(!empty($data['Product']['barcode'])){
                    $conditions['Product.barcode'] = $data['Product']['barcode'];
                } else {
                    unset($data['Product']['barcode']);
                }
            }

            if (isset($data['Product']['id'])) {
                $conditions['Product.id !=']=$data['Product']['id'];
            }

			if (isset($data['Product']['barcode']) && $this->find('first', ['conditions' =>$conditions ]) !== false) {
				$return['status'] = false;
				$return['message'] = __("This barcode already used", true);
				return $return;
			}
			return ['status' => true];
		}

    function before_import($mydata) {

        $Category = GetObjectOrLoadModel('Category');
        $Tax = GetObjectOrLoadModel('Tax');

        if (
            empty($mydata['Product']['type']) ||
            (
                !in_array($mydata['Product']['type'], self::ProductTypes()) &&
                !array_key_exists($mydata['Product']['type'], self::ProductTypes())
            )
        ) {
            $mydata['Product']['type'] = self::PRODUCT_TYPE;
        }

        if(ifPluginActive(PluginUtil::PRODUCT_TRACKING_PLUGIN) && isset($mydata['Product']['tracking_type'])) {
            $mydata['Product']['tracking_type'] = strtolower(str_replace(' ','_', $mydata['Product']['tracking_type']));
        }
        if(empty($mydata['Product']['id']) && empty($mydata['Product']['tracking_type'])) {
            if(!isset($mydata['Product']['type']) || $mydata['Product']['type'] != Product::SERVICE_TYPE){
                $mydata['Product']['tracking_type'] = TrackStockUtil::QUANTITY_ONLY;
            }
        }

        $mydata['Product']['tax1_insert'] = !empty($mydata['Product']['tax1']) ? $mydata['Product']['tax1'] : settings::getValue(0, 'default_product_tax_1_id');
        $mydata['Product']['tax2_insert'] = !empty($mydata['Product']['tax2']) ? $mydata['Product']['tax2'] : settings::getValue(0, 'default_product_tax_2_id');
        if (!empty($mydata['Product']['tax1'])) {
            $TaxRow = $Tax->find('first', ['conditions' => ['OR' => ['Tax.name' => $mydata['Product']['tax1'], 'Tax.id' => $mydata['Product']['tax1']]]]);
            $mydata['Product']['tax1'] = $TaxRow['Tax']['id'];
            $mydata['Product']['tax1_insert']  = $TaxRow['Tax']['id'];
        }
        if (!empty($mydata['Product']['store_id'])) {
            $Store = GetObjectOrLoadModel('Store');
            $store = $Store->find('first', ['conditions' => ['OR' => ['Store.name' => $mydata['Product']['store_id'], 'Store.id' => $mydata['Product']['store_id']]]]);
            if (!empty($store)) $mydata['Product']['store_id'] = $store['Store']['id'];
        }
        
        if (!empty($mydata['Product']['tax2'])) {
            $TaxRow = $Tax->find('first', ['conditions' => ['OR' => ['Tax.name' => $mydata['Product']['tax2'], 'Tax.id' => $mydata['Product']['tax2']]]]);
            $mydata['Product']['tax2'] = $TaxRow['Tax']['id'];
            $mydata['Product']['tax2_insert']  = $TaxRow['Tax']['id'];
        }
        if (empty($mydata['Product']['type'])) {
            $mydata['Product']['type'] = self::PRODUCT_TYPE;
        }
        if (ifPluginActive(InventoryPlugin) and isset($mydata['Product']['stock_balance'])) {
            $mydata['Product']['new_stock_balance'] = $mydata['Product']['stock_balance'];
            if (!empty($mydata['Product']['new_stock_balance']) && $mydata['Product']['new_stock_balance'] != "") {
                $mydata['Product']['track_stock'] = 1;
            }
        }



		unset($mydata['Product']['stock_balance']);
        if(isset($mydata['Product']['track_stock'])){
            if($mydata['Product']['track_stock']=='yes'){
                $mydata['Product']['track_stock']=1;
            }
            if($mydata['Product']['track_stock']=='no'){
                $mydata['Product']['track_stock']=0;
            }
        }
            //if he doesn't have the setting for products , make the track stock = 0 ... for new items only !! .
        if(!isset($mydata['Product']['track_stock']) && $mydata['Product']['type']==self::PRODUCT_TYPE) {
            if (!isset ($mydata['Product']['id']) or $mydata['Product']['id'] == "") {

                if (in_array(getCurrentSite('sold_item_type'),[0,1])) {
                    $mydata['Product']['track_stock'] = 1;
                } else {
                    $mydata['Product']['track_stock'] = 0;
                }
            }
        }
		//loading custom data
                $have_custom_fields = false ;
        $have_app_fields = false;
		foreach ( $mydata['Product'] as $k => $v ) {
			if ( strpos ($k , "field_" ) !== false ){
				$mydata['products_custom_data'][$k] = $v;
                                $have_custom_fields = true ;
				unset ($mydata['Product'][$k] );
			}

            if (strpos ($k , '.') !== false) {
                $fieldKey = explode('.', $k);
                $appEntityKey = $fieldKey[0];
                $fieldDbName = $fieldKey[1];
                $mydata[$appEntityKey][$fieldDbName] = $v;
                $have_app_fields = true;
            }
		}

        if ($have_app_fields) {
            $mydata['entity_key'] = EntityKeyTypesUtil::PRODUCT_ENTITY_KEY;
        }

        if ($have_custom_fields ){
            $mydata['CustomTable'] = 'products_custom_data';
            $mydata['CustomTableId'] = 'product_id';
        }

        /** Set default status to active in case it is not set **/
        if (empty($mydata['Product']['status'])) {
            $mydata['Product']['status']  = ProductStatusUtil::STATUS_ACTIVE;
        }

        return $mydata;
    }

    function before_import_save($mydata, $updated_field = null)
    {
        if (!empty($mydata['Product']['supplier'])) {

            $mydata['Product']['supplier_id'] = $this->findOrCreateSupplier($mydata['Product']['supplier']);
            unset($mydata['Product']['supplier']);
        }

        if (!empty($mydata['Product']['brand'])){
            $Brand = GetObjectOrLoadModel('Brand');
            $mydata['Product']['brand_id'] = $Brand->findOrCreate($mydata['Product']['brand']);
        }

        if (!empty($mydata['Product']['unit_template_id'])) {
            $UnitTemplate = ClassRegistry::init('UnitTemplate');
            $row = $UnitTemplate->find('first', array('conditions' => array('OR' => array('UnitTemplate.id' => $mydata['Product']['unit_template_id'], 'UnitTemplate.template_name' => $mydata['Product']['unit_template_id']))));
            $mydata['Product']['unit_template_id'] = $row['UnitTemplate']['id'];
        }

        if (!empty($mydata['Product'][$updated_field])) {
            $product = $this->find('first', ['conditions' => [$updated_field => $mydata['Product'][$updated_field]], 'recursive' => -1]);
            if (!empty($product['Product']['id'])) {
                $mydata['Product']['type'] = $product['Product']['type'];
            }
        } else {
            $mydata['Product']['tax1'] = $mydata['Product']['tax1_insert'];
            $mydata['Product']['tax2'] = $mydata['Product']['tax2_insert'];
        }

        unset($mydata['Product']['tax1_insert']);
        unset($mydata['Product']['tax2_insert']);

        if (!empty($mydata['Product']['default_buy_factor_id'])) {
            $UnitTemplate = ClassRegistry::init('UnitTemplate');
            $row = $UnitTemplate->find('first', array('conditions' => array('OR' => array('UnitTemplate.id' => $mydata['Product']['default_buy_factor_id'], 'UnitTemplate.template_name' => $mydata['Product']['default_buy_factor_id']))));
            if (!isset($row['UnitTemplate']['id'])) {
                $UnitFactor = ClassRegistry::init('UnitFactor');
                $row = $UnitFactor->find('first', array('conditions' => array('OR' => array('UnitFactor.id' => $mydata['Product']['default_buy_factor_id'], 'UnitFactor.factor_name' => $mydata['Product']['default_buy_factor_id']))));
                $mydata['Product']['default_buy_factor_id'] = $row['UnitFactor']['id'];
            } else {
                $mydata['Product']['default_buy_factor_id'] =  $this->check_unit_factor_with_same_unit_template_name($mydata['Product']['default_buy_factor_id'],$row['UnitTemplate']['id']);
            }
        }

        if (!empty($mydata['Product']['default_retail_factor_id'])) {
            $UnitTemplate = ClassRegistry::init('UnitTemplate');
            $row = $UnitTemplate->find('first', array('conditions' => array('OR' => array('UnitTemplate.id' => $mydata['Product']['default_retail_factor_id'], 'UnitTemplate.template_name' => $mydata['Product']['default_retail_factor_id']))));
            if (!isset($row['UnitTemplate']['id'])) {
                $UnitFactor = ClassRegistry::init('UnitFactor');
                $row = $UnitFactor->find('first', array('conditions' => array('OR' => array('UnitFactor.id' => $mydata['Product']['default_retail_factor_id'], 'UnitFactor.factor_name' => $mydata['Product']['default_retail_factor_id']))));
                $mydata['Product']['default_retail_factor_id'] = $row['UnitFactor']['id'];
            } else {
                $mydata['Product']['default_retail_factor_id'] =  $this->check_unit_factor_with_same_unit_template_name($mydata['Product']['default_retail_factor_id'],$row['UnitTemplate']['id']);
            }
        }
        return $mydata;
    }

    function check_unit_factor_with_same_unit_template_name($factor_id ,$unit_template_id)  {
        $UnitFactor = ClassRegistry::init('UnitFactor');
        $row = $UnitFactor->find('first', array('conditions' => array('UnitFactor.unit_template_id'=> $unit_template_id, 'OR' => array('UnitFactor.id' => $factor_id, 'UnitFactor.factor_name' => $factor_id))));
        return  !empty($row['UnitFactor']['id']) ?$row['UnitFactor']['id']:0;
    }

    function findOrCreateSupplier($name)
    {
        $this->loadModel('Supplier');
        if (isModelSharable('Supplier')) {
            $this->Supplier->applyBranch['onFind'] = false;
        }
        $row = $this->Supplier->find('first', ['recursive' => -1, 'conditions' => ['business_name' => $name]]);
        if ($row) {
            return $row['Supplier']['id'];
        } else {
            $newSupplier['Supplier']['business_name'] = $name;
            $this->Supplier->create();
            $this->Supplier->saveSupplier($newSupplier);
            return $this->Supplier->getLastInsertID();
        }
    }
    function import_view_link($id) {
    $row=$this->find('first',array('conditions'=>array('Product.id'=>$id)))    ;
    return '<a target="_blank" href="'.Router::url(array('controller'=>'products','action'=>'view',$id)).'" >'.$row['Product']['name'].' #'.$id.'</a>';
    }

    function after_import($mydata, $row) {
        $RequisitionModel = GetObjectOrLoadModel('Requisition');
        GetObjectOrLoadModel('StockTransaction');
        if (ifPluginActive(InventoryPlugin) and isset($row['Product']['new_stock_balance']) && !empty($row['Product']['track_stock'])) {
            $product = $this->read(null, $row['Product']['id']);
            $currentBalance  = $product['Product']['stock_balance']?:0;
            if ( !empty($mydata['Product']['store_id'])){
                $this->loadModel('StoreStockBalance');
                $this->loadModel('Store');
                $mydata['Product']['store_id'] = is_numeric($mydata['Product']['store_id']) ? $mydata['Product']['store_id'] : $this->Store->getStoreIdByName($mydata['Product']['store_id']);
                $currentBalance = $this->StoreStockBalance->find('first' , ['recursive' => -1 , 'conditions' => ['store_id' =>$mydata['Product']['store_id'],'product_id' => $row['Product']['id'] ] ])['StoreStockBalance']['balance'];
            }
            $new_stock = ((float)$row['Product']['new_stock_balance']?:0) -$currentBalance ;


            if ($new_stock > 0 or $new_stock < 0) {
                $requisitionId = $this->addImportRequisition(
                    $mydata,
                    $row,
                    $new_stock);
                if(!isset($this->cache_data['stores']))
                {
                    $this->loadModel('Store');
                    $stores_list=$this->Store->find('list' );
                    $this->cache_data['stores']= array_keys($stores_list);
                    $this->cache_data['default_store']=$this->Store->getPrimaryStore();
                }

                $AUTO_INCREMENT = $this->query("SELECT AUTO_INCREMENT FROM information_schema.tables WHERE table_name = 'stock_transactions' AND table_schema = DATABASE( ) ;", false);
                $data['notes'] = "Import Product";
                $data['price'] = isset($row['Product']['buy_price']) ? $row['Product']['buy_price'] : $product['Product']['buy_price'];
                $data['currency_code'] = getCurrentSite('currency_code');
                $data['staff_id'] = getAuthOwner('staff_id');
                $data['received_date'] = date("Y-m-d H:i:s");
                $data['order_id'] = $AUTO_INCREMENT[0]['tables']['AUTO_INCREMENT'];
                $data['product_id'] = $row['Product']['id'];
                $data['source_type'] = StockTransaction::SOURCE_MANUAL;
                if ($requisitionId) {
                    $data['order_id'] = $requisitionId;
                    $data['source_type'] = $new_stock > 0 ? StockTransaction::SOURCE_RQ_MANUAL_INBOUND : StockTransaction::SOURCE_RQ_MANUAL_OUTBOUND;
                }
                $data['status'] = StockTransaction::STATUS_PROCESSED;
                $data['quantity'] = (float)$new_stock;
                $data['price'] = $data['price'] ?: 0;
                $data['total_price'] = $data['quantity'] * $data['price'];
                if ( !empty($mydata['Product']['store_id'])){
                    $data['store_id'] = $mydata['Product']['store_id'];
                }
                if(empty($data['store_id'] )||!in_array($data['store_id'] ,$this->cache_data['stores']))
                {
                    $data['store_id']=$this->cache_data['default_store'];
                }

                if ($new_stock > 0) {
                    $data['transaction_type'] = StockTransaction::TRANSACTION_IN;
                } else {
                    $data['transaction_type'] = StockTransaction::TRANSACTION_OUT;
                }
                StockTransaction::setApplyNewWayForCommunication(false);
                StockTransaction::saveTransaction($data, array('param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name']));
            }
        }
        if (!empty($mydata['fields'])) {
            $this->loadModel('GroupPrice');
            $this->loadModel('ProductPrice');
            $priceLists = $this->GroupPrice->find('all');
            $customData = $mydata['fields'];
            foreach ($priceLists as  $key => $priceList) {
                $key = "pricelist_".$priceList['GroupPrice']["id"];
                if (array_key_exists($key, $customData) && is_numeric($customData[$key]) &&  $customData[$key] !== "") {
                    $this->ProductPrice->create();
                    $this->ProductPrice->deleteAll([
                        "ProductPrice.group_price_id" => $priceList['GroupPrice']["id"],
                        "ProductPrice.product_id" => $row['Product']['id'],
                    ]);
                    $this->ProductPrice->group_price_id = $priceList['GroupPrice']["id"];
                    $this->ProductPrice->product_id = $row['Product']['id'];
                    $priceListInsertData = ['ProductPrice' => ["group_price_id" => $priceList['GroupPrice']["id"], "product_id" => $row['Product']['id'], "price" => $customData[$key]]];
                    $this->ProductPrice->save($priceListInsertData);
                    $createdId = $this->ProductPrice->getLastInsertId();
                    $record = getRecordWithEntityStructure(EntityKeyTypesUtil::PRICE_LIST_ITEMS, $createdId, 1)->toArray();
                    $newData['id'] = $createdId;
                    $addedRelations = [];
                    $addedRelations[]= new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest($record['group_price_id'], EntityKeyTypesUtil::PRICE_LIST);
                    $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::PRICE_LIST_ITEMS);
                    $requests = (new DetailsEntityActivityLogRequestsCreator())->create($st, $newData, [], $addedRelations);
                    $activityLogService =  new \App\Services\ActivityLogService();
                    foreach ($requests as $requestObj) {
                        $activityLogService->addActivity($requestObj);
                    }
                }
            }

        }

        //      echo "<pre>After";
//     print_r($mydata);
        //      print_r($row);
//        die();
        return $mydata;
    }

    function addImportRequisition($mydata, $row, $new_stock)
    {
        $entityAppData = GetObjectOrLoadModel('EntityAppData');
        $RequisitionModel = GetObjectOrLoadModel('Requisition');
        $orderType = $new_stock > 0 ? $RequisitionModel::ORDER_TYPE_MANUAL_IMPORT_INBOUND : $RequisitionModel::ORDER_TYPE_MANUAL_IMPORT_OUTBOUND;
        $requisitionType = $new_stock > 0  ? Requisition::TYPE_INBOUND :  Requisition::TYPE_OUTBOUND;
        $uuid = $mydata['_uuid'];
        $currentRequisition = $entityAppData->find('first', ['conditions' => ['id' => $uuid]]);

        $jsonData = json_decode($currentRequisition['EntityAppData']['data'], true);

        $this->loadModel('Store');
        $storeId = !empty($mydata['Product']['store_id']) ?
            (is_numeric($mydata['Product']['store_id']) ? $mydata['Product']['store_id'] : $this->Store->getStoreIdByName($mydata['Product']['store_id']))
            : $this->Store->getPrimaryStore();

        $requisitionId = null;
        if ($jsonData['inbound_requisition_' . $storeId] && $requisitionType == Requisition::TYPE_INBOUND) {
            $requisitionId = $jsonData['inbound_requisition_' . $storeId];
        } elseif ($jsonData['inbound_requisition_' . $storeId] && $requisitionType == Requisition::TYPE_OUTBOUND) {
            $requisitionId = $jsonData['outbound_requisition_' . $storeId];
        }

        if (!is_null($requisitionId)) {
            $data = $RequisitionModel->find('first', ['conditions' => ['Requisition.id' => $requisitionId]]);
            $data['RequisitionItem'][] = [
                'product_id' => $row['Product']['id'],
                'quantity' => abs($new_stock),
                'price' => $row['Product']['buy_price'],
                'item' => $row['Product']['name'],
            ];
            $RequisitionModel->updateRequisition($data);
        } else {
            $data = [
                'Requisition' => [
                    'status' => $RequisitionModel::STATUS_ACCEPTED,
                    'order_id' => 0, // Required for autonumber
                    'store_id' => $storeId,
                    'order_type' => $orderType, // Required
                    'type' => $requisitionType, // Required for autonumber
                ],
                'RequisitionItem' => [
                    [
                        'product_id' => $row['Product']['id'],
                        'quantity' => abs($new_stock),
                        'price' => $row['Product']['buy_price'],
                        'item' => $row['Product']['name'],
                    ],
                ],
            ];
            $requisition = $RequisitionModel->addRequisition($data);

            $jsonData[$requisitionType == Requisition::TYPE_INBOUND ? 'inbound_requisition_' . $storeId : 'outbound_requisition_' . $storeId] = $requisition['data']['Requisition']['id'];
            $entityAppData->id = $currentRequisition['EntityAppData']['id'];
            $entityAppData->saveField('data', json_encode($jsonData));
            $requisitionId = $requisition['data']['Requisition']['id'];
        }
        return $requisitionId;
    }

	function after_import_save($mydata){
        if(isset($mydata['Product']['buy_price']) && !isset($mydata['Product']['average_price'])) {
            $sql = "UPDATE products set average_price = buy_price where id = {$this->id} and 0 = (select count(*) from stock_transactions where product_id = {$this->id})";
            $this->query($sql);
        }

		if(!empty($mydata['Product']['category'])){
            $cats = explode(',', $mydata['Product']['category']);
            $this->Category=GetObjectOrLoadModel('Category');
            $this->ItemsCategory=GetObjectOrLoadModel('ItemsCategory');
            $this->ItemsCategory->deleteAll([ 'item_id' => $this->id,
                'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT,]);
            foreach ($cats as $cat) {

                $cat_id=$this->Category->findOrCreate($cat,Category::CATEGORY_TYPE_PRODUCT);
                if(!$this->ItemsCategory->hasAny(array('item_id'=>$this->id,'category_id'=>$cat_id,'item_type'=>ItemsCategory::ITEM_TYPE_PRODUCT))){
                    $this->ItemsCategory->create();
                    $obj['ItemsCategory'] = [
                        'item_id' => $this->id,
                        'item_type' => ItemsCategory::ITEM_TYPE_PRODUCT,
                        'category_id' => $cat_id
                    ];
                    $temp[] =$obj;
                    $this->ItemsCategory->save($obj);
                }
            }
		}
        if (isset($mydata['Product']['id'])) {
            $this->add_actionline(ACTION_EDIT_PRODUCT, array('primary_id' => $mydata['Product']['id'], 'secondary_id' => $mydata['Product']['id'], 'param4' => $mydata['Product']['name']));
        }
	}
    function has_transactions ($product_id )
    {
        $Transaction = GetObjectOrLoadModel ('StockTransaction') ;
        $StocktakingRecord = GetObjectOrLoadModel('StocktakingRecord');
        $stocktaking = $StocktakingRecord->find('first', ['conditions' => ['product_id' => $product_id]]);
        $tt = $Transaction->find ( 'first' , ['recursive' => -1 , 'conditions' => [
            'StockTransaction.product_id' => $product_id,
            'StockTransaction.ignored_date IS NULL'
        ] ]);
        return (!empty ( $tt ) || !empty($stocktaking));
    }
    function has_stock_taking_transactions ($product_id )
    {
        $StocktakingRecord = GetObjectOrLoadModel('StocktakingRecord');
        $stocktaking = $StocktakingRecord->find('first', ['recursive' => -1 ,'conditions' => ['product_id' => $product_id]]);
        return  !empty($stocktaking['StocktakingRecord']['stocktaking_id'])?$stocktaking['StocktakingRecord']['stocktaking_id']:false;
    }

    function delete_all_transactions  ( $product_id ) {
        $this->loadModel('StockTransaction');
        $transactions = $this->StockTransaction->find ( 'all' , ['recursive' => -1 ,'conditions' => ['StockTransaction.product_id' => $product_id ] ]);
        foreach ( $transactions as $t ) {
            StockTransaction::removeTransaction($t['StockTransaction']);
        }
        $this->loadModel('StoreStockBalance');
        $this->StoreStockBalance->deleteAll(['StoreStockBalance.product_id' => $product_id]);
        return true ;
    }
    function get_bundle_cost ( $product_data ) {
        $total_cost = 0 ;
        // warning suppress
        if (is_array($product_data) && array_key_exists('ProductBundle', $product_data))
        foreach ( $product_data['ProductBundle'] as $d ) {
            $p = $this->find('first' , [ 'recursive' => -1 , 'conditions' => ['Product.id' => $d['product_id']] ]) ;
            if($p['Product']['type'] == self::SERVICE_TYPE) {
                $price = $p['Product']['buy_price'];
            } else {
                $price = $p['Product']['average_price'];
            }
            $total_cost += ( (float) $price * (float) $d['quantity'] );
        }
        return $total_cost  ;
    }
    function get_default_factor ( $product ){
        if (! is_array($product)){
            $product = $this->find('first' , ['recursive' => -1 , 'conditions' => ['id' => $product] ]);
        }
        if ( empty($product['Product']['unit_template_id']))
        {
            return NULL ;
        }
        $this->loadModel('UnitTemplate');
        $this->loadModel('UnitFactor');
        $default_display_unit = settings::getValue(InventoryPlugin,'default_display_unit');

        // Cache Unit Templates for the current request for other products use
        $unit_templates_configure_key = getCurrentSite('id') . '_UnitTemplates_';
        $cachedUnitTemplates = Configure::read($unit_templates_configure_key);
        if ($cachedUnitTemplates === null) {
            $allUnitTemplatesFromDb = $this->UnitTemplate->find('all', [
                'recursive' => -1,
                'applyBranchFind' => false,
                'fields' => ['UnitTemplate.id', 'UnitTemplate.main_unit_name', 'UnitTemplate.unit_small_name']
            ]);

            $unitTemplates = [];
            foreach ($allUnitTemplatesFromDb as $unitTemplate) {
                $unitTemplates[$unitTemplate['UnitTemplate']['id']] = $unitTemplate;
            }
            $cachedUnitTemplates = $unitTemplates;
            Configure::write($unit_templates_configure_key, $cachedUnitTemplates);
        }


        switch ($default_display_unit){
            case settings::OPTION_UNIT_DISPLAY_DEFAULT:
                $unit_factor_id = 0;
                break;
            case settings::OPTION_UNIT_DISPLAY_SELL:
                $unit_factor_id = $product['Product']['default_retail_factor_id'];
                break;
            case settings::OPTION_UNIT_DISPLAY_BUY:
                $unit_factor_id = $product['Product']['default_buy_factor_id'];
                break;
            default:
                $unit_factor_id = 0 ;
        }
        $returned_factor = [] ;
        if ( $unit_factor_id == 0 ) {
            $ut = $cachedUnitTemplates[$product['Product']['unit_template_id']];
            $returned_factor['factor'] = 1;
            $returned_factor['factor_name'] = $ut['UnitTemplate']['main_unit_name'];
            $returned_factor['factor_small_name'] = $ut['UnitTemplate']['unit_small_name'];
        }else {
            $uf = $this->UnitFactor->find('first' , ['recursive'=>-1 , 'conditions' => ['id' =>$unit_factor_id ] ]);
            $returned_factor['factor'] = $uf['UnitFactor']['factor'];
            $returned_factor['factor_name'] = $uf['UnitFactor']['factor_name'];
            $returned_factor['factor_small_name'] = $uf['UnitFactor']['small_name'];
        }
        $returned_factor ['id'] = $unit_factor_id ;
        return $returned_factor ;
    }

	function beforeDelete($cascade = true)
	{
		App::import('Vendor', 'notification_2');
		NotificationV2::delete_notificationbyref($this->id, NotificationV2::Product_Low_Stock);
		return true;
	}

	function getTypeCounts()
    {
        $count[self::PRODUCT_TYPE] = $this->getTypeCount(self::PRODUCT_TYPE, true);
        $count[self::BUNDLE_TYPE] = $this->getTypeCount(self::BUNDLE_TYPE);
        $count[self::SERVICE_TYPE] = $this->getTypeCount(self::SERVICE_TYPE);
        return $count;
    }

	function getTypeCount($type, $null = false)
    {
        if($null)
        {
            $conditions['OR'] = ['Product.type' => [$type, 0], 'Product.type IS NULL',];
        }else{
            $conditions = ['Product.type' => $type];
        }

        $cc=$this->find('count', ['conditions' => $conditions]);;

        return $cc;

    }

    function getBookingCounts()
    {
        return $this->find('count', ['conditions' => ['Product.type' => self::SERVICE_TYPE, 'Product.duration_minutes > 0' ]]);
    }
    function paginateCount($conditions = null, $recursive = 0, $extra = array())
    {
        $db = $this->getDataSource();
        if (ifPluginActive(BranchesPlugin) && empty(settings::getValue(BranchesPlugin, 'share_products'))) {
            $conditions ['Product.branch_id'] = getCurrentBranchID();
        }

        $query_contents = array_merge([
            'fields' => ['Product.*', 'count(Product.id) as cc'],
            'table' => $db->fullTableName($this),
            'alias' => 'Product',
            'recursive' => $recursive,
            'conditions' => $conditions
        ], $extra);

        if ($this->checkFilterContainsCustomFields($conditions)) {
            $query_contents['joins'][] =  [
                'table' => 'products_custom_data',
                'alias' => 'CustomModel',
                'type' => 'INNER',
                'conditions' => [
                    'Product.id = CustomModel.product_id'
                ]
            ];
        }

        $subQuery = $db->buildStatement($query_contents, $this);


        $query = 'SELECT COUNT(*) AS `count` FROM (' . $subQuery . ') AS `subQuery`';

        $result = $db->query($query);
        return $result[0][0]['count'];
    }

    private function checkFilterContainsCustomFields($conditions){
        $keyToCheck = "CustomModel";
        $keyFound = false;

        foreach ($conditions as $key => $value) {
            if (strpos($key, $keyToCheck) !== false) {
                $keyFound = true;
                break;
            }
        }
        return $keyFound;
    }

    /**
     * @param $products
     * @param $prod
     * @param $oldData is edit data so if tracking item qty is 0 and we edit its deduct transaction
     * the tracking numbers of this transactions will come
     */
    public function handleTrackingNumber($prod, $oldData = [])
    {
        $transactionProductQty = [];
        if(!empty($oldData)) {
            foreach($oldData as $item) {
                $transactionProductQty[$item['product_id']] = $item['quantity'];
            }
        }
        if (ifPluginActive(ProductTracking)) {
            $serials = [];
            $lots = [];
            $expiry_dates = [];
            $quantities = [];
//
            foreach ($prod['TrackingNumber'] as $trackingData) {
                // This to prevent adding 0 quantity items into the list
                if(isset($transactionProductQty[$prod['Product']['id']])) {
                    $oldQty = $transactionProductQty[$prod['Product']['id']];
                } else {
                    $oldQty = 0;
                }
                $sumQty = (is_numeric($oldQty) && is_numeric($trackingData['quantity'])) ? ($trackingData['quantity'] + $oldQty) : 0; //php8 fix
                if($sumQty <= 0) continue;
                if (!isset($trackingData['quantity']))
                    continue;
                if (isset($trackingData['serial'])) {
                    $serials[] = $trackingData['serial'];
                }
                if (isset($trackingData['lot'])) {
                    $lots[] = $trackingData['lot'];
                }
                if (isset($trackingData['expiry_date'])) {
                    $expiry_dates[] = $trackingData['expiry_date'];
                }
                if (isset($trackingData['quantity'])) {
                    $quantities[] = $trackingData['quantity'];
                }
            }

            $prod['Product']['serials'] = $serials;
            $prod['Product']['lots'] = $lots;
            $prod['Product']['expiry_dates'] = $expiry_dates;
            $prod['Product']['quantities'] = $quantities;
        }
        return $prod;
    }

    public static function displayTrackingData($trackingType, $trackingData)
    {
        $temp = json_decode($trackingData, true);
        if((json_last_error() == JSON_ERROR_NONE))
        {
            $trackingData = $temp;
        }
        switch ($trackingType)
        {
            case TrackStockUtil::TYPE_SERIAL:
                return implode(', ', $trackingData['serial'] ?? []);
            case TrackStockUtil::TYPE_LOT:
                return $trackingData['lot'];
            case TrackStockUtil::TYPE_EXPIRY:
                return format_date($trackingData['expiry_date']);
            case TrackStockUtil::TYPE_LOT_EXPIRY:
                return isset($trackingData['lot']) ? $trackingData['lot'] . '-' . format_date($trackingData['expiry_date']) : format_date($trackingData['expiry_date']);
            default:
                return '';
        }
    }

    public function getFeatured()
    {

        $conditions = [
            [ 'is_featured' => 1 ],
            [ 'availabe_online' => 1 ],
            [ 'status' => ProductStatusUtil::STATUS_ACTIVE ],
        ];
        if (ifPluginActive(BranchesPlugin)  && empty(settings::getValue(BranchesPlugin, 'share_products'))) {
            $conditions ['Product.branch_id'] = getBranchForShopFront();
        }
        $this->loadModel('ItemsCategory');
        $this->bindModel(
            [
                'hasAndBelongsToMany' =>
                [
                    'Category' =>
                    [
                        'className' => 'Category',
                        'joinTable' => 'items_categories',
                        'foreignKey' => 'item_id',
                        'associationForeignKey' => 'category_id',
                        'conditions' => ['item_type' => ItemsCategory::ITEM_TYPE_PRODUCT, 'category_type' => Category::CATEGORY_TYPE_PRODUCT]
                    ]
                ]
            ]
        );
        $data = $this->find('all', [ 'conditions' => $conditions, 'group' => 'Product.id']);
        $processed_data = [];
        $client = getAuthClient();
        if(!empty($client)!=0){
            $group_price_id=intval(getAuthClient('group_price_id'));
        }else{
            $group_price_id=0;
        };
        $processed_data['items'] = [];
        foreach($data as $k => $v){
            $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault('product',$v['Product']['id']);
            if(count( $defaultS3Images)) {
                $v['ProductMasterImage']['file_full_path'] = $defaultS3Images[0]->files->path;
            }

            $processed_data['items'][$k]['name'] = $v['Product']['name'];
            $processed_data['items'][$k]['unit_price'] = $v['Product']['unit_price'];

            if (!empty($v['ProductPrice'])) {
                $priceList = current(array_filter($v['ProductPrice'], function ($item) use ($group_price_id) {
                    return $item['group_price_id'] == $group_price_id;
                }));
                if (!empty($priceList)) {
                    $processed_data['items'][$k]['unit_price'] = $priceList['price'];
                }
            }
            $unitPrice = $processed_data['items'][$k]['unit_price'];
            $processed_data['items'][$k]['discout_type'] = $v['Product']['discout_type'];
            $processed_data['items'][$k]['discount'] = $v['Product']['discount'];
            $processed_data['items'][$k]['id'] = $v['Product']['id'];
            $processed_data['items'][$k]['description'] = $v['Product']['description'];
            $processed_data['items'][$k]['ProductMasterImage'] = $v['ProductMasterImage'];
            $processed_data['items'][$k]['brand'] = $v['Product']['brand'];
            $processed_data['items'][$k]['stock_balance'] = $v['Product']['stock_balance'];
            $processed_data['items'][$k]['product_code'] = $v['Product']['product_code'];
            if (count($v['Category'])) {
                foreach ($v['Category'] as $category) {
                    $processed_data['items'][$k]['category'] .= ($category['name'] . ',');
                }
            }

            if (!empty($v['Product']['discount'])) {
                if ($v['Product']['discout_type'] == 2) {
                    $finalPrice = $unitPrice - $v['Product']['discount'];
                } else {
                    $finalPrice =  $unitPrice - (($v['Product']['discount']/100) * $unitPrice);
                }
                $processed_data['items'][$k]['final_price_number'] = $finalPrice;
                $processed_data['items'][$k]['final_price'] = format_price($finalPrice);
            } else {
                $processed_data['items'][$k]['final_price'] = format_price($unitPrice);
                $processed_data['items'][$k]['final_price_number'] =$unitPrice;
            }
            $processed_data['items'][$k]['unit_price'] = format_price($unitPrice);
            $processed_data['items'][$k]['file_full_path'] = $v['ProductMasterImage']['file_full_path'] ? $v['ProductMasterImage']['file_full_path'] : CDN_URL.'templates/shop_1/img/product_img.svg';
            unset( $processed_data['items'][$k]['ProductMasterImage']);
        }
        $processed_data['itemsCount'] = count($processed_data['items']);
        return $processed_data;
    }

    /**
     * Returns an array of Product Images
     * product - product object returned from $this->find->first
     */
    public static function getProductImages($product){
        $product_images = [];
        if($product['ProductMasterImage']){
            $product_images[] = $product['ProductMasterImage']['file_full_path'];
        }
        foreach($product['ProductImage'] as $product_image){
            if(!in_array($product_image['file_full_path'], $product_images)){
                $product_images[] = $product_image['file_full_path'];
            }
        }
        return array_filter($product_images);
    }

    /**
     * Returns the product object and added to it final_price (after_discount) and has_discount
     * product - product object returned from $this->find->first
     */
    public static function getProductWithDiscount($product){
        $product['Product']['has_discount'] = false;
        switch ($product['Product']['discout_type']){
            case Product::DISCOUNT_TYPE_PERCENTAGE:
                // Percentage Discount
                $product['Product']['final_price'] = $product['Product']['unit_price'] - (($product['Product']['discount']/100) * $product['Product']['unit_price']);
                break;
            case Product::DISCOUNT_TYPE_VALUE:
                // Amount Discount
                $product['Product']['final_price'] = $product['Product']['unit_price'] - $product['Product']['discount'];
                break;
            default:
                $product['Product']['final_price'] = $product['Product']['unit_price'];
                break;
        }
        if($product['Product']['final_price'] != $product['Product']['unit_price']) {
            $product['Product']['has_discount'] = true;
        }

        return $product;
    }

    public static function ProductTypes()
    {
        return [
            self::BUNDLE_TYPE=>'Bundle',
            self::PRODUCT_TYPE=>'Product',
            self::SERVICE_TYPE=>'Service'
        ];
    }

    private function isProductLinkedToCommissionRule($product_id){
        $this->loadModel('CommissionRuleSubItem');
        $data = $this->CommissionRuleSubItem->find('first',
            ['conditions' =>
                [
                    'CommissionRuleSubItem.item_type' => 'App\Models\Product',
                    'CommissionRuleSubItem.item_id' => $product_id
                ]
            ]);
        if (isset($data['CommissionRuleItem'])){
            $commission_rule_id = $data['CommissionRuleItem']['commission_rule_id'];
            $validationError = sprintf(__('You cannot delete the item or category as it\'s already in use in the Commission Rule %s', true), "<a href='/v2/owner/commission_rules/$commission_rule_id'>#$commission_rule_id");
            throw new Exception($validationError);
        }
    }

    public function getSalesAccount($id)
    {
        $this->loadModel('JournalAccountRoute');
        $account=$this->JournalAccountRoute->getAccountForForm('product_sales',$id);

        return $account['JournalAccountRoute']['account_id'];
    }

    public function getSalesCostAccount($id)
    {
        $this->loadModel('JournalAccountRoute');
        $account=$this->JournalAccountRoute->getAccountForForm('product_cost_sales',$id);

        return $account['JournalAccountRoute']['account_id'];
    }

    public function canProductBeDeleted($product_id)
    {
        if ($this->isProductLinkedToShippingOptions($product_id)) {
            throw new Exception(__('You cannot delete the service that has already been selected in the <a href="/v2/owner/shipping_options/settings">cash on delivery fee settings</a>.', true));
        }

        if (ifPluginActive(COMMISSION_PLUGIN)) {
            $this->isProductLinkedToCommissionRule($product_id);
        }

        if(ifPluginActive(PURCHASE_CYCLE_PLUGIN)) {
            
            $this->isProductLinkedToPurchaseRequest($product_id);

            $this->isProductLinkedToQuotationRequest($product_id);

            //Is linked to Purchase Quotation OR Purchase Order
            $this->isProductLinkedToPurchaseOrder($product_id);

        }

        if(ifPluginActive(MANUFACTURING_PLUGIN)) {
            $this->isProductLinkedToBom($product_id);
            $this->isProductLinkedToProductionPlan($product_id);
        }

		$this->isProductLinkedToStockRequest($product_id);

		$this->isProductLinkedToInvoice($product_id);

        $this->isDefaultServiceInBooking($product_id);

    }

    /**
     * Check if the product is linked to the COD service option
     * to prevent the product from being deleted, suspended, and deactivated.
     *
     * @param int|array $productId
     *
     * @return bool
     * @see https://izam.atlassian.net/browse/ID-8012
     */
    public function isProductLinkedToShippingOptions(int|array $productId): bool
    {
        $this->loadModel('Setting');

        $data = settings::getValue(InvoicesPlugin, 'shipping_options.default_cod_product_id');

        if ($data == $productId) {
            return true;
        }

        return false;
    }

    private function isProductLinkedToPurchaseOrder($product_id){
        $this->loadModel('PurchaseOrderItem');
        $data = $this->PurchaseOrderItem->find('first',
            ['conditions' =>
                [
                   'PurchaseOrderItem.product_id' => $product_id
                ]
            ]);
        if (isset($data['PurchaseOrderItem'])){
            $purchase_order_id = $data['PurchaseOrderItem']['purchase_order_id'];
            $this->loadModel('PurchaseOrder');
            $purchase_order = $this->PurchaseOrder->find('first', ['conditions' => [
                'PurchaseOrder.id' => $purchase_order_id
            ]]);
         

            if($purchase_order && in_array($purchase_order['PurchaseOrder']['type'], [PurchaseOrder::PURCHASE_ORDER, PurchaseOrder::PURCHASE_QUOTATION, PurchaseOrder::PURCHASE_INVOICE])) {

                $message = "You cannot delete the Items as it’s already selected in the the %s <a target='_blank' href='%s'>#%s</a>";

                if($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_QUOTATION) {
                    $resolved_message = sprintf(__($message, true), __("Purchase Quotation", true), '/v2/owner/entity/'.EntityKeyTypesUtil::PURCHASE_QUOTATION.'/'.$purchase_order_id.'/show' , $purchase_order['PurchaseOrder']['no']);
                }
                else if($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_ORDER) {  
                    $resolved_message = sprintf(__($message, true), __("Purchase Order", true), '/v2/owner/entity/'.EntityKeyTypesUtil::PURCHASE_ORDER.'/'.$purchase_order_id.'/show'  , $purchase_order['PurchaseOrder']['no']);
                } elseif($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_INVOICE) {
                    $resolved_message = sprintf(__($message, true), __("Purchase Invoice", true), '/owner/purchase_invoices/view/'. $purchase_order_id .'?reset=1', $purchase_order['PurchaseOrder']['no']);
                }

                throw new Exception($resolved_message);
            }

        }
    }

    private function isProductLinkedToPurchaseRequest($product_id) {
        $this->loadModel('PurchaseRequestItem');
        $data = $this->PurchaseRequestItem->find('first',
            ['conditions' =>
                [
                   'PurchaseRequestItem.product_id' => $product_id
                ]
            ]);

        if (isset($data['PurchaseRequestItem'])) {
            $this->loadModel('PurchaseRequest');
            $pid = $data['PurchaseRequestItem']['reference_id'];
            $purchase_request = $this->PurchaseRequest->find('first',
            ['conditions' =>
                [
                   'PurchaseRequest.id' => $pid
                ]
            ]);

            $message = "You cannot delete the Items as it’s already selected in the the %s <a target='_blank' href='%s'>#%s</a>";
            $resolved_message = sprintf(__($message, true), __("Purchase Request", true), '/v2/owner/entity/'.EntityKeyTypesUtil::PURCHASE_REQUEST.'/'.$pid.'/show'  , $purchase_request['PurchaseRequest']['code']);
            throw new Exception($resolved_message);
        }

    }

		private function isProductLinkedToInvoice($product_id) {
			$this->loadModel('InvoiceItem');
			$data = $this->InvoiceItem->find('first', ['conditions' => ['InvoiceItem.product_id' => $product_id]]);

			if (empty($data)) {
				return false;
			}
            $this->loadModel('Invoice');
            $invoiceType = $this->Invoice->InvoiceTypesMetaData[$data['Invoice']['type']];
            $routeAction = Router::url(['controller' => $invoiceType['url']['controller'], 'action' => $invoiceType['url']['view_action'], $data['InvoiceItem']['invoice_id']]);

            $message = "You cannot delete the items as it’s already selected in the %s <a target='_blank' href='%s'>#%s</a>";
            $resolved_message = sprintf(__($message, true), __($invoiceType['url']['name_action'], true), $routeAction, $data['Invoice']['no']);
			throw new Exception($resolved_message);
		}

    private function isProductLinkedToQuotationRequest($product_id){
        $this->loadModel('QuotationRequestItem');
        $data = $this->QuotationRequestItem->find('first',
            ['conditions' =>
                [
                   'QuotationRequestItem.product_id' => $product_id
                ]
            ]);
        if (isset($data['QuotationRequestItem'])){
            $qid = $data['QuotationRequestItem']['reference_id'];
            $this->loadModel('QuotationRequest');
            $quotation_request = $this->QuotationRequest->find('first', ['conditions' => [
                'QuotationRequest.id' => $qid
            ]]);
         
            $message = "You cannot delete the Items as it’s already selected in the the %s <a target='_blank' href='%s'>#%s</a>";
            $resolved_message = sprintf(__($message, true), __("Quotation Request", true), '/v2/owner/entity/'.EntityKeyTypesUtil::QUOTATION_REQUEST.'/'.$qid.'/show'  , $quotation_request['QuotationRequest']['code']);
            throw new Exception($resolved_message);

        }
    }

    public function isRelatedToPackage($id)
    {
        $conditions = [
            "Product.id" => $id,
            "Product.source_type" => "package",
            "Product.source_id is not null"
        ];
        $product = $this->find($conditions);
        if (!$product) {
            return false;
        }
        $this->loadModel('Package');
        $packageConditions = [
            "Package.id" => $product['Product']['source_id'],
            "Package.product_id" => $id,
            "Package.deleted_at is null"
        ];
        $package = $this->Package->find($packageConditions);
        if (!$package) {
            return false;
        }
        return true;
    }

    public function getProductSellPrice($id) {
    	return $this->findById($id)['Product']['unit_price'];
    }

    function afterSave($created)
    {
        setLastUpdatedAt(0, SettingsUtil::PRODUCTS_LAST_UPDATED_AT);
    }

    function afterDelete()
    {
        //$this->deleteRelated($this->id);
        setLastUpdatedAt(0, SettingsUtil::PRODUCTS_LAST_UPDATED_AT);
    }

	public function isProductTrackingTypeQuantityOnly($product_id) {
		$product = $this->findById($product_id);
		if (!$product) {
			return false;
		}
		if ($product['Product']['tracking_type'] !== 'quantity_only') {
			return false;
		}
		return true;
	}


    public function getBarcodeListByProduct($product) {
        $barcodes = [];
        $this->loadModel('ItemBarcode');
        $barcodeList = $this->ItemBarcode->find('all', ['conditions' => ['product_id' => $product['Product']['id']]]);
        $unitTemplate = $product['UnitTemplate'];
        $this->loadModel('UnitTemplate');
        $this->loadModel('UnitFactor');
        $factors = $this->UnitFactor->find('all' , ['recursive' => -1]);
        $factors_list = []  ;
        foreach ( $factors as $f ) {
            $factors_list[$f['UnitFactor']['unit_template_id']][$f['UnitFactor']['id']] = $f['UnitFactor'];
        }
        $unit_templates = $this->UnitTemplate->find('all' , ['recursive' => -1 ]); 
        foreach ($unit_templates as  $t ){
            $factors_list[$t['UnitTemplate']['id']][0] = ['id' => 0 , 'factor_name' => $t['UnitTemplate']['main_unit_name'], 'small_name' => $t['UnitTemplate']['unit_small_name'], 'factor' => 1 ];
        }

        if(!empty($barcodeList)) {
            $barcodes[] = __('Main Barcode', true);
            foreach($barcodeList as $barcode) {
                $small_name = $factors_list[$product['Product']['unit_template_id']][$barcode['ItemBarcode']['unit_factor_id']]['small_name'];
                $title = $small_name;
                $barcodes[$barcode['ItemBarcode']['id']] = $title; 
            }
        }

        return $barcodes;
    }

    public static function getBundleTypes() {
        GetObjectOrLoadModel('Setting');
        return [
            settings::OPTION_BUNDLE_PACK => __('Pack', true),
            settings::OPTION_BUNDLE_COMPOUND => __('Compound', true)
        ];
    }

    /**
     * @param $product product to check its bundle type
     * @param $systemBundleType system bundle type
     * @param $compareValue the value to compare it to
     * @return bool
     * checks if product or system bundle type equal to a give value
     */
    public static function checkProductBundleTypeOrSystem($product, $systemBundleType, $compareValue) {
        return ($product['Product']['bundle_type'] == $compareValue || (is_null($product['Product']['bundle_type']) && $systemBundleType == $compareValue));
    }

    public static function checkProductBundleTypePack($product) {
        $bundle_type = settings::getValue(InventoryPlugin, 'bundle_type');
        return self::checkProductBundleTypeOrSystem($product, $bundle_type, settings::OPTION_BUNDLE_PACK);
    }

    public static function checkProductBundleTypeCompound($product) {
        $bundle_type = settings::getValue(InventoryPlugin, 'bundle_type');
        return self::checkProductBundleTypeOrSystem($product, $bundle_type, settings::OPTION_BUNDLE_COMPOUND);
    }

    function getItemGroup($itemGroupId)
    {
        $this->loadModel('ItemGroup') ;
        return $this->ItemGroup->find('first' ,  ['conditions' =>['id' => $itemGroupId ]]);
    }

    function getAttributes($product_id)
    {
        $this->loadModel('ProductAttributeOption') ;
        return $this->ProductAttributeOption->find('all' ,  ['conditions' =>['product_id' => $product_id ]]);
    }

    private function isProductLinkedToBom($product_id){
        
        $productsIds = [];
        if(is_array($product_id)){
            $productsIds = $product_id;
        }else{
            $productsIds[] = $product_id;
        }
        $bomService = resolve(BomService::class);
        foreach($productsIds as $productId){
            $relatedBom = $bomService->getBomRelatedToProduct($productId);
            if($relatedBom){
                $message = "You cannot delete the Items as it’s already selected in the the %s <a target='_blank' href='%s'>#%s</a>";
                $resolved_message = sprintf(__($message, true), __("Bill of Material", true), '/v2/owner/entity/'.EntityKeyTypesUtil::BOM_ENTITY_KEY.'/'.$relatedBom->id.'/show'  , $relatedBom->code);
                throw new Exception($resolved_message);
            }
        }
    }
    private function isProductLinkedToProductionPlan($product_id) {
        $this->loadModel('ProductionPlanItem');
        $productionPLanItem = $this->ProductionPlanItem->find('first', ['conditions' => ['ProductionPlanItem.product_id' => $product_id]]);
        if (empty($productionPLanItem)) {
            return false;
        }
        $routeAction = '/v2/owner/entity/production_plan/'. $productionPLanItem['ProductionPlan']['id'] . '/show';

        $message = "You cannot delete the product because it’s already selected in %s <a target='_blank' href='%s'>#%s</a>";
        $resolved_message = sprintf(__($message, true), __("Production Plan", true), $routeAction, $productionPLanItem['ProductionPlan']['code']);
        throw new Exception($resolved_message);
    }

    private function isProductLinkedToStockRequest($product_id) {
        $this->loadModel('StockRequestItem');
        $stockRequestItem = $this->StockRequestItem->find('first', ['conditions' => ['StockRequestItem.product_id' => $product_id]]);
        if (empty($stockRequestItem)) {
            return false;
        }
        $routeAction = '/v2/owner/entity/stock_request/'. $stockRequestItem['StockRequestItem']['id'] . '/show';

        $message = "You cannot delete the product because it’s already selected in %s <a target='_blank' href='%s'>#%s</a>";
        $resolved_message = sprintf(__($message, true), __("Stock Request", true), $routeAction, $stockRequestItem['StockRequest']['code']);
        throw new Exception($resolved_message);
    }

    public function getProductPriceListsById($product_id) {
        $this->loadModel('ProductPrice');

        $productPrices = $this->ProductPrice->find('all', [
            'conditions' => ['ProductPrice.product_id' => $product_id],
            'recursive' => -1,
        ]);

        $result = [];

        foreach ($productPrices as $row) {
            $listId = $row['ProductPrice']['group_price_id'];
            $priceValue = $row['ProductPrice']['price'];
            $result[$listId] = $priceValue;
        }

        return $result;
    }


    public function isDefaultServiceInBooking($product_id) {
        if(settings::getValue(PluginUtil::NEW_BOOKING_PLUGIN, 'default_booking_service') == $product_id){
            $message = "You cannot delete, deactivate, or suspend a service that has already been selected as the default in the booking system";
            throw new Exception($message);
        }
    }

}
