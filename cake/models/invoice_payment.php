<?php

use App\Transformers\ServiceModelDataTransformer;
use Izam\Daftra\Common\Utils\JournalUtil;
use Izam\Daftra\Invoice\Services\InvoicePaymentService;
use App\Helpers\CurrencyHelper;

define('PAYMENT_STATUS_NOT_COMPLETED', 0);
define('PAYMENT_STATUS_COMPLETED', 1);
define('PAYMENT_STATUS_PENDING', 2);
define('PAYMENT_STATUS_FAILED', 3);
define('PAYMENT_STATUS_OVERPAID', 4);
define('PAYMENT_STATUS_DRAFT', 5);

define('PAYMENT_CUSTOM_CATEGORY', 'CUSTOM');
define('PAYMENT_MANUALLY_ADDED_CATEGORY', 'MANUALLY_ADDED');
define('PAYMENT_ONLINE_CATEGORY', 'ONLINE');

class InvoicePayment extends AppModel {
    var $applyBranch = ['onFind' => true, 'onSave' => true];
    var $name = 'InvoicePayment';
    var $filters = array(); 
    var $actsAs = array(
        'file' => array(
            'attachment' => array('required' => false, 'max_file_size' => DOCUMENT_SIZE_MB, 'max_file_size_kb' => DOCUMENT_SIZE, 'extensions' => array('pdf', 'doc', 'docx', 'jpg', 'png', 'gif', 'tif', 'zip', 'rar', 'tar', 'gz'))
        ),
        'journal' => array(
            'is_journal'=>true,
            'entity_type'=>['invoice_payment','invoice_payment_currency_rate_difference']
        ),
    );
    public static $default_payments_gatways = false;
    var $belongsTo = array('Staff' => array('className' => 'Staff', 'foreignKey' => 'staff_id')
        , 'Invoice' => array('className' => 'Invoice', 'foreignKey' => 'invoice_id')
        , 'Client' => array('className' => 'Client', 'foreignKey' => 'client_id')
        , 'Treasury' => array('className' => 'Treasury', 'foreignKey' => 'treasury_id')
        
        
    );
    //var $hasOne=array('Staff');

    /**
     * @var Invoice
     */
    var $Invoice;

    function __construct($id = false, $table = null, $ds = null) {
        
        parent::__construct($id, $table, $ds);
        $required = array('rule' => 'notEmpty', 'message' => __('Required', true), 'required' => true);
        $this->validate = array(
            'email' => array($required, array('rule' => 'email', 'message' => __('Valid email required', true))),
//            'first_name' => $required,
//            'last_name' => $required,
//            'address1' => $required,
//            'city' => $required,
//            'state' => $required,
//            'country_code' => $required,
//            'phone1' => $required,
			'date' => array(
				'validateIsOpenedPeriod' => ['rule' => 'validateIsOpenedPeriod', 'message' => __('You can not add, edit, or delete a transaction in this date %s within a closed period', true)],
				),
            'payment_method' => array('rule' => 'checkMethod', 'message' => __('Payment method is not supported', true)),
            'terms_agree' => array('rule' => array('comparison', '>', 0), 'message' => __('You must agree to terms and conditions', true)),
            //'transaction_id' => array('required' => true, 'rule' => 'offlineRequired', 'message' => __('Required', true)),
            //'transaction_type' => array('required' => true, 'rule' => 'offlineRequired', 'message' => __('Required', true)),
            'notes' => array('rule' => array('maxlength', 500), 'message' => __('Only 500 characters allowed', true), 'allowEmpty' => true)
        );
    }

    function beforeValidate($options = array())
    {
        /* as discussed with m.azzam */
        if (!in_array($this->data['InvoicePayment']['payment_method'], ['paypal', 'stripe'])) {
            unset($this->validate['email']);
        }
    }

    function beforeSave($options = array()) {

        parent::beforeSave($options);
	
        $this->loadModel ('Treasury') ;
        $this->loadModel ('SitePaymentGateway') ;
        // ShortCut to $this->data[$this->alias]; & reference to it
        $data=&$this->data;
        // if payment add by client
        if(isset($data[$this->alias]['added_by']) && ($data[$this->alias]['added_by']==0 or $data[$this->alias]['staff_id']==-1) && !empty($data[$this->alias]['payment_method']) ){
         $data[$this->alias]['treasury_id'] = $this->SitePaymentGateway->getPaymentTreasury($data[$this->alias]['payment_method']);
        }
        // this will work only if payment add by owner and no treasury were selected or SitePaymentGateway->getPaymentTreasury return false
        if  (empty($data[$this->alias]['treasury_id']))
        {
            $this->loadModel('ItemPermission');
            $tt = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY, ItemPermission::PERMISSION_DEPOSIT);
            $pmt = $this->SitePaymentGateway->getPaymentTreasury($data[$this->alias]['payment_method']);
            $staffDefaultTreasury = NULL;
            if (getAuthStaff() && !empty(getAuthStaff('default_treasury_id'))) {
                $staffDefaultTreasury = getAuthStaff('default_treasury_id');
            }
            if ($pmt && isset($tt[$pmt])) {
                $data[$this->alias]['treasury_id'] = $pmt;
            } elseif ($staffDefaultTreasury && isset($tt[$staffDefaultTreasury])) {
                $data[$this->alias]['treasury_id'] = $staffDefaultTreasury;
            } elseif (getAuthStaff() && getAuthStaff('id') != -1 && !empty($tt)) {
                $data[$this->alias]['treasury_id'] = array_key_first($tt);
            } else {
                $data[$this->alias]['treasury_id'] = $this->Treasury->get_primary();
            }
        }

        if  (empty($data[$this->alias]['date']))
        {
            $data[$this->alias]['date'] = date('Y-m-d');
        }
       
        if ( empty($data[$this->alias]['staff_id']) ){
            $data[$this->alias]['staff_id'] = 0;
        }
        // dirty quick fix missing currency_code in invoice payment
        if ( empty($data[$this->alias]['currency_code']) && empty($data[$this->alias]['id']) && !empty($data[$this->alias]['invoice_id']) ){

            // please note that following query only select currency_code form invoice not everything ....
            $invoice_currency_code=$this->Invoice->find('first',array('fields'=>'Invoice.currency_code','recursive'=>-1,'conditions'=>array('Invoice.id'=>$data[$this->alias]['invoice_id'])));
            $data[$this->alias]['currency_code'] = $invoice_currency_code['Invoice']['currency_code'];

        }
        Cache::delete('statement_data_' . $data[$this->alias]['client_id'] . '_' . getCurrentSite('id'));
        return true ;
    }
function getSortFields($sorted_by) {
		
        return array(
		array('title' => __('Payment',true),'field' => 'id' ),
		array('title' => __('Created Date',true),'field' => 'created' ,'default' => 'desc'),
		array('title' => __('Payment Status',true),'field' => 'status' ),
		array('title' => __('Date',true),'field' => 'date','default' => 'desc' ),
		'default_order' => $sorted_by
		);
    }    
    function FindCountry($code = "") {
        $this->loadModel('Country');
        $row = $this->Country->find('first', array('conditions' => array('Country.code' => $code)));
        return $row['Country'];
    }

    function FindCurrency($code = "") {
        $this->loadModel('Currency');
        $row = $this->Currency->find('first', array('conditions' => array('Currency.code' => $code)));
        return $row['Currency'];
    }

    function checkOverPaidPosRefundPayment($data) {
        // Check if POS Plugin is Active
        if (!ifPluginActive(PosPlugin)) {
            return true;
        }
        $invoice_id = $data[0]['invoice_id'];
        $invoice = $this->Invoice->find('first', array('conditions' => array('Invoice.id' => $invoice_id), 'recursive' => -1));
        if ($invoice['Invoice']['type'] != Invoice::Refund_Receipt) {
            return true;
        }
        $original_invoice_id = $invoice['Invoice']['subscription_id'];
        $previous_refunds = $this->query("SELECT SUM(summary_paid) as total_paid FROM invoices WHERE subscription_id = $original_invoice_id and type = " . Invoice::Refund_Receipt, false);
        $previous_refunds_total = $previous_refunds ? $previous_refunds[0][0]['total_paid'] : 0;
        $payment_total = round($this->Invoice->sumInvoicePayments($data) + $previous_refunds_total, getAllowedNumberOfDigitsAfterFraction($invoice['Invoice']['currency_code']));
        $original_invoice = $this->Invoice->getInvoice($invoice['Invoice']['subscription_id'], [], false, -1);
        $original_invoice_paid_amount = round($original_invoice['Invoice']['summary_paid'], getAllowedNumberOfDigitsAfterFraction($invoice['Invoice']['currency_code']));
        if ($payment_total > $original_invoice_paid_amount) {
            return false;
        }
        return true;
    }

    protected function offlineRequired($param) {

        if (!in_array(low($this->data[$this->alias]['payment_method']), array('offline', 'cheque', 'cash', 'bank', 'credit'))) {
            return true;
        }
        $keys = array_keys($param);
        return strlen($param[$keys[0]]);
    }

    public static function getPaymentStatus() {
        return array(
            PAYMENT_STATUS_NOT_COMPLETED => __('Incomplete', true),
            PAYMENT_STATUS_COMPLETED => __('Completed', true),
            PAYMENT_STATUS_PENDING => __('Pending', true),
            PAYMENT_STATUS_FAILED => __('Failed', true),
            PAYMENT_STATUS_DRAFT => __('Draft', true),
        );
    }

    public static function getGatewayCategory($category) {
        $cats =  array(
            PAYMENT_CUSTOM_CATEGORY => __('Custom Payment Methods', true),
            PAYMENT_MANUALLY_ADDED_CATEGORY => __('Manual Options', true),
            PAYMENT_ONLINE_CATEGORY => __('Online Payment Gateways', true));

        return $cats[$category] ?? $cats[PAYMENT_MANUALLY_ADDED_CATEGORY];    
    }

    public static function getPaymentMethods() {
        $payments =  array(
            'paypal' => 'Paypal Standard',
            'paypalV2' => 'Paypal Express',
            'paymob' => 'PayMob',
            'paymob2' => 'PayMob',
            'securepay' => 'SecurePay',
            'tap' => 'Tap',
            'paytabs2' => 'PayTabs Version 2',
            'authorizenet' => "Authorize.Net",
            '2checkout' => '2Checkout',
            'stripe' => 'Stripe',
            'square' => 'Square',
            'paytabs' => 'PayTabs',
            //'payfort' => 'PayFort',
            'payumoney' => 'Payumoney',
            'eway' => 'eWay (AU)',
            'paymate' => 'Paymate',
            'ccavenue' => 'Ccavenue',
            'westernunion' => 'Western Union',
            'bank' => 'Bank Transfer',
            'cash' => 'Cash',
            'credit' => 'Credit Card',
            'cheque' => 'Cheque',
            'offline' => 'Offline Payment',
            'client_credit' => 'Client Credit',
            'starting_balance' => 'Starting Balance',
            'supplier_credit' => 'Supplier Credit',
            'tamara' => 'Tamara Pay',
            'tabby' => 'Tabby',
            'neoleap_pos' => 'NeoLeap POS',
//			'google_checkout' => 'Google Checkout'
        );

        if (ifPluginInstalled(WebsiteFrontPlugin)) {
            $payments['cash_on_delivery'] = 'Cash On Delivery';
        }
        return $payments;
    }
    public static function AdvancedPaymentMethods() {
        $payments =  array(
            'paypal' => array('name'=>"Paypal Standard","img"=>'/css/images/cp_paypal.png','category' => 'ONLINE', 'url' => 'https://www.paypal.com/eg/home', 'urlLabel' => 'paypal.com'),
            'authorizenet' =>  array('name'=>"Authorize.Net","img"=>'/css/images/cp_authorizenet.png','category' => 'ONLINE', 'url' => 'https://www.authorize.net/', 'urlLabel' => 'authorize.net'),
            '2checkout' =>array('name'=>"2Checkout","img"=>'/css/images/cp_2checkout.png','category' => 'ONLINE', 'url' => 'https://www.2checkout.com/', 'urlLabel' => '2checkout.com'),
            'stripe' =>array('name'=>'Stripe',"img"=>'/css/images/cp_stripe.png','category' => 'ONLINE', 'url' => 'https://stripe.com/', 'urlLabel' => 'stripe.com') ,
            // 'payfort' =>array('name'=>'PayFort',"img"=>'/css/images/payfort.png','country'=>'EG,SA,AE,JO,LB,QA') ,
            'square' =>array('name'=>'Square',"img"=>'/css/images/cp_square.png','category' => 'ONLINE', 'url' => 'https://squareup.com/us/en', 'urlLabel' => 'squareup.com') , // US, Canada, Japan, Australia, and the United Kingdom
            'payumoney' =>array('name'=> 'Payumoney','country'=>'IN',"img"=>'/css/images/cp_payumoney.png','category' => 'ONLINE', 'url' => 'https://www.payumoney.com/pay/', 'urlLabel' => 'payumoney.com'),
            'eway' => array('name'=> 'eWay (AU)','country'=>'AU,NZ,UK,HK,SG,MY',"img"=>'/css/images/cc.png','category' => 'ONLINE', 'url' => 'https://www.eway.com.au/', 'urlLabel' => 'eway.com.au'),
            'paymate' => array('name'=> 'Paymate','country'=>'AU,NZ',"img"=>'/css/images/cc.png','category' => 'ONLINE', 'url' => 'https://global.paymate.com/paymate-online-payments/', 'urlLabel' => 'global.paymate.com'),
            'bank' =>  array('name'=>'Bank Transfer', 'category' => 'MANUALLY_ADDED'),
            'cash' => array('name'=>'Cash', 'category' => 'MANUALLY_ADDED'),
            'ccavenue' => array('name'=>'Ccavenue',"img"=>'/css/images/ccavenue.png','country'=>'AE','category' => 'ONLINE', 'url' => 'https://www.ccavenue.com/', 'urlLabel' => 'ccavenue.com'),
            'westernunion' => array('name'=>'Western Union','country'=>'AD,AE,AF,AG,AI,AL,AM,AN,AO,AQ,AR,AS,AT,AU,AW,AZ,BA,BB,BD,BE,BF,BG,BH,BI,BJ,BM,BN,BO,BR,BS,BT,BV,BW,BY,BZ,CA,CC,CD,CF,CG,CH,CI,CK,CL,CM,CN,CO,CR,CU,CV,CX,CY,CZ,DE,DJ,DK,DM,DO,DZ,EC,EE,EH,ER,ES,ET,FI,FJ,FK,FM,FO,FR,FX,GA,GB,GD,GE,GF,GH,GI,GL,GM,GN,GP,GQ,GR,GS,GT,GU,GW,GY,HK,HM,HN,HR,HT,HU,ID,IE,IL,IN,IO,IQ,IR,IS,IT,JM,JO,JP,KE,KG,KH,KI,KM,KN,KP,KR,KW,KY,KZ,LA,LB,LC,LI,LK,LR,LS,LT,LU,LV,LY,MA,MC,MD,MG,MH,MK,ML,MM,MN,MO,MP,MQ,MR,MS,MT,MU,MV,MW,MX,MY,MZ,NA,NC,NE,NF,NG,NI,NL,NO,NP,NR,NU,NZ,OM,PA,PE,PF,PG,PH,PK,PL,PM,PN,PR,PT,PW,PY,QA,RE,RO,RU,RW,SB,SC,SD,SE,SG,SH,SI,SJ,SK,SL,SM,SN,SO,SR,ST,SV,SY,SZ,TC,TD,TF,TG,TH,TJ,TK,TM,TN,TO,TP,TR,TT,TV,TW,TZ,UA,UG,UM,US,UY,UZ,VA,VC,VE,VG,VI,VN,VU,WF,WS,YE,YT,YU,ZA,ZM,ZW,PS,RS,GG,IM,JE,ME','category' => 'ONLINE', 'url' => 'https://www.westernunion.com/', 'urlLabel' => 'westernunion.com'),
            'credit' => array('name'=>'Credit Card',"img"=>'/css/images/cc.png'),
            'cheque' => array('name'=>'Cheque', 'category' => 'MANUALLY_ADDED'),
            'offline' =>array('name'=> __('Offline Payment',true)),
            'client_credit' =>array('name'=> 'Client Credit','country'=>'XX'),
            'starting_balance' => array('name'=>'Starting Balance','country'=>'XX'),
            'paytabs2' => array('name'=>'PayTabs Version 2',"img"=>'/css/images/Paytabs-Extended.png','country' => 'SA,IN,KW,ID,MY,OM,TN,AE,PH,SG,EG,HK,DZ,BH,JO,LB,MA,IQ','category' => 'ONLINE', 'url' => 'https://site.paytabs.com/','urlLabel' => 'paytabs.com') ,
            'paytabs' =>array('name'=>'PayTabs',"img"=>'/css/images/Paytabs-Extended.png','country' => 'SA,IN,KW,ID,MY,OM,TN,AE,PH,SG,EG,HK,DZ,BH,JO,LB,MA','category' => 'ONLINE','url' => 'https://site.paytabs.com/','urlLabel' => 'paytabs.com') ,
            'paypalV2' => array('name'=>'Paypal Version 2',"img"=>'/css/images/cp_paypal.png','category' => 'ONLINE', 'url' => 'https://www.paypal.com/eg/home', 'urlLabel' => 'paypal.com'),
            'paymob' => array('name'=>'Paymob','country' => 'JO,EG,KE,PK,PS','category' => 'ONLINE', 'url' => 'https://paymob.com/', 'urlLabel' => 'paymob.com') ,
            'paymob2' => array('name'=>'Paymob','country' => 'OM,SA,AE,PK,EG','category' => 'ONLINE', 'url' => 'https://paymob.com/', 'urlLabel' => 'paymob.com') ,
            'tap' => array('name'=>'Tap','country' => 'KW,SA,AE,BH,OM,QA,EG','category' => 'ONLINE', 'url' => 'https://www.tap.company/', 'urlLabel' => 'tap.company') ,
            'securepay' => array('name' => 'SecurePay','country'=>'AU','category' => 'ONLINE', 'url' => 'https://www.securepay.com.au/', 'urlLabel' => 'securepay.com.au'),
            'tamara' => ['name' => 'Tamara Pay', 'country' => 'SA', 'category' => 'ONLINE', 'url' => 'https://api-reference.tamara.co/', 'urlLabel' => 'api-reference.tamara.co', 'img' => '/css/images/payment/tamara.svg'],
            'tabby' => ['name' => 'Tabby', 'category' => 'ONLINE', 'url' => 'https://docs.tabby.ai/pay-in-4-custom-integration/quick-start', 'urlLabel' => 'docs.tabby.ai', 'img' => '/css/images/payment/tabby.svg'],
            'neoleap_pos' => ['name' => 'NeoLeap POS', 'category' => 'POS', 'url' => 'https://neoleap.com/', 'urlLabel' => 'neoleap.com', 'img' => '/css/images/payment/neoleap.svg', 'country' => 'SA'],
//			'google_checkout' => 'Google Checkout'
        );
        if (ifPluginInstalled(WebsiteFrontPlugin)) {
            $payments['cash_on_delivery'] =  array('name'=>'Cash On Delivery',"img"=>'/css/images/money.png');
        }
        return $payments;
    }
	
	public static function getAllPaymentMethods($conditions=[]) {

        $names = self::getPaymentMethods();

        $SitePaymentGateway = GetObjectOrLoadModel('SitePaymentGateway');
        $SitePaymentGateway->recursive = -1;
		
        $gateways = $SitePaymentGateway->find('all',['conditions'=>$conditions]);
        $gs = array();
        foreach ($gateways as $g) {
            if ($g['SitePaymentGateway']['default'])
                self::$default_payments_gatways = $g['SitePaymentGateway']['payment_gateway'];
			
            $gs[$g['SitePaymentGateway']['payment_gateway']] = $names[$g['SitePaymentGateway']['payment_gateway']];
			if(!empty($g['SitePaymentGateway']['label']))	$gs[$g['SitePaymentGateway']['payment_gateway']]=$g['SitePaymentGateway']['label'];
        }


        return $gs;
    }

   
    public static function getActivePaymentMethods() {
		return self::getAllPaymentMethods(['SitePaymentGateway.active' => 1]);
    }

    function getFilters($client = false) {
        $filters = array(
            'no' => array('label' => __('Invoice No.', true)),
            'code' => array('type' => 'like','more-options' => false, 'label' => __('Payment No.', true)),
            'client_id' => array('div_class' => 'full-width', 'input_type' => 'advanced_client', 'more-options' => false, 'empty' => __('Any Client', true), 'label' => __('Client', true)),
            'payment_method' => array('more-options' => true, 'options' => array('type' => 'select', 'empty' => 'Please Select')),
            'date' => array('more-options' => true, 'type' => 'date_range'),
            'id' => array( 'more-options' => true, 'options' => array('type' => 'text')),
            'transaction_id' => array('label' => __('Transaction ID', true), 'more-options' => true, 'options' => array('type' => 'text')),
            'amount' => array('more-options' => true, 'from_label' => __('Amount more than', true), 'to_label' => __('Amount less than', true), 'type' => 'number_range'), 'status' => array('more-options' => true),
            'created' => array('more-options' => true,'label' =>  __('Created Date',true), 'type' => 'date_range', 'to_label' => __('Date To', true), 'from_label' => __('Date From', true))
        );


        if ($client) {
            $filters = array('date' => array('more-options' => true, 'type' => 'date_range'), 'status', 'amount' => array('more-options' => true, 'from_label' => __('Amount more than', true), 'to_label' => __('Amount less than', true), 'type' => 'number_range'));
        }

        if (ifPluginActive(PosPlugin) && check_permission(Invoices_View_All_Invoices) || check_permission(Invoices_View_Invoices_Details)) {
            $filters['pos_shift_id'] = array('input_type' => 'text', 'more-options' => true, 'empty' => __('Any POS Shift', true), 'label' => __('Pos Shift', true));

        }

        return $filters;
    }

    function afterFind($results, $primary = false) {

        parent::afterFind($results, $primary);
        if (empty($results)) {
            return $results;
        }
        foreach ($results as &$result) {

           
            

            if (!empty($result[$this->name]['country_code']) && isset($result['Country'])) {
                 $result['Country'] = $this->FindCountry($result[$this->alias]['country_code']);
                $result[$this->name]['country'] = $this->FindCountry($result[$this->name]['country_code']);
            }
            if (!empty($result[$this->name]['currency_code']) && isset($result['Currency'])) {
                $result['Currency'] = $this->FindCurrency($result[$this->alias]['currency_code']);
                $result[$this->name]['currency'] = $result['Currency']['name'];
            }
        }
        return $results;
    }

    function saveOffline($data) {
        $this->set($data);
        $this->validate = array(/* 'transaction_id' => array('required' => true, 'rule' => 'notEmpty', 'message' => __('Required', true)), */'date' => array(
				'validateIsOpenedPeriod' => ['rule' => 'validateIsOpenedPeriod', 'message' => __('You can not add, edit, or delete a transaction in this date %s within a closed period', true)],
				),'transaction_type' => array('required' => true, 'rule' => 'notEmpty', 'message' => __('Required', true)), 'notes' => array('rule' => array('maxlength', 500), 'message' => __('Only 500 characters allowed', true)));
        return $this->validates();
//		if ($this->validates()){
//			$data['InvoicePayment']['payment_id'] = $paymentId;
//			$data['InvoicePayment']['status'] = PAYMENT_STATUS_PENDING;
//
//			return $this->save($data, array('validate' => false, 'callbacks' => false, 'fieldList' => array('id', 'status', 'transaction_id', 'transaction_type', )));
//		}
//
//		return false;
    }

    function myobExport() {
        $csv_fields = array('Co./Last Name', 'First Name', 'Addr 1 - Line 1', ' - Line 2', ' - Line 3', ' - Line 4', 'Inclusive', 'Invoice #', 'Date', 'Customer PO', 'Ship Via', 'Delivery Status', 'Description', 'Account #', 'Amount', 'Inc-Tax Amount', 'Job', 'Comment', 'Journal Memo', 'Salesperson Last Name', 'Salesperson First Name', 'Shipping Date', 'Referral Source', 'Tax Code', 'Non-GST Amount', 'GST Amount', 'LCT Amount', 'Freight Amount', 'Inc-Tax Freight Amount', 'Freight Tax Code', 'Freight Non-GST Amount', 'Freight GST Amount', 'Freight LCT Amount', 'Sale Status', 'Terms - Payment is Due', ' - Discount Days', ' - Balance Due Days', '- % Discount', ' - % Monthly Charge', 'Amount Paid', 'Payment Method', 'Payment Notes', 'Name on Card', 'Card Number', 'Expiry Date', 'Authorisation Code', 'BSB', 'Account Number', 'Drawer/Account Name', 'Cheque Number', 'Category');
        $invoices = $this->query("SELECT IP.*, I.*, C.*, (SELECT SUM(invoice_value) FROM invoice_taxes WHERE invoice_taxes.invoice_id = I.id) as taxes FROM invoice_payments as IP JOIN (invoices as I) ON (IP.invoice_id = I.id AND I.`type` = 0) JOIN (clients as C) ON (I.client_id = C.id) WHERE I.site_id = " . getAuthOwner('id') . " AND IP.status = 1");
        $csv_lines = array(implode(CSV_SEPARATOR, array_map('csv_quote', $csv_fields)));
        foreach ($invoices as $invoice) {
            $taxes = floatval($invoice[0]['taxes']);
            $line = array($invoice['IP']['last_name'], $invoice['IP']['first_name'], $invoice['IP']['address1'], $invoice['IP']['address2'], $invoice['IP']['city'], $invoice['IP']['state'], 0, $invoice['I']['no'], date('m-d-y', strtotime($invoice['IP']['date'])), '', '', 'A', '', $invoice['C']['client_number'], $invoice['I']['summary_total'] - $taxes, $invoice['I']['summary_total'], '', '', '', '', '', '', '', 'GST', $invoice['I']['summary_total'] - $taxes, $taxes, '0', '0', '0', '0', '0', '0', '0', 'I', $invoice['I']['due_after'], '0', '0', $invoice['I']['discount'], '0', $invoice['IP']['amount'], $invoice['IP']['payment_method'], $invoice['IP']['notes'], '', '', '', $invoice['IP']['transaction_id'], '', '', '', '', '');
            $csv_lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', $line));
        }
        header('Content-Type: text/csv; charset=utf-8');
        $dateFormats = getDateFormats('std');
        $format = $dateFormats[getAuthOwner('date_format')];
        header('Content-Disposition: attachment; filename=' . Inflector::slug(low(getAuthOwner('business_name') . '-' . date($format))) . '.csv');
        Configure::write('debug', 0);
        die(implode("\r\n", $csv_lines));
    }

    protected function checkMethod($param) {
        $method = $param['payment_method'];
        $PaymentMethod = GetObjectOrLoadModel('SitePaymentGateway');
        return $PaymentMethod->hasAny(array('SitePaymentGateway.payment_gateway' => $method, 'SitePaymentGateway.active' => 1));
    }

    function editPayment($data) {
        
        $this->set($data);
        $payment = $this->findById($data[$this->alias]['id']);

        $whiteList = array('treasury_id', 'receipt_notes','id', 'status', 'notes','invoice_id', 'attachment');
        if ($payment[$this->alias]['added_by']) {
            $whiteList = array('currency_code','invoice_id','treasury_id' ,'receipt_notes','staff_id', 'date', 'id', 'payment_method', 'amount', 'transaction_id', 'status', 'notes','attachment','extra_details');
        }


        if (empty($data['InvoicePayment']['date'])) {
            $data['InvoicePayment']['date'] = date('Y-m-d');
        } else {
            $data['InvoicePayment']['date'] = $this->formatDate($data['InvoicePayment']['date']);
        }
        if($data['InvoicePayment']['payment_method']=="starting_balance"){
        unset($this->validate['payment_method']);
        }

        $this->data=$data;

        if (!empty($payment['InvoicePayment']['invoice_id'])) {
            // to correct when there is a very small diff between amount and PurchaseOrder total
            $invoice = $this->Invoice->find('first', array('applyBranchFind' => false, 'conditions' => array('Invoice.id' => $payment['InvoicePayment']['invoice_id'])));
            $smallest_fraction = pow(10, CurrencyHelper::getFraction($invoice['Invoice']['currency_code']) * -1);
            if ($payment['InvoicePayment']['status'] != PAYMENT_STATUS_COMPLETED && $data['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED) {
                $abs_diff = abs($invoice['Invoice']['summary_total'] - $invoice['Invoice']['summary_paid'] - (float) $data['InvoicePayment']['amount'] - $invoice['Invoice']['summary_refund']);
                if ($abs_diff > 0 && $abs_diff < $smallest_fraction) {
                    $data['InvoicePayment']['amount'] += $invoice['Invoice']['summary_total'] - $invoice['Invoice']['summary_paid'] - (float) $data['InvoicePayment']['amount'] - $invoice['Invoice']['summary_refund'];
                }
            } elseif ($payment['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED && $data['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED) {
                $abs_diff = abs($invoice['Invoice']['summary_total'] - $invoice['Invoice']['summary_paid'] + $payment['InvoicePayment']['amount'] - (float) $data['InvoicePayment']['amount'] - $invoice['Invoice']['summary_refund']);
                if ($abs_diff > 0 && $abs_diff < $smallest_fraction) {
                    $data['InvoicePayment']['amount'] += $invoice['Invoice']['summary_total'] - $invoice['Invoice']['summary_paid'] + $payment['InvoicePayment']['amount'] - (float) $data['InvoicePayment']['amount'] - $invoice['Invoice']['summary_refund'];
                }
            }
        }

        $result = $this->save($data, array('validate' => true, 'callbacks' => true, 'fieldList' => $whiteList));

        $allData = $payment;
        $allData['InvoicePayment'] = array_merge($payment['InvoicePayment'], $data['InvoicePayment']);

        izam_resolve(InvoicePaymentService::class)->update(ServiceModelDataTransformer::transform($allData, 'InvoicePayment'));

        if(!empty($payment['Invoice']['id'])) {
            $this->Invoice->updateInvoicePayments($payment['Invoice']['id']);
        }
        return $result;
    }
    function ConvertPayment($data,$id) {
        $this->set($data);
     //   $payment = $this->findById($data[$this->alias]['id']);
       
        $result = $this->save($data, array('validate' => false, 'callbacks' => false));
        $this->update_journals($result);
        $this->Invoice->updateInvoicePayments($id);
        return $result;
    }
    
    function get_financial_report_view ( $get_client_credit = false, $get_credit_payments=false ) {
        $clientCreditQ = "" ;
        $this->loadModel('Requisition');
        if ( !$get_client_credit )
            $clientCreditQ = " and payment_method <> 'client_credit' ";
        // Note to anyone will add a new query please dont forget the other function that depend on that query
        return "(
                select invoice_payments.currency_code , (case when `invoices`.`type` = 0 then 'invoice' else 'refund' end) AS `type`,`invoice_payments`.`date` AS `date`,`invoice_payments`.`amount` AS `amount`,`invoice_payments`.`payment_method` AS `field1`, CONVERT(`invoices`.`no` USING utf8)  AS `field2`,`invoices`.`id` AS `field3`,`invoice_payments`.`id` AS `id`, `invoice_payments`.`receipt_notes` as description from (`invoice_payments` inner join `invoices` on((`invoices`.`id` = `invoice_payments`.`invoice_id`))) where ((`invoices`.`draft` <> 1)) and `invoice_payments`.`status` = 1 $clientCreditQ AND %s )
                union all (select purchase_order_payments.currency_code ,(case when `purchase_orders`.`type` = 0 then 'purchase_invoice' else 'purchase_refund' end) AS `type`,`purchase_order_payments`.`date` AS `date`,(`purchase_order_payments`.`amount`*-1) AS `amount`,`purchase_order_payments`.`payment_method` AS `field1`,CONVERT(`purchase_orders`.`no` USING utf8) AS `field2`,`purchase_orders`.`id` AS `field3`,`purchase_order_payments`.`id` AS `id`, `purchase_order_payments`.`receipt_notes` as description from (`purchase_order_payments` left join `purchase_orders` on((`purchase_orders`.`id` = `purchase_order_payments`.`purchase_order_id`))) where ((`purchase_orders`.`draft` <> 1) and (`purchase_orders`.`id` is not null)) AND `purchase_order_payments`.`status` = 1 and `purchase_order_payments`.`payment_method` !='supplier_credit'  AND %s ) 
                union all (select expenses.currency_code , (case when (`expenses`.`is_income` = 1) then 'income' else 'expense' end) AS `type`,`expenses`.`date` AS `date`,(case when(`expenses`.`is_income` = 1) then `expenses`.`amount` else (`expenses`.`amount`*-1) end)  AS `amount`,`expenses`.`category` AS `field1`, CONVERT(`expenses`.`vendor` USING utf8) AS `field2`,`expenses`.`note` AS `field3`,`expenses`.`id` AS `id`, CONVERT(`expenses`.`expense_number` USING utf8) as description  from `expenses` where %s)
                /*union all (select `time_trackings`.`currency` AS `currency_code`,'' AS `field2`, '' as `field3`, 'time_track' AS `type` , `cost` AS `amount`,'' AS `field1`,`time_trackings`.`date` ,`time_trackings`.`id` AS `id` , '' as description  from  (`time_trackings` join `projects` on((`projects`.`id` = `time_trackings`.`project_id`)))  where group by `time_trackings`.`id`)*/
                union all (select expenses.currency_code , 'time_track' AS `type`,`expenses`.`date` AS `date`,(`expenses`.`amount` *-1)AS `amount`,'' AS `field1`,'' AS `field2`,''  AS `field3`,`expenses`.`id` AS `id` , '' as description  from `timetracking_expense_structure` as `expenses` where amount > 0 AND %s)
                ".($get_credit_payments?"
                union all (select ip2.currency_code , 'credit' AS  `type`,`ip2`.`date` AS `date`,`ip2`.`amount` AS `amount`,`ip2`.`payment_method` AS `field1`, '' AS `field2`, `ip2`.`id` AS `field3`,`ip2`.`id` AS `id`, `ip2`.`receipt_notes` as description from `invoice_payments` as ip2  where `ip2`.`status` = 1 $clientCreditQ AND %s AND  `ip2`.`invoice_id` IS NULL )"
                :"")."
                ";
    }
    function get_financial_results (  $where ,$per_page = 20 , $page = 1 , $get_client_credit=false, $get_credit_payments=false ) {
        $view_query = $this->get_financial_report_view($get_client_credit,$get_credit_payments) ;
        // warning suppress
        $timeTrackingWhere = str_replace($this->FRBranchConditions ?? '', '', $where);
        if(!$get_credit_payments)
        $main_query = sprintf($view_query ,
            str_replace ("{{}}", "invoice_payments" , $where )
            , str_replace ("{{}}", "purchase_order_payments", $where )
            , str_replace ("{{}}", "expenses", $where )
            ,str_replace ("{{}}", "expenses", $timeTrackingWhere )
        );
        else
            $main_query = sprintf($view_query ,
                str_replace ("{{}}", "invoice_payments" , $where )
                , str_replace ("{{}}", "purchase_order_payments", $where )
                , str_replace ("{{}}", "expenses", $where )
                ,str_replace ("{{}}", "expenses", $timeTrackingWhere )
                ,str_replace ("{{}}", "ip2" , $where )
            );

        $main_query = str_replace(["time_trackings.currency_code",  "invoice_payments.currency_code", "purchase_order_payments.currency_code" , "invoice_payments.work_order_id" ,"purchase_order_payments.work_order_id", "ip2.work_order_id" ]
                , ["time_trackings.currency",  "invoices.currency_code" , "purchase_orders.currency_code" , "invoices.work_order_id", "purchase_orders.work_order_id" , "ip2.payment_work_order_id" ], $main_query);

        $results = $this->flat_query_results ($main_query." order by `date` ASC LIMIT $per_page OFFSET ".($per_page * ($page-1))  ) ;
        return  $results ;
    }
    function get_previous_income (  $where ,$per_page = 20 , $page = 1,$work_order_id=null ) {
        if($work_order_id!=null) {
            \App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
            $this->loadModel('WorkOrder');
            $row = $this->WorkOrder->find('first', ['recursive' => -1, 'conditions' => ['WorkOrder.id' => $work_order_id]]);
        }
        // warning suppress
        $timeTrackingWhere = str_replace($this->FRBranchConditions ?? '', '', $where);
        $view_query = $this->get_financial_report_view() ;
                
        $main_query = sprintf($view_query , str_replace ("{{}}", "invoice_payments" , $where ) , str_replace ("{{}}", "purchase_order_payments", $where ) , str_replace ("{{}}", "expenses", $where ) ,str_replace ("{{}}", "expenses", $timeTrackingWhere ),str_replace ("{{}}", "r" , $where ));
        $main_query = str_replace(["time_trackings.currency_code",  "invoice_payments.currency_code","purchase_order_payments.currency_code" , "invoice_payments.work_order_id" ,"purchase_order_payments.work_order_id" ] 
                , ["time_trackings.currency",  "invoices.currency_code" , "purchase_orders.currency_code" , "invoices.work_order_id", "purchase_orders.work_order_id" ], $main_query);
        $qq = "select (case when `type` in (  'invoice','income')  then `amount` else 0 END) as sum_amount,currency_code from (". $main_query." order by `date` ASC LIMIT ".($per_page*($page) )." )aa";
//        echo $qq ;
        //print_pre($qq);
        debug($qq);
        $results = $this->flat_query_results ( $qq  ) ;
        //print_pre($results);
        if($work_order_id!=null) {
            $total = 0;
            foreach ($results as $c) {
                $rate = CurrencyConverter::index($c['currency_code'], $row['WorkOrder']['budget_currency'], date('Y-m-d'));
                $total = $total + ($rate * $c['sum_amount']);
            }
            $expense = $total;
        }else {

            $expense = $results[0]['sum_amount'];
        }
        return  $expense ;
    }
    function get_previous_expense (  $where ,$per_page = 20 , $page = 1,$work_order_id=null ) {
        if($work_order_id!=null) {
            \App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
            $this->loadModel('WorkOrder');
            $row = $this->WorkOrder->find('first', ['recursive' => -1, 'conditions' => ['WorkOrder.id' => $work_order_id]]);
        }
        // warning suppress
        $timeTrackingWhere = str_replace($this->FRBranchConditions ?? '', '', $where);

        $view_query = $this->get_financial_report_view() ;
        
        $main_query = sprintf($view_query , str_replace ("{{}}", "invoice_payments" , $where ) , str_replace ("{{}}", "purchase_order_payments", $where ) , str_replace ("{{}}", "expenses", $where ) ,str_replace ("{{}}", "expenses", $timeTrackingWhere ),str_replace ("{{}}", "r" , $where ));
        $main_query = str_replace(["time_trackings.currency_code",  "invoice_payments.currency_code","purchase_order_payments.currency_code" , "invoice_payments.work_order_id" ,"purchase_order_payments.work_order_id" ] 
                , ["time_trackings.currency",  "invoices.currency_code" , "purchase_orders.currency_code" , "invoices.work_order_id", "purchase_orders.work_order_id" ], $main_query);
        $qq = "select (case when `type` in ('time_track',  'refund',  'purchase_order','expense') then `amount` else 0 END) as sum_amount,currency_code from (". $main_query." order by `date` ASC LIMIT ".($per_page*($page) )." )aa" ;

        $results = $this->flat_query_results ( $qq ) ;
        if($work_order_id!=null) {
            $total = 0;
            foreach ($results as $c) {
                $rate = CurrencyConverter::index($c['currency_code'], $row['WorkOrder']['budget_currency'], date('Y-m-d'));
                $total = $total + ($rate * $c['sum_amount']);
            }
            $expense = $total;
        }else {

            $expense = $results[0]['sum_amount'];
        }

        return  $expense ;
    }
    /**
     * for pagination
     * @param string $where Where string with {{}} placeholder 
     * @return flat query result
     */
    function count_all_financial_results ( $where ) {
        $timeTrackingWhere = str_replace($this->FRBranchConditions, '', $where);
        $view_query = $this->get_financial_report_view() ;
        $main_query = sprintf($view_query , str_replace ("{{}}", "invoice_payments" , $where ) , str_replace ("{{}}", "purchase_order_payments", $where ) , str_replace ("{{}}", "expenses", $where ) ,str_replace ("{{}}", "expenses", $timeTrackingWhere ),str_replace ("{{}}", "r" , $where ));
        $main_query = str_replace(["time_trackings.currency_code",  "invoice_payments.currency_code","purchase_order_payments.currency_code" , "invoice_payments.work_order_id" ,"purchase_order_payments.work_order_id" ] 
                , ["time_trackings.currency",  "invoices.currency_code" , "purchase_orders.currency_code" , "invoices.work_order_id", "purchase_orders.work_order_id" ], $main_query);
        return $this->flat_query_results ( "select count(*)as cc from ( $main_query )aaa");
    }
    function previous_period_financial_sum ( $where ) {
        $timeTrackingWhere = str_replace($this->FRBranchConditions, '', $where);
        $view_query = $this->get_financial_report_view() ;
        $date_balance_query = sprintf(
            $view_query ,
            str_replace ("{{}}", "invoice_payments" , $where ) ,
            str_replace ("{{}}", "purchase_order_payments", $where ) ,
            str_replace ("{{}}", "expenses", $where ) ,
            str_replace ("{{}}", "expenses", $timeTrackingWhere ),
            str_replace ("{{}}", "r" , $where )
        );
        $date_balance_query = str_replace(["time_trackings.currency_code",  "invoice_payments.currency_code","purchase_order_payments.currency_code" , "invoice_payments.work_order_id" ,"purchase_order_payments.work_order_id" ] 
                , ["time_trackings.currency",  "invoices.currency_code" , "purchase_orders.currency_code" , "invoices.work_order_id", "purchase_orders.work_order_id" ], $date_balance_query);
        
        $starting_balance_result = $this->flat_query_results ( "select sum(`amount`) as sum_amount from ( $date_balance_query )aa" );
        
        $starting_balance = $starting_balance_result[0]['sum_amount'];
        return $starting_balance ; 
    }
    function get_previous_page_balance ( $where , $per_page = 20 , $page = 1  ) {
        $timeTrackingWhere = str_replace($this->FRBranchConditions, '', $where);
        $view_query = $this->get_financial_report_view(false, true);
        $main_query = sprintf(
            $view_query ,
            str_replace ("{{}}", "invoice_payments" , $where ) ,
            str_replace ("{{}}", "purchase_order_payments", $where ) ,
            str_replace ("{{}}", "expenses", $where ) ,
            str_replace ("{{}}", "expenses", $timeTrackingWhere ),
            str_replace ("{{}}", "r" , $where )
        );
        $main_query = str_replace(["time_trackings.currency_code",  "invoice_payments.currency_code","purchase_order_payments.currency_code" , "invoice_payments.work_order_id" ,"purchase_order_payments.work_order_id" ] 
                , ["time_trackings.currency",  "invoices.currency_code" , "purchase_orders.currency_code" , "invoices.work_order_id", "purchase_orders.work_order_id" ], $main_query);
        
        $q2= "select sum(`amount`) as sum_amount from ( $main_query  order by `date` ASC LIMIT ".($per_page * ($page-1)) ." OFFSET 0 )aaa ";
        $starting_balance_result = $this->flat_query_results ($q2 );
        $starting_balance = $starting_balance_result[0]['sum_amount'];
        return $starting_balance;
    }
    
    
    function get_client_id($data)
    {
        if(!is_array($data)&&is_int($data))
            $data=$this->findById($data);
        if(!empty($data[$this->alias]['client_id'])) return $data[$this->alias]['client_id'];
        if(!empty($data['Invoice']['client_id'])) return $data['Invoice']['client_id'];
        else if(!empty($data[$this->alias]['invoice_id']))
        {
            $invoice = $this->Invoice->findById($data[$this->alias]['invoice_id']);
            return $invoice['Invoice']['client_id'];
        }
            
    }
    
    function get_invoice_data($data)
    {
        if(!is_array($data)&&is_int($data))
            $data=$this->findById($data);
        if(!empty($data['Invoice'])) return $data['Invoice'];
        $invoice=$this->Invoice->findById($data[$this->alias]['invoice_id']);
        return  $invoice['Invoice'];
    }
    
    function get_client_data($data)
    {
        if(!is_array($data)&&is_int($data))
            $data=$this->findById($data);
        if(!empty($data['Client'])) return $data['Client'];
        $client=$this->Client->findById($this->get_client_id($data));
        return $client['Client'];
    }
    
    function get_treasury_data($data)
    {
        if(!is_array($data)&&is_int($data))
            $data=$this->findById($data);
        if(!empty($data['Treasury'])) return $data['Treasury'];
        
        $this->loadModel('Treasury');
        
        if(!isset($data[$this->alias]['treasury_id']))
            $data[$this->alias]['treasury_id']=$this->Treasury->get_primary();
            
        $treasury=$this->Treasury->findById($data[$this->alias]['treasury_id']);
        
        return $treasury['Treasury'];
    }
    
    
    
    public function fix_alias($data)
    {
        if(isset($data['InvoicePayment'])&&!isset($data['Payment']))
            $this->alias='InvoicePayment';
        else if(isset($data['Payment'])&&!isset($data['InvoicePayment']))
            $this->alias='Payment';
    }

    public function update_currency_difference_rates($from_date)
    {
        $default_currency=getCurrentSite('currency_code');
        $invoice_payments = $this->find('all',['conditions'=> [$this->alias.'.currency_code <> '=>$default_currency,$this->alias.'.date >='=>$from_date]]);
        if(!empty($invoice_payments))
        foreach ($invoice_payments as $payment)
        {
            $this->update_journals($payment);
        }
    }
    
    public function get_journals($data)
    {
        $advancePaymentsEnabled = settings::getValue(InvoicesPlugin ,settings::ENABLE_ADVANCE_PAYMENT);
        if ($advancePaymentsEnabled) {
            $invoiceModel = GetObjectOrLoadModel('Invoice');
            $invoice = $invoiceModel->find('first',array('conditions'=>array('Invoice.id'=>$data[$this->alias]['invoice_id'])));
            if ($invoice['Invoice']['type'] == $this->Invoice::ADVANCE_PAYMENT) {
                return false;
            }

        }

        \App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
        
        $default_currency=getCurrentSite('currency_code');

        $disable_invoice_auto_journals = settings::getValue(AccountingPlugin, "disable_invoice_auto_journal"  );
        if($disable_invoice_auto_journals){
            return false;
        }

        $this->fix_alias($data);

        if(empty($data[$this->alias]['id'])) $data[$this->alias]['id']= $this->id;

        if(isset($data[$this->alias]['amount'])&&!empty($data[$this->alias]['id'])&&!isset($data[$this->alias]['invoice_id'])&&!isset($data[$this->alias]['client_id']))
        $data=$this->findById($data[$this->alias]['id']);


        if(!isset($data[$this->alias]['amount'])||(!isset($data[$this->alias]['client_id'])&&!isset($data[$this->alias]['invoice_id']))) {
            return false;
        }



        if(empty($data['Invoice'])&&!empty($data[$this->alias]['invoice_id']))  $data['Invoice']=$this->get_invoice_data($data);

        $this->loadModel('SitePaymentGateway');
        $paymentMethod = $this->SitePaymentGateway->find('first', ['conditions' => ['SitePaymentGateway.payment_gateway' => $data[$this->alias]['payment_method']]]);
        /**
         * This assumes that the payment gateway does not exist in payment gateways and its added via api
         * So add it to Payment gateways if it does not exist
         */
        if(!$paymentMethod && IS_REST) {
            $this->SitePaymentGateway->save(['payment_gateway' => $data[$this->alias]['payment_method'],'label' => $data[$this->alias]['payment_method'],'active' =>  true,'manually_added' => true, 'category' => PAYMENT_CUSTOM_CATEGORY], array('validate' => false));
            $paymentMethod['SitePaymentGateway'] = ['id' => $this->SitePaymentGateway->id];
        }
        $paymentSettings = null;
        $extraDetails = [];
        if (array_key_exists('extra_details', $data[$this->alias]) && !empty($data[$this->alias]['extra_details'])) {
            $extraDetails = json_decode($data[$this->alias]['extra_details'], true);
            $paymentSettings = array_key_exists('paymentSetting', $extraDetails) ? $extraDetails['paymentSetting'] : null;
        }
        if ($paymentSettings === null) {
            $paymentSettings = $paymentMethod['SitePaymentGateway']['settings'] ? array_merge(['id' => $paymentMethod['SitePaymentGateway']['id']],json_decode($paymentMethod['SitePaymentGateway']['settings'],true)) : ['id' => $paymentMethod['SitePaymentGateway']['id'], 'fixed_rate' => 0, 'percentage_rate' => 0, 'minimum_rate' => 0, 'tax_id' => 0];
            $extraDetails['paymentSetting'] = $paymentSettings;
            $this->id = $data[$this->alias]['id'];
            $this->saveField('extra_details',json_encode($extraDetails));
        }



        if(isset($data[$this->alias]['client_id'])&&!isset($data[$this->alias]['Client']))
            $data['Client']=$this->get_client_data($data);


        $client_business_name='';
        if(!empty($data['Invoice']['client_business_name']))
            $client_business_name=' '.$data['Invoice']['client_business_name'];
        else if(!empty($data['Client']['business_name']))
            $client_business_name=' '.$data['Client']['business_name'];
        else if (isset($data['Invoice']['client_id']))
            $client_business_name = ' ' . $this->Client->findById($data['Invoice']['client_id'])['Client']['business_name'];

        $description=__('Client Payment',true).' #'.$data[$this->alias]['code'].$client_business_name;

        if(!empty($data['Invoice']['id']))
        $description.=__(',',true).' '.__('Invoice',true).' #'.$data['Invoice']['no'];
        
        if($data[$this->alias]['amount']<0 )
		{
			
            $description=__('Client Refund',true).' #'.$data[$this->alias]['code'].$client_business_name;
		
			if(!empty($data['Invoice']['id']))
			$description.=__(',',true).' '.__('Refund Receipt',true).' #'.$data['Invoice']['no'].$client_business_name;
		}

        if ($data[$this->alias]['payment_method'] == "starting_balance") {
            $description = __('Opening Balance', true) . ' #' . $data[$this->alias]['code'] . $client_business_name;
        }
		
       


        if(!empty($data[$this->alias]['receipt_notes']))
            $description.=' '.$data[$this->alias]['receipt_notes'];
        
        if(!empty($data[$this->alias]['transaction_id']))
            $description.=__(',',true).' '.__('Transaction ID',true).' #'.$data[$this->alias]['transaction_id'];

        $journal['Journal']['date']=$data[$this->alias]['date'];
        $journal['Journal']['entity_type']='invoice_payment';
        $journal['Journal']['description']=$description;
        $journal['Journal']['entity_id']=$data[$this->alias]['id'];
        if (!empty($data[$this->alias]['branch_id'])) { //if branch plugin not active will save journal branch to null so must check if data has branch id or not
            $journal['Journal']['branch_id'] = $data[$this->alias]['branch_id'];
        }
        if($data[$this->alias]['status']!=PAYMENT_STATUS_COMPLETED||$data[$this->alias]['payment_method']=='client_credit'||!empty($data['Invoice']['draft']))
        {
            //dont save journal
            return  $journal;
        }
        
        if(empty($data[$this->alias]['treasury_id']))
        {
            $this->loadModel('Treasury');
            $data[$this->alias]['treasury_id']=$this->Treasury->get_primary();
        }
        
        if(empty($data[$this->alias]['currency_code']))
        {
            if(!empty($data['Invoice']['currency_code']))   
               $data[$this->alias]['currency_code'] = $data['Invoice']['currency_code'];
            else 
                $data[$this->alias]['currency_code']= getCurrentSite('currency_code');
        }
        
         $data[$this->alias]['client_id']=$this->get_client_id($data);

        /*
            if pos_shift_id exists on 'InvoicePayment' or 'Payment' then the payment is associated to the pos shift and should abide by the pos shift rules (calculated per invoice or not),
            if it's not associated with the pos shift then it should add a journal by default and not adjourn to the shift rules.
        */

        $pos_shift_id = ifPluginActive(PosPlugin) ? ($data['InvoicePayment']['pos_shift_id'] ?? $data['Payment']['pos_shift_id']) : false;
        if ($pos_shift_id) {
            $this->loadModel('PosShift');
            if(!$this->PosShift->checkPosShiftIdIsCalculatedPerInvoice($pos_shift_id)) {
                return $journal;
            }
        }

        $journalTransaction =  [[
            'subkey'=>'treasury',
            'currency_debit'=>$data[$this->alias]['amount'],
            'currency_code'=>$data[$this->alias]['currency_code'],
            'description'=>$description,
            'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'treasury','entity_id'=>$data[$this->alias]['treasury_id'])
        ]];
        if(isset($data[$this->alias]['treasury_id'])) {
            //handle treasury account creation
            $treasury = $this->Treasury->findById($data[$this->alias]['treasury_id']);
            if($treasury['Treasury']['type'] == Treasury::TYPE_BANK) {
                $this->loadModel('Journal');
                Journal::$auto_accounts['treasury']['next_journal_cat_type']= 'banks';
            }
        }

        if (ifPluginActive(PosPlugin) && !empty($data[$this->alias]['pos_shift_id'])) {
            $this->loadModel('PosShift');
            $this->PosShift->recursive = -1;
            $staff_id = $this->PosShift->find('first',['conditions' => ['PosShift.id' => $data[$this->alias]['pos_shift_id']]])['PosShift']['staff_id'];
            $journalTransaction =[[
                'subkey'=>'staff_petty_cash',
                'currency_debit'=>$data[$this->alias]['amount'],
                'currency_code'=>$data[$this->alias]['currency_code'],
                'description'=>$description,
                'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'staff_petty_cash','entity_id'=>$staff_id)
            ]];
        }
        $feesFactor = 1;
        if ($data[$this->alias]['amount'] < 0)
            $feesFactor = -1;
        $paymentFees = ($feesFactor *(float) $paymentSettings['fixed_rate']) + ((float)$paymentSettings['percentage_rate'] * (float)$data[$this->alias]['amount'] / 100);
        if (abs($paymentFees) < $paymentSettings['minimum_amount'])
            $paymentFees = (float) $feesFactor * (float) $paymentSettings['minimum_amount'];

        if (abs($paymentFees) > 0) {
            if(!empty($paymentSettings['tax_id']))
            {
                $this->loadModel('Tax');
                $tax=$this->Tax->findById($paymentSettings['tax_id']);
                if(!empty($tax))
                {
                    $tax_amount=$this->Tax->getTaxAmount($tax,$paymentFees);
                    if(abs($tax_amount)>0)
                    {
                        if($tax['Tax']['included'])
                        {
                            $paymentFees-=$tax_amount;
                        }
                        $journalTransaction[] = [
                            'subkey'=>'payment_fees_tax',
                            'currency_debit'=>$tax_amount,
                            'currency_code'=>$data[$this->alias]['currency_code'],
                            'description'=>$description . ' ' . sprintf(__('%s Payment Fees Taxes', true), $data[$this->alias]['payment_method']),
                            'auto_account'=>array('type'=>'dynamic' ,  'entity_type'=>'outcome_tax','entity_id'=>$tax['Tax']['id'])
                        ];

                    }


                }
            }
            $journalTransaction[0]['currency_debit'] -= ($paymentFees +$tax_amount);
            $journalTransaction[] = [
                'subkey'=>'payment_account_fees',
                'currency_debit'=>$paymentFees,
                'currency_code'=>$data[$this->alias]['currency_code'],
                'description'=>$description . ' ' . sprintf(__('%s Payment Fees', true), $data[$this->alias]['payment_method']),
                'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'payment_account_fees','entity_id'=>$paymentSettings['id'])
            ];
        }

        $journal['JournalTransaction'] = $journalTransaction;

         $journal['JournalTransaction'][]=
            array(
                    'subkey'=>'client', 
                    'currency_credit'=>$data[$this->alias]['amount'],
                    'currency_code'=>$data[$this->alias]['currency_code'],
                    'description'=>$description,
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'client','entity_id'=>$data[$this->alias]['client_id'])
                );
        
         if($data[$this->alias]['payment_method']=='starting_balance')
         {
             $journal['JournalTransaction'][0]['auto_account']=array('type'=>'fixed' , 'entity_type'=> 'opening_balance','entity_id'=>0);
         }

         if($data[$this->alias]['currency_code']!=$default_currency)
         {
             if($data[$this->alias]['date']!=$data['Invoice']['date'])
             {
                 $this->loadModel('LocalCurrencyRate');
                 $currencyRatesCount = LocalCurrencyRate::getCurrencyRatesCount();
                 $invoice_rate = 0;
                 $invoice_payment_rate = 0;
                 if(isset($currencyRatesCount[$data['Invoice']['currency_code']])) {
                     $invoice_rate =  CurrencyConverter::index($data['Invoice']['currency_code'], $default_currency, $data['Invoice']['date']);
                     $invoice_payment_rate =  CurrencyConverter::index($data['Invoice']['currency_code'], $default_currency, $data[$this->alias]['date']);
                 } else {
                     $this->loadModel('Journal');
                     $invoiceJournal = $this->Journal->find('first', ['conditions' => ['Journal.entity_type' => [
                         JournalUtil::AUTO_JOURNAL_TYPE_INVOICE,
                         JournalUtil::AUTO_JOURNAL_TYPE_CREDIT_NOTE,
                         JournalUtil::AUTO_JOURNAL_TYPE_REFUND_RECEIPT
                     ], 'Journal.entity_id' => $data['Invoice']['id']]]);
                     $paymentJournal = $this->Journal->find('first', ['conditions' => ['Journal.entity_type' => JournalUtil::AUTO_JOURNAL_TYPE_INVOICE_PAYMENT, 'Journal.entity_id' => $data[$this->alias]['id']]]);
                    $invoice_rate = $invoiceJournal['Journal']['currency_rate'];
                    $invoice_payment_rate = $paymentJournal['Journal']['currency_rate'];
                 }
                if(!empty($invoice_rate) && $invoice_rate !=$invoice_payment_rate)
                {
                     $amount_difference=($invoice_rate-$invoice_payment_rate)*$data[$this->alias]['amount'];
                     $description=__('Currency Exchange Rate Difference',true).' '.$description;
                     $journals=[];
                     $journals[0]=$journal;
                     $journals[1]['Journal']['date']=$data[$this->alias]['date'];
                     $journals[1]['Journal']['entity_type']='invoice_payment_currency_rate_difference';
                     $journals[1]['Journal']['description']=$description;
                     $journals[1]['Journal']['entity_id']=$data[$this->alias]['id'];

                     $journals[1]['JournalTransaction'][]=
                         array(
                             'subkey'=>'client',
                             'currency_credit'=>$amount_difference,
                             'currency_code'=>$default_currency,
                             'description'=>$description,
                             'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'client','entity_id'=>$data[$this->alias]['client_id'])
                         );

                     $journals[1]['JournalTransaction'][]=
                         array(
                             'subkey'=>'currency_exchange',
                             'currency_debit'=>$amount_difference,
                             'currency_code'=>$default_currency,
                             'description'=>$description,
                             'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'currency_exchange','entity_id'=>0)
                         );

                     return $journals;

                 } else {
                    $this->loadModel('Journal');
                    $diffJournal = $this->Journal->find('first', ['conditions' => ['Journal.entity_id' => $data[$this->alias]['id'], 'Journal.entity_type' => 'invoice_payment_currency_rate_difference']]);
                    if(!empty($diffJournal)) {
                        $this->Journal->delete_journal($diffJournal['Journal']['id']);
                    }
                }
             }
             $this->loadModel('Journal');
             $exchange_journal=$this->Journal->find('first',['conditions'=>['Journal.entity_id'=>$data[$this->alias]['id'],'Journal.entity_type'=>'invoice_payment_currency_rate_difference']]);
             if(!empty($exchange_journal)) $this->Journal->delete_journal($exchange_journal['Journal']['id']);

         } else {
             $this->loadModel('Journal');
             $exchange_journal=$this->Journal->find('first',['conditions'=>['Journal.entity_id'=>$data[$this->alias]['id'],'Journal.entity_type'=>'invoice_payment_currency_rate_difference']]);
             if(!empty($exchange_journal)) $this->Journal->delete_journal($exchange_journal['Journal']['id']);
         }

         return $journal;
       
      
    }
    
    function SetTreasuryForPayment($data){
        
        if($data['InvoicePayment']['added_by']!=0){
            return $data;
        }
        $this->loadModel('SitePaymentGateway');
        $this->loadModel('Treasury');
        $pt=$this->SitePaymentGateway->find('first',array('conditions'=>array('SitePaymentGateway.treasury_id is not null','SitePaymentGateway.payment_gateway'=> $data['InvoicePayment']['payment_gateway'])));
        if(isset($pt['SitePaymentGateway']['treasury_id'])){
        $data['InvoicePayment']['treasury_id']=$pt['SitePaymentGateway']['treasury_id'];
        }else{
        $data['InvoicePayment']['treasury_id']=$this->Treasury->get_primary();    
        }
        //treasury_id
        //
        return $data;
    }

    public function create_stripe_customer_session($stripe_secret_key, $client_id){
        require_once dirname(dirname(__FILE__)) . '/lib/StripeSca.php';
        $stripe = new \Stripe\StripeClient($stripe_secret_key);
        $customer = $stripe->customers->search(['query' => "metadata['client_id']:'{$client_id}'"]);
        if(empty($customer->data)){
            $customer = $stripe->customers->create(['metadata' => ['client_id' => "{$client_id}"]]);
        }else{
            $customer = $customer->data[0];
        }
        $customer_session = $stripe->customerSessions->create([
            'customer' => $customer->id,
            'components' => [
            'payment_element' => [
                'enabled' => true,
                    'features' => [
                        'payment_method_redisplay' => 'enabled',
                        'payment_method_save' => 'enabled',
                        'payment_method_save_usage' => 'on_session',
                        'payment_method_remove' => 'enabled',
                    ],
                ],
            ],
        ]);

        return $customer_session->client_secret;
    }
}
