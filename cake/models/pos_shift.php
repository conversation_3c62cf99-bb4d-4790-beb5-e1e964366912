<?php

App::import('Model', 'Category', array('file' => 'category.php'));

class PosShift extends AppModel {
    var $applyBranch = ['onFind' => true, 'onSave' => true];
    var $name = 'PosShift';
    var $filters = array();

    const STATUS_OPENED = 1;
    const STATUS_CLOSED = 2;
    const STATUS_VALIDATED = 3;
    var $belongsTo = [
        'Store' => [
            'className' => 'Store',
            'foreignKey' => 'warehouse_id'
        ],
        'Staff' => [
            'className' => 'Staff',
            'foreignKey' => 'staff_id'
        ],
        'ValidateStaff' => [
            'className' => 'Staff',
            'foreignKey' => 'validated_by'
        ],
        'Shift' => [
            'className' => 'Category',
            'conditions' => ['Shift.category_type' => Category::CATEGORY_TYPE_Shift],
            'foreignKey' => 'shift_id'
        ],
        'PosDevice' => [
            'className' => 'Category',
            'conditions' => ['PosDevice.category_type' => Category::CATEGORY_TYPE_POS_Device],
            'foreignKey' => 'pos_id'
        ]
    ];

    var $hasMany = [
        'PosShiftTransaction' => [
            'className' => 'PosShiftTransaction',
            'foreignKey' => 'pos_shift_id'
        ],
        'Invoices' => [
            'className' => 'Invoice',
            'foreignKey' => 'pos_shift_id'
        ]
    ];

    var $actsAs = array(
        'journal' => array(
            'is_journal'=>true,
        )
    );

    static $PosShiftProductTotalPricesAndDiscounts = [];

    function __construct($id = false, $table = null, $ds =null){
        parent::__construct($id, $table, $ds);
        $this->validate = array();
    }

    function getSortFields($sorted_by = []) {
        return array(
            array('title' => __('Created Date',true),'field' => 'created' ,'default' => 'desc'),
            array('title' => __('Staff',true),'field' => 'staff_id' ),
            array('title' => __('Status',true),'field' => 'status' ),
            array('title' => __('Open Date',true),'field' => 'open_time','default' => 'desc' ),
            array('title' => __('Close Date',true),'field' => 'close_time','default' => 'desc' ),
            'default_order' => $sorted_by
        );
    }

    function getFilters(){
        return array(
            'pos_id' => array('div_class' => 'full-width', 'more-options' => false, 'empty' => __('Any POS Device', true), 'label' => __('Device', true)),
            'shift_id' => array('div_class' => 'full-width', 'more-options' => false, 'empty' => __('Any POS Shift', true), 'label' => __('Shift', true)),
            'status' => array('div_class' => 'full-width' ,'more-options' => false, 'empty' => __('Any Status', true), 'label' => __('Status', true)),
            'staff_id' => array('more-options' => true, 'empty' => __('Any Staff', true), 'label' => __('Added By', true)),
            'open_time' => array('more-options' => true,'label' =>  __('Open Date',true), 'type' => 'date_range', 'to_label' => __('Open Date To', true), 'from_label' => __('Open Date From', true)),
            'close_time' => array('more-options' => true,'label' =>  __('Close Date',true), 'type' => 'date_range', 'to_label' => __('Close Date To', true), 'from_label' => __('Close Date From', true)),
            'id' => array('type' => 'like', 'more-options' => false, 'label' => __('Session ID', true), 'input_type' => 'text'),
        );
    }

    function getStatuses(){
        return [
            PosShift::STATUS_OPENED => __('Opened',true),
            PosShift::STATUS_CLOSED => __('Closed',true),
            PosShift::STATUS_VALIDATED => __('Validated',true)
        ];
    }

    /**
     * @param $id
     * @return array
     * get session invoice payment summary by calculating the sum of sales/refund payments and
     * what is actually received from those payments and returns this summary
     * mapped by its related treasury or staff
     */
    function getSessionData($id){
        $shiftTotals = [];
        $this->loadModel('Invoice');
        $this->loadModel('PosShift');
        $posShift = $this->PosShift->find('first',['recursive' => -1, 'conditions' => ['PosShift.id' => $id]]);
        $allowedNumberOfDigitsAfterFraction = getAllowedNumberOfDigitsAfterFraction($posShift['PosShift']['currency_code']);
        $shiftTotals['total_sale'] =$this->Invoice->find('first',['recursive' => -1, 'conditions' => ['pos_shift_id' => $id,'type' => Invoice::Invoice],'fields' => ['SUM(summary_total) as total_sale']])[0]['total_sale'];
        $shiftTotals['total_refund'] = $this->Invoice->find('first',['recursive' => -1, 'conditions' => ['pos_shift_id' => $id,'type' => Invoice::Refund_Receipt],'fields' => ['SUM(summary_total) as total_refund']])[0]['total_refund'];
        $shiftTotals['total_cash_in'] = $this->PosShiftTransaction->find('first',['recursive' => -1, 'conditions' => ['pos_shift_id' => $id,'type' => PosShiftTransaction::TYPE_IN],'fields' => ['SUM(amount) as total_cash_in']])[0]['total_cash_in'];
        $shiftTotals['total_cash_out'] = $this->PosShiftTransaction->find('first',['recursive' => -1, 'conditions' => ['pos_shift_id' => $id,'type' => PosShiftTransaction::TYPE_OUT],'fields' => ['SUM(amount) as total_cash_out']])[0]['total_cash_out'];
        $shiftTotals['total_cash_movement'] = $shiftTotals['total_cash_in'] - $shiftTotals['total_cash_out'];
        $shiftTotals['total'] = 0;
        $shiftTotals['totalCounted'] = 0;
        $payments_sales_query = "SELECT InvoicePayment.id,InvoicePayment.payment_method,InvoicePayment.amount AS total_sale,ROUND(InvoicePayment.amount, $allowedNumberOfDigitsAfterFraction) AS rounded_total_sale, InvoicePayment.extra_details FROM invoice_payments InvoicePayment JOIN invoices Invoice ON InvoicePayment.invoice_id = Invoice.id
                                  WHERE Invoice.type = ".Invoice::Invoice." AND InvoicePayment.pos_shift_id = {$id} AND InvoicePayment.payment_method <> 'client_credit'";
        $payments_sales_result = $this->Invoice->query($payments_sales_query);
        $payments_refund_query = "SELECT InvoicePayment.id,InvoicePayment.payment_method,InvoicePayment.amount AS total_refund, InvoicePayment.extra_details FROM invoice_payments InvoicePayment JOIN invoices Invoice ON InvoicePayment.invoice_id = Invoice.id
                                  WHERE Invoice.type = ".Invoice::Refund_Receipt." AND InvoicePayment.pos_shift_id = {$id}";
        $payments_refund_result = $this->Invoice->query($payments_refund_query);
        $this->loadModel('PosShiftSummary');
        $shiftValidationResult = $this->PosShiftSummary->find('all',['recursive' => -1, 'conditions' => ['pos_shift_id' => $id,'type' => 'PaymentMethod']]);
        $shiftValidationData = [];
        $payments_totals = [];
        foreach ($shiftValidationResult as $item){
            //here we calculate what is actually received from every payment methods
            $item = $item['PosShiftSummary'];
            $value = $item['value'];
            $shiftValidationData[$item['key']] = ['value' => $value,'journal_entity' => $item['journal_entity'],'entity_id' => $item['entity_id']];
            $payments_totals[$item['key']]['counted'] = $value;
            $payments_totals[$item['key']]['entity_id'] = $item['entity_id'];
            $payments_totals[$item['key']]['journal_entity'] = $item['journal_entity'];
            $shiftTotals['totalCounted'] += $value;
        }
        foreach ($payments_sales_result as $payments_sale) {
            //here we calculate the sum of the invoice payments of sales invoices
            $total_sale = $payments_sale[0]['rounded_total_sale'];
            if (!isset($payments_totals[$payments_sale['InvoicePayment']['payment_method']])) {
                $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['total_sale'] = $total_sale;
                $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['total'] = $total_sale;
                $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['payment_fees'] = 0;
            } else {
                $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['total_sale'] += $total_sale;
                $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['total'] += $total_sale;
            }
            $paymentSettings = json_decode($payments_sale['InvoicePayment']['extra_details'],true);
            if ($paymentSettings) {
                //here we calculate the payment fees it could be percentage or amount
                $paymentSettings = $paymentSettings['paymentSetting'];
                $paymentFees = $paymentSettings['fixed_rate'] + ((float)$paymentSettings['percentage_rate'] * (float)$payments_sale['InvoicePayment']['total_sale'] / 100);
                if (abs($paymentFees) < $paymentSettings['minimum_amount'])
                    $paymentFees = $paymentFees > 0 ? $paymentSettings['minimum_amount'] : -1 * $paymentSettings['minimum_amount'];
                $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['payment_fees'] += $paymentFees;
                $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['payment_fees_id'] = $paymentSettings['id'];
                $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['tax_id'] = $paymentSettings['tax_id'] ?? null;
            }
            $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['counted'] = $shiftValidationData[$payments_sale['InvoicePayment']['payment_method']]['value'];
            $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['entity_id'] = $shiftValidationData[$payments_sale['InvoicePayment']['payment_method']]['entity_id'];
            $payments_totals[$payments_sale['InvoicePayment']['payment_method']]['journal_entity'] = $shiftValidationData[$payments_sale['InvoicePayment']['payment_method']]['journal_entity'];
            $shiftTotals['total'] += $total_sale;
        }
        foreach ($payments_refund_result as $payments_refund){
            //here we calculate the sum of the invoice payments of refund invoices
            if(!isset($payments_totals[$payments_refund['InvoicePayment']['payment_method']])) {
                $payments_totals[$payments_refund['InvoicePayment']['payment_method']] = [];
                $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['total_refund'] = 0;
                $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['total'] = 0;
                $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['payment_fees'] = 0;
            }
            $paymentSettings = json_decode($payments_refund['InvoicePayment']['extra_details'],true);
            if ($paymentSettings) {
                //here we calculate the payment fees it could be percentage or amount
                $paymentSettings = $paymentSettings['paymentSetting'];
                $paymentFees = (-1 * $paymentSettings['fixed_rate']) + (((float) trim($paymentSettings['percentage_rate'])) * $payments_refund['InvoicePayment']['total_refund'] / 100);
                if (abs($paymentFees) < $paymentSettings['minimum_amount'])
                    $paymentFees = $paymentFees > 0 ? $paymentSettings['minimum_amount'] : -1 * $paymentSettings['minimum_amount'];
                $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['payment_fees'] += $paymentFees;
                $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['payment_fees_id'] = $paymentSettings['id'];
                $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['tax_id'] = $paymentSettings['tax_id'] ?? null;
            }
            $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['total_refund'] += $payments_refund['InvoicePayment']['total_refund'];
            $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['total'] += $payments_refund['InvoicePayment']['total_refund'];
            $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['counted'] = $shiftValidationData[$payments_refund['InvoicePayment']['payment_method']]['value'];
            $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['entity_id'] = $shiftValidationData[$payments_refund['InvoicePayment']['payment_method']]['entity_id'];
            $payments_totals[$payments_refund['InvoicePayment']['payment_method']]['journal_entity'] = $shiftValidationData[$payments_refund['InvoicePayment']['payment_method']]['journal_entity'];
            //$shiftTotals['total'] += $payments_refund['InvoicePayment']['total_refund'];
        }
        if(!$payments_totals['cash']){
            $payments_totals['cash']['total'] = 0;
        }
        $summaryQueryResult = $this->Invoice->find('first', ['recursive' => -1,
            'conditions' => ['pos_shift_id' => $id,'type' => Invoice::Invoice],
            'fields' => [
                "SUM(ROUND(summary_paid, $allowedNumberOfDigitsAfterFraction)) as summary_total_paid",
                "SUM(ROUND(summary_unpaid, $allowedNumberOfDigitsAfterFraction)) as summary_total_unpaid",
                "SUM(ROUND(summary_total, $allowedNumberOfDigitsAfterFraction)) as summary_total_sales",
            ]
        ])[0];
        $summaryQueryResultRefund = $this->Invoice->find('first', ['recursive' => -1,
            'conditions' => ['pos_shift_id' => $id,'type' => Invoice::Refund_Receipt],
            'fields' => [
                "SUM(ROUND(summary_total, $allowedNumberOfDigitsAfterFraction)) as summary_total_refunds",
            ]
        ])[0];
        $shiftTotals['summary_total_paid'] = $summaryQueryResult['summary_total_paid'];
        $shiftTotals['summary_total_unpaid'] = $summaryQueryResult['summary_total_unpaid'];
        $shiftTotals['summary_total_sales'] = $summaryQueryResult['summary_total_sales'];
        $shiftTotals['summary_total_refunds'] = $summaryQueryResultRefund['summary_total_refunds'];
        $shiftTotals['summary_total_net'] = $summaryQueryResult['summary_total_sales'] - $summaryQueryResultRefund['summary_total_refunds'];
        $payments_totals['cash']['total'] += $shiftTotals['total_cash_in'];
        $payments_totals['cash']['total'] -= $shiftTotals['total_cash_out'];
        $shiftTotals['total'] += $shiftTotals['total_cash_in'];
        $shiftTotals['total'] -= $shiftTotals['total_cash_out'];
        $shiftTotals['payment'] = $payments_totals;
        return $shiftTotals;
    }

    function getSessionItemsData($shift_id, $getSales = true) {
        $this->loadModel('Invoice');
        $totalProductsResult = $this->query("SELECT iv.product_id,iv.item,SUM(iv.subtotal) AS total,SUM(iv.quantity) AS quantity,iv.store_id,iv.unit_price,iv.description FROM invoice_items iv
                                JOIN invoices i ON i.id = iv.invoice_id AND i.pos_shift_id = {$shift_id} AND type = ".($getSales ? Invoice::Invoice : Invoice::Refund_Receipt)." GROUP BY iv.product_id");
        return $totalProductsResult;
    }

    function update_sales_journal($data){
        $shift_id = $data[$this->alias]['id'] ?: $this->id;

        $debit='debit';
        $credit='credit';

        $entity_type = 'pos_shift_sales';
        $description=__('POS Session',true).' #'.$shift_id.' '.__('sales',true);

        $journal['Journal']['date']=$data[$this->alias]['close_time'];
        $journal['Journal']['entity_type']=$entity_type;
        $journal['Journal']['description']=$description;
        $journal['Journal']['entity_id']=$shift_id;
        $journal['Journal']['currency_code']=$data[$this->alias]['currency_code'];

        $this->loadModel('Invoice');
        $fractions = getAllowedNumberOfDigitsAfterFraction($journal['Journal']['currency_code']);
        $totals = $this->query("SELECT SUM(round(summary_total,{$fractions})) AS total ,sum(summary_discount) as total_discount FROM invoices WHERE pos_shift_id = {$shift_id} AND type = ".Invoice::Invoice);
        $invoiceType = Invoice::Invoice;
        $total_discount = $this->query("SELECT SUM(calculated_discount) as total_discount FROM invoice_items WHERE invoice_id in (SELECT id FROM invoices WHERE pos_shift_id = {$shift_id} AND type = {$invoiceType})");
        $total = $totals[0][0]['total'];
        $total_discount = $total_discount[0][0]['total_discount'];
        if (!$total) {
            return false;
        }
        $totalTaxesResult = $this->query("SELECT t.id,t.name,SUM(iv.summary_tax1) AS total FROM invoice_items iv LEFT JOIN taxes t ON t.id = iv.tax1 
                                JOIN invoices i ON i.id = iv.invoice_id AND i.pos_shift_id = {$shift_id} AND type = ".Invoice::Invoice." WHERE t.name IS NOT NULL GROUP BY t.id");
        $totalTaxesResult = array_merge($totalTaxesResult,$this->query("SELECT t.id,t.name,SUM(iv.summary_tax2) AS total FROM invoice_items iv LEFT JOIN taxes t ON t.id = iv.tax2 
                                JOIN invoices i ON i.id = iv.invoice_id AND i.pos_shift_id = {$shift_id} AND type = ".Invoice::Invoice." WHERE t.name IS NOT NULL GROUP BY t.id"));

        $totalTaxes = [];
        foreach ($totalTaxesResult as $item){
            if(isset($totalTaxes[$item['t']['id']]))
                $totalTaxes[$item['t']['id']]['total'] += $item[0]['total'];
            else
                $totalTaxes[$item['t']['id']] = $item[0];
            $totalTaxes[$item['t']['id']]['name'] = $item['t']['name'];
        }

        $journal['JournalTransaction'][0]=
            array(
                'subkey'=>'sales',
                'currency_'.$credit=>$total,
                'currency_code'=>$data[$this->alias]['currency_code'],
                'description'=>$description,
                'auto_account'=>array('type'=>'fixed' , 'entity_type'=> 'sales','entity_id'=>0)
            );

        $is_discount_allowed = settings::getValue(AccountingPlugin , "discount_allowed_accounts_routing") ;
        $is_discount_allowed = (!empty($is_discount_allowed) && $is_discount_allowed!=Settings::CANCEL_ACCOUNTS_ROUTING);

        if($is_discount_allowed)
        {
            $journal['JournalTransaction'][0]['currency_'.$credit]+=$total_discount;
            $journal['JournalTransaction'][]=
                array(
                    'subkey'=>'discount',
                    'currency_'.$debit=>$total_discount,
                    'currency_code'=>$data[$this->alias]['currency_code'],
                    'description'=>$description.' - '.__('Discount',true),
                    'auto_account'=>array('type'=>'dynamic', 'entity_type'=>'discount_allowed','entity_id'=>0)
                );
        }


        $adjustment_routing = settings::getValue(AccountingPlugin , "adjustment_accounts_routing") ;
        $is_adjustment_allowed = (!empty($adjustment_routing) && $adjustment_routing!=Settings::CANCEL_ACCOUNTS_ROUTING);
        if ($is_adjustment_allowed) {
            $this->loadModel('Journal');
            $this->loadModel('JournalAccountRoute');
            $adjustment_value = $this->query("SELECT SUM(adjustment_value) AS total FROM invoices WHERE pos_shift_id = {$shift_id} AND type = ".Invoice::Invoice)[0][0]['total'];
            $adjustmentTransaction = [
                'subkey'=> Journal::ADJUSTMENT_ACCOUNT_ENTITY_TYPE,
                'currency_' . $credit => $adjustment_value,
                'currency_code' => $data[$this->alias]['currency_code'],
                'description'=> $description.' - '.__('Adjustment',true),
            ];
            $this->JournalAccountRoute->addAdjustmentJournalTransactionForPos($adjustmentTransaction);
            $journal['JournalTransaction'][] = $adjustmentTransaction;
            $journal['JournalTransaction'][0]['currency_' . $credit] -= $adjustment_value;
        }

        //calculate shipping taxes
        $invoicesWithShipping = $this->Invoice->find('all', ['conditions' => ['Invoice.pos_shift_id' => $shift_id, 'Invoice.shipping_amount >' => 0]]);
        $TaxObject = GetObjectOrLoadModel('Tax');
        $ShippingOptionModel = GetObjectOrLoadModel('ShippingOption');
        $shippingAccounts = $ShippingOptionModel->find('list', ['fields' => ['ShippingOption.id', 'ShippingOption.account_id']]);

        foreach ($invoicesWithShipping as $invoice) {
            $invoice = $this->Invoice->getInvoice($invoice['Invoice']['id']);
            $shippingAmount = (float)$invoice['Invoice']['shipping_amount'];
            $tax = $TaxObject->find('first', ['conditions' => ['Tax.id' => $invoice['Invoice']['shipping_tax_id']]]);
            $invoiceShippingTax = $TaxObject->getTaxAmount($tax, $invoice['Invoice']['shipping_amount']);
            if(!isset($totalTaxes[$invoice['Invoice']['shipping_tax_id']])) {
                $totalTaxes[$invoice['Invoice']['shipping_tax_id']] = ['total' => $invoiceShippingTax, 'name' => $tax['Tax']['name']];
            } else {
                $totalTaxes[$invoice['Invoice']['shipping_tax_id']]['total'] += $invoiceShippingTax;
            }
            $shippingTax = !empty($invoice['InvoiceTax'][$invoice['Invoice']['shipping_tax_id']]) ? $invoice['InvoiceTax'][$invoice['Invoice']['shipping_tax_id']]: [];
            $inclusiveTax = $this->Invoice->getInclusiveTaxAmount($shippingAmount, $shippingTax);
            $TaxObject->getTaxAmount($tax, $invoice['Invoice']['shipping_amount']);
            //Deduct the tax from the sales account, and add to the tax account
            $journal['JournalTransaction'][0]['currency_'.$credit]-=$shippingAmount - $inclusiveTax;
            $shippingAccount = $shippingAccounts[$invoice['Invoice']['shipping_option_id']];
            $journal['JournalTransaction'][]=
                [
                    'subkey'=>'shipping',
                    'currency_'.$credit=>$shippingAmount - $inclusiveTax,
                    'currency_code'=>$invoice['Invoice']['currency_code'],
                    'description'=>$description.' '.__('Shipping',true),
                    'journal_account_id' => $shippingAccount,
                    'auto_account'=> ['type'=>'static' , 'entity_type'=>'sales_shipping','entity_id'=>0]
                ];
        }

        $product_sales_routing = settings::getValue(AccountingPlugin,'product_sales_accounts_routing');
        if(!empty($product_sales_routing) && $product_sales_routing == settings::MANUAL_ACCOUNTS_ROUTING) {
            $invoices = $this->Invoice->find('all', ['conditions' => ['Invoice.pos_shift_id' => $shift_id]]);
            $Product = GetObjectOrLoadModel('Product');

            foreach ($invoices as $invoice) {
                foreach ($invoice['InvoiceItem'] as $item) {
                    if(!empty($item['product_id']))
                    {
                        $account_id = $Product->getSalesAccount($item['product_id']);
                        if(!empty($account_id))
                        {
                            $product_sales_amount=$item['subtotal'];
                            //Deduct taxes from subtotla if it is applied
                            if(!empty($item['summary_tax1']))
                                $product_sales_amount-=$item['summary_tax1'];

                            if(!empty($item['summary_tax2']))
                                $product_sales_amount-=$item['summary_tax2'];

                            if($is_discount_allowed)
                                $product_sales_amount+=$item['calculated_discount'];

                            if(!isset($journal['JournalTransaction']['product_sales_'.$item['product_id']])) {
                                $journal['JournalTransaction']['product_sales_'.$item['product_id']]=
                                    array(
                                        'subkey'=>'product_sales_'.$item['product_id'].'_'.$item['id'],
                                        'currency_'.$credit=> 0,
                                        'currency_code'=>$data['Invoice']['currency_code'],
                                        'description'=>$description.' - '.__('Sales',true).' - '.$item['item'],
                                        'journal_account_id'=>$account_id
                                    );
                            }
                            $journal['JournalTransaction']['product_sales_'.$item['product_id']]['currency_'.$credit] += $product_sales_amount;
                            $journal['JournalTransaction'][0]['currency_'.$credit]-= $product_sales_amount;
                        }
                    }
                }
            }
        }

        foreach ($totalTaxes as $id => $tax){
            //Deduct the tax from the sales account, and add to the tax account
            $journal['JournalTransaction'][0]['currency_'.$credit]-=$tax['total'];
            $journal['JournalTransaction'][]=
                array(
                    'subkey'=>$tax['name'],
                    'currency_'.$credit=>$tax['total'],
                    'currency_code'=>$data[$this->alias]['currency_code'],
                    'description'=>$description.' '.$tax['name'].' '.__('Tax',true),
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=>'income_tax','entity_id'=>$id)
                );
        }
        $journal['JournalTransaction'][]=
            array(
                'subkey'=>'cashier',
                'currency_'.$debit=> $total,
                'currency_code'=>$data[$this->alias]['currency_code'],
                'description'=>$description,
                'auto_account'=> array('type'=>'dynamic' , 'entity_type'=> 'staff_petty_cash','entity_id'=>$data[$this->alias]['staff_id'])
            );
        return $journal;
    }

    function update_refund_journal($data){
        $shift_id = $data[$this->alias]['id'] ?: $this->id;

        $debit='credit';
        $credit='debit';

        $entity_type = 'pos_shift_refund';
        $description=__('POS shift',true).' #'.$shift_id.' '.__('refund',true);

        $journal['Journal']['date']=$data[$this->alias]['close_time'];
        $journal['Journal']['entity_type']=$entity_type;
        $journal['Journal']['description']=$description;
        $journal['Journal']['entity_id']=$shift_id;
        $journal['Journal']['currency_code']=$data[$this->alias]['currency_code'];

        $this->loadModel('Invoice');

        $totals = $this->query("SELECT SUM(summary_total) AS total,sum(summary_discount) as total_discount FROM invoices WHERE pos_shift_id = {$shift_id} AND type = ".Invoice::Refund_Receipt);
        $total = $totals[0][0]['total'];
        $invoiceType = Invoice::Refund_Receipt;
        $total_discount = $this->query("SELECT SUM(calculated_discount) as total_discount FROM invoice_items WHERE invoice_id in (SELECT id FROM invoices WHERE pos_shift_id = {$shift_id} AND type = {$invoiceType})");
        $total_discount = $total_discount[0][0]['total_discount'];
        if (!$total)
            return false;
        $totalTaxesResult = $this->query("SELECT t.id,t.name,SUM(iv.summary_tax1) AS total FROM invoice_items iv LEFT JOIN taxes t ON t.id = iv.tax1 
                                JOIN invoices i ON i.id = iv.invoice_id AND i.pos_shift_id = {$shift_id} AND type = ".Invoice::Refund_Receipt." WHERE t.name IS NOT NULL GROUP BY t.id");
        $totalTaxesResult = array_merge($totalTaxesResult,$this->query("SELECT t.id,t.name,SUM(iv.summary_tax2) AS total FROM invoice_items iv LEFT JOIN taxes t ON t.id = iv.tax2 
                                JOIN invoices i ON i.id = iv.invoice_id AND i.pos_shift_id = {$shift_id} AND type = ".Invoice::Refund_Receipt." WHERE t.name IS NOT NULL GROUP BY t.id"));

        $totalTaxes = [];
        foreach ($totalTaxesResult as $item){
            if($totalTaxes[$item['t']['id']])
                $totalTaxes[$item['t']['id']] += $item[0];
            else
                $totalTaxes[$item['t']['id']] = $item[0];
            $totalTaxes[$item['t']['id']]['name'] = $item['t']['name'];
        }

        $journal['JournalTransaction'][0]=
            array(
                'subkey'=>'returns',
                'currency_'.$credit=>$total,
                'currency_code'=>$data[$this->alias]['currency_code'],
                'description'=>$description,
                'auto_account'=>array('type'=>'fixed' , 'entity_type'=> 'returns','entity_id'=>0)
            );
        $is_discount_allowed = settings::getValue(AccountingPlugin , "discount_allowed_accounts_routing") ;
        $is_discount_allowed = (!empty($is_discount_allowed) && $is_discount_allowed!=Settings::CANCEL_ACCOUNTS_ROUTING);

        //calculate shipping taxes
        $invoicesWithShipping = $this->Invoice->find('all', ['conditions' => ['Invoice.pos_shift_id' => $shift_id, 'Invoice.shipping_amount >' => 0]]);
        $TaxObject = GetObjectOrLoadModel('Tax');
        $ShippingOptionModel = GetObjectOrLoadModel('ShippingOption');
        $shippingAccounts = $ShippingOptionModel->find('list', ['fields' => ['ShippingOption.id', 'ShippingOption.account_id']]);

        foreach ($invoicesWithShipping as $invoice) {
            $invoice = $this->Invoice->getInvoice($invoice['Invoice']['id']);
            $shippingAmount = (float)$invoice['Invoice']['shipping_amount'];
            $tax = $TaxObject->find('first', ['conditions' => ['Tax.id' => $invoice['Invoice']['shipping_tax_id']]]);
            $invoiceShippingTax = $TaxObject->getTaxAmount($tax, $invoice['Invoice']['shipping_amount']);
            if(!isset($totalTaxes[$invoice['Invoice']['shipping_tax_id']])) {
                $totalTaxes[$invoice['Invoice']['shipping_tax_id']] = ['total' => $invoiceShippingTax, 'name' => $tax['Tax']['name']];
            } else {
                $totalTaxes[$invoice['Invoice']['shipping_tax_id']]['total'] += $invoiceShippingTax;
            }
            $shippingTax = !empty($invoice['InvoiceTax'][$invoice['Invoice']['shipping_tax_id']]) ? $invoice['InvoiceTax'][$invoice['Invoice']['shipping_tax_id']]: [];
            $inclusiveTax = $this->Invoice->getInclusiveTaxAmount($shippingAmount, $shippingTax);
            $TaxObject->getTaxAmount($tax, $invoice['Invoice']['shipping_amount']);
            //Deduct the tax from the sales account, and add to the tax account
            $journal['JournalTransaction'][0]['currency_'.$credit]-=$shippingAmount - $inclusiveTax;
            $shippingAccount = $shippingAccounts[$invoice['Invoice']['shipping_option_id']];
            $journal['JournalTransaction'][]=
                [
                    'subkey'=>'shipping',
                    'currency_'.$credit=>$shippingAmount - $inclusiveTax,
                    'currency_code'=>$invoice['Invoice']['currency_code'],
                    'description'=>$description.' '.__('Shipping',true),
                    'journal_account' => $shippingAccount,
                    'auto_account'=> ['type'=>'static' , 'entity_type'=>'sales_shipping','entity_id'=>0]
                ];
        }

        if($is_discount_allowed)
        {
            $journal['JournalTransaction'][0]['currency_'.$credit]+=$total_discount;
            $journal['JournalTransaction'][]=
                array(
                    'subkey'=>'discount',
                    'currency_'.$debit=>$total_discount,
                    'currency_code'=>$data[$this->alias]['currency_code'],
                    'description'=>$description.' - '.__('Discount',true),
                    'auto_account'=>array('type'=>'dynamic', 'entity_type'=>'discount_allowed','entity_id'=>0)
                );
        }

        $adjustment_routing = settings::getValue(AccountingPlugin , "adjustment_accounts_routing") ;
        $is_adjustment_allowed = (!empty($adjustment_routing) && $adjustment_routing!=Settings::CANCEL_ACCOUNTS_ROUTING);
        if ($is_adjustment_allowed) {
            $this->loadModel('Journal');
            $this->loadModel('JournalAccountRoute');
            $adjustment_value = $this->query("SELECT SUM(adjustment_value) AS total FROM invoices WHERE pos_shift_id = {$shift_id} AND type = ".Invoice::Refund_Receipt)[0][0]['total'];
            $adjustmentTransaction = [
                'subkey'=>Journal::ADJUSTMENT_ACCOUNT_ENTITY_TYPE,
                'currency_' . $credit => $adjustment_value,
                'currency_code' => $data[$this->alias]['currency_code'],
                'description'=> $description.' - '.__('Adjustment',true),
            ];
            $journal['JournalTransaction'][] = $this->JournalAccountRoute->addAdjustmentJournalTransactionForPos($adjustmentTransaction);
            $journal['JournalTransaction'][0]['currency_'.$credit] -= $adjustment_value;
        }

        foreach ($totalTaxes as $id => $tax){
            //Deduct the tax from the sales account, and add to the tax account
            $journal['JournalTransaction'][0]['currency_'.$credit]-=$tax['total'];
            $journal['JournalTransaction'][]=
                array(
                    'subkey'=>$tax['name'],
                    'currency_'.$credit=>$tax['total'],
                    'currency_code'=>$data[$this->alias]['currency_code'],
                    'description'=>$description.' '.$tax['name'].' '.__('Tax',true),
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=>'income_tax','entity_id'=>$id)
                );
        }
        $journal['JournalTransaction'][]=
            array(
                'subkey'=>'cashier',
                'currency_'.$debit=> $total,
                'currency_code'=>$data[$this->alias]['currency_code'],
                'description'=>$description,
                'auto_account'=> array('type'=>'dynamic' , 'entity_type'=> 'staff_petty_cash','entity_id'=>$data[$this->alias]['staff_id'])
            );


        return $journal;
    }

    function update_validate_journal($data){
        $shift_id = $data[$this->alias]['id'] ?: $this->id;

        $debit='credit';
        $credit='debit';

        $entity_type = 'pos_shift_validate';
        $description=__('POS Session',true).' #'.$shift_id.' '.__('validate',true);

        $journal['Journal']['date']=$data[$this->alias]['close_time'];
        $journal['Journal']['entity_type']=$entity_type;
        $journal['Journal']['description']=$description;
        $journal['Journal']['entity_id']=$shift_id;
        $journal['Journal']['currency_code']=$data[$this->alias]['currency_code'];

        $this->loadModel('SitePaymentGateway');
        $this->loadModel('Journal');

        $shiftData = $this->getSessionData($shift_id);
        $counted = 0;
        $total = 0;
        foreach ($shiftData['payment'] as $method => $paymentData){
            if ($paymentData['total'] || $paymentData['counted']) {
                if(isset($paymentData['counted'])) {
                    $journal['JournalTransaction'][] =
                        [
                            'subkey' => $method,
                            'currency_' . $credit => $paymentData['counted'] - ($paymentData['payment_fees'] ?? 0),
                            'currency_code' => $data[$this->alias]['currency_code'],
                            'description' => $description,
                            'auto_account' => ['type' => 'dynamic', 'entity_type' => $paymentData['journal_entity'], 'entity_id' => $paymentData['entity_id']]
                        ];
                }
                if (settings::getValue(PosPlugin, 'pos_auto_adjustment') === "0") {
                    $total += $paymentData['counted'];
                } else {
                    $total += $paymentData['total'];
                    $counted += $paymentData['counted'];
                }
                $accountingPerInvoice = $this->checkPosShiftIsCalculatedPerInvoice($data);
                if (abs($paymentData['payment_fees']) > 0) {
                    $tax_amount = 0;
                    if(!empty($paymentData['tax_id'])){
                        $this->loadModel('Tax');
                        $tax=$this->Tax->findById($paymentData['tax_id']);
                        if(!empty($tax))
                        {
                            $tax_amount=$this->Tax->getTaxAmount($tax,$paymentData['payment_fees']);
                        }   
                    }
                    if(!$accountingPerInvoice)
                    {
                            if(abs($tax_amount)>0)
                            {
                                if($tax['Tax']['included'])
                                {
                                    $paymentData['payment_fees'] -= $tax_amount;
                                }
                                $journal['JournalTransaction'][] = [
                                    'subkey'=>'payment_fees_tax',
                                    'currency_debit' => $tax_amount,
                                    'currency_code' => $data[$this->alias]['currency_code'],
                                    'description' => $description . ' ' . sprintf(__('%s Payment Fees Taxes', true), $method),
                                    'auto_account' => array('type'=>'dynamic' ,  'entity_type'=>'outcome_tax','entity_id'=>$tax['Tax']['id'])
                                ];

                                $transactionIndex = array_search($method, array_column($journal['JournalTransaction'], 'subkey'));
                                if(empty($tax['Tax']['included']))
                                {
                                    $journal['JournalTransaction'][$transactionIndex]['currency_debit'] -= $tax_amount;
                                }
                            }
                    }
                    if ($accountingPerInvoice) {
                        $total -= $paymentData['payment_fees'];
                        $counted -= $paymentData['payment_fees'];
                        foreach ($journal['JournalTransaction'] as $key => $transaction) {
                            if ($transaction['subkey'] == $method && !$tax['Tax']['included']) {
                                $journal['JournalTransaction'][$key]['currency_debit'] -= $tax_amount;
                            }
                        }
                        if($tax_amount > 0 && !$tax['Tax']['included']) {
                            $total -= $tax_amount;
                            $counted -= $tax_amount;
                        }
                    } else {
                        $journal['JournalTransaction'][] = [
                            'subkey' => 'payment_account_fees',
                            'currency_debit' => $paymentData['payment_fees'],
                            'currency_code' => $data[$this->alias]['currency_code'],
                            'description' => $description . ' ' . sprintf(__('%s Payment Fees', true), $method),
                            'auto_account' => ['type' => 'dynamic', 'entity_type' => 'payment_account_fees', 'entity_id' => $paymentData['payment_fees_id']]
                        ];
                    }
                }
            }
        }
        if (settings::getValue(PosPlugin, 'pos_auto_adjustment') !== "0") {
            $difference = $total - $counted;
            if (abs($difference) > 0.001) {
                $subkey = $difference > 0 ? 'losses' : 'profit';
                $amountKey = $difference > 0 ? 'currency_' . $credit : 'currency_' . $debit;
                $amount = abs($difference);
        
                $journal_account_id = false;
                $journalAccountType = $difference > 0 ? 'pos_losses_journal_account' : 'pos_profit_journal_account';
        
                // Get the journal account ID
                if (settings::getValue(PosPlugin, $journalAccountType)) {
                    $journal_account_id = settings::getValue(PosPlugin, $journalAccountType);
                }
                if (!$journal_account_id) {
                    $journalAccount = $this->Journal->get_auto_account(['entity_type' => 'cash_short_over', 'entity_id' => 0]);
                    if ($journalAccount) {
                        $journal_account_id = $journalAccount['JournalAccount']['id'];
                    }
                }
        
                // Add to journal transaction
                $journal['JournalTransaction'][] = [
                    'subkey' => $subkey,
                    $amountKey => $amount,
                    'currency_code' => $data[$this->alias]['currency_code'],
                    'description' => $description,
                    'journal_account_id' => $journal_account_id ?: null,
                    'auto_account' => $journal_account_id ? null : [
                        'type' => 'fixed',
                        'entity_type' => 'capital_profit_loss',
                        'entity_id' => 0
                    ]
                ];
            }
        }
        $journal['JournalTransaction'][]=
            array(
                'subkey'=>'cashier',
                'currency_'.$debit=> $total,
                'currency_code'=>$data[$this->alias]['currency_code'],
                'description'=>$description,
                'auto_account'=> array('type'=>'dynamic' , 'entity_type'=> 'staff_petty_cash','entity_id'=>$data[$this->alias]['staff_id'])
            );
        return $journal;
    }

    function get_journals($data){
        $shift_id = $data[$this->alias]['id'] ?: $this->id;
        $this->loadModel('Requisition');
        $this->Requisition->updateForPOS($shift_id);
        $calculatedPerInvoice = $this->checkPosShiftIsCalculatedPerInvoice($data);

        if($data[$this->alias]['status'] != PosShift::STATUS_VALIDATED && !defined('IGNORE_POS_STATUS')){
            $journal[0] = $this->update_sales_journal($data);
            $journal[1] = $this->update_refund_journal($data);
            $journal[2] = $this->update_validate_journal($data);
            unset($journal[0]['JournalTransaction']);
            unset($journal[1]['JournalTransaction']);
            unset($journal[2]['JournalTransaction']);
            return $calculatedPerInvoice ? $journal[2] : $journal; //remove all journals in case per shift remove only validate journal in case per invoice
        }

        $journal[0] = $this->update_sales_journal($data);
        $journal[1] = $this->update_refund_journal($data);
        $journal[2] = $this->update_validate_journal($data);
        if($calculatedPerInvoice) {
            unset($journal[0]['JournalTransaction']);
            unset($journal[1]['JournalTransaction']);
        }
        return $journal;
    }

    function getOpenedSessionID() {
        $session = new CakeSession;
        $shift_id = $session->read('shift_id');
        if($shift_id){
            $opened_session = $this->find('first',['conditions' => ['PosShift.status' => PosShift::STATUS_OPENED,'PosShift.id' => $shift_id]]);
            if($opened_session && (!ifPluginActive(BranchesPlugin) || (ifPluginActive(BranchesPlugin) && $opened_session['PosShift']['branch_id'] == getCurrentBranchID())) && !empty($opened_session['Store']['id'])) {
                return $shift_id;
            }
        }
        $user_id = getAuthOwner('staff_id');
        $opened_session = $this->find('first',['conditions' => ['PosShift.status' => PosShift::STATUS_OPENED,'PosShift.staff_id' => $user_id]]);
        if($opened_session && !empty($opened_session['Store']['id'])){
            $session->write('shift_id',$opened_session['PosShift']['id']);
            return $opened_session['PosShift']['id'];
        }
        return null;
    }

    function isThereAnyPosSessionOpen() {
        $this->loadModel('ItemPermission');
        $availableStores = array_keys($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING, null, true));
        $opened_session = $this->find('first',['conditions' => ['PosShift.status' => PosShift::STATUS_OPENED, 'PosShift.warehouse_id' => $availableStores]]);
        if (!empty($opened_session)) return true;
        return false;
    }

    function getPosShiftProductTotalPriceAndDiscount($posShiftID, $productID, $invoice_type = 0) {
        if(!isset(self::$PosShiftProductTotalPricesAndDiscounts[$posShiftID][$invoice_type])) {
            $query = "SELECT IV.product_id,SUM(IV.subtotal) AS summary_subtotal, SUM(IV.calculated_discount) AS summary_discount, sum(IV.summary_tax1) tax1_total, sum(IV.summary_tax2) tax2_total FROM invoice_items IV JOIN invoices I ON I.id = IV.invoice_id WHERE `I`.`pos_shift_id` = {$posShiftID}  AND `I`.`type` = {$invoice_type} GROUP BY IV.product_id";
            $results = $this->query($query, false);
            foreach ($results as $result) {
                self::$PosShiftProductTotalPricesAndDiscounts[$posShiftID][$invoice_type][$result["IV"]["product_id"]] = $result[0];
            }
        }
        return self::$PosShiftProductTotalPricesAndDiscounts[$posShiftID][$invoice_type][$productID] ?? [];
    }

    /**
     * this function check if the pos has any transactions or not to make sure it can be deleted
     * @param $id int
     * @return bool if it has transactions or not
     */
    function hasTransactions($id) {
        //Make sure that id is array
        if (!is_array($id)) {
            $id = [$id];
        }
        //Check if it has invoices
        $invoicesCount = $this->query('select count(*) as count from invoices where pos_shift_id in ('.implode(',',$id).')')[0][0]['count'];
        if ($invoicesCount > 0)
            return true;
        //Check if it has transactions
        $posTransactionsCount = $this->query('select count(*) as count from pos_shift_transactions where pos_shift_id in ('.implode(',',$id).')')[0][0]['count'];
        if ($posTransactionsCount > 0)
            return true;
        //it is safe to be deleted
        return false;
    }

    function isStoreUsed($store_id) {
        $used_in_pos_shifts = $this->find('first', ['recursive' => -1, 'conditions' => [
            'PosShift.warehouse_id' => $store_id
        ]]);
        $CategoryModel = GetObjectOrLoadModel('Category');
        $used_in_pos_devices = $CategoryModel->find('first', ['conditions' => [
            'Category.category_type' => Category::CATEGORY_TYPE_POS_Device,
            'Category.classification_id1' => $store_id
        ]]);
        if ($used_in_pos_devices) {
            return $used_in_pos_devices;
        }
        return $used_in_pos_shifts;
    }

    /**
     * @param $id
     * @return bool
     * checks if any invoice that belongs to a pos session has a journal
     */
    function checkPosShiftHasInvoiceJournals($id) {
        $query = "SELECT * from journals where (entity_type = 'invoice' or entity_type = 'refund_receipt') and entity_id in (select id from invoices where pos_shift_id = $id) limit 1";
        $result = $this->query($query);
        return count($result ?: []) > 0;
    }

    function checkPosShiftIdIsCalculatedPerInvoice($posShiftId) {
        $posShift = $this->find('first', ['conditions' => ['PosShift.id' => $posShiftId], 'applyBranchFind' => false]);
        return $this->checkPosShiftIsCalculatedPerInvoice($posShift);
    }

    function isPosShiftOpen($pos_shift_id) {
        $pos_shift = $this->find('first', ['conditions' => ['PosShift.id' => $pos_shift_id]]);
        // Doesn't exist
        if (empty($pos_shift)) {
            return false;
        }
        return (int) $pos_shift['PosShift']['status'] === PosShift::STATUS_OPENED;
    }

    function checkPosShiftIsCalculatedPerInvoice($posShift) {
        $value = $posShift['PosShift']['is_calculated_per_invoice'];
        if($posShift['PosShift']['is_calculated_per_invoice'] === null) {
            if($this->checkPosShiftHasInvoiceJournals($posShift['PosShift']['id'])) {
                $value = 1;
            } else {
                $value = settings::getValue(PosPlugin, "pos_accounting_settings") == 1;
            }
            $this->id = $posShift['PosShift']['id'];
            $this->saveField('is_calculated_per_invoice', 1, ['callbacks' => false]);
        }
        return $value;
    }

    function can_add_invoice_details()
    {
        if (check_permission(CHANGE_SALES_PERSON)) {
            return true;
        }
        $this->loadModel('CustomForm');
        $custom_form = $this->CustomForm->findByTableName('invoices');
        if (isset($custom_form['CustomFormFields']) && count($custom_form['CustomFormFields'])) {
            return true;
        }

        return false;
    }

    function getPosShiftsList($site_id = false, $otherConditions = array(), $limit = 30, $order = false, $includes = false) {
        $fields = "$this->alias.id";
        $postShiftsList = [];
        $posShifts = $this->find(
            'list',
            array(
                'limit' => $limit,
                'conditions' => $otherConditions,
                'fields' => $fields,
                'order' => $order, 'recursive' => -1
            ));

        foreach ($posShifts as $posShiftId => $posShift) {
            $postShiftsList[$posShiftId] = $posShiftId;
        }
        if (is_array($includes) && count($includes) > 0) {
            $posShifts = $this->find(
                'list',
                [
                    'limit' => $limit,
                    'conditions' => ['PosShift.id' => $includes],
                    'fields' => $fields,
                    'order' => $order, 'recursive' => -1
                ]);
            foreach ($posShifts as $posShiftId => $posShift) {
                $postShiftsList[$posShiftId] = $posShiftId;
            }
        }
        return $postShiftsList;
    }
}
