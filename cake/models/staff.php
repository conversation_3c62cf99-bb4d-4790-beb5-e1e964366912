<?php

use App\Utils\StaffTypeUtil;
use Izam\Aws\S3UploadHandler;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Ramsey\Uuid\Uuid;

class Staff extends AppModel {
    const OWNER_STAFF_ID = 0;
    const OWNER_ROLE_ID = -1;
    static $ownerStaff = null;
	var $applyBranch = ['onFind' => false, 'onSave' => true];
    var $name = 'Staff';
    var $filters = array();
    var $belongsTo = array(
        'Role' => array('className' => 'Role', 'foreignKey' => 'role_id'),
    );

    var $actsAs = [
        'customform' => [
            'custom_model_name' => 'StaffsCustomData',
            'custom_data_table_name' => 'staffs_custom_data',
            'callbacks' => [
                'afterSave' => 'afterCustomSaveCallback'
            ],
        ],
    ];

    // generated because full name is null if last name is null
    function __construct($id = false, $table = null, $ds = null) {
    	parent::__construct($id, $table, $ds);
        $this->validate = array(
            'name' => array(
				array('required'=>true,'allowEmpty' => false, 'rule' => 'notempty', 'message' => __('Required', true))
            ),
			'role_id' => array(
				array('required'=>true,'rule' => 'notempty', 'message' => __('Required', true))
			),
            'email_address' => array(
				array('required'=>true,'last' => true, 'allowEmpty' => false, 'rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'isUnique', 'allowEmpty' => false, 'message' => __('Email already exists', true))
            ),
            'password' =>array( 
				array('rule' => array('minLength', 4), 'allowEmpty' => true, 'message' => __('The password should be 6 characters at least', true))
			),
        );
    }

    function getFilters() {
        $filters = array(
            'name' => 'like',
            'email_address' => '=',
        );
        if ( ifPluginActive(ExpensesPlugin) ) {
            $filters['default_treasury_id']='=';


        }
        return $filters; //set filters here
    }

    public function beforeSave($options = array()) {
        parent::beforeSave($options);
        if(isset($this->data[$this->name]['password_already_hashed'])){
            unset($this->data[$this->name]['password_already_hashed']);
        }else{
            if (!empty($this->data[$this->name]['password'])) {
                $this->data[$this->name]['password'] = HashPassword($this->data[$this->name]['password']);
            }
        }
        return true;
    }

    function getStaffWithRoleForSession($staffId) {
        $StaffRow = $this->find('first', array('applyBranchFind'=>false,'conditions' => ['Staff.id' => $staffId], 'recursive' => 1));
        $roleRow = $this->Role->GetRole($StaffRow['Staff']['role_id']);
        $StaffRow['Staff']['Role'] = $roleRow['Role'];
        $StaffRow['Staff']['RolesPermission'] = $roleRow['RolesPermission'];
        return $StaffRow;
    }
	
	public function afterSave($created)
	{
		$this->addShifts();
        $this->addBranches();
        $this->loadModel('Site');
        $this->Site->update_current_staff_count();

        setLastUpdatedAt(StaffPlugin, SettingsUtil::STAFFS_LAST_UPDATED_AT);
	}

	public function afterDelete() {
        $this->loadModel('Site');
        $this->Site->update_current_staff_count();
    }
	
	function addShifts()
	{
			$staff_id = [$this->id];
			$this->loadModel('ItemStaff');
			$this->ItemStaff->assign_staff_members($staff_id, $this->data['Staff']['shifts'], ItemStaff::SHIFT_ITEM_TYPE, 'staff');
			
	}

    public function updateAuthCountByBranchId($branchId)
    {
        $this->loadModel('Setting');
        $settings = $this->Setting->find('all', [
            'fields' => ['key'],
            'applyBranchFind' => false,
            'conditions' => [
                'Setting.key LIKE' => '%branch-%',
                'Setting.value' => $branchId,
            ]
        ]);

        if (empty($settings)) {
            return;
        }

        $staffIds = array_map(function ($setting) {
                $staffID = trim($setting['Setting']['key'], 'branch-');
                if($staffID != "") {
                    return $staffID;
                }
        }, $settings);
        $staffIds = array_filter($staffIds, function ($staffId) {
            return $staffId != null;
        });
        $_staffIds = implode(', ', $staffIds);

        $query = "UPDATE staffs SET auth_id = auth_id+1 WHERE id IN ($_staffIds)";
        $this->query($query);
    }

    function UpdateAuthCount($id=null)
    {
        if ($id!=null) {
            $this->query("update staffs set auth_id=auth_id+1 where id='{$id}'", false);

        }
        return true;
    }

    function ChangeEmail($data) {
        $user = $this->findById(getAuthStaff('id'));

        $site = array();
        $site[$this->name]['id'] = $user[$this->name]['id'];
        $site[$this->name]['email_address'] = $data[$this->name]['email_address'];

        if (empty($data[$this->name]['password'])) {
            $this->validationErrors['password'] = __('Required', true);
            return false;
        }

        if (HashPassword($data[$this->name]['password']) != $user[$this->name]['password']) {
            $this->validationErrors['password'] = __('The password is incorrect', true);
            return false;
        }

        if ($this->save($site, false)) {
            return true;
        }
        return false;
    }

    function addBranches() {
        if (ifPluginActive(BranchesPlugin)){
            $staff_id = [$this->id];
            $this->loadModel('ItemStaff');
            $conditions = ['ItemStaff.item_id ' =>  getStaffBranchesIDs() , 'ItemStaff.staff_id ' =>  $staff_id , 'ItemStaff.item_type' => ItemStaff::BRANCH_ITEM_TYPE];
            $this->ItemStaff->assign_staff_members($staff_id, $this->data['Staff']['branches'], ItemStaff::BRANCH_ITEM_TYPE, 'custom', $conditions);
        }
    }

    public function ChangePassword($data, $user) {
        if (empty($data[$this->name]['old_password'])) {
            $this->validationErrors['old_password'] = __('Required', true);
        }
        if (empty($data[$this->name]['password'])) {
            $this->validationErrors['password'] = __('Required', true);
        }
        if (empty($data[$this->name]['psswrd'])) {
            $this->validationErrors['psswrd'] = __('Required', true);
        }
        if ($this->validationErrors) {
            return false;
        }
        if (HashPassword($data[$this->name]['old_password']) != $user['password']) {
            $this->validationErrors['old_password'] = __('Current Password incorrect', true);
            return false;
        }
        if ($data[$this->name]['password'] != $data[$this->name]['psswrd']) {
            $this->validationErrors['psswrd'] = __('Not match', true);
            return false;
        }
        $data[$this->name]['id'] = getAuthStaff('id');
        if ($this->save($data, false)) {
            return true;
        }
        return false;
    }

    public function getList($inlude_admin = true, $conditions = array(),$active_only=false,$withDeleted=false) {
		if($active_only){
        $conditions['Staff.active'] = 1;
		}
        $list=[];
        if ($withDeleted){
            $conditions[] = 'withDeleted';
        }else {
            $conditions[] = 'Staff.deleted_at is null';
        }
        $lists = $this->find('all', array('fields'=>"Staff.id,concat('#',IFNULL(Staff.code,Staff.id), ' ', Staff.name,' ',IFNULL(Staff.last_name,'')) as full_name, Staff.deleted_at",'conditions' => $conditions, 'order' => 'Staff.name'));
		foreach ($lists as $a){
		$list[$a['Staff']['id']]=$a['0']['full_name'];
        if (!is_null($a['Staff']['deleted_at'])) {
            $list[$a['Staff']['id']] .= __('(Deleted user)', true);
        }
        }

        if ($inlude_admin) {
            $list = array(0 => $this->getOwnerUserName()) + $list;
        }
        return $list;
    }

    function reload_session($id = null) {
        if (empty($id)) {
            $id = getAuthStaff('id');
        }
        $Staff = $this->findById($id);
        unset($_SESSION['branch_id']);
        $roleRow = $this->Role->find('first', array('conditions' => array('Role.id' => $Staff['Staff']['role_id'])));
        $Staff['Staff']['Role'] = $roleRow['Role'];
        $Staff['Staff']['RolesPermission'] = $roleRow['RolesPermission'];
        $session = new CakeSession;
        $session->delete('branch_id');
        $session->write('STAFF', $Staff['Staff']);
        return true;
    }

    function getCountryList($conditions = array()) {

        $Countrylist = array();
        $rows = $this->find('all', array('fields' => 'DISTINCT country_code', 'conditions' => $conditions, 'recursive' => -1));
        foreach ($rows as $row) {
            $Countrylist[] = $row[$this->alias]['country_code'];
        }
        debug($Countrylist);
        $this->loadModel('Country');
        return $this->Country->find('list', array('fields' => 'code, country', 'conditions' => array('Country.code' => $Countrylist)));
    }

    function checkPasswd($data) {
        if (!empty($this->data[$this->name]['password'])) {

            if ($this->data[$this->name]['password'] != $data['confirm_password']) {
                $this->invalidate('password', __('Passwords not match', true));
                return false;
            }
        }
        return true;
    }

    function calculate_current_staff_count() {
        return $this->find('count',['conditions' => ['active' => 1, 'deleted_at is null'],'applyBranchFind'=>false]);
    }

    function setStaffStatus($id, $status){
        $this->id = $id;
        $this->saveField('active',$status);
    }

    /**
     * Get a list that can be used in select box
     * @param integer $site_id Site id to get suppliers, defaults to logged in owner id.
     * @param array $otherConditions Optional extra conditions to get suppliers
     * @return array List of suppliers.
     */
    function getStaffList($otherConditions = array(), $includeOwner = false) {
        $staff = $this->find('all', array('conditions' => $otherConditions,  'order' => "$this->alias.created", 'recursive' => -1));
        $staffList = [];
        if ($includeOwner) {
            $staffList = array(0 => $this->getOwnerUserName()) + $staffList;
        }
        foreach ($staff as $employee)
        {
            $staffList[$employee['Staff']['id']] = $employee['Staff']['name'] . ' ' . $employee['Staff']['last_name'] . ' #' . $employee['Staff']['code'];
        }
        return $staffList;
    }

    function getStaffCount($otherConditions = array()) {

        $count = $this->find('count', array('recursive' => -1,'conditions' => $otherConditions));
        return $count;
    }

    public function find($conditions = null, $fields = array(), $order = null, $recursive = null)
    {
        if (!is_string($conditions) || !array_key_exists($conditions, $this->_findMethods)) {
            $conditions[] = 'Staff.deleted_at IS NULL';
        } else {
            if(is_null($fields['conditions'])) {
                $fields['conditions'] = [];
            }
            if (!in_array('withDeleted', $fields['conditions'])) {
                $fields['conditions'][] = 'Staff.deleted_at IS NULL';
            }else {
                $fields['conditions']  = array_filter($fields['conditions'], function ($cond) {
                    return $cond != 'withDeleted';
                });
            }
        }
        return parent::find($conditions, $fields, $order, $recursive);
    }

    public function activeEmployeesCount()
    {
        return $this->find('count', [
            'conditions' => [
                'type' => 'employee',
                'active' => '1'
            ]
        ]);
    }

    public function getStaffImageUrl($imagePath,$staff_name){

        $s3FileManager = new \App\Services\S3FileManager();
        $image_url = resizeImage($s3FileManager->getUrlAttribute($imagePath, $staff_name), ['w' => 40, 'h' => 40, 'c' => 0]) ;
                    
        if (empty($imagePath)) {
            $image_url = CDN_ASSETS_URL."imgs/account-def.png";
        }

        return $image_url;
    }

    public function saveOwnerUser($site, $staffId = null){
        $ownerStaffData = [
            'name' => $site['first_name'],
            'role_id' => Staff::OWNER_ROLE_ID,
            'middle_name' => '',
            'last_name' => $site['last_name'],
            'full_name' => $site['first_name'] .' '. $site['last_name'],
            'email_address' => $site['email'],
            'password' => $site['password'],
            'active' => 1,
            'country_code' => $site['country_code'],
            'address1' => $site['address1'],
            'address2' => $site['address2'],
            'city' => $site['city'],
            'state' => $site['state'],
            'postal_code' => $site['postal_code'],
            'mobile' => $site['phone2'],
            'home_phone' => $site['phone1'],
            'language_code' => $site['language_code'],
            'created' => date("Y:m:d H:i:s"),
            'modified' => date("Y:m:d H:i:s"),
        ];
        if(!$staffId){
            $this->create();

            $this->loadModel('Role');
            $ownerRole = $this->Role->find('first', array('conditions' => ['Role.id' => Staff::OWNER_ROLE_ID]));
            if(!$ownerRole){
                $this->Role->create();
                $ownerRoleData = [
                    'id'=> Staff::OWNER_ROLE_ID,
                    'name'=> 'Owner',
                    'is_super_admin'=> 1,
                    'branch_id'=> 1, 
                    'type'=> 'user'
                ];
                $this->Role->save($ownerRoleData);
            }
        }else{
            $ownerStaffData['id'] = $staffId;
            unset($ownerStaffData['created']);
            unset($ownerStaffData['modified']);
            unset($ownerStaffData['role_id']);
            unset($ownerStaffData['active']);
            unset($ownerStaffData['password']);
    
            return $this->save($ownerStaffData, false);
        }
        $ownerStaffData['password_already_hashed'] = true;

        $results =  $this->save($ownerStaffData);
        if($results){
            $insertedId = $this->getInsertID();
            $this->saveField('code', $insertedId);
        }
        return $results;
    }

    public function changeOwnerUserEmail($newEmail){
        $ownerUser = $this->getOwnerUser();
        if($ownerUser){
            $this->id = $ownerUser['id'];
            $this->saveField('email_address',$newEmail);
        }
    }

    public function changeOwnerUserPassword($newPassword){
        $ownerUser = $this->getOwnerUser();
        if($ownerUser){
            $this->id = $ownerUser['id'];
            $this->saveField('password',$newPassword);
        }
    }

    public function getOwnerUser($ignoreCache = false){
        $cachedOwnerStaff = self::$ownerStaff;
        if(!$ignoreCache && $cachedOwnerStaff){
            return $cachedOwnerStaff;
        }else{
            $ownerStaff = $this->find('first', [
                'conditions' => [
                    'role_id' => Staff::OWNER_ROLE_ID,
                ]
            ]);
            if(isset($ownerStaff['Staff'])){
                self::$ownerStaff = $ownerStaff['Staff'];
                return self::$ownerStaff;
            }
        }
    }

    private function attachSiteLogoToStaff($staffId ,$logo = null)
    {
        $logo = $logo ?? getCurrentSite()['site_logo_full_path'];
        $fileUrl =  'https://' . getCurrentSite('subdomain') . $logo;
    
        return izam_resolve(S3UploadHandler::class)->uploadPath($fileUrl , SITE_HASH,'staff');
    }

    public function getOwnerUserName():string
    {
        $ownerUserStaff = $this->getOwnerUser();
        $ownerName = getAuthOwnerName();
        if($ownerUserStaff){
            $owner = getAuthOwner();
            $ownerName = '';
            if (!empty($owner['business_name'])) {
                $ownerName = $owner['business_name'];
            }elseif (($owner['first_name'] or $owner['last_name'])) {
                $ownerName = "{$owner['first_name']} {$owner['last_name']}";
            }
            $ownerName .= ' ('.__('Owner', true).')';
        }
        return $ownerName ?: '';
    }
}
