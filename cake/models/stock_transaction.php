<?php

use App\Events\Queue\AveragePriceChanged;
use App\Services\StockTransactions\StockTransactionUpdater;
use App\Services\StockTransactions\TrackingNumberHelper;
use App\Utils\TrackStockUtil;
use App\vendors\Services\InvoiceItemService;
use Izam\Daftra\Queue\Models\EventAction;
use Izam\Daftra\Queue\Repositories\EventActionRepository;
use Izam\Daftra\Common\Utils\SettingsUtil;

App::import('Vendor', 'settings');
App::import('Vendor', 'notification_2');
App::import('Vendor', 'CurrencyConverter', ['file' => 'CurrencyConverter.php']);
App::import('vendor','WarehouseService' , ['file' => 'WarehouseService.php']);

class StockTransaction extends AppModel {
    var $applyBranch = ['onFind' => false, 'onSave' => true];
    public $belongsTo = ['Product', 'Store'];
    var $name = 'StockTransaction';
    static $applyNewWayForCommunication = true;
    const SOURCE_MANUAL = 1;
    const SOURCE_INVOICE = 2;
    const SOURCE_PO = 3;
    const SOURCE_CN = 4;
    const SOURCE_TRANSFER = 5;
    const SOURCE_RR = 6;
    const SOURCE_PR = 7;
    const SOURCE_PDN = 14;

    const SOURCE_BUNDLE_RECON = 10;
    const SOURCE_BUNDLE = 8;
    const SOURCE_REQUISITION = 9;

    //REQUISITION TYPES + 100
    const SOURCE_RQ_MANUAL_INBOUND = 101;
    const SOURCE_RQ_MANUAL_OUTBOUND = 102;
    const SOURCE_RQ_INVOICE = 103;
    const SOURCE_RQ_INVOICE_REFUND = 104;
    const SOURCE_RQ_INVOICE_CREDIT_NOTE = 105;
    const SOURCE_RQ_PURCHASE_ORDER = 106;
    const SOURCE_RQ_PURCHASE_REFUND = 107;
    const SOURCE_RQ_PURCHASE_DEBIT_NOTE = 115;
    const SOURCE_RQ_TRANSFER_REQUISITION = 108;
    const SOURCE_RQ_TRANSFER_INBOUND = 109;
    const SOURCE_RQ_TRANSFER_OUTBOUND = 110;
    const SOURCE_RQ_POS_INBOUND = 111;
    const SOURCE_RQ_POS_OUTBOUND = 112;
    const SOURCE_RQ_STOCKTAKING_OUT = 113;
    const SOURCE_RQ_STOCKTAKING_IN = 114;
    const SOURCE_RQ_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND = 116;

    const SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_PRODUCT = 121;
    const SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_SCRAP = 122;

    const SOURCE_RQ_MANUFACTURE_ORDER_MATERIAL_OUTBOUND = 118;

    const STATUS_DRAFT = 1;
    const STATUS_PENDING = 2;
    const STATUS_PROCESSED = 4;
    const TRANSACTION_IN = 1;
    const TRANSACTION_OUT = 2;
    const STATUS_TRANSFER = 5;

    const CALC_METHOD_AVERAGE = 2 ;
    const CALC_METHOD_BOUGHT_FIRST = 1 ;

    /*
        Bundle Requisition Complexity

        1. Basic Requisition Logic:
        - Requisitions can be inbound (add stock) or outbound (remove stock)
        - Simple for individual products

        2. Bundle Requisition Challenges:
        - Adding bundle stock:
            * Must first deduct stock from component products
            * Requires validation of each component's stock availability

        3. Main Issue: Deletion of Bundle Requisitions
        - Normal requisition deletion: 
            Simple reversal (add becomes subtract, or vice versa)
        - Bundle requisition deletion:
            Complex reversal needed (must undo multiple component transactions)

        4. Solution Implemented:
        - Added new validations mode/type specifically for bundles
        - Simplifies reversal process for bundle requisitions
    */
    const ADD_FROM_DELETE = 'add_delete';

    function __construct($id = false, $table = null, $ds = null) {

        parent::__construct($id, $table, $ds);

        $this->validate = [
            'received_date' => ['validateIsOpenedPeriod' => ['rule' => 'validateIsOpenedPeriod', 'message' => __('You can not add, edit, or delete a transaction in this date %s within a closed period', true)],],
            'store_id' => array(
                'rule' => 'isStoreActive',
                'message' => __t('You cannot add the transaction through a suspended warehouse')
            ),
            'branch_id' => array(
                'rule' => 'checkBranchActive',
                'message' => __t('You cannot add a transaction in a suspended branch')
            ),
    
    ];


    }


    var $actsAs = [

        'journal' => [
            'is_journal'=>true,
            'entity_type'=>'stock_transaction'
        ],
    ];

    /**
     * @return bool
     */
    public static function isApplyNewWayForCommunication(): bool
    {
        return self::$applyNewWayForCommunication;
    }

    /**
     * @param bool $applyNewWayForCommunication
     */
    public static function setApplyNewWayForCommunication(bool $applyNewWayForCommunication): void
    {
        self::$applyNewWayForCommunication = $applyNewWayForCommunication;
    }

    /**
     * @param bool $withStockTaking bcs stock taking is only requisition transactions
     * so even if you don't have requisitions on the stock taking transactions will
     * be requisitions transactions
     * @return array
     */
    public static function getSourceRequisition($withStockTaking = true) {
        $data = [
            self::SOURCE_RQ_MANUAL_INBOUND,
            self::SOURCE_RQ_MANUAL_OUTBOUND,
            self::SOURCE_RQ_INVOICE,
            self::SOURCE_RQ_INVOICE_REFUND,
            self::SOURCE_RQ_INVOICE_CREDIT_NOTE,
            self::SOURCE_RQ_PURCHASE_ORDER,
            self::SOURCE_RQ_PURCHASE_REFUND,
            self::SOURCE_RQ_PURCHASE_DEBIT_NOTE,
            self::SOURCE_RQ_TRANSFER_REQUISITION,
            self::SOURCE_RQ_TRANSFER_INBOUND,
            self::SOURCE_RQ_TRANSFER_OUTBOUND,
            self::SOURCE_RQ_POS_INBOUND,
            self::SOURCE_RQ_POS_OUTBOUND,
            self::SOURCE_RQ_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
            self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_PRODUCT,
            self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_SCRAP,
            self::SOURCE_RQ_MANUFACTURE_ORDER_MATERIAL_OUTBOUND,
            self::SOURCE_RQ_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
        ];
        if($withStockTaking) {
            $data = array_merge($data,[
                self::SOURCE_RQ_STOCKTAKING_OUT,
                self::SOURCE_RQ_STOCKTAKING_IN
            ]);
        }
        return $data;
    }

    public static function getSourceAutomaticRequisition($withStockTaking = true) {
        $data = [
            self::SOURCE_RQ_INVOICE,
            self::SOURCE_RQ_INVOICE_REFUND,
            self::SOURCE_RQ_INVOICE_CREDIT_NOTE,
            self::SOURCE_RQ_PURCHASE_ORDER,
            self::SOURCE_RQ_PURCHASE_REFUND,
            self::SOURCE_RQ_PURCHASE_DEBIT_NOTE,
            self::SOURCE_RQ_TRANSFER_REQUISITION,
            self::SOURCE_RQ_TRANSFER_INBOUND,
            self::SOURCE_RQ_TRANSFER_OUTBOUND,
        ];
        if($withStockTaking) {
            $data = array_merge($data,[
                self::SOURCE_RQ_STOCKTAKING_OUT,
                self::SOURCE_RQ_STOCKTAKING_IN
            ]);
        }
        return $data;
    }

    public static function getStockTransactionType($requisitionType) {
        GetObjectOrLoadModel('Requisition');
        $stockTransactionTypes = [
            Requisition::ORDER_TYPE_MANUAL_INBOUND => self::SOURCE_RQ_MANUAL_INBOUND,
            Requisition::ORDER_TYPE_MANUAL_OUTBOUND => self::SOURCE_RQ_MANUAL_OUTBOUND,
            Requisition::ORDER_TYPE_INVOICE => self::SOURCE_RQ_INVOICE,
            Requisition::ORDER_TYPE_INVOICE_REFUND => self::SOURCE_RQ_INVOICE_REFUND,
            Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE => self::SOURCE_RQ_INVOICE_CREDIT_NOTE,
            Requisition::ORDER_TYPE_PURCHASE_ORDER => self::SOURCE_RQ_PURCHASE_ORDER,
            Requisition::ORDER_TYPE_PURCHASE_REFUND => self::SOURCE_RQ_PURCHASE_REFUND,
            Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE => self::SOURCE_RQ_PURCHASE_DEBIT_NOTE,
            Requisition::ORDER_TYPE_TRANSFER_REQUISITION => self::SOURCE_RQ_TRANSFER_REQUISITION,
            Requisition::ORDER_TYPE_TRANSFER_INBOUND => self::SOURCE_RQ_TRANSFER_INBOUND,
            Requisition::ORDER_TYPE_TRANSFER_OUTBOUND => self::SOURCE_RQ_TRANSFER_OUTBOUND,
            Requisition::ORDER_TYPE_POS_INBOUND => self::SOURCE_RQ_POS_INBOUND,
            Requisition::ORDER_TYPE_POS_OUTBOUND => self::SOURCE_RQ_POS_OUTBOUND,
            Requisition::ORDER_TYPE_STOCKTAKING_IN => self::SOURCE_RQ_STOCKTAKING_IN,
            Requisition::ORDER_TYPE_STOCKTAKING_OUT => self::SOURCE_RQ_STOCKTAKING_OUT,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND => self::SOURCE_RQ_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP => self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_SCRAP,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT => self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_PRODUCT,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL => self::SOURCE_RQ_MANUFACTURE_ORDER_MATERIAL_OUTBOUND,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND => self::SOURCE_RQ_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,

        ];
        return $stockTransactionTypes[$requisitionType];
    }

    /**
     * @return array
     * this function return the transaction source of the transactions
     * that don't affect the avg price of a product as they take
     * the previous transaction avg price and don't change it
     *
     * P.S : This function is also used to get the transactions that it's price can be modified to match the correct average price
     */
    public static function getTransactionNotAffectAvgSource(){
        return [
            self::SOURCE_RQ_STOCKTAKING_IN,
            self::SOURCE_RQ_STOCKTAKING_OUT,
            self::SOURCE_RQ_TRANSFER_REQUISITION,
            self::SOURCE_RQ_TRANSFER_INBOUND,
            self::SOURCE_RQ_TRANSFER_OUTBOUND,
            self::SOURCE_RQ_MANUFACTURE_ORDER_MATERIAL_OUTBOUND,
            self::SOURCE_TRANSFER
        ];
    }

    public static function getTransactionNotAffectAvgSourceButAreProtectedFromUpdate(){
        if(calculateRefundWithSellingPrice()) {
            return [
                StockTransaction::SOURCE_RQ_POS_INBOUND
            ];
        } else {
            return [
                StockTransaction::SOURCE_CN,
                StockTransaction::SOURCE_RR,
                StockTransaction::SOURCE_RQ_INVOICE_CREDIT_NOTE,
                StockTransaction::SOURCE_RQ_INVOICE_REFUND,
                StockTransaction::SOURCE_RQ_POS_INBOUND
            ];
        }

    }




    public static function getRequisitionStoreTransactionSourceTypeMapping() {
        GetObjectOrLoadModel('Requisition');
        $mapping = [
            Requisition::ORDER_TYPE_MANUAL_INBOUND => self::SOURCE_RQ_MANUAL_INBOUND,
            Requisition::ORDER_TYPE_MANUAL_OUTBOUND => self::SOURCE_RQ_MANUAL_OUTBOUND,
            Requisition::ORDER_TYPE_INVOICE => self::SOURCE_RQ_INVOICE,
            Requisition::ORDER_TYPE_INVOICE_REFUND => self::SOURCE_RQ_INVOICE_REFUND,
            Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE => self::SOURCE_RQ_INVOICE_CREDIT_NOTE,
            Requisition::ORDER_TYPE_PURCHASE_ORDER => self::SOURCE_RQ_PURCHASE_ORDER,
            Requisition::ORDER_TYPE_PURCHASE_REFUND => self::SOURCE_RQ_PURCHASE_REFUND,
            Requisition::ORDER_TYPE_TRANSFER_REQUISITION => self::SOURCE_RQ_TRANSFER_REQUISITION,
            Requisition::ORDER_TYPE_TRANSFER_INBOUND => self::SOURCE_RQ_TRANSFER_INBOUND,
            Requisition::ORDER_TYPE_TRANSFER_OUTBOUND => self::SOURCE_RQ_TRANSFER_OUTBOUND,
            Requisition::ORDER_TYPE_POS_INBOUND => self::SOURCE_RQ_POS_INBOUND,
            Requisition::ORDER_TYPE_POS_OUTBOUND => self::SOURCE_RQ_POS_OUTBOUND,
            Requisition::ORDER_TYPE_STOCKTAKING_OUT => self::SOURCE_RQ_STOCKTAKING_OUT,
            Requisition::ORDER_TYPE_STOCKTAKING_IN => self::SOURCE_RQ_STOCKTAKING_IN,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP => self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_SCRAP,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT => self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_PRODUCT,
            Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND => self::SOURCE_RQ_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND,
        ];
        return $mapping;
    }

    public static function getSourceTypeAffectAvgPrice() {
        if(calculateRefundWithSellingPrice()) {
            return [
                self::SOURCE_RQ_PURCHASE_ORDER,
                self::SOURCE_RQ_MANUAL_INBOUND,
                self::SOURCE_PO,
                self::SOURCE_MANUAL,
                self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_PRODUCT,
                self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_SCRAP,
                self::SOURCE_RR,  // Refund Receipts
                self::SOURCE_CN,  // Credit Notes
                self::SOURCE_RQ_INVOICE_REFUND,
                self::SOURCE_RQ_INVOICE_CREDIT_NOTE
            ];
        } else {
            return [
            self::SOURCE_RQ_PURCHASE_ORDER,
            self::SOURCE_RQ_MANUAL_INBOUND,
            self::SOURCE_PO,
            self::SOURCE_MANUAL,
            self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_PRODUCT,
            self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_SCRAP,
        ];
        }

    }


    /**
     * @param $order_type
     * @param $alias
     * This functions returns the source type of the stock_transaction of requisitions based on order_type and the alias (entity_name)
     * @return bool|mixed
     */
    public static function getStockTransactionTypeForPurchaseOrderAndRefund($order_type, $alias){
        $mapping = [
            Requisition::ORDER_TYPE_PURCHASE_ORDER => StockTransaction::SOURCE_RQ_PURCHASE_ORDER,
            Requisition::ORDER_TYPE_PURCHASE_REFUND => StockTransaction::SOURCE_RQ_PURCHASE_REFUND
        ];
        // Check if Source Is a Requisition
        if($alias == Requisition::REQUISITION){
            return $mapping[$order_type];
        }
        return $order_type;
    }

    public static function getGroupedSources() {
        return [
            'PurchaseOrder' => [
                'sources' => [self::SOURCE_RQ_PURCHASE_ORDER, self::SOURCE_PO],
                'transaction_type' => self::TRANSACTION_IN,
                'label' => __('Purchase invoices', true)
            ],
            'RefundReceipt' => [
                'sources' => [self::SOURCE_RQ_INVOICE_REFUND, self::SOURCE_RQ_INVOICE_CREDIT_NOTE, self::SOURCE_RR, self::SOURCE_CN, self::SOURCE_RQ_POS_INBOUND],
                'transaction_type' => self::TRANSACTION_IN,
                'label' => __('Refund Receipts', true)
            ],
            'TransferIN' => [
                'sources' => [self::SOURCE_RQ_TRANSFER_REQUISITION, self::SOURCE_TRANSFER],
                'transaction_type' => self::TRANSACTION_IN,
                'label' => __('Transfer', true)
            ],
            'ManualIN' => [
                'sources' => [self::SOURCE_RQ_MANUAL_INBOUND, self::SOURCE_MANUAL, self::SOURCE_RQ_STOCKTAKING_IN],
                'transaction_type' => self::TRANSACTION_IN,
                'label' => __('Manual', true)
            ],
            'Invoice' => [
                'sources' => [self::SOURCE_RQ_INVOICE, self::SOURCE_INVOICE, self::SOURCE_RQ_POS_OUTBOUND, self::SOURCE_BUNDLE_RECON],
                'transaction_type' => self::TRANSACTION_OUT,
                'label' => __('Sales Invoices', true)
            ],
            'PurchaseRefund' => [
                'sources' => [self::SOURCE_RQ_PURCHASE_REFUND, self::SOURCE_PR],
                'transaction_type' => self::TRANSACTION_OUT,
                'label' => __('Purchase Refund', true)
            ],
            'TransferOUT' => [
                'sources' => [self::SOURCE_RQ_TRANSFER_REQUISITION, self::SOURCE_TRANSFER, self::SOURCE_BUNDLE],
                'transaction_type' => self::TRANSACTION_OUT,
                'label' => __('Transfer', true)
            ],
            'ManualOUT' => [
                'sources' => [self::SOURCE_RQ_MANUAL_OUTBOUND, self::SOURCE_MANUAL, self::SOURCE_RQ_STOCKTAKING_OUT],
                'transaction_type' => self::TRANSACTION_OUT,
                'label' => __('Manual', true)
            ],
        ];
    }

    public static function getSourceGroup($sourceType, $transactionType) {
        $groupedSources = self::getGroupedSources();
        foreach ($groupedSources as $groupKey => $groupValue) {
            if (in_array($sourceType, $groupValue['sources']) && $transactionType == $groupValue['transaction_type']) {
                return $groupKey;
            }
        }
        return $transactionType == self::TRANSACTION_OUT ? 'ManualOUT' : 'ManualIN';
    }

    private static $viewableStoresCache = [];

    public static function getProductStock($id , $store_id= null , $stores_list = [] ,$no_stores = false, $order_id = null, $source_type = null) {
        if ( empty($stores_list )){
            $ItemPermission = GetObjectOrLoadModel('ItemPermission');
            if(empty(self::$viewableStoresCache)) {
                self::$viewableStoresCache = $ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_VIEW);
            }
            $stores_list = self::$viewableStoresCache;
        }
        $product = new self();
        $conditions = ['StockTransaction.status' => self::STATUS_PROCESSED,' (ignored = 0 OR ignored IS NULL) ', 'product_id' => $id];
        if ( !$no_stores ){
            $conditions['StockTransaction.store_id'] = array_keys($stores_list);
            if ( !is_null ($store_id) ) {
                $conditions['StockTransaction.store_id'] = $store_id;
            }
        }
        $productModel = GetObjectOrLoadModel('Product');
        $is_bundle = $productModel->find('first', ['recursive' => -1, 'conditions' => ['Product.id' => $id]])['Product']['type'] == Product::BUNDLE_TYPE;
        if ($is_bundle && $order_id && $source_type) {
            $refund_order_ids = self::getRefundsOrderIds($order_id, $source_type);
            if (!empty($refund_order_ids)) {
                $conditions['NOT'] = [
                    'StockTransaction.source_type' => [self::SOURCE_RR, self::SOURCE_RQ_INVOICE_REFUND],
                    'StockTransaction.id' => $refund_order_ids
                ];
            }
        }
        $sum = $product->find('all', ['recursive' => -1, 'fields' => 'ROUND(sum(quantity),5) as current_quantity', 'conditions' =>$conditions]);
        return $sum[0][0]['current_quantity']?$sum[0][0]['current_quantity']: 0;
    }

    public static function getRefundsOrderIds($order_id, $source_type)
    {
        $InvoiceModel = GetObjectOrLoadModel('Invoice');
        $RequisitionModel = GetObjectOrLoadModel('Requisition');
        if ($source_type == self::SOURCE_INVOICE) {
            $invoice_ids = Set::extract('{n}.Invoice.id', $InvoiceModel->find('all', ['fields', 'id', 'recursive' => -1, 'conditions' => ['Invoice.subscription_id' => $order_id]]));
            return $invoice_ids ?: [];
        }
        if ($source_type == self::SOURCE_RQ_INVOICE) {
            $requisition = $RequisitionModel->find('first', ['recursive' => -1, 'conditions' => ['Requisition.id' => $order_id]]);
            if (!$requisition) return [];
            $invoice_refund = $InvoiceModel->find('first', ['recursive' => -1, 'conditions' => ['Invoice.subscription_id' => $requisition['Requisition']['order_id']]]);
            if (!$invoice_refund) return [];
            $refund_requisition = $RequisitionModel->find('first', ['recursive' => -1, 'conditions' => ['Requisition.order_id' => $invoice_refund['Invoice']['id'], 'Requisition.order_type' => Requisition::ORDER_TYPE_INVOICE_REFUND]]);
            return $refund_requisition['Requisition']['id'] ?: [];
        }
        return [];
    }

    public static function getSoldProductQuantity($id,$conditions= [])
    {
        $product = new self();
        $conditions = array_merge($conditions, ['StockTransaction.status' => self::STATUS_PROCESSED,'StockTransaction.source_type' => [self::SOURCE_INVOICE, self::SOURCE_RR, self::SOURCE_CN, self::SOURCE_RQ_INVOICE, self::SOURCE_RQ_INVOICE_REFUND, self::SOURCE_RQ_INVOICE_CREDIT_NOTE], ' (ignored = 0 OR ignored IS NULL) ', 'product_id' => $id]);
        if (ifPluginActive(PosPlugin)) {
            $conditions['StockTransaction.source_type'] = array_merge($conditions['StockTransaction.source_type'], [self::SOURCE_RQ_POS_INBOUND, self::SOURCE_RQ_POS_OUTBOUND]);
        }
        $sum = $product->find('all', ['fields' => 'sum(quantity) as total_quantity', 'conditions' =>$conditions]);
        return -1*$sum[0][0]['total_quantity'];
    }

    public static function getTransactions($source, $id, $include_nonactive = true) {
        $transaction = new self();
        $conditions = [];
        if (!$include_nonactive)
            $conditions = ['StockTransaction.status' => self::STATUS_PROCESSED, ' (ignored = 0 OR ignored IS NULL)'];

        $conditions['StockTransaction.order_id'] = $id;
        $conditions['StockTransaction.source_type'] = $source;

        $list = $transaction->find('all', ['conditions' => $conditions]);
        foreach ($list as $i => $l) {
            $list[$i]['StockTransaction']['stock_balance'] = self::getProductStock($list[$i]['StockTransaction']['product_id']);
        }

        return is_array($list)?$list:[];
    }

    public static function getInvoiceTransactions($invoice_id, $include_nonactive = true) {
        return self::getTransactions(self::SOURCE_INVOICE, $invoice_id, $include_nonactive);
    }
    public static function getCreditNoteTransactions($invoice_id, $include_nonactive = true) {
        return self::getTransactions(self::SOURCE_CN, $invoice_id, $include_nonactive);
    }
    public static function getRefundTransactions($invoice_id, $include_nonactive = true) {
        return self::getTransactions(self::SOURCE_RR, $invoice_id, $include_nonactive);
    }

    public static function getPOTransactions($po_id, $include_nonactive = true) {
        return self::getTransactions(self::SOURCE_PO, $po_id, $include_nonactive);
    }
    public static function getPRTransactions($po_id, $include_nonactive = true) {
        return self::getTransactions(self::SOURCE_PR, $po_id, $include_nonactive);
    }

    /**
     * Purchase debit note source
     * @param mixed $po_id
     * @param bool $include_nonactive
     * @return array
     */
    public static function getPDNTransactions($po_id, $include_nonactive = true) {
        return self::getTransactions(self::SOURCE_PDN, $po_id, $include_nonactive);
    }

    public static function getProductStockBefore($id, $transaction_id , $store_id = null ) {
        $transaction = new self();
        $curr = $transaction->read(null, $transaction_id);
        $ItemPermission = GetObjectOrLoadModel('ItemPermission');
        $store_list = $ItemPermission->getAuthenticatedList( ItemPermission::ITEM_TYPE_STORE, ItemPermission::PERMISSION_VIEW);
        $conditions = ['StockTransaction.store_id' => array_keys($store_list), 'StockTransaction.status' => self::STATUS_PROCESSED, ' (ignored = 0 OR ignored IS NULL)', '( received_date < \'' . $curr['StockTransaction']['received_date'] . '\' OR (received_date = \'' . $curr['StockTransaction']['received_date'] . '\' AND StockTransaction.id < ' . $curr['StockTransaction']['id'] . ' ) )', 'product_id' => $id];
        if ( !empty ( $store_id ) ) {
            $conditions['StockTransaction.store_id'] = $store_id ;
        }
        $sum = $transaction->find('all', ['fields' => 'sum(quantity) as current_quantity', 'conditions' => $conditions]);
        return $sum[0][0]['current_quantity'];
    }

    public static function getSources() {
        return [
            self::SOURCE_RR => __('Refund Receipt', true),
            self::SOURCE_CN => __('Credit Note', true),
            self::SOURCE_PO => __('Purchase Invoice', true),
            self::SOURCE_PR => __('Purchase Refund', true),
            self::SOURCE_PDN => __('Purchase Debit Note', true),
            self::SOURCE_INVOICE => __('Invoice', true),
            self::SOURCE_MANUAL => __('Manual Adjustment', true),
            self::SOURCE_BUNDLE => __('Bundle Product', true),
            self::SOURCE_REQUISITION => __('Requisition', true),
            self::SOURCE_BUNDLE_RECON => __('Bundle Outbound', true),
            self::SOURCE_RQ_MANUAL_INBOUND => __('Requisition Manual Inbound',true),
            self::SOURCE_RQ_MANUAL_OUTBOUND => __('Requisition Manual Outbound',true),
            self::SOURCE_RQ_INVOICE => __('Requisition Invoice',true),
            self::SOURCE_RQ_INVOICE_REFUND => __('Requisition Refund',true),
            self::SOURCE_RQ_INVOICE_CREDIT_NOTE => __('Requisition Credit Note',true),
            self::SOURCE_RQ_PURCHASE_ORDER => __('Requisition Purchase Invoice',true),
            self::SOURCE_RQ_PURCHASE_REFUND => __('Requisition Purchase Refund',true),
            self::SOURCE_RQ_PURCHASE_DEBIT_NOTE => __('Requisition Purchase Debit Note',true),
            self::SOURCE_RQ_TRANSFER_REQUISITION => __('Requisition Transfer',true),
            self::SOURCE_RQ_TRANSFER_INBOUND => __('Requisition Transfer IN',true),
            self::SOURCE_RQ_TRANSFER_OUTBOUND => __('Requisition Transfer OUT',true),
            self::SOURCE_RQ_POS_INBOUND => __('Requisition POS IN',true),
            self::SOURCE_RQ_POS_OUTBOUND => __('Requisition POS OUT',true),
            self::SOURCE_RQ_STOCKTAKING_OUT => __('Requisition Stocktaking OUT',true),
            self::SOURCE_RQ_STOCKTAKING_IN => __('Requisition Stocktaking IN',true),
            self::SOURCE_TRANSFER => __('Transfer Stock',true),
            self::SOURCE_RQ_MANUFACTURE_ORDER_MATERIAL_OUTBOUND => __('Outbound manufacturing order Material', true),
            self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_PRODUCT => __('Manufacturing Order Main Product Inbound Requisition', true),
            self::SOURCE_RQ_MANUFACTURE_ORDER_INBOUND_SCRAP => __('Manufacturing Order Scrap Items Inbound Requisition', true),
            self::SOURCE_RQ_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND => __('Manufacturing Order Returned Materials', true)
        ];
    }

    /**
     * update stock transaction for purchase orders
     * @param mixed $purchaseOrder id or array of Purchase orders
     * @return void
     */
    public static function updateForPo($purchaseOrder) {
        StockTransactionUpdater::update(self::SOURCE_PO, $purchaseOrder);
    }

    /**
     * update stock transaction for Purchase Refund
     * @param mixed $purchaseRefund id or array of Purchase Refund
     * @return void
     */
    public static function updateForPr($purchaseRefund) {
        StockTransactionUpdater::update(self::SOURCE_PR, $purchaseRefund);
    }

        /**
     * update stock transaction for Purchase Refund
     * @param mixed $purchaseRefund id or array of Purchase Refund
     * @return void
     */
    public static function updateForPdn($purchaseDebitNote) {
        StockTransactionUpdater::update(self::SOURCE_PDN, $purchaseDebitNote);
    }



    public static function updateForCn($creditNote)
    {
        StockTransactionUpdater::update(self::SOURCE_CN, $creditNote);
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $invoiceModel->update_cost_journals($creditNote);
    }

    public static function updateForRR($refundReceipt)
    {
        StockTransactionUpdater::update(self::SOURCE_RR, $refundReceipt);
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $invoiceModel->update_cost_journals($refundReceipt);
    }

    public static function updateForInvoice($invoice)
    {
        StockTransactionUpdater::update(self::SOURCE_INVOICE, $invoice);
        $invoiceModel = GetObjectOrLoadModel('Invoice');
        $invoiceModel->update_cost_journals($invoice);
    }

    //this function is used to change the date of the "Add Quantity" transaction stock , which added after negative stock balance to be added before the stock become negative
    //we created this function to correct the average cost calculations when adding a new stock during the stock balance is negative, we should then add this transaction before the stock become negative
    public function correctNegativeTransactionAdd($transaction_id)
    {

        if(is_array($transaction_id)&&isset($transaction_id['StockTransaction']))
        {
            $transaction=$transaction_id;
            $transaction_id=$transaction['StockTransaction']['id'];
        }

        else if(is_numeric($transaction_id))
        {
            $transaction=$this->findById($transaction_id);
        }
        else return false;
        $before_options =  ['recursive' => -1 , 'order' => 'StockTransaction.received_date DESC , id DESC',
            'conditions' => [
                'status NOT'=> [self::STATUS_DRAFT, self::STATUS_PENDING] ,
                '(ignored = 0 OR ignored IS NULL)',
                'product_id'=>$transaction['StockTransaction']['product_id'],
                'OR' => [
                    'received_date < '=> $transaction['StockTransaction']['received_date'],
                    'AND' => [
                        'received_date = ' => $transaction['StockTransaction']['received_date'],
                        'id < ' => $transaction['StockTransaction']['id']
                    ]
                ]]];
        $s = $this->find ('first',  array_merge (['fields' => 'sum(quantity) as previous_quantity'], $before_options )) ;

        $current_quantity = (!empty ($s[0]['previous_quantity'])?$s[0]['previous_quantity'] : 0);
        if($current_quantity >=0) return true;


        $before_options['limit']=100;
        $before_options['page']=0;
        $transactions=['no_empty'];
        while(!empty($transactions))
        {

            $before_options['page']++;
            $transactions = $this->find ( 'all' ,$before_options );

            if(!empty($transactions)){
                $last_date = date('Y-m-d H:i:s',strtotime('-1 Minute', strtotime($transactions[0]['StockTransaction']['received_date'])));
                /*
                    Here this block of code tries to substract the quantity of hte previous transaction from the current quantity in hope of finding the transaction that made the stock negative so it puts the
                    other transaction that will add to the stock before it (in hope of correcting the negative stock) so we can calculate the average price :) (in the next step)
                */

                foreach ($transactions as $t) {
                    // We already take count for the current transaction id in the before conditions ? i Think why should we ignore the first transaction but we SUM it in previous quantity ???
                    $current_quantity -= $t['StockTransaction']['quantity'];
                    if ($current_quantity >= 0) {
                        break;
                    }
                    $last_date = date('Y-m-d H:i:s', strtotime('-1 Minute', strtotime($t['StockTransaction']['received_date'])));
                }
                if ($last_date < $transaction['StockTransaction']['received_date']) {
                    $old_date = $transaction['StockTransaction']['received_date'];
                    $transaction['StockTransaction']['received_date'] = $last_date;
                    $is_saved = $this->save($transaction);
                    if (!$is_saved) {
                        /*
                            If Editing the "received_date" of the transaction failed for any reason we simply exit by returning
                            and then we show the validation error as an error message
                        */
                        CustomValidationFlash([$this->validationErrors['received_date']]);
                        return false;
                    }
                    $this->transaction_date_change($transaction, $old_date);

                    if (strtotime($transaction['StockTransaction']['received_date']) > 0) {
                        $transactions = null;
                        $this->log("Correct Negative Transaction {$transaction['StockTransaction']['id']} date: {$transaction['StockTransaction']['received_date']} current quantity: $current_quantity",LOG_DEBUG);
                        return $transaction;
//                        return $this->average_on_all($transaction['StockTransaction']['product_id'], $transaction['StockTransaction']['id']);
                    }
                    $transactions = null;
                }

            }
        }
    }

    /**
     * @param $product_id
     * @param null $store_id
     * @param null $my_store_total
     * @param null $product_store_name
     * @param null $product_average_price
     * @return int total stock balance
     * update the product total stock balance and product store stock balance
     * and sets $my_store_total = store stock balance
     */
    public static function updateProductBalance($product_id ,  $store_id = null ,&$my_store_total = null , &$product_store_name = null , &$product_average_price = null, $order_id = null, $source_type = null, $transactionType = null) {
        $storeModel = ClassRegistry::init('Store');
        if ( is_null ($store_id ) ) {
            $store_id = $storeModel->getPrimaryStore () ;
        }

        //get product stock in the updated store
        $store_total = self::getProductStock($product_id , $store_id, [], false, $order_id, $source_type);
        //findById ($store_id);
        $reread_store = $storeModel->find('first' , ['recursive' => -1,'conditions' => ['Store.id' => $store_id]]);
        $product_store_name = $reread_store['Store']['name'];
        $my_store_total = $store_total;
        //get all product stock
        $total = self::getProductStock($product_id , null , [] , true, $order_id, $source_type); //Gets total in all cases

        $productModel = GetObjectOrLoadModel('Product');
        $read_product = $productModel->read(null, $product_id);
        $product_average_price = $read_product['Product']['average_price'];
        if ((float)$total <= (float)$read_product['Product']['low_stock_thershold'] &&!empty($read_product['Product']['track_stock'])) {
            $more = ['param1' => $total];
            NotificationV2::add_notification(NotificationV2::Product_Low_Stock, $product_id,[getAuthOwner('staff_id')] , NotificationV2::NOTI_USER_TYPE_STAFF,null, null,null, $more);
        } else {
            if(empty($transactionType) || $transactionType == self::TRANSACTION_IN) {
                NotificationV2::delete_notificationbyref($product_id, NotificationV2::Product_Low_Stock);
            }
        }
        if ( !empty($product_id))
        {
            //update total stock balance of product
            GetObjectOrLoadModel('Product')->query("UPDATE products SET stock_balance = $total WHERE id = $product_id", false);
        }
        //saving store total
        $storeStockBalanceModel = ClassRegistry::init('StoreStockBalance');
        $stock_balance = $storeStockBalanceModel->find('first' , ['conditions' => ['product_id' => $product_id , 'store_id' =>$store_id]]);
        if ( empty ( $stock_balance ) ){
            //update product store stock balance
            $storeStockBalanceModel->create ( ) ;
            $storeStockBalanceModel->set (['StoreStockBalance' =>['product_id' => $product_id , 'store_id' =>$store_id , 'balance' => $store_total] ] );
            $storeStockBalanceModel->save ();
        } else {
            // update product store stock balance
            $storeStockBalanceid = $stock_balance['StoreStockBalance']['id'];
            GetObjectOrLoadModel('StoreStockBalance')->query("UPDATE store_stock_balance SET balance = $store_total WHERE id = $storeStockBalanceid", false);
        }
        return $total;
    }


    function updatePurchaseOrderStatus($purchaseOrderId) {
        $PurchaseOrderModel = GetObjectOrLoadModel('PurchaseOrder');
        //handle update purchase order received status
        $total = $this->find('count', ['conditions' => ['StockTransaction.source_type' => self::SOURCE_PO, 'StockTransaction.order_id' => $purchaseOrderId]]);
        $received = $this->find('count', ['conditions' => [' (ignored = 0 OR ignored IS NULL) ','StockTransaction.status' => 4, 'StockTransaction.source_type' => self::SOURCE_PO, 'StockTransaction.order_id' => $purchaseOrderId]]);
        $not_received = $this->find('count', ['conditions' => [' (ignored = 0 OR ignored IS NULL) ','StockTransaction.status !=' => 4, 'StockTransaction.source_type' => self::SOURCE_PO, 'StockTransaction.order_id' => $purchaseOrderId]]);
        $re_read_po = $PurchaseOrderModel->find('first',['recursive'=>0,'conditions'=>['PurchaseOrder.id'=>$purchaseOrderId]]);
        if($re_read_po)
        {
            // we added $total > 0 to make sure that there is at least 1 stock_transaction item in the purchase order incase the user decided to write an imaginary product that doesn't exist
            if (($total - $received) == 0 && $total > 0) {
                //set status as received

                $PurchaseOrderModel->id = $purchaseOrderId;



                if($re_read_po['PurchaseOrder']['is_received'] !== "1") {
                    $PurchaseOrderModel->saveField('is_received', true);
                    //this is correct as discuessed with business
                    $PurchaseOrderModel->saveField('received_date', date('Y-m-d H:i:s'));
                }

                $arry = ['primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => empty($re_read_po['PurchaseOrder']['draft']) ? $re_read_po['PurchaseOrder']['is_received'] : -1, 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']];
                $this->add_actionline(ACTION_UPDATE_PO, $arry);
            }

            else if (($total - $received) != 0 && $received > 0) {
                //set status as partial received

                $PurchaseOrderModel->id = $purchaseOrderId;
                $PurchaseOrderModel->saveField('is_received', 2);
                $PurchaseOrderModel->saveField('received_date', date('Y-m-d H:i:s'));

                $arry = ['primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => empty($re_read_po['PurchaseOrder']['draft']) ? $re_read_po['PurchaseOrder']['is_received'] : -1, 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']];
                $this->add_actionline(ACTION_UPDATE_PO, $arry);

            }
            // we added $total > 0 to make sure that there is at least 1 stock_transaction item in the purchase order incase the user decided to write an imaginary product that doesn't exist
            else if (($total - $not_received) == 0 && $total > 0) {
                //set as not received
                $PurchaseOrderModel->id = $purchaseOrderId;
                $PurchaseOrderModel->saveField('is_received', 0);
                $PurchaseOrderModel->saveField('received_date', null);
                $arry = ['primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => empty($re_read_po['PurchaseOrder']['draft']) ? $re_read_po['PurchaseOrder']['is_received'] : -1, 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number']];
                $this->add_actionline(ACTION_UPDATE_PO, $arry);
            }


            if(empty($action_line_data['param3'])) $action_line_data['param3']=$re_read_po['PurchaseOrder']['no'];
        }

    }

    /**
     *
     * @param mixed $data
     * @param type $action_line_data
     * @param type $is_transfer
     * @return type
     */
    public static function saveTransaction($data, $action_line_data = false , $is_transfer = false) {
        if (isset($data['po_status']) && $data['po_status'] == 2) {
            unset($data['status'], $data['po_status']);
        }

        $Transaction = new self();

        // Handle tracking data if present
        if(isset($data['Product']['tracking_type']) && $data['Product']['tracking_type'] !== TrackStockUtil::QUANTITY_ONLY) {
            $trackingData = $data['tracking_data'];
            $old_tracking_id = TrackingNumberHelper::findTrackingId($trackingData, $data['Product']['tracking_type'], $data['Product']['id'], $data['store_id']);
            $oldStockTransactionsConditions =  [
                'StockTransaction.source_type' => $data['source_type'],
                'StockTransaction.order_id' => $data['order_id'],
                'StockTransaction.product_id' => $data['product_id'],
                'StockTransaction.store_id' => $data['store_id'],
                'StockTransaction.tracking_number_id' => $old_tracking_id,
                /**
                 * we can't use -1 bcs of serials in case u added multiple serials in purchase order to the same item it will create only 1 stock transaction
                 * "StockTransaction.tracking_number_id" => [$old_tracking_id , -1]
                 */

            ];
            if(empty($trackingData['lot']) && empty($trackingData['serial']) && empty($trackingData['expiry_date']))
            {
                unset($oldStockTransactionsConditions['StockTransaction.tracking_number_id']);
                $oldStockTransactionsConditions[] = ['(StockTransaction.tracking_number_id IS NULL OR StockTransaction.tracking_number_id = -1)'];
            }
            $old = $Transaction->find('first', ['conditions' => $oldStockTransactionsConditions]);

            if($data['tracking_number_id'] === -1)
            {
                $old = null;
            }
        } else {
            $old = $Transaction->find('first', ['conditions' =>
                [
                    'StockTransaction.source_type' => $data['source_type'],
                    'StockTransaction.order_id' => $data['order_id'],
                    'StockTransaction.product_id' => $data['product_id'],
                    'StockTransaction.store_id' => $data['store_id'],
                ]
            ]);
        }

        $stockarray['StockTransaction'] = $data;
        $product = null;
        if(isset($data['Product']) && !empty($data["Product"])) {
            $product['Product'] = $data['Product'];
        }
        // Here We Handle when to save Quantity as Negative Or Positive in Stock Transactions Table
        $stockarray['StockTransaction']['quantity'] = ($data['transaction_type'] == self::TRANSACTION_IN) ? $data['quantity'] : -1 * abs($data['quantity']);

        //Those 2 values are used for add_to_average function
        if($old) {
            $old['StockTransaction']['subscription_id'] = $data['subscription_id'];  //1
            $old['StockTransaction']['client_id'] = $data['client_id']; //2
        }

        if ( !empty ( $stockarray['StockTransaction']['product_id'] ) && empty($product) ) {
            //get transaction product
            $Transaction->loadModel ( 'Product' );
            $product = $Transaction->Product->find ( 'first', [  'recursive' => -1 ,  'conditions' => ['Product.id' => $stockarray['StockTransaction']['product_id']] ]);
        }

        if (isset($old['StockTransaction']['id']) && !$is_transfer) {
            $is_new = false;
            $stockarray['StockTransaction']['id'] = $old['StockTransaction']['id'];
            if ( $product['Product']['type'] == Product::BUNDLE_TYPE && !in_array($old['StockTransaction']['source_type'] , [self::SOURCE_BUNDLE,self::SOURCE_BUNDLE_RECON] ) ){
                //remove bundle transactions because every time we save the bundle we save its transactions
                self::removeBundleTransaction($old);
            }
        } else {
            $is_new = true;
            $Transaction->create();
        }

        // Handle dates for processed transactions
        if ($is_new && $stockarray['StockTransaction']['status'] === self::STATUS_PROCESSED && empty($stockarray['StockTransaction']['date']))
        {
            $stockarray['StockTransaction']['date'] = date('Y-m-d H:i:s');
        }
        if ($is_new && $stockarray['StockTransaction']['status'] === self::STATUS_PROCESSED && empty($stockarray['StockTransaction']['received_date']))
        {
            $stockarray['StockTransaction']['received_date'] = date('Y-m-d H:i:s');
        }
        if (!$is_new && $stockarray['StockTransaction']['status'] === self::STATUS_PROCESSED && $old['StockTransaction']['status'] != self::STATUS_PROCESSED && empty($stockarray['StockTransaction']['received_date']))
        {
            $stockarray['StockTransaction']['received_date'] = date('Y-m-d H:i:s');
        }

        if(empty($stockarray['StockTransaction']['price'])) $stockarray['StockTransaction']['price']=0;

        if ($stockarray['StockTransaction']['store_id'] === null){
            $storeModel = GetObjectOrLoadModel('Store');
            $stockarray['StockTransaction']['store_id'] = $storeModel->getPrimaryStore () ;
        }

        // Handle original cost for refund/credit transactions
        if ($Transaction->isRefundCreditTransaction($stockarray['StockTransaction'])) {
            $originalCost = $Transaction->getOriginalCostPriceForRefund($stockarray['StockTransaction']);
            if ($originalCost > 0) {
//                $stockarray['StockTransaction']['price'] = $originalCost;
                $stockarray['StockTransaction']['purchase_price'] = $originalCost;
            } else {
                // Fallback to current average price if original cost not found
                $stockarray['StockTransaction']['purchase_price'] = (empty($product['Product']['average_price'] ) ? 0 : $product['Product']['average_price'] ) ;
            }
        } else {
            $stockarray['StockTransaction']['purchase_price'] = !empty($stockarray['StockTransaction']['purchase_price']) ? $stockarray['StockTransaction']['purchase_price'] : (empty($product['Product']['average_price'] ) ? 0 : $product['Product']['average_price'] ) ;
        }

        $Transaction->save($stockarray,false);

        if (!isset($data['Product'])) {
            $data['Product'] = $product['Product'];
        }

        // Handle tracking numbers
        if ($data['Product']['tracking_type'] !== null && $data['Product']['tracking_type'] !== TrackStockUtil::QUANTITY_ONLY)
        {
            $old_tracking_transaction_qty =  $old['StockTransaction']['quantity'];
            $new_transaction_qty = $data['quantity'];
            $transaction_status = $stockarray['StockTransaction']['status'];
            $old_transaction_status = $old['StockTransaction']['status'];
            $mode = isset($old['StockTransaction']['id']) ? 'Edit' : 'Add';
            $TrackingNumberHelper = new TrackingNumberHelper();

            if(($old_transaction_status == self::STATUS_DRAFT && $transaction_status == self::STATUS_DRAFT) || ($old_transaction_status == self::STATUS_PENDING && $transaction_status == self::STATUS_PENDING)){
                $new_transaction_qty = 0;
            }
            elseif(($old_transaction_status == null && $transaction_status == self::STATUS_DRAFT) || ($old_transaction_status == null && $transaction_status == self::STATUS_PENDING)){
                $new_transaction_qty = 0;
            }
            elseif(($old_transaction_status == self::STATUS_DRAFT || $old_transaction_status == self::STATUS_PENDING) && $transaction_status == self::STATUS_PROCESSED){
                $old_tracking_transaction_qty = 0;
            }
            elseif($old_transaction_status == self::STATUS_PROCESSED && ($transaction_status == self::STATUS_DRAFT || $transaction_status == self::STATUS_PENDING)){
                $mode = 'Remove';
            }
            // If there was no old and the new is zero then we should just treat it as a placeholder as well.
            if($old_transaction_status == null && $new_transaction_qty == 0){
                $new_tracking_id = -1;
            }

            $tracking_number_quantity = TrackingNumberHelper::calculateTrackingQuantity($data['transaction_type'], $mode, $old_tracking_transaction_qty, $new_transaction_qty);
            if(!isset($new_tracking_id)){
                $new_tracking_id = $TrackingNumberHelper->recordTracking($data['Product'], $data['tracking_data'], $data['Product']['id'], $tracking_number_quantity, $data['store_id']);
            }
            $Transaction->saveField('tracking_number_id', $new_tracking_id);
            $TrackingNumberHelper->fixTrackingNumberQuantity($new_tracking_id);
        }

        $transaction_id = $Transaction->id;
        unset($Transaction->id);
        if (empty ($stockarray['StockTransaction']['id'])) {
            $stockarray['StockTransaction']['id'] = $transaction_id;
        }

        // Calculate average price
        $Transaction->average_on_all($stockarray['StockTransaction']['product_id'], $stockarray , $old['StockTransaction']['received_date'] ?? null);

        // Handle bundles
        $enable_bundles = settings::getValue(InventoryPlugin, 'enable_bundles');
        $bundle_type = settings::getValue(InventoryPlugin, 'bundle_type');
        if ( $enable_bundles && Product::checkProductBundleTypePack($product)) {
            if (
                (
                    //source manual stock transaction
                    (!empty($stockarray['StockTransaction']['fetch_from_raw'] )&&$stockarray['StockTransaction']['source_type'] == self::SOURCE_MANUAL) ||
                    //source manual requisition
                    ($stockarray['StockTransaction']['source_type'] == self::SOURCE_RQ_MANUAL_INBOUND)
                )
                && ($product['Product']['type'] == settings::OPTION_SOLD_TYPE_BUNDLES   &&  $stockarray['StockTransaction']['quantity'] > 0)
            ){
                //save bundles stock transaction foreach product in the bundle
                //it creates a stock transaction in which the order id is the
                //bundle stock transaction
                self::updateForBundles($stockarray['StockTransaction']);
                $Transaction->update_bundle_journal($stockarray);
            }
        }

        $new = $Transaction->find('first', ['conditions' => ['StockTransaction.source_type' => $data['source_type'], 'StockTransaction.order_id' => $data['order_id'], 'StockTransaction.product_id' => $data['product_id']]]);

        // Transaction Source Purchase Order
        if ($stockarray['StockTransaction']['source_type'] == self::SOURCE_PO && $old['StockTransaction']['status'] !=  $stockarray['StockTransaction']['status'] ) {
            $Transaction->updatePurchaseOrderStatus($new['StockTransaction']['order_id']);
        }
        else if($stockarray['StockTransaction']['source_type'] == self::SOURCE_INVOICE && empty($action_line_data['param3']))
        {
            //set activity log param3 as order id
            $InvoiceModel = ClassRegistry::init('Invoice');
            $invoice = $InvoiceModel->read(null, $new['StockTransaction']['order_id']);
            $action_line_data['param3']=$invoice['Invoice']['no'];
        }

        $product_store_count = 0;
        $product_store_name = "0";
        $product_average_price = "0";

        if (!empty ( $old ) && $old['StockTransaction']['store_id'] != $new['StockTransaction']['store_id'])
        {
            //update the product stock balance and store stock balance of the old stock transaction store
            self::updateProductBalance($stockarray['StockTransaction']['product_id'] , $old['StockTransaction']['store_id'],$product_store_count,$product_store_name , $product_average_price);
        }

        //update the product stock balance and store stock balance of the new stock transaction store
        $product_count = self::updateProductBalance($stockarray['StockTransaction']['product_id'] , $stockarray['StockTransaction']['store_id'],$product_store_count,$product_store_name , $product_average_price, $stockarray['StockTransaction']['order_id'], $stockarray['StockTransaction']['source_type'], $stockarray['StockTransaction']['transaction_type']);
        $storeQuantityBalance = $product_store_count;
        $conditionNumber = 0;
        $conditionNumber = ($_GET['conditionNumber']) ? $_GET['conditionNumber'] : $conditionNumber;

        if ( $storeQuantityBalance < $conditionNumber && $enable_bundles && Product::checkProductBundleTypeCompound($product) && $product['Product']['type'] == settings::OPTION_SOLD_TYPE_BUNDLES && !in_array($stockarray['StockTransaction']['source_type'] , [self::SOURCE_BUNDLE , self::SOURCE_BUNDLE_RECON] ) ) {
            //create the bundles transaction to zero the compound bundle balance
            self::adjustBundleBalance($stockarray['StockTransaction']['product_id'] , $stockarray['StockTransaction']['quantity'] * -1 ,$stockarray );
        }

        if ($action_line_data != false) {
            $action_line_key = false;
            if (!is_array($action_line_data)) {
                $action_line_data = [];
            }
            $action_line_data = array_merge(['primary_id' => $new['StockTransaction']['order_id'], 'secondary_id' => $new['StockTransaction']['product_id'], 'param1' => $stockarray['StockTransaction']['transaction_type'] == self::TRANSACTION_IN ? $stockarray['StockTransaction']['quantity'] : $new['StockTransaction']['quantity'], 'param2' => $new['StockTransaction']['id'],'param7' => $new['StockTransaction']['currency_code'], 'param6' => $new['StockTransaction']['price'], 'param8' => $product_count,'param9' => json_encode ([$product_store_count, $product_store_name, $product_average_price])], $action_line_data);

            if (($is_new && $new['StockTransaction']['status'] == self::STATUS_PROCESSED && !$new['StockTransaction']['ignored']) || (!$is_new && $old['StockTransaction']['status'] != self::STATUS_PROCESSED && $new['StockTransaction']['status'] == self::STATUS_PROCESSED) || (!$is_new && $old['StockTransaction']['ignored'] && !$new['StockTransaction']['ignored'])) {
                switch ($stockarray['StockTransaction']['source_type']) {
                    case self::SOURCE_INVOICE:
                        $action_line_key = ACTION_TRANSACTION_INVOICE_ADDED;
                        break;
                    case self::SOURCE_PO:
                        $action_line_key = ACTION_TRANSACTION_PO_ADDED;
                        break;
                    case self::SOURCE_PR:
                        $action_line_key = ACTION_TRANSACTION_PR_ADDED;
                        break;
                    case self::SOURCE_CN:
                        $action_line_key = ACTION_TRANSACTION_CREDITNOTE_ADDED;
                        break;
                    case self::SOURCE_RR:
                        $action_line_key = ACTION_TRANSACTION_REFUND_ADDED;
                        break;
                    default:
                        $action_line_key = ACTION_TRANSACTION_MANUAL_ADDED;
                }
            } else if (!$is_new && !$new['StockTransaction']['ignored'] && $new['StockTransaction']['status'] == self::STATUS_PROCESSED && $old['StockTransaction']['status'] == self::STATUS_PROCESSED && ($old['StockTransaction']['price'] != $new['StockTransaction']['price'] || $old['StockTransaction']['quantity'] != $new['StockTransaction']['quantity'] || $old['StockTransaction']['store_id'] != $new['StockTransaction']['store_id'])) {
                switch ($stockarray['StockTransaction']['source_type']) {
                    case self::SOURCE_INVOICE:
                        $action_line_key = ACTION_TRANSACTION_INVOICE_UPDATED;
                        break;
                    case self::SOURCE_PO:
                        $action_line_key = ACTION_TRANSACTION_PO_UPDATED;
                        break;
                    case self::SOURCE_PR:
                        $action_line_key = ACTION_TRANSACTION_PR_UPDATED;
                        break;
                    case self::SOURCE_CN:
                        $action_line_key = ACTION_TRANSACTION_CREDITNOTE_UPDATED;
                        break;
                    case self::SOURCE_RR:
                        $action_line_key = ACTION_TRANSACTION_REFUND_UPDATED;
                        break;
                    default:
                        $action_line_key = ACTION_TRANSACTION_MANUAL_UPDATED;
                }
            } else if ((!$is_new && $new['StockTransaction']['ignored'] && !$old['StockTransaction']['ignored']) || (!$is_new && $new['StockTransaction']['status'] != self::STATUS_PROCESSED && $old['StockTransaction']['status'] == self::STATUS_PROCESSED)) {
                switch ($stockarray['StockTransaction']['source_type']) {
                    case self::SOURCE_INVOICE:
                        $action_line_key = ACTION_TRANSACTION_INVOICE_DELETED;
                        break;
                    case self::SOURCE_PO:
                        $action_line_key = ACTION_TRANSACTION_PO_DELETED;
                        break;
                    case self::SOURCE_PR:
                        $action_line_key = ACTION_TRANSACTION_PR_DELETED;
                        break;
                    case self::SOURCE_CN:
                        $action_line_key = ACTION_TRANSACTION_CREDITNOTE_DELETED;
                        break;
                    case self::SOURCE_RR:
                        $action_line_key = ACTION_TRANSACTION_REFUND_DELETED;
                        break;
                    default:
                        $action_line_key = ACTION_TRANSACTION_MANUAL_DELETED;
                }
            }

            if ($action_line_key != false)
                $Transaction->add_actionline($action_line_key, $action_line_data);
        }
        return $transaction_id;
    }


    public static function removeTransaction($transaction, $action_line_data = false) {
        $Transaction = new self();
        if (is_numeric($transaction)) {
            $old = $Transaction->find('first', ['applyBranchFind' => false, 'conditions' => ['StockTransaction.id' => $transaction]]);
        } else {
            $conditions = [
                'StockTransaction.source_type' => $transaction['source_type'],
                'StockTransaction.order_id' => $transaction['order_id'],
                'StockTransaction.product_id' => $transaction['product_id'],
                'StockTransaction.store_id' => $transaction['store_id']
            ];
            $product = $Transaction->Product->find('first' ,['conditions' => ['Product.id' =>$transaction['product_id']  ] ] );
            if(
                !empty($product['Product']['tracking_type']) &&
                $product['Product']['tracking_type'] != TrackStockUtil::QUANTITY_ONLY
            ) {
                $conditions['StockTransaction.tracking_number_id'] = $transaction['tracking_number_id'];
            }
            $old = $Transaction->find('first', ['applyBranchFind' => false,
                'conditions' => $conditions]);
        }



        if (!empty($old)) {
            $Transaction->id = $old['StockTransaction']['id'];
            if ( !empty ( $old['StockTransaction']['product_id'] ) ) {
                $Transaction->loadModel ( 'Product' );
                $Transaction->Product->id = $old['StockTransaction']['product_id'];
                $Transaction->Product->saveField ( 'updated_price' , 0);
                if(empty($product)) {
                    $product = $Transaction->Product->find('first' ,['conditions' => ['Product.id' =>$old['StockTransaction']['product_id']  ] ] );
                }
                if ( $product['Product']['type'] == settings::OPTION_SOLD_TYPE_BUNDLES && !in_array($old['StockTransaction']['source_type'] , [self::SOURCE_BUNDLE , self::SOURCE_BUNDLE_RECON] ) )
                {
                    //remove bundle transaction
                    self::removeBundleTransaction($old);
                }
            }

            // Delete StockTransaction First before Counting it :)
            $Transaction->delete();
            // If the Stock Transaction Had Tracking id & status was Processed it must reflect back in Tracking Numbers !
            if(isset($old['StockTransaction']['tracking_number_id']) && $old['StockTransaction']['tracking_number_id'] != -1 && $old['StockTransaction']['status'] == self::STATUS_PROCESSED){
                $TrackingNumberHelper = new TrackingNumberHelper();
                $trackingNumberQuantity = TrackingNumberHelper::calculateTrackingQuantity($old['StockTransaction']['transaction_type'], 'Remove', $old['StockTransaction']['quantity'], null);
                $TrackingNumberHelper->adjustTrackingQuantity($old['StockTransaction']['tracking_number_id'], $trackingNumberQuantity);
                $TrackingNumberHelper->fixTrackingNumberQuantity($old['StockTransaction']['tracking_number_id']);
            }
            $Transaction->average_on_all($old['StockTransaction']['product_id'], $old, $old['StockTransaction']['received_date'] ?? null);
            $avg_price = 0  ;
            $product_count = self::updateProductBalance($old['StockTransaction']['product_id'] ,$old['StockTransaction']['store_id'] , $a , $aa , $avg_price );
            if ( !$old['StockTransaction']['ignored'] && $old['StockTransaction']['status'] == self::STATUS_PROCESSED) {
                $productModel = GetObjectOrLoadModel('Product');
                $product = $productModel->find('first',['conditions' =>['Product.id' =>$old['StockTransaction']['product_id']]]);

                switch ($old['StockTransaction']['source_type']) {
                    case self::SOURCE_INVOICE:
                        $action_line_key = ACTION_TRANSACTION_INVOICE_DELETED;
                        break;
                    case self::SOURCE_PO:
                        $action_line_key = ACTION_TRANSACTION_PO_DELETED;
                        break;
                    case self::SOURCE_PR:
                        $action_line_key = ACTION_TRANSACTION_PR_DELETED;
                        break;
                    case self::SOURCE_CN:
                        $action_line_key = ACTION_TRANSACTION_CREDITNOTE_DELETED;
                        break;
                    case self::SOURCE_RR:
                        $action_line_key = ACTION_TRANSACTION_REFUND_DELETED;
                        break;
                    default:
                        $action_line_key = ACTION_TRANSACTION_MANUAL_DELETED;
                }

                if ($action_line_key != false) {
                    if (!is_array($action_line_data))
                        $action_line_data = [];

                    $action_line_data = array_merge(['primary_id' => $old['StockTransaction']['order_id'], 'secondary_id' => $product['Product']['id'], 'param1' => $old['StockTransaction']['quantity'], 'param2' => $old['StockTransaction']['id'], 'param4' => $product['Product']['product_code'], 'param5' => $product['Product']['name'], 'param6' => $old['StockTransaction']['price'], 'param8' => $product_count , 'param9' => json_encode (['','',$avg_price])], $action_line_data);

                    $Transaction->add_actionline($action_line_key, $action_line_data);
                }
            }
        }
    }

    public static function SaveProductStock($product_id = null, $transaction_type = null, $source_type = null, $ref_id = null, $date = null, $notes = null, $quantity = null, $price = null, $price_without_tax = null, $added_by = null, $ignored = null, $ignored_date = null, $status = 0) {
        $product = new self();
        $stockarray = [];
        $find = $product->find('first', ['conditions' => ['StockTransaction.source_type' => $source_type, 'StockTransaction.transaction_type' => $transaction_type, 'StockTransaction.product_id' => $product_id, 'StockTransaction.ref_id' => $ref_id]]);
        if (isset($find['StockTransaction']['id']) and $transaction_type == 1 and $source_type == 3) {
            $stockarray['StockTransaction']['id'] = $find['StockTransaction']['id'];
        } else {
            $product->create();
        }
        $stockarray['StockTransaction']['product_id'] = $product_id;
        $stockarray['StockTransaction']['transaction_type'] = $transaction_type;
        $stockarray['StockTransaction']['source_type'] = $source_type;
        $stockarray['StockTransaction']['ref_id'] = $ref_id;

        $stockarray['StockTransaction']['date'] = $date;
        $stockarray['StockTransaction']['notes'] = $notes;
        $stockarray['StockTransaction']['quantity'] = $quantity;
        $stockarray['StockTransaction']['price'] = $price;
        $stockarray['StockTransaction']['price_without_tax'] = $price_without_tax;
        $stockarray['StockTransaction']['added_by'] = $added_by;
        $stockarray['StockTransaction']['ignored'] = $ignored;
        $stockarray['StockTransaction']['ignored_date'] = $ignored_date;
        $stockarray['StockTransaction']['status'] = $status;
        $stockarray['StockTransaction']['balance_to_date'] = self::getProductStock($product_id) + $quantity;
        $product->save($stockarray);
    }

    public static function DeletePurchaseOrderItem($id) {
        $st = new self();
        $transactions_deleted = $st->find ( 'list' , ['applyBranchFind' => false, 'conditions' => [StockTransaction::SOURCE_PO,'StockTransaction.ref_id' => $id] ]);
        foreach ( $transactions_deleted as $k => $c ){
            StockTransaction::removeTransaction($k);
        }
    }
    public function get_profit ( ) {
        $transactions = [
            ['StockTransaction'=> ['price' => 8 , 'quantity' => 10 , 'source_type' => 3]] ,
            ['StockTransaction'=> ['price' => 15 , 'quantity' => 2 , 'source_type' => 2]] ,
            ['StockTransaction'=> ['price' => 13 , 'quantity' => 2 , 'source_type' => 2]] ,
            ['StockTransaction'=> ['price' => 20 , 'quantity' => 7 , 'source_type' => 3]] ,
            ['StockTransaction'=> ['price' => 18 , 'quantity' => 5 , 'source_type' => 2]] ,
            ['StockTransaction'=> ['price' => 18 , 'quantity' => 2 , 'source_type' => 2]] ,
            ['StockTransaction'=> ['price' => 18 , 'quantity' => 4 , 'source_type' => 2]] ,
            ['StockTransaction'=> ['price' => 5 , 'quantity' => 1 , 'source_type' => 3]] ,
            ['StockTransaction'=> ['price' => 8 , 'quantity' => 2 , 'source_type' => 3]] ,
            ['StockTransaction'=> ['price' => 10 , 'quantity' => 4 , 'source_type' => 2]] ,
            ['StockTransaction'=> ['price' => 20 , 'quantity' => 1 , 'source_type' => 2]] ,
        ];
        $sold =0 ;
        $total_sale_price = 0 ;
        $bought = 0 ;
        $stock = [] ;
        $purchase_prices = [] ;
        foreach ( $transactions as $t ) {
            if ( $t['StockTransaction']['source_type'] == 2)
            {
                $quantity_temp = $t['StockTransaction']['quantity'];
                $sold += ($quantity_temp * $t['StockTransaction']['price'] ) ;
                $purchase_price = 0;
                while ( $quantity_temp > 0 ) {
                    $current_stock = array_shift ($stock) ;
                    if ( empty ( $current_stock ) ) {
                        break ;
                    }
                    if ( $quantity_temp >= $current_stock['quantity'])
                    {
                        $quantity_temp -= $current_stock['quantity'];
                        $purchase_price += ($current_stock['quantity'] *$current_stock['price'] );
                    }else {
                        $current_stock['quantity'] -= $quantity_temp;
                        $purchase_price += ($quantity_temp * $current_stock['price'] );
                        array_unshift ( $stock , $current_stock ) ;
                        break ;
                    }
                }
                $purchase_prices[] = $purchase_price ;
                $total_sale_price += $purchase_price ;
            }
            else if ( $t['StockTransaction']['source_type'] == 3)
            {
                $stock[] = ['price' => $t['StockTransaction']['price'] ,
                    'quantity' => $t['StockTransaction']['quantity']] ;
                $bought += ($t['StockTransaction']['price']* $t['StockTransaction']['quantity'] );
            }
        }
        debug ( "Total Invoices: ".$sold);
        debug ( "Total Sale Price: ".$total_sale_price);
        debug ( "Total Bought Price: ".$bought);
        debug ( "Profit: ".($sold - $total_sale_price));
        debug ( $purchase_prices ) ;
    }
    function apply_on_transaction  ( $transaction_id )
    {
        $transaction_read = $this->find  ( 'first' ,['conditions' =>  ['StockTransaction.id' => $transaction_id] ]) ;
        if ( $transaction_read['StockTransaction']['quantity'] > 0)
        {
            return false ;
        }
        $transactions = $this->find ( 'all' , ['order' => 'received_date asc','conditions' => ['StockTransaction.product_id' => $transaction_read['StockTransaction']['product_id'] ,  'received_date < ' => $transaction_read['StockTransaction']['received_date'] ,'StockTransaction.status' => StockTransaction::STATUS_PROCESSED ]]) ;
        $sold =0 ;
        $total_sale_price = 0 ;
        $bought = 0 ;
        $stock = [] ;
        $purchase_prices = [] ;
        foreach ( $transactions as $t ) {
            if ( $t['StockTransaction']['quantity'] <= 0)
            {
                $quantity_temp = $t['StockTransaction']['quantity']*-1;
                $sold += ($quantity_temp * $t['StockTransaction']['price'] ) ;
                $purchase_price = 0;
                $purchase_orders_ids = [] ;
                debug ( $quantity_temp ) ;
                while ( $quantity_temp > 0 ) {
                    $current_stock = array_shift ($stock) ;
                    if ( empty ( $current_stock ) ) {
                        break ;
                    }
                    $purchase_orders_ids[] = $stock['order_id'];
                    if ( $quantity_temp >= $current_stock['quantity'])
                    {
                        $quantity_temp -= $current_stock['quantity'];
                        $purchase_price += ($current_stock['quantity'] *$current_stock['price'] );
                    }else {
                        $current_stock['quantity'] -= $quantity_temp;
                        $purchase_price += ($quantity_temp * $current_stock['price'] );
                        array_unshift ( $stock , $current_stock ) ;
                        break ;
                    }
                }
                $t['StockTransaction']['purchase_order_ids'] = implode(',', $purchase_orders_ids);
                $t['StockTransaction']['purchase_price'] = (empty ($purchase_price)?0:$purchase_price );
                $this->id = $t['StockTransaction']['id'];
                $this->save ( $t ) ;
                $purchase_prices[] = $purchase_price ;
                $total_sale_price += $purchase_price ;
            }
            else if ( $t['StockTransaction']['quantity'] > 0)
            {
                $stock[] =  $t['StockTransaction'];
                $bought += ($t['StockTransaction']['price']* $t['StockTransaction']['quantity'] );
            }
        }
        //SALE
        $t = $transaction_read ;
        $quantity_temp = $t['StockTransaction']['quantity'];
        $sold += ($quantity_temp * $t['StockTransaction']['price'] ) ;
        $purchase_price = 0;
        $purchase_orders_ids = [] ;
        while ( $quantity_temp > 0 ) {
            $current_stock = array_shift ($stock) ;
            if ( empty ( $current_stock ) ) {
                break ;
            }
            $purchase_orders_ids[] = $stock['order_id'];
            if ( $quantity_temp >= $current_stock['quantity'])
            {
                $quantity_temp -= $current_stock['quantity'];
                $purchase_price += ($current_stock['quantity'] *$current_stock['price'] );
            }else {
                $current_stock['quantity'] -= $quantity_temp;
                $purchase_price += ($quantity_temp * $current_stock['price'] );
                array_unshift ( $stock , $current_stock ) ;
                break ;
            }
        }
        $t['StockTransaction']['purchase_order_ids'] = implode(',', $purchase_orders_ids);
        $t['StockTransaction']['purchase_price'] = (empty ($purchase_price )? 0: $purchase_price);
        $this->id = $t['StockTransaction']['id'];
        return $this->save ( $t ) ;
    }
    /**
     *
     * @param int $product_id
     */
    function apply_purchase_price_on_all ( $product_id  ) {
        $this->loadModel ( 'Product');

        $calculation_method = StockTransaction::CALC_METHOD_AVERAGE ;//settings::getValue(InventoryPlugin, 'calculation_method');
        switch ($calculation_method) {
            case StockTransaction::CALC_METHOD_BOUGHT_FIRST:
                $this->cost_first_calculation($product_id);
                break;
            case StockTransaction::CALC_METHOD_AVERAGE:
                $this->average_calculation_new($product_id);
                break ;
            default:
                $this->average_calculation_new($product_id) ;
                break ;
        }
        return true ;
    }
    //Currently shouldn't be used anywhere because FIFO is removed 28/12/2017
    function cost_first_calculation  ( $product_id , $fake_invetory = null , &$fake_price = 0 ) {
        $this->recursive = -1;
        $this->Product->recursive = -1 ;
        $product = $this->Product->find ( 'first' , ['conditions' => ['Product.id' => $product_id]]);
        $transactions = $this->find ( 'all' , ['fields' => "currency_code, id,quantity,price,source_type",  'order' => 'received_date asc', 'conditions' => ['ignored <> 1',  'StockTransaction.product_id' => $product_id ,'StockTransaction.status' => StockTransaction::STATUS_PROCESSED ]]) ;

        $default_currency = $this->get_default_currency();
        if ( !empty ( $fake_invetory  ) ){
            $fake_sale_transaction = ['StockTransaction' => [
                'currency_code' => $default_currency ,
                'id' => NULL , 'quantity' => abs($fake_invetory) * -1 ,'price' => NULL , 'source_type' => NULL ,
                'fake_inventory' => 1
            ] ];
            array_push( $transactions , $fake_sale_transaction  ) ;
        }
        $total_sale_price = 0 ;
//        $bought = 0 ; 
        $stock = [] ;
        $sale_stock = [] ;
        $purchase_prices = [] ;
        $no_stock_yet = true ;
        $old_purchase_price = 0 ;
        foreach ( $transactions as $t ) {
            if ( $t['StockTransaction']['quantity'] <= 0)
            {
                //SALE
                $quantity_temp = $t['StockTransaction']['quantity']*-1;
//                $sold += ($quantity_temp * $t['StockTransaction']['price'] ) ; 
                $purchase_price = 0;
                $purchase_orders_ids = [] ;
                while ( $quantity_temp > 0 ) {
                    $current_stock = array_shift ($stock) ;
//					
                    if ( $no_stock_yet ) {
                        $purchase_price = $product['Product']['buy_price'] * $t['StockTransaction']['quantity'];
                    }

                    if ( empty ( $current_stock )  ) {
                        if ( $purchase_price == 0 ) {
                            $purchase_price = $old_purchase_price ;
                        }
                        break ;
                    }
                    $purchase_orders_ids[] = $stock['order_id'];
                    if ( $quantity_temp >= $current_stock['quantity'])
                    {
                        $quantity_temp -= $current_stock['quantity'];
                        $purchase_price += ($current_stock['quantity'] *$current_stock['price']* CurrencyConverter::index($current_stock["currency_code"], $default_currency, $current_stock['received_date']) );
                        $sale_stock[] = $current_stock ;
                    }else {
                        $current_stock['quantity'] -= $quantity_temp;
                        $purchase_price += ($quantity_temp * $current_stock['price']*CurrencyConverter::index($current_stock["currency_code"], $default_currency, $current_stock['received_date']) );
                        $sale_stock[] = ['quantity' => $quantity_temp , 'price' =>$current_stock['price'] , 'currency_code' => $current_stock['currency_code'] ]  ;
                        array_unshift ( $stock , $current_stock ) ;
                        break ;
                    }
                }
                $t['StockTransaction']['purchase_order_ids'] = implode(',', $purchase_orders_ids);
                $t['StockTransaction']['purchase_price'] = (empty ($purchase_price ) ?0 : $purchase_price);
                if (!empty ($t['StockTransaction']['fake_inventory'] ) ) {
                    if ( $fake_invetory < 0 ) {
                        $fake_price = $purchase_price * -1 ;
                    }else {
                        $fake_price = $purchase_price ;
                    }

                }else {
                    $this->id = $t['StockTransaction']['id'];

                    $this->save ( $t ) ;
                    $old_purchase_price = $purchase_price ;
                    $purchase_prices[] = $purchase_price ;
                    $total_sale_price += $purchase_price ;
                    $sale_stock[] =  $t['StockTransaction'];
                }

            }
            else if ( $t['StockTransaction']['quantity'] > 0)
            {
                if ( in_array ( $t['StockTransaction']['source_type'] , [StockTransaction::SOURCE_CN , StockTransaction::SOURCE_RR] ) ) {
                    $quantity_temp = $t['StockTransaction']['quantity'];
//                    $sold += ($quantity_temp * $t['StockTransaction']['price'] ) ;
                    $purchase_price = 0;
                    while ( $quantity_temp > 0 ) {
                        $current_stock = array_shift ($sale_stock) ;
                        if ( empty ( $current_stock )  ) {
                            break ;
                        }
                        if ( $quantity_temp >= $current_stock['quantity'])
                        {
                            $quantity_temp -= $current_stock['quantity'];
                            $purchase_price += ($current_stock['quantity'] *$current_stock['price']*CurrencyConverter::index($current_stock["currency_code"], $default_currency, $current_stock['received_date']) );
                        }else {
                            $current_stock['quantity'] -= $quantity_temp;
                            $purchase_price += ($quantity_temp * $current_stock['price']*CurrencyConverter::index($current_stock["currency_code"], $default_currency, $current_stock['received_date']) );
                            array_unshift ( $sale_stock , $current_stock ) ;
                            break ;
                        }
                    }
                    $this->id = $t['StockTransaction']['id'];
                    $t['StockTransaction']['purchase_price'] = -1 * (empty ($purchase_price)? 0 :$purchase_price ) ;
                    $this->save ( $t ) ;
                }
                $no_stock_yet = false ;

                $stock[] =  $t['StockTransaction'];//['price' => $t['StockTransaction']['price'] ,
//                    'quantity' => $t['StockTransaction']['quantity']] ; 
//                $bought += ($t['StockTransaction']['price']* $t['StockTransaction']['quantity'] );
            }
        }
        if ( !empty ( $fake_invetory ) ) {

        }
        $this->Product->id = $product_id ;
        $this->Product->save ( ['Product' => ['updated_price' => StockTransaction::CALC_METHOD_BOUGHT_FIRST]]);
    }

    /**
     * Calculates the average price and save it to the product and return it
     * @param int $product_id
     * @param boolean $update_avg_price if true it will calculate and save all the purchase prices for invoices , additions
     * @return boolean
     */
    function average_calculation_new ( $product_id , $update_avg_price = false ) {

        $Product = ClassRegistry::init('Product');
        $Product->recursive = -1 ;
        $product = $Product->find ( 'first' , ['conditions' => ['Product.id' => $product_id , 'track_stock' => 1 ]]);
        if ( empty ( $product ) ){
            return false ;
        }
        $this->recursive = -1;
        $conditions = [ 'ignored <> 1',  'StockTransaction.product_id' => $product_id ,'StockTransaction.status' => StockTransaction::STATUS_PROCESSED ] ;
        if ( !$update_avg_price ) { //Getting only additions to stock , if there is no update for purchase price - we don't need it
            $conditions[]= 'StockTransaction.quantity > 0 ';
        }
        $transactions = $this->find ( 'all' , ['fields' => "currency_code, id,quantity,price,source_type",  'order' => 'received_date asc', 'conditions' => $conditions]) ;
        if ( $update_avg_price ) {
            //Getting invoices so that we know the purchase price of the invoice for RR and CN
            $CN_RR_purcase_prices = $this->flat_query_results("select st.id ,st_inv.id as inv_id, st_inv.average_price from stock_transactions st "
                . " inner join invoices rr_cn on id = st.order_id "
                . " inner join stock_transactions st_inv on st_inv.order_id = rr_cn.subscription_id and st_inv.product_id = st.product_id "
                . " where st.ignored <> 0 and st.status = ".StockTransaction::STATUS_PROCESSED." and st.product_id = ".intval ( $product_id ) ." st.source_type in ( ".StockTransaction::SOURCE_CN." , ".StockTransaction::SOURCE_RR." ) ");
            $cn_rr = [] ;
            foreach ( $CN_RR_purcase_prices as $l ) {
                $cn_rr['id'] = $l;
            }
            foreach ( $transactions as &$t) {
                $t['invoice_return_id'] = $cn_rr[$t['StockTransaction']['inv_id']];
                unset ( $t ) ;
            }
            $invoice_returns = []  ;
        }
        debug ( $transactions ) ;
        $average=  0 ;
        $quantity = 0 ;
        $default_currency = $this->get_default_currency();
        foreach ( $transactions as $tc ){
            if ( $update_avg_price && $tc['StockTransaction']['source_type'] == StockTransaction::SOURCE_INVOICE){
                //Updating purchase price for invoices , to be calculated in COST
                $this->id = $tc['StockTransaction']['id'];
                $purchase_price = ( $average/$quantity) ;
                $invoice_returns[$tc['StockTransaction']['id']] = $purchase_price ;
                $this->saveField ( 'purchase_price' , (empty ($purchase_price) ? 0 : $purchase_price) );
            }else {

                $rate_val = CurrencyConverter::index($tc['StockTransaction']["currency_code"], $default_currency, $tc['StockTransaction']['received_date']);
                if ( $update_avg_price ) {
                    if ( !empty ( $invoice_returns[$tc['invoice_return_id']])&& in_array ( $tc['StockTransaction']['source_type'] , [StockTransaction::SOURCE_CN , StockTransaction::SOURCE_RR] ))
                    {
                        //Gets purchase price from original invoice for RR and CN
                        $purchase_price = $invoice_returns[$tc['invoice_return_id']] ;
                    }else if ( !empty ( $cn_rr[$tc['StockTransaction']['id']] ) && in_array ( $tc['StockTransaction']['source_type'] , [StockTransaction::SOURCE_CN , StockTransaction::SOURCE_RR] ) )
                    {
                        //Gets purchase price from original invoice for RR and CN ( from previous query ) 
                        $purchase_price = $cn_rr[$tc['StockTransaction']['id']]['average_price']  ;
                    }else {
                        //When all fails , go to mama 
                        $purchase_price = ( $average/$quantity);
                    }
                    $this->id = $tc['StockTransaction']['id'];
                    $this->saveField ( 'purchase_price' , (empty ($purchase_price) ? 0 : $purchase_price));
                }
                $average += $rate_val * ($tc['StockTransaction']['quantity']*$tc['StockTransaction']['price']);// / $tc[0]['sq']);
                $quantity += $tc['StockTransaction']['quantity'] ;
            }
        }
        if ( !empty ( $average ) ) {
            $average = $average / $quantity ;
        }
        $Product->id = $product_id ;
        debug('External AVG  '.$average);
        $Product->saveField ( 'average_price' , $average);
        return $average ;

    }
    //Older method - NOT USED !!!
    function cost_average_calculation ( $product_id ) {
        $this->recursive = -1 ;
        $transaction_currencies = $this->find ( 'all' , ['group'=>['StockTransaction.currency_code'] ,  'fields' => "currency_code , sum(quantity*price) as sqp, sum(quantity) as sq", 'conditions' => ['ignored <> 1','StockTransaction.source_type' => StockTransaction::SOURCE_PO,  'StockTransaction.product_id' => $product_id ,'StockTransaction.status' => StockTransaction::STATUS_PROCESSED ]]) ;
        if ( empty ($transaction_currencies ))
        {
            $this->loadModel ( 'Product');
            $product = $this->Product->find ( 'first', ['conditions' => ['Product.id' => $product_id ]]);

            $avg =  $product['Product']['buy_price'];
        }else {
            $avg = 0  ;
        }
        $quantity = 0 ;
        $total_avg = 0 ;
        $default_currency = $this->get_default_currency();
        foreach ( $transaction_currencies as $tc ){


            $rate_val = CurrencyConverter::index($tc['StockTransaction']["currency_code"], $default_currency, $tc['StockTransaction']["received_date"]);
            $total_avg += $rate_val * ($tc[0]['sqp']);// / $tc[0]['sq']);
            $quantity += $tc[0]['sq'] ;
        }
        if ( !empty ( $total_avg ) ) {
            $avg = $total_avg / $quantity ;
        }



        $this->Product->save ( ['Product' => [
            'average_price' => $avg,
            'id'=>$product_id,
            'updated_price' => StockTransaction::CALC_METHOD_AVERAGE]]);
        return $avg ;
    }
    /**
     * Checks if the store have enough quantity stored before the operation is saved in the database.
     * =Should only be called if the disable overdraft option is checked.
     * @param int $product_id
     * @param int $store_id
     * @param int $new_quantity
     * @param int $old_quantity
     * @param string $type
     * @return boolean
     */
    function check_balance_open ( $product_id ,$store_id , $new_quantity ,$old_quantity, $type='deduct'){
        $this->loadModel ( 'Product');
        $this->loadModel ( 'ItemPermission');
        $this->loadModel ( 'StoreStockBalance');
        $product = $this->Product->find ('first' ,['conditions' => ['Product.id' => $product_id,'track_stock = 1 /*OR track_stock is not null */' ]]);


        $bundle_type = settings::getValue(InventoryPlugin, 'bundle_type');
        $allow_negative_bundle_products = settings::getValue(InventoryPlugin, 'allow_negative_bundle_products');

        if ( empty ( $product ) || $product['Product']['type'] == Product::SERVICE_TYPE || $product['Product']['track_stock'] != 1) {
            return true ;
        }

        // If The Product Is Type Bundle and we add quantity to it that means we are substracting from the components so we also check for the components quantity
        if ($product['Product']['type'] == Product::BUNDLE_TYPE && in_array($type, ['add', self::ADD_FROM_DELETE]) && !$allow_negative_bundle_products) {
            // This is used to compare balance available for bundle products when type is pack, when the user is adding quantity of the bundle = deducting from the components, so we can't set $type to add here at all.
            $bundle_componenets_store_id_override = $store_id;
            if (!empty($product['Product']['raw_store_id'])) {
                $bundle_componenets_store_id_override = $product['Product']['raw_store_id'];
            }
            if ($type == self::ADD_FROM_DELETE) {
                $components_quantity_available = $this->checkBalanceOpenBundles($product_id, $bundle_componenets_store_id_override, $new_quantity, $old_quantity, 'add', $product['Product']['name'] . '#' . $product['Product']['product_code']);
            } else {
                $components_quantity_available = $this->checkBalanceOpenBundles($product_id, $bundle_componenets_store_id_override, $new_quantity, $old_quantity, 'deduct', $product['Product']['name'] . '#' . $product['Product']['product_code']);
            }
            if (!$components_quantity_available) {
                return false;
            }
        }

        if (($product['Product']['type'] == Product::BUNDLE_TYPE && Product::checkProductBundleTypeCompound($product)) && $type === 'deduct' && settings::getValue(InventoryPlugin, 'disable_overdraft')) {
            $bundle_componenets_store_id_override = $store_id;
            if (!empty($product['Product']['raw_store_id'])) {
                $bundle_componenets_store_id_override = $product['Product']['raw_store_id'];
            }
            $components_quantity_available = $this->checkBalanceOpenBundles($product_id, $bundle_componenets_store_id_override , $new_quantity ,$old_quantity, 'deduct', $product['Product']['name'] . '#' . $product['Product']['product_code']);
            if ($components_quantity_available) {
                return true;
            }
        }
        $balance = $this->getProductStockBalance($product_id, $store_id);
        $quantity = 0 ;
        if ( $type == 'deduct' ) {
            $quantity = ( (float)$new_quantity - (float)$old_quantity ) * -1 ;
        }else if (in_array($type, ['add', self::ADD_FROM_DELETE])) {
            $quantity = ( (float)$new_quantity - (float)$old_quantity )  ;
        }
        return (($quantity + $balance) >= -0.0000001);
    }

    function getProductStockBalance($product_id, $store_id) {
        $showByOnHandAndAvailable = settings::getValue(InventoryPlugin, SettingsUtil::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS);
        $checkAvailableQtyInstead = settings::getValue(InventoryPlugin, SettingsUtil::VALIDATE_STOCK_OF_PRODUCTS_BY_AVAILABLE);
        $productStock[$store_id] = 0;
        $staff_id = getAuthOwner('staff_id') == -1 ? 0 : getAuthOwner('staff_id');
        if ($showByOnHandAndAvailable && $checkAvailableQtyInstead) {
            $invoiceItemService = new \App\vendors\Services\InvoiceItemService();
            $productStock = $invoiceItemService->getAvailableProductStock(ItemPermission::PERMISSION_VIEW, $product_id, $staff_id, $store_id, true);
        } else {
            $ws = new WarehouseService();
            $productStock = $ws->getStaffProductStock(ItemPermission::PERMISSION_VIEW, $product_id, $staff_id, $store_id);
        }
        $balance = array_sum($productStock);
        return $balance;
    }

    /**
     * Checks the balance for the bundle's components
     */
    function checkBalanceOpenBundles($product_id ,$store_id , $new_quantity ,$old_quantity, $type='deduct', $bundle_name_code = ''){
        $Product_bundles = GetObjectOrLoadModel('ProductBundle');
        $bundled_products = $Product_bundles->find('all' , ['conditions' => ['bundle_product_id'=> $product_id] ]);
        foreach($bundled_products as $p){
            if(!$this->check_balance_open(
                $p['ProductBundle']['product_id'],
                $store_id,
                $new_quantity*$p['ProductBundle']['quantity'],
                $old_quantity*$p['ProductBundle']['quantity'],
                $type
            )){
                if (!IS_REST) {
                    $product = GetObjectOrLoadModel('Product')->findById($p['ProductBundle']['product_id']);
                    $product_name = $product['Product']['name'];
                    $product_code = $product['Product']['product_code'];
                    CustomValidationFlash([sprintf(__("Amount not sufficient for (%s#%s) of the %s", true), $product_name, $product_code, $bundle_name_code)]);
                }
                return false;
            }
        }
        return true;
    }

    /**
     * get bundle items for a given product
     * @param int $product_id
     */
    function getBundleItems($product_id){
        $Product_bundles = GetObjectOrLoadModel('ProductBundle');
        $bundled_products = $Product_bundles->find('all' , ['conditions' => ['bundle_product_id'=> $product_id] ]);
        return $bundled_products;
    }
    /**
     * Calculates average price on the fly to save it in the product
     * function to be called in the saveTransaction, removeTransaction functions to calculate the average price on the flu
     * =New Average calculation
     * @param Array $data StockTransaction data -- should contain subscription_id,client_id in case of Refund Receipt , Credit Note
     * @param int $op   should be either 1 or -1 , 1 indicating that this is an addition to the store, -1 indicates that this is a subtraction/deduction from the store
     * @param int $after_sb optional parameter indicating a virtual store balance to used in calculation instead of the current store balance.
     * @return type
     */
    function add_to_average($data, $op = 1, $after_sb = null, $update_transaction = false, $old_average = null)
    {
        \App\Services\QueueRunner::checkMemory(5000);
        $Product = ClassRegistry::init('Product');
        $product_id = $data['product_id'];
        if (empty ($product_id)) {
            return false;
        }

        $quantity = $data['quantity'] * $op;
        $price = $data['price'] - $data['discount'];
        $currency_code = $data['currency_code'] ?? $this->get_default_currency();
        $product = $Product->find('first', ['fields' => "id,buy_price,average_price,stock_balance,type", 'conditions' => ['Product.id' => $product_id], 'recursive' => -1, 'applyBranchFind' => false]);
        $average_price = $product['Product']['average_price'];
        $buy_price = $product['Product']['buy_price'];
        $default_currency = $this->get_default_currency();
        $rate_val = CurrencyConverter::index($currency_code, $default_currency, $data['received_date']);
        $originalCost = 0;
        // Handle refund/credit transactions with original cost
        if ($this->isRefundCreditTransaction($data)) {
            $originalCost = $this->getOriginalCostPriceForRefund($data);
            if ($originalCost > 0) {
                $price = $originalCost;
                $this->log("Using original cost for refund/credit - Product: {$product_id}, Original Cost: {$originalCost}", LOG_DEBUG);
            }
        }

        // Here this code block will replace the average price with the "old average price" this is used to put the correct average price for the transaction that it's date was changed.
        if (!is_null($old_average)) {
            $average_price = $old_average;
            if ($average_price == 0 && $product['Product']['type'] == Product::BUNDLE_TYPE) {
                $average_price = $Product->get_bundle_final_cost($product['Product']['id']);
            }
        } else if (empty ($average_price) && $average_price != 0) {
            if ($product['Product']['type'] == Product::BUNDLE_TYPE)
                $product['Product']['buy_price'] = $Product->get_bundle_final_cost($product['Product']['id']);
            $average_price = $product['Product']['buy_price'];
            if (empty ($average_price)) {
                $average_price = 0;
            }
        }

        //using is_null instead of empty because $after_sb can contain a legit zero
        if (is_null($after_sb)) {
            $stock_balance = $product['Product']['stock_balance'];
        } else {
            $stock_balance = $after_sb;
        }
        // Updated logic: Refund/Credit transactions are now treated as inbound (affecting average)
        if ($data['quantity'] < 0 && !in_array($data['source_type'], [StockTransaction::SOURCE_PO, StockTransaction::SOURCE_RQ_MANUAL_INBOUND]) && !$this->isRefundCreditTransaction($data)) {
            if ($product['Product']['type'] == Product::BUNDLE_TYPE) {
                $product['Product']['buy_price'] = $Product->get_bundle_final_cost($product['Product']['id']);
            }
            $avg_p = (empty ($average_price) && $average_price != 0) ? $product['Product']['buy_price'] : $average_price;
            if (!empty($avg_p) || $avg_p === 0 || $avg_p === "0") {
                $saved_data = ['purchase_price' => $avg_p, 'currency_rate' => $rate_val];
                if (in_array($data['source_type'], self::getTransactionNotAffectAvgSource())) {
                    $saved_data['price'] = $avg_p;
                }
                $this->updateAll($saved_data, ['StockTransaction.id' => $data['id']]);
            }
            // These transcations price need to be updated as their source was always the aveage price that might been outdated/wrong
            if (in_array($data['source_type'], [StockTransaction::SOURCE_RQ_MANUAL_OUTBOUND, StockTransaction::SOURCE_MANUAL, StockTransaction::SOURCE_BUNDLE])) {
                $saved_data = ['purchase_price' => $avg_p, 'currency_rate' => $rate_val, 'price' => $avg_p];
                $this->updateAll($saved_data, ['StockTransaction.id' => $data['id']]);
            }
            if (abs($data ['purchase_price'] - $avg_p) > 0.000000000001 && in_array($data ['source_type'], [self::SOURCE_INVOICE, self::SOURCE_CN, self::SOURCE_RR])) {
                $this->average_price_change(['StockTransaction' => $data], $avg_p);
            }
            if (in_array($data['source_type'], StockTransaction::getSourceRequisition()) && abs($data ['purchase_price'] - $avg_p) > 0.000000000001) {
                $this->loadModel('Requisition');
                $this->Requisition->updateCostJournals($data, $avg_p);
            }
            return $stock_balance + $quantity;
        }

        //Refund Receipt and Credit Note average price.
        // Credit Note Buy Price Override
        if (($average_price == null || $average_price === false) && in_array($data['source_type'], [StockTransaction::SOURCE_RQ_INVOICE_CREDIT_NOTE, StockTransaction::SOURCE_CN])) {
            $average_price = $buy_price ?: 0;
        }

        if(in_array($data['source_type'], [self::SOURCE_RQ_PURCHASE_ORDER]) && $default_currency != $data['currency_code']) {
            $ED = GetObjectOrLoadModel('ExpenseDistribution');
            $expenseDistribution = $ED->find('first', ['conditions' => ['ExpenseDistribution.requisition_id' => $data['order_id']]]);
            if($expenseDistribution['ExpenseDistribution']['requisition_rate']) {
                $rate_val = $expenseDistribution['ExpenseDistribution']['requisition_rate'];
            }
        }

        if (in_array($data['source_type'], self::getTransactionNotAffectAvgSourceButAreProtectedFromUpdate())) {
            $currency_code = $default_currency;
            $price = $average_price;
            $rate_val = 1;
        }

        if (in_array($data['source_type'], self::getTransactionNotAffectAvgSource())) {
            $price = $average_price;
        }

        if ($stock_balance < 0 && $quantity > 0 && (in_array($data['source_type'], self::getSourceTypeAffectAvgPrice()))) {
            $new_average = $price * $rate_val;
        } else if($stock_balance < 0 ) {
            //last valid average price
            $lastAvgPriceTransaction = $this->find('first', ['conditions' => [
                'StockTransaction.product_id' => $product['Product']['id'],
                'StockTransaction.status' => self::STATUS_PROCESSED,
                '(ignored = 0 OR ignored IS NULL)',
                'StockTransaction.source_type' => self::getSourceTypeAffectAvgPrice(),
                'OR' => [
                    'StockTransaction.received_date <'=> $data['received_date'],
                    'AND' => [
                        'StockTransaction.received_date = ' => $data['received_date'],
                        'StockTransaction.id < ' => $data['id']
                    ]
                ]
            ],
                'order' => 'StockTransaction.received_date DESC , StockTransaction.id DESC'
            ]);
            if($lastAvgPriceTransaction) {
                $new_average = $lastAvgPriceTransaction['StockTransaction']['purchase_price'];
            } else {
                $new_average = $product['Product']['average_price'];
            }
        } else {
            // Average Price Formula
            if($data['ignored']) {
                $quantity = 0;
                $price = 0;
            }
            $new_average = $stock_balance + $quantity != 0 ? ((($average_price * $stock_balance) + ($quantity * $price * $rate_val)) / ($stock_balance + $quantity)) : false;
        }

        //To not give database error
        if (empty ($new_average)) {
            $new_average = 0;
        }

        if ($update_transaction && !empty ($data['id'])) {
            $saved_data = ['purchase_price' => $new_average, 'currency_rate' => $rate_val];
            //For outbound requsitions only ... quantity < 0
            if (in_array($data['source_type'], StockTransaction::getSourceRequisition()) && $data['quantity'] < 0 && abs($data['purchase_price'] - $new_average) > 0.0001) {
                $this->loadModel('Requisition');
                $this->Requisition->updateCostJournals($data, $avg_p);
                $saved_data['price'] = $avg_p;
            } else if (in_array($data['source_type'], self::getTransactionNotAffectAvgSource())) {
                $saved_data['price'] = $average_price;
                if (in_array($data['source_type'], self::getSourceRequisition())) {
                    $this->loadModel('Requisition');
                    $this->Requisition->updateCostJournals($data, $average_price);
                } else if ($data['source_type'] == StockTransaction::SOURCE_TRANSFER) {
                    $this->loadModel('StockTransfer');
                    $this->StockTransfer->updateCostJournals($data, $average_price);
                }
            } else if($this->isRefundCreditTransaction($data)) {
                if($originalCost > 0) {
                    $cost = $originalCost;
                } else {
                    $cost = $average_price;
                }
                $this->loadModel('Requisition');
                if(in_array($data['source_type'], self::getSourceRequisition())) {
                    $this->Requisition->updateCostJournals($data, $cost);
                    $saved_data['price'] = $cost;
                }
            }
            $this->updateAll($saved_data, ['StockTransaction.id' => $data['id']]);
        }

        if ($data ['purchase_price'] != $new_average) {
            $this->average_price_change(['StockTransaction' => $data], $new_average);
        }

        $Product->id = $product_id;
        $Product->saveField('average_price', $new_average);
        return ($stock_balance + $quantity);
    }

    /**
     * @param null $product_id product id to update its average price
     * @param null $transaction_after_id transaction id to update it's average price and all transaction after it
     * @param null $old_received_date transaction date to update its average price and all transactions after it
     * @return bool|void
     * first we get the first transaction before the transaction that have been changed
     * then we get the first transaction average price and use it to update all the transactions
     * after it
     */
    function average_on_all($product_id = null , $transaction_after_id = null ,$old_received_date = null ) {
        if (empty($product_id) || empty($transaction_after_id)) {
            return;
        }
        $average_price_disabled = settings::getValue(0, 'disable_average_price_calculation');
        if(isset($_GET['average_on_all_override']) || $average_price_disabled || Configure::read('ohshit')){
            return;
        }
        set_time_limit(7200);
        $Product = ClassRegistry::init('Product');
        $Invoice = ClassRegistry::init('Invoice');
        if ( is_array ( $transaction_after_id) ) {
            $transaction_after = $transaction_after_id;
            $transaction_after_id = $transaction_after['StockTransaction']['id'];
        }else {
            $transaction_after = $this->findById ($transaction_after_id);
        }
        $transaction_conditions = [] ;
        $conditions = [] ;
        if ( !empty ( $product_id)){
            $conditions['Product.id'] = $product_id ;
            $transaction_conditions['StockTransaction.product_id'] = $product_id ;
        }
        $old_average = null ;
        $applyNewWayForCommunication = self::isApplyNewWayForCommunication();
        $should_run_background = $this->shouldRunBackground($product_id, $transaction_after, $old_received_date);
        if (!isRunningInQueue() && $applyNewWayForCommunication && $should_run_background) {
            return $this->dispatchAveragePriceJob($transaction_after, $old_received_date);
        }
        if ( !empty ( $old_received_date ) && strtotime($old_received_date)< strtotime($transaction_after['StockTransaction']['received_date'] ) )
        {
            $received_date = $old_received_date;
        }else {
            $received_date = $transaction_after['StockTransaction']['received_date'];
        }
        //END PREROSESSING STEP
        if ( !empty ( $transaction_after_id )&&!empty($product_id ) ) {
            //this conditions to get this transaction and all transactions after it and adjust their purchase price
            $transaction_conditions['OR'] = [
                'received_date > '=> $received_date,
                'AND' => [
                    'received_date = ' => $received_date,
                    'id >= ' => $transaction_after_id
                ]
            ];
            $this->loadModel('Store');
            $inActiveStoreIdList = $this->Store->getInActiveStoreIds();
            //this condition to get the first transaction before this transaction to get it's average price
            $before_options =  ['recursive' => -1 , 'order' => 'StockTransaction.received_date DESC , id DESC',
                'conditions' => [
                    'NOT' => ['status' => [self::STATUS_DRAFT, self::STATUS_PENDING], 'StockTransaction.store_id' => $inActiveStoreIdList] ,
                    '(ignored = 0 OR ignored IS NULL)',
                    'product_id'=>$product_id,
                    'OR' => [
                        'received_date < '=> $received_date,
                        'AND' => [
                            'received_date = ' => $received_date,
                            'id < ' => $transaction_after_id
                        ]
                    ]]];
            // TODO: This part will try to find the transaction right before our current transaction for the same product to get it's average price otherwise it will set it to average price of the product or zero
            $before_transaction = $this->find ( 'first' ,$before_options );
            if (empty($before_transaction))
            {
                $transaction_source = $this->find('first', ['fields' => "id,price,discount,source_type", 'conditions' => ['StockTransaction.id' => $transaction_after_id], 'recursive' => -1])['StockTransaction']['source_type'];
                $product_buy_price = $Product->find('first', ['fields' => "id,average_price,buy_price", 'conditions' => ['Product.id' => $product_id], 'recursive' => -1])['Product']['buy_price'] ?? 0;
                $old_average = in_array($transaction_source, [StockTransaction::SOURCE_RQ_STOCKTAKING_IN]) ? $product_buy_price : 0;
            } else {
                $old_average = $before_transaction['StockTransaction']['purchase_price'];
            }
        }
        $conditions['track_stock'] = 1 ;

        $all_products = $Product->find ( 'all' , ['recursive' => -1 , 'conditions' => $conditions, 'applyBranchFind' => false]);//get the transaction product to update it's average price
        foreach ( $all_products as $p ) {
            if(empty($transaction_after_id)){
                $this->updateAll(['price' => $p['Product']['buy_price']] , ['StockTransaction.source_type'=> StockTransaction::SOURCE_MANUAL ,'price' => 0 ,  'product_id' => $p['Product']['id']]);
            }
            //get the transactions to update its average price
            $transactions = $this->find('all', ['order' => ['received_date' => 'ASC', 'id' => 'ASC'], 'recursive' => -1, 'conditions' => array_merge($transaction_conditions, ['status' => StockTransaction::STATUS_PROCESSED, 'product_id' => $p['Product']['id']])]);
            $current_quantity = 0 ;
            if ( !empty ( $transaction_after_id ) ){
                $s = $this->find ('first',  array_merge (['fields' => 'sum(quantity) as previous_quantity'], $before_options )) ;
                $current_quantity = (!empty ($s[0]['previous_quantity'])?$s[0]['previous_quantity'] : 0);
            }
            $i = 0 ;
            $transactionsCount= count($transactions);
            foreach ($transactions as $k => $t) {
                /**
                 * in first transaction we don't want to use product average price to calculate transaction price because
                 * because this is the transaction that have been changed so it made the current product average price incorrect
                 * but after the first call to add average the product average price is corrected and now we can use it
                 * as a base to calculate the next transactions price
                 */
                $useProductAveragePriceToUpdateTransactionPrice = (($i > 0) ? null : $old_average);
                $current_quantity = $this->add_to_average($t['StockTransaction'], 1, $current_quantity, true, $useProductAveragePriceToUpdateTransactionPrice);
                if ($t['StockTransaction']['quantity'] > 0) {
                    $i++;
                }
            }
            if (empty ($transactions) && !empty($product_id)) {
                $Product->updateAll(['average_price' => $old_average], ['Product.id' => $product_id]);
            }
        }
    }



    public function average_price_change ( $transaction ,  $new ){
        if(in_array($transaction['StockTransaction']['source_type'], [self::SOURCE_INVOICE, self::SOURCE_RR, self::SOURCE_CN])) {
            $this->loadModel('Invoice');
            debug('Update J COST '.$transaction['StockTransaction']['order_id']);
            $this->Invoice->update_cost_journals ( $transaction['StockTransaction']['order_id']);
            $Transaction = new self();
            $action_line_data = ['primary_id' => $transaction['StockTransaction']['order_id'], 'secondary_id' => $transaction['StockTransaction']['product_id'], 'param1' => $transaction['StockTransaction']['id'], 'param2' => $transaction['StockTransaction']['id'], 'param3' => $transaction['StockTransaction']['purchase_price'], 'param4' => $new,'param5' => format_datetime($transaction['StockTransaction']['received_date'])];
            $Transaction->add_actionline(ACTION_UPDATED_AVG_PRICE, $action_line_data);
        }

        // Handle requisition-based refund/credit average price changes
//        if(in_array($transaction['StockTransaction']['source_type'], [self::SOURCE_RQ_INVOICE_REFUND, self::SOURCE_RQ_INVOICE_CREDIT_NOTE])) {
//            $this->loadModel('Requisition');
//            $this->Requisition->updateCostJournals($transaction['StockTransaction'], $new);
//            $Transaction = new self();
//            $action_line_data = ['primary_id' => $transaction['StockTransaction']['order_id'], 'secondary_id' => $transaction['StockTransaction']['product_id'], 'param1' => $transaction['StockTransaction']['id'], 'param2' => $transaction['StockTransaction']['id'], 'param3' => $transaction['StockTransaction']['purchase_price'], 'param4' => $new,'param5' => format_datetime($transaction['StockTransaction']['received_date'])];
//            $Transaction->add_actionline(ACTION_UPDATED_AVG_PRICE, $action_line_data);
//        }
    }





    public function transaction_date_change ( $transaction ,  $old_received_date ){
        $Transaction = new self();
        $action_line_data = ['primary_id' => $transaction['StockTransaction']['order_id'], 'secondary_id' => $transaction['StockTransaction']['product_id'], 'param1' => $transaction['StockTransaction']['id'], 'param2' => $transaction['StockTransaction']['id'], 'param3' => $transaction['StockTransaction']['purchase_price'], 'param4' => $old_received_date,'param5' => format_datetime($transaction['StockTransaction']['received_date'])];
        $Transaction->add_actionline(ACTION_UPDATED_TRANSACTION_DATE, $action_line_data);
    }

    public function update_bundle_journal($bundleProductTransaction) {
        $this->loadModel('Product');
        $this->loadModel('Journal');
        $product = $this->Product->findById($bundleProductTransaction['StockTransaction']['product_id']);
        $defaultCurrency = $this->get_default_currency();
        $bundleOutboundJournal = false;
        if(
            $product['Product']['type'] == Product::BUNDLE_TYPE &&
            Product::checkProductBundleTypePack($product) &&
            $bundleProductTransaction[$this->alias]['quantity'] > 0
        ) {
            $bundleOutboundJournal = [];
            $bundleOutboundJournal['Journal']['date']=$bundleProductTransaction[$this->alias]['received_date'];
            $bundleOutboundJournal['Journal']['entity_type']='pack_bundle_outbound_stock_transaction';
            $bundleOutboundJournal['Journal']['description']=sprintf(__("Raw Materials Outbound for product %s #%s, Stock Transaction #%s",true), $product['Product']['name'], $product['Product']['product_code'], $bundleProductTransaction[$this->alias]['id']);
            $bundleOutboundJournal['Journal']['entity_id']=$bundleProductTransaction[$this->alias]['id'];
            $bundleOutboundJournal['Journal']['currency_code']= $defaultCurrency;
            /**
             * get transactions for compound transactions
             */
            $bundleTransactions= $this->find('all', ['conditions' => ['StockTransaction.source_type' => self::SOURCE_BUNDLE, 'StockTransaction.order_id' => $bundleProductTransaction[$this->alias]['id']]]);
            $total = 0;
            if($bundleTransactions) {
                foreach ($bundleTransactions as $bundleTransaction) {
                    if($bundleTransaction['Product']['type'] == Product::SERVICE_TYPE){
                        $total += $bundleTransaction['Product']['buy_price'] * abs($bundleTransaction['StockTransaction']['quantity']);
                    } else {
                        $total += $bundleTransaction['StockTransaction']['price'] * abs($bundleTransaction['StockTransaction']['quantity']);
                    }
                }
                $bundleOutboundJournal['JournalTransaction'][] = [
                    'subkey'=>"store",
                    'currency_credit' => $total,
                    'currency_code' => $defaultCurrency,
                    'description'=>"",
                    'auto_account' => ['type'=>'dynamic' , 'entity_type'=> 'store','entity_id'=> !empty($product['Product']['raw_store_id']) ? $product['Product']['raw_store_id'] : $bundleProductTransaction[$this->alias]['store_id']]
                ];
                $bundleOutboundJournal['JournalTransaction'][] = [
                    'subkey'=>"account",
                    'currency_debit' => $total,
                    'currency_code' => $defaultCurrency,
                    'description'=>"",
                    'auto_account' => ['type'=>'fixed' , 'entity_type'=> 'other_credit', 'entity_id'=> 0]
                ];
            }
        }
        $this->Journal->update_auto_journal($bundleOutboundJournal, ifPluginActive(AccountingPlugin), ifPluginActive(AccountingPlugin));
    }


    public function get_journals($data)
    {
        if(empty($data[$this->alias]['id'])) $data[$this->alias]['id']= $this->id;

        if(!isset($data[$this->alias]['quantity']))
            return false;


        if(!empty($data[$this->alias]['id'])&&!isset($data[$this->alias]['source_type']))
            $data=$this->findById($data[$this->alias]['id']);

        if($data[$this->alias]['source_type']!=self::SOURCE_MANUAL) {
            return false;
        }
        if($data[$this->alias]['quantity']>0)
        {

            $credit='credit';
            $debit='debit';
            $description=__('Add',true);
            $journal_account='other_credit';
        }
        else
        {
            $credit='debit';
            $debit='credit';
            $description=__('Deduct',true);
            $journal_account='other_debit';
        }

        $description.=' '.abs($data[$this->alias]['quantity']). ' '.__('Units',true).' - '.__('Product',true).' '.'#'.$data[$this->alias]['product_id'].' - '.__('Transaction',true).' '.'#'.$data[$this->alias]['id'];

        if(empty($data[$this->alias]['store_id']))
        {
            $this->loadModel('Store');
            $data[$this->alias]['store_id']= $this->Store->getPrimaryStore();
        }

        if(empty($data[$this->alias]['currency_code']))
        {
            $data[$this->alias]['currency_code']= $this->get_default_currency();
        }
        $currency_code=$data[$this->alias]['currency_code'];
        $amount= abs($data[$this->alias]['price'] * $data[$this->alias]['quantity']);

        $journal['Journal']['date']=$data[$this->alias]['received_date'];
        $journal['Journal']['entity_type']='stock_transaction';
        $journal['Journal']['description']=$description."\r\n".$data[$this->alias]['notes'];
        $journal['Journal']['entity_id']=$data[$this->alias]['id'];
        $journal['Journal']['currency_code']= $currency_code;


        //Delete The Old Journal in Case:
        if(empty($amount)||$amount=="0") return $journal;
        if(isset($data[$this->alias]['status'])&&$data[$this->alias]['status']!=self::STATUS_PROCESSED) return $journal;
        if(!empty($data[$this->alias]['ignored'])) return $journal;



        $journal['JournalTransaction'][0]=
            [
                'subkey'=>'store',
                'currency_'.$debit=>$amount,
                'currency_code'=>$currency_code,
                'description'=>$description,
                'auto_account'=> ['type'=>'dynamic' , 'entity_type'=> 'store','entity_id'=>$data[$this->alias]['store_id']]
            ];
        $journal['JournalTransaction'][1]=
            [
                'subkey'=>'account',
                'currency_'.$credit=>$amount,
                'currency_code'=>$currency_code,
                'description'=>$description,
            ];

        if(!empty($data[$this->alias]['journal_account_id']))
        {
            $journal['JournalTransaction'][1]['journal_account_id']=$data[$this->alias]['journal_account_id'];
        }
        else
        {
            $journal['JournalTransaction'][1]['auto_account']= ['type'=>'fixed' , 'entity_type'=> $journal_account,'entity_id'=>0];
        }
        return $journal;

    }
    /**
     *
     * @param mixed $bundle essentially a StockTransaction array coming from
     */
    static function updateForBundles ($bundle){
        $Transaction = new self();
        $Product = ClassRegistry::init('Product');
        $Product_bundles = ClassRegistry::init('ProductBundle');
        $bundle_product = $Product->find('first' , ['recursive' => -1 ,  'conditions' => ['Product.id' => $bundle['product_id']] ]) ;
        $bundled_products = $Product_bundles->find('all' , ['conditions' => ['bundle_product_id'=> $bundle['product_id']] ]);

        $transactions = [ ] ;
        foreach ( $bundled_products as $p ) {
            $current_product = $Product->find ('first' , ['recursive' => -1, 'fields' => ['average_price']  ,'conditions' => ['Product.id' => $p['ProductBundle']['product_id']] ] );
            $quantity = $bundle['quantity'] * $p['ProductBundle']['quantity'] ;
            debug('AVG 12 '.$current_product['Product']['average_price']);
            $ttype=self::TRANSACTION_OUT;
            $q=$quantity;
            if($quantity<0)
            {
                $ttype=self::TRANSACTION_IN;
                $q=$q*-1;

            }
            $transactions[$p['ProductBundle']['product_id']] = [
                'received_date' => $Transaction->formatDateTime($bundle['received_date'] ),
                'order_id' => $bundle['id'],
                'ref_id' => $bundle['product_id'],
                'currency_code' => $bundle['currency_code'],
                'product_id' => $p['ProductBundle']['product_id'],
                'source_type' => self::SOURCE_BUNDLE,
                'transaction_type' => $ttype,
                'status' => self::STATUS_PROCESSED,
                'quantity' => $q,
                'price' => $current_product['Product']['average_price'],
                'total_price' => $quantity * $current_product['Product']['average_price'],
                'purchase_price' => $current_product['Product']['average_price'],
                'unit_name' => $p['ProductBundle']['unit_name'],
                'unit_small_name' => $p['ProductBundle']['unit_small_name'],
                'unit_factor' => $p['ProductBundle']['unit_factor'],
                'unit_factor_id' => $p['ProductBundle']['unit_factor_id'],
            ];
            if ( !empty ($bundle['added_by'])){
                $transactions[$p['ProductBundle']['product_id']]['added_by'] = $bundle['added_by'];
            }
        }
        $new_transactions= [];
        foreach ($transactions as $product_id => $transaction) {
            $transaction['store_id'] = !empty($bundle_product['Product']['raw_store_id']) ? $bundle_product['Product']['raw_store_id'] : $bundle['store_id'];
            $action_line_data = ['param3' => $invoice['Invoice']['no'], 'param4' => $transaction['Product']['product_code'], 'param5' => $transaction['Product']['name']];
            self::saveTransaction($transaction, $action_line_data);
            $new_transactions[]=$transaction;
        }

    }
    public static function adjustBundleBalance ( $product_id , $count , $stockarray ){
        $productM = ClassRegistry::init('Product');
        $product = $productM->find('first' , ['recursive' => -1 ,  'conditions' => ['Product.id' => $product_id] ]);
        if ( empty ( $product ) || $product['Product']['type'] != settings::OPTION_SOLD_TYPE_BUNDLES ){
            return false ;
        }else {
            $productBundleM = ClassRegistry::init('ProductBundle');
            $bundled_products = $productBundleM->find('all' , ['recursive' => -1 , 'conditions' => ['bundle_product_id' =>$product_id ] ]);
            $transactions = [] ;
            $bundleded_products_productIds = [];
            foreach ($bundled_products as $element) {
                $bundleded_products_productIds[] = $element['ProductBundle']['product_id'];
            }
            $imploded_bundleded_products_productIds = implode(',', $bundleded_products_productIds);
            $product_average_prices = GetObjectOrLoadModel('Product')->query("SELECT id, average_price FROM products WHERE id in ($imploded_bundleded_products_productIds)", false);
            $bundleded_products_average_prices = [];
            foreach ($product_average_prices as $product_average_price) {
                $bundleded_products_average_prices[$product_average_price['products']['id']] = $product_average_price['products']['average_price'];
            }
            $storeId = !empty($product['Product']['raw_store_id']) ? $product['Product']['raw_store_id'] : $stockarray['StockTransaction']['store_id'];
            foreach ( $bundled_products as $p ) {
                $quantity = $count * $p['ProductBundle']['quantity'];

                /**
                 * this section handles when user enters negative quantity in bundle product
                 * so that when the user create a new invoice the stock transaction will be in
                 * instead of out as instructed by Muhammed azzam
                 */
                $ttype=self::TRANSACTION_OUT;
                $q=$quantity;
                if($quantity<0)
                {
                    $ttype=self::TRANSACTION_IN;
                    $q=$q*-1;
                }

                $transactions[$p['ProductBundle']['product_id']] = [
                    'received_date' => $stockarray['StockTransaction']['received_date'],
                    'order_id' => $stockarray['StockTransaction']['id'],
                    'ref_id' => $product['Product']['id'],
                    'currency_code' => $stockarray['StockTransaction']['currency_code'],
                    'product_id' => $p['ProductBundle']['product_id'],
                    'source_type' => self::SOURCE_BUNDLE,
                    'transaction_type' => $ttype,
                    'status' => $stockarray['StockTransaction']['status'],
                    'quantity' => $q,
                    'store_id' => $storeId,
                    'price' => $bundleded_products_average_prices[$p['ProductBundle']['product_id']],
                    'total_price' => $quantity * $bundleded_products_average_prices[$p['ProductBundle']['product_id']],
                    'purchase_price' => $bundleded_products_average_prices[$p['ProductBundle']['product_id']],
                    'unit_name' => $p['ProductBundle']['unit_name'],
                    'unit_small_name' => $p['ProductBundle']['unit_small_name'],
                    'unit_factor' => $p['ProductBundle']['unit_factor'],
                    'unit_factor_id' => $p['ProductBundle']['unit_factor_id'],
                ];
                if ( !empty ($stockarray['StockTransaction']['added_by'])){
                    $transactions[$p['ProductBundle']['product_id']]['added_by'] = ($stockarray['StockTransaction']['added_by']?:getAuthOwner('staff_id'));
                }
            }
            foreach ($transactions as $transaction) {
                self::saveTransaction($transaction, NULL);
            }
            debug('x1');
            $final_cost=$productM->get_bundle_final_cost($stockarray['StockTransaction']['product_id']);
            if (!empty($stockarray['StockTransaction']['received_date'])) {
                $transactionDateTime = new DateTime($stockarray['StockTransaction']['received_date']);
                $transactionDateTime->modify("-1 second");
                $stockarray['StockTransaction']['received_date'] = $transactionDateTime->format('Y-m-d H:i:s');
            }
            $StockTransaction = GetObjectOrLoadModel("StockTransaction");
            $sources = $StockTransaction->getSources();
            $orderNo = self::getOrderNo($stockarray);
            $notes = "Add Bundle Quantity For ". $sources[$stockarray['StockTransaction']['source_type']] . " #{$orderNo}";
            $adjusting_transaction = [
                'notes' => $notes,
                'received_date' => $stockarray['StockTransaction']['received_date'],
                'order_id' => $stockarray['StockTransaction']['id'],
                'ref_id' => ($stockarray['StockTransaction']['ref_id']?:0),
                'currency_code' => getCurrentSite('currency_code'),
                'product_id' => $stockarray['StockTransaction']['product_id'],
                'source_type' => self::SOURCE_BUNDLE_RECON,
                'transaction_type' => self::TRANSACTION_IN,
                'status' => $stockarray['StockTransaction']['status'],
                'quantity' => $count,
                'store_id' => $stockarray['StockTransaction']['store_id'],
                'price' => $final_cost ,
                'total_price' => $stockarray['StockTransaction']['total_price'],
                'purchase_price' => $final_cost,
                'unit_name' => $stockarray['StockTransaction']['unit_name'],
                'unit_small_name' => $stockarray['StockTransaction']['unit_small_name'],
                'unit_factor' => $stockarray['StockTransaction']['unit_factor'],
                'unit_factor_id' => $stockarray['StockTransaction']['unit_factor_id'],
            ];
            $adjusting_transaction['added_by'] = ($stockarray['StockTransaction']['added_by']?:getAuthOwner('staff_id'));
            self::saveTransaction($adjusting_transaction, NULL , false ,true );
        }
    }

    public static function updateForRequisition($requisition) {
        StockTransactionUpdater::update(self::getStockTransactionType($requisition['Requisition']['order_type']), $requisition);
    }

    /**
     * Removes the related transaction for the bundle
     * @param mixed $transaction the transaction that needs to be deleted
     */
    public static function removeBundleTransaction($transaction ){
        $transactionModel = GetObjectOrLoadModel('StockTransaction') ;
        if ( is_numeric ($transaction))
        {
            $transaction = $transactionModel->find('all' , ['conditions' => ['StockTransaction.id' => $transaction] ]) ;
        }
        $bundle_transactions = $transactionModel->find('all' , ['recursive' => -1 , 'conditions' =>['order_id' => $transaction['StockTransaction']['id'] , 'ref_id' => $transaction['StockTransaction']['product_id'] , 'source_type' => StockTransaction::SOURCE_BUNDLE] ]);
        foreach ( $bundle_transactions as $t ) {
            self::removeTransaction($t['StockTransaction']);
        }
        debug('x2');
        $adjusting_transactions = $transactionModel->find('all' , ['recursive' => -1 , 'conditions' =>['order_id' => $transaction['StockTransaction']['id'] , 'ref_id' => $transaction['StockTransaction']['ref_id'] , 'source_type' => StockTransaction::SOURCE_BUNDLE_RECON] ]);
        //print_r($adjusting_transactions);
        //die();
        foreach ( $adjusting_transactions as $t ) {
            self::removeTransaction($t['StockTransaction']);
        }
    }

    static function getTransactionLink($transaction)
    {
        $orderId = $transaction['order_id'];
        $refId = $transaction['ref_id'];
        $id = $transaction['id'];
        $sourceType = $transaction['source_type'];

        if($sourceType==StockTransaction::SOURCE_PO)
            $url=Router::url(['controller'=>'purchase_orders','action'=>'view',$orderId]);
        elseif($sourceType==StockTransaction::SOURCE_PR)
            $url=Router::url(['controller'=>'purchase_orders','action'=>'view_refund',$orderId]);
        elseif($sourceType==StockTransaction::SOURCE_PDN)
            $url=Router::url(['controller'=>'purchase_invoices','action'=>'view_debit_note',$orderId]);

        elseif($sourceType==StockTransaction::SOURCE_INVOICE)
            $url=Router::url(['controller'=>'invoices','action'=>'view',$orderId]);
        elseif($sourceType==StockTransaction::SOURCE_RR)
            $url=Router::url(['controller'=>'invoices','action'=>'view_refund',$orderId]);
        elseif($sourceType==StockTransaction::SOURCE_CN)
            $url=Router::url(['controller'=>'invoices','action'=>'view_creditnote',$orderId]);
        elseif($sourceType==StockTransaction::SOURCE_BUNDLE)
            $url=Router::url(['controller'=>'products','action'=>'view',$refId]);
        elseif(in_array($sourceType,StockTransaction::getSourceRequisition()))
            $url=Router::url(['controller'=>'requisitions','action'=>'view',$orderId]);
        else
            $url=Router::url(['controller'=>'products','action'=>'edit_stock_transaction',$id]);

        return $url;
    }

    public function beforeSave($options = [])
    {
        $defaultCurrency = $this->get_default_currency();
        if(empty($this->data['StockTransaction']['currency_rate']) && $defaultCurrency != $this->data['StockTransaction']['currency_code'])
        {
            $transactionCurrencyCode = $this->data['StockTransaction']['currency_code'];
            $currencyRate = CurrencyConverter::index($transactionCurrencyCode, $defaultCurrency, date('Y-m-d', strtotime($this->data['StockTransaction']['received_date'])));
            if(in_array($this->data['StockTransaction']['source_type'], self::getSourceRequisition())) {
                $ED = GetObjectOrLoadModel('ExpenseDistribution');
                $expenseDistribution = $ED->find('first', ['conditions' => ['ExpenseDistribution.requisition_id' => $this->data['StockTransaction']['order_id']]]);
                if($expenseDistribution['ExpenseDistribution']['requisition_rate']) {
                    $currencyRate = $expenseDistribution['ExpenseDistribution']['requisition_rate'];
                }
            }
            debug($currencyRate);
            $this->data['StockTransaction']['currency_rate'] = $currencyRate;
        }else if($defaultCurrency == $this->data['StockTransaction']['currency_code'])
        {
            $this->data['StockTransaction']['currency_rate'] = 1;
        }
        return true;
    }

    /**
     * @param $requisition
     * @param $oldStockTransactionSourceType
     * delete old converted requisition stock transaction as
     * when we convert a manual inbound/outbound requsitions to purchase order/invoice
     * we create new stock transactions but can't delete the old ones as the stock transactions
     * source type have changed so we have to delete them here manually
     */
    public function deleteConvertedRequisitionOldStockTransactions($requisition, $oldStockTransactionSourceType){
        foreach ($requisition['RequisitionItem'] as  $requisitionItem) {
            $oldRequisitionTransactionConditions = [
                'StockTransaction.order_id' => $requisition['Requisition']['id'],
                'StockTransaction.product_id' => $requisitionItem['product_id'],
                'StockTransaction.source_type' => [$oldStockTransactionSourceType]
            ];
            $oldRequisitionTransaction = $this->find('first', ['conditions' => $oldRequisitionTransactionConditions] );
            if($oldRequisitionTransaction) {
                self::removeTransaction($oldRequisitionTransaction['StockTransaction'], ['param3' => $requisition['Requisition']['number']]);
            }
        }
    }

    public function dispatchAveragePriceJob($transaction_after, $oldReceivedDate = null) {
        $product_id = $transaction_after['StockTransaction']['product_id'];
        // want to check here with billal and jasdllah how to move it
        ###############################################resvist#########################
        init_queue_connection();
        $repo = new EventActionRepository(new EventAction());
        $eventAction = $repo->first([
            ['signature' , 'product-'.$product_id],
            ['site_id' , getCurrentSite('id')],
            ['status' , 'created']
        ]);
        if($eventAction) {
            $data = json_decode(json_encode($eventAction->data), true);
        }

        if(
            $eventAction &&
            $data['product_id'] == $product_id &&
            (empty($transaction_after['StockTransaction']['tracking_data']['serial']) || !isset($transaction_after['StockTransaction']['tracking_data']['serial']))
        ) {
            $shouldUpdate = false;
            $startTransaction = $data['start_transaction'];
            $newOldReceivedDate = $data['old_received_date'] ?? null;
            $startDate = $data['start_date'];
            if(
                ($transaction_after['StockTransaction']['received_date'] <= $data['start_transaction']['StockTransaction']['received_date'])
            ) {
                $startTransaction = $transaction_after;
                $startDate = $transaction_after['StockTransaction']['received_date'];
                $shouldUpdate = true;
            }
            if($oldReceivedDate && (($oldReceivedDate < $data['old_received_date']) || is_null($data['old_received_date']))) {
                $shouldUpdate = true;
                $newOldReceivedDate = $oldReceivedDate;

            }
            if($shouldUpdate) {
                unset($transaction_after['StockTransaction']['description']);
                unset($transaction_after['StockTransaction']['Product']);
                $value = json_encode(
                    [
                        'product_id' => $product_id,
                        'start_transaction' => $startTransaction,
                        'start_date' => $startDate,
                        'old_received_date' => $newOldReceivedDate
                    ]
                );
                if( $repo->update( ['id' => $eventAction->id], ['data' => $value])) {
                    dispatch_event_action(new AveragePriceChanged([
                        'product_id' => $product_id,
                        'old_received_date' => $newOldReceivedDate,
                        'start_transaction' => $startTransaction,
                        'start_date' => $startDate
                    ], "product-$product_id"));
                }

            }
        } else {
            dispatch_event_action(new AveragePriceChanged([
                'product_id' => $product_id,
                'old_received_date' => $oldReceivedDate,
                'start_transaction' => $transaction_after,
                'start_date' => $transaction_after['StockTransaction']['received_date']
            ], "product-$product_id"));
        }
        return;
    }

    /**
     * takes stock transaction and return order number
     */
    public static function getOrderNo($stockTransaction) {
        $Invoice = GetObjectOrLoadModel('Invoice');
        $Requisition = GetObjectOrLoadModel('Requisition');
        $PurchaseOrder = GetObjectOrLoadModel('PurchaseOrder');
        $orderId = $stockTransaction['StockTransaction']['order_id'];
        $sourceType = $stockTransaction['StockTransaction']['source_type'];
        if ($sourceType == StockTransaction::SOURCE_INVOICE)
            $orderNo = $Invoice->field('no', array('Invoice.id' => $orderId));
        else if ($sourceType == StockTransaction::SOURCE_PO)
            $orderNo = $PurchaseOrder->field('no', array('PurchaseOrder.id' => $orderId));
        else if ($sourceType == StockTransaction::SOURCE_RR)
            $orderNo = $Invoice->field('no', array('Invoice.id' => $orderId));
        else if ($sourceType == StockTransaction::SOURCE_CN)
            $orderNo = $Invoice->field('no', array('Invoice.id' => $orderId));
        else if (in_array($sourceType, StockTransaction::getSourceRequisition()))
            $orderNo = $Requisition->field('number', array('id' => $orderId));
        else
            $orderNo = $stockTransaction['StockTransaction']['id'];

        return $orderNo;
    }

    /**
     * Get original cost price for refund/credit note transactions
     * @param array $refundData - The refund/credit transaction data
     * @return float - Original cost price from the refunded transaction
     */
    public function getOriginalCostPriceForRefund($refundData) {
        if(!calculateRefundWithSellingPrice()) {
            return 0;
        }
        $originalCost = 0;
        $sourceType = $refundData['source_type'];

        try {
            if (in_array($sourceType, [self::SOURCE_RR, self::SOURCE_CN])) {
                // Direct Invoice Refunds - Use subscription_id approach
                $originalCost = $this->getOriginalCostFromInvoice($refundData);

            } elseif (in_array($sourceType, [self::SOURCE_RQ_INVOICE_REFUND, self::SOURCE_RQ_INVOICE_CREDIT_NOTE])) {
                // Requisition-based Refunds - Use requisition transactions
                $originalCost = $this->getOriginalCostFromRequisition($refundData);
            }

            // Log the retrieval for debugging
            $this->log("Original cost retrieval for refund - Product: {$refundData['product_id']}, Source: {$sourceType}, Cost: {$originalCost}", LOG_DEBUG);

        } catch (Exception $e) {
            $this->log("Error retrieving original cost for refund: " . $e->getMessage(), LOG_ERROR);
            // Fallback to current average price
            $Product = ClassRegistry::init('Product');
            $product = $Product->find('first', [
                'conditions' => ['Product.id' => $refundData['product_id']],
                'fields' => ['average_price'],
                'recursive' => -1
            ]);
            $originalCost = $product ? $product['Product']['average_price'] : 0;
        }

        return $originalCost;
    }

    /**
     * Get original cost from invoice-based refunds using subscription_id
     * @param array $refundData - The refund transaction data
     * @return float - Original cost from invoice stock transaction
     */
    private function getOriginalCostFromInvoice($refundData) {
        // Get refund invoice to find subscription_id
        $Invoice = ClassRegistry::init('Invoice');
        $refundInvoice = $Invoice->find('first', [
            'conditions' => ['Invoice.id' => $refundData['order_id']],
            'fields' => ['subscription_id'],
            'recursive' => -1
        ]);

        if (empty($refundInvoice['Invoice']['subscription_id'])) {
            $this->log("No subscription_id found for refund invoice: {$refundData['order_id']}", LOG_WARNING);
            return 0;
        }

        // Find original invoice (type = 0)
        $originalInvoice = $Invoice->find('first', [
            'conditions' => [
                'Invoice.id' => $refundInvoice['Invoice']['subscription_id'],
                'Invoice.type' => 0  // Original invoice type
            ],
            'fields' => ['id'],
            'recursive' => -1
        ]);

        if (empty($originalInvoice)) {
            $this->log("No original invoice found for subscription_id: {$refundInvoice['Invoice']['subscription_id']}", LOG_WARNING);
            return 0;
        }

        // Get stock transaction from original invoice
        $originalTransaction = $this->find('first', [
            'conditions' => [
                'StockTransaction.source_type' => self::SOURCE_INVOICE,
                'StockTransaction.order_id' => $originalInvoice['Invoice']['id'],
                'StockTransaction.product_id' => $refundData['product_id'],
                'StockTransaction.status' => self::STATUS_PROCESSED,
                '(ignored = 0 OR ignored IS NULL)'
            ],
            'fields' => ['purchase_price'],
            'recursive' => -1
        ]);

        if (empty($originalTransaction)) {
            $this->log("No original transaction found for invoice: {$originalInvoice['Invoice']['id']}, product: {$refundData['product_id']}", LOG_WARNING);
            return 0;
        }

        return $originalTransaction['StockTransaction']['purchase_price'];
    }

    /**
     * Get original cost from requisition-based refunds
     * @param array $refundData - The refund transaction data
     * @return float - Original cost from requisition stock transaction
     */
    private function getOriginalCostFromRequisition($refundData) {
        // Get refund requisition
        $Requisition = ClassRegistry::init('Requisition');
        $refundRequisition = $Requisition->find('first', [
            'conditions' => ['Requisition.id' => $refundData['order_id']],
            'fields' => ['order_id', 'order_type'],
            'recursive' => -1
        ]);

        if (empty($refundRequisition)) {
            debug("No refund requisition found: {$refundData['order_id']}", LOG_WARNING);
            return 0;
        }

        // Find original invoice from requisition's order_id
        $Invoice = ClassRegistry::init('Invoice');
        $refundInvoice = $Invoice->find('first', [
            'conditions' => [
                'Invoice.id' => $refundRequisition['Requisition']['order_id'],
                'Invoice.type' => [Invoice::Refund_Receipt, Invoice::Credit_Note]  // Original invoice type
            ],
            'fields' => ['id', 'subscription_id'],
            'recursive' => -1
        ]);

        if (empty($refundInvoice)) {
            debug("No original invoice found for requisition order_id: {$refundRequisition['Requisition']['order_id']}", LOG_WARNING);
            return 0;
        }

        // Find original requisition for this invoice
        $originalRequisition = $Requisition->find('first', [
            'conditions' => [
                'Requisition.order_id' => $refundInvoice['Invoice']['subscription_id'],
                'Requisition.order_type' => Requisition::ORDER_TYPE_INVOICE
            ],
            'fields' => ['id'],
            'recursive' => -1
        ]);

        // Initialize default values
        $stockTransactionSource = self::SOURCE_RQ_INVOICE;
        $stockTransactionOrderId = $originalRequisition['Requisition']['id'] ?? 0;
        $hasOriginalRequisition = !empty($originalRequisition);

// Handle case when no original requisition exists
        if (!$hasOriginalRequisition) {
            debug("No original requisition found for invoice: {$refundInvoice['Invoice']['id']}", LOG_WARNING);

            $originalInvoice = $Invoice->find('first', [
                'conditions' => ['Invoice.id' => $refundInvoice['Invoice']['subscription_id']],
                'fields' => ['id'],
                'recursive' => -1
            ]);

            $hasOriginalInvoice = !empty($originalInvoice);
            if (!$hasOriginalInvoice) {
                return 0;
            }

            // Update source and order ID for invoice fallback
            $stockTransactionSource = self::SOURCE_INVOICE;
            $stockTransactionOrderId = $originalInvoice['Invoice']['id'];
        }

// Get stock transaction from original requisition
        $originalTransaction = $this->find('first', [
            'conditions' => [
                'StockTransaction.source_type' => $stockTransactionSource,
                'StockTransaction.order_id' => $stockTransactionOrderId,
                'StockTransaction.product_id' => $refundData['product_id'],
                'StockTransaction.status' => self::STATUS_PROCESSED,
                '(ignored = 0 OR ignored IS NULL)'
            ],
            'fields' => ['purchase_price'],
            'recursive' => -1
        ]);

        if (empty($originalTransaction)) {
            debug("No original requisition transaction found for requisition: {$originalRequisition['Requisition']['id']}, product: {$refundData['product_id']}", LOG_WARNING);
            return 0;
        }

        return $originalTransaction['StockTransaction']['purchase_price'];
    }

    /**
     * Check if transaction is a refund/credit that should use original cost
     * @param array $transactionData
     * @return bool
     */
    public function isRefundCreditTransaction($transactionData) {
        if(!calculateRefundWithSellingPrice()) {
            return false;
        }
        return in_array($transactionData['source_type'], [
            self::SOURCE_RR,
            self::SOURCE_CN,
            self::SOURCE_RQ_INVOICE_REFUND,
            self::SOURCE_RQ_INVOICE_CREDIT_NOTE,
            self::SOURCE_RQ_POS_INBOUND
        ]);
    }

    /**
     * Validate original cost retrieval for refund/credit transactions
     * Useful for debugging and testing
     */
    public function validateRefundCostRetrieval($refundTransactionId) {
        $refundTransaction = $this->findById($refundTransactionId);
        if (!$refundTransaction || !$this->isRefundCreditTransaction($refundTransaction['StockTransaction'])) {
            return ['valid' => false, 'error' => 'Not a refund/credit transaction'];
        }

        $originalCost = $this->getOriginalCostPriceForRefund($refundTransaction['StockTransaction']);
        $currentPrice = $refundTransaction['StockTransaction']['price'];

        return [
            'valid' => $originalCost > 0,
            'original_cost' => $originalCost,
            'current_price' => $currentPrice,
            'using_original' => ($originalCost > 0 && abs($originalCost - $currentPrice) < 0.01),
            'transaction_data' => $refundTransaction['StockTransaction']
        ];
    }

    /**
     * Batch update historical refund/credit transactions to use original costs
     * Use this for data migration after implementing the feature
     */
    public function migrateHistoricalRefunds($productId = null, $limit = 100) {
        $conditions = [
            'StockTransaction.source_type' => [
                self::SOURCE_RR,
                self::SOURCE_CN,
                self::SOURCE_RQ_INVOICE_REFUND,
                self::SOURCE_RQ_INVOICE_CREDIT_NOTE
            ],
            'StockTransaction.status' => self::STATUS_PROCESSED,
            '(ignored = 0 OR ignored IS NULL)'
        ];

        if ($productId) {
            $conditions['StockTransaction.product_id'] = $productId;
        }

        $refundTransactions = $this->find('all', [
            'conditions' => $conditions,
            'limit' => $limit,
            'order' => 'StockTransaction.received_date ASC'
        ]);

        $updated = 0;
        $errors = [];

        foreach ($refundTransactions as $transaction) {
            try {
                $originalCost = $this->getOriginalCostPriceForRefund($transaction['StockTransaction']);
                if ($originalCost > 0 && abs($originalCost - $transaction['StockTransaction']['price']) > 0.01) {
                    $this->updateAll(
                        ['price' => $originalCost, 'purchase_price' => $originalCost],
                        ['id' => $transaction['StockTransaction']['id']]
                    );
                    $updated++;

                    // Recalculate average price for this product
                    $this->average_on_all(
                        $transaction['StockTransaction']['product_id'],
                        $transaction['StockTransaction'],
                        $transaction['StockTransaction']['received_date']
                    );
                }
            } catch (Exception $e) {
                $errors[] = [
                    'transaction_id' => $transaction['StockTransaction']['id'],
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'processed' => count($refundTransactions),
            'updated' => $updated,
            'errors' => $errors
        ];
    }

    /**
     * Get statistics on refund/credit transactions for reporting
     */
    public function getRefundCreditStatistics($productId = null, $dateFrom = null, $dateTo = null) {
        $conditions = [
            'StockTransaction.source_type' => [
                self::SOURCE_RR,
                self::SOURCE_CN,
                self::SOURCE_RQ_INVOICE_REFUND,
                self::SOURCE_RQ_INVOICE_CREDIT_NOTE
            ],
            'StockTransaction.status' => self::STATUS_PROCESSED,
            '(ignored = 0 OR ignored IS NULL)'
        ];

        if ($productId) {
            $conditions['StockTransaction.product_id'] = $productId;
        }

        if ($dateFrom) {
            $conditions['StockTransaction.received_date >='] = $dateFrom;
        }

        if ($dateTo) {
            $conditions['StockTransaction.received_date <='] = $dateTo;
        }

        $stats = $this->find('all', [
            'fields' => [
                'StockTransaction.source_type',
                'COUNT(*) as count',
                'SUM(StockTransaction.quantity) as total_quantity',
                'SUM(StockTransaction.quantity * StockTransaction.price) as total_value',
                'AVG(StockTransaction.price) as avg_price'
            ],
            'conditions' => $conditions,
            'group' => 'StockTransaction.source_type'
        ]);

        return $stats;
    }

    /**
     * @param $product_id
     * @param $old_received_date
     * @param $transaction_after
     * @return bool
     */
    public function shouldRunBackground($product_id, $transaction_after, $old_received_date = null): bool
    {
        try {
            $numberOfDays = (new DateTime())->diff(new DateTime($old_received_date ?? $transaction_after['StockTransaction']['received_date']))->days ?? 0;
        } catch (Throwable $exception) {
            $numberOfDays = null;
        }
//        $numberOfTransactions = $this->find('count', ['order' => ['received_date' => 'ASC', 'id' => 'ASC'], 'recursive' => -1, 'conditions' => [
//            'StockTransaction.product_id' => $product_id,
//            'StockTransaction.status' => StockTransaction::STATUS_PROCESSED,
//            '(ignored = 0 OR ignored IS NULL)',
//            'OR' => [
//                'received_date > ' => $old_received_date ?? $transaction_after['StockTransaction']['received_date'],
//                'AND' => [
//                    'received_date = ' => $old_received_date ?? $transaction_after['StockTransaction']['received_date'],
//                    'id >= ' => $transaction_after['StockTransaction']['id']
//                ]
//            ]
//        ]]);
//        $shouldRunBackground = $numberOfDays > 3 || $numberOfTransactions > 100;
        $shouldRunBackground = $numberOfDays > 3 || $numberOfDays === null;
        return $shouldRunBackground;
    }
}

?>
