<?php

use App\Services\UserKeyValueRedisService;
use Izam\Daftra\Common\EntityStructure\EntityAndRelationCache;
use Izam\Daftra\Common\Events\EntityStructureSaved;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Daftra\Portal\Models\User as PortalUser;
use Izam\Daftra\Portal\Services\SiteLimitService;
use Izam\Daftra\Portal\Services\SitePluginService;
use Izam\Daftra\Portal\Services\SiteService;
use Izam\Daftra\Portal\Services\SiteUserService;
use Izam\ScoreCalculator\Services\SiteScoreCalculator;

/**
 * @property AppModel $SiteLimit
 * @property AppModel $SiteLimitation
 * @property AppModel $Staff
 * @property Plan $Plan
 */
class Site extends AppModel {

    var $name = 'Site';
    var $filters = array('business_name');
    var $actsAs = array(
        'image' => array('site_logo' => array('width' => 0, 'height' => 0, 'aspect_required' => false,'extensions' => array('jpg', 'png', 'gif','svg','jpeg'),'before_save'=>false, 'folder' => 'files/images/site-logos/'))
    );
    var $belongsTo = array(
        'Plan' => array('className' => 'Plan', 'foreignKey' => 'plan_id'),
    );
    var $hasOne = array(
        'SiteLimit' => array('className' => 'SiteLimit', 'foreignKey' => 'owner_id'),

    );

//    var $hasMany=array(
//        'SiteLimitation' => array('className' => 'SiteLimitation', 'foreignKey' => 'site_id'),
//    );
    var $useDbConfig = 'portal';

    function __construct($id = false, $table = null, $ds = null) {
        parent::__construct($id, $table, $ds);
        $this->validate = array(
            'business_name' => array(
                array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'trimNotEmpty', 'message' => __('Required', true)),
                array('rule' => array('maxLength', 150), 'message' => __('Business name should be 150 characters at most', true))
            ),
            'first_name' => array(
                array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'trimNotEmpty', 'message' => __('Required', true)),
                array('rule' => array('maxLength', 150), 'message' => __('First name should be 150 characters at most', true))
            ),
            'last_name' => array(
                array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'trimNotEmpty', 'message' => __('Required', true)),
                array('rule' => array('maxLength', 150), 'message' => __('Last name should be 150 characters at most', true))
            ),
            'subdomain' => array(
                array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'isUnique', 'message' => __('Subdomain already exists', true)),
                array('rule' => 'validateDomain', 'message' => __('Invalid domain', true))
//				array('rule' => 'alphaNumeric', 'message' => __('Only letters and numbers allowed', true)),
//				array('rule' => array('maxLength', 20), 'message' => __('Last name should be 20 characters at most', true))
            ),
            'country_code' => array(
                array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'checkCountry', 'allowEmpty' => false, 'message' => __('Invalid country', true))
            ),
            'timezone' => array(
                array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'checkTimezone', 'allowEmpty' => false, 'message' => __('Invalid timezone', true))
            ),
            'language_code' => array(
                array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'checkLanguage', 'allowEmpty' => false, 'message' => __('Invalid language', true))
            ),
            'currency_code' => array(
                array('rule' => 'checkCurrency', 'allowEmpty' => true, 'message' => __('Invalid currency', true))
            ),
            'date_format' => array(
                array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'checkDateformat', 'allowEmpty' => true, 'message' => __('Invalid Date format', true))
            ),
            'email' => array(
                array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'email', 'allowEmpty' => true, 'message' => __('Invalid Email', true)),
                array('rule' => 'trimNotEmpty', 'message' => __('Required', true)),
                array('rule' => 'isUnique', 'allowEmpty' => true, 'message' => __('Email already exists', true))
            ),
            'password' => array(
                'rule' => array('minLength', 6), 'allowEmpty' => false, 'message' => __('The password should be 6 characters at least', true)
            ),
            //'model' => array('rule' => 'blank', 'required' => false)
        );
    }

    public function trimNotEmpty($check) {
        $value = trim(array_values($check)[0]);
        return !empty($value);
    }


    //------------------------------
    public function beforeSave($options = array()) {
        parent::beforeSave($options);
        if (!empty($this->data[$this->name]['site_logo']) and is_array($this->data[$this->name]['site_logo']) || json_decode($this->data[$this->name]['site_logo'])) {
        unset($this->data[$this->name]['site_logo']);    
        $this->data[$this->name]['error_upload_logo']=true;
        }
        if (!empty($this->data[$this->name]['password']) && strlen($this->data[$this->name]['password']) != strlen(HashPassword($this->data[$this->name]['password']))) {
            $this->data[$this->name]['password'] = HashPassword($this->data[$this->name]['password']);
        }
        return true;
    }

	public function afterSave($created) {
		parent::afterSave($created);
        setLastUpdatedAt(0, SettingsUtil::SITE_INFO_UPDATED_AT);

        // in case of update only
        if (!$created) {
            $this->loadModel('SiteToUpdateThirdParty');
            $check = $this->SiteToUpdateThirdParty->find('count', [
                'conditions' => [
                    'SiteToUpdateThirdParty.site_id' => $this->id,
                    'SiteToUpdateThirdParty.status' => 0
                ]
            ]);
            if ($check == 0){
                $this->SiteToUpdateThirdParty->create();
                $siteToUpdateRecord = [
                    'site_id' => $this->id,
                    'status' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                ];
                $this->SiteToUpdateThirdParty->save($siteToUpdateRecord);
            }
        }
	}

		/**
    * update plugin code installation
     */
    function owner_update_plugin($id,$status,$recursiveCall = false, $isAjax =1, $activityLog = true){

        $this->loadModel('SitePlugin');
        $this->loadModel('Plugin');
        $session = new CakeSession;
        $site = getCurrentSite();
        $site_db=$this->find('first',array('conditions'=>array('Site.id'=>$site['id'])));
        $config= json_decode($site_db['Site']['db_config'],true);
        $oplugin=$this->Plugin->read(null,$id);
        $find=$this->SitePlugin->find('first',array('conditions'=>array('SitePlugin.plugin_id'=>$id,'SitePlugin.site_id'=>$site['id'])));

        if(empty($find) && $status == "false"){
            if($recursiveCall){
                return array();
            }else{
                return  json_encode(['status' => true]);
            }
        }
        if(empty($oplugin)){
            if($isAjax){
            echo json_encode(array('status'=>false,'msg'=>__('Plugin not found',true)));
            die();
            }
	        return false;
        }
        $this->loadModel('PluginDependency');
        $reflectedPlugins = [];
        if (!$recursiveCall) {
            $this->clearCacheBasedOnEntity();
        }
        if ($status == 'true') {

            $dependantPlugins = $this->PluginDependency->find('all', ['conditions' => ['PluginDependency.plugin_id' => $id]]);
            foreach ($dependantPlugins as $dependantPlugin) {
                $reflectedPlugins += $this->owner_update_plugin($dependantPlugin['PluginDependency']['parent_plugin_id'], $status, true, $isAjax, $activityLog);
                $reflectedPlugins[] = $dependantPlugin['PluginDependency']['parent_plugin_id'];
            }
        } else {
            $dependantPlugins = $this->PluginDependency->find('all', ['conditions' => ['PluginDependency.parent_plugin_id' => $id]]);
            foreach ($dependantPlugins as $dependantPlugin) {
                $reflectedPlugins += $this->owner_update_plugin($dependantPlugin['PluginDependency']['plugin_id'], $status, true, $isAjax, $activityLog);
                $reflectedPlugins[] = $dependantPlugin['PluginDependency']['plugin_id'];
            }
        }
        $save=[];
        $save['SitePlugin']['id'] = $find['SitePlugin']['id'];
        $save['SitePlugin']['plugin_id'] = $id;
        $save['SitePlugin']['site_id'] = $site['id'];
        $save['SitePlugin']['active'] = ($status == "true");
        $save['SitePlugin']['installed'] = 1;
        if($oplugin['Plugin']['is_external']=="1" && $status=="true" && empty($find)){
            $sql_file=APP.'other'.DS.'plugin'.DS.strtolower($oplugin['Plugin']['plugin_key']).'.sql';
            if(file_exists($sql_file) ){
//                shell_exec("mysql -u{$config['login']} -p{$config['password']} -h{$config['host']} {$config['database']} < " . $sql_file);
                $tmpDir = '/tmp/'.SITE_HASH.DS;// getSiteFilesPath('invoices');
                if (!is_dir($tmpDir)) {
                    mkdir($tmpDir, 0777, true);
                }
                $tmpFile=$tmpDir.'plugin_id'.$id.'.txt';
                exec("mysql -u{$config['login']} -p{$config['password']} -h{$config['host']} {$config['database']} < " . $sql_file.' 2> '.$tmpFile,$out,$commandStatus);
                if($commandStatus!=0){
                    notify_admin_fetal_error(__FUNCTION__." Error In Line ".__LINE__.file_get_contents($tmpFile));
                }

            }
            if (method_exists($this->Plugin,'plugin_callback_'.$save['SitePlugin']['plugin_id'])){
                $this->Plugin->{'plugin_callback_'.$save['SitePlugin']['plugin_id']}();
            }
        }
        if($oplugin['Plugin']['is_external']=="1" && $status=="false"){
            if (method_exists($this->Plugin,'plugin_disabled_callback_'.$save['SitePlugin']['plugin_id'])){
                $this->Plugin->{'plugin_disabled_callback_'.$save['SitePlugin']['plugin_id']}();
            }
        } else if($status == "true" && $oplugin['Plugin']['is_external'] =="1") {
            if (method_exists($this->Plugin,'plugin_enabled_callback_'.$save['SitePlugin']['plugin_id'])){
                $this->Plugin->{'plugin_enabled_callback_'.$save['SitePlugin']['plugin_id']}();
            }
        }

        if(!$find){     
            $row = SitePluginService::create($save['SitePlugin']);
        }else{
            $data = $save['SitePlugin'];
            unset($save['SitePlugin']['active']);
            unset($save['SitePlugin']['installed']);
            $condition = $save['SitePlugin'];
            $row = SitePluginService::updateWhere($data,$condition);
        }
       

        if ($row) {
            if ($status == "true") {
                $return = ['status' => true, 'msg' => __('Plugin has been enabled', true)];
                if ($reflectedPlugins) {
                    $return['reflectedPlugins'] = $reflectedPlugins;
                }
                if ($activityLog) {
	                $this->add_actionline(ACTION_UPDATED_PLUGIN, ['param5' => $oplugin['Plugin']['name']]);
                }
            } else {
                $return = ['status' => true, 'msg' => __('Plugin has been disabled', true)];
                if ($reflectedPlugins) {
                    $return['reflectedPlugins'] = $reflectedPlugins;
                }
	            if ($activityLog) {
		            $this->add_actionline(ACTION_UPDATED_PLUGIN, ['param4' => $oplugin['Plugin']['name']]);
	            }
            }
            if(!$recursiveCall)
                \App\Services\Communicator\CommunicatorFacade::getInstance()->updatePluginWidgets($id, $status, $return['reflectedPlugins']);
        } else {
            $return = ['status' => false, 'msg' => __('Please try again', true)];
        }
        $tempFileName = APP."tmp".DS."cache".DS."models".DS."cake_model_default_".$config['database']."_list";
        exec("rm ".$tempFileName, $rmOutput, $rmStatus);
        Cache::delete('cake_model_default_'.$config['database'].'_list');
        $session->delete('CurrentPlugin');
        Cache::delete('current_plugin_' . $site['id']);
        getCurrentPlugin();
        delete_menus();
        if (!$recursiveCall) {
            if($isAjax)
                die(json_encode($return));
            else
                return json_encode($return);
        } else {
            return $reflectedPlugins;
        }
    }


    public function clearCacheBasedOnEntity()
    {
        EntityAndRelationCache::setCacheDriver(new \App\Entity\EntityCacheDriver(getCurrentSite("id")));
        EntityAndRelationCache::clearCache();
    }


    public function get_pincode()
    {
        $id = getCurrentSite('id');
        $pin = rand(1000, 9999);
        $conditions['Site.id'] = $id;
        $site = $this->find($conditions, null, null, 0);

        $forceUpdate = $this->forceUpdateSupportPin();
        
        if (!empty($site['Site']['support_pin']) && !$forceUpdate) {
            return $site['Site']['support_pin'];
        }
        $this->id = $id;
        SiteService::updateById($id, ['support_pin' => $pin]);
        return $pin;
    }

    public function forceUpdateSupportPin()
    {
        if(isset($_GET['foreUpdatePin']) && $_GET['foreUpdatePin']){
            return true;
        }

        if(useRedis()) {
            $supportPinCacheKey = 'support_pin_refresh_date';

            $redisService =  resolve(UserKeyValueRedisService::class);
            $supportPinRefreshDate = $redisService->get($supportPinCacheKey);

            debug('SupportPinRefreshDate:'.$supportPinRefreshDate);

            if(empty($supportPinRefreshDate)) {
                $redisService->set($supportPinCacheKey, date('Y-m-d'));
                return false;
            }

            $date = DateTime::createFromFormat('Y-m-d', $supportPinRefreshDate);
            $now = new DateTime();
            $now->modify('-1 week');

            debug('SupportPinCompareDate:'.$now->format('Y-m-d'));
            debug('SupportPinUpdateCheck:'.($date < $now?'true':'false'));

            if ($date < $now) {
                $redisService->set($supportPinCacheKey, date('Y-m-d'));
                return true;
            } 
        }

        return false;
    }

    //-----------------------------------------------------
    function login_user($data) {
        $conditions = array();
        $conditions['Site.email'] = $data[$this->name]['email'];
        $user = $this->find($conditions, null, null, 0);
        if ($user) {
            $password = base64_encode(Security::cipher($data[$this->name]['password'], $user[$this->name]['email']));
            if ($user[$this->name]['password'] == $password) {
                return $user;
            }
        }
        return false;
    }

    /**
     *
     * @param integer $site_id
     * @return string language code
     */
    public function get_site_language($site_id = false) {
        if (!$site_id) {
            $site_id = getCurrentSite('id');
        }
        $site = $this->findById($site_id);
        if (!empty($site) && !empty($site['Site']['language_code']) && ClassRegistry::init('Language')->check_language_code($site['Site']['language_code'])) {
            return $site['Site']['language_code'];
        }
        return DEFAULT_LANGUAGE;
    }

    /**
     *
     * @param <type> $site_id
     * @return <type>
     */
    public function get_site_country($site_id = false) {
        if (!$site_id) {
            $site_id = getAuthOwner('id');
        }
        $site = $this->findById($site_id);
        if (!empty($site) && !empty($site['Site']['country_code']) && ClassRegistry::init('Country')->check_country_code($site['Site']['country_code'])) {
            return $site['Site']['country_code'];
        }
        return DEFAULT_COUNTRY;
    }

    /**
     *
     * @param <type> $site_id
     * @return <type>
     */
    public function get_site_currency($site_id = false) {
        if (!$site_id) {
            $site_id = getAuthOwner('id');
        }
        $site = $this->findById($site_id);
        if (!empty($site) && !empty($site['Site']['currency_code']) && ClassRegistry::init('Currency')->check_currency_code($site['Site']['currency_code'])) {
            return $site['Site']['currency_code'];
        }
        return DEFAULT_CURRENCY;
    }

    function ChangePassword($data, $user) {
        
        $site=$this->find('first',array('recursive'=>-1,'conditions'=>array('Site.id'=> getCurrentSite('id'))));
        $user=$site['Site'];
       
        $this->loadModel('User');
        $ouser=$this->User->find('first',array('recursive'=>-1,'conditions'=>array('User.id'=>  getCurrentSite('user_id'))));
        $site_count=$this->find('count',array('recursive'=>-1,'conditions'=>array('Site.user_id'=>  getCurrentSite('user_id'))));
        
       // print_pre($ouser);
      //  die();
        if (empty($data[$this->name]['old_password'])) {
            $this->validationErrors['old_password'] = __('Required', true);
        }
        if (empty($data[$this->name]['password'])) {
            $this->validationErrors['password'] = __('Required', true);
        }
        if (empty($data[$this->name]['psswrd'])) {
            $this->validationErrors['psswrd'] = __('Required', true);
        }
        if ($this->validationErrors) {
            return false;
        }
        $hashedPassword = HashPassword($data[$this->name]['old_password']);
        if ($hashedPassword != $user['password'] && $hashedPassword != $ouser['User']['password']) {
            $this->validationErrors['old_password'] = __('Current Password incorrect', true);
            return false;
        }
        if ($data[$this->name]['old_password'] == $data[$this->name]['password']) {
            $this->validationErrors['psswrd'] = __('You can\'t use current password as new password', true);
            return false;
        }
        if ($data[$this->name]['password'] != $data[$this->name]['psswrd']) {
            $this->validationErrors['psswrd'] = __('Not match', true);
            return false;
        }
        $data[$this->name]['id'] = getCurrentSite('id');
        $update = SiteService::updateById($data[$this->name]['id'], $data[$this->name]);
        if ($update) {
            if ($site_count == 1) {
                PortalUser::where('id', $ouser['User']['id'])->update(['password' => HashPassword($data[$this->name]['password'])]);
//                $userModel->where('id', $ouser['User']['id'])
//                    ->update(['password' => HashPassword($data[$this->name]['password'])]);
            }
            return true;
        }
        return false;
    }

    function ChangeEmail($data) {
        
        $user = $this->findById(getAuthOwner('id'));
        $this->loadModel('User');
        $ouser=$this->User->find('first',array('recursive'=>-1,'conditions'=>array('User.id'=>  getCurrentSite('user_id'))));
        $site_count=$this->find('count',array('recursive'=>-1,'conditions'=>array('Site.user_id'=>  getCurrentSite('user_id'))));
        $site_email = $this->find('count', ['recursive' => -1, 'conditions' => ['Site.email' => $data[$this->name]['email'], 'Site.id !=' =>$user[$this->name]['id']]]);
        if ($site_email > 0) {
            $this->validationErrors['email'] = __('This account is already registered before, make sure you have entered the correct details or you can try to', true);
            return false;
        }
        $site = array();
        $site[$this->name]['id'] = $user[$this->name]['id'];
        $site[$this->name]['email'] = $data[$this->name]['email'];

        if (empty($data[$this->name]['password'])) {
            $this->validationErrors['password'] = __('Required', true);
            return false;
        }
        $hashedPassword = HashPassword($data[$this->name]['password']);
        if ($hashedPassword != $user[$this->name]['password'] && $hashedPassword != $ouser['User']['password']) {
            $this->validationErrors['password'] = __('The password is incorrect', true);
            return false;
        }

        if (SiteService::updateById($site[$this->name]['id'], $site[$this->name])) {
            if ($site_count == 1) {
                PortalUser::where('id', $ouser['User']['id'])->update(['email' => $site[$this->name]['email']]);
            }
            return true;
        }
        
        return false;
    }

    function ChangeSmtp($data, $save = false) {
        $user = $this->findById(getAuthOwner('id'));

        $site = array();
        $site[$this->name]['id'] = $user[$this->name]['id'];
        $site[$this->name]['use_smtp'] = $data[$this->name]['use_smtp'];

        if ($site[$this->name]['use_smtp'] != "1") {
            if ($save == true) {
                if ($data[$this->name]['reset-btn'] == '1') {
                    unset($data[$this->name]['reset-btn']);
                    $site[$this->name] = $data[$this->name];
                    $site[$this->name]['smtp_credentials'] = null;
                }
                if (SiteService::updateById(getCurrentSite('id'), $site[$this->name])) {
                    return true;
                }
            }
            return true;
        }
        $site[$this->name]['smtp_from_email'] = $data[$this->name]['smtp_from_email'];

        if (empty($data[$this->name]['smtp_from_email'])) {
            $this->validationErrors['smtp_from_email'] = __('Required', true);
            return false;
        }
        $site[$this->name]['smtp_from_name'] = $data[$this->name]['smtp_from_name'];
        if (empty($data[$this->name]['smtp_from_name'])) {
            $this->validationErrors['smtp_from_name'] = __('Required', true);
            return false;
        }
        $site[$this->name]['smtp_user_name'] = $data[$this->name]['smtp_user_name'];
//        if (empty($data[$this->name]['smtp_user_name'])) {
//            $this->validationErrors['smtp_user_name'] = __('Required', true);
//            return false;
//        }
        $site[$this->name]['smtp_password'] = $data[$this->name]['smtp_password'];
        $site[$this->name]['smtp_credentials'] = ($data[$this->name]['smtp_credentials'] != '') ? $data[$this->name]['smtp_credentials'] : null;
//        if (empty($data[$this->name]['smtp_password'])) {
//            $this->validationErrors['smtp_password'] = __('Required', true);
//            return false;
//        }
        $site[$this->name]['smtp_host'] = $data[$this->name]['smtp_host'];
        if (empty($data[$this->name]['smtp_host'])) {
            $this->validationErrors['smtp_host'] = __('Required', true);
            return false;
        }
        $site[$this->name]['use_smtp'] = $data[$this->name]['use_smtp'];
        if (empty($data[$this->name]['use_smtp'])) {
            $this->validationErrors['use_smtp'] = __('Required', true);
            return false;
        }
        $site[$this->name]['smtp_ssl'] = $data[$this->name]['smtp_ssl'];

        $site[$this->name]['smtp_port'] = $data[$this->name]['use_smtp'] ? $data[$this->name]['smtp_port'] : 25;

        if ($save == true) {
            if (SiteService::updateById(getCurrentSite('id'), $site[$this->name])) {
                return true;
            }
        } else {
            return true;
        }
        return false;
    }

    function checkDateformat($data) {
//		$dateFormat = array_shift($data);
//		$date_formats=getDateFormats(1);
//		if(!empty ($dateFormat) && !in_array($dateFormat, $date_formats)){
//			return false;
//		}
        return true;
    }

    function checkTimezone($data) {
        $timezone = array_shift($data);
        if (!empty($timezone) && !(ClassRegistry::init('Timezone')->hasAny(array('Timezone.id' => $timezone)))) {
            return false;
        }
        return true;
    }

    function reload_session($id = null) {
        if ($id == null) {
            $id = getCurrentSite('id');
        }
        $site = $this->findById($id);
        // dd($site);
        $session = new CakeSession;

        $site['Site']['type'] = 'owner';
        unset($site['Site']['password']);
        $staff = $session->read('STAFF');


        if (isset($staff['id'])) {
            $site['Site']['staff_id'] = $staff['id'];
            $site['Site']['is_super_admin'] = $staff['Role']['is_super_admin'];
        } else {
            $site['Site']['staff_id'] = 0;
            $site['Site']['is_super_admin'] = 1;
        }
        
        $session->write('CurrentSite', $site['Site']);
        CurrentSiteLang();
        
        $timeZoneModel = $this->loadModel('Timezone');
        $timezone = $timeZoneModel->field('zone_name', array('Timezone.id' => $site['Site']['timezone']));
        $session->write('timezone', $timezone);

        $session->write('OWNER', $site['Site']);
//        debug($session->read());

        return TRUE;
    }

//	function check_user_limitation($site_id = false) {
//		return true;
//	}

    function getDefaultPaymentGateway() {
        $site_id = getCurrentSite('id');
        
       // return false;
        
       return  ClassRegistry::init('SitePaymentGateway')->field('payment_gateway', array('(SitePaymentGateway.disable_for_client is null or SitePaymentGateway.disable_for_client=0)','SitePaymentGateway.default' => 1));
    }

    function adminSave($data) {
        $this->validate['password'] = array('rule' => array('minLength', 6), 'message' => __('Password must be at least 6 characters', true));
        $this->validate['passwd'] = array('rule' => 'matchPasswords', 'message' => __('Passwords do not match', true));
        $this->validate['model'] = array('rule' => 'checkModelSite', 'message' => __('A model site already exists for this country', true));
        $this->validate['country_code'] = array('rule' => 'checkModelCountry', 'message' => __('Another default model site already found. Please select a country.', true));
        unset($this->validate['timezone']);

        if (!empty($data['Site']['id'])) {
            if (empty($data['Site']['password'])) {
                unset($data['Site']['password'], $data['Site']['passwd'], $this->validate['password'], $this->validate['passwd']);
            }

            $data['Site']['first_login'] = empty($data['Site']['model']);
        } else {
            $data['Site']['first_login'] = empty($data['Site']['model']);
            $data['Site']['next_invoice_number'] = '000001';
            $data['Site']['theme_color'] = '';
            $data['Site']['colors_set'] = 0;
        }

        if (!empty($data['Site']['id'])) {
            $data['SiteLimit']['owner_id'] = $data['Site']['id'];

            $limitId = $this->SiteLimit->field('id', array('SiteLimit.owner_id' => $data['Site']['id']));
            $data['SiteLimit']['id'] = $limitId;
        }
        $result = SiteService::updateby($data['Site']);
        $siteId = $data['Site']['id'] ?? getCurrentSite('id');
        return SiteService::updateById($siteId, $data['Site']);
    }

    function checkModelCountry($param) {
        if (!empty($this->data['Site']['model'])) {
            if (!empty($param['country_code'])) {
                return $this->checkCountry($param);
            } else {
                $conditions = array('Site.model' => 1, 'Site.country_code IS NULL OR Site.country_code = ""');
                if (!empty($this->data['Site']['id'])) {
                    $conditions['Site.id !='] = $this->data['Site']['id'];
                }
                return !$this->hasAny($conditions);
            }
        }

        return !empty($param['country_code']) && $this->checkCountry($param);
    }

    function checkModelSite($param) {
        if ($param['model']) {
            $conditions = array('Site.model' => 1, 'Site.country_code' => $this->data[$this->alias]['country_code']);
            if (!empty($this->data[$this->alias]['id'])) {
                $conditions['Site.id !='] = $this->data[$this->alias]['id'];
            }
            return !$this->hasAny($conditions);
        }
        return true;
    }

    function matchPasswords($param) {
        if ($param['passwd'] != $this->data['Site']['password']) {
            if (empty($this->validationErrors['password'])) {
                $this->invalidate('password', __('Passwords do not match', true));
            }
            return false;
        }

        return true;
    }

//	public function afterSave($created = false) {
//		parent::afterSave($created);
//		if ($created) {
//			$id = $this->getLastInsertID();
//			$taxes = array(
//				array('Tax' => array('site_id' => 8, 'name' => __('Tax 1', true), 'value' => '10')),
//				array('Tax' => array('site_id' => 8, 'name' => __('Tax 2', true), 'value' => '10'))
//			);
//
//			ClassRegistry::init('taxes')->saveAll($taxes);
//		}
//	}
    //Check limits area
    function check_add_invoice_limit($site_id = false) {

        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find('first', array('conditions' => array('Site.id' => $site_id), 'recursive' => -1));
            $site = $dbSite['Site'];
        }


        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('You cannot  create more invoices as your account is suspended.', true);

            return $return;
        }

        if ($site["created"] > date('Y-m-d', strtotime(Trial_Period)))
            return array('status' => true, 'message' => '');

        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site['id']));
        if ($limits) {
            $limit = $limits['SiteLimit']['invoice_limit'];
        } else {
            $limit = $this->Plan->field('invoice_limit', array('Plan.id' => $site['plan_id']));
        }
        $this->loadModel('OwnerWatch');
        $count = $this->OwnerWatch->field('invoices');
        if ($limit <= $count) {
            return array('status' => false, 'message' => sprintf(__('You can create only %d invoices. Please upgrade if you want to create more invoices.', true), $limit));
        }
        return array('status' => true, 'message' => '');
    }

    /**
     *
     * @param int $site_id
     * @param bool $is_staff
     * @return Array Return array with two keys
     *            <ul>
     *                <li>status: a boolean to check if owner has  staff limitation</li>
     *                <li>message: The message to show after redirect</li>
     *            </ul>
     */
    
   public function check_add_staff_limit($site_id = null, $is_staff = true) {
       if (!$site_id) {
           $site = getCurrentSite();
           $site["session_data"] = true;
       } else {
           $dbSite = $this->find(array('Site.id' => $site_id), 'id, plan_id , created, status, expiry_date');
           $site = $dbSite['Site'];
       }
       $site_id = $site['id'];

       $is_suspended = $this->IsSuspended($site);
       if ($is_suspended) {
           $return['status'] = false;
           $return['message'] = __('You cannot  create more staff members as your account is suspended.', true);

           return $return;
       }
       if ($site["plan_id"] > 1 && $is_staff && strtotime($site['expiry_date']) < strtotime('+1 Month')) {
           return array('status' => true, 'message' => '');
       }

       $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site_id));
       if ($limits) {
           $limit = $limits['SiteLimit']['staff_count'] + $limits['SiteLimit']['included_staff'];

           if ($limit == 0) {
               $limit = 1;
           }
       }

       if ($is_staff && (empty($limit) || $limit == 0)) {
           $limit = 1;
       }
       $this->id = $site_id;



       // get staff(users) and branches count on fly
       $count = $this->activeUsersAndBranchesCount();

       $return = array('status' => true, 'message' => '');

       if ($count >= $limit) {
           $return['status'] = false;
           $message = 'You can not create more employees or branches using your current package';
           if (ifPluginActive(BranchesPlugin))
               $message = 'You can not create more employees or branches using your current package';
           $return['message'] = sprintf(__($message, true), $limit);
       }

       return $return;
    }

    public function check_add_branch_limit($site_id = null) {
        if (!$site_id) {
            $site_id = getCurrentSite('id');
        }
        return $this->check_add_staff_limit($site_id, false);
    }

    public function update_current_staff_count($site_id = null) {
        if (!$site_id) {
            $site_id = getCurrentSite('id');
        }
        $this->id = $site_id;

        $this->loadModel('Staff');
        $count = $this->Staff->calculate_current_staff_count();

        if (ifPluginActive(BranchesPlugin)) {
            $this->loadModel('Branch');
            $count += $this->Branch->find('count', ['conditions' => ['Branch.status' => Branch::STATUS_ACTIVE]]) - 1;
        }
        SiteService::updateById($site_id, ['current_staff_count' => $count]);
    }

    public function check_add_client_limit($site_id = null) {
		$this->loadModel('Client');

        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find(array('Site.id' => $site_id), 'id, created, plan_id , status');
            $site = $dbSite['Site'];
        }
        $site_id = $site['id'];

        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('You cannot create more clients as your account is suspended.', true);

            return $return;
        }


        if (Site_Full_name_NoSpace != "OnlineInvoices" && $site["created"] > date('Y-m-d', strtotime(Trial_Period))) {
            return array('status' => true, 'message' => '');
        }        

        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site_id));

        if ($limits) {
			
            $limit = $limits['SiteLimit']['client_limit'];
			if($limits['SiteLimit']['client_limit_type']=="1"){
			$client_count=$this->Client->find('count',array('conditions'=>array('date(Client.created)>=DATE_FORMAT(NOW(), "%Y-%m-01")')));	
			}else{
			$client_count=$this->Client->find('count');		
			}
        } else {
            $limit = $this->Plan->field('client_limit', array('Plan.id' => $site['plan_id']));
        }
		
	
        //$this->loadModel('OwnerWatch');
        //$count = $this->OwnerWatch->field('clients');
		
		
		//client_limit_type
        if ($site['plan_id'] == 1) {
            
            $client_count = $this->Client->find('count');
        }

        $return = array('status' => true, 'message' => '');
        if ($client_count >= $limit) {
            $return['status'] = false;
            $return['message'] = sprintf(__('You can create only %d clients. Please consider the upgrade if you want to add more clients.', true), $limit);
        }

        return $return;
    }

    public function check_limit($limit_name, $count) {
        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find(array('Site.id' => $site_id));
            $site = $dbSite['Site'];
        }

        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('Sorry, Your account is suspended.', true);

            return $return;
        }

        if ($site["created"] > date('Y-m-d', strtotime(Trial_Period)))
            return array('status' => true, 'message' => '');

        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site_id));
        if ($limits) {
            $limit = $limits['SiteLimit'][$limit_name . '_limit'];
        } else {
            $limit = $this->Plan->field($limit_name . '_limit', array('Plan.id' => $site['plan_id']));
        }

        if ($count >= $limit) {
            return array('status' => false, 'message' => sprintf(__('You can only add %d %s.', true), $limit, Inflector::humanize($limit_name)));
        }

        return array('status' => true, 'message' => '');
    }

    public function check_add_subscription_limit($site_id = null) {

        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find(array('Site.id' => $site_id));
            $site = $dbSite['Site'];
        }

        $site_id = $site['id'];

        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('You cannot create more  subscriptions as your account is suspended.', true);

            return $return;
        }

        if ($site["created"] > date('Y-m-d', strtotime(Trial_Period)))
            return array('status' => true, 'message' => '');

        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site_id));
        if ($limits) {
            $limit = $limits['SiteLimit']['subscription_limit'];
        } else {
            $limit = $this->Plan->field('subscription_limit', array('Plan.id' => $site['plan_id']));
        }

        $this->loadModel('OwnerWatch');
        $count = $this->OwnerWatch->field('subscriptions');

        if ($count >= $limit) {
            return array('status' => false, 'message' => sprintf(__('You can only add %d recurring invoice generators.', true), $limit));
        }

        return array('status' => true, 'message' => '');
    }

    public function check_add_estimate_limit($site_id = null) {

        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find(array('Site.id' => $site_id));
            $site = $dbSite['Site'];
        }

        $site_id = $site['id'];

        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('You cannot  create more estimates as your account is suspended.', true);

            return $return;
        }

        if ($site["created"] > date('Y-m-d', strtotime(Trial_Period)))
            return array('status' => true, 'message' => '');

        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site_id));

        if ($limits) {
            $limit = $limits['SiteLimit']['estimate_limit'];
        } else {
            $limit = $this->Plan->field('estimate_limit', array('Plan.id' => $site['plan_id']));
        }

        $this->loadModel('OwnerWatch');
        $count = $this->OwnerWatch->field('estimates');

        if ($count >= $limit) {
            return array('status' => false, 'message' => sprintf(__('You can only add %d estimates.', true), $limit));
        }

        return array('status' => true, 'message' => '');
    }

    public function check_add_layout_limit($site_id = null) {
        return array('status' => true, 'message' => '');
        return array('status' => true, 'message' => '');
        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find(array('Site.id' => $site_id));
            $site = $dbSite['Site'];
        }

        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('You cannot  create more layouts as your account is suspended.', true);

            return $return;
        }

        if ($site["created"] > date('Y-m-d', strtotime(Trial_Period)))
            return array('status' => true, 'message' => '');

        $site_id = $site['id'];
        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site_id));

        if ($limits) {
            $limit = $limits['SiteLimit']['custom_layout_limit'];
        } else {
            $limit = $this->Plan->field('custom_layout_limit', array('Plan.id' => $site['plan_id']));
        }

        $this->loadModel('OwnerWatch');
        $count = $this->OwnerWatch->field('layouts');

        if ($count >= $limit) {
            return array('status' => false, 'message' => sprintf(__('You can only add %d invoice layouts. Please consider the upgrade if you want to add more layouts.', true), $limit));
        }
        //return array('status' => true, 'message' => '');
    }

    //TODO: use those functions
    function check_daily_email_limit($site_id = false) {


        $site = getCurrentSite();
//        if ($site['plan_id'] > 1) {
//            return array('status' => true, 'message');
//        }
        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site_id));
        $invoice_limit = $limits['SiteLimit']['invoice_limit'];




        //SELECT ROUND ((LENGTH(send_to)- LENGTH( REPLACE ( send_to, ",", "") ) ) / LENGTH(","))+1 AS count FROM `email_logs` WHERE 1 order by id desc

        $email_logs = ClassRegistry::init('EmailLog');
        $email_logs->recursive = -1;
        $date = date("Y-m-d");
        $today_email_count = $email_logs->find('all', array('fields' => 'sum(ROUND ((LENGTH(send_to)- LENGTH( REPLACE ( send_to, ",", "") ) ) / LENGTH(","))+1) AS count', 'conditions' => array('Date(EmailLog.sent_date)' => $date)));
        $today_email_count = intval($today_email_count[0][0]['count']);



        if (($site['plan_id'] > 1 && $today_email_count>=250 &&  $site['use_smtp']!="1") ||  ($site['plan_id']==1 && $today_email_count >= $invoice_limit)) {

            return array('status' => false, 'message' => sprintf(__('You can only send %d email per day. Please consider the upgrade if you want to send more emails.', true), ($site['plan_id']==1?$invoice_limit:250) ));
        }
        return array('status' => true, 'message', 'left' => ($invoice_limit - $today_email_count));
    }

    function check_file_szie_limit($site_id = false) {
        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site_id));
        $file_size_limit = $limits['SiteLimit']['file_size'];
        $upgrade_link = '<a style="color:black" target="_blank" class="limit-link" href="/owner/owners/upgrade">Upgrade</a>';

        $SiteRow = $this->find('first', array('conditions' => array('owner_id' => $site_id)));

        if ($file_size_limit != 0 and $SiteRow['Site']['file_size'] >= $file_size_limit) {

            return array('upgrade_link' => $upgrade_link, 'status' => false, 'message' => (__('You have exceeded your allowed disk space for attachments, Please %s your account or contact us for more details .', true) ));
        }
        return array('status' => true, 'Site' => $SiteRow['Site']);
    }

    function check_add_invoice_template_limit($site_id = false) {
        return array('status' => true, 'message');
        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find('first', array('conditions' => array('Site.id' => $site_id), 'recursive' => -1));
            $site = $dbSite['Site'];
        }

        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('You cannot  create more  templates as your account is suspended.', true);

            return $return;
        }

        if ($site["created"] > date('Y-m-d', strtotime(Trial_Period)))
            return array('status' => true, 'message' => '');

        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site['id']));
        if ($limits) {
            $limit = $limits['SiteLimit']['invoice_template'];
        } else {
            $limit = $this->Plan->field('invoice_template', array('Plan.id' => $site['plan_id']));
        }

        $this->loadModel('OwnerWatch');
        $count = $this->OwnerWatch->field('invoice_template');
        if ($limit <= $count) {
            return array('status' => false, 'message' => sprintf(__('You can create only %d prefilled invoices.', true), $limit));
        }
        return array('status' => true, 'message' => '');
    }

    function check_add_document_limit($site_id = false) {
        return array('status' => true, 'message' => '');
        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find('first', array('conditions' => array('Site.id' => $site_id), 'recursive' => -1));
            $site = $dbSite['Site'];
        }
        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('You cannot create more documents as your account is suspended.', true);

            return $return;
        }

        if ($site["created"] > date('Y-m-d', strtotime(Trial_Period)))
            return array('status' => true, 'message' => '');

        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site['id']));
        if ($limits) {
            $limit = $limits['SiteLimit']['documents'];
        } else {
            $limit = $this->Plan->field('documents', array('Plan.id' => $site['plan_id']));
        }

        $this->loadModel('OwnerWatch');
        $count = $this->OwnerWatch->field('documents');
        if ($limit <= $count) {
            return array('status' => false, 'message' => sprintf(__('You can create only %d documents.', true), $limit));
        }
        return array('status' => true, 'message' => '');
    }

    function check_add_terms_limit($site_id = false) {
        return array('status' => true, 'message' => '');
        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find('first', array('conditions' => array('Site.id' => $site_id), 'recursive' => -1));
            $site = $dbSite['Site'];
        }

        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('You cannot  create any terms as your account is suspended.', true);

            return $return;
        }

        if ($site["created"] > date('Y-m-d', strtotime(Trial_Period)))
            return array('status' => true, 'message' => '');

        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site['id']));
        if ($limits) {
            $limit = $limits['SiteLimit']['terms'];
        } else {
            $limit = $this->Plan->field('terms', array('Plan.id' => $site['plan_id']));
        }

        $this->loadModel('OwnerWatch');
        $count = $this->OwnerWatch->field('terms');
        if ($limit <= $count) {
            return array('status' => false, 'message' => sprintf(h(__('You can create only %d terms & conditions.', true)), $limit));
        }
        return array('status' => true, 'message' => '');
    }

    function check_add_email_template_limit($site_id = false) {
        return array('status' => true, 'message' => '');
        if (!$site_id) {
            $site = getCurrentSite();
            $site["session_data"] = true;
        } else {
            $dbSite = $this->find('first', array('conditions' => array('Site.id' => $site_id), 'recursive' => -1));
            $site = $dbSite['Site'];
        }

        $is_suspended = $this->IsSuspended($site);
        if ($is_suspended) {
            $return['status'] = false;
            $return['message'] = __('You cannot  create any templates as your account is suspended.', true);

            return $return;
        }

        if ($site["created"] > date('Y-m-d', strtotime(Trial_Period)))
            return array('status' => true, 'message' => '');
        $limits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site['id']));
        if ($limits) {
            $limit = $limits['SiteLimit']['email_templates'];
        } else {
            $limit = $this->Plan->field('email_templates', array('Plan.id' => $site['plan_id']));
        }

        $this->loadModel('OwnerWatch');
        $count = $this->OwnerWatch->field('terms');
        if ($limit <= $count) {
            return array('status' => false, 'message' => sprintf(h(__('You can create only %d templates.', true)), $limit));
        }
        return array('status' => true, 'message' => '');
    }

    function getCheckList() {
        $id = getAuthOwner('id');

        $owner = $this->findById($id);
        $logo = empty($owner[$this->alias]['site_logo']);
        $smtp = $owner[$this->alias]['use_smtp'] == 0 ? 1 : 0;
        $taxes = !$this->loadModel('Tax')->hasAny(array('Tax.site_id' => $id, 'Tax.value >' => 0));
        $paymentGateways = !$this->loadModel('SitePaymentGateway')->hasAny(array('SitePaymentGateway.active' => 1, 'OR' => array('SitePaymentGateway.payment_gateway NOT ' => array('offline', 'bank', 'cash', 'cheque', 'credit'), 'AND' => array('SitePaymentGateway.username !=' => '', 'SitePaymentGateway.username IS NOT NULL'))));
        $firstInvoice = !($this->loadModel('Invoice')->find('count',array('limit'=>1,'recursive'=>-1))==0?false:true);
        $defaultInvoiceLayout = !$this->loadModel('InvoiceLayout')->hasAny(array('InvoiceLayout.site_id' => $id));

        $limits = $this->getLimits($id);

        $invoicesLimit = $limits['invoices']['percent'] > 80;
        $estimatesLimit = $limits['estimates']['percent'] > 80;

        $documentsLimit = $limits['documents']['percent'] > 99;
        $subscriptionsLimit = $limits['subscriptions']['percent'] > 99;
        $clientsLimit = $limits['clients']['percent'] > 99;

        return array_filter(
                compact('smtp', 'logo', 'firstInvoice', 'taxes', 'paymentGateways', 'defaultInvoiceLayout', 'invoicesLimit', 'estimatesLimit', 'clientsLimit', 'subscriptionsLimit', 'documentsLimit')
        );
    }

    function isBranded($site_id = false) {
        if (PHP_SAPI == 'cli')  return false;
       if ($site_id) {
           $planId = $this->field('plan_id', array('Site.id' => $site_id));
       } else {
           $planId = getCurrentSite('plan_id');
       }
      if($planId!="1"){
          return false;
      }else{
          return true;
      }
   }

    function validateDomain($param) {
        $patterns = array(
            'ip' => '(?:(?:25[0-5]|2[0-4][0-9]|(?:(?:1[0-9])?|[1-9]?)[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|(?:(?:1[0-9])?|[1-9]?)[0-9])',
            'hostname' => '(?:[a-z0-9][-a-z0-9]*\.)*(?:[a-z0-9][-a-z0-9]{0,62})\.(?:(?:[a-z]{2}\.)?[a-z]{2,4}|museum|travel)'
        );

        foreach ($patterns as $p) {
            if (preg_match('#' . $p . '#', $param['subdomain'])) {
                return true;
            }
        }

        return false;
    }

    function saveFirstSettings($data,$removeValidation=[]) {
        
        $required = array('rule' => 'notEmpty', 'message' => __('Required', true), 'required' => true);

        $this->validate = array(
            'first_name' => $required,
            'last_name' => $required,
            'address1' => $required,
            'city' => $required, //'state' => $required,
//            'postal_code' => $required,
            // 'phone2' => $required,
            'site_logo' => [
                [
                    'rule' => 'validateImage',
                    'message' => 'Please upload a valid image file (jpg, png, gif, webp).'
                ]
            ],
            'country_code' => array($required, array('rule' => 'checkCountry', 'allowEmpty' => false, 'message' => __('Invalid country', true))),
            'timezone' => array($required, array('rule' => 'checkTimezone', 'allowEmpty' => false, 'message' => __('Invalid timezone', true))),
            'currency_code' => array($required, array('rule' => 'checkCurrency', 'allowEmpty' => true, 'message' => __('Invalid currency', true)))
        );
        foreach($removeValidation as $v){
            unset($this->validate[$v]);
        }
        if ($data['Site']['country_code'] == 'US') {

            $data['Site']['enable_po'] = true;
            $data['Site']['date_format'] = 3;
        }
        else
            $data['Site']['date_format'] = 0;

        if(!empty($data['Site']['bn1'])){
        $this->loadModel('CountryMain');
        $data['Site']['invoice_default_title'] = $this->CountryMain->field('default_invoice_title', array('CountryMain.code' => $data['Site']['country_code']));
        }else{
        $data['Site']['invoice_default_title']=__("Invoice",true);    
        }
        $data['Site']['theme_color'] = '247dbd';
        $data['Site']['font_color'] = 'ffffff';
        $data['Site']['first_login'] = 2;
        $data['Site']['id'] = getAuthOwner('id');

        if (isset($data['Site']['invoicing_method']) && ($data['Site']['invoicing_method'] !== ""))
            settings::setValue(InvoicesPlugin, 'invoicing_method', $data['Site']['invoicing_method']);

        if (isset($data['Site']['sold_item_type']) && ($data['Site']['sold_item_type'] !== ""))
            settings::setValue(InvoicesPlugin, 'sold_item_type', $data['Site']['sold_item_type']);


        App::import('Vendor', 'notification_2');
        App::import('Core', 'Sanitize');
		$staff_id = getAuthOwner('staff_id');
        if (settings::getValue(InvoicesPlugin, 'invoicing_method') != settings::OPTION_INVOICING_METHOD_PRINT) {
			if($staff_id == null)
				$staff_id = 0;
            NotificationV2::add_notification(NotificationV2::NOTI_NO_SMTP_SETTING, 0,[$staff_id], NotificationV2::NOTI_USER_TYPE_STAFF);
        }
        $data['Site']['first_name'] = str_replace('<', '&lt;', $data['Site']['first_name']);
        $data['Site']['last_name'] = str_replace('<', '&lt;', $data['Site']['last_name']);
        $data['Site']['address1'] = str_replace('<', '&lt;',$data['Site']['address1']);
        $data['Site']['address2'] = str_replace('<', '&lt;',$data['Site']['address2']);
        $data['Site']['city'] = str_replace('<', '&lt;',$data['Site']['city']);
        $data['Site']['state'] = str_replace('<', '&lt;',$data['Site']['state']);
//        $data['Site']['phone2'] = Sanitize::html($data['Site']['phone2']);
//        $data['Site']['phone1'] = Sanitize::html($data['Site']['phone1']);

//        NotificationV2::add_notification(NotificationV2::NOTI_NO_ONLINE_PAYMENT_OPTIONS, 0,[$staff_id], NotificationV2::NOTI_USER_TYPE_STAFF);





//        if (empty($_FILES)){
//            NotificationV2::add_notification(NotificationV2::NOTI_NO_LOGO_UPLOADED, 0,[$staff_id], NotificationV2::NOTI_USER_TYPE_STAFF);
//        }
        unset($data['Site']['password']);
//        if (SiteService::siteLogoUploadError($data['Site'])) {
//            unset($this->data[$this->name]['site_logo']);
//            $this->data[$this->name]['error_upload_logo'] = true;
//        }
        //$data = $this->invokeBehaviorBeforeSave('Site', 'image', $data);
        SiteService::updateById(getCurrentSite('id'), $data['Site']);
        $user = $this->findById(getCurrentSite('id'));

        if ($user) {

            // SiteUserService::reassignCrmUser($user['Site']['user_id'], $user['Site']['country_code']);
            SiteUserService::updateVersion();

            if (!empty($data['Site']['country_code'])) {
                $this->loadModel('CountryTax');
                $taxes = $this->CountryTax->find('all', array('conditions' => array('CountryTax.country_code' => $data['Site']['country_code'])));
                if (!empty($taxes)) {
                    $taxModel = $this->loadModel('Tax');
                    foreach ($taxes as $tax) {
                        $taxes_data[]['Tax'] = array('name' => __($tax['CountryTax']['name'], true), 'site_id' => $data['Site']['id'], 'value' => $tax['CountryTax']['value'], 'included' => $tax['CountryTax']['included']);
                    }
                    debug($taxes_data);
                    $taxModel->saveAll($taxes_data);
                }
//                else
//                    NotificationV2::add_notification(NotificationV2::NOTI_NO_TAXES_ADDED, 0,[$staff_id], NotificationV2::NOTI_USER_TYPE_STAFF);
            }



            $siteId = getCurrentSite('id');
            $pgModel = $this->loadModel('SitePaymentGateway');
            /* @var $pgModel SitePaymentGateway */
            $pgModel->initiateGateways($user['Site']['id'],$user['Site']['country_code']);
            $pgModel->updateAll(array('active' => 1, 'default' => 1, 'disable_for_client' => 1), array('SitePaymentGateway.site_id' => $siteId, 'SitePaymentGateway.payment_gateway' => 'offline'));
            $pgModel->updateAll(array('active' => 0, 'default' => 0), array('SitePaymentGateway.site_id' => $siteId, 'SitePaymentGateway.payment_gateway !=' => 'offline'));

            $this->loadModel('Plan');
            $dbPlan = $this->Plan->findById(1);
            $plan = $dbPlan['Plan'];

            SiteLimitService::create(
                array(
                    'owner_id' => $siteId,
                    'client_limit' => $plan['client_limit'],
                    'invoice_limit' => $plan['invoice_limit'],
                    'subscription_limit' => $plan['subscription_limit'],
                    'estimate_limit' => $plan['estimate_limit'],
                    'custom_layout_limit' => $plan['custom_layout_limit'],
                    'unbranded_invoice' => $plan['unbranded_invoice'],
                    'unbranded_email' => $plan['unbranded_email'],
                ));

            $this->copyModelTo($siteId);

            $result = array('status' => 'true', 'user' => $user,'error_upload_logo'=>isset($this->data[$this->name]['error_upload_logo'])?true:false);
        } else {
            $result = array('status' => false,'error_upload_logo'=>isset($this->data[$this->name]['error_upload_logo'])?true:false);
        }

        return $result;
    }
/**
* install country tax to current site
* @param array $site
* @return void
*/

function validateImage($check) {
    // $check looks like: array('attachments' => array(file info))
    $file = array_values($check);
    $file = $file[0]; // this is the uploaded file array

    // If no file uploaded, you can decide whether it's required or not
    if ($file['error'] === UPLOAD_ERR_NO_FILE) {
        return true; // or return false if file is mandatory
    }

    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    // Get real MIME type
    $fileType = mime_content_type($file['tmp_name']);
    $allowed = array('image/jpeg', 'image/png', 'image/gif', 'image/webp','image/jpg', 'image/tif', 'image/svg');

    return in_array($fileType, $allowed);
}

function install_tax($site=array()){
            if (!empty($site['id']) and !empty($site['country_code'])) {
                $this->loadModel('CountryTax');
                $taxes = $this->CountryTax->find('all', array('conditions' => array('CountryTax.country_code' => $site['country_code'])));
                if (!empty($taxes)) {
                    $taxModel = $this->loadModel('Tax');
                    foreach ($taxes as $tax) {
                        $taxes_data[]['Tax'] = array('name' => $tax['CountryTax']['name'], 'site_id' => $site['id'], 'value' => $tax['CountryTax']['value'], 'included' => $tax['CountryTax']['included']);
                    }
                    debug($taxes_data);
                    $taxModel->saveAll($taxes_data);
                }
            }	
	
}
 function copyModelTo($newSiteId) {

        $site = $this->findById($newSiteId);
        if (!$site || $site['Site']['model']) {
            return false;
        }

        $modelSite = $this->find(array('Site.model' => 1, 'Site.country_code' => $site['Site']['country_code']));
        if (!$modelSite) {
            $modelSite = $this->find(array('Site.model' => 1, 'OR' => array('Site.country_code' => '', 'Site.country_code IS NULL')));
            if (!$modelSite) {
                return false;
            }
        }

        $foreignModels = array('EmailTemplate', 'Tax', 'Term', 'SavedReport');
        foreach ($foreignModels as $fModel) {
            $foreignModel = $this->loadModel($fModel);
            if (!$foreignModel->hasAny(array("$fModel.site_id" => $newSiteId))) {
                $items = $foreignModel->find('all', array('conditions' => array("$fModel.site_id" => $modelSite['Site']['id'])));

                foreach ($items as &$item) {
                    unset($item[$fModel]['id']);
                    $item[$fModel]['site_id'] = $newSiteId;
                }
                $foreignModel->saveAll($items);
            }
        }

        $layoutModel = $this->loadModel('InvoiceLayout');
        /* @var $layoutModel InvoiceLayout */
        $layouts = $layoutModel->find('all', array('conditions' => array('InvoiceLayout.site_id' => $modelSite['Site']['id']), 'recursive' => 1));
        foreach ($layouts as &$layout) {
            unset($layout['InvoiceLayout']['id']);
            $layout['InvoiceLayout']['site_id'] = $newSiteId;
            foreach ($layout['InvoiceLayoutCustomField'] as &$field) {
                unset($field['id'], $field['invoice_layout_id']);
            }

            foreach ($layout['InvoiceLayoutTag'] as &$tag) {
                unset($tag['id'], $tag['invoice_layout_id']);
            }

            $layoutModel->saveAll($layout);
        }

        return true;
    }

    function get_document_size_limit($site_id = false) {
        if (!$site_id) {
            $site = getCurrentSite();
            $site_id = $site['id'];
        } else {
            $dbSite = $this->find(array('Site.id' => $site_id));
            $site = $dbSite['Site'];
        }

        $documentLimit = $this->SiteLimit->field('document_size', array('SiteLimit.owner_id' => $site_id));
        if (!$documentLimit) {
            $documentLimit = $this->Plan->field('document_size', array('Plan.id' => $site['plan_id']));
            if (!$documentLimit) {
                $documentLimit = 5;
            }
        }

        return $documentLimit * 1024;
    }

    function get_terms_size_limit($site_id = false) {
        if (!$site_id) {
            $site = getCurrentSite();
            $site_id = $site['id'];
        } else {
            $dbSite = $this->find(array('Site.id' => $site_id));
            $site = $dbSite['Site'];
        }

        $termsLimit = $this->SiteLimit->field('terms_size', array('SiteLimit.owner_id' => $site_id));
        if (!$termsLimit) {
            $termsLimit = $this->Plan->field('terms_size', array('Plan.id' => $site['plan_id']));
            if (!$termsLimit) {
                $termsLimit = 5;
            }
        }

        return $termsLimit * 1024;
    }

    function get_logo_size_limit($site_id = false) {
        if (!$site_id) {
            $site = getCurrentSite();
            $site_id = $site['id'];
        } else {
            $dbSite = $this->find(array('Site.id' => $site_id));
            $site = $dbSite['Site'];
        }

        $logoLimit = $this->SiteLimit->field('logo_size', array('SiteLimit.owner_id' => $site_id));
        if (!$logoLimit) {
            $logoLimit = $this->Plan->field('logo_size', array('Plan.id' => $site['plan_id']));
            if (!$logoLimit) {
                $logoLimit = 10;
            }
        }

        return $logoLimit * 1024;
    }

    function get_attachment_size_limit($site_id = false) {
        if (!$site_id) {
            $site = getCurrentSite();
            $site_id = $site['id'];
        } else {
            $dbSite = $this->find(array('Site.id' => $site_id));
            $site = $dbSite['Site'];
        }

        $attachmentLimit = $this->SiteLimit->field('attachment_size', array('SiteLimit.owner_id' => $site_id));
        if (!$attachmentLimit) {
            $attachmentLimit = $this->Plan->field('attachment_size', array('Plan.id' => $site['plan_id']));
            if (!$attachmentLimit) {
                $attachmentLimit = 10;
            }
        }


        return $attachmentLimit * 1024;
    }

    function get_image_max_size() {
        return defined('LOGO_SIZE') ? LOGO_SIZE : '20000';
    }

    function get_image_max_size_mb() {
        return defined('LOGO_SIZE_MB') ? LOGO_SIZE_MB : '20MB';
    }

    function getLimits($site_id = false) {
        if (!$site_id) {
            $site_id = getCurrentSite('id');
        }

        $watchesModel = $this->loadModel('OwnerWatch');
        $dbWatches = $watchesModel->find('first');
        $watches = $dbWatches['OwnerWatch'];
		$this->loadModel('SiteLimit');
		$this->loadModel('SiteLimitation');
		$this->loadModel('Staff');

        $siteLimits = $this->SiteLimit->find(array('SiteLimit.owner_id' => $site_id));
        $siteLimitations = $this->SiteLimitation->find(array('SiteLimitation.site_id' => $site_id, 'SiteLimitation.name' => 'employee_limit'));
        $plan_id = $this->field('plan_id', array('Site.id' => $site_id));
        if ($siteLimitations) {
            $siteLimitations = array_merge(
                $siteLimitations['SiteLimitation'],
                ['active_employees' => $this->Staff->activeEmployeesCount()]
            );
        }
        if ($siteLimits) {
            $limits = $siteLimits['SiteLimit'];

        } else {
            $this->loadModel('Plan');
            $plan = $this->Plan->find(array('Plan.id' => $plan_id));
            $limits = $plan['Plan'];
        }

        if ($plan_id == 1) {
            $this->loadModel('Client');
            $watches['clients'] = $this->Client->find('count');
        }

        // get staff(users) and branches count on fly
        $users_and_branches = $this->activeUsersAndBranchesCount();
     
        $staff_percent=($limits['included_staff']+$limits['staff_count']) !=0 ? $users_and_branches / ($limits['included_staff']+$limits['staff_count']):0;
        
        $results = array(
            'invoices' => array('limit' => $limits['invoice_limit'], 'current' => $watches['invoices'], 'percent' => round($watches['invoices'] / $limits['invoice_limit'], 2) * 100),
            'clients' => array('percent' => round($watches['clients'] / $limits['client_limit'], 2) * 100, 'current' => $watches['clients'], 'limit' => $limits['client_limit']),
            'estimates' => array('percent' => round($watches['estimates'] / $limits['estimate_limit'], 2) * 100, 'current' => $watches['estimates'], 'limit' => $limits['estimate_limit']),
            'subscriptions' => array('percent' => round($watches['subscriptions'] / $limits['subscription_limit'], 2) * 100, 'current' => $watches['subscriptions'], 'limit' => $limits['subscription_limit']),
            'invoice_layouts' => array('percent' => round(($watches['layouts']) / $limits['custom_layout_limit'], 2) * 100, 'current' => $watches['layouts'], 'limit' => $limits['custom_layout_limit']),
            'documents' => array('percent' => round($watches['documents'] / $limits['documents'], 2) * 100, 'current' => $watches['documents'], 'limit' => $limits['documents']),
            'terms' => array('percent' => round($watches['terms'] / $limits['terms'], 2) * 100, 'current' => $watches['terms'], 'limit' => $limits['terms']),
            'staff' => array('percent' => round($staff_percent, 2) * 100, 'current' => $users_and_branches, 'limit' => $limits['included_staff']+$limits['staff_count']),
            'employees' => array('percent' => round($siteLimitations['active_employees'] / ($siteLimitations['max_value']), 2) * 100, 'current' => $siteLimitations['active_employees'] ?: 0, 'limit' => $siteLimitations['max_value'] ?: 0),
        );

        foreach ($results as &$result) {
            if ($result['percent'] > 100) {
                $result['percent'] = 100;
            }
        }

        return $results;
    }

    public function activeUsersAndBranchesCount() {
        $this->loadModel('Staff');

        $users_current = $this->Staff->getStaffCount(['type' => 'user', 'active' => 1]);
        $branches_current = 0;

        if (ifPluginActive(BranchesPlugin)) {
	        $this->loadModel('Branch');
	        $branches_current = $this->Branch->getBranchesCount(['status' => 1]);
        }

        return $users_current + $branches_current;
    }

    function IsSuspended($site) {
        if ($site["status"] != SITE_STATUS_STOPPED && $site["status"] != SITE_STATUS_DELETED && $site["status"] != SITE_STATUS_EXPIRED) {
            return false;
        } else {

            $dbSite = $this->findById($site["id"]);

            if ($dbSite['Site']["status"] != SITE_STATUS_STOPPED && $site["status"] != SITE_STATUS_DELETED && $site["status"] != SITE_STATUS_EXPIRED) {
                return false;
            }
            $gp=Grace_Period+(int)$dbSite['Site']['tolerant'];
            
            $str=strtotime($dbSite['Site']["expiry_date"]." +$gp day");
            if (date("Y-m-d",$str) > date('Y-m-d') || $dbSite['Site']["plan_id"] < 2){
                return false;
            }
            
        }
        return true;
    }

    /**
     * @param $subDomain
     * @return bool
     */
    public function isFreeAndCreatedFromMoreThanOneMonth($subDomain)
    {
        // escaping from replacement in testing
        // Note: this if condition is ignored because sites on testing served on "daftara" so it needs to be replaced with short domain
//        if (!in_array(getenv('APP_ENV'), ['webtesting'])) {
            $subDomain = str_replace(Beta_Domain, Domain_Short_Name, $subDomain);
//        }

        $site = $this->find("first", ["conditions" => ["subdomain" => $subDomain]]);
        if (isset($site["Site"]) && isset($site["Site"]["plan_id"]) && $site["Site"]["plan_id"] != 1) {
            return false;
        }
        $createdDate = isset($site["Site"]["created"]) ? $site["Site"]["created"] : null;
        $createdDate = date("Y-m-d", strtotime("+1 months", strtotime($createdDate)));
        return $createdDate < date("Y-m-d");
    }

    public function get_site_positions(){
        return [
            '' => __("Select your position", true),
            CEO_OWNER => __("CEO/Owner", true),
            ACCOUNTING_OFFICER => __("Accounting Officer", true),
            SALES_OFFICER => __("Sales Officer", true),
            HR_OFFICER => __("HR Officer", true),
            INVENTORY_OFFICER => __("Inventory Officer", true),
            CLIENTS_RELATIONSHIP_OFFICER => __("Clients Relationship Officer", true),
            OPERATIONS_OFFICER => __("Operations Officer", true),
            ERP_IMPLEMNTER_CONSULTANT => __("ERP Implementer / Consultant", true),
            OTHER => __("Other", true)
        ];
    }

    public function get_site_company_size(){
        return [
            '' => __("Please Select", true),
            0 => __("Self-Employed", true),
            1 => sprintf(__("%s Employees", true), "1-5"),
            6 => sprintf(__("%s Employees", true), "6-10"),
            11 => sprintf(__("%s Employees", true), "11-50"),
            51 => sprintf(__("%s Employees", true), "51-200"),
            201 => sprintf(__("%s Employees", true), "201-500"),
            501 => sprintf(__("More than %s %s", true), "500" , __("Employee", true))
        ];
    }

    public function activate_plugins_depending_on_position_and_company_size(){
        $this->reload_session();
        $user = getCurrentSite();
        if($user['position'] == ACCOUNTING_OFFICER){
            $this->owner_update_plugin(ExpensesPlugin, "true", false, 0, false);
            $this->owner_update_plugin(AccountingPlugin, "true", false, 0, false);
            $account_routes = [
                'payable_notes_account_routing' => 2,
                'receivable_notes_account_routing' => 2,
                'staff_account_routing' => 2,
                'treasuries_accounts_routing' => 2,
                'stores_accounts_routing' => 2,
                'suppliers_accounts_routing' => 2,
                'purchases_accounts_routing' => 2,
                'purchase_returns_accounts_routing' => 2,
                'purchases_adjustment_accounts_routing' => 2,
                'clients_accounts_routing' => 2,
                'sales_accounts_routing' => 2,
                'returns_accounts_routing' => 2,
                'product_sales_accounts_routing' => 2,
                'adjustment_accounts_routing' => 2,
                'discount_allowed_accounts_routing' => 1,
                'discount_received_accounts_routing' => 1,
                'retained_earnings_accounts_routing' => 1
            ];
            foreach($account_routes as $key => $value){
                settings::setValue(AccountingPlugin, $key, $value);
            }
        }
        if($user['position'] == HR_OFFICER || $user['company_size'] > 5){
            $this->owner_update_plugin(StaffPlugin, "true", false, 0, false);
            $this->owner_update_plugin(HRM_PLUGIN, "true", false, 0, false);
            $this->owner_update_plugin(HRM_ATTENDANCE_PLUGIN, "true", false, 0, false);
            $this->owner_update_plugin(HRM_PAYROLL_PLUGIN, "true", false, 0, false);
        }
        if(in_array($user['country_code'], ['US', 'CA', 'GB', 'AU', 'NZ'])){
            settings::setValue(0, 'negative_currency_formats', 'negative_currency_format_currency_with_parenthesis');
        }
    }
}
