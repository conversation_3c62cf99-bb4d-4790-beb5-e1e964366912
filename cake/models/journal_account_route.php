<?php

use App\Domain\JournalRoute\Savers\RouteSaverFactory;

App::import('Vendor', 'settings');

class JournalAccountRoute extends AppModel {
    var $applyBranch = ['onFind' => true, 'onSave' => true];
	var $name = 'JournalAccountRoute';
	var $filters = array();

    CONST AUTO_ACCOUNT_TYPE_DYNAMIC = 'dynamic';
    CONST AUTO_ACCOUNT_TYPE_FIXED = 'fixed';
    CONST AUTO_ACCOUNT_TYPE_FORM = 'form';

    static $journalAccountsRoutingData = [];
    public $accountType = null;
    public $routingEntityData = [];
    public $routingEntityname = '';
	function __construct($id = false, $table = null, $ds =null){
		parent::__construct($id, $table, $ds);
		$this->validate = array();
		$this->Journal = GetObjectOrLoadModel('Journal');
		$this->JournalAccount = GetObjectOrLoadModel('JournalAccount');
		$this->JournalCat = GetObjectOrLoadModel('JournalCat');
        self::$journalAccountsRoutingData = [

            Journal::RETURNS_ACCOUNT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Returns',true) ,'entity_type' => Journal::RETURNS_ACCOUNT_ENTITY_TYPE],
            Journal::SALES_ACCOUNT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Sales',true), 'entity_type' => Journal::SALES_ACCOUNT_ENTITY_TYPE],
            Journal::PRODUCT_SALES_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Product Sales Account',true), 'entity_type' => Journal::PRODUCT_SALES_ENTITY_TYPE],
            Journal::ADJUSTMENT_ACCOUNT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Adjustment Routing',true), 'entity_type' => Journal::ADJUSTMENT_ACCOUNT_ENTITY_TYPE],
            Journal::PURCHASES_ADJUSTMENT_ACCOUNT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Adjustment Routing',true), 'entity_type' => Journal::PURCHASES_ADJUSTMENT_ACCOUNT_ENTITY_TYPE],
            Journal::DISCOUNT_ALLOWED_ENTITY_TYPE  => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Discount Allowed',true), 'entity_type' => Journal::DISCOUNT_ALLOWED_ENTITY_TYPE],
            Journal::CLIENTS_CAT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT, 'name' => __('Clients',true), 'entity_type' => Journal::CLIENTS_CAT_ENTITY_TYPE],

            Journal::SUPPLIERS_CAT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT, 'name' => __('Suppliers',true), 'entity_type' => Journal::SUPPLIERS_CAT_ENTITY_TYPE],
            Journal::PURCHASES_ACCOUNT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Purchases',true), 'entity_type' => Journal::PURCHASES_ACCOUNT_ENTITY_TYPE],
            Journal::DISCOUNT_RECEIVED_ENTITY_TYPE  => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Discount Received',true), 'entity_type' => Journal::DISCOUNT_RECEIVED_ENTITY_TYPE],
            Journal::PURCHASE_RETURNS_ACCOUNT_ENTITY_TYPE => ['settings_entity_type' => Journal::PURCHASES_ACCOUNT_ENTITY_TYPE, 'account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Purchase Returns',true), 'entity_type' => Journal::PURCHASE_RETURNS_ACCOUNT_ENTITY_TYPE],



            Journal::STORES_CAT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT, 'name' => __('Warehouses',true), 'entity_type' => Journal::STORES_CAT_ENTITY_TYPE ],
            Journal::BANKS_CAT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT, 'name' => __('Banks',true), 'entity_type' => Journal::BANKS_CAT_ENTITY_TYPE, ],
            Journal::TREASURIES_CAT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT, 'name' => __('Treasuries',true), 'entity_type' =>Journal::TREASURY_ACCOUNT_ENTITY_TYPE],

            Journal::RETAINED_EARNINGS_ACCOUNT_ENTITY_TYPE => ['account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT, 'name' => __('Retained Earnings Routing',true), 'entity_type' => Journal::RETAINED_EARNINGS_ACCOUNT_ENTITY_TYPE],
        ];
	}

	function setAccountType($accountType)
    {
        $this->accountType = $accountType;
    }

    function setRoutingEntityData($entityName)
    {
        $this->routingEntityname = $entityName;
        $this->routingEntityData = self::$journalAccountsRoutingData[$entityName];
        $this->setAccountType($this->routingEntityData['account_type']);
    }


    function addAdjustmentJournalTransactionForPos(&$transactionArray)
    {
        $accountRoutingSetting = settings::getValue(AccountingPlugin, 'adjustment_accounts_routing');
        switch ($accountRoutingSetting) {
            case settings::MANUAL_ACCOUNTS_ROUTING:
                $this->addDefaultAdjustmentAccount($transactionArray);
                return $transactionArray;
            case settings::AUTOMATIC_ACCOUNTS_ROUTING:
                $adjustmentRecord = $this->find('first', ['recursive' => -1, 'conditions' => ['entity_type' => 'adjustment', 'entity_id' => 0, 'is_transaction' => 0]]);
                if ($adjustmentRecord) {
                    $transactionArray['journal_account_id'] = $adjustmentRecord['JournalAccountRoute']['account_id'];
                    return $transactionArray;
                }
                $this->addDefaultAdjustmentAccount($transactionArray);
                return $transactionArray;
            default:
                return $transactionArray;
        }
    }

    function addDefaultAdjustmentAccount(&$journal_transaction) {
        $defaultAdjustmentAccount = $this->Journal->get_auto_account(['entity_type' => 'discount_allowed', 'entity_id' => 0]);
        if (!$defaultAdjustmentAccount) {
            $journal_transaction['auto_account'] = $this->Journal->create_auto_account(['entity_type' => 'discount_allowed', 'entity_id' => 0]);
            return $journal_transaction;
        }
        $journal_transaction['journal_account_id'] = $defaultAdjustmentAccount['JournalAccount']['id'];
        return $journal_transaction;
    }



	function getFilters(){
				return array();//set filters here
		}

    function getRelatedEntities($groupEntityName)
    {
        $relatedEntities = [];
        switch ($groupEntityName){
            case 'sales':
                $relatedEntities =  [Journal::CLIENTS_CAT_ENTITY_TYPE, Journal::SALES_ACCOUNT_ENTITY_TYPE, Journal::RETURNS_ACCOUNT_ENTITY_TYPE, Journal::DISCOUNT_ALLOWED_ENTITY_TYPE, Journal::PRODUCT_SALES_ENTITY_TYPE, Journal::ADJUSTMENT_ACCOUNT_ENTITY_TYPE];
                break;
            case 'purchases':
                $relatedEntities =  [Journal::SUPPLIERS_CAT_ENTITY_TYPE, Journal::PURCHASES_ACCOUNT_ENTITY_TYPE, Journal::PURCHASE_RETURNS_ACCOUNT_ENTITY_TYPE, Journal::DISCOUNT_RECEIVED_ENTITY_TYPE, Journal::PURCHASES_ADJUSTMENT_ACCOUNT_ENTITY_TYPE];
                break;
            case 'inventories':
                $relatedEntities =  [Journal::STORES_CAT_ENTITY_TYPE];
                break;
            case 'treasuries':
                $relatedEntities =  [Journal::TREASURIES_CAT_ENTITY_TYPE];
                break;
            case 'banks':
                $relatedEntities =  [Journal::BANKS_CAT_ENTITY_TYPE];
                break;
            case 'fiscal_years':
                $relatedEntities =  [Journal::RETAINED_EARNINGS_ACCOUNT_ENTITY_TYPE];
                break;

        }
        return $relatedEntities;
    }

    /**
     * @param $entityName
     * @return bool|mixed
     */
    function getMainAccount($entityDetails)
    {
        $journalCat = false;
        $conditions =  ['JournalAccountRoute.account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT ,'JournalAccountRoute.entity_type' => $entityDetails['entity_type']];
        if($entityDetails['entity_id'])
        {
            $conditions['JournalAccountRoute.entity_id'] = $entityDetails['entity_id'];
        }
        $routedAccount = $this->find('first', ['conditions' => $conditions]);
        if(!$routedAccount && ifPluginActive(BranchesPlugin))
        {
            $conditions['JournalAccountRoute.branch_id'] = getMainBranch('id');
            $routedAccount = $this->find('first', ['conditions' => $conditions]);
        }
        if($routedAccount)
        {
            $journalCat = $this->JournalCat->find('first', ['conditions' => ['JournalCat.id' => $routedAccount['JournalAccountRoute']['account_id']]]);
        }

        return $journalCat;
    }

    /**
     * @param $entityName
     * @return bool|mixed
     */
    function getSubAccount($entityName, $entityId = null)
    {
        $conditions = ['JournalAccountRoute.entity_type' => $entityName];
        if(!empty($entityId) || $entityId === 0 )
        {
            $conditions['JournalAccountRoute.entity_id'] = $entityId;
        }
        return $this->find('first', ['conditions' => $conditions]);
    }



    /**
     * @param $entity
     * @return mixed|null the entity account routing type
     */
    public function getEntityAccountRoutingType()
    {
        $entitySettingsName = $this->routingEntityname . '_accounts_routing';
        $routingType = Settings::getValue(AccountingPlugin, $entitySettingsName);
        return $routingType;
    }

    public function getRoutingOptions()
    {

        return Settings::getOptions(AccountingPlugin, settings::getEntityRoutingName($this->routingEntityname));
    }

    public function getAccount($entityId = 0)
    {
        $conditions = [
            'JournalAccountRoute.entity_type' => $this->routingEntityname,
            'JournalAccountRoute.entity_id' => $entityId,
            'JournalAccountRoute.account_type' => $this->accountType,
            ];
        return $this->find('first', ['conditions' => $conditions]);
    }

    /**
     * @param $entityType
     * @param $entityId
     * @return bool|mixed
     */
    public function getAccountForForm($entityType, $entityId)
    {
        $conditions = [
            'JournalAccountRoute.entity_type' => $entityType,
            'JournalAccountRoute.entity_id' => $entityId,
            'JournalAccountRoute.account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT,
        ];
        if($entityType == Journal::TREASURY_ACCOUNT_ENTITY_TYPE) {
            $this->applyBranch = ['onFind' => false, 'onSave' => false];
        }else {
            $this->applyBranch = ['onFind' => true, 'onSave' => true];
        }
        $route = $this->find('first', ['conditions' => $conditions]);
        if(!$route && ifPluginActive(BranchesPlugin))
        {
            $conditions['JournalAccountRoute.branch_id'] = getMainBranch('id');
            $route = $this->find('first', ['conditions' => $conditions]);
        }
        return $route;
    }
    public function getRoutedAccount( $entityId = 0, $settings = false)
    {

        switch ($this->accountType ){
            case JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT:
                $routedAccount = $this->Journal->getAutoCat(['entity_type' => $this->routingEntityname]);
                break;
            case JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT:
                $entityType = $this->routingEntityname;
                    $settingsKey = Settings::getEntityRoutingName($entityType);
                    $value = Settings::getValue(AccountingPlugin, $settingsKey);
                    if($settings && !empty($this->routingEntityData['settings_entity_type']) && empty($value)){
                        $entityType = $this->routingEntityData['settings_entity_type'];
                    }
                    $routedAccount = $this->Journal->get_auto_account(['entity_type' => $entityType, 'entity_id' => $entityId]);
            default:

                break;
        }
        return $routedAccount;
    }


    public function deleteRoutingAccount($entityId = 0)
    {
        return $this->deleteAll(
            [
                'JournalAccountRoute.entity_id' => $entityId,
                'JournalAccountRoute.entity_type' => $this->routingEntityname,
                'JournalAccountRoute.account_type' => $this->accountType
            ]);
    }

    public function saveRoutedAccount($data)
    {
        $routeData = $data['JournalAccountRoute'];
        $conditions = [
            'entity_type' => $routeData['entity_type'],
            'entity_id' => $routeData['entity_id'],
//            'transaction_type' => $routeData['transaction_type'],
//            'is_transaction' => $routeData['is_transaction'],
            'account_type' => $routeData['account_type']
        ];
        $routedAccount = $this->find('first', ['conditions' => $conditions]);
        $result = true;
        if($data['JournalAccountRoute']['account_id'])
        {
            //then it want to change the account_id
            if($routedAccount)
            {
                //update
                $this->id = $routedAccount['JournalAccountRoute']['id'];
            }else{
                //create new
                $this->create();
            }
            $result = $this->save($data);

        }else if($routedAccount){
            //then it has a routed account but now he wants to delete it
            $result = $this->delete($routedAccount['JournalAccountRoute']['id']);
        }
        return $result;
    }
    


    /**
     * @param $entitiesData
     */
    public function saveRoutingTypeSettings($entitiesData)
    {
 
        foreach($entitiesData as $entityName => $entityData)
        {
            $routingType = $entityData['routing_type'];
            if(empty($routingType))
            {
                CustomValidationFlash([__('Routing can not be empty',true)]);
                return false;
            }
            $this->setRoutingEntityData($entityName);

            $routedAccount = [
                'JournalAccountRoute' => [
                    'account_type' => $this->accountType,
                    'account_id' => $entityData['account_id'],
                    'entity_id' => ($entityData['entity_id'] ? $entityData['entity_id']:0),
                    'entity_type' => $entityName
                ]
            ];

            if($routingType == Settings::AUTOMATIC_ACCOUNTS_ROUTING)
            {

                //save journal cat routing
                $result = $this->saveRoutedAccount($routedAccount);
                if(!$result)
                {
                    return false;
                }
            }else{
                $this->deleteRoutingAccount( $routedAccount['JournalAccountRoute']['entity_id']);
            }
            $settingsKey = Settings::getEntityRoutingName($entityName);
            if(Settings::getValue(AccountingPlugin, $settingsKey) != $routingType) {
                $array['param2']= $settingsKey;
                if(isset($routedAccount['JournalAccountRoute']['account_id']) && $routingType == Settings::AUTOMATIC_ACCOUNTS_ROUTING) {
                    $array['secondary_id'] = $routedAccount['JournalAccountRoute']['account_id'];
                }
                $array['param1']= Settings::accountRoutingText($routingType);
                $this->add_actionline(ACTION_UPDATE_ROUTING_SETTINGS, $array);
            }
            Settings::setValue(AccountingPlugin, $settingsKey, $routingType);

            // set a new value in settings to handle a new main account 
            if($routingType == Settings::MANUAL_MAIN_ACCOUNTS_ROUTING)
            {  
                Settings::setValue(AccountingPlugin, Settings::CLINET_MAIN_ACCOUNT, $entityData['main_account_id']);
            }



        }

        return true;

    }


    /**
     * @param null $entityType
     * @param null $entityId
     * @param null $accountId
     * @return bool|mixed
     */
    function getJournalAccountRoute($entityType = null, $entityId = null, $accountId = null)
    {
        $conditions = ['JournalAccountRoute.account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT];
        if($entityType)
        {
            $conditions['JournalAccountRoute.entity_type'] = $entityType;
        }
        if($entityId){
            $conditions['JournalAccountRoute.entity_id'] = $entityId;
        }

        if($accountId)
        {
            $conditions['JournalAccountRoute.account_id'] = $accountId;
        }
        $route = $this->find('first', ['conditions' => $conditions]);


        if(!$route && ifPluginActive(BranchesPlugin) && !empty($accountId))
        {
            $route =  $this->find('first', ['conditions' => $conditions, 'applyBranchFind' => false]);
        }else if(!$route && ifPluginActive(BranchesPlugin)){
            $conditions['JournalAccountRoute.branch_id'] = getMainBranch('id');
            $route =  $this->find('first', ['conditions' => $conditions]);
        }

        return $route;
    }

    /**
     * @param null $entityType
     * @param null $entityId
     * @param null $accountId
     * @return bool|mixed
     */
    function getAllJournalAccountRoute($entityType = null, $entityId = null, $accountId = null)
    {
        $conditions = ['JournalAccountRoute.account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT];
        if($entityType)
        {
            $conditions['JournalAccountRoute.entity_type'] = $entityType;
        }

        if($entityId){
            $conditions['JournalAccountRoute.entity_id'] = $entityId;
        }

        if($accountId)
        {
            $conditions['JournalAccountRoute.account_id'] = $accountId;
        }
        return $this->find('all', ['conditions' => $conditions]);
    }



    /**
     * @param null $entityType
     * @param null $entityId
     * @param null $accountId
     * @return bool|mixed
     */
    function getJournalCatRoute($entityType = null, $entityId = null, $accountId = null)
    {
        $conditions = ['JournalAccountRoute.account_type' => JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT];
        if($entityType)
        {
            $conditions['JournalAccountRoute.entity_type'] = $entityType;
        }

        if($entityId){
            $conditions['JournalAccountRoute.entity_id'] = $entityId;
        }

        if($accountId)
        {
            $conditions['JournalAccountRoute.account_id'] = $entityType;
        }

        $route = $this->find('first', ['conditions' => $conditions]);
        if(!$route && ifPluginActive(BranchesPlugin) && !empty($accountId))
        {
            $route = $this->find('first', ['conditions' => $conditions, 'applyBranchFind' => false]);
        }else if(!$route && ifPluginActive(BranchesPlugin))
        {
            $conditions['JournalAccountRoute.branch_id'] = getMainBranch('id');
            $route = $this->find('first', ['conditions' => $conditions, 'applyBranchFind' => false]);
        }
        return $route;
    }

    /**
     * @param $entityDetails
     * @param $formData
     */
    function getAutoJournalAccountAccountRoute($entityDetails, $journalData)
    {
        $data = $journalData;
        $Journal = GetObjectOrLoadModel('Journal');
        $entityType = $entityDetails['entity_type'];
        $entityId = $entityDetails['entity_id'];
        //warning suppress
        $entityAutoAccountType = $entityDetails['type'] ?? '';
        $relatedAlias = $this->Journal->autoJournalRelatedAlias ?? '';
        //here we handle the case of a form submit and a save JournalAccountRoute
        if($entityAutoAccountType == 'fixed' && $entityId == 0 && in_array($journalData['Journal']['entity_type'], RouteSaverFactory::getSupportedFixedTransactions()))
        {
            $entityId = $data['Journal']['entity_id'];
            $data['JournalAccountRoute']['entity_id'] = $entityId;
        }
        unset($data['JournalAccountRoute']['auto_account_type']);
        //warning suppress
        if(array_key_exists('JournalAccountRoute' , $data) && empty($data['JournalAccountRoute']['entity_id']) && $data['JournalAccountRoute']['entity_id'] !== 0 && !empty($entityId))
        {
            //entity id provided
            //here we get the dynamic accounts routes i.e clients and suppliers
            $data['JournalAccountRoute']['entity_id'] = $entityId;
        }
        if($entityType === Journal::SALES_COST_ACCOUNT_ENTITY_TYPE && $relatedAlias === "PurchaseOrder") {
            $Journal = GetObjectOrLoadModel('Journal');
            $purchaseOrder = $Journal->autoJournalRelatedData['PurchaseOrder'];
            /*
             * because sales cost has not routing and
             * as per my discussion with mr azzam
             * so we want the sales cost to use the same
             * routing as the purchases account
             * you can observe this when you
             * select account in the purchase order add
             */

            if($purchaseOrder) {
                $entityType = [Journal::PURCHASES_ACCOUNT_ENTITY_TYPE, Journal::SALES_COST_ACCOUNT_ENTITY_TYPE, Journal::PURCHASE_RETURNS_ACCOUNT_ENTITY_TYPE];
                $entityId = $purchaseOrder['PurchaseOrder']['id'];
            }
        }

        $conditions = [];
        //get the routed account for this entity
        $routingAccount = $this->getAccountForForm($entityType, $entityId);
        if(empty($routingAccount) && $entityAutoAccountType == 'fixed')
        {
            $routingAccount = $this->getAccountForForm($entityType, $data['Journal']['entity_id']);
        }

        if(empty($routingAccount) && $entityAutoAccountType == 'fixed') {
            $entityType = $entityDetails['entity_type'];
            $entityId = $entityDetails['entity_id'];
            $routingAccount = $this->getAccountForForm($entityType, $entityId);
        }

        if($routingAccount){
            $conditions = ['JournalAccount.id' => $routingAccount['JournalAccountRoute']['account_id']];
        }
        return $conditions;
    }


}
?>