<?php
use App\Domain\JournalRoute\Util;


class Asset extends AppModel {
    var $applyBranch = ['onFind' => true, 'onSave' => true];
    const STATUS_INSERVICE = 1 ; 
    const STATUS_INACTIVE = 2 ; 
    const STATUS_DEPRECATED = 3 ; 
    const STATUS_SOLD = 4 ;
	const INTERVAL_MONTH = 1;
	const INTERVAL_DAY = 2;
	const INTERVAL_WEEK = 3;
    var $name = 'Asset';
    var $parent_cat_id= 15 ;
    var $actsAs = array(
        
        'journal' => array(
            'is_journal'=>true,
            'is_account'=>true,
            'entity_type'=>array('asset'),
        ),
    );
    public $hasMany = array(
        'AssetDeprecation' => array('className' => 'AssetDeprecation', 'foreignKey' => 'asset_id','order' => 'AssetDeprecation.date', 'dependant' => true),
        'AssetOperation' => array('className' => 'AssetOperation', 'foreignKey' => 'asset_id','order' => 'AssetOperation.date', 'dependant' => true),
        );

    static function get_status_list ( ) {
        return [
            Asset::STATUS_INSERVICE => __("In Service" , true ),
            Asset::STATUS_INACTIVE => __("Inactive" , true ),
            Asset::STATUS_DEPRECATED => __("Depreciated" , true ),
            Asset::STATUS_SOLD => __("Sold" , true ),
            ];
    }
    function getFilters() {
//        $filters = array(
//            
//            'category' => array('div_class' => 'full-width', 'options' => array('class' => 'selectpicker'), 'more-options' => true),
//            //'email' => array('more-options' => false, 'label' => __('Email Address', true)),
//            'address' => array('type' => 'like', 'more-options' => true),
//            'postal_code' => array('more-options' => true),
//            'country_code' => array('div_class' => 'full-width', 'options' => array('class' => 'selectpicker '), 'more-options' => true, 'label' => __('Country', true), 'empty' => __('[Any Country]', true)),
//        );
        $filters = []  ;
        $filters['name'] = array('type' => 'like' ,  'div_class' => 'full-width', 'more-options' => false, 'label' => __('Name', true));
        $filters['code'] = array('type' => 'like' ,  'div_class' => 'full-width', 'more-options' => false, 'label' => __('Code', true));
        if (ifPluginActive(StaffPlugin)) {
	        $filters['asset_staff_id'] = ['input_type'=>'element','element_name' => 'asset_advanced_staff', 'element_options' => ['input_name' => 'asset_staff_id', 'allow_inactive' => true, 'selected_staff_id' => $_GET['asset_staff_id'] /*warning suppress*/ ?? null,'empty' => __('All', true), 'label' => __('Employee', true)], 'more-options' => false];
        }
        $filters['asset_status'] = array('div_class' => 'full-width', 'options' => array('class' => 'selectpicker'), 'more-options' => false, 'label' => __('Status', true));
        $filters['next_deprecation_date'] = array('label' => __('Next Depreciation Date',true), 'type' => 'date_range', 'from_label' => __('From Next Depreciation Date', true), 'to_label' => __('To Next Depreciation Date', true));
        $filters['asset_location_id'] = array('div_class' => 'full-width', 'options' => array('class' => 'selectpicker'), 'more-options' => true, 'label' => __('Location', true));
        $filters['journal_cat_id'] = array('div_class' => 'full-width', 'options' => array('class' => 'selectpicker'), 'more-options' => true, 'label' => __('Category', true));
        return $filters;
    }
    function __construct($id = false, $table = null, $ds = null) {
        parent::__construct($id, $table, $ds);

        $this->validate = array(
            'name' => array(array('rule' => 'notempty', 'message' => __('Required', true)),
                array('rule' => 'isFieldUnique', 'message' => __('This name already exists', true))
                ) ,
//            'asset_group_id' => array('rule' => 'notempty', 'message' => __('Required', true)) ,
//            'asset_location_id' => array('rule' => 'notempty', 'message' => __('Required', true)) ,
            'in_service_date' => array('rule' => 'notempty', 'message' => __('Required', true)) ,
//            'deprecation_end_date' => array('rule' => 'notempty', 'message' => __('Required', true)) ,
            'asset_purchase_value' => array('rule' => 'notempty', 'message' => __('Required', true)) ,
            'journal_account_id' => array('rule' => 'notempty', 'message' => __('Required', true)) ,
            'journal_cat_id' => array('rule' => 'notempty', 'message' => __('Required', true)) ,
            'purchase_date' => array(
                'validateIsOpenedPeriod' => ['rule' => 'validateIsOpenedPeriod', 'message' => __('You can not add, edit, or delete a transaction in this date %s within a closed period', true)],
                'purchase_date_rule_2' => ['rule' => 'notEmpty', 'message' => __('Required', true)],
            ),
            'quantity' => ['rule' => 'numeric', 'allowEmpty' => true, 'message' => sprintf(__("%s must be a number", true), __('quantity', true))],
            'branch_id' => array(
                'rule' => 'checkBranchActive',
                'message' => __t('You cannot add a transaction in a suspended branch')
            ),
            );
    }

	function getSortFields($sorted_by) {  
		return array(
			array('title' => __('Created Date',true),'field' => 'created', 'default' => 'desc'),
            array('title' => __('Next Depreciation Date',true),'field' => 'next_deprecation_date', 'default' => 'desc'),
		);

    }
    function validateDeprecationData($data){
        $validation_errors = [] ;
        if($data['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_AMOUNT)
        {
            if(empty($data['Asset']['deprecation_period'])){
                $validation_errors['deprecation_period'] = __("Required",true);
            } 
            if(empty($data['Asset']['deprecation_amount']))
            {
                $validation_errors['deprecation_amount'] = __("Required",true);
            }
                
        }else if ( $data['Asset']['deprecation_method'] == AssetDeprecation::METHOD_PERCENTAGE)
        {
            if(empty($data['Asset']['deprecation_period'])){
                $validation_errors['deprecation_period'] = __("Required",true);
            }
            if(empty($data['Asset']['deprecation_percentage']))
            {
                $validation_errors['deprecation_percentage'] = __("Required",true);
            }
        }else if ( $data['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_UNITS)
        {
            if(empty($data['Asset']['cost_per_unit'])){
                $validation_errors['cost_per_unit'] = __("Required",true);
            }
        }
        return (empty($validation_errors)?['valid' => true]:['valid' => false,'validation_errors'=>$validation_errors]) ; 
    }   
    function get_action_line_data( $id ){
        $asset = $this->findById ( $id ) ;
        if ( !empty ( $asset  ) ) {
            $this->loadModel ( 'ActionLine' ) ;
            require_once APP . 'vendors' . DS . 'Timeline.php';
            $timeline_asset= new Timeline('Asset', array('primary_id' => $id));
            
            $asset_depreciation_action_list = $timeline_asset->getAssetDepreciationList();
            $asset_depreciation_action_ids = $this->ActionLine->find( 'list' ,['fields' => 'ActionLine.id' ,'conditions' => ['secondary_id' =>$id , 'action_key' => $asset_depreciation_action_list ] ] );
            if ( empty ( $asset_depreciation_action_ids ) ){
                $asset_depreciation_action_ids = [] ; //to not break array_merge function ;
            }
            debug ( $asset_depreciation_action_ids ) ;
            $asset_action_list = $timeline_asset->getAssetActionsList();
            $asset_action_ids = $this->ActionLine->find( 'list' ,['fields' => 'ActionLine.id' ,'conditions' => ['primary_id' =>$id , 'action_key' => $asset_action_list ] ] );
            if ( empty ( $asset_action_ids ) ){
                $asset_action_ids = [] ; //to not break array_merge function ;
            }
            $all_actions_lists = array_merge ($asset_action_list , $asset_depreciation_action_list );
            $all_action_ids = array_merge ($asset_action_ids ,$asset_depreciation_action_ids);
            return compact('all_actions_lists' , 'all_action_ids');
        }else {
            return ['all_actions_lists'=> NULL  , 'all_action_ids' => NULL ];
        }
            
    }
    function beforeSave ($options = []) {
        parent::beforeSave($options);

        if ( !empty ($this->data['Asset']['deprecation_end_date'] ))
        {
            $this->data['Asset']['deprecation_end_date'] = $this->formatDate($this->data['Asset']['deprecation_end_date']);
        }
        if ( !empty ($this->data['Asset']['last_deprecation_date'] ))
        {
            $this->data['Asset']['last_deprecation_date'] = $this->formatDate($this->data['Asset']['last_deprecation_date']);
        }
        if ( !empty ($this->data['Asset']['next_deprecation_date'] ))
        {
            $this->data['Asset']['next_deprecation_date'] = $this->formatDate($this->data['Asset']['next_deprecation_date']);
        }
        if ( !empty ($this->data['Asset']['in_service_date'] ))
        {
            $this->data['Asset']['in_service_date'] = $this->formatDate($this->data['Asset']['in_service_date']);
        }
        if ( !empty ($this->data['Asset']['purchase_date'] ))
        {
            $this->data['Asset']['purchase_date'] = $this->formatDate($this->data['Asset']['purchase_date']);
        }
        if ( !empty ($this->data['Asset']['next_deprecation_date'] ))
        {
            $this->data['Asset']['next_deprecation_date'] = $this->formatDate($this->data['Asset']['next_deprecation_date']);
        }
        
        if ( empty ($this->data['Asset']['id']) ){
            $this->data['Asset']['staff_id'] = getAuthOwner ('staff_id');
            if ( empty ($this->data['Asset']['asset_current_value'] ) ){
                $this->data['Asset']['asset_current_value'] = $this->data['Asset']['asset_purchase_value'];
            }
            if ( empty ( $this->data['Asset']['next_deprecation_date']))
            {
                $period_count = $this->data['Asset']['deprecation_period'];
                $period = AssetDeprecation::$deprecation_intervals[$this->data['Asset']['deprecation_interval']]['sql_lt'];
                $fromDate = $this->data['Asset']['in_service_date'] ; 
                App::import('Vendor', 'Recurring');
                $this->data['Asset']['next_deprecation_date'] = Recurring::nextDate(
                    $period_count,
                    $period,
                    $fromDate,
                    []
                ); 
            }
            $this->data['Asset']['asset_status'] = Asset::STATUS_INSERVICE;
        }

        if (! empty($this->data['Asset']['tax_1'])) {
            $taxModel = GetObjectOrLoadModel('Tax');
            $tax = $taxModel->findById($this->data['Asset']['tax_1']);
            $this->data['Asset']['tax_1_data'] = json_encode($tax['Tax']);
        }else{
            $this->data['Asset']['tax_1_data'] = null;
        }

        if (! empty($this->data['Asset']['tax_2'])) {
            $taxModel = GetObjectOrLoadModel('Tax');
            $tax = $taxModel->findById($this->data['Asset']['tax_2']);
            $this->data['Asset']['tax_2_data'] = json_encode($tax['Tax']);
        }else{
            $this->data['Asset']['tax_2_data'] = null;
        }

        return true ; 
    }
    function beforeDelete($cascade = true) {
        parent::beforeDelete($cascade);
        $this->loadModel('AssetOperation');
        $this->loadModel('AssetDeprecation');
        $deprecations = $this->AssetDeprecation->find ( 'list' ,['recursive' => -1 , 'conditions' => ['AssetDeprecation.asset_id' => $this->id]]);
        foreach ( $deprecations as $k => $d ) {
            $this->AssetDeprecation->delete($k);
        }
        $operations = $this->AssetOperation->find ( 'list' ,['recursive' => -1 , 'conditions' => ['AssetOperation.asset_id' => $this->id]]);
        foreach ( $operations as $k => $d ) {
            $this->AssetOperation->delete($k);
        }
        $this->delete_auto_accounts ($this , $this->id);
//        $this->delete_auto_journals ($this , $this->id);
        return true;
    }

    function addDepreciation($data, $asset = null, $isAutomatic = false) {
        $validation_errors = null;

        $AssetDeprecationModel = GetObjectOrLoadModel('AssetDeprecation');

        if(empty($asset)){
            $asset = $this->findById($data['AssetDeprecation']['asset_id']);
        }
        if ( !empty ( $data ) ){
            if ( $asset['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_UNITS)
            {
                //we take the cost for the total units to be deprecated 
                $total_units = $data['AssetDeprecation']['cost'] ;
                if($isAutomatic){
                    $data['AssetDeprecation']['cost'] =(float)$data['AssetDeprecation']['cost'];
                }else{
                    $data['AssetDeprecation']['cost'] =(float)$data['AssetDeprecation']['cost'] * (float)$asset['Asset']['cost_per_unit'];
                }
            }
            $data['AssetDeprecation']['deprecation_method'] = $asset['Asset']['deprecation_method'];
            $data['AssetDeprecation']['currency_code'] = $asset['Asset']['asset_currency'];
            
            if ( $asset_deprecation_id = $AssetDeprecationModel->add ( $data , $validation_errors )) {

                $last_deprecation_date = max([strtotime($data['AssetDeprecation']['date']) , strtotime($asset['Asset']['last_deprecation_date']) ] );
                
                $updated_asset = ['Asset' =>[
                    'id' => $asset['Asset']['id'],
                    'asset_current_value' => ($asset['Asset']['asset_current_value'] - $data['AssetDeprecation']['cost'] ) ,
                        'last_deprecation_date' => date('Y-m-d H:i:s' , $last_deprecation_date ),
                    'total_deprecation' => ($asset['Asset']['total_deprecation'] + $data['AssetDeprecation']['cost'] )
                ] ];
                if ( $asset['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_UNITS)
                {
                    $updated_asset['Asset']['deprecation_total_units'] = $asset['Asset']['deprecation_total_units'] + $total_units;
                    
                }
                if ( ($asset['Asset']['asset_current_value'] - $data['AssetDeprecation']['cost'] ) <=0 )
                {
                    $updated_asset['Asset']['asset_status'] = self::STATUS_DEPRECATED;
                }
                $this->save ( $updated_asset );
                $AssetDeprecationModel->add_action(ACTION_ASSET_DEPRECATION_ADD , $asset_deprecation_id);
                return ['status' => true, 'id' => $asset_deprecation_id];
            } else {
                return ['status' => false, 'errors' => $validation_errors];
            }
       }

    }

    /**
     * adds Depreciation to the asset
     * @param mixed $asset Asset return from database OR id of the asset 
     */
    function add_deprecation ( $asset , $deprecation_data = [] ) {
        $this->loadModel ( 'AssetDeprecation');
        if (is_numeric($asset ) ) {
            $asset_data = $this->findById ( $asset);
        }else {
            $asset_data = $asset ; 
        }
        if ( !empty ( $this->data['AssetDeprecation']['cost']))
        {
            $current_deprecation = $this->data['AssetDeprecation']['cost'];
        }
        if ( $asset_data['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_AMOUNT ){
            $this->AssetDeprecation->save  (['AssetDeprecation'=>[
                'asset_id' => $asset_data['Asset']['id'],
                'date' => date('Y-m-d'),
                'deprecation_method' =>$asset_data['Asset']['deprecation_method'] ,
                'period' =>$asset_data['Asset']['deprecaton_period'] ,
                'cost'   =>  ( empty ( $current_deprecation ) ?$asset_data['Asset']['deprecation_amount'] : $current_deprecation )  ,
            ] ] ) ;
            $this->id = $asset_data['Asset']['id'];
//            $this->save ( ['Asset' => [
//                'current_deprecation' => $asset_data['Asset']['deprecation_unit'],
//                'total_deprecation' => $asset_data['Asset']['total_deprecation'] + $asset_data['Asset']['deprecation_unit'],  
//                'last_deprecation_date' => date('Y-m-d'),  
//            ] ]) ;
        }else if ( $asset_data['Asset']['deprecation_method'] == AssetDeprecation::METHOD_PERCENTAGE  ) {
            if ( empty ( $current_deprecation ) ) {
                $current_deprecation = ($asset_data['Asset']['deprecation_unit'] * ($asset_data['Asset']['asset_value'] - $asset_data['Asset']['total_deprecation'] ))/100;
            }
            
            $this->AssetDeprecation->save  (['AssetDeprecation'=>[
                'asset_id' => $asset_data['Asset']['id'],
                'date' => date('Y-m-d'),
                'deprecation_method' =>$asset_data['Asset']['deprecation_method'] ,
                'period' =>$asset_data['Asset']['deprecaton_period'] ,
                'cost'   =>  $current_deprecation,
            ] ] ) ;
            $this->id = $asset_data['Asset']['id'];
//            $this->save ( ['Asset' => [
//                'current_deprecation' => $current_deprecation,
//                'total_deprecation' => $asset_data['Asset']['total_deprecation'] + $current_deprecation,  
//                'last_deprecation_date' => date('Y-m-d'),  
//            ] ]) ;
        }else if ($asset_data['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_UNITS && !empty ( $deprecation_data)) {
            
            $this->AssetDeprecation->save  (['AssetDeprecation'=>[
                'asset_id' => $asset_data['Asset']['id'],
                'date' => date('Y-m-d'),
                'deprecation_method' =>$asset_data['Asset']['deprecation_method'] ,
                'period' =>$asset_data['Asset']['deprecaton_period'] ,
                'cost'   =>  $current_deprecation,
            ] ] ) ;
            $this->Asset->id = $asset_data['Asset']['id']; 
            $this->Asset->save (['Asset' => [
                'deprecation_total_units' => $asset_data['Asset']['deprecation_total_units'] + $deprecation_data['AssetDeprecation']['cost']
            ] ]);
        }
    }

    function updateAssetLastDeprecationDate($asset_id)
    {
        $this->loadModel('AssetDeprecation');
        $this->AssetDeprecation->recursive = -1;
        $deprecation = $this->AssetDeprecation->find('first', ['conditions' => ['AssetDeprecation.asset_id' => $asset_id], 'order' => 'AssetDeprecation.date desc']);
        if (!empty($deprecation)) {
            $last_deprecation_date = $deprecation['AssetDeprecation']['date'];
        } else {
            $asset = $this->findById($asset_id);
            $last_deprecation_date = $asset['Asset']['in_service_date'];
        }
        $this->updateField($asset_id,'last_deprecation_date', $last_deprecation_date);

    }
    
    /**
     * the function to be executed to get the saved journal
     * @param array $data data of the asset 
     * @return boolean
     */
    public function get_journals($data)
    {
      
        
        
        if(empty($data[$this->alias]['id'])) $data[$this->alias]['id']= $this->id;
        
        
        
        if(empty($data[$this->alias]['asset_purchase_value'])) 
            return false;
        
        $entity_type='asset';
        $description = $data[$this->alias]['name'].' '.__("Purchase",true)."\r\n".$data[$this->alias]['description'];
        if(empty($data[$this->alias]['asset_currency']))
        {
                $data[$this->alias]['asset_currency']= getCurrentSite('currency_code');
        }

        $journal['Journal']['date']=$this->formatDate($data[$this->alias]['purchase_date'] ) ;
        $journal['Journal']['entity_type']=$entity_type;
        $journal['Journal']['description']=$description;
        $journal['Journal']['entity_id']=$data[$this->alias]['id'];
        $journal['Journal']['currency_code']=$data[$this->alias]['asset_currency'];
        $this->id = $data[$this->alias]['id'];
       

       
        $journal['JournalTransaction'][0]=
            array(
                    'subkey'=>'account', 
                    'currency_credit'=>(float)$data[$this->alias]['asset_purchase_value'],
                    'currency_code'=>$data[$this->alias]['asset_currency'],
                    'description'=>$description,
                );
        if(!empty($data[$this->alias]['journal_account_id']))
         {
             $journal['JournalTransaction'][0]['journal_account_id']=$data[$this->alias]['journal_account_id'];
         }
         else 
         {
             $journal['JournalTransaction'][0]['journal_account_id']=$this->default_account_id;
//             $journal['JournalTransaction'][1]['auto_account']=array('type'=>'fixed' , 'entity_type'=> 'asset','entity_id'=>0);
         }
        $journal['JournalTransaction'][1]=
            array(
                    'subkey'=>'asset', 
                    'currency_debit'=>(float)$data[$this->alias]['asset_purchase_value'],
                    'currency_code'=>$data[$this->alias]['asset_currency'],
                    'description'=>$description,
                    'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'asset','entity_id'=>$data[$this->alias]['id'])
                );



        $tax1_total=0;
        if(!empty($data[$this->alias]['tax_1']))
        {

            $tax1_data=json_decode($data[$this->alias]['tax_1_data'],true);

            if(empty($tax1_data['included'])||$tax1_data['included']==0) {
                $tax1_total = (float)$tax1_data['value'] * (float)$journal['JournalTransaction'][1]['currency_debit'] / 100;
                //if tax excluded add it the total purchase value
                $journal['JournalTransaction'][0]['currency_credit']+=$tax1_total;
            }
            else
            {
                $tax1_total = ($tax1_data['value'] / (100 + $tax1_data['value'])) * $journal['JournalTransaction'][1]['currency_debit'];
                //if included reduce the the tax from asset value
                $journal['JournalTransaction'][1]['currency_debit']=$journal['JournalTransaction'][1]['currency_debit']-$tax1_total;
            }

            if(abs($tax1_total)>0)
            {
                $journal['JournalTransaction'][]=
                    array(
                        'subkey'=>'tax_1_'.$tax1_data['id'],
                        'currency_debit'=>$tax1_total,
                        'currency_code'=>$data[$this->alias]['asset_currency'],
                        'description'=>$description,
                        'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'outcome_tax','entity_id'=>$tax1_data['id'])
                    );
//                $this->saveField ( 'tax_1_total' , $tax1_total);
            }
            $this->updateField($this->id,'tax_1_total', $tax1_total);
        } else {
            $this->updateField($this->id,'tax_1_total', 0);
        }

        $tax2_total=0;
        if(!empty($data[$this->alias]['tax_2']))
        {
            $tax2_data=json_decode($data[$this->alias]['tax_2_data'],true);
            if(empty($tax2_data['included'])||$tax2_data['included']==0) {
                $tax2_total = $tax2_data['value'] * $journal['JournalTransaction'][1]['currency_debit'] / 100;
                $journal['JournalTransaction'][0]['currency_credit']+=$tax2_total;
            }
            else
            {
                $tax2_total = ($tax2_data['value'] / (100 + $tax2_data['value'])) * $journal['JournalTransaction'][1]['currency_debit'];
                $journal['JournalTransaction'][1]['currency_debit']=$journal['JournalTransaction'][1]['currency_debit']-$tax2_total;
            }
            if(abs($tax2_total)>0) {
                $journal['JournalTransaction'][] =
                    array(
                        'subkey' => 'tax_2_' . $tax2_data['id'],
                        'currency_debit' => $tax2_total,
                        'currency_code' => $data[$this->alias]['asset_currency'],
                        'description' => $description,
                        'auto_account' => array('type' => 'dynamic', 'entity_type' => 'outcome_tax', 'entity_id' => $tax2_data['id'])
                    );
//                $this->saveField ( 'tax_2_total' , $tax2_total);
            } 
            $this->updateField($this->id,'tax_2_total', $tax2_total);
        } else {
            $this->updateField($this->id,'tax_2_total', 0);
        }

        $operations = $this->count_operations($data[$this->alias]['id']);
        $deprecations= $this->count_deprecations($data[$this->alias]['id']);
        if($deprecations+$operations<2&&$journal['JournalTransaction'][1]['currency_debit']!=$data[$this->alias]['asset_purchase_value'])
        {
            $this->updateField($this->id,'asset_current_value', $journal['JournalTransaction'][1]['currency_debit']);
//            $this->saveField ( 'asset_current_value' , $journal['JournalTransaction'][1]['currency_debit']);
        }

        if($tax2_total>0||$tax1_total>0 || $journal['JournalTransaction'][1]['currency_debit'] != $data[$this->alias]['purchase_value_without_tax']) {
            $this->updateField($this->id,'purchase_value_without_tax', $journal['JournalTransaction'][1]['currency_debit']);
//            $this->saveField ( 'purchase_value_without_tax' , $journal['JournalTransaction'][1]['currency_debit']);
        }
        if(isset($data['Asset']['branch_id'])) {
            $journal['Journal']['branch_id'] = $data['Asset']['branch_id'];
        }
        return $journal;
    }

    public function updateField($id, $field, $value) {
        $query = "update assets set {$field} = '{$value}' where id = {$id}";
        $this->query($query, false);
    }
    /**
     * To be used in form validation of cakephp 
     * @param mixed $param
     * @return bool
     */
    function isFieldUnique($param) {
        

        list($field, $value) =[key($param), current($param)];
        $conditions = array();
//         print_r ( $this->data ) ;
        if (!empty($this->data[$this->alias]['id'])) {
//            print_r ( $this->data ) ;
            $conditions[$this->alias . '.id <>'] = $this->data[$this->alias]['id'];
        }

        $conditions[$this->alias . ".{$field}"] = $value;

        if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)) {
            $conditions[$this->alias . ".branch_id"] = getCurrentBranchID();
        }

        return !($this->hasAny($conditions));
    }
    /**
     * Counts deprecations for an asset
     * @param int $asset_id
     * @return int
     */
    function count_deprecations ( $asset_id ) {
        $this->loadModel( 'AssetDeprecation') ; 
        return $this->AssetDeprecation->find('count' , ['conditions' => ['asset_id' => $asset_id]]);
    }
    /**
     * Counts operations for an asset
     * @param int $asset_id
     * @return int
     */
    function count_operations ( $asset_id ) {
        $this->loadModel( 'AssetOperation') ; 
        return $this->AssetOperation->find('count' , ['conditions' => ['asset_id' => $asset_id]]);
    }
    /**
     * makes it disabled and writes if off 
     * @param type $asset_id
     * @return boolean
     */
    function write_off ( $asset_id ) {
        $asset = $this->findById ( $asset_id ) ;
        if ( empty ( $asset ) || $asset['Asset']['status'] ==$this::STATUS_DEPRECATED ) {
            return false ;
        }else {   
            $journal = [] ; 
            $amount = $asset['Asset']['asset_current_value']  ;
            $this->loadModel ( 'Journal');
            $this->loadModel('AssetOperation');
            $operation = $this->AssetOperation->find('first', ['conditions' => ['AssetOperation.asset_id' => $asset_id, 'AssetOperation.type' => AssetOperation::TYPE_WRITE_OFF]]);
            if(!empty($operation)) {
                $amount = $operation['AssetOperation']['asset_current_before'];
            }
            //Adding Journals
            $description = $asset['Asset']['name']. ' '.__("Write off",true);
            $journal['Journal']['date']=date ( 'Y-m-d') ;
//            $journal['Journal']['entity_type']='asset_write_off';
            $journal['Journal']['description']=$description;
//            $journal['Journal']['entity_id']=$asset_id;
            $journal['Journal']['currency_code']=$asset[$this->alias]['asset_currency'];
            $this->loadModel('JournalAccount' );
            $this->loadModel('Journal');
            
            $asset_account = $this->Journal->get_auto_account(['entity_id' => $asset_id,'entity_type' => 'asset']);//$this->JournalAccount->find ( 'first' , ['recursive' => -1 ,   'conditions' => ['entity_type' => 'asset' , 'entity_id' => $asset_id] ]);
            $asset_depreciation_account = $this->Journal->get_auto_account(['entity_id' => $asset_id,'entity_type' => 'asset_deprecation']);//$this->JournalAccount->find ( 'first' , ['recursive' => -1 ,   'conditions' => ['entity_type' => 'asset_deprecation' , 'entity_id' => $asset_id] ]);
            $profit_loss_account = $this->Journal->get_auto_account(['entity_id' => $asset_id,'entity_type' => 'write_off']);//$this->JournalAccount->find ( 'first' , ['recursive' => -1 , 'conditions' => ['entity_type' => 'capital_profit_loss' ] ]);
      
            $journal['JournalTransaction'][0]=
                array(
                        'subkey'=>'profit_loss_account', 
                        'currency_debit'=>$amount,
                        'currency_code'=>$asset[$this->alias]['asset_currency'],
                        'description'=>$description,
                        'journal_account_id' => $profit_loss_account['JournalAccount']['id']
                    );
            $journal['JournalTransaction'][1]=
                array(
                        'subkey'=>'depreciation_accounts', 
                        'currency_debit'=>$asset[$this->alias]['total_deprecation'],//$asset_depreciation_account['JournalAccount']['total_debit'],
                        'currency_code'=>$asset[$this->alias]['asset_currency'],
                        'description'=>$description,
                        'journal_account_id' => $asset_depreciation_account['JournalAccount']['id']
                    );
            $journal['JournalTransaction'][2]=
                array(
                        'subkey'=>'asset_account', 
                        'currency_credit'=> !empty($asset[$this->alias]['purchase_value_without_tax']) ? $asset[$this->alias]['purchase_value_without_tax']: $asset[$this->alias]['asset_purchase_value'],
                        'currency_code'=>$asset[$this->alias]['asset_currency'],
                        'description'=>$description,
                        'journal_account_id' => $asset_account['JournalAccount']['id']
                    );  

            $this->id = $asset_id ;
            $this->saveField ( 'asset_status' , $this::STATUS_DEPRECATED); 
            $this->saveField ( 'asset_current_value' , 0); 
            $this->add_action(ACTION_ASSET_WRITE_OFF,$asset,$asset['Asset']['asset_current_value']);
       
            return $journal ; 
        }
    }
    /**
     * Re Evaluates the asset with the new value 
     * @param int $asset_id
     * @param float $new_value
     * @return boolean
     */
    function re_evaluate ( $asset_id , $new_value , $operation_id = null  )
    {
        $asset = $this->findById ( $asset_id ) ;
        if ( empty ( $asset ) || empty ( $new_value ) ){
            return false ; 
        }else {
            $this->loadModel ( 'JournalAccount');
            $this->loadModel ( 'Journal');
            $asset_account = $this->Journal->get_auto_account(['entity_type' => 'asset' , 'entity_id' => $asset_id ]);//$this->JournalAccount->find('first' , ['recursive' => -1 ,   'conditions' => ['entity_type' => 'asset' , 'entity_id' => $asset_id] ]);
            $reval_account = $this->Journal->get_auto_account(['entity_type' => Util::ASSET_RE_EVALUATION_ACCOUNT , 'entity_id' => $operation_id ?? 0 ]);

            if(!$reval_account) {
                $reval_account = $this->Journal->get_auto_account(['entity_type' => Util::ASSET_RE_EVALUATION_ACCOUNT , 'entity_id' => 0 ]);
            }
            if(!$reval_account) {
                $reval_account['JournalAccount']['id']=$this->Journal->create_auto_account(['entity_type' => 're_evaluate' , 'entity_id' => 0 ]);
            }
            $journal = [] ;
            
            $this->loadModel ( 'Journal');
            //Adding Journals 
            $description = $asset['Asset']['name']. ' '.sprintf(__("Re-evaluation with value %s (%s)",true),format_price($new_value),$asset[$this->alias]['asset_currency'] );
            
//            $journal['Journal']['entity_type']='asset_re_evaluate';
            $journal['Journal']['description']=$description;
//            $journal['Journal']['entity_id']=$asset_id;
            $journal['Journal']['currency_code']=$asset[$this->alias]['asset_currency'];
            if ( !empty ( $operation_id ) ){
                $this->loadModel('AssetOperation');
                $operation = $this->AssetOperation->find('first' ,['recursive' => -1 ,  'conditions' => ['AssetOperation.id' => $operation_id]  ] );
                $current_value = $operation['AssetOperation']['asset_current_before'];
                $journal['Journal']['date']=date ( 'Y-m-d', strtotime($operation['AssetOperation']['date']) ) ;
            }else {
                $journal['Journal']['date']=date ( 'Y-m-d') ;
                $current_value = $asset['Asset']['asset_current_value'] ; 
            }
            
            $journal_value = $new_value - $current_value;
        
            $journal_abs_value = $journal_value ;
            $asset_debit_credit = 'currency_debit';
            $reval_debit_credit = 'currency_credit';
            if ( $journal_value < 0 ) {
                $journal_abs_value = abs($journal_value);
                $asset_debit_credit = 'currency_credit';
                $reval_debit_credit = 'currency_debit';
            }
            $journal['JournalTransaction'][0]=
                array(
                        'subkey'=>'asset_account', 
                        $asset_debit_credit=>$journal_abs_value,
                        'currency_code'=>$asset[$this->alias]['asset_currency'],
                        'description'=>$description,
                        'journal_account_id' => $asset_account['JournalAccount']['id']
                    );
            
            $journal['JournalTransaction'][1]=
                array(
                        'subkey'=>'account', 
                        $reval_debit_credit=>$journal_abs_value,
                        'currency_code'=>$asset[$this->alias]['asset_currency'],
                        'description'=>$description,
                        'journal_account_id' => $reval_account['JournalAccount']['id'],
                    );  
            $this->id = $asset_id ; 
            $this->saveField ( 'asset_status' , $this::STATUS_INSERVICE); 
            $this->saveField ( 'asset_current_value' , $new_value); 
            $action = ACTION_ASSET_RE_EVALUATE ; 
            if ( !empty ( $operation_id))
            {
                $action = ACTION_ASSET_EDIT_RE_EVALUATE ;
            }
            $this->add_action($action,$asset,$new_value);
            return $journal ;
        }
    }
    /**
     * Sells the asset of asset_id with buying price to the buying journal account id 
     * @param int $asset_id
     * @param float $buy_price
     * @param int $buying_journal_account_id
     * @return boolean
     */
    function sell_asset ( $asset_id , $buy_price , $soldQuantity, $buying_journal_account_id , $operation_id = null )
    {
        $buying_journal_account_id = intval ( $buying_journal_account_id ) ; // because it can return text :D , so it will be empty  .
        $asset = $this->findById ( $asset_id ) ;
        if ( empty ( $asset ) || empty ( $buying_journal_account_id ) || empty($buy_price) ){
            return false ; 
        }else {
            $this->loadModel ( 'JournalAccount');
            $this->loadModel ( 'Journal');
            $this->loadModel('AssetDeprecation');
            $asset_account = $this->Journal->get_auto_account(['entity_type' => 'asset' , 'entity_id' => $asset_id ]);//$this->JournalAccount->find ( 'first' , ['recursive' => -1 ,   'conditions' => ['entity_type' => 'asset' , 'entity_id' => $asset_id] ]);
            $asset_depreciation_account = $this->Journal->get_auto_account(['entity_type' => 'asset_deprecation' , 'entity_id' => $asset_id ]);//$this->JournalAccount->find ( 'first' , ['recursive' => -1 ,   'conditions' => ['entity_type' => 'asset_deprecation' , 'entity_id' => $asset_id] ]);
            $profit_loss_account = $this->Journal->get_auto_account(['entity_type' => 'capital_profit_loss' , 'entity_id' => 0 ]);//$this->JournalAccount->find ( 'first' , ['recursive' => -1 , 'conditions' => ['entity_type' => 'capital_profit_loss' ] ]);
            $journal = [] ; 
            if ( !empty ( $operation_id ) ){
                $this->loadModel('AssetOperation') ;
                $operation = $this->AssetOperation->find('first' , ['recursive' => -1 ,'conditions' => ['AssetOperation.id' => $operation_id] ]); 
                $current_amount = $operation['AssetOperation']['asset_current_before'];
            }else {
                $current_amount = $asset[$this->alias]['asset_current_value'];
            } 
            $amount = !empty($asset[$this->alias]['purchase_value_without_tax']) ? $asset[$this->alias]['purchase_value_without_tax']: $asset[$this->alias]['asset_purchase_value'];
            $total_deps = $this->AssetDeprecation->find('first' , ['fields' => "SUM(cost) as calculated_total_deprecation",'recursive' => -1 ,'conditions' => ['AssetDeprecation.asset_id' => $asset_id] ])[0]['calculated_total_deprecation'];
            $this->loadModel ( 'Journal');
            //Adding Journals 
            $description = $asset['Asset']['name']. ' '.__("Sell",true);
            if(!empty($operation['AssetOperation']['date']))
            $journal['Journal']['date']=$operation['AssetOperation']['date'];
            
            if(empty($journal['Journal']['date']))
            $journal['Journal']['date']=date ( 'Y-m-d') ;
//            $journal['Journal']['entity_type']='asset_sell';
            $journal['Journal']['description']=$description;
//            $journal['Journal']['entity_id']=$asset_id;
            $journal['Journal']['currency_code']=$asset[$this->alias]['asset_currency'];

            $balanceCalculationRequest = new \App\Services\Journal\JournalAccountBalanceCalculatorRequest([$asset_account['JournalAccount']['id']]);
            $date = $journal['Journal']['date'];
            $balanceCalculator = new \App\Services\Journal\JournalAccountsBalanceCalculator(getPDO());
            $operationJournal = $this->Journal->find('first', ['conditions' => ['Journal.entity_type' => 'asset_operation', 'Journal.entity_id' => $operation_id]]);
            if($operationJournal) {
                //its an edit
                $balanceCalculator->addWhere("(J.date < '{$date}' or (J.date = '{$date}' and J.id < {$operationJournal['Journal']['id']}))");
            } else {
                //its a create
                $balanceCalculator->addWhere("(J.date <= '{$date}' or (J.date = '{$date}'))");
            }
            $accountBalance = $balanceCalculator->calculateFirst($balanceCalculationRequest);
            $amount = $accountBalance['total_debit'] - $accountBalance['total_credit'];

            $assetQuantity = $soldQuantity + $this->getRemainingQuntity($asset_id);
            if ($soldQuantity) {
                $amount *= $soldQuantity / $assetQuantity;
            }

            //Asset transaction
            $journal['JournalTransaction'][0]=
                array(
                        'subkey'=>'asset_account', 
                        'currency_credit'=>$amount,
                        'currency_code'=>$asset[$this->alias]['asset_currency'],
                        'description'=>$description,
                        'journal_account_id' => $asset_account['JournalAccount']['id']
                    );
            
            //Profit Or LOSS
            //setting data for profit loss 
            $loss = false ; 
            $debit_or_credit = 'currency_credit';
            $profit_loss_description = __("Profit",true);

            if ($soldQuantity) {
                $current_amount *= $soldQuantity / $assetQuantity;
                $total_deps *= $soldQuantity / $assetQuantity;
            }
            $after_amount  = $buy_price - $current_amount  ;
            if ( $after_amount < 0 ){
                $loss = true ; 
                $debit_or_credit = 'currency_debit';
                $after_amount = abs ( $after_amount ) ;
                $profit_loss_description = __("Loss",true);
            }
            //profit_loss transaction
            if ( !empty ( $after_amount  ) ) {
                $journal['JournalTransaction'][1]=
                    array(
                            'subkey'=>'profit_loss_account', 
                            $debit_or_credit=>$after_amount,
                            'currency_code'=>$asset[$this->alias]['asset_currency'],
                            'description'=>$profit_loss_description,
                            'journal_account_id' => $profit_loss_account['JournalAccount']['id'],
                        );
            }
              
            //buying transaction
            $journal['JournalTransaction'][2]=
                array(
                        'subkey'=>'buying_account', 
                        'currency_debit'=>$buy_price,
                        'currency_code'=>$asset[$this->alias]['asset_currency'],
                        'description'=>$description,
                        'journal_account_id' => $buying_journal_account_id,
                    );  
            $journal['JournalTransaction'][3]=
                array(
                        'subkey'=>'depreciation_account', 
                        'currency_debit'=> $total_deps,
                        'currency_code'=>$asset[$this->alias]['asset_currency'],
                        'description'=>$description,
                        'journal_account_id' => $asset_depreciation_account['JournalAccount']['id'],
                    ); 
            $this->id = $asset_id ;
            $sell_price_without_tax=$buy_price;
            $tax1_total=0;

            if(!empty($operation['AssetOperation']['tax_1']))
            {
                $tax1_data=json_decode($operation['AssetOperation']['tax_1_data'],true);

                if(empty($tax1_data['included'])||$tax1_data['included']==0) {
                    $tax1_total = $tax1_data['value'] * $sell_price_without_tax / 100;
                    //if tax excluded add it the total sell price
                    $journal['JournalTransaction'][2]['currency_debit']+=$tax1_total;
                }
                else
                {
                    $tax1_total = ($tax1_data['value'] / (100 + $tax1_data['value'])) * $sell_price_without_tax;
                    //if included reduce the the tax from asset value
                    if ($debit_or_credit == 'currency_credit') {
                        $journal['JournalTransaction'][1][$debit_or_credit]=$journal['JournalTransaction'][1][$debit_or_credit]-$tax1_total;
                    } else {
                        $journal['JournalTransaction'][1][$debit_or_credit]=$journal['JournalTransaction'][1][$debit_or_credit]+$tax1_total;
                    }
                    $sell_price_without_tax=$sell_price_without_tax-$tax1_total;

                }

                if($tax1_total>0)
                {
                    $journal['JournalTransaction'][]=
                        array(
                            'subkey'=>'tax_'.$tax1_data['id'],
                            'currency_credit'=>$tax1_total,
                            'currency_code'=>$asset[$this->alias]['asset_currency'],
                            'description'=>$description,
                            'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'income_tax','entity_id'=>$tax1_data['id'])
                        );
                }


            }

            $tax2_total=0;
            if(!empty($operation['AssetOperation']['tax_2']))
            {

                $tax2_data=json_decode($operation['AssetOperation']['tax_2_data'],true);

                if(empty($tax2_data['included'])||$tax2_data['included']==0) {
                    $tax2_total = $tax2_data['value'] * $sell_price_without_tax / 100;
                    //if tax excluded add it the total sell price
                    $journal['JournalTransaction'][2]['currency_debit']+=$tax2_total;
                }
                else
                {
                    $tax2_total = ($tax2_data['value'] / (100 + $tax2_data['value'])) * $sell_price_without_tax;
                    //if included reduce the the tax from asset value
                    if ($debit_or_credit == 'currency_credit') {
                        $journal['JournalTransaction'][1][$debit_or_credit]=$journal['JournalTransaction'][1][$debit_or_credit]-$tax2_total;
                    } else {
                        $journal['JournalTransaction'][1][$debit_or_credit]=$journal['JournalTransaction'][1][$debit_or_credit]+$tax2_total;
                    }
                    $sell_price_without_tax=$sell_price_without_tax-$tax2_total;

                }

                if($tax2_total>0)
                {
                    $journal['JournalTransaction'][]=
                        array(
                            'subkey'=>'tax_'.$tax2_data['id'],
                            'currency_credit'=>$tax2_total,
                            'currency_code'=>$asset[$this->alias]['asset_currency'],
                            'description'=>$description,
                            'auto_account'=>array('type'=>'dynamic' , 'entity_type'=> 'income_tax','entity_id'=>$tax2_data['id'])
                        );
                }


            }

            if ($this->getQuntity($asset_id) == $this->AssetOperation->getSoldQuantity($asset_id)) {
                $this->saveField('asset_current_value', 0);
                $this->saveField('asset_status', $this::STATUS_SOLD);
            } else {
                $this->saveField('asset_current_value', $operation['AssetOperation']['asset_current_after']);
                $this->saveField('asset_status', $this::STATUS_INSERVICE);
            }

            $action = ACTION_ASSET_SOLD ; 
            if ( !empty ( $operation_id))
            {
                $action = ACTION_ASSET_EDIT_SOLD ;
            }
            $this->add_action($action,$asset,$buy_price);
            $totalCredit = 0;
            $totalDebit = 0;
            $profitIndex = null;
            foreach ($journal['JournalTransaction'] as $key => $transaction ) {
                $totalCredit += $transaction['currency_credit'];
                $totalDebit += $transaction['currency_debit'];
                if($transaction['subkey'] == 'profit_loss_account') {
                    $profitIndex = $key;
                }
            }
            if(abs($totalDebit - $totalCredit) > 0.01) {
                if($profitIndex) {
                    if($totalCredit > $totalDebit) {
                        $journal['JournalTransaction'][$profitIndex]['currency_debit'] += $totalCredit - $totalDebit;
                    } else {
                        $journal['JournalTransaction'][$profitIndex]['currency_credit'] += $totalCredit - $totalDebit;
                    }
                } else {
                    $journal['JournalTransaction'][] = [
                        'subkey' => 'profit_loss_account',
                        'currency_credit' => $totalDebit - $totalCredit,
                        'currency_code' => $asset[$this->alias]['asset_currency'],
                        'description' => $profit_loss_description,
                        'journal_account_id' => $profit_loss_account['JournalAccount']['id'],
                    ];

                }
            }
            return $journal ;
            
        }
    }
    /**
     * 
     * @param INT $action a constant representing the action 
     * @param array $asset can be an integer representing the asset 
     * @param float $extra_value the value replacing the purchase value in some cases 
     * @return boolean
     */
    function add_action ( $action , $asset , $extra_value = null ) {
        if ( !is_array ( $asset ) ) {
            $asset = $this->findById ( $asset ) ;
        }
        if ( empty ( $asset )|| empty ( $action ))
        {
            return false ; 
        }
        $this->loadModel ( 'JournalCat');
        $this->loadModel ( 'JournalAccount');
        $journal_cat_name = $this->JournalCat->findById ( $asset['Asset']['journal_cat_id'])['JournalCat']['name'];
        $journal_account_name = $this->JournalAccount->findById ( $asset['Asset']['journal_account_id'])['JournalAccount']['name'];
        $deprecation_period = $asset['Asset']['deprecation_period'].' '. __(Inflector::camelize( AssetDeprecation::$deprecation_intervals[$asset['Asset']['deprecation_interval']]['sql_lt']),true) ;
        switch ($asset['Asset']['deprecation_method']){
            case AssetDeprecation::METHOD_FIXED_AMOUNT: 
                $deprecation = format_price( $asset['Asset']['deprecation_amount'] ,$asset['Asset']['asset_currency'] );
                break ; 
            case AssetDeprecation::METHOD_PERCENTAGE: 
                $deprecation = $asset['Asset']['deprecation_percentage'].'%';
                break ;
            default : 
                $deprecation = NULL ; 
                $deprecation_period = NULL;
                break ;
        };
        $action_line =[
                    'primary_id'=>$asset['Asset']['id'],
                    'param1' => $journal_cat_name, 
                    'param2' => format_price($asset['Asset']['asset_purchase_value'] , $asset['Asset']['asset_currency']),
                    'param3' => $journal_account_name,
                    'param4' => format_price($asset['Asset']['asset_current_value'] , $asset['Asset']['asset_currency'] ),
                    'param5' => $deprecation,
                    'param6' => $deprecation_period,
                    'staff_id' => getAuthOwner("staff_id"),
                        ];
        switch ($action){
            
            case ACTION_ASSET_RE_EVALUATE:
            case ACTION_ASSET_EDIT_RE_EVALUATE:
            case ACTION_ASSET_DELETE_RE_EVALUATE:
                
            case ACTION_ASSET_DELETE_WRITE_OFF:
            case ACTION_ASSET_WRITE_OFF:
                
            case ACTION_ASSET_EDIT_SOLD:
            case ACTION_ASSET_SOLD:
            case ACTION_ASSET_DELETE_SOLD:
                $action_line['param2'] = format_price($extra_value,$asset['Asset']['asset_currency']);
                break;
        } 
        
        $this->add_actionline($action ,$action_line );
        
    }


    function get_all_operations ( $asset_id ) {
        $asset = $this->find ( 'first' , ['recursive' => -1 , 'conditions' => ['Asset.id' => $asset_id] ]);
        if ( empty ( $asset  )){
            return [] ; 
        }

        $p_value=empty($asset['Asset']['purchase_value_without_tax'])?$asset['Asset']['asset_purchase_value']:$asset['Asset']['purchase_value_without_tax'];
        $all_operations = [
            ['date' => $asset['Asset']['purchase_date'] , 
             'transaction' => __("Purchase", true ),
             'amount' =>  $p_value,
             'debit' => $p_value,
             'credit' => 0,
             'created' => $asset['Asset']['created']

                ]
        ] ;


        $this->loadModel('AssetOperation');
        $this->loadModel('AssetDeprecation');
        $operations = $this->AssetOperation->find('all' , ['recursive' => -1 , 'conditions' => ['asset_id' => $asset_id], 'order' => ['date DESC', 'id DESC']]);
        $depreciants = $this->AssetDeprecation->find('all' , ['recursive' => -1 , 'conditions' =>['asset_id' => $asset_id] ]);
        foreach ( $operations as $dp ) {
            $operationData = [
                'date' => $dp['AssetOperation']['date'],
                'transaction' => AssetOperation::$operations[$dp['AssetOperation']['type']]['name'],
                'amount' => $dp['AssetOperation']['value'],
                'debit' => ($dp['AssetOperation']['asset_current_after'] - $dp['AssetOperation']['asset_current_before']),
                'credit' => 0,
                'created' => $dp['AssetOperation']['created']
            ];
            if(AssetOperation::TYPE_SELL == $dp['AssetOperation']['type']) {
                $operationData['amount'] = $dp['AssetOperation']['asset_current_before']-$dp['AssetOperation']['asset_current_after'];
            }
            $all_operations[] = $operationData;
        }
        foreach ( $depreciants as $dp ) {
            $all_operations[] = [
                'date' => $dp['AssetDeprecation']['date'],
                'transaction' => __("Depreciation",true),
                'amount' => $dp['AssetDeprecation']['cost'],
                'debit' => 0,
                'credit' => $dp['AssetDeprecation']['cost'],
                'created' => $dp['AssetDeprecation']['created']
                    ] ; 
        }
        usort ($all_operations , function ($a , $b ) {
            $aStrToTime = strtotime($a['date'] );
            $bStrToTime = strtotime($b['date'] );
            if ($aStrToTime == $bStrToTime) {
                $a = strtotime($a['created'] );
                $b = strtotime($b['created'] );
                return ($a < $b) ? -1 : 1;
            }
            return ($aStrToTime < $bStrToTime) ? -1 : 1;
        } );
        return $all_operations ; 
    }

	public function isValid($asset) {
		$asset = $asset['Asset'];
		$asset_id = $asset['id'];
		if (empty($asset)) {
			return false;
		}
		$last_deprecation_date = $this->AssetDeprecation->query("SELECT max(date) as max_date  FROM `asset_deprecations` AS `AssetDeprecation` WHERE `AssetDeprecation`.`asset_id` = $asset_id ORDER BY `AssetDeprecation`.`id` DESC LIMIT 1", false);
		$last_generated_date = $last_deprecation_date[0][0]['max_date'];
		// We need 1 Method to return a date we can start deprecating from at least.
		if (!$this->isRealDate($asset['last_deprecation_date']) && !$this->isRealDate($asset['in_service_date']) && !$this->isRealDate($last_generated_date)){
			return false;
		}
		// Needs to look at since it's currency value is negative
		if ($asset['asset_current_value'] <= 0.001) {
			return false;
		}
		// Needs Customer To Fix the Interval (Every Day/Month/Week) ?
		if (!in_array($asset['deprecation_interval'], [self::INTERVAL_DAY, self::INTERVAL_WEEK, self::INTERVAL_MONTH])) {
			return false;
		}
		// Needs Customer To Fix the Period (Every X (day/month/week) ?
		if (empty($asset['deprecation_period'])) {
			return false;
		}
		if ($asset['deprecation_period'] > 1000 && $asset['deprecation_interval'] !== self::INTERVAL_DAY) {
			return false;
		}
		return true;
	}

	private function isRealDate($date) {
		if (false === strtotime($date)) {
			return false;
		}
		list($year, $month, $day) = explode('-', $date);
		return checkdate($month, $day, $year);
	}

    public function getCurrencylist($conditions = array()) {
        $currencies = [];
        $rows = $this->find('all', array('applyBranchFind'=>false,'fields' => 'DISTINCT asset_currency', 'conditions' => $conditions, 'recursive' => -1));
        foreach ($rows as $row) {
            $currencies[] = $row['Asset']['asset_currency'];
        }
        return $currencies;
    }

    public function getQuntity($assetId)
    {
        $this->loadModel('AssetOperation');
        $operation = $this->AssetOperation->find('first', ['conditions' => ['AssetOperation.asset_id' => $assetId, "AssetOperation.type in (" . AssetOperation::TYPE_RE_EVALUATE . "," . AssetOperation::TYPE_WRITE_OFF . ")"], 'order' => ['AssetOperation.date desc', 'AssetOperation.id desc']]);
        if (!empty($operation)) {
            return $operation['AssetOperation']['quantity'] ?? 0;
        }
        $asset = $this->findById($assetId);
        return $asset['Asset']['quantity'] ?? 0;
    }

    public function getRemainingQuntity($assetId, $exceptOperationId = null)
    {
        $this->loadModel('AssetOperation');
        return $this->getQuntity($assetId) - $this->AssetOperation->getSoldQuantity($assetId, $exceptOperationId);
    }
}
