{"name": "vendor/daftra-dev", "version": "1.0.0", "config": {"platform": {"php": "8.0"}, "allow-plugins": {"php-http/discovery": false}}, "description": "Description of project daftra-dev.", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0", "rollbar/rollbar": "^3.1", "twilio/sdk": "^5.16", "plivo/php-sdk": "^4.0", "izam/daftra-activity-log": "*@dev", "ext-mysqli": "*", "php-amqplib/php-amqplib": "^2.11", "vlucas/phpdotenv": "^4.1", "izam/daftra-js-injector": "*@dev", "izam/daftra-staff": "*@dev", "izam/daftra-jwt": "*@dev", "izam/forms": "*@dev", "izam/queue": "*@dev", "izam/limitation": "*@dev", "izam/daftra-common": "*@dev", "izam/daftra-supplier": "*@dev", "izam/daftra-client": "*@dev", "izam/daftra-expense": "*@dev", "izam/daftra-invoice": "*@dev", "izam/daftra-advance-payment": "*@dev", "izam/daftra-journal": "*@dev", "izam/daftra-product": "*@dev", "izam/daftra-store": "*@dev", "izam/daftra-purchase-order": "*@dev", "izam/shorturl": "*@dev", "ramsey/uuid": "^4.2.3", "gettext-bilal-azzam/gettext": "^4.8", "izam/service-provider": "*@dev", "izam/daftra-app-manager": "*@dev", "izam/autonumber": "*@dev", "izam/daftra-dynamic-permissions": "*@dev", "izam/daftra-workflow": "*@dev", "izam/database": "*@dev", "league/flysystem": "^1.0", "league/flysystem-aws-s3-v3": "^1.0", "izam/daftra-diagnostics": "*@dev", "izam/template": "*@dev", "izam/attachment": "*@dev", "izam/izam-view": "*@dev", "izam/daftra-cache": "*@dev", "izam/rental": "*@dev", "izam/payslip": "*@dev", "izam/navigation": "*@dev", "izam/aws": "*@dev", "izam/dynamic-list": "*@dev", "daftra/validator": "*@dev", "izam/izam-recurring-profiles": "*@dev", "izam/izam-tafket": "*@dev", "izam/logging": "*@dev", "izam/daftra-portal": "*@dev", "izam/score-calculator": "*@dev", "giggsey/libphonenumber-for-php": "8.13.53", "izam/stock-request": "*@dev", "illuminate/collections": "8.x-dev", "symfony/cache": "5.4.31", "izam/fcm": "*@dev", "izam/daftra-manufacturing-order": "*@dev", "izam/auto-reminder": "*@dev", "izam/item-permission": "*@dev", "izam/portal-auth": "*@dev", "ezyang/htmlpurifier": "dev-master", "izam/daftra-e-invoice": "*@dev", "endroid/qr-code": "^4.8", "ext-memcache": "*", "ext-fileinfo": "*"}, "repositories": [{"type": "path", "url": "../izam-packages/izam-score-calculator"}, {"type": "path", "url": "../izam-packages/izam-template"}, {"type": "path", "url": "../izam-packages/daftra-diagnostics"}, {"type": "path", "url": "../izam-packages/izam-shorturl"}, {"type": "path", "url": "../izam-packages/izam-database"}, {"type": "path", "url": "../izam-packages/izam-service-provider"}, {"type": "path", "url": "../izam-packages/daftra-app-manager"}, {"type": "path", "url": "../izam-packages/daftra-supplier"}, {"type": "path", "url": "../izam-packages/daftra-dynamic-permissions"}, {"type": "path", "url": "../izam-packages/daftra-client"}, {"type": "path", "url": "../izam-packages/izam-autonumber"}, {"type": "path", "url": "../izam-packages/daftra-expense"}, {"type": "path", "url": "../izam-packages/daftra-invoice"}, {"type": "path", "url": "../izam-packages/daftra-journal"}, {"type": "path", "url": "../izam-packages/daftra-product"}, {"type": "path", "url": "../izam-packages/daftra-store"}, {"type": "path", "url": "../izam-packages/daftra-purchase-order"}, {"type": "path", "url": "../izam-packages/daftra-cache"}, {"type": "path", "url": "../izam-packages/daftra-js-injector"}, {"type": "path", "url": "../izam-packages/daftra-supplier"}, {"type": "path", "url": "../izam-packages/daftra-jwt"}, {"type": "path", "url": "../izam-packages/daftra-staff"}, {"type": "path", "url": "../izam-packages/queue"}, {"type": "path", "url": "../izam-packages/daftra-common"}, {"type": "path", "url": "../izam-packages/izam-forms"}, {"type": "path", "url": "../izam-packages/daftra-activity-log"}, {"type": "path", "url": "../izam-packages/izam-limitation"}, {"type": "path", "url": "../izam-packages/daftra-workflow"}, {"type": "path", "url": "../izam-packages/izam-attachment"}, {"type": "path", "url": "../izam-packages/izam-view"}, {"type": "path", "url": "../izam-packages/izam-rental"}, {"type": "path", "url": "../izam-packages/izam-payslip"}, {"type": "path", "url": "../izam-packages/izam-entity"}, {"type": "path", "url": "../izam-packages/izam-currency"}, {"type": "path", "url": "../izam-packages/izam-aws"}, {"type": "path", "url": "../izam-packages/izam-dynamic-list"}, {"type": "path", "url": "../izam-packages/daftra-validator"}, {"type": "path", "url": "../izam-packages/daftra-advance-payment"}, {"type": "path", "url": "../izam-packages/izam-recurring-profiles"}, {"type": "path", "url": "../izam-packages/izam-branch"}, {"type": "path", "url": "../izam-packages/izam-navigation"}, {"type": "path", "url": "../izam-packages/izam-tafket"}, {"type": "path", "url": "../izam-packages/izam-logging"}, {"type": "path", "url": "../izam-packages/daftra-portal"}, {"type": "path", "url": "../izam-packages/izam-tafket"}, {"type": "path", "url": "../izam-packages/izam-stock-request"}, {"type": "path", "url": "../izam-packages/izam-fcm"}, {"type": "path", "url": "../izam-packages/daftra-manufacturing-order"}, {"type": "path", "url": "../izam-packages/izam-auto-reminder"}, {"type": "path", "url": "../izam-packages/izam-item-permission"}, {"type": "path", "url": "../izam-packages/izam-portal-auth"}, {"type": "path", "url": "../izam-packages/daftra-e-invoice"}], "minimum-stability": "dev", "autoload": {"psr-4": {"App\\": "./"}, "files": ["vendors/ReportV2/Helper/JournalLocalEntityCustomData.php"]}, "require-dev": {"symfony/var-dumper": "5.4.x-dev"}}