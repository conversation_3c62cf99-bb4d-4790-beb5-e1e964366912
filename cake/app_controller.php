<?php

use App\Auth\Strategy\ApiKeyAuth;
use App\Auth\Strategy\AuthStrategyFactory;
use App\Services\UserSessionRedisService;
use Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\PageScriptErrorHandlerDecorator;
use Izam\Daftra\AppManager\Utils\AppPermissionUtil;
use Izam\Daftra\AppManager\Utils\PageScriptsMapper;
use Izam\Daftra\Cache\PortalCache;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Portal\Models\ImportData as PortalImportData;
use Izam\Navigation\Navigation;
use Izam\Portal\Auth\Exception\InvalidScopeException;
use Izam\Portal\Auth\Interfaces\IRequestedPermissionInterface;

App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));
App::import('Core', 'L10n');
App::import('Vendor', 'settings');

/**
 * @property SessionComponent $Session
 * @property RequestHandlerComponent $RequestHandler
 * @property Client $Client
 * @property Staff $Staff
 * @property Site $site
 * @property CookieComponent $Cookie
 * @property ApiRequestsComponent $ApiRequests
 */
class AppController extends Controller {
    var $helpers = array('html', 'form', 'javascript', 'sidemenu', 'list');
    var $config = array();
    var $components = array('Cookie', 'Email', 'Session', 'RequestHandler','ApiRequests');
    var $crumbs = false;
    var $lang = false;
    public $AppKey;
    public $AppSite;

	var $api_credentials = false;
    public $js_lang_labels = array(
        'Quantity',
        'Unit Price',
        'Today',
        'Last 7 days',
        'Date Range',
        'Specific Date',
        'All Dates Before',
        'All Dates After',
        'Start date',
        'End date',
        'Next',
        'Prev',
        'Last week',
        'Last month',
        'Month to date',
        'Last year',
        'Year to date',
        'Reset',
        'Yes',
        'No',
        'Update',
        'Close',
        'Add',
        'Edit',
        'Remove',
        'Issue days must be less than subscirption period',
        'Value must be between [0-100]',
        'Only :value characters allowed',
        'Value must be less than or equal to :value',
        'Value must be less than :value',
        'Either label or value required',
        'Valid date required',
        'Valid number required',
        'Invalid number of days',
        'Are you sure you want to delete?',
        'Really delete this entry?',
        'Positive number required',
        'Characters left',
        'Required',
        'Please enter a valid email',
        'Please enter a valid number',
        'error',
        'type invoice no',
        'type client name',
        'Please select the required terms',
        'Please select project',
        'Please enter a positive time value',
        'Valid email required',
        'Upload',
        'Full Name / Business Name ',
        'Full Name',
        'Client Details',
        'Business Name',
		'Active',
        'Not Active',
		'All',
		'This Field must be unique',
		'Apply',
		'Ignore',
		'Change it',
        'Are you sure ?',
        'Copied',
        'Drop file here or',
        'select from your computer',
        'Choose file',
        'The Points Usage cannot exceed the total remaining amount from the  Client Loyalty balance',
        'Electronic Signature',
        "You should install signature Program before sending transactions to ETA <a href='/v2/owner/eta/download_file' target='_blank'><b>Daftra Digital Signature</b> </a>",
        'Please connect the dongle to your computer',
        'Please install tax authority certificate on your machine',
        "You entered invalid pin ,please change pin code form <a href='/v2/owner/electronic_invoice_settings' target='_blank'>Electronic Invoice Settings</a>",
        "Please, download the latest version of program from <a href='/v2/owner/eta/download_file' target='_blank'><b>Daftra Digital Signature</b></a>",
        "Please select items first by clicking on the small left box",
        "Non Created Item",
        "Add New Product",
        "Invalid Barcode",
        "The barcode is not linked to any product in the inventory",
        "This is a required field and could not be empty",
        "Please enter 1 keyword or more to search",
        "No results found",
        "This is a required field and could not be empty",
        "Today",
        "Last 30 Days",
        "This Month",
        "Last Month",
        "Custom Range",
        "Auto Salary Component"
    );

    /**
     * @var RequestHandlerComponent
     */
    var $RequestHandler;

    function redirect($url, $status = null, $exit = true)
    {
        if (isset($this->no_redirect) && $this->no_redirect == true) {
            return;
        }
        return parent::redirect($url, $status, $exit);
    }

    protected function cachePaginationConditions($model, $conditions)
    {
        $this->Session->write($model.'PaginationConditions', $conditions);
    }

    protected function getCachedPaginationConditions($model)
    {
        return $this->Session->read($model.'PaginationConditions');
    }

    function paginate($object = null, $scope = [], $whitelist = [])
    {
        $modelName = Inflector::singularize($this->name);
        if (empty($this->params['named']['sort'])) {
            $this->get_order_settings();
        }
        $this->cachePaginationConditions(is_array($object)?$modelName:$object, is_array($object)?$object:$scope);
        $results = parent::paginate($object, $scope, $whitelist);
        if (!empty($this->params['named']['sort']) && count((array)$results) > 0) {
            $this->write_order_settings();
        }
        if (isset ($this->{$modelName})) {
            // warning suppress
            $sortFields = $this->{$modelName}->getSortFields($setting ?? null);
            $this->set(compact('sortFields'));
        }
        return $results;
    }

    // NAV SECTION

	function setup_nav_data($data,$options = array(/*model_name,id_name,view_page,nav_key,view_controller*/))
	{
        // warning suppress
		$modelName = $options['model_name'] ?? Inflector::humanize(Inflector::singularize($this->name));
		$id_name = $options['id_name'] ?? 'id';
		$view_action = $options['view_page'] ?? 'view';
		$nav_key = $options['nav_key'] ?? $this->name.'.nav';
		$view_controller = $options['view_controller'] ?? $this->name;
		$nav = array();

		foreach($data as $k => $item){

            // warning suppress
			$nav[$k] = $item[$modelName][$id_name] ?? null;
		}

		if($modelName == 'ClientAppointment')
			$modelName = 'FollowUpReminder';


		$nav['paging'] = $this->params['paging'];
		$nav['url'] = $_SERVER['REQUEST_URI'];
		$nav['model_name'] = $modelName;
		$nav['id_name'] = $id_name;
		$nav['view_action'] = $view_action;
		$nav['add_action'] = $options['add_page'] ?? 'add';
		$nav['view_controller'] = $view_controller;
        // warning suppress
        if (isset($this->Session->read($nav_key)['next_page']) && $this->Session->read($nav_key)['next_page']) {
            $nav['next_page'] = false;
            //for nav section
            $this->Session->write($nav_key, $nav);
            $this->redirect(Router::url(array('controller' => $view_controller, 'action' => $nav['view_action'], $nav[0])));
        } else if (isset($this->Session->read($nav_key)['prev_page']) && $this->Session->read($nav_key)['prev_page']) {
            $nav['prev_page'] = false;
            //for nav section
            $this->Session->write($nav_key, $nav);
            $this->redirect(Router::url(array('controller' => $view_controller, 'action' => $nav['view_action'], $nav[19])));
        } else {
            $this->Session->write($nav_key, $nav);
        }
        // end warning suppress
    }
    function setup_nav_add_url($url)  {
            if (empty($this->viewVars['izamNavigation'])) {
                $izamNavigation = new Navigation();
                $izamNavigation->setPageAddUrl($url);
                $this->set('izamNavigation', $izamNavigation);
            }else {
                $this->viewVars['izamNavigation']->setPageAddUrl($url);
            }
        }

	function setup_nav_view($id,$nav_key = false){

		$nav_key = !$nav_key ? $this->name.'.nav' : $nav_key;
		$nav = $this->Session->read($nav_key);
		$item_nav_index = array_search($id,$nav ?? []);
		if($item_nav_index !== FALSE && !empty($nav))
		{
        $has_next=$this->has_next($id,$nav);
        $has_prev=$this->has_prev($id,$nav);
		$this->set('nav_text',$this->get_nav_text($id,$nav));
        $this->set('has_next', $has_next);
        $this->set('has_prev', $has_prev);

        $next_url['action'] = 'next';
        $next_url[] = $id;
        $prev_url['action'] = 'previous';
        $prev_url[] = $id ;
        
        $izamNavigation = new Navigation();
        if ($has_next)  $izamNavigation->setPageNextUrl(Router::url($next_url));
        if ($has_prev)  $izamNavigation->setPagePreviousUrl(Router::url($prev_url));
        $izamNavigation->setPageAddUrl(Router::url(['action'=>$nav['add_action']??'add']));

        $this->set('izamNavigation', $izamNavigation);
		return true ;
		}
		return false;
	}

	function get_nav_text($id,$nav,$title = false){

		$page_no = $nav['paging'][$nav['model_name']]['page'];
		$count =  $nav['paging'][$nav['model_name']]['count'];
		$item_nav_index = array_search($id,$nav);
        $nav_text .= ' '.(($page_no - 1)*20 + (float) $item_nav_index + 1). ' '.__('of',true).' ' . $count ;

        return $nav_text;
	}

	function has_next($id,$nav){

		$item_nav_index  = array_search($id,$nav);
		if($nav['paging'][$nav['model_name']]['page'] < $nav['paging'][$nav['model_name']]['pageCount'])
			return true;
		else if($nav['paging'][$nav['model_name']]['page'] == $nav['paging'][$nav['model_name']]['pageCount'] && ($item_nav_index  < $nav['paging'][$nav['model_name']]['current'] - 1 ))
			return true;
		else return false ;

	}

	function has_prev($id,$nav){

		$item_nav_index = array_search($id,$nav);
		if($nav['paging'][$nav['model_name']]['page'] > 1)
			return true;
		else if($nav['paging'][$nav['model_name']]['page'] == 1 && ($item_nav_index  > 0 ))
			return true;
		else return false ;

	}

    function owner_next($id)
    {
        $nav_key = $_GET['nav_key'] ? $_GET['nav_key'] : $this->name . '.nav';
        $nav = $this->Session->read($nav_key);

        if ($nav && count($nav) > 1) {
            $item_nav_index = array_search($id, $nav);

            if ($item_nav_index !== false) {
                if ($item_nav_index >= 19) {
                    $url = $nav['url'];
                    if ($nav['paging'][$nav['model_name']]['page'] < $nav['paging'][$nav['model_name']]['pageCount']) {
                        if ($nav['paging'][$nav['model_name']]['page'] == 1) {
                            $pos = strrpos($url, '/');
                            $new_url = substr_replace($url, '/index/page:2/', $pos);
                        } else {
                            $new_url = str_replace('page:' . $nav['paging'][$nav['model_name']]['page'], 'page:' . ($nav['paging'][$nav['model_name']]['page'] + 1), $nav['url']);
                        }
                    } else {
                        $this->flashMessage(__("{$nav['model_name']} not found", true));
                        $this->redirect(Router::url(['controller' => $nav['view_controller'], 'action' => 'index']));
                    }
                    $nav['next_page'] = true;
                    $this->Session->write($nav_key, $nav);
                    $this->redirect(Router::url($new_url));
                } else {
                    $item_nav_next_index = $item_nav_index + 1;
                    $this->redirect(Router::url(['controller' => $nav['view_controller'], 'action' => $nav['view_action'], $nav[$item_nav_next_index]]));
                }
            }
        }
        $this->flashMessage(__("{$nav['model_name']} not found", true));
        $this->redirect(Router::url(['action' => 'index']));
    }

	function owner_previous($id)
    {
        $nav_key = $_GET['nav_key'] ? $_GET['nav_key'] : $this->name . '.nav';
        $nav = $this->Session->read($nav_key);
        if ($nav && count($nav) > 1) {
            $item_nav_index = array_search($id, $nav);
            if ($item_nav_index !== false) {
                if ($item_nav_index <= 0) {
                    $url = $nav['url'];
                    if ($nav['paging'][$nav['model_name']]['page'] > 1) {
                        $new_url = str_replace('page:' . $nav['paging'][$nav['model_name']]['page'], 'page:' . ($nav['paging'][$nav['model_name']]['page'] - 1), $nav['url']);
                    } else {
                        $this->flashMessage(__("{$nav['model_name']} not found", true));
                        $this->redirect(Router::url(['controller' => $nav['view_controller'], 'action' => 'index']));
                    }
                    $nav['prev_page'] = true;
                    $this->Session->write($nav_key, $nav);
                    $this->redirect(Router::url($new_url));
                } else {
                    $item_nav_prev_index = $item_nav_index - 1;
                    $this->redirect(Router::url(['controller' => $nav['view_controller'], 'action' => $nav['view_action'], $nav[$item_nav_prev_index]]));
                }
            }
        }
        $this->flashMessage(__("$modelName not found", true));
        $this->redirect(Router::url(['action' => 'index']));
    }

	//end nav section


	private function _rest_prepare_data($data, $content_type) {
		switch ($content_type) {
			case "application/json":
				$data = json_decode($data, true);
				if(json_last_error()!==JSON_ERROR_NONE) $this->cakeError("error500", ["message"=>"Problem parsing JSON input"]);
                if((is_array($data) && $data['CustomModel']) || $this->params['array'])
				{
					$this->data = $data;
				}else{
					foreach($this->params['allowed_models'] as $key=>$value){
                    if (is_numeric($key)) $this->data[$value] = is_array($data) ? $data[$value] : null;
					else $this->data[$key] = $data[$value];
					}
				}

				break;
			default:
				$this->cakeError("error500", ["message"=>"Invalid content type"]);
		}
	}

    public function blackhole($type)
    {
        \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , 'The CSRF token is invalid. Please try to resubmit the form.');
        $this->flashMessage(__('The CSRF token is invalid. Please try to resubmit the form.', TRUE));
        return $this->redirect('/' .$_REQUEST['url']);
    }

    function beforeFilter() {
        $BadWords=["FLOOR(RAND","SELECT COUNT(*)","IFNULL(CAST","INFORMATION_SCHEMA","PROCEDURE ANALYSE","UNION ALL SELECT","SELECT(SLEEP","mysql.innodb_table_stats"];
        foreach($this->params['url'] as $key=>$value){
            if(is_array($value)){
                continue;
            }
            $badWord="";
            if ($badWord = containsWords($value, $BadWords) || preg_match('/(?<=SELECT\s)(.*?)(?=\sFROM)/i', $value) ) {

                \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , "Sql Inject detected ",
                    [
                        'Bad Word' => $badWord,
                        'key' => $key,
                        'value' => $value,
                    ]
                );
                unset($this->params['url'][$key]);
                unset($_GET[$key]);
            }
        }

        if (php_sapi_name() == 'cli') {
            $_SERVER['GLOBAL_SITE_ID'] = getCurrentSite('id');
            $_SERVER['GLOBAL_SUBDOMAIN'] = getCurrentSite('subdomain');
            $_SERVER['GLOBAL_DB_CONFIG'] = getCurrentSite('db_config');
        }
        TillNow::tillnow('BEFORE FILTER');
        $site = getCurrentSite();
        $siteId = $site['id'];
        $isLogin = ($this->Session->check('OWNER') || $this->Session->check('CLIENT'));
        $site=PortalCache::get('site', function () use ($siteId) {
            $this->loadModel('Site');
            $site = $this->Site->find('first', ['recursive' => -1, 'conditions' => ['Site.id' => $siteId]]);
            return $site['Site'];
        });
        $tolerant = (int)$site['tolerant'];
        $expiry = ($site['plan_id'] != 1 && (date('Y-m-d') > date("Y-m-d",strtotime($site['expiry_date']." +$tolerant day"))));
        $siteStatusStopped = ($site['status'] == SITE_STATUS_STOPPED && $site['plan_id'] == 1);
        //here we need to bypass request that ge menu and login as admin not to apply suspend redirect
        if (empty($_GET['force_menu']) && (empty($_SESSION['LOGGED_AS_ADMIN']))) {
            //if user not logged and activate shopfront any routes should redirect to login when site expired or suspended
            if (
                !$isLogin &&
                ifPluginActive(WebsiteFrontPlugin) &&
                ($expiry || $siteStatusStopped) &&
                (
                    strpos($_GET['url'], 'contents') !== false ||
                    $_GET['url'] == '/' ||
                    $_GET['url'] == 'home'
                )
            ) {
                return $this->redirect('/login', 200, true);
            }
            // we put all urls that not need to apply expire or suspended middleware
            $except = [
                'renew',
                'sites/login_as',
                'owner/owners/logout',
                'owner/owners/upgrade',
            ];
            $filteredRoutes = array_filter($except, function ($ele){
                return strpos($_GET['url'], $ele) !== false ;
            }) ;
            // if count == 0 then the url not in the listed array
            if (!count($filteredRoutes)) {
                if ($isLogin && $expiry) {
                    return $this->redirect('/v2/suspended-site', 200, true);
                }
                if ($isLogin && $siteStatusStopped) {
                    return $this->redirect('/v2/suspended-free-site', 200, true);
                }
            }
        }
        $GLOBALS['ServiceProvider']['RequestHandler'] = $this->RequestHandler;
        parent::beforeFilter();
        if (PHP_SAPI == 'cli') {
            return;
        }
        if ($this->RequestHandler->isAjax()) {
            $this->layout = 'ajax';
        }
        if (isset($_POST['go_to_custom_details']) && !empty($_POST['go_to_custom_details'])) {
            //this is used for custom form redirect to redirect to the related custom form after saveing item
            $this->Session->write('go_to_custom_details', $_POST['go_to_custom_details']);
        }
        if (IS_REST) {
            $headers = array_merge($_SERVER, apache_request_headers());
            $authenticator = AuthStrategyFactory::getAuthenticator($headers);

            if ($authenticator instanceof IRequestedPermissionInterface) {
                $authenticator->setRequestedPermission(AppPermissionUtil::getCakePermission($this->params));
            }

            try {
                $status = $authenticator->authenticate($headers, $this->params);
            } catch (InvalidScopeException) {
                $this->cakeError('error403');
            }
            if (!$status) {
                if(ConnectionManager::getDataSource('portal')->error) {
                    http_response_code(503);
                    die();
                }
                $this->cakeError('error401');
            }
            if($authenticator instanceof ApiKeyAuth){
                $whole_url = $this->params['url']['url'];
                $url = parse_url($whole_url, PHP_URL_PATH);
                if(!$authenticator->checkRoles($url)){
                    $this->cakeError('error405');
                }
            }
            $authOwner = getAuthOwner();
            if (empty($authOwner)) {
                define("TRUE_REST", true);
            }
            $this->layout = false;
            $this->viewPath = 'rest';
            App::import("Vendor", "api");
            if ($this->RequestHandler->ext == 'html') $this->RequestHandler->ext = "json";
            if ($this->RequestHandler->isPost() || $this->RequestHandler->isPut()) {
                switch ($_SERVER["CONTENT_TYPE"]) {
                    case "application/json":
                    case "application/json; charset=utf-8":
                        $this->_rest_prepare_data(file_get_contents('php://input'), "application/json");
                        break;
                    default:
                        $this->cakeError("error500", ["message" => "Invalid content type"]);
                }
            }
        }
	    if (!defined("TRUE_REST")) {
		  define('TRUE_REST', false);
	    }
        $this->Client = GetObjectOrLoadModel('Client');
        $this->RequestHandler->setContent('xls', 'application/vnd.ms-excel');
        $this->RequestHandler->setContent('xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $this->__init_lang();
        //Loading configurations
        $this->_set_smtp();
        $this->__load_config();
        $this->set('config', $this->config);
        $prefix = empty($this->params['prefix']) ? '' : $this->params['prefix'];
        if(php_sapi_name()!='cli') {
            $this->_SecureSession();
        }
        //if (!$this->RequestHandler->isAjax()) {
            if (strtolower($prefix) == 'owner' && !$this->_isOwnerAuthenticated()) {
                if (!strstr($_SERVER['REQUEST_URI'], '/logout')) {
                    //                  echo $this->name;
//                    die();
                    $this->Session->write('LastPath', $_SERVER['REQUEST_URI']);
                    $this->_record_path();
                }
                if (ifPluginActive(WebsiteFrontPlugin))
                    $this->redirect(getLoginUrl());
                $this->redirect(str_replace('http://', 'https://', Router::url(['controller' => 'clients', 'action' => 'login', 'client' => false, 'owner' => false], true)));
            } elseif (strtolower($prefix) == 'client' && !$this->_isClientAuthenticated()) {
                //    echo 'not_login';
                //     die();
                if (!strstr($_SERVER['REQUEST_URI'], '/logout')) {
                    $this->Session->write('LastPath', $_SERVER['REQUEST_URI']);
                    $this->_record_path();
                }
                if (ifPluginActive(WebsiteFrontPlugin)) {
                    if (strpos($this->params['url']["url"], "contents/booking/") !== false && empty(getAuthClient())) {
                        $this->flashMessage(__("Please Register First to continue your reservation order", true), 'alert alert-danger m-t');
                    }
                    if (empty(settings::getValue(PluginUtil::ClientsPlugin, 'client_permission_register'))) {
                        $this->redirect(str_replace('http://', 'https://', Router::url(['controller' => 'contents', 'action' => 'login', 'client' => false, 'owner' => false,], true))."?redirect_to=".  base64_encode($this->referer()));
                    } else {
                        $this->redirect(str_replace('http://', 'https://', Router::url(['controller' => 'contents', 'action' => 'register', 'client' => false, 'owner' => false ], true))."?redirect_to".base64_encode($this->referer()));
                    }

                } else
                    $this->redirect(str_replace('http://', 'https://', Router::url(['plugin' => '', 'controller' => 'clients', 'action' => 'login', 'client' => false, 'owner' => false], true)));
//			$this->redirect(array('controller' => 'clients', 'action' => 'login', 'client' => false));
           // }
        }
        if (strtolower($prefix) == 'client' && $this->_isClientAuthenticated()) {
            $client_id = getAuthClient('id');
            $this->loadModel('Invoice');
            $this->set('HasCreditNotes', $this->Invoice->find('count', ['conditions' => ['Invoice.client_id' => $client_id, 'Invoice.type' => Invoice::Credit_Note]]));
            $this->set('HasRefund', $this->Invoice->find('count', ['conditions' => ['Invoice.client_id' => $client_id, 'Invoice.type' => Invoice::Refund_Receipt]]));
        }
        set_mime_type();
        //this part is to check	if the user selects the with all in the responsiveadmin index page export options
        if (isset($_POST['conditions_link'])) {
            $conditions_link = $_POST['conditions_link'];
            $conditions_name = end(explode('/', $conditions_link));
            $conditions_part = explode('_', $conditions_name);
            $export_conditions_keys_group_mode = isset($_POST['export_conditions_keys_group_mode']) ? $_POST['export_conditions_keys_group_mode'] : false;
            $modelName = Inflector::humanize(Inflector::singularize($conditions_part[0]));
            if ($modelName) {
                $this->loadModel($modelName);
                $conditions = $this->Session->read($conditions_name);
                $temp_recursive = $this->$modelName->recursive;
                $this->$modelName->recursive=0;
                if ($this->Session->check($modelName.'_apply_branch_select_all')) {
                    $apply_branch_select_all = $this->Session->read($modelName.'_apply_branch_select_all');
                    $ids = $this->$modelName->find('all', ['applyBranchFind'=>$apply_branch_select_all,'fields'=>$modelName.'.id','conditions' => $conditions]);
                }else{
                    if($export_conditions_keys_group_mode){
                        $ids = $this->$modelName->find('all', $conditions);
                    } else {
                        $ids = $this->$modelName->find('all', ['fields'=>$modelName.'.id','conditions' => $conditions]);
                    }

                }
                $ids=Set::extract('/'.$modelName.'/id', $ids);
                $this->$modelName->recursive=$temp_recursive;
 
                // if request type is contains ids_excluded
                if (!empty($_POST['ids_excluded'])) { 
                    $ids = array_diff($ids, $_POST['ids_excluded']);
                } 
             
                $_POST['ids'] = $ids;
            }
        }
        if (!$this->RequestHandler->isAjax() && !IS_REST) {
            $this->check_filter_conditions();
        }
        if (strtolower($prefix) == 'admin' && $this->_isAdminAuthenticated()) {
            $this->set('user', ['first_name' => __('Administrator', true), 'last_name' => '', 'type' => 'admin']);
        }
        if (empty($this->titleAlias)) {
            $this->titleAlias = Inflector::humanize(Inflector::underscore($this->name));
        }
        $this->set('selectedMenu', $this->titleAlias);
        $this->set('titleAlias', $this->titleAlias);
        if (isset($_GET['stop_custom']) && $_GET['stop_custom']) {
            $additional_code = false;
            $global_code = false;
            $appsScripts = '';
        } else {
            $additional_code = settings::getValue(0, 'code_' . $this->name . '_' . $this->action,null,false, false) ?: '';

            /** @var PageScriptErrorHandlerDecorator $pageScriptService */
            $pageScriptService = resolve(PageScriptErrorHandlerDecorator::class);

            $currentUrl = Router::url($this->here);
            $appsScripts = $pageScriptService->getByURL($currentUrl);
            $global_code = settings::getValue(0, 'code_*',null,false, false) ?: '';
        }

        if (!empty($additional_code) || !empty($global_code) || $appsScripts != '') {
            $this->set('additional_code', $global_code . ' ' . $additional_code . ' ' . $appsScripts);
        }

        if (!isSuperAdmin() || isLoggedAsAdmin('AdminSystemRoles')) {
            $this->_parseBlockedPages();
        }

        // If Redis is enabled, manage user sessions using the UserSessionRedisService.
        if (useRedis()) {
            // Resolve the UserSessionRedisService to handle session management with Redis.
            $userSessionRedisService = resolve(UserSessionRedisService::class);

            // Check if the session belongs to the owner. If so, retrieve the owner details and
            // ensure their session is recorded in Redis using their site ID.
            if ($this->Session->check('OWNER')) {
                $site = getAuthOwner();
                $userSessionRedisService->checkOrAssignSession($site['id']);
            } 
            // Check if the session belongs to a client. If so, retrieve the client (staff) details
            // and ensure their session is recorded in Redis using their site ID.
            else if ($this->Session->check('CLIENT')) {
                $staff = getAuthStaff();
                $userSessionRedisService->checkOrAssignSession($staff['id']);
            }
        }


        TillNow::tillnow('After Controller BEFORE FILTER');

    }

	function afterFilter() {
	}

//--------------------------------


    public function getNotificationCacheKey()
    {
        return getNotificationCacheKey();
    }

    private function getNotificationCount()
    {

        $this->loadModel('Client');
        $user = 'owner_'.getCurrentSite('id');
        if (getAuthClient('id')) {
            $user = 'client_'.getAuthClient('id');
        }  else if (getAuthStaff('id')) {
            $user = 'staff_'.getAuthStaff('id');
        }

        $data = explode('_', $user);
        $cache_type=strtolower($data[0]);
        $data[0] = "App\\\Models\\\\".ucfirst($data[0]);
        $noti_settings_key = $cache_key = "Notification_" . $data[1] . "_" . "App\\Models\\".ucfirst($cache_type);
        $count=Cache::read($cache_key);
        if($count){
            return  $count;
        }
        $max_count=settings::getValue(0, $noti_settings_key,null,true,false);
        if($max_count>=100){
            return "$max_count"."+";
        }
       $query = "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = {$data[1]}
                 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = '{$data[0]}' 
                 and ( COALESCE(JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.dismissed_at')), 'null') = 'null' or COALESCE(JSON_UNQUOTE(JSON_EXTRACT(`data`, '$.dismissed')), 'null') = 'null') and `read_at` is null";
        Cache::write($cache_key, $count=$this->Client->query($query)[0][0]["aggregate"]);

        settings::setValue(0,$noti_settings_key,$count,false);
        return $count;

    }

    private function getSystemUpdateCount(){
        $language_code = getCurrentSite('language_code');
        if (getAuthClient('language_code')) {
            $language_code = getAuthClient('language_code');
        }  else if (getAuthStaff('language_code')) {
            $language_code = getAuthStaff('language_code');
        }
        $beta = getCurrentSite('beta_version');
        $query = "select count(*) as aggregate from `system_updates` 
        where exists (select * from `release_versions` where `system_updates`.`release_version_id` = `release_versions`.`id` and `is_beta` = {$beta}) 
        and `status` = 1 and `language_id` = {$language_code} order by `created` desc";
        $this->loadModel('SystemUpdate');
        return $this->SystemUpdate->query($query)[0][0]["aggregate"];
    }

    function setCustomFormData(){

        $CustomForm = GetObjectOrLoadModel('CustomForm');
        $item_id = null;
        $Model = GetObjectOrLoadModel(Inflector::singularize($this->name));
        // warning suppress
        if(isset($Model->actsAs)
            && array_key_exists('customform', $Model->actsAs)
            && $Model->actsAs['customform']){
            // warning suppress
            if($this->params['pass'] && $this->params['pass'][0] && $this->action != 'owner_add'){
                $item_id = $this->params['pass'][0];
            }
            $result = $CustomForm->get_display_form_fields($Model->name, $item_id);
            $this->set('table', $result['table']); //for the redirect link when you want to edit the custom form field
            $this->set('item_id', $result['item_id']); //for the redirect link when you want to edit the custom form field
            $this->set('custom_form_fields', $result['fields']);
            $this->set('many_fields', $result['many_fields']);
            $this->set('table_name', $result['table_name']); //for the redirect link when you want to edit the custom form field
            $this->set('file_form', $result['file_form']);
            $this->set('custom_form_name', $result['customForm']['title'] ?? null);
        }
    }

    function beforeRender() {
        TillNow::tillnow('BEFORE RENDER');
        $this->_get_crumbs();
        $owner = getAuthOwner() || getAuthStaff() || getAuthClient();
        if ($owner && strtolower($_SERVER['REQUEST_METHOD']) == 'get') {
            $notification_count = Cache::read($this->getNotificationCacheKey());
            if ($notification_count === false) {
                $notification_count = $this->getNotificationCount();
                 Cache::write( $this->getNotificationCacheKey(), $notification_count);
            }
            $this->set('system_update_count',0);
            $this->set('notification_count', $notification_count);
        }


        if (ifPluginActive(BranchesPlugin) && (getAuthOwner('staff_id')!=null || getAuthOwner('id'))) {
            $staffBranches = getStaffBranches();
            $staffBranchesSuspended = getStaffBranchesSuspended();
            $this->set('allStaffBranches', $staffBranches);
            $this->set('allStaffBranchesAndSuspended', $staffBranchesSuspended);
            $currentBranch = getCurrentBranchID();

            $branchName = isset($staffBranches[$currentBranch]) ? $staffBranches[$currentBranch] : '';

            unset($staffBranches[$currentBranch]);
            $this->set('staffBranches', $staffBranches);
            $this->set('activeBranch',$currentBranch);

            $this->set('branchName',$branchName);

        }

        $site = getCurrentSite();
        $isLogin = ($this->Session->check('OWNER') || $this->Session->check('CLIENT'));
        if ($isLogin && !empty($site['expiry_date']) && $site['expiry_date'] < date("Y-m-d", strtotime('+5 Days'))) {
            $this->set("about_expire", 1);
        }


        if (PHP_SAPI == 'cli' || $this->RequestHandler->isAjax()) {
            return;
        }
        $this->__load_config();

        if (empty($this->titleAlias)) {
            $this->titleAlias = Inflector::humanize(Inflector::underscore($this->name));
        }

        $this->set('is_rtl',is_rtl());
        $this->set('config', $this->config);
        $this->set('selectedMenu', $this->titleAlias);
        $this->set('titleAlias', $this->titleAlias);

        if (!empty($this->params['url']['box']) || !empty($this->params['url']['from_pos']) || !empty($_POST['box'])) {
            $this->layout = 'box';
            $this->set('isBox', true);
        }

        if (!empty($this->crumbs)) {
            $this->set('crumbs', $this->crumbs);
        }

        $this->set('translate_languages', $this->_json_lang());

        if (!empty($this->params['prefix']) && $this->params['prefix'] == 'owner' && $this->action != 'owner_first_settings') {
            if (!empty($this->params['url']['colors'])) {
                $this->set('renderColors', true);
                $this->set('logoSnippet', $this->get_snippet('quick-logo-upload', false, false));
                $this->set('colorSnippet', $this->get_snippet('quick-color-change', false, false));
                $this->set('logoSuccess', $this->get_snippet('logo-color-success', false, false));
            }
        }


        if ($this->Session->check('OWNER') && isset($this->params['prefix']) && strtolower($this->params['prefix']) == 'client') {
            $this->set('logged_as_client', true);
        }

        $timeZone = PortalCache::get('timezone_'. $site['timezone'], function () use ($site) {
            $this->loadModel('Timezone');
            $timeZone = $this->Timezone->findById($site['timezone']);
            return $timeZone['Timezone'];
        });

        $zone = 'Australia/Sydney';
        if (!empty($timeZone['zone_name'])) {
            $zone = $timeZone['zone_name'];
        }

        $dateFormats = getDateFormats('std');
        $dateFormat = $dateFormats[intval($site['date_format'])];
        $date = new DateTime;
        $timeZoneObj = new DateTimeZone($zone);
        if (!$timeZone) {
            $timeZoneObj = new DateTimeZone('UTC');
        }
        $date->setTimezone($timeZoneObj);
        $this->set('siteTime', $date->format($dateFormat . ' H:i'));
        $this->set('timeZone', $timeZoneObj->getName());

        if ($this->layout == 'box' and $this->lang == 'ara') {
            $this->layout = 'side_box';
        }
        $breadCrumbsExcludedActions = [
            'Sites.owner_dashboard'
        ];
        $actionName = "{$this->name}.{$this->action}";
        if(strpos($_SERVER['REQUEST_URI'],'api2') === false && !in_array($actionName, $breadCrumbsExcludedActions) && $this->name !== 'Reports' && file_exists(ROOT.DS.APP_DIR.DS.'models'.DS.strtolower(Inflector::singularize(Inflector::underscore($this->name))).'.php') )
        {
            $Model = GetObjectOrLoadModel(Inflector::singularize($this->name));
            if (empty($this->params['action']) || $this->params['action'] != "owner_add_advance_payment" || $this->params['action'] != "owner_add_advance_payment") {
                $this->addBreadCrumbs($Model, $this->params);
            }

            if(in_array($this->action, ['owner_add', 'owner_edit', 'owner_add_refund', 'owner_add_creditnote','owner_edit_creditnote','owner_edit_refund', 'owner_convert_to_invoice', 'owner_add_advance_payment']) && ($this->name != 'PaymentGateways' ) ){

                $this->setCustomFormData();

            }else if($this->action == 'owner_view' && $this->name != 'PaymentGateways'){
                $Model = GetObjectOrLoadModel(Inflector::singularize($this->name));
                $CustomForm = GetObjectOrLoadModel('CustomForm');
                $item_id = null;

                // warning suppress
                if(isset($Model->actsAs)
                    && array_key_exists('customform', $Model->actsAs)
                    && $Model->actsAs['customform']){
                    if($this->params['pass'][0]){
                        $result = $Model->prepare_custom_fields();
                        $this->set('sections', $result);
                    }
                }
            }
        }
     


		if($this->Session->read('go_to_custom_details')){
			$redirect = $this->Session->read('go_to_custom_details');
			$this->Session->delete('go_to_custom_details');
			$this->redirect($redirect);
		}
        $this->sharedVars['extraContainerClass'] = 'container-full';
        TillNow::tillnow('AFTER BEFORE RENDER');
    }


    public function handleResponseIframe($action, $data = [])
    {
        $this->set('action', $action);
        $this->set('data', $data);
        $this->layout = 'box';
        $this->render('/elements/result_js');
    }

    public function initBoxLayout()
    {
        $this->layout = 'box';
         $this->params['url']['box'] = 1;
        $this->set('box', true);
    }
//--------------------------------
    function _get_crumbs() {
        if (!isset($this->viewVars['crumbs'])) {
            $crumbs = array();
            $prefix = empty($this->params['prefix']) ? '' : $this->params['prefix'];

            if ($this->name != 'CakeError') {
                $crumbs[0]['title'] = __($this->titleAlias, true);
                $crumbs[0]['url'] = "/$prefix/" . Inflector::underscore($this->name);
            }
            $action = str_replace($prefix . "_", '', $this->action);
            if (!empty($action) && $action != 'index') {
                $crumbs[1]['title'] = __(Inflector::humanize($action), true);
                $crumbs[1]['url'] = '#'; //Router::url(array('action' => $action));
            }

            $this->set('crumbs', $crumbs);
        }
    }

//----------------------------
    function _is_client() {
        if ($this->Session->check('CLIENT')) {
            return $this->Session->read('CLIENT');
        }
        return false;
    }

    // 
    function _SecureSession()
    {
        $this->Site = ClassRegistry::init(['class' => 'Site', 'ds' => 'portal']);
        $original_host = $_SERVER['HTTP_HOST'];
        $main_domain = explode('.', $original_host, 2);
        $host = str_replace(Beta_Domain, Domain_Short_Name, $original_host);
        $con = ['Site.subdomain' => $host];
         $site = $this->Site->find('first',['conditions'=>$con]);
        $hostSlug = str_replace('.', '_', $host);
        $site_id = $site['Site']['id'];
            $cs = $this->Session->read('CurrentSite');
        if(empty($cs)) {
            \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO, "Session CurrentSite is empty ",
                [
                    'site_id' => $site_id,
                    'session_site_id' => $cs['id'],
                    'CurrentSite' => $cs,
                ]
            );
        }
            if (!empty($site_id) && !empty($cs) && $site_id != $cs['id']) {
                \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , "Session Not Match ",
                    [
                        'site_id' => $site_id,
                        'session_site_id' => $cs['id'],
                        'CurrentSite' => $cs,
                    ]
                );
                $this->Cookie->destroy();
                $this->Session->destroy();
                $this->flashMessage(__('You need to login', true), 'Errormessage', 'Loginpage');
                $this->redirect('/');
            }
//        } else if(strpos($_SERVER['REQUEST_URI'],'api2') === false){
//            if (isset($this->params['plugin'])) {
//                $this->Site = ClassRegistry::init(['class' => 'Site', 'ds' => 'portal']);
//            } else {
//                $this->loadModel("Site");
//            }
//            $con = ['Site.subdomain' => $host];
//            $site = $this->Site->find($con);
//            $cs = $this->Session->read('CurrentSite');
//            if ($cs['id'] != $site['Site']['id']) {
//                $this->Session->destroy();
//                $this->flashMessage(__('You need to login', true), 'Errormessage', 'Loginpage');
//                $this->redirect('/');
//            }
//        }
    }

    function __load_config()
    {
        $this->config = parse_ini_file(WWW_ROOT . '../app_config.ini');
        $this->config['txt.site_name'] = Site_Full_name_NoSpace;
        $this->config['txt.domain'] = Domain_Short_Name;
        $this->config['txt.maildelivery'] = MailDeliveryEmail;
        $this->config['txt.admin_mail'] = AdminEmail;
        $this->config['txt.send_mail_from'] = Send_Mail_From;
    }

    /**
     * A better warper for $this->Session->setFlash()
     * @param String $message the message to set in flash session variable
     * @param String $class the class for the output mesage div, defaults to 'Errormessage'
     * @param String $key
     */
    function flashMessage($message, $class = 'Errormessage', $key = 'flash') {
        $this->Session->setFlash($message, 'default', array('class' => $class), $key);
    }

    function izamFlashMessage($message, $type = 'success') {
        $this->flashMessage($message, $type);
    }

    function owner_undo() {
        $this->loadModel("ImportData");
        App::import('Vendor', 'imported');

        if ($this->name == 'time_tracking') {
            $model = 'TimeTracking';
        } else {
            $model = Inflector::singularize($this->name);
        }
        // For Incomes Route Only
        if (!empty($this->params['is_income'])) {
            $this->name = __('Incomes', true);
        }
        if (!empty($this->modelName)) {
            $model = $this->modelName;
        }
        if (empty($this->{$model}->import)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
    }

    function owner_import() {
        set_time_limit(3600);
        ini_set('memory_limit','5G');
        $title = __("Import",true) .' '. __($this->name == 'TimeTracking' ? 'Time sheet' : $this->name,true);
        $this->set('title_for_layout',  $title);

        $this->loadModel("ImportData");
        App::import('Vendor', 'imported');
        if ($this->name == "TimeTracking" && !check_permission(Track_All_Staffs_Times) && !check_permission(Edit_All_Timesheets)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if ($this->name == "Expenses" && !check_permission(View_All_expenses) && !check_permission(View_All_incomes)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        if ($this->name == 'time_tracking') {
            $model = 'TimeTracking';
        } else {
            $model = Inflector::singularize($this->name);
        }
        // For Incomes Route Only
        if (!empty($this->params['is_income'])) {
            $this->name = __('Incomes', true);
        }
        if (!empty($this->modelName)) {
            $model = $this->modelName;
        }
        if (empty($this->{$model}->import)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }


        $this->set('content', $this->get_snippet(strtolower($model) . '-import'));
        if (!empty($this->params['is_income'])) {
            $this->set('content', $this->get_snippet('income-import'));
        }

        $import_data_keys = $this->{$model}->get_import_data();
        if (($this->data['Expense']['is_mileage'])) {
            $this->name = 'Mileages';
            $import_data_keys = $this->{$model}->get_mileage_import_data();
        }
        $this->set('imports', $import_data_keys);
        $this->set('modelName', $model);
        if (!empty($this->data)) {
            $file_path = '/var/www/html/' . CAKE_DIR . '/webroot/files' . DS . 'import_data' . DS;
            if ($this->data[$model]['step'] == 1) {
                if (empty($this->data[$model]['delimiter'])) {
                    $this->flashMessage(__('Please Select Delimiter', TRUE));
                    $this->redirect(array('action' => 'import'));
                }
                if (empty($this->data[$model]['file']['tmp_name'])) {
                    $this->flashMessage(__('Please select a file to import', TRUE));
                    $this->redirect(array('action' => 'import'));
                }
                if (filesize($this->data[$model]['file']['tmp_name']) == 0) {
                    $this->flashMessage(__('Please Select file to import', TRUE));
                    $this->redirect(array('action' => 'import'));
                }



                $file = utf8_fopen_read($this->data[$model]['file']['tmp_name'], "r");
                $header = fgetcsv($file, 0, $this->data[$model]['delimiter']);
                $header = array_map(function($string) {
                    $string = preg_replace('/[\x00-\x1F\x7F]/', '', $string);
                    $string = preg_replace('/[\x00-\x1F\x7F]/u', '', $string);
                    $string = preg_replace('/[\x00-\x1F\x7F\xA0]/u', '', $string);
                    return $string;
                }, $header);
                $header = array_map('utf8_encode',$header);
                if (count($header) <= 1 and $this->{$model}->allow_one_column != TRUE) {
                    $this->flashMessage(__('Sorry, it is not allowed to import one column file, make sure you have selected a file with the correct format', TRUE));
                    $this->redirect(array('action' => 'import'));
                }

                fclose($file);
                $handle = utf8_fopen_read($this->data[$model]['file']['tmp_name'], "r");
                while (($data = fgetcsv($handle, 0, $this->data[$model]['delimiter'])) !== FALSE) {
                    if (count($data) != count($header)) {
                        $this->flashMessage(__('CSV columns count is not the same through all file rows', TRUE));
                        $this->redirect(array('action' => 'import'));
                    }
                }
   
                if( !file_exists($file_path) ){
                    mkdir($file_path, 0777 , true );
                }
                $check_moved_file=move_uploaded_file($this->data[$model]['file']['tmp_name'], $file_path . ucfirst($model) . '_' . getAuthOwner('id') . '_' . getAuthOwner('staff_id') . '.csv');
                if (!$check_moved_file ) {
                    \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , 'directory ' . $file_path. ' does not exist');
                    debug(['directory ' . $file_path. ' does not exist']);
                }
                $header = array_map('utf8_decode',$header);
                $this->set('header_list', $header);
                $this->Session->write('header_list', $header);
                
                $this->render('/importing_data/step2');
            }
            if ($this->data[$model]['step'] == 2) {
                $this->loadModel('EntityAppData');
                $this->EntityAppData->create();
                $entityAppRecord = $this->EntityAppData->save([
                    'EntityAppData' => [
                        'entity_key' => $model,
                        'entity_id' => 0,
                        'app_key' => 'import',
                        'action_key' => '',
                    ]]);
                $importUUID = $this->EntityAppData->getLastInsertId();

                $con = false;
                $Fields = $this->data['Field'];

                $Fields = array_filter($Fields, function ($item) {
                    if ($item == "") {
                        return false;
                    }
                    return true;
                });
                $imports = $this->{$model}->get_import_data() ;
                if($this->data['Expense']['is_mileage']) $imports = $this->{$model}->get_mileage_import_data();
                //$imports = $this->{$model}->import;
                foreach ($Fields as $key => $Field) {
                    if (isset($imports[$key]['is_unique']) and $imports[$key]['is_unique'] == true) {
                        $con = true;
                    }
                    if ($imports[$key]['required'] == true and trim($Field) == "") {
                        $import_error = true;
                    }
                }
                if (!isset($import_error)) {
                    if (($handle = utf8_fopen_read($file_path . DS . ucfirst($model) . '_' . getAuthOwner('id') . '_' . getAuthOwner('staff_id') . '.csv', "r")) !== FALSE) {
                        $i = 0;
                        $sucess_record = 0;
                        $fail_record = 0;
                        $warning_record = 0;
                        $updated_record = 0;
                        $list_count=0;
                        $fail_list = array();
                        $header_list = $this->Session->read('header_list');
                        while (($data = fgetcsv($handle, 0, $this->data[$model]['delimiter'])) !== FALSE) {
                            $errr=false;
                            if ($this->data[$model]['first_row'] == "0" && $i == 0) {
                                $i++;
                                continue;
                            }
                            debug($handle);
                            debug($this->data[$model]['delimiter']);
                            $row = array();
                            $mydata = array('_uuid' => $importUUID);
                            $conditions = array();

                            foreach ($Fields as $key => $value) {
                                if (!is_array($value)) {
                                    continue;
                                }

                                foreach ($value as $innerKey => $innerValue) {
                                    $newKey = $key . '.' . $innerKey;
                                    $Fields[$newKey] = $innerValue;
                                }
                                unset($Fields[$key]);
                            }
                            
                            foreach ($Fields as $key => $Field) {
                                if ($Field != "") {
                                    debug($key);
                                    debug($Field);
                                    debug($data[$Field]);
                                    $oFormat = $data[$Field];
                                    $mydata[$model][$key] = Imported::import_format($data[$Field], $imports[$key]['format']);

                                    if ($imports[$key]['format'] == 'date' and empty($mydata[$model][$key])) {
                                        $correctDateFormat = getDateFormats('moment_js')[getCurrentSite('date_format')] ?? '';
                                        unset($mydata);
                                        $fail_record++;
                                        $list_count++;
                                        $fail_list[] = sprintf(
                                            __("Error, line %s : %s, %s", true),
                                            $i,
                                            $header_list[$Field],
                                            sprintf(
                                                __("You Imported Date With Invalid Format, the correct format is ( %s )", true),
                                                $correctDateFormat
                                            )
                                        );
                                        break;
                                    }  if ($imports[$key]['format'] == 'date' and empty($mydata[$model][$key])) {
                                        unset($mydata);
                                        $fail_record++;
                                        $list_count++;
                                        $fail_list[] = sprintf(__("Error, line %s : {$header_list[$Field]} has invalid format...ignored", true), $i);
                                        break;
                                    }


                                    if ($imports[$key]['required'] == 1 and empty($mydata[$model][$key])) {
                                        unset($mydata);
                                        $fail_record++;
                                        $list_count++;
                                        $fail_list[] = sprintf(__("Error, line %s : {$header_list[$Field]} is required...ignored", true), $i);
                                        $log_list[] = array('class'=>'text-danger','text'=>sprintf(__("Error, line %s : {$key} is required...ignored", true), $i));
                                        if(in_array($i, array(0,1,2))){
                                            $this->flashMessage(sprintf(__("Error, line %s : {$header_list[$Field]} is required", true), $i));
                                          $this->redirect(array('#' => 'start'));
                                        }
                                        break;
                                    }

                                    if ($imports[$key]['positive'] == 1 &&  $mydata[$model][$key] !== "" && (!is_numeric($mydata[$model][$key]) || (float) $mydata[$model][$key] < 0 )) {
                                        unset($mydata);
                                        $fail_record++;
                                        $list_count++;
                                        $fail_list[] = sprintf(__("Invalid Data, Data must be greater than or equal zero %s at row %s", true), $i+1, $header_list[$Field]);
                                        $log_list[] = array('class'=>'text-danger','text'=>sprintf(__("Invalid Data, Data must be greater than or equal zero %s at row %s", true), $i+1, $key));
                                        if(in_array($i, array(0,1,2))){
                                            $this->flashMessage(sprintf(__("Invalid Data, Data must be greater than or equal zero %s at row %s", true), $i+1, $header_list[$Field]));
                                            $this->redirect(array('#' => 'start'));
                                        }
                                        break;
                                    }

                                    if ($imports[$key]['format'] == 'currency_code' and empty($mydata[$model][$key])) {
                                        $mydata[$model][$key] = getAuthOwner('currency_code');
                                    }

                                    if ($imports[$key]['format'] == 'email' and empty($mydata[$model][$key])) {
                                        $fail_list[] = sprintf(__("Warning, line %s : {$header_list[$Field]} has invalid format...ignored", true), $i);

                                        $warning_record++;
                                        $list_count++;
                                    }

                                }
                            }

                            if($errr){
                                continue;
                            }
                            if (is_countable($mydata) && count($mydata) != 0) {//php8 fix

                                foreach ($Fields as $key => $Field) {
                                    if($this->data[$model]['update_field']==""){
                                    if (isset ( $imports[$key]['is_unique']) &&  $imports[$key]['is_unique'] == true and trim($data[$Field]) != "") {
                                        $conditions['OR'][$model . '.' . $key] = trim($data[$Field]);
                                    }
                                    }

                                     if($this->data[$model]['update_field']==$key){
                                     $conditions[$model . '.' . $key] = trim($data[$Field]);
                                     }
                                }

                                if (!empty($conditions)) {


                                    $row = $this->{$model}->find('first', array('conditions' => $conditions));
                                    if (isset($row[$model]['id']) and $this->data[$model]['update_data'] == 1) {
                                        $old_data[$row[$model]['id']] = $row;
                                        $mydata[$model]['id'] = $row[$model]['id'];
                                    }
                                }


                                $mydata[$model]['site_id'] = getAuthOwner('id');
                                $mydata[$model]['staff_id'] = getAuthOwner('staff_id');



                                if (($this->data[$model]['first_row'] == "1" and $i == 0) or $i != 0) {
                                    if (method_exists($this->{$model}, 'before_import')) {                   
                                        $mydata = $this->{$model}->before_import($mydata);
                                    }
                                }


                                if (!empty($this->params['is_income'])) {
                                    $mydata[$model]['is_income'] = 1;
                                }

                                $this->loadModel('CustomTable');

                                if (!isset($row[$model]['id']) or (isset($row[$model]['id']) and $this->data[$model]['update_data'] == 1)) {
                                    if ($this->data[$model]['first_row'] == "1" and $i == 0) {
                                        $this->{$model}->create();
                                        if (method_exists($this->{$model}, 'before_import_save')) {
                                            $mydata = $this->{$model}->before_import_save($mydata, $this->data[$model]['update_field']);
                                        }
                                        if ($row = $this->{$model}->save($mydata, false)) {

											if (method_exists($this->{$model}, 'after_import_save')) {
                                                 $this->{$model}->after_import_save($mydata);
                                                }
                                                $row[$model]['id'] = $this->{$model}->id;
                                                if (method_exists($this->{$model}, 'import_view_link')) {
                                               $link=$this->{$model}->import_view_link($row[$model]['id']);
                                               }else{
                                               $link=$row[$model]['id'];
                                               }

                                            if (!isset($mydata[$model]['id'])) {
                                                $sucess_import = $sucess_import + 1;
                                                $sucess_record++;
                                            } else {
                                                $updated_record++;
                                            }
                                        } else {
                                            $fail_record++;
                                            $log_list[] = array('class'=>'text-danger','text'=>sprintf(__("Error, line %s is duplicated...ignored", true), $i));
                                        }
                                    } elseif ($i != 0) {

                                        $this->{$model}->create();
                                        if (!isset($mydata[$model]['id'])) {
                                            if (method_exists($this->{$model}, 'check_import')) {

                                                $check_result=$this->{$model}->check_import($mydata);
                                                if($check_result['status']==false) {

                                                    $fail_record++;
                                                    $log_list[] = array('class'=>'text-danger','text'=>sprintf(__("Error, line %s :%s", true), $i,$check_result['message']));
                                                    $list_count++;
                                                    $i++;
                                                    continue;
                                                }
                                            }
                                            if (method_exists($this->{$model}, 'before_import_save')) {
                                                $mydata = $this->{$model}->before_import_save($mydata, $this->data[$model]['update_field']);
                                            }
                                            if ($row = $this->{$model}->save($mydata, false)) {
                                                if (method_exists($this->{$model}, 'after_import_save')) {
                                                 $this->{$model}->after_import_save($mydata);
                                                }
                                                $row[$model]['id'] = $this->{$model}->id;
                                                if (method_exists($this->{$model}, 'import_view_link')) {
                                               $link=$this->{$model}->import_view_link($row[$model]['id']);
                                               }else{
                                               $link=$row[$model]['id'];
                                               }
                                               $log_list[] = array('class'=>'text-success','text'=>sprintf(__("Created new record %s", true), $link));

                                                $sucess_record++;
                                                $addrow[] = $this->{$model}->id;
                                            } else {
                                                $fail_record++;
                                                $log_list[] = array('class'=>'text-danger','text'=>sprintf(__("Error, line %s is duplicated ", true), $i));

                                            }


                                        } else {
                                            if (method_exists($this->{$model}, 'before_import_save')) {
                                                $mydata = $this->{$model}->before_import_save($mydata, $this->data[$model]['update_field']);
                                            }
                                            if (method_exists($this->{$model}, 'check_import')) {
                                                $check_result=$this->{$model}->check_import($mydata);
                                                if($check_result['status']==false) {
                                                    $fail_record++;
                                                    $log_list[] = array('class'=>'text-danger','text'=>sprintf(__("Error, line %s :%s", true), $i,$check_result['message']));
                                                    $list_count++;
                                                    $i++;
                                                    continue;
                                                }
                                            }
                                            if ($row = $this->{$model}->save($mydata, false, array_keys($mydata[$model]))) {
												if (method_exists($this->{$model}, 'after_import_save')) {
                                                 $this->{$model}->after_import_save($mydata);
                                                }
                                               if (method_exists($this->{$model}, 'import_view_link')) {
                                               $link=$this->{$model}->import_view_link($row[$model]['id']);
                                               }else{
                                               $link=$row[$model]['id'];
                                               }
                                               $log_list[] = array('class'=>'text-success','text'=>sprintf(__("Updated record %s", true), $link));
                                                $updated_record++;
                                            } else {
                                                $fail_record++;
                                                 if (method_exists($this->{$model}, 'import_view_link')) {
                                               $link=$this->{$model}->import_view_link($row[$model]['id']);
                                               }else{
                                               $link=$row[$model]['id'];
                                               }
                                                $log_list[] = array('class'=>'text-danger','text'=>sprintf(__("Error updating record %s", true),$link));
                                            }
                                        }
                                    }
                                    debug ( $mydata ) ;

                                    if (isset($mydata ['CustomTable']) && $row) {
                                        $this->CustomTable->_modelSettings($mydata ['CustomTable'], [], 0);
                                        $this->CustomTable->create();
                                        $mydata[$mydata ['CustomTable']][$mydata['CustomTableId']] = $row[$model]['id'];
                                        $ct_temp = $this->CustomTable->find('first', ["conditions" => [$mydata['CustomTableId'] => $row[$model]['id']]]);

                                        if (!empty($ct_temp)) {
                                            $mydata[$mydata ['CustomTable']]["id"] = $ct_temp[$mydata ['CustomTable']]['id'];
                                        }
                                        unset($mydata['CustomTable']);
                                        unset($mydata['CustomTableId']);
                                        $this->CustomTable->save($mydata);
                                    }

                                    if (isset($mydata['entity_key'])) {
                                        $additionalFormsHandler =
                                            new \App\Services\LocalEntityForm\AdditionalFormsHandler(
                                                $mydata['entity_key']
                                            );
                                        $additionalFormsHandler->update($row[$model]['id'], $mydata);
                                    }

                                    if (($this->data[$model]['first_row'] == "1" and $i == 0) or $i != 0) {
                                        if (!empty($row)) {

                                            if (method_exists($this->{$model}, 'after_import')) {
                                                $this->{$model}->after_import($mydata, $row);
                                            }
                                        }
                                    }
                                    if (empty($row)) {
                                        debug($this->{$model}->invalidFields());
                                    }
                                } else {
                                    debug('test222');
                                    $fail_record++;

                                    $log_list[] = array('class'=>'text-danger','text'=>sprintf(__("Error in line %s is duplicated...ignored", true), $i));
                                }
                            }

                            $i++;
                        }
                        fclose($handle);

                        $import_data['ImportData']['site_id'] = getAuthOwner('id');
                        $import_data['ImportData']['staff_id'] = getAuthOwner('staff_id');
                        $import_data['ImportData']['model_name'] = $model;
                        $import_data['ImportData']['number_of_add'] = $sucess_record;
                        $import_data['ImportData']['number_of_update'] = $updated_record;
                        $import_data['ImportData']['number_of_fail'] = $fail_record;
                        $import_data['ImportData']['file'] = ucfirst($model) . '_' . getAuthOwner('id') . '_' . getAuthOwner('staff_id') . '.csv';
                        $result_data['added'] = $addrow;
                        $result_data['updated'] = $old_data;
                        $import_data['ImportData']['result_data'] = json_encode($result_data);

                        $importData = PortalImportData::create($import_data['ImportData']);
                        rename($file_path . DS . ucfirst($model) . '_' . getAuthOwner('id') . '_' . getAuthOwner('staff_id') . '.csv', $file_path . DS . ucfirst($model) . '_' . getAuthOwner('id') . '_' . getAuthOwner('staff_id') . '_' . $this->ImportData->id . '.csv');
                        $this->set('error', $error);


                        $this->set('sucess_record', $sucess_record);
                        $this->set('updated_record', $updated_record);
                        $this->set('fail_record', $fail_record);
                        $this->set('warning_record', $warning_record);
                        $this->set('fail_list', $fail_list);
                        $this->set('log_list', $log_list);
                        $this->add_actionline(ACTION_IMPORT_DATA, array('primary_id' => 0, 'secondary_id' => 0, 'param2' => $sucess_record, 'param3' => $updated_record, 'param4' => $this->name, 'param5' => $fail_record, 'param6' => $warning_record));

                        $this->render('/importing_data/final');
                    } else {
                        $this->redirect(array('#' => 'start'));
                    }
                } else {
                    $this->set('header_list', $this->Session->read('header_list'));
                    $this->flashMessage(__('Please, enter the value for all required inputs marked with \"*\"', true));
                    $this->render('/importing_data/step2');
                }
            }
        } else {
            $this->set('delimiters', array(',' => 'Comma Separated ( , )', ';' => 'Semicolon Separated ( ; )', "\t" => 'Tab Delimited'));

            $this->render('/importing_data/index');
        }
    }

    function owner_update_display_order() {
        $params = $this->params['form'];
        $model = Inflector::singularize($this->name);
        if (!empty($this->modelName))
            $model = $this->modelName;
        foreach ($params as $field => $value) {
            if (strpos($field, 'display_order') === false) {
                continue;
            }
            $id = substr($field, strlen('display_order_'));
            $this->{$model}->create();
            $this->{$model}->id = $id;
            $this->{$model}->saveField('display_order', intval($value));
        }
        $this->redirect($this->referer(array('action' => 'index'), true));
    }

//----------------------------------
    function owner_update_active($field_name = 'active') {
        $params = $this->params['form'];

        $model = Inflector::singularize($this->name);
        if (!empty($this->modelName))
            $model = $this->modelName;
        foreach ($params as $field => $value) {
            if (strpos($field, $field_name) === false) {
                continue;
            }
            $id = substr($field, strlen($field_name . '_'));
            $this->{$model}->create();
            $this->{$model}->id = $id;
            $this->{$model}->saveField($field_name, intval($value));
        }
        $this->redirect($this->referer(array('action' => 'index'), true));
    }

//----------------------------------

    function reset_filter() {
        $ajax = $this->RequestHandler->isAjax();
        $this->autoRender = false;
        $modelName = Inflector::singularize($this->name);
         if ( $modelName == 'ClientAppointment' ) {
             $modelName = 'FollowUpReminder' ;
         }
        if (!empty($this->modelName)) {
            $modelName = $this->modelName;
        }
        $this->Session->delete("{$modelName}_Filter");
        if ($ajax) {
            echo "Success";
            die();
        } else {
            $controller = Inflector::tableize($this->name);

            $this->redirect(array('owner' => true, 'controller' => strtolower($controller), 'action' => 'index' , '?' => 'reset=1'));
        }
    }
    function client_reset_filter() {
        $ajax = $this->RequestHandler->isAjax();
        $this->autoRender = false;
        $modelName = Inflector::singularize($this->name);

        if (!empty($this->modelName)) {
            $modelName = $this->modelName;
        }
        $this->Session->delete("{$modelName}_Filter");
        if ($ajax) {
            echo "Success";
        } else {
            $controller = Inflector::tableize($this->name);

            $this->redirect(array('owner' => true, 'controller' => strtolower($controller), 'action' => 'index'));
        }
    }


    function _isOwnerAuthenticated() {
        if ($this->Session->check('OWNER') || IS_REST) {
            $user = $this->Session->read('OWNER');
            if($user['staff_id']!=0){
                $sessionStaff=$this->Session->read('STAFF');
                $this->loadModel('Staff');
                $dbStaff = $this->Staff->find('first',array('conditions' => ['Staff.id' => $sessionStaff['id']]));
                if($sessionStaff['auth_id'] != $dbStaff['Staff']['auth_id']){
                    $this->Session->destroy();
                    $this->Cookie->destroy();
                    return false;
                }
            }
            if ( (!empty($user['first_login']) && $user['first_login'] == 1) && $this->action != 'owner_first_settings' && $this->action != 'owner_logout' && $this->params['controller'] != 'sites_enquiries') {
                $this->redirect(str_replace('https://', 'https://', Router::url(array('controller' => 'sites', 'action' => 'first_settings'), true)));
            }

            $this->set('user', $user);
			App::import('Vendor', 'notification_2');

            $this->set('staff', $this->Session->read('STAFF'));
            return true;
        }


        return $this->auto_login();
    }

    function _isClientAuthenticated() {
        if ($this->Session->check('CLIENT')) {
			$user =  $this->Session->read('CLIENT');
            $this->set('user', $user);
			App::import('Vendor', 'notification_2');
			$client_settings = $this->Session->read('CLIENT_SETTINGS');
			$this->set('client_settings',$client_settings);
			$this->Invoice = ClassRegistry::init('Invoice');
			$this->loadModel('WorkOrder');
			$this->Post = ClassRegistry::init('Post');

            $this->set('client_invoices_count',$this->Invoice->find('count',array('conditions' => array('Invoice.client_id' => $this->Session->read('CLIENT.id')))));
            $this->set('client_work_orders_count', 
                $this->WorkOrder->find('count', [
                    'conditions' => [
                        'WorkOrder.client_id' => $this->Session->read('CLIENT.id'),
                        'WorkOrder.workflow_type_id' => null
                    ]
                ])
            );

            $this->set('client_workflows_count',
                $this->WorkOrder->find('count', [
                    'conditions' => [
                        'WorkOrder.client_id' => $this->Session->read('CLIENT.id'),
                        'WorkOrder.workflow_type_id IS NOT NULL'
                    ]
                ])
            );

            $this->set('client_shared_notes_count',$this->Post->find('count',array('conditions' => array('Post.partner_type' => FollowUp::CLIENT_PARTNER_TYPE,'Post.partner_id' => $this->Session->read('CLIENT.id'), 'OR' => ['Post.client_permissions' => 1, 'Post.share_with_partner' => 1]))));
            $this->set('is_estimates_disabled',settings::getValue(InvoicesPlugin, 'disable_estimate_module'));
        }

        return $this->Session->check('CLIENT');
    }


    function _isAdminAuthenticated() {
        if (!empty($this->params['url']['admin'])) {
            $this->Session->write('allow_admin', 1);
        }

        if (!$this->Session->check('allow_admin')) {
            $this->cakeError('error404');
        }
        $httpUser = isset($_SERVER['PHP_AUTH_USER']) ? $_SERVER['PHP_AUTH_USER'] : '';
        $httpPassword = isset($_SERVER['PHP_AUTH_PW']) ? md5($_SERVER['PHP_AUTH_PW']) : '';
        if ($httpUser == $this->config['txt.admin_user_name'] && $httpPassword == $this->config['pwd.admin_password']) {
            return true;
        } else {
            header('HTTP/1.0 401 Authentication Required');
            header('WWW-Authenticate: Basic realm="Admin Area"');
            die('You are not allowed to access this section please click <a href="/">here</a> to go to home page');
        }
    }

    function filter_field_html($filter_key, $passed_model_name = false)
    {
        if ($filter_key) {
            $this->_filter_params();
            $modelName = Inflector::singularize($this->name);
            if (!empty($this->modelName)) {
                $modelName = $this->modelName;
            }
            if ($passed_model_name)
                $modelName = $passed_model_name;
            if (method_exists($this->{$modelName}, 'getFilters')) {
                $filters = $this->{$modelName}->getFilters();
            } else {
                $filters = [];
            }
            $this->set(compact('filters'));
            $this->set('filter_key', $filter_key);
            $this->set('modelName', $modelName);
            $this->render('../elements/filter_field');
        }
    }

	/**
	 *
	 * @param type $params
	 * @param string $filters
	 * @param type $passedModelName
	 * @return array
	 */
    function _filter_params($params = false, $filters = array(), $passedModelName = false) {
        $modelName = Inflector::singularize($this->name);
         if ( $modelName == 'ClientAppointment' ) {
             $modelName = 'FollowUpReminder' ;
         }
        $conditions = array();

        if (!empty($this->modelName)) {
            $modelName = $this->modelName;
        }
		$modelName = $passedModelName ? $passedModelName : $modelName ;

        if (empty($filters) && method_exists($this->{$modelName},'getFilters')) {
            $filters = $this->{$modelName}->getFilters();
        }

        // warning suppress
		if(isset($this->{$modelName}->actsAs) && array_key_exists('customform', $this->{$modelName}->actsAs) && $this->{$modelName}->actsAs['customform']){
			//customform behaviour
            $this->loadModel('CustomForm');

			$custom_filters = $this->{$modelName}->get_custom_fields_filters();

			if($custom_filters){
				$filters = array_merge($custom_filters,$filters);
			}
        }

		$url_params = $this->params['url'];
        unset($url_params['url'], $url_params['page'], $url_params['sort'], $url_params['direction']);
        $params = empty($params) ? $url_params : $params;
        // warning suppress
        $params['tags'] = null;
        if (array_key_exists('data', $params)) {
            $params['tags'] = $params['data'][$modelName]['tags'];
        }
        // end warning suppress
        $sessionParams = $this->Session->read("{$modelName}_Filter");
//
		foreach($params as $k => $v)
		{
            if(!empty($v) && isset($filters[$k]) && is_array($filters[$k])){

				$filters[$k]['more-options'] = 0;
				isset($filters[$k]['div-class']) ? ($filters[$k]['div-class'] .= ' filtered') : ($filters[$k]['div-class'] = 'filtered' );
			}
		}

        $this->set(compact('filters', 'modelName'));

        if(isset($this->params['url']['tag_type']) && !empty($this->params['url']['tag_type']) && (!empty($this->params['url']['tags']) || !empty($this->params['url']['tag'])))
        {
            $items_ids = $this->filter_tags();
            if(!$items_ids)
                $items_ids = [];
//					 dd($items_ids);
            $conditions = array_merge($conditions,$items_ids);
        }
        if (empty($filters)) {
            return $conditions;
        }

        if ($sessionParams && empty($params)) {
            $params = $sessionParams;
        } elseif (!empty($params)) {
            $this->Session->write("{$modelName}_Filter", $params);
        }

        // warning suppress
        $type = '';
        foreach ($filters as $field => $filters) {
            if($field == 'selector')
                continue;
			$conditionModelName = $modelName;
            if (is_numeric($field)) {
                $field = $filters;
                $type = '=';
                $param = $field;
            } elseif (is_string($filters)) {
                $type = $filters;
                $param = $field;
            } elseif (is_array($filters)) {
                $type = empty($filters['type']) ? '=' : $filters['type'];
                $param = $field;
            }
			$fieldParts = explode('.',$field);
			if(count($fieldParts) > 1)
			{
				//first part is model name second one is fieldname
				$conditionModelName = $fieldParts[0];
				$param = $fieldParts[1];
				$field = $param;
			}

            $typeCheck = is_string($type) ? low($type) : null;
            switch ($typeCheck) {
                case 'custom_field':
                    if (!empty($params[$param]) || (isset($params[$param]) && strval($params[$param]) === '0')) {
                        $this->loadModel('Client');
                        if (isset($params['type_' . $param]) && $params['type_' . $param] == "date") {
                            $conditions["CustomModel.$field"] = $this->Client->formatDate($params[$param]);
                        } else if (isset($params['type_' . $param]) && $params['type_' . $param] == "datetime") {
                            $conditions["CustomModel.$field"] = $this->Client->formatDateTime($params[$param]);
                        } else {
                            if (is_array($params[$param])) {
                                $temp_or_condition = [];
                                foreach ($params[$param] as $k => $par) {
                                    $temp_or_condition['OR'][] = "FIND_IN_SET('{$par}',CustomTable.$field)";
                                }
                                $conditions[] = $temp_or_condition;
                            } else {
                                $conditions["CustomModel.$field LIKE"] = "%{$params[$param]}%";
                            }
                        }
                    }
                    break;
                case 'hidden':
                case '=' :
                case 'radio':
                    if (!empty($params[$param]) || (isset($params[$param]) && strval($params[$param]) === '0')) {
                        $conditions["$conditionModelName.$field"] = $params[$param];
                    }
                    break;
                case 'or_like':
                    if (!empty($params[$param]) || (isset($params[$param]) && strval($params[$param]) === '0')) {
                        $conditions['OR'] = ["$conditionModelName.$field LIKE" => "%{$params[$param]}%"];
                        foreach ($filters['more_fields'] as $f) {
                            $conditions['OR']["$conditionModelName.$f LIKE"] = "%{$params[$param]}%";
                        }
                    }
                    break;
                case 'like':
                    if (!empty($params[$param]) || (isset($params[$param]) && strval($params[$param]) === '0')) {
                        // Check if value is surrounded by quotation then find exact matches only
                        if (preg_match('/^(["\']).*\1$/m', $params[$param])) {
                            $value = str_replace(['"', "'"], "", $params[$param]);
                            $conditions["$conditionModelName.$field LIKE"] = $value;
                        } else {
                            $conditions["$conditionModelName.$field LIKE"] = "%{$params[$param]}%";
                        }
                    }
                    break;
                case 'advanced_like':
                    if (!empty($params[$param]) || (isset($params[$param]) && strval($params[$param]) === '0')) {
                        $keywords = explode(' ', $params[$param]);
                        if (count($keywords) == 1) {
                            $conditions["$conditionModelName.$field LIKE"] = "%{$keywords[0]}%";
                        } else {
                            foreach ($keywords as $k => $keyword) {
                                $conditions['AND'][]["$conditionModelName.$field LIKE"] = "%{$keyword}%";
                            }
                            debug($conditions);
                        }
                    }
                    break;
                case 'date_range':
                    $selector = $field . '_selector';
                    if (!empty($params[$selector])) {
                        if ($params[$selector] == 'lastmonth') {
                            $conditions["$conditionModelName.$field >="] = date('Y-m-d 00:00:00', strtotime('-1 month'));
                            $conditions["$conditionModelName.$field <="] = date('Y-m-d 23:59:59');
                        } elseif ($params[$selector] == 'lastyear') { //php fix
                            $conditions["$conditionModelName.$field >="] = date('Y-m-d 00:00:00', strtotime('-1 year'));
                            $conditions["$conditionModelName.$field <="] = date('Y-m-d 23:59:59');
                        }
                    } else {
                        $from_param = empty($filters['from']) ? "from" : $filters['from'];
                        $to_param = empty($filters['from']) ? 'to' : $filters['to'];
                        $ownerFormat = getCurrentSite('date_format');
                        if (!empty($params["{$field}_{$from_param}"])) {
                            $from_date = $this->{$modelName}->formatDate($params["{$field}_{$from_param}"], $ownerFormat);
                            $conditions["$conditionModelName.$field >="] = date('Y-m-d 00:00:00', strtotime($from_date));
                        } elseif (!empty($params[$from_param])) {
                            $from_date = $this->{$modelName}->formatDate($params[$from_param], $ownerFormat);
                            $conditions["$conditionModelName.$field >="] = date('Y-m-d 00:00:00', strtotime($from_date));
                        }
                        if (!empty($params["{$field}_{$to_param}"])) {
                            $to_date = $this->{$modelName}->formatDate($params["{$field}_{$to_param}"], $ownerFormat);
                            $conditions["$conditionModelName.$field <="] = date('Y-m-d 23:59:59', strtotime($to_date));
                        } elseif (!empty($params[$to_param])) {
                            $to_date = $this->{$modelName}->formatDate($params[$to_param], $ownerFormat);
                            $conditions["$conditionModelName.$field <="] = date('Y-m-d 23:59:59', strtotime($to_date));
                        }
                    }
                    break;
                case 'number_range':
                    $from_param = empty($filters['from']) ? $field . '_from' : $filters['from'];
                    $to_param = empty($filters['from']) ? $field . '_to' : $filters['to'];
                    if (!empty($params[$from_param]) || (isset($params[$param]) && strval($params[$from_param]) === '0')) {
                        $conditions["$conditionModelName.$field >="] = $params[$from_param];
                    }
                    if (!empty($params[$to_param]) || (isset($params[$to_param]) && strval($params[$to_param]) === '0')) {
                        $conditions["$conditionModelName.$field <="] = $params[$to_param];
                    }
                    break;
                case 'set':
                    if (isset($params[$param]) && $params[$param] != '')
                        $conditions[] = 'FIND_IN_SET("' . $params[$param] . '",' . $conditionModelName . '.' . $field . ')';
                    break;
                case 'bool':
                    if (!empty($params[$param]) || (isset($params[$param]) && $params[$param] === '0') || (!empty($params[$param]) && $params[$param] === '0')) {
                        $conditions["$conditionModelName.$field"] = $params[$param];
                    }
                    break;
                default:
                    //warning suppress
                    if(isset($param))
                    if (!empty($params[$param]) || (isset($params[$param]) && strval($params[$param]) === '0')) {
                        $conditions["$conditionModelName.$field $type"] = $params[$param];
                    }
                    break;
            }
        }


		if(count($conditions) > 0 )
		{
			$_SESSION['conditioned'] = true ;
		}
		if (!$this->RequestHandler->isAjax() && !IS_REST) {
		$this->write_filter_conditions($conditions);
		}

        return $conditions;
    }

	function filter_tags(){
		$modelName = Inflector::singularize($this->name);
		$tag_name = $this->params['url']['tag'] ;

		if($modelName == 'ClientAppointment'){
			$modelName = 'FollowUpReminder';
		}
		$tag_ids = $this->params['url']['tags'];
		$tag_type = $this->params['url']['tag_type'];
		if(($tag_name || $tag_ids) && $tag_type)
		{
			$this->loadModel('Tag');
			if(!$tag_ids){
			$tag = $this->Tag->get_tag_id($tag_name);
			$tag_ids = $tag['Tag']['id'] ;
			}

			$this->loadModel('ItemsTag');
			$items_ids = $this->ItemsTag->find('list',array('conditions' => array('ItemsTag.tag_id' => $tag_ids  , 'ItemsTag.item_type' => $tag_type), 'fields' => array('ItemsTag.item_id')));
			return array( ItemsTag::tag_type_to_model_name($tag_type).'.id' =>$items_ids);

		}
	}


	/**
	 *
	 * @return null
	 * this function is called in the filter params when user chooses a way to order item
	 * the order is saved for this specfiec page and user so when the user opens this page again
	 * the saved order will be loaded automatically
	 */
	function write_order_settings(){
        if(empty($this->params['named']['sort']) || empty($this->params['named']['direction'])){
            return ;
        }
		$modelName = Inflector::singularize($this->name);
		$this->loadModel('Setting');
		$controller = $this->params['controller'];
		$action = $this->params['action'];
		$staff_id = getAuthOwner('staff_id');
		$data['Setting']['key'] = $controller.'_'.$action.'_'.$staff_id ;
		$this->params['named']['sort'] = str_replace($modelName.'.', '', $this->params['named']['sort']);
		$data['Setting']['value'] = $modelName.'.'.$this->params['named']['sort'].' '.$this->params['named']['direction'];

		$setting = $this->Setting->find('first',array('conditions' => array('Setting.key' => $data['Setting']['key'])));
		if($setting)
		{
			if($setting['Setting']['value'] != $data['Setting']['value']){
				$setting['Setting']['value'] = $data['Setting']['value'];
				$this->Setting->save($setting);
			}
		}
		else{
			$this->Setting->create();
			$this->Setting->save($data);
			 $setting = $data;
		}
		$Setting = new settings();
        $Setting->removeCache(0);
	}

	/**
	 * checks if this action has any preferred ordering criteria saved in the database
	 *
	 * @return array|false array is this model saved order
	 */
	function check_order_settings()
	{
		$this->loadModel('Setting');
		$modelName = Inflector::singularize($this->name);
		$controller = $this->params['controller'];
		$action = $this->params['action'];
		$staff_id = getAuthOwner('staff_id');
		$data['Setting']['key'] = $controller.'_'.$action.'_'.$staff_id ;
		$setting = $this->Setting->find('first',array('conditions' => array('Setting.key' => $data['Setting']['key'])));
		if($setting)
        {
            $array = explode(' ',$setting['Setting']['value']);
            if(empty($array[0]) || empty($array[1])){
                settings::deleteByKey($setting['Setting']['key']);
                return false;
            }
            $fieldNameWithModel = $array[0];
            $fieldParts = explode('.',$fieldNameWithModel);
            $modelName = $fieldParts[0];
            $sortFields = $this->{$modelName}->getSortFields(null);
            $field = $fieldParts[1];
            $dir = $array[1];
            foreach ($sortFields as $sortField)
            {
                //check saved sort field is in current model sort fields
                if($sortField['field'] == $fieldNameWithModel || $sortField['field'] == $field)
                {
                    return $setting;
                }
            }
        }
		return false;
	}

	/**
	 * loads the saved order for this page to the paginate variable
	 */

	function get_order_settings(){

		$setting = $this->check_order_settings();


		if($setting) {
		    $array = explode(' ',trim($setting['Setting']['value']));
		    if(empty($array[0]) || empty($array[1])){
                settings::deleteByKey($setting['Setting']['key']);
		        return;
            }

		    $fieldParts = explode('.',$array[0]);
		    $modelName = $fieldParts[0];
		    $field = $fieldParts[1];
		    $dir = $array[1];
			$this->paginate[$modelName]['order'] = [$array[0] => $dir];
        }
	}


	/**
	 * checks if there is a saved filter conditions for this page from the session
	 * if exists it execute it and remove it from the session
	 */

	function check_filter_conditions(){

        $refer_url = $this->referer('/', true);
        $parse_url_params = Router::parse($refer_url);
        $controller = Inflector::humanize(Inflector::singularize($this->params['controller']));
        $action = $this->params['action'];
        if ($action == 'owner_index')
            $filter_key = "$controller.$action";
        else if ($action == 'owner_view') {
            $filter_key = "$controller.owner_index";
        }
        if (empty($filter_key)) {
            return;
        }
        $redirect = $this->Session->read($filter_key);

        if ($controller == 'Expense') {
            //warning suppress
            $is_income = $this->params['is_income'] ?? '';
            if (strpos($redirect, 'incomes') === false && $is_income) {
                unset($redirect);
            }
            if (isset($redirect) && strpos($redirect, 'expenses') === false && !$is_income) {
                unset($redirect);
            }
        }
        // warning suppress
        if (isset($redirect) && $redirect && (count($this->params['url']) <= 2 && !isset($this->params['url']['reset']))) {
            $this->reset_flash_message();
            if ($redirect && $parse_url_params['action'] == 'delete') {
                $this->Session->delete($filter_key);
                $this->redirect($redirect);
            } else if ($redirect && $action == 'owner_index') {
                $this->Session->delete($filter_key);
                $this->redirect($redirect);
            } else if ($redirect) {
                $this->set('filter_url', $redirect);
            }
        }
	}

	/**
	 * gets the flash message from session and resets it this maybe useful in the redirects bcs when redirecting the flash message disappears
	 */
    function reset_flash_message()
    {
        if ($_SESSION['Message']['flash']['message']) {
            $flash_message = $_SESSION['Message']['flash'];
            $this->flashMessage($flash_message['message'], $flash_message['params']['class']);
        }
        return;
    }


	function write_filter_conditions($conditions)
	{
		$controller = Inflector::humanize(Inflector::singularize($this->params['controller']));
		$action = $this->params['action'];
		if(count($conditions) > 0){
		    $this->Session->write("$controller.$action",$_SERVER['REQUEST_URI']);
		}
		else if($this->Session->read("$controller.$action")){
			$this->Session->delete("$controller.$action");
		}
	}



	function owner_get_google_api_keys(){
		$data['geocode'] = $this->config['google_geocode_api'];
		$data['maps'] = $this->config['google_maps_api'];
		die(json_encode($data));
	}

    function owner_delete_field($type, $id, $field = false, $redirect = false) {
        if (!$redirect) {
            $redirect = $this->referer();
        }

        $modelName = $this->modelNames[0];
        if (!empty($this->modelName)) {
            $modelName = $this->modelName;
        }


        $type = ucfirst($type);
        if (low($modelName) == 'site') {

            $conds = array("$modelName.id" => getAuthOwner('id'));
        } else {
            $conds = array("$modelName.id" => $id);
        }
        if($this->name === 'Sites') {
            $this->loadModel('Site');
            $modelName = 'Site';
        }
        $item = $this->{$modelName}->find($conds, false, false, false, -1);

        if (!$item) {
            $this->flashMessage(__('Item not found', true));
            $this->redirect($redirect);
        } else if (!$field) {
            $this->flashMessage(__('Invalid file name', true));
            $this->redirect($redirect);
        } else {
            $this->{$this->modelNames[0]}->id = $id;
            $base_name = $this->{$this->modelNames[0]}->field($field);
            if (!$base_name) {
                $this->flashMessage(sprintf(__('%s is not found', true), ucfirst($type)));
                $this->redirect($redirect);
            }
            if (!$this->{$this->modelNames[0]}->saveField($field, '')) {
                $this->flashMessage(sprintf(__('Could not delete %s', true), lcfirst($type)));
                $this->redirect($redirect);
            }

            $this->{$this->modelNames[0]}->{'delete' . $type}($field, $base_name);
            if (low($modelName) == 'site') {
                $this->Site->reload_session();
            }

            $this->flashMessage(sprintf(__('%s has been removed', true), __(ucfirst($type),true)), 'Sucmessage');
            $this->redirect($redirect);
        }
    }

    function auto_login() {

        if ($this->Cookie->read('User_id')) {
            $user_id=$this->Cookie->read('User_id');
            if ($this->Cookie->read('User_level') == 3) {
                $client = $this->Client->find(array('Client.id'=>$user_id,'Client.suspend'=>0));
                if (md5($_SERVER['HTTP_USER_AGENT'] . $client['Client']['id'].$client['Client']['password']) == $this->Cookie->read('User_hash')) {
                    $this->Client->reload_session($client);
                    $user = $this->Session->read('OWNER');
                    $this->set('user', $user);
                    return true;
                } else {
                    $this->Cookie->destroy();
                    //$this->flashMessage(__('You need to login', true), 'Errormessage', 'Loginpage');
                    $this->redirect("/");
                }
            } elseif ($this->Cookie->read('User_level') == 2) {

                $this->loadModel('Site');
                $this->loadModel('Staff');
                $staff = $this->Staff->find(array('Staff.id'=>$user_id,'Staff.active'=>1));

                if (!$staff) {
                    $this->Cookie->destroy();
                       $this->flashMessage(__('You need to login to access this page', true), 'Errormessage', 'Loginpage');
                    $this->redirect("/");
                } else {
                    if (md5($_SERVER['HTTP_USER_AGENT'] . $staff['Staff']['id'].$staff['Staff']['password']) == $this->Cookie->read('User_hash')) {
                        $query = 'UPDATE staffs SET last_login = NOW() WHERE id = ' . $user_id;
                        $this->Staff->query($query);
                        $this->Staff->reload_session($user_id);
                        $this->Site->reload_session(getCurrentSite('id'));
                        $user = $this->Session->read('OWNER');
                        $this->set('user', $user);
                        $this->set('staff', $this->Session->read('STAFF'));
                        return true;
                    } else {
                        $this->Cookie->destroy();
                        //      $this->flashMessage(__('Invalid email or password', true), 'Errormessage', 'Loginpage');
                        return true;
                    }
                }
            } else {
                $this->loadModel('Site');
                $site = $this->Site->findById($user_id);
                if (!$site) {
                    $this->Cookie->destroy();
                    $this->flashMessage(__('Invalid email or password', true), 'Errormessage', 'Loginpage');
                    $this->redirect("/");
                } else {
                    if (md5($_SERVER['HTTP_USER_AGENT'] . $site['Site']['id']) == $this->Cookie->read('User_hash')) {
                        $this->Site->reload_session($user_id);
                        $this->Site->id = $site['Site']['id'];
                        $this->Site->saveField('last_login', date('Y-m-d H:i:s'));
                        $user = $this->Session->read('OWNER');
                        $this->set('user', $user);
                        return true;
                    } else {
                        $this->Cookie->destroy();
                        $this->redirect("/");
                    }
                }
            }
        }
    }

    function add_stats($action, $params = array(),$force_save=false) {
        $this->loadModel('Stats');
        return $this->Stats->add_stats($action,$params,$force_save);
    }

    function add_actionline($action, $params = array()) {
		$this->loadModel('ActionLine');
		$this->ActionLine->add_actionline($action,$params);
    }

//-------------------------------
    function get_snippet($name, $language_id = false, $close = true)
    {
        if (empty($language_id)) {
            $language_id = 41;
            if (getCurrentSite('language_code')) {
                $language_id = getCurrentSite('language_code');
            }
            if (!empty(getAuthStaff()) ) {
                $language_id = getAuthStaff('language_code');
            }
        }
        if(empty($language_id)) $language_id=41;

        $conditions['Snippet.name'] = $name;
        $conditions[] = "(Snippet.language_id='$language_id' OR Snippet.language_id='41')"; //array('OR'=>array('Snippet.language_id'=>$language_id),array('Snippet.language_id'=>41));

        $close_button = '';
        $owner_id = getAuthOwner('id');
        if (!empty($owner_id) && !empty($this->params['prefix']) && $this->params['prefix'] == 'owner' && $close) {
            $this->loadModel('SnippetsUser');

            $snippetUsers = $this->SnippetsUser->find('first', [
                'conditions' => ['SnippetsUser.snippet_name' => $name, 'SnippetsUser.user_id' => $owner_id],
                'fields' => ['snippet_name']
            ]);

            if ($snippetUsers) {
                $conditions[] = ['Snippet.name != ' => $name];
            }

            $close_button = "<a href=\"#\" class=\"close close-snippet\" data-snippet=\"{$name}\">X</a>";
        }
        $cacheKey = 'get_snippet_' . $name . '_' . $language_id . '_' . $close;
        $snippet = PortalCache::get($cacheKey, function () use ($language_id, $conditions){
            $this->loadModel('Snippet');
            $snippet = $this->Snippet->find('first', array('conditions' => $conditions, 'order' => "FIELD(Snippet.language_id,$language_id,41)"));
            return $snippet;
        });
        // warning suppress
        $content = $snippet['Snippet']['content'] ?? null;
        if (!empty($content) && $close) {
            $content = "<div class='snippet-content'>{$close_button}{$content}</div><div class=\"clear\"></div>";
            $this->set('snippet_top', $name);
        }

        return $content;
    }

    function get_compiled_snippet($name, $language_id = false)
    {
        if (!$language_id) {
            $language_id = 41;
            if (getCurrentSite('language_code')) {
                $language_id = getCurrentSite('language_code');
            }
        }

        $user_id = getAuthOwner('staff_id');
        $this->loadModel('SnippetsUser');
        $cacheKey = 'compiled_snippet_' . $name . '_' . $language_id . '_' . $user_id;
        $content = PortalCache::get($cacheKey, function () use ($language_id){
            $this->loadModel('Snippet');
            $dismissed_snippets = [];
            $dismissed_snippets_records = $this->SnippetsUser->find('all', array('SnippetsUser.snippet_name' => $name, 'SnippetsUser.user_id' => $user_id));
            foreach ($dismissed_snippets_records as $dismissed_snippets_record)
                $dismissed_snippets[] = $dismissed_snippets_record['SnippetsUser']['snippet_name'];

            $conditions['Snippet.name'] = $name;
            $conditions[] = "Snippet.language_id='$language_id'";
            $conditions[] = "Snippet.name NOT IN ('" . implode("','", $dismissed_snippets) . "')";

            $snippet = $this->Snippet->find('first', array('conditions' => $conditions, 'order' => "FIELD(Snippet.language_id,$language_id,41)"));
            if ($snippet) {
                $content = $snippet['Snippet']['content'];
                if (preg_match_all("/{\\$[a-zA-Z0-9_]+}/", $content, $matches)) {
                    $compiled_content = $content;
                    foreach ($matches[0] as $match) {
                        $match_key = trim($match, "{}$");
                        $compiled_content = str_replace($match, isset($this->Snippet->compiledVars[$match_key]) ? $this->Snippet->compiledVars[$match_key] : '', $compiled_content);
                    }
                    $content = $compiled_content;
                    return $content;
                }
            } else {
                $content = null;
                return $content;
            }
        });
        return $content;

    }

    function set_journal($id,$entity_type=false)
    {
        $model = Inflector::singularize($this->name);
        if (!empty($this->modelName))  $model = $this->modelName;
        if(isset($this->modelName)&&$this->modelName===false) return false;
        $journal = $this->{$model}->getJournal($id,$entity_type);
        $this->set('linked_journal',$journal);
    }

//-------------------------------
    function owner_get_tooltip($name, $language_id = false) {
        Configure::write('debug', 0);
        $this->layout = '';
        if (empty($language_id)) {
            $language_id = 41;
            if (getCurrentSite('language_code')) {
                $language_id = getCurrentSite('language_code');
            }
        }
        $this->loadModel('Tooltip');
        $conditions['Tooltip.name'] = $name;
        $conditions[] = "(Tooltip.language_id='$language_id' OR Tooltip.language_id='41')"; //array('OR'=>array('Snippet.language_id'=>$language_id),array('Snippet.language_id'=>41));


        $close_button = '';
//		$owner_id	 = getAuthOwner('id');

        $tooltip = $this->Tooltip->find('first', array('conditions' => $conditions, 'order' => "FIELD(Tooltip.language_id,$language_id,41)"));
        $content = '';
        if (!empty($tooltip['Tooltip']['content'])) {
            $content = "<div class='tooltip-content'>{$close_button}{$tooltip['Tooltip']['content']}</div>";
        }

        echo $content;
        exit();
    }

    function owner_delete_snippet($snippet_name) {
        $this->loadModel('SnippetsUser');
        $data = array();
        $data['SnippetsUser']['user_id'] = getAuthOwner('id');
        $data['SnippetsUser']['snippet_name'] = $snippet_name;
        $this->SnippetsUser->create();
        $this->SnippetsUser->save($data);
        $this->redirect($this->referer(array('action' => 'index'), true));
    }

//-------------------------------
    function _is_user() {
        if ($this->Session->check('CLIENT') || $this->Session->check('OWNER')) {
            $this->redirect('https://' . $_SERVER['HTTP_HOST']);
        }
        return false;
    }

//-------------------------------
    function _record_path() {
    // url handled by php not redirectable
        $excluded_urls=[
        '/owner/pos_shifts/pos_js',
        '/owner/pos_shifts/translate'
    
       ];

        if (!$this->RequestHandler->isAjax()) {
            $url = substr($_SERVER['REQUEST_URI'], strlen($this->base));
            if (!in_array($url, $excluded_urls) &&  ! str_starts_with($url,'/api2/')) {
                $this->Session->write('LOGIN_REDIRECT', $url);
            }
        }
    }
    

//-------------------------------------
    public function _record_referer_path() {

        $referer_url = $this->referer(array('action' => 'index'), true);
        $action = str_replace("{$this->params['prefix']}_", '', $this->params['action']);
        $current_url = "/{$this->params['prefix']}/{$this->params['controller']}/{$action}";




        if (strpos($referer_url, $current_url) === false && $current_url != $referer_url) {

            $this->Session->write('referer_url', $this->referer(array('action' => 'index'), true));
        }
    }

//-------------------------------------
    public function _get_smtp($site = null) {

        if ($site == null) {
            $site = getAuthOwner('id');
        }

        //$user = $this->Site->find('first', array('recursive' => -1,'conditions' => array('Site.id' => $site)));
        $user['Site']=  getCurrentSite();
        if ($user['Site']['use_smtp'] == 1) {
            return array(
                'port' => $user['Site']['smtp_port'] ? $user['Site']['smtp_port'] : 25,
                'timeout' => '30',
                'host' => $user['Site']['smtp_host'],
                'username' => $user['Site']['smtp_user_name'],
                'password' => $user['Site']['smtp_password'],
                'client' => 'OnlineInvoice'
            );
        } else {
            return array();
        }
    }

    public function _set_smtp($id = null) {

        if ($id == null) {
            $site = getAuthOwner('id');
        } else {
            $site = $id;
        }
        if ($site) {
            $array = $this->_get_smtp($site);
            if (isset($array['port'])) {
                $this->Email->smtpOptions = $array;
                $this->Email->delivery = 'smtp';
            }
        }
    }

//-------------------------------------
    public function _get_referer_path($delete=true) {

        $action = str_replace("{$this->params['prefix']}_", '', $this->params['action']);
        $current_url = "/{$this->params['controller']}/{$action}";
        $referer_url = !($this->Session->check('referer_url')) ? array('action' => 'index') : $this->Session->read('referer_url');
        if(is_array($referer_url)) {
            $referer_url = Router::url($referer_url);
        }
        if (strpos($referer_url, $current_url) !== false || $current_url == $referer_url) {
            $referer_url = array('action' => 'index');
        }
        if($delete) {
            $this->Session->delete('referer_url');
        }
        return $referer_url;
    }

//----------------------------
    function __init_lang() {
        if (PHP_SAPI == 'cli') {
            Configure::write('Config.language', 'eng');
            $l10n = new L10n();
            $l10n->get('eng');
            return;
        }

        $client=  getAuthClient();
        $staff=  getAuthStaff();
        $site = getCurrentSite();

        if ($client && $client['language_code']!=""){
        $language_code=$client['language_code'];
        }else if ($staff && $staff['language_code']!=""){
        $language_code=$staff['language_code'];
        }else{
        $language_code=$site['language_code'];
        }

        $cacheName = "language_code_{$language_code}";
        $lang = PortalCache::get($cacheName, function () use ($language_code){
            $this->Language=ClassRegistry::init('Language');
            if ($this->Language->check_language_code($language_code)) {//if(!empty($this->params['ar'])) {
                $lang = $this->Language->get_language_code($language_code, 'code3');
            } else {
                $lang = Site_Lang;
            }
            return $lang;
        });
        $this->lang = $lang;
        if (isset($_GET['ar'])) {
            $this->lang = 'ara';
        }

        $this->set('lang', $this->lang);

        $this->__load_locale();
    }

//----------------------------
    function __load_locale() {
        $l10n = new L10n();
        if ($this->lang) {
            $l10n->get($this->lang);
            Configure::write('Config.language', $this->lang);
        } else {
            $l10n->get(Site_Lang);
            Configure::write('Config.language', Site_Lang);
        }

    }

    function _json_lang() {
        $curr_labels = array();

        if (!empty($this->js_lang_labels)) {
            foreach ($this->js_lang_labels as $key) {
                $curr_labels[($key)] = __($key, true);
            }
        }
        return $curr_labels;
    }

//--------------------



    function _setBranded($site_id = false) {
        $this->Site = getSiteModel();
        if ($this->Site->isBranded($site_id)) {
            $this->set('branded', true);
            $brandedText = $this->get_snippet('email-brand', false, false);
            $this->set('brandText', $brandedText);
            return array('branded' => true, 'brandText' => $brandedText);
        }
        return array('branded' => false, 'brandText' => '');
    }

    function _flashLimitMessage($message, $model = '', $element = 'limits') {

        if (empty($model)) {
            $model = low(Inflector::humanize(Inflector::pluralize(Inflector::underscore($this->modelNames[0]))));
        }


        $this->Session->setFlash($message, $element, array('model' => $model));
    }

    function notify_admin($subject,$msg) {
        // send email to support
        $Email = new EmailComponent();
        $Email->from = array(AdminEmail => Site_Full_name_NoSpace);
        $Email->to = InfoEmail;
        // $Email->to = "<EMAIL>";
        $Email->subject = $subject;
        $Email->send($msg);
    }

	/**
	 *
	 * @param type $date the date to check if it lays on a closed period
	 * @param type $redirect if true it will redirect if the period is opened else it will return the result of is opened
	 * @return type redirect to referer if th date is opened
	 */
	function validate_open_day($date = null,$redirect = true){
		if(ifPluginActive(AccountingPlugin)){
			$this->loadModel('ClosedPeriod');
			if(!$date){
				$date = date('Y-m-d');
			}
			$is_opened = $this->ClosedPeriod->is_opened_date($date);
			if($redirect && !$is_opened){
				if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('You can not add, edit, or delete a transaction in this date %s within a closed period', true), $date)]);
				$this->flashMessage(sprintf(__('You can not add, edit, or delete a transaction in this date %s within a closed period', true), $date));
				if(is_array($redirect)){
					return $this->redirect(Router::url($redirect));
				}else
					return $this->redirect($this->referer());
			}else{
				return $is_opened;
			}
		}
	}

    /**
     * @param $data
     * @param $modelName
     * @param $fieldName
     * @return bool
     * checks if the date field identified by model name and field name in the data
     * is in closed period if so it will set validation error
     */
	function validateOpenDayWithValidationError($data, $modelName, $fieldName)
    {
        $is_opened = true;
        if(ifPluginActive(AccountingPlugin)){
            $date = $data[$modelName][$fieldName];
            $this->loadModel('ClosedPeriod');
            if(!$date){
                $date = date('Y-m-d');
            }
            $is_opened = $this->ClosedPeriod->is_opened_date($date);
            if(!$is_opened){
                $is_opened = $this->{$modelName}->validationErrors[$fieldName] = sprintf(__('You can not add, edit, or delete a transaction in this date %s within a closed period', true), format_date($date));
            }
            return $is_opened;
        }
        return $is_opened;
    }


    /**
     * @param $settingsKey
     * @param $accountEntity
     * @param $accountEntityId
     */
	function setAccountRoute($settingsKey, $accountEntity, $accountEntityId)
    {
        App::import('Vendor', 'settings');
        $this->loadModel('JournalAccountRoute');
        $routingType = Settings::getValue(AccountingPlugin, $settingsKey);
        if($routingType == Settings::MANUAL_ACCOUNTS_ROUTING){
            $this->set('routedAccountEntityType',$accountEntity);
            $this->set('showRouting', true);
            if($accountEntityId) {
                $this->set('routedAccountEntityId',$accountEntityId);
                $accountRoute = $this->JournalAccountRoute->Journal->get_auto_account(['entity_type' => $accountEntity, 'entity_id'=> $accountEntityId]);
                $this->set('accountRoute', $accountRoute);
            }
        }else if($routingType == Settings::MANUAL_MAIN_ACCOUNTS_ROUTING){
            $this->set('showMainRouting', true);
        }
    }

    /**
     * @param $controllerName
     * @param $conditions
     */
    protected function setConditionsKey($controllerName, $conditions)
    {

        $conditions_key = $controllerName.'_' . md5(serialize($conditions));
        $this->Session->write($conditions_key, $conditions);
        $this->set('conditions_key', $conditions_key);
    }


    function addBreadCrumbs($model, $url, $data = [])
    {
        App::import('vendor','BreadCrumbs',['file'=>'BreadCrumbs/autoload.php']);
        $tempUrl = [];
        foreach($url as $k => $v) {
            $tempUrl[$k] = $v;
        }
        $breadCrumbs = \BreadCrumbs\BreadCrumbs::generateBreadCrumbs($model, $tempUrl, $data);
        if($breadCrumbs) {
            $formattedBC = [];
            foreach($breadCrumbs as $k => $v) {
                foreach($v as $link => $title) {
                    $formattedBC[] = ['link' => $link, 'title' => __($title,true)];
                }
            }
            $this->set('_PageBreadCrumbs', $formattedBC);
        }
    }

	function _parseBlockedPages() {
        $url = $_SERVER['REQUEST_URI'];
        $url= ltrim($url, '/');
        $isBlocked = isBlockedPage($url);

        if ($isBlocked) {
            if (IS_REST) {
                $this->autoRender = false;
                $this->RequestHandler->respondAs('json');
                http_response_code(403);
                die(json_encode([
                    "message" => $this->getBlockedPageMessage(),
                    'error' => $this->getBlockedPageMessage(),
                ]));
            }

            $no_redirect_actions = ['login_as', 'first_settings'];
            if(!$this->RequestHandler->isAjax() && strpos($url, 'preview') === false && !in_array($this->action, $no_redirect_actions)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                if($url == 'owner/sites/dashboard' || $url == 'owner/owners/dashboard'){
                    $this->redirect('/owner/staffs/account_info');
                }
                $this->redirect('/');
            }
            $this->layout=false;
            die($this->render('../errors/blocked_page'));
        }
    }
    function setTimeOut($id,$x=1.5){
        $timeLimit=120; // default 120

        if (is_null($id)) $countId = 0;
        elseif (is_array($id)) $countId = count($id);
        else $countId = 1;

        if($countId>=120) {
            $timeLimit=$countId*$x;
        }
        set_time_limit($timeLimit);
    }

    function handleSiteLimit(array $result, $redirect = ['action' => 'index'], $report = false) {
        if ($result['status']) {
            return;
        }

        if ($report || IS_REST) {
            die(json_encode(array(
                'status' => $result['status'],
                'code' => 402,
                'message' => sprintf(__($result['message'], true), __($result['title'], true)),
                'upgrade_url' => $result['upgrade_url'],
                'upgrade_btn_title' => __($result['upgrade_btn_title'], true),
            )));
        }

        if (is_string($redirect) && strpos($redirect, 'v2') !== false) {
            $this->siteLimitFlashMessageIntoLaravel($result);
        } else {
            $this->siteLimitFlashMessage($result);
        }

        $this->redirect($redirect);
    }

    function siteLimitFlashMessage($result)
    {
        $this->_flashLimitMessage($result['message'], __($result['title'], true));
    }

    function siteLimitFlashMessageIntoLaravel($result)
    {
        $_SESSION['limit_exceeded'] = $result;
        $_SESSION['site_limits'] = $result;
    }

    protected function setDefaultViewData()
    {
        $site = getCurrentSite();
        $user = [];
        if ($user = getAuthStaff()) {
            $user['staff_id'] = $user['id'];
            $user['type'] = 'staff';
        } elseif (getAuthClient()) {
            $user = getAuthClient();
        } else {
            $user = getAuthOwner();
        }

        $username = '';
        if ((!empty($user['first_name'])  or $user['last_name']) and $user['staff_id'] == 0) {
            $username = $user['first_name']. ' ' .$user['last_name'];
            $image_alt = ucfirst(mb_substr($user['first_name'], 0, 1)) .' '. ucfirst(mb_substr($user['last_name'], 0, 1)) ;


        } else if (!empty($user['business_name']) and $user['staff_id'] == 0) {
            $username = $user['business_name'];
        } else {
            $username = $user['name'];
            $image_alt = ucfirst(mb_substr($user['name'], 0, 1)) .' '. ucfirst(mb_substr($user['last_name'], 0, 1)) ;

        }


        $changeEmailUrl = null;
        $changePasswordUrl = null;
        $QRCodeAccessUrl = null;
        $editMyDetails = null;
        $showMobileApps=false;
        

        if ($user['type'] == 'owner' && $user['staff_id'] == 0) {
            $changeEmailUrl = Router::url(array('controller' => 'owners', 'action' => 'change_email'));
            $changePasswordUrl = Router::url(array('controller' => 'owners', 'action' => 'change_password'));
            $showMobileApps=true;
        } else if ($user['type'] == 'owner' and $user['staff_id'] != 0) {
            $changeEmailUrl = url(array('controller' => 'staffs', 'action' => 'change_email'));
            $changePasswordUrl = Router::url(array('controller' => 'staffs', 'action' => 'change_password'));
            $showMobileApps=true;
        }else if (isset($user['type']) && $user['type'] == 'staff' && $user['staff_id'] != 0) {
            $changeEmailUrl = Router::url(['controller' => 'staffs', 'action' => 'change_email']);
            $changePasswordUrl = Router::url(['controller' => 'staffs', 'action' => 'change_password']);
            $showMobileApps=true;
        }
        else if (getClientSettings("client_permission_edit_profile")) {
            $changeEmailUrl = Router::url(array('controller' => 'clients', 'action' => 'change_email', 'client' => 1));
            $changePasswordUrl = Router::url(array('controller' => 'clients', 'action' => 'change_password', 'client' => 1));
            $showMobileApps=true;
            $editMyDetails = Router::url(array('controller' => 'clients', 'action' => 'change_settings', 'client' => 1));
        }
            
        if (showMobileApps() && $showMobileApps) {
            $QRCodeAccessUrl = '/v2/owner/mobile-apps';
        }

        if ($user['type'] == 'client') {
            $LogoutUrl = Router::url(array('controller' => 'clients', 'action' => 'logout', 'client' => true));
        } else {
            $LogoutUrl = Router::url(array('controller' => 'owners', 'action' => 'logout'));
        }



        $this->set('branchName', '');
        $this->set('staffBranches', []);



        $this->set('image_alt',$image_alt);
        $this->set('changeEmailUrl',$changeEmailUrl);
        $this->set('changePasswordUrl',$changePasswordUrl);
        $this->set('QRCodeAccessUrl',$QRCodeAccessUrl);
        $this->set('editMyDetails',$editMyDetails);
        $this->set('LogoutUrl',$LogoutUrl);


        $this->set('params',$this->params);
        $this->set('site',$site);
        $this->set('username',$username);
        $this->set('user',$user);
        $this->set('enable_simple_side_menu', settings::getValue(0, 'enable_simple_side_menu'));
    }

    protected function getBlockedPageMessage() {
        return __('You are not allowed to view this page', TRUE);
    }

    /**
     * Method to respond based on the content type
     *
     * @param mixed $data
     * @param int $responseCode
     *
     * @return mixed
     */
    public function detectedResponse(
        mixed $data,
        int $responseCode = 200
    ) {

        $isJson = $this->detectJsonContentType();
        http_response_code($responseCode);

        if ($responseCode < 300) {
            $response = [
                'status' => 'OK',
                'data' => $data,
            ];
        } elseif ($responseCode >= 300 && $responseCode < 400) {
            $response = [
                'status' => 'REDIRECT',
                'url' => $data
            ];
        } else {
            $response = [
                'status' => 'ERR',
                'error' => isset($error) ? $error : 'INVALID_REQUEST',
                'errors' => $data
            ];
        }

        if ($isJson) {
            $this->autoRender = false;
            $this->RequestHandler->respondAs('json');
            die(
                json_encode($response)
            );
        }

        if ($responseCode == 302) {
            $this->redirect(
                $data
            );
        }

        if ($responseCode >= 400) {
            $this->cakeError('error' . $responseCode);
        }

        return;
    }

    /**
     * Detect if the request is a json request based on the
     * application/type header
     *
     * @return bool
     */
    public function detectJsonContentType(): bool
    {
        $headers = [];

        foreach (getallheaders() as $key => $header) {
            $headers[strtolower($key)] = strtolower($header);
        }

        return isset($headers['accept'])
            && $headers['accept'] == 'application/json';
    }

    /**
     * Check if the current request has a specific header
     *
     * @param string $headerName
     *
     * @return bool
     * @todo this must be a part of Request handling library
     */
    public function hasHeader(string $headerName)
    {
        return isset(getallheaders()[$headerName]);
    }

    /**
     * Return the value of a specific header
     *
     * @param string $headerName
     *
     * @return string
     * @todo this must be a part of Request handling library
     */
    public function getHeader(string $headerName)
    {
        return getallheaders()[$headerName];
    }

	/**
	 * This function is used to rate limit the report results based on a threshold limit variable
	 * It takes 2 params
	 * QueryData (Report result)
	 * UrlPath which is related to the current report
	 * @return void
	 */
	function displayLimitThreshold($rowCount, $limit = false) {

		$actual_link = "https://{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}";
        $url_parts = parse_url($actual_link);
        $thresholdLimit = $limit ? $limit : (defined('REPORT_DATA_THRESHOLD_LIMIT') ? REPORT_DATA_THRESHOLD_LIMIT : 7000);
        if($rowCount > $thresholdLimit && strpos($actual_link, '.csv') === false) {

            $actual_link = str_replace($url_parts['path'], $url_parts['path'] . ".csv", $actual_link);
            if(!isset($_GET['csv_confirmed']))
            {
                $actual_link= str_replace(".csv?",".csv?auto_csv=1&records_count=$rowCount&",$actual_link);
            }

            return  redirect($actual_link);
        }

	}

    public function startTransaction() {
        $db =& ConnectionManager::getDataSource('default');
        $db->query('SET AUTOCOMMIT = 0;');
        $db->query('SET TRANSACTION ISOLATION LEVEL READ COMMITTED;');
        $db->query('START TRANSACTION;');
    }

    public function rollbackTransaction() {
        $db =& ConnectionManager::getDataSource('default');
        $db->query('ROLLBACK;');
        $db->query('SET AUTOCOMMIT = 1;');
    }

    public function commitTransaction() {
        $db =& ConnectionManager::getDataSource('default');
        $db->query('COMMIT;');
        $db->query('SET AUTOCOMMIT = 1;');
    }

    protected function getJsonImportRequiredData(): array
    {
        return [
            'models' => [],
            'ownerAddParameters' => []
        ];
    }

    public function getJsonImportSuccessMessage($entityKey, $result): string
    {
        return "";
    }

    public function getJsonImportFailMessage($entityKey, $result, $record): string
    {
        return sprintf(__("Error Adding ".ucfirst($entityKey)." #%s:%s", true), $result['code'], implode(", ", $this->array_flatten($result["validation_errors"])));
    }

    protected function array_flatten(array $array, $depth = INF): array
    {
        $result = [];

        foreach ($array as $item) {
            if (is_array($item) && $depth > 1) {
                $result = array_merge($result, $this->array_flatten($item, $depth - 1));
            } else {
                $result[] = $item;
            }
        }

        return $result;
    }

    public function fillImportJsonData($records, $entityAppData, $entityKey)
    {
        set_time_limit ( 0);
        ini_set('memory_limit', '-1');
        if (empty( json_decode($entityAppData->data, true)['finished'])) {
            $status = ['finished' => 0, 'success' => 0, 'errors' => 0];
        } else {
            $status = json_decode($entityAppData->data, true);
            unset($status["results"]);
        }


        $results = [];

        $requiredData = $this->getJsonImportRequiredData();

        foreach ($requiredData['models'] as $model) {
            $this->loadModel($model);
        }

        $entityAppDataRepo = new \Izam\Daftra\Common\Repositories\EntityAppDataRepository();

        foreach ($records as $key => $record) {

            if (!empty($status['finished']) && ($status['finished'] - 1) >= $key) {
                continue;
            }
            $this->data = $record;

            foreach ($requiredData['models'] as $model) {
                $this->{$model}->create();
            }


            $result = $this->owner_add(...$requiredData['ownerAddParameters']);

            $recordId = $entityAppDataRepo->insertGetId([
                'entity_key' => $entityKey,
                'entity_id' => $entityAppData->id,
                'app_key' => "import",
                "action_key" => "import-record",
                "data" => json_encode($result)
            ]);

            $status['finished']++;
            $success = 0;
            if ($result['code'] == 202) {
                $status['success']++;
                $success = $recordId;
            } else {
                $status['errors']++;
            }

            $entityData = json_decode($entityAppData->data, true);

            if ($success) {
                $entityData['results'][$key] = ["status" => 1, "reference_id" => $recordId,  "message" => $this->getJsonImportSuccessMessage($entityKey, $result)];
            } else {
                $entityData['results'][$key] = ["status" => 0, "reference_id" => $recordId,  "message" => $this->getJsonImportFailMessage($entityKey, $result, $record)];
            }

            $entityAppData->data = json_encode($status + $entityData);
            $entityAppData->save();

            $results[] = $result;
        }

        return $results;
    }
}
