<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/show/show.min.css?v=".CSS_VERSION, null, []); ?>
<?php
$site = getAuthOwner();

echo $javascript->link(array('bootstrap-select.js'));
echo $javascript->link(array('ajax-bootstrap-select.min.js'));

echo $javascript->link(array('magicsuggest-min.js'));
echo $html->css(array( 'magicsuggest-min.css'));
echo $html->css(array('jquery.qtip.min', 'product_view_v'.CSS_VERSION.'.css', 'time-tracker.css?v=3', 'fontello.css', 'bootstrap-multiselect.css'));
echo $html->css('timeline_v'.CSS_VERSION.'.css?v=2');
echo $html->css('bootstrap-multiselect.css');
echo $html->css('view_new_style.css');
if($is_rtl){echo $html->css('view_new_style_ar.css'); }
//echo $html->css('clients_boxs.css');
echo $html->css(array('clients_boxs_v'.CSS_VERSION.'.css'), false, ['inline' => false]);
echo $javascript->link(array('client_view.js?v=1.2', 'bootstrap-multiselect.js'));
$dateFormats = getDateFormats('std');
$dateFormat = $dateFormats[getAuthOwner('date_format')];
echo $javascript->link(array('jqueryui', 'jquery.qtip.min'));
echo $html->css(array('jqueryui'), false, ['inline' => false]);

?>
<style>
            .modal-backdrop {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                min-height: 100vh !important;
                height: 100% !important;
            }
            .modal .modal-dialog {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        min-height: calc(100% - (10px* 2));
        padding-top: 0px !important;
    }
    .modal .modal-content {
        width: 100%;
    }
    @media (min-width: 768px) { 
        .modal .modal-dialog {
            min-height: calc(100% - (30px* 2));
        }
    }
    @media (max-width: 575.98px) {
        #clientAdvanced .modal-dialog {
            max-width: 100% !important;
        }
    }
                        
     .pr-status {
        border-radius: 3px;
        font-size: 11px;
        padding: 3px 7px !important;
        margin: 0 3px;
        float: right !important;
    }
    [dir=rtl] .pr-status {
        float: left !important
    }
</style>



		<div class="pages-head">

		<div class="container">
		    <h1>
                <span><?php echo $client['Client']['business_name'] ?></span> <span class="inline title_id_with_hash"> #<?php echo $client['Client']['client_number'] ?></span>
		        <div class="status <?php echo empty($client['Client']['suspend']) ? 'rate-green' : 'rate-red' ?>"> 
                <?php if(!empty($client['Client']['follow_up_status']) && $client['Client']['follow_up_status'] != $client['Client']['secondary_follow_up_status'] && $client['Client']['secondary_follow_up_status']){?>
                <span class="pr-status" <?= $FollowUpStatus[$client['Client']['follow_up_status']]['style'] ?>>
                <?php if($FollowUpStatus[$client['Client']['follow_up_status']]['sticky'] == 1){
                                       ?> <i class="mdi mdi-pin"></i><?php
                                    }  ?> 
                    <?= $FollowUpStatus[$client['Client']['follow_up_status']]['name'] ?>
                </span>
                <?php } ?>
                <span class="status-symble "><?php echo empty($client['Client']['suspend']) ? __('Active', true) : __('Suspended', true) ?></span> 
                </div>
                
                <div class="sub-headings">

		        <?
		        if (ifPluginActive(FollowupPlugin) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $client['Client']['id'])) {
		            if (is_countable($staff_assignd) && count($staff_assignd) > 0) {
		                ?>
                    <span class="invoice-client sub-heading sub-heading2">
		                <span style="font-size: 14px"><?php __('Assigned To') ?>: </span>

		                <?

		                foreach ($staff_assignd as $staff) {
		                    if(empty($staff['Staff']['name'])){
		                        continue;
                            }
		                    ?>
		                    <span class="btn btn-default btn-xs remove-tag" id="staff_<?php echo $staff['ItemStaff']['staff_id'] ?>">
		                        <?php echo $staff['Staff']['name']?$staff['Staff']['name']:__("Deleted") ?>
		                        <?php if (check_permission(Assign_Clients_To_Staff)) { ?><a staff_id="<?php echo $staff['ItemStaff']['staff_id'] ?>" class="btn-group remove delete_staff fa fa-times" href="<?php echo Router::url(array('action' => 'delete_staff', $staff['ItemStaff']['item_id'], $staff['ItemStaff']['staff_id'])); ?>"> <i class=""></i></a><?php } ?>
		                    </span>
		                <?php } ?>
                    </span>
		                <?
		            }
		        }
		        ?>
		        <?php
		        if (ifPluginActive(FollowupPlugin) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $client['Client']['id'])) {
		            if ($last_appointment) {
		                ?>
                    <span class="invoice-client sub-heading sub-heading2">
		                <a style="font-size: 14px;color: #777;"  target="_blank" href="<?php echo Router::url(array('controller' => 'appointments', '?' => array('item_id' => $client['Client']['id']))) ?>"><i class="fa fa-calendar-o"></i> <?php echo '<strong>' . $actionlist[$last_appointment['ClientAppointment']['action_id']] . '</strong> ' . __('at', true) . ' ' . format_date($last_appointment['ClientAppointment']['date']) . ' ' . date('h:i A', strtotime($last_appointment['ClientAppointment']['date'])) ?></a>
                         </span>
		                <?
		            }
		        }
		        ?>
                    <?php if (ifPluginActive(AccountingPlugin)&&check_permission(VIEW_ALL_JOURNALS)&&!empty($account['JournalAccount']['id'])) { ?>
                    <span class="invoice-client sub-heading sub-heading2">
                       <?php echo __('Ledger Account',true) ?>: <a href="/v2/owner/chart-of-accounts/accounts/<?php echo $account['JournalAccount']['id'] ?>" target="_blank"><?php echo $account['JournalAccount']['name'].' #'. $account['JournalAccount']['code']?></a>
                    </span>
                    <?php } ?>
                </div>
		    </h1>
			<?php if(settings::getValue(ClientsPlugin,'focus_on_posts')) { ?>
			<a href="<?php echo Router::url(array('controller' => 'posts', 'action' => 'post', 1, $client['Client']['id'])); ?>" class="add-new-btn btn btn-success btn-addon btn-lg  button right"><span><i class="fa fa-book"></i><?= __('Add Note')?></span></a>
			<?php } ?>
            <?php if (!(isset($_GET['from_pos']))) { ?>
                <div class="top-actions" style="display: flex; align-items:center; margin-bottom: 7px">

                    <div class="pull-right" style="order: 2">
                        <?php if ($has_custom_data) {
                        echo $this->element('navigation', ['entityKey' => 'client', 'id' => $client['Client']['id']]);
                    } else{
                        echo $this->element('nav', array('id' => $client['Client']['id']));
                    } ?>
                        <?php // warning suppress
                            if(isset($appointments_nav) && $appointments_nav)
                        {

                            echo $this->element('nav',array('id'=> $appintment_id,'controller' => 'appointments'));
                        } ?>
                    </div>

                    <?php //Removed on purpose if (check_permission(Edit_And_delete_his_own_added_clients)) { ?>
                            <!--            <a class="add-new-btn btn btn-lg btn-success btn-addon button right width-auto" href="<?php echo Router::url(array('action' => 'edit', $client['Client']['id'])) ?>"><span><i class="fa fa-pencil"></i>
                                <?php __("Edit Client") ?>
                            </span></a> -->
                    <?php //} ?>

                    <?php
                    if (ifPluginActive(FollowupPlugin) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $client['Client']['id'])) {
                        $drop = "";
                        foreach ($FollowUpStatus as $key => $status_row) {
                            $pinIcon = "";
                            if( $status_row['sticky'] == 1 ){ 
                                $pinIcon = '<i class="mdi mdi-pin"></i>';
                            };
                            $drop_style[$key] = $status_row['style'];
                            $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $client['Client']['id'], $key)) . '" class="" tabindex="-1"><span ' . $status_row['style'] . ' class="badge rounded-3px shadow-light"><span>'.$pinIcon . $status_row['name'] . '</span></span></a>';
                            $button_no_link[$key] = '<a href="#" ' . $status_row['style'] . ' class="btn dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"> '.$pinIcon . $status_row['name'] . '</a>';
                        }
                        ?>
                        <div class="statuses statuses-btn-wrapper" style="display: flex; flex-direction: column; align-items: center; gap: 3px">
                            
                            <?php if (!check_permission(Edit_And_delete_his_own_added_clients)) { ?>

                            <div class="btn-group status-list">
                                <?php
                                foreach ($button as $key => $button_code) {
                                    if ($key == $client['Client']['secondary_follow_up_status']) {
                                        $drop = $drop_style[$key];
                                        echo $button_no_link[$key];
                                    }
                                }
                                ?>
                            </div>    
                        <?
                        } else {
                            ?>

                            <div class="btn-group status-list">
                                <?php
                                foreach ($button as $key => $button_code) {
                                    if ($key == $client['Client']['secondary_follow_up_status']) {
                                        $drop = $drop_style[$key];
                                        echo $button_no_link[$key];
                                    }
                                }
                                ?>

                                <button type="button"  <?php echo $drop ?> class="btn not btn-grey dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <?php
                                    if (empty($client['Client']['secondary_follow_up_status'])) {
                                        echo __('Select Status', true);
                                    }
                                    ?> <span class="caret"></span>
                                </button>



                                <ul class="dropdown-menu dropdown-menu-right">
                                    <?php if($client['Client']['secondary_follow_up_status'] != null){?>
                                        <li><a href="/owner/clients/change_status/<?php echo $client['Client']['id'] ?>/0" class="" tabindex="-1"><span style="border-color:white;background: white; color: black;" class="badge rounded-3px shadow-light"><span>Please Select</span></span></a></li>
                                    <?php } ?>
                                    <?php
                                    foreach ($button as $key => $button_code) {
                                        if ($key != $client['Client']['secondary_follow_up_status']) {
                                            ?>
                                            <li><?php echo str_replace('<i class="fa fa-check"></i>', '', $button_code) ?></li>
                                            <?
                                        }
                                    }
                                    ?>
                                    <li> <a href="/owner/follow_up_statuses/index/1" class="py-3" tabindex="-1"><i class="fa fa fa-cog"></i> <?php __('Edit statuses list for client') ?></a></li>
                                </ul>

                            </div>
                    

                        <?php
                        }?>
                    </div>
                    <?php }
                    ?>
                    <?php if(count($entity->getChildren())) { ?>
                        <?php 
                            $requestUri = $_SERVER['REQUEST_URI'];
                        ?>
                        <div class="btn-group m-r-xs ml-2 children-entities-btn-wrapper">
                            <a href="#" class="btn btn-lg add-new-btn btn-addon btn-success" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true"><span><i class="fa fa-plus"></i><?php __("Add") ;?></span> </a>
                            <ul class="dropdown-menu">
                                <?php
                                /**
                                 * @var \Izam\Daftra\Common\EntityStructure\Entity $entity
                                 */
                                foreach ($entity->getChildren() as $value) {
                                    ?>
                                    <li><a href="<?php echo "/v2/owner/children/{$value->getEntityKey()}/create/{$client['Client']['id']}?success_redirect={$requestUri}&backUrl=/owner/clients/view/{$client['Client']['id']}"?>">
                                            <?php echo sprintf(__('Add %s',true), $value->getLabel()) ?></a></li>
                                    <?php
                                }
                                ?>
                            </ul>
                        </div>
                    <?php } ?>
                </div>
            <?php } ?>
          
            <?php if ((userHavePermissionToAddInvoice() || !empty($site['is_super_admin']) || check_permission(Edit_And_delete_his_own_added_clients))) { 
                    echo $this->element('clients/client_balance'); 
            } 
            ?>
            <div class="mb-opt-btn"></div>
		    <div class="clear"></div>
		</div>
		</div>


			   <!---start client profile box-->
			   <div class="row" id="client-additional-data">
				   <div class="col-md-<?php echo $client_settings['map_location'] == '1'&&!empty($client['Client']['map_location']) ? "8" : "12" ?>">
						<div class="client-profile">
							<div class="media">
								<div class="row">
									<div class="col-md-7">

												  <?php
												  if($client_settings['photo'] == "1"){
                                                        echo '<div class="media-left media-top">';
                                                        $image_full_path = \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($client['Client']['business_name'], $client['Client']['photo']['id'] ?? null, 128, null);
                                                        if (!empty($client['AttachmentsByFieldKey'])) {
                                                            $image_full_path = \Izam\Aws\Aws::getPermanentUrl($client['AttachmentsByFieldKey'][0]['path']);
                                                        } elseif (!empty($client['Client']['photo'])) {
                                                            $image_full_path = DS . "files" . DS . SITE_HASH . DS . "photos" . DS . $client['Client']['photo'];
                                                        }

                                                       
                                                       
													  //values required
												  //field_name => name
												  //extensions => .jpg,.png ....
												  //image_full_path
												  //upload_photo_url
												  //get_photo_url
												  //image_edit_permission => a condition that if it == true the user can edit image
													 $staff_id = getAuthStaff('id');
													  echo $this->element("ajax_photo",array(
														  "img_attr","class='media-object'",
														  "field_name" => "file",
														  "extensions" => ".jpg,.png,.gif",
														  "image_full_path" => $image_full_path,
														  "get_photo_url"	=> Router::url(array('controller'=>'clients', 'action' => 'get_photo',$client['Client']['id'])),
														  "upload_photo_url"	=> "/v2/api/entity/upload_file/client/clients.photo?entity_id=".$client['Client']['id'], //Router::url(array('controller'=>'clients','action'=>'upload_photo',$client['Client']['id'])),
														  "image_edit_permission" => ( check_permission(Edit_Delete_all_clients) || (check_permission(Edit_And_delete_his_own_added_clients) && $client['Client']['staff_id'] == $staff_id))

													  ));
															  echo '</div>';
												  ?>

												  <?php } ?>

											<div class="media-body m-r-lg">
												<?php if (!empty($client['Client']['type']) && $client['Client']['type'] == 3 && (!empty ($client['Client']['first_name'] )|| !empty ($client['Client']['last_name'])) ){  ?>
													  <h4 class="media-heading"><?php echo $client['Client']['first_name'] . ' ' . $client['Client']['last_name'] ?></h4>
											  <?php } else if (! empty($client['Client']['business_name'])) { ?>
                                                    <h4 class="media-heading"><?= $client['Client']['business_name'] ?></h4>
                                              <?php } ?>
											 <div class="">
												 <ul class="f-s-15 m-b-10">
													 <?php if ( !empty ($client['Client']['city'] )|| !empty ($client['Client']['state'])|| !empty ($client['Client']['postal_code']) ){ ?><li>
														 <span class="fa fa-map-marker pull-left"></span>
														 <span class="client_address">
															 <?php //echo "{$client['Client']['address1'] }<br>{$client['Client']['address2'] }"; ?>
															 <?php // echo "{$client['Client']['city'] }, {$client['Client']['state'] } {$client['Client']['postal_code']}"; ?>
															 <?php echo $this->element ('format_address_html' , $client['Client'] ) ; ?>
													 <!-- khanka, qlyuopia  13758khanka, qlyuopia  13758khanka, qlyuopia  13758-->
														 </span>
														 </a>
													 </li><?php }?>
												 </ul>
											 </div>
											 <?php 	echo $this->element('tags_view',array('item_id' => $client['Client']['id'],'model_name' => 'Client','get_ajax' => true)); ?>
											</div>
									</div>
									<div class="col-md-5">
										<table class="f-s-17 <?= IS_PC? 'pull-right' : '' ?>">
											<tr>
												<td>
													<?php if (!empty ($client['Client']['email'] )){ ?>
                                                        <div class="d-flex">
                                                            <a href="mailto:<?php echo $client['Client']['email'] ?>" class="client_email full-width add-new-btn  btn btn-default text-center-mobile btn-addon width-auto btn-md  button m-b-10 text-left"><i class="fa fa-envelope"></i> <?php echo $client['Client']['email'] ?></a>
                                                            <button class="add-new-btn btn btn-default btn-md button m-b-10 non observed px-2 py-0 text-left tip width-auto text-center-mobile" title="<?= __('Copy') ?>" data-copy-btn="<?php echo $client['Client']['email'] ?>"><span class="fs-16 mdi mdi-content-copy"></span></button>
                                                        </div>
													 <?php } ?>
												</td>
											</tr>
											<tr>
												<td>
													<?php if( !empty ($client['Client']['phone1']) ){?>
                                                        <div class="d-flex">
														    <a class="client_email add-new-btn full-width  btn btn-default btn-addon width-auto btn-md  text-center-mobile button m-b-10 text-left" href="tel:<?php echo $client['Client']['phone1'] ?>" class="client_phone1"><i class="fa fa-phone"></i> <?php echo $client['Client']['phone1'] ?></a>
                                                            <button class="add-new-btn btn btn-default btn-md button m-b-10 non observed px-2 py-0 text-left tip width-auto text-center-mobile" title="<?= __('Copy') ?>" data-copy-btn="<?php echo $client['Client']['phone1'] ?>"><span class="fs-16 mdi mdi-content-copy"></span></button>
                                                        </div>
												    <?php }?>
												</td>
											</tr>
											<tr>
												<td>
													<?php if( !empty ($client['Client']['phone2']) ){?>
                                                        <div class="d-flex">
													        <a style="direction: ltr;" class="client_email add-new-btn full-width  btn btn-default btn-addon text-center-mobile width-auto btn-md  button m-b-10 text-left" href="tel:<?php echo preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $client['Client']['phone2']) ?>" class="client_phone2"><i class="fa fa-mobile"></i> <?php echo $client['Client']['phone2'] ?></a>
                                                            <button class="add-new-btn btn btn-default btn-md button m-b-10 non observed px-2 py-0 text-left tip width-auto text-center-mobile" title="<?= __('Copy') ?>" data-copy-btn="<?php echo $client['Client']['phone2'] ?>"><span class="fs-16 mdi mdi-content-copy"></span></button>
                                                        </div>
													<?php }?>
												</td>
											</tr>
										</table>
									</div>
								</div>
							</div>
						</div>
				   </div>
				   <div class="col-md-4 p-0">
						<?php if($client_settings['map_location'] == '1' &&!empty($client['Client']['map_location'])){ ?>
				<?php

				echo $this->element("map",
							array(
								'country'=> $client['Client']['country_code'],
								'state' => $client['Client']['state'],
								"map_location" => $client['Client']['map_location'],
								"geocode_api" => $geocode_api ,
								"google_maps_api" => $google_maps_api ,
								"view_only" => true
	//							"map_auto_update" => true,
	//							"map_auto_update_url" => Router::url(array('action' => "ajax_map_update")),
	//							"map_auto_update_record_id" => $client['Client']['id']
							)
						) ?>
				<?php } ?>

				   </div>
			   </div>
			   <!---end client profile box-->
<div class="invoice-actions btn-group dropdown-btn-group">
    <?php if (check_permission(Edit_And_delete_his_own_added_clients)) { ?>
        <a href="<?php echo Router::url(array('action' => 'edit', $client['Client']['id'])) ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-pencil"></i> <?php __("Edit") ?></a>
    <?php } ?>
    <?php
    if (ifPluginActive(FollowupPlugin) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $client['Client']['id'])) {
        if (check_permission(Add_Notes_Attachments_For_All_Clients) or (check_permission(Add_Notes_Attachments_For_His_Assigned_Clients_Only) and $is_assignd == true )) {
            ?>
            <a   href="<?php echo Router::url(array('controller' => 'posts', 'action' => 'post', 1, $client['Client']['id'])); ?>" class=" btn btn-default btn-sm btn-5 "> <i class="fa fa-book"></i> <?php __("Add Note / Attachment") ?></a>
            <?php if ($client['Client']['suspend'] == 0 || is_null($client['Client']['suspend'])): ?>
            <a   href="<?php echo Router::url(array('controller' => 'appointments', 'action' => 'add', $client['Client']['id'] , Post::CLIENT_TYPE)); ?>" class=" btn btn-default btn-sm btn-5 "> <i class="fa fa-calendar-o"></i> <?php __("Schedule Appointment") ?></a>
            <?php endif; ?>
        <?php } ?>
    <?php } ?>
    <?php if (!empty($client['Client']['email'])) { ?>
        <a href="<?php echo Router::url(array('action' => 'send_email', $client['Client']['id'])) ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-envelope-o"></i> <?php __("Send Email") ?></a>
    <?php } ?>
    <?php if ((!empty($client['Client']['phone1'])||!empty($client['Client']['phone2']))&&sms_enabled()) { ?>
        <a href="<?php echo Router::url(array('controller'=>"sms_campaigns", 'action' => 'send_sms', "clients", $client['Client']['id'])) ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-comments-o"></i> <?php __("Send SMS") ?></a>
    <?php } ?>
    <?php if (ifPluginActive(SalesPlugin) && userHavePermissionToAddInvoice() and $client['Client']['suspend']!="1") { ?>
        <a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add', '?' => array('client_id' => $client['Client']['id']))) ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-money"></i> <?php __("Create Invoice") ?> </a>
    <?php } ?>
    <?php if (ifPluginActive(SalesPlugin) && userHavePermissionToAddEstimate() && $client['Client']['suspend']!="1") { ?>
        <a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add_estimate', '?' => array('client_id' => $client['Client']['id']))) ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-money"></i> <?php __("Create Estimate") ?> </a> 
    <?php } ?>
    <?php if (ifPluginActive(SalesPlugin) && userHavePermissionToAddInvoice()  && $client['Client']['suspend']!="1") { ?>
        <a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add_creditnote', '?' => array('client_id' => $client['Client']['id']))) ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-money"></i> <?php __("Create Credit Note") ?> </a> 
    <?php } ?>
    <?php if (!IS_PC && ifPluginActive(WorkOrderPlugin) && check_permission(ADD_NEW_WORK_ORDERS) and $client['Client']['suspend']!="1") { ?>
        <a href="<?php echo Router::url(array('controller' => 'work_orders', 'action' => 'add', '?' => array('client_id' => $client['Client']['id']))) ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-money"></i> <?php echo sprintf(__("Add %s",true), __('Work Order',true) ); ?> </a>
    <?php } if(check_permission(Invoices_View_All_Invoices) || check_permission(Invoices_View_Invoices_Details)){?>
        <a href="<?php echo Router::url(array('action' => 'statement', $client['Client']['id'])) ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-calculator"></i> <?php __("Statement") ?></a>
    <?php } if (ifPluginActive(PrescriptionPlugin)) { ?>
        <a href="<?php echo Router::url(array('plugin'=>'clinic','controller' => 'prescriptions', 'action' => 'owner_add', '?' => array('client_id' => $client['Client']['id']))) ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-user-md"></i> <?php __("Add Prescription") ?></a>
    <?php } ?>
    <?php if ( check_permission(Invoices_Add_Payments_to_All) && ifPluginActive(SalesPlugin) && userHavePermissionToAddInvoice()  && $client['Client']['suspend']!="1") { ?>
        <a href="<?php echo Router::url(array('action' => 'add_payment_credit',$client['Client']['id'])); ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-money"></i> <?php __("Add Payment Credit") ?> </a>
    <?php } ?>
    <?php 
    if (ifPluginActive(SalesPlugin) &&  (check_permission(SALES_ORDER_ADD_NEW_TO_ALL_CLIENTS)  || (check_permission(SALES_ORDER_ADD_NEW_TO_HIS_OWN_CLIENTS ) && $is_assignd) )   && $client['Client']['suspend']!="1" && settings::getValue(InvoicesPlugin, 'enable_sales_order') ) { ?>
        <a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add_sales_order', '?' => array('client_id' => $client['Client']['id']))); ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-money"></i> <?php  echo sprintf(__("Add %s", true),__("Sales Order", true))  ?> </a>
    <?php } ?>





    <?
    if (!IS_PC) {
        ?>
        <? if(!empty($sb)) {?>
            <a href="<?php echo Router::url(array('controller'=>'invoice_payments','action' => 'edit', $sb['InvoicePayment']['id'],'starting_balance')); ?>" class=""><i class="fa fa-credit-card"></i> <?php __("Edit Opening Balance") ?></a>
            <?php }else{?>
            <a href="<?php echo Router::url(array('controller'=>'invoice_payments','action' => 'add_open_balance',$client['Client']['id'],'starting_balance')); ?>" class=""><i class="fa fa-credit-card"></i> <?php __("Add Opening Balance") ?></a>
        <? } ?>

        <?php if (empty($client['Client']['is_offline'])) { ?>
            <a onclick="return confirm('<?php echo __('This will change the old password and create a new one for this client, do you want to proceed?') ?>');" href="<?php echo Router::url(array('action' => 'send_login_details', $client['Client']['id'])) ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-envelope-o"></i> <?php __("Send Login Details") ?></a>
        <?php } ?>
        <?php if (empty($client['Client']['is_offline'])) { ?>
            <a href="<?php echo Router::url(array('action' => 'change_password', $client['Client']['id'])) ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-envelope-o"></i> <?php __("Change Password") ?></a>
        <?php } ?>

        <?php if (!empty($site['is_super_admin'])) { ?>
            <a href="<?php echo Router::url(array('action' => 'login_as', $client['Client']['id'])) ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-unlock-alt"></i> <?php __("Login as Client") ?> </a>
        <?php } ?>

        <?php
            if (check_permission(Edit_Delete_all_clients) || ($client['Client']['staff_id'] == getAuthOwner('staff_id')) && check_permission(Edit_And_delete_his_own_added_clients)) {
                $clientStatus = empty($client['Client']['suspend']) ? 'fa-minus-circle' : 'fa fa-check';
        ?>
            <a href="<?php echo Router::url(array('action' => 'suspend_users', empty($client['Client']['suspend']) ? 1 : 0, $client['Client']['id'])) ?>" class="btn btn-default btn-sm btn-5 "><i class="fa <?php echo $clientStatus; ?>"></i> <?php echo empty($client['Client']['suspend']) ? __('Suspend', true) : __('Unsuspend', true) ?></a>
        <?php } ?>

        <?php if (ifPluginActive(FollowupPlugin) && ifPluginActive(StaffPlugin) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $client['Client']['id'])) {
            if (check_permission(Assign_Clients_To_Staff)) {
                if ($staff_count > 0) {
                    ?>
                    <a  data-toggle="modal" data-target="#assign_staff_box" href="#" class="assign_staff" > <i class="fa fa-user"></i>  <?php __("Assign to a Staff Member") ?> </a>
                    <?php
                }
            } ?>
        <?php } ?>
        <?php if (check_permission(Edit_And_delete_his_own_added_clients)) { ?>
            <a href="<?php echo Router::url(array('action' => 'delete_client', $client['Client']['id'])) ?>" class="btn btn-default btn-sm btn-4 "> <i class="fa fa-trash-o"></i> <?php __("Delete Account") ?></a>
        <?php } ?>
        <a href="<?php echo Router::url(array('action' => 'add', $client['Client']['id'])) ?>" class=""><i class="fa fa-copy"></i> <?php __("Clone") ?></a>
        <?
    }
    ?>
        <?php  if(isset($shareWithSocialMedia) && $shareWithSocialMedia){
        ?>
            <div class="btn-group">
                <?php if (count($socialLinks) > 1) : ?>
                    <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                        <span class="fa fa-share" aria-hidden="true"></span>
                        <?php echo sprintf(__('Send Via %s', true), '') ?> <span class="caret"></span>
                    </button>

                    <ul class="dropdown-menu">
                        <?php
                        foreach ($socialLinks as $key => $socialLink) {
                            ?>
                            <li>
                                <?= $socialLink['link'] ?>
                            </li>
                            <?php
                        }
                        ?>
                    </ul>
                <?php else: ?>
                    <?php
                    $socialMediaLabel = match($socialLinks[0]['label']){
                        'whatsapp' => 'WhatsApp',
                        default => $socialLinks[0]['label']
                    };
                    $pattern = '/<a href="([^"]+)" target="_blank">'.  ucfirst( $socialLinks[0]['label']) .'<\/a>/';
                    $replacement = '<a href="$1" class="btn btn-default btn-sm quick-action-btn"><span class="fa fa-share" aria-hidden="true"></span>'. sprintf( __('Send Via %s', true), $socialMediaLabel)   .' </a>';
                    echo preg_replace($pattern, $replacement, $socialLinks[0]['link']) ?>
                <?php endif; ?>
            </div>
    <?php }
    ?>
    <?php if ((userHavePermissionToAddInvoice() || !empty($site['is_super_admin']) || check_permission(Edit_And_delete_his_own_added_clients))) { ?>
        <?php if($has_templates): ?>
            <div class="btn-group">
                <button type="button"   class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <?php echo __('Voucher', true) ?> <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" >
                    <?php foreach($printableTemplates as $template) : ?>
                        <li>
                            <a href="<?php echo Router::url(array('controller'=>'printable_templates', 'action' => 'view',  $client['Client']['id'], $template['PrintableTemplate']['id'], Inflector::singularize(strtolower($this->name)) )) ?>" class=""> <?= $template['PrintableTemplate']['name']; ?></a>
                        </li>
                    <?php endforeach; ?>
                    <?php
                        echo draw_templates_list($view_templates, $client['Client']['id']);
                    ?>
                </ul>
            </div>
        <?php endif; ?>
    <?php } ?>
    <?php if (IS_PC && (userHavePermissionToAddInvoice() || !empty($site['is_super_admin']) || check_permission(Edit_And_delete_his_own_added_clients))) { ?>
        <div class="btn-group">
            <button type="button"   class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <?php echo __('More Actions', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu ">
				<?php

				?>
                <? if(!empty($sb)) {?>
                    <li>
                        <a href="<?php echo Router::url(array('controller'=>'invoice_payments','action' => 'edit', $sb['InvoicePayment']['id'],'starting_balance')); ?>" class=""><i class="fa fa-credit-card"></i> <?php __("Edit Opening Balance") ?></a>
                    </li>
                <?php }else{?>
                    <li>
                        <a href="<?php echo Router::url(array('controller'=>'invoice_payments','action' => 'add_open_balance',$client['Client']['id'],'starting_balance')); ?>" class=""><i class="fa fa-credit-card"></i> <?php __("Add Opening Balance") ?></a>
                    </li>
                <? } ?>
                <?php if (ifPluginActive(WorkOrderPlugin) && check_permission(ADD_NEW_WORK_ORDERS) and $client['Client']['suspend']!="1") { ?>
                    <li>
                        <a href="<?php echo Router::url(array('controller' => 'work_orders', 'action' => 'add', '?' => array('client_id' => $client['Client']['id']))) ?>"><i class="fa fa-money"></i> <?php echo sprintf(__("Add %s",true), __('Work Order',true) ); ?> </a>
                    </li>
                <?php } ?>
                <?php if ($client['Client']['is_offline'] != 1 && $client_settings['client_disable_online_access'] != '1' ) { ?>
                    <li><a onclick="return confirm('<?php __('This will change and generate new password for this client and send login details . Are you sure?') ?> ');" href="<?php echo Router::url(array('action' => 'send_login_details', $client['Client']['id'])) ?>" class=""><i class="fa fa-envelope-o"></i> <?php __("Send Login Details") ?></a>  </li>
                <?php } ?>
                <?php if (empty($client['Client']['is_offline']) && $client_settings['client_disable_online_access'] != '1' ) { ?>
                    <li><a  href="<?php echo Router::url(array('action' => 'change_password', $client['Client']['id'])) ?>" class=""><i class="fa fa-unlock-alt"></i> <?php __("Change Password") ?></a>  </li>
                <?php } ?>
                <?php if (!empty($site['is_super_admin']) && $client_settings['client_disable_online_access'] != '1' ) { ?>
                    <li><a href="<?php echo Router::url(array('action' => 'login_as', $client['Client']['id'])) ?>" class=""><i class="fa fa-unlock-alt"> </i> <?php __("Login as Client") ?> </a>
                    </li>
                <?php } ?>
                <?php
                if (check_permission(Edit_Delete_all_clients) || ($client['Client']['staff_id'] == getAuthOwner('staff_id')) && check_permission(Edit_And_delete_his_own_added_clients)) {
                ?>
                <li>
                    <?php $clientStatus = empty($client['Client']['suspend']) ? 'fa-minus-circle' : 'fa fa-check'; ?>
                    <a href="<?php echo Router::url(array('action' => 'suspend_users', empty($client['Client']['suspend']) ? 1 : 0, $client['Client']['id'])) ?>" class=""><i class="fa <?php echo $clientStatus; ?>"></i> <?php echo empty($client['Client']['suspend']) ? __('Suspend', true) : __('Unsuspend', true) ?></a>
                </li>
                <?php
                }
                ?>
                <?php if (check_permission(Edit_And_delete_his_own_added_clients)) { ?>
                    <li><a href="<?php echo Router::url(array('action' => 'delete_client', $client['Client']['id'])) ?>" class=""> <i class="fa fa-trash-o"></i> <?php __("Delete Client") ?></a></li>
                <?php } if (ifPluginActive(FollowupPlugin) && ifPluginActive(StaffPlugin) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $client['Client']['id'])) {
                    if (check_permission(Assign_Clients_To_Staff)) {
                        if ($staff_count > 0) {
                            ?>
                <li><a  data-toggle="modal" data-target="#assign_staff_box" href="#" class="assign_staff" > <i class="fa fa-user"></i>  <?php __("Assign to a Staff Member") ?> </a></li>
                            <?php
                        }
                    }
                }
                ?>
                <li><a href="<?php echo Router::url(array('action' => 'add', $client['Client']['id'])) ?>" class=""><i class="fa fa-copy"></i> <?php __("Clone") ?></a></li>
            </ul>
        </div>
    <?php } ?>
</div>

<style>
    .modal-open .pages-head {
        z-index: 10;
    }
</style>
<?php
echo $javascript->link(array('owner-view_v' . JAVASCRIPT_VERSION . '.js'));
?>
			   <!--start hold_dropdown_in_tab to more tab--->
			   <?php if(IS_PC){ ?>
					<li style=" display: none;" class="dropdown new-li-in-tab">
					   <a class="dropdown-toggle" data-toggle="dropdown" href="#"><?php echo  __('More')?><span class="caret"></span></a>
					   <ul class="dropdown-menu new-dp-tabs">
					   </ul>
					</li>
					<?php echo $javascript->link(array('hold_dropdown_in_tab.js','jquery.ocupload.js')); ?>
				   <style>
					   .tab_into_drop{
						   display: none !important;
					   }
				   </style>
			   <?php } ?>
			   <!--end hold_dropdown_in_tab to more tab--->
<div class="tabs-box box">
    <ul class="nav nav-tabs responsive" role="tablist">
        <li role="presentation" class="active"><a class="active" href="#ProfileBlock" aria-controls="ProfileBlock" role="tab" data-toggle="tab"><span class="one-line">
                    <?php __("Details") ?>
                </span></a>
        </li>
        <?php
        if (ifPluginActive(FollowupPlugin) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $client['Client']['id'])) {

            if (check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) or (check_permission(Add_Notes_Attachments_For_His_Assigned_Clients_Only) and $is_assignd == true)) {
                if ($post_count > 0) {
                    ?>
                    <li role="presentation"><a aria-controls="actions-tab" role="tab" data-toggle="tab" onclick="reload_posts()" href="#NotesBlock" title="<?php __('Notes / Attachments') ?>"><span class="one-line"><?php __('Notes / Attachments') ?>  <small class="counter"> (<?php echo $post_count ?>)</small> </span></a></li>
                    <?php
                }
                    if (array_sum($appointment_status_counts ?? []) > 0  ) {
                    ?>
                    <li role="presentation"><a aria-controls="actions-tab" role="tab" data-toggle="tab" onclick="reload_appointments($('#statuses').val())" href="#AppointmentsBlock" title="<?php __('Appointments') ?>"><span class="one-line"><?php __('Appointments') ?>  <small class="counter"> (<?php echo $appointments_count ?>)</small>  </span></a></li>
                    <?php
                    }
            }
        }
        ?>
        <?php if (ifPluginActive(BookingPlugin) && $bookingsCount > 0) { ?>
            <li role="presentation">
                <a onclick=" <?php if (IS_PC) { ?>reload_bookings()<?php }?>" 
                   href="#BookingsBlock" 
                   aria-controls="BookingsBlock" role="tab" data-toggle="tab" 
                   title="<?php printf(__('Latest Bookings for %s', true), h($client['Client']['business_name'])) ?>"
                ><span class="one-line">
                    <?php __("Bookings") ?>
                    <small class="counter">(<?php echo $bookingsCount; ?>)</small>
                </span>
                </a>
            </li>
        <?php } ?>
        <?php if (ifPluginActive(SalesPlugin) && $invoicesCount > 0) { ?>
            <li role="presentation"><a onclick=" <?php if (IS_PC) { ?>reload_invoices()<?php }?>"  href="#LatestInvoices"  aria-controls="LatestInvoices" role="tab" data-toggle="tab" title="<?php printf(__('Latest invoices for %s', true), h($client['Client']['business_name'])) ?>"><span class="one-line">
                        <?php __("Invoices ") ?>
                        <small class="counter">(<?php echo $invoicesCount; ?>)</small></span></a></li>
        <?php } ?>
        <?php
        if(ifPluginActive(SalesPlugin) && settings::getValue(InvoicesPlugin, 'enable_sales_order')  && $salesOrderCount && (check_permission(SALES_ORDER_VIEW_ALL_SALES_ORDER) || check_permission(SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER))){
            ?>
            <li role="presentation"><a aria-controls="actions-tab" role="tab" data-toggle="tab" onclick="reload_filter()" href="#SalesOrderBlock" title="<?php __("Sales Order") ?>"><span class="one-line"><?php __("Sales Order") ?><small class="counter">(<?php echo $salesOrderCount; ?>)</small></span></a></li> </span></a></li>
        <?php }?>

        <?php if (ifPluginActive(SalesPlugin) && $paymentsCount >0) { ?>
            <li role="presentation"><a onclick="reload_payments()"  href="#LatestPayments"  aria-controls="LatestPayments" role="tab" data-toggle="tab" title="<?php printf(__('Latest payments for %s', true), h($client['Client']['business_name'])) ?>"><span class="one-line">
                        <?php __("Payments") ?>
                        <small class="counter">(<?php echo $paymentsCount; ?>)</small></span></a></li>
        <?php } ?>
           <?php if ( $transaction_count > 0 && (check_permission(Invoices_View_All_Invoices) || check_permission(Invoices_View_Invoices_Details))) {?>
            <li role="presentation"><a onclick="reload_transaction()"  href="#TransactionList"  aria-controls="TransactionList" role="tab" data-toggle="tab" title=""><span class="one-line">
                        <?php __("Transaction List ") ?>
                        <small class="counter">(<?php echo $transaction_count; ?>)</small></span></a></li>
           <?php }?>

        <?php if ($estimatesCount != 0) { ?>
            <li role="presentation"><a onclick="<?php if (IS_PC) { ?>reload_estimates() <?php }?>"  href="#LatestEstimates"  aria-controls="LatestEstimates" role="tab" data-toggle="tab" title="<?php printf(__('Latest estimates for %s', true), h($client['Client']['business_name'])) ?>"><span class="one-line">
                        <?php __("Estimates") ?>
                        <small class="counter">(<?php echo $estimatesCount; ?>)</small></span></a></li>
        <?php } ?>

        <?php if (ifPluginActive(WorkOrderPlugin) && $workOrdersCount != 0) { ?>
            <li role="presentation"><a onclick="<?php if (IS_PC) { ?>reload_work_orders() <?php }?>"  href="#LatestWorkOrders"  aria-controls="LatestWorkOrders" role="tab" data-toggle="tab" title="<?php printf(__('Latest work orders for %s', true), h($client['Client']['business_name'])) ?>"><span class="one-line">
                        <?php __("Work orders") ?>
                        <small class="counter">(<?php echo $workOrdersCount; ?>)</small></span></a></li>
        <?php } ?>

        <?
        if (check_permission(View_All_Activity_Logs) || check_permission(View_His_Activity_Logs)) {
            ?>
            <?php if ($emailCount > 0) { ?>
                <li role="presentation"><a href="#LatestNotification"  aria-controls="LatestNotification" role="tab" data-toggle="tab" title="<?php printf(__("Latest notification emails sent to %s", true), h($client['Client']['business_name'])) ?>"><span  class="one-line">
                            <?php __("Sent Emails") ?>
                            <small class="counter">(<?php echo $emailCount; ?>)</small></span></a></li>
                <?
            }
        }
        ?>

        <?php
            if(ifPluginActive(CREDIT_PLUGIN)){ ?>
                <li role="presentation"><a aria-controls="CreditSummaryBlock" role="tab" data-toggle="tab" onclick="reload_credit_summary()" href="#CreditSummaryBlock" title="<?php __('Credit Summary') ?>"><span class="one-line"><?php __('Credit Summary') ?> </span></a></li>
            <?php }
        ?>

        <?php
        if(ifPluginActive(MEMBERSHIP_PLUGIN)){ ?>
            <li role="presentation"><a aria-controls="MembershipBlock" role="tab" data-toggle="tab" onclick="reload_membership()" href="#MembershipBlock" title="<?php __('Membership') ?>"><span class="one-line"><?php __('Membership') ?> </span></a></li>
        <?php }
        ?>
        <?php
        if(ifPluginActive(CLIENT_ATTENDANCE_PLUGIN) && $clientAttendanceLogsCount > 0){ ?>
            <li role="presentation"><a aria-controls="ClientAttendanceLogBlock" role="tab" data-toggle="tab" onclick="reload_client_attendance()" href="#ClientAttendanceLogBlock" title="<?php __('Client Attendance Logs') ?>"><span class="one-line"><?php __('Client Attendance Logs') ?> (<?= $clientAttendanceLogsCount ?>) </span></a></li>
        <?php }
        ?>

        <?php
        if (check_permission(View_All_Activity_Logs) || check_permission(View_His_Activity_Logs)) {
            ?>
            <li role="presentation"><a aria-controls="actions-tab" role="tab" data-toggle="tab" onclick="reload_filter()" href="#TimelineBlock" title="<?php __('Timeline') ?>"><span class="one-line"><?php __('Timeline') ?> </span></a></li>
            <?php if ($showActivityLog) { ?>
            <li role="presentation">
                <a aria-controls="ActivityLogBlock" role="tab" data-toggle="tab" href="#ActivityLogBlock"
                   title="<?= __t('Activity Log for Client') ?>">
                    <span class="one-line"><?= __('Activity Log') ?></span>
                </a>
            </li>
            <? } ?>
            <?
        }
        ?>

        
        
        <?php 
        use Izam\Daftra\Common\Utils\PermissionUtil;
        if (ifPluginActive(WorkflowPlugin) && (check_permission(PermissionUtil::VIEW_HIS_OWN_WORKFLOWS) || check_permission(PermissionUtil::VIEW_ALL_WORKFLOWS))) {
            foreach ($workFlowTabs as $index => $tab) {
        ?>
            <li role="presentation">
                <a aria-controls="workflowBlock<?= $index ?>" role="tab" data-toggle="tab" href="#workflowBlock<?= $index?>" title="<?= $tab['name'] ?>">
                    <span class="one-line"><?= $tab['name'] ?></span>
                </a>
            </li>
        <?php 
            } 
        } 
        ?>   

        <?php
            if(ifPluginActive(PrescriptionPlugin) && !empty($prescs))
            {
                //get all the current active plugins
                $plugins = getCurrentPlugin(true);
                //check if this plugion is external to find itin plugins folder
                foreach($plugins as $plugin)
                {
                    if($plugin['Plugin']['is_external'] == true) {
                        $pluginFile = (ROOT . DS . APP_DIR . "/plugins/{$plugin['Plugin']['plugin_key']}/{$plugin['Plugin']['plugin_key']}_integration.php");
                        if (file_exists($pluginFile)) {
                            require_once ($pluginFile);
                            $class_name = $plugin['Plugin']['plugin_key'] . "_integration";
                            $temp = $class_name::get_elements_config();
                            $plugin_tabs = $temp['clients']['client_tabs'];
                            //die(debug($plugin_tabs));
                            foreach ($plugin_tabs as $tab) {
                                ?>
                                <li role="presentation">
                                    <a aria-controls="actions-tab"
                                       role="tab"
                                       data-toggle="tab"
                                       href="#Manage<?php echo $tab['plugin_name']; ?>"
                                       title="<?php echo $tab['title']; ?>"><span class="one-line"><?php echo $tab['title']; ?> </span></a>
                                </li>
                                <?php
                            }
                        }
                    }
                }
            }
        ?>


        <?php foreach ($this->data['Tabs'] as $k => $t) { ?>
            <li role=""><a role="tab" data-toggle="tab" href="#tabs_<?php echo $k; ?>" title="<?php echo __($t['name']) ?>"><span class="one-line"><?php echo __($t['name']) ?> </span></a></li>
        <?php } ?>
        <?php
        $tabsViewHelper = new \Izam\Forms\Tab\Helper\TabsViewHelper();
        foreach ($tabActions as $tab) {
            echo $tabsViewHelper->renderTabHeader($tab);
        }
        ?>
    </ul>

    <!-- / tabs-buttons -->
    <div class="tab-content responsive">

       <?php
       if (ifPluginActive(PrescriptionPlugin) && !empty($prescs))
           {
               $plugins = getCurrentPlugin(true);
               foreach($plugins as $plugin)
               {
                   if($plugin['Plugin']['is_external'] == true)
                   {
                       $class_name = $plugin['Plugin']['plugin_key'] . "_integration";
                       $temp = $class_name::get_elements_config();
                       $plugin_tabs = $temp['clients']['client_tabs'];
                   ?>
                       <div class="tab-pane" role="tabpanel" id="Manage<?php echo $tab['plugin_name']; ?>">

                           <?php echo $this->element('../../plugins/'.$tab['plugin_name'].'/elements/'.$tab['element']); ?>

                       </div>
                    <?php
                   }
               }
           }
           ?>

        <?php
        if (check_permission(View_All_Activity_Logs) or check_permission(View_His_Activity_Logs)) {
            ?>
            <div class="tab-pane" role="tabpanel" id="LatestNotification">
                <?php
                if (!empty($email_logs)):
                    foreach ($email_logs as $i => $email_log) {
                        $date = ($email_log['EmailLog']['sent_date']);
                        if (!$date) {
                            $date = ($email_log['EmailLog']['created']);
                        }
                        $date = format_date($date) . date(' H:i', strtotime($date));
                        $email_logs[$i]['EmailLog']['time'] = $date;
                    }
                    ?>
                    <h4><?php printf(__("Latest notification emails sent to %s", true), h($client['Client']['business_name'])) ?></h4>
                    <?php if (IS_PC) { ?>
                        <div class="table-responsive">
                            <table class="table table-striped b-light" width="100%">
                                <tr class="table-header">
                                    <th><?php __('Subject'); ?></th>
                                    <th><?php __('From'); ?></th>
                                    <th><?php __('To'); ?></th>
                                    <th><?php __('Invoice'); ?></th>
                                    <th><?php __('Sent date'); ?></th>
                                    <th><?php __("Actions"); ?></th>
                                </tr>
                                <?php
                                $sitename = $site['first_name'] . ' ' . $site['last_name'];
                                ?>
                                <?php
                                debug($email_logs);
                                foreach ($email_logs as $email_log):
                                    ?>
                                    <tr>
                                        <td><?php echo $email_log['EmailLog']['subject']; ?></td>
                                        <td><?php echo $email_log['EmailLog']['send_from']; ?></td>
                                        <td><?php echo $email_log['EmailLog']['send_to']; ?></td>
                                        <td><?php
                    if ($email_log['Invoice']['type'] == 0) {
                        $action = 'view';
                    } else {
                        $action = 'view_estimate';
                    }
                    echo $html->link($email_log['Invoice']['no'], array('controller' => 'invoices', 'action' => $action, $email_log['EmailLog']['invoice_id']));
                                    ?></td>
                                        <td><?php echo $email_log['EmailLog']['time']; ?></td>
                                        <td><ul class="action">
                                                <li><?php echo $html->link(__('View', true), array('controller' => 'email_logs', 'action' => 'view', $email_log['EmailLog']['id']), array('class' => 'View view-emaillog')); ?></li>
                                            </ul></td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>

                        
                        

                         



                        <?
                    } else {
                        echo $list->adminResponsiveList($email_logs, 'general_row', array('model' => 'EmailLog', 'main_field' => 'subject', 'secondary_field' => 'time', 'default_action' => false, 'actions' => array(array('url' => $html->link(__('View', true), array('controller' => 'email_logs', 'action' => 'view', '%id%'), array('class' => 'View view-payment'))))), array('no_paging' => true));
                    }
                    ?>
                    <?php
                    if ($emailCount > count($email_logs)) {
                        echo $html->link(sprintf(__('View all %d notification emails', true), $emailCount), array('controller' => 'email_logs', 'action' => 'index', '?' => array('client_id' => $client['Client']['id'])), array('class' => 'view-all barrow'));
                    }
                    ?>
                <?php else : ?>
                    <div class="Notemessage">
                        <?php __("No notifications have been sent to this client yet") ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php } ?>

        <?php if(ifPluginActive(BookingPlugin)){  ?>
            <div class="tab-pane" id="BookingsBlock">
                <iframe src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
            </div>
        <?php } ?>

        <?php
        foreach ($tabActions as $tab) {
            echo $tabsViewHelper->renderTabDetails($tab);
        }
        ?>

        <?php if(ifPluginActive(CREDIT_PLUGIN)){  ?>
        <div class="tab-pane" id="CreditSummaryBlock">
            <iframe id="CreditSummaryIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>
        <?php } ?><?php if(ifPluginActive(MEMBERSHIP_PLUGIN)){  ?>
        <div class="tab-pane" id="MembershipBlock">
            <iframe id="MembershipIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>
        <?php } ?>
        <?php if(ifPluginActive(CLIENT_ATTENDANCE_PLUGIN) && $clientAttendanceLogsCount > 0){  ?>
        <div class="tab-pane" id="ClientAttendanceLogBlock">
            <iframe id="ClientAttendanceLogBlockIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>
        <?php } ?>
        <?php
        if (check_permission(View_All_Activity_Logs) or check_permission(View_His_Activity_Logs)) {
            ?>
            <div class="tab-pane" id="TimelineBlock">
                <script type="text/javascript">
                var timeline_url = "<?php echo Router::url(array('action' => 'timeline', $client['Client']['id'])) ?>";
                var timeline_row_url = "<?php echo Router::url(array('action' => 'timeline_row')) ?>";
                </script>
                <?php echo $this->element('timeline'); ?>
            </div>
            <?php if ($showActivityLog) { ?>
            <div class="tab-pane" id="ActivityLogBlock">
                <iframe src="/v2/owner/activity_logs/entity/iframe?entity_key=client&entity_id=<?= $client['Client']['id'] ?>&sort=DESC&layout2022=1"
                        style="width: 100%; min-height: 500px;border: 0"></iframe>
            </div>
            <?php } ?>

            <?php
            
            if(ifPluginActive(SalesPlugin) && settings::getValue(InvoicesPlugin, 'enable_sales_order') && $salesOrderCount && (check_permission(SALES_ORDER_VIEW_ALL_SALES_ORDER) || check_permission(SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER))){?>
                <div class="tab-pane" id="SalesOrderBlock">
                <iframe src="/v2/owner/entity/sales_order/list?iframe=1&filter[client_id]=<?= $client['Client']['id'] ?>&hide_filters=1&hide_actions=1&hide_page_header=1"
                style="width: 100%; min-height: 500px;border: 0"></iframe>
                </div>

            <?php }?>
        <?php } ?>
        <?php 
        if (ifPluginActive(WorkflowPlugin) && (check_permission(PermissionUtil::VIEW_HIS_OWN_WORKFLOWS) || check_permission(PermissionUtil::VIEW_ALL_WORKFLOWS))) {
            foreach($workFlowTabs as $index => $tab){ ?>
                <div class="tab-pane" id="workflowBlock<?= $index?>">
                    <iframe src="/v2/owner/entity/workflow/<?= $tab['entityKey'] ?>/list?filter[client_id]=<?= $client['Client']['id'] ?>&iframe=1" style="width: 100%; min-height: 500px;border: 0"></iframe>
                </div>
        <?php } } ?>

        <?php foreach ($this->data['Tabs'] as $k => $t) { ?>
            <div class="tab-pane" id="tabs_<?php echo $k ?>">
                <iframe style="width: 100%; height: 100%;min-height: 700px;" src="<?php echo $t['url'] ?>" ></iframe>

            </div>
        <?php } ?>
        <script>var client_id = <?php echo $client['Client']['id'] ?>;</script>

        <?php
        if (ifPluginActive(FollowupPlugin) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $client['Client']['id'])) {

            if (check_permission(View_All_Attachments_And_Notes_For_All_Clients) or check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients)or check_permission(Add_Notes_Attachments_For_All_Clients) or (check_permission(Add_Notes_Attachments_For_His_Assigned_Clients_Only) and $is_assignd == true )) {
                if ($post_count > 0) {
                    ?>
                    <div class="tab-pane" id="NotesBlock">
                        <?php echo $this->element('clients/client_note', ['item_type' => Post::CLIENT_TYPE , 'item_id' => $client['Client']['id']]); ?>
                    </div>
                    <?php
                }
                if (array_sum($appointment_status_counts ?: [] ) > 0  ) {
                    ?>
                    <div class="tab-pane" id="AppointmentsBlock">
                        <div class="panel panel-default">
        <div class="panel-body ">
        <div class="panel panel-default no-margin" style="border-bottom: none;">
                        <div class="panel-heading" style="border-bottom: none;">

                     <div  class="btn-group  pull-right pos3">
                     <?php foreach ($appointment_statuses as $k => $s) { ?>
                        <a class=" btn btn-default reload_status <?php if ($k == 0) { ?>active<?php } ?>" title="<?php echo $s ?>" status="<?php echo $k; ?>">
                            <?php echo $s; ?>
                            <span class="reload_status_count_<?php echo $s ?>"><?php echo " ($appointment_status_counts[$k])" ?></span>
                        </a>
                     <?php } ?>
                     </div>
                     <div class="clearfix"></div>
                     </div>
                     </div>
            <div class="row" >
                <div  class="col-md-12" id="AppointmentsBlock_rel">
                    <div class="notifcation-loader"><div class="inner-loader"></div></div>
                </div>
            </div>
                </div>
                </div>

                    </div>
                    <?
            }
            }
        }
        ?>


        <div class="tab-pane" role="tabpanel" id="TransactionList">
            <div id="ReportTable">

                <?php echo $this->element('clients/client_transaction', array('id' => $client['Client']['id'])); ?>

            </div>

        </div>


         <?php if(ifPluginActive(SalesPlugin)){ ?>
            <div class="tab-pane" role="tabpanel" id="LatestPayments">

            </div>
        <?php } ?>

        <?php if (ifPluginActive(SalesPlugin) && $invoicesCount > 0) { ?>
            <div class="tab-pane" role="tabpanel" id="LatestInvoices">
                <?php if (!empty($invoices)): ?>
                    <h4><?php printf(__('Latest invoices for %s', true), $client['Client']['business_name']) ?></h4>
                    <?php if (IS_PC) { ?>



                        <?php echo $this->element('clients/client_invoice'); ?>
                    <?php } else { ?>
                        <?php
                        $actions = array(
                            $html->link(__('View', true), array('controller' => 'invoices', 'action' => 'view', '%id%'), array('class' => 'View', 'title' => __('View Invoice', true))),
                            $html->link(__('Image', true), array('controller' => 'invoices', 'action' => 'view', '%id%', 'ext' => 'jpeg'), array('class' => 'Image', 'title' => __('View invoice as Image', true))),
                            $html->link(__('PDF', true), array('controller' => 'invoices', 'action' => 'view', '%id%', 'ext' => 'pdf'), array('class' => 'Pdf', 'title' => __('View invoice as PDF', true))),
                        );
                        if (!empty($client['Client']['email'])) {
                            $actions[] = $html->link(__('Email to client', true), array('controller' => 'invoices', 'action' => 'owner_send_to_client', '%id%'), array('class' => 'Send', 'title' => __('Send invoice to its client', true)));
                        }
                        $staff_id = getAuthStaff('id');

                        echo $list->adminResponsiveList($invoices, 'invoice_row', array('staff_id' => $staff_id, 'invoiceStatuses' => $statuses, 'no_client' => true, 'actions' => $actions), array('no_paging' => true));
                        ?>
                    <?php } ?>
                    <?php
                    if (count($invoices) < $invoicesCount) {
                        echo $html->link(sprintf(__('View all %d invoices', true), $invoicesCount), array('controller' => 'invoices', 'action' => 'index', '?' => array('client_id' => $client['Client']['id'])), array('class' => 'view-all barrow', 'target' => '_blank'));
                    }
                    ?>
                <?php else: ?>
                    <div class="Notemessage">
                        <?php __("The client has no invoices yet") ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php } ?>
        <div class="tab-pane" role="tabpanel" id="LatestEstimates">
            <?php if (!empty($estimates)): ?>
                <h4><?php printf(__('Estimates for %s', true), $client['Client']['business_name']) ?></h4>
                <?php if (IS_PC) { ?>
                    <?php echo $this->element('clients/client_estimates'); ?>
                <?php } else { ?>
                    <?php
                    $actions = array(
                        $html->link(__('View', true), array('controller' => 'invoices', 'action' => 'view_estimate', '%id%'), array('class' => 'View', 'title' => __('View Invoice', true))),
                        $html->link(__('PDF', true), array('controller' => 'invoices', 'action' => 'view_estimate', '%id%', 'ext' => 'pdf'), array('class' => 'Pdf', 'title' => __('View invoice as PDF', true))),
                        $html->link(__('Image', true), array('controller' => 'invoices', 'action' => 'view_estimate', '%id%', 'ext' => 'jpeg'), array('class' => 'Image', 'title' => __('View invoice as PDF', true))),
                    );
                    if (!empty($client['Client']['email'])) {
                        $actions[] = $html->link(__('Email to client', true), array('controller' => 'invoices', 'action' => 'owner_send_to_client', '%id%'), array('class' => 'Send', 'title' => __('Send invoice to its client', true)));
                    }
                    $staff_id = getAuthStaff('id');

                    echo $list->adminResponsiveList($estimates, 'invoice_row', array('invoice_type' => 'estimate', 'staff_id' => $staff_id, 'estimateStatuses' => $estimateStatuses, 'no_client' => true, 'actions' => $actions), array('no_paging' => true));
                    ?>
                <?php } ?>
                <?php
                if (count($estimates) < $estimatesCount) {
                    echo $html->link(sprintf(__('View all %d estimates', true), $estimatesCount), array('controller' => 'invoices', 'action' => 'estimates', '?' => array('client_id' => $client['Client']['id'])), array('class' => 'view-all barrow', 'target' => '_blank'));
                }
                ?>
            <?php else: ?>
                <div class="Notemessage">
                    <?php __("The client has no estimates yet") ?>
                </div>
            <?php endif; ?>
            <div class="clearfix"></div>
        </div>
        <?php if ($workOrdersCount > 0) { ?>
            <div class="tab-pane" role="tabpanel" id="LatestWorkOrders">
                <?php if (!empty($workOrders)) { ?>
                    <?php if (IS_PC) {
                        echo $this->element('clients/client_work_order');
                    } else {
                        $actions = array(
                            $html->link(__('View', true), array('controller' => 'work_orders', 'action' => 'view', '%id%'), array('class' => 'View', 'title' => __('View Invoice', true))),
                            $html->link(__('Image', true), array('controller' => 'work_orders', 'action' => 'view', '%id%', 'ext' => 'jpeg'), array('class' => 'Image', 'title' => __('View invoice as Image', true))),
                            $html->link(__('PDF', true), array('controller' => 'work_orders', 'action' => 'view', '%id%', 'ext' => 'pdf'), array('class' => 'Pdf', 'title' => __('View invoice as PDF', true))),
                        );
                        $staff_id = getAuthStaff('id');
                        $work_order_status_map = [WorkOrder::STATUS_OPEN => 2, WorkOrder::STATUS_CANCELLED => 0, WorkOrder::STATUS_CLOSED => 3];
                        echo $list->adminResponsiveList2($workOrders, 'work_orders/work_order', array('actions' => $actions, 'status_map' => $work_order_status_map), array('multi_select' => false));
                    }
                    if (count($workOrders) < $workOrdersCount) {
                        echo $html->link(sprintf(__('View all %d work orders', true), $workOrdersCount), array('controller' => 'work_orders', 'action' => 'index', '?' => array('client_id' => $client['Client']['id'])), array('class' => 'view-all barrow', 'target' => '_blank'));
                    }
                } else { ?>
                    <div class="Notemessage">
                        <?php __("The client has no work orders yet") ?>
                    </div>
                <?php } ?>
            </div>
        <?php } ?>
        <div class="tab-pane active" role="tabpanel" id="ProfileBlock">
            <?php if (!empty($client['Client']['bn1_label']) || !empty($client['Client']['bn1']) || !empty($client['Client']['bn2_label']) || !empty($client['Client']['bn2']) || !empty($client['Client']['timezone']) || $client['Client']['timezone'] != $site['timezone'] || !empty($client['Client']['notes']) || !empty($client['Client']['category'])) { ?>
            <div class="inputs-group-list">
                <div class="row">
                    <?php if ((!empty($client['Client']['bn1_label']) && !empty($client['Client']['bn1'])) || (!empty($client['Client']['bn2_label']) && !empty($client['Client']['bn2'])) || (!empty($client['Client']['timezone']) && $client['Client']['timezone'] != $site['timezone']) || !empty($client['Client']['country_code']) || !is_null($barcode)) { ?>
                    <div class="col-md-6">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="view-table">

                            <?php if (!empty($client['Client']['bn1_label']) and !empty($client['Client']['bn1'])) { ?>
                            <tr>
                                <td>
                                    <strong><?= __($client['Client']['bn1_label']); ?></strong>
                                </td>
                                <td>
                                    <?= $client['Client']['bn1']; ?>
                                </td>
                            </tr>
                            <?php } ?>

                            <?php if (!empty($client['Client']['bn2_label']) and !empty($client['Client']['bn2'])) { ?>
                            <tr>
                                <td>
                                    <strong><?= __($client['Client']['bn2_label']); ?></strong>
                                </td>
                                <td>
                                    <?= $client['Client']['bn2']; ?>
                                </td>
                            </tr>
                            <?php } ?>

                            <?php
                            if (!empty($client['Client']['timezone']) && $client['Client']['timezone'] != $site['timezone']):
                                GetObjectOrLoadModel('Timezone');
                                $newtime2 = Timezone::OwnerZoneToClientZone($site['timezone'], $client['Client']['timezone'], null, "l, H:iA");
                            ?>
                                <tr><td>
                                        <span class="has-tip" data-tip="local_time_<?php echo $client['Client']['id'] ?>">
                                            <i class="fa fa-globe" aria-hidden="true"></i> <?php echo __($newtime2['to'], true) ?>
                                        </span>
                                        <div class="clearfix"></div>
                                        <div  style="display:none;" id="local_time_<?php echo $client['Client']['id'] ?>">
                                            <span class="text-normal">
                                                <?php echo __('Current local time', true) ?>:<strong> <?php echo __(explode(',', $newtime2['result'])[0]) . ', ' . __(explode(',', $newtime2['result'])[1]) . ' ' . __("PM") ?> </strong>
                                        </div>
                                </td></tr>
                            <?php endif; ?>

                            <?php if (!empty($client['Client']['country_code'])) { ?>
                                <tr>
                                    <td width="160">
                                        <strong><?= __('Country Code') ?></strong>
                                    </td>
                                    <td>
                                        <?= $client['Client']['country_code']; ?>
                                    </td>
                                </tr>
                            <?php } ?>
                            <?php if (!is_null($barcode)) { ?>
                                <tr>
                                    <td width="160">
                                        <strong><?= __('Barcode') ?></strong>
                                    </td>
                                    <td><?= $barcode; ?></td>
                                </tr>
                            <?php } ?>
                            <?php if (settings::getValue(ClientsPlugin, 'national_id') && !empty($client['Client']['national_id'])) { ?>
                                <tr>
                                    <td>
                                        <strong><?php __("National Id") ?>: </strong>
                                    </td>
                                    <td><?= $client['Client']['national_id'] ?></td>
                                </tr>
                            <?php } ?>
                            <?php if (settings::getValue(ClientsPlugin, 'national_id') && !empty($client['Client']['birth_date']) && $client['Client']['birth_date'] != '0000-00-00') { ?>
                                <tr>
                                    <td>
                                        <strong><?php __("Birth Date") ?>: </strong>
                                    </td>
                                    <td><?= $client['Client']['birth_date'] ?></td>
                                </tr>
                            <?php } ?>
                            <?php if (settings::getValue(ClientsPlugin, 'link') && !empty($client['Client']['link'])) { ?>
                                <tr>
                                    <td>
                                        <strong><?php __("Link") ?>: </strong>
                                    </td>
                                    <td style="word-break: break-all;"><?= $client['Client']['link'] ?></td>
                                </tr>
                            <?php } ?>
                        </table>
                        </td>
                    </div>
                    <?php }
                    if (!empty($client['Client']['notes']) || !empty($client['Client']['category'])) { ?>
                    <div class="col-md-6">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="view-table">
                            <?php if (!empty($client['Client']['notes'])) {
                                ?>
                                <tr>
                                    <td><strong>
                                            <?php __("Notes") ?>: </strong></td>

									<?php $client_notes = $client['Client']['notes'];
											preg_match('/Latest job title[^\:]*\:([^,]*)/', $client_notes,$m);
											$latest_job_title = $m[1];
											preg_match('/jobs[^\(]*\((https[^\)]*)/',$client_notes,$m);
											$client_jobs_link = $m[1];
											$client_jobs_link = str_replace('admin/','', $client_jobs_link);
											preg_match('/\%company_name([^\%]*)/',$client_notes,$m);
											$company_name = $m[1];
											preg_match('/\%company_url([^\%]*)/',$client_notes,$m);
											$company_url = $m[1];
											debug($company_name);
											if($latest_job_title && $client_jobs_link)
											{
											?>
									<td> <p class="text-info"><?= __('Company',true).': ' ?>  <?php echo $company_url?"<a href='".$company_url."'>".$company_name."</a>" : $company_name ; ?></p><p class="text-info"><?= __('Latest Job Title',true).': '. $latest_job_title ?></p>	<p><a target="_blank" href="<?= $client_jobs_link ?>" class="btn btn-sm btn-info"><?php __('View All Client Jobs'); ?></a></p> </td>
											<?php }else{
											    ?>

                                    <td><?php echo nl2br($client['Client']['notes']) ?></td>
											<?php } ?>
                                </tr>
                            <?php } ?>
                            <?php if (!empty($client['Client']['category'])) { ?>
                                <tr>
                                    <td width="160"><strong>
                                            <?php __("Category") ?>: </strong></td>

                                    <td><?php echo nl2br($client['Client']['category']) ?></td>
                                </tr>
                            <?php } ?>
                            <?php if (settings::getValue(ClientsPlugin, 'gender') && !empty($client['Client']['gender'])) { ?>
                                <tr>
                                    <td>
                                        <strong><?php __("Gender") ?>: </strong>
                                    </td>
                                    <td><?= $client['Client']['gender'] == '1' ? 'Male' : 'Female' ?></td>
                                </tr>
                            <?php } ?>
                        </table>
                    </div>
                    <?php } ?>
                </div>
            </div>
            <?php } ?>
            <?
            if (!empty($client['ClientDetail'])) {
                ?>
                <div class="input-fields client-main-profile">
                    <h3 class="head-bar theme-color-a"><span class="details-info"><?php __("Contacts List") ?></span></h3>
                    <div class="index entry-content">
                        <ul class="day-view-entry-list"><li class="day-view-entry">
                                <?php foreach ($client['ClientDetail'] as $detail) { ?>
                                <li class="day-view-entry">
                                    <table cellspacing="0" cellpadding="0" border="0">
                                        <tbody>
                                            <tr>
                                                <td >
                                                    <div class="invoice-row entry-info">
                                                        <div class="project-client"> <span class="project"><?php echo $detail['first_name'] . ' ' . $detail['last_name'] ?></span></div>
                                                        <div class="task-notes">
                                                            <span class="project">
                                                                <?php if (!empty($detail['email']) || !empty($detail['home_phone']) || !empty($detail['mobile'])) { ?>
                                                                    <ul class="meta-details">
                                                                        <?php if (!empty($detail['email'])) { ?>
                                                                            <li>
                                                                                <div class="added-by">
                                                                                    <span class="added-by-label"><i class="fa fa-envelope-o"></i>  </span>
                                                                                    <span class="added-by-value"><?php echo $detail['email'] ?></span>
                                                                                </div>
                                                                            </li>
                                                                        <?php } ?>
                                                                        <?php if (!empty($detail['home_phone'])) { ?>
                                                                            <li>
                                                                                <div class="added-by">
                                                                                    <span class="added-by-label"><i class="fa fa-phone"></i>  </span>
                                                                                    <span class="added-by-value"><?php echo $detail['home_phone'] ?></span>
                                                                                </div>
                                                                            </li>
                                                                        <?php } ?>
                                                                        <?php if (!empty($detail['mobile'])) { ?>
                                                                            <li>
                                                                                <div class="added-by">
                                                                                    <span class="added-by-label"><i class="fa fa-mobile"></i>  </span>
                                                                                    <span class="added-by-value"><?php echo $detail['mobile'] ?></span>
                                                                                </div>
                                                                            </li>
                                                                        <?php } ?>
                                                                    </ul>
                                                                <?php } ?>

                                                                <div class="clearfix"></div>

                                                            </span>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </li>
                            <?php } ?>
                        </ul>
                    </div>
                <?php } ?>

				<?php echo $this->element ('custom_forms/view_fields_form2') ;?>

                <?php if (check_permission(Invoices_View_Invoices_Details)) { ?>
                    <div class="input-fields mt-6">
                        <h3 class="head-bar theme-color-a"><span class="details-info">
                                <?php __("Quick Information") ?>
                            </span></h3>
                        <div class="row">
                            <div class="col-md-6">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="view-table">
                                    <tr>
                                        <td width="160"><strong>
                                                <?php __("Count of invoices") ?>: </strong></td>

                                        <td><?php if ($issuedCount): ?>
                                                <?php $viewLink = array('controller' => 'invoices', 'action' => 'index', '?' => array('client_id' => $client['Client']['id'])); ?>
                                        <u><?php echo $html->link($issuedCount, $viewLink, array('target' => '_blank', 'title' => sprintf(__('View all %d issued invoices', true), $issuedCount))) ?></u> (<u><?php echo $html->link(__('View', true), $viewLink, array('class' => 'view', 'target' => '_blank')) ?></u>)
                                    <?php else: ?>
                                        <?php __("No issued invoices") ?>
                                            <?php endif; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>
                                                <?php __("Count of due invoices") ?>: </strong></td>
                                        <td><?php if ($dueCount): ?>
                                                <?php $viewLink = array('controller' => 'invoices', 'action' => 'due', '?' => array('client_id' => $client['Client']['id'])); ?>
                                        <u><?php echo $html->link($dueCount, $viewLink, array('target' => '_blank', 'title' => sprintf(__('View all %d due invoices', true), $dueCount))); ?></u> (<u><?php echo $html->link(__('View', true), $viewLink, array('class' => 'view', 'traget' => '_blank')); ?></u>)
                                    <?php else: ?>
                                        <?php __("No due invoices") ?>
                                            <?php endif; ?></td>
                                    </tr>
                                    <?php if (empty($client['Client']['is_offline'])) { ?>
                                        <tr>
                                            <td><strong><?php __("Last login") ?>: </strong></td>
                                            <td><?php
                                                if (!empty($client['Client']['last_login'])) {
                                                    echo format_date($client['Client']['last_login']) . date(' h:i A', strtotime($client['Client']['last_login']));
                                                } else {
                                                    __('No Login');
                                                }
                                                ?></td>
                                        </tr>
                                    <?php } ?>

                                </table>
                            </div>
                            <div class="col-md-6">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="view-table">
                                    <tr>
                                        <td width="160"><strong>
                                                <?php __('Last invoice') ?>: </strong></td>

                                        <td><?php if (!empty($lastInvoice)): ?>
                                                <?php $viewLink = array('controller' => 'invoices', 'action' => 'view', $lastInvoice['Invoice']['id']) ?>
                                        <u><?php echo $html->link('#' . $lastInvoice['Invoice']['no'], $viewLink, array('target' => '_blank', 'title' => sprintf(__('View invoice %s', true), '#' . $lastInvoice['Invoice']['no']))) ?></u> <?php echo  format_price($lastInvoice['Invoice']['summary_total'], $lastInvoice['Invoice']['currency_code']) . ' <small  class="counter">' . $lastInvoice['Invoice']['currency_code'] . '</small>'; ?> (<u><?php echo $html->link(__('View', true), $viewLink, array('class' => 'view', 'target' => '_blank')) ?></u>)
                                    <?php else: ?>
                                        <?php __("No issued invoices") ?>
                                            <?php endif; ?></td>
                                    </tr>

                                    <tr>
                                        <td><strong>
                                                <?php __("Last payment") ?>: </strong></td>

                                        <td><?php if (!empty($lastPayment)): ?>
                                                <?php $viewLink = array('controller' => 'invoice_payments', 'action' => 'view', $lastPayment['InvoicePayment']['id']); ?>
                                        <u><?php echo $html->link('#' . $lastPayment['InvoicePayment']['code'], $viewLink, array('target' => '_blank', 'title' => sprintf(__('View payment %s', true), $lastPayment['InvoicePayment']['code']))) ?></u> (<u><?php echo $html->link(__('View', true), $viewLink, array('class' => 'view', 'target' => '_blank')); ?></u>)
                                    <?php else: ?>
                                        <?php __("No payments have been made") ?>
                                            <?php endif; ?></td>
                                    </tr>
                                    <?php if (empty($client['Client']['is_offline']) || $emailCount > 0) { ?>
                                        <tr>
                                            <td><strong>
                                                    <?php __("Latest email notification") ?>
                                                    :</strong></td>
                                            <td><?php
                                                if (!empty($lastEmail['EmailLog']['sent_date'])) {
                                                    echo format_date($lastEmail['EmailLog']['sent_date']) . date(' h:i A', strtotime($lastEmail['EmailLog']['sent_date']));
                                                } else {
                                                    __('No notifications have been sent');
                                                }
                                                ?></td>
                                        </tr>
                                    <?php } ?>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <?php if(isset($client['Attachments']) && count($client['Attachments']) > 0) {?>
                    <div class="input-fields mt-8">
                        <h3 class="head-bar theme-color-a"><span class="details-info">
                            <?php __("Attachments") ?>
                        </span></h3>
                        <?php echo $this->element('multiple_files_preview', ['record' => $client]) ?>
                    </div>
                <?php } ?>

                <br />
                <br />
                <?php if (isset($summaries[0][0]['total']) && (check_permission(Invoices_View_All_Invoices) || check_permission(Invoices_View_Invoices_Details))): ?>
                    <div class="input-fields report">
                        <h3 class="head-bar theme-color-a"><span class="details-info">
                                <?php __("Account Summary") ?>
                            </span></h3>
                        <div class="table-responsive">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class=" ">
                                <tr class="report-results-head">
                                    <th><?php __('Currency') ?></th>
                                    <th><?php __('Total') ?></th>
                                    <th><?php __('Refunded') ?></th>
                                    <th><?php __('Paid to date') ?></th>
                                    <th><?php __('Credit Notes') ?></th>
                                    <th><?php __('Balance Due') ?></th>

<!--                                    <th><?php __('Unpaid') ?></th>
                                    <th><?php __('Action') ?></th>-->
                                </tr>
                                <?php
                                foreach ($summaries as $summary):

                                    ?>
                                    <tr>
                                        <td><?php $currency = $summary['I']['currency_code']; echo $summary['I']['currency_code']; ?></td>
                                        <td><?php echo format_price($totalAmounts[$currency], $currency); ?></td>
                                        <td><?php echo format_price($refunds[$currency], $currency); ?></td>
                                        <td><?php echo format_price($paidAmounts[$currency]*-1, $currency); ?></td>
                                        <td><?php echo format_price($creditNotes[$currency]*-1, $currency); ?></td>
                                        <td><?php echo format_price($clientCredits[$currency], $currency); ?></td>

                                    </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                    </div>
					 <br />
                <br />
                <?php endif; ?>
                    <?php echo $this->element('custom_forms/le_form_show'); ?>
                </div>
        </div>
    
    <div class="modal fade" id="edit_staff_box" tabindex="-1" role="dialog" aria-labelledby="edit_staff_box">
        <div class="modal-dialog"  role="document">
            <div class="modal-content">
                <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
                <div class="modal-header">
                    <h3 id="edit_staff_box_title"><?php echo __('Edit Post', true) ?></h3>
                </div>
                <div class="modal-body">
                    <iframe id="edit_staff_box_iframe" width="100%" height="600" src="" frameborder="0" allowfullscreen></iframe>
                </div>

            </div>
        </div>
    </div>
</div>

    <?php
    echo $javascript->link('jquery.lightbox_me');
    $this->set('hasTabs', true);
    ?>

<?php echo $html->css(array('owner-page', 'tabs', 'reports_v2'), false, false, false) ?>
    <!-- <script type="text/javascript">
        $(function() {
<?php if (!IS_PC) { ?>
                $('.invoice-actions').html('<button type="button" style="padding:3px 18px;" class="converted-actions btn btn-lg btn-primary dropdown-toggle pull-right" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fa fa-chevron-down"></i></button><div class="clearfix"></div><ul class="dropdown-menu dropdown-menu-right">' + $('.invoice-actions').html().replace(/<a/g, '<li><a').replace(/<\/a>/g, '</a></li>') + '</ul>')
<?php } ?>
        });
    </script> -->

    <?php
    if (ifPluginActive(FollowupPlugin) && ifPluginActive(StaffPlugin) && (!ifPluginActive(PosPlugin) || settings::getValue(PosPlugin, 'pos_default_client') != $client['Client']['id'])) {
        if (check_permission(Assign_Clients_To_Staff)) {
            ?>
            <div  class="modal fade" id="assign_staff_box" tabindex="-1" role="dialog" aria-labelledby="assign_staff_box">
                <div class="modal-dialog"  role="document">
                    <div class="modal-content">
                        <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
                        <div class="modal-header">
                            <h3 id="assign_staff_box_title"><?php echo __('Assign Client to Staff', true) ?></h3>
                        </div>
                        <div class="modal-body">
                            <iframe id="assign_staff_box_iframe" width="100%" height="250" src="" frameborder="0" allowfullscreen></iframe>
                        </div>

                    </div>
                </div>
            </div>
        <?
        }
    }
    ?>
    <script>
        $(document).ready(function() {
//			console.log($('#client-additional-data').text() + "111");
			if($('.client-profile').text().trim() == ""){
				$('.client-profile').hide();
			}
            $(".reload_status").click ( function () {
                $(".reload_status").removeClass ( 'active')
                $(this).addClass ( 'active')
                reload_appointments ( $(this).attr('status'))
                update_appointment_counts();
            })

            function update_appointment_counts() {
                fetch('/owner/clients/get_appointment_statuses_count/<?= $client['Client']['id'] ?>')
                .then((res) => res.json())
                .then((res) =>  {
                    for (let status in res) {
                        let count = res[status];
                        $('.reload_status_count_' + status).html(`(${count})`)
                    }
                });
            }
            $("#statuses").change ( function () {
            reload_appointments ($(this).val() ) ;
        })
            $(document).on ('click', '[data-target="#assign_staff_box"]', function (e) {
                e.preventDefault();
                $('#assign_staff_box_iframe').attr('src', '<?php echo Router::url(array('action' => 'assign_staff', $client['Client']['id'])) ?>?box=1');
                $("#assign_staff_box").modal () ;
                $('[data-target="#assign_staff_box"]').closest(".btn-group").removeClass('open');
                return false ;

            })
            $('.has-tip').each(function() {
                console.log(this);
                $(this).qtip({content: $('#' + $(this).data('tip')).html(), style: {
                        classes: 'qtip-bootstrap'
                    }, position: {my: 'top center', at: 'bottom center'}});
            });
            $('body').on('click', '.appointments', function() {
                $("#AppointmentsBlock_rel").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');


                $.ajax({url: $(this).attr('href'), cache: false, success: function(result) {

                        reload_appointments($('#statuses').val());
                        update_appointment_counts()


                    }});
                return false;
            });

            if (location.hash !== '') {
                $('a[href="' + location.hash + '"]').tab('show');
                $('a[aria-controls="' + location.hash.replace('#', '') + '"]').click();
            }
            $('body').on('click', '.delete_xpost', function() {
                $('#edit_staff_box_title').html('<?php echo __('Delete Note / Attachment', true) ?>');

                $('#edit_staff_box').modal();
                $('#edit_staff_box_iframe').attr('src', 'about:blank');
                $('#edit_staff_box_iframe').attr('src', $(this).attr('href') + '?box=1');

                return  false;
            });

            $('body').on('click', '.edit_xpost', function() {
                $('#edit_staff_box_title').html('<?php echo __('Edit Note / Attachment', true) ?>');

                $('#edit_staff_box').modal();
                $('#edit_staff_box_iframe').attr('src', $(this).attr('href') + '?box=1');

                return  false;
            });
            $('body').on('click', '.add_xpost', function() {
                $('#edit_staff_box_title').html('<?php echo __('Add Note / Attachment', true) ?>');
                $('#edit_staff_box').modal();
                $('#edit_staff_box_iframe').attr('src', $(this).attr('href') + '?box=1');
                return  false;
            });

            $('body').on('click', '[data-target="#assign_staff_box"]', function() {

                $('#assign_staff_box_iframe').attr('src', '<?php echo Router::url(array('action' => 'assign_staff', $client['Client']['id'])) ?>?box=1');
            });
        });

 function reload_payments(note_page) {


         $("#LatestPayments").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');

       $.ajax({url: '/owner/invoice_payments/index?client_id=<?php echo $client['Client']['id'] ?> ',cache:false, success: function(result) {

                $("#LatestPayments").html(result);

            }});

     }

        function reload_bookings() {
            $("#BookingsBlock").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');
            $.ajax({url: '/owner/bookings/index?client_id=<?php echo $client['Client']['id'] ?> ',cache:false, success: function(result) {
                $("#BookingsBlock").html(result);
            }});
        }

        function reload_credit_summary() {
            document.getElementById('CreditSummaryIframe').src = '/v2/owner/clients/<?php echo $client['Client']['id'] ?>/credit_summary?iframe=1';
				}
        function reload_membership() {
            document.getElementById('MembershipIframe').src = '/v2/owner/clients/<?php echo $client['Client']['id'] ?>/membership?iframe=1';
        }
        function reload_client_attendance() {
            document.getElementById('ClientAttendanceLogBlockIframe').src = '/v2/owner/client_attendance_logs?client=<?php echo $client['Client']['id'] ?>&iframe=1';
        }
			</script>
		</div>

			<!-- end photo section  -->
				<?php echo $javascript->link(array('layout_header_adjust')) ; ?>
		<?php echo $javascript->link(array('tag_ajax_input')) ; ?>

<?php if (isset($_GET['box']) && isset($_GET['from_pos'])) { ?>
<style>
.tabs-box > .nav-tabs,
.invoice-actions {
    display: none;
}

.pages-head h1,
.top-actions,
.B_clients_pay {
    display: inline-block;
    vertical-align: middle;
}

.B_clients_pay {
    margin-top: 13px;
}

.tab-content {
    padding: 0;
    border: 0;
}

#client-additional-data {
    margin: 0 5px;
}

.container {
    width: auto;
    padding: 0 15px
}

.children-entities-btn-wrapper,
.statuses-btn-wrapper {
    display: none !important;
}

.tab-content .head-bar {
    margin-bottom: 15px;
}

.B_clients_pay {
    margin-bottom: 15px;
}
</style>
<?php } ?>
<style>
.btn-group .multiselect-container{
    width: auto;
}
@media (max-width: 768px) {
    .text-center-mobile {
        text-align: center;
    }
}
@media print {
    .report-table-dashed .report-table-dashed--head th {
        -webkit-print-color-adjust:exact !important;
        print-color-adjust:exact !important;
        color: #fff !important;
        background-color: #373B50 !important;
    }
}
</style>
<script>
$('[data-copy-btn]').click(function() {
    var text = $(this).data('copy-btn');
    if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text);
    }
    if (typeof initToast != 'undefined') {
        initToast(__('Copied'), 'success');
    } else {
        alert(__('Copied'));
    }
    
});
</script>
<div class="modal fade" id="myModalTax" tabindex="-1" role="dialog" aria-labelledby="myModalTaxLabel">
                            <div class="modal-dialog modal-md"  role="document">
                                <div class="modal-content">
                                    <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
                                    <div class="modal-body table-responsive"  id="myModalTaxBody"></div>

                                </div>
                            </div>
                        </div>