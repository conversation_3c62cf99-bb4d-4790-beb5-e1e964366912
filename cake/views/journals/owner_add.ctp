<link rel="stylesheet" type="text/css" href="/css/invoicing.css" />
<?php

use Izam\Aws\Aws;
use Izam\Daftra\Common\Utils\SettingsUtil;

 echo $html->css(CDN_ASSETS_URL ."s2020/css/create/create.min.css?v=".CSS_VERSION, null, []); ?>
<?php echo $html->css(array('pages-head-buttons-s2022.css?v=2'), false, ['inline' => false], false); ?>
<script src="<?= CDN_ASSETS_URL ?>js/forms/plugin_select2.js"></script>
<link rel="stylesheet" href="<?php echo CDN_ASSETS_URL ?>css/forms/plugin_select2.min.css?v=<? echo CSS_VERSION ?>" />
<link rel="stylesheet" href="<?php echo CDN_ASSETS_URL ?>css/forms/plugin_select2_rtl.min.css?v=<? echo CSS_VERSION ?>" />
<?php echo $html->css("table/table-responsive.css?v=".CSS_VERSION, null, []); ?>
<script>
    var oldTags = [];
    var tagsSelect2Options = {
        theme: 'bootstrap4',
        width: '100%',
        tags: true,
        placeholder: function () {
            return $(this).data('placeholder');
        },
        ajax: {
            url: '<?php echo Router::url(array('controller' => 'tags', 'action' => 'get_tags')) ?>',
            method: 'get',
            dataType: 'json',
            data: function(params) {
                return {
                    q: params.term,
                }
            },
            delay: 400,
            processResults: function(data) {
                var items = oldTags = [];
                if (data && data.tags && data.tags.length) {
                    data.tags.forEach(function(tag) {
                        items.push({
                            id: tag,
                            text: tag,
                        })
                    })
                }
                items = items.concat(oldTags);
                return {
                    results: items
                };
            }
        }
    };
    if ($('html').is(':lang(ara)')) {
        tagsSelect2Options['language'] = "ar";
    }
</script>
<style>
    .item-wrap .select2-container,
    .item-wrap .select2-container .selection,
    .item-wrap .select2-container .select2-selection {
        background: none;
        height: 100%;
        border: 0;
    }
    .item-wrap .select2-container .select2-selection .select2-selection__rendered {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        height: 100%;
        padding: 0 8px !important;
        flex-direction: row;
    }
    .item-wrap .select2-container .select2-selection .select2-search__field {
        background: none;
    }

    <?php if ($enable_tags || !$display_cost_centers_in_journals){?>
    @media (min-width: 1500px) {
        .container {
            width: 1300px;
        }
    }

    .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {
        max-width: unset;
    }

    .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
        /*max-width: 0;*/
        /*overflow: hidden;*/
        visibility: hidden;
    }

    .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice:hover .select2-selection__choice__remove {
        /*max-width: unset;*/
        visibility: visible;
    }
    .item-tags .item-wrap{
        display: flex;
    }
    .item-tags .input.select{
        padding-bottom: 0;
        flex: 1;
    }

    <?php } ?>
</style>
<?php
$initial_cost_centers = ClassRegistry::init('CostCenter');
$conditions = [
    'OR' => ['CostCenter.is_primary' => ['', 0]],
    'cost_center_id IN (SELECT id FROM cost_centers) OR (cost_center_id = 0 AND is_primary = 0)'
];
$initial_cost_centers = $initial_cost_centers->find('list', ['conditions' => $conditions,'limit' => 10]);
?>
<style>
    .journals-table {
        --color-primary: #00b0ef;
    }

    .journals-table tbody > .TableHeader > th {
        padding-top: 18px;
        padding-bottom: 18px;
    }

    .journals-table .item-name-td {
        width: 250px;
        min-width: 250px !important;
        max-width: 250px;
    }

    .journals-table .item-taxes-td {
        width: 150px;
        min-width: 150px !important;
        max-width: 150px;
    }

    .journals-table tbody>tr>td.td_editable>textarea.item-description {
        width: 100% !important;
    }

    .journals-table .journals-table-row .selectize-control {
        height: 100%;
    }

    .journals-table .journals-table-row .selectize-input {
        height: 100%;
        margin: 0;
        display: inline-flex !important;
        align-items: center;
        width: 100%;
        background: transparent !important;
        border: 0;
        border-radius: 0;
    }

    .journals-table .journals-table-row .selectize-dropdown,
    .journals-table .journals-table-row .selectize-dropdown .ui-btn {
        border-radius: 0;
    }

    .journals-table .journals-table-row .selectize-input:hover,
    .journals-table .journals-table-row .selectize-control.single .selectize-input.input-active,
    .journals-table .journals-table-row .selectize-control.single .selectize-input.focus {
        box-shadow: 0 0 0 1px #00b0ef;
        outline: 0;
    }

    .journals-table .journals-table-row>td:not(.td_editable).move-table {
        background: #f6f9fc !important;
        border-bottom: 1px solid #e9ecef !important;
    }


    .journals-table .journals-table-row>td:not(.td_editable).move-table,
    .journals-table .journals-table-row>td:not(.td_editable).move-table .sortable-arrows {
        padding: 0;
    }

    .journals-table .journals-table-row>td:not(.td_editable).move-table .sortable-arrows {
        display: flex;
        flex-direction: row;
        width: 100%;
        height: 100%;
        text-align: center;
        min-width: 100%;
        flex: 100%;
        flex-shrink: 0;
        flex-grow: 1;
        flex-wrap: wrap;
    }

    .journals-table .journals-table-row>td:not(.td_editable).move-table .sortable-arrows a.move-up:not(.move-up-notactive),
    .journals-table .journals-table-row>td:not(.td_editable).move-table .sortable-arrows a.move-down:not(.move-down-notactive) {
        width: 100%;
        flex-grow: 1;
        height: auto;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .journals-table .journals-table-row>td:not(.td_editable).move-table .sortable-arrows a.move-up:not(.move-up-notactive):hover,
    .journals-table .journals-table-row>td:not(.td_editable).move-table .sortable-arrows a.move-down:not(.move-down-notactive):hover {
        box-shadow: 0 0 0 1px #28a745 inset;
    }

    .journals-table .journals-table-row:hover>td:not(.td_editable).move-table {
        background: #f1f5fa !important;
    }

    .journals-table .journals-table-row>td:not(.td_editable).move-table .sortable-arrows a {
        color: #b5b7c9;
    }

    .journals-table .journals-table-row:hover>td:not(.td_editable).move-table .sortable-arrows a {
        color: #28a745;
    }

    .journal-cost-center-table-wrapper {
        /* border-bottom: 15px solid #e4ebf2; */
        background: #e4ebf2;
    }

    .journal-cost-center-table {
        background: #e4ebf2 !important;
    }

    .journal-cost-center-table >tbody>tr>td.td_editable {
        background: #fff;
    }

    .journal-cost-center-table .table-tr-totals-s2020 h6 {
        font-size: 12px;
        margin: 12px 0;
        position: relative;
        top: 1px;
    }

    .journal-cost-center-table .table-tr-totals-s2020 .item-percentage,
    .journal-cost-center-table .table-tr-totals-s2020 .item-amount {
        padding: 0 10px !important;
    }

    .journal-cost-center-table th {
        font-size: 12px;
        line-height: 1 !important;
        vertical-align: middle !important;
        min-height: unset;
    }

    .journal-cost-center-table tbody>tr>td.td_editable .item-name .bootstrap-select .btn.dropdown-toggle.selectpicker,
    .journal-cost-center-table tbody>tr>td.td_editable .item-wrap .bootstrap-select .btn.dropdown-toggle.selectpicker {
        font-size: 12px;
        line-height: 1 !important;
        vertical-align: middle !important;
        min-height: unset;
        min-height: 40px !important;
    }

    .journal-cost-center-table tbody>tr>td.delete-product-cell,
    .journal-cost-center-table tbody>tr>td.delete-product-cell .btn-s2020 {
        min-height: 40px !important;
        font-size: 14px !important;
    }

    @media (max-width: 768px) {
        .unit-price, .item-subtotal {
            float: none !important;
        }
        .unit-price {
            min-width: 150px;
        }
    }

    .table>tbody>tr>td.td_editable .item-name .bootstrap-select .btn.dropdown-toggle.selectpicker,
    .table>tbody>tr>td.td_editable .item-wrap .bootstrap-select .btn.dropdown-toggle.selectpicker {
        min-height: 52px;
        padding-right: 30px;
        padding-left: 12px;
    }
    .rtl .table>tbody>tr>td.td_editable .item-wrap .bootstrap-select .btn.dropdown-toggle.selectpicker {
        padding-left: 30px;
        padding-right: 12px;
    }
    .add-row-btn-wrapper{
        min-width: 166px;
    }
    .add-row-btn-wrapper #AddItem {
        margin-top: 0;
    }
    @media ( max-width: 768px ) {
        .journals-table > tbody > .TableHeader > .count-cell,
        .journals-table-row > td > table > tbody > tr > td.td_editable:nth-child(2),
        .journals-table-row > td > table > tbody > tr > td.td_editable:nth-child(4),
        .journals-table-row > td > table > tbody > tr > td.td_editable:nth-child(5),
        .journals-table-row > td > table > tbody > tr > td.td_editable:nth-child(6) {
            width: 200px;
            min-width: 200px;
        }
        .journals-table > tbody > .TableHeader > .detail-cell,
        .journals-table-row > td > table > tbody > tr > td.td_editable:nth-child(3) {
            width: 300px;
            min-width: 300px;
        }
        .table-totals-s2020 > tbody > tr > td:nth-child(1) {
            width: 630px;
            min-width: 630px;
        }

        .table-totals-s2020 > tbody > tr > td:nth-child(2),
        .table-totals-s2020 > tbody > tr > td:nth-child(3),
        .table-totals-s2020 > tbody > tr > td:nth-child(4) {
            width: 200px;
            min-width: 200px;
        }

        .table-totals-s2020 > tbody > tr > td:nth-child(5) {
            width: 45px;
            min-width: 45px;
        }
        /* .add-row-btn-wrapper #AddItemCart {
            height: 33px !important;
        } */
        #AddCostItem {
            min-width: 140px;
            margin: 0 10px;
        }
    }

    .assign-cost-center-btn:hover {
        background-color: #008cbe;
    }
    #chead, #dhead{
        white-space: nowrap;
    }
</style>
	<?php



$dateFormats = getDateFormats('std');
debug($this->data);
$this->data['Journal']['date'] = empty(format_date($this->data['Journal']['date']))? $this->data['Journal']['date'] : format_date($this->data['Journal']['date']);
debug($this->data);
$useNewEl = false;
if(count((array)$this->data['JournalTransaction']) > 20) {
    $useNewEl = true;
}
 echo $html->css(array('quote-request.css', 'magicsuggest-min.css', 'attachments.css'), false, ['inline' => false]);

echo $form->create('Journal', ['target' => '_self']); ?>

<style>
				input::-webkit-input-placeholder { color:#CCCCCC; }
				input:focus:-moz-placeholder { color:transparent; } /* Firefox 18- */
				input:focus::-moz-placeholder { color:transparent; } /* Firefox 19+ */
				input:focus:-ms-input-placeholder { color:transparent; } /* oldIE 😉 */
				.ui-menu .ui-menu-item a{
					color: #96f226;
					border-radius: 0px;
					border: 1px solid #454545;
				}
				.description_journal{
					background: #fff;
					padding: 15px 15px 5px 15px;
					border: 1px solid #ddd;
				}
				.td_editable .selectpicker {
					border-width: 0px;
				}
				/* input.editable-input{
					width: 150px !important;
				} */
				.journal_add .count-cell:first-child{
					min-width: 150px;
				}

                #cost-centers-tables .item-name span.input-group-addon {
                    width: 1%;
                    display: table-cell
                }
                #cost-centers-tables {
                    padding: 20px 0
                }
                #cost-centers-tables > div {
                    position: relative;
                    margin: 20px 0;
                }
                /* a.assign-cost-center {
                    margin: 0 2px
                } */
                #cost-centers-tables > div .cost-table-title {
                    position: absolute;
                    top: -20px;
                }
                #cost-centers-tables .bootstrap-select.form-control {
                    margin-bottom: 0;
                    padding: 0;
                    border: none;
                }
                .currency-container{
                    display: flex;
                    justify-content: space-between;
                }
                .system-rate-container{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                }
			</style>

			<?php
			$disabled = $this->data['Journal']['is_automatic'] == 1 ? true : FALSE ;

//			$journal_col_span = $disabled ? 'col-md-4' : 'col-md-6' ;
			$transaction_col_span = '' ;
			?>
<div class="invoice-options col-md-6  m-b-md">
	<div class="invoice-fields">
		<table width="100%" border="0" cellspacing="0" cellpadding="0" class="table table-bordered b-light" id="InvoiceCustomFields">
			<?php
                echo $form->hidden('id');
                if (isset($redirectType)) {
                    echo $form->hidden('redirect_type', ['value' => $redirectType]);
                }
                // handle workOrder
                if(isset($_GET['entity_type']))
                {
                    echo $form->input('entity_type', array('value' => $_GET['entity_type'],'type' => 'hidden'));
                }

                if(isset($_GET['entity_id']))
                {
                    echo $form->input('entity_id', array('value' => $_GET['entity_id'],'type' => 'hidden'));
                }
            ?>

			<tr class="unmovable">
				<td  class="bg-white s2020 td_editable px-3 py-2 text-right"><strong><?php echo $form->label('date', __("Date", true)) ?></strong></td>
				<td class="bg-white s2020 td_editable px-3 py-2 text-left">
                    <?php
                    if(!$disabled)
                    {
                        echo $this->element('datePicker',['options' => ['name' => 'date']]);
                    }else{
                        echo $this->element('datePicker',['options' => ['disabled' => true,'name' => 'date']]);
//                        echo $form->input('date', ['div' => ' text','class' => 'INPUT form-control','label' => false,'type' => 'text','disabled' => 'disabled', 'value' => $this->data['Journal']['date']]);
                    }
                    ?>
				</td>
			</tr>

            <tr class="unmovable">
				<td  class="bg-white s2020 td_editable px-3 py-2 text-right"><strong><?php echo $form->label('currency', __("Currency", true)) ?></strong></td>
				<td class="bg-white s2020 px-3 py-2 text-left">
                    <div class="currency-container">
                        <?php
                        $updateJournalEntriesCurrencyRatesSetting = settings::getValue(AccountingPlugin, SettingsUtil::UPDATE_JOURNAL_ENTRIES_CURRENCY_RATES);
                        $chosenCurrencyCode = is_array($this->data['JournalTransaction']) ? $this->data['Journal']['currency_code'] : null;
                        $siteCurrency = getCurrentSite('currency_code');
                        $manualCurrencyRateVisible = $updateJournalEntriesCurrencyRatesSetting && isset($chosenCurrencyCode) && $chosenCurrencyCode != $siteCurrency;

                        if(!$disabled){
                            echo $form->input('currency_code', array('class' => 'form-x2  required currency_code_picker','data-live-search'=>'true', 'div' => 'text','options' => $currencies ,'value'=> $chosenCurrencyCode ?? $siteCurrency, 'label' => false));
                        }else{
                            echo $form->hidden('currency_code', array('class' => 'form-x2  required currency_code_picker','data-live-search'=>'true', 'div' => 'text','options' => $currencies ,'value'=> $chosenCurrencyCode ?? $siteCurrency, 'label' => false));
                            echo $form->input('currency_code2', array('disabled' => $disabled,'class' => 'form-x2  required currency_code_picker','data-live-search'=>'true', 'div' => 'text','options' => $currencies ,'value'=> $chosenCurrencyCode ?? $siteCurrency, 'label' => false));

                        }
						?>
                        <?php if($updateJournalEntriesCurrencyRatesSetting): ?>
                            <div id="system_rate_div" class="system-rate-container" <?= $manualCurrencyRateVisible ? '' : 'style="display:none;"' ?>>
                                <div class="d-flex align-items-center">
                                    <span class="mr-1 flex-shrink-0">1 <span id="selected_currency_span"><?= $chosenCurrencyCode ?></span> = </span>
                                    <label class="d-flex form-control align-items-center font-weight-regular mb-0">
                                        <?php
                                            $manualCurrencyRateValue = !empty($this->data['Journal']['manual_currency_rate']) ? 
                                                $this->data['Journal']['manual_currency_rate']
                                                : (!empty($this->data['Journal']['currency_rate']) ? $this->data['Journal']['currency_rate'] : '');
                                        ?>
                                        <input <?= $manualCurrencyRateVisible ? '' : 'disabled="disabled"' ?> class="bg-transparent border-0" name="data[Journal][manual_currency_rate]" type="text" maxlength="255" value="<?= $manualCurrencyRateValue ?>" style="width: 100px;">
                                        <span><?= $siteCurrency ?></span>
                                    </label>
                                </div>
                                <div style="display: flex; align-items:center;">
                                <label class="l-radio-label ui-radio-label  mb-0 pt-1">
                                    <input <?= $manualCurrencyRateVisible ? '' : 'disabled="disabled"' ?> name="data[update_system_rate]" class="ui-switch m-0 ui-switch--size-40 ui-switch--active-color-active" type="checkbox" style="flex-shrink: 0"  value="1">
                                    <span class="ui-radio-label-content"><?= __('Update System Rate', true) ?></span>
                                </label>
                                <span class="tooltip" title="journal-update-system-rate-option"></span>
                                </div>
                            </div> 
                            <script>
                                function getSmallerDateString(firstDateString, secondDateString) {
                                    let firstDate = firstDateString instanceof Date ? firstDateString : new Date(firstDateString);
                                    let secondDate = secondDateString instanceof Date ? secondDateString : new Date(secondDateString);
                                    firstDateString = firstDate.getFullYear() + "-" + firstDate.getMonth() + "-" + firstDate.getDate();
                                    secondDateString = secondDate.getFullYear() + "-" + secondDate.getMonth() + "-" + secondDate.getDate();
                                    return firstDateString > secondDateString ? secondDateString : firstDateString;
                                }
                                let systemCurrencyCode = '<?= $siteCurrency ?>';
                                let $currencyCodeInput = $('[name="data[Journal][currency_code]"]')
                                $currencyCodeInput.on('change', function(value){
                                    let $systemRateDiv = $('#system_rate_div')
                                    let $manualCurrencyRate = $('[name="data[Journal][manual_currency_rate]"]')
                                    let $updateSystemRateSwitch = $('[name="data[update_system_rate]"]')
                                    let selectedCurrencyValue = this.value
                                    let $dateInput = $('[name="data[Journal][date]"]')
                                    let dateValue = $dateInput.val()
                                    let $selectedCurrencySpan = $('#selected_currency_span')
                                    $selectedCurrencySpan.text(selectedCurrencyValue)
                                    if(selectedCurrencyValue == systemCurrencyCode){
                                        $systemRateDiv.hide()
                                        $manualCurrencyRate.attr('disabled','disabled')
                                        $updateSystemRateSwitch.attr('disabled','disabled')
                                    }else{
                                        let nowDate = new Date();
                                        dateValue = getSmallerDateString(dateValue, nowDate)
                                        $.ajax({
                                            url: '/v2/api/convert_currency',
                                            data: {
                                                'from': selectedCurrencyValue,
                                                // 'to': systemCurrencyCode,
                                                'date': dateValue
                                            },
                                            dataType: 'json',
                                            success: function (data) {
                                                $systemRateDiv.show();
                                                $manualCurrencyRate.removeAttr('disabled')
                                                $updateSystemRateSwitch.removeAttr('disabled')
                                                $manualCurrencyRate.val(data.rate)
                                            }
                                        })
                                    }
                                })
                            </script>
                        <?php endif; ?>
                    </div>
				</td>
			</tr>
			<tr class="unmovable">
				<td  class="bg-white s2020 td_editable px-3 py-2 text-right"><strong><?php echo $form->label('number', __("Number", true)) ?></strong></td>
				<td class="bg-white s2020 td_editable px-3 py-2 text-left">
					<?php

					    echo $form->input('number', array('class' => 'form-x2','div'=> 'input text pb-0','label' => false));
					    echo $form->hidden('hidden_number', array('class' => 'form-x2','label' => false));

						?>
				</td>
			</tr>

		</table>

		<!-- Single button -->


	</div>
</div>
<div <?= $disabled ? "style='display:none'" :"" ?> class="invoice-options col-md-6  m-b-md">
	<div class="description_journal">
		<div class="form-group">
		    <?php echo  $form->label('Description', __("Description", true)) ?>
            <?php
				if(!$disabled){
                echo $form->textarea('description', array('class' => 'form-control journalDescription','rows'=>'5', 'div' => 'text',  'type' => 'textarea', 'rows' => 2, 'label' => false));
				}else{

					  echo $form->hidden('description', array('class' => 'form-control','rows'=>'5', 'div' => 'text',  'type' => 'textarea', 'rows' => 2, 'label' => false));
//				  echo $form->textarea('description2', array('value'=>$this->data['Journal']['description'],'disabled' => $disabled,'class' => 'form-control','rows'=>'5', 'div' => 'text',  'type' => 'textarea', 'rows' => 3, 'label' => false));
				}
            ?>
		</div>
        <div class="form-group">
                <?php

                    if (!empty($this->data['File']['id'])) {
                        $this->data['Attachments'][] = $this->data['File'];
                    }

                    if (!$disabled) {
                        echo $this->element('new_s3_file_uploader', ['recordAttachments' => $this->data['Attachments'], 'name' => 'data[Journal][attachment]', 'entityKey' => 'journal', 'fieldKey' => 'journals.attachments', 'options' => ['multiple' => true, 'sortable' => true, 'mimes' => []]]);
                    }
                ?>
	    </div>
    </div>
</div>
			<?php if($disabled){ ?>
<div class="invoice-options col-md-6  m-b-md ">
	<div class="description_journal">
		<div class="form-group">
		    <?php echo $form->label('Alter Description', __("Description", true)) ?>

            <?php
			if(!empty($this->data['Journal']['alter_description']))
							$description = $this->data['Journal']['alter_description'];
						else
							$description = $this->data['Journal']['description'];
                echo $form->textarea('alter_description', array('class' => 'form-control journalDescription','value' => $description,'rows'=>'5', 'div' => 'text',  'type' => 'textarea', 'rows' => 2, 'label' => FALSE));
            ?>

		</div>
        <div class="form-group">
            <div>
                <?php
                    echo $this->element('new_s3_file_uploader', ['recordAttachments' => $this->data['Attachments'], 'name' => 'data[Journal][attachment]', 'entityKey' => 'journal', 'fieldKey' => 'journals.attachments', 'options' => ['multiple' => true, 'sortable' => true, 'mimes' => []]]);
                ?>
            </div>
        </div>
	</div>
</div>
			<?php } ?>
<div class="col-lg-12">
    <div id="table-loading" class="is-loading" style="height: 250px;"></div>
</div>
<div class="prescription-items m-b-lg journal_add d-none">
	<div class=" table-responsive invoice-items">
		<table width="100%" border="0" cellspacing="0" cellpadding="0" class="table table-bordered b-light items-listing mobile-invoicing journals-table" id="listing_table">
			<tr class="TableHeader table-header active unmovable">
<!--				<th>#</th>-->
                <?php if ($useNewCostCenterMode) { ?>
                    <th class="sort-cell">&nbsp;</th>
                    <th id="label_unit_price" class="item-name-td count-cell"><span><?php __("Account Name") ?><input type="hidden" id="transactionsCount"/><span class="required"> *</span></span></th>
                    <th width="35%" id="label_unit_price" class="detail-cell"><span><?php __("Description") ?></span></th>
                    <?php if ($enable_tags){ ?>
                        <th class="item-taxes-td"><span><?php __("Tags") ?></span></th>
                    <?php } ?>
                    <?php if($display_cost_centers_in_journals || $assigned_cost_centers_list) { ?>
                    <th width="10%" id="cchead"  class="count-cell " style="min-width: 200px;"><span id="label_cost_center"><?php __("Cost Center") ?></span></th>
                    <?php } ?>
                <?php if($display_taxes) { ?>
                        <th class="item-taxes-td" id="taxes-head"  class="count-cell "><?php __("Tax") ?></th>
                    <?php } ?>

                <th width="10%" id="dhead"  class="count-cell "><?php __("Debit") ?><span class="required"> *</span></th>
                    <th width="10%" id="chead"  class="count-cell "><span id="label_description"><?php __("Credit") ?><span class="required"> *</span></span></th>
                    <?php if(!$disabled){ ?>
                    <th width="90" style="max-width: 47px;" class="sort-cell"><div style="width: 35px;height: 1px;"></div></div></th>
                    <?php } ?>
				<?php } else { ?>
                    <th id="label_unit_price" class="count-cell"><span><?php __("Account Name") ?><input type="hidden" id="transactionsCount"/><span class="required"> *</span></span></th>
                    <th id="label_unit_price" class="detail-cell"><span><?php __("Description") ?></span></th>
                    <?php if ($enable_tags){ ?>
                        <th id="label_unit_price" class="detail-cell"><span><?php __("Tags") ?></span></th>
                    <?php } ?>
                    <th id="dhead"  class="count-cell "><?php __("Debit") ?><span class="required"> *</span></th>
                    <?php if($display_taxes) { ?>
                        <th id="taxes-head"  class="count-cell "><?php __("Tax") ?></th>
                    <?php } ?>
                    <th id="chead"  class="count-cell "><span id="label_description"><?php __("Credit") ?><span class="required"> *</span></span></th>
                    <?php if(!$disabled){ ?>
                    <th class="sort-cell"></th>
                    <?php } ?>
				<?php } ?>






<?php
    $modelName = "JournalTransaction";

    if(is_array($this->data['JournalTransaction']) && !empty($this->data['JournalTransaction'])){
        foreach($this->data['JournalTransaction'] as $idx => $journal)
        {

            if(!$this->data['Journal']['is_automatic'] && strpos($journal['subkey'], 'tax')) {
                continue;
            }
            if(!isset($journal['id'])) {
                $journal['id'] = $idx;
            }

            //add taxes to credit and debit and currency debit and currency credit for included transactions
            if($journal['tax_included'] == 1 && !is_null($journal['tax_id'])){
                $this->data['JournalTransaction'][$idx]['currency_debit'] = $journal['currency_debit'] + $journal['tax_debit'];
                $this->data['JournalTransaction'][$idx]['currency_credit'] = $journal['currency_credit'] + $journal['tax_credit'];
                $this->data['JournalTransaction'][$idx]['debit'] = $journal['debit'] + ($journal['tax_debit'] * $journal['currency_rate']);
                $this->data['JournalTransaction'][$idx]['credit'] = $journal['credit'] + ($journal['tax_credit'] * $journal['currency_rate']);
            }
?>
            <?php if ($useNewCostCenterMode) { ?>
                <tr class="itemRow movable fix-clear movable border-0 journals-table-row" data-center-id="<?= $journal['id'] == "" ? $idx : $journal['id']; ?>" data-id="account-<?= $idx ?>">
                    <td class="move-table notEditable">
                        <div class="sortable-arrows">
                            <a class="ArrawUp move-up" href="#" title="Move up" tabindex="-1"></a>
                            <a class="ArrawDown move-down"  href="#" title="Move Down" tabindex="-1"></a>
                        </div>
                    </td>
                    <td class="p-0" colspan="10">
                        <table class="total-table table table-bordered b-light items-listing mobile-invoicing" style="width: 100%;border: 0;">
                            <tbody>
                                <tr>
                                    <?php echo $form->hidden("{$modelName}.$idx.id", array('class' => 'resizable ', 'label' => false, 'class' => 'form-control')); ?>
                                    <td class="td_editable item-name-td">
                                        <div class="item-name">
                                            <div class="item-wrap">
                                                <?php
                                                //debug($this->data[$itemModel][$idx]);
                                                ///echo $form->input("{$modelName}.$idx.journal_account_id" ,array('class' => 'account_id','type'=>'hidden'));
                                                if(!$disabled){
                                                    echo $this->element('journal_accounts_advanced_search_journals_add',array('value' => $journal['journal_account_id'],'options'=>array(
                                                            'input_name' => "data[{$modelName}][$idx][journal_account_id]",
                                                            'id' => "JournalTransaction{$idx}JournalAccountId",
                                                            'class' => 'form-x2 required journal_account_id already_fetched_auto_cost_centers',
                                                            'style' => 'width:220px;',
                                                            'cats_list' => $catsList,
                                                            'value' => $this->data['JournalTransaction'][$idx]['journal_account_id']
                                                        )
                                                    ));
                                                } else {
                                                    echo $form->hidden("{$modelName}.$idx.journal_account_id", array('style'=>'width:220px;', 'class' => 'form-x2  required journal_account_id', 'div' => 'text','options' => $accountNames,'data-live-search' => "TRUE", 'label' => false));
                                                    echo $form->input("{$modelName}.$idx.journal_account_id", array('disabled' => $disabled,'style'=>'width:220px;', 'class' => 'form-x2  required journal_account_id', 'div' => 'text','options' => $accountNames,'data-live-search' => "TRUE", 'label' => false));
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="td_editable unit-price" width="35%">
                                        <?php if(!$disabled){
                                            echo $form->textarea("{$modelName}.$idx.description", array('label' => false, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required', 'placeholder' => __t("Description"), 'data-placeholder' => __t("Description")));
                                        } else {
                                                if (!empty($journal['alter_description'])) {
                                                    echo $form->hidden("{$modelName}.$idx.description", array('label' => false, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required', 'placeholder' => __t("Description"), 'data-placeholder' => __t("Description")));
                                                    echo $form->textarea("{$modelName}.$idx.alter_description", array('label'=> FALSE, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required', 'placeholder' => __t("Description"), 'data-placeholder' => __t("Description")));
                                                } else {
                                                    echo $form->textarea("{$modelName}.$idx.description", array('label' => false, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required', 'placeholder' => __t("Description"), 'data-placeholder' => __t("Description")));
                                                    echo $form->hidden("{$modelName}.$idx.alter_description", array('label'=> FALSE, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required', 'placeholder' => __t("Description"), 'data-placeholder' => __t("Description")));
                                                }
                                        } ?>
                                    </td>
                                    <?php if ($enable_tags){ ?>
                                        <td class="td_editable" width="35%" style="width: 150px;min-width: 150px;max-width: 150px;">
                                            <div class="item-name item-tags">
                                                <div class="item-wrap">
                                                    <?= $form->input("ItemsTag.$idx.tags", array('options'=> $this->data['ItemsTag'][$idx]['tags'], 'class' => 'form-control', 'label' => false ,'type'=>'select','style' => 'height: 52px;', 'multiple' => true, 'placeholder' => __t("Tags"), 'data-placeholder' => __t("Tags"), 'data-tags-input' => 'true')); ?>
                                                    <script>
                                                        $('#ItemsTag<?= $idx ?>Tags').find('option').each(function () {
                                                            $(this).attr('selected', true);
                                                            $(this).val($(this).text());
                                                            oldTags.push({
                                                                id: $(this).val(),
                                                                text: $(this).text()
                                                            });
                                                        });
                                                        $('#ItemsTag<?= $idx ?>Tags').select2(tagsSelect2Options);
                                                    </script>
                                                </div>
                                            </div>
                                        </td>
                                    <?php } ?>
                                    <?php if($display_cost_centers_in_journals || $assigned_cost_centers_list) { ?>
                                    <td class="td_editable" width="10%">
                                        <div class="item-name" style="min-width: 200px;">
                                            <div class="item-wrap">
                                                <?php
                                                    //$m_options = ['selected_field' => 'name', 'id' => 'cost_center_id', 'class' => 'cost_center_advanced_id', 'empty' => __('None', true), 'width' => '100%', 'input_name' => "data[CostCenterTransaction][#jt][#index][cost_center_id]", 'value' => $cost_center_transaction['CostCenterTransaction']['cost_center_id'],'label' => '', 'options' => $cost_centers_list];
                                                    //if (isset($options) && is_array($options)) $m_options = array_merge($m_options, $options);
                                                    //    echo $this->element('advanced_search3', ['controller' => 'cost_centers', 'model' => 'CostCenter', 'action' => 'json_find', 'input_options' => $m_options]);
                                                    $journal['id'] = $journal['id'] == '' ? $idx : $journal['id'];
                                                    $isSingle = isset($assigned_cost_centers[$journal['id']]) && count($assigned_cost_centers[$journal['id']]) == 1 && $assigned_cost_centers[$journal['id']][0]['percentage'] == 100;
                                                    $selectedCostCenters = [];
                                                    foreach($assigned_cost_centers_list as $cost_center) {
                                                      $selectedCostCenters[$cost_center['id']] = $cost_center['name'];
                                                    }
                                                ?>
                                                <select name="data[JournalTransaction][<?php echo $idx; ?>][cost_center]" data-journal-cost-center-select="true" class="l-input ui-input cost_center" data-app-form-select="true" placeholder="<?= __('Cost Center', false) ?>" data-app-form-select-template="ajax-simple-2" data-app-form-select-options='{"ajax": { "url": "/owner/cost_centers/json_find?q=__q__"}, "dropdownFooter": {"title": "<?php __('Multiple') ?>", "class": "l-btn ui-btn u-bg-color-primary u-text-color-white assign-cost-center-btn", "icon": "fas s2020 fa-code-branch", "attributes": [{"data-journal-multiple-cost-centers-btn": "true"}, {"data-id": "<?= $idx ?>"}] } }' >
                                                    <option value=""></option>
                                                    <?php foreach ($initial_cost_centers + $selectedCostCenters as $key => $value) { ?>
                                                        <option value="<?= $key ?>" <?php if($isSingle && $key == $assigned_cost_centers[$journal['id']][0]['cost_center_id']){ echo 'selected';} ?>><?= $value ?></option>
                                                    <?php } ?>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                    <?php } ?>
                                    <?php if($display_taxes) { ?>
                                        <td class="td_editable item-taxes-td" width="10%">
                                            <div class="item-name">
                                                <div class="item-wrap">
                                                    <?php echo $form->input("{$modelName}.$idx.tax_id", array('empty' => '-', 'div' => false, 'data-width' => '88', 'class' => 'tax_id form-control no-margin tax-width', 'label' => false, 'options' => $taxesList, 'placeholder' => __t("Tax"), 'data-placeholder' => __t("Tax"))); ?>
                                                </div>
                                            </div>
                                        </td>
                                    <?php } ?>
                                    <td class="td_editable unit-price" width="10%">
                                        <div class="item-name">
                                            <div class="item-wrap">
                                                <?php
                                                //debug($this->data[$itemModel][$idx]);
                                                if(!$disabled){
                                                    echo $form->input("{$modelName}.$idx.currency_debit", array('value'=>$this->data['JournalTransaction'][$idx]['currency_debit'], 'class' => 'debit-item item_name editable-input resizable FormInput required number','type' => 'number','min' => 0, 'label' => false, 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                                                } else {
                                                    echo $form->hidden("{$modelName}.$idx.currency_debit", array('class' => 'debit-item item_name editable-input resizable FormInput required number','type' => 'number','min' => 0, 'label' => false, 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                                                    echo $form->input("{$modelName}.$idx.currency_debit2", array('value'=>$this->data['JournalTransaction'][$idx]['currency_debit'],'disabled' => $disabled,'class' => 'debit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false, 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="td_editable unit-price" width="10%">
                                        <div class="item-name">
                                            <div class="item-wrap">
                                                <?php
                                                //debug($this->data[$itemModel][$idx]);
                                                if(!$disabled){
                                                    echo $form->input("{$modelName}.$idx.currency_credit", array('value'=>$this->data['JournalTransaction'][$idx]['currency_credit'], 'class' => 'credit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false ,'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
                                                } else {
                                                    echo $form->hidden("{$modelName}.$idx.currency_credit", array('class' => 'credit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false ,'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
                                                    echo $form->input("{$modelName}.$idx.currency_credit2", array('value'=>$this->data['JournalTransaction'][$idx]['currency_credit'],'disabled' => $disabled,'class' => 'credit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false ,'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td data-id="<?php echo $this->data[$modelName][$idx]['id']; ?>" class="delete-product-cell item-subtotal" width="90">
                                        <div class="d-flex">
                                            <a title="<?php __('Remove') ?>"
                                                class="tip removeItem delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020">
                                                <i class="s2020 far fa-minus-circle"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="journal-cost-center-table-wrapper" <?php if(isset($assigned_cost_centers[$journal['id']]) && !$isSingle) {  ?> style="display: block" <? } else { ?> style="display: none" <?php } ?>>
                            <div class="d-flex">
                                <table class="table table-bordered b-light items-listing mobile-invoicing journal-cost-center-table" style="width: 100%;">
                                    <thead>
                                        <tr class="TableHeader table-header active unmovable">
                                            <th id="label_unit_price" class="count-cell"><span><?php __("Cost Center") ?></th>
                                            <th id="label_unit_price" class="detail-cell"><span><?php __("Percentage") ?></th>
                                            <th  class="count-cell "><span id="label_description"><?php __("Amount") ?></th>
                                            <th  class="count-cell p-0">
                                                <div class="d-flex justify-content-end">
                                                    <a class="btn-danger-s2020 btn-s2020 btn-sm-s2020 fs-18 remove-cost-center-table tip" title="<?php __('Delete Cost Center') ?>" style="width: 45px;">
                                                        <i class="s2020 far fa-minus-circle"></i>
                                                    </a>
                                                </div>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="journal-cost-center-tbody" data-type="<?php echo $journal['debit'] > $journal['credit'] ? "debit" : "credit";  ?>" data-acc-id="<?php echo $journal['journal_account_id']; ?>">
                                        <?php
                                          if(isset($assigned_cost_centers[$journal['id']]) && count($assigned_cost_centers[$journal['id']]) && !$isSingle) {
                                          foreach($assigned_cost_centers[$journal['id']] as $cost_center_index => $cost_center_transaction){
                                        ?>
                                    <tr class="itemRow fix-clear movable" id="cost-center-rows-<?php echo $journal['id']; ?>">
                                        <td class="td_editable">
                                            <div class="item-name">
                                                <div class="item-wrap">
                                                <input class="cost_center_transaction_id" type="hidden" name="data[CostCenterTransactionV2][<?php echo $journal['id']; ?>][<?php echo $cost_center_index; ?>][id]" value="<?php echo $cost_center_transaction['id']; ?>" />
                                                <input class="cost_center_type" type="hidden" name="data[CostCenterTransactionV2][<?php echo $journal['id']; ?>][<?php echo $cost_center_index; ?>][type]" value="<?php echo $cost_center_transaction['debit'] > $cost_center_transaction['credit'] ? 'debit' : 'credit'; ?>" />
                                                <input class="journal_transaction_acc" type="hidden" name="data[CostCenterTransactionV2][<?php echo $journal['id']; ?>][<?php echo $cost_center_index; ?>][account_id]" value="<?php echo $cost_center_transaction['journal_account_id']; ?>" />
                                                <?php
                                                    $transaction_id = $journal['id'];
                                                    $m_options = ['selected_field' => 'name', 'id' => 'cost_center_id_'.$journal['id'].'_'.$cost_center_index, 'class' => 'cost_center_advanced_id', 'empty' => __('Any Cost Center', true), 'width' => '100%', 'input_name' => "data[CostCenterTransactionV2][$transaction_id][$cost_center_index][cost_center_id]", 'value' => $cost_center_transaction['cost_center_id'],'label' => false];
                                                    if (isset($options) && is_array($options)) $m_options = array_merge($m_options, $options);
                                                        echo $this->element('advanced_search3', ['controller' => 'cost_centers', 'model' => 'CostCenter', 'action' => 'json_find', 'input_options' => $m_options]);

                                                ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="td_editable item-percentage">
                                            <div class="item-name">
                                                <div class="item-wrap">
                                                    <div class="d-flex align-items-center h-100 h-100">
                                                    <input name="data[CostCenterTransactionV2][<?php echo $journal['id']; ?>][<?php echo $cost_center_index; ?>][percentage]" type="text" required="required" class="percentage editable-input item_name"  value="<?php echo $cost_center_transaction['percentage'] ?>" id="CostCenterTransaction0Percentage" placeholder="<?= __("Percentage") ?>">
                                                    <span class="input-group-addon border-0 p-2" style="background: none;">%</span>
                                                        </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="td_editable item-amount">
                                            <div class="item-name">
                                                <div class="item-wrap">
                                                <input name="data[CostCenterTransactionV2][<?php echo $journal['id']; ?>][<?php echo $cost_center_index; ?>][credit]" type="text" required="required" class="cost-center-amount item_name  editable-input" value="" id="CostCenterTransaction0Debit" placeholder="<?= __("Amount") ?>">																	</div>
                                            </div>
                                        </td>

                                        <td data-id="" class="delete-product-cell">
                                            <div class="d-flex justify-content-end">
                                                <a title="<?php __('Remove') ?>" class="tip removeCostCenterItemBtn delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020">
                                                    <i class="s2020 far fa-minus-circle"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                        <?php
                                         }
                                        }
                                     ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class=" fix-clear notEditable table-tr-totals-s2020">
                                            <td class="p-0 bg-transparent">
                                                <div class="new-row mt-0">
                                                    <div class="d-flex justify-content-between bg-light s2020">
                                                        <div>
                                                            <a data-id="#index" class="add-row my-0 fs-12 btn-s2020 btn-light-s2020 text-dark-blue s2020 font-weight-bold py-2 AddCostItemBtn" tabindex="21"><i class="s2020 far fa-plus-circle text-success mr-2"></i><?php __("Add"); echo ' ';  __('Cost Center') ?></a>
                                                        </div>
                                                        <div class="text-right s2020 bg-light">
                                                            <div class="px-2"><h6 style="font-weight: bold">	<?php __('Total') ?> </h6></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="item-percentage">
                                                <div class="item-name">
                                                    <div class="item-wrap">
                                                    <span class="total-percent"></span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="item-amount">
                                                <div class="item-name">
                                                    <div class="item-wrap">
                                                    <span class="total-amount"></span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                                <div style="width: 47px;background: #e4ebf2" class="flex-shrink-0 journal-cost-center-empty-area"></div>
                            </div>
                        </div>
                    </td>
                    </tr>
            <?php } else { ?>
			<tr class="itemRow fix-clear movable"  data-center-id="<?= $idx ?>" data-id="account-<?= $idx ?>">

                <?php
        echo $form->hidden("{$modelName}.$idx.id", array('class' => 'resizable ', 'label' => false, 'class' => 'form-control'));
    ?>
<!--                    <td>--><?php //echo __("Transaction",true)?><!--</td>-->
                    <td  class="td_editable select-item drop-items select-prod">
                        <div class="item-name">
                            <div class="item-wrap">
                                <?php
                    if (!$disabled) {
                        if (!$useNewEl) {
                            echo $this->element('journal_accounts_advanced_search_journals_add', array('value' => $journal['journal_account_id'],'options'=>array(
                                'input_name' => "data[{$modelName}][$idx][journal_account_id]",
                                'id' => "JournalTransaction{$idx}JournalAccountId",
                                'class' => 'form-x2  journal_account_id',
                                'style' => 'width:220px;',
                                'cats_list' => $catsList,
                                'value' => $this->data['JournalTransaction'][$idx]['journal_account_id']
                            )));
                        } else {
                            echo $this->element('ajax_search/journal_accounts', array(
                                    'options' => [
//                                                    'getEmptyOption' => true,
                                        'id' => "JournalTransaction{$idx}JournalAccountId",
                                        'data-placeholder' => __('Default Account', true),
                                        'class' => 'form-x2  journal_account_id new-accounts-search',
                                        'style' => 'width:220px;',
                                        'data-record-value' => $this->data['JournalTransaction'][$idx]['journal_account_id'],
                                        'div' => 'account-type-account form-group text title col-xs-12',
                                        'multiple' => false,
                                        'label' => false,
                                        'empty' => __('Default Account', true),
                                        'input_name' => "{$modelName}.{$idx}.journal_account_id"
                                    ],
                                'value' => $this->data['JournalTransaction'][$idx]['journal_account_id'] ?: null));
                        }



                    //								echo $form->input("{$modelName}.$idx.journal_account_id", array('style'=>'width:220px;','class' => 'form-x2  required journal_account_id', 'div' => 'text','options' => $accountNames,'data-live-search' => "TRUE", 'label' => false));
                    } else {
                        echo $form->hidden("{$modelName}.$idx.journal_account_id", array('value' => $this->data['JournalTransaction'][$idx]['journal_account_id'],'style'=>'width:220px;','class' => 'form-x2   journal_account_id', 'div' => 'text','options' => $accountNames,'data-live-search' => "TRUE", 'label' => false));
                        echo $form->input("{$modelName}.$idx.journal_account_id", array('value' => $this->data['JournalTransaction'][$idx]['journal_account_id'], 'disabled' => $disabled,'style'=>'width:220px;','class' => 'form-x2   journal_account_id', 'div' => 'text','options' => $accountNames,'data-live-search' => "TRUE", 'label' => false));
                    }
    ?>
                            </div>

                        </div>

                    </td>
                    <td class="td_editable unit-price">

                        <?php
                        if (!$disabled) {
                            echo $form->textarea("{$modelName}.$idx.description", array('label' => false, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required'));
                        } else {
                            if (!empty($journal['alter_description'])) {
                                $description = $journal['alter_description'];
                            } else {
                                $description = $journal['description'];
                            }
                            echo $form->hidden("{$modelName}.$idx.description", array('label' => false, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required'));
                            echo $form->textarea("{$modelName}.$idx.alter_description", array('label'=> false,'value' => $description ,'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required'));
                        }
    ?>
                    </td>

					 <td  class="td_editable unit-price">
                        <div class="item-name">
                            <div class="item-wrap">
                                <?php
                //debug($this->data[$itemModel][$idx]);
                if (!$disabled) {
                    echo $form->input("{$modelName}.$idx.currency_debit", array('value'=>$this->data['JournalTransaction'][$idx]['currency_debit'], 'class' => 'debit-item item_name editable-input resizable FormInput required number', 'label' => false, 'type' => 'number', 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                } else {
                    echo $form->hidden("{$modelName}.$idx.currency_debit", array('class' => 'debit-item item_name editable-input resizable FormInput required number', 'label' => false, 'type' => 'number', 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                    echo $form->input("{$modelName}.$idx.currency_debit2", array('value'=>$this->data['JournalTransaction'][$idx]['currency_debit'], 'disabled' => $disabled,'class' => 'debit-item item_name editable-input resizable FormInput required number', 'label' => false, 'type' => 'number', 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                }

    ?>
                            </div>
                        </div>
				    </td>
                    <td  class="td_editable unit-price">
                        <div class="item-name">
                            <div class="item-wrap">
                                <?php
    if (!$disabled) {
        echo $form->input("{$modelName}.$idx.currency_credit", array('value'=>$this->data['JournalTransaction'][$idx]['currency_credit'], 'class' => 'credit-item item_name editable-input resizable FormInput required number', 'label' => false, 'type' => 'number','min' => 0, 'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
    } else {
        echo $form->hidden("{$modelName}.$idx.currency_credit", array('class' => 'credit-item item_name editable-input resizable FormInput required number', 'label' => false, 'type' => 'number','min' => 0, 'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
        echo $form->input("{$modelName}.$idx.currency_credit2", array('value'=>$this->data['JournalTransaction'][$idx]['currency_credit'],'disabled' => $disabled,'class' => 'credit-item item_name editable-input resizable FormInput required number', 'label' => false, 'type' => 'number', 'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
    }
    ?>
                            </div>

                        </div>
                    </td>
                    <td data-id="<?php echo $this->data[$modelName][$idx]['id']; ?>"  class="delete-product-cell">
                       <div class="d-flex">
                         <?php if (!$disabled) { ?>
                            <a title="<?php __('Remove') ?>" class="tip removeItem delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020" ><i class="s2020 far fa-minus-circle"></i></a>
                         <?php } ?>
                            <a title="<?php __('Assign Cost Center To Account') ?>" data-id="<?= $idx ?>"  class="tip assign-cost-center btn-s2020 btn-sm-s2020 fs-18 text-dark-s2020 tooltipstered non observed" style="background: none;" ><i class="fas s2020 fa-code-branch"></i></a>
                       </div>
                    </td>
			</tr>
            <?php } ?>

<?php
        } // endforeach
    }else{

        foreach (range(0, 1) as $idx) {
?>
            <?php if ($useNewCostCenterMode) { ?>
                <tr class="itemRow fix-clear movable border-0 journals-table-row" data-center-id="<?= $idx ?>" data-id="account-<?= $idx ?>">
                    <td class="move-table notEditable">
                        <div class="sortable-arrows">
                            <a class="ArrawUp move-up" href="#" title="Move up" tabindex="-1"></a>
                            <a class="ArrawDown move-down"  href="#" title="Move Down" tabindex="-1"></a>
                        </div>
                    </td>
                    <td class="p-0" colspan="10">
                        <table class="table table-bordered b-light items-listing mobile-invoicing" style="width: 100%;border: 0;">
                            <tbody>
                                <tr>
                                    <?php echo $form->hidden("{$modelName}.$idx.id", array('class' => 'resizable ', 'label' => false, 'class' => 'form-control')); ?>
                                    <td class="td_editable item-name-td">
                                        <div class="item-name">
                                            <div class="item-wrap">
                                                <?php
                                                //debug($this->data[$itemModel][$idx]);
                                                ///echo $form->input("{$modelName}.$idx.journal_account_id" ,array('class' => 'account_id','type'=>'hidden'));
                                                if(!$disabled){
                                                    echo $this->element('journal_accounts_advanced_search_journals_add',array('value' => $journal['journal_account_id'],'options'=>array(
                                                            'input_name' => "data[{$modelName}][$idx][journal_account_id]",
                                                            'id' => "JournalTransaction{$idx}JournalAccountId",
                                                            'class' => 'form-x2 required journal_account_id',
                                                            'style' => 'width:220px;',
                                                            'cats_list' => $catsList,
                                                            'empty' => __('Please Select', true),
                                                            'value' => $this->data['JournalTransaction'][$idx]['journal_account_id']
                                                        )
                                                    ));
                                                } else {
                                                    echo $form->hidden("{$modelName}.$idx.journal_account_id", array('style'=>'width:220px;', 'class' => 'form-x2  required journal_account_id', 'div' => 'text','options' => $accountNames,'data-live-search' => "TRUE", 'label' => false));
                                                    echo $form->input("{$modelName}.$idx.journal_account_id2", array('disabled' => $disabled,'style'=>'width:220px;', 'class' => 'form-x2  required journal_account_id', 'div' => 'text','options' => $accountNames,'data-live-search' => "TRUE", 'label' => false));
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="td_editable unit-price" width="35%">
                                        <?php if(!$disabled){
                                            echo $form->textarea("{$modelName}.$idx.description", array('label' => false, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required', 'placeholder' => __t("Description"), 'data-placeholder' => __t("Description")));
                                        } else {
                                                echo $form->hidden("{$modelName}.$idx.description", array('label' => false, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required', 'placeholder' => __t("Description"), 'data-placeholder' => __t("Description")));
                                                echo $form->textarea("{$modelName}.$idx.alter_description", array('label'=> FALSE, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required', 'placeholder' => __t("Description"), 'data-placeholder' => __t("Description")));
                                        } ?>
                                    </td>
                                    <?php if ($enable_tags){ ?>
                                        <td class="td_editable" width="35%" style="width: 150px;min-width: 150px;max-width: 150px;">
                                            <div class="item-name item-tags">
                                                <div class="item-wrap">
                                                    <?= $form->input("ItemsTag.$idx.tags", array('options'=> $this->data['ItemsTag'][$idx]['tags'], 'class' => 'form-control', 'label' => false ,'type'=>'select','style' => 'height: 52px;', 'multiple' => true, 'placeholder' => __t("Tags"), 'data-placeholder' => __t("Tags"), 'data-tags-input' => 'true')); ?>
                                                </div>
                                            </div>
                                        </td>
                                    <?php } ?>
                                    <?php if($display_cost_centers_in_journals || $assigned_cost_centers_list) { ?>
                                        <td class="td_editable" width="10%">
                                            <div class="item-name" style="min-width: 200px;">
                                                <div class="item-wrap">
                                                    <?php
                                                        //$m_options = ['selected_field' => 'name', 'id' => 'cost_center_id', 'class' => 'cost_center_advanced_id', 'empty' => __('None', true), 'width' => '100%', 'input_name' => "data[CostCenterTransaction][#jt][#index][cost_center_id]", 'value' => $cost_center_transaction['CostCenterTransaction']['cost_center_id'],'label' => '', 'options' => $cost_centers_list];
                                                        //if (isset($options) && is_array($options)) $m_options = array_merge($m_options, $options);
                                                        //    echo $this->element('advanced_search3', ['controller' => 'cost_centers', 'model' => 'CostCenter', 'action' => 'json_find', 'input_options' => $m_options]);
                                                    ?>
                                                    <select tabindex="0" name="data[JournalTransaction][<?php echo $idx; ?>][cost_center]"  data-journal-cost-center-select="true" class="l-input ui-input cost_center" data-app-form-select="true" placeholder="<?= __('Cost Center', false) ?>" data-app-form-select-template="ajax-simple-2" data-app-form-select-options='{"ajax": { "url": "/owner/cost_centers/json_find?q=__q__"}, "dropdownFooter": {"title": "<?php __('Multiple') ?>", "class": "l-btn ui-btn u-bg-color-primary u-text-color-white assign-cost-center-btn", "icon": "fas s2020 fa-code-branch", "attributes": [{"data-journal-multiple-cost-centers-btn": "true"}, {"data-id": "<?= $idx ?>"}] } }' >
                                                        <option value=""></option>
                                                        <?php foreach ($initial_cost_centers as $key => $value) { ?>
                                                            <option value="<?= $key ?>"><?= $value ?></option>
                                                        <?php } ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </td>
                                    <?php } ?>
                                    <?php if($display_taxes) { ?>
                                        <td class="td_editable item-taxes-td" width="10%">
                                            <div class="item-name">
                                                <div class="item-wrap">
                                                    <?php echo $form->input("{$modelName}.$idx.tax_id", array('empty' => '-', 'div' => false, 'data-width' => '88', 'class' => 'tax_id form-control no-margin tax-width', 'label' => false, 'options' => $taxesList,  'placeholder' => __t("Tax"), 'data-placeholder' => __t("Tax"))); ?>
                                                </div>
                                            </div>
                                        </td>

                                    <?php } ?>

                                    <td class="td_editable unit-price" width="10%">
                                        <div class="item-name">
                                            <div class="item-wrap">
                                                <?php
                                                //debug($this->data[$itemModel][$idx]);

                                                if(!$disabled){
                                                    echo $form->input("{$modelName}.$idx.currency_debit", array('value'=>$this->data['JournalTransaction'][$idx]['currency_debit'], 'class' => 'debit-item item_name editable-input resizable FormInput required number','type' => 'number','min' => 0, 'label' => false, 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                                                } else {
                                                    echo $form->hidden("{$modelName}.$idx.currency_debit", array('class' => 'debit-item item_name editable-input resizable FormInput required number','type' => 'number','min' => 0, 'label' => false, 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                                                    echo $form->input("{$modelName}.$idx.currency_debit2", array('value'=>$this->data['JournalTransaction'][$idx]['currency_debit'],'disabled' => $disabled,'class' => 'debit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false, 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="td_editable unit-price" width="10%">
                                        <div class="item-name">
                                            <div class="item-wrap">
                                                <?php
                                                //debug($this->data[$itemModel][$idx]);
                                                if(!$disabled){
                                                    echo $form->input("{$modelName}.$idx.currency_credit", array('value'=>$this->data['JournalTransaction'][$idx]['currency_credit'], 'class' => 'credit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false ,'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
                                                } else {
                                                    echo $form->hidden("{$modelName}.$idx.currency_credit", array('class' => 'credit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false ,'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
                                                    echo $form->input("{$modelName}.$idx.currency_credit2", array('value'=>$this->data['JournalTransaction'][$idx]['currency_credit'],'disabled' => $disabled,'class' => 'credit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false ,'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td data-id="<?php echo $this->data[$modelName][$idx]['id']; ?>" class="delete-product-cell item-subtotal" width="90">
                                        <div class="d-flex">
                                            <a title="<?php __('Remove') ?>"
                                                class="tip removeItem delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020">
                                                <i class="s2020 far fa-minus-circle"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="journal-cost-center-table-wrapper" style="display: none">
                            <div class="d-flex">
                                <table class="table table-bordered b-light items-listing mobile-invoicing journal-cost-center-table" style="width: 100%;">
                                    <thead>
                                        <tr class="TableHeader table-header active unmovable">
                                            <th id="label_unit_price" class="count-cell"><span><?php __("Cost Center") ?></th>
                                            <th id="label_unit_price" class="detail-cell"><span><?php __("Percentage") ?></th>
                                            <th  class="count-cell "><span id="label_description"><?php __("Amount") ?></th>
                                            <th  class="count-cell p-0">
                                                <div class="d-flex justify-content-end">
                                                    <a class="btn-danger-s2020 btn-s2020 btn-sm-s2020 fs-18 remove-cost-center-table tip" title="<?php __('Delete Cost Center') ?>" style="width: 45px;">
                                                        <i class="s2020 far fa-minus-circle"></i>
                                                    </a>
                                                </div>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="journal-cost-center-tbody">
                                         <?php if(isset($assigned_cost_centers)){ ?>
                                         <?php } ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class=" fix-clear notEditable table-tr-totals-s2020">
                                            <td class="p-0 bg-transparent">
                                                <div class="new-row mt-0">
                                                    <div class="d-flex justify-content-between bg-light s2020">
                                                        <div>
                                                            <a data-id="#index" class="add-row my-0 fs-12 btn-s2020 btn-light-s2020 text-dark-blue s2020 font-weight-bold py-2 AddCostItemBtn" tabindex="21"><i class="s2020 far fa-plus-circle text-success mr-2"></i><?php __("Add"); echo ' ';  __('Cost Center') ?></a>
                                                        </div>
                                                        <div class="text-right s2020 bg-light">
                                                            <div class="px-2"><h6 style="font-weight: bold">	<?php __('Total') ?> </h6></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="item-percentage">
                                                <div class="item-name">
                                                    <div class="item-wrap">
                                                    <span class="total-percent"></span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="item-amount">
                                                <div class="item-name">
                                                    <div class="item-wrap">
                                                    <span class="total-amount"></span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                                <div style="width: 47px;background: #e4ebf2" class="flex-shrink-0 journal-cost-center-empty-area"></div>
                            </div>
                        </div>
                    </td>
                    </tr>
            <?php } else { ?>
                <tr class="itemRow fix-clear movable" data-center-id="<?= $idx ?>" data-id="account-<?= $idx ?>">
                    <?php echo $form->hidden("{$modelName}.$idx.id", array('class' => 'resizable ', 'label' => false, 'class' => 'form-control')); ?>
                    <td class="td_editable">
                        <div class="item-name">
                            <div class="item-wrap">
                                <?php
                                //debug($this->data[$itemModel][$idx]);
                                ///echo $form->input("{$modelName}.$idx.journal_account_id" ,array('class' => 'account_id','type'=>'hidden'));
                                if(!$disabled){
                                    echo $this->element('journal_accounts_advanced_search_journals_add',array('value' => $journal['journal_account_id'],'options'=>array(
                                            'input_name' => "data[{$modelName}][$idx][journal_account_id]",
                                            'id' => "JournalTransaction{$idx}JournalAccountId",
                                            'class' => 'form-x2 required journal_account_id',
                                            'style' => 'width:220px;',
                                            'cats_list' => $catsList,
                                            'value' => $this->data['JournalTransaction'][$idx]['journal_account_id']
                                        )
                                    ));
                                } else {
                                    echo $form->hidden("{$modelName}.$idx.journal_account_id", array('style'=>'width:220px;', 'class' => 'form-x2  required journal_account_id', 'div' => 'text','options' => $accountNames,'data-live-search' => "TRUE", 'label' => false));
                                    echo $form->input("{$modelName}.$idx.journal_account_id2", array('disabled' => $disabled,'style'=>'width:220px;', 'class' => 'form-x2  required journal_account_id', 'div' => 'text','options' => $accountNames,'data-live-search' => "TRUE", 'label' => false));
                                }
                                ?>
                            </div>
                        </div>
                    </td>
                    <td class="td_editable unit-price">
                        <?php if(!$disabled){
                            echo $form->textarea("{$modelName}.$idx.description", array('label' => false, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required'));
                        } else {
                                echo $form->hidden("{$modelName}.$idx.description", array('label' => false, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required'));
                                echo $form->textarea("{$modelName}.$idx.alter_description", array('label'=> FALSE, 'rows' => '2', 'class' => 'resizable item-description editable-input editable-area FormInput required'));
                        } ?>
                    </td>
                    <td class="td_editable unit-price">
                        <div class="item-name">
                            <div class="item-wrap">
                                <?php
                                //debug($this->data[$itemModel][$idx]);
                                if(!$disabled){
                                    echo $form->input("{$modelName}.$idx.currency_debit", array('value'=>$this->data['JournalTransaction'][$idx]['currency_debit'], 'class' => 'debit-item item_name editable-input resizable FormInput required number','type' => 'number','min' => 0, 'label' => false, 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                                } else {
                                    echo $form->hidden("{$modelName}.$idx.currency_debit", array('class' => 'debit-item item_name editable-input resizable FormInput required number','type' => 'number','min' => 0, 'label' => false, 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                                    echo $form->input("{$modelName}.$idx.currency_debit2", array('value'=>$this->data['JournalTransaction'][$idx]['currency_debit'],'disabled' => $disabled,'class' => 'debit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false, 'div' => false, 'placeholder' => __t("Debit"), 'data-placeholder' => __t("Debit")));
                                }
                                ?>
                            </div>
                        </div>
                    </td>
                    <td class="td_editable unit-price">
                        <div class="item-name">
                            <div class="item-wrap">
                                <?php
                                //debug($this->data[$itemModel][$idx]);
                                if(!$disabled){
                                    echo $form->input("{$modelName}.$idx.currency_credit", array('value'=>$this->data['JournalTransaction'][$idx]['currency_credit'], 'class' => 'credit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false ,'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
                                } else {
                                    echo $form->hidden("{$modelName}.$idx.currency_credit", array('class' => 'credit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false ,'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
                                    echo $form->input("{$modelName}.$idx.currency_credit2", array('value'=>$this->data['JournalTransaction'][$idx]['currency_credit'],'disabled' => $disabled,'class' => 'credit-item item_name editable-input resizable FormInput required number','type' => 'number', 'label' => false ,'div' => false, 'placeholder' => __t("Credit"), 'data-placeholder' => __t("Credit")));
                                }
                                ?>
                            </div>
                        </div>
                    </td>
                    <td data-id="<?php echo $this->data[$modelName][$idx]['id']; ?>" class="delete-product-cell item-subtotal">
                        <div class="d-flex">
                            <a title="<?php __('Remove') ?>"
                                class="tip removeItem delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020">
                                <i class="s2020 far fa-minus-circle"></i>
                            </a>
                            <a title="<?php __('Assign Cost Center To Account') ?>" data-id="<?= $idx ?>"
                                class="tip assign-cost-center btn-s2020 btn-sm-s2020 fs-18 text-dark-s2020" style="background: none;">
                                <i class="fas s2020 fa-code-branch"></i>
                            </a>
                        </div>
                    </td>
                </tr>
            <?php } ?>
<?php }} ?>

        </table>


<?php
$pasteSheetModalId = 'pasteSheetModal';
$pasteSheetIframeURL = "/js/entities/journals/journals-paste-sheet-table.js?v=".JAVASCRIPT_VERSION;
?>

<table width="100%" border="0" cellspacing="0" cellpadding="0" class="table  b-light table-bordered  items-listing mobile-invoicing table-totals-s2020 totals-table">
    <tr class="items-subtotal fix-clear movable notEditable">
        <td class="tspan new-row-cell add-row-btn-wrapper" rowspan="20" style="vertical-align: top;">
            <div class="mb-0">
                <div class="btn-group-flex new-row mt-0">
                    <a href="#" id="AddItem" class="add-row py-0 py-lg-3 d-flex align-items-center px-5 my-0 fs-14 btn-s2020 btn-light-s2020 text-dark-blue s2020 font-weight-bold add-new-btng" tabindex="21" <?= $disabled ? 'disabled' : '' ?>>
                        <i class="s2020 far fa-plus-circle text-primary mr-2"></i><?php __("Add") ?>
                    </a>
                    <button type="button" class="btn-s2020 btn-light-s2020 text-dark-blue dropdown-toggle border-left-input-s2020" id="AddItemCart" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" <?= $disabled ? 'disabled' : '' ?>>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a href="#" data-toggle="modal" data-target="#pasteSheetModal"><?php __('Paste from Sheet') ?></a></li>
                    </ul>
                </div>
                <?php
                    echo $this->element('paste_sheet_table', [
                        'modal_id' => $pasteSheetModalId,
                        'iframe_id' => 'pasteSheetIframe',
                    ]);
                ?>

            </div>
        </td>
        <td style="display: none" class="items-subtotal-items new-row-cell"></td>
        <td style="display: none" class="items-subtotal-items" colspan="3"><strong><?php  __('Total Without Tax'); ?></strong></td>
        <td style="display: none" class="items-subtotal-items" id="subtotal-debit"></td>
        <td style="display: none" class="items-subtotal-items" id="subtotal-credit"></td>
        <td style="display: none" class="items-subtotal-items sort-cell" id="" style=" width: 47px;"></td>
    </tr>
    <tr class="items-totals fix-clear movable notEditable">
		<?php if(!$disabled) { ?>
        <td class="tspan new-row-cell add-row-btn-wrapper">
            <!-- <div class="mb-0 mb-lg-2">
                <div class="btn-group-flex new-row mt-0">
                    <a href="#" id="AddItem" class="add-row py-0 py-lg-3 d-flex align-items-center px-5 my-0 fs-14 btn-s2020 btn-light-s2020 text-dark-blue s2020 font-weight-bold add-new-btng" tabindex="21">
                        <i class="s2020 far fa-plus-circle text-primary mr-2"></i><?php __("Add") ?>
                    </a>
                    <button type="button" class="btn-s2020 btn-light-s2020 text-dark-blue dropdown-toggle border-left-input-s2020" id="AddItemCart" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a href="#" data-toggle="modal" data-target="#pasteSheetModal"><?php __('Paste from Sheet') ?></a></li>
                    </ul>
                </div>
                <?php
                    /* echo $this->element('paste_sheet_table', [
                        'modal_id' => $pasteSheetModalId,
                        'iframe_id' => 'pasteSheetIframe',
                    ]); */
                ?>
            </div> -->
        </td>
		<?php } ?>
        <td class="text-left" colspan="3"><strong><?php echo __("Total",true) ?></strong></td>
        <td class="text-left" id="dtotal" style="width: 170px;">
					<span>0</span>
					<input type="hidden" id="debit-total"/>
		</td>
        <td class="text-left" id="ctotal" style="width: 171px;">
					<span>0</span>
					<input type="hidden" id="credit-total"/>
		</td>
		<?php if(!$disabled){ ?>
        <td class=" sort-cell" id="" style=" width: 47px;"></td>
		<?php } ?>
	</tr>

</table>

<br><br><br>

    <template id="create_cost_center_template">
        <div class="table-responsive invoice-items {type}" id="cost-center-wrapper-#index" data-id="#index" data-acc-id="#acc-id">
            <div class="panel panel-default border-0 m-0 h-100">
                <div class="px-4 py-3 border-bottom border-secondary s2020 d-flex" style="justify-content: space-between;background-color: rgba(246, 246, 246, 0.5);">
                    <h3 class="text-dark-blue s2020 pb-0 fs-16 font-weight-bold d-flex align-items-center">
                        <span class="cost-center-wrapper-title">#title</span>
                    </h3>
                    <div class="details">
                     <span class="cost_center_type">#type</span>  :
                      <span class="cost_center_sum">#sum</span>
                    </div>
                </div>
                <div class="panel-body bg-transparent p-0 s2020">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table table-bordered b-light items-listing mobile-invoicing bg-transparent" id="cost_listing_table">
                        <tr class="TableHeader table-header active unmovable">
                            <th id="label_unit_price" class="count-cell"><span><?php __("Cost Center") ?></th>
                            <th id="label_unit_price" class="detail-cell"><span><?php __("Percentage") ?></th>
                            <th  class="count-cell "><span id="label_description"><?php __("Amount") ?></th>
                            <th  class="count-cell p-0">
                                <div class="d-flex justify-content-end">
                                    <a class="btn-danger-s2020 btn-s2020 btn-sm-s2020 fs-18 remove-cost-center-table tip" title="<?php __('Delete Cost Center') ?>" style="width: 45px;">
                                        <i class="s2020 far fa-minus-circle"></i>
                                    </a>
                                </div>
                            </th>
                        </tr>

                        <tfoot>
                            <tr class=" fix-clear notEditable table-tr-totals-s2020">
                                <td class="p-0 bg-transparent">
                                    <div class="new-row mt-0">
                                        <div class="d-flex justify-content-between bg-light s2020">
                                            <div>
                                                <a id="AddCostItem" data-id="#index" class="add-row py-3 px-5 my-0 fs-14 btn-s2020 btn-light-s2020 text-dark-blue s2020 font-weight-bold" tabindex="21"><i class="s2020 far fa-plus-circle text-primary mr-2"></i><?php __("Add") ?></a>
                                            </div>
                                            <div class="text-right s2020 bg-light">
                                                <div class="px-2"><h6 style="font-weight: bold">	<?php __('Total') ?> </h6></div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="item-percentage">
                                    <div class="item-name">
                                        <div class="item-wrap">
                                        <span id="total-percent"></span>
                                        </div>
                                    </div>
                                </td>
                                <td class="item-amount">
                                    <div class="item-name">
                                        <div class="item-wrap">
                                        <span id="total-amount"></span>
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </template>


    <?php

        if($useNewCostCenterMode) {
            include "add_cost_center2.ctp";
        } else {
            include "add_cost_center1.ctp";
        }

    ?>



        <div class="pages-head-s2020">
            <div class="container">
                <div class="row-flex align-items-center">
                    <div class="col-flex-sm-6">
                        <?php if ($allowDraft && !isset($_GET['from_matching'])) { ?>
                        <div class="btn-group m-r-md in_preview">
                            <a href="#" class="button save draft btn btn-secondary s2020" title="(ALT + D)">
                                <?php __("<span class='hidden-xs'>Save as</span> Draft") ?>
                            </a>
                        </div>
                        <?php } ?>
                    </div>
                    <div class="col-flex-sm-6 d-flex justify-content-end">
                        <button type="button" class="btn s2020 btn-icn btn-secondary font-weight-medium ml-2 mt-0 cancel-btn">
                            <i class="mdi mdi-close fs-20"></i>
                            <span><?php __("Cancel") ?></span>
                        </button>
                        <?php if ($allowNormalSave) { ?>
                            <button type="submit" class="btn s2020 btn-icn btn-success font-weight-medium ml-2" onclick="initPageLoader()">
                                <i class="mdi mdi-content-save-outline fs-20"></i>
                                <span><?php (!empty($prescription) ? __("Save") : __("Save") ) ?></span>
                            </button>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
        <style>
            .ltr .save.draft span.hidden-xs {
                margin-right: 4px;
            }
            input.editable-input.percentage {
            width: calc(100% - 30px) !important;
            min-width: unset !important;
        }
        </style>

        <template id="create_cost_center_row_template">
        <tr class="itemRow fix-clear movable" id="cost-center-rows-#subIndex">
            <td class="td_editable">
                <div class="item-name">
                    <div class="item-wrap">
                    <input type="hidden" name="data[CostCenterTransaction][#accid][#index][#subIndex][type]" value="#type" />
                    <input type="hidden" name="data[CostCenterTransaction][#accid][#index][#subIndex][account_id]" value="#accid" />
                    <?php
                        $m_options = ['selected_field' => 'name', 'id' => 'cost_center_id', 'class' => 'cost_center_advanced_id', 'empty' => __('Any Cost Center', true), 'width' => '100%', 'input_name' => "data[CostCenterTransaction][#accid][#index][#subIndex][cost_center_id]", 'value' => $cost_center_transaction['CostCenterTransaction']['cost_center_id'],'label' => '', 'options' => $cost_centers_list];
                        if (isset($options) && is_array($options)) $m_options = array_merge($m_options, $options);
                            echo $this->element('advanced_search3', ['controller' => 'cost_centers', 'model' => 'CostCenter', 'action' => 'json_find', 'input_options' => $m_options]);

                    ?>
                    </div>
                </div>
            </td>
            <td class="td_editable item-percentage">
                <div class="item-name">
                    <div class="item-wrap">
                        <div class="input-group h-100">

                        <input name="data[CostCenterTransaction][#accid][#index][#subIndex][percentage]" type="text" required="required" class="percentage editable-input item_name"  value="" id="CostCenterTransaction0Percentage">
                        <span class="input-group-addon border-0" style="background: none;">%</span>
                            </div>
                    </div>
                </div>
            </td>
            <td class="td_editable item-amount">
                <div class="item-name">
                    <div class="item-wrap">
                    <input name="data[CostCenterTransaction][#accid][#index][#subIndex][credit]" type="text" required="required" class="cost-center-amount item_name  editable-input" value="" id="CostCenterTransaction0Debit" placeholder="<?= __("Amount") ?>">																	</div>
                </div>
            </td>

            <td data-id="" class="delete-product-cell">
                <div class="d-flex justify-content-end">
                    <a title="<?php __('Remove') ?>" class="tip removeItem delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020">
                        <i class="s2020 far fa-minus-circle"></i>
                    </a>
                </div>
            </td>
        </tr>

    </template>
    <template id="create_cost_center_row_template_v2">
        <tr class="itemRow fix-clear movable" id="cost-center-rows-#jt">
            <td class="td_editable">
                <div class="item-name">
                    <div class="item-wrap">
                    <input class="cost_center_type" type="hidden" name="data[CostCenterTransactionV2][#jt][#index][type]" value="#type" />
                    <input class="journal_transaction_acc" type="hidden" name="data[CostCenterTransactionV2][#jt][#index][account_id]" value="#accid" />
                    <?php
                        $m_options = ['selected_field' => 'name', 'id' => 'cost_center_id', 'class' => 'cost_center_advanced_id', 'empty' => __('Any Cost Center', true), 'width' => '100%', 'input_name' => "data[CostCenterTransactionV2][#jt][#index][cost_center_id]", 'value' => $cost_center_transaction['CostCenterTransaction']['cost_center_id'],'label' => false, 'options' => $cost_centers_list];
                        if (isset($options) && is_array($options)) $m_options = array_merge($m_options, $options);
                            echo $this->element('advanced_search3', ['controller' => 'cost_centers', 'model' => 'CostCenter', 'action' => 'json_find', 'input_options' => $m_options]);

                    ?>
                    </div>
                </div>
            </td>
            <td class="td_editable item-percentage">
                <div class="item-name">
                    <div class="item-wrap">
                        <div class="d-flex align-items-center h-100 h-100">
                        <input name="data[CostCenterTransactionV2][#jt][#index][percentage]" type="text" required="required" class="percentage editable-input item_name"  value="" id="CostCenterTransaction0Percentage" placeholder="<?= __("Percentage") ?>">
                        <span class="input-group-addon border-0 p-2" style="background: none;">%</span>
                            </div>
                    </div>
                </div>
            </td>
            <td class="td_editable item-amount">
                <div class="item-name">
                    <div class="item-wrap">
                    <input name="data[CostCenterTransactionV2][#jt][#index][credit]" type="text" required="required" class="cost-center-amount item_name  editable-input" value="" id="CostCenterTransaction0Debit"  placeholder="<?= __("Amount") ?>">																	</div>
                </div>
            </td>

            <td data-id="" class="delete-product-cell">
                <div class="d-flex justify-content-end">
                    <a title="<?php __('Remove') ?>" class="tip removeCostCenterItemBtn delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020">
                        <i class="s2020 far fa-minus-circle"></i>
                    </a>
                </div>
            </td>
        </tr>

    </template>
    <?php
		$SEARCH_COST_CENTER_AJAX = Router::url(['controller'=> 'cost_centers','action'=>'json_find', '?' => ['parent_type' => $_GET['parent_type']]]);
		echo <<<HTML
<script>
let SEARCH_COST_CENTER_AJAX = "$SEARCH_COST_CENTER_AJAX";
</script>
HTML; ?>

        <div class="row">
            <div class="col-lg-12">
                <div id="insert-here">
                    <?php echo $this->element('custom_forms/le_forms'); ?>
                </div>
            </div>
        </div>
		<!--
script for journal_transactions

		-->

        <script>
            var currencies = <?php echo json_encode(include APP . 'config' . DS . 'currencies.php') ?>;
            var number_formats = <?php echo json_encode($number_formats) ?>;
            var country_code = '<?php echo getCurrentSite('country_code'); ?>';
            var currency_code = '<?php echo getCurrentSite('currency_code'); ?>';
            var initial_currency_round = 5;
            var currency_round = initial_currency_round;
            <?php if ((empty($this->data['Journal']['id']) || $this->data['Journal']['created'] > "2025-08-02")) { ?>
            // If journal is new or created after 2025-08-03,
            // get currency_round value from number_formats using selected currency
            initial_currency_round = number_formats[$("[name='data[Journal][currency_code]']").val()]?.[0] ?? 2;
            currency_round = initial_currency_round;
            $("[name='data[Journal][currency_code]']").on('change', function() {
                initial_currency_round = number_formats[$(this).val()]?.[0] ?? 5;
                currency_round = initial_currency_round;
                calc_total_credit();
                calc_total_debit();
                
            });
        <?php } ?>
        
    
        </script>
        <script>
            function getCurrencyRound(){
                currency_round = initial_currency_round;
                $('.credit-item').each(function(){
                    item_amount = parseFloat($(this).val());
                    if(!isNaN(item_amount) && item_amount >= 0) {
                        item_fraction = item_amount.toString().split('.')[1]?.length ?? 0;
                        if(item_fraction > currency_round){
                            currency_round = item_fraction;
                        }
                    }
                });
                $('.debit-item').each(function(){
                    item_amount = parseFloat($(this).val());
                    if(!isNaN(item_amount) && item_amount >= 0) {
                        item_fraction = item_amount.toString().split('.')[1]?.length ?? 0;
                        if(item_fraction > currency_round){
                            currency_round = item_fraction;
                        }
                    }
                });
            }
            var pasteSheetModalJSLoaded = false;
            $('#<?= $pasteSheetModalId ?>').on('show.bs.modal', function () {
                if (pasteSheetModalJSLoaded === false) {
                    $.getScript("<?= $pasteSheetIframeURL ?>")
                        .done(function() {
                            pasteSheetModalJSLoaded = true;
                            document.querySelector(pasteSheetIframeSelector).height = (window.innerHeight - 140);
                            initLoader($(pasteSheetModalSelector).find('.modal-body')[0]);
                            $(pasteSheetIframeSelector).attr('src', pasteSheetIframeURL);
                        })
                        .fail(function(jqxhr, settings, exception) {
                            console.error(exception);
                        });
                }
            });
        </script>
        <script type="text/javascript">//<![CDATA[
            var jsDateFormats = <?php echo json_encode($jsDateFormat = getDateFormats(false)); ?>;
            var jsDateFormat = '<?php echo $jsDateFormat[getCurrentSite('date_format')] ?>';
            //]]></script>
            <?php
                echo $javascript->link('magicsuggest-min.js');
            ?>

		<script type="text/javascript" src="/js/jqueryui.js"></script>
		<script type="text/javascript" src="/js/jquery.elastic.js"></script>
        <script type="text/javascript" src="/js/decimal.min.js"></script>
<?php //			echo $javascript->link('functions_ajax_v' . JAVASCRIPT_VERSION) ?>
        <script type="text/javascript" src="/js/new-journal-v2_v<?php echo JAVASCRIPT_VERSION ?>.js"></script>
		<script>
           var medications_group;
           var availableTags ;
           var debit_total = 0;
           var credit_total = 0;
           function calc_total_credit(element){
               if(typeof element != 'undefined')
                   $(element.target).closest('tr').find('.debit-item').val(0);
                getCurrencyRound();
               credit_total = 0;
               let subtotal = new Decimal(0);
               let credit_total_obj = new Decimal(0);
               $('.credit-item').each(function(){
                   item_amount = parseFloat($(this).val());
                   let without_tax_item_amount = item_amount;
                   if(!isNaN(item_amount) && item_amount >= 0) {
                       let taxPercentage = 0;
                       let taxAmount = 0;
                       const taxId = $(this).closest('tr').find('.tax_id').val();
                       if(taxId && taxesValues[taxId]) {
                           if(includedTaxes[taxId] == 1) {
                               taxPercentage = parseFloat(taxesValues[taxId]);
                               without_tax_item_amount = item_amount / ((100 + taxPercentage) / 100);
                               taxAmount = item_amount - without_tax_item_amount;
                           } else {
                               taxPercentage = parseFloat(taxesValues[taxId]);
                               taxAmount = (item_amount * taxPercentage) / 100;
                               taxAmount = round(taxAmount, currency_round);
                            }
                       }
                       

                       subtotal = subtotal.plus(new Decimal(without_tax_item_amount));
                       credit_total_obj = credit_total_obj.plus(new Decimal(without_tax_item_amount)).plus(new Decimal(taxAmount));
                   }
               });
               credit_total = credit_total_obj.toFixed();
               $credit_total.siblings("span").text(round(credit_total,2));
               $('#subtotal-credit').text(round(subtotal,2));

               $('#dtotal').width($('#dhead').outerWidth() - 20);
               $('#ctotal').width($('#chead').outerWidth() - 21);
               if(displayTaxes) {
                   calculateTotalTaxCreditAndDebit();
               }
           }


           function calc_total_debit(element){
               if(typeof element != 'undefined'){
                   $(element.target).closest('tr').find('.credit-item').val(0);
               }
               getCurrencyRound();
               debit_total = 0;
               let subtotal = new Decimal(0);
               let debit_total_obj = new Decimal(0);
               $('.debit-item').each(function(){
                   item_amount = parseFloat($(this).val());
                   let without_tax_item_amount = item_amount;
                   if(!isNaN(item_amount)) {
                       let taxPercentage = 0;
                       let taxAmount = 0;
                       const taxId = $(this).closest('tr').find('.tax_id').val();
                       if(taxId && taxesValues[taxId]) {
                           if(includedTaxes[taxId] == 1) {
                               taxPercentage = parseFloat(taxesValues[taxId]);
                               without_tax_item_amount = item_amount / ((100 + taxPercentage) / 100);
                               taxAmount = item_amount - without_tax_item_amount;
                           } else {
                               taxPercentage = parseFloat(taxesValues[taxId]);
                               taxAmount = (item_amount * taxPercentage) / 100;
                               taxAmount = round(taxAmount, currency_round);
                            }
                       }
                       subtotal = subtotal.plus(new Decimal(without_tax_item_amount));
                       debit_total_obj = debit_total_obj.plus(new Decimal(without_tax_item_amount)).plus(new Decimal(taxAmount));
                   }

               });

               debit_total = debit_total_obj.toFixed();
               $debit_total.siblings("span").text(round(debit_total,2));
               $('#subtotal-debit').text(round(subtotal,2));
               $('#dtotal').width($('#dhead').outerWidth() - 20);
               $('#ctotal').width($('#chead').outerWidth() - 21);
               if(displayTaxes) {
                   calculateTotalTaxCreditAndDebit();
               }
           }
           $(function() {
               $credit_total = $('#credit-total');
               $debit_total = $('#debit-total');





               $('body').on('change','.credit-item',calc_total_credit);

               $('body').on('change','.debit-item',calc_total_debit);

               calc_total_credit();
               calc_total_debit();
               //alert(medications_group);
           });



           function validDate(value){
               if ($(value).hasClass('is-calendarsPicker')) {
                   if (!(notEmpty(value.value) && $(value).calendarsPicker('getDate').length > 0)) {
                       $(value).after($('<div>', {
                           'class': 'error-message'
                       }).text(__('Invalid date. Date must match date format')));
                       return false;
                   }
                   return true;
               }
               try {
                   var result = (value == $.datepicker.formatDate(jsDateFormat, $.datepicker.parseDate(jsDateFormat, value)));
                   if (!result){
                       $(selector).after($('<div>', {
                           'class': 'error-message'
                       }).text(__('Invalid date. Date must match date format')));
                   }
                   return result;
               } catch (exception) {
                   return false;
               }
           }

		    function isEqual(value){
                if(Math.abs(parseFloat(debit_total) - parseFloat(credit_total)) < 0.0001){
                    return true;
			    } else {   
                    currency_round = number_formats[$("[name='data[Journal][currency_code]']").val()]?.[0] ?? 2;
                    calc_total_credit();
                    calc_total_debit();
                    if(Math.abs(parseFloat(debit_total) - parseFloat(credit_total)) < 0.0001){
                        <?php if(empty($this->data['Journal']['id'])) { ?>
                            $('#JournalOwnerAddForm').append(`<input type="hidden" name="data[force_rounding]" value="1">`);
                        <?php } else { ?>
                            $('#JournalOwnerEditForm').append(`<input type="hidden" name="data[force_rounding]" value="1">`);
                        <?php } ?>
                        return true;
                    } else {
                        return false;
                    }
                }
            }

           function validAccount(value)
           {
               var accountIds = [];
               <?php foreach($accountNames as $key => $val){ ?>
                    accountIds.push('<?php echo $key; ?>');
               <?php } ?>
               //alert(accountIds.includes(value));
               accountIds = accountIds.filter(item => item !== '0');
               try {
                   if( typeof value !== 'undefined' )
                       fl = accountIds.includes(value);

                   if(fl === false)
                   {

                       $(selector).after($('<div>', {
                           'class': 'error-message'
                       }).text(__('Invalid Account.')));

                       return false;
                   }


                   return true;
               } catch (exception) {

                   return false;
               }
           }
		   transactionsCountElement = $('#transactionsCount');

		   function moreThanOne(value){
			   if(value > 1) return true;
			   else return false
		   }

           function validateFields(){
               calc_total_credit();
               calc_total_debit();
               $('.debit-item,.credit-item').each(function(){
                   if($(this).val()=='') $(this).val('0');
               })
               var rules = {
                   '.debit-item': ['notEmpty', 'isNotMinus'],
                   '.credit-item': ['notEmpty', 'isNotMinus'],
                 //  '.select-item-name':  ['notEmpty'],
                   //'.journal_account_id': ['validAccount'],
                   '#JournalDate': {
                       rules: ['notEmpty', 'validDate'],
                       pass: 'selector'
                   },
                   '#debit-total': ['isEqual'],
                   '#transactionsCount': ['moreThanOne'],
               };


               var validationMessages = {
                   isEqual: __('Total Debit Must be Equal to Total Credit'),
                   notEmpty: __('Required'),
                   moreThanOne: __('You Have to Add at Least 2 Journal Transactions'),
                   validAccount: __('Required'),
                   isNumeric: __('Valid number required'),
                   validDate: __('Valid date required'),
                   isNotMinus: __('Positive number required'),
                   lessThan: __('Value must be less than :value'),
                   lessThanOrEqual: __('Value must be less than or equal to :value'),

               };

               let selectCostCenter = $('.select-item-name[data-required]');
               let emptyCostCenters = [];
               $('.select-item-name-error').remove();
               selectCostCenter.each(function(){
                   let val = $(this).val();
                   if(val == ""){
                     $(this).next('.bootstrap-select').after('<div class="select-item-name-error error-message error_undefined">'+__('Required')+'</div>');
                     emptyCostCenters.push(true);
                   }
               });

               if(emptyCostCenters.length){
                   return false;
               }

               return validate(rules, validationMessages);
           }

           function getUrlQuiry(quiryName)
            {
                var hash = null;
                if(window.location.href.indexOf(quiryName) > 0){
                    hash = window.location.href.slice(window.location.href.indexOf(quiryName) + quiryName.length + 1);
                }
                return hash;
            }
            //activate the required field
            $('#JournalOwnerAddForm').submit(function(e){
                $(window).unbind('beforeunload');
                const draftId = getUrlQuiry("?draftId");
                if (draftId) {
                    $('#JournalOwnerAddForm').append(`<input type="hidden" name="data[non_completed_id]" id="nonCompletedID" value="${draftId}">`);

                }
				  transactionsCountElement.val($('.itemRow').length);
                    if (validateFields() ){
                    }
                    else
                    {
                    $('.page-loader').fadeOut("fast", function () {
                        $(this).remove(); 
                    });
                        e.preventDefault();
                    }
            });


            //activate the required field on edit form
            $('#JournalOwnerEditForm').submit(function(){
				transactionsCountElement.val($('.itemRow').length);
                if (validateFields()){

                }
                else
                {
                    //alert('unvalid');
                    if($('.more-options-box .error-message').length>0)
                    {
                        if($('.more-options-box .collapse.in').length==0) $('button[href="#extra-settings"]').click();
                        $('a[href=#'+$('.more-options-box .error-message').parents('.tab-pane').attr('id')+']').click();
                    }
                    if($('.error-message:first').length>0&&$('.error-message:first').is(':visible'))
                    {
                        $('html,body').animate({
                            scrollTop: $('.error-message:first').offset().top-100
                        }, 600);
                    }
                    else
                    {
                        $('html,body').animate({
                            scrollTop: 0
                        }, 600);
                    }

                    $('.page-loader').fadeOut("fast", function () {
                        $(this).remove(); 
                    });
                    return false;
                }

            });
        </script>
		<!-- end script for journal_transactions -->


	</div>
</div>

<?php echo $html->css('jqueryui', null, array('inline' => false)); ?><?php echo $html->css('clients', null, array('inline' => false)); ?> <?php echo $html->css('bootstrap-select', null, array('inline' => false)); ?>  <?php echo $html->css('time-tracker', null, array('inline' => false)); ?>  <?php echo $html->css('ico', null, array('inline' => false));echo $html->css('magicsuggest-min.css', null, array('inline' => false)); ?>
<?php echo $javascript->link(array('jqueryui' ));   ?>
<?php
// Add checks before including JS and CSS files
$javascript->link(array('bootstrap-select_v'.JAVASCRIPT_VERSION.'.js'), false, ['inline' => false]);
$javascript->link(array('ajax-bootstrap-select.min.js'),  false, ['inline' => false]);
$html->css(array('bootstrap-select_v'.CSS_VERSION.'.css'),  false, ['inline' => false]);
$html->css(array('ajax-bootstrap-select'),  false, ['inline' => false]);
?>
<script>

    $(document).ready(function(){
        //Recalculate cost center percentages when loading page in edit mode
        $('input.percentage').trigger('change');

        <?php if (empty($this->data['Journal']['id'])) { ?>
        $('.invoice-items').each(function(){
            let index = $(this).attr('data-id');
            calculateTotal(index);
        });
        <?php } ?>

        $('.currency_code_picker').selectpicker({
            //style: 'btn-info',
            size: 4
            //maxOptions:10,
        });
//		console.log($('[disabled="disabled"]'));
		$('[disabled="disabled"]').parent('div').click(function()
			{

				alert("<?=  __('You can edit only the description of the auto journal') ?>");
			});
//
    });


	$(function(){
//		$('[disabled="disabled"]').hide();

	})

        // Assign Cost Center Feature
        <?php $costCenterTransaction = json_encode($this->data['CostCenterTransaction']); ?>
        let costCenterTransaction = JSON.parse('<?= $costCenterTransaction ?>');
        accountsCostCenters = [];
        let typeTranslations = {
            'credit' : "<?php echo __("Credit") ?>",
            'debit' : "<?php echo __("Debit") ?>",
        } ;

</script>

    <?php
    if (isset($useNewCostCenterMode) && $useNewCostCenterMode) {
        ?>
    <script type="text/javascript" src="/js/add_cost_center_to_jtransactions2.js?v=<?= rand(1,555555) ?>"></script>
    <?php } else { ?>
        <script type="text/javascript" src="/js/add_cost_center_to_jtransactions1.js?v=<?= rand(1,555555) ?>"></script>
    <?php } ?>
<script>
    $(function(){
        $(document).on('click','.removeItem',function(){
            update_free_column_placeholder();
        })
        $(document).on('focusout','.debit-item,.credit-item',function(){
            update_free_column_placeholder()}
            )
        $(document).on('focus','.debit-item,.credit-item',function(){
            update_free_column_placeholder()}
        )
        $(document).on('keydown','.debit-item,.credit-item',function(event ){
            if(event.which==38||event.which==40) return false;
            if(event.which==13||event.which==32||event.which==9&&($(this).val()==''||$(this).val()=='0'))
            {
                $(this).val($(this).attr('placeholder'));
                calc_total_credit();
                calc_total_debit();
            }

        })
    })
    function is_balanced()
    {
        return Math.abs(parseFloat(credit_total)-parseFloat(debit_total)) < 0.0001
    }


    function dump_fraction_calculator(amount) {
        var fractionsCount = 0;
        var maxCounter = 5;
        while (fractionsCount < maxCounter && ( (Math.abs(amount - round(amount,fractionsCount))) > (1 / Math.pow(10,fractionsCount + 1)) )) {
            fractionsCount++;
        }
        return fractionsCount;
    }

    function update_free_column_placeholder()
    {

        calc_total_credit();
        calc_total_debit();
        if(!is_balanced())
        {
            var found=0;
            $('.itemRow').each(function(){

                if(($(this).find('.debit-item').val()==''||$(this).find('.debit-item').val()=='0')&&($(this).find('.credit-item').val()==''||$(this).find('.credit-item').val()=='0'))
                {
                    console.log(typeof (credit_total),credit_total, debit_total);
                    if(parseFloat(credit_total)>parseFloat(debit_total))
                    {
                        found=1;
                        $(this).find('.debit-item').attr('placeholder',round(credit_total-debit_total,5));
                        $(this).find('.debit-item').val('');
                        $(this).find('.credit-item').attr('placeholder','');
                    }
                    else
                    {
                        found=1;
                        $(this).find('.credit-item').attr('placeholder',round(debit_total-credit_total, 5));
                        $(this).find('.credit-item').val('');
                        $(this).find('.debit-item').attr('placeholder','');
                    }

                    return false;
                }
                else
                {
                    $(this).find('.credit-item').attr('placeholder','');
                    $(this).find('.debit-item').attr('placeholder','');
                }


            });
            if(found==0) $('#AddItem').click();

        }

    }

    function drawTable(obj, targetIndex, titleText, accId, sum, isRepeated, type) {
        let wrapperId = "cost-center-wrapper-" + targetIndex;
        if(isRepeated) {
            return false;
        }
        if(!$('#' + wrapperId).filter(".is-"+type).length) {
            let tableContent = document.querySelector('#create_cost_center_template').innerHTML;
            tableContent = tableContent.replaceAll('#index', targetIndex);
            tableContent = tableContent.replaceAll('#acc-id', accId);
            tableContent = tableContent.replaceAll('#sum', sum);
            tableContent = tableContent.replaceAll('#type', typeTranslations[type]);
            tableContent = tableContent.replace('#title', titleText);
            tableContent = tableContent.replace('{type}', 'is-'+type);
            $('#cost-centers-tables').append(tableContent);
            $('.selectpicker').selectpicker();
            obj.parents('.itemRow').first().attr('data-cost-center-index', targetIndex);
            drawCostCenterRow(targetIndex,accId, 0, type);
        }
    }

    function drawCostCenterRow(mainIndex,accId, subIndex, type) {
        let wrapperId = "cost-center-rows-" + mainIndex + '-' + subIndex;
        let tableRow = document.querySelector('#create_cost_center_row_template').innerHTML;
        tableRow = tableRow.replaceAll('#index', mainIndex);
        tableRow = tableRow.replaceAll('#type', type);
        if(accId == "") {
            accId = "default";
        }
        tableRow = tableRow.replaceAll('#accid', accId);
        tableRow = tableRow.replaceAll('#index', mainIndex);
        tableRow = tableRow.replaceAll('#subIndex', subIndex);
        $('#cost-center-wrapper-'+ mainIndex).filter(".is-"+type).find('#cost_listing_table tbody').append(tableRow);
        $('.selectpicker').selectpicker();
    }

    function resetAccountCostCenterStatuses() {
        $('select.journal_account_id').each(function() {
            let item = $(this).parents('.itemRow').first();
            let accId = $(this).val();
            let type = item.find('.debit-item').val() > 0 ? "debit" : "credit";
            if(accId.length) {
                let isThereCostCenters = $('[data-acc-id="'+accId+'"]').length;
                if(!isThereCostCenters) {
                    item.find('.assign-cost-center').removeClass('disabled');
                } else {
                    item.find('.assign-cost-center').addClass('disabled');
                }
              let data = calculateAccountSumAndReturnElms(accId, type);

              let sum = data.sum;
              $('[data-acc-id="'+accId+'"]').filter(".is-"+type).find('.cost_center_sum').text(sum);
              $('[data-acc-id="'+accId+'"]').filter(".is-"+type).find('.cost_center_type').text(typeTranslations[type]);
            }

            if((item.find('.debit-item').val().trim() != "" && item.find('.debit-item').val().trim() != 0) || (item.find('.credit-item').val().trim() != "" && item.find('.credit-item').val().trim() != 0)) {
                item.find('.assign-cost-center').removeClass('disabled');
            } else {
                item.find('.assign-cost-center').addClass('disabled');
            }
        });
    }

    resetAccountCostCenterStatuses();

    function calculateAccountSumAndReturnElms(accountId, type) {
        let currentAcc = [];
        let accList = [];
        $('select.journal_account_id').each(function() {
            let currentSelect = $(this).parents('.itemRow').first();
            let debit  = Number(currentSelect.find('.debit-item').val()) || 0;
            let credit = Number(currentSelect.find('.credit-item').val()) || 0;
            let currentType = debit > 0 ? "debit" : "credit";
            if(type == currentType) {
                if($(this).val() == accountId) {
                currentAcc.push(Math.max(debit, credit));
                accList.push($(this));
            }
            }
        });

        let sum = currentAcc.reduce((a, b) => a + b, 0);
        return {
            sum,
            elms : accList
        };
    }

    function recalculateCostCenters() {
        let accounts = {};
        let accList = [];
        $('select.journal_account_id').each(function() {
            let item = $(this).parents('.itemRow').first();
            let accId = $(this).val();
            let debit  = Number(item.find('.debit-item').val()) || 0;
            let credit = Number(item.find('.credit-item').val()) || 0;
            let currentType = debit > 0 ? "debit" : "credit";
            let numberList = {debit,credit};
            if(!accounts[accId]) {
                accounts[accId] = {};
            }
            if(!accounts[accId][currentType]) {
                accounts[accId][currentType] = [];
            }
            if(debit || credit) {
              if(!accList.includes(accId)) {
                accList.push(accId);
              }
             let costCenter = $('[data-acc-id="'+accId+'"]').filter(".is-"+currentType);
             if(costCenter.length) {
                if(!accountsCostCenters.some(costCenter => costCenter.row === item.index())){
                    accountsCostCenters.push({
                        type: currentType,
                        row: item.index(),
                        accId,
                        index: costCenter.data('id')
                    });
                }

                $(this).parents('.itemRow').first().attr('data-cost-center-index', costCenter.attr('data-id'));
             }
             accounts[accId][currentType].push(numberList[currentType]);
            }

            });

            for(let acc in accounts) {
                for(let type in accounts[acc]) {
                    let costCenter = $('[data-acc-id="'+acc+'"]').filter(".is-"+type);
                    let index = costCenter.attr('data-id');
                    isThereCostCenters = costCenter.length;
                    let relatedAccounts = $('[data-cost-center-index="'+index+'"]');
                    if(isThereCostCenters) {
                        relatedAccounts.find('.assign-cost-center').addClass('disabled');
                        let sum = accounts[acc][type].reduce((a, b) => a + b, 0);
                        costCenter.find('.cost_center_sum').text(sum);
                        costCenter.find('.cost_center_type').text(typeTranslations[type]);
                    } else {
                        relatedAccounts.find('.assign-cost-center').removeClass('disabled');
                    }
                }
            }

            $('#cost-centers-tables').find('.table-responsive').each(function() {
                if(!accList.includes($(this).attr('data-acc-id'))) {
                    $(this).remove();
                }
            });

    }

    $('body').on('change','.debit-item, .credit-item', function() {
        resetAccountCostCenterStatuses();
        let container = $(this).parents('.itemRow').first();
        let type = $(this).hasClass('debit-item') ? "debit" : "credit";
        if(accountsCostCenters.some(costCenter => costCenter.row === container.index() && type != costCenter.type)){
            let costCenter = accountsCostCenters.filter(costCenter => costCenter.row === container.index())[0];
            if(!$('[data-acc-id="'+costCenter.accId+'"]').filter(".is-"+type).length) {
              $('[data-acc-id="'+costCenter.accId+'"]').filter(".is-"+costCenter.type).removeClass("is-"+costCenter.type).addClass("is-"+type);
            } else {
                let id = $('[data-acc-id="'+costCenter.accId+'"]').filter(".is-"+costCenter.type).attr('data-id');
                let count = $('[data-cost-center-index="'+id+'"]').length;
                if(count < 2) {
                    $('[data-acc-id="'+costCenter.accId+'"]').filter(".is-"+costCenter.type).remove();
                }
            }
        }
        setTimeout(function() {
            recalculateCostCenters();
        });

        return false;
    });

    //old implementation
    // $('body').on('change','.debit-item, .credit-item', function() {
    //     let container = $(this).parents('.itemRow').first();
    //     let getAccId = container.find('select.journal_account_id').val();
    //     let type = $(this).hasClass('debit-item') ? "debit" : "credit";
    //     let isThereCostCenters = $('[data-acc-id="'+getAccId+'"]').filter(".is-"+type).length;

    //     setTimeout(function() {
    //         let data = calculateAccountSumAndReturnElms(getAccId, type);
    //         let sum = data.sum;
    //         if(isThereCostCenters) {
    //         $('[data-acc-id="'+getAccId+'"]').filter(".is-"+type).find('.cost_center_sum').text(sum);
    //         $('[data-acc-id="'+getAccId+'"]').filter(".is-"+type).find('.cost_center_type').text(typeTranslations[type]);
    //         //Recalculate prices
    //         $('[data-acc-id="'+getAccId+'"]').filter(".is-"+type).find('.percentage').trigger('change');
    //         }
    //         resetAccountCostCenterStatuses();
    //     });

    //     return false;
    // });

    $(document).on('click', 'a.assign-cost-center', function(e) {
        let target = e.currentTarget;
        $(target).addClass('disabled');
        let targetIndex = $(target).attr('data-id');
        let targetRow = $(target).closest('tr.itemRow');
        let targetSelectTag = $(targetRow).find('select#JournalTransaction'+targetIndex+'JournalAccountId');
        let titleText = $(targetSelectTag).find('option:selected').text();
        let accId = $(targetSelectTag).val();
        let type = $(target).parents('tr').find('.debit-item').val() > 0 ? "debit" : "credit";
        let isAccRepeated = $('[data-acc-id="'+accId+'"]').filter(".is-"+type).length;
        let obj = $(this);
        if(!isAccRepeated) {
            drawTable(obj, targetIndex, titleText, accId, 0, isAccRepeated, type);
        } else {
            obj.parents('.itemRow').first().attr('data-cost-center-index', targetIndex);
        }
        recalculateCostCenters();
        // data.elms.forEach(function(item) {
        //       item.parents('.itemRow').find('.assign-cost-center').addClass('disabled');
        // });
        initAdvancedSearch('.cost_center_advanced_id.advanced-selectpicker.selectpicker.with-ajax', false, "<?php echo Router::url(['controller'=> 'cost_centers','action'=>'json_find', '?' => ['parent_type' => $_GET['parent_type']]]); ?>");
    });

    $(document).on('click', 'a#AddCostItem', function(e) {
        let target = e.currentTarget;
        let targetIndex = $(target).attr('data-id') || '';
        let totalCostItem =  $('#cost-center-wrapper-'+ targetIndex).find('tr.itemRow').length;
        let type = $('#cost-center-wrapper-'+ targetIndex).hasClass('is-debit') ? "debit" : "credit";
        drawCostCenterRow(targetIndex, $('#cost-center-wrapper-'+ targetIndex).attr('data-acc-id'), totalCostItem, type);
        initAdvancedSearch('.cost_center_advanced_id.advanced-selectpicker.selectpicker.with-ajax', false, "<?php echo Router::url(['controller'=> 'cost_centers','action'=>'json_find', '?' => ['parent_type' => $_GET['parent_type']]]); ?>");

    });


    // remove items from cost center table row
    $(document).on('click', '#cost-centers-tables .removeItem', function() {
        let targetRow = $(this).closest('tr.itemRow');
        let targetTable = $(this).closest('div.invoice-items');
        let targetTableId = $(targetTable).attr('data-id');

        if( $(targetTable).find('tr.itemRow').length != 1) {
            $(targetRow).remove();
            calculateTotal(targetTableId);
            $(targetTable).find('tr.itemRow').each(function(i, ele) {
                $(ele).attr('id', 'cost-center-rows-' + i)
            })
        }
    });

    // remove added cost center table when click on remove-cost-center-table Btn
    $(document).on('click', '#cost-centers-tables .remove-cost-center-table', function() {
        let targetTable = $(this).closest('.table-responsive');
        let targetTableId = $(this).closest('.table-responsive').attr('data-id');
        let tableCostCenterBtn = $('tr[data-id=account-'+targetTableId+']').find('.assign-cost-center');
        $(tableCostCenterBtn).removeClass('disabled')
        $(targetTable).remove();
    });

    // remove add cost center table when click on removeItem on originTable [#listing_table]
    $(document).on('click', '#listing_table .removeItem', function() {
        setTimeout(function() {
            recalculateCostCenters();
        });
    });

    $(document).ready(function() {
        // change on select then updated Table title
        $(document).on('change','select[id*=JournalTransaction]', function() {
                let optionValue = $(this).find('option:selected').text();
                let targetRow = $(this).closest('tr.itemRow');
                let targetRowId = targetRow.attr('data-center-id');
                let targetTable = $('#cost-center-wrapper-' + targetRowId);
                let accId = $(this).val();
                let costCenter = $('[data-acc-id="'+accId+'"]').length;
                if(optionValue != 'Default Account') {
                    if(costCenter && costCenter.length > 1 && $("[value="+accId+"]").length > 1 && $(this).val() != $(this).attr('data-record-value')) {
                        $(targetTable).fadeOut(function() { $(this).remove();});
                    } else {
                        $(targetTable).find('.cost-center-wrapper-title').text(optionValue);
                        $(targetTable).attr('data-acc-id', accId);
                    }

                }
                let data = calculateAccountSumAndReturnElms(accId);
                let sum = data.sum;
                $('[data-acc-id="'+accId+'"]').find('.cost_center_sum').text(sum);

                $(this).attr('data-record-value', accId);

                resetAccountCostCenterStatuses();


            });

    });

    // change on debit or credit then updated Table title
    function updatedRowsValues() {
        let debitValue = $(this).val();
        let targetRowId = $(this).closest('tr.itemRow').attr('data-center-id');
        let targetTable = $('#cost-center-wrapper-' + targetRowId);
        let allPercentageInputs = $(targetTable).find('.percentage');
        let allAmountInputs = $(targetTable).find('.cost-center-amount');
        let totalPercentEle = $(targetTable).find('#total-percent');
        let totalAmountEle  = $(targetTable).find('#total-amount');
        let calcTotalAmount = 0;
        $(allPercentageInputs).each(function(i, input) {
            let inputValue = $(input).val();
            if(inputValue != "") {
                let amountInput = $(input).closest('tr.itemRow').find('.cost-center-amount');
                let calcValue = (inputValue * debitValue) / 100;
                $(amountInput).val(calcValue);
            }
        });
        $(allAmountInputs).each(function(i, input) {
            calcTotalAmount += Number($(input).val())
        });
        $(totalAmountEle).text(calcTotalAmount.toFixed(2));
    }
    $(document).on('change','.debit-item', updatedRowsValues);
    $(document).on('change','.credit-item', updatedRowsValues);

    // Update height of TextArea Description
    // textarea = document.querySelector("#body");
    // textarea.addEventListener('input', autoResize, false);



</script>
<script src="<?= CDN_URL ?>IzamV1/FormsV1/Components/File.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

<script>
    let displayTaxes = false;
    <?php if($display_taxes) { ?>
    displayTaxes = true;
    let taxesValues = JSON.parse('<?= json_encode($taxesPercentages) ?>');
    let taxNames = JSON.parse('<?= addslashes(json_encode($taxesList)) ?>');
    let includedTaxes = JSON.parse('<?= json_encode($includedTaxes) ?>');
        $('body').on('change', '.tax_id', function() {
            // let taxPercentage = 0;
            // let taxDebit = 0;
            // let taxCredit = 0;
            // let debit = $(this).closest('tr.itemRow').find('.debit-item').val();
            // let credit = $(this).closest('tr.itemRow').find('.credit-item').val();
            // if($(this).val() && taxesValues[$(this).val()]) {
            //     taxPercentage = taxesValues[$(this).val()];
            // }
            calc_total_credit();
            calc_total_debit();
        })
    function calculateTotalTaxCreditAndDebit() {
        $('.items-subtotal-items').hide();
        let totalTaxCredit = 0;
        let totalTaxDebit = 0;
        const taxesTotals = {};
        $('.tax_id').each(function(i, ele) {
            let taxPercentage = 0;
            let taxDebit = 0;
            let taxCredit = 0;
            const taxId = $(ele).val();
            if(!taxId || taxNames[taxId] == undefined) {
                return;
            }
            let debit = $(ele).closest('tr.itemRow').find('.debit-item').val();
            let credit = $(ele).closest('tr.itemRow').find('.credit-item').val();
            if(taxId && taxesValues[taxId]) {
                taxPercentage = parseFloat(taxesValues[taxId]);
            }
            const isIncluded = includedTaxes[taxId] == 1;
            if(!(taxId in taxesTotals)) {
                taxesTotals[taxId] = {debit: 0, credit: 0, name: taxNames[taxId], percentage: taxPercentage};
            }

            if(debit) {
                if(isIncluded) {
                    taxDebit = (debit * taxPercentage) / (100 + taxPercentage);
                } else {
                    taxDebit = (debit * taxPercentage) / 100;
                }
            }
            if(credit) {
                if(isIncluded) {
                    taxCredit = (credit * taxPercentage) / (100 + taxPercentage);
                } else {
                    taxCredit = (credit * taxPercentage) / 100;
                }
            }
            totalTaxCredit += Number(taxCredit);
            totalTaxDebit += Number(taxDebit);
            taxesTotals[taxId]['debit'] += taxDebit;
            taxesTotals[taxId]['credit'] += taxCredit;

        });
        $('.tax-row').remove();
        for(taxId in taxesTotals) {
            $('.items-subtotal-items').show();
            const taxData = taxesTotals[taxId];
            const formattedDebit = round(taxData.debit,2);
            const formattedCredit = round(taxData.credit,2);
            const taxRowHtml = `<tr id="Tax${taxId}Row" class="fix-clear movable notEditable tax-row notEditable"><td colspan="3" class="tspan new-row-cell add-row-btn-wrapper"></td><td class="text-left text-nowrap"><strong>${taxData.name} (${taxData.percentage}%)</strong></td><td class="text-left tax-debit">${formattedDebit}</td><td class="text-left tax-credit">${formattedCredit}</td><td class=" sort-cell" id="" style=" width: 47px;"></td></tr>`;
            $(taxRowHtml).insertAfter('.items-subtotal');
        }

    }
    <?php } ?>

    let journalDescription = document.querySelector('.journalDescription');
    journalDescription.addEventListener('input', autoResize, false);
    function autoResize() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    }
    if(journalDescription.scrollHeight > 45) {
        journalDescription.style.height = journalDescription.scrollHeight + 'px'
    }
    $(document).ready(function() {
            $('[data-izam1-forms1-file-picker]').each(function(i, element) {
                var options = $(element).data('izam1-forms1-file-picker');
                if (options && options.hasOwnProperty('init') && options.init === false) {
                    return true;
                }
                try {
                    new IzamV1.FormsV1.File(element, options);
                } catch (error) {
                    console.log(error);
                }
            });

        });

        $('.journals-table').on('keydown', function(event) {
            if (document.activeElement.nodeName == "TEXTAREA"){
                return;
            }
            if (event.key === 'Enter') {
                if (document.activeElement.nodeName == "INPUT" && document.activeElement.type=="text" ){
                    event.preventDefault(); // Prevent the Enter key from submit form
                }
            }
         });
</script>
<script type="text/javascript" src="/js/jjlc.min.js"></script>
<script type="text/javascript" src="/js/journals_add.js?v=<?= JAVASCRIPT_VERSION ?>""></script>
<script src="<?= '/v2/js/helpers/utils.js?v=' . JAVASCRIPT_VERSION ?>"></script>

<script>



//this fields is used in dynamic_add-remove-rows so it doesnt remove certian fields when creating new rows


$(function () {
 if (typeof initAdvancedSearch === "function") {
    initAdvancedSearch('.cost_center_advanced_id.advanced-selectpicker.selectpicker.with-ajax', false, "<?php echo Router::url(['controller'=> 'cost_centers','action'=>'json_find', '?' => ['parent_type' => $_GET['parent_type']]]); ?>");
}
})





</script>

<?php if (isset($_GET['from_matching'])) {
    echo $html->css(['cal-css/jquery.calendars.picker'], null, []);
    ?>
    <style>
        .calendars-month-header select, .calendars-month-header input {
            font-size: 12px;
        }
        .invoice-fields label {
            padding: 0 10px;
        }
        .info a {
            text-decoration: underline;
        }
        #body_box .pages-head-s2020 {
            padding: 0 !important;
            min-height: unset;
            border: 0;
        }
        #body_box .pages-head-s2020 .row-flex {
            margin: 0;
        }
        #body_box .pages-head-s2020 .container {
            padding: 0;
            max-width: unset;
            width: 100%;
        }
        #body_box .pages-head-s2020 .col-flex-sm-6 {
            padding: 0;
            flex: 0 0 100%;
            max-width: 100%;
        }
        #body_box .pages-head-s2020 .col-flex-sm-6 .btn {
            min-height: 55px;
            line-height: 1;
            margin: 0;
            font-size:20px;
            width: 50%;
        }
        #body_box .pages-head-s2020 .col-flex-sm-6 .btn.btn-success {
            background-color: #13b272;
        }
    </style>
<?php } ?>
<script>
    var detectIfSelectpickerIsInitialized = setInterval(function () {
        if ($('#listing_table > tbody > tr:last-child > td table tbody tr:last-child').find('.btn.dropdown-toggle.selectpicker.btn-default').length > 0 || $('#listing_table > tbody > tr:last-child > td table tbody tr:last-child').find('.journal_account_id').attr('disabled')) {
            $('.prescription-items').removeClass('d-none');
            $('#table-loading').remove();
            clearInterval(detectIfSelectpickerIsInitialized);
            detectIfSelectpickerIsInitialized = null; // Set to null after clearing
        }
    }, 2000);

    // Auto clear interval after 1 minute
    setTimeout(function() {
        if (detectIfSelectpickerIsInitialized) { // Check if interval still exists
            $('.prescription-items').removeClass('d-none');
            $('#table-loading').remove();
            clearInterval(detectIfSelectpickerIsInitialized);
            detectIfSelectpickerIsInitialized = null;
        }
    }, 60000);

</script>
<script>

function getUrlParams()
{
    var params = [], hash;
    var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
    for(var i = 0; i < hashes.length; i++)
    {
        hash = hashes[i].split('=');
        params.push(hash[0]);
        params[hash[0]] = hash[1];
    }
    return params;
}
var initial_form_state = $('#JournalOwnerAddForm').serialize();
$(window).on('beforeunload', function(e) {
    
    var form_state = $('#JournalOwnerAddForm').serialize();
    var new_form_state = form_state;
    var url = window.location.href;
    for (var i = 0; i < $("#listing_table .itemRow").length; i++) {
        var rowName=$('#listing_table .itemRow').eq(i).find('.item_name').attr('name');
            var itemRowNum = rowName.substring(18, rowName.indexOf(']', 18));
            var newRowName = "data%5BInvoiceItem%5D%5B" + i + "%5D";
            new_form_state = new_form_state.replaceAll("data%5BInvoiceItem%5D%5B" + itemRowNum + "%5D", newRowName);
        }

    var draftId = getUrlParams()["draftId"];
    var sendRequest = getUrlParams()["send"];
    var storageName = 'journals';
       if (initial_form_state != form_state && initial_form_state != "") {

            var invoicesDrafts = JJLC.getItem(storageName);
           if (draftId !=null) {
                var parsedValue = invoicesDrafts !=null ? JSON.parse(invoicesDrafts) : {};
                var tagsValues =   []
                $.each($("[data-tags-input]"), function(){
                    tagsValues.push($(this).val());
                });
                var invoiceItemsData= $('#JournalOwnerAddForm .invoice-items').clone();
                invoiceItemsData.find('div.journal_account_id').remove();
                invoiceItemsData.find('div.bootstrap-select').remove();
                invoiceItemsData.find('div.selectize-control').remove();
                parsedValue[draftId] = {
                    invoiceItems: $("#listing_table .itemRow").length,
                    invoiceItemsData: invoiceItemsData.html(),
                    tagsValues:tagsValues,
                    invoicesDraft: form_state,
                    totalInvoicesDebit:$('#dtotal').text(),
                    totalInvoicesCredit:$('#ctotal').text(),
                    currency: {
                        id:$("[name='data[Journal][currency_code]']").val(),
                        value: $("select[name='data[Journal][currency_code]'] optgroup option").text()
                    }
                }
                
                JJLC.setItem(storageName, JSON.stringify(parsedValue))
            }
           
           var message = __("You have unsaved changes on this page. Do you want to leave this page and discard your changes or stay on this page ?");
           e.returnValue = message; // Cross-browser compatibility (src: MDN)
           return message;
          
       }
   });
    </script>
