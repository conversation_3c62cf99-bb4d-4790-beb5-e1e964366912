<?php

use Izam\Aws\Aws;
use Izam\Daftra\Common\Utils\DistanceUnitUtil;
use Izam\Logging\Service\RollbarLogService;

include('partials/fileLayout.php');
echo $html->css(array('jquery.qtip.min','product_view_v'.CSS_VERSION.'.css', 'time-tracker.css?v=2', 'fontello.css'));
echo $html->css('timeline_v'.CSS_VERSION.'.css');
echo $html->css('bootstrap-multiselect.css');
echo $javascript->link(array('bootstrap-multiselect.js'));
echo $javascript->link(array('jqueryui','jquery.qtip.min'));
$html->css(array('jqueryui'), false, ['inline' => false]);
echo $javascript->link(array('magicsuggest-min.js'));
echo $html->css(array( 'magicsuggest-min.css'));
$owner = getAuthOwner();
$site = getCurrentSite();
$full_name = $owner['first_name'] . ' ' . $owner['last_name'];
$dateFormats = getDateFormats('std');
$dateFormat = $dateFormats[getCurrentSite('date_format')];
$sitename = $site['business_name'];
//dd($expense);

$can_clone = false ;
if ( $is_income && (check_permission (Add_New_Incomes))){
        $can_clone = true ;
}
if ( !$is_income && (check_permission (Add_New_Expenses))){
        $can_clone = true ;
}

?>
<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/show/show.min.css?v=".CSS_VERSION, null, []); ?>
<style>
     .img-box {
        width: 140px;
        height: 140px;
    }
    .img-full-size{
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    @media (max-width:1024px){
        .invoice-actions .btn {
            margin: 0px !important;
            border-color:rgba(255,255,255,0) !important;
            border-bottom: 1px solid #eee !important;
        }
        .dropdown-menu i ,.dropdown-menu span
        {
            font-size:14px !important;
        }
    }
</style>
<?php

$title = sprintf(__( $alias.' %s', true),'<span class="title_id_with_hash">'. '#' . $expense['Expense']['expense_number'].'</span>') ;

?>
<?php

if($filter_url){

    echo $this->element ('breadcrumbs' , ['no_head_class' => TRUE,'breadcrumbs' => [
        ['link' =>  Router::url (['controller' =>   ($is_income ? 'incomes' : 'expenses') , 'action'=>'index'] ) ,'title' =>__($alias,true) ],
        ['link' => Router::url ($filter_url ) ,'title' =>__('Search Results',true) ],
        ['link' => '#' ,'title' =>  __($alias, true).' #'.$expense['Expense']['expense_number']],
    ]]);
}else{
//    echo $this->element ('breadcrumbs' , ['breadcrumbs' => $_PageBreadCrumbs ]);

}

?>
<div class="pages-head fixed-div">
    <div class="container">
        <div class="row">
            <div class="col-md-7">
                    <h1 class="left no-margin">
                        <?php echo $title; ?>
                        <div class="status">
                            <?php

                            if ($expense['Expense']['status'] == 'draft') {
                            ?>
                            <span class="status-symble status-draft"><?php __('Draft'); ?></span>
                            <?php } else { ?>
                            <span class="status-symble status-2"><?php __('Issued'); ?></span>
                            <?php } ?>
                        </div>

                    </h1>
                    <div class="sub-headings" style="clear:both;">
                        <?php if ($this->params['prefix'] == 'owner') {

                            if ($this->params['prefix'] == 'owner'&&ifPluginActive(AccountingPlugin)&&check_permission(VIEW_ALL_JOURNALS)&&!empty($linked_journal)) {
                                echo $this->element('linked-journal',['journal'=>$linked_journal]);
                            }
                        }
                        ?>
                    </div>

                    <div class="top-actions m-t-xs">
                        <?php if($expenses_nav) echo $this->element('nav',array('id'=> $expense['Expense']['id'])) ; ?>

                    </div>
                    <div class="clear"></div>
                    <?php 	echo $this->element('tags_view',array('item_id' => $expense['Expense']['id'],'model_name' => 'Expense','get_ajax' => true)); ?>
            </div>
            <div class="col-md-5">


                        <?php if (((getAuthStaff('id') != 0 && $expense['Expense']['staff_id']) || getAuthStaff('id') == 0) && ($expense['Expense']['status'] == "issued") && check_permission($permissions['Draft']) ) { ?>
                            <a class="primary-btn btn btn-default btn-icn  responsive  pull-right mr-1"  href="<?php echo Router::url(array('controller'=> $is_income ? 'incomes':'expenses','action' => 'update_draft', $expense['Expense']["id"], 1)); ?>"><span class="fa fa-pencil-square" aria-hidden="true"></span> <?php __("Mark as Draft") ?></a>
                        <?php } else if ($expense['Expense']['status'] == "draft" &&
                            (
                                check_permission($permissions['Edit_Delete_all']) || (check_permission($permissions['Edit_delete_his_own']) && $expense['Expense']['staff_id'] == getAuthStaff() )

                            )
                        )
                        { ?>
                           <a class="primary-btn btn btn-success btn-icn  responsive  pull-right mr-1"  href="<?php echo Router::url(array('controller'=>  $is_income ? 'incomes':'expenses','action' => 'update_draft', $expense['Expense']["id"], 0)); ?>"><span class="fa fa-usd" aria-hidden="true"></span> <?php $is_income ?  __("Issue Income") :  __("Issue Expense") ?></a>
                        <? } ?>

            </div>
        </div>
    </div>
</div>
<div class="m-t-30"></div>

<div class="invoice-actions btn-group dropdown-btn-group">

        <a href="<?php echo Router::url(array('controller' => ($is_income ? 'incomes': 'expenses' ),'action' => 'edit', $expense['Expense']['id'])); ?>" class="btn btn-default btn-sm quick-action-btn">
            <span class="fa fa-pencil" aria-hidden="true"></span>
            <?php __("Edit") ?></a>
        <?php 
        if($can_clone) { ?>
            <a href="<?php echo Router::url(array('controller' => ($is_income ? 'incomes': 'expenses' ),'action' => 'add', $expense['Expense']['id'])); ?>" class="btn btn-default btn-sm quick-action-btn">
                <span class="fa fa-copy" aria-hidden="true"></span>
                <?php __("Clone") ?>
            </a>  
       <?php }  ?>    
        <a href="<?php echo Router::url(array('controller' => ($is_income ? 'incomes': 'expenses' ),'action' => 'delete', $expense['Expense']['id'])); ?>" class="btn btn-default btn-sm quick-action-btn">
            <span class="fa fa-trash-o" aria-hidden="true"></span>
            <?php __("Delete") ?></a>
    <?php if($has_templates): ?>
        <div class="btn-group">
            <button type="button"   class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Voucher', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu dropdown-menu-right" >
                <?php foreach($printableTemplates as $template) : ?>
                    <li>
                        <a href="<?php echo Router::url(array('controller'=>'printable_templates', 'action' => 'view',  $expense['Expense']['id'], $template['PrintableTemplate']['id'], $is_income ? 'income' : 'expense')) ?>" class=""> <?= $template['PrintableTemplate']['name']; ?></a>
                    </li>
                <?php endforeach; ?>
                <?php
                    echo draw_templates_list($view_templates, $expense['Expense']['id']);
                ?>
            </ul>
        </div>
    <?php endif; ?>
</div>
<div role="tabpanel" class="tabs-box box">

    <ul class="nav nav-tabs responsive">
        <li class="active" role="presentation"><a aria-controls="DetailsBlock" role="tab" data-toggle="tab" href="#detailsBlock" title="<?php __('Preview ' . $alias) ?>" id="ViewDetails"><span class="one-line"><?php __('Details') ?></span></a></li>

        <li class="" role="presentation"><a aria-controls="InvoiceBlock" role="tab" data-toggle="tab" href="#expenseBlick" title="<?php __('Preview ' . $alias) ?>" id="ViewInvoice"><span class="one-line"><?php __('Voucher') ?></span></a></li>
        <li role="presentation"><a aria-controls="TimelineBlock" onclick="reload_filter()" role="tab" data-toggle="tab" href="#TimelineBlock" title="<?php __('Timeline') ?>"><span class="one-line"><?php __('Activity Log') ?></span></a></li>
        <?php if($expense['RecurringExpense']['id']) { ?>
        <li role="presentation"><a aria-controls="RecurringBlock"  role="tab" data-toggle="tab" href="#RecurringBlock" title="<?php __('Recurring') ?>"><span class="one-line"><?php __('Recurring') ?> (<?php echo $expense['RecurringExpense']['active']=="1"?__('Active',true):__('Inactive',true); ?>)</span></a></li>
        <?php } ?>
    </ul>

    <!-- / tabs-buttons -->

    <div class="tab-content responsive">
        <?php if(!empty($expense['Expense']['recurring_expense_id'])){

            ?>
        <div class="tab-pane" id="RecurringBlock">
                <h2><?php __('Expenses Generated', true)?></h2>
                <div id="RecurringBlock__expenses">

                </div>
                <script>
                    $(function(){
                        $.get('<?= Router::url(['controller' => ($is_income ? 'incomes': 'expenses' ),'action' => 'index', '?' => ['recurring_expense_id' => $expense['Expense']['recurring_expense_id']] ]) ?>',function(data)
                        {
                            $('#RecurringBlock__expenses').html(data);
                        })
                    })
                </script>
        </div>
        <?php } ?>
        <div class="tab-pane" id="TimelineBlock">
            <script type="text/javascript">
                var timeline_url = "<? echo Router::url(array('action' => 'timeline', $expense['Expense']['id'])) ?>";
                var timeline_row_url = "<? echo Router::url(array('action' => 'timeline_row')) ?>";
            </script>
            <?php echo $this->element('timeline'); ?>
        </div>
        <div class="tab-pane active" id="detailsBlock">
            <div class="row">
                <div class="col-md-4 col-sm-4">
                    <table class="view-table">
                        <tr>
                            <td><strong><?php __('Code') ?>:</strong></td>
                            <td>
                                <?php

                                    echo $expense['Expense']['expense_number'];

                                ?>
                            </td>
                        </tr>
                        <tr>
                            <td><strong><?php __('Date') ?>:</strong></td>
                            <td>
                                <?php

                                echo format_date($expense['Expense']['date']);

                                ?>
                            </td>
                        </tr>
                        <?php if(!empty($expense['Client']['business_name'])){ ?>
                        <tr>
                            <td><strong><?php __('Client') ?>:</strong></td>
                            <td><?php echo $expense['Client']['business_name'] ?></td>
                        </tr>
                        <?php } ?>
                        <?php if(!empty($expense['Expense']['note'])) { ?>
                            <tr>
                                <td><strong><?php __('Description') ?>:</strong></td>
                                <td>
                                    <?php echo nl2br($expense['Expense']['note']) ?>
                                </td>
                            </tr>
                        <?php } ?>

                        <?php if(!empty($expense['Expense']['journal_account_id'])) { ?>
                         <tr>
                            <td><strong><?php __('Journal Account') ?>:</strong></td>
                            <td>
                                <?php if (!empty($expense['Expense']['accounts_data'])) { ?>
                                    <?php 
                                    $accounts = json_decode($expense['Expense']['accounts_data'],true);
                                    if (isset($accounts[0]) && !is_null($accounts[0]['journal_account_id'])) {
                                        foreach($accounts as $k => $account) { ?>
                                            <?php echo $account['journal_account']['JournalAccount']['name'] . '<br>'; ?>
                                        <?php } ?>
                                    <?php } else { ?>
                                        <?php echo $expense['JournalAccount']['name'] ?> 
                                    <?php } ?>
                                <?php } else { ?>
                                    <?php echo $expense['JournalAccount']['name'] ?> 
                                <?php } ?>
                            </td>
                        </tr>
                        <?php } ?>
                        <?php if(!$is_income && $expense['Supplier']['id']) { ?>
                            <tr>
                                <td><strong><?php __('Supplier') ?>:</strong></td>
                                <td> <?= $expense['Supplier']['business_name']?> <a style="text-decoration: underline" href="<?= Router::url(['controller' => 'suppliers', 'action' => 'view', $expense['Supplier']['id']]) ?>">#<?= $expense['Supplier']['supplier_number'] ?></a></td>
                            </tr>
                        <?php } ?>
                    </table>
                </div>
                <div class="col-md-4 col-sm-4">
                    <table class="view-table">
                        <tr>
                            <td><strong><?php __('Amount') ?>:</strong></td>
                            <td>
                               <?php echo format_price($expense['Expense']['amount'], $expense['Expense']['currency_code']); ?>
                            </td>
                        </tr>
                        <?php if($default_currency!=$expense['Expense']['currency_code']) {?>
                        <tr>
                            <td><strong><?php __('Currency') ?>:</strong></td>
                            <td>
                                <?php echo$expense['Expense']['currency_code'] ?>
                            </td>
                        </tr>
                        <?php } ?>

                        <?php if(!empty($expense['Expense']['tax1_name'])) { ?>
                        <tr>
                            <td><strong><?php echo $expense['Expense']['tax1_name'] ?>:</strong></td>
                            <td>
                                <?php  echo  format_price($expense['Expense']['tax1_amount'], $expense['Expense']['currency_code']); ?>
                            </td>
                        </tr>
                        <?php } ?>

                        <?php if(!empty($expense['Expense']['tax2_name'])) { ?>
                        <tr>
                            <td><strong><?php  echo  $expense['Expense']['tax2_name'] ?>:</strong></td>
                            <td>
                                <?php  echo   format_price($expense['Expense']['tax2_amount'], $expense['Expense']['currency_code']); ?>
                            </td>
                        </tr>
                        <?php } ?>
                        <?php if(!empty($expense['Treasury'])) { ?>
                            <tr>
                                <td><strong><?php __('Treasury') ?>:</strong></td>
                                <td> <?= $expense['Treasury']['name'] ?> </td>
                            </tr>
                        <?php } ?>
                        <?php if(isset($expense['Expense']['source_type'])) { ?>
                        <tr>
                            <td><strong><?php __('Source') ?>:</strong></td>
                            <td>
                                <a href="<?= $expenseSource->getViewLink() ?>" target="_blank">
                                    <?php echo Expense::getSourceTypeOptions($expense['Expense']['source_type']) .' #'. $expense['Expense']['source_id']?>
                                </a>
                            </td>
                        </tr>
                        <?php } ?>
                    </table>

                </div>

                <div class="col-md-12 col-sm-12">
                    <table class="view-table">
                        <?php if (!empty($expense['Attachments']) || !empty($expense['Expense']['file'])) { ?>
                            <tr>
                                <!-- <td> -->
                                    <div style="display: block; margin-bottom:10px;"><strong><?php __('Attachments') ?></strong>:</div>
                                <!-- </td> -->
                                <!-- <td> -->
                                <?php if (!empty($expense['Expense']['file'])) { ?>
                                    <div class="attachment-file">
                                        <?php echo $this->element('ajaxFileView', array('view_only' => true, 'file' => $expense['Expense']['file'], 'file_settings' => $file_settings['file'])); ?>
                                    </div>
                                <?php 
                                    }

                                    $aws = new Aws;
                                    // used to load attachments uploaded to s3 . 
                                    if (!empty($expense['Attachments'])) {
                                        foreach ($expense['Attachments'] as $key => $attach) {
                                            $attach['site_id'] = getCurrentSite('id'); 
                                            $url = "/v2/owner/entity/files/preview/" . $attach['id'];
                                        ?>

                                            <div class="attachment-file">
                                                <a target="_blank" title="<?= $file['value']['name'] ?>" href="<?= $url ?>" download="">
                                                    <?php switch ($attach['mime_type']) {
                                                        case 'png':
                                                        case 'jpeg':
                                                        case 'jpg':
                                                        case 'gif':
                                                        case 'svg': ?>
                                                            <div class="img-box">
                                                             <img  class="img-full-size"  id="photome" src="<?= $url ?>?w=140&amp;h=140&amp;c=1">
                                                            </div>
                                                            <?php
                                                            break;
                                                        
                                                        default:
                                                        
                                                            switch ($attach['mime_type']) {
                                                                case "xls":
                                                                case "xlsx":
                                                                    $icon = "mdi mdi-file-excel";
                                                                    break;
                                                                case "doc":
                                                                case "docx":
                                                                    $icon = "mdi mdi-file-word";
                                                                    break;
                                                                case "pdf":
                                                                    $icon = "mdi mdi-file-pdf-box";
                                                                    break;
                                                                case "jpg":
                                                                case "jpeg":
                                                                case "png":
                                                                case "gif":
                                                                    $icon = "mdi mdi-file-image";
                                                                    break;
                                                                case "txt":
                                                                case "csv":
                                                                    $icon = "mdi mdi-file-document";
                                                                    break;
                                                                case "zip":
                                                                case "rar":
                                                                    $icon = "mdi mdi-file-key";
                                                                    break;
                                                            
                                                                default:
                                                                    $icon =  "mdi mdi-file";
                                                                    break;
                                                        }
                                                        ?>
                                                            <div style="height: 140px;" class="d-flex align-items-center justify-content-center"><i class="<?= $icon; ?>"></i></div>
                                                            <?php
                                                            break;
                                                    } ?>
                                                    
                                                    <span><?= $file['value']['name'] ?></span>
                                                </a>
                                                <div class="attach-hov">
                                                    <?php
                                                        RollbarLogService::logTempFilesToRollbar($attach['is_temp'],$attach);
                                                    ?>
                                                    <a target="_blank" href="<?= $aws->getProxyUrl($attach['path']) ?>" download="" title="">
                                                        <div class="ofh" style="overflow:hidden;">
                                                            <h4><?= $attach['name'] ?></h4>
                                                            <span><?= round($attach['file_size'] / 1024, 2); ?> kb</span>
                                                        </div>
                                                        <div class="clearfix"></div>
                                                    </a>
                                                    <div class="text-center btt" style="">
                                                        <a target="_blank" href="<?= $url ?>" download="" title="Download" class="btn btn-sm btn-primary"><i class="fa fa-download"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                    <?php }
                                    } ?>
                                <!-- </td> -->
                            </tr>
                        <?php } ?>
                    </table>
                </div>
                <div class="col-md-12 col-sm-12">
                    <?php

                    $accounts = json_decode($expense['Expense']['accounts_data'],true);
                    if(!empty($accounts) && !empty($accounts[0]['journal_account_id'])) {
                        ?>
                    <table class="list-table table table-hover tableClass">
                        <thead>
                        <tr>
                            <th class=""> <?php __('Name') ?> </th>
                            <th class=""> <?php __('Description') ?>  </th>
                            <th class=""> <?php __('Amount') ?>  </th>
                            <th class=""> <?php __('Tax') ?>  </th>
                            <th class=""> <?php __('Tax Amount') ?>  </th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        foreach ($accounts as $account) {
                            $tax = current(array_filter($taxes, function ($item) use($account) {

                                return $item['id'] == $account['tax_id'];
                            }));
                            ?>
                            <tr>
                                <td><?php echo "#".$account['journal_account']['JournalAccount']['code'] .' '. $account['journal_account']['JournalAccount']['name'] ?></td>
                                <td><?php echo $account['description']; ?></td>
                                <td><?php echo format_price_simple($account['amount'],$expense['Expenses']['currency_code']); ?></td>
                                <td><?php echo $tax ?  " {$tax['name']} ({$account['percent']}%)" : ""; ?></td>
                                <td><?php echo format_price_simple($account['tax_value'],$expense['Expenses']['currency_code']); ?></td>
                            </tr>
                        <?php } ?>
                        </tbody>
                        <tfoot>
                        </tfoot>
                    </table>
                    <?php } ?>
                </div>
               
                <div class="col-md-12 col-sm-12">
                    <?php  if($expense['Expense']['is_mileage']) {  ?>
                    <table class="list-table table table-hover tableClass">
                        <thead>
                        <tr>
                            <th class="">  Vehicle </th>
                            <th class="">  Distance  </th>
                            <th class="">  Rate </th>
                        </tr>
                        </thead>
                        <tbody>
                        
                            <tr>
                                <td>
                                    <?php 
                                
                                    if(isset($expense['Vehicle']['File'])){?>
                                
                                    <img class="thumb thumb-4xl" src="<?php echo \Izam\Aws\Aws::getPermanentUrl($expense['Vehicle']['File']['path']); ?>" />
                                <?php }
                                echo  ($expense['Vehicle']['id'])?("<a href='/v2/owner/entity/vehicle/".$expense['Vehicle']['id']."/show' target='_blank' >"."#".$expense['Vehicle']['id'] .'</a> '. $expense['Vehicle']['name']): "-" ;
                                ?>
                            </td>
                                <td><?php echo $expense['Expense']['distance']." ". DistanceUnitUtil::getUnit($expense['Expense']['distance_unit']) ; ?></td>
                                <td><?php echo ($expense['Expense']['rate']); ?></td>
                            </tr>
                        </tbody>
                        <tfoot>
                        </tfoot>
                    </table>
                    <?php } ?>
                </div>
            </div>

            <?php echo $this->element ('custom_forms/view_fields_form2') ;?>
            <?php if(!empty($expense['RecurringExpense']['id']) && false){ ?>
                <div class="input-fields ">
                    <h3 class="head-bar theme-color-a"><span class="details-info"><?= __('Recurring Expense') ?></span></h3>
                    <div class="row">
                        <div class="has-many-item">

                            <div class="col-sm-12 ">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="view-table">
                                    <tbody>
                                    <tr>
                                        <td><strong><?php __('Status') ?></strong></td>
                                        <td>
                                            <?php
                                            if($expense['RecurringExpense']['active'] && !$expense['RecurringExpense']['is_ended']){
                                                $statusText = __('Active', true);
                                            }else if($expense['RecurringExpense']['is_ended'])
                                            {
                                                $statusText = __('Expired', true);
                                            }else if($expense['RecurringExpense']['active'] && !$expense['RecurringExpense']['is_ended'])
                                            {
                                                $statusText = __('Not Active', true);
                                            }
                                            echo $statusText;

                                            ?>
                                        </td>
                                        <td>
                                            <strong><?php __('Id') ?></strong>
                                        </td>
                                        <td>
                                            <?= $expense['RecurringExpense']['id'] ?>
                                        </td>
                                        <td>
                                            <strong><?php __('Every') ?> </strong>
                                        </td>
                                        <td>
                                            <?=  $expense['RecurringExpense']['unit_count'].' ' . __($expense['RecurringExpense']['period_unit'],true) ?>
                                        </td>
                                        <td>
                                            <strong><?php __('First Generated') ?></strong>
                                        </td>
                                        <td>
                                            <a href="<?= Router::url([$firstRecurring['Expense']['id']]) ?>" >#<?php echo $firstRecurring['Expense']['expense_number'] .' ' .format_date($firstRecurring['Expense']['created']) ?></a>
                                        </td>
                                        <td>
                                            <strong><?php __('Last Generated') ?></strong>
                                        </td>
                                        <td>
                                            <a href="<?= Router::url([$expense['RecurringExpense']['last_generated_id']]) ?>" >#<?php echo $lastRecurring['Expense']['expense_number'] .' ' .format_date($expense['RecurringExpense']['last_generated_date']) ?></a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?= __('Generated Count') ?></td></strong>
                                        <td><?= $recurringCount ?></td>
                                        <td><strong><?= __('Next to be generated') ?></td></strong>
                                        <td><?= format_date(date('Y-m-d',strtotime($expense['RecurringExpense']['last_generated_date'] ." +".$expense['RecurringExpense']['unit_count'] . ' '. $expense['RecurringExpense']['period_unit']))); ?></td>
                                    </tr>
                                    </tbody></table>
                            </div>


                        </div>


                    </div>
                </div>
            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <table class="view-table">



                    </table>
                </div>
            </div>
            <?php } ?>
        </div>
        <div class="tab-pane " id="expenseBlick">
            <div class="preview-expense invoice-Block">
             <div class="notifcation-loader"><div class="inner-loader"></div></div>
<!--                    <div class="invoice-template">
                        <iframe frameBorder="0" src="<?php echo Router::url(array('controller' => 'printable_templates','action' => 'view', $expense['Expense']['id'], $default_template['PrintableTemplate']['id'], ($is_income ? 'income' : 'expense'))); ?><?php if($invoice_print_method==settings::OPTION_PRINT_PDF){?>.pdf?inline=true <?php } ?>" id="InvoicePreview" name="InvoicePreview" width="100%" height="800"></iframe>
                    </div>-->

            </div>
			<script>
				$(function()
					{
                        template_url = "<?php echo Router::url(array('controller' => 'printable_templates','action' => 'view', $expense['Expense']['id'], $default_template['PrintableTemplate']['id'], ($is_income ? 'income' : 'expense'))); ?>";
							$.ajax({method: 'GET',url: template_url,success: function (data)
								{
									$('.invoice-Block').html(data);
								}});
					})
			</script>
        </div>

    </div>

</div>



<?php
echo $javascript->link(array('jquery.lightbox_me', 'invoices/view-invoice_v3.js?v=2'));
$html->css(array('owner-page', 'tabs'), false, ['inline' => false]);
$this->set('hasTabs', true);
?>
<script type="text/javascript">


    var item_id = <?php echo $expense['Expense']['id']?>;

    var item_type = 2;

    $(document).ready(function() {


        $('.clickable').each(function () {
            $(this).attr('onclick', '');

        });

    });




    $('#InvoicePreview').load(function() {
<?php if (empty($this->params['named']['print'])) { ?>
            $('#InvoicePreview').contents().find('body').css({background: '#bcc5cd', 'padding': '20px'});
<?php } else { ?>
            print_inv();
<?php } ?>
    });
    var print_log_url = "<? echo Router::url(array('action' => 'print_log', $this->params['pass'][0])) ?>";

</script>
<?php echo $javascript->link(array('invoice-view_v4.js?v=2.1')); ?>
<script>
    <?php $jsonExpense = $expense; unset($jsonExpense['Expense']['accounts_data']) ?>
    savedRecord = JSON.parse('<?= json_encode($jsonExpense['Expense'], JSON_UNESCAPED_SLASHES) ?>');
</script>
