 <?php

use Izam\Daftra\Common\Utils\PluginUtil;

echo $html->css('nav-tabs', false);
echo $html->css('bootstrap-multiselect.css', null, []);
echo $javascript->link(array('main', 'nicEdit', 'humanize', 'bootstrap-multiselect.js'));

?>

 <div class="nav-wrapper side-nav-content">
     <div class="compat">
         <div class="my-10 vstack gap-10">

             <?php echo $form->create('Site', array('id'=> "settingsForm",   'type' => 'file','url' => '/owner/invoices/estimates_settings' ,  'controller' => 'invoices', 'action' => 'settings')); ?>
             <header class="pages-head page-fixed-start bg-none p-0" data-page-start="true">
                 <div class="page-head add-page-head">
                     <div class="container container-full">
                         <div class="page-head-row">
                             <div class="start">
                                 <!-- left area -->
                                 <div class="hstack">

                                 </div>
                                 <!-- /left area -->
                             </div>
                             <div class="center">
                                 <!-- center area -->
                                 <div class="hstack">

                                 </div>
                                 <!-- /center area -->
                             </div>
                             <div class="end">
                                 <!-- right area -->
                                 <div class="hstack">

                                     <a href="#" class="btn btn-secondary btn-responsive-icon">
                                         <i class="mdi mdi-close-thick me-xl-4"></i>
                                         <span><?php __("Cancel") ?></span>
                                     </a>

                                     <button type="submit" class="btn btn-success btn-responsive-icon add-new-btn">
                                         <i class="mdi mdi-content-save me-xl-4"></i>
                                         <span><?php __("Save") ?></span>
                                     </button>

                                 </div>
                                 <!-- /right area -->
                             </div>
                         </div>
                     </div>
                 </div>
             </header>

             <div class="card mb-10">
                 <div class="card-body p-25">

                     <div class="setting-section" id="section1">
                         <h3 class="fs-13 text-black mb-4 p-0"><?= __('Estimate Configuration', true) ?></h3>
                         <p><?= __('Configure how estimates are created, managed, and converted into invoices.', true) ?>
                         </p>
                         <div class="">



                             <div class="form-group mt-md-15 mb-0">
                                 <label class="form-label fs-8 fw-medium text-black"
                                     for="SiteDisableEstimateModule"><?= __('Estimates Module', true) ?></label>
                                 <label
                                     class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                                     <?php
                                    echo $form->input('disable_estimate_module', array('div' => false, 'class' => 'form-check-input', 'label' => false, 'type' => 'checkbox'));
                                    ?>
                                     <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
                                 </label>
                                 <div class="form-text fs-7 mt-5">
                                     <?= __('Enable the Estimates Module to allow users to create, share, manage, and convert estimates into invoices.', true); ?>
                                 </div>
                             </div>

                             <!-- <div class="form-group">
                                <?php
                                //                    echo $form->input('sold_item_type', array_merge($form_data['sold_item_type'], array('type' => 'select', 'div' => 'col-md-6 col-sm-12 m-b-md', 'class' => 'form-control', 'label' => __('You Sell', true))));
                                echo $form->input('disable_estimate_module', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Disable Estimates Module', true)));
                                ?>
                                <div class="clearfix"></div>
                            </div> -->

                             <div class="form-group mt-md-15 mb-0">
                                 <label class="form-label fs-8 fw-medium text-black" for="SiteNextInvoiceNumber">
                                     <?= __('Next Auto-Generated Estimate Number', true) ?>
                                 </label>
                                 <div class="d-flex gap-5">
                                     <div class="flex-grow-1">
                                         <?php
                                        if (empty($this->data['Site']['next_estimate_number'])) {
                                            $value = '000001';
                                        } else {
                                            $value = $this->data['Site']['next_estimate_number'];
                                        }
                                        echo $form->input('next_estimate_number', array('div' => false, 'type' => 'number', 'class' => 'INPUT form-control required', 'value' => $value, 'readonly' => true, 'disabled' => true, 'label' => false));
                                        ?>
                                     </div>
                                     <div class="flex-shrink-0">

                                         <?php echo '<button type="button" tabindex="-1" class="btn btn-secondary text-decoration-none" id="autoNumberSettingsBtn" href="#" title="Auto Number Settings"><i class="fa fa-cog me-4"></i>  ' . __('Auto Number Settings', true) . ' </button>'; ?>
                                     </div>
                                 </div>
                                 <div class="form-text fs-7 mt-5">
                                     <?= __('The number the system will assign to the next estimate.', true) ?>
                                 </div>
                             </div>


                             <?php if(ifPluginActive(FollowupPlugin)){ ?>
                             <div class="form-group mt-md-15 mb-0">
                                 <label class="form-label fs-8 fw-medium text-black" for="SiteEnableInvoiceStatus">
                                     <?= __('Estimate Custom Statuses', true) ?>
                                 </label>
                                 <div class="d-flex gap-5">
                                     <div class="flex-grow-1">
                                         <label
                                             class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                                             <?php
                                                echo $form->input('enable_estimate_status', array('type' => 'checkbox', 'div' => false, 'class' => 'form-check-input ', 'label' => false,));
                                                ?>
                                             <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
                                         </label>
                                     </div>
                                     <div class="flex-shrink-0">
                                         <?php //echo '<span id="enable_invoice_link"' . (!settings::getValue(InvoicesPlugin, 'enable_estimate_status') ? "style='display:none;'" : "") . '> <button type="button" id="statusIdBtn" status_id="' . Post::INVOICE_TYPE . '" class="btn btn-secondary text-decoration-none" ><i class="fa fa fa-cog me-4"></i>' . __("Manage Custom Statuses", true) . '</button></span>'; ?>
                                     </div>
                                 </div>
                                 <div class="form-text fs-7 mt-5">
                                     <?= __('Create custom estimate statuses that suit your workflow, such as “Pending Response” or “Follow-up Needed”, and assign them to estimate. Use custom statuses to filter and search estimates.', true) ?>
                                 </div>
                             </div>
                             <?php } ?>



                             <div class="form-group mt-md-15 mb-0">
                                 <label class="form-label fs-8 fw-medium text-black"
                                     for="SiteDisableEstimateModule"><?= __('Apply Offers to Estimates', true) ?></label>
                                 <label
                                     class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                                
                                    <?php
                                    echo $form->input('apply_offers_on_estimates', ['checked'=>!empty($this->data['Site']['apply_offers_on_estimates']), 'type' => 'checkbox', 'label'=>false,'class' => 'form-check-input', 'div' => false]);
                                    ?>
                                     <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
                                 </label>
                                 <div class="form-text fs-7 mt-5">
                                     <?= __('Automatically apply available sales offers to estimates so clients can see potential savings early.Note: Offers may still apply if the estimate is converted to an invoice after the offer expires.', true); ?>
                                 </div>
                             </div>

                             <!-- <div class="form-group">
                                <?php
                                echo $form->input('apply_offers_on_estimates', ['checked'=>!empty($this->data['Site']['apply_offers_on_estimates']), 'type' => 'checkbox', 'label'=>__('Apply Offers For Estimates',true),'class' => 'form-x1', 'div' => 'clip-check check-info']);
                                ?>
                                <div class="clearfix"></div>
                            </div> -->





                         </div>
                     </div>
                 </div>
             </div>

             <?php echo $form->end(); ?>
         </div>
     </div>
 </div>


 <?php include('views/invoices/partials/settings/settings-estimates-sidebar.ctp'); ?>

 <script>
$(document).ready(function() {

    $(document).on('click', '#autoNumberSettingsBtn', function() {
        IzamModal.closeModals();

        // Build URL with multiple query params
        const srcUrl =
            '<?= Router::url(['controller' => 'settings', 'action' => 'numbering', AutoNumber::TYPE_ESTIMATE]) ?>?box=1&frameModal=1';

        IzamModal.addHtmlModal(
            '<iframe style="width:100%; height:100%; border:none;" ' +
            'src="' + srcUrl + '"></iframe>',
            '',
            ''
        );
    });
});
 </script>