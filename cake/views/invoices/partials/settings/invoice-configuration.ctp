<div class="setting-section" id="section1">
    <h3 class="fs-13 text-black mb-4 p-0"><?= __('Invoice Configuration', true) ?></h3>
    <p><?= __('Manage the invoice creation form and how invoices are displayed.', true) ?></p>


    <div class="">


        <div class="form-group mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black" for="SiteNextInvoiceNumber">
                <?= __('Next Auto-Generated Invoice Number', true) ?>
            </label>
            <div class="d-flex gap-5">
                <div class="flex-grow-1">
                    <?php
                    if (empty($this->data['Site']['next_invoice_number'])) {
                        $value = '000001';
                    } else {
                        $value = $this->data['Site']['next_invoice_number'];
                    }
                    echo $form->input('next_invoice_number', array('div' => false, 'type' => 'number', 'class' => 'INPUT form-control required', 'value' => $value, 'readonly' => true, 'disabled' => true, 'label' => false));
                    ?>
                </div>
                <div class="flex-shrink-0">

                    <?php echo '<button type="button" tabindex="-1" class="btn btn-secondary text-decoration-none" id="autoNumberSettingsBtn" href="#" title="Auto Number Settings"><i class="fa fa-cog me-4"></i>  ' . __('Auto Number Settings', true) . ' </button>'; ?>
                </div>
            </div>
            <div class="form-text fs-7 mt-5"><?= __('The number the system will assign to the next invoice.', true) ?>
            </div>
        </div>


        <div class="form-group mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black"
                for="SiteEditInvoiceNumber"><?= __('Manual Editing of Invoice Numbers', true) ?></label>
            <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                <?php
                echo $form->input('edit_invoice_number', array('div' => false, 'class' => 'form-check-input', 'label' => false, 'type' => 'checkbox'));
                ?>
                <span class="form-check-label fw-normal"><?= __('Allowed', true) ?></span>
            </label>
            <div class="form-text fs-7 mt-5">
                <?= __('Allow users to manually set or change the invoice number during creation.', true); ?></div>
        </div>

        <div class="form-group mt-md-15 mb-0">
            <?php
            echo $form->input('invoicing_method', array_merge($form_data['invoicing_method'], array('type' => 'select', 'div' => false, 'class' => 'form-control form-control-selectize', 'label' => ['text' => __('Invoicing Method', true), 'class' => 'form-label fs-8 fw-medium text-black'])));
            ?>
            <div class="form-text fs-7 mt-5"><?= __('Choose how invoices are issued and sent to clients.', true); ?>
            </div>
        </div>



        <div class="form-group mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black"
                for="SiteDisableInvoiceEdit"><?= __('Manual Item Edits and Custom Entry', true) ?></label>
            <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                <?php
                echo $form->input('disable_invoice_edit', array('type' => 'checkbox', 'div' => false, 'class' => 'form-check-input ', 'label' => false, "value" => !$this->data['Site']['disable_invoice_edit']));
                ?>
                <span class="form-check-label fw-normal"><?= __('Allowed', true) ?></span>
            </label>
            <div class="form-text fs-7 mt-5">
                <?= __('Allow users to manually edit items or add custom, unregistered items when creating invoices. This is a global setting. Use roles and permissions to control who can perform these actions.', true); ?>
            </div>
        </div>


        <div class="form-group mt-md-15 mb-0">
            <?php
            echo $form->input('display_last_selling_and_min_product_price', array_merge($form_data['display_last_selling_and_min_product_price'], array('type' => 'select', 'div' => false, 'class' => 'form-control form-control-selectize', 'label' => ['text' => __('Minimum and Last Selling Price Per Item Display', true), 'class' => 'form-label fs-8 fw-medium text-black'])));
            ?>
            <div class="form-text fs-7 mt-5">
                <?= __('Control how they appear when creating invoices. These values are for internal reference only and are not shown to clients or in the invoice. The minimum price is defined in the product/service edit page.', true); ?>
            </div>
        </div>


        <div class="form-group mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black"
                for="SiteCopyNotes"><?= __('Copy Notes/Terms When Converting a Sales Order or Estimate to an Invoice or Estimate to a Sales Order', true) ?></label>
            <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                <?php echo $form->input('copy_notes', array('type' => 'checkbox', 'checked' => !empty($this->data['Site']['copy_notes']), 'div' => false, 'class' => 'form-check-input ', 'label' => false)); ?>
                <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
            </label>
            <div class="form-text fs-7 mt-5">
                <?= __('Enabling this option will automatically copy notes/terms in these scenarios, while converting a Sales Order or Estimate to an Invoice and while converting an Estimate to a Sales Order.', true); ?>
            </div>
        </div>


        <div class="form-group mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black"
                for="SiteEnablePreviewInvoice"><?= __('Invoice Preview Before Saving', true) ?></label>
            <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                <?php
                echo $form->input('enable_preview_invoice', array('type' => 'checkbox', 'div' => false, 'class' => 'form-check-input ', 'label' => false,));
                ?>
                <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
            </label>
            <div class="form-text fs-7 mt-5">
                <?= __('Enable users to preview the printable version of the invoice before saving.', true); ?></div>
        </div>


        <?php if(ifPluginActive(FollowupPlugin)){ ?>
        <div class="form-group mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black" for="SiteEnableInvoiceStatus">
                <?= __('Invoice Custom Statuses', true) ?>
            </label>
            <div class="d-flex gap-5">
                <div class="flex-grow-1">
                    <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                        <?php
                            echo $form->input('enable_invoice_status', array('type' => 'checkbox', 'div' => false, 'class' => 'form-check-input ', 'label' => false,));
                            ?>
                        <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
                    </label>
                </div>
                <div class="flex-shrink-0">
                    <?php echo '<span id="enable_invoice_link"' . (!settings::getValue(InvoicesPlugin, 'enable_invoice_status') ? "style='display:none;'" : "") . '> <button type="button" id="statusIdBtn" status_id="' . Post::INVOICE_TYPE . '" class="btn btn-secondary text-decoration-none" ><i class="fa fa fa-cog me-4"></i>' . __("Manage Custom Statuses", true) . '</button></span>'; ?>
                </div>
            </div>
            <div class="form-text fs-7 mt-5">
                <?= __('Create custom invoice statuses that suit your workflow, such as ‘Pending Delivery’ or ‘Under Review’, and assign them to invoices. Use custom statuses to filter and search invoices.', true) ?>
            </div>
        </div>

        <?php } ?>


        <div class="form-group mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black"
                for="SiteDisplayInvoicesProfit"><?= __('Show Invoice Profit Tab (Hidden from Clients)', true) ?></label>
            <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                <?php
                echo $form->input('display_invoices_profit', array('checked' => !empty($this->data['Site']['display_invoices_profit']), 'type' => 'checkbox', 'label' => false, 'class' => 'form-check-input', 'div' => false));
                ?>
                <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
            </label>
            <div class="form-text fs-7 mt-5">
                <?= __('Display a Profit tab on the invoice page, visible only to users with permission. Profit is calculated from the average cost of items in purchase invoices. Use roles and permissions to control visibility.', true); ?>
            </div>
        </div>


        <div class="form-group mt-md-15 mb-0">
            <p class="form-label fs-8 fw-medium text-black"><?= __('Zero Fraction Appearing', true); ?></p>
            <div class="">
                <label class="form-check form-check-group py-10 form-check-custom">
                    <input class="form-check-input" type="radio" name="data[Site][invoice_fraction_appearing]"
                        <?= $form_data['invoice_fraction_appearing']['value'] == 1 ? 'checked' : '' ?> />
                    <span class="form-check-label fw-normal flex-column">
                        <p class="m-0 p-0"><?= __('Show trailing zeros', true) ?></p>
                        <div class="form-text fs-7 mt-5">
                            <?= __('Always show trailing zeros', true) ?><strong><?= __('(e.g., 10.00)', true) ?></strong>
                        </div>
                    </span>
                </label>


                <label class="form-check form-check-group py-10 form-check-custom">
                    <input class="form-check-input" type="radio" name="data[Site][invoice_fraction_appearing]"
                        <?= $form_data['invoice_fraction_appearing']['value'] == 2 ? 'checked' : '' ?> />
                    <span class="form-check-label fw-normal flex-column">
                        <p class="m-0 p-0"><?= __('Hide trailing zeros', true) ?></p>
                        <div class="form-text fs-7 mt-5">
                            <?= __('Always hide trailing zeros', true) ?><strong><?= __('(e.g., 10)', true) ?></strong>
                        </div>
                    </span>
                </label>


                <label class="form-check form-check-group py-10 form-check-custom">
                    <input class="form-check-input" type="radio" name="data[Site][invoice_fraction_appearing]"
                        <?= $form_data['invoice_fraction_appearing']['value'] == 0 ? 'checked' : '' ?> />
                    <span class="form-check-label fw-normal flex-column">
                        <p class="m-0 p-0"><?= __('Auto (based on value)', true) ?></p>
                        <div class="form-text fs-7 mt-5">
                            <?= __("Show trailing zeros only for values under 100 <strong>(e.g., 10.00)</strong> and hide them for values 100 or higher <strong>(e.g., 100)</strong>", true) ?>
                        </div>
                    </span>
                </label>

            </div>
            <div class="form-text fs-7 mt-5"><?= __('Control how trailing zeros appear in decimal numbers.', true); ?>
            </div>
        </div>



    </div>
</div>