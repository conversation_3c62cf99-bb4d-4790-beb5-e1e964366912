<div class="side-nav-container">
    <a href="#" class="btn btn-default btn-md fa fa-chevron-left disabled" id="nav-prev" title="Previous Page (<)"></a>
    <a href="#" class="btn btn-default btn-md fa fa-chevron-right" id="nav-next" title="Next Page (>)"></a>
    <div class="col-md-3 col-sm-12 no-padding side-nav-new">

        <ul class="" id="myTabs">

            <li>
                <a href="#section1" class="active">
                    <i class="side-nav-new-icon mdi mdi-receipt-text"></i>
                    <div class="">
                        <h4><?= __("Invoice Configuration", true);?></h4>
                        <p><?= __("Manage the invoice creation form and how invoices are displayed.", true);?></p>
                    </div>
                </a>
            </li>
            <li>
                <a href="#section2">
                    <i class="side-nav-new-icon mdi mdi-tag-text"></i>
                    <div class="">
                        <h4><?= __("Pricing & Discounts", true);?></h4>
                        <p><?= __("Control item pricing rules, invoice discount behavior, and discount limits.", true);?>
                        </p>
                    </div>
                </a>
            </li>
            <li>
                <a href="#section3">
                    <i class="side-nav-new-icon mdi mdi-cash-multiple"></i>
                    <div class="">
                        <h4><?= __("Payments & Credits", true);?></h4>
                        <p><?= __("Configure auto payment behavior and manage client credit and balances.", true);?></p>
                    </div>
                </a>
            </li>
            <li>
                <a href="#section4">
                    <i class="side-nav-new-icon mdi mdi-package"></i>
                    <div class="">
                        <h4><?= __("Inventory Control", true);?></h4>
                        <p><?= __("Manage inventory rules that affect item availability and billing behavior in sales.", true);?>
                        </p>
                    </div>
                </a>
            </li>
            <li>
                <a href="#section5">
                    <i class="side-nav-new-icon mdi mdi-file-tree"></i>
                    <div class="">
                        <h4><?= __("Accounting Entries", true);?></h4>
                        <p><?= __("Control how invoice journal descriptions are generated in the accounting system.", true);?>
                        </p>
                    </div>
                </a>
            </li>

        </ul>
    </div>
</div>
<script>
var isRTL = document.documentElement.getAttribute('dir') == 'rtl';

$('#nav-next').on("click", function() {
    if (isRTL) {
        $('.side-nav').animate({
            scrollLeft: -600
        }, 500);
    } else {
        $('.side-nav').animate({
            scrollLeft: 600
        }, 500);
    }
})

$('#nav-prev').on("click", function() {
    if (isRTL) {
        $('.side-nav').animate({
            scrollLeft: 600
        }, 500);
    } else {
        $('.side-nav').animate({
            scrollLeft: -600
        }, 500);
    }
})

$('.side-nav').on("scroll", function() {
    if (isRTL) {
        if ($(this).scrollLeft() * -1 <= 0) {
            $('#nav-prev').addClass('disabled')
            $('#nav-next').removeClass('disabled')
        } else if ($('.nav-pills').width() - $(this).width() <= $(this).scrollLeft() * -1 + 30) {
            $('#nav-prev').removeClass('disabled')
            $('#nav-next').addClass('disabled')
        } else {
            $('#nav-prev').removeClass('disabled');
            $('#nav-next').removeClass('disabled');
        }
    } else {
        if ($(this).scrollLeft() <= 0) {
            $('#nav-prev').addClass('disabled')
            $('#nav-next').removeClass('disabled')
        } else if ($('.nav-pills').width() - $(this).width() <= $(this).scrollLeft() + 30) {
            $('#nav-prev').removeClass('disabled')
            $('#nav-next').addClass('disabled')
        } else {
            $('#nav-prev').removeClass('disabled');
            $('#nav-next').removeClass('disabled');
        }
    }
});

$(document).ready(function() {
    const $tabs = $('#myTabs a');

    // Build an array of jQuery section elements from the hrefs (skip missing)
    function buildSections() {
        return $tabs.map(function() {
            const sel = $(this).attr('href');
            const $el = $(sel);
            return $el.length ? $el : null;
        }).get().filter(Boolean).map($ => $); // returns array of jQuery objects
    }

    let sections = buildSections();

    // Rebuild on resize in case layout or anchors change
    $(window).on('resize', function() {
        sections = buildSections();
    });

    // Click behavior: scroll and set active immediately
    $tabs.on('click', function(e) {
        e.preventDefault();
        const target = $(this).attr('href');
        // Set active class on click
        $tabs.removeClass('active');
        $(this).addClass('active');

        // Animate scroll
        $('html, body').animate({
            scrollTop: $(target).offset().top - 150
        }, 500);
    });

    // Debounce helper
    function debounce(fn, wait) {
        let t;
        return function() {
            clearTimeout(t);
            t = setTimeout(() => fn.apply(this, arguments), wait);
        }
    }

    // Scroll handler: mark active based on section positions,
    // and force last when near bottom of page.
    function onScroll() {
        const scrollPos = $(document).scrollTop() + 180;
        let current = null;

        // find the last section whose top is <= scrollPos
        for (let i = 0; i < sections.length; i++) {
            const $sec = sections[i];
            if ($sec.offset().top <= scrollPos) {
                current = $sec;
            }
        }

        // If near bottom, make last section active (fixes tiny last section)
        const nearBottom = (window.innerHeight + window.pageYOffset) >= (document.body.offsetHeight - 5);
        if (nearBottom && sections.length) {
            current = sections[sections.length - 1];
        }

        if (current && current.length) {
            const id = current.attr('id');
            if (id) {
                $tabs.removeClass('active');
                $tabs.filter('[href="#' + id + '"]').addClass('active');
            }
        } else {
            // nothing matched — remove active (optional)
            // $tabs.removeClass('active');
        }
    }

    // Use a small debounce to avoid handling scroll too many times
    $(window).on('scroll', debounce(onScroll, 50));

    // run once on load to set initial state
    onScroll();
});

</script>