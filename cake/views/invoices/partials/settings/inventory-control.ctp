<div class="setting-section" id="section4">
    <h3 class="fs-13 text-black mb-4 p-0"><?= __('Inventory Control', true) ?></h3>
    <p><?= __('Manage inventory rules that affect item availability and billing behavior in sales.', true) ?></p>

    <div class="">

        <div class="form-group mt-md-15 mb-0">
            <?php echo $form->input("excluded_categories", ['label' => ['text' => __('Prevent Specific Item Categories from Being Sold or Invoiced', true), 'class' => 'form-label fs-8 fw-medium text-black'], 'div' => false, 'class' => 'form-control form-control-selectize-multiple', 'options' => $categories, 'multiple' => true]) ?>
            <div class="form-text fs-7 mt-5"><?= __('Restrict specific item categories from being added to sales invoices or included in sales transactions. This helps enforce business rules for non-sellable or internal-use-only items.', true); ?></div>
        </div>

        <!--
        Change Note: inverted
        -->
        <?php if (isset($display_stop_selling_expired_items) && $display_stop_selling_expired_items) { ?>
            <div class="form-group mt-md-15 mb-0">
                <label class="form-label fs-8 fw-medium text-black"
                       for="SiteStopSellingExpiredTrackingItems"><?= __('Sell Expired Items', true) ?></label>
                <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                    <?php
                    echo $form->input('stop_selling_expired_tracking_items', array('checked' => empty($this->data['Site']['stop_selling_expired_tracking_items']), 'type' => 'checkbox', 'label' => false, 'class' => 'form-check-input', 'div' => false));
                    ?>
                    <span class="form-check-label fw-normal"><?= __('Allowed', true) ?></span>
                </label>
                <div class="form-text fs-7 mt-5">
                    <?= sprintf(
                            __('Allow or prevent selling items after their expiry date. Activate “Track Products Using Serial, Lot or Expiry Date” option in %s and define Track Expire Date in desired products and ensure you enter expiry dates when recording items in Stock Requisitions.', true),
                            '<a href="#" target="_blank" class="text-primary text-link">'. __('Inventory Settings', true) .'</a>'
                    ); ?></div>
            </div>
        <?php } ?>


    </div>
</div>
