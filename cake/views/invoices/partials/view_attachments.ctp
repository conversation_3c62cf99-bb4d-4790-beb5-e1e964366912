<?php

use Izam\Aws\Aws;

foreach ($invoice['Attachments'] as $key => $attach) {
    $aws = new Aws;
    $url = $aws->getProxyUrl($attach['path']);
    $mime_type = $attach['mime_type'];
    $extensions = ['png', 'jpeg', 'jpg', 'gif', 'svg'];
?>

    <div class="attachment-file" <?= in_array($mime_type, $extensions) ? 'data-app-lightbox data-src="' . $url . '"' : '' ?>>
        <a target="_blank" title="<?= $attach['name'] ?>" href="<?= $url ?>" download=""  <?= in_array($mime_type, $extensions) ? 'class="thumb-link"' : '' ?>>

            <?php
            if (in_array($mime_type, $extensions)) {
            ?>

                <img width="140" height="140" id="photome" loading="lazy" src="<?= $url ?>">
            <?php
            } else {
                $icon = getAttachmentIconBasedMimeType($mime_type);

            ?>
                <div style="height: 140px;" class="d-flex align-items-center justify-content-center"><i class="<?= $icon; ?>"></i></div>

            <?php } ?>

            <span><?= $attach['name'] ?></span>
        </a>
        <div class="attach-hov">
            <a target="" href="<?= $url ?>" download="" title="">
                <div class="ofh" style="overflow:hidden;">
                    <h4><?= $attach['name'] ?></h4>
                    <span><?= round($attach['file_size'] / 1024, 2); ?> kb</span>
                </div>
                <div class="clearfix"></div>
            </a>
            <div class="text-center btt" style="">
                <a target="" href="<?= "/v2/owner/entity/files/preview/".$attach['id'] ?>" download="" title="Download"   class="btn btn-sm btn-primary <?= in_array($mime_type, $extensions) ? 'btn-download' : '' ?>">
                <i class="fa fa-download"></i></a>
            </div>
        </div>
    </div>
<?php   } ?>