<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/show/show.min.css?v=".CSS_VERSION, null, []);
echo $html->css(array('pages-head-buttons-s2022.css'), false, ['inline' => false], false);
echo $html->css(array('jquery.qtip.min','product_view_v'.CSS_VERSION.'.css', 'time-tracker.css?v=2', 'fontello.css'));
echo $html->css('timeline_v'.CSS_VERSION.'.css');
echo $html->css('bootstrap-multiselect.css');
echo $javascript->link(array('bootstrap-multiselect.js'));
echo $javascript->link(array('jqueryui','jquery.qtip.min'));
echo $html->css('view_new_style_v'.CSS_VERSION.'.css');
if($is_rtl){echo $html->css('view_new_style_ar_v'.CSS_VERSION.'.css'); }
echo $html->css(array('jqueryui'), false, ['inline' => false]);
echo $javascript->link(array('magicsuggest-min.js'));
echo $html->css(array( 'magicsuggest-min.css'));
$owner = getAuthOwner();
$site = getCurrentSite();
$full_name = $owner['first_name'] . ' ' . $owner['last_name'];
$dateFormats = getDateFormats('std');
$dateFormat = $dateFormats[getCurrentSite('date_format')];
$sitename = $site['business_name'];
if ( $enable_requisitions){
    $delivery_status = Requisition::$delivery_status_list[$invoice['Invoice']['requisition_delivery_status']];
}
$id = $invoice['Invoice']['id'];
$isAdvancePaymentActive = settings::getValue(InvoicesPlugin, 'enable_advance_payment');
$isInvoiceHasPayment = $advance_invoice_counsumtion > 0 || count($invoice['InvoicePayment']) > 0;
?>
<?php
$title = sprintf(__('Invoice %s', true),/*'<span class="title_id_with_hash">'.*/ '#' . $invoice['Invoice']['no']/*.'</span>'*/) ;
if ( !empty ( $invoice['Invoice']['work_order_id']))
{

    $additional [] = ['title' =>$title, 'link' => '#' ];
    echo $this->element  ('work_orders/work_order_breadcrumbs' , ['no_head_class' => false ,  'wo_id' => $invoice['Invoice']['work_order_id'] , 'additional' => $additional]  ) ;
}

$relatedProductionPlans = [];
if(isset($invoice['ProductionPlan'])){
    $relatedProductionPlans =  ProductionPlan::getRelatedProductionPlans($invoice['ProductionPlan']);
    $productionPlansFilterStr= "&filter[source_id]=" . $invoice['Invoice']['id']."&filter[source_type]=invoice". (count($invoice['ProductionPlan']) <= 20 ? '&hide_page_header=1' :'') ;
}
?>

<?php
// warning suppress
if((isset($appointment_filter_url) && $appointment_filter_url) ||  (isset($filter_url) && $filter_url)) {
	?>

	<?php


    if(!$appointment_filter_url && $filter_url){
        $breadCrumbs = [
            ['link' =>  Router::url (['controller' =>   'invoices' , 'action'=>'index'] ) ,'title' =>__('Invoices',true) ],
            ['link' => Router::url ($filter_url ) ,'title' =>__('Search Results',true) ],
            ['link' => '#' ,'title' =>  __("Invoice", true).' #'.$invoice['Invoice']['no']]
            ];
echo $this->element ('breadcrumbs' , ['no_head_class' => TRUE,'breadcrumbs' => $_PageBreadCrumbs]);
	} else if ($appointment_filter_url){
	echo $this->element ('breadcrumbs' , ['no_head_class' => TRUE,'breadcrumbs' => [
    ['link' =>  Router::url (['controller' =>   'appointments' , 'action'=>'index'] ) ,'title' =>__('Appointments',true) ],
    ['link' => Router::url ($appointment_filter_url ) ,'title' =>__('Search Results',true) ],
	['link' => '#' ,'title' =>  __("Appointment", true).' #'.$_GET['appointment_id']]]]
			);

	}
	?>

<?php } ?>

<style>
    .list-table {
        border: 1px solid #eee;
    }
    .list-table td {
        padding: 14px 8px !important;
    }

    .list-table>thead>tr>th {
        background: #fbfbfb !important;
        padding: 14px 8px !important;
    }
    .modal .modal-dialog {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        min-height: calc(100% - (10px* 2));
        padding-top: 0px !important;
    }

    .modal .modal-content {
        width: 100%;
    }

    @media (min-width: 768px) { 
        .modal .modal-dialog {
            min-height: calc(100% - (30px* 2));
        }
    }
</style>

<div class="pages-head-s2020">
	<div class="container">
        <div class="row">
            <div class="col-lg-5 ">
                <div class="<?php if(IS_MOBILE){ echo "view-title-mobile d-flex justify-content-between"; }; ?>">
                <h1 class="left no-margin view-title-new">
                    <?php echo $title; ?>

                    <?php
                    echo $this->element('invoice-status');
                    ?>
                    <?php if ( $enable_requisitions && !empty($invoice['Invoice']['requisition_delivery_status']) && $requisition_exists) { ?>
                        <div class="status">
                                    <span style="color: <?php echo $delivery_status['color']?>;background-color: <?php echo $delivery_status['background-color']?>" class="status-symble "><?php echo __($delivery_status['label'] , true ); ?></span>
                        </div>
                    <?php } ?>








                    <?
                    if ($refund_count > 0 and $invoice['Invoice']['type']==0) {
                        ?>
                        <div class="clearfix"></div>
                        <span style="font-size: 14px"><?php __('Refund Receipts') ?> : </span>

                    <?

                        foreach ($refund_list as $key=>$rr) {

                        ?>
                            <span class="btn btn-default btn-xs" id="rr_<? echo $key ?>">

                                <a target="_blank"  class="btn-group" href="<?php echo Router::url(array('action' => 'view_refund', $key)); ?>">#<? echo $rr ?> </a>

                            </span>
                        <? } ?>
                    <? } ?>


                    <?
                    if ($credit_note_count > 0 and $invoice['Invoice']['type']==0) {
                        ?>
                        <div class="clearfix"></div>
                        <span style="font-size: 14px"><?php __('Credit Note') ?> : </span>

                        <?

                        foreach ($credit_note_list as $key=>$rr) {

                            ?>
                            <span class="btn btn-default btn-xs" id="rr_<? echo $key ?>">

                                <a target="_blank"  class="btn-group" href="<?php echo Router::url(array('action' => 'view_creditnote', $key)); ?>">#<? echo $rr ?> </a>

                            </span>
                        <? } ?>
                    <? } ?>


                </h1>
                <?php
                    if(IS_MOBILE){
                        $drop = "";

                        foreach ($FollowUpStatus as $key => $status_row) {
                            $drop_style[$key] = $status_row['style'];
                            $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $invoice['Invoice']['id'], $key)) . '" tabindex="-1"><span ' . $status_row['style'] . ' class="badge rounded-3px shadow-light"><span>' . $status_row['name'] . '</span></span></a>';
                            $button_no_link[$key] = '<a href="#" ' . $status_row['style'] . ' class="btn  not dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"> ' . $status_row['name'] . '</a>';
                        }
                        if ( $enable_invoice_status ){
                        ?>
                        <div class="flex-shrink-0">
                            <div class="btn-group status-list status-list-mobile">
                                <?
                                foreach ($button as $key => $button_code) {
                                    if ($key == $invoice['Invoice']['follow_up_status']) {
                                        $drop = $drop_style[$key];
                                        echo $button_no_link[$key];
                                    }
                                }
                                ?>

                                <button type="button"  <? echo $drop ?> class="btn <?php echo empty($drop) ? 'btn-grey' : '' ?> dropdown-toggle not" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <?
                                    if (empty($invoice['Invoice']['follow_up_status'])) {
                                        echo __('Select Status', true);
                                    }
                                    ?> <span class="caret"></span>
                                </button>



                                <ul class="dropdown-menu dropdown-menu-right">
                                    <?
                                    foreach ($button as $key => $button_code) {
                                        if ($key != $invoice['Invoice']['follow_up_status']) { ?>
                                            <li><?php echo str_replace('<i class="fa fa-check"></i>', '', $button_code) ?></li>
                                            <? } } ?>
                                    <li> <a href="/owner/follow_up_statuses/index/<?php echo Post::INVOICE_TYPE?>" class="py-3" tabindex="-1"><i class="fa fa fa-cog"></i> <?php __('Edit statuses list for invoice') ?></a></li>
                                </ul>

                            </div>
                        </div>
                    <?php }
                        }

                    ?>
                </div>


                <div class="sub-headings <?php if(IS_MOBILE){ echo "sub-headings-mobile"; }; ?>">
                        <?php if ($owner['staff_id'] == 0 || ($owner && $owner['staff_id'] != 0 && check_permission(Clients_View_his_own_Clients))) { ?>
                            <span class="invoice-client sub-heading sub-heading2"><?php printf(__('Recipient: %s', true), $html->link($invoice['Invoice']['client_business_name'], array('controller' => 'clients', 'action' => 'view', $invoice['Invoice']['client_id']), array('target' => '_blank'))); ?></span>
                        <?php } ?>
                        <?php
                        if (!empty($invoice['Invoice']['pos_shift_id']) && ifPluginActive(PosPlugin) && check_permission(VIEW_ALL_POS_SESSIONS)) {
                            ?>
                            <span class="invoice-client sub-heading sub-heading2"><?php echo __('Pos Session', true) . ': ' . sprintf('%s', $html->link(($pos_shift['PosDevice']['name'] . '/' . $pos_shift['PosShift']['id']), array('controller' => 'pos_shifts', 'action' => 'view', $invoice['Invoice']['pos_shift_id']), array('target' => '_blank')), ($pos_shift['PosDevice']['name'] . '/' . $pos_shift['PosShift']['id'])); ?></span>
                        <?php } ?>

                        <?php if (!is_null($priceListName) && settings::getValue(InvoicesPlugin, 'change_price_list')){ ?>
                            <span class="invoice-client sub-heading sub-heading2"><?php echo __('Price List', true) . ': ' . $priceListName; ?></span>
                        <?php } ?>

                        <?php
                            if (ifPluginActive(AccountingPlugin)&&check_permission(VIEW_ALL_JOURNALS)&&!empty($linked_journal)) {
                                echo $this->element('linked-journal',['journal'=>$linked_journal]);
                            }
                            if (ifPluginActive(AccountingPlugin) && check_permission(VIEW_ALL_JOURNALS) && !empty($sales_of_cost_journal)) {
                                echo $this->element('sales-cost-linked-journal', ['journal' => $sales_of_cost_journal]);
                            }
                        ?>


                        <?php if ($owner && $invoice['Invoice']['subscription_id'] && !empty($subscription['Invoice']['name'])){ ?>
                            <span class="invoice-subscription sub-heading sub-heading2"><?php printf(__('Generated By: %s', true), $html->link($subscription['Invoice']['name'], array('action' => 'view_subscription', $subscription['Invoice']['id']), array('target' => '_blank'))); ?></span>
                        <?php } ?>

                        <?php if ($owner and $owner['staff_id'] != 0 and check_permission(Clients_View_his_own_Clients) and $invoice['Invoice']['staff_id'] >=0 ): ?>
                            <?php
                                    if ($invoice['Invoice']['staff_id'] != 0 && $owner['staff_id'] != $invoice['Staff']['id']) {
                                        $staff_link = $html->link($invoice['Staff']['name'], '/v2/owner/staff/' . $invoice['Staff']['id'], array('target' => '_blank'));
                                    } elseif ($owner['staff_id'] == $invoice['Staff']['id']) {
                                        $staff_link = $invoice['Staff']['name'];
                                    } else {
                                        $staff_link = $full_name;
                                    }
                            ?>
                            <span class="invoice-client sub-heading sub-heading2"><?php printf(__('Added By: %s', true),$staff_link); ?></span>

                            <?php endif; ?>


                        <?php  if (!$invoice["Invoice"]["draft"]) {
                            echo $this->element('view_page_invoice_status',[
                                'invoice_status' => json_decode($invoice['EntityAppData'][0]['data'] ?? null,true),
                                'is_submitted' => !empty($invoice['SubmitEntityAppData'][0]["id"]),
                                'is_sent' => !empty($invoice['SentEntityAppData'][0]["id"]),
                                'have_error' => !empty($invoice['ErrorEntityAppData'][0]["id"])
                            ]);
                        } ?>

                        <?php if ($invoice['Invoice']['external_source'] && $invoice['Invoice']['external_source'] !=" "){ ?>
                            <span class="invoice-client sub-heading sub-heading2"><?= __t('Source') . ': ' . Izam\Daftra\Common\Utils\InvoiceExternalSourceUtil::getSourceForPreview($invoice['Invoice']['external_source']); ?></span>
                        <?php } ?>
                        <?php if ($owner && Invoice::SALES_ORDER == $invoice['Invoice']['source_type'] && !empty($invoice['Invoice']['source_id']) && $salesOrder) { ?>
                            <span class="invoice-client sub-heading sub-heading2"><?php echo __('Sales Order', true) . ' : ' . sprintf('#%s', $html->link($salesOrder['Invoice']['no'], array('controller' => 'invoices', 'action' => 'view_sales_order', $salesOrder['Invoice']['id']), array('target' => '_blank'))); ?></span>
                        <?php } ?>
                        <?php echo $this->element('tags_view',array('item_id' => $invoice['Invoice']['id'],'model_name' => 'Invoice','get_ajax' => true)); ?>


                </div>

                    <?php if(!IS_MOBILE){ ?>
                        <div class="clearfix"></div>
                    <?php } ?>
            </div>
            <div class="col-lg-7">
                <?php
                    if(IS_PC){
                ?>
                <div class="hidden-xs">
                    <div class="mb-opt-btn">

                    </div>
                    <div class="pagination-box-new pagination-box-new-md top-actions">
                        <?php if($invoices_nav) echo $this->element('nav',array('id'=> $invoice['Invoice']['id'])) ; ?>
                        <?php if(isset($appointments_nav) && $appointments_nav)
                        {

                            echo $this->element('nav',array('id'=> $appintment_id,'controller' => 'appointments'));
                        } ?>
                    </div>
                    <? if (empty($invoice['Invoice']['is_offline']) and $invoice['Invoice']['draft'] != "1") { ?>

                        <a href="<?php echo Router::url(array('action' => 'send_to_client', $id)); ?>" class="primary-btn btn btn-info btn-icn s2020 responsive  pull-right"><i class="fa fa-envelope-o"></i> <span class="hidden-xs"><?php __("Email to Client") ?></span></a>

                    <? } else if (!IS_PC || IS_MOBILE_APPLICATION) { ?>

                        <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview', $id, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'preview', $id, 'ext' => 'pdf')); ?>" style="height: auto;" class="print primary-btn btn btn-info btn-icn s2020 responsive  pull-right"><i class="fs-18 m-0 fa fa-print"></i> <span class="hidden-xs"><?php __("Print Invoice ") ?></span></a>

                        <? } else { ?>
                        <a href="#" class="print primary-btn btn btn-info btn-icn s2020 responsive  pull-right"><i class="fa fa-print"></i> <span class="hidden-xs"><?php __("Print Invoice ") ?></span></a>
                    <?php } ?>
                    <?
                    if($invoice['Invoice']['draft']=="1"){
                    ?>
                    <?php if (check_permission(Invoices_Edit_All_Invoices) || (check_permission(Invoices_Edit_his_own_Invoices) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) { ?>
                        <a href="<?php echo Router::url(array('action' => 'update_draft', $id,0)); ?>" class="primary-btn btn btn-success btn-icn s2020 responsive  pull-right mr-1"><i class="fa fa-usd"></i> <span class="hidden-xs"> <?php __("Issue Invoice") ?></span></a>
                    <?
                    }
                    }
                    ?>
                        <?php if ((check_permission(Invoices_Add_Invoice_Payments) || (check_permission(Invoices_Add_Payments_to_All) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) && $isPosOpenedOrNotPos) { ?>
                    <? if ($invoice['Invoice']['payment_status'] < 2 && empty($invoice['Invoice']['draft'])) { ?>
                        <a href="<?php echo Router::url(array('action' => 'add_payment', $id)); ?>" class="primary-btn btn btn-success btn-icn s2020 responsive  pull-right mr-1"><i class="fa fa-credit-card"></i> <span class="hidden-xs"> <?php __("Add Payment") ?> </span></a>
                    <? } ?>

                        <? } ?>

                    <?php
                        $drop = "";

                        foreach ($FollowUpStatus as $key => $status_row) {
                            $drop_style[$key] = $status_row['style'];
                            $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $invoice['Invoice']['id'], $key)) . '" tabindex="-1"><span ' . $status_row['style'] . ' class="badge rounded-3px shadow-light"><span>' . $status_row['name'] . '</span></span></a>';
                            $button_no_link[$key] = '<a href="#" ' . $status_row['style'] . ' class="btn s2020 not dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"> ' . $status_row['name'] . '</a>';
                        }
                        if ( $enable_invoice_status && ifPluginActive(FollowupPlugin)){
                        ?>
                        <div class="btn-group status-list pull-right">
                            <?
                            foreach ($button as $key => $button_code) {
                                if ($key == $invoice['Invoice']['follow_up_status']) {
                                    $drop = $drop_style[$key];
                                    echo $button_no_link[$key];
                                }
                            }
                            ?>

                            <button type="button"  <? echo $drop ?> class="btn s2020 <?php echo empty($drop) ? 'btn-secondary' : '' ?> dropdown-toggle not" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?
                                if (empty($invoice['Invoice']['follow_up_status'])) {
                                    echo __('Select Status', true);
                                }
                                ?><span class="ml-1 caret"></span>&nbsp;
                            </button>



                            <ul class="dropdown-menu">
                                <?
                                foreach ($button as $key => $button_code) {
                                    if ($key != $invoice['Invoice']['follow_up_status']) { ?>
                                        <li><?php echo str_replace('<i class="fa fa-check"></i>', '', $button_code) ?></li>
                                        <? } } ?>
                                <li> <a href="/owner/follow_up_statuses/index/<?php echo Post::INVOICE_TYPE?>" class="py-3" tabindex="-1"><i class="fa fa fa-cog"></i> <?php __('Edit statuses list for invoice') ?></a></li>
                            </ul>

                        </div>
                    <?php }?>
                </div>
                <?php
                    }else{
                        ?>
                        <div class="actions-mobile d-flex">

                            <div class="mb-opt-btn btn-action-mob"></div>

                            <a href="#" class="btn-action-mob btn btn-dark p-0 toggleSubHeader"><i class="w-auto h-auto fs-22 m-0 fa fa-exclamation-circle"></i></a>
                            <a href="#" data-title="<?php echo $title; ?>" data-invoice-url="<?php echo 'https://'.getCurrentSite('subdomain').Router::url(array('action' => 'view', $id)); ?>" data-url="<?php echo Router::url(array($id, 'ext' => 'pdf')); ?>" class="btn-action-mob btn btn-dark-blue p-0" id="shareButton"><i class="w-auto h-auto fs-22 m-0 fa fa-share"></i></a>
                            <? if (empty($invoice['Invoice']['is_offline']) and $invoice['Invoice']['draft'] != "1") { ?>
                                <a href="<?php echo Router::url(array('action' => 'send_to_client', $id)); ?>" class="btn-action-mob btn btn-blue p-0"><i class="w-auto h-auto fs-22 m-0 fa fa-envelope-o"></i> <span class="hidden-xs"><?php __("Email to Client") ?></span></a>
                            <? } else { ?>
                                    <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview', $id, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'preview', $id, 'ext' => 'pdf')); ?>" class="btn-action-mob btn btn-blue p-0"><i class="w-auto h-auto fs-22 m-0 fa fa-print"></i></a>
                            <?php } ?>


                            <?
                            if($invoice['Invoice']['draft']=="1"){
                                ?>
                                <?php if (check_permission(Invoices_Edit_All_Invoices) || (check_permission(Invoices_Edit_his_own_Invoices) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) { ?>
                                    <a href="<?php echo Router::url(array('action' => 'update_draft', $id,0)); ?>" class="btn-action-mob btn btn-green p-0"><i class="w-auto h-auto fs-22 m-0 fa fa-print fa fa-usd"></i></a>
                                <?
                                }
                            }
                            ?>

                            <?php if ((check_permission(Invoices_Add_Invoice_Payments) || (check_permission(Invoices_Add_Payments_to_All) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) && $isPosOpenedOrNotPos) { ?>
                                <? if ($invoice['Invoice']['payment_status'] < 2 && empty($invoice['Invoice']['draft'])) { ?>
                                    <a href="<?php echo Router::url(array('action' => 'add_payment', $id)); ?>" class="btn-action-mob btn btn-green p-0"><i class="w-auto h-auto fs-22 m-0 fa fa-credit-card"></i></a>
                                <? } ?>
                            <? } ?>

                            <div class="pagination-box-new">
                                <?php if($invoices_nav) echo $this->element('nav',array('id'=> $invoice['Invoice']['id'])) ; ?>
                                <?php if(isset($appointments_nav) && $appointments_nav)
                                {

                                    echo $this->element('nav',array('id'=> $appintment_id,'controller' => 'appointments'));
                                } ?>
                            </div>

                        </div>
                        <?php
                    }
                ?>
            </div>
        </div>

        <div class="clear"></div>

    </div>
    <div class="clear"></div>
	</div>
</div>
<div class="container">
    <?php
    if(($invoice['Invoice']['summary_paid']-$invoice['Invoice']['summary_total'])>MINOVERPAID && !$invoice['Invoice']['draft']){
        echo $this->element('invoices/resolve_over_paid',array('invoice'=>$invoice));
    }
    ?>
    <!-- <div class="m-t-30"></div> -->
    <?php
    echo $this->element('invoices/invoice-toolbar');
    ?>
    <?php
    echo $javascript->link(array('owner-view_v'.JAVASCRIPT_VERSION));
    ?>
    <div role="tabpanel" class="tabs-box box">

        <ul class="nav nav-tabs responsive">

            <li class="active" role="presentation"><a aria-controls="InvoiceBlock" role="tab" data-toggle="tab" href="#InvoiceBlock" title="<?php __('Preview invoice') ?>" id="ViewInvoice"><span class="one-line"><?php __("Invoice") ?></span></a></li>
            <li class="" role="presentation"><a aria-controls="DetailsBlock" role="tab" data-toggle="tab" href="#DetailsBlock" title="<?php __('Details') ?>" id="ViewDetails"><span class="one-line"><?php __("Details") ?></span></a></li>
            <?php
            if (ifPluginActive(FollowupPlugin)) {

//            if (check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) or (check_permission(Add_Notes_Attachments_For_His_Assigned_Clients_Only) and $is_assignd == true)) {
                if ($post_count > 0) {
                    ?>
                    <li role="presentation"><a class="tab-grey" aria-controls="actions-tab" role="tab" data-toggle="tab" onclick="reload_posts()" href="#NotesBlock" title="<?php __('Notes / Attachments') ?>"><span class="one-line"><?php __('Notes / Attachments') ?>  <small class="counter"> (<? echo $post_count ?>)</small> </span></a></li>
                    <?php
                }
                if (array_sum((array)$appointment_status_counts ) > 0  ) {
                    ?>
                    <li role="presentation"><a aria-controls="actions-tab" role="tab" data-toggle="tab" onclick="reload_appointments($('#statuses').val())" href="#AppointmentsBlock" title="<?php __('Appointments') ?>"><span class="one-line"><?php __('Appointments') ?>  <small class="counter"> (<? echo $appointments_count ?>)</small>  </span></a></li>
                    <?php
                }
            }
            //            }

            ?>
            <?php if($isInvoiceHasPayment) { ?>
                <li role="presentation"><a aria-controls="PaymentsBlock" role="tab" data-toggle="tab" href="#PaymentsBlock" title="<?php __('Payments of this invoice') ?>"><span class="one-line"><?php __("Payments") ?></span></a></li>
            <?php } ?>
            <?php if (check_permission(REQUISITION_VIEW) && $requisition_exists !== false && $enable_requisitions && !empty($invoice['Invoice']['requisition_delivery_status'])) { ?>
                <li role="presentation"><a aria-controls="RequisitionBlock" onclick="reload_requisitions()" role="tab" data-toggle="tab" href="#RequisitionBlock" title="<?php __('Requisitions') ?>"><span class="one-line"><?php __('Requisitions') ?>

                    </span></a></li>
            <? } ?>
            <?php if ($invoice['Invoice']['created'] > '2016-03-20') { ?>
                <li role="presentation"><a aria-controls="TimelineBlock" role="tab" data-toggle="tab" href="#TimelineBlock" title="<?php __('Timeline for invoice') ?>"><span class="one-line"><?php __('Activity Log') ?>

                    </span></a></li>
            <? } ?>
            <?php if (count($relatedProductionPlans)) { ?>
                <li role="presentation" id="productionPlanListItem">
                    <a aria-controls="ProductionPlansBlock" role="tab" data-toggle="tab" href="#ProductionPlansBlock" title="<?= __t('Sales Order Production Plans') ?>">
                        <span class="one-line"><?= __('Production Plans') . " (<span id='productionPlanCount'>" . count($relatedProductionPlans) . "</span>)" ?></span>
                    </a>
                </li>
            <? } ?>
            <? if (check_permission(Add_New_Purchase_Orders) && ifPluginActive(InventoryPlugin) && $stock_count > 0 && empty($invoice['Invoice']['draft'])) { ?>

                <li role="presentation"><a class="tab-grey" aria-controls="StockBlock" role="tab" data-toggle="tab" href="#StockBlock" title="<?php __('Stock') ?>"><span class="one-line"><?php __('Stock') ?> </span></a></li>
            <? } ?>
            <?php

            if(ifPluginActive(CREDIT_PLUGIN) && $hasCreditCharge && check_permission(MANAGE_CREDIT_CHARGES)){ ?>
                <li role="presentation"><a aria-controls="CreditBlock" role="tab" data-toggle="tab" onclick="reload_creditCharges()" href="#CreditBlock" title="<?php __('Credit Charges') ?>"><span class="one-line"><?php __('Credit Charges') ?> </span></a></li>
            <?php } ?>

            <?php
            if(ifPluginActive(CREDIT_PLUGIN) && $hasCreditUsage && check_permission(MANAGE_CREDIT_CHARGES)){ ?>
                <li role="presentation"><a aria-controls="CreditUsageBlock" role="tab" data-toggle="tab" onclick="reload_creditUsages()" href="#CreditUsageBlock" title="<?php __('Credit Usages') ?>"><span class="one-line"><?php __('Credit Usages') ?> </span></a></li>
            <?php } ?>

            <? if ($refund_count > 0 && empty($invoice['Invoice']['draft'])) { ?>

                <li role="presentation"><a onclick="reload_refund()" aria-controls="RefundedBlock" role="tab" data-toggle="tab" href="#RefundedBlock" title="<?php __('Refund Receipts') ?>"><span class="one-line"><?php __('Refund Receipts') ?> (<? echo $refund_count ?>)</span></a></li>
            <? } ?>
            <? if ($debit_note_count > 0 && (check_permission(DEBIT_NOTE_VIEW_ALL) || check_permission(DEBIT_NOTE_VIEW_HIS_OWN))) { ?>
                <li role="presentation"><a onclick="reload_debit_notes()" aria-controls="DebitNoteBlock" role="tab" data-toggle="tab" href="#DebitNoteBlock" title="<?php __('Debit Notes') ?>"><span class="one-line"><?php __('Debit Notes') ?> (<? echo $debit_note_count ?>)</span></a></li>
            <? } ?>
            <? if (isset($insuranceInvoice) && $insuranceInvoice) { ?>

                <li role="presentation"><a aria-controls="InsuranceBlock" role="tab" data-toggle="tab" href="#InsuranceBlock" title="<?php __('Insurance Receipt') ?>"><span class="one-line"><?php __('Insurance Receipt') ?></span></a></li>
            <? } ?>
            <? if (empty($invoice['Invoice']['is_offline']) && $emailCount > 0) { ?>
                <li role="presentation"><a class="tab-grey" aria-controls="NotificationsBlock" role="tab" data-toggle="tab" href="#NotificationsBlock" title="<?php __("Latest notification emails for invoice") ?>"><span  class="one-line"> <?php __("Notification Emails") ?> <small class="badge badge-md bg-default"><?php echo $emailCount; ?></small></span></a></li>
            <? } ?>
            <? if (false && $client_credit_count>0){ ?>
                <li  role="presentation"><a onclick="window.open('<? echo Router::url(array('controller'=>'clients','action'=>'view',$invoice['Invoice']['client_id'],'#'=>'LatestPayments')) ?>');setTimeout(function(){ $('a[href=#InvoiceBlock]').focus().tab('show'); }, 250);" aria-controls="LatestPayments" role="tab" data-toggle="tab" href="#ClientPayment" title="<?php __('Client Payment') ?>" id="ViewClient"><span class="one-line"><?php __("Client Payment") ?></span></a></li>
            <? } ?>
            <?php
            if(ifPluginActive(INSTALLMENT_AGREEMENT_PLUGIN) && isset($hasInstallmentAgreement) && $hasInstallmentAgreement){ ?>
                <li role="presentation"><a aria-controls="AgreementInstallmentsBlock" role="tab" data-toggle="tab"  onclick="reloadAgreementInstallments()" href="#AgreementInstallmentsBlock" title="<?php __('Installment Agreement') ?>"><span class="one-line"><?php __('Installment Agreement') ?> </span></a></li>
            <?php }
            ?>

            <?php if(isset($showRenewalsBlock) && $showRenewalsBlock){ ?>
                <li role="presentation"><a aria-controls="MembershipRenewalsBlock" role="tab" data-toggle="tab" onclick="reloadMembershipRenewals()" href="#MembershipRenewals" title="<?php __('Membership Subscriptions') ?>"><span class="one-line"><?php __('Membership Subscriptions') ?> </span></a></li>
            <?php } ?>
            <?php if(isset($profits) && $profits) { ?>
                <li role="presentation"><a aria-controls="InvoiceProfit" role="tab" data-toggle="tab"  href="#InvoiceProfit" title="<?php __('Invoice Profit') ?>"><span class="one-line"><?php __('Invoice Profit') ?> </span></a></li>
            <?php } ?>
        </ul>

        <!-- / tabs-buttons -->

        <div class="tab-content responsive">
            <div class="tab-pane " id="DetailsBlock">
                <div class="preview-details details-Block">
                    <?php echo $this->element ('custom_forms/view_fields_form2') ;?>
                </div>

            </div>
            <div class="tab-pane active" id="InvoiceBlock">
                <div class="preview-invoice invoice-Block">
                    <? if (IS_PC) {
                        $invoice_print_method = settings::getValue(InvoicesPlugin, 'invoice_print_method');
                        ?>
                        <div class="invoice-template">
                            <iframe frameBorder="0" src="<?php echo Router::url(array('action' => 'preview', $invoice['Invoice']['id'],$alt_template)); ?><?php if($invoice_print_method==settings::OPTION_PRINT_PDF){?>.pdf?inline=true <?php } ?>" id="InvoicePreview" name="InvoicePreview" width="100%" height="800"></iframe>

                        </div>
                    <? } else { ?>
                        <div class="responsive-invoice">
                            <img style="max-width:100%;width:100%;"  src="<?php echo Router::url(array('action' => 'view', $invoice['Invoice']['id'],$alt_template)); ?>.jpeg" />
                        </div>
                    <? } ?>
                </div>

            </div>
            <?php if (isset($hasInstallmentAgreement) && $hasInstallmentAgreement)  { ?>
                <div class="tab-pane is-loading" id="AgreementInstallmentsBlock">

                    <iframe id="agreementInstallmentsIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>

                </div>
            <?php } ?>

            <?php if(isset($showRenewalsBlock) && $showRenewalsBlock){ ?>
                <div class="tab-pane" id="MembershipRenewals">
                    <iframe id="MembershipRenewalsIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
                </div>
            <?php } ?>

            <!-- InvoiceProfit -->
            <?php  if(isset($profits) && $profits) { ?>
                <div class="tab-pane" id="InvoiceProfit">
                    <table class="list-table table table-hover tableClass">
                        <thead>
                        <tr>
                            <th class=""> <?php __('Name') ?> </th>
                            <th class=""> <?php __('Qty') ?>  </th>
                            <th class=""> <?php __('Selling Price') ?>  </th>
                            <th class=""> <?php __('Average Price') ?>  </th>
                            <th class=""> <?php __('Profit') ?>  </th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach($profits['items'] as $profitItem) { ?>
                            <tr>
                                <td class=""><?= $profitItem['name']; ?>

                                    <div class="store_handle">
                                        <?php
                                        if(!empty($profitItem['tracking_data'])){

                                            $text = $profitItem['tracking_data']['text'];
                                            $url = $profitItem['tracking_data']['url'];
                                            echo "<a class='force' href='$url' style='color: #0000ff; text-decoration: none;'>$text</a>";
                                        }
                                        ?>
                                    </div>
                                </td>
                                <td class=""><?= $profitItem['quantity']; ?></td>
                                <td class=""><?= format_price($profitItem['selling_price'], $currencyCode); ?></td>
                                <td class=""><?= format_price($profitItem['average_cost'], $currencyCode); ?></td>
                                <td class=""><?= format_price($profitItem['profit'], $currencyCode); ?></td>
                            </tr>
                        <?php } ?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="4"> <?php __('Total') ?></td>
                            <td><b><?= format_price($profits['total'], $currencyCode); ?></b></td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            <?php } ?>
            <?php
            if (ifPluginActive(FollowupPlugin)) {

                //if (check_permission(View_All_Attachments_And_Notes_For_All_Clients) or check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients)or check_permission(Add_Notes_Attachments_For_All_Clients) or (check_permission(Add_Notes_Attachments_For_His_Assigned_Clients_Only) and $is_assignd == true )) {
//                debug ( $post_count ) ;
                if ($post_count > 0) {
                    ?>
                    <div class="tab-pane" id="NotesBlock">
                        <?php echo $this->element('clients/client_note' , ['item_type' => Post::INVOICE_TYPE , 'item_id' => $invoice['Invoice']['id']]); ?>
                    </div>
                    <?
                }
                if (array_sum((array)$appointment_status_counts ) > 0  ) {
                    ?>
                    <div class="tab-pane" id="AppointmentsBlock">
                        <div class="panel panel-default">
                            <div class="panel-body ">
                                <div class="panel panel-default no-margin" style="border-bottom: none;">
                                    <div class="panel-heading" style="border-bottom: none;">

                                        <div  class="btn-group  pull-right pos3">
                                            <?php foreach ( $appointment_statuses as $k => $s ){?>
                                                <a class=" btn btn-default reload_status <?php if ( $k == 0 ) {?>active<?php } ?>"  title="<? echo $s ?>" status="<?php echo $k;?>"  ><?php echo $s . " ($appointment_status_counts[$k])";?></a>
                                            <?php }?>
                                        </div>
                                        <div class="clearfix"></div>
                                    </div>
                                </div>
                                <div class="row" >
                                    <div  class="col-md-12" id="AppointmentsBlock_rel">
                                        <div class="notifcation-loader"><div class="inner-loader"></div></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <?
                }
                //}
            }
            ?>

            <?php if ($isInvoiceHasPayment) { ?>
                <div class="tab-pane " id="PaymentsBlock">
                    <div class="invoice-payments content-area invoice-Block">
                        <?php
                            echo $this->element('invoices/advance_invoices',['advanceInvoices'=>$advance_invoices , 'invoice'=>$invoice, 'isAdvancePaymentActive' => $isAdvancePaymentActive]);
                        ?>
                        <?php if ( count($invoice['InvoicePayment']) > 0 ) { ?>
                        <h3><?php printf(__('Payments for invoice %s', true), '#' . $invoice['Invoice']['no']) ?>
                            <?php if (check_permission(Invoices_Add_Invoice_Payments) || (check_permission(Invoices_Add_Payments_to_All) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) { ?>
                                <a href="<?php echo Router::url(array('action' => 'add_payment', $invoice['Invoice']['id'])); ?>" class="btn btn-success pull-right add-payment"><?php __("Add Payment") ?></a>
                            <? } ?></h3>
                        <div class="clear"></div><br>

                        <?php
                        if (!empty($invoice['InvoicePayment'])):
                            $links = array();
                            $links[] = $html->link(__('View', true), array('controller' => 'invoice_payments', 'action' => 'view', '%id%',$invoice['Invoice']['id']), array('class' => 'View'));

                            if( ! empty($invoicePaymentsPrintableTemplates) )
                            {
                                $invoicePaymentPrintLink = $html->link(__('Print', true), array('controller' => 'printable_templates', 'action' => 'print_saved','%id%', $invoicePaymentsPrintableTemplates[0]['PrintableTemplate']['id'], $invoicePaymentsPrintableTemplates[0]['PrintableTemplate']['type']),array('target'=> (!(IS_MOBILE_APPLICATION || IS_MOBILE))? '_blank': '','class' => 'View'));
                            }else{
                                $invoicePaymentPrintLink = $html->link(__('Print', true), array('controller' => 'invoice_payments', 'action' => 'print', '%id%',$invoice['Invoice']['id']), array('target'=> (!(IS_MOBILE_APPLICATION || IS_MOBILE))? '_blank': '','class' => 'Print'));
                            }
//                    dd($invoicePaymentPrintLink);
                            $links[] = $invoicePaymentPrintLink;
                            $staff_id = getAuthStaff('id');
                            if ($isPosOpenedOrNotPos) {
                                if (check_permission(Invoices_Add_Invoice_Payments) && !check_permission(Invoices_Add_Payments_to_All)) {
                                    $links['edit'] = array('php_expression' => '$row["InvoicePayment"]["staff_id"]==' . $staff_id . ';', 'url' => $html->link(__('Edit', true), array('controller' => 'invoice_payments', 'action' => 'edit', '%id%', '?' => array('back' => 'invoice')), array('class' => 'view-payment Edit', 'title' => __('Edit', true))));
                                    $links['delete'] = array('php_expression' => '$row["InvoicePayment"]["staff_id"]==' . $staff_id . ';', 'url' => $html->link(__('Delete', true), array('controller' => 'invoice_payments', 'action' => 'delete', '%id%', $invoice['Invoice']['id']), array('class' => 'view-payment Delete', 'title' => __('Delete', true))));
                                } elseif (check_permission(Invoices_Add_Payments_to_All)) {
                                    $links['edit'] = array('url' => $html->link(__('Edit', true), array('controller' => 'invoice_payments', 'action' => 'edit', '%id%', '?' => array('back' => 'invoice')), array('class' => 'view-payment Edit', 'title' => __('Edit', true))));
                                    $links['delete'] = $html->link(__('Delete', true), array('controller' => 'invoice_payments', 'action' => 'delete', '%id%', $invoice['Invoice']['id']), array('class' => 'Delete'));
                                }
                            }
                            if ($cannot_edit_payments) {
                                unset($links["edit"]);
                            }
                            ?>
                            <?
                            if (IS_PC) {
                                $invoicePayments = array();
                                foreach ($invoice['InvoicePayment'] as $i => $payment):
                                    $invoicePayments[$i]['InvoicePayment'] = $payment;
                                    $invoicePayments[$i]['Invoice'] = $invoice['Invoice'];
                                endforeach;
                                echo $list->adminResponsiveList($payment_list, 'payment_row', array('statuses' => $invoicePaymentStatus, 'payment_methods' => $invoicePaymentMethods, 'actions' => $links), array('no_paging' => true));
                                ?>

                                <br/>
                            <? } else { ?>
                                <?php
                                $invoicePayments = array();
                                foreach ($invoice['InvoicePayment'] as $i => $payment):
                                    $invoicePayments[$i]['InvoicePayment'] = $payment;
                                    $invoicePayments[$i]['Invoice'] = $invoice['Invoice'];
                                endforeach;
                                echo $list->adminResponsiveList($invoicePayments, 'payment_row', array('statuses' => $invoicePaymentStatus, 'payment_methods' => $invoicePaymentMethods, 'actions' => $links), array('no_paging' => true));
                                ?>

                            <? } ?>
                            <div class="clear"></div>
                        <?php else: ?>
                            <div class="Notemessage"><?php __('No payments for this invoice yet') ?></div>
                        <?php endif; ?>
                        <?php } ?>
                        <?php if(!empty($advance_invoices)) {
                            echo $this->element('payments/advance-invoice-info');
                        }else{
                            echo $this->element('payments/invoice-info');
                        } ?>
                    </div>
                </div>
            <?php } ?>
            <? if (check_permission(Add_New_Purchase_Orders) && ifPluginActive(InventoryPlugin) && $stock_count > 0 && empty($invoice['Invoice']['draft'])) { ?>
                <div class="tab-pane" id="StockBlock">
                    <?php echo $this->element('invoice_stock'); ?>
                </div>
            <?php } ?>
            <? if ($refund_count > 0 && empty($invoice['Invoice']['draft'])) { ?>
                <div class="tab-pane" id="RefundedBlock">
                    <?php echo $this->element('invoices/invoice_refunded'); ?>
                </div>
            <?php } ?>
            <? if ($debit_note_count > 0 && (check_permission(DEBIT_NOTE_VIEW_ALL) || check_permission(DEBIT_NOTE_VIEW_HIS_OWN))) { ?>
                <div class="tab-pane" id="DebitNoteBlock">
                    <?php echo $this->element('invoices/debit_notes'); ?>
                </div>
            <?php } ?>
            <? if (isset($insuranceInvoice) && $insuranceInvoice) { ?>
                <div class="tab-pane" id="InsuranceBlock">
                    <?php echo $this->element('invoices/invoice_insurance'); ?>
                </div>
            <?php } ?>

            <?php if(ifPluginActive(CREDIT_PLUGIN) && $hasCreditCharge && check_permission(MANAGE_CREDIT_CHARGES)){ ?>
                <div class="tab-pane" id="CreditBlock">
                    <iframe id="CreditChargeIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
                </div>
            <?php } ?>

            <?php if(ifPluginActive(CREDIT_PLUGIN) && $hasCreditUsage && check_permission(MANAGE_CREDIT_CHARGES)){ ?>
                <div class="tab-pane" id="CreditUsageBlock">
                    <iframe id="CreditUsageIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
                </div>
            <?php } ?>

            <?
            if (empty($invoice['Invoice']['is_offline']) && $emailCount > 0) {
                $email_logs = array();
                foreach ($invoice['EmailLog'] as $i => $email_log) {
                    debug($email_log['sent_date']);
                    $date = ($email_log['sent_date']);
                    if (!$date) {
                        $date = ($email_log['created']);
                    }
                    $date = format_date($date) . date(' H:i', strtotime($date));
                    $invoice['EmailLog'][$i]['time'] = $date;
                    if (!IS_PC)
                        $email_logs[]['EmailLog'] = $invoice['EmailLog'][$i];
                }
                ?>


                <div class="tab-pane" id="NotificationsBlock">
                    <div class="invoice-email-logs content-area invoice-Block">
                        <h3><?php printf(__('Email history for invoice %s', true), '#' . $invoice['Invoice']['no']) ?></h3>
                        <?php if (!empty($invoice['EmailLog'])): ?>
                            <? if (IS_PC) { ?>
                                <table class="table table-striped b-light" width="100%">
                                    <tr class="table-header">
                                        <th><?php echo __('Subject'); ?></th>
                                        <th><?php echo __('From'); ?></th>
                                        <th><?php echo __('To'); ?></th>
                                        <th><?php echo __('Sent Date'); ?></th>
                                        <th><?php echo __('Actions'); ?></th>
                                    </tr>
                                    <?php foreach ($invoice['EmailLog'] as $email_log): ?>
                                        <tr>
                                            <td> <?php echo $email_log['subject']; ?> </td>
                                            <td> <?php echo htmlentities($email_log['send_from']); ?> </td>
                                            <td> <?php echo htmlentities($email_log['send_to']); ?> </td>
                                            <td> <?php echo $email_log['time']; ?>  </td>
                                            <td>
                                                <ul class="action">
                                                    <li><?php echo $html->link(__('View', true), array('controller' => 'email_logs', 'action' => 'view', $email_log['id']), array('class' => 'View')); ?></li>
                                                </ul>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </table>
                                <?
                            } else {

                                echo $list->adminResponsiveList($email_logs, 'general_row', array('model' => 'EmailLog', 'main_field' => 'subject', 'secondary_field' => 'time', 'default_action' => false, 'actions' => array(array('url' => $html->link(__('View', true), array('controller' => 'email_logs', 'action' => 'view', '%id%'), array('class' => 'View'))))), array('no_paging' => true));
                            }
                            ?>
                        <?php else: ?>
                            <div class="Notemessage"><?php __('No emails sent for this invoice yet') ?></div>
                        <?php endif; ?>
                        <div class="clear"></div>
                    </div>
                </div>
            <? } ?>
            <div class="tab-pane" id="TimelineBlock">
                <script type="text/javascript">
                    var timeline_url = "<? echo Router::url(array('action' => 'timeline', $invoice_id)) ?>";
                    var timeline_row_url = "<? echo Router::url(array('action' => 'timeline_row')) ?>";
                </script>
                <?php echo $this->element('timeline'); ?>
            </div>
            <?php if (count($relatedProductionPlans)) { ?>
                <div class="tab-pane" id="ProductionPlansBlock">
                    <iframe id="productionPlanIframe" src="/v2/owner/entity/production_plan/list?iframe=1<?= $productionPlansFilterStr ?>&hide_filters=1&hide_header_actions=1" style="width: 100%; min-height: 500px;border: 0"></iframe>
                </div>
            <?php } ?>
            <?php if (check_permission(REQUISITION_VIEW) && $enable_requisitions){?>
                <div class="tab-pane" id="RequisitionBlock">
                </div>
            <?php } ?>
        </div>

    </div>
    <?php if (isset($hasInstallmentAgreement) && $hasInstallmentAgreement)  { ?>
        <script>
            // reloadAgreementInstallments();

            function reloadAgreementInstallments() {
                if (document.getElementById('agreementInstallmentsIframe').src == "about:blank") {
                    document.getElementById('agreementInstallmentsIframe').src = "/v2/owner/invoice_installment_agreements/view_agreement_with_installments/<?php echo $invoice['Invoice']['id'] ?>?iframe=1&sort_field=due_date-ASC";
                    document.getElementById('agreementInstallmentsIframe').onload = function () {
                        removeLoader('#AgreementInstallmentsBlock');
                    }
                }
            }
        </script>
    <?php } ?>

    <?php if(isset($showRenewalsBlock) && $showRenewalsBlock){ ?>
        <script>
            reloadMembershipRenewals();
            function reloadMembershipRenewals() {
                if (document.getElementById('MembershipRenewalsIframe').src == "about:blank") {
                    document.getElementById('MembershipRenewalsIframe').src = "/v2/owner/invoices/membership_renewals?invoice_id=<?php echo $invoice['Invoice']['id'] ?>&iframe=1";
                }
            }


        </script>
    <?php } ?>

    <?php if ( !empty ( $invoiceDocs  ) || (!empty($invoice['Attachments'])) ) {?>
        <!--start attachments panel-->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3><?= __('Attachments')?></h3>
            </div>
            <div class="panel-body">
                <?
                foreach($invoiceDocs as $doc){
                    echo $this->element('invoices/invoice_doc_view',array('doc'=>$doc));
                }
                ?>

               <?php
                require('partials/view_attachments.ctp');
                ?>

            </div>
        </div>
        <!--end start attachments panel-->
    <?php }?>
</div>

<div class="modal fade" id="PaymentView" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div style="max-width:800px;max-height:500px" class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
            <div class="modal-body">
                <iframe id="Paymentlink" width="100%" height="100%" src="" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>
<!-- Global Delete Modal -->
<div class="modal fade" id="modalDelete" tabindex="-1" aria-labelledby="modalDeleteLabel" aria-hidden="true">
    <div class="modal-dialog compat modal-dialog-centered" style="max-width: 450px;">
        <div class="modal-content modal-delete bg-white pb-12" style="border-radius: 4px !important;overflow: hidden;">
            <form action="" method="GET" data-md-form="true">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <i class="mdi mdi-trash-can-outline text-danger"></i>
                    <div data-md-form-message="true"></div>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="submit" class="btn btn-danger"><?= __t('Yes') ?></button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= __t('No') ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- /Global Delete Modal -->
<?php
echo $javascript->link(array('jquery.lightbox_me', 'invoices/view-invoice_v4.js?v=2'));
echo $html->css(array('owner-page', 'tabs'), false, ['inline' => false]);
$this->set('hasTabs', true);
?>
<script type="text/javascript" src="/js/jjlc.min.js"></script>

<script type="text/javascript">
    item_id = "<?php echo $invoice['Invoice']['id'] ?>";
    item_type = "<?php echo Post::INVOICE_TYPE  ?>";
    <?php if ( $enable_requisitions ){?>
    var requisition_item_id = '<?php echo $invoice['Invoice']['id']?>';
    var requisition_item_type = '<?php echo Requisition::ORDER_TYPE_INVOICE?>';
    function reload_requisitions(){

        $("#RequisitionBlock").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');

        $('#RequisitionBlock').load('/owner/requisitions/summary/' + requisition_item_id + '/'+requisition_item_type, function (){
                        $('.has-tip').each(function(){
                                $(this).qtip({content:$('#'+$(this).data('tip')).html(), style: {
                                classes: 'qtip-bootstrap'
                            }, position: {my: 'top center', at: 'bottom center'}});
                        });
        });
    }
    <?php } ?>
    $(document).ready(function() {
        // show active tab on reload

        reload_appointments($('#statuses').val());
        // remember the hash in the URL without jumping
        $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {

            if (history.pushState) {
                history.pushState(null, null, '#' + $(e.target).attr('href').substr(1));
            } else {
                location.hash = '#' + $(e.target).attr('href').substr(1);
            }
        });
        $(".reload_status").click ( function () {
            $(".reload_status").removeClass ( 'active')
            $(this).addClass ( 'active')
            reload_appointments ( $(this).attr('status'))
        })
        $("#statuses").change ( function () {
            reload_appointments ($(this).val() ) ;
        })

        $('.clickable').each(function(){
        $(this).attr('onclick','');

        });
        if(IS_MOBILE!=1){
        $('.payment_menu').each(function(){
        console.log( $(this).data('menu-id'));
        $table=$('*[data-table-id="'+$(this).data('menu-id')+'"]');
        $('<td><ul class="inline-menu">'+$(this).html()+'</ul></td>').insertAfter( $table.find('.invoice_view_clickable') );

        });
        }

        if(window?.last_non_completed_id){
            var invoicesDrafts = JSON.parse(JJLC.getItem("invoicesDrafts"));
            delete invoicesDrafts[window?.last_non_completed_id.replaceAll("'", "").replaceAll('"', "")];
            JJLC.setItem("invoicesDrafts", JSON.stringify(invoicesDrafts))
        }
    });
    function reload_creditCharges() {
        document.getElementById('CreditChargeIframe').src = '/v2/owner/invoices/creditCharges?invoice_id=<?php echo $invoice['Invoice']['id'] ?>&iframe=1';
    }
    function reload_creditUsages() {
        document.getElementById('CreditUsageIframe').src = '/v2/owner/invoices/creditUsages?invoice_id=<?php echo $invoice['Invoice']['id'] ?>&iframe=1';
    }
    $('a[aria-controls="TimelineBlock"]').on('click', function(e) {
        reload_filter()
    });
    $('a[aria-controls="StockBlock"]').on('click', function(e) {
        reload_stock();
    });

    <?php if ($last_non_completed_id) { ?>
        var last_non_completed_id = '<?= $last_non_completed_id ?>' || null;
    <?php } ?>



    if (location.hash !== '') {
        $('a[href="' + location.hash + '"]').tab('show');
        $('a[aria-controls="' + location.hash.replace('#', '') + '"]').click();
    }
    function updateStatus() {
//        $('#item_status').load('<?php echo Router::url(array('action' => 'status', $id)) ?>');
    }

    $("#productionPlanIframe").on("load", function () {
        var listingTable = $(this).contents().find('.listing-table')[0];
        if (!listingTable){
            $('#ViewInvoice').click()
            $('#productionPlanListItem').addClass('d-none')
            return;
        }
        var dataset = listingTable.dataset;
        var newCount = dataset.totalCount || 0;
        var countSpan = $('span#productionPlanCount');
        countSpan.text(newCount)
    })


<?php if(IS_MOBILE){ ?>
    $(document).ready(function() {
        if (window.location.href.includes("print:1") && typeof Android === 'undefined') {
            return print_iframe('<?php echo Router::url(array('action' => 'preview', $id, 'print' => 1)); ?>');
        }
    });
<?php } ?>
//<![CDATA[
    $('#InvoicePreview').load(function() {
<?php if (empty($this->params['named']['print'])) { ?>
            $('#InvoicePreview').contents().find('body').css({background: '#BCC5CD', 'padding': '20px'});
<? } else { ?>
            print_inv();
<? } ?>

if($('#InvoicePreview').attr('src').indexOf('pdf') == -1){
this.style.height =this.contentWindow.document.body.offsetHeight + 50 + 'px';
}

    });

    $("#ViewInvoice").on("click", function(){
        setTimeout(() => {
            if($('#InvoicePreview').attr('src').indexOf('pdf') == -1){
            document.getElementById("InvoicePreview").style.height = document.getElementById("InvoicePreview").contentWindow.document.body.offsetHeight + 50 + 'px';
        }
    }, 50);

    })
    var print_log_url = "<? echo Router::url(array('action' => 'print_log', $this->params['pass'][0])) ?>";

//]]></script>
<script>
    var useSignature = '<?php echo empty(Settings::getValue(ETA_PLUGIN, "use_signature"))? 0 : 1?>';
    var einvoce_type = 'invoice';
    var pinCode = '<?php echo Settings::getValue(ETA_PLUGIN, "dongle_pin")?>';
    var issuer = '<?php echo Settings::getValue(ETA_PLUGIN, "certificate_name")?>';
</script>
<?php if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN)) { ?>
    <script type="text/javascript" src="/js/e-invoice_v<?php echo JAVASCRIPT_VERSION ?>.js"></script>
<?php } ?>
<?php echo $javascript->link(array('invoice-view_v4.js?v=2.1')); ?>
<?php echo $this->element('on-print', ['printFunction' => 'print_inv']) ?>

<!-- Edit Stock Modal -->
<div class="modal" id="edTransModal" tabindex="-1" role="dialog" aria-labelledby="ShownotiLabel">
    <div style="max-withd:100%;width:850px;" class="modal-dialog modal-md"  role="document">
        <div class="modal-content">
            <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
            <div class="modal-header">
                <h4 class="modal-title" id="ShowactionLabel"><?php __('Edit Stock Transaction') ?></h4>
            </div>
            <div id="" class="modal-body">
                <iframe src="" frameborder="0" height="380" width="99.6%"></iframe>
            </div>
        </div>
    </div>
</div>
<script>
$(document).on('click', '.btn-download', function (e) {
    e.preventDefault();
    e.stopPropagation();    // stop Fancybox
    // Force download using hidden <a>
    const url = $(this).attr('href');
    const a = document.createElement('a');
    a.href = url;
    a.setAttribute('download', '');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
});
</script>