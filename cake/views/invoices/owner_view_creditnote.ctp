<?php

use Izam\Daftra\Common\Utils\ElectronicInvoicesStatus;

echo $html->css(array('product_view_v'.CSS_VERSION.'.css', 'time-tracker.css?v=2', 'fontello.css'));
echo $html->css('timeline_v'.CSS_VERSION.'.css');
echo $html->css('bootstrap-multiselect.css');
echo $javascript->link(array('bootstrap-multiselect.js'));
//echo $javascript->link(array('adjust_views.js'));
echo $html->css('view_new_style.css');
if($is_rtl){echo $html->css('view_new_style_ar.css'); }
echo $javascript->link(array('jqueryui'));
$html->css(array('jqueryui'), false, ['inline' => false]);
$owner = getAuthOwner();
$site = getCurrentSite();
$full_name = $owner['first_name'] . ' ' . $owner['last_name'];
$dateFormats = getDateFormats('std');
$dateFormat = $dateFormats[getCurrentSite('date_format')];
$sitename = $site['business_name'];

$id = $invoice['Invoice']['id'];
$enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions');
if ( $enable_requisitions){
    $delivery_status = Requisition::$delivery_status_list[$invoice['Invoice']['requisition_delivery_status']];
}
?>
<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/show/show.min.css?v=".CSS_VERSION, null, []); ?>

<div class="pages-head">
    <div class="container">
        <h1 class="left no-margin">
            <?php printf(__('Credit Note %s', true),'<span class="title_id_with_hash">'. '#' . $invoice['Invoice']['no'].'</span>'); ?>
            <?php
            if( $invoice['Invoice']['draft']==1){
            echo $this->element('invoice-status');
            }
            ?>

            <?php if ( $enable_requisitions && !empty($invoice['Invoice']['requisition_delivery_status']) && $requisition_exists) { ?>
                    <div class="status">
                        <span style="color: <?php echo $delivery_status['color']?>;background-color: <?php echo $delivery_status['background-color']?>" class="status-symble "><?php echo __($delivery_status['label'] , true ); ?></span>
                    </div>
            <?php } ?>
            
            <div class="sub-headings">
            <?php if ($owner && $owner['staff_id'] == 0): ?>

                <span class="invoice-client sub-heading sub-heading2 no-"><?php echo __('Recipient', true) . ' : ' . sprintf('%s', $html->link($invoice['Invoice']['client_business_name'], array('controller' => 'clients', 'action' => 'view', $invoice['Invoice']['client_id']), array('target' => '_blank')), $invoice['Invoice']['client_business_name']); ?></span>
            <?php endif; ?>

            <?php if ($owner && $owner['staff_id'] != 0 && check_permission(Clients_View_his_own_Clients)): ?>
                <span class="invoice-client sub-heading sub-heading2 ww"><?php printf(__('Recipient: %s', true), $html->link($invoice['Invoice']['client_business_name'], array('controller' => 'clients', 'action' => 'view', $invoice['Invoice']['client_id']), array('target' => '_blank'))); ?></span>
            <?php endif; ?>


            <?php  if (!$invoice["Invoice"]["draft"]) {
                echo $this->element('view_page_invoice_status',[
                    'invoice_status' => json_decode($invoice['EntityAppData'][0]['data'] ?? null,true),
                    'is_submitted' => !empty($invoice['SubmitEntityAppData'][0]["id"]),
                    'is_sent' => !empty($invoice['SentEntityAppData'][0]["id"]),
                    'have_error' => !empty($invoice['ErrorEntityAppData'][0]["id"])
                ]);
            } ?>

            <?php
            if (ifPluginActive(AccountingPlugin)&&check_permission(VIEW_ALL_JOURNALS)&&!empty($linked_journal)) {
                echo $this->element('linked-journal',['journal'=>$linked_journal]);
            }
            if (ifPluginActive(AccountingPlugin)&&check_permission(VIEW_ALL_JOURNALS)&&!empty($sales_of_cost_journal)) {
                echo $this->element('sales-cost-linked-journal',['journal'=>$sales_of_cost_journal]);
            }
            ?>




               <?php if ($owner && $invoice['Invoice']['subscription_id'] && !empty($subscription)){ ?>
                    <span class="invoice-client sub-heading sub-heading2"><?php echo __('Invoice', true) . ': ' . sprintf('#%s', $html->link($subscription['Invoice']['no'], array('controller' => 'invoices', 'action' => 'view', $subscription['Invoice']['id']), array('target' => '_blank')), $subscription['Invoice']['client_business_name']); ?></span>
                    <?php
                }
                ?>


            <?php if ($owner and $owner['staff_id'] != 0 and check_permission(Clients_View_his_own_Clients)): ?>
                <span class="invoice-client sub-heading sub-heading2"><?php printf(__('Added By: %s', true), $invoice['Invoice']['staff_id'] != 0 ? $html->link($invoice['Staff']['name'], array('action' => 'creditnotes', '?'=>array('staff_id'=>$invoice['Staff']['id'])), array('target' => '_blank')) : $full_name); ?></span>
            <?php endif; ?>
            <?php echo $this->element('tags_view',array('item_id' => $invoice['Invoice']['id'],'model_name' => 'CreditNote','get_ajax' => true)); ?>
            </div>
            <div class="clear"></div>
        </h1>
        <div class="top-actions top-actions-w100-mob m-t-xs">
        <div class="clear"></div>
        <div class="mb-opt-btn"></div>
            <?php  echo $this->element('nav',array('id'=> $invoice['Invoice']['id'])) ; ?>

            <? if (empty($invoice['Invoice']['is_offline']) and $invoice['Invoice']['draft'] != "1") { ?>

                <a href="<?php echo Router::url(array('action' => 'send_to_client', $id)); ?>" class="btn btn-success pull-right m-l-sm"><i class="fa fa-envelope-o"></i> <?php __("Email") ?></a>
            <? } else { ?>
            <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview', $invoice['Invoice']['id'],$alt_template, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'view_refund', $id,$alt_template, 'print' => 1)); ?>" class="btn btn-success pull-right m-l-sm"><i class="fa fa-print"></i> <span class="hidden-xs"><?php __("Print") ?></span></a>
            <? } ?>


            <?php
                $drop = "";

                foreach ($FollowUpStatus as $key => $status_row) {
                    $drop_style[$key] = $status_row['style'];
                    $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $invoice['Invoice']['id'], $key)) . '" ' . $status_row['style'] . ' class="" tabindex="-1"> ' . $status_row['name'] . '</a>';
                    $button_no_link[$key] = '<a href="#" ' . $status_row['style'] . ' class="btn   dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"> ' . $status_row['name'] . '</a>';
                }
                if ( $enable_invoice_status ){
                ?>
                <div class="btn-group status-list">
                    <?
                    foreach ($button as $key => $button_code) {
                        if ($key == $invoice['Invoice']['follow_up_status']) {
                            $drop = $drop_style[$key];
                            echo $button_no_link[$key];
                        }
                    }
                    ?>

                    <button type="button"  <? echo $drop ?> class="btn  btn-grey dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <?
                        if (empty($invoice['Invoice']['follow_up_status'])) {
                            echo __('Select Status', true);
                        }
                        ?> <span class="caret"></span>
                    </button>



                    <ul class="dropdown-menu">
                        <?
                        foreach ($button as $key => $button_code) {
                            if ($key != $invoice['Invoice']['follow_up_status']) { ?>
                                <li><?php echo str_replace('<i class="fa fa-check"></i>', '', $button_code) ?></li>
                                <? } } ?>
                        <li> <a href="/owner/follow_up_statuses/index/<?php echo Post::INVOICE_TYPE?>" class="" tabindex="-1"><i class="fa fa fa-cog"></i> <?php __('Edit Statuses List') ?></a></li>
                    </ul>

                </div>
                <?php }?>
                <div class="clear"></div>
        </div>
        <div class="clear"></div>
       
    </div>
</div>
<div class="invoice-actions btn-group dropdown-btn-group">
    <?php if (check_permission(Invoices_Edit_All_Invoices) || (check_permission(Invoices_Edit_his_own_Invoices) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) { ?>
        <a href="<?php echo Router::url(array('action' => 'edit_creditnote', $id)); ?>" class="btn btn-default btn-sm quick-action-btn">
            <span class="fa fa-pencil" aria-hidden="true"></span>
            <?php __("Edit") ?></a>
    <? } ?>

    <?php if (ifPluginActive(ProductsPlugin)) { ?>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Vouchers', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu ">
                <?php
                $printActions = ['packing_slip' => 'Packing Slip', 'pick_list' => 'Pick List', 'shipping_label' => 'Shipping Label'];
                foreach ($printActions as $type => $translation){
                    $urlParams = ['controller' => 'invoices', 'action' => 'print', $type, $id];
                    if (!IS_PC) {$urlParams['ext'] = 'pdf';} ?>
                    <li><a href="<?php echo Router::url($urlParams); ?>"><?php __($translation) ?> </a></li>
                <?php }

                foreach ($invoiceLayouts as $key => $invoiceLayout) { ?>
                    <li><a href="<?php echo Router::url(array($id, $key)); ?>"><?php echo $invoiceLayout ?></a></li>
                <? } ?>
                <?php echo draw_templates_list($view_templates, $invoice['Invoice']['id']); ?>
            </ul>
        </div>
    <?php } ?>


    <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview', $invoice['Invoice']['id'],$alt_template, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'view_creditnote', $id,$alt_template, 'print' => 1)) ?>" class="btn btn-default btn-sm quick-action-btn">
        <span class="fa fa-print" aria-hidden="true"></span>

        <?php __("Print") ?></a>
    <?php if (count($invoiceLayouts) == 0) {?>
        <a href="<?php echo Router::url(array($id, $alt_template, 'ext' => 'pdf')); ?>" class="btn btn-default btn-sm quick-action-btn" download>
            <span class="fa fa-file-pdf-o" aria-hidden="true"></span>
            <?php __("PDF") ?></a>
    <?} else {?>

        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default">
                <a href="<?php echo Router::url(array($id,$alt_template, 'ext' => 'pdf')); ?>"> <span class="fa fa-file-pdf-o" aria-hidden="true"></span> <? echo __('PDF', true) ?> </a>
            </button>
            <button type="button" class="btn btn-sm btn-default" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <span class="caret"></span>
            </button>
            <ul class="dropdown-menu ">

                <?  foreach ($invoiceLayouts as $key => $invoiceLayout) {?>
                    <li><a href="<?php echo Router::url(array($id, $key, 'ext' => 'pdf')); ?>"><?php echo $invoiceLayout ?></a></li>
                <?}?>
                 <?php echo draw_templates_list($view_templates, $invoice['Invoice']['id']); ?>
            </ul>
        </div>
    <?}?>

    <?php if (!IS_PC) { ?>
        <a href="<?php echo Router::url(array($id, 'ext' => 'jpeg')); ?>" class="btn btn-default btn-sm quick-action-btn">
            <span class="fa fa-file-image-o" aria-hidden="true"></span>
            <?php __("Image") ?></a>
    <? } ?>

    
    
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false">
                <span class="fa fa-share" aria-hidden="true"></span>
                <?php echo sprintf(__('Send Via %s', true), '') ?> <span class="caret"></span>
            </button>

            <ul class="dropdown-menu">
                <?php if (!empty($invoice['Client']['email']) && $invoice['Invoice']['draft'] != "1") { ?>
                    <li><a href="<?php echo Router::url(array('action' => 'send_to_client', $id)); ?>">
                        <span class="fa fa-envelope-o" aria-hidden="true"></span>
                        <?php __("Email") ?></a></li>
                <? } ?>
                <?php if (isset($shareWithSocialMedia) && $shareWithSocialMedia) { ?>
                    <?php
                    foreach ($socialLinks as $key => $socialLink) {
                        ?>
                        <li>
                            <?= $socialLink['link'] ?>
                        </li>
                        <?php
                    }
                    ?>
                <?php } ?>
            </ul>
        </div>
    

    <?php if (check_permission(Invoices_Edit_All_Invoices) || (check_permission(Invoices_Edit_his_own_Invoices) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) { ?>
        <?php if ($invoice['Invoice']['draft']!=1) { ?>
            <a class="btn btn-default btn-sm quick-action-btn" href="<?php echo Router::url(array('action' => 'owner_update_draft', $id, 1)); ?>">
                <span class="fa " aria-hidden="true"></span>
                <?php __("Mark as Draft") ?></a>
        <?php } else { ?>
            <a class="btn btn-default btn-sm quick-action-btn" href="<?php echo Router::url(array('action' => 'owner_update_draft', $id, 0)); ?>">
                <span class="fa fa-check" aria-hidden="true"></span>
                <?php __("Activate") ?></a>
        <? } ?>

        <!-- Electronic Invoices start  -->
        <?php if (ifPluginActive(ETA_PLUGIN)  && !$invoice['Invoice']['draft'] && check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::SYNC_E_INVOICE_ETA) && (settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN, 'use_invoice') || settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN, 'use_receipt'))) { ?>

            <div class="btn-group">
                <button type="button"  <?php   /*echo (
                    !empty($invoice['EntityAppData'][0]["data"]) && in_array(json_decode($invoice['EntityAppData'][0]['data'], true)['status'],  [ElectronicInvoicesStatus::VALID, ElectronicInvoicesStatus::IN_PROGRESS, ElectronicInvoicesStatus::SUBMITTED]) ||
                    empty($invoice['EntityAppData'][0]["data"]) && !empty($invoice['SubmitEntityAppData'][0]["id"])
                ) ? "disabled='disabled'" : ''*/?>  class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <? echo __('ETA', true) ?> <span class="caret"></span>
                </button>
                <ul class="dropdown-menu ">
                    <?php if (settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN, 'use_invoice')) { ?>
                    <li>
                        <a class="send-invoice-eta" <?php   echo (
                            !empty($invoice['EntityAppData'][0]["data"]) && in_array(json_decode($invoice['EntityAppData'][0]['data'], true)['status'],  [ElectronicInvoicesStatus::VALID, ElectronicInvoicesStatus::IN_PROGRESS, ElectronicInvoicesStatus::SUBMITTED]) ||
                            empty($invoice['EntityAppData'][0]["data"]) && !empty($invoice['SubmitEntityAppData'][0]["id"])
                        ) ? "disabled='disabled'" : ''?>  data-id="<?= $invoice['Invoice']['id']?>" data-type="refund_receipt"  href="#">
                            <span class="mdi mdi-receipt" aria-hidden="true"></span>
                            <?php __("Send Invoice") ?></a>
                    </li>
                    <?php } ?>
                    <?php if (settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN, 'use_receipt')) { ?>
                    <li>
                        <a class="send-invoice-eta" <?php   echo (
                            !empty($invoice['EntityAppData'][0]["data"]) && in_array(json_decode($invoice['EntityAppData'][0]['data'], true)['status'],  [ElectronicInvoicesStatus::VALID, ElectronicInvoicesStatus::IN_PROGRESS, ElectronicInvoicesStatus::SUBMITTED]) ||
                            empty($invoice['EntityAppData'][0]["data"]) && !empty($invoice['SubmitEntityAppData'][0]["id"])
                        ) ? "disabled='disabled'" : ''?> data-id="<?= $invoice['Invoice']['id']?>" data-type="return_receipt" href="#">
                            <span class="mdi mdi-paper-roll-outline" aria-hidden="true"></span>
                            <?php __("Send Receipt") ?></a>
                    </li>
                    <?php } ?>
                    <li>
                        <a class=""  <?php   echo (empty($invoice['SubmitEntityAppData'][0]["id"])) ? "disabled='disabled'" : ''?> href="<?php echo "/v2/owner/get_document_state/refund_receipt/" . $id; ?>">
                            <span class="fa fa-refresh" aria-hidden="true"></span>
                            <?php __("Refresh Status") ?></a>
                    </li>
                    <li>

                        <a class=""  <?php   echo (empty($invoice['EntityAppData'][0]["data"]) || (json_decode($invoice['EntityAppData'][0]['data'], true)['status'] != ElectronicInvoicesStatus::VALID)

                        ) ? "disabled='disabled'" : ''?>  href="<?php echo "/v2/owner/cancel_document/refund_receipt/" . $invoice['Invoice']['id']; ?>">
                            <span class="fa fa-remove" aria-hidden="true"></span>
                            <?php __("Cancel Submission") ?></a>
                    </li>
                </ul>
            </div>
        <? } ?>
        <!-- Electronic Invoices end  -->
        <!-- Electronic Invoices end  -->
        <? if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN) && !$invoice['Invoice']['draft']  && !empty(settings::getValueEvenEmpty(\Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_binarySecurityToken'))  && check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::SYNC_E_INVOICE_KSA)) { ?>
            <a href="/v2/owner/zatca/invoice/<?= $id ?>/credit" <?=!empty($invoice['EntityAppData'][0]["data"]) && json_decode($invoice['EntityAppData'][0]['data'], true)['status'] == "Valid" ? "disabled":"" ?> class="btn btn-default btn-sm btn-5 "> <i class="fa fa-send-o"></i> <?php __("Send To ZATCA") ?></a>
        <?php } ?>

        <?php if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN) && !$invoice['Invoice']['draft'] && !empty(settings::getValueEvenEmpty(\Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_binarySecurityToken')) && check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::SYNC_E_INVOICE_KSA)  && json_decode($invoice['EntityAppData'][0]['data'], true)['status'] == "Valid") { ?>
            <a href="/owner/invoices/get_json/<?= $id ?>"  class="btn btn-default btn-sm btn-5 "> <i class="fa fa-download"></i> <?php __("Export XML") ?></a>
        <?php } ?>
        <a class="btn btn-default btn-sm quick-action-btn" href="<?php echo Router::url(array('action' => 'delete', $id)); ?>">
            <span class="fa fa-trash-o" aria-hidden="true"></span>
            <?php __("Delete") ?></a>



    <? } ?>

    <?php echo $this->element('apps/app_buttons'); ?>
    <div class="clearfix"></div>
</div>
<?php
echo $javascript->link(array('owner-view_v' . JAVASCRIPT_VERSION . '.js'));
?>
<div role="tabpanel" class="tabs-box box">

    <ul class="nav nav-tabs responsive">
        <li class="active" role="presentation"><a aria-controls="InvoiceBlock" role="tab" data-toggle="tab" href="#InvoiceBlock" title="<?php __('Preview credit note') ?>" id="ViewInvoice"><span class="one-line"><?php __("Credit Note") ?></span></a></li>


        <?php if ($invoice['Invoice']['created'] > '2016-03-20') { ?>
            <li role="presentation"><a aria-controls="TimelineBlock" role="tab" data-toggle="tab" href="#TimelineBlock" title="<?php __('Timeline for credit note') ?>"><span class="one-line"><?php __('Activity Log') ?>

                    </span></a></li>
        <? } ?>
        <? if (ifPluginActive(InventoryPlugin) && $stock_count > 0 && empty($invoice['Invoice']['draft'])) { ?>

            <li role="presentation"><a aria-controls="StockBlock" role="tab" data-toggle="tab" href="#StockBlock" title="<?php __('Stock') ?>"><span class="one-line"><?php __('Stock') ?> </span></a></li>
        <? } ?>
        <?php if (check_permission(REQUISITION_VIEW) && $enable_requisitions && $requisition_count > 0) { ?>
            <li role="presentation"><a aria-controls="RequisitionBlock" onclick="reload_requisitions()" role="tab" data-toggle="tab" href="#RequisitionBlock" title="<?php __('Requisitions') ?>"><span class="one-line"><?php __('Requisitions') ?></span></a></li>
        <? } ?>

        <? if (empty($invoice['Invoice']['is_offline']) && $emailCount > 0) { ?>
            <li role="presentation"><a  aria-controls="NotificationsBlock" role="tab" data-toggle="tab" href="#NotificationsBlock" title="<?php __("Latest notification emails for credit note") ?>"><span  class="one-line"> <?php __("Notification Emails") ?> <small class="badge badge-md bg-default"><?php echo $emailCount; ?></small></span></a></li>
        <? } ?>
    </ul>

    <!-- / tabs-buttons -->

    <div class="tab-content responsive">
        <div class="tab-pane active" id="InvoiceBlock">
            <div class="preview-invoice invoice-Block">
                <? if (IS_PC) {
                    $invoice_print_method = settings::getValue(InvoicesPlugin, 'invoice_print_method');
                    ?>
                    <div class="invoice-template">
                        <iframe frameBorder="0" src="<?php echo Router::url(array('action' => 'preview', $invoice['Invoice']['id'],$alt_template)); ?><?php if($invoice_print_method==settings::OPTION_PRINT_PDF){?>.pdf?inline=true <?php } ?>" id="InvoicePreview" name="InvoicePreview" width="100%" height="800"></iframe>
                    </div>
                <? } else { ?>
                    <div class="responsive-invoice">
                        <img style="max-width:100%"  src="<?php echo Router::url(array('action' => 'view_creditnote', $invoice['Invoice']['id'],$alt_template)); ?>.jpeg" />
                    </div>
                <? } ?>
            </div>
        </div>

        <?php if (ifPluginActive(InventoryPlugin) && $stock_count > 0 && empty($invoice['Invoice']['draft'])) { ?>
            <div class="tab-pane" id="StockBlock">
                <?php echo $this->element('invoice_stock'); ?>
            </div>
        <?php } ?>
        <?php if (check_permission(REQUISITION_VIEW) && $enable_requisitions){?>
            <div class="tab-pane" id="RequisitionBlock">
            </div>
        <?php } ?>

        <?
        if (empty($invoice['Invoice']['is_offline']) && $emailCount > 0) {
            $email_logs = array();
            foreach ($invoice['EmailLog'] as $i => $email_log) {
                debug($email_log['sent_date']);
                $date = ($email_log['sent_date']);
                if (!$date) {
                    $date = ($email_log['created']);
                }
                $date = format_date($date) . date(' H:i', strtotime($date));
                $invoice['EmailLog'][$i]['time'] = $date;
                if (!IS_PC)
                    $email_logs[]['EmailLog'] = $invoice['EmailLog'][$i];
            }
            ?>


            <div class="tab-pane" id="NotificationsBlock">
                <div class="invoice-email-logs content-area invoice-Block">
                    <h3><?php printf(__('Email history for credit note %s', true), '#' . $invoice['Invoice']['no']) ?></h3>
                    <?php if (!empty($invoice['EmailLog'])): ?>
                        <? if (IS_PC) { ?>
                            <table class="table table-striped b-light" width="100%">
                                <tr class="table-header">
                                    <th><?php echo __('Subject'); ?></th>
                                    <th><?php echo __('From'); ?></th>
                                    <th><?php echo __('To'); ?></th>
                                    <th><?php echo __('Sent Date'); ?></th>
                                    <th><?php echo __('Actions'); ?></th>
                                </tr>
                                <?php foreach ($invoice['EmailLog'] as $email_log): ?>
                                    <tr>
                                        <td> <?php echo $email_log['subject']; ?> </td>
                                        <td> <?php echo htmlentities($email_log['send_from']); ?> </td>
                                        <td> <?php echo htmlentities($email_log['send_to']); ?> </td>
                                        <td> <?php echo $email_log['time']; ?>  </td>
                                        <td>
                                            <ul class="action">
                                                <li><?php echo $html->link(__('View', true), array('controller' => 'email_logs', 'action' => 'view', $email_log['id']), array('class' => 'View view-payment')); ?></li>
                                            </ul>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                            <?
                        } else {

                            echo $list->adminResponsiveList($email_logs, 'general_row', array('model' => 'EmailLog', 'main_field' => 'subject', 'secondary_field' => 'time', 'default_action' => false, 'actions' => array(array('url' => $html->link(__('View', true), array('controller' => 'email_logs', 'action' => 'view', '%id%'), array('class' => 'View view-payment'))))), array('no_paging' => true));
                        }
                        ?>
                    <?php else: ?>
                        <div class="Notemessage"><?php __('No emails sent for this credit note yet') ?></div>
                    <?php endif; ?>
                    <div class="clear"></div>
                </div>
            </div>
        <? } ?>
        <div class="tab-pane" id="TimelineBlock">
            <script type="text/javascript">
                var timeline_url = "<? echo Router::url(array('action' => 'timeline', $invoice_id)) ?>";
                var timeline_row_url = "<? echo Router::url(array('action' => 'timeline_row')) ?>";
            </script>
            <?php echo $this->element('timeline'); ?>
        </div>
    </div>
</div>
<?php
 if ( !empty ( $invoiceDocs  ) || (count($invoice['Attachments'])) ) {?>
    <!--start attachments panel-->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3><?= __('Attachments')?></h3>
        </div>
        <div class="panel-body">
            <?
            foreach($invoiceDocs as $doc){
                echo $this->element('invoices/invoice_doc_view',array('doc'=>$doc));
            }
            ?>

           <?php
            require('partials/view_attachments.ctp');
            ?>

        </div>
    </div>
    <!--end start attachments panel-->
<?php } ?>
<div class="modal fade" id="PaymentView" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div style="max-width:800px;max-height:500px" class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
            <div class="modal-body">
                <iframe id="Paymentlink" width="100%" height="100%" src="" frameborder="0" allowfullscreen></iframe>
            </div>

        </div>
    </div>
</div>

<?php
echo $javascript->link(array('jquery.lightbox_me', 'invoices/view-invoice_v10.js?v=2'));
$html->css(array('owner-page', 'tabs'), false, ['inline' => false]);
$this->set('hasTabs', true);
?>

<script type="text/javascript" src="/js/jjlc.min.js"></script>

<script type="text/javascript">
    var item_id = <?php echo $invoice['Invoice']['id']?>;

    var item_type = 2;

    <?php if ( $enable_requisitions ) { ?>
        var requisition_item_id = '<?php echo $invoice['Invoice']['id']?>';
        var requisition_item_type = '<?php echo Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE?>';
        function reload_requisitions(){
            $("#RequisitionBlock").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');

            $('#RequisitionBlock').load('/owner/requisitions/summary/' + requisition_item_id + '/'+requisition_item_type, function (){
                $('.has-tip').each(function(){
                    $(this).qtip({content:$('#'+$(this).data('tip')).html(), style: {
                            classes: 'qtip-bootstrap'
                        }, position: {my: 'top center', at: 'bottom center'}});
                });
            });
        }
    <?php } ?>

    $(document).ready(function() {
        // show active tab on reload

      //  reload_appointments();
        // remember the hash in the URL without jumping
        $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
            if (history.pushState) {
                history.pushState(null, null, '#' + $(e.target).attr('href').substr(1));
            } else {
                location.hash = '#' + $(e.target).attr('href').substr(1);
            }
        });


        $('.clickable').each(function(){
        $(this).attr('onclick','');

        });
        if(IS_MOBILE!=1){
        $('.payment_menu').each(function(){
        console.log( $(this).data('menu-id'));
        $table=$('*[data-table-id="'+$(this).data('menu-id')+'"]');
        $('<td><ul class="inline-menu">'+$(this).html()+'</ul></td>').insertAfter( $table.find('.invoice_view_clickable') );

        });
        }
        if(window?.last_non_completed_id){
            var invoicesDrafts = JSON.parse(JJLC.getItem("creditDrafts"));
            delete invoicesDrafts[window?.last_non_completed_id.replaceAll("'", "").replaceAll('"', "")];
            JJLC.setItem("creditDrafts", JSON.stringify(invoicesDrafts))
        }

    });
    $('a[aria-controls="TimelineBlock"]').on('click', function(e) {
        reload_filter()
    });
    $('a[aria-controls="StockBlock"]').on('click', function(e) {
        reload_stock();
    });



    if (location.hash !== '') {
        $('a[href="' + location.hash + '"]').tab('show');
        $('a[aria-controls="' + location.hash.replace('#', '') + '"]').click();
    }



//<![CDATA[
    $('#InvoicePreview').load(function() {
<?php if (empty($this->params['named']['print'])) { ?>
            $('#InvoicePreview').contents().find('body').css({background: '#bcc5cd', 'padding': '20px'});
<? } else { ?>
            print_inv();
<? } ?>
    });
    var print_log_url = "<? echo Router::url(array('action' => 'print_log', $this->params['pass'][0])) ?>";


    <?php if ($last_non_completed_id) { ?>
        var last_non_completed_id = '<?= $last_non_completed_id ?>' || null;
    <?php } ?>

//]]></script>
<script>
    var useSignature = '<?php echo empty(Settings::getValue(ETA_PLUGIN, "use_signature"))? 0 : 1?>';
    var einvoce_type = 'credit_note';
    var pinCode = '<?php echo Settings::getValue(ETA_PLUGIN, "dongle_pin")?>';
    var issuer = '<?php echo Settings::getValue(ETA_PLUGIN, "certificate_name")?>';
</script>
<?php if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN)) { ?>
    <script type="text/javascript" src="/js/e-invoice_v<?php echo JAVASCRIPT_VERSION ?>.js"></script>
<?php } ?>
<?php echo $javascript->link(array('invoice-view_v4.js?v=1')); ?>
