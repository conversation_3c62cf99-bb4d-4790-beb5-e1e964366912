<?php

use Izam\Daftra\Common\Utils\PluginUtil;

echo $html->css('nav-tabs', false);
echo $html->css('bootstrap-multiselect.css', null, []);
echo $javascript->link(array('main', 'nicEdit', 'humanize', 'bootstrap-multiselect.js'));

?>
<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/create/create.min.css?v=".CSS_VERSION, null, []); ?>
<div class=" ">
    
        <div class=" " style="padding: 20px">
        <h3><?php __("Sales Adjustment Accounts") ?></h3>
        <div id="flashMessage" class="Sucmessage invoice_accounts_flash_message d-none">
            <?= __t("Sales Adjustment Accounts Updated Successfully") ?></div>
        <form id="invoice-accounts-form">
            <div class="prescription-items m-b-lg journal_ad  multi-accounts-div">
                <div class="  invoice-items">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0"
                        class="table table-bordered b-light items-listing mobile-invoicing" id="listing_table">
                        <tr class="TableHeader table-header active unmovable">
                            <th id="label_unit_price" class="count-cell"><span><?php __("Account") ?></th>
                            <th id="label_unit_price" class="detail-cell"><span><?php __("Account Label") ?></th>
                            <th class="count-cell "><span id="label_description"></th>
                        </tr>
                        <tr class="itemRow fix-clear d-none movable">
                            <td class="td_editable ">
                                <div class="item-name">
                                    <div class="item-wrap">
                                        <?php
                                                echo $this->element('journal_accounts_advanced_search',array('value' => '','options'=>array(
                                                    'input_name' => "data[InvoiceAccount][0][journal_account_id]",
                                                    'id' => "InvoiceAccount0JournalAccountId",
                                                    'class' => 'form-x2 required account-0',
                                                    'empty' => __('Select Account',true),
                                                    'style' => 'width:220px;',
                                                )
                                                ));
                                                ?>
                                    </div>
                                </div>
                            </td>
                            <td class="td_editable item-percentage">
                                <div class="item-name">
                                    <div class="item-wrap">
                                        <div class="form-group">
                                            <?php echo $form->text("InvoiceAccount.0.label", array('label' => false,'div' => '','rows' => 1, 'class' => ' item_name form-control')); ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td data-id="" class="delete-product-cell">
                                <a href="#"
                                    class="removeItem delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020">
                                    <i class="s2020 far fa-minus-circle"></i>
                                </a>
                            </td>
                        </tr>
                        <?php
                                $invoice_accountsSetting = settings::getValue(InvoicesPlugin, "invoice_accounts");
                                $invoice_accounts=json_decode($invoice_accountsSetting ?? "",true);
                                if(!empty($invoice_accounts))
                                {
                                    $counter = 0;
                                    foreach($invoice_accounts as $k => $account)
                                    {
                                        ?>
                        <tr class="itemRow fix-clear movable">
                            <td class="td_editable ">
                                <div class="item-name">
                                    <div class="item-wrap">
                                        <?php

                                                        echo $this->element('journal_accounts_advanced_search',array('value' => $account['journal_account_id'],'options'=>array(
                                                            'input_name' => "data[InvoiceAccount][$counter][journal_account_id]",
                                                            'id' => "InvoiceAccount{$counter}JournalAccountId",
                                                            'class' => 'form-x2 required journal_account_id account-'.$counter,
                                                            'empty' => __('Select Account',true),
                                                            'style' => 'width:220px;',
                                                            'value' => $k
                                                        )

                                                        ));
                                                        ?>
                                    </div>
                                </div>
                            </td>



                            <td class="td_editable item-percentage">
                                <div class="item-name">
                                    <div class="item-wrap">
                                        <div class="form-group">
                                            <?php echo $form->text("InvoiceAccount.$counter.label", array('value' => $account['label'],'label' => false,'div' => '', 'class' => ' item_name journal_account_name form-control')); ?>
                                        </div>
                                    </div>
                                </div>
                            </td>


                            <td data-id="" class="delete-product-cell">
                                <a href="#"
                                    class="removeItem delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020">
                                    <i class="s2020 far fa-minus-circle"></i>
                                </a>
                            </td>
                        </tr>
                        <?php
                                        $counter++;
                                    }
                                }else{
                                    $k = 0;
                                    ?>
                        <tr class="itemRow fix-clear movable">
                            <td class="td_editable ">
                                <div class="item-name">
                                    <div class="item-wrap">
                                        <?php
                                                    echo $this->element('journal_accounts_advanced_search',array('value' => '','options'=>array(
                                                        'input_name' => "data[InvoiceAccount][$k][journal_account_id]",
                                                        'id' => "InvoiceAccount{$k}JournalAccountId",
                                                        'class' => 'form-x2 required journal_account_id account-'.$k,
                                                        'empty' => __('Select Account',true),
                                                        'style' => 'width:220px;',
                                                    )
                                                    ));
                                                    ?>
                                    </div>
                                </div>
                            </td>
                            <td class="td_editable item-percentage">
                                <div class="item-name">
                                    <div class="item-wrap">
                                        <div class="form-group">
                                            <?php echo $form->text("InvoiceAccount.$k.label", array('label' => false,'div' => '','rows' => 1, 'class' => ' item_name journal_account_name form-control')); ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td data-id="" class="delete-product-cell">
                                <a href="#"
                                    class="removeItem delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020">
                                    <i class="s2020 far fa-minus-circle"></i>
                                </a>
                            </td>
                        </tr>
                        <?php } ?>
                    </table>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table table-bordered b-light "
                        id="taxes-table">
                        <tbody></tbody>
                    </table>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0"
                        class="table  b-light table-bordered  items-listing mobile-invoicing bg-transparent table-totals-s2020">
                        <tr class="items-totals fix-clear border-bottom-0-s2020">
                            <td colspan="" class="tspan new-row-cell p-0">
                                <div class="new-row my-0 d-flex justify-content-end" style="">
                                    <a href="#" id="AddItem"
                                        class="add-row py-3 px-5 my-0 fs-14 btn-s2020 btn-light-s2020 text-dark-blue s2020 font-weight-bold"
                                        tabindex="21"><i
                                            class="s2020 far fa-plus-circle text-primary mr-2"></i><?php __("Add") ?></a>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

            </div>
        </form>
    </div>
 
    <div class="compat" id="modalBtns" >
        <div class="d-flex p-10 border-top gap-6 hstack justify-content-center position-fixed bottom-0 w-100 bg-white">
            <div class="flex-grow-1">
                <button type="button"  class="btn btn-secondary py-8 w-100" data-dismiss="modal"><?php __("Discard") ?></button>
            </div>
            <div class="flex-grow-1">
                <button type="submit" id="saveAccountsBtn" class="btn btn-success py-8 w-100"><?php __("Save") ?></button>
            </div>
        </div>
    </div>

</div>
<script type="text/javascript" src="/js/modals.js"></script>
<?php
echo $javascript->link('functions_ajax_v1');
echo $javascript->link('dynamic_add-remove-rowsV2');
?>
<script>
    function validAccount(value) {
        if(typeof value != 'undefined' && value != '') {
            return true;
        }
        return false;
    }
    var invoiceAccounts = '<?= $invoice_accountsSetting  ?>';
    $('document').ready(function() {
    
        
        function validateFields(){

            var rules = {
                '.journal_account_name': ['notEmpty'],
                'select.journal_account_id': ['validAccount'],

            };

            var validationMessages = {
                notEmpty: __('Required'),
                validAccount: __('Required'),
            };

            return validate(rules, validationMessages);
        }

        var collectFormData = function () {
            var data = [];
            $('.invoice-items .itemRow').each((item,row)=>{
                var accountLabel = row.querySelector('.journal_account_name')?.value || null
                var accountId = row.querySelector('.journal_account_id')?.value || null
                if (accountLabel && accountId){
                    data.push({
                        accountId,
                        accountLabel,
                    })
                }

            })
            return data;
        }

        $('#saveAccountsBtn').on( 'click', function(e){
            if(validateFields()){
            
                $('.prescription-items .error-message').hide();
                var data = collectFormData();
                console.log(data)
                $.ajax({
                    url: '/owner/invoices/update_invoice_accounts',
                    method: "POST",
                    dataType: "JSON",
                    data : {...data},
                    success: function (result) {
                        if(result.status == 200){
                            $('.invoice_accounts_flash_message').toggleClass('d-none')
                             setTimeout(() => {
                                try {
                                    // preferred: same-origin direct call
                                    if (window.parent && window.parent.IzamModal && typeof window.parent.IzamModal.closeModals === 'function') {
                                        window.parent.IzamModal.closeModals();
                                        return;
                                    }

                                    // fallback: postMessage to parent (works cross-origin)
                                    if (window.parent && window.parent !== window) {
                                        window.parent.postMessage({ action: 'closeModalFromIframe', source: 'invoiceAccountsIframe' }, '*');
                                    }
                                } catch (err) {
                                    console.warn('Could not call parent closeModals directly, sending postMessage instead.', err);
                                    if (window.parent && window.parent !== window) {
                                        window.parent.postMessage({ action: 'closeModalFromIframe', source: 'invoiceAccountsIframe' }, '*');
                                    }
                                }
                            }, 500);
                        }
                    }
                });
            }else{
                var inputSelectors = ['.journal_account_name','select.journal_account_id'];
                inputSelectors.forEach((item , index)=>{
                    var $selectorInputs = $(item);
                    $selectorInputs.each(function (index , item) {
                        var caseMessages = item.parentElement.querySelectorAll('.error-message')
                        var inputClassList = Array.from(item.classList);
                        if (inputClassList.includes('form-error')) {
                            caseMessages.forEach((message)=>{
                                message.style.display = 'block'
                            })
                        } else {
                            caseMessages.forEach((message)=>{
                                message.style.display = 'none'
                            })
                        }

                    })
                })
            }
        });


        $('#listing_table').dynamicRows({
            addBtn: '#AddItem',
            allowremovingFirstRow: true,
            afterCloning: function(row) {
                row.find('.filter-option').text('<?php echo __('Select Account',true); ?>');
                row.find('.item_name').addClass('journal_account_name');
                row.find('select').addClass('journal_account_id');
                return row;
            },
            addCallback:  function() {
                var firstRow = $('tr.itemRow:first', '#listing_table');
                var lastRow = $('tr.itemRow:last', '#listing_table');
                var model_ajax_options = firstRow.find('select.selectpicker').data('model_ajax_options');
                var selectionParam = $(lastRow).find('select.selectpicker').get(0);
                $(lastRow).find('.bootstrap-select').last().remove();
                $(selectionParam).selectpicker().ajaxSelectPicker(model_ajax_options);
            }
        });

        $(document).on('click', 'button[data-dismiss="modal"]', function (e) {
    e.preventDefault();

    // optional: any cleanup in iframe before telling parent to close
    // ...

    try {
        // preferred: direct same-origin call
        if (window.parent && window.parent.IzamModal && typeof window.parent.IzamModal.closeModals === 'function') {
            window.parent.IzamModal.closeModals();
            return;
        }

        // otherwise fallback to postMessage
        if (window.parent && window.parent !== window) {
            window.parent.postMessage({ action: 'closeModalFromIframe', source: 'discardButton' }, '*');
        }
    } catch (err) {
        // if same-origin access throws, fallback to postMessage
        if (window.parent && window.parent !== window) {
            window.parent.postMessage({ action: 'closeModalFromIframe', source: 'discardButton', error: String(err) }, '*');
        }
    }
});

    });
</script>