<?php echo $html->css(CDN_ASSETS_URL . "s2020/css/show/show.min.css?v=" . CSS_VERSION, null, []); ?>
<?php
use Izam\Daftra\Common\Utils\InvoiceSourceTypesUtil;
echo $html->css(array('jquery.qtip.min', 'product_view_v' . CSS_VERSION . '.css', 'time-tracker.css?v=2', 'fontello.css'));
echo $html->css('timeline_v' . CSS_VERSION . '.css');
echo $html->css('bootstrap-multiselect.css');
echo $javascript->link(array('bootstrap-multiselect.js'));
echo $javascript->link(array('jqueryui', 'jquery.qtip.min'));
//echo $javascript->link(array('adjust_views.js'));
echo $html->css('view_new_style.css');
if ($is_rtl) {
    echo $html->css('view_new_style_ar.css');
}
$html->css(array('jqueryui'), false, ['inline' => false]);
$owner = getAuthOwner();
$site = getCurrentSite();
$dateFormats = getDateFormats('std');
$dateFormat = $dateFormats[getCurrentSite('date_format')];
$sitename = $site['business_name'];
$id = $invoice['Invoice']['id'];
$is_client = $this->params['prefix'] == 'client';
function canEditOrDeleteSalesOrder($owner, $invoice): bool
{
    return check_permission(SALES_ORDER_EDIT_DELETE_ALL_SALES_ORDER) || (check_permission(SALES_ORDER_EDIT_DELETE_HIS_OWN_SALES_ORDER) && ($owner['staff_id'] == $invoice['Invoice']['staff_id'] || $owner['staff_id'] == $invoice['Invoice']['sales_person_id']));
}
function canConvertSalesOrderToInvoice($owner, $invoice, $ownerClients): bool
{
    return check_permission(Invoices_Add_New_Invoices) || (check_permission(INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS) && ($owner['staff_id'] == $invoice['Invoice']['sales_person_id'] || isset($ownerClients[$invoice['Invoice']['client_id']])));
}
$relatedProductionPlans = [];
if(isset($invoice['ProductionPlan'])){
    $relatedProductionPlans =  ProductionPlan::getRelatedProductionPlans($invoice['ProductionPlan']);
    $productionPlansFilterStr= "&filter[source_id]=" . $invoice['Invoice']['id']."&filter[source_type]=sales_order". (count($invoice['ProductionPlan']) <= 20 ? '&hide_page_header=1' :'' );
}
?>
<?php if (!$is_client) { ?>
    <div class="pages-head-s2020">
        <div class="container">
            <div class="row">
                <div class="col-lg-5 ">
                    <div class="<?php if (IS_MOBILE) {
                                    echo "view-title-mobile d-flex justify-content-between";
                                }; ?>">
                        <h1 class="left no-margin view-title-new">
                            <?php echo sprintf(__('Sales Order #%s', true), $invoice['Invoice']['no']); ?>

                            <?php
                            if ($invoice['Invoice']['draft'] == 1) {
                                $status = -1;
                            } else {
                                $status = $invoice['Invoice']['payment_status'];
                            }

                            if (empty($status)) {
                                $status = 0;
                            }

                            switch ($status) {
                                case -1:
                                    $statusClass = 'status-draft';
                                    break;
                                case SALES_ORDER_STATUS_PENDING:
                                    $statusClass = 'blue';
                                    break;
                                case SALES_ORDER_STATUS_PARTIALLY_INVOICED:
                                    $statusClass = 'orange';
                                    break;
                                case SALES_ORDER_STATUS_INVOICED:
                                    $statusClass = 'green';
                                    break;
                            }
                            ?>
                            <div class="status <?php echo 'rate-' . $statusClass ?>">
                                <span class="status-symble <?php echo $statusClass; ?>"><?php echo (!empty($invoice['Invoice']['draft']) ? __('Draft', true) : $salesOrderStatuses[$status]); ?></span>
                            </div>
                        </h1>
                        <?php
                    if(IS_MOBILE){
                        $drop = "";

                        foreach ($FollowUpStatus as $key => $status_row) {
                            $drop_style[$key] = $status_row['style'];
                            $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $invoice['Invoice']['id'], $key)) . '" tabindex="-1"><span ' . $status_row['style'] . ' class="badge rounded-3px shadow-light"><span>' . $status_row['name'] . '</span></span></a>';
                            $button_no_link[$key] = '<a href="#" ' . $status_row['style'] . ' class="btn  not dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"> ' . $status_row['name'] . '</a>';
                        }
                        if ( $enable_sales_order_status ){
                        ?>
                        <div class="flex-shrink-0">
                            <div class="btn-group status-list status-list-mobile">
                                <?
                                foreach ($button as $key => $button_code) {
                                    if ($key == $invoice['Invoice']['follow_up_status']) {
                                        $drop = $drop_style[$key];
                                        echo $button_no_link[$key];
                                    }
                                }
                                ?>

                                <button type="button"  <? echo $drop ?> class="btn <?php echo empty($drop) ? 'btn-grey' : '' ?> dropdown-toggle not" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <?
                                    if (empty($invoice['Invoice']['follow_up_status'])) {
                                        echo __('Select Status', true);
                                    }
                                    ?> <span class="caret"></span>
                                </button>

                                <ul class="dropdown-menu dropdown-menu-right">
                                    <?
                                    foreach ($button as $key => $button_code) {
                                        if ($key != $invoice['Invoice']['follow_up_status']) { ?>
                                            <li><?php echo str_replace('<i class="fa fa-check"></i>', '', $button_code) ?></li>
                                            <? } } ?>
                                    <li> <a href="/owner/follow_up_statuses/index/<?php echo Post::SALES_ORDER_TYPE?>" class="py-3" tabindex="-1"><i class="fa fa fa-cog"></i> <?php __('Edit statuses list for sales order') ?></a></li>
                                </ul>

                            </div>
                        </div>
                    <?php }
                        }
                    
                    ?>
                    </div>
                    <div class="sub-headings <?php if (IS_MOBILE) {
                                                    echo "sub-headings-mobile";
                                                }; ?>">
                        <?php if ($owner['staff_id'] == 0 || ($owner && $owner['staff_id'] != 0 && check_permission(Clients_View_his_own_Clients))) { ?>
                            <span class="invoice-client sub-heading sub-heading2"><?php printf(__('Recipient: %s', true), $html->link($invoice['Invoice']['client_business_name'], array('controller' => 'clients', 'action' => 'view', $invoice['Invoice']['client_id']), array('target' => '_blank'))); ?></span>
                        <?php } ?>
                        <?php if ($owner and $owner['staff_id'] != 0 and check_permission(Clients_View_his_own_Clients)) : ?>
                            <span class="invoice-client sub-heading sub-heading2"><?php printf(__('Added By: %s', true), $invoice['Invoice']['staff_id'] != 0 ? $html->link($invoice['Staff']['name'], array('action' => 'index', '?' => array('staff_id' => $invoice['Staff']['id'])), array('target' => '_blank')) : $full_name); ?></span>
                        <?php endif; ?>
                        <?php echo $this->element('tags_view', array('item_id' => $invoice['Invoice']['id'], 'model_name' => 'SalesOrder', 'get_ajax' => true)); ?>
                    </div>
                    <?php if (!IS_MOBILE) { ?>
                        <div class="clearfix"></div>
                    <?php } ?>
                </div>
                <div class="col-lg-7">
                <?php if (IS_PC) { ?>
                    <div class="top-actions top-actions-w100-mob">
                        <div class="mb-opt-btn"></div>
                        <?= $this->element('navigation', ['entityKey' => $entity_key, 'id' => $id]); ?>
                        <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview_sales_order', $id, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'view_sales_order', $id, 'print' => 1)); ?>" class="btn btn-lg btn-primary-s2020 btn-addon pull-right add-new-btn m-r-sm"><i class="fa fa-print"></i><span class="hidden-xs"> <?php __("Print Sales Order") ?></span></a>
                        <? if ($this->params['prefix'] == 'owner' && !$invoice['Invoice']['draft']) { ?>
                            <?php if ($this->params['prefix'] == 'owner' && $invoice['Invoice']['payment_status'] != SALES_ORDER_STATUS_INVOICED) : ?>
                                <? if (canConvertSalesOrderToInvoice($owner, $invoice, $ownerClients)) { ?>
                                    <a href="<?php echo Router::url(array('controller' => 'sales_orders', 'action' => 'convert_to_invoice', $id)); ?>" class="btn btn-lg btn-success btn-addon pull-right add-new-btn m-r-sm"><i class="fa fa-usd"></i> <span class="hidden-xs"><?php __("Convert To Invoice") ?></span></a>
                                <? } ?>
                            <?php endif; ?>
                        <? } else { ?>
                            <? if ($invoice['Invoice']['draft'] == "1") { ?>
                                <?php if (check_permission(Invoices_Edit_All_Invoices) || (check_permission(Invoices_Edit_his_own_Invoices) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) { ?>
                                    <a href="<?php echo Router::url(array('action' => 'update_draft', $id, 0)); ?>" class="btn btn-lg btn-success btn-addon pull-right add-new-btn m-r-sm"><i class="fa fa-usd"></i> <span class="hidden-xs"><?php __("Issue Sales Order") ?></span></a>
                            <?
                                }
                            }
                            ?>
                        <? } ?>
                        <?php
                        $drop = "";

                        foreach ($FollowUpStatus as $key => $status_row) {
                            $drop_style[$key] = $status_row['style'];
                            $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $invoice['Invoice']['id'], $key,1)) . '" tabindex="-1"><span class="badge rounded-3px shadow-light" ' . $status_row['style'] . '>' . $status_row['name'] . '</span></a>';
                            $button_no_link[$key] = '<a href="#" ' . $status_row['style'] . ' class="btn  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"> ' . $status_row['name'] . '</a>';
                        }
                        ?>

                        <?php if  ( $enable_sales_order_status && ifPluginActive(FollowupPlugin)) {?>
                            <div class="btn-group status-list pull-right d-flex">
                                <?
                                foreach ($button as $key => $button_code) {
                                    if ($key == $invoice['Invoice']['follow_up_status']) {
                                        $drop = $drop_style[$key];
                                        echo $button_no_link[$key];
                                    }
                                }
                                ?>

                                <button type="button"  <? echo $drop ?> class="btn s2020 <?php echo empty($drop) ? 'btn-secondary' : '' ?> dropdown-toggle not" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <?
                                    if (empty($invoice['Invoice']['follow_up_status'])) {
                                        echo __('Select Status', true);
                                    }
                                    ?> <span class="ml-1 caret"></span>
                                </button>

                                <ul class="dropdown-menu">
                                    <?
                                    foreach ($button as $key => $button_code) {
                                        if ($key != $invoice['Invoice']['follow_up_status']) { ?>
                                            <li><?php echo str_replace('<i class="fa fa-check"></i>', '', $button_code) ?></li>
                                        <? } } ?>
                                    <li> <a href="/owner/follow_up_statuses/index/<?php echo Post::SALES_ORDER_TYPE?>" class="" tabindex="-1"><i class="fa fa fa-cog"></i> <?php __('Edit statuses list for sales order') ?></a></li>
                                </ul>

                            </div>
                        <?php }?>
                        <div class="clearfix"></div>
                    </div>
                    <?php } else { ?>
                        <div class="actions-mobile d-flex">

                        <div class="mb-opt-btn btn-action-mob"></div>

                        <a href="#" class="btn-action-mob btn btn-dark p-0 toggleSubHeader"><i class="w-auto h-auto fs-22 m-0 fa fa-exclamation-circle"></i></a>

                        <? if ($this->params['prefix'] == 'owner' && empty($invoice['Invoice']['is_offline'])) { ?>
                            <a href="<?php echo Router::url(array('action' => 'owner_send_to_client', $id)); ?>" class="btn-action-mob btn btn-blue p-0"><i class="w-auto h-auto fs-22 m-0 fa fa-envelope-o"></i> <span class="hidden-xs"><?php __("Email to Client") ?></span></a>
                        <? }?>
                        <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview_sales_order', $id, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'view_sales_order', $id, 'print' => 1)); ?>" class="btn-action-mob btn btn-blue p-0"><i class="w-auto h-auto fs-22 m-0 fa fa-print"></i></a>
                        <?php if ($this->params['prefix'] == 'owner' && $invoice['Invoice']['payment_status'] != SALES_ORDER_STATUS_INVOICED): ?>
                            <? if (canConvertSalesOrderToInvoice($owner, $invoice, $ownerClients)) { ?>
                                <a href="<?php echo Router::url(array('controller' => 'sales_orders', 'action' => 'convert_to_invoice', $id)); ?>" class="btn-action-mob btn btn-green p-0"><i class="w-auto h-auto fs-22 m-0 fa fa-usd"></i></a>
                            <?}?>
                        <?php elseif ($invoice['Invoice']['draft'] == 1): ?>
                            <?php
                                 if (check_permission(Invoices_Edit_All_Invoices) || (check_permission(Invoices_Edit_his_own_Invoices) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) { ?>
                                <a href="<?php echo Router::url(array('action' => 'update_draft', $id, 0)); ?>" class="btn-action-mob btn btn-green p-0"><i class="w-auto h-auto fs-22 m-0 fa fa-usd"></i></a>
                            <?}?>
                        <?php endif; ?>

                    </div>
                    <?php } ?>
                </div>
            </div>



        </div>
    </div>
<?php } else { ?>
    <div class="pages-head-s2020 d-flex align-items-center">
        <div class="flex-fill">
            <div class="container">
                <div class="row-flex align-items-center">
                    <div class="col-flex-md-6">
                        <div class="pages-head-title">
                            <h2 class="my-0 py-0">
                                <?php printf(__('Sales Order #%s', true), '<span class="title_id_with_hash" style="color:#000;font-size:18px;">' . $invoice['Invoice']['no'] . '</span>'); ?>
                                <?php
                                if ($invoice['Invoice']['draft'] == 1) {
                                    $status = -1;
                                } else {
                                    $status = $invoice['Invoice']['payment_status'];
                                }

                                if (empty($status)) {
                                    $status = 0;
                                }

                                switch ($status) {
                                    case -1:
                                        $statusClass = 'status-draft';
                                        break;
                                    case SALES_ORDER_STATUS_PENDING:
                                        $statusClass = 'blue';
                                        break;
                                    case SALES_ORDER_STATUS_PARTIALLY_INVOICED:
                                        $statusClass = 'orange';
                                        break;
                                    case SALES_ORDER_STATUS_INVOICED:
                                        $statusClass = 'green';
                                        break;
                                }
                                ?>
                                <span class="<?php echo 'rate-' . $statusClass ?>">
                                    <span class="status pl-4 s2020 status-<?php echo $statusClass ?>">
                                        <i class="cir mr-1"></i>
                                        <span><?php echo (!empty($invoice['Invoice']['draft']) ? __('Draft', true) : $salesOrderStatuses[$status]); ?></span>
                                    </span>
                                </span>
                            </h2>
                            <?php if ($this->params['prefix'] == 'owner') : ?>
                                <span class="pt-1 invoice-client sub-heading sub-heading2 no-margin"><?php echo __('Recipient', true) . ': ' . sprintf('%s', $html->link($invoice['Invoice']['client_business_name'], array('controller' => 'clients', 'action' => 'view', $invoice['Invoice']['client_id']), array('target' => '_blank')), $invoice['Invoice']['client_business_name']); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-flex-md-6 d-flex justify-content-end pt-2 pt-lg-0">
                        <div class="d-none d-sm-block">
                            <?php if ($this->params['prefix'] == 'owner' && $invoice['Invoice']['payment_status'] != SALES_ORDER_STATUS_INVOICED) : ?>
                                <?php if (canConvertSalesOrderToInvoice($owner, $invoice, $ownerClients)) { ?>
                                    <a href="<?php echo Router::url(array('action' => 'convert_sales_order_to_invoice', $id)); ?>" class="btn s2020 btn-icn btn-success-2-s2020 font-weight-medium ml-2 m-r-sm"><i class="fa fa-usd"></i> <span class=""><?php __("Convert To Invoice") ?></span></a>
                                <?php } ?>
                            <?php endif; ?>
                            <?php
                            $drop = "";
                            foreach ($FollowUpStatus as $key => $status_row) {
                                $drop_style[$key] = $status_row['style'];
                                $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $invoice['Invoice']['id'], $key,1)) . '" ' . $status_row['style'] . ' class="" tabindex="-1"> ' . $status_row['name'] . '</a>';
                                $button_no_link[$key] = '<a href="#" ' . $status_row['style'] . ' class="btn  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"> ' . $status_row['name'] . '</a>';
                            }
                            ?>
                            <?php if  ( $enable_sales_order_status && ifPluginActive(FollowupPlugin)) {?>
                                <div class="btn-group status-list pull-right d-flex">
                                    <?php
                                    foreach ($button as $key => $button_code) {
                                        if ($key == $invoice['Invoice']['follow_up_status']) {
                                            $drop = $drop_style[$key];
                                            echo $button_no_link[$key];
                                        }
                                    }
                                    ?>
                                    <button type="button"  <?php echo $drop ?> class="btn not btn-grey dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <?
                                        if (empty($invoice['Invoice']['follow_up_status'])) {
                                            echo __('Select Status', true);
                                        }
                                        ?> <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php
                                        foreach ($button as $key => $button_code) {
                                            if ($key != $invoice['Invoice']['follow_up_status']) { ?>
                                                <li><?php echo str_replace('<i class="fa fa-check"></i>', '', $button_code) ?></li>
                                            <?php } } ?>
                                        <li> <a href="/owner/follow_up_statuses/index/<?php echo Post::SALES_ORDER_TYPE?>" class="" tabindex="-1"><i class="fa fa fa-cog"></i> <?php __('Edit statuses list for sales order') ?></a></li>
                                    </ul>
                                </div>
                            <?php } ?>
                        </div>
                        <?php echo $this->element('tags_view', array('item_id' => $invoice['Invoice']['id'], 'model_name' => 'SalesOrder', 'get_ajax' => true)); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .mobile-fixed-bottom-actions {
            display: none;
        }

        @media screen and (max-width: 576px) {
            .mobile-fixed-bottom-actions {
                border: 0;
                position: fixed;
                bottom: 0;
                z-index: 999;
                width: 100%;
                right: 0;
                left: 0;
                margin: 0;
                padding: 15px;
                display: block;
                background: linear-gradient(0, rgba(0, 0, 0, 0.48), transparent);
            }

            .mobile-fixed-bottom-actions .mobile-fixed-bottom-actions__content {
                width: 90%;
                margin: 0 auto;
                float: none;
            }
        }
    </style>
    <div class="mobile-fixed-bottom-actions">
        <div class="mobile-fixed-bottom-actions__content">
            <div class="d-flex">
                <div class="ml-2">
                    <div class="dropup">
                        <button class="d-block btn btn-lg btn-default dropdown-toggle dropdown-parent btn-body-s2020" type="button" data-toggle="dropdown">
                            <span class="fa fa-ellipsis-h"></span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview_sales_order', $id, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'view_sales_order', $id, 'print' => 1)) ?>">
                                    <span><i class="s2020 fas fa-print fs-16 mr-2"></i><?php __("Print") ?></span>
                                </a>
                            </li>
                            <?php if (is_array($invoiceLayouts) && count($invoiceLayouts) == 0) { ?>
                                <li>
                                    <a href="<?php echo Router::url(array($id, 'ext' => 'pdf')); ?>">
                                        <span><i class="s2020 fas fa-file-pdf fs-16 mr-2"></i><?php __("PDF") ?></span>
                                    </a>
                                </li>
                            <?php } else { ?>
                                <li>
                                    <a href="<?php echo Router::url(array($id, 'ext' => 'pdf')); ?>">
                                        <span><i class="s2020 fas fa-file-pdf fs-16 mr-2"></i><?php __("PDF") ?></span>
                                    </a>
                                </li>
                                <?php foreach ($invoiceLayouts as $key => $invoiceLayout) { ?>
                                    <li>
                                        <a href="<?php echo Router::url(array($id, $key, 'ext' => 'pdf')); ?>">
                                            <span><i class="s2020 fas fa-file-pdf fs-16 mr-2"></i><?php echo $invoiceLayout ?></span>
                                        </a>
                                    </li>
                                <?php } ?>
                            <?php } ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php } ?>

<?php if (!$is_client) { ?>
    <div class="invoice-actions btn-group dropdown-btn-group">

        <?php if ($this->params['prefix'] == 'owner') : ?>
            <?php if (canEditOrDeleteSalesOrder($owner, $invoice)) { ?>
                <a href="<?php echo Router::url(array('action' => 'edit_sales_order', $id)); ?>" class="btn btn-default btn-sm quick-action-btn"><span aria-hidden="true" class="fa fa-pencil"></span> <?php __("Edit") ?></a>
            <? } ?>
        <?php endif; ?>

        <?php if ($this->params['prefix'] == 'owner' and canEditOrDeleteSalesOrder($owner, $invoice)) { ?>
            <a class="btn btn-default btn-sm quick-action-btn" href="<?php echo Router::url(['controller' => 'sales_orders', 'action' => 'delete', $id]); ?>">
                <span class="fa fa-trash-o" aria-hidden="true"></span>
                <?php __("Delete") ?></a>
        <? } ?>
        <?php if (!empty($invoice['Client']['email']) && $this->params['prefix'] == 'owner') { ?>

        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false">
                <span class="fa fa-share" aria-hidden="true"></span>
                <?php echo sprintf(__('Send Via %s', true), '') ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                        <li><a href="<?php echo Router::url(array('action' => 'owner_send_to_client', $id)); ?>">
                            <span class="fa fa-envelope-o" aria-hidden="true"></span>
                            <?php __("Email") ?></a></li>
            </ul>
        </div>
        <?php } ?>

        <? if (ifPluginActive(FollowupPlugin) && $this->params['prefix'] == 'owner') { ?>
            <a href="/v2/owner/entity/post/create/<?= Post::SALES_ORDER_TYPE . '/' . $id ?>" class=" btn btn-default btn-sm btn-5 "> <i class="fa fa-book"></i> <?php __("Add Note / Attachment") ?></a>
        <?php  } ?>


        <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview_sales_order', $id, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'view_sales_order', $id, 'print' => 1)) ?>" class="btn btn-default btn-sm "><span aria-hidden="true" class="fa fa-print"></span> <?php __("Print") ?></a>


        <?php
        if (count($invoiceLayouts) == 0) {
        ?>
            <a href="<?php echo Router::url(array($id, 'ext' => 'pdf')); ?>" class="btn btn-default btn-sm quick-action-btn" download>
                <span class="fa fa-file-pdf-o" aria-hidden="true"></span>
                <?php __("PDF") ?></a>
        <?
        } else {
        ?>

            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-default">
                    <a href="<?php echo Router::url(array($id, 'ext' => 'pdf')); ?>"> <span class="fa fa-file-pdf-o" aria-hidden="true"></span> <? echo __('PDF', true) ?> </a>
                </button>
                <button type="button" class="btn btn-sm btn-default" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu ">

                    <?
                    foreach ($invoiceLayouts as $key => $invoiceLayout) {
                    ?>
                        <li><a href="<?php echo Router::url(array($id, $key, 'ext' => 'pdf')); ?>"><?php echo $invoiceLayout ?></a></li>
                    <?
                    }
                    ?>
                </ul>
            </div>
        <?
        }
        ?>

        <?php
        if (ifPluginActive(ProductsPlugin)) {
        ?>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <? echo __('Vouchers', true) ?> <span class="caret"></span>
                </button>
                <ul class="dropdown-menu ">

                <?php
                $printActions = ['packing_slip' => 'Packing Slip', 'pick_list' => 'Pick List', 'shipping_label' => 'Shipping Label'];
                foreach ($printActions as $type => $translation){
                    $urlParams = ['controller' => 'invoices', 'action' => 'print', $type, $id];
                    if (!IS_PC) {$urlParams['ext'] = 'pdf';}
                    ?>
                    <li><a href="<?php echo Router::url($urlParams); ?>"><?php __($translation) ?> </a></li>
                    <?php
                }
                ?>
                    <?
                    foreach ($invoiceLayouts as $key => $invoiceLayout) {
                    ?>
                        <li><a href="<?php echo Router::url(array($id, $key)); ?>"><?php echo $invoiceLayout ?></a></li>
                    <?
                    }
                    ?>
                    <?php echo draw_templates_list($view_templates, $invoice['Invoice']['id']); ?>
                </ul>
            </div>
        <?php } ?>

        <?php if (!IS_PC) { ?>
            <a href="<?php echo Router::url(array($id, 'ext' => 'jpeg')); ?>" class="btn btn-default btn-sm quick-action-btn">
                <span class="fa fa-file-image-o" aria-hidden="true"></span>
                <?php __("Image") ?></a>
        <? } ?>
        <?php if(ifPluginActive(MANUFACTURING_PLUGIN) && check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::ADD_PRODUCTION_PLANS) &&  $invoice['Invoice']['draft'] != 1) { ?>
            <a href="/v2/owner/entity/production_plan/create?invoice_id=<?= $id ?>" class="btn btn-default btn-sm quick-action-btn">
                <span class="mdi mdi-file-replace-outline" aria-hidden="true"></span>
                <?php __("Convert To Production Plan") ?></a>
        <? } ?>

    </div>
<?php } ?>

<?php
echo $javascript->link(array('owner-view_v' . JAVASCRIPT_VERSION . '.js'));
?>
<div role="tabpanel" class="tabs-box box clearfix">

    <ul class="nav nav-tabs responsive">
        <li class="active"><a href="#SalesOrderBlock" title="<?php __('Sales Order') ?>" id="ViewInvoice" aria-expanded="false" aria-controls="actions-tab" role="tab" data-toggle="tab"><span class="one-line"><?php __("Sales Order") ?></span></a></li>

        <?php if ($invoiceCount > 0) { ?>
            <li role="presentation">
                <a aria-controls="RelatedInvoiceBlock" role="tab" data-toggle="tab" onclick="reload_RelatedInvoices()" href="#RelatedInvoiceBlock" title="<?php __('Invoice') ?>">
                    <span class="one-line"><?php __('Invoices') ?> </span>
                </a>
            </li>
        <?php } ?>

        <?php if ($this->params['prefix'] == 'owner' && $invoice['Invoice']['created'] > '2016-03-20') { ?>
            <li role="presentation">
                <a aria-controls="ActivityLogBlock" role="tab" data-toggle="tab" href="#ActivityLogBlock" title="<?= __t('Activity Log for Sales Order') ?>">
                    <span class="one-line"><?= __('Activity Log') ?></span>
                </a>
            </li>
        <? } ?>
        <?php if (count($relatedProductionPlans)) { ?>
            <li role="presentation" id="productionPlanListItem">
                <a aria-controls="ProductionPlansBlock" role="tab" data-toggle="tab" href="#ProductionPlansBlock" title="<?= __t('Sales Order Production Plans') ?>">
                    <span class="one-line"><?= __('Production Plans') . " (<span id='productionPlanCount'>" .count($relatedProductionPlans)  . "</span>)" ?></span>
                </a>
            </li>
        <? } ?>
        <?php
        if (ifPluginActive(FollowupPlugin)) {

            if ($post_count > 0) {
        ?>
                <li role="presentation"><a aria-controls="actions-tab" role="tab" data-toggle="tab" onclick="reload_posts()" href="#NotesBlock" title="<?php __('Notes / Attachments') ?>"><span class="one-line"><?php __('Notes / Attachments') ?> <small class="counter"> (<? echo $post_count ?>)</small> </span></a></li>
        <?php
            }
        }
        ?>
        <? if ((empty($invoice['Invoice']['is_offline']) && $emailCount > 0) && $this->params['prefix'] == 'owner') { ?>
            <li class=""><a href="#NotificationsBlock" title="<?php __("Latest notification emails for the sales order") ?>" aria-expanded="false" aria-controls="actions-tab" role="tab" data-toggle="tab"><span class="one-line"> <?php __("Notification Emails") ?> <small class="counter">(<?php echo $emailCount; ?>)</small></span></a></li>
        <? } ?>
    </ul>

    <div class="tab-content responsive">
        <div class="tab-pane active" id="SalesOrderBlock">
            <?php if ($is_client) { ?>
                <div class="row-flex">
                    <div class="col-flex-lg-9 order-flex-2 order-flex-lg-1">
                    <?php } ?>
                    <div class="preview-invoice invoice-Block">
                        <? if (IS_PC) {
                            $invoice_print_method = settings::getValue(InvoicesPlugin, 'invoice_print_method');
                        ?>
                            <div class="invoice-template">
                                <iframe frameBorder="0" src="<?php echo Router::url(array('action' => 'preview_sales_order', $invoice['Invoice']['id'], $alt_template)); ?><?php if ($invoice_print_method == settings::OPTION_PRINT_PDF) { ?>.pdf?inline=true <?php } ?>" id="InvoicePreview" name="InvoicePreview" width="100%" height="900"></iframe>
                            </div>
                        <? } else { ?>
                            <div class="responsive-invoice">
                                <img style="max-width:100%" src="<?php echo Router::url(array('action' => 'view_sales_order', $invoice['Invoice']['id'], $alt_template)); ?>.jpeg" />
                            </div>
                        <? } ?>
                    </div>
                    <?php if ($is_client) { ?>
                    </div>
                    <div class="col-flex-lg-3 order-flex-1 order-flex-lg-2">
                        <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview_sales_order', $id, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'view_sales_order', $id, 'print' => 1)) ?>" class="print btn s2020 btn-icn btn-primary-s2020 font-weight-medium btn-block-s2020 mb-1">
                            <i class="s2020 fas fa-print fs-16"></i>
                            <span><?php __("Print") ?></span>
                        </a>
                        <?php if (is_array($invoiceLayouts) && count($invoiceLayouts) == 0) { ?>
                            <a href="<?php echo Router::url(array($id, 'ext' => 'pdf')); ?>" class="btn s2020 btn-icn quick-action-btn btn-purple-2-s2020 font-weight-medium btn-block-s2020 mb-1">
                                <i class="s2020 fas fa-file-pdf fs-16"></i>
                                <span><?php __("PDF") ?></span>
                            </a>
                        <?php } else { ?>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-default">
                                    <a href="<?php echo Router::url(array($id, 'ext' => 'pdf')); ?>"> <span class="fa fa-file-pdf-o" aria-hidden="true"></span> <? echo __('PDF', true) ?> </a>
                                </button>
                                <button type="button" class="btn btn-sm btn-default" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu ">
                                    <?php foreach ($invoiceLayouts as $key => $invoiceLayout) { ?>
                                        <li><a href="<?php echo Router::url(array($id, $key, 'ext' => 'pdf')); ?>"><?php echo $invoiceLayout ?></a></li>
                                    <? } ?>
                                </ul>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            <?php } ?>
        </div>

        <?php
        if (ifPluginActive(FollowupPlugin)) {

            if ($post_count > 0) {
        ?>
                <div class="tab-pane" id="NotesBlock">
                    <?php echo $this->element('clients/client_note', ['item_type' => Post::SALES_ORDER_TYPE, 'item_id' => $invoice['Invoice']['id']]); ?>
                </div>
            <?
            }
            ?>

        <?
        }

        ?>

        <?php if ($invoiceCount > 0) { ?>
            <div class="tab-pane" id="RelatedInvoiceBlock">
                <iframe id="RelatedInvoiceIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
            </div>
        <?php } ?>

        <?php if ($this->params['prefix'] == 'owner') { ?>
            <div class="tab-pane" id="ActivityLogBlock">
                <iframe src="/v2/owner/activity_logs/entity/iframe?entity_key=sales_order&entity_id=<?= $invoice['Invoice']['id'] ?>&sort=DESC&layout2022=1" style="width: 100%; min-height: 500px;border: 0"></iframe>
            </div>
        <?php } ?>
        <?php if (count($relatedProductionPlans)) { ?>
            <div class="tab-pane" id="ProductionPlansBlock">
                <iframe id="productionPlanIframe" src="/v2/owner/entity/production_plan/list?iframe=1<?= $productionPlansFilterStr ?>&hide_filters=1&hide_header_actions=1" style="width: 100%; min-height: 500px;border: 0"></iframe>
            </div>
        <?php } ?>
        <?php if ($this->params['prefix'] == 'owner') : ?>
            <div class="tab-pane" id="NotificationsBlock" style="background: #fff;">
                <div class="invoice-email-logs content-area invoice-Block">
                    <h3><?php printf(__('Email history for sales order %s', true), '#' . $invoice['Invoice']['no']) ?></h3>
                    <?php if (!empty($invoice['EmailLog'])) : ?>
                        <div class="table-responsive" data-pattern="priority-columns">
                            <table class="table table-small-font  table-striped b-t b-light">
                                <thead>
                                    <tr class="table-header">
                                        <th data-priority="1"><?php echo __('Subject'); ?></th>
                                        <th data-priority="2"><?php echo __('From'); ?></th>
                                        <th data-priority="2"><?php echo __('To'); ?></th>
                                        <th data-priority="2"><?php echo __('Sent Date'); ?></th>
                                        <th data-priority="1"><?php echo __('Actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($invoice['EmailLog'] as $email_log) : ?>
                                        <tr>
                                            <td> <?php echo $email_log['subject']; ?> </td>
                                            <td> <?php echo h($email_log['send_from']); ?> </td>
                                            <td> <?php echo h($email_log['send_to']); ?> </td>
                                            <td>
                                                <?php
                                                $date = ($email_log['sent_date']);
                                                if (!$date) {
                                                    $date = ($email_log['created']);
                                                }
                                                echo format_date($date) . date(' H:i', strtotime($date)); //php8 fix
                                                ?>
                                            </td>
                                            <td>
                                                <ul class="action">
                                                    <li><?php echo $html->link(__('View', true), array('controller' => 'email_logs', 'action' => 'view', $email_log['id']), array('class' => 'View view-payment')); ?></li>
                                                </ul>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else : ?>
                        <div class="Notemessage"><?php __('No emails sent for this sales order yet') ?></div>
                    <?php endif; ?>
                    <div class="clear"></div>
                </div>
            </div>
        <?php endif; ?>
        <div class="tab-pane" id="SalesOrderBlock">

        </div>
    </div>
</div>
<?php
if (!empty($invoiceDocs) || (count($invoice['Attachments']))) { ?>
    <!--start attachments panel-->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3><?= __('Attachments') ?></h3>
        </div>
        <div class="panel-body">
            <?
            foreach ($invoiceDocs as $doc) {
                echo $this->element('invoices/invoice_doc_view', array('doc' => $doc));
            }
            ?>

            <?php
            require('partials/view_attachments.ctp');
            ?>

        </div>
    </div>
    <!--end start attachments panel-->
<?php } ?>

 <!-- Global Delete Modal -->
    <div class="modal fade" id="modalDelete" tabindex="-1" aria-labelledby="modalDeleteLabel" aria-hidden="true">
        <div class="modal-dialog compat modal-dialog-centered" style="max-width: 450px;">
            <div class="modal-content modal-delete bg-white pb-12" style="border-radius: 4px !important;overflow: hidden;">
                <form action="" method="GET" data-md-form="true">
                    <div class="modal-header">
                        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <i class="mdi mdi-trash-can-outline text-danger"></i>
                        <div data-md-form-message="true"></div>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="submit" class="btn btn-danger"><?= __t('Yes') ?></button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= __t('No') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<!-- /Global Delete Modal -->
<script type="text/javascript" src="/js/jjlc.min.js"></script>

<script type="text/javascript">
    var item_id = <?php echo $invoice['Invoice']['id'] ?>;

    var item_type = 3;
    var print_log_url = "<?php echo Router::url(array('action' => 'sales_order_print_log', $this->params['pass'][0])) ?>";
    $('#InvoicePreview').load(function() {
        <?php if (empty($this->params['named']['print'])) { ?>
        <? } else { ?>
            print_inv();
        <? } ?>

        $(document).ready(function() {

            if (window?.last_non_completed_id) {
                var invoicesDrafts = JSON.parse(JJLC.getItem("salesOrderDrafts"));
                delete invoicesDrafts[window?.last_non_completed_id.replaceAll("'", "").replaceAll('"', "")];
                JJLC.setItem("salesOrderDrafts", JSON.stringify(invoicesDrafts))
            }

        });

        if ($('#InvoicePreview').attr('src').indexOf('pdf') == -1) {
            this.style.height = this.contentWindow.document.body.offsetHeight + 50 + 'px';
        }

    });

    $("#productionPlanIframe").on("load", function () {
        var listingTable = $(this).contents().find('.listing-table')[0];
        if (!listingTable){
            $('#ViewInvoice').click()
            $('#productionPlanListItem').addClass('d-none')
            return;
        }
        var dataset = listingTable.dataset;
        var newCount = dataset.totalCount || 0;
        var countSpan = $('span#productionPlanCount');
        countSpan.text(newCount)
    })

    $("#ViewInvoice").on("click", function() {
        setTimeout(() => {
            if ($('#InvoicePreview').attr('src').indexOf('pdf') == -1) {
                document.getElementById("InvoicePreview").style.height = document.getElementById("InvoicePreview").contentWindow.document.body.offsetHeight + 50 + 'px';
            }
        }, 50);
    })


    $(".reload_status").click(function() {
        $(".reload_status").removeClass('active')
        $(this).addClass('active')
    });


    if (location.hash !== '') {
        $('a[href="' + location.hash + '"]').tab('show');
        $('a[aria-controls="' + location.hash.replace('#', '') + '"]').click();
    }

    <?php if ($last_non_completed_id) { ?>
        var last_non_completed_id = '<?= $last_non_completed_id ?>' || null;
    <?php } ?>

    function reload_RelatedInvoices() {
        document.getElementById('RelatedInvoiceIframe').src = '/owner/invoices/index?type=0&source_id=<?= $invoice['Invoice']['id'] ?>&source_type=<?= InvoiceSourceTypesUtil::SALES_ORDER ?>&box=1';
    }
</script>
<!-- <script type="text/javascript">
        $(function() {
<?php if (!IS_PC) { ?>
                $('.invoice-actions').html('<button type="button" style="padding:3px 18px;" class="converted-actions btn btn-lg btn-primary dropdown-toggle pull-right" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fa fa-chevron-down"></i></button><div class="clearfix"></div><ul class="dropdown-menu dropdown-menu-right">' + $('.invoice-actions').html().replace(/<a/g, '<li><a').replace(/<\/a>/g, '</a></li>') + '</ul>')
<? } ?>
        });
    </script> -->
<?php
echo $javascript->link('invoices/view-invoice_v3', 'timeline.js?v=1');
$html->css(array('owner-page', 'tabs'), false, ['inline' => false]);
$this->set('hasTabs', true);

?>