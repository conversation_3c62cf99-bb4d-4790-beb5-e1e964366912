<style>
	.send-treasury button.dropdown-toggle{
		    height: 46px !important;
	}
</style>
<?php echo $html->css(array('jqueryui' , 'jquery-ui-timepicker-addon', 'attachments.css'), false, ['inline' => false]);?>
<link rel="stylesheet" type="text/css" href="/css/bootstrap-select_v<?php echo CSS_VERSION ?>.css" inline="" />
<link rel="stylesheet" type="text/css" href="/css/ajax-bootstrap-select.css" inline="" />

<?php echo $javascript->link(array('jqueryui' ,'jquery-ui-timepicker-addon' ));?>

<script type="text/javascript" src="/js/bootstrap-select.js"></script>
<script type="text/javascript" src="/js/ajax-bootstrap-select.min.js"></script>
<?php 
$formats = getDateFormats('js');
$jsFormat = $formats[getCurrentSite('date_format')];
$breadcrumbs = [
    ['link' => Router::url(['controller' => 'treasuries' , 'action' => 'index'])  ,'title' => __("Treasuries", true )],
    ['link' => '#' ,'title' => __("Transfer",true )],
    ]; 
//if  ( empty ( $this->data['AssetDeprecation']['id']) ) {
//    unset ( $breadcrumbs[1]);
//}
echo $this->element ('breadcrumbs' , ['breadcrumbs' =>array_values ( $breadcrumbs ) ]); ?>
<div class="row send-treasury">
    <?php echo $form->create('Treasury', ['action' => $action ]); ?>
    <div class="col-sm-6">
        <div class="panel panel-default">
            <div class="panel-heading"><?= __('From') ?>:</div>
            <div class="panel-body">
                
                <div class="row">
                    <div class="col-sm-7">
                        <?php echo $form->input('from_treasury_id', ['options' => $withdrawTreasuries,'label' => __('Treasury',true), 'empty' => __("Choose Treasury", true),'class' => 'required INPUT  form-control input-lg', 'div' => 'form-group']); ?>
                        
                    </div>
                    <div class="col-sm-5">
                        <?php echo $form->input('from_currency_code', ['options' => [],'label' => __('Currency',true), 'empty' => __("Choose Currency", true),'class' => 'INPUT  form-control input-lg selectpicker btn-lg', 'div' => 'form-group']); ?>
                    </div>
                </div>
                <br>
                <div class="row">
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label for="email"><?= __('Amount') ?></label>
                            <?php echo $form->input('balance', array('type'=> 'number', 'div' => false,'placeholder' => '0.00', 'class' => 'form-control input-lg', 'label' =>false )); ?>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label for="email"><?= __('Available before') ?></label>
                            <input type="email" class="form-control input-lg" id="from_before" disabled value="0">
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label for="email"><?= __('Available after') ?></label>
                            <input type="email" class="form-control input-lg" id="from_after" disabled value="0">
                        </div>
                    </div>
                </div>
                
            </div>
        </div>	
    </div>
    <div class="col-sm-6">
        <div class="panel panel-default">
            <div class="panel-heading"><?= __('To') ?>:</div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-7">
                        <?php echo $form->input('to_treasury_id', ['options' => $depositTreasuries,'label' => __('Treasury',true), 'empty' => __("Choose Treasury", true),'class' => 'INPUT  form-control input-lg', 'div' => 'form-group']); ?>
                    </div>
                    <div class="col-sm-5">
                        <?php  echo $form->input('to_currency_code', array( 'options' => $all_currencies, 'div' =>'form-group','data-live-search' => 'true', 'class' => 'INPUT  form-control input-lg selectpicker btn-lg', 'label' => __("Exchange Currency",true) ,'empty' => false)); ?>
                    </div>
                </div>
                <div id="rate_row" class="row" style="display:none;">
					<br>
                    <div class="col-sm-12">
                        <?php echo $form->input('currency_rate', ['type' => 'number','class' => 'INPUT  form-control input-lg', 'div' => 'form-group']); ?>
                    </div>
                </div>
				<br>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label for="email"><?= __('Available before') ?></label>
                            <input type="email" class="form-control input-lg" id="to_before" disabled value="0">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label for="email"><?= __('Available after') ?></label>
                            <input type="email" class="form-control input-lg" id="to_after" disabled value="0">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-12">
        <div class="panel panel-default">
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <?php echo $form->input('transfer_date', ['label' => __('Transfer Date',true),'value' => (empty ( $this->data['Treasury']['transfer_date'])? format_datetime(date("Y-m-d H:i:s")) : $this->data['Treasury']['transfer_date']),'class' => 'required INPUT  form-control input-lg', 'div' => 'form-group']); ?>
                    </div>
                    <div class="col-md-6">
                         <?php echo $form->input('notes', ['type' =>'textarea' ,  'label' => __('Notes',true),'class' => 'required INPUT  form-control input-lg','rows' => 2, 'div' => 'form-group']); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <?php if(!$disabled){
                            echo $this->element('new_s3_file_uploader', ['recordAttachments' => $this->data['Attachments'], 'name' => 'data[Treasury][attachment]', 'entityKey' => 'treasury_transfer', 'fieldKey' => 'treasury_transfers.attachment', 'options' => ['multiple' => true, 'sortable' => true, 'mimes' => []]]);
                        } ?>
                    </div>
                </div>
            </div>
         </div>
    </div>
    
</div>
<div class="submit-btn form-end">



    <div class="btn-group">
        <input type="hidden" name="next_action"  id="sbt_btn" value="go_index" />
        <button  type="submit" class="btn btn-success btn-addon btn-lg"><i class="fa fa-check"></i><?php echo  __('Transfer', true) ?></button>
        
    </div>

    <?php
        $cancel_url = Router::url(array('action' => 'index'));
        if ($this->data['Treasury']['from_treasury_id']) {
            $cancel_url = Router::url(array('action' => 'view', $this->data['Treasury']['from_treasury_id']));
        }

     ?>
    <a href="<?php echo $cancel_url ?>"  class="text-danger m-t m-l text-underline">
        <i class="fa fa-reply"></i>

        <?php __('Cancel') ?>

    </a>
</div>
<?php $balance =$this->data['Treasury']['balance'] ; ?>
<script>
let allow_changeToCurrencyCode=true; // it will disabled if bank has currency
var first_from = true ;
var first_balance = true ;
var first_to = true ;
var jsDateFormat = '<?php echo $jsFormat ?>';
var currency_balances = <?php echo json_encode ( $currency_balances )?>  ;
var treasury_currencies = <?php echo json_encode($treasury_currencies)?>;
function calc_from_to ( loop = false ) {
    if ( typeof currency_balances[$("#TreasuryFromTreasuryId").val()] === 'undefined' || currency_balances[$("#TreasuryFromTreasuryId").val()] == null || typeof currency_balances[$("#TreasuryFromTreasuryId").val()][$("#TreasuryFromCurrencyCode").val()] === 'undefined')
    {
        from_balance =0 ;
    }else {
        from_balance = currency_balances[$("#TreasuryFromTreasuryId").val()][$("#TreasuryFromCurrencyCode").val()]
    }
    if ( typeof currency_balances[$("#TreasuryToTreasuryId").val()] === 'undefined' || currency_balances[$("#TreasuryToTreasuryId").val()] == null  ||   typeof currency_balances[$("#TreasuryToTreasuryId").val()][$("#TreasuryToCurrencyCode").val()] === 'undefined')
    {
        to_balance =0 ;
    }else {
        to_balance = currency_balances[$("#TreasuryToTreasuryId").val()][$("#TreasuryToCurrencyCode").val()]
    }
    if ( isNaN (parseFloat($("#TreasuryBalance" ).val()) ))
    {
         treasury_balance = 0;
    }else {
         treasury_balance = parseFloat($("#TreasuryBalance" ).val());
    }
    from_val = parseFloat (from_balance)  - treasury_balance ;
    if ($("#TreasuryFromTreasuryId").val() == ""  )
    {
        from_val = 0 
    }
   if (isNaN ( parseFloat( $("#TreasuryCurrencyRate").val ( ) ) ) ) {
       currency_rate = 0;
   }else {
       currency_rate = parseFloat( $("#TreasuryCurrencyRate").val ( ) )
   }
    
    to_val = parseFloat(to_balance)  + ( parseFloat (treasury_balance) * parseFloat( currency_rate )  );
    if ($("#TreasuryToTreasuryId").val() == ""  )
    {
        to_val = 0 
    }
    balance_from = 0 ;
    <?php if ( !empty ( $this->data['Treasury']['id'] ) ){?>
    if ( $("#TreasuryFromCurrencyCode").val() === '<?php echo $this->data['Treasury']['from_currency_code'];?>' && $("#TreasuryFromTreasuryId").val() == <?php echo $this->data['Treasury']['from_treasury_id'];?>)
    {
        balance_from = <?php echo $balance ; ?> ;
    }
    <?php }?>
    balance_to = 0 ;
    <?php if ( !empty ( $this->data['Treasury']['id'] ) ){?>
    if ( $("#TreasuryToCurrencyCode").val() === '<?php echo $this->data['Treasury']['to_currency_code'];?>' && $("#TreasuryToTreasuryId").val() == <?php echo $this->data['Treasury']['to_treasury_id'];?>)
    {
        balance_to = <?php echo $balance ; ?> ;
    }
    <?php }?>
    
    if ( !isNaN(from_val)){
        $("#from_after").val ( (from_val + balance_from).toFixed(2)) ;
    }else {
        if ( loop ) {
            $("#TreasuryFromCurrencyCode").change ()
        }else {
            $("#from_after").val ( 0) ;
        }
    }
    if ( !isNaN(to_val)){
        $("#to_after").val ( (to_val - balance_to).toFixed(2)) ;
    }else {
        if ( loop ) {
            $("#TreasuryToCurrencyCode").change ()
        }else {
            $("#to_after").val ( 0) ;
        }
    }
}
$("#TreasuryFromTreasuryId").change ( function ( ){
    if ($(this).val() != "" ) {
        currencies = treasury_currencies[$(this).val()] ;
        $("#TreasuryFromCurrencyCode").html ("" ) ;
        $.each (currencies , function ( index , value  ) {
            $("#TreasuryFromCurrencyCode").append ("<option value='"+index+"' >"+value+"</option>" ) ;
        })
       checkBankHasCurrency('#TreasuryFromTreasuryId', '#TreasuryFromCurrencyCode')

       
    }
    <?php if ( !empty ( $this->data['Treasury']['id'] ) ){?>
            if ( first_from ){
                console.log ( 'nour' ) ;
                $("#TreasuryFromCurrencyCode").val('<?php echo $this->data['Treasury']['from_currency_code']?>') ;
                first_from = false ;
            }
    <?php }?>
    $("#TreasuryFromCurrencyCode").change ( )
}) ;
$("#TreasuryBalance" ).keyup(function ( ) {
    calc_from_to(true) ;
    
}) 
$("#TreasuryCurrencyRate").keyup(function ( ) {
    calc_from_to(false) ;
})
$("#TreasuryToTreasuryId").change ( function ( ){
    <?php if ( !empty ( $this->data['Treasury']['id'] ) ){?>
        if ( first_to ){
            console.log ( 'nour to ' ) ;
            $("#TreasuryToCurrencyCode").val('<?php echo $this->data['Treasury']['to_currency_code']?>') ;
            first_to = false ;
        }
    <?php }?>
    $("#TreasuryToCurrencyCode").change();
}) ;
$("#TreasuryFromCurrencyCode").change ( function ( ) {
    $("#rate_row").hide ( 100) ;
    if (allow_changeToCurrencyCode) {
        $("#TreasuryToCurrencyCode").val ($("#TreasuryFromCurrencyCode").val() )
    }
    if ( typeof currency_balances[$("#TreasuryFromTreasuryId").val()] === 'undefined' || currency_balances[$("#TreasuryFromTreasuryId").val()] == null ||  typeof currency_balances[$("#TreasuryFromTreasuryId").val()][$("#TreasuryFromCurrencyCode").val()] === 'undefined'){
        from_val = 0 
    }else {
        from_val = currency_balances[$("#TreasuryFromTreasuryId").val()][$("#TreasuryFromCurrencyCode").val()]
    }
    balance = 0 ;
    <?php if ( !empty ( $this->data['Treasury']['id'] ) ){?>
    if ( $("#TreasuryFromCurrencyCode").val() === '<?php echo $this->data['Treasury']['from_currency_code'];?>' && $("#TreasuryFromTreasuryId").val() == <?php echo $this->data['Treasury']['from_treasury_id'];?>)
    {
        balance = <?php echo $balance ; ?> ;
    }
    <?php }?>
    $("#from_after").val ((from_val - balance).toFixed(2))
    $("#from_before").val ((from_val + balance).toFixed(2));
    $("#TreasuryCurrencyRate").val (1 )
    $("#TreasuryFromCurrencyCode").selectpicker ('refresh')

    if (!$('button[ data-id="TreasuryToCurrencyCode"]').hasClass("disabled")) {
        $('#TreasuryToCurrencyCode').selectpicker('refresh');

    }
    calc_from_to(false)
})
let get_change_rate = function() {
        if ($("#TreasuryToCurrencyCode").val() != "" && $("#TreasuryFromCurrencyCode").val() && $("#TreasuryToCurrencyCode").val() != $("#TreasuryFromCurrencyCode").val()) {
            from = $("#TreasuryFromCurrencyCode").val()
            to = $("#TreasuryToCurrencyCode").val()

            $.ajax({
                url: '<?php echo Router::url(['controller' => 'treasuries', 'action' => 'ajax_convert_currency']) ?>/' + from + '/' + to,
                success: function(rate) {
                    if (rate == -1) {
                        $("#TreasuryCurrencyRate").val('')
                    } else {
                        <?php if (!empty($this->data['Treasury']['currency_rate'])) { ?>
                            if (first_balance) {
                                first_balance = false;
                                rate = <?php echo $this->data['Treasury']['currency_rate']; ?>;
                            }
                        <?php } ?>
                        $("#TreasuryCurrencyRate").val(parseFloat(rate))
                    }
                    $("#rate_row").show(100);
                    calc_from_to(false)
                }
            })
        } else {
            $("#rate_row").hide(100);
            $("#TreasuryCurrencyRate").val(1)
        }
        if (typeof currency_balances[$("#TreasuryToTreasuryId").val()] === 'undefined' || currency_balances[$("#TreasuryToTreasuryId").val()] == null || typeof currency_balances[$("#TreasuryToTreasuryId").val()][$("#TreasuryToCurrencyCode").val()] === 'undefined') {
            to_value = 0;
        } else {
            to_value = currency_balances[$("#TreasuryToTreasuryId").val()][$("#TreasuryToCurrencyCode").val()];
        }
        balance = 0;
        <?php if (!empty($this->data['Treasury']['id'])) { ?>
            if ($("#TreasuryToCurrencyCode").val() === '<?php echo $this->data['Treasury']['to_currency_code']; ?>' && $("#TreasuryToTreasuryId").val() == <?php echo $this->data['Treasury']['to_treasury_id']; ?>) {
                balance = <?php echo $balance; ?>;
            }
        <?php } ?>
        $("#to_after").val((to_value + balance).toFixed(2));
        $("#to_before").val((to_value - balance).toFixed(2));
        calc_from_to(false)

    }

    <?php if (empty($this->data['Treasury']['id'])) { ?>
        $("#TreasuryToCurrencyCode").change(function() {
            get_change_rate()

        })
    <?php } ?>
$("#TreasuryCurrencyRate").keyup(function () {
calc_from_to(false)
})
$(function ( ) {
    $('#TreasuryTransferDate').datetimepicker({dateFormat: jsDateFormat, timeFormat: 'HH:mm'});
    $("#TreasuryFromTreasuryId").change ()
    $("#TreasuryToTreasuryId").change ()
    
})

 
</script>

<?php echo $this->element('js/dimmedSelector'); ?>

<script>
    <?php if (empty($this->data['Treasury']['id'])) { ?>
        checkBankHasCurrency('#TreasuryToTreasuryId', '#TreasuryToCurrencyCode',get_change_rate)
    <?php } ?>

    $('#TreasuryToTreasuryId').change(function() {

        checkBankHasCurrency('#TreasuryToTreasuryId', '#TreasuryToCurrencyCode',get_change_rate)

    });
</script>
<link rel="stylesheet" type="text/css" href="/css/transfer.css?v=9" />

