<?php

// This element used when create invoice with layout
$all_place_holder=PlaceHolder::invoice_get_all_place_holders($invoice['Invoice']);
$all_place_holder += PlaceHolder::invoice_custom_field_place_holder($invoice);
$all_place_holder  = array_merge($all_place_holder, PlaceHolder::invoice_custom_field_value_adder($invoice));
//echo $layout['InvoiceLayout']['label_creditnote_date'] .' NOIUR';
//$layout['InvoiceLayout']['label_invoice_date'] = "" ;
//print_r ( $layout['InvoiceLayout']);
$extra_details=json_decode($invoice['Invoice']['extra_details'],true);
$enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
foreach ($invoice['InvoiceItem'] as $k => $item2) {
    $item2['unit_price'] = (float)$item2['unit_price'];
    $item2['unit_factor'] = (float)$item2['unit_factor'];
    $item2['quantity'] = (float)$item2['quantity'];
    if ($enable_multi_units && !empty($item2['unit_factor']) && !empty($item2['unit_name'])) {
        $invoice['InvoiceItem'][$k]['quantity_written'] = ($item2['quantity']/$item2['unit_factor']).' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
        $invoice['InvoiceItem'][$k]['unit_price_factor'] = round($item2['unit_price']*$item2['unit_factor'],5);//.' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
        $invoice['InvoiceItem'][$k]['item_subtotal'] = round($invoice['InvoiceItem'][$k]['item_subtotal'], 5);
        //$invoice['InvoiceItem'][$k]['unit_price'] = ($item2['unit_price']*$item2['unit_factor']);//.' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
    }else {
        $invoice['InvoiceItem'][$k]['quantity_written'] = format_number((float)$item2['quantity'] );
        $invoice['InvoiceItem'][$k]['unit_price_factor'] = $item2['unit_price'];
    }
    $tracking = Product::displayTrackingData($invoice['InvoiceItem'][$k]['Product']['tracking_type'], $invoice['InvoiceItem'][$k]['tracking_data']);
    $tracking_description = $tracking != ""? "<br/>".$tracking : "" ;
    $invoice['InvoiceItem'][$k]['item'] .= $tracking_description;
    $invoice['InvoiceItem'][$k]['tracking_description'] = $tracking_description;
}

$all_place_holder['{%refund_invoice_no_title%}'] = $layout['InvoiceLayout']['label_invoice_no'];
if($invoice['Invoice']['type']==0){
    
    $head_title= __("Invoice %s",true);

    }elseif($invoice['Invoice']['type']==Invoice::Credit_Note){
     $head_title=__("Credit Note %s",true);   
        $layout['InvoiceLayout']['invoice_title']=empty($layout['InvoiceLayout']['creditnote_title']) ?$layout['InvoiceLayout']['invoice_title'] :$layout['InvoiceLayout']['creditnote_title'] ;
        $layout['InvoiceLayout']['label_invoice_no']=empty($layout['InvoiceLayout']['label_creditnote_no']) ?$layout['InvoiceLayout']['label_invoice_no'] :$layout['InvoiceLayout']['label_creditnote_no'] ;
        $layout['InvoiceLayout']['label_date']=empty($layout['InvoiceLayout']['label_creditnote_date']) ?$layout['InvoiceLayout']['label_date'] :$layout['InvoiceLayout']['label_creditnote_date'] ;

        $layout['InvoiceLayout']['field1']=empty($layout['InvoiceLayout']['label_to']) ?$layout['InvoiceLayout']['field1'] :$layout['InvoiceLayout']['label_to'] ;

//        $layout['InvoiceLayout']['label_invoice_to']=empty($layout['InvoiceLayout']['label_creditnote_to']) ?$layout['InvoiceLayout']['label_invoice_to'] :$layout['InvoiceLayout']['label_creditnote_to'] ;

    }elseif($invoice['Invoice']['type']==Invoice::Refund_Receipt){
        $layout['InvoiceLayout']['invoice_title']=empty($layout['InvoiceLayout']['refundreceipt_title']) ?$layout['InvoiceLayout']['invoice_title'] :$layout['InvoiceLayout']['refundreceipt_title'] ;
        $head_title=    __("Refund Receipt %s",true);   
        $layout['InvoiceLayout']['label_invoice_no']=empty($layout['InvoiceLayout']['label_refundreceipt_no']) ?$layout['InvoiceLayout']['label_invoice_no'] :$layout['InvoiceLayout']['label_refundreceipt_no'] ;
        $layout['InvoiceLayout']['label_date']=empty($layout['InvoiceLayout']['label_refundreceipt_date']) ?$layout['InvoiceLayout']['label_date'] :$layout['InvoiceLayout']['label_refundreceipt_date'] ;

        $layout['InvoiceLayout']['field1']=empty($layout['InvoiceLayout']['label_to']) ?$layout['InvoiceLayout']['field1'] :$layout['InvoiceLayout']['label_to'] ;

//        $layout['InvoiceLayout']['label_invoice_to']=empty($layout['InvoiceLayout']['label_refundreceipt_to']) ?$layout['InvoiceLayout']['label_invoice_to'] :$layout['InvoiceLayout']['label_refundreceipt_to'] ;

    }elseif($invoice['Invoice']['type']==Invoice::DEBIT_NOTE){
        $layout['InvoiceLayout']['invoice_title']=empty($layout['InvoiceLayout']['sales_debit_note_title']) ?$layout['InvoiceLayout']['invoice_title'] :$layout['InvoiceLayout']['sales_debit_note_title'] ;
        $layout['InvoiceLayout']['label_invoice_no']=empty($layout['InvoiceLayout']['label_sales_debit_note_no']) ?$layout['InvoiceLayout']['label_invoice_no'] :$layout['InvoiceLayout']['label_sales_debit_note_no'] ;
        $layout['InvoiceLayout']['label_date']=empty($layout['InvoiceLayout']['label_sales_debit_note_date']) ?$layout['InvoiceLayout']['label_date'] :$layout['InvoiceLayout']['label_sales_debit_note_date'] ;
        $layout['InvoiceLayout']['field1']=empty($layout['InvoiceLayout']['label_to']) ?$layout['InvoiceLayout']['field1'] :$layout['InvoiceLayout']['label_to'] ;
    }elseif($invoice['Invoice']['type']==Invoice::SALES_ORDER){
        $layout['InvoiceLayout']['invoice_title']=empty($layout['InvoiceLayout']['sales_order_title']) ?$layout['InvoiceLayout']['invoice_title'] :$layout['InvoiceLayout']['sales_order_title'] ;
        $layout['InvoiceLayout']['label_invoice_no']=empty($layout['InvoiceLayout']['label_sales_order_no']) ?$layout['InvoiceLayout']['label_invoice_no'] :$layout['InvoiceLayout']['label_sales_order_no'] ;
        $layout['InvoiceLayout']['label_date']=empty($layout['InvoiceLayout']['label_sales_order_date']) ?$layout['InvoiceLayout']['label_date'] :$layout['InvoiceLayout']['label_sales_order_date'] ;
        $layout['InvoiceLayout']['field1']=empty($layout['InvoiceLayout']['label_to']) ?$layout['InvoiceLayout']['field1'] :$layout['InvoiceLayout']['label_to'] ;

    }else{
    $head_title=__("Invoice #",true);    

    }
	
	 if(ifPluginActive(EINVOICE_SA_PLUGIN)&&$layout['InvoiceLayout']['invoice_title']=='فاتورة')
	 {
		 $layout['InvoiceLayout']['invoice_title']='<span style="zoom: 0.7;">فاتورة ضريبية مبسطة</span>';
	 }
ob_start();
$ViewCss = json_decode($layout['InvoiceLayout']['view_style'],true);

if(is_array($ViewCss))
    foreach($ViewCss as $key=>$value) {
        if(strpos($key,"label_")==0) {
            $all_place_holder['{%' . $key . '%}'] = $value;
        }
    }
$more_style = '';


$shipping_name = $invoice['Invoice']['client_secondary_name'];
$shipping_address = $invoice['Invoice']['client_secondary_address1'] . (empty($invoice['Invoice']['client_secondary_address2']) ? '' : '<br/>' . $invoice['Invoice']['client_secondary_address2']);
$shipping_city = $invoice['Invoice']['client_secondary_city'];
$shipping_state = $invoice['Invoice']['client_secondary_state'];
$shipping_postal_code = $invoice['Invoice']['client_secondary_postal_code'];
$shipping_country_code = $invoice['Invoice']['client_secondary_country_code'];


$show_shipping = false;
if ($invoice['Invoice']['shipping_options'] == 2 ||
        (empty($invoice['Invoice']['shipping_options']) && !empty($layout['InvoiceLayout']['show_ship']) && !empty($layout['InvoiceLayout']['show_ship']) && (empty($invoice['Invoice']['client_secondary_name']) && empty($invoice['Invoice']['client_secondary_address1'])))
) {
    $show_shipping = true;
    $shipping_name = $invoice['Invoice']['client_business_name'];
    $shipping_address = $invoice['Invoice']['client_address1'] . (empty($invoice['Invoice']['client_address2']) ? '' : '<br/>' . $invoice['Invoice']['client_address2']);
    $shipping_city = $invoice['Invoice']['client_city'];
    $shipping_state = $invoice['Invoice']['client_state'];
    $shipping_postal_code = $invoice['Invoice']['client_postal_code'];
    $shipping_country_code = $invoice['Invoice']['client_country_code'];
} else if (
        (empty($invoice['Invoice']['shipping_options']) && !empty($layout['InvoiceLayout']['show_ship']) && (!empty($invoice['Invoice']['client_secondary_name']) || !empty($invoice['Invoice']['client_secondary_address1']))) || $invoice['Invoice']['shipping_options'] == 3) {
    $show_shipping = true;
} else if (
    // This case added to handle the shipping options = 1  and show_ship = true and secondary address is empty
    $invoice['Invoice']['shipping_options'] == 1 && !empty($layout['InvoiceLayout']['show_ship']) &&  (empty($invoice['Invoice']['client_secondary_name']) && empty($invoice['Invoice']['client_secondary_address1']))) {
    $show_shipping = true;
    $shipping_name = $invoice['Invoice']['client_business_name'];
    $shipping_address = $invoice['Invoice']['client_address1'] . (empty($invoice['Invoice']['client_address2']) ? '' : '<br/>' . $invoice['Invoice']['client_address2']);
    $shipping_city = $invoice['Invoice']['client_city'];
    $shipping_state = $invoice['Invoice']['client_state'];
    $shipping_postal_code = $invoice['Invoice']['client_postal_code'];
    $shipping_country_code = $invoice['Invoice']['client_country_code'];
}


if ($show_shipping)
    $more_style.=' 
#shipping_options {display: table-row !important;}
';

$this->layout = '';

if ($this->params['url']['ext'] != 'pdf') {
    header('Content-Type: text/html; charset=utf-8');
}
if (empty($site)) {
    $site = getCurrentSite();
}

$hide_description = true;
foreach ($invoice['InvoiceItem'] as $item) {
    if (!empty($item['description'])) {
        $hide_description = false;
        break;
    }
}

if ($hide_description)
    $cols = 1;
else
    $cols = 2;


// warning suppress
$site['site_logo_full_path'] = str_replace('/', 'ZXASDFQ', $site['site_logo_full_path'] ?? null);
$site['site_logo_full_path'] = rawurlencode($site['site_logo_full_path']);
$site['site_logo_full_path'] = str_replace('ZXASDFQ', '/', $site['site_logo_full_path']);


$layoutHTML = $layout['InvoiceLayout']['html'];

$fullUrl = str_replace("&shy;","-",'https://' . $site['subdomain']);


$logo = $fullUrl.'/css/images/transparent.gif';


if (!empty($layout['InvoiceLayout']['logo']) && $layout['InvoiceLayout']['logo'] != '{%logo_site%}' && $layout['InvoiceLayout']['logo'] != '') {

    $logoPath = DS . 'files' . DS . 'images' . DS . 'logos' . DS . ($layout['InvoiceLayout']['logo']);

        $logo = $fullUrl . str_replace(DS, '/', $logoPath);

} elseif (!empty($site['site_logo'])) {
    $logoPath = DS . 'files' . DS . 'images' . DS . 'site-logos' . DS . ($site['site_logo']);
    $logo = $fullUrl . str_replace(DS, '/', $logoPath);
} else {
    $more_style.='
#logo{display:none;}';
}
if (!defined('d')) define('d', '43');

$all_place_holder['{%logo%}']=$logo;
$all_place_holder['{%logo-width%}']=$layout['InvoiceLayout']['default_width'];
if($layout['InvoiceLayout']['default_height']=='0'){
    $layout['InvoiceLayout']['default_height']='';
}
$all_place_holder['{%logo-height%}']=$layout['InvoiceLayout']['default_height'];
$all_place_holder['{%invoice_title%}']=$layout['InvoiceLayout']['invoice_title'];
foreach($layout['InvoiceLayout'] as $key=>$value) {
    if(strpos($key,"label_")==0) {
        if ($key == 'logo') 
            continue;
        $all_place_holder['{%' . $key . '%}'] = $layout['InvoiceLayout'][$key];
    }
}
//$layoutHTML = str_replace(array('{%logo%}', '{%logo-width%}', '{%logo-height%}'), array($logo, $layout['InvoiceLayout']['default_width'], $layout['InvoiceLayout']['default_height']), $layoutHTML);


$customFields = array();

$layoutCustomFields = '';
$customFieldsHtml = $layout['InvoiceLayout']['custom_fields'];
if (empty($customFieldsHtml) && !empty($parentLayout)) {
    $customFieldsHtml = $parentLayout['InvoiceLayout']['custom_fields'];
}
$fields = array();

$enable_custom_fields =    (!empty($layout['InvoiceLayoutCustomField']) || !empty($invoice['InvoiceCustomField']))
                        || (!empty($invoice['Invoice']['show_from_to']) && !empty($invoice['Invoice']['from']))
                        || (!empty($invoice['Invoice']['show_from_to']) && !empty($invoice['Invoice']['to']));

if ( $enable_custom_fields) {
    
    foreach ($invoice['InvoiceCustomField'] as $field) {
 
        $field['real-label'] = $label = $field['label'];
        if (!empty($field['placeholder'])) {
            $placeholder = 'label_' . str_replace(array("%", '-'), array('', '_'), $field['placeholder']);

            if (isset($layout['InvoiceLayout'][$placeholder])) {
                $label = $layout['InvoiceLayout'][$placeholder];
            }
            $field['real-label'] = $label;
        }else{
         
        }
        
        $customFields[$field['label']] = $field;
    }


    if (!empty($site['enable_po']) && !empty($invoice['Invoice']['po_number'])) {
        
        $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array(!empty($layout['InvoiceLayout']['label_po_no']) ? $layout['InvoiceLayout']['label_po_no'] : (empty($site['po_no_label']) ? __('PO Number', true) : $site['po_no_label']), $invoice['Invoice']['po_number']), $customFieldsHtml);
    }
    if (!empty($invoice['Invoice']['show_from_to']) && !empty($invoice['Invoice']['from'])) {
        
        $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array((empty($layout['InvoiceLayout']['label_from_date']) ? __('From Date', true) : $layout['InvoiceLayout']['label_from_date']), $invoice['Invoice']['from']), $customFieldsHtml);
    }
    if (!empty($invoice['Invoice']['show_from_to']) && !empty($invoice['Invoice']['to'])) {
        
        $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array((empty($layout['InvoiceLayout']['label_to_date']) ? __('To Date', true) : $layout['InvoiceLayout']['label_to_date']), $invoice['Invoice']['to']), $customFieldsHtml);
    }
   // print_r($customFields);
    foreach ($layout['InvoiceLayoutCustomField'] as $customField) {
   
        $field = $customFields[$customField['label']];
        if (!empty($customFields[$customField['label']]) && !empty($field['real-value'])) {
            
            $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array($field['real-label'], $field['real-value']), $customFieldsHtml);
            unset($customFields[$customField['label']]);
        }
    }
    
    foreach ($customFields as $label => $customField) {
        debug ( $label );
        debug ( $customField );
        if (!empty($label) && !empty($customField['real-value'])) {
            
            $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array($customField['real-label'], $customField['real-value']), $customFieldsHtml);
        }
    }
}
if(!empty($invoice['Invoice']['subscription_id']) && (strpos($layoutHTML, "{%refund_invoice_no_title%}") === false || strpos($layoutHTML, "{%refund_invoice_number%}") === false) && ($invoice['Invoice']['type'] == Invoice::Refund_Receipt || $invoice['Invoice']['type'] == Invoice::Credit_Note)) {
    $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array($all_place_holder['{%refund_invoice_no_title%}'], $all_place_holder['{%refund_invoice_number%}']), $customFieldsHtml);
}
$layoutHTML = str_replace(array('{%custom_fields%}', '<custom_field', '</custom_field'), array($layoutCustomFields, '<table', '</table'), $layoutHTML);

if ($layout['InvoiceLayout']['default_height'] == '0')
    $layout['InvoiceLayout']['default_height'] = '';
$layoutHTML = str_replace(array('{%logo%}', '{%logo-width%}', '{%logo-height%}'), array($logo, $layout['InvoiceLayout']['default_width'], $layout['InvoiceLayout']['default_height']), $layoutHTML);

if (getCurrentSite('id') == 2109941) {
    $paper_size = ' --page-size A3';
    $margin = "";
    $brandOptions = '';
    $invoiceDir = '/tmp/' . SITE_HASH . '/';
    if (!is_dir($invoiceDir)) {
        mkdir($invoiceDir, 0777, true);
    }
    $outFile = $invoiceDir . $invoice['Invoice']['id'] . '.html';
    $outPdf = $invoiceDir . $invoice['Invoice']['id'] . '_test.pdf';
    file_put_contents($outFile, $layoutHTML); // input to pdf 
    $cmd = Wkhtmltopdf_Path . ' --dpi 96 --encoding "utf-8" ' . $paper_size . ' ' . $margin . $brandOptions . ' ' . $outFile . ' ' . $outPdf;
    exec($cmd, $output, $status);

}
if ($orderSource) {
    $layoutHTML = str_replace(array('{%invoice_number%}', '{%invoice_date%}', '{%invoice_order_source%}'), array($invoice['Invoice']['no'], (!empty($invoice['Invoice']['date']) && $invoice['Invoice']['date'] != '01/01/1970'? $invoice['Invoice']['date']:null), $orderSource), $layoutHTML);
} else {
    $layoutHTML = str_replace(array('{%invoice_number%}', '{%invoice_date%}', '{%invoice_order_source%}', 'label_order_source">مصدر الطلب', 'label_order_source">Order Source'), array($invoice['Invoice']['no'], (!empty($invoice['Invoice']['date']) && $invoice['Invoice']['date'] != '01/01/1970'? $invoice['Invoice']['date']:null), '', 'label_order_source">', 'label_order_source">'), $layoutHTML);
}
$defaultLayoutItemCols =  $layout['InvoiceLayout']['item_columns'];

if(isset($invoice['Invoice']['id']) and !empty($invoice['Invoice']['id']) && $invoice['Invoice']['invoice_layout_id'] == $layout['InvoiceLayout']['id']){
    $layout['InvoiceLayout']['item_columns']=$invoice['Invoice']['item_columns']   ;
}

if(isset($invoice['Invoice']['original_invoice_layout_id']) && $layout['InvoiceLayout']['id'] != $invoice['Invoice']['original_invoice_layout_id']) {
    $layout['InvoiceLayout']['item_columns']= $defaultLayoutItemCols;
}


if(empty( $layout['InvoiceLayout']['item_columns'])  ){
   $forcecols=1; 
$columns=array
(
    'field1' => array
        (
            'name' => 'name',
            'label' => $layout['InvoiceLayout']['label_item']
        ),

    'field2' => array
        (
            'name' => $hide_description==false?'description':'',
            'label' => $hide_description==false?$layout['InvoiceLayout']['label_description']:''
        ),

    'field3' => array
        (
            'name' => '',
            'label' => ''
        ),

    'field4' => array
        (
            'name' => '',
            'label' => ''
        ),

    'field5' => array
        (
            'name' => '',
            'label' => ''
        ),

);    

  
}else{
    
$columns=json_decode($layout['InvoiceLayout']['item_columns'],true);    

}


if(is_array($columns)&&count($columns))
{
   
$layout['InvoiceLayout']['items_list']=$this->element('dynamic_items_list',array('invoice'=>$invoice,'InvoiceItem'=>$invoice['InvoiceItem'],'columns'=>$columns));
}else{
//   if($layout['InvoiceLayout']['quantity_price']=="show_quantity_before_price"){
//       
//$layout['InvoiceLayout']['items_list']=str_replace(array('label_quantity','label_unit_price'),array('_label_quantity','_label_unit_price'),$layout['InvoiceLayout']['items_list']);
//$layout['InvoiceLayout']['items_list']=str_replace(array('_label_quantity','_label_unit_price'),array('label_unit_price','label_quantity'),$layout['InvoiceLayout']['items_list']);
//
//   }elseif($layout['InvoiceLayout']['quantity_price']=="hide_price_and_quantity"){
//   $layout['InvoiceLayout']['items_list']=str_replace('<th width="60" bgcolor="#e5e5e5" class="editable-area" id="label_unit_price">{%label_unit_price%}</th>','',$layout['InvoiceLayout']['items_list'])    ;
//   $layout['InvoiceLayout']['items_list']=str_replace('<th width="30" bgcolor="#e5e5e5" class="editable-area" id="label_quantity">{%label_quantity%}</th>','',$layout['InvoiceLayout']['items_list'])    ;
//   }
   
}

if(strpos($invoice['Invoice']['html_notes'],'replace_template_notes'))  $layout['InvoiceLayout']['footer']= $invoice['Invoice']['html_notes'];


preg_match_all('/\{%([^%]+)%\}/', $layoutHTML, $m);


$s = $m[0];
$r = array('{%business_info%}' => '', '{%client_info%}' => '');

foreach ($m[1] as $idx => $val) {


    $cellValue = isset($layout['InvoiceLayout'][$val])?$layout['InvoiceLayout'][$val]:$m[0][$idx];
    if ($val == 'business_info' || $val == 'client_info' || $val == 'ship_info') {
        $cellValue = nl2br_fix($layout['InvoiceLayout'][$val]);
    
    } else if ($val == 'label_ship') {
        if($show_shipping){
            if(!empty($invoice['Invoice']['shipping_option_id'])){
                $ShippingOptionModel = GetObjectOrLoadModel('ShippingOption');
                $shipping_option = $ShippingOptionModel->find('first', ['conditions' => ['ShippingOption.id' => $invoice['Invoice']['shipping_option_id']]]);
                $cellValue = $layout['InvoiceLayout'][$val].$shipping_option['ShippingOption']['name'];
            }
        }
    }
	
    $r[$s[$idx]] = $cellValue; 
}

//$all_place_holder["{%$val%}"]=$cellValue    ;
$layoutHTML = str_replace(array_keys($r), $r, $layoutHTML);


$itemsList = '';
/* @var $html HtmlHelper */
if (!$hide_description){
    
    foreach ($invoice['InvoiceItem'] as $item) {
    $td=array(
            nl2br($item['item']),
            nl2br($item['description'])
                );

        $item['quantity'] = (float)$item['quantity'];
        $item['unit_price'] = (float)$item['unit_price'];

//$layout['InvoiceLayout']['items_list']=str_replace(array('label_quantity','label_unit_price'),array('1','2'),$layout['InvoiceLayout']['items_list']);
    $td[]=empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['unit_price_factor'], $invoice['Invoice']['currency_code'], false) : format_price_simple($item['unit_price_factor'], $invoice['Invoice']['currency_code'], false);
    $td[]=$item['quantity_written'];//format_number($item['quantity'], $invoice['Invoice']['currency_code']);

    $td[]=  empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code'], false) : format_price_simple($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code'], false);
    
        $itemsList .= $html->tableCells($td, array('class' => 'line'), array('class' => 'line'));
    }
}else{
    
    foreach ($invoice['InvoiceItem'] as $item) {
        $item['quantity'] = (float)$item['quantity'];
        $item['unit_price'] = (float)$item['unit_price'];

        $itemsList .= $html->tableCells(array(
            nl2br($item['item']),
            empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['unit_price_factor'], $invoice['Invoice']['currency_code'],false) : format_price_simple($item['unit_price_factor'], $invoice['Invoice']['currency_code'],false),
            $item['quantity_written'],//format_number($item['quantity'], $invoice['Invoice']['currency_code']),
            empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code'],false) : format_price_simple($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code'],false)
                ), array('class' => 'line'), array('class' => 'line'));
    }
}
// Moved To Element !!! dynamic items list
//if(!empty( $layout['InvoiceLayout']['item_columns'])&&is_array($columns=json_decode($layout['InvoiceLayout']['item_columns'],true))&&count($columns))
//{
//    $keymap['field3']='col_3';
//    $keymap['field4']='col_4';
//    $keymap['field5']='col_5';
//    unset($itemsList);
//      foreach ($invoice['InvoiceItem'] as $item) {
//          foreach($columns as $key=>$col){
//              if(!empty($col['name'])){
//              if($col['name']=="name"){
//              $colvalue='item';    
//              }elseif($col['name']=="description"){
//              $colvalue='description';    
//              }else{
//                  $colvalue=$keymap[$key];
//              }
//              $listitem[]=$item[$colvalue];
//              }
//          }
//          $listitem[]= empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code']) : format_price_simple($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code']);
//      $itemsList .= $html->tableCells($listitem, array('class' => 'line'));
//      unset($listitem);
//      }
//}

$placeholders = array(
    '{%business_name%}' => $site['business_name'],
    '{%cols%}' => $cols,
    '{%first_name%}' => $site['first_name'],
    '{%last_name%}' => $site['last_name'],
    '{%subdomain%}' => $site['subdomain'],
    '{%address1%}' => $site['address1'],
    '{%address2%}' => $site['address2'],
    '{%city%}' => $site['city'],
    '{%state%}' => $site['state'],
    '{%postal_code%}' => $site['postal_code'],
    '{%full_business_address%}' => $this->element('format_address_html',$site+array('is_inline'=>true)),
    '{%phone1%}' => $site['phone1'],
    '{%phone2%}' => $site['phone2'],
    '{%telephone%}' => $site['phone1'],
    '{%mobile%}' => $site['phone2'],
    '{%client_organization%}' => $invoice['Invoice']['client_business_name'],
    '{%client_number%}' => $invoice['Client']['client_number'],
    '{%client_name%}' => $invoice['Invoice']['client_first_name'] . ' ' . $invoice['Invoice']['client_last_name'],
    '{%client_first_name%}' => $invoice['Invoice']['client_first_name'],
    '{%client_last_name%}' => $invoice['Invoice']['client_last_name'],
    '{%client_phone%}' => $invoice['Client']['phone1'],
    '{%client_mobile%}' => $invoice['Client']['phone2'],
    '{%client_email%}' => $invoice['Client']['email'],
    '{%client_address%}' => $invoice['Invoice']['client_address1'] . (empty($invoice['Invoice']['client_address2']) ? '' : '<br>' . $invoice['Invoice']['client_address2']),
    '{%client_city%}' => $invoice['Invoice']['client_city'],
    '{%client_state%}' => $invoice['Invoice']['client_state'],
    '{%client_national_id%}' => $invoice['Client']['national_id'],
    
    '{%full_client_address%}'=>$this->element('format_address_html',array('address1'=>$invoice['Invoice']['client_address1'],'address2'=>$invoice['Invoice']['client_address2'],'city'=>$invoice['Invoice']['client_city'],'state'=>$invoice['Invoice']['client_state'],'postal_code'=>$invoice['Invoice']['client_postal_code'],'is_inline'=>true)),
    '{%client_country%}' => !empty($clientCountry) ? $clientCountry : '',
    '{%client_secondary_name%}' => $shipping_name,
    '{%client_secondary_address%}' => $shipping_address,
    '{%client_secondary_city%}' => $shipping_city,
    '{%client_secondary_state%}' => $shipping_state,
    '{%client_secondary_postcode%}' => $shipping_postal_code,
    '{%client_secondary_postal_code%}' => $shipping_postal_code,
    '{%items%}' => $itemsList,
    '{%invoice_notes%}' => nl2br(h($invoice['Invoice']['notes'])) . '<span style="font-style: normal;" >' . strip_tags(str_replace(array('onclick', 'onmouseup', 'onmousewheel', 'onscroll', 'onmousedown', 'onmousemove', 'onmouseout', 'onmouseover'), '', $invoice['Invoice']['html_notes']), '<div><span><ul><li><ol><br><p><b><i><strong><font><small><big><h1><h2><h3><h4><h5><h6><h7><a><style>') . '</span>',
    //'{%html_invoice_notes%}' => , 
    '{%from-date%}' => $invoice['Invoice']['from'],
    '{%to-date%}' => $invoice['Invoice']['to'],
)+$all_place_holder;

if (!empty($layout['InvoiceLayoutTag'])) {
    foreach ($layout['InvoiceLayoutTag'] as $tag) {
        $placeholders[$tag['tag']] = $tag['replacement'];
    }
}

//	$labels = array('label_invoice_no', 'label_date', 'label_po_no', 'label_total', 'label_status', 'label_due_after', 'label_due_date', 'label_deposit', 'label_paid_amount', 'label_unpaid_amount', 'label_subtotal', 'label_description', 'label_item', 'label_tax1', 'label_tax2', 'label_quantity', 'label_unit_price', 'label_item_total', 'label_from_date', 'label_to_date');

preg_match_all('/{%(label_[^%]+)%}/', $layoutHTML, $m);
$labels = $m[1];
foreach ($labels as $label) {
    
    $placeholders['{%' . $label . '%}'] = $layout['InvoiceLayout'][$label];
    
}

$placeholders['{%value_item_total%}'] = format_price($invoice['Invoice']['summary_subtotal'] + (!empty($invoice['Invoice']['adjustment_value']) ? (float) $invoice['Invoice']['adjustment_value']  * -1 : 0), $invoice['Invoice']['currency_code']);
$placeholders['{%value_unpaid_amount%}'] = format_price($invoice['Invoice']['summary_unpaid'], $invoice['Invoice']['currency_code']);
$placeholders['{%value_paid_amount%}'] = format_price($invoice['Invoice']['summary_paid'] * -1, $invoice['Invoice']['currency_code']);
$placeholders['{%value_total%}'] = format_price($invoice['Invoice']['summary_total'], $invoice['Invoice']['currency_code']);

$taxes = '';
if(!empty( $layout['InvoiceLayout']['item_columns'])&&is_array($columns=json_decode($layout['InvoiceLayout']['item_columns'],true))&&count($columns))
{

$table_cell = '<tr class="{%sclass%}">
					<td bgcolor="#FFF" colspan="1" style="border:none;"></td>
					<td colspan="1" style="border-left:none;border-right:none;"><strong id="label_item_total" class="editable-area">{%label%}</strong></td>
					<td class="text-left" style="border-left:none;border-right:none;">{%value%}</td>
			</tr>';
}else{
if($forcecols==1){
 $table_cell = '<tr>
					<td width="65%" style="border:none;" bgcolor="#FFF"></td>
					<td colspan="1" style="border-left:none;border-right:none;"><strong id="label_item_total" class="editable-area">{%label%}</strong></td>
					<td class="text-left" style="border-left:none;border-right:none;">{%value%}</td>
			</tr>';   
}else{
    
$table_cell = '<tr>
					<td bgcolor="#FFF" colspan="'.$cols.'" style="border:none;"></td>
					<td colspan="2" style="border-left:none;border-right:none;"><strong id="label_item_total" class="editable-area">{%label%}</strong></td>
					<td class="text-left" style="border-left:none;border-right:none;">{%value%}</td>
			</tr>';
}
}			
//$invoice['Invoice']['summary_discount'] += $invoice['Invoice']['item_discount_amount']  ;

if(!empty($invoice['Invoice']['summary_total_discount'])) {$invoice['Invoice']['summary_discount']=$invoice['Invoice']['summary_total_discount'];$invoice['Invoice']['discount_amount']=1;}
if ($invoice['Invoice']['summary_discount']) {
    $taxes .= str_replace(
            array('{%label%}', '{%value%}','{%sclass%}'), array(empty($invoice['Invoice']['discount_amount'])?sprintf('%s (%s%%)', $layout['InvoiceLayout']['label_discount'], round($invoice['Invoice']['discount'], 2)):$layout['InvoiceLayout']['label_discount'], format_price($invoice['Invoice']['summary_discount'] * -1, $invoice['Invoice']['currency_code']),'row-summary-discount'), $table_cell);
}



foreach ($invoice['InvoiceTax'] as $tax) {
    $taxes .= str_replace(
        array('{%label%}', '{%value%}','{%sclass%}'), array(sprintf('%s (%s%%)', $tax['name'], round($tax['value'], 2)), format_price($tax['invoice_value'], $invoice['Invoice']['currency_code']),'row-tax-summary'), $table_cell);
}
$style="";

if (!empty($invoice['Invoice']['shipping_amount'])) {
    $taxes .= str_replace(
            array('{%label%}', '{%value%}','{%sclass%}'), array(empty($layout['InvoiceLayout']['label_shipping'])?__('Shipping',true):$layout['InvoiceLayout']['label_shipping'],  format_price((float)$invoice['Invoice']['shipping_amount'] - GetObjectOrLoadModel('Invoice')->getInclusiveTaxAmount($invoice['Invoice']['shipping_amount'], $invoice['InvoiceTax'][$invoice['Invoice']['shipping_tax_id']]), $invoice['Invoice']['currency_code']),'row-shipping-amount'), $table_cell);
}

if (!empty($invoice['Invoice']['adjustment_value'])) {
    $taxes .= str_replace(
            array('{%label%}', '{%value%}'), array(
                empty($invoice['Invoice']['adjustment_label'])?__('Adjustment', true):$invoice['Invoice']['adjustment_label'],
                format_price($invoice['Invoice']['adjustment_value'], $invoice['Invoice']['currency_code']),
            ),
            $table_cell
    );
}


if (!empty($extra_details['invoice_accounts'])&&is_array($extra_details['invoice_accounts'])) {
    foreach($extra_details['invoice_accounts'] as $account_id=>$invoice_account) {
        if(!empty($invoice_account['value'])&&abs((float)$invoice_account['value'])>0) {
            $taxes .= str_replace(
                array('{%label%}', '{%value%}'), array(
                $invoice_account['label'],
                format_price($invoice_account['value'], $invoice['Invoice']['currency_code']),
            ),
                $table_cell
            );

             } } }


$refunded_row='';
if (!empty($invoice['Invoice']['summary_refund'])) {
    $refunded_row = str_replace( array('{%label%}', '{%value%}','{%sclass%}'), array(empty($layout['InvoiceLayout']['label_refunded'])?__('Refunded',true):$layout['InvoiceLayout']['label_refunded'],  format_price($invoice['Invoice']['summary_refund'], $invoice['Invoice']['currency_code']),'row-summary-refund'), $table_cell);
     if (strpos($layoutHTML, '#<!-- PaidAmount -->'))
        $layoutHTML = str_replace('#<!-- PaidAmount -->', $refunded_row . ' #<!-- PaidAmount -->', $layoutHTML);
    else if (strpos($layoutHTML, '<!-- PaidAmount -->'))
        $layoutHTML = str_replace('<!-- PaidAmount -->', $refunded_row . ' <!-- PaidAmount -->', $layoutHTML);
}

$placeholders['<head>'] = '<head>' . $style;

$placeholders['{%invoice-taxes%}'] = $taxes;
 if ($site['plan_id']==1) {
$isPdf = (!empty($this->params['url']['ext']) && $this->params['url']['ext'] == 'pdf') || !empty($pdf); 
$placeholders['</body>'] = $this->element('branded',['isPdf'=>$isPdf]) . '</body>';
}


if ($invoice['Invoice']['summary_deposit'] && $invoice['Invoice']['summary_deposit'] != $invoice['Invoice']['summary_unpaid'] && abs($invoice['Invoice']['summary_deposit'] - $invoice['Invoice']['summary_unpaid']) >= 0.01) {
    $next_payment_row = str_replace(
            array('{%label%}', '{%value%}'), array(sprintf('%s ', $layout['InvoiceLayout']['label_deposit']), format_price($invoice['Invoice']['summary_deposit'], $invoice['Invoice']['currency_code'])), $table_cell);

    if (strpos($layoutHTML, '#<!-- PaidAmount -->'))
        $layoutHTML = str_replace('#<!-- PaidAmount -->', $next_payment_row . ' #<!-- PaidAmount -->', $layoutHTML);
    else if (strpos($layoutHTML, '<!-- PaidAmount -->'))
        $layoutHTML = str_replace('<!-- PaidAmount -->', $next_payment_row . ' <!-- PaidAmount -->', $layoutHTML);
    else
        $placeholders['{%invoice-taxes%}'] .= str_replace(
                array('{%label%}', '{%value%}'), array(sprintf('%s ', $layout['InvoiceLayout']['label_deposit']), format_price($invoice['Invoice']['summary_deposit'], $invoice['Invoice']['currency_code'])), $table_cell);
}

foreach ($placeholders as $k => $v) {
    if (empty($v)) {
        $layoutHTML = preg_replace('/' . $k . '(\r|\s|\n|,|<br\/>|<br>|<br \/>|)*/', '', $layoutHTML);
    }
    if (is_string($k)) $v = (string)$v;
    $layoutHTML = str_replace($k, $v, $layoutHTML);
}

    if ($invoice['Invoice']['type']==Invoice::Credit_Note || $invoice['Invoice']['type']==Invoice::Refund_Receipt || (empty($layout['InvoiceLayout']['show_balance_due']) && (empty($invoice['Invoice']['summary_paid']) || $invoice['Invoice']['summary_paid'] == $invoice['Invoice']['summary_total']))) {
    $layoutHTML = preg_replace('#<!-- PaidAmount -->.*<!-- /PaidAmount -->#sm', '', $layoutHTML);
    }


if ($invoice['Invoice']['summary_subtotal'] == $invoice['Invoice']['summary_total']) {
    $layoutHTML = preg_replace('#<!-- Subtotal -->.*<!-- /Subtotal -->#sm', '', $layoutHTML);
}

if ($hide_description) {
    $layoutHTML = preg_replace('#<!-- Description -->.*<!-- /Description -->#sm', '', $layoutHTML);
}

$layoutHTML = preg_replace('/<\/table[^>]*>[\r\n\s]*<table[^>]+custom_fields[^>]+>/', '', $layoutHTML);
echo $layoutHTML;

if ($layout['InvoiceLayout']['language_id'] == 'ara'||$layout['InvoiceLayout']['language_id'] == 'ar'||$layout['InvoiceLayout']['language_id'] == 7) {
		//debug('GALLO');
    $style = '<style type="text/css">' . file_get_contents(WWW_ROOT . 'css/invoice-layout-template-ar.css') . (!empty($more_style) ? $more_style : '') . '</style>';
} else {
	//debug('SSSALLO');
    $style = '<style type="text/css">' . file_get_contents(WWW_ROOT . 'css/invoice-layout-template.css') . (!empty($more_style) ? $more_style : '') . '</style>';
}
?>
    <? echo $style ?>    
<?php
echo $this->element('view-style',array('ViewCss'=>$ViewCss));
?>
<?php
$out = ob_get_clean();
//html_sticky_footer
//print_pre($layout['InvoiceLayout']);
//echo $layout['InvoiceLayout']['sticky_footer'];
//die();

foreach($layout['InvoiceLayout'] as $key=>$value) {
    if(strpos($key,"label_")==0) {
        if ($key == 'logo') 
            continue;
        $all_place_holder['{%' . $key . '%}'] = $layout['InvoiceLayout'][$key];
    }
}

$all_place_holder['{%invoice_notes%}'] = nl2br(h($invoice['Invoice']['notes'])) . '<span style="font-style: normal;" >' . strip_tags(str_replace(array('onclick', 'onmouseup', 'onmousewheel', 'onscroll', 'onmousedown', 'onmousemove', 'onmouseout', 'onmouseover'), '', $invoice['Invoice']['html_notes']), '<div><span><ul><li><ol><br><p><b><i><strong><font><small><big><h1><h2><h3><h4><h5><h6><h7><a><style>') . '</span>';
$all_place_holder['{%business_info%}']=$layout['InvoiceLayout']['business_info'];
$all_place_holder['{%business_info%}']=nl2br(PlaceHolder::replace($all_place_holder['{%business_info%}'],array_keys($all_place_holder),array_values($all_place_holder)));
$all_place_holder['{%client_info%}']=$layout['InvoiceLayout']['client_info'];
$all_place_holder['{%client_info%}']=nl2br(PlaceHolder::replace($all_place_holder['{%client_info%}'],array_keys($all_place_holder),array_values($all_place_holder)));
//$all_place_holder['{%client_info%}']=(PlaceHolder::replace($all_place_holder['{%client_info%}'],array_keys($all_place_holder),array_values($all_place_holder)));
$all_place_holder['{%ship_info%}']=$layout['InvoiceLayout']['ship_info'];
$all_place_holder['{%ship_info%}']=(PlaceHolder::replace($all_place_holder['{%ship_info%}'],array_keys($all_place_holder),array_values($all_place_holder)));
if ($this->params['url']['ext'] != 'pdf') {
 // print_pre($all_place_holder);
   //die();
$all_place_holder['{%html_sticky_footer%}']=$html->tag("div",(PlaceHolder::replace($layout['InvoiceLayout']['sticky_footer'],array_keys($all_place_holder),array_values($all_place_holder))),array('class'=>'StickyFooter'));
$all_place_holder['{%html_sticky_header%}']=$html->tag("div",(PlaceHolder::replace($layout['InvoiceLayout']['sticky_header'],array_keys($all_place_holder),array_values($all_place_holder))),array('class'=>'StickyHeader'));    

}else{

$sticky_footer_html= (PlaceHolder::replace($layout['InvoiceLayout']['sticky_footer'],array_keys($all_place_holder),array_values($all_place_holder)));
$this->InvoiceFooter=$sticky_footer_html;

$sticky_header_html= (PlaceHolder::replace($layout['InvoiceLayout']['sticky_header'],array_keys($all_place_holder),array_values($all_place_holder)));    
     
$this->InvoiceHeader=$sticky_header_html;
}
$payment_count=count((array)$invoice['InvoicePayment']);
$payment_row=$invoice['InvoicePayment'][0] ?? null;
$all_place_holder +=PlaceHolder::last_invoice_payment_place_holder($payment_row,true);

if(!empty($payment_row)) {
    $treasury = GetObjectOrLoadModel('Treasury')->find('first',['conditions' => ['id' => $payment_row['treasury_id']]]);
    $treasury_row = $treasury['Treasury']??[]; 
    $all_place_holder +=PlaceHolder::treasury_place_holder($treasury_row);
}

if(ifPluginActive(EINVOICE_SA_PLUGIN)&&strpos($out,'<!--qr_code-->')&&!strpos($out,'{%sa_qr_code'))
{
   $out=str_replace('<!--qr_code-->','{%sa_qr_code_image%}<br/>', $out);
}
if(ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::JORDAN_EINVOICE_PLUGIN)&&strpos($out,'<!--qr_code-->'))
{
    $out=str_replace('<!--qr_code-->','{%sa_qr_code_image%}<br/>', $out);
}
$final_out_put= PlaceHolder::replace($out,array_keys($all_place_holder),array_values($all_place_holder));
$final_out_put = formatSaudiRiyalSymbol($final_out_put, $invoice['Invoice']['currency_code']);
echo $final_out_put;
