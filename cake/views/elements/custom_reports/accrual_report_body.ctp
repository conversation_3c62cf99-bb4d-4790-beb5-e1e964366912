<?php
$maxLevel = $report->params['max_level'];
function displayAccounts($accounts, $counter = 0, $maxLevel = 3 ,$account_type=0, $parentAccount = false)
{
    //we use the above counter to calcualte the colspan of the parent account
    $levelTotal = $levelSum = [];
    $lastUsedLevel = 0;
    foreach ($accounts as $k => $account)
    {
        $counter++;
        $class = ife($counter % 2, 'odd', 'even');

        $level = count(explode(',', $account['parent_cat_ids']));

        if($level == 3) {
            $isLastAccount = $k === array_key_last(array_filter($accounts, function($account) {
                $level = count(explode(',', $account['parent_cat_ids']));
                return $level == 3;
            }));
        }
        $hasAccounts = isset($account['accounts']) && !empty($account['accounts']);
        $account['name'] = __($account['name'], true);
        echo "<tr class='{$class} level-{$level}'><td class='sticky-cell'><div class='d-flex'>
                        ";
        $lastUsedLevel = $level;                
        spaces($level);
        echo "
                           {$account['name']}
                           </div></td>
                    ";
//        var_dump($account['name']);
//        var_dump($account['type']);
        $multiplier = $account_type == Journal::JOURNAL_TYPE_CREEDITOR ? 1 : -1 ;
        $showValue = ((!$hasAccounts && $level != $maxLevel) || ($level == $maxLevel));

    foreach($account['credits'] as $periodTitle => $credit)
        {
            if(!isset($levelTotal[$periodTitle])) {
                $levelTotal[$periodTitle] = 0;
            }
            $levelTotal[$periodTitle] += ($credit - $account['debits'][$periodTitle]) * $multiplier;
            echo "<td class='text-right'>".(($showValue) ? report_format_price_simple(($credit - $account['debits'][$periodTitle]) * $multiplier) : '&nbsp;' )."</td>";
            
        }
        $levelSum[] = ($account['total_credit'] - $account['total_debit']) * $multiplier;

        $addPDFClass = _request()->ext == 'pdf' ? 'text-right' : "";
        echo "<td class='". $addPDFClass ."' >" . ($showValue ? (report_format_price_simple(($account['total_credit'] - $account['total_debit']) * $multiplier)) : "&nbsp;"). "</td>";
        echo "</tr>";
        if($hasAccounts && $level != $maxLevel){
            displayAccounts($account['accounts'], $counter, $maxLevel,$account_type, $account);
        }

        
    if($level == 3 && $isLastAccount) {
        $title = '';
        if($parentAccount) {
            $title = __($parentAccount['name'], true);
        }
        echo "<tr class='level-total level-{$level}'><td class='sticky-cell'><div class='d-flex'>
        ";
            spaces($lastUsedLevel - 1);
            echo "
            $title ".__('Subtotal', true)."
                    </div></td>
                ";

        $multiplier = $account_type == Journal::JOURNAL_TYPE_CREEDITOR ? 1 : -1 ;
        $showValue = (($level != $maxLevel) || ($level == $maxLevel));

        foreach($account['credits'] as $periodTitle => $credit)
        {
            echo "<td class='text-right'>".(($showValue) ? report_format_price_simple($levelTotal[$periodTitle]) : '&nbsp;' )."</td>";
        }

        echo "<td class='". $addPDFClass ."' >" . ($showValue ? (report_format_price_simple(array_sum($levelSum))) : "&nbsp;"). "</td>";
        echo "</tr>";        
    }
    }

}

function getAccountsTotalRecursive($accounts, $level = 1){
    $dataList = [];
    foreach($accounts as $_account) {
        $data = [];
        if(!isset($_account['accounts'])) {
            foreach($_account['debits'] as $key => $value) {
                $data[$key] = $value - $_account['credits'][$key]; 
            }
            $dataList[$_account['name']] = array_values($data);
            continue;
        }
        foreach($_account['accounts'] as $_sub_account) {
            $level++; 
            if(isset($_sub_account['accounts']) && $level < 2) {
                $dataList[$_account['name']] = getAccountsTotalRecursive($_sub_account['accounts'], $level);
            }

            foreach($_account['debits'] as $key => $value) {
                $data[$key] = $value - $_account['credits'][$key]; 
            }
            $dataList[$_sub_account['name']] = array_values($data);
        }
    }
    return $dataList;
}

$total_credits = array();
$total_debits = array();
$totals = array();
?>

<?php
    $selectedGraphType = false;
    $graphTypes = [
        'period-totals'     => 'Period Totals',
        'period-expenses1'  => 'Accounts Expense',
       // 'period-expenses2'  => 'Sub-accounts Expense',
        'period-income1'    => 'Accounts Income',
       // 'period-income2'    => 'Sub-accounts Income',
        0                   => 'No Graph'
     ];
    $urlParams = $_GET;
    if(isset($urlParams['graphType'])) { 
    $graphType = $urlParams['graphType'];
    unset($urlParams['no_graph']);
    }

    if(!isset($urlParams['no_graph']) && is_null($graphType)){
        $graphType = 'period-totals'; 
    }

    $selectedGraphType = $graphTypes[$graphType];
?>
<div class="">
    <div class="subreport">
    <div class="btn-group mb-3">

<button type="button" class="s2020 btn-s2020 btn-secondary-s2020 dropdown-toggle s2020 border border-light active graphButton" data-toggle="dropdown" aria-expanded="false">
    <i class="fas fa-chart-bar s2020"></i>
    <span class="d-none d-sm-inline-block currentGraphButton"><?php $selectedGraphType ? __($selectedGraphType) :  __('Report Type'); ?></span>
    <span class="caret d-none d-sm-inline-block"></span>
</button>
<ul class="dropdown-menu graphTypes" role="menu">
    <li><a data-graph="<?php echo 'period-totals'; ?>" href="javascript:void(0)"><?php __('Period Totals'); ?></a></li>
    <li><a data-graph="<?php echo 'period-expenses1'; ?>" href="javascript:void(0)"><?php __($graphTypes['period-expenses1']); ?></a></li>
    <?php if($maxLevel > 2 && false){ ?>
        <li><a data-graph="<?php echo 'period-expenses2'; ?>" href="javascript:void(0)"><?php __($graphTypes['period-expenses2']); ?></a></li>
    <?php } ?>    
    <li><a data-graph="<?php echo 'period-income1'; ?>" href="javascript:void(0)"><?php __($graphTypes['period-income1']); ?></a></li>
    <?php if($maxLevel > 2 && false){ ?>
    <li><a data-graph="<?php echo 'period-income2'; ?>" href="javascript:void(0)"><?php __($graphTypes['period-income2']); ?></a></li>
    <?php } ?>  
    <li><a data-graph="0" href="javascript:void(0)"><?php __('No Graph'); ?></a></li>
    <li class="divider"></li>
</ul>
</div>
        <div style="margin-left: auto" class="row chart-wrap">
        
    

            <div class="" id="chart">
                <?php 
                    if ($report->params['no_graph'] != "1") {
                        include('graph.php'); 
                    }
                ?>
            </div>
        </div>




        <div  class="report-table--wrapper <?=!empty($quick)?'hidden':''?> " style="overflow-x:auto">

            <table cellspacing="0" cellpadding="4" width="100%" class="report-table-dashed report-table-bordered table-shadow">
                <thead>
                    <tr class="report-table-dashed--head">
                        <th class="sticky-cell"></th>
                        <?PHP $columns_counter = 0; ?>
                        <?php foreach($report->period_components as $group_by_title => $value) { $columns_counter++; ?>
                        <th class="text-right"> <?= $group_by_title ?></th>
                        <?php } ?>
                        <th colspan="2" class="text-right"><?= __('Total') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $total = 0;
                    $counter = 0;
                    $reportData = $report->getReportData();

                    $mainIncome = $reportData['incomesAccounts'][array_keys($reportData['incomesAccounts'])[0]];
                    $multiplier = $mainIncome['type'] == Journal::JOURNAL_TYPE_CREEDITOR ? 1 : -1 ;
                    $mainExpense = $reportData['expensesAccounts'][array_keys($reportData['expensesAccounts'])[0]];
                    //var_dump($mainIncome);
                    //var_dump($mainExpense );

                    $total = $mainIncome['total_credit'] + $mainExpense['total_credit'] - $mainIncome['total_debit'] - $mainExpense['total_debit'];
                    displayAccounts($reportData['incomesAccounts'], 0, $maxLevel,Journal::JOURNAL_TYPE_CREEDITOR);
                    ?>
                    <tr>
                        <th class="sticky-cell"><?= __('Total Income', true) ?></th>
                        <?php foreach($mainIncome['credits'] as $group_by_title => $credit) {
                        $total_credits[$group_by_title] += $credit - $mainIncome['debits'][$group_by_title];
                        // $total_debits[$group_by_title] += $mainIncome['debits'][$group_by_title];
                        ?>
                        <th class="text-right"> <?= (report_format_price_simple(($credit-$mainIncome['debits'][$group_by_title])*$multiplier)) ?>
                        </th>
                        <?php } ?>
                        <th colspan="2" class="text-right">
                            <?= report_format_price(($mainIncome['total_credit'] - $mainIncome['total_debit'])*$multiplier,$currency) ?>
                        </th>
                    </tr>
                    <tr>

                        <?php

                        $result = count($mainIncome['credits']);


                        ?>
                        <td colspan="<?php echo $result+2; ?>">
                            &nbsp;
                        </td>


                    </tr>
                    <?php
                      displayAccounts($reportData['expensesAccounts'], 0, $maxLevel,Journal::JOURNAL_TYPE_DEBITOR);
                    ?>



                    <tr>
                        <th class="sticky-cell"><?= __('Total Expenses', true) ?></th>
                        <?php
                        $multiplier = $mainExpense['type'] == Journal::JOURNAL_TYPE_CREEDITOR ? 1 : -1 ;
                        foreach($mainExpense['credits'] as $group_by_title => $credit) {
                            // $total_credits[$group_by_title] += $credit;
                            $total_debits[$group_by_title] += $mainExpense['debits'][$group_by_title] - $credit;
                            ?>
                        <th class="text-right">
                            <?= (report_format_price_simple(($credit-$mainExpense['debits'][$group_by_title])*$multiplier)) ?>
                        </th>
                        <?php } ?>
                        <th colspan="2" class="text-right">
                            <?= report_format_price(($mainExpense['total_credit'] - $mainExpense['total_debit'])*$multiplier,$currency) ?>
                        </th>
                    </tr>
                    <tr class="">
                        <td class="sticky-cell"><span class="d-block">&nbsp; &nbsp;</span></td>
                        <td class="text-right" colspan="<?php echo (count($mainIncome['credits']) + 1) ?>">&nbsp;</td>
                    </tr>

                    <tr class="grand-total">
                        <th class="sticky-cell grand-total-cell"><?php __('Total Profit') ; ?></th>
                        <?php foreach($total_debits as $k => $v){
                        $totals[$k] = $total_credits[$k] - $v ;
                        ?>
                        <th class="text-right"><b
                                class="<?= $totals[$k] >= 0 ? 'profit' : 'loss' ?>"><?php echo report_format_price($totals[$k]) ?></b>
                        </th>
                        <?php } ?>
                        <th class="text-right"><b class="<?= $total >= 0 ? 'profit' : 'loss' ?>">
                                <?= report_format_price($total ,$currency) ?></b>
                        </th>
                    </tr>
                </tbody>
            </table>

        </div>
    </div>
</div>
<?php
if ($report->params['no_graph'] != "1") {
    ?>
<?php

$graphTypeSettings = [];
$chart_params = array(
    'title' => $report->title,
    'chartType' => 'bar',
    'group' =>  $report->params['group_by_date'] ,
    'xLabels' => array_keys($report->period_components),
    'extra' => array('series' => array( 0 => array('type' => 'line'))),
    'values' => []);

foreach(array_filter(array_keys($graphTypes)) as $graph) {
    switch($graph) {
        case 'period-expenses1':
            $accounts = [];
            foreach($reportData['expensesAccounts'][0]['accounts'] as $_account) {
                $data = [];
                foreach($_account['debits'] as $key => $value) {
                   $data[$key] = -($value - $_account['credits'][$key]); 
                }
                $accounts[$_account['name']] = array_values($data);
            }
            $chartType = 'line';
            break;
    
        case 'period-expenses2':
                $accountsWithTotals = getAccountsTotalRecursive($reportData['expensesAccounts'][0]['accounts']);
                $chartType = 'line';
                $accounts = $accountsWithTotals;
                break;   
    
        case 'period-income1':
            $accounts = [];
            foreach($reportData['incomesAccounts'][0]['accounts'] as $_account) {
                $data = [];
                foreach($_account['debits'] as $key => $value) {
                   $data[$key] = abs($value - $_account['credits'][$key]); 
                }
                $accounts[$_account['name']] = array_values($data);
            }
            $chartType = 'line';
            break;   
        case 'period-income2':
            $accountsWithTotals = getAccountsTotalRecursive($reportData['incomesAccounts'][0]['accounts']);
            $chartType = 'line';
            $accounts = $accountsWithTotals;
            break;       
        default:
        $chartType = 'bar';
         $accounts = array(
            __('Total Profit', true) => array_values($totals),
            __('Total Income', true) => array_values($total_credits),
            __('Total Expenses', true) => array_values($total_debits),
        );
    }
    
    $graphTypeSettings[$graph] = $chart_params;
    $graphTypeSettings[$graph]['values']    = $accounts;
    $graphTypeSettings[$graph]['chartType']   = $chartType;
}    


    $chart_params = $graphTypeSettings[$graphType];
    
   echo $this->element('reports/charts', array('div' => 'chart', 'jsonParams' => $chart_params));



    ?>
<?php } ?>
<script>
        var graphTypeSettings = JSON.parse(`<?php echo json_encode($graphTypeSettings); ?>`);
        var table = $('table').get(0);
        var $thead = $(table).find('thead');
        if ($thead.length) {
            window.addEventListener('scroll', function() {
                var headerHeight = $('.header').length && $('.header').get(0).clientHeight || 0;
                var pageHeadHeight = $('.pages-head').length && $('.pages-head').get(0).clientHeight || 0;
                var theadHeight = $thead.get(0).clientHeight || 0;
                var theadPaddingHeight = 16;
                var offset = pageHeadHeight + headerHeight + theadHeight + 5;
                if((table.offsetTop - (pageHeadHeight > 0 ? pageHeadHeight : 0)) - window.scrollY <= 0){
                    $thead.addClass('fixed-offset');
                    $thead.get(0).style.setProperty("--listing-table-thead-offset", (window.scrollY - table.offsetTop - headerHeight + offset) + 'px');
                } else {
                    $thead.removeClass('fixed-offset');
                    $thead.get(0).style.setProperty("--listing-table-thead-offset", "0px");
                }
            });
        }


        $(document).ready(function() {

            function addOrUpdateQueryParam(url, paramName, paramValue) {
                // Parse the URL
                var urlParts = url.split('?');
                var baseUrl = urlParts[0];
                var queryString = urlParts[1] || '';

                // Parse existing query parameters
                var queryParams = new URLSearchParams(queryString);

                // Update or add the new parameter
                queryParams.set(paramName, paramValue);

                // Construct the updated URL
                var updatedUrl = baseUrl + '?' + queryParams.toString();
                
                return updatedUrl;
            }
            
            $('.graphTypes a').on('click', function() {
                let graph = $(this).data('graph');
                $('#chart').html('');
                $('.currentGraphButton').text($(this).text());
                if(graph) { 
                    let graphSetting = graphTypeSettings[graph];
                    if(graphSetting) {
                        if ($('#FilterForm').find('input[name="graphType"]').length === 0) {
                            // Create a hidden input element
                            var hiddenInput = $('<input>').attr({
                                type: 'hidden',
                                name: 'graphType',
                                value: graph
                            });

                            // Append the hidden input to your form
                            $('#FilterForm').append(hiddenInput);
                        } else {
                            // If an input with name "graphType" already exists, update its value
                            $('#FilterForm').find('input[name="graphType"]').val(graph); // Set the value as needed
                        }

                        let pdfUrl = decodeURI($('.btn-group .Pdf').attr('href'));
                        var updatedUrl = addOrUpdateQueryParam(pdfUrl, 'graphType', graph);
                        $('.btn-group .Pdf').attr('href', encodeURI(updatedUrl));
                        $.post('/owner/reports/load_report_chart_ajax/', {data: graphSetting}).done(function(data) {
                            $('body').append(data);
                        });
                    }
                }
                else {
                    $('#FilterForm').find('input[name="graphType"]').remove(); 
                    let pdfUrl = decodeURI($('.btn-group .Pdf').attr('href'));
                    var updatedUrl = addOrUpdateQueryParam(pdfUrl, 'graphType', graph);
                    $('.btn-group .Pdf').attr('href', encodeURI(updatedUrl));
                }
            });


        });

</script>
