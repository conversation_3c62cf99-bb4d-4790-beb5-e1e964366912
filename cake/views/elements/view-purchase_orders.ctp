<?php

// This element used when create puchase orders or puchase refund with layout
$all_place_holder=PlaceHolder::purchaseorders_get_all_place_holders($purchase_orders);
//$all_place_holder += PlaceHolder::invoice_custom_field_place_holder($purchase_orders);
if(!empty($layout['InvoiceLayout']['label_to'])){ $all_place_holder['{%field1%}']=$layout['InvoiceLayout']['label_to'];}else{ $all_place_holder['{%field1%}']==$layout['InvoiceLayout']['field1'];}
//echo $layout['InvoiceLayout']['label_creditnote_date'] .' NOIUR';
//$layout['InvoiceLayout']['label_invoice_date'] = "" ;
//print_r ( $layout['InvoiceLayout']);
$enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
foreach ($purchase_orders['PurchaseOrderItem'] as $k => $item2) {
    $put_units = $enable_multi_units && !empty($item2['unit_factor']) && !empty($item2['unit_name']) && ((!empty($item2['Product']))?count($item2['Product']['unit_factors'] ?? [] )>=1 : true) && $item2['unit_factor'] > 0;
    if ($put_units) {
        $purchase_orders['PurchaseOrderItem'][$k]['quantity_written'] = ((float)$item2['quantity']/(float)$item2['unit_factor']).' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
        $purchase_orders['PurchaseOrderItem'][$k]['unit_price_factor'] = ((float)$item2['unit_price']*(float)$item2['unit_factor']);//.' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
//        $invoice['InvoiceItem'][$k]['unit_price'] = ($item2['unit_price']*$item2['unit_factor']);//.' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
    }else {
        $purchase_orders['PurchaseOrderItem'][$k]['quantity_written'] = format_number($item2['quantity'] );
        $purchase_orders['PurchaseOrderItem'][$k]['unit_price_factor'] = $item2['unit_price'];
    }
    $tracking = Product::displayTrackingData($purchase_orders['PurchaseOrderItem'][$k]['Product']['tracking_type'], $purchase_orders['PurchaseOrderItem'][$k]['tracking_data']);
    $tracking_description = $tracking != ""? "<br/>".$tracking : "";
    $purchase_orders['PurchaseOrderItem'][$k]['item'] .= $tracking_description;
    $purchase_orders['PurchaseOrderItem'][$k]['tracking_description'] = $tracking_description;}
if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::PURCHASE_INVOICE){
    
    $head_title= __("Purchase Invoice %s",true);

    }elseif($purchase_orders['PurchaseOrder']['type']==  PurchaseOrder::Purchase_Refund){ 
     $head_title=__("Purchase Refund %s",true);   
        $layout['InvoiceLayout']['invoice_title']=empty($layout['InvoiceLayout']['refundreceipt_title']) ?$layout['InvoiceLayout']['invoice_title'] :$layout['InvoiceLayout']['creditnote_title'] ;
        $layout['InvoiceLayout']['label_invoice_no']=empty($layout['InvoiceLayout']['label_creditnote_no']) ?$layout['InvoiceLayout']['label_invoice_no'] :$layout['InvoiceLayout']['label_creditnote_no'] ;
        $layout['InvoiceLayout']['label_date']=empty($layout['InvoiceLayout']['label_creditnote_date']) ?$layout['InvoiceLayout']['label_date'] :$layout['InvoiceLayout']['label_creditnote_date'] ;
    $head_title=__("Purchase Refund %s",true);   
        $layout['InvoiceLayout']['invoice_title']=empty($layout['InvoiceLayout']['refundreceipt_title']) ?$layout['InvoiceLayout']['invoice_title'] :$layout['InvoiceLayout']['refundreceipt_title'] ;
        $layout['InvoiceLayout']['label_invoice_no']=empty($layout['InvoiceLayout']['label_refundreceipt_no']) ?$layout['InvoiceLayout']['label_refundreceipt_no'] :$layout['InvoiceLayout']['label_refundreceipt_no'] ;
        $layout['InvoiceLayout']['label_date']=empty($layout['InvoiceLayout']['label_refundreceipt_date']) ?$layout['InvoiceLayout']['label_refundreceipt_date'] :$layout['InvoiceLayout']['label_refundreceipt_date'] ;
//print_pre($layout);
        
        $layout['InvoiceLayout']['field1']=empty($layout['InvoiceLayout']['label_to']) ?$layout['InvoiceLayout']['field1'] :$layout['InvoiceLayout']['label_to'] ;
    }elseif($purchase_orders['PurchaseOrder']['type']==  PurchaseOrder::DEBIT_NOTE){ 
         $head_title=__("Purchase Debit Note %s",true);   
        $layout['InvoiceLayout']['invoice_title']=empty($layout['InvoiceLayout']['purchase_debit_note_title']) ?__t('Debit Note') :$layout['InvoiceLayout']['purchase_debit_note_title'] ;
        $layout['InvoiceLayout']['label_invoice_no']=empty($layout['InvoiceLayout']['label_purchase_debit_note_no']) ?__t('Debit Note No') :$layout['InvoiceLayout']['label_purchase_debit_note_no'] ;
        $layout['InvoiceLayout']['label_date']=empty($layout['InvoiceLayout']['label_purchase_debit_note_date']) ?__t('Debit Note Date') :$layout['InvoiceLayout']['label_purchase_debit_note_date'] ;
        $layout['InvoiceLayout']['field1']= __t('Debit Note') .' '.__t('to');
    }elseif($purchase_orders['PurchaseOrder']['type']==  PurchaseOrder::PURCHASE_QUOTATION){ 
        $layout['InvoiceLayout']['invoice_title']= $layout['InvoiceLayout']['estimate_title'] ;  
    }elseif($purchase_orders['PurchaseOrder']['type']==  PurchaseOrder::CREDIT_NOTE){
        $head_title=__("Purchase Credit Note %s",true);   
        $layout['InvoiceLayout']['invoice_title']=empty($layout['InvoiceLayout']['purchase_credit_note_title']) ?__t('Credit Note') :$layout['InvoiceLayout']['purchase_credit_note_title'] ;
        $layout['InvoiceLayout']['label_invoice_no']=empty($layout['InvoiceLayout']['label_purchase_credit_note_no']) ?__t('Credit Note No') :$layout['InvoiceLayout']['label_purchase_credit_note_no'] ;
        $layout['InvoiceLayout']['label_date']=empty($layout['InvoiceLayout']['label_purchase_credit_note_date']) ?__t('Credit Note Date') :$layout['InvoiceLayout']['label_purchase_credit_note_date'] ;
        $layout['InvoiceLayout']['field1']= __t('Credit Note') .' '.__t('to');
    }else{ 
    $head_title=__("PurchaseOrder #",true);    

    }
ob_start();
$ViewCss = json_decode($layout['InvoiceLayout']['view_style'],true);

if(is_array($ViewCss))
    foreach($ViewCss as $key=>$value) {
        if(strpos($key,"label_")==0) {
            $all_place_holder['{%' . $key . '%}'] = $value;
        }
    }

$more_style = '';

$show_shipping = true;
$shipping_name = $purchase_orders['PurchaseOrder']['client_secondary_name'];
$shipping_address = $purchase_orders['PurchaseOrder']['client_secondary_address1'] . (empty($purchase_orders['PurchaseOrder']['client_secondary_address2']) ? '' : '<br/>' . $purchase_orders['PurchaseOrder']['client_secondary_address2']);
$shipping_city = $purchase_orders['PurchaseOrder']['client_secondary_city'];
$shipping_state = $purchase_orders['PurchaseOrder']['client_secondary_state'];
$shipping_postal_code = $purchase_orders['PurchaseOrder']['client_secondary_postal_code'];
$shipping_country_code = $purchase_orders['PurchaseOrder']['client_secondary_country_code'];


//$show_shipping = false;
//if ($purchase_orders['PurchaseOrder']['shipping_options'] == 2 ||
//        (empty($purchase_orders['PurchaseOrder']['shipping_options']) && !empty($layout['InvoiceLayout']['show_ship']) && (empty($purchase_orders['PurchaseOrder']['client_secondary_name']) && empty($purchase_orders['PurchaseOrder']['client_secondary_address1'])))
//) {
//    $show_shipping = true;
//    $shipping_name = $purchase_orders['PurchaseOrder']['client_business_name'];
//    $shipping_address = $purchase_orders['PurchaseOrder']['client_address1'] . (empty($purchase_orders['PurchaseOrder']['client_address2']) ? '' : '<br/>' . $purchase_orders['PurchaseOrder']['client_address2']);
//    $shipping_city = $purchase_orders['PurchaseOrder']['client_city'];
//    $shipping_state = $purchase_orders['PurchaseOrder']['client_state'];
//    $shipping_postal_code = $purchase_orders['PurchaseOrder']['client_postal_code'];
//    $shipping_country_code = $purchase_orders['PurchaseOrder']['client_country_code'];
//} else if (
//        (empty($purchase_orders['PurchaseOrder']['shipping_options']) && !empty($layout['InvoiceLayout']['show_ship']) && (!empty($purchase_orders['PurchaseOrder']['client_secondary_name']) || !empty($purchase_orders['PurchaseOrder']['client_secondary_address1']))) || $purchase_orders['PurchaseOrder']['shipping_options'] == 3) {
//    $show_shipping = true;
//}

if ($show_shipping){
    $more_style.=' 
#shipping_options {display: table-row !important;}
';
}
$this->layout = '';

if ($this->params['url']['ext'] != 'pdf') {
    header('Content-Type: text/html; charset=utf-8');
}
if (empty($site)) {
    $site = getCurrentSite();
}

$hide_description = true;
foreach ($purchase_orders['PurchaseOrderItem'] as $item) {
    if (!empty($item['description'])) {
        $hide_description = false;
        break;
    }
}

if ($hide_description)
    $cols = 1;
else
    $cols = 2;


$site['site_logo_full_path'] = str_replace('/', 'ZXASDFQ', $site['site_logo_full_path']);
$site['site_logo_full_path'] = rawurlencode($site['site_logo_full_path']);
$site['site_logo_full_path'] = str_replace('ZXASDFQ', '/', $site['site_logo_full_path']);


$layoutHTML = $layout['InvoiceLayout']['html'];

$fullUrl = 'https://' . $site['subdomain'];
if ($this->params['url']['ext'] == 'pdf') {
   // $fullUrl = WWW_ROOT;
}
$logo = $fullUrl.'/css/images/transparent.gif';

if (!empty($layout['InvoiceLayout']['logo']) && $layout['InvoiceLayout']['logo'] != '{%logo_site%}' && $layout['InvoiceLayout']['logo'] != '') {

    $logoPath = DS . 'files' . DS . 'images' . DS . 'logos' . DS . ($layout['InvoiceLayout']['logo']);
    $logo = $fullUrl . str_replace(DS, '/', $logoPath);
} elseif (!empty($site['site_logo'])) {
    $logoPath = DS . 'files' . DS . 'images' . DS . 'site-logos' . DS . ($site['site_logo']);
    $logo = $fullUrl . str_replace(DS, '/', $logoPath);
} else {
    $more_style.='
#logo{display:none;}';
}

$customFields = array();

$layoutCustomFields = '';
$customFieldsHtml = $layout['InvoiceLayout']['custom_fields'];
if (empty($customFieldsHtml) && !empty($parentLayout)) {
    $customFieldsHtml = $parentLayout['InvoiceLayout']['custom_fields'];
}
$fields = array();
if (!empty($layout['InvoiceLayoutCustomField']) || !empty($purchase_orders['PurchaseOrderCustomField'])) {

    foreach ($purchase_orders['PurchaseOrderCustomField'] as $field) {
 
        $field['real-label'] = $label = $field['label'];
        if (!empty($field['placeholder'])) {
            $placeholder = 'label_' . str_replace(array("%", '-'), array('', '_'), $field['placeholder']);

            if (isset($layout['InvoiceLayout'][$placeholder])) {
                $label = $layout['InvoiceLayout'][$placeholder];
            }
            $field['real-label'] = $label;
        }else{
         
        }
        
        $customFields[$field['label']] = $field;
    }


    if (!empty($site['enable_po']) && !empty($purchase_orders['PurchaseOrder']['po_number'])) {
        
        $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array(!empty($layout['InvoiceLayout']['label_po_no']) ? $layout['InvoiceLayout']['label_po_no'] : (empty($site['po_no_label']) ? __('PO Number', true) : $site['po_no_label']), $purchase_orders['PurchaseOrder']['po_number']), $customFieldsHtml);
    }
    if (!empty($purchase_orders['PurchaseOrder']['show_from_to']) && !empty($purchase_orders['PurchaseOrder']['from'])) {
        
        $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array((empty($layout['InvoiceLayout']['label_from_date']) ? __('From Date', true) : $layout['InvoiceLayout']['label_from_date']), $purchase_orders['PurchaseOrder']['from']), $customFieldsHtml);
    }
    if (!empty($purchase_orders['PurchaseOrder']['show_from_to']) && !empty($purchase_orders['PurchaseOrder']['to'])) {
        
        $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array((empty($layout['InvoiceLayout']['label_to_date']) ? __('To Date', true) : $layout['InvoiceLayout']['label_to_date']), $purchase_orders['PurchaseOrder']['to']), $customFieldsHtml);
    }
   // print_r($customFields);
    foreach ($layout['InvoiceLayoutCustomField'] as $customField) {
	
        $field = $customFields[$customField['label']];
        if (!empty($customFields[$customField['label']]) && !empty($field['real-value'])) {
            
            $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array($field['real-label'], $field['real-value']), $customFieldsHtml);
            unset($customFields[$customField['label']]);
        }
    }
    
    foreach ($customFields as $label => $customField) {

        debug ( $label );
        debug ( $customField );
        if (!empty($label) && !empty($customField['real-value'])) {
            
            $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array($customField['real-label'], $customField['real-value']), $customFieldsHtml);
        }
    }
}

$layoutHTML = str_replace(array('{%custom_fields%}', '<custom_field', '</custom_field'), array($layoutCustomFields, '<table', '</table'), $layoutHTML);

if ($layout['InvoiceLayout']['default_height'] == '0'){
    $layout['InvoiceLayout']['default_height'] = '';
}
$layoutHTML = str_replace(array('{%logo%}', '{%logo-width%}', '{%logo-height%}'), array($logo, $layout['InvoiceLayout']['default_width'], $layout['InvoiceLayout']['default_height']), $layoutHTML);
$layoutHTML = str_replace(array('{%invoice_number%}', '{%invoice_date%}'), array($purchase_orders['PurchaseOrder']['no'], $purchase_orders['PurchaseOrder']['date']), $layoutHTML);


if(isset($purchase_orders['PurchaseOrder']['id']) and !empty($purchase_orders['PurchaseOrder']['id']) && $invoice['Invoice']['invoice_layout_id'] == $layout['InvoiceLayout']['id']){
    $layout['InvoiceLayout']['item_columns']=$purchase_orders['PurchaseOrder']['item_columns'];
}



if(empty($layout['InvoiceLayout']['item_columns'])){
	
   $forcecols=1; 
$columns=array
(
    'field1' => array
        (
            'name' => 'name',
            'label' => $layout['InvoiceLayout']['label_item']
        ),

    'field2' => array
        (
            'name' => $hide_description==false?'description':'',
            'label' => $hide_description==false?$layout['InvoiceLayout']['label_description']:''
        ),

    'field3' => array
        (
            'name' => '',
            'label' => ''
        ),

    'field4' => array
        (
            'name' => '',
            'label' => ''
        ),

    'field5' => array
        (
            'name' => '',
            'label' => ''
        ),

);    

  
}else{

$columns=json_decode($layout['InvoiceLayout']['item_columns'],true);    

}





if(is_array($columns)&&count($columns))
{
    $purchase_orders['Invoice']=$purchase_orders['PurchaseOrder'];
    $layout['InvoiceLayout']['items_list']=$this->element('dynamic_items_list',array('invoice'=>$purchase_orders,'InvoiceItem'=>$purchase_orders['PurchaseOrderItem'],'columns'=>$columns));
}else{
//   if($layout['InvoiceLayout']['quantity_price']=="show_quantity_before_price"){
//       
//$layout['InvoiceLayout']['items_list']=str_replace(array('label_quantity','label_unit_price'),array('_label_quantity','_label_unit_price'),$layout['InvoiceLayout']['items_list']);
//$layout['InvoiceLayout']['items_list']=str_replace(array('_label_quantity','_label_unit_price'),array('label_unit_price','label_quantity'),$layout['InvoiceLayout']['items_list']);
//
//   }elseif($layout['InvoiceLayout']['quantity_price']=="hide_price_and_quantity"){
//   $layout['InvoiceLayout']['items_list']=str_replace('<th width="60" bgcolor="#e5e5e5" class="editable-area" id="label_unit_price">{%label_unit_price%}</th>','',$layout['InvoiceLayout']['items_list'])    ;
//   $layout['InvoiceLayout']['items_list']=str_replace('<th width="30" bgcolor="#e5e5e5" class="editable-area" id="label_quantity">{%label_quantity%}</th>','',$layout['InvoiceLayout']['items_list'])    ;
//   }
   
}


if(strpos($purchase_orders['PurchaseOrder']['html_notes'],'replace_template_notes'))  $layout['InvoiceLayout']['footer']= $purchase_orders['PurchaseOrder']['html_notes'];


preg_match_all('/\{%([^%]+)%\}/', $layoutHTML, $m);


$s = $m[0];
$r = array('{%business_info%}' => '', '{%client_info%}' => '');
 
foreach ($m[1] as $idx => $val) {


    $cellValue = isset($layout['InvoiceLayout'][$val])?$layout['InvoiceLayout'][$val]:$m[0][$idx];
    if ($val == 'business_info' || $val == 'client_info' || $val == 'ship_info') {
        $cellValue = nl2br_fix($layout['InvoiceLayout'][$val]);
    }
    $r[$s[$idx]] = $cellValue;
}

 
$layoutHTML = str_replace(array_keys($r), $r, $layoutHTML);


$itemsList = '';
/* @var $html HtmlHelper */
if (!$hide_description){
    
    foreach ($purchase_orders['PurchaseOrderItem'] as $item) {
        $item['quantity'] =(float)$item['quantity'];
        $item['unit_price']=(float)$item['unit_price'];
        $item['unit_price_factor'] = (float)$titem['unit_price_factor'];

    $td=array(
            nl2br($item['item']),
            nl2br($item['description'])
                );
       
//$layout['InvoiceLayout']['items_list']=str_replace(array('label_quantity','label_unit_price'),array('1','2'),$layout['InvoiceLayout']['items_list']);
    $td[]=empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['unit_price_factor'], $purchase_orders['PurchaseOrder']['currency_code'], false) : format_price_simple($item['unit_price_factor'], $purchase_orders['PurchaseOrder']['currency_code'], false);
    $td[]=$item['quantity_written'];//format_number($item['quantity'], $purchase_orders['PurchaseOrder']['currency_code']);

    $td[]=  empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['quantity'] * $item['unit_price'], $purchase_orders['PurchaseOrder']['currency_code'], false) : format_price_simple($item['quantity'] * $item['unit_price'], $purchase_orders['PurchaseOrder']['currency_code'], false);
    
        $itemsList .= $html->tableCells($td, array('class' => 'line'), array('class' => 'line'));
    }
}else{
    
    foreach ($purchase_orders['PurchaseOrderItem'] as $item) {

        $item['quantity'] =(float)$item['quantity'];
        $item['unit_price']=(float)$item['unit_price'];
        $item['unit_price_factor'] = (float)$titem['unit_price_factor'];
        $itemsList .= $html->tableCells(array(
            nl2br($item['item']),
            empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['unit_price_factor'], $purchase_orders['PurchaseOrder']['currency_code'],false) : format_price_simple($item['unit_price_factor'], $purchase_orders['PurchaseOrder']['currency_code'],false),
            $item['quantity_written'],//format_number($item['quantity'], $purchase_orders['PurchaseOrder']['currency_code']),
            empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['quantity'] * $item['unit_price'], $purchase_orders['PurchaseOrder']['currency_code'],false) : format_price_simple($item['quantity'] * $item['unit_price'], $purchase_orders['PurchaseOrder']['currency_code'],false)
                ), array('class' => 'line'), array('class' => 'line'));
    }
}
// Moved To Element !!! dynamic items list
//if(!empty( $layout['InvoiceLayout']['item_columns'])&&is_array($columns=json_decode($layout['InvoiceLayout']['item_columns'],true))&&count($columns))
//{
//    $keymap['field3']='col_3';
//    $keymap['field4']='col_4';
//    $keymap['field5']='col_5';
//    unset($itemsList);
//      foreach ($purchase_orders['InvoiceItem'] as $item) {
//          foreach($columns as $key=>$col){
//              if(!empty($col['name'])){
//              if($col['name']=="name"){
//              $colvalue='item';    
//              }elseif($col['name']=="description"){
//              $colvalue='description';    
//              }else{
//                  $colvalue=$keymap[$key];
//              }
//              $listitem[]=$item[$colvalue];
//              }
//          }
//          $listitem[]= empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['quantity'] * $item['unit_price'], $purchase_orders['PurchaseOrder']['currency_code']) : format_price_simple($item['quantity'] * $item['unit_price'], $purchase_orders['PurchaseOrder']['currency_code']);
//      $itemsList .= $html->tableCells($listitem, array('class' => 'line'));
//      unset($listitem);
//      }
//}

$placeholders = array(
    '{%business_name%}' => $site['business_name'],
    '{%cols%}' => $cols,
    '{%first_name%}' => $site['first_name'],
    '{%last_name%}' => $site['last_name'],
    '{%subdomain%}' => $site['subdomain'],
    '{%address1%}' => $site['address1'],
    '{%address2%}' => $site['address2'],
    '{%city%}' => $site['city'],
    '{%state%}' => $site['state'],
    '{%postal_code%}' => $site['postal_code'],
    '{%full_business_address%}' => $this->element('format_address_html',$site+array('is_inline'=>true)),
    '{%phone1%}' => $site['phone1'],
    '{%phone2%}' => $site['phone2'],
    '{%telephone%}' => $site['phone1'],
    '{%mobile%}' => $site['phone2'],

    '{%items%}' => $itemsList,
    '{%invoice_notes%}' => nl2br(h($purchase_orders['PurchaseOrder']['notes'])) . '<span style="font-style: normal;" >' . strip_tags(str_replace(array('onclick', 'onmouseup', 'onmousewheel', 'onscroll', 'onmousedown', 'onmousemove', 'onmouseout', 'onmouseover'), '', $purchase_orders['PurchaseOrder']['html_notes']), '<div><span><ul><li><ol><br><p><b><i><strong><font><small><big><h1><h2><h3><h4><h5><h6><h7><a><style>') . '</span>',
    //'{%html_invoice_notes%}' => , 
    '{%from-date%}' => $purchase_orders['PurchaseOrder']['from'],
    '{%to-date%}' => $purchase_orders['PurchaseOrder']['to'],
)+$all_place_holder;

debug($placeholders['{%client_secondary_name%}']);




if (!empty($layout['InvoiceLayoutTag'])) {
    foreach ($layout['InvoiceLayoutTag'] as $tag) {
        $placeholders[$tag['tag']] = $tag['replacement'];
    }
}

//	$labels = array('label_invoice_no', 'label_date', 'label_po_no', 'label_total', 'label_status', 'label_due_after', 'label_due_date', 'label_deposit', 'label_paid_amount', 'label_unpaid_amount', 'label_subtotal', 'label_description', 'label_item', 'label_tax1', 'label_tax2', 'label_quantity', 'label_unit_price', 'label_item_total', 'label_from_date', 'label_to_date');

preg_match_all('/{%(label_[^%]+)%}/', $layoutHTML, $m);
$labels = $m[1];
foreach ($labels as $label) {
    
    $placeholders['{%' . $label . '%}'] = $layout['InvoiceLayout'][$label];
    
}

$placeholders['{%value_item_total%}'] = format_price($purchase_orders['PurchaseOrder']['summary_subtotal'] + (!empty($purchase_orders['PurchaseOrder']['adjustment_value']) ? $purchase_orders['PurchaseOrder']['adjustment_value']  * -1 : 0), $purchase_orders['PurchaseOrder']['currency_code']);
$placeholders['{%value_unpaid_amount%}'] = format_price($purchase_orders['PurchaseOrder']['summary_unpaid'], $purchase_orders['PurchaseOrder']['currency_code']);
$placeholders['{%value_paid_amount%}'] = format_price($purchase_orders['PurchaseOrder']['summary_paid'] * -1, $purchase_orders['PurchaseOrder']['currency_code']);
$placeholders['{%value_total%}'] = format_price($purchase_orders['PurchaseOrder']['summary_total'], $purchase_orders['PurchaseOrder']['currency_code']);
$taxes = '';
if(!empty( $layout['InvoiceLayout']['item_columns'])&&is_array($columns=json_decode($layout['InvoiceLayout']['item_columns'],true))&&count($columns))
{

$table_cell = '<tr>
					<td bgcolor="#FFF" colspan="1" style="border:none;"></td>
					<td colspan="1" style="border-left:none;border-right:none;"><strong id="label_item_total" class="editable-area">{%label%}</strong></td>
					<td class="text-left" style="border-left:none;border-right:none;">{%value%}</td>
			</tr>';
}else{
if($forcecols==1){
 $table_cell = '<tr>
					<td width="65%" style="border:none;" bgcolor="#FFF"></td>
					<td colspan="1" style="border-left:none;border-right:none;"><strong id="label_item_total" class="editable-area">{%label%}</strong></td>
					<td class="text-left" style="border-left:none;border-right:none;">{%value%}</td>
			</tr>';   
}else{
    
$table_cell = '<tr>
					<td bgcolor="#FFF" colspan="'.$cols.'" style="border:none;"></td>
					<td colspan="2" style="border-left:none;border-right:none;"><strong id="label_item_total" class="editable-area">{%label%}</strong></td>
					<td class="text-left" style="border-left:none;border-right:none;">{%value%}</td>
			</tr>';
}
}
if(!empty($purchase_orders['PurchaseOrder']['summary_total_discount'])) {$purchase_orders['PurchaseOrder']['summary_discount']=$purchase_orders['PurchaseOrder']['summary_total_discount'];$purchase_orders['PurchaseOrder']['discount_amount']=1;}
if ($purchase_orders['PurchaseOrder']['summary_discount']) {
    $taxes .= str_replace(
        array('{%label%}', '{%value%}','{%sclass%}'), array(empty($purchase_orders['PurchaseOrder']['discount_amount'])?sprintf('%s (%s%%)', $layout['InvoiceLayout']['label_discount'], round($purchase_orders['PurchaseOrder']['discount'], 2)):$layout['InvoiceLayout']['label_discount'], format_price($purchase_orders['PurchaseOrder']['summary_discount'] * -1, $purchase_orders['PurchaseOrder']['currency_code']),'row-summary-discount'), $table_cell);
}



foreach ($purchase_orders['PurchaseOrderTax'] as $tax) {
    $taxes .= str_replace(
            array('{%label%}', '{%value%}'), array(sprintf('%s (%s%%)', $tax['name'], round($tax['value'], 2)), format_price($tax['invoice_value'], $purchase_orders['PurchaseOrder']['currency_code'])), $table_cell);
}
$style="";

if (!empty($purchase_orders['PurchaseOrder']['shipping_amount'])) {
    $taxes .= str_replace(
            array('{%label%}', '{%value%}'), array(empty($layout['InvoiceLayout']['label_shipping'])?__('Shipping',true):$layout['InvoiceLayout']['label_shipping'],  format_price($purchase_orders['PurchaseOrder']['shipping_amount'], $purchase_orders['PurchaseOrder']['currency_code'])), $table_cell);
}

if (!empty($purchase_orders['PurchaseOrder']['adjustment_value'])) {
    $taxes .= str_replace(
            array('{%label%}', '{%value%}'), array(
                empty($purchase_orders['PurchaseOrder']['adjustment_label'])?__('Adjustment', true):$purchase_orders['PurchaseOrder']['adjustment_label'],
                $purchase_orders['PurchaseOrder']['adjustment_value'],
            ),
            $table_cell
    );
}

$refunded_row='';
if (!empty($purchase_orders['PurchaseOrder']['summary_refund'])) {
    $refunded_row = str_replace( array('{%label%}', '{%value%}'), array(empty($layout['InvoiceLayout']['label_refunded'])?__('Refunded',true):$layout['InvoiceLayout']['label_refunded'],  format_price($purchase_orders['PurchaseOrder']['summary_refund'], $purchase_orders['PurchaseOrder']['currency_code'])), $table_cell);
     if (strpos($layoutHTML, '#<!-- PaidAmount -->'))
        $layoutHTML = str_replace('#<!-- PaidAmount -->', $refunded_row . ' #<!-- PaidAmount -->', $layoutHTML);
    else if (strpos($layoutHTML, '<!-- PaidAmount -->'))
        $layoutHTML = str_replace('<!-- PaidAmount -->', $refunded_row . ' <!-- PaidAmount -->', $layoutHTML);
}

$placeholders['<head>'] = '<head>' . $style;

$placeholders['{%invoice-taxes%}'] = $taxes;
//if ($branded) {
//$placeholders['</body>'] = $this->element('branded') . '</body>';
//}


foreach ($placeholders as $k => $v) {

    if (empty($v))
        $layoutHTML = preg_replace('/' . $k . '(\r|\s|\n|,|<br\/>|<br>|<br \/>|)*/', $v, $layoutHTML);
    $layoutHTML = str_replace($k, $v, $layoutHTML);
}

if ($purchase_orders['PurchaseOrder']['summary_deposit'] && $purchase_orders['PurchaseOrder']['summary_deposit'] != $purchase_orders['PurchaseOrder']['summary_unpaid'] && abs($purchase_orders['PurchaseOrder']['summary_deposit'] - $purchase_orders['PurchaseOrder']['summary_unpaid']) >= 0.01) {
    $next_payment_row = str_replace(
            array('{%label%}', '{%value%}'), array(sprintf('%s ', $layout['InvoiceLayout']['label_deposit']), format_price($purchase_orders['PurchaseOrder']['summary_deposit'], $purchase_orders['PurchaseOrder']['currency_code'])), $table_cell);

    if (strpos($layoutHTML, '#<!-- PaidAmount -->'))
        $layoutHTML = str_replace('#<!-- PaidAmount -->', $next_payment_row . ' #<!-- PaidAmount -->', $layoutHTML);
    else if (strpos($layoutHTML, '<!-- PaidAmount -->'))
        $layoutHTML = str_replace('<!-- PaidAmount -->', $next_payment_row . ' <!-- PaidAmount -->', $layoutHTML);
    else
        $taxes .= str_replace(
                array('{%label%}', '{%value%}'), array(sprintf('%s ', $layout['InvoiceLayout']['label_deposit']), format_price($purchase_orders['PurchaseOrder']['summary_deposit'], $purchase_orders['PurchaseOrder']['currency_code'])), $table_cell);
}

    if ($purchase_orders['PurchaseOrder']['type']==Invoice::Credit_Note || $purchase_orders['PurchaseOrder']['type']==Invoice::Refund_Receipt || (empty($layout['InvoiceLayout']['show_balance_due']) && (empty($purchase_orders['PurchaseOrder']['summary_paid']) || $purchase_orders['PurchaseOrder']['summary_paid'] == $purchase_orders['PurchaseOrder']['summary_total']))) {
    $layoutHTML = preg_replace('#<!-- PaidAmount -->.*<!-- /PaidAmount -->#sm', '', $layoutHTML);
    }


if ($purchase_orders['PurchaseOrder']['summary_subtotal'] == $purchase_orders['PurchaseOrder']['summary_total']) {
    $layoutHTML = preg_replace('#<!-- Subtotal -->.*<!-- /Subtotal -->#sm', '', $layoutHTML);
}

if ($hide_description) {
    $layoutHTML = preg_replace('#<!-- Description -->.*<!-- /Description -->#sm', '', $layoutHTML);
}

$layoutHTML = preg_replace('/<\/table[^>]*>[\r\n\s]*<table[^>]+custom_fields[^>]+>/', '', $layoutHTML);

echo $layoutHTML;

if ($layout['InvoiceLayout']['language_id'] == 'ara'||$layout['InvoiceLayout']['language_id'] == 'ar'||$layout['InvoiceLayout']['language_id'] == 7) {
		//debug('GALLO');
    $style = '<style type="text/css">' . file_get_contents(WWW_ROOT . 'css/invoice-layout-template-ar.css') . (!empty($more_style) ? $more_style : '') . '</style>';
} else {
	//debug('SSSALLO');
    $style = '<style type="text/css">' . file_get_contents(WWW_ROOT . 'css/invoice-layout-template.css') . (!empty($more_style) ? $more_style : '') . '</style>';
}
?>
    <? echo $style ?>    
<?php
echo $this->element('view-style',array('ViewCss'=>$ViewCss));
?>
<?php
$payment_count=count((array)$purchase_orders['PurchaseOrderPayment']);
$payment_row=$purchase_orders['PurchaseOrderPayment'][0] ?? null;
$all_place_holder +=PlaceHolder::last_invoice_payment_place_holder($payment_row,true);
$out = ob_get_clean();
//html_sticky_footer
//print_pre($layout['InvoiceLayout']);
//echo $layout['InvoiceLayout']['sticky_footer'];
//die();
$all_place_holder['{%logo%}']=$logo;
$all_place_holder['{%logo-width%}']=$layout['InvoiceLayout']['default_width'];
if($layout['InvoiceLayout']['default_height']=='0'){
    $layout['InvoiceLayout']['default_height']='';
}
$all_place_holder['{%logo-height%}']=$layout['InvoiceLayout']['default_height'];
$all_place_holder['{%invoice_title%}']=$layout['InvoiceLayout']['invoice_title'];
foreach($layout['InvoiceLayout'] as $key=>$value) {
    if(strpos($key,"label_")==0) {
        $all_place_holder['{%' . $key . '%}'] = $layout['InvoiceLayout'][$key];
    }
}
if ($this->params['url']['ext'] != 'pdf') {
$all_place_holder['{%html_sticky_footer%}']=nl2br(PlaceHolder::replace($layout['InvoiceLayout']['sticky_footer'],array_keys($all_place_holder),array_values($all_place_holder)));    
$all_place_holder['{%html_sticky_header%}']=nl2br(PlaceHolder::replace($layout['InvoiceLayout']['sticky_header'],array_keys($all_place_holder),array_values($all_place_holder)));    
}else{
$sticky_footer_html= nl2br(PlaceHolder::replace($layout['InvoiceLayout']['sticky_footer'],array_keys($all_place_holder),array_values($all_place_holder)));    
$this->PoFooter=$sticky_footer_html;
$sticky_header_html= nl2br(PlaceHolder::replace($layout['InvoiceLayout']['sticky_header'],array_keys($all_place_holder),array_values($all_place_holder)));
$this->PoHeader=$sticky_header_html;
}
$final_out_put= PlaceHolder::replace($out,array_keys($all_place_holder),array_values($all_place_holder));
$final_out_put = formatSaudiRiyalSymbol($final_out_put, $purchase_orders['PurchaseOrder']['currency_code']);
echo $final_out_put;
