<?php

use Izam\Aws\Aws;

$status = $statuses[$invoicePayment['InvoicePayment']['status']];
$invoice=$invoicePayment;
$isbox=isset($this->params['url']['box']);
$owner = getAuthOwner();
$site = getCurrentSite();
$full_name = $owner['first_name'] . ' ' . $owner['last_name'];
$viewlinks = Invoice::getInvoiceViewActionList();
$invoiceTypespluralize=Invoice::getInvoiceTypeList(true);
$invoiceTypes=Invoice::getInvoiceTypeList(false);
$invoiceActions=Invoice::getInvoiceActionList();
$invoiceViewActions=Invoice::getInvoiceViewActionList();
echo $html->css('view_new_style.css');
if($is_rtl){echo $html->css('view_new_style_ar.css'); }
if(!empty($invoicePayment['Client']['id']))  $client_link=$html->link(  $invoicePayment['Client']['business_name'] , Router::url (['controller' => 'clients' , 'action'=>'view', $invoicePayment['Client']['id']]  ),array('target'=>'_blank') );
else if(!empty($invoicePayment['Invoice']['client_id'])) $client_link=$html->link(  $invoicePayment['Invoice']['client_business_name'] , Router::url (['controller' => 'clients' , 'action'=>'view', $invoicePayment['Invoice']['client_id']]  ),array('target'=>'_blank') );

$invoice_payment_code = (!empty($invoicePayment['InvoicePayment']['code']))?$invoicePayment['InvoicePayment']['code']:$invoicePayment['InvoicePayment']['id'];
?>
<div class="pages-head p-10">
	<div class="row">
		<div class="col-xs-6">
				<?php if ($this->params['prefix'] == 'client'){ ?>
				<?php
				if (empty($invoicePayment['InvoicePayment']['client_id'])) {
					?>
			<h1><?php echo sprintf(__('Payment %s for Invoice %s', true), '#' . $invoice_payment_code, $html->link('#' . $invoicePayment['Invoice']['no'], array('controller' => 'invoices', 'action' => $viewlinks[$invoicePayment['Invoice']['type']], $invoicePayment['Invoice']['id']), array('target' => '_blank'))); ?>    </h1>
				<?
				}else{
					?>
			<h1><?php echo sprintf(__('Payment %s', true), '#' . $invoice_payment_code, $html->link('#' . $invoicePayment['Invoice']['no'], array('controller' => 'invoices', 'action' => $viewlinks[$invoicePayment['Invoice']['type']], $invoicePayment['Invoice']['id']), array('target' => '_blank'))); ?>    </h1>
				<?php
				}
			   }
			?>
			<?php if ($this->params['prefix'] == 'owner'){ ?>

				<?php
				if (!empty($invoicePayment['Invoice']['id'])) {

					?>
					<?php $title = sprintf(__('Payment %s', true), '#' . $invoice_payment_code, $html->link('#' . $invoicePayment['Invoice']['no'], array('controller' => 'invoices', 'action' => $viewlinks[$invoicePayment['Invoice']['type']], $invoicePayment['Invoice']['id']), array('target' => '_blank'))); ?>
					<?php
                                $breadcrumbs = [
						['link' => Router::url(array('controller' => 'invoices', 'action' => 'index')), 'title' => $invoiceTypespluralize[$invoicePayment['Invoice']['type']] ],
                                                ['link' => Router::url (['controller' => 'invoices' , 'action'=>$invoiceViewActions[$invoice['Invoice']['type']], $invoice['Invoice']['id']]  ) ,'title' => $invoiceTypes[$invoice['Invoice']['type']].sprintf(__(' %s', true ),'#'.$invoice['Invoice']['no'] )],
						['link' => '#', 'title' => $title],
				];
				} else {

					?>
					<?php $title = sprintf(__('Payment %s', true), '#' . $invoice_payment_code, $html->link('#' . $invoicePayment['Client']['client_number'], array('controller' => 'clients', 'action' => 'view', $invoicePayment['Client']['id']), array('target' => '_blank'))); ?>
					<?php
                                $breadcrumbs = [
						['link' => Router::url(array('controller' => 'clients', 'action' => 'index')), 'title' => __('Clients',true) ],
                                                ['link' => Router::url (['controller' => 'clients' , 'action'=>'view', $invoicePayment['Client']['id']]  ) ,'title' => sprintf(__(' %s', true ),$invoicePayment['Client']['business_name'] )],
						['link' => '#', 'title' => $title],
				];
				}
				?>
				<?php

                                if(!$isbox){

				?>
					
			<h1 class=" "><? echo $title ?></h1>
			<?
                                }else{
                                    echo '<h1>'.$title.'</h1>';
                                }
			}
			?>
            <div class="sub-headings" style="clear:both;">
            <?php if ($this->params['prefix'] == 'owner') {

                if(!empty($client_link)) {
             ?>
                <span class="invoice-client sub-heading sub-heading2"><?php echo __('Client', true) . ': '.$client_link ?></span>
            <?php } ?>

            <?php if(isset($client_payemnt_id)) { ?>
                <span class="invoice-client sub-heading sub-heading2"><?php echo __('Client Distribution Payment', true) . ': ' . $html->link('#' . $client_payemnt_id, Router::url(['controller' => 'invoice_payments', 'action' => 'view', $client_payemnt_id]))?></span>
            <?php } ?>

                <?php
            if ($this->params['prefix'] == 'owner'&&ifPluginActive(AccountingPlugin)&&(check_permission(VIEW_ALL_JOURNALS) || (check_permission(VIEW_OWN_JOURNALS) && $invoicePayment['Staff']['id'] == getAuthOwner('staff_id')))&&!empty($linked_journal)) {
                echo $this->element('linked-journal',['journal'=>$linked_journal]);
            }
            }
            ?>
            </div>

		</div>
		<div class="col-xs-6">
			<?php if ($this->params['prefix'] == 'owner'): ?>



				<div class="top-actions">
					<?php
					$editURL = array('action' => 'edit', $invoicePayment['InvoicePayment']['id']);
					$deleteURL = array('action' => 'delete', $invoicePayment['InvoicePayment']['id']);

					$printURL = array('action' => 'print', $invoicePayment['InvoicePayment']['id']);
					$printURL['?'] = 'box=1';
					if (!empty($_GET['box'])) {

						$printURL['?'] = 'box=1';
						$editURL['?'] = 'box=1';
					}
					?>
					<?php if ($invoicePayment['InvoicePayment']['status'] != PAYMENT_STATUS_COMPLETED): ?>
						<?php
						$approveURL = array('action' => 'approve', $invoicePayment['InvoicePayment']['id']);
						if (!empty($this->params['url']['box'])) {
							$approveURL['?'] = 'box=1';
						}
						?>
						<a href="<?php echo Router::url($approveURL); ?>" class="btn button btn-success pull-right"><?php __("Confirm Received") ?><span class="tooltip" title="confirm-received-payment"></span></a>
					<?php endif; ?>
					<?php if ((check_permission(Invoices_Add_Invoice_Payments) && $invoicePayment['InvoicePayment']['staff_id'] == $owner['staff_id']) || check_permission(Invoices_Add_Payments_to_All)) { ?>
						<a href="<?php echo Router::url($editURL); ?>" class="btn button btn-success <?php if(!$isbox){echo 'btn-addon';} ?> pull-right">
							<i class="fa fa-edit"></i> <?php __("Edit") ?></a>
					<? } ?>
					<?
					if (true) {
						?>

                    <?php
                        $links = [];

                        if ($has_templates) {
                            foreach ($printableTemplates as $template) {
                                if ($template['PrintableTemplate']['default_template'] == 1) {
                                    $default_template = $template;
                                    continue;
                                }
                                $links[] = [
                                    'url' => Router::url(array('controller' => 'printable_templates', 'action' => 'view',$invoicePayment['InvoicePayment']['id'], $template['PrintableTemplate']['id'], $template['PrintableTemplate']['type'])),
                                    'title' => $template['PrintableTemplate']['name']
                                ];
                            }

                            if ((!empty($view_templates['global']) || !empty($view_templates['local']))) {
                                foreach ($view_templates['global'] as $globalTemplate) {
                                    $links[] = [
                                        'url' => get_global_template_view_url($globalTemplate['id'], $invoicePayment['InvoicePayment']['id']),
                                        'title' => $globalTemplate['name'],
                                    ];
                                }

                                foreach ($view_templates['local'] as $localTemplate) {
                                    $links[] = [
                                        'url' => get_local_template_view_url($localTemplate['id'], $invoicePayment['InvoicePayment']['id']),
                                        'title' => $localTemplate['name'],
                                    ];
                                }
                            }
                        }

                        array_unshift($links,[
                            'url' => Router::url($printURL),
                            'title' => '<i class="d-inline-block d-md-none fa-receipt fas s2020"></i><span class="d-none d-md-inline-block">'.__("Receipt",true).'</span>'
                        ]);

                        if( ! empty($default_template) )
                            array_unshift($links,[
                                'url' => Router::url(array('controller' => 'printable_templates', 'action' => 'view',$invoicePayment['InvoicePayment']['id'], $default_template['PrintableTemplate']['id'], $default_template['PrintableTemplate']['type'])),
                                'title' => $default_template['PrintableTemplate']['name']
                            ]);

                     ?>


						<?= $this->element('invoice_payment_templates_button',['links'=>$links, 'button_class'=>'btn-default button right']); ?>

                        <a href="<?php echo Router::url($deleteURL); ?>" class="btn button btn-danger mr-2 <?php if(!$isbox){echo 'btn-addon';} ?> pull-right">
                            <i class="fa fa-trash"></i> <?php __("Delete") ?>
                        </a>



					<? } ?>

				</div>
			<?php endif; ?>
		</div>
	</div>
</div>

<?php
if (!empty($_GET['box'])) {
    $html->css('pages', null, array(), false);
}
$html->css('owner-page', false, ['inline' => false]);
?>
<div class="row">
    <div class="col-md-6">
        <div class="input-fields inside-form-box">
            <h3 class="rounded-item  head-bar theme-color-a"><span class="contact-info">
                </span><?php __("Client Details") ?></h3>
            <div class="inputs-group-list">
                <table cellpadding="0" cellspacing="0" class="table table-striped" width="100%">
                    <tr>
                        <td><strong><?php __("Client Name") ?></strong></td>
                        <td>

                            <?php
                            if($this->params['prefix'] == 'owner' && !empty($client_link)) { echo   '<u>'.$client_link.'</u>' ; }
                            else { echo !empty($invoicePayment['InvoicePayment']['first_name']) ? ($invoicePayment['InvoicePayment']['first_name'] . " " . $invoicePayment['InvoicePayment']['last_name']) : (!empty($invoicePayment['Invoice']['client_first_name']) ? ($invoicePayment['Invoice']['client_first_name'] . " " . $invoicePayment['Invoice']['client_last_name']) : (!empty($invoicePayment['Invoice']['client_business_name']) ? $invoicePayment['Invoice']['client_business_name'] : $invoicePayment['Client']['business_name'])); } ?>
                        </td>
                    </tr>
                    <?php if (!empty($invoicePayment['InvoicePayment']['address1'])) { ?>
                        <tr>
                            <td><strong><?php __("Street Address") ?></strong></td>
                            <td><?php echo ($invoicePayment['InvoicePayment']['address1']) ?></td>
                        </tr>
                        <?php if ($invoicePayment['InvoicePayment']['address2']): ?>
                            <tr>
                                <td>&nbsp;</td>
                                <td><?php echo ($invoicePayment['InvoicePayment']['address2']) ?></td>
                            </tr>
                        <?php endif; ?>
                    <? } else if (!empty($invoicePayment['Invoice']['client_address1'])) { ?>
                        <tr>
                            <td><strong><?php __("Street Address") ?></strong></td>
                            <td><?php echo ($invoicePayment['Invoice']['client_address1']) ?></td>
                        </tr>
                        <?php if ($invoicePayment['Invoice']['client_address2']): ?>
                            <tr>
                                <td>&nbsp;</td>
                                <td><?php echo ($invoicePayment['Invoice']['client_address2']) ?></td>
                            </tr>
                        <?php endif; ?>
                    <? } ?>
                    <tr>
                        <td><strong><?php __("City") ?></strong></td>
                        <td><?php echo ($invoicePayment['InvoicePayment']['city']) ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php __("State") ?></strong></td>
                        <td><?php echo ($invoicePayment['InvoicePayment']['state']) ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php __("Postal Code") ?></strong></td>
                        <td><?php echo ($invoicePayment['InvoicePayment']['postal_code']) ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php __("Telephone") ?></strong></td>
                        <td><?php echo ($invoicePayment['InvoicePayment']['phone1']) ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php __("Country") ?> </strong></td>
                        <td><?php echo (empty($invoicePayment['InvoicePayment']['country_code']) ? "" : $invoicePayment['InvoicePayment']['country_code']) ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php __("Currency") ?></strong></td>
                        <td><?php echo (empty($invoicePayment['InvoicePayment']['currency_code']) ? "" : $invoicePayment['InvoicePayment']['currency_code']) ?></td>
                    </tr>
                    <?php if ($this->params['prefix'] == 'owner'): ?>
                        <tr>
                            <td><strong><?php __("IP") ?></strong></td>
                            <td><?php echo ($invoicePayment['InvoicePayment']['ip']) ?></td>
                        </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="input-fields inside-form-box">
            <h3 class="rounded-item  head-bar theme-color-a"><span class="contact-info">
                </span><?php __("Payment Details") ?></h3>
            <div class="inputs-group-list">
                <table cellpadding="0" cellspacing="0" class="table table-striped" width="100%">
                    <?
                    if (empty($invoicePayment['InvoicePayment']['client_id'])) {
                        ?>
                        <tr>
                            <td><strong><?php __("Invoice No.") ?></strong></td>
                            <td><?php echo $html->link($invoicePayment['Invoice']['no'], array('controller' => 'invoices', 'action' => $invoicePayment['Invoice']['type'] == 0 ? 'view' : 'view_refund', $invoicePayment['Invoice']['id']), array('target' => '_top')) ?></td>
                        </tr>
                    <? } ?>
                    <tr>
                        <td><strong><?php __("Payment Method") ?></strong></td>
                        <td><?php echo __($paymentMethods[$invoicePayment['InvoicePayment']['payment_method']]) ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php __("Amount") ?></strong></td>
                        <td><?php echo format_price($invoicePayment['InvoicePayment']['amount'], $invoicePayment['InvoicePayment']['currency_code']) ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php __("Transaction ID") ?></strong></td>
                        <td><?php echo $invoicePayment['InvoicePayment']['transaction_id'] ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php __("Status") ?></strong></td>
                        <td><?php echo $status ?></td>
                    </tr>
                    <?php if (!empty($invoicePayment['InvoicePayment']['response_code'])): ?>
                        <tr>
                            <td width="35%"><strong><?php __("Response Code") ?></strong></td>
                            <td><?php echo ($invoicePayment['InvoicePayment']['response_code']) ?></td>
                        </tr>
                    <?php endif; ?>
                    <?php if (!empty($invoicePayment['InvoicePayment']['response_message'])): ?>
                        <tr>
                            <td><strong><?php __("Response Message") ?></strong></td>
                            <td><?php echo nl2br($invoicePayment['InvoicePayment']['response_message']) ?></td>
                        </tr>
                    <?php endif; ?>
                    <tr>
                        <td><strong><?php __("Date") ?></strong></td>
                        <td>
                            <?php
                            echo format_date($invoicePayment['InvoicePayment']['date']);
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong><?php __("Added By") ?></strong></td>
                        <td>
                            <?php 
                            if($invoicePayment['InvoicePayment']['added_by'] == 0){
                                echo __('Client') .': ';
                                if($this->params['prefix'] == 'owner')
                                    echo '<span style="text-decoration: underline">' . $client_link . '</span>';
                                else
                                    echo $invoicePayment['Invoice']['client_business_name'];
                            }
                            elseif($invoicePayment['InvoicePayment']['staff_id'] == 0)
                                echo $full_name;
                            elseif($invoicePayment['InvoicePayment']['staff_id'] == -3)
                                echo 'API';
                            else
                                echo $invoicePayment['Staff']['name'];
                            ?>
                        </td>
                    </tr>
                    <?php if ( $invoicePayment['InvoicePayment']['payment_method'] != 'starting_balance' && count ( $treasuries ) >  1 && !empty ($treasuries[$invoicePayment['InvoicePayment']['treasury_id']] ) ){?>
                    <tr><td><strong><?php echo __("Treasury",true);?></strong></td>
                        <?php
                        if ($invoicePayment['InvoicePayment']['payment_method'] != 'client_credit') { ?>
                            <td><?php echo $treasuries[$invoicePayment['InvoicePayment']['treasury_id']] ?></td>
                            <?php } else { ?>
                            <td> <?php __('Client Credit') ?></td>
                            <?php } ?>
                     </tr><?php }?>
                    <?php if ($invoicePayment['InvoicePayment']['notes']): ?>
                        <tr>
                            <td><strong><?php __("Details") ?></strong></td>
                            <td><?php echo nl2br($invoicePayment['InvoicePayment']['notes']) ?></td>
                        </tr>
                    <?php endif; ?>
                    <?php if ($invoicePayment['InvoicePayment']['receipt_notes']): ?>
                        <tr>
                            <td><strong><?php __("Receipt Notes") ?></strong></td>
                            <td><?php echo nl2br($invoicePayment['InvoicePayment']['receipt_notes']) ?></td>
                        </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>

    </div>

    <?php if (!empty($invoicePayment['InvoicePayment']['attachment'])) { ?>
        <div class="view-actions btn-group">
            <a class="btn btn-success pull-right send-to-client" href="<?php echo $invoicePayment['InvoicePayment']['attachment_full_path']; ?>" target="_blank"> <i class="fa fa-cloud-download"></i> Download the Attachment <br/>(<strong><?php echo substr($invoicePayment['InvoicePayment']['attachment'], 6, 40); ?></strong>)</a>
        </div>
    <? } ?>

    <?php if (!empty($invoicePayment['Attachments'])) {
        echo $this->element('file-preview-aws',['attachment'=> $invoicePayment['Attachments']]);
    } ?>

</div>

<div class="clear"></div>

<?php if (!empty($this->params['url']['box'])): ?>
    <script type="text/javascript">//<![CDATA[
        $('.accept-payment').click(function() {
            var href = $(this).attr('href');
            $.ajax({
                url: href,
                dataType: 'json',
                cache: false,
                success: function(data) {
                    console.log(data);
                    var div = $('<div>', {'class': data.messageClass}).html(data.message);
                    $('.pages-head').after(div);
                    if (data.status) {
                        window.parent.location.reload();
                    }
                }
            });
            return false;
        })
        //]]></script>
<?php endif; ?>
	<style>
		.m-t-3{
			margin-top: 3px;
		}
		.p-10{
			padding: 10px !important;
		}
	</style>
