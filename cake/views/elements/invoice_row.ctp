<?php
$iframe = $_GET["box"];

if (!empty($row))
    $invoice = $row;
if (empty($invoice_type))
    $invoice_type = 'invoice';

if ($invoice_type == 'invoice' && $row['Invoice']['type'] != 16) {
    $view_link = 'view';
} elseif ($invoice_type == 'creditnote') {
    $view_link = 'view_creditnote';
} elseif ($invoice_type == 'insurance') {
    $view_link = 'view_insurance';
} elseif ($invoice_type == 'insurance_refund') {
    $view_link = 'view_insurance_refund';
} elseif ($invoice_type == 'refund') {
    $view_link = 'view_refund';
} elseif ($row['Invoice']['type'] == 16) {
    $view_link = 'view_debitnote';
} else {
    $view_link = 'view_estimate';
}
// warning suppress
$show_requisitions = isset($enable_requisitions) && $enable_requisitions && !empty($invoice['Invoice']['requisition_delivery_status']) && !isset($stockIds[$invoice['Invoice']['id']]);
if ( $show_requisitions ) {
    $delivery_status = Requisition::$delivery_status_list[$invoice['Invoice']['requisition_delivery_status']];
}
?>
<li class="day-view-entry day-view-entry-label" <?php if (!empty($invoice['Invoice']['follow_up_status'])) { ?> style="border-color:<?php echo $invoice_status_colors[$invoice['Invoice']['follow_up_status']]; ?>" <? } ?>>
    <?php
    if (!empty($check_box)) {
        echo $check_box;
    }
    ?>
    <table cellspacing="0" cellpadding="0" border="0">
        <tbody>
            <tr>
                <td class="clickable" onclick="window.top.location.href = '<?php echo Router::url(array('controller' => 'invoices', 'action' => $view_link, $invoice['Invoice']['id'])) ?>'">
                    <div  class="invoice-row entry-info">

                        <?php if (empty($no_client)) {
                            $invoice_client_name = $invoice['Invoice']['client_first_name'] . ' ' . $invoice['Invoice']['client_last_name'];
                            $invoice_client_name = strlen($invoice_client_name) > 1 ? $invoice_client_name : $invoice['Client']['business_name'];
                            ?>
                            <div class="project-client">
                                <span class="project"><?= strlen($invoice['Invoice']['client_business_name']) > 1 ? $invoice['Invoice']['client_business_name'] : $invoice_client_name ?> </span>
                            </div>
                        <?php } ?>
                        <?php
                            $codeMessage = match ($row['Invoice']['type']){
                                (string)Invoice::DEBIT_NOTE => __('Debit Note', true). ' ',
                                (string)Invoice::ADVANCE_PAYMENT => __('Advance Payment', true). ' ',
                                default => null
                            };
                        ?>
                        <div class="task-notes"> <span class="project"><?php echo  $codeMessage ?>#<?php echo $invoice['Invoice']['no']; ?></span> - <span class="expense-date"> <?php echo format_date($invoice_type == 'estimate' ? (strtotime($invoice['Invoice']['date']) ? $invoice['Invoice']['date'] : $invoice['Invoice']['created']) : $invoice['Invoice']['date']); ?> </span>  </div>


                        <?php if ($invoice['Invoice']['staff_id'] != $staff_id) { ?>
                            <ul class="meta-details">
                                <li>
                                    <div class="added-by">
                                        <span class="added-by-label"><i class="fa fa-user"></i>  <?php __('By') ?>: </span>
                                        <span class="added-by-value"><strong><?php echo $staffs[$invoice['Invoice']['staff_id']] ?></strong></span>
                                    </div>
                                </li>
                            </ul>
                        <? } ?>




                    </div></td>
                <td class="entry-time">
                    <?php echo format_price($invoice['Invoice']['summary_total'], $invoice['Invoice']['currency_code']); ?>
<?php
if($invoice['Invoice']['summary_refund']!=0){
?>                    
                    <div class="status">
       
						<span class="currency refunded"><? echo __('Refunded',true) ?>: <span class="inline p-0"> <?php echo format_price($invoice['Invoice']['summary_refund'], $invoice['Invoice']['currency_code']); ?></span></span>
</div>
<?
}
?>                    
                    
<?php if($invoice['Invoice']['payment_status']==1){?>                    
                    <div class="status">       
                        <span style=" padding: 0px;" class="currency"><? echo __('Balance Due',true) ?>: <span class="inline p-0"> <?php echo format_price($invoice['Invoice']['summary_unpaid'], $invoice['Invoice']['currency_code']); ?></span></span>
</div><?}?> 
                    <?php echo $this->element($invoice_type . '-status', array('invoiceStatuses' => $invoiceStatuses, 'invoice' => $invoice)); ?>
                    <?php /** warning suppress */ if (isset($enable_invoice_status) && $enable_invoice_status && ifPluginActive(FollowupPlugin) && !empty($invoice['Invoice']['follow_up_status'])) { ?>
                        <div class="status big-label" > <span style="background-color:<?php echo $invoice_status_colors[$invoice['Invoice']['follow_up_status']]; ?>;color:<?php echo $colors_options[$invoice_status_colors[$invoice['Invoice']['follow_up_status']]]['color'] ?>" class="status-symble "><?php echo $invoice_statuses[$invoice['Invoice']['follow_up_status']]; ?></span> </div>
                    <?php }?>

                    <?php if ( $show_requisitions ) { ?>
        <div class="status">
            <?php if (!$isAdvancePayment): ?>
                <span style="color: <?php echo $delivery_status['color']?>;background-color: <?php echo $delivery_status['background-color']?>" class="status-symble "><?php echo __($delivery_status['label'] , true ); ?></span>
            <?php endif; ?>
         </div>
        <?php } ?> 

        
                </td>


                <?php if (!isset($_GET['from_reservation_order'])) { ?>
                <td class="entry-button">
                    <div class="dropdown mobile-options dropdown-dialog" data-dd="true">
                        <button class="btn btn-lg btn-default dropdown-toggle" type="button" data-toggle="dropdown">
                            <span class="fa fa-ellipsis-h"></span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-right">
                            <div class="dropdown-dialog-header" data-toggle="dropdown">
                                <span class="dropdown-dialog-header-title" data-dd-title="true">
                                    <?= __t('Actions') ?>
                                </span>
                                <span class="dropdown-dialog-header-close">
                                    <i class="mdi mdi mdi-close-thick"></i>
                                </span>
                            </div>
                            <ul class="dropdown-menu dropdown-dialog-menu">
                                <?php
                                if (is_array($actions) && !empty($actions)) {
                                    $actions2 = $actions;
                                    $more_actions = empty($actions2['more-actions']) ? array() : $actions2['more-actions'];
                                    $actions2 = array_merge($actions2, $more_actions);
                                    unset($actions2['more-actions']);
                                    foreach ($actions2 as $action) {

                                        if (is_array($action)) {
                                            if (!isset($action['php_expression']) || (eval('$more =' . $action['php_expression']) || $more))
                                                $action = $action['url'];
                                            else
                                                continue;
                                        }
                                        foreach ($row['Invoice'] as $field => $x) {
                                            if (!is_array($x)) {
                                                $action = str_ireplace("%{$field}%", $x, $action);
                                            }
                                        }
                                        if ($row['Invoice']['pos_shift_id']) {
                                            $temp = $action;
                                            $action = strtolower($action);
                                            if (strpos($action,'edit') !== false || strpos($action,'delete') !== false || strpos($action,'clone') !== false || strpos($action,'payment') !== false){
                                                $InvoiceModel = ClassRegistry::init('Invoice');
                                                if (!$InvoiceModel->isPosOpenedOrNotPos($row['Invoice']['pos_shift_id'])) {
                                                    continue;
                                                }
                                            }
                                            $action = $temp;
                                        }
                                        if (str_contains($action, 'add_payment') && $invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT){
                                            continue;
                                        }
                                        echo '<li>' . $action . '</li>';
                                    }
                                }
                                ?>
                            </ul>
                        </div>
                        <div class="dropdown-dialog-backdrop"></div>
                    </div>

                </td>
                <?php } ?>
            </tr>
        </tbody>
    </table>
</li>
