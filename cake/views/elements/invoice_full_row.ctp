<?php
// warning suppress
$iframe = $_GET["box"];
$country_code = $country_code ?? null;
$item_tag_type  = $item_tag_type ?? null;
$isAdvancePayment = $row['Invoice']['type'] == Invoice::ADVANCE_PAYMENT;
if($invoice_type=='invoice' && $row['Invoice']['type'] != 16){
    //$item_tag_type = 'Invoice';
    $view_link='view';
    // Advance Payment Invoice
    if ($isAdvancePayment){
        $view_link = 'view_advance_payment';
    }
}elseif($invoice_type=='creditnote'){
$view_link='view_creditnote';        
}elseif($invoice_type=='insurance'){
$view_link='view_insurance';
}elseif($invoice_type=='insurance_refund'){
$view_link='view_insurance_refund';
}elseif($invoice_type=='refund'){
//	$item_tag_type = 'Refund';
$view_link='view_refund';        
}elseif($row['Invoice']['type'] == 16){
    $view_link='view_debitnote'; 
    $invoice_type = 'debitnote';   
    
    if (ifPluginActive(StaffPlugin) && check_permission(DEBIT_NOTE_VIEW_ALL)) {
        // warning suppress
        $full_name = $full_name ?? null;
        $fields['Name'] = array('title' => 'Added By', 'php_expression' => '".$row["Staff"]["id"]==null?\'' . $full_name . '\':$row["Staff"]["name"]."');
    }    
    $staff_id = getAuthStaff('id');
    $target = '';
    if($iframe){
        $target ="_top";
    }

    $links[] = $html->link(__('View', true), array('action' => 'view_debitnote', '%id%'), array('class' => 'View', 'title' => __('View Invoice', true),'target' => $target));

    if (!check_permission(DEBIT_NOTE_EDIT_DELETE_ALL) && check_permission(DEBIT_NOTE_EDIT_DELETE_HIS_OWN)) {
        $links[] = array('php_expression' => '$row["Invoice"]["staff_id"]==' . $staff_id . ';', 'url' => $html->link(__('Edit', true), array('action' => 'edit_debitnote', '%id%'), array('class' => 'Edit', 'title' => __('Edit', true),'target' => $target)));
    } elseif (check_permission(DEBIT_NOTE_EDIT_DELETE_ALL)) {
        $links[] = array('url' => $html->link(__('Edit', true), array('action' => 'edit_debitnote', '%id%'), array('class' => 'Edit', 'title' => __('Edit', true),'target' => $target)));
    }

    if (!IS_PC)
        $links[] = $html->link(__('Image', true), array('action' => 'view', '%id%', 'ext' => 'jpeg'), array('class' => 'Image', 'title' => __('View invoice as Image', true)));

    $links['more-actions'][] = $html->link(__('PDF', true), array('action' => 'view_debitnote', '%id%', 'ext' => 'pdf'), array('class' => 'Pdf', 'title' => __('View invoice as PDF', true),'target' => $target));
    $links['more-actions'][] = $html->link(__('Print', true), array('action' => 'view_debitnote', '%id%', 'print' => '1'), array('class' => 'Print', 'title' => __('Print Invoice', true),'target' => $target));
    $links['more-actions'][] = $html->link(__('Email to client', true), array('action' => 'owner_send_to_client', '%id%'), array('class' => 'Send', 'title' => __('Send invoice to its client', true),'target' => $target));


    $more_url_params = '';
    if($this->params['url']['from_lease_contract_view']) {
        $more_url_params = 'redirect=/v2/owner/entity/lease_contract/' . $this->params['url']['source_id'].'/show';
    }

    if (!check_permission(DEBIT_NOTE_EDIT_DELETE_ALL) && check_permission(DEBIT_NOTE_EDIT_DELETE_HIS_OWN)) {
        $links['more-actions'][] = array('php_expression' => '$row["Invoice"]["staff_id"]==' . $staff_id . ';', 'url' => $html->link(__('Delete', true), array('action' => 'delete', '%id%', '?' => $more_url_params), array('class' => 'Delete', 'title' => __('Delete', true),'target' => $target)));
    } elseif (check_permission(DEBIT_NOTE_EDIT_DELETE_ALL)) {
        $links['more-actions'][] = array('url' => $html->link(__('Delete', true), array('action' => 'delete', '%id%', '?' => $more_url_params), array('class' => 'Delete', 'title' => __('Delete', true),'target' => $target)));
    }

    $actions = $links;
    

}else{
$view_link='view_estimate';       
//	$item_tag_type = 'Estimate';

}
$is_crm = isset($_GET["from_crm"]);

if (!empty($row))
    $invoice = $row;
if (empty($invoice_type))
    $invoice_type = 'invoice';
// warning suppress
$show_requisitions = isset($enable_requisitions) && $enable_requisitions && !empty($invoice['Invoice']['requisition_delivery_status']) && !isset($stockIds[$invoice['Invoice']['id']]);
if ( $show_requisitions ) {
    $delivery_status = Requisition::$delivery_status_list[$invoice['Invoice']['requisition_delivery_status']];
}
$owner = getAuthOwner();
$full_name = $owner['first_name'] . ' ' . $owner['last_name'];

//debug($invoice);
//debug ( $invoice_status_colors ) ;
?>
<li class="day-view-entry day-view-entry-label <?php echo $iframe ? 'border-top-0 border-bottom-0' : ''; ?>" <?php if (!empty($invoice['Invoice']['follow_up_status'])) { ?> style="border-color:<?php echo $invoice_status_colors[$invoice['Invoice']['follow_up_status']]; ?>" <? } ?>>
    <?php
    if (!empty($check_box)) {
        echo $check_box;
    }
    ?>
    <table cellspacing="0" cellpadding="0" border="0" class="<?php echo $iframe ? 'bordered margin-bottom-n' : ''; ?>">
        <tbody>
            <tr>
                <td width="45%" style="<?php echo $is_crm ? 'min-width: 220px' : ''; ?>" class="clickable" onclick="window.open('<?php echo $url = Router::url(array('controller' => 'invoices', 'action' => $view_link, $invoice['Invoice']['id'])) ?>', '<?php echo $iframe ? '_blank' : '_self'; ?>')">
                    <div  class="invoice-row entry-info">

                        <?php
                            $codeMessage = match ($row['Invoice']['type']){
                                (string)Invoice::DEBIT_NOTE => __('Debit Note', true). ' ',
                                (string)Invoice::ADVANCE_PAYMENT => __('Advance Payment', true). ' ',
                                default => null
                            };
                        ?>
                        <div class="task-notes"><a href="<?php echo $url ?>" <?= $row['Invoice']['type'] == 16 ? 'style="color: #f05050"' : null ?> > <span class="project"><?= $codeMessage ?>#<?php echo $invoice['Invoice']['no']; ?></span> - <span class="expense-date"> <?php echo format_date($invoice_type == 'estimate' ? (strtotime($invoice['Invoice']['date']) ? $invoice['Invoice']['date'] : $invoice['Invoice']['created']) : $invoice['Invoice']['date']); ?> </span></a>  </div>
                        <?php if (empty($no_client) /*and (!isset($_GET['client_id']) or $_GET['client_id']=="")*/) {
                             $invoice_client_name = $invoice['Invoice']['client_first_name'] . ' ' . $invoice['Invoice']['client_last_name'];
                             $invoice_client_name = strlen($invoice_client_name) > 1 ? $invoice_client_name : $invoice['Client']['business_name'];
                            ?>
                            <div class="project-client"><a href="<?php echo $url ?>"> <span
                                            class="project"><?= strlen($invoice['Invoice']['client_business_name'])>1? $invoice['Invoice']['client_business_name']: $invoice_client_name?></span>
                                    <?php if (!empty(trim($invoice['Invoice']['client_first_name']))
                                        || /** warning suppress */ (isset($invoice['Client']) && !empty($invoice['Client']['client_number']))) { ?>  <span
                                            class="project sub_with_num"> <?=$invoice_client_name ?> <span
                                                class="inline"> <?php $invoice_client_number = $invoice['Client']['client_number'] ?? ''; echo ' #' . $invoice_client_number; ?>   </span> </span>
                                    <?php } ?>
                                </a></div>
                        <? } ?>

                        <div class="task-notes"> 
                            <?php echo $this->element('format_address_html',array('address1'=>$invoice['Invoice']['client_address1'],'address2'=>$invoice['Invoice']['client_address2'], 'city'=>$invoice['Invoice']['client_city'],'state'=>$invoice['Invoice']['client_state'],'postal_code'=>$invoice['Invoice']['client_postal_code'],'country_code'=>$invoice['Invoice']['client_country_code'])); ?>
                            <?php if (!empty($invoice['Invoice']['client_country_code']) && $invoice['Invoice']['client_country_code'] != $country_code): ?>
                                <?php echo $invoice['Country']['country'] ?><div class="clearfix"></div>
                            <?php endif; ?>

                        </div>
                        <ul class="meta-details">
                            <li>
                                <div class="added-by">
                                    <span class="added-by-label"><i class="fa fa-user"></i> <?php __('By') ?>: </span>
                                   <?php if ($invoice['Invoice']['staff_id'] != $staff_id) { ?>
                                    <?php 
                                        $added_by_name = '';
                                        if ($invoice['Invoice']['staff_id'] != 0) {
                                            $added_by_name = $invoice['Invoice']['staff_id'] == -3 ? __t('The API') : $staffs[$invoice['Invoice']['staff_id']];
                                        } else {
                                            $added_by_name = $full_name;
                                        }
                                        ?>
                                        <?php }  else  {  
                                            $added_by_name = __t('You');
                                        } ?>
                                <span class="added-by-value"><strong><?php echo $added_by_name ?></strong></span>
                            </div>
                            </li> 
                        <?php if (($invoice['Invoice']['staff_id'] != $staff_id) || (isset($invoice['SalesPerson']) && $invoice['SalesPerson']['staff_id'] != $staff_id)) { ?>
                                <?php 
                                if (isset($invoice['SalesPerson']) && $invoice['SalesPerson']['staff_id'] !== $staff_id && $invoice['Invoice']['sales_person_id']) { ?>
                                <li>
                                    <div class="added-by">
                                        <span class="added-by-label"><i class="fa fa-user"></i>  <?php __('Sold by') ?>: </span>
                                        <?php if($invoice['Invoice']['sales_person_id'] == Invoice::MULTIPLE_SALES_PERSONS) {?>
                                            <span class="added-by-value"><strong><?php echo __('Multiple Sales Persons')?></strong></span>
                                        <?php } else {?>
                                            <span class="added-by-value"><strong><?php echo $staffs[$invoice['SalesPerson']['staff_id']] /*warning suppress */ ?? '' ?></strong></span>
                                        <?php } ?>
                                    </div>
                                </li>
                                <?php } ?>
                                <? } ?>
                            <?php if ($invoice['Invoice']['external_source'] && $invoice['Invoice']['external_source'] !=" "): ?>
                                <li>
                                    <div class="added-by">
                                        <span class="added-by-label"><i class="fa fa-gear"></i>  <?php __('Source: ') ?> </span>
                                        <span class="added-by-value" title="<?= $invoice['pos_shift']['PosDevice']['name'] ?? ''  ?>" ><strong><?php echo \Izam\Daftra\Common\Utils\InvoiceExternalSourceUtil::getSourceForPreview($invoice['Invoice']['external_source']) ?></strong></span>
                                    </div>
                                </li>
                            <?php endif; ?>
                            </ul>

                    </div>
									<div class="preventClick">
						<?php
                        // warning suppress
						echo $this->element('tags_view',array('tag_type' => $item_tag_type, 'tags' => $tags[$invoice['Invoice']['id']] ?? []));
//						die(debug($tags));
//						echo $this->element('tag_input_ajax',array('disabled' => true, 'allow_filter' => true, 'item_id' => $invoice['Invoice']['id'],'item_type' => $item_tag_type, 'values' => $tags[$invoice['Invoice']['id']])); ?>

									</div>
				</td>
                <td style="<?php echo $is_crm  || $iframe ? 'min-width: 170px' : ''; ?>">
                    <strong style=" display: inline-flex;"><?php /** warning suppress */ echo $invoice['LastAction']['title'] ?? '' ?>
                    <?php /** warning suppress */ if (isset($enable_invoice_status) && $enable_invoice_status && ifPluginActive(FollowupPlugin) && !empty($invoice['Invoice']['follow_up_status']) && !IS_MOBILE) { ?>
                        <div class="status big-label" > <span style="background-color:<?php echo $invoice_status_colors[$invoice['Invoice']['follow_up_status']]; ?>;color:<?php echo $colors_options[$invoice_status_colors[$invoice['Invoice']['follow_up_status']]]['color'] ?>" class="status-symble "><?php echo $invoice_statuses[$invoice['Invoice']['follow_up_status']]; ?></span> </div>
                    <?php }?>
                </strong>
                    <div style="text-align:right">
                        <ul class="meta-details tiny">
                            <li>
                                <div class="added-by d-flex align-items-center">
                                    <?php if ( !empty ($invoice['LastAction']['ActionLine']['created']) ){?>
                                    <span class="added-by-label"><i class="fa fa-clock-o"></i> </span>
                                    <span class="added-by-value"><strong><?php echo format_date($invoice['LastAction']['ActionLine']['created']) . ' ' . date('H:i:s', strtotime($invoice['LastAction']['ActionLine']['created'])); ?></strong></span>
                                    <?php }?>
                                </div>
                            </li>
                            <?php if (isset($invoice['LastAction']) && $invoice['LastAction']['ActionLine']['staff_id'] != -10 && $invoice['LastAction']['ActionLine']['staff_id'] != $staff_id) { ?>
                                <li>
                                    <div class="added-by">
                                        <span class="added-by-label"><i class="fa fa-user"></i>   </span>
                                        <span class="added-by-value"><strong><?php echo $invoice['LastAction']['ActionLine']['staff_name'] ?></strong></span>
                                    </div>
                                </li>
                            <?php } ?>


                        </ul>
                    </div>
                </td>

                <?php
                if (in_array($invoice_type, ['refund', 'invoice', 'creditnote']) && !$invoice['Invoice']['draft']) {
                    if ((in_array($invoice_type , ['refund', 'invoice']) &&  invoice::checkIfJordanEInvoiceEnabled()) || !invoice::checkIfJordanEInvoiceEnabled() ) {
                        echo $this->element('index_invoice_status',['invoice_status' => json_decode($invoice['EntityAppData'][0]['data'],true), "have_error" => $invoice["have_error"], 'is_submitted' => $invoice['is_submitted'], 'is_sent' => $invoice['is_sent']]);
                    }
                }
                ?>

                <td class="entry-time" style="<?php echo $is_crm ? 'min-width: 170px' : ''; ?>">
                    <?php echo format_price($invoice['Invoice']['summary_total'], $invoice['Invoice']['currency_code']); ?>
                    
<?php if($invoice['Invoice']['summary_refund']!=0){
?>                    
                    <div class="status">
                    <span class="currency refunded"><? echo __('Refunded',true) ?>: <span class="inline p-0"> <?php echo format_price($invoice['Invoice']['summary_refund'], $invoice['Invoice']['currency_code']); ?></span></span>
                    </div>
<?
}
?>
                    
<?php
if($invoice['Invoice']['payment_status']==1){
?>                    
                    <div class="status">
       
                        <span style=" padding: 0px;" class="currency text-capitalize"><? echo __('Balance Due',true) ?>: <span class="inline p-0"> <?php echo format_price($invoice['Invoice']['summary_unpaid'], $invoice['Invoice']['currency_code']); ?></span></span>
</div>
<?
}

?>

                    <?php if ($invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT && $invoice['Invoice']['unSettledAmount'] > 0.001){ ?>
                        <div class="status">
                        <span style=" padding: 0px;" class="currency text-capitalize"><? echo __('Unsettled amount',true) ?>: <span class="inline p-0"> <?php echo format_price($invoice['Invoice']['unSettledAmount'], $invoice['Invoice']['currency_code']); ?></span></span>
                        </div>
                   <?php } ?>


   <?php
   // warning suppress
   echo $this->element($invoice_type . '-status', array('invoiceStatuses' => $invoiceStatuses ?? null, 'invoice' => $invoice)); ?>
               <?php if ( $show_requisitions ) { ?>
        <div class="status">
            <?php if (!$isAdvancePayment): ?>
                <span style="color: <?php echo $delivery_status['color']?>;background-color: <?php echo $delivery_status['background-color']?>" class="status-symble "><?php echo __($delivery_status['label'] , true ); ?></span>
            <?php endif; ?>
         </div>
        <?php } ?> 
                </td>
                <?php //if (!$iframe) { ?>
                <td class="entry-button">
                    <div class="dropdown mobile-options">
                        <button class="btn btn-lg btn-default dropdown-toggle dropdown-parent" type="button" data-toggle="dropdown">

                            <span class="fa fa-ellipsis-h"></span></button>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <?php
                            if (is_array($actions) && !empty($actions)) {
                                $actions2 = $actions;
                                $more_actions = empty($actions2['more-actions']) ? array() : $actions2['more-actions'];
                                $actions2 = array_merge($actions2, $more_actions);
                                unset($actions2['more-actions']);
                                foreach ($actions2 as $action) {

                                    if (is_array($action)) {
                                        if (!isset($action['php_expression']) || (eval('$more =' . $action['php_expression']) || $more))
                                            $action = $action['url'];
                                        else
                                            continue;
                                    }
                                    foreach ($row['Invoice'] as $field => $x) {
                                        if (!is_array($x)) {
                                            $action = str_ireplace("%{$field}%", $x, $action);
                                        }
                                    }
                                    if ($row['Invoice']['pos_shift_id']) {
                                        $temp = $action;
                                        $action = strtolower($action);
                                        if (strpos($action,'edit') !== false || strpos($action,'delete') !== false || strpos($action,'clone') !== false || strpos($action,'payment') !== false){
                                            $InvoiceModel = ClassRegistry::init('Invoice');
                                            if (!$InvoiceModel->isPosOpenedOrNotPos($row['Invoice']['pos_shift_id'])) {
                                                continue;
                                            }
                                        }
                                        $action = $temp;
                                    }
                                    if (str_contains($action, 'add_payment') && $invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT){
                                        continue;
                                    }
                                    echo '<li>' . $action . '</li>';
                                }
                            }
                            ?>

                        </ul>
                    </div>

                </td>
				<?php //} ?>
               
            </tr>
        </tbody>
    </table>
</li>
