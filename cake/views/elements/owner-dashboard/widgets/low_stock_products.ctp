<style>
    #report_low_stock_products {
        min-height: 260px;
        overflow: auto;
        max-height: 300px;
    }
    #report_low_stock_products::-webkit-scrollbar {
        background-color:#fff;
        width:16px
    }
    #report_low_stock_products::-webkit-scrollbar-track {
        background-color:#fff
    }
    #report_low_stock_products::-webkit-scrollbar-track:hover {
        background-color:#f4f4f4
    }
    #report_low_stock_products::-webkit-scrollbar-thumb {
        background-color:#babac0;
        border-radius:16px;
        border:5px solid #fff
    }
    #report_low_stock_products::-webkit-scrollbar-thumb:hover {
        background-color:#a0a0a5;
        border:4px solid #f4f4f4
    }
    #report_low_stock_products::-webkit-scrollbar-button {
        display:none
    }
    #report_low_stock_products .day-view-entry .entry-time {
        display: none;
    }
    #report_low_stock_products .day-view-entry td {
        padding-top: 10px;
        padding-bottom: 10px;
    }
</style>

<div class="card s2020 shadow mb-3" id="recent_activities_widget">
    <div class="px-4 py-2 py-xl-3 border-bottom border-secondary s2020 bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h3 class="text-dark-blue s2020 pb-0 fs-16 font-weight-bold d-flex align-items-center">
                <span><?php __("Low Stock Products") ?></span>
                <span class="badge badge-danger bg-danger s2020 ml-2"><?php echo $low_stock_count; ?></span>
            </h3>
            <a href="<?php echo $html->url(array('controller' => 'products', 'action' => 'index', '?' => array('status' => [3,2]))) ?>" class="text-primary-2 p-0 s2020 border-0"><?php __('View All') ?></a>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="widget-body-wrapper" id="report_low_stock_products">
            <?php echo $list->adminResponsiveList($low_products, 'products/product_row', array('actions' => array(array('url' => $html->link(__('View', true), array('controller' => 'products', 'action' => 'view', '%id%'), array('class' => 'View', 'title' => __('View', true)))))), array('no_paging' => true)); ?>
        </div>
    </div>
</div>

