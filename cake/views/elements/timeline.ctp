<?php
$user = getAuthOwner();
// warning suppress
if (!isset($is_ajax) || !$is_ajax) {
    ?>
    <div class="activity-log-s2020-panel panel panel-default">
        <div class="panel-body ">
            <div class="row">
                <div class="col-md-4">
                    <select id="fliter-action" multiple="multiple">
                        <? foreach ($actions as $key => $action) { ?>
                            <option value="<? echo $key ?>"><? echo $action['name'] ?></option>
                        <? } ?>
                    </select>

                    <?php if (!empty($filter_staff)) { ?>
                        <select id="fliter-staff" multiple="multiple">
                            <? foreach ($filter_staff as $id => $name) { ?>
                                <option value="<? echo $id ?>"><? echo $name ?></option>
                            <? } ?>
                        </select>
                    <? } ?>


                    <div class="btn-group pos2" data-toggle="buttons">
                        <a href="#" id="Ascending" title="<?php __('Ascending') ?>" order="1"
                           class="order_view btn btn-default linehightfix" style="line-height: 1.7;"><i class="fa fa-chevron-up"></i></a>
                        <a href="#" id="Descending" title="<?php __('Descending') ?>" order="2"
                           class="order_view btn btn-default linehightfix active" style="line-height: 1.7;"><i class="fa fa-chevron-down"></i></a>
                    </div>


                </div>
                <div class="col-md-8">
                    <div class="btn-group pull-right pos3  hidden-sm" data-toggle="buttons">
                        <a id="Timeline_View" href="#" view="1" title="<?php __('Timeline View') ?>"
                           class="view_level btn btn-md btn-default active"><i class="fa fa-th-list"></i></a>
                        <a id="Table_View" href="#" view="2" title="<?php __('Table View') ?>"
                           class="view_level btn btn-md btn-default"><i class="fa fa-table"></i></a>
                    </div>


                    <div class="pull-right m-r-xs pos4  hidden-sm hidden-xs">
                        <?= $this->element('dateRangePicker', ['onChange'=>'reload_filter()','datePickerLabel' => false]); ?>
                    </div>
                    <?
                    if ($this->name == 'ActionLines') {
                        ?>
                        <div class="pull-right m-r-xs pos5  hidden-sm hidden-xs">

                            <input id="Keyword" type="text" placeholder="<?php __("Keyword") ?>"
                                   class="form-control text-small m-x" value=""/>

                        </div>
                        <?
                    }
                    ?>
                </div>
            </div>

        </div>
        <div class="clear"></div>
    </div>
<div class="activity-log-s2020-content" id="timeline_content">
    <?php } ?>


    <?
    if (!empty($data['data'])) {
        // warning suppression
        $_GET['view'] = $_GET['view'] ?? '';
        if ($_GET['view'] == "1" || empty($_GET['view'])) {
            ?>
            <div class="timeline-wrapper mobile-timeline-wrapper">
                <?php foreach ($data['data'] as $key => $groups) {

                    ?>
                    <div class="date-label">
                        <p><? echo time2str($key) ?></p>
                    </div>
                    <?php foreach ($groups as $group) { ?>
                        
                        <div class="item-wrapper extended <? echo $group['class'] ?>">
                            <div class="timeline-item">
                                <span class="extend id-<?php echo $group['action-id'] ?>"><i class="fa <? echo $group['class'] ?>"></i></span>
                                <p><? echo $group['text'] ?></p>
                                <span>
                                    <i class="fa fa-clock-o"></i> <? echo date("H:i:s", strtotime($group['date'])) ?> - <span <?= array_key_exists('ip', $group) ? 'class="tip" title="'.$group['ip'].'"' : null ?> >
                                    <?php if ($group['staff_name'] == __('The system', true)) { ?>
                                        <img class="user-avatar" src="/files/images/site-logos/<?= $user['site_logo'] ?>" width="20" height="20" />
                                    <?php } else { ?>
                                        <img class="user-avatar" src="<?= \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($group['staff_name'], null , 20, null); ?>" width="20" height="20" />
                                    <?php } ?>
                                    <? echo $group['staff_name'] ?></span><?php if ($allStaffBranches) { ?> - <i class="fa fa-building"></i> <? echo $allStaffBranches[$group['branch_id']];} ?>
                                </span>
                                <?php if(isLoggedAsAdmin() && $group['logged_as_admin']){ ?>
                                <p style="color:red">Logged Admin: <?= $group['logged_as_admin'] ?></p>
                                <?php } ?>
                            </div>
                        </div>
                    <?php } ?>
                <?php } ?>
                <div class="clearfix"></div>
            </div>
            <?
        } else {
            $fields = array(
                'basicModel' => 'ActionLine',
                'ActionLine.date' => array('class' => 'td_time'),
                'ActionLine.staff_name' => array('title' => __('By', true)),
                'ActionLine.name' => array('title' => __('Action', true)),
                'ActionLine.amount' => array('title' => __('Amount', true)),
            );
            if(isLoggedAsAdmin('Logged Admin')) {
                $fields['ActionLine.logged_as_admin'] = ['title' => "Logged as Admin"];
            }
            foreach ($data['data'] as $i => $record)
                $data['data'][$i]['ActionLine']['date'] = format_date($data['data'][$i]['ActionLine']['date']) . ' ' . date('H:i:s', strtotime($data['data'][$i]['ActionLine']['date']));

            $links[] = $html->link(__('View', true), '#View%id%', array('onclick' => 'timeline_row(%id%)', 'id' => '%id%', 'class' => 'timeline_view', 'title' => __('View', true)));

            $output = $list->adminIndexList($fields, $data['data'], $links, false, false, array('no_paging' => true));
            echo $output;
        }

        if (empty($_GET['quick'])) {
            ?>
            <ul class="pagination">
                <?php
                // warning suppress
                $_GET['page'] = $_GET['page'] ?? 1;
//                dd($data);
                if ((int)$data['pagination']['total_pages'] > 1) {
                    
                    $start = $_GET['page'] - 10 > 0 ? $_GET['page'] - 10 : 1;
                    $end = $_GET['page'] + 10 <= $data['pagination']['total_pages'] ? $_GET['page'] + 10 : $data['pagination']['total_pages'];
                    for ($i = $start; $i <= $end; $i++) { ?>
                    <li <?php echo $_GET['page'] == $i || (!$_GET['page'] && $i == 1) ? 'class="active"' : '' ?>><a
                                class="page_number" page="<? echo $i ?>" href="javascript:void()"><? echo $i ?></a></li>
                    <? }
                }
                ?>
            </ul>
        <? } ?>
        <?php
    } else {
        ?>
        <div class="Notemessage"><?php __('No activity logs match this criteria') ?></div><?php
    }


    if (!isset($is_ajax) || !$is_ajax) {
    ?>
</div>

    <script type="text/javascript">
        var allActionsTxt = "<?php __('All Actions') ?>";
        var allStaffTxt = "<?php __('All Users') ?>";
    </script>
<?php
echo $html->css(CDN_ASSETS_URL ."s2020/css/entities/activity_log/activity_log.min.css?v=".CSS_VERSION, null, []);
echo $javascript->link(array('timeline_v'.JAVASCRIPT_VERSION));


?>
<?php } ?> 

<?php if (isset($_GET['layout2022'])) { ?>
<style>
    #body_box {
        margin: 0;
        padding: 0 !important;
    }
    .panel-body {
        padding: 0 !important;
    }
    .activity-log-s2020-content {
        padding: 25px;
        background-color: #e4ebf2;
    }
</style>
<?php } ?>
