<?php
//$menus = (include APP . 'owner-menus.php');
//debug (  APP . 'owner-menus.php' ); 
$country_code = getAuthOwner('country_code');
//debug ( $menus );
$roleId = getAuthStaff('role_id');
if ($roleId) {
    $blockedURLs = settings::getValue(0, 'blocked_role_' . $roleId,null,false,false);
    if ($blockedURLs) {
        $blockedURLs = json_decode($blockedURLs);
    }
}

//show the menus item and loop on the menus array from ./<user-type>-menus.php
foreach ($menus as $key => $menu) {
    if (!$menu['quicklink'] && count($menu['submenus'][0]) == 0) {
        unset($menus[$key]);
        continue;
    }
    if (isset($menu['plugin']) && !empty($menu['plugin']) && !ifPluginActive($menu['plugin'])) {
        unset($menus[$key]);
        continue;
    }
    if (isset($menu['submenus'][0])){

        foreach ($menu['submenus'][0] as $subkey => $submenus)
        {
            if (isset($submenus['showif']) && !empty($submenus['showif']) && !check_permission(@$submenus['showif'])) {
                unset($menus[$key]['submenus'][0][$subkey]);
                continue;
            }
            if (isset($submenus['and_showif'])) {
                foreach ($submenus['and_showif'] as $permission) {
                    if (!check_permission($permission)) {
                        unset($menus[$key]['submenus'][0][$subkey]);
                        break;
                    }
                }
            }

            if (isset($submenus['check_plugins'])) {
                foreach ($submenus['check_plugins'] as $plugin) {
                    if (!ifPluginActive($plugin)) {
                        unset($menus[$key]['submenus'][0][$subkey]);
                        break;
                    }
                }
            }

            if (isset($submenus['only_countries'])) {
                if (!in_array($country_code, $submenus['only_countries'])) {
                    unset($menus[$key]['submenus'][0][$subkey]);
                }
            }
            if (isset($blockedURLs) && $blockedURLs) {
                $url = 'owner'.Router::url($submenus['url']);
                foreach ($blockedURLs as $blockedURL) {
                    if (strpos($url, $blockedURL) !== false || strpos($url, str_replace('/*','',$blockedURL)) !== false) {
                        unset($menus[$key]['submenus'][0][$subkey]);
                        break;
                    }
                }
            }
        }
        if (count($menus[$key]['submenus'][0]) == 0) {
            unset($menus[$key]);
            continue;
        }
    }
}

if (isset($menus['inventory'])) {

    unset($menus['products']);
    if(settings::getValue(InvoicesPlugin, 'sold_item_type')==settings::OPTION_SOLD_TYPE_SERVICES){
      $menus['inventory']['submenus'][0]['products-listing-inventory']['title']=__('Manage Services',true);
        
    }else if(settings::getValue(InvoicesPlugin, 'sold_item_type')==settings::OPTION_SOLD_TYPE_PRODUCTS){
      $menus['inventory']['submenus'][0]['products-listing-inventory']['title']=__('Manage Products',true);
        
    }
    if (isset($menus['invoices'])) {
        $menus['invoices']['title'] = __('Sales', true);
    }


}
if (!($staff['is_super_admin'] || $staff['staff_id'] == 0))
{
    unset($menus['sites']['submenus'][0]['account_information']) ; 
}

if (isset($menus['products'])) {
    if (settings::getValue(InvoicesPlugin, 'sold_item_type') == settings::OPTION_SOLD_TYPE_SERVICES) {
        $menus['products']['submenus'][0]['products-add']['title'] = __('Add New Service', true);
        $menus['products']['submenus'][0]['products-settings']['title'] = __('Services Categories', true);
        $menus['products']['submenus'][0]['manage-products']['title'] = __('Manage Services', true);
        $menus['products']['title'] = __('Services', true);
    } else if (settings::getValue(InvoicesPlugin, 'sold_item_type') == settings::OPTION_SOLD_TYPE_PRODUCTS) {
        $menus['products']['submenus'][0]['products-add']['title'] = __('Add Product', true);
        $menus['products']['submenus'][0]['products-settings']['title'] = __('Products Categories', true);
        $menus['products']['title'] = __('Products', true);
    }
}

if(settings::getValue(InvoicesPlugin,'disable_estimate_module'))
{
	unset($menus['invoices']['submenus'][0]['estimates']);
	unset($menus['invoices']['submenus'][0]['estimate-add-new']);
   
}

if (IS_REST)
    echo json_encode($menus);
else
    echo $this->element('menus/menu', array('menus' => $menus));
?>
