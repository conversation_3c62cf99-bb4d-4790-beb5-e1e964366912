<?php
//warning suppress
if (isset($is_ajax) && $is_ajax) {
    die();
}
?>
<!-- Modal -->
<div id="LoaderModal" class="modal fade" role="dialog">
    <!-- <div class="modal-dialog"> -->
        <!-- Modal content-->
        <!-- <div class="modal-content" style="opacity: 1.0;"> -->
            <div style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                <i class="fa fa-circle-o-notch fa-spin" style="color: #fff;font-size:50px;"></i>
            </div>
        <!-- </div> -->

    <!-- </div> -->
</div>

<?php echo $javascript->link(['jquery.scannerdetection.js']); ?>
<script>

    $( document ).ready( function() {
            // var anything_is_focused = (
            //     document.hasFocus() &&
            //         document.activeElement !== null &&
            //         document.activeElement !== document.body &&
            //         document.activeElement !== document.documentElement
            //     );
            //
            // $( document).keydown(function(){
            //     anything_is_focused = (
            //         document.hasFocus() &&
            //             document.activeElement !== null &&
            //             document.activeElement !== document.body &&
            //             document.activeElement !== document.documentElement
            //         );
            // });


            $( document ).scannerDetection({
                timeBeforeScanTest: 200, // wait for the next character for upto 200ms
                avgTimeByChar: 100, // it's not a barcode if a character takes longer than 100ms
                onComplete: function(barcode, qty){
                    $('#LoaderModal').modal('show');
                    $.get('<?= Router::url( array( 'owner'=>true, 'controller'=>'oibarcodes', 'action'=>'redirect_using_barcode' ) ); ?>/'+ barcode)
                        .done(function(data){
                            console.log("data", data);
                            if(data == "fail")
                            {
                                $('#LoaderModal').modal('hide');
                                initToast(__('The barcode is not linked to any product in the inventory', true), 'danger');
                                return false;
                            }
                            window.location = data;
                        }).fail(function(data){
                            $('#LoaderModal').modal('hide');
                            initToast(__('Invalid Barcode', true), 'danger');
                        });
                        

                }
            });

        }
    );
</script>
