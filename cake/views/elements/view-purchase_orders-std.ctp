<?php // This element used when create puchase orders or puchase refund with no layout
//print_r($purchase_orders['Supplier']);
$is_product_code=isset($po_setting['product_code']['value']) && $po_setting['product_code']['value']=="1";
$is_credit_note = $purchase_orders['PurchaseOrder']['type'] == PurchaseOrder::CREDIT_NOTE;
if($is_product_code){
$plus1 =1;
}else{
    $plus1=0;
}

ob_start();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
        <?php         if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::Purchase_Refund){
        ?>
        <title><?php printf(__('Purchase Refund %s', true), '#' . $purchase_orders['PurchaseOrder']['no']) ?></title>
        <?php         }else if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::PURCHASE_INVOICE){
        ?>
        <title><?php printf(__('Purchase Invoice %s', true), '#' . $purchase_orders['PurchaseOrder']['no']) ?></title>
        <?php         }
        else if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::PURCHASE_QUOTATION){
        ?>
        <title><?php printf(__('Purchase Quotation %s', true), '#' . $purchase_orders['PurchaseOrder']['no']) ?></title>
         <?php         } else if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::PURCHASE_ORDER){
            ?>
            <title><?php printf(__('Purchase Order %s', true), '#' . $purchase_orders['PurchaseOrder']['no']) ?></title>
            <?php         } else if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::DEBIT_NOTE){
            ?>
            <title><?php printf(__('Purchase Debit Note %s', true), '#' . $purchase_orders['PurchaseOrder']['no']) ?></title>
            <?php         } else if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::CREDIT_NOTE){
            ?>
            <title><?php printf(__('Purchase Credit Note %s', true), '#' . $purchase_orders['PurchaseOrder']['no']) ?></title>
            <?php         }
        ?>
        <?php
        $old_address=false;
        $isPdf = (!empty($this->params['url']['ext']) && $this->params['url']['ext'] == 'pdf') || !empty($pdf);
        if (!isset($site)) {
            $site = getCurrentSite();
        }

        $hide_description = true;

        $placeholders = array(
            '{%from-date%}' => $purchase_orders['PurchaseOrder']['from'],
            '{%to-date%}' => $purchase_orders['PurchaseOrder']['to'],
        );

        foreach ($purchase_orders['PurchaseOrderItem'] as $k => $item) {
            $purchase_orders['PurchaseOrderItem'][$k]['item'] = str_replace(array_keys($placeholders), array_values($placeholders), $purchase_orders['PurchaseOrderItem'][$k]['item']);
            $purchase_orders['PurchaseOrderItem'][$k]['description'] = str_replace(array_keys($placeholders), array_values($placeholders), $purchase_orders['PurchaseOrderItem'][$k]['description']);
            $tracking = Product::displayTrackingData($purchase_orders['PurchaseOrderItem'][$k]['Product']['tracking_type'], $purchase_orders['PurchaseOrderItem'][$k]['tracking_data']);
            $purchase_orders['PurchaseOrderItem'][$k]['item'] .= $tracking != ""? "\n".$tracking : "" ;

            if (!empty($purchase_orders['PurchaseOrderItem'][$k]['description'])) {
                $hide_description = false;
            }
        }

        if ($hide_description)
            $cols = 1;
        else
            $cols = 2;

        if ( $show_discount ){$cols ++ ; }

        $style = '<style type="text/css">' . file_get_contents(WWW_ROOT . 'css/common.css') . PHP_EOL . file_get_contents(WWW_ROOT . 'css/invoice-template.css');
        if (CurrentSiteLang() == 'ara') {
            $style .=PHP_EOL . file_get_contents(WWW_ROOT . 'css/invoice-template-ar.css');
        }
        $style .= '</style>';
        echo $style;
        ?>
    </head>
    <?php
    $show_shipping = false;
    App::import('Vendor', 'settings');
    $use_shipping_billing = settings::getValue(InventoryPlugin, 'shipping_billing');
    if (!empty($use_shipping_billing)) {
        debug('NOT EMP');
        $shipping_details = settings::getValue(InventoryPlugin, 'po_shipping_address');
        $billing_details = settings::getValue(InventoryPlugin, 'po_billing_address');
    }
    $show_shipping = false;
    if (!empty(str_replace(array(' ', "\r", "\n"), '', strip_tags($shipping_details)))) {
        $show_shipping = true;
    }
    if (!empty(str_replace(array(' ', "\r", "\n"), '', strip_tags($billing_details)))) {
        $show_billing = true;
    }


    //$purchase_orders_title = $purchase_orders['invoice_default_title'];
            if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::Purchase_Refund){
            $purchase_orders_title = __("Purchase Refund",true);
            }else if ($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::PURCHASE_INVOICE){
            $purchase_orders_title = __("Purchase Invoice",true);
            } else if ($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::PURCHASE_ORDER){
                $purchase_orders_title = __("Purchase Order",true);
            } else if ($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::PURCHASE_QUOTATION){
                $purchase_orders_title = __("Purchase Quotation",true);
            } else if ($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::DEBIT_NOTE){
                $purchase_orders_title = __("Purchase Debit Note",true);
            } else if ($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::CREDIT_NOTE){
                $purchase_orders_title = __("Purchase Credit Note",true);
            } else{
            $purchase_orders_title = __("Purchase Invoice",true);
            }
    $logo = false;
    $width = 200;
    $height = 0;
    $fullUrl = str_replace("&shy;","-",'https://' . getCurrentSite('subdomain'));
    if ($site['site_logo']) {
        $logo = $site['site_logo_full_path'];
        $absolute_path = str_replace('//', '/', WWW_ROOT . $logo);


        $logoPath = DS . 'files' . DS . 'images' . DS . 'site-logos' . DS . ($site['site_logo']);
        $logo = $fullUrl.$logoPath;
        list($img_width, $img_height, $type, $attr) = getimagesize($logo);
        if (!empty($img_width) && !empty($img_height)) {
            $generated_height = 200 / $img_width * $img_height;
            if ($generated_height > 120) {
                $width = 0;
                $height = 120;
            }
        }

    }
    if ($logo) {
        $logo_code = '<img src="' . $logo . '" ' . (!empty($width) ? ' width="' . $width . '" ' : '') . (($height) ? ' height="' . $height . '" ' : '') . '  />';
    }
    ?>
    <body>
        <div class="invoice-wrap">
            <div class="invoice-inner">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="text-left" valign="top" >
                            <p>
                                <?php if (!empty($purchase_orders_title) && !empty($logo_code)) { ?>
                                    <?php echo $logo_code; ?><br/><br/>
                                <?php }

                                ?>


                                <span class="bussines-name"><?php echo $site['business_name'] ?></span><br />
                                <?php if (!empty($site['bn1_label']) && !empty($site['bn1'])): ?>
                                    <strong><?php echo $site['bn1_label'] ?>:</strong> <?php echo $site['bn1'] ?><br />
                                <?php endif; ?>
                                <?php if (!empty($site['bn2_labe2']) && !empty($site['bn2'])): ?>
                                    <strong><?php echo $site['bn2_labe2'] ?>:</strong> <?php echo $site['bn2'] ?><br />
                                <?php endif; ?>
                                <?php  if($old_address) {  ?>
				<?php echo $site['address1'] ?><br />
                                <?php if (!empty($site['address2'])): ?>
                                    <?php echo $site['address2'] ?><br />
                                <?php endif; ?>
                                <?php echo!empty($site['city']) ? $site['city'] . (!empty($site['state']) ? ', ' : '') : '' ?><?php echo!empty($site['state']) ? $site['state'] : '' ?> <?php echo $site['postal_code'] ?><br />
                                <?php } else { ?>
                                 <?php  echo $this->element('format_address_html',$site);  ?>
                                <?php } ?>
                            </p>
                        </td>
                        <td  class="text-right" valign="top">


                            <?php if (empty($purchase_orders_title) && !empty($logo_code)) { ?>
                                <?php echo $logo_code; ?>
                            <?php } else if (!empty($purchase_orders_title)) { ?>
                                <h1><?php echo $purchase_orders_title ?></h1>
                                <?php
                            } else {

                            }
                            ?>
                            <?php if ($show_shipping) { ?>
                                <br/><br/><br/>
                                <?php echo $this->element('purchase_order-custom_fields', array('site' => $site, 'purchase_orders' => $purchase_orders)); ?>
                            <?php } ?>
                        </td>

                    </tr>
                </table>
                <?php
                $w1 = 50;
                $w2 = 50;

                if ($show_shipping && $show_billing)
                    $w1 = $w2 = 35;
                else if ($show_shipping || $show_billing)
                    $w1 = $w2 = 50;
                ?>
                <div class="invoice-address">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td id="second_left" width="<?php echo $w1 ?>%" class="text-left" valign="top">
                                <p>
                                    <strong><?php __('Supplier') ?>:</strong><br/>
                                    <?php echo $purchase_orders['PurchaseOrder']['supplier_business_name'] ?><br />
 <?if(!empty($purchase_orders['Supplier']['bn1_label']) && !empty($purchase_orders['Supplier']['bn1'])){
                                        $bn_br=true;
                                        echo ' <strong>'.$purchase_orders['Supplier']['bn1_label'].' </strong>:'.$purchase_orders['Supplier']['bn1'].'<br>';
                                        }?>
<?if(!empty($purchase_orders['Supplier']['bn2_label']) && !empty($purchase_orders['Supplier']['bn2'])){
    $bn_br=true;
echo ' <strong>'.$purchase_orders['Supplier']['bn2_label'].'</strong>:'.$purchase_orders['Supplier']['bn2'];
}
if($bn_br){
?>
                                        <br />

                                    <?php
}

                                    if (!empty($purchase_orders['PurchaseOrder']['supplier_first_name']) || !empty($purchase_orders['PurchaseOrder']['supplier_last_name'])) {
                                        echo (!empty($purchase_orders['PurchaseOrder']['supplier_first_name']) ? $purchase_orders['PurchaseOrder']['supplier_first_name'] . " " : '') . $purchase_orders['PurchaseOrder']['supplier_last_name']
                                        ?><br />
                                        <?php
                                    }  ?>

				<?php  if($old_address) {  ?>
                                    <?php  if (!empty($purchase_orders['PurchaseOrder']['supplier_address1'])) {
                                        echo $purchase_orders['PurchaseOrder']['supplier_address1']
                                        ?><br />
                                    <?php } if (!empty($purchase_orders['PurchaseOrder']['supplier_address2'])): ?>
                                        <?php echo $purchase_orders['PurchaseOrder']['supplier_address2'] ?><br />
                                    <?php endif; ?>
                                    <?php if (!empty($purchase_orders['PurchaseOrder']['supplier_city']) || !empty($purchase_orders['PurchaseOrder']['supplier_state']) || !empty($purchase_orders['PurchaseOrder']['supplier_postal_code'])) { ?>
                                        <?php echo (!empty($purchase_orders['PurchaseOrder']['supplier_city']) ? $purchase_orders['PurchaseOrder']['supplier_city'] . ', ' : ''), $purchase_orders['PurchaseOrder']['supplier_state'], ' ', $purchase_orders['PurchaseOrder']['supplier_postal_code'] ?><br />
                                    <?php  } ?>
                                    <?php } else { ?>
                                        <?php  echo $this->element('format_address_html',array('address1'=>$purchase_orders['PurchaseOrder']['supplier_address1'],'address2'=>$purchase_orders['PurchaseOrder']['supplier_address2'],'city'=>$purchase_orders['PurchaseOrder']['supplier_city'],'state'=>$purchase_orders['PurchaseOrder']['supplier_state'],'postal_code'=>$purchase_orders['PurchaseOrder']['supplier_postal_code'],'country_code'=>$purchase_orders['PurchaseOrder']['supplier_country_code']));  ?>
                                    <?php } ?>

                                    <?php if (!empty($purchase_orders['PurchaseOrder']['supplier_country_code']) && $purchase_orders['PurchaseOrder']['supplier_country_code'] != $site['country_code']): ?>
                                        <?php echo $clientCountry ?><br />
                                    <?php endif; ?>
                                </p>



                            </td>
                            <td id="second_right" width="<?php echo $w2 ?>%" class="text-right" valign="top" >

                                <?php if ($show_shipping || $show_billing) { ?>
                                    <table  class="float-right"  border="0"  cellspacing="0" cellpadding="0"><tr><td class="text-left">
                                                <div id="shipping_info">
                                                    <p>

                                                        <?php
                                                        if ($show_billing)
                                                            echo $billing_details;
                                                        else
                                                            echo $shipping_details;
                                                        ?>
                                                    </p>
                                                </div>
                                            </td></tr></table>
                                <?php } else { ?>
                                    <?php echo $this->element('purchase_order-custom_fields', array('site' => $site, 'purchase_orders' => $purchase_orders)); ?>
                                <?php } ?>
                            </td>
                            <?php if ($show_shipping && $show_billing) { ?>
                                <td id="" width="<?php echo $w2 ?>%" class="text-right" valign="top" >
                                    <table class="right"  border="0" cellspacing="0" cellpadding="0"><tr><td class="text-left">
                                                <div id="shipping_info">
                                                    <p>
                                                        <?php echo $shipping_details; ?>
                                                    </p>
                                                </div>
                                            </td></tr></table>

                                </td>
                            <?php } ?>
                        </tr>
                    </table>
                </div>
                <?php if ($isPdf): ?><p>&nbsp;</p><?php endif; ?>
                <table cellspacing="0" cellpadding="2" border="0" width="100%" id="listing_table" class="invoice-listing-table total-table" style="">
                    <?php                     if ($layout == '-1') {
                        if($is_product_code==1){
                        ?>
                        <tr>
                            <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Code") ?></th>
                            <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Item") ?></th>
                            <?php if (!$hide_description) { ?>
                                <th width="210" bgcolor="#E5E5E5"><?php __("Details") ?></th>
                            <?php } ?>
                            <th width="80" bgcolor="#E5E5E5"><?php __("Hour Rate") ?></th>
                            <th width="40" bgcolor="#E5E5E5"><?php __("Hours") ?></th>
                            <?php if ( $show_discount){?>
                                <th width="60" bgcolor="#E5E5E5"><?php __("Discount") ?></th>
                            <?php }?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Subtotal") ?></th>
                        </tr>
                        <?php                         }else{
                        ?>
                      <tr>
                            <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Item") ?></th>
                            <?php if (!$hide_description) { ?>
                                <th width="210" bgcolor="#E5E5E5"><?php __("Details") ?></th>
                            <?php } ?>
                            <th width="80" bgcolor="#E5E5E5"><?php __("Hour Rate") ?></th>
                            <th width="40" bgcolor="#E5E5E5"><?php __("Hours") ?></th>
                              <?php if ( $show_discount){?>
                                  <th width="60" bgcolor="#E5E5E5"><?php __("Discount") ?></th>
                              <?php }?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Subtotal") ?></th>
                        </tr>
                    <?php                         }
                    } else {
                         if($is_product_code==1){
                        ?>
                        <tr>
                            <?php if(!$is_credit_note){ ?>
                                <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Code") ?></th>
                                <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Item") ?></th>
                            <?php } ?>
                            <?php if (!$hide_description || $is_credit_note) { ?>
                                <th <?= $is_credit_note ? 'width="" style="border-left:1px solid #555;"' : 'width="210"' ?> bgcolor="#E5E5E5"><?php __("Details") ?></th>
                            <?php } ?>
                            <th width="80" bgcolor="#E5E5E5"><?php __("Unit Price") ?></th>
                            <?php if ( $show_discount){?>
                                <th width="60" bgcolor="#E5E5E5"><?php __("Discount") ?></th>
                            <?php }?>
                            <?php if(!$is_credit_note){ ?>
                                <th width="40" bgcolor="#E5E5E5"><?php __("Qty") ?></th>
                            <?php } ?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Subtotal") ?></th>
                        </tr>
                        <?php                          }else{
                        ?>
                     <tr>
                        <?php if(!$is_credit_note){ ?>
                            <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Item") ?></th>
                        <?php } ?>
                            <?php if (!$hide_description || $is_credit_note) { ?>
                                <th <?= $is_credit_note ? 'width="" style="border-left:1px solid #555;"' : 'width="210"' ?> bgcolor="#E5E5E5"><?php __("Details") ?></th>
                            <?php } ?>
                            <th width="80" bgcolor="#E5E5E5"><?php __("Unit Price") ?></th>
                             <?php if ( $show_discount){?>
                                 <th width="60" bgcolor="#E5E5E5"><?php __("Discount") ?></th>
                             <?php }?>
                            <?php if(!$is_credit_note){ ?>
                                <th width="40" bgcolor="#E5E5E5"><?php __("Qty") ?></th>
                            <?php } ?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Subtotal") ?></th>
                        </tr>
                    <?php                          }
                    }
                    ?>
                    <?php
                    $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
                    foreach ($purchase_orders['PurchaseOrderItem'] as $item2): ?>
                        <tr>
                            <?php                              if($is_product_code==1){
                            ?>
                            <?php if(!$is_credit_note){ ?>
                                <td width=""><?php echo nl2br(h($item2['item'])) ?></td>
                            <?php } ?>
                            <td width=""><?php echo nl2br(h($item2['description'])) ?></td>
                              <?php if (!$hide_description || $is_credit_note) { ?>
                            <td width=""><?php echo nl2br(h($item2['col_3'])) ?></td>
                            <?php                               }
                             }else{
                            ?>
                            <?php if(!$is_credit_note){ ?>
                                <td width=""><?php echo nl2br(h($item2['item'])) ?></td>
                            <?php } ?>
                             <?php if (!$hide_description || $is_credit_note) { ?>
                            <td width=""><?php echo nl2br(h($item2['description'])) ?></td>
                           <?php
                             }
                           }
                           ?>
                            <td width="80"><?php
                            $put_units = $enable_multi_units && !empty($item2['unit_factor']) && !empty($item2['unit_name']) && ((!empty($item2['Product']))?count($item2['Product']['unit_factors'] ?? [] )>=1 : true) && $item2['unit_factor'] > 0;
                            if ($put_units) {
                                $item2['unit_factor'] = (float)$item2['unit_factor'];
                                $item2['unit_price'] = (float)$item2['unit_price'];
                                $item2['quantity'] = (float) $item2['quantity'];
                                $item2['quantity_written'] = ($item2['unit_factor'] ? ($item2['quantity'] / $item2['unit_factor']) : 0 ). ' <span class="unit_factor_name" >' . $item2['unit_small_name'] . '</span>';
                                $unit_price = $item2['unit_price'] * $item2['unit_factor'];
                            }else {
                                $unit_price = $item2['unit_price'] ;
                                $item2['quantity_written'] = ($item2['quantity'] );
                            }
                            echo format_price_simple($unit_price, $purchase_orders['PurchaseOrder']['currency_code'],false) ?></td>
                            <?php if ( $show_discount){?> <td width="80"><?php $item_subtotal = $item2['item_subtotal'];
                                echo $item2['discount_string'] ?></td>
                            <?php } ?>
                            <?php if(!$is_credit_note){ ?>
                            <td width="40"><?php
                                echo $item2['quantity_written'];//format_number($item2['quantity'], $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                            <?php } ?>
                            <td width="60"><?php echo format_price_simple(((float)$item2['quantity'] * (float)$item2['unit_price'] )- (float)$item2['discount_value'], $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                        </tr>

                    <?php endforeach; ?>
                    <?php if (1) { ?>
                        <?php if ($purchase_orders['PurchaseOrder']['summary_subtotal'] != $purchase_orders['PurchaseOrder']['summary_total']): ?>
                            <tr>
                                <td  style="border:none;" bgcolor="#FFF" colspan="<?= $cols+$plus1-($is_credit_note ? 1 : 0) ?>"></td>
                                <td colspan="<?= $is_credit_note ? 1 : 2 ?>" style="border-left:none;border-right:none;"  class="text-left"><strong><?php __("Subtotal") ?>:</strong></td>
                                <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($purchase_orders['PurchaseOrder']['summary_subtotal'], $purchase_orders['PurchaseOrder']['currency_code'])  ?></td>
                            </tr>
                        <?php endif; ?>
            <?php if(!empty($purchase_orders['PurchaseOrder']['summary_total_discount'])) $purchase_orders['PurchaseOrder']['summary_discount']=$purchase_orders['PurchaseOrder']['summary_total_discount'];$purchase_orders['PurchaseOrder']['discount_amount']=1; ?>
            <?php if (!empty($purchase_orders['PurchaseOrder']['adjustment_value'])): ?>
                        <tr>
                            <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                            <td style="border-left:none;border-right:none;"  colspan="<?= $is_credit_note ? 1 : 2 ?>" ><strong><?php __($purchase_orders['PurchaseOrder']['adjustment_label']) ?>:</strong></td>
                            <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($purchase_orders['PurchaseOrder']['adjustment_value'], $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                        </tr>
            <?php endif; ?>
            <?php if (!empty($purchase_orders['PurchaseOrder']['summary_discount'])): ?>
                                        <tr>
                                            <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols+$plus1-($is_credit_note ? 1 : 0)?>"></td>
                                            <td style="border-left:none;border-right:none;"  colspan="<?= $is_credit_note ? 1 : 2 ?>" ><strong><?php empty($purchase_orders['PurchaseOrder']['discount_amount'])? printf( __("Discount (%s)", true), round($purchase_orders['PurchaseOrder']['discount'], 2) . '%'):__("Discount") ?>:</strong></td>
                                            <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($purchase_orders['PurchaseOrder']['summary_discount'] * -1, $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                                        </tr>
                                    <?php endif; ?>



                        <?php
                        if (!empty($purchase_orders['PurchaseOrder']['summary_deposit']) && $purchase_orders['PurchaseOrder']['summary_deposit'] != $purchase_orders['PurchaseOrder']['summary_unpaid'] && abs($purchase_orders['PurchaseOrder']['summary_deposit'] - $purchase_orders['PurchaseOrder']['summary_unpaid']) >= 0.01):
                            ?>
                            <tr>
                                <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols+$plus1 -($is_credit_note ? 1 : 0)?>"></td>
                                <td style="border-left:none;border-right:none;"  colspan="<?= $is_credit_note ? 1 : 2 ?>" ><strong><?php printf(__("Next Payment", true)) ?>:</strong></td>
                                <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($purchase_orders['PurchaseOrder']['summary_deposit'], $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php
                        if (!empty($purchase_orders['PurchaseOrderTax'])):
                            foreach ($purchase_orders['PurchaseOrderTax'] as $tax):
                                ?>
                                <tr>
                                    <td  style="border:none" bgcolor="#FFF" colspan="<?= $cols+$plus1 -($is_credit_note ? 1 : 0)?>"></td>
                                    <td style="border-left:none;border-right:none;"  colspan="<?= $is_credit_note ? 1 : 2 ?>"><strong><?php printf('%s (%s%%)', $tax['name'], round($tax['value'], 2)) ?>:</strong></td>
                                    <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($tax['invoice_value'], $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                                </tr>
                                <?php
                            endforeach;
                        endif;
                        ?>
                               <?php if (!empty($purchase_orders['PurchaseOrder']['shipping_amount'])): ?>
                                        <tr>
                                            <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols+$plus1-($is_credit_note ? 1 : 0) ?>"></td>
                                            <td style="border-left:none;border-right:none;"  colspan="<?= $is_credit_note ? 1 : 2 ?>" ><strong><?php echo __("Shipping", true) ?>:</strong></td>
                                            <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($purchase_orders['PurchaseOrder']['shipping_amount'], $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                                        </tr>
                                    <?php endif; ?>
                        <tr class="total-row">
                            <td style="border:none"  bgcolor="#FFF" colspan="<?= $cols+$plus1-($is_credit_note ? 1 : 0) ?>"></td>
                            <td style="border-left:none;border-right:none;"  colspan="<?= $is_credit_note ? 1 : 2 ?>"><strong><?php __("Total") ?>:</strong></td>
                            <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($purchase_orders['PurchaseOrder']['summary_total'], $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                        </tr>
    <?php
    if ($purchase_orders['PurchaseOrder']['type'] == 0) {
        if (!empty($purchase_orders['PurchaseOrder']['summary_refund'])):
            ?>
                                <tr>
                                    <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols+$plus1 -($is_credit_note ? 1 : 0)?>"></td>
                                    <td style="border-left:none;border-right:none;"  colspan="<?= $is_credit_note ? 1 : 2 ?>" ><strong><?php echo __("Refunded", true) ?>:</strong></td>
                                    <td style="border-left:none;border-right:none;" class="text-left">-<?php echo format_price($purchase_orders['PurchaseOrder']['summary_refund'], $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                                </tr>
            <?php

        endif;
    }
    ?>
       <?php
                        if ($purchase_orders['PurchaseOrder']['type'] == 0) {
                            if ($site['id'] > 0 || (!empty($purchase_orders['PurchaseOrder']['summary_paid']) && $purchase_orders['PurchaseOrder']['summary_paid'] != $purchase_orders['PurchaseOrder']['summary_total'])){


?>

                                <tr>
                                    <td  style="border:none" bgcolor="#FFF" colspan="<?= $cols+$plus1-($is_credit_note ? 1 : 0) ?>"></td>
                                    <td style="border-left:none;border-right:none;"  colspan="<?= $is_credit_note ? 1 : 2 ?>" ><strong><?php __("Paid") ?>:</strong></td>
                                    <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($purchase_orders['PurchaseOrder']['summary_paid'] * -1, $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                                </tr>
                                <tr>
                                    <td  style="border:none" bgcolor="#FFF" colspan="<?= $cols+$plus1 -($is_credit_note ? 1 : 0)?>"></td>
                                    <td  style="border-left:none;border-right:none;" colspan="<?= $is_credit_note ? 1 : 2 ?>"><strong><?php __("Balance Due") ?>:</strong></td>
                                    <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($purchase_orders['PurchaseOrder']['summary_unpaid'], $purchase_orders['PurchaseOrder']['currency_code']) ?></td>
                                </tr>

                        <?} }?>
                    <?php } ?>
                </table>

                <br />
                <br />
                <div class="notes-block">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td>
                                <?php if (!empty($purchase_orders['PurchaseOrder']['notes'])) { ?>
                                    <p style="font-style:italic"><?php echo nl2br(h($purchase_orders['PurchaseOrder']['notes'])) ?></p>
                                <?php } ?>
                                <?php if (!empty($purchase_orders['PurchaseOrder']['html_notes'])) { ?>
                                    <p ><?php echo strip_tags(str_replace(array('onclick', 'onmouseup', 'onmousewheel', 'onscroll', 'onmousedown', 'onmousemove', 'onmouseout', 'onmouseover'), '', $purchase_orders['PurchaseOrder']['html_notes']), '<div><span><ul><li><ol><br><p><b><i><strong><font><small><big><h1><h2><h3><h4><h5><h6><h7><a>') ?></p>
                                <?php } ?>
                            </td>
                        </tr>
                    </table>
                </div>
                <br />
                <br />
                <br />
                <?php
                if (!$isPdf && isset($branded)) {
                    echo $this->element('branded');
                }
                ?>
            </div>
        </div>
    </body>
</html>
<?php

$out = ob_get_clean();
$out = formatSaudiRiyalSymbol($out, $purchase_orders['PurchaseOrder']['currency_code']);
echo $out;