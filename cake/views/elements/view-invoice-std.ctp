<?php
use Izam\Daftra\Common\Utils\PluginUtil;
$bn_br = false;
if ($invoice['Invoice']['type'] == Invoice::Invoice) {
    $head_title = __("Invoice %s", true);
    $label_to = __('Bill to', true);
} elseif ($invoice['Invoice']['type'] == Invoice::Credit_Note) {
    $head_title = __("Credit Note %s", true);
    $label_to = __('To', true);
} elseif ($invoice['Invoice']['type'] == Invoice::Refund_Receipt) {
    $head_title = __("Refund Receipt %s", true);
    $label_to = __('To', true);
}elseif ($invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT) {
    $head_title = __("Advance Payment Invoice %s", true);
    $label_to = __('To', true);
} else {
    $head_title = __("Invoice #", true);
    $label_to = __('Bill To', true);
}
$extra_details=json_decode($invoice['Invoice']['extra_details'],true);
$is_debit_note = $invoice['Invoice']['type'] == Invoice::DEBIT_NOTE;
?>
<?php
ob_start();
$invoice_title = empty($invoice['invoice_default_title']) ? $invoice_default_title : $invoice['invoice_default_title'];
 if(ifPluginActive(EINVOICE_SA_PLUGIN)&&$invoice_title=='فاتورة')
 {
	$invoice_title ='<span style="zoom: 0.7;">فاتورة ضريبية مبسطة</span>';
 }
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
        <?php  if (!$multi) { ?><title><?php printf($head_title, '#' . $invoice['Invoice']['no']) ?></title><?php } else {
            ?><title><?php echo __('select invoices') ?></title><?php } ?>
        <?php
        $old_address = false;
        $isPdf = (!empty($this->params['url']['ext']) && $this->params['url']['ext'] == 'pdf') || !empty($pdf);
        if (!isset($site)) {
            $site = getCurrentSite();
        }

        $hide_description = true;
        //debug($invoice['Invoice']['from']);
        $placeholders = array(
            '{%from-date%}' => $invoice['Invoice']['from'],
            '{%to-date%}' => $invoice['Invoice']['to'],
        );

        $hideDescriptionTemp = [];
        foreach ($invoice['InvoiceItem'] as $k => $item) {
            $invoice['InvoiceItem'][$k]['item'] = str_replace(array_keys($placeholders), array_values($placeholders), $invoice['InvoiceItem'][$k]['item']);
            $invoice['InvoiceItem'][$k]['description'] = str_replace(array_keys($placeholders), array_values($placeholders), $invoice['InvoiceItem'][$k]['description']);
            $tracking = Product::displayTrackingData($invoice['InvoiceItem'][$k]['Product']['tracking_type'], $invoice['InvoiceItem'][$k]['tracking_data']);
            $invoice['InvoiceItem'][$k]['item'] .= $tracking != ""? "\n".$tracking : "" ;
            if (!empty($invoice['InvoiceItem'][$k]['description'])) {
                $hideDescriptionTemp[] = false;
            }
        }

        $hide_description = in_array(false, $hideDescriptionTemp)?false:true;

        if ($hide_description) {
            $cols = 1;
        } else {
            $cols = 2;
        }
        if ( $show_discount ){$cols ++ ; }

        $style = '<style type="text/css">' . file_get_contents(WWW_ROOT . 'css/common.css');
        $style .=PHP_EOL . file_get_contents(WWW_ROOT . 'css/invoice-template.css');
        if (CurrentSiteLang() == 'ara') {
            $style .=PHP_EOL . file_get_contents(WWW_ROOT . 'css/invoice-template-ar.css');
        } else {
            
        }
        $style .= '</style>';
        echo $style;
        ?>
    </head>
    <?php
    $show_shipping = false;
    if ($invoice['Invoice']['shipping_options'] == 2) {
        $show_shipping = true;
        $shipping_name = $invoice['Invoice']['client_business_name'];
        $shipping_address = $invoice['Invoice']['client_address1'] . (empty($invoice['Invoice']['client_address2']) ? '' : '<br/>' . $invoice['Invoice']['client_address2']);
        $shipping_city = $invoice['Invoice']['client_city'];
        $shipping_state = $invoice['Invoice']['client_state'];
        $shipping_postal_code = $invoice['Invoice']['client_postal_code'];
        $shipping_country_code = $invoice['Invoice']['client_country_code'];
    } else if (
            (empty($invoice['Invoice']['shipping_options']) && (!empty($invoice['Invoice']['client_active_secondary_address']))) || $invoice['Invoice']['shipping_options'] == 3) {
        $show_shipping = true;
        $shipping_name = $invoice['Invoice']['client_secondary_name'];
        $shipping_address = $invoice['Invoice']['client_secondary_address1'] . (empty($invoice['Invoice']['client_secondary_address2']) ? '' : '<br/>' . $invoice['Invoice']['client_secondary_address2']);
        $shipping_city = $invoice['Invoice']['client_secondary_city'];
        $shipping_state = $invoice['Invoice']['client_secondary_state'];
        $shipping_postal_code = $invoice['Invoice']['client_secondary_postal_code'];
        $shipping_country_code = $invoice['Invoice']['client_secondary_country_code'];

        if ($shipping_name . $shipping_address . $shipping_city . $shipping_country_code == '')
            $show_shipping = false;
    }



    $logo = false;
    $width = 200;
    $height = 0;
    $fullUrl = str_replace("&shy;","-",'https://' . getCurrentSite('subdomain'));
    if ($site['site_logo']) {
        $logo = $site['site_logo_full_path'];
        $absolute_path = str_replace('//', '/', WWW_ROOT . $logo);

	
		$logoPath = DS . 'files' . DS . 'images' . DS . 'site-logos' . DS . ($site['site_logo']);
        $logo = $fullUrl.$logoPath;
        list($img_width, $img_height, $type, $attr) = getimagesize($logo);
        if (!empty($img_width) && !empty($img_height)) {
            $generated_height = 200 / $img_width * $img_height;
            if ($generated_height > 120) {
                $width = 0;
                $height = 120;
            }
        }

//        if (false && isset($pdf) && !empty($logo)) {
//            $imageScript = WWW_ROOT . DS . 'image.php';
//            $tmplogo = `php $imageScript $logo`;
//            $size = getimagesize($tmplogo);
//
//
//            $logo = $tmplogo;
//        } else {
//            $logo .= '';
//        }
    }
    if ($logo) {
        $logo_code = '<img src="' . $logo . '" ' . (!empty($width) ? ' width="' . $width . '" ' : '') . (($height) ? ' height="' . $height . '" ' : '') . '  />';
    }
    ?>
    <body>
        <div class="invoice-wrap">
            <div class="invoice-inner">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="text-left" valign="top" >
                            <p>
<?php if (!empty($invoice_title) && !empty($logo_code)) { ?>
    <?php echo $logo_code; ?><br/><br/>
<?php } ?>


                                <span class="bussines-name"><?php echo $site['business_name'] ?></span><br />
                                <?php if (!empty($site['bn1_label']) && !empty($site['bn1'])): ?>
                                    <strong><?php echo $site['bn1_label'] ?>:</strong> <?php echo $site['bn1'] ?><br />
<?php endif; ?>
                                <?php if (!empty($site['bn2_label']) && !empty($site['bn2'])): ?>
                                    <strong><?php echo $site['bn2_label'] ?>:</strong> <?php echo $site['bn2'] ?><br />
                                <?php endif; ?>
                                <?php if ($old_address) { ?>
                                    <?php echo $site['address1'] ?><br />
                                    <?php if (!empty($site['address2'])): ?>
                                        <?php echo $site['address2'] ?><br />
                                    <?php endif; ?>
                                    <?php echo!empty($site['city']) ? $site['city'] . (!empty($site['state']) ? ', ' : '') : '' ?><?php echo!empty($site['state']) ? $site['state'] : '' ?> <?php echo $site['postal_code'] ?><br />
                                <?php } else { ?>
                                    <?php echo $this->element('format_address_html', $site); ?>
                                <?php } ?>


                            </p>
                        </td>
                        <td  class="text-right" valign="top">


<?php if (empty($invoice_title) && !empty($logo_code)) { ?>
    <?php echo $logo_code; ?>
<?php } else if (!empty($invoice_title)) {
    ?>
                                <h1><?php echo $invoice_title ?></h1>
                                <?php if(ifPluginActive(PluginUtil::JORDAN_EINVOICE_PLUGIN) ) {
                                    $imgPath = Invoice::get_jordan_invoice_qr_data($invoice);
                                    if (!empty($imgPath)){
                                        echo '<img style="width:130px;height:130px" src="data:image/png;base64,' .$imgPath .' "alt="QR Code" />';
                                    }
                                 } ?>
                                <?php if(ifPluginActive(EINVOICE_SA_PLUGIN)) {
                                    $data=Invoice::get_kas_invoice_qr_data($invoice);
                                    ?>
                                    <img src="<?php echo $fullUrl ?>/qr/<?php if(empty($invoice['Invoice']['id'])) echo 'dummy.jpg'?>?d64=<?php echo $data; ?>" alt=""/><br/><br/>
                                <?php }?>

                                <?php
                            }
                            ?>
                            <?php if ($show_shipping) { ?>
                                <br/><br/><br/>
                                <?php echo $this->element('invoice-std/custom_fields', array('site' => $site, 'invoice' => $invoice)); ?>
                            <?php } ?>
                        </td>

                    </tr>
                </table>

                <div class="invoice-address">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td id="second_left" width="65%" class="text-left" valign="top">
                                <p>
                                    <strong><?php echo $label_to ?>:</strong><br/>
<?php echo $invoice['Invoice']['client_business_name'];

?>
                                    <br />
<?php if (!empty($invoice['Client']['bn1_label']) && !empty($invoice['Client']['bn1'])) {
    $bn_br = true;
    echo '<strong>'.$invoice['Client']['bn1_label'] . '</strong>: ' . $invoice['Client']['bn1'];
}
?>
                                    <?php
                                    if (!empty($invoice['Client']['bn2_label']) && !empty($invoice['Client']['bn2'])) {
                                        $bn_br = true;
                                        echo ' <strong>' . $invoice['Client']['bn2_label'] . '</strong>: ' . $invoice['Client']['bn2'];
                                    }
                                    if ($bn_br) {
                                        ?>
                                        <br /><br />
                                        <?php
                                    }
                                    if (!empty($invoice['Invoice']['client_first_name']) || !empty($invoice['Invoice']['client_last_name'])) {
                                        echo (!empty($invoice['Invoice']['client_first_name']) ? $invoice['Invoice']['client_first_name'] . " " : '') . $invoice['Invoice']['client_last_name']
                                        ?><br />
                                    <?php } ?>
                                    <?php if ($old_address) {
                                        
                                        if (!empty($invoice['Invoice']['client_address1'])) {
                                            echo $invoice['Invoice']['client_address1']
                                            ?><br />
                                        <?php } if (!empty($invoice['Invoice']['client_address2'])): ?>
                                            <?php echo $invoice['Invoice']['client_address2'] ?><br />
                                        <?php endif; ?>
                                        <?php if (!empty($invoice['Invoice']['client_city']) || !empty($invoice['Invoice']['client_state']) || !empty($invoice['Invoice']['client_postal_code'])) { ?>
                                            <?php echo (!empty($invoice['Invoice']['client_city']) ? $invoice['Invoice']['client_city'] . ', ' : ''), $invoice['Invoice']['client_state'], ' ', $invoice['Invoice']['client_postal_code'] ?><br />
                                        <?php } ?>
                                    <?php } else { 
                                        
                                        ?>
                                        <?php echo $this->element('format_address_html', array('address1' => $invoice['Invoice']['client_address1'], 'address2' => $invoice['Invoice']['client_address2'], 'city' => $invoice['Invoice']['client_city'], 'state' => $invoice['Invoice']['client_state'], 'postal_code' => $invoice['Invoice']['client_postal_code'], 'country_code' => $invoice['Invoice']['client_country_code'])); ?>
                                    <?php } ?>


                                    <?php if (!empty($invoice['Invoice']['client_country_code']) && $invoice['Invoice']['client_country_code'] != $site['country_code']): ?>
                                    <?php echo $clientCountry ?><br />
                                    <?php endif; ?>
                                </p>

                            </td>
                            <td id="second_right" width="50%" class="text-right" valign="top" > 

<?php if ($show_shipping) { ?>
                                    <table border="0" cellspacing="0" class="float-right" cellpadding="0"><tr><td class="text-left">
                                                <div id="shipping_info">
                                                    <p>
                                                        <strong><?php __('Ship To') ?>:</strong><br/>
    <?php if (!empty($shipping_name)) { ?>
        <?php echo $shipping_name ?><br />
                                                        <?php } ?>

                                                        <?php if ($old_address) { ?>
                                                            <?php if (!empty($shipping_address)) { ?>
                                                                <?php echo $shipping_address ?></br>
                                                            <?php } ?>
                                                            <?php if (!empty($shipping_city) || !empty($shipping_state) || !empty($shipping_postal_code)) { ?>
                                                                <?php echo (!empty($shipping_city) ? $shipping_city . ', ' : ''), $shipping_state, ' ', $shipping_postal_code ?><br />
                                                            <?php } ?>
                                                        <?php } else { ?>
                                                            <?php echo $this->element('format_address_html', array('address1' => $shipping_address, 'city' => $shipping_city, 'state' => $shipping_state, 'postal_code' => $shipping_postal_code, 'country_code' => $shipping_country_code)); ?>
                                                        <?php } ?>


                                                        <?php if (!empty($shipping_country_code) && ($shipping_country_code != $site['country_code'] || $shipping_country_code != $invoice['Invoice']['client_country_code'])) {
                                                            ?>
                                                            <?php echo $shippingCountry ?><br />
                                                        <?php } ?>  
                                                    </p>
                                                </div>
                                            </td></tr></table>
<?php } else { ?> 
    <?php echo $this->element('invoice-std/custom_fields', array('site' => $site, 'invoice' => $invoice)); ?>
                                <?php } ?>
                            </td>
                        </tr>
                    </table>
                </div>
<?php if ($isPdf): ?><p>&nbsp;</p><?php endif; ?>
                <table cellspacing="0" cellpadding="2" border="0" width="100%" id="listing_table" class="invoice-listing-table total-table" style="">
                <?php                 if ($layout == '-1') {
                    ?>
                        <tr>
                            <?php if(!$is_debit_note){ ?>
                                <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Item") ?></th>
                            <?php } ?>
                            <?php if (!$hide_description || $is_debit_note) { ?>
                                <th <?= $is_debit_note ? 'width="" style="border-left:1px solid #555;"' : 'width="210"' ?> bgcolor="#E5E5E5"><?php __("Description") ?></th>
                            <?php } ?>
                            <th width="80" bgcolor="#E5E5E5"><?php __("Hour Rate") ?></th>
                            <th width="40" bgcolor="#E5E5E5"><?php __("Hours") ?></th>
                            <?php if ( $show_discount){?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Discount") ?></th>
                            <?php }?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Subtotal") ?></th>
                            <?php if ($invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT) { ?>
                                <th width="60" bgcolor="#E5E5E5"><?php __('Unsettled amount') ?></th>
                            <?php } ?>
                        </tr>
    <?php } else {
    ?>
                        <tr>
                            <?php if(!$is_debit_note){ ?>
                                <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Item") ?></th>
                            <?php } ?>
                            <?php if (!$hide_description || $is_debit_note) { ?>
                                <th <?= $is_debit_note ? 'width="" style="border-left:1px solid #555;"' : 'width="210"' ?> bgcolor="#E5E5E5"><?php __("Description") ?></th>
                            <?php } ?>
                            <th width="80" bgcolor="#E5E5E5"><?php __("Unit Price") ?></th>
                            <?php if(!$is_debit_note){ ?>
                                <th width="40" bgcolor="#E5E5E5"><?php __("Qty") ?></th>
                            <?php } ?>
                            <?php if ( $show_discount){?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Discount") ?></th>
                            <?php }?>
                            <?php if ( ifPluginActive(INSURANCE_PLUGIN) && $invoice['Invoice']['summary_copayment']) {?>
                                <th width="80" bgcolor="#e5e5e5" class="editable-area col-10" id="label_copayment"><?php __('Copyment') ?></th>
                            <?php } ?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Subtotal") ?></th>
                        </tr>
    <?php }
?>
                    <?php 
                    $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
                    foreach ($invoice['InvoiceItem'] as $item2): ?>
                        <tr>
                            <?php if(!$is_debit_note){ ?>
                                <td width=""><?php echo nl2br(h($item2['item'])) ?></td>
                            <?php } ?>
                            <?php if (!$hide_description || $is_debit_note) { ?>
                                <td width=""><?php echo nl2br(h($item2['description'])) ?></td>
                            <?php } ?>
                            <td width="80"><?php 
                            if ($enable_multi_units && !empty($item2['unit_factor']) && !empty($item2['unit_name'])) {
                                $item2['quantity_written'] = ((float)$item2['quantity']/(float)$item2['unit_factor']).' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
                                $unit_price = (float)$item2['unit_price']*(float)$item2['unit_factor'];
                            }else {
                                $item2['quantity_written'] = format_number($item2['quantity'] );
                                $unit_price = $item2['unit_price'];
                            }
                            echo format_price_simple($unit_price, $invoice['Invoice']['currency_code']) ?></td>
                            <?php if(!$is_debit_note){ ?>
                                <td width="40"><?php 
                                echo $item2['quantity_written']//format_number($item2['quantity'], $invoice['Invoice']['currency_code']) ?></td>
                            <?}?>
                            <?php if ( $show_discount){?> <td width="80"><?php $item_subtotal = $item2['item_subtotal'];
                                echo $item2['discount_string'] ?></td>
                            <?php } ?>
                            <?php if ( ifPluginActive(INSURANCE_PLUGIN) && $invoice['Invoice']['summary_copayment']) {?>
                            <td width="80"><?php echo format_price_simple($item2['copayment'], $invoice['Invoice']['currency_code']) ?></td>
                            <?php } ?>
                            <td width="80"><?php echo format_price_simple($item2['item_subtotal'] ?: $item2['subtotal'], $invoice['Invoice']['currency_code']) ?></td>
                        </tr>

<?php endforeach; ?>
<?php if (ifPluginActive(INSURANCE_PLUGIN) && $invoice['Invoice']['summary_copayment']) { $cols ++ ; } ?>
<?php if ($is_debit_note) { $cols-- ; } ?>
<?php if (1) { ?>
    <?php
        $showSubtotal = !empty($invoice['Invoice']['adjustment_value']) || !empty($invoice['Invoice']['summary_discount'] || $invoice['Invoice']['summary_subtotal'] != $invoice['Invoice']['summary_total']);
    ?>
                        <?php if ($showSubtotal): ?>
                            <tr>
                                <td  style="border:none;" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                <td colspan="<?= $is_debit_note ? 1 : 2 ?>" style="border-left:none;border-right:none;"  class="text-left"><strong><?php __("Subtotal") ?>:</strong></td>
                                <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['summary_subtotal'] + (!empty($invoice['Invoice']['adjustment_value']) ? $invoice['Invoice']['adjustment_value']  * -1 : 0), $invoice['Invoice']['currency_code']) ?></td>
                            </tr>
    <?php endif; ?>

    <?php if (isset($cod_invoice_item) && ! empty($cod_invoice_item)) : ?>
    <tr>
        <td  style="border:none;" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
        <td colspan="<?= $is_debit_note ? 1 : 2 ?>" style="border-left:none;border-right:none;"  class="text-left"><strong><?php echo $label_cod_fees; ?>:</strong></td>
        <td style="border-left:none;border-right:none;" class="text-left"><?php echo $value_cod_fees; ?></td>
    </tr>
    <?php endif; ?>

    <?php if (ifPluginActive(INSURANCE_PLUGIN) && $invoice['Invoice']['summary_copayment']) { ?>
        <!-- Copayment Subtotal -->
        <tr>
            <td  style="border:none;" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
            <td colspan="<?= $is_debit_note ? 1 : 2 ?>" style="border-left:none;border-right:none;"  class="text-left"><strong><?php __("Copayment Total") ?>:</strong></td>
            <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['summary_copayment'], $invoice['Invoice']['currency_code']) ?></td>
        </tr>
    <?php } ?>
    <?php if(!empty($invoice['Invoice']['summary_total_discount'])) $invoice['Invoice']['summary_discount']=$invoice['Invoice']['summary_total_discount'];$invoice['Invoice']['discount_amount']=1; ?>
    <?php if (!empty($invoice['Invoice']['adjustment_value'])): ?>
                            <tr>
                                <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                <td style="border-left:none;border-right:none;"  colspan="<?= $is_debit_note ? 1 : 2 ?>" ><strong><?php __($invoice['Invoice']['adjustment_label']) ?>:</strong></td>
                                <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['adjustment_value'], $invoice['Invoice']['currency_code']) ?></td>
                            </tr>
    <?php endif; ?>
    <?php  if (!empty($extra_details['invoice_accounts'])&&is_array($extra_details['invoice_accounts'])) {
        foreach($extra_details['invoice_accounts'] as $account_id=>$invoice_account) {
            if(!empty($invoice_account['value'])&&abs($invoice_account['value'])>0) {
        ?>
        <tr class="invoice_account" id="invoice_account_<?php echo $account_id ?>">
            <td   style="border:none"  bgcolor="#FFF" colspan="<?= $cols ?>"></td>
            <td style="border-left:none;border-right:none;"  colspan="<?= $is_debit_note ? 1 : 2 ?>" ><strong><?php echo $invoice_account['label'] ?>:</strong></td>
            <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice_account['value'], $invoice['Invoice']['currency_code']) ?></td>
        </tr>
    <?php } } } ?>

    <?php if (!empty($invoice['Invoice']['summary_discount'])): ?>
                            <tr>
                                <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                <td style="border-left:none;border-right:none;"  colspan="<?= $is_debit_note ? 1 : 2 ?>" ><strong><?php empty($invoice['Invoice']['discount_amount']) ? printf(__("Discount (%s)", true), round($invoice['Invoice']['discount'], 2) . '%') : __("Discount") ?>:</strong></td>
                                <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['summary_discount'] * -1, $invoice['Invoice']['currency_code']) ?></td>
                            </tr>
    <?php endif; ?>

                        <?php
                        if (!empty($invoice['InvoiceTax'])):
                            foreach ($invoice['InvoiceTax'] as $tax):
                                ?>
                                <tr>
                                    <td  style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                    <td style="border-left:none;border-right:none;"  colspan="<?= $is_debit_note ? 1 : 2 ?>"><strong><?php printf('%s (%s%%)', $tax['name'], round($tax['value'], 2)) ?>:</strong></td>
                                    <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($tax['invoice_value'], $invoice['Invoice']['currency_code']) ?></td>
                                </tr> 
            <?php
        endforeach;
    endif;
    ?>


                        <?php if (!empty($invoice['Invoice']['shipping_amount'])): ?>
                            <tr>
                                <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                <td style="border-left:none;border-right:none;"  colspan="<?= $is_debit_note ? 1 : 2 ?>" ><strong><?php echo __("Shipping", true) ?>:</strong></td>
                                <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price((float)$invoice['Invoice']['shipping_amount'] - GetObjectOrLoadModel('Invoice')->getInclusiveTaxAmount((float)$invoice['Invoice']['shipping_amount'], $invoice['InvoiceTax'][$invoice['Invoice']['shipping_tax_id']]), $invoice['Invoice']['currency_code']) ?></td>
                            </tr>
    <?php endif; ?>
                        <tr class="total-row">
                            <td style="border:none"  bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                            <td style="border-left:none;border-right:none;"  colspan="<?= $is_debit_note ? 1 : 2 ?>"><strong><?php __("Total") ?>:</strong></td>
                            <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['summary_total'], $invoice['Invoice']['currency_code']) ?></td>
                        </tr>
    <?php
    if ($invoice['Invoice']['type'] == 0) {
        if (!empty($invoice['Invoice']['summary_refund'])):
            ?>
                                <tr>
                                    <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                    <td style="border-left:none;border-right:none;"  colspan="<?= $is_debit_note ? 1 : 2 ?>" ><strong><?php echo __("Refunded", true) ?>:</strong></td>
                                    <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['summary_refund'], $invoice['Invoice']['currency_code']) ?></td>
                                </tr>
            <?php
           
        endif;
    }
    ?>
                        <?php
                        if ($invoice['Invoice']['type'] == 0) {
                            if (!empty($invoice['Invoice']['summary_deposit']) && $invoice['Invoice']['summary_deposit'] != $invoice['Invoice']['summary_unpaid'] && abs($invoice['Invoice']['summary_deposit'] - $invoice['Invoice']['summary_unpaid']) >= 0.01) {
                                ?>
                                <tr>
                                    <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                    <td style="border-left:none;border-right:none;"  colspan="<?= $is_debit_note ? 1 : 2 ?>" ><strong><?php printf(__("Next Payment", true)) ?>:</strong></td> 
                                    <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['summary_deposit'], $invoice['Invoice']['currency_code']) ?></td>
                                </tr>
        <?php }
    } ?>


                        <?php if ($invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT) { ?>
                            <tr>
                                <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                <td style="border-left:none;border-right:none;"  colspan="2"><strong><?php echo __("Unsettled amount") ?>:</strong></td>
                                <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['unsettledAmount'], $invoice['Invoice']['currency_code']) ?></td>
                            </tr>
                        <?php }
                        if ($invoice['Invoice']['type'] == 0) {
                            if ($site['id'] > 0 || (!empty($invoice['Invoice']['summary_paid']) && $invoice['Invoice']['summary_paid'] != $invoice['Invoice']['summary_total'])): ?>
                                
                                <tr>
                                    <td  style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                    <td style="border-left:none;border-right:none;"  colspan="<?= $is_debit_note ? 1 : 2 ?>" ><strong><?php __("Paid") ?>:</strong></td>
                                    <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['summary_paid'] * -1, $invoice['Invoice']['currency_code']) ?></td>
                                </tr>
                                <tr>
                                    <td  style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                    <td  style="border-left:none;border-right:none;" colspan="<?= $is_debit_note ? 1 : 2 ?>"><strong><?php __("Balance Due") ?>:</strong></td>
                                    <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['summary_unpaid'], $invoice['Invoice']['currency_code']) ?></td>
                                </tr>
                            <?php endif; ?>
                        <?php }
                    } ?>
                </table>
                <?php if (!empty($invoice['Invoice']['notes']) || !empty($invoice['Invoice']['html_notes'])) { ?>
                    <br />
                    <br />
                    <div class="notes-block">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td>
                                    <?php if (!empty($invoice['Invoice']['notes'])) { ?>
                                        <p style="font-style:italic"><?php echo nl2br(h($invoice['Invoice']['notes'])) ?></p>
                                    <?php } ?>
                                    <?php if (!empty($invoice['Invoice']['html_notes'])) { ?>
                                        <p ><?php echo strip_tags(str_replace(array('onclick', 'onmouseup', 'onmousewheel', 'onscroll', 'onmousedown', 'onmousemove', 'onmouseout', 'onmouseover'), '', $invoice['Invoice']['html_notes']), '<div><span><ul><li><ol><br><p><b><i><strong><font><small><big><h1><h2><h3><h4><h5><h6><h7><a><style>') ?></p>
                                    <?php } ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <br />
                    <br />
                    <br />
                <?php } ?>
                <?php
                if ($site['plan_id']==1) {
                    echo $this->element('branded',['isPdf'=>$isPdf]);
                }
                ?>
            </div>
        </div>
    </body>
</html>

<?php
$payment_row=null;
$payment_count=0;
if (is_countable($invoice['InvoicePayment'])) {
    $payment_count=count($invoice['InvoicePayment']);
    $payment_row=$invoice['InvoicePayment'][$payment_count-1];
}

$all_place_holder=PlaceHolder::invoice_get_all_place_holders($invoice['Invoice']);
$all_place_holder +=PlaceHolder::invoice_custom_field_place_holder($invoice);
$all_place_holder +=PlaceHolder::invoice_payment_place_holder($payment_row,true);

$out = ob_get_clean();
//    $out = str_replace(array_keys($all_place_holder), array_values($all_place_holder), $out);
$out = PlaceHolder::replace($out, array_keys($all_place_holder), array_values($all_place_holder));
$out = formatSaudiRiyalSymbol($out, $invoice['Invoice']['currency_code']);
echo $out;
