<?php
$auto_payment=settings::getValue(InvoicesPlugin, 'automatic_pay_invoices');
$invoice_types=Invoice::getInvoiceTypeList();
$view_links=Invoice::getInvoiceViewActionList();
if (!empty($row)) {
	$payment = $row;
	//print_pre($payment);
}
$PaymentOnly=$payment['InvoicePayment'];
?>
<li class="day-view-entry">
    <?php
    if (!empty($check_box)) {
        echo $check_box;
    }
    ?>
    <table data-table-id="<?php echo $payment['InvoicePayment']['id']; ?>" cellspacing="0" cellpadding="0" border="0">
        <tbody>
            <tr>
                
                <td class="invoice_view_clickable clickable" <?php if(empty($payment['InvoicePayment']['attachment_full_path'])){?>  onclick="document.location.href = '<?php echo Router::url(array('controller' => 'invoice_payments', 'action' => 'view', $payment['InvoicePayment']['id'])) ?>' " <?php } ?>>
                    <div  class="invoice-row entry-info">
                        <?
                        if(empty($payment['InvoicePayment']['client_id'])){


                            $name=$invoice_types[$payment['Invoice']['type']];
                            $view_link=$view_links[$payment['Invoice']['type']];
                            ?>
                        <div onclick="document.location.href = '<?php echo Router::url(array('controller' => 'invoice_payments', 'action' => 'view', $payment['InvoicePayment']['id'])) ?>' "  class="project-client">  <span class="project">#<?php echo $payment['InvoicePayment']['code'] ?? $payment['InvoicePayment']['id']; ?></span> - <?php echo $payment['Invoice']['client_business_name']; ?> <?php if (!empty($payment['Invoice']['client_first_name'])) { ?>  <span class="project sub_with_num"> <?php echo $payment['Invoice']['client_first_name'] . ' ' . $payment['Invoice']['client_last_name']; ?> </span> <?php } ?> </div>
                                                <div class="task-notes"> <span class="project"><a target="_blank" href="<?php echo Router::url(array('controller' => 'invoices', 'action' => $view_link, $payment['Invoice']['id'])) ?>"><?php echo $name; ?> #<?php echo $payment['Invoice']['no']; ?></a></span> - <span class="expense-date"> <?php echo format_date(strtotime($payment['InvoicePayment']['date']) ? $payment['InvoicePayment']['date'] : $payment['InvoicePayment']['created']); ?> </span>  </div>
                        <?}else{?>
                        <div onclick="document.location.href = '<?php echo Router::url(array('controller' => 'invoice_payments', 'action' => 'view', $payment['InvoicePayment']['id'])) ?>' " class="project-client">  <span class="project">#<?php echo $payment['InvoicePayment']['code'] ?? $payment['InvoicePayment']['id']; ?></span> - <?php echo $payment['Client']['business_name']; ?> <?php if (!empty($payment['Client']['first_name'])) { ?>  <span class="project sub_with_num"> <?php echo $payment['Client']['first_name'] . ' ' . $payment['Client']['last_name']; ?> </span> <?php } ?> </div>
                        <div onclick="document.location.href = '<?php echo Router::url(array('controller' => 'invoice_payments', 'action' => 'view', $payment['InvoicePayment']['id'])) ?>' " class="task-notes"> <span class="project"><?php __('Credit') ?></span> - <span class="expense-date"> <?php echo format_date(strtotime($payment['InvoicePayment']['date']) ? $payment['InvoicePayment']['date'] : $payment['InvoicePayment']['created']); ?> </span> </div>
                        <?}?>


                        <?php
                        if(!empty($payment['InvoicePayment']['attachment_full_path'])){ 
                        ?>
                        <div class="project-client">
                        <a target="_blank"  href="<? echo $payment['InvoicePayment']['attachment_full_path'] ?>" >    <span class="added-by-label"><i class="fa fa-download"></i>  </span> <? echo $payment['InvoicePayment']['attachment'] ?> (<?php echo number_format(filesize($payment['InvoicePayment']['attachment_system_full_path'])/1024/1024,2) ?>kb)</a>
                        </div>
                        <?php
                        }elseif(!is_null($payment['Attachments']) && count($payment['Attachments'])){
                        ?>
                            <div class="project-client">
                            <a target="_blank"  href="<? echo "/v2/owner/entity/files/preview/" . $payment['Attachments'][0]['id']; ?>" >     <span class="added-by-label"><i class="fa fa-download"></i>  </span> <? echo $payment['Attachments'][0]['name']?> (<?php echo number_format($payment['Attachments'][0]['file_size']/1024,2) ?>kb)</a>
                            </div>
                        <?php } ?>    
                        <?php if (!empty($payment['InvoicePayment']['pos_shift_id'])){ ?>
                            <div class="task-notes"> <span class="project"><a target="_blank" href="<?php echo Router::url(array('controller' => 'pos_shifts', 'action' => 'view', $payment['InvoicePayment']['pos_shift_id'])) ?>"><?php __('POS Shift') ?> #<?php echo $payment['InvoicePayment']['pos_shift_id']; ?></a></span></div>
                        <?php } ?>
                        <ul class="meta-details">
                            <li>
                                <div class="added-by">
                                    <span class="added-by-label"><i class="fa fa-user"></i>  </span>
                                    <?php // warning suppress

                                    /* staff added payment */
                                    if ($payment['InvoicePayment']['added_by'] == 1) {
                                        if (isset($invoicePaymentActions[$payment['InvoicePayment']['id']])) {
                                            $staffId = $invoicePaymentActions[$payment['InvoicePayment']['id']];
                                        } else {
                                            /* payment is created while invoice creation, so no action line */
                                            $staffId = $payment['InvoicePayment']['staff_id'] ?? $payment['Invoice']['staff_id'];
                                        }
                                        if (getAuthStaff() && getAuthStaff('id') == $staffId) {
                                            $addedBy = __t('You');
                                        } else {
                                            $addedBy = $staffs[$staffId] ?? null;
                                        }
                                    } else {
                                        /* client added payment */
                                        $addedBy = __t('Client');
                                    }

                                    ?>
                                    <span class="added-by-value">
                                        <?php echo $addedBy  ?>
                                    </span>
                                </div>
                            </li>
                            <li>
                                <div class="added-by">
                                    <span class="added-by-label"><i class="fa fa-credit-card"></i>  </span>
									<span class="added-by-value"><?php /** warning suppress */ echo __($payment_methods[$payment['InvoicePayment']["payment_method"]] ?? '',true) ?> <?php if($PaymentOnly['payment_method']=='client_credit' && $auto_payment){ ?> <span class="tooltip" title="paid-by-client-credit"></span><?php } ?></span>
								
                                </div>
                            </li>
                        </ul>
                    </div></td>
                <td class="entry-time <?if($payment['InvoicePayment']['amount']<0){ echo 'refunded'; }?>">
                    <?
                        if(empty($payment['InvoicePayment']['client_id'])){
                        ?>
                    <?php echo format_price($payment['InvoicePayment']['amount'], $payment['Invoice']['currency_code']); ?>
                    <?
                        }else{
                    ?>
                    <?php echo format_price($payment['InvoicePayment']['amount'], $payment['InvoicePayment']['currency_code']); ?>
                    <?}?>
                    <br>
                    <div class="status">

                        <?php echo $this->element('payment-status', array('payment' => $payment, 'statuses' => $statuses)); ?>
                    </div>
					<?php
					if($payment['InvoicePayment']["payment_method"]=="client_credit" && $auto_payment){
					?>
                    <div class="status">

                        <span class="status-symble rate-grey"><?  __('Auto') ?></span>
                    </div>
					<?}?>
                </td>



                <td class="entry-button">
                    <div class="dropdown mobile-options">
                        <button class="btn btn-lg btn-default dropdown-toggle " type="button" data-toggle="dropdown">

                            <span class="fa fa-ellipsis-h"></span></button>
                        <ul data-menu-id="<?php echo $payment['InvoicePayment']['id']; ?>"  class="dropdown-menu dropdown-menu-right payment_menu">
                            <?php
                            if (is_array($actions) && !empty($actions)) {
                                $actions2 = $actions;
                                $more_actions = empty($actions2['more-actions']) ? array() : $actions2['more-actions'];
                                $actions2 = array_merge($actions2, $more_actions);
                                unset($actions2['more-actions']);
                                foreach ($actions2 as $key=>$action) {
						if(($key=='delete' || $key=='edit') && ($payment['InvoicePayment']["payment_method"]=="client_credit" && $auto_payment)){
							continue;
						}
                        if ( $staff  && !check_permission(INVOICE_PAYMENTS_EDIT_REMOVE_ALL) && $key=='edit' &&  $payment['InvoicePayment']['staff_id']!=$staff['id']) {
                            continue;
                        }
                                    if (is_array($action)) {
                                        if (!isset($action['php_expression']) || (eval('$more =' . $action['php_expression']) || $more))
                                            $action = $action['url'];
                                        else
                                            continue;
                                    }
                                    foreach ($row['InvoicePayment'] as $field => $x) {
                                        if (!is_array($x)) {
                                            $action = str_ireplace("%{$field}%", $x, $action);
                                        }
                                    }
									
                                    echo '<li>' . $action . '</li>';
                                }
                            }
                            ?>




                        </ul>
                    </div>

                </td> 
            </tr>
        </tbody>
    </table>
</li>