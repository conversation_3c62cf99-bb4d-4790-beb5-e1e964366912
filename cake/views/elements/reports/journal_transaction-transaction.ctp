<?php
$colsNo = 5;
if(!function_exists('my_format')) {
    function my_format($amount, $currency, $showCurrency = false)
    {
        $amount = round($amount, 8); // to avoid float precision issues in php  like float(163708.38499999978)
        if(_request()->ext === 'csv') {
            return $amount;
        } else {
            return \App\Helpers\CurrencyHelper::formatPrice($amount, $currency, $showCurrency);
        }
    }
}

if(!function_exists('my_format_date')) {
    function my_format_date($date)
    {
        return appFormatDateWithCache($date);
    }
}
$subtotalText = __('Subtotal', true);
$removedText = __('Removed',true);
$balanceBeforeText = __('Balance Before', true);
?>
<style>
    @media print {
        .print-sm {
            width: 50px;
        }
    }
</style>

<div class="report">
    <?php
    $dateFormats = getDateFormats('std');
    $dateFormat = $dateFormats[getAuthOwner('date_format')];
    $totals = array();
    $show_staff = $params['group_by'] != 'staff' && check_permission(Invoices_View_All_Invoices) && ifPluginActive(StaffPlugin);
    $show_client = $params['group_by'] != 'account';
    $show_date = $params['group_by'] != 'daily';
    $show_cost_center = !isset($params['cost_center_id']) || empty($params['cost_center_id']) ? 0 : 2;
    $colspan = 9 + $show_staff + $show_client + $show_date + $show_cost_center;
    $credit = $debit = $before_debit = $before_credit = $total_after_debit = $total_after_credit = 0;
    if(!empty($reportData[$currency]))
    {


        ?>

        <div class="subreport">
            <script>
                //                $(document).ready(function() {
                //
                //
                //
                //
                //
                //                    $("#remove_chart").toggle(function() {
                //                        $(this).removeClass("btn-danger");
                //                        $("#remove_chart i").removeClass("fa-times");
                //                        $("#remove_chart i").attr("title", "Show Charts");
                //                        $(".chart-wrap").slideUp();
                //                        $("#charts-wrap").addClass("hidden-div");
                //                    }, function() {
                //                        $(this).addClass("btn-danger");
                //                        $("#remove_chart i").addClass("fa-times")
                //                        $("#remove_chart i").attr("title", "Remove Charts");
                //                        $(".chart-wrap").slideDown();
                //                        $("#charts-wrap").removeClass("hidden-div");
                //                    }
                //                    );
                //
                //                });
                //

            </script>




            <br/>

            <table style="empty-cells: show" cellspacing="0" cellpadding="4" width="100%" class="report reports_currency no-collapse fixed-table-head" id="report-table">
                <thead>

                <tr class="report-results-head">
                    <th  rowspan="2"  class="first-column no-sort"><?php __("Number") ?></th>
                    <th  rowspan="2"  class="first-column no-sort"><?php __("Journal ID") ?></th>
                    <th  rowspan="2"  class="first-column no-sort"><?php __("Transaction ID") ?></th>
                    <?php if ($show_date) { ?>
                        <th rowspan="2"   class="no-sort"><?php __("Date") ?></th>
                    <? } ?>
                    <?php if ($show_client) { ?>
                        <th  rowspan="2" class="no-sort"><?php __("Account") ?></th>
                    <? } ?>
                    <?php if ($show_staff) { ?>
                        <th rowspan="2"   class="no-sort"><?php __("Staff") ?></th>
                    <? } ?>
                    <th class="print-sm" rowspan="2"> <?php __('Description')  ?> </th>

                    <?php if($enableTags){ $colsNo = 4;?>
                        <th class="print-sm" rowspan="2"> <?php __('Tags')  ?> </th>
                    <?php } ?>

                    <?php if($show_cost_center){?>
                        <th colspan="2"><?php __('Cost Center Transactions') ?></th>
                    <?php } ?>
                    <?php foreach ($display_custom_fields as $display_custom_field) { ?>
                        <th class="print-sm" rowspan="2"><?php echo $display_custom_field ?></th>
                    <?php } ?>

                    <th colspan="2"> <?php __('Transaction')  ?> </th>

                    <th colspan="2"> <?php __('Balance')  ?> </th>
                </tr>
                <tr>
                    <?php if($show_cost_center){?>
                        <td class="no-sort"><?php __("Debit") ?> (<?php echo $currency ?>)</td>
                        <td class="no-sort no-sort"><?php __("Credit") ?> (<?php echo $currency ?>)</td>
                    <?} ?>
                    <td   class="no-sort"><?php __("Debit") ?> (<?php echo $currency ?>)</td>
                    <td   class="no-sort no-sort"><?php __("Credit") ?> (<?php echo $currency ?>)</td>
                    <td   class="no-sort"><?php __("Debit") ?> (<?php echo $currency ?>)</td>

                    <td   class="no-sort no-sort"><?php __("Credit") ?> (<?php echo $currency ?>)</td>
                </tr>
                </thead>
                <tbody>
                <?php

                foreach ($reportData[$currency] as $group => $subData) {
                    if (empty($currency)) {
                        $currency= getCurrentSite('currency_code');
                    }
                    $counter = 0;
                    $first_transaction = array_pop(array_reverse($subData));
                    if(
                            $_GET['display_all_accounts'] == 'non_zero_balance' &&
                            empty($first_transaction['before_debit']) &&
                            empty($first_transaction['before_credit']) &&
                            count($subData) === 1
                    ) {
                        continue;
                    }
                    $groups[] = $group;
                    $after_credit = 0;
                    $after_debit = 0;
                    $sub_credit = 0;
                    $sub_debit = 0;
                    $sub_cost_center_credit = 0;
                    $sub_cost_center_debit = 0;
                    $sub_before_credit =0;
                    $sub_before_debit = 0;
                    $group_title = $group;
                    if ($params['group_by'] == 'staff')
                        $group_title = $staffs[$group];
                    if ($params['group_by'] == 'account')
                        $group_title = __at($accounts_list[$group]) . ' ' . end($subData)['code'];
                    if ($params['group_by'] == 'weekly' || $params['group_by'] == 'daily')
                        $group_title = my_format_date(($group));
                    if (empty($group_title))
                        $group_title = '[' . $removedText . ']';


                    ?>
                    <?php if($params['group_by'] != 'all'){ ?>
                        <tr class="empty-row">
                            <td class="empty-row" colspan="<?php echo $colspan ?>"> &nbsp; </td>
                        </tr>
                        <tr class="sub-head">
                            <td class="sub-head" colspan="<?php echo $colspan ?>" ><?php echo $group_title ?></td>
                        </tr>
                        <tr class="">
                            <td colspan="<?php echo $colspan-3+($enableTags ? 1 : 0) ?>" ><?= $balanceBeforeText ?></td>
                            <td class="format-me" data-currency-code="<?= $currency ?>" ><?php echo $first_transaction['before_debit'] ? my_format($first_transaction['before_debit'], $currency) : my_format(0, $currency) ?></td>
                            <td class="format-me" data-currency-code="<?= $currency ?>"  ><?php echo $first_transaction['before_credit'] ? my_format($first_transaction['before_credit'], $currency) : my_format(0, $currency) ?></td>
                        </tr>
                    <?php } ?>
                    <?php
                    $code = up($currency);
                    if(!$number_formats = Cache::read('number_formats'))
                    {
                        clearstatcache('currencies.php');
                        $currencies = (include 'config/currencies.php'); //fix
                        Cache::write('number_formats', $number_formats);
                        Cache::write('currencies', $currencies);
                    } else{
                        $currencies = Cache::read('currencies');
                    }
                    // Incase something went wrong just use the 2 decimal rounding
                    if (!isset($number_formats[$code])) { //fix
                        $number_formats[$code] = array(2, '.', ',');
                    }
                    foreach ($subData as $transaction) {
                        $counter++;
                        $sub_credit+=$transaction['credit'];
                        $sub_debit+=$transaction['debit'];
                        $sub_before_credit += $transaction['before_credit'] ?? 0;
                        $sub_before_debit+= $transaction['before_debit'] ?? 0;
                        if($show_cost_center && !empty($transaction['id'])){
                           $sub_cost_center_credit +=  $transaction['CCT_credit'];
                           $sub_cost_center_debit +=  $transaction['CCT_debit'];
                        }
                        ?>
                        <?php
                        //calculate credit and debit after

                        if($after_credit == 0 && $after_debit == 0){
                            //it's the first iteration
                            $totalTransactionsCredit = $transaction['credit']+$transaction['before_credit'];
                            $totalTransactionsDebit = $transaction['debit']+$transaction['before_debit'];
                            if($totalTransactionsCredit > $totalTransactionsDebit){
                                $after_credit = $totalTransactionsCredit - $totalTransactionsDebit ;
                                $after_debit = 0 ;
                            }
                            else if($totalTransactionsCredit  == $totalTransactionsDebit)
                            {
                                $after_credit = 0;
                                $after_debit = 0 ;

                            }else{
                                $after_credit = 0 ;
                                $after_debit = $totalTransactionsDebit - $totalTransactionsCredit ;
                            }

                        }else{
                            $after_credit_result = $after_credit + $transaction['credit'];
                            $after_debit_result = $after_debit + $transaction['debit'];
                            if($after_credit_result > $after_debit_result){
                                $after_credit = $after_credit_result - $after_debit_result;
                                $after_debit = 0 ;
                            }else if($after_credit_result == $after_debit_result){

                                $after_debit = $after_credit = 0;
                            }else{
                                $after_debit = $after_debit_result - $after_credit_result;
                                $after_credit = 0 ;
                            }
                        }
                        if($transaction['credit']==0 && $transaction['debit']==0 ){
                            continue;
                        }
                        ?>
                        <tr class="indent-td">
                            <td><?php echo $counter ?></td>
                            <td ><strong><a style="color: #247dbd" href="<?= Router::url(['controller' => 'journals', 'action' => 'view', $transaction['journal_id']]); ?>"><?php echo $transaction['number']; ?></a></strong></td>
                            <td><?php echo $transaction['id'] ?></td>
                            <?php if ($show_date) {

                                ?>
                                <td><?php echo my_format_date($transaction['date']); ?></td>
                            <? } ?>
                            <?php if ($show_client) { ?>
                                <td><?php echo __at($transaction['name']) ?></td>
                            <? } ?>
                            <?php if ($show_staff) { ?>
                                <td><?php echo $staffs[$transaction['staff_id']] ?></td>
                            <? } ?>
                            <td> <?= !empty($transaction['alter_description']) ? $transaction['alter_description']  : $transaction['description']; ?> </td>
                            <?php foreach ($display_custom_fields as $key => $display_custom_field) { ?>
                                <td> <?= $transaction[$key] ?? '' ?> </td>
                            <?php } ?>
                            <?php if ($enableTags) { ?>
                                <td> <?= !empty($transaction['tags']) ? $transaction['tags'] : '-'; ?> </td>
                            <?php } ?>
                            <?php if($show_cost_center){?>
                                <td class="format-me" data-currency-code="<?= $currency ?>" ><?php echo $transaction['CCT_debit'] != 0 ?  my_format($transaction['CCT_debit'], $currency) . ' (' . $transaction['CCT_percentage'] . '%)' : '' ?></td>
                                <td class="format-me" data-currency-code="<?= $currency ?>" ><?php echo $transaction['CCT_credit'] != 0 ? my_format($transaction['CCT_credit'], $currency) . ' (' . $transaction['CCT_percentage'] . '%)' : '' ?></td>
                            <?php } ?>

                            <td class="format-me" data-currency-code="<?= $currency ?>" ><?php echo $transaction['debit'] != 0 ? my_format($transaction['debit'], $currency) : '' ?></td>
                            <td class="format-me" data-currency-code="<?= $currency ?>" ><?php echo $transaction['credit'] != 0 ? my_format($transaction['credit'], $currency) : '' ?></td>


                            <td class="format-me" data-currency-code="<?= $currency ?>" ><?php echo  my_format($after_debit, $currency)  ?></td>
                            <td class="format-me" data-currency-code="<?= $currency ?>" ><?php echo  my_format($after_credit, $currency) ?></td>

                        </tr>
                        <?php
                    }


                    $credit+=$sub_credit;
                    $debit+=$sub_debit;
                    if($show_cost_center){
                        $cost_center_credit+=$sub_cost_center_credit;
                        $cost_center_debit+=$sub_cost_center_debit;
                    }

                    $before_credit += $sub_before_credit;
                    $before_debit += $sub_before_debit ;
                    $total_after_debit += $after_debit;
                    $total_after_credit += $after_credit;
                    if ($is_periodic)
                        $st_groups[] = $group . ($params['group_by'] == 'monthly' ? '-01' : '');
                    else
                        $st_groups[] = $group_title;
                    ?>
                    <?php if($params['group_by'] != 'all'){ ?>

                        <tr class="subtotal">
                            <td colspan="<?php echo $colspan - $colsNo - ($show_cost_center ? 2 : 0) + count($display_custom_fields); ?>"><?php echo $subtotalText ?></td>
                            <?php if($show_cost_center){ ?>
                            <td class="format-me" data-currency-code="<?= $currency ?>"><?php echo my_format($sub_cost_center_debit, $currency) ?></td>
                            <td class="format-me" data-currency-code="<?= $currency ?>"><?php echo my_format($sub_cost_center_credit, $currency) ?></td>
                            <?php } ?>    
                            <td class="format-me" data-currency-code="<?= $currency ?>"><?php echo my_format($sub_debit, $currency) ?></td>
                            <td class="format-me" data-currency-code="<?= $currency ?>"><?php echo my_format(round($sub_credit,5), $currency) ?></td>
                            <td class="format-me" data-currency-code="<?= $currency ?>" ><?php echo my_format($after_debit, $currency) ?></td>
                            <td class="format-me" data-currency-code="<?= $currency ?>" ><?php echo my_format($after_credit, $currency) ?></td>


                        </tr>


                        <tr class="empty-row">
                            <td class="empty-row" colspan="<?php echo $colspan ?>">&nbsp;</td>
                        </tr>
                    <?php } ?>


                <?php } ?>
                <tr class="section-net">
                    <td colspan="<?php echo $colspan - $colsNo - ($show_cost_center ? 2 : 0) + count($display_custom_fields); ?>" class="first-column"><?php printf(__("TOTAL (%s)", true), $currency) ?></td>
                    <?php if($show_cost_center){ ?>
                    <td><?php echo my_format($cost_center_debit, $currency, true) ?></td>
                    <td><?php echo my_format($cost_center_credit, $currency, true) ?></td>
                    <?php } ?>    
                    <td><?php echo my_format($debit, $currency, true) ?></td>
                    <td><?php echo my_format(round($credit,5), $currency, true) ?></td>
                    <td><?php echo $total_after_debit > $total_after_credit ?  my_format($total_after_debit - $total_after_credit, $currency, true) : my_format(0, $currency, true) ?></td>
                    <td><?php echo $total_after_credit > $total_after_debit ?  my_format( $total_after_credit - $total_after_debit, $currency, true) : my_format(0, $currency, true) ?></td>

                </tr>
                </tbody>

            </table>
        </div>
        <br/>
        <?php
    }
    //            ?>

</div>
<style>
    table, tr, td, th, tbody, thead, tfoot {
        page-break-inside: auto !important;
    }
    thead, tfoot {
        display: table-row-group !important;
    }

@media (min-width: 400px) { 
    html{
        font-size: 13px;
    } 
}
@media (min-width: 992px) { 
    html{
        font-size: 14px;
    } 
}
@media (min-width: 1200px) { 
    html{
        font-size: 15px;
    } 
}
@media (min-width: 1600px) { 
    html{
        font-size: 18px;
    } 
}

    #report-table  thead tr td, #report-table  thead  .report-results-head th, #report-table tbody tr{
        font-size: 0.9rem;
    }
    @media print {
        #report-table tbody tr td{
            font-size: 16px !important;
        }
        #report-table  thead tr td, #report-table  thead  .report-results-head th{
            font-size: 15px !important;
        }
    }
</style>
