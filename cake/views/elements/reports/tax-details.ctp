<?php

use Izam\Attachment\Service\AttachmentsService;

$unspan=0;
$has_bn1=false;
$has_bn2=false;
$params = $this->params['url'];
$site=getCurrentSite();
if(!empty($site['bn1_label']))
{
    $has_bn1=true;
    $unspan--;
}
if(!empty($site['bn2_label']))
{
    $has_bn2=true;
    $unspan--;
}

unset($params['ext'], $params['url']);
$owner = getAuthOwner();

// warning suppress
$group_by = $params['group_by'] ?? '';
if($group_by=='invoice') $unspan++;



$formats = getDateFormats('std');
$format = $formats[$owner['date_format']];

$date_str='';
$dateFormats = getDateFormats('std');
$dateFormat = $dateFormats[getAuthOwner('date_format')];
if (!empty($date_from)) {
	$date_str .= sprintf(__("From %s", true), '<strong class="report-date">' . date($dateFormat, strtotime($date_from)) . '</strong>&nbsp;');
}

if (!empty($date_to)) {
    $date_str .= sprintf(__("To %s", true), '<strong class="report-date">' . date($dateFormat, strtotime($date_to)) . '</strong>&nbsp;');
}
$allToSystemCurrency = 0;
if ($currency == '-1') {
    $allToSystemCurrency = 1;
    $currency = getAuthOwner('currency_code');
}
?>
<?php echo  $this->element('reports/report-header',
array(
	'report_title'=> __("Tax Details", true).' ('. $allToSystemCurrency ? sprintf(__('All In (%s)', true), $currency) : $currency. ')',
	'date_str'=>$date_str
));

$is_details=(($_GET['details']=='0')||!isset($_GET['details']));

?>

<?/*<div class="chart-wrap">
<div class="panel panel-default">
<div class="panel-body">
	<div id="TaxChart"></div>
</div>
</div>*/?>



</div>
<?php $totalTaxes = 0 ?>
	<?php if(empty($pdf)) { ?>
		<div class="actions-bar">

			<!-- Split button -->
			<div class="actions-bar right" id="report_controls">
                <button type="button" class="Print btn btn-default"><i class="fa fa-print"></i> <?php __("Print") ?></button>
				<div class="btn-group">
				  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-expanded="false"><i class="fa fa-cloud-download"></i> <?php __("Export Options") ?></button>
				  <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
					<span class="caret"></span>
					<span class="sr-only">Toggle Dropdown</span>
				   </button>
				  <ul class="dropdown-menu" role="menu">
						<?php
						$params = $this->params['url'];
						unset($params['ext'], $params['url']);
						$owner = getAuthOwner();

						$formats = getDateFormats('std');
						$format = $formats[$owner['date_format']];
						 ?>
					 <li><?php echo $html->link(__('Export to CSV', true), array('ext' => 'csv', '?' => $params), array('class' => 'op-export')) ?></li>
					 <li><?php echo $html->link(__('Export to Excel', true), array('ext' => 'xlsx', '?' => $params), array('class' => 'op-export')) ?></li>
					 <li><?php echo $html->link(__('Export to PDF', true), array('ext' => 'pdf', '?' => $params), array('class' => 'Pdf')); ?></li>
<!--					 <li class="divider"></li>-->
<!--					<li>--><?php //echo $html->link(__('Print', true), '#', array('class' => 'Print')) ?><!--</li>-->
				  </ul>
				</div>
			</div>

			<div class="actions-bar left" id="report_controls">
				<div  class="btn-group pull-left m-r-xs" data-toggle="buttons">
					<a  href="#" title="<?php __('Summary') ?>" data-param1="details" data-value1="0" class="view_level btn btn-default <?php if($is_details) echo 'active'; ?> "><i class="fa fa-bars"></i> <?php __('Summary') ?></a>
					<a  href="#"  title="<?php __('Details') ?>" data-param1="details" data-value1="1"  class="view_level btn btn-default  <?php if(!$is_details) echo 'active'; ?>"><i class="fa  fa-search"></i> <?php __('Details') ?></a>
				</div>
			</div>

		</div>
		<?php } ?>

<style>
    .report.no-collapse td.description {
        max-width: 300px;
        word-break: break-word;
    }
</style>
<div class="report" style="margin: 50px auto;">
	<table cellspacing="0" cellpadding="4" width="100%" class="report reports_currency no-collapse fixed-table-head">
		<thead>
			<tr class="report-results-head">
				<th   class="first-column no-sort"><?php __("No.") ?></th>
				<th   class="first-column no-sort"><?php __("Taxpayer") ?></th>
                <?php if($has_bn1) {?>
                <th   class="first-column no-sort"><?php __($site['bn1_label']) ?></th>
                <?php }?>
                <?php if($has_bn2) {?>
                    <th   class="first-column no-sort"><?php __($site['bn2_label']) ?></th>
                <?php }?>
				<th   class="first-column no-sort"><?php __("Date") ?></th>
                <?php if($group_by!='invoice') {?>
				<th   class="first-column no-sort"><?php __("Item") ?></th>
                <?php } ?>
				<th   class="first-column no-sort"><?php __("Description") ?></th>
				<th   class="no-sort ta_right no-sort"><?php __("Taxable Amount") ?></th>
				<th   class="no-sort ta_right no-sort"><?php __("Taxes") ?></th>

			</tr>
		</thead>
		<tbody>
			<?php $counter = 0; ?>

			<?php
				$taxes_count=0;
				debug($report_data);
				foreach ($report_data as $tax_id=>$data) {
					if(dataHasTaxes($data))
						$taxes_count++;

			}

			$net_taxable_total=0;
			$net_tax_total=0;
			?>

			<?php

            //print_pre($report_data);
            foreach ($report_data as $tax_id=>$data) {
			$data_income_total_without_tax = $data['incomes'][__('Income', true)]['total_without_tax'] ?? 0;
			$data_expense_total_without_tax = $data['expenses'][__('Expense', true)]['total_without_tax'] ?? 0;

			$data_income_total_tax = $data['incomes'][__('Income',true)]['total_tax'] ?? 0;
			$data_expense_total_tax = $data['expenses'][__('Expense',true)]['total_tax'] ?? 0;
			if(dataHasTaxes($data)) {
			$taxable_total=$data['invoices']['total_without_tax'];
			if (ifPluginActive(ExpensesPlugin)) {
                $taxable_total += $data_income_total_without_tax - $data_expense_total_without_tax;
			}

			if (ifPluginActive(InventoryPlugin)) {
				$taxable_total-=$data['purchase_orders']['total_without_tax'];
				$taxable_total+=$data['purchase_orders_refunds']['total_without_tax'];
                $taxable_total+=$data['purchase_orders_debit_notes']['total_without_tax'];
                $taxable_total+=$data['purchase_orders_credit_notes']['total_without_tax'];
			}
			$taxable_total -= ($data['refund_receipts']['total_without_tax'] + $data['credit_notes']['total_without_tax']);


			$tax_total=$data['invoices']['total_tax'];
			$tax_total+=$data['debit_note']['total_tax'];
			if (ifPluginActive(ExpensesPlugin)) {
                $tax_total += $data_income_total_tax - $data_expense_total_tax;
			}
			if (ifPluginActive(InventoryPlugin)) {
				$tax_total-=$data['purchase_orders']['total_tax'];
				$tax_total+=$data['purchase_orders_refunds']['total_tax'];
                $tax_total+=$data['purchase_orders_debit_notes']['total_tax'];
                $tax_total+=$data['purchase_orders_credit_notes']['total_tax'];
			}

			$tax_total -= $data['refund_receipts']['total_tax'] + $data['credit_notes']['total_tax'];

			?>
				<tr class="empty-row">
				<td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
				</tr>

				<tr class="sub-head">
					<td colspan="<?php echo 7-$unspan?>" class="sub-head">
                        <?php echo h(!empty($tax_id)?$taxes[$tax_id]['name'] . ' (' . $taxes[$tax_id]['value'] . '%)':__('Non Taxed',true)); ?></td>
				</tr>

				<tr class="secondary-head">
					<td class="" colspan="<?php echo 7-$unspan?>"><?php echo $invoice_type=='accrual'?__('Sales Invoices',true):__('Paid Invoices',true) ?>:</td>
				</tr>
				<?php foreach($data['invoices']['details'] as $row) {
                    ?>
				<tr class="indent-td">
					<td ><a href="/owner/invoices/view/<?php echo $row['inv_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
					<td ><?php echo $clients[$row['client_id']]['Client']['business_name']; ?>  <?php if (!$has_bn1 && !empty ( $clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label']))
                        {
                            echo "<br>".$clients[$row['client_id']]['Client']['bn1_label'].": ".$clients[$row['client_id']]['Client']['bn1'];
                        }
                        if (!$has_bn2 && !empty ( $clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label']))
                        {
                            echo "<br>".$clients[$row['client_id']]['Client']['bn2_label'].": ".$clients[$row['client_id']]['Client']['bn2'];
                        }?>
                    </td>
                    <?php if($has_bn1) {?>
                        <td ><?php echo h($clients[$row['client_id']]['Client']['bn1']); ?></td>
                    <?php }?>
                    <?php if($has_bn2) {?>
                        <td ><?php echo h($clients[$row['client_id']]['Client']['bn2']) ?></td>
                    <?php }?>
					<td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                    <?php if($group_by!='invoice') {?>
                    <td ><?php
 
                    if(strpos($row['item'], 'amazonaws.com')) {
                        
                        $fileDetails = izam_resolve(AttachmentsService::class)->getProductsDefaultImages([$row['product_id']]);
                        if(isset($fileDetails[$row['product_id']]['file'])) {
                            echo "<img height='50' src=\"{$fileDetails[$row['product_id']]['file']}\"/>";
                        }
                    }else{
                        echo h($row['item']);
                    } ?></td>
                    <?php } ?>
					<td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                        $fileDetails = izam_resolve(AttachmentsService::class)->getProductsDefaultImages([$row['product_id']]);
                        if(isset($fileDetails[$row['product_id']]['file'])) {
                            echo "<img height='50' src=\"{$fileDetails[$row['product_id']]['file']}\"/>";
                        }
                    }else{
                        echo h($row['description']);
                    } ?></td>

					<td ><?php echo formatPriceDisplayNumber($row['subtotal_without_tax'], $currency); ?></td>
					<td ><?php echo formatPriceDisplayNumber($row['tax'], $currency); ?></td>
				</tr>
				<? } ?>


				<tr class="subtotal">
					<td colspan="<?php echo  5-$unspan ?>"><?php echo __('Subtotal',true)?></td>
					<td class=""><?php echo  formatPriceDisplayNumber($data['invoices']['total_without_tax'], $currency) ?></td>
					<td class=""><?php echo formatPriceDisplayNumber(round($data['invoices']['total_tax'],5), $currency) ?></td>
				</tr>

                <tr class="empty-row">
                    <td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
                </tr>

                <?php if (settings::getValue(SalesPlugin, settings::ENABLE_ADVANCE_PAYMENT)) { ?>
                        <?php
                    $taxable_total += $data['advance_payments']['total_without_tax'];
                    $tax_total += $data['advance_payments']['total_tax'];
                        ?>
                    <tr class="secondary-head">
                        <td class="" colspan="<?php echo 7 - $unspan ?>"><?php echo __('Advance Payment', true) ?>:
                        </td>
                    </tr>
                    <?php foreach ($data['advance_payments']['details'] as $row) {
                        ?>
                        <tr class="indent-td">
                            <td><a href="/owner/invoices/view/<?php echo $row['inv_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
                            <td><?php echo $clients[$row['client_id']]['Client']['business_name']; ?><?php if (!$has_bn1 && !empty ($clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label'])) {
                                    echo "<br>" . $clients[$row['client_id']]['Client']['bn1_label'] . ": " . $clients[$row['client_id']]['Client']['bn1'];
                                }
                                if (!$has_bn2 && !empty ($clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label'])) {
                                    echo "<br>" . $clients[$row['client_id']]['Client']['bn2_label'] . ": " . $clients[$row['client_id']]['Client']['bn2'];
                                } ?>
                            </td>
                            <?php if ($has_bn1) { ?>
                                <td><?php echo h($clients[$row['client_id']]['Client']['bn1']); ?></td>
                            <?php } ?>
                            <?php if ($has_bn2) { ?>
                                <td><?php echo h($clients[$row['client_id']]['Client']['bn2']) ?></td>
                            <?php } ?>
                            <td><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                            <?php if ($group_by != 'invoice') { ?>
                                <td><?php if (strpos($row['item'], 'amazonaws.com')) {
                                        echo "<img src=\"{$row['item']}\"/>";
                                    } else {
                                        echo h($row['item']);
                                    } ?></td>
                            <?php } ?>
                            <td class="description"><?php if (strpos($row['description'], 'amazonaws.com')) {
                                    echo "<img src=\"{$row['description']}\"/>";
                                } else {
                                    echo h($row['description']);
                                } ?></td>

                            <td><?php echo formatPriceDisplayNumber($row['subtotal_without_tax'], $currency); ?></td>
                            <td><?php echo formatPriceDisplayNumber($row['tax'], $currency); ?></td>
                        </tr>
                    <? } ?>


                    <tr class="subtotal">
                        <td colspan="<?php echo 5 - $unspan ?>"><?php echo __('Subtotal', true) ?></td>
                        <td class=""><?php echo formatPriceDisplayNumber($data['advance_payments']['total_without_tax'], $currency) ?></td>
                        <td class=""><?php echo formatPriceDisplayNumber($data['advance_payments']['total_tax'], $currency) ?></td>
                    </tr>

                    <tr class="empty-row">
                        <td class="empty-row" colspan="<?php echo 7 - $unspan ?>">&nbsp</td>
                    </tr>
                <?php } ?>

				<tr class="secondary-head">
					<td class="" colspan="<?php echo 7-$unspan?>"><?php echo __('Sales Debit Note',true) ?>:</td>
				</tr>
				<?php foreach($data['debit_note']['details'] as $row) {
                    ?>
				<tr class="indent-td">
                    <td ><a href="/owner/invoices/view_debitnote/<?php echo $row['inv_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
					<td ><?php echo $clients[$row['client_id']]['Client']['business_name']; ?>  <?php if (!$has_bn1 && !empty ( $clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label']))
                        {
                            echo "<br>".$clients[$row['client_id']]['Client']['bn1_label'].": ".$clients[$row['client_id']]['Client']['bn1'];
                        }
                        if (!$has_bn2 && !empty ( $clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label']))
                        {
                            echo "<br>".$clients[$row['client_id']]['Client']['bn2_label'].": ".$clients[$row['client_id']]['Client']['bn2'];
                        }?>
                    </td>
                    <?php if($has_bn1) {?>
                        <td ><?php echo h($clients[$row['client_id']]['Client']['bn1']); ?></td>
                    <?php }?>
                    <?php if($has_bn2) {?>
                        <td ><?php echo h($clients[$row['client_id']]['Client']['bn2']) ?></td>
                    <?php }?>
					<td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                    <?php if($group_by!='invoice') {?>
                    <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                        echo "<img src=\"{$row['item']}\"/>";
                    }else{
                        echo h($row['item']);
                    } ?></td>
                    <?php } ?>
					<td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                        echo "<img src=\"{$row['description']}\"/>";
                    }else{
                        echo h($row['description']);
                    } ?></td>

					<td ><?php echo formatPriceDisplayNumber($row['subtotal_without_tax'], $currency); ?></td>
					<td ><?php echo formatPriceDisplayNumber($row['tax'], $currency); ?></td>
				</tr>
				<? } ?>


				<tr class="subtotal">
					<td colspan="<?php echo  5-$unspan ?>"><?php echo __('Subtotal',true)?></td>
					<td class=""><?php echo  formatPriceDisplayNumber($data['debit_note']['total_without_tax'], $currency) ?></td>
					<td class=""><?php echo formatPriceDisplayNumber($data['debit_note']['total_tax'], $currency) ?></td>
				</tr>

                <tr class="empty-row">
                    <td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
                </tr>

                <tr class="secondary-head">
                    <td class="" colspan="<?php echo 7-$unspan?>"><?php __('Sales Returns') ?>:</td>
                </tr>
                <?php foreach($data['refund_receipts']['details'] as $row) { ?>
                    <tr class="indent-td">
                        <td ><a href="/owner/invoices/view_refund/<?php echo $row['inv_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
                        <td ><?php echo $clients[$row['client_id']]['Client']['business_name']; ?>  <?php if (!$has_bn1 && !empty ( $clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label']))
                            {
                                echo "<br>".$clients[$row['client_id']]['Client']['bn1_label'].": ".$clients[$row['client_id']]['Client']['bn1'];
                            }
                            if (!$has_bn2 && !empty ( $clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label']))
                            {
                                echo "<br>".$clients[$row['client_id']]['Client']['bn2_label'].": ".$clients[$row['client_id']]['Client']['bn2'];
                            }?>
                        </td>
                        <?php if($has_bn1) {?>
                            <td ><?php echo h($clients[$row['client_id']]['Client']['bn1']); ?></td>
                        <?php }?>
                        <?php if($has_bn2) {?>
                            <td ><?php echo h($clients[$row['client_id']]['Client']['bn2']) ?></td>
                        <?php }?>
                        <td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                        <?php if($group_by!='invoice') {?>
                            <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                                echo "<img src=\"{$row['item']}\"/>";
                            }else{
                                echo h($row['item']);
                            } ?></td>
                        <?php } ?>
                        <td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                            echo "<img src=\"{$row['description']}\"/>";
                        }else{
                            echo h($row['description']);
                        } ?></td>
                        <td ><?php echo formatPriceDisplayNumber(-1 * $row['subtotal_without_tax'], $currency); ?></td>
                        <td ><?php echo formatPriceDisplayNumber(-1 * $row['tax'], $currency); ?></td>
                    </tr>
                <? } ?>


                <?php foreach($data['credit_notes']['details'] as $row) { ?>
                    <tr class="indent-td">
                        <td ><a href="/owner/invoices/view_creditnote/<?php echo $row['inv_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
                        <td ><?php echo $clients[$row['client_id']]['Client']['business_name']; ?>
                        <?php if (!$has_bn1 && !empty ( $clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label']))
                            {
                            echo "<br>".$clients[$row['client_id']]['Client']['bn1_label'].": ".$clients[$row['client_id']]['Client']['bn1'];
                            }
                            if (!$has_bn2 && !empty ( $clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label']))
                            {
                            echo "<br>".$clients[$row['client_id']]['Client']['bn2_label'].": ".$clients[$row['client_id']]['Client']['bn2'];
                            }?>
                        </td>
                        <?php if($has_bn1) {?>
                            <td ><?php echo h($clients[$row['client_id']]['Client']['bn1']); ?></td>
                        <?php }?>
                        <?php if($has_bn2) {?>
                            <td ><?php echo h($clients[$row['client_id']]['Client']['bn2']) ?></td>
                        <?php }?>
                        <td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                        <?php if($group_by!='invoice') {?>
                            <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                                echo "<img src=\"{$row['item']}\"/>";
                            }else{
                                echo h($row['item']);
                            } ?></td>
                        <?php } ?>
                        <td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                            echo "<img src=\"{$row['description']}\"/>";
                        }else{
                            echo h($row['description']);
                        } ?></td>
                        <td ><?php echo formatPriceDisplayNumber(-1 * $row['subtotal_without_tax'], $currency); ?></td>
                        <td ><?php echo formatPriceDisplayNumber(-1 * $row['tax'], $currency); ?></td>
                    </tr>
                <? } ?>

                <tr class="subtotal">
                    <td colspan="<?php echo 5-$unspan?>"><?php echo __('Subtotal',true)?></td>
                    <td class=""><?php echo  formatPriceDisplayNumber(-1*($data['refund_receipts']['total_without_tax']+$data['credit_notes']['total_without_tax']), $currency) ?></td>
                    <td class=""><?php echo formatPriceDisplayNumber(-1*($data['refund_receipts']['total_tax']+$data['credit_notes']['total_tax']), $currency) ?></td>
                </tr>


                <tr class="empty-row">
                    <td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
                </tr>

                <tr class="secondary-head">
                    <td class="" colspan="<?php echo 7-$unspan?>"><?php __('Purchase Invoices') ?>:</td>
                </tr>
                <?php foreach($data['purchase_orders']['details'] as $row) {

                    ?>
                    <tr class="indent-td">
                        <td ><a href="/owner/purchase_invoices/view/<?php echo $row['inv_id'] ?>/" target="_blank">   <?php echo h($row['id']); ?><?php if(!empty($row['po_number'])){
                                    echo '<br>';
                                    echo '<span>'.__('Po number').'</span>:<span>'.$row['po_number'].'</span>';
                                }
                                ?></a></td>
                        <td ><?php
                            echo $suppliers[$row['supplier_id']]['Supplier']['business_name'];
                            // We did this since somehow there are some suppliers that have bn1 (vat number) but doesn't have any bn1_label at all which is just the label name which doesn't make sense
                            if (!empty ($suppliers[$row['supplier_id']]['Supplier']['bn1']) && empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label'])) {
                                $suppliers[$row['supplier_id']]['Supplier']['bn1_label'] = __("VAT Number", true);
                            }
                            if (!$has_bn1 && !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn1']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label']))
                            {
                                echo "<br>".$suppliers[$row['supplier_id']]['Supplier']['bn1_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn1'];
                            }
                            if (!$has_bn2 && !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn2']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn2_label']))
                            {
                                echo "<br>".$suppliers[$row['supplier_id']]['Supplier']['bn2_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn2'];
                            }
                            ?></td>
                        <?php if($has_bn1) {?>
                            <td ><?php echo h($suppliers[$row['supplier_id']]['Supplier']['bn1']); ?></td>
                        <?php }?>
                        <?php if($has_bn2) {?>
                            <td ><?php echo h($suppliers[$row['supplier_id']]['Supplier']['bn2']) ?></td>
                        <?php }?>
                        <td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                        <?php if($group_by!='invoice') {?>
                            <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                                echo "<img src=\"{$row['item']}\"/>";
                            }else{
                                echo h($row['item']);
                            } ?></td>
                        <?php } ?>
                        <td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                            echo "<img src=\"{$row['description']}\"/>";
                        }else{
                            echo h($row['description']);
                        } ?></td>
                        <td ><?php echo formatPriceDisplayNumber(-1 * $row['subtotal_without_tax'], $currency); ?></td>
                        <td ><?php echo formatPriceDisplayNumber( -1 * $row['tax'], $currency); ?></td>
                    </tr>
                <? } ?>


                <tr class="subtotal">
                    <td colspan="<?php echo 5-$unspan?>"><?php echo __('Subtotal',true)?></td>
                    <td class=""><?php echo  formatPriceDisplayNumber(-1*$data['purchase_orders']['total_without_tax'], $currency) ?></td>
                    <td class=""><?php echo formatPriceDisplayNumber(-1*$data['purchase_orders']['total_tax'], $currency) ?></td>
                </tr>

                <tr class="empty-row">
                    <td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
                </tr>


                <tr class="secondary-head">
                    <td class="" colspan="<?php echo 7-$unspan?>"><?php __('Purchases Returns') ?>:</td>
                </tr>
                <?php foreach($data['purchase_orders_refunds']['details'] as $row) { ?>
                    <tr class="indent-td">
                        <td ><a href="/owner/purchase_invoices/view_refund/<?php echo $row['inv_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
                        <td ><?php echo $suppliers[$row['supplier_id']]['Supplier']['business_name'];
                            if (!empty ($suppliers[$row['supplier_id']]['Supplier']['bn1']) && empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label'])) {
                                $suppliers[$row['supplier_id']]['Supplier']['bn1_label'] = __("VAT Number", true);
                            }
                            if (!$has_bn1 && !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn1']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label']))
                            {
                                echo "<br>".$suppliers[$row['supplier_id']]['Supplier']['bn1_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn1'];
                            }
                            if (!$has_bn2 && !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn2']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn2_label']))
                            {
                                echo "<br>".$suppliers[$row['supplier_id']]['Supplier']['bn2_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn2'];
                            }
                            ?></td>
                        <?php if($has_bn1) {?>
                            <td ><?php echo h($suppliers[$row['supplier_id']]['Supplier']['bn1']); ?></td>
                        <?php }?>
                        <?php if($has_bn2) {?>
                            <td ><?php echo h($suppliers[$row['supplier_id']]['Supplier']['bn2']) ?></td>
                        <?php }?>
                        <td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                        <?php if($group_by!='invoice') {?>
                            <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                                echo "<img src=\"{$row['item']}\"/>";
                            }else{
                                echo h($row['item']);
                            } ?></td>
                        <?php } ?>
                        <td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                            echo "<img src=\"{$row['description']}\"/>";
                        }else{
                            echo h($row['description']);
                        } ?></td>
                        <td ><?php echo formatPriceDisplayNumber($row['subtotal_without_tax'], $currency); ?></td>
                        <td ><?php echo formatPriceDisplayNumber($row['tax'], $currency); ?></td>
                    </tr>
                <? } ?>

                <?php foreach($data['purchase_orders_debit_notes']['details'] as $row) { ?>
                    <tr class="indent-td">
                        <td ><a href="/owner/purchase_invoices/view_debit_note/<?php echo $row['inv_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
                        <td ><?php echo $suppliers[$row['supplier_id']]['Supplier']['business_name'];
                            if (!empty ($suppliers[$row['supplier_id']]['Supplier']['bn1']) && empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label'])) {
                                $suppliers[$row['supplier_id']]['Supplier']['bn1_label'] = __("VAT Number", true);
                            }
                            if (!$has_bn1 && !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn1']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label']))
                            {
                                echo "<br>".$suppliers[$row['supplier_id']]['Supplier']['bn1_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn1'];
                            }
                            if (!$has_bn2 && !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn2']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn2_label']))
                            {
                                echo "<br>".$suppliers[$row['supplier_id']]['Supplier']['bn2_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn2'];
                            }
                            ?></td>
                        <?php if($has_bn1) {?>
                            <td ><?php echo h($suppliers[$row['supplier_id']]['Supplier']['bn1']); ?></td>
                        <?php }?>
                        <?php if($has_bn2) {?>
                            <td ><?php echo h($suppliers[$row['supplier_id']]['Supplier']['bn2']) ?></td>
                        <?php }?>
                        <td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                        <?php if($group_by!='invoice') {?>
                            <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                                echo "<img src=\"{$row['item']}\"/>";
                            }else{
                                echo h($row['item']);
                            } ?></td>
                        <?php } ?>
                        <td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                            echo "<img src=\"{$row['description']}\"/>";
                        }else{
                            echo h($row['description']);
                        } ?></td>
                        <td ><?php echo formatPriceDisplayNumber($row['subtotal_without_tax'], $currency); ?></td>
                        <td ><?php echo formatPriceDisplayNumber($row['tax'], $currency); ?></td>
                    </tr>
                <? } ?>


                <tr class="subtotal">
                    <td colspan="<?php echo 5-$unspan?>"><?php echo __('Subtotal',true)?></td>
                    <td class=""><?php echo formatPriceDisplayNumber(
                            $data['purchase_orders_refunds']['total_without_tax'] +
                            $data['purchase_orders_debit_notes']['total_without_tax'], $currency
                        ); ?></td>
                    <td class=""><?php echo formatPriceDisplayNumber(
                                $data['purchase_orders_refunds']['total_tax'] +
                                $data['purchase_orders_debit_notes']['total_tax'], $currency
                        ); ?></td>
                </tr>

                <tr class="secondary-head">
                    <td class="" colspan="<?php echo 7-$unspan?>"><?php __('Purchases Credit Note') ?>:</td>
                </tr>
                <?php foreach($data['purchase_orders_credit_notes']['details'] as $row) { ?>
                    <tr class="indent-td">
                        <td ><a href="/owner/purchase_invoices/view_credit_note/<?php echo $row['inv_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
                        <td ><?php echo $suppliers[$row['supplier_id']]['Supplier']['business_name'];
                            if (!empty ($suppliers[$row['supplier_id']]['Supplier']['bn1']) && empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label'])) {
                                $suppliers[$row['supplier_id']]['Supplier']['bn1_label'] = __("VAT Number", true);
                            }
                            if (!$has_bn1 && !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn1']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label']))
                            {
                                echo "<br>".$suppliers[$row['supplier_id']]['Supplier']['bn1_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn1'];
                            }
                            if (!$has_bn2 && !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn2']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn2_label']))
                            {
                                echo "<br>".$suppliers[$row['supplier_id']]['Supplier']['bn2_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn2'];
                            }
                            ?></td>
                        <?php if($has_bn1) {?>
                            <td ><?php echo h($suppliers[$row['supplier_id']]['Supplier']['bn1']); ?></td>
                        <?php }?>
                        <?php if($has_bn2) {?>
                            <td ><?php echo h($suppliers[$row['supplier_id']]['Supplier']['bn2']) ?></td>
                        <?php }?>
                        <td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                        <?php if($group_by!='invoice') {?>
                            <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                                echo "<img src=\"{$row['item']}\"/>";
                            }else{
                                echo h($row['item']);
                            } ?></td>
                        <?php } ?>
                        <td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                            echo "<img src=\"{$row['description']}\"/>";
                        }else{
                            echo h($row['description']);
                        } ?></td>
                        <td ><?php echo formatPriceDisplayNumber($row['subtotal_without_tax'], $currency); ?></td>
                        <td ><?php echo formatPriceDisplayNumber($row['tax'], $currency); ?></td>
                    </tr>
                <? } ?>


                <tr class="subtotal">
                    <td colspan="<?php echo 5-$unspan?>"><?php echo __('Subtotal',true)?></td>
                    <td class=""><?php echo  formatPriceDisplayNumber($data['purchase_orders_credit_notes']['total_without_tax'], $currency) ?></td>
                    <td class=""><?php echo  formatPriceDisplayNumber($data['purchase_orders_credit_notes']['total_tax'], $currency)  ?></td>
                </tr>




				<?php if (!empty($data['incomes']['details'])) { ?>
				<tr class="empty-row">
				<td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
				</tr>
				<tr class="sundry">
					<td colspan="<?php echo 7-$unspan?>"><?php echo __('Other Income',true) ?>:</td>
				</tr>

				<?php foreach($data['incomes']['details'] as $row) { ?>
				<tr class="indent-td">
                    <td ><a href="/owner/incomes/view/<?php echo $row['expen_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
                    <td >&nbsp;</td>
                    <?php if($has_bn1) {?>
                    <td >&nbsp;</td>
                    <?php }?>
                    <?php if($has_bn2) {?>
                        <td >&nbsp;</td>
                    <?php }?>
					<td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                    <?php if($group_by!='invoice') {?>
                        <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                            echo "<img src=\"{$row['item']}\"/>";
                        }else{
                            echo h($row['item']);
                        } ?></td>
                    <?php } ?>
                    <td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                        echo "<img src=\"{$row['description']}\"/>";
                    }else{
                        echo $row['description'];
                    } ?></td>
					<td ><?php echo formatPriceDisplayNumber($row['subtotal_without_tax'], $currency); ?></td>
					<td ><?php echo formatPriceDisplayNumber($row['tax'], $currency); ?></td>
				</tr>

				<? } ?>

				<tr class="subtotal">
					<td  colspan="<?php echo 5-$unspan?>"><?php echo __('Subtotal',true) ?></td>
					<td class=""><?php echo  formatPriceDisplayNumber($data_income_total_without_tax , $currency) ?></td>
					<td class=""><?php echo formatPriceDisplayNumber($data_income_total_tax, $currency) ?></td>
				</tr>

                <?php }
                if (!empty($data['expenses']['details'])) { ?>
				<tr class="empty-row">
				<td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
				</tr>

				<tr class="sundry">
					<td colspan="<?php echo 7-$unspan?>"><?php echo __('Expenses',true) ?>:</td>
				</tr>

				<?php foreach($data['expenses']['details'] as $row) { if (empty($row['expen_id'])) {dd($row);}?>
				<tr class="indent-td">
                    <td ><a href="/owner/expenses/view/<?php echo $row['expen_id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
                    <td ><?php echo h($row['business_name']); ?></td>
                    <?php if($has_bn1) {?>
                        <td ><?php echo h($row['bn1']); ?></td>
                    <?php }?>
                    <?php if($has_bn2) {?>
                        <td ><?php echo h($row['bn2']); ?></td>
                    <?php }?>
					<td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                    <?php if($group_by!='invoice') {?>
                        <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                            echo "<img src=\"{$row['item']}\"/>";
                        }else{
                            echo h($row['item']);
                        } ?></td>
                    <?php } ?>
					<td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                        echo "<img src=\"{$row['description']}\"/>";
                    }else{
                        echo $row['description'];
                    } ?></td>
					<td ><?php echo formatPriceDisplayNumber(-1*$row['subtotal_without_tax'], $currency); ?></td>
					<td ><?php echo formatPriceDisplayNumber(-1*$row['tax'], $currency); ?></td>
				</tr>


				<?  } ?>


				<tr class="subtotal">
					<td  colspan="<?php echo 5-$unspan?>"><?php echo __('Subtotal',true) ?></td>
					<td class=""><?php echo  formatPriceDisplayNumber(-1*$data_expense_total_without_tax, $currency) ?></td>
					<td class=""><?php echo formatPriceDisplayNumber(-1*$data_expense_total_tax, $currency) ?></td>
				</tr>




				<? } ?>




                <?php if(!empty($data['other']['details'])||!empty($data['assets']['details'] )) { ?>
                <tr class="empty-row">
                    <td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
                </tr>


                <tr class="secondary-head">
                    <td class="" colspan="<?php echo 7-$unspan?>"><?php __('Other') ?>:</td>
                </tr>
                <?php foreach($data['other']['details'] as $journalId => $row) { ?>
                    <tr class="indent-td">
                        <td><a target="_blank"  href="/owner/journals/view/<?= $row['journal_id'] ?>"> <?php echo h($row['id']); ?></a></td>
                        <td ><?php echo $clients[$row['client_id']]['Client']['business_name'];
                            if (!$has_bn1 && !empty ( $clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label']))
                            {
                                echo "<br>".$clients[$row['client_id']]['Client']['bn1_label'].": ".$clients[$row['client_id']]['Client']['bn1'];
                            }
                            if ($has_bn2 && !empty ( $clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label']))
                            {
                                echo "<br>".$clients[$row['client_id']]['Client']['bn2_label'].": ".$clients[$row['client_id']]['Client']['bn2'];
                            }
                            ?></td>
                        <?php if($has_bn1) {?>
                            <td ><?php echo isset($clients[$row['client_id']]['Client'])?$clients[$row['client_id']]['Client']['bn21']:'&nbsp;' ?></td>
                        <?php }?>
                        <?php if($has_bn2){?>
                            <td ><?php echo isset($clients[$row['client_id']]['Client'])?$clients[$row['client_id']]['Client']['bn2']:'&nbsp;' ?></td>
                        <?php }?>
                        <td ><?php echo date($dateFormat, strtotime($row['date'])); ?></td>
                        <?php if($group_by!='invoice') {?>
                            <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                                echo "<img src=\"{$row['item']}\"/>";
                            }else{
                                echo h($row['item']);
                            } ?></td>
                        <?php } ?>
                        <td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                            echo "<img src=\"{$row['description']}\"/>";
                        }else{
                            echo h($row['description']);
                        } ?></td>
                        <td ><?php echo formatPriceDisplayNumber($row['subtotal_without_tax'], $currency); ?></td>
                        <td ><?php echo formatPriceDisplayNumber($row['subtotal_with_tax'], $currency); ?></td>
                    </tr>
                <? } ?>

                <?php
                $assetPurchasesTaxableAmount = $assetPurchasesTaxes = 0;
                foreach($data['assets']['details'] as $row) { ?>
                    <?php
                    if (isset($row['tax']) && isset($row['sales_tax'])) {
                        $arr = [
                            [
                                'taxable' => $row['subtotal_without_tax'],
                                'tax' => $row['tax']
                            ], [
                                'taxable' => $row['sales_taxable_amount'],
                                'tax' => $row['sales_tax']
                            ]
                        ];
                    } else {
                        $arr = [
                            [
                                'taxable' => $row['subtotal_without_tax'] ?? $row['sales_taxable_amount'],
                                'tax' => $row['tax'] ?? $row['sales_tax']
                            ]
                        ];
                    }
                    foreach($arr as $type) :
                        ?>
                        <tr class="indent-td">
                            <td ><a href="/owner/assets/view/<?php echo $row['id'] ?>/" target="_blank"><?php echo h($row['id']); ?></a></td>
                            <td >&nbsp;</td>
                            <?php if($has_bn1) {?>
                                <td >&nbsp;</td>
                            <?php }?>
                            <?php if($has_bn2) {?>
                                <td >&nbsp;</td>
                            <?php }?>
                            <td ><?php if(!isset($row['sell_date'])) { echo date($dateFormat, strtotime($row['date'])); } else {echo date($dateFormat, strtotime($row['sell_date']));} ?></td>
                            <?php if($group_by!='invoice') {?>
                                <td ><?php if(strpos($row['item'], 'amazonaws.com')){
                                    echo "<img src=\"{$row['item']}\"/>";
                                }else{
                                    echo h($row['item']);
                                } ?></td>
                            <?php } ?>
                            <td class="description"><?php if(strpos($row['description'], 'amazonaws.com')){
                                echo "<img src=\"{$row['description']}\"/>";
                            }else{
                                echo h($row['description']);
                            } ?></td>
                            <td >
                                <?php
                                $totalAssetValue = -1 * $type['taxable'];
                                echo formatPriceDisplayNumber($totalAssetValue, $currency);
                                ?>
                            </td>
                            <td >
                                <?php
                                echo formatPriceDisplayNumber(-1 * ($type['tax']), $currency);
                                ?>
                            </td>
                        </tr>
                    <? endforeach; ?>
                <? } ?>
                <?
                $assetPurchasesTaxableAmount = $data['assets'][__('Asset',true)]['total_without_tax'] ?? 0;
                $assetPurchasesTaxes = $data['assets'][__('Asset',true)]['total_tax'] ?? 0;

                $assetsSalesAmount = $data['assets'][__('Asset',true)]['total_sales_taxable_amount'] ?? 0;
                $assetsSalesTax = $data['assets'][__('Asset',true)]['total_sales_tax'] ?? 0;

                $taxableAmount = $assetsSalesAmount + $assetPurchasesTaxableAmount;
                $taxesValue = $assetsSalesTax + $assetPurchasesTaxes;
                $taxable_total -= $taxableAmount;
                $tax_total -= $taxesValue;
                $taxable_total += $data['other']['other']['total_without_tax'] ?? 0;
                $tax_total += $data['other']['other']['total_tax'] ?? 0;
                ?>
                <tr class="subtotal">
                    <td colspan="<?php echo 5-$unspan?>"><?php echo __('Subtotal',true)?></td>
                    <td class=""><?php echo formatPriceDisplayNumber((-1 * $data['assets'][__('Asset', true)]['total_without_tax'] - $data['assets'][__('Asset', true)]['total_sales_taxable_amount'] + $data['other']['other']['total_without_tax']), $currency) ?></td>
                    <td class=""><?php echo formatPriceDisplayNumber((-1 * $data['assets'][__('Asset', true)]['total_tax'] - $data['assets'][__('Asset', true)]['total_sales_tax']  + $data['other']['other']['total_tax']), $currency) ?></td>
                </tr>

                <?php } ?>
				<tr class="empty-row">
				<td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
				</tr>

				<tr class="section-net">
				<td colspan="<?php echo 5-$unspan?>" class="first-column"><?php printf($taxes_count<2?__("NET (%s)", true):__("Total (%s)", true), $currency) ?></td>
				<td><?php
                    echo isNumberSignificantlySmall($taxable_total) ? '0.00' : formatPriceDisplayNumber($taxable_total, $currency);
                ?></td>
				<td><?php
                    echo isNumberSignificantlySmall($tax_total) ? '0.00' : formatPriceDisplayNumber($tax_total, $currency);
                ?></td>
				</tr>

			<?php
                    $net_taxable_total+=$taxable_total;
                    $net_tax_total+=$tax_total;
			} }  ?>
			<?php if($taxes_count>1) {?>
				<tr class="empty-row">
				<td class="empty-row" colspan="<?php echo 7-$unspan?>">&nbsp</td>
				</tr>

				<tr class="section-net">
				<td colspan="<?php echo 5-$unspan?>" class="first-column"><?php printf(__("NET (%s)", true), $currency) ?></td>
				<td><?php
                    echo  formatPriceDisplayNumber(isNumberSignificantlySmall($net_taxable_total) ? 0 :$net_taxable_total, $currency);
                ?></td>
				<td><?php
                    echo  formatPriceDisplayNumber(isNumberSignificantlySmall($net_tax_total-$taxesValue) ? 0 : $net_tax_total, $currency);
                ?></td>
				</tr>
			<?php } ?>

		</tbody>
	</table>
</div>
	<?php if(empty($pdf)) { ?>
 <div class="actions-bar">

                <!-- Split button -->
                <div class="btn-group">
                  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-expanded="false"><i class="fa fa-cloud-download"></i> <?php __("Export Options") ?></button>
                  <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                    <span class="caret"></span>
                    <span class="sr-only">Toggle Dropdown</span>
                   </button>
                  <ul class="dropdown-menu" role="menu">

                     <li><?php echo $html->link(__('Export to CSV', true), array('ext' => 'csv', '?' => $params), array('class' => 'op-export')) ?></li>
                    <li><?php echo $html->link(__('Export to PDF', true), array('ext' => 'pdf', '?' => $params), array('class' => 'Pdf')); ?></li>
                     <li class="divider"></li>
                    <li><?php echo $html->link(__('Print', true), '#', array('class' => 'Print')) ?></li>
                  </ul>
                </div>
				</div>
	<? } ?>
<?php //echo $this->element('reports/charts', array('div' => 'TaxChart')); ?>

<script type="text/javascript">
	function replaceUrlParam(url, paramName, paramValue){
		var pattern = new RegExp('\\b('+paramName+'=).*?(&|$)')
		if(url.search(pattern)>=0){
			return url.replace(pattern,'$1' + paramValue + '$2');
		}
		return url + (url.indexOf('?')>0 ? '&' : '?') + paramName + '=' + paramValue
	}
	$('#report_type').html($('.dropdown-menu .active a').html());
	$(function(){

		$('#report_controls a').click(function(e){
			if ($(this).data('param1')) {
			e.preventDefault();
			document.location.href=replaceUrlParam(document.location.href,$(this).data('param1'),$(this).data('value1'));
		}
		});
	})
</script>

