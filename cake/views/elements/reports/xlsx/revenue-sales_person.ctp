<?php
$header = array(csv_quote(__('Staff', true)));
$totals = array();
foreach ($reportCurrencies as $curr) {
	$header[] = csv_quote(__('Paid',true).' ('.$curr.')');
	$header[] = csv_quote(__('Unpaid',true).' ('.$curr.')');
    $header[] = csv_quote(__('Refund',true).' ('.$curr.')');
	$header[] = csv_quote(__('Total',true).' ('.$curr.')');
	$totals[$curr] = 0;
	$paids[$curr] = 0;
	$unpaids[$curr] = 0;
    $refunds[$curr] = 0;
}

$lines = array(implode(CSV_SEPARATOR, $header));
foreach ($reportData as $row) {
	$line = array(csv_quote($row['name']));
	foreach ($reportCurrencies as $curr) {
		if (isset($row[$curr])) {
			$value = round($row[$curr][0]['total'], 2);
		} else {
			$value = 0;
		}
		$totals[$curr] += $value;
		$paids[$curr] += round($row[$curr][0]['paid'],2);
		$unpaids[$curr] += round($row[$curr][0]['unpaid'],2);
        $refunds[$curr] += round($row[$curr][0]['refund'],2);
		$line[] = csv_quote( round($row[$curr][0]['paid'],2));
		$line[] = csv_quote(round($row[$curr][0]['unpaid'],2));
        $line[] = csv_quote(round($row[$curr][0]['refund'],2));
		$line[] = csv_quote(round($row[$curr][0]['total'],2));
	}
	
	$lines[] = implode(CSV_SEPARATOR, $line);
}

$totalsLine = array(csv_quote(__('TOTAL', true)));
foreach ($reportCurrencies as $currency) {
	$totalsLine[] = csv_quote($paids[$currency]);
	$totalsLine[] = csv_quote($unpaids[$currency]);
    $totalsLine[] = csv_quote($refunds[$currency]);
	$totalsLine[] = csv_quote($totals[$currency]);
	
}

$lines[] = implode(CSV_SEPARATOR, $totalsLine);
echo implode("\n", $lines);