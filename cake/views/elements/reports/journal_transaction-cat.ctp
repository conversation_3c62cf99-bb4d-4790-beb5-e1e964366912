<?php
 if (!function_exists('fix_riyal_symbol')) {
    function fix_riyal_symbol($value, $currency) {
        if(in_array(_request()->ext , ['csv' , 'xlsx'])){
            return str_replace(
                ["SR", "ر.س"],
                [" SR ", " ر.س "],
                format_price($value, $currency)
            );            
        }
        return  format_price($value, $currency);
    }
}

    $keys = [
        'show_net',
        'balance_only',
        'date_from',
        'date_to',
        'group_by',
        'accounts_branch',
        'staff_id',
        'currency',
        'branch_transactions',
        'display_all_accounts',
        'cost_center_id',
        'fy_id',
        'fy_id',
    ];

    $url = "/owner/reports/journal_transactions?show_net=&balance_only=&type=&report_type=transaction&display_all_accounts=1";
    $queryParams = [];

    foreach ($keys as $key) {
        if (isset($_GET[$key])) {
            $queryParams[$key] = $_GET[$key];
        }
    }

    if (isset($_GET['fy_id']) && is_array($_GET['fy_id'])) {
        foreach ($_GET['fy_id'] as $index => $value) {
            $queryParams["fy_id[$index]"] = $value;
        }
    }
    $queryString = http_build_query($queryParams);
    $url = $url . '&' . $queryString;

?>


<?php
//credit and debits are transaction_credits
$credits = $debits = $credits_before = $debits_before = $credits_after = $debits_after = $net_credit = $net_debit = array();
$rowspan = '';
$is_multi_currency = count($reportCurrencies) > 1;

if ($is_multi_currency)
	$rowspan = ' rowspan="3" ';

$df = $_GET['date_from'];
$dt = $_GET['date_to'];
$levelSelected = !empty($_GET['level']);
if($_GET['type'] == 'trial-net') {
    $levelSelected = false;
}
$show_net = !$levelSelected && $_GET['show_net']
?>
<style>

@media (min-width: 400px) { 
  html.no-js{
      font-size: 13px;
  } 
}
@media (min-width: 992px) { 
  html.no-js{
      font-size: 14px;
  } 
}
@media (min-width: 1200px) { 
  html.no-js{
      font-size: 15px;
  } 
}
@media (min-width: 1600px) { 
  html.no-js{
      font-size: 18px;
  } 
}
.btn-space {
    width: 22px;
    height: 22px;
}
.is-loading {
        height: 500px;
    }
.account-name {
    min-width: 250px;
}
.report-table-dashed tbody td {
    font-size: 0.9rem;
}
@media print {
    .account-name {
        min-width: 180px;
    }
  .report-table-dashed tbody td,
  .report-table-dashed tbody th,
  .report-table-dashed .cell-head {
    font-size: 16px !important;
  }
}
</style>

<div class="report-table--wrapper" style="overflow-x: auto;">
<div class="expand-levels">
<?php if(!$levelSelected) { ?>
<button id="expand-level" class="btn btn-success">+</button>
<?php
// Get current query parameters
$queryParams = $this->params['url'];
unset($queryParams['ext']);
unset($queryParams['url']); // Remove the URL key which contains the actual string URL

$levelOptions = [];
for($i=1; $i <= $maxLevel; $i++) {
    ?>
    <a href="<?php echo Router::url(array('?' => array_merge($queryParams, ['expand_level' => $i]))) ?>" class="btn btn-secondary <?php echo $i == $_GET['expand_level'] ? 'disabled' : '' ?>"><?php echo sprintf(__('Level %s',true), $i)?></a>
    <?php
}

?>
<?php } ?>
</div>
<style>
    @media print {
        .expand-levels,
        th .btn,
        td .btn {
            display: none;
        }
    }
</style>
<div class="report">


	<table cellspacing="0" cellpadding="4" width="100%" class="report reports_currency report-table-dashed report-table-bordered">
		<thead class="disable-safari-fix">
			<tr class="report-table-dashed--head">
				<th rowspan="2"  class="first-column no-sort">
                <?php if(!$levelSelected) { ?>
                    <button id="expand-level" class="btn btn-sm btn-secondary mr-2">+</button>
                <?php } ?>
                <?php __("Name") ?></th>
				<th  rowspan="2"  class="first-column no-sort"><?php __("Code") ?></th>
				<? if ($is_multi_currency) { ?>
					<th >&nbsp;</th>
				<? } ?>
				<?php foreach ($reportCurrencies as $currency): ?>
					<?php $credits[$currency] = $debits[$currency] = $credits_before[$currency] = $debits_before[$currency] = $credits_after[$currency] = $debits_after[$currency] = 0; ?>
					<? if ($is_multi_currency) { ?>
						<th  class="no-sort ta_right no-sort"><?php echo $currency ?></th>
					<? } else { ?>
			
						<?php if( (!empty($dt) || !empty($df)) && !$balanceOnly){ ?>
						<th colspan="2" class="text-center"><?php  __('Balance Before') ;?></th>
						<?php } ?>
                        <?php if(!$balanceOnly) { ?>
						<th colspan="2" class="text-center"><?php __('Transaction Balance') ?> </th>
                        <?php } ?>
						<?php if(!empty($dt) && !$balanceOnly){ ?>
						<th colspan="2" class="text-center"><?php  __('Balance After') ;?></th>
						<?php } ?>
						
						<?php if($show_net) { ?>
						<th colspan="2" class="text-center"><?php __('Net Balance') ?> </th>
						<?php } ?>
			</tr>
			<tr class="report-table-dashed--head">
						<?php if( (!empty($dt) || !empty($df)) && !$balanceOnly){ ?>
						<th   class="no-sort ta_right text-right no-sort"><?php __('Debit') ?> (<?php echo $currency ?>)</th>
						<th   class="no-sort ta_right text-right no-sort"><?php __('Credit') ?> (<?php echo $currency ?>)</th>
						<?php } if(!$balanceOnly){ ?>
						<th   class="no-sort ta_right text-right no-sort"><?php __('Debit') ?> (<?php echo $currency ?>)</th>
						<th   class="no-sort ta_right text-right no-sort"><?php __('Credit') ?> (<?php echo $currency ?>)</th>
						<?php }if(!empty($dt) && !$balanceOnly){ ?>
						<th   class="no-sort ta_right text-right no-sort"><?php __('Debit') ?> (<?php echo $currency ?>)</th>
						<th   class="no-sort ta_right text-right no-sort"><?php __('Credit') ?> (<?php echo $currency ?>)</th>
						<?php } ?>
						<?php if($show_net) { ?>
						<th   class="no-sort ta_right text-right no-sort"><?php __('Debit') ?> (<?php echo $currency ?>)</th>
						<th   class="no-sort ta_right text-right no-sort"><?php __('Credit') ?> (<?php echo $currency ?>)</th>
						<?php } ?>

			</tr>
					<? } ?>
				<?php endforeach; ?>
			
		</thead>
		<tbody>
			<?php $counter = 0; ?>
			<?php foreach ($reportData as $row): ?>
				<?php $class = ife( ++$counter % 2, 'odd', 'even'); ?>
				<tr class="<?php echo $class ?>">
					<td class="cat d-flex align-items-center account-name" <?php echo $rowspan ?> > <?php if ($row[$currency]["JC"]['has_children'] == 1 && !$levelSelected) { ?><span data-padding="0" class="cat btn btn-xs btn-secondary mr-2" data-id="<?php echo $row[$currency]['JC']['id'] ?>" > <i class="fa fa-plus "></i> </span><?php }else{ ?><span data-padding="0" class="btn-space mr-2" style="opacity:0"></span><?php } ?><span><?php echo $row['name'] === "0" ? $title = "0" : $title = __($row['name'], true) ?></span></td>
					<td <?php echo $rowspan ?> ><?php echo isset($row[$currency]["JC"]['code'])?$row[$currency]["JC"]['code']:(isset($row[$currency]['code'])? $row[$currency]['code']: '') ?></td>
					<? if ($is_multi_currency) { ?>
						<td><?php __('Total Debit') ?></td>
					<? } ?>
					<?php foreach ($reportCurrencies as $currency): ?>
						<?php
						if (isset($row[$currency])) {
							$value = $row[$currency][0]['total'];
						} else {
							$value = 0;
						}

                        $credits[$currency] +=$row[$currency][0]['total_credit'];
                        $debits[$currency] += $row[$currency][0]['total_debit'];
                        $credits_after[$currency] +=$row[$currency]['after']['total_credit'];
                        $debits_after[$currency] +=$row[$currency]['after']['total_debit'];

                        if($flag == 'groups_only') {
                            $credits_before[$currency] +=$row[$currency]['before']['total_credit'];
                            $debits_before[$currency] +=$row[$currency]['before']['total_debit'];
                        } elseif ($row[$currency]['before']['total_debit'] > $row[$currency]['before']['total_credit']) {
                            $debits_before[$currency] += $row[$currency]['before']['total_debit'] - $row[$currency]['before']['total_credit'];
                        } else {
                            $credits_before[$currency] += $row[$currency]['before']['total_credit'] - $row[$currency]['before']['total_debit'];
                        }

                        $net_credit[$currency] += $row[$currency]['net']['credit'];
                        $net_debit[$currency] += $row[$currency]['net']['debit'];
						?>
						<? if ($is_multi_currency) { ?>
							<td class="text-right"><?php echo report_format_price_simple($row[$currency][0]['total_debit'], $currency); ?></td>
						<? } else { ?>
						<?php if((!empty($dt) || !empty($df)) && !$balanceOnly){ ?>
                            <?php if($flag == 'groups_only') { ?>
                                <td class="text-right"><?php echo report_format_price_simple($row[$currency]['before']['total_debit'], $currency); ?></td>
                                <td class="text-right"><?php echo report_format_price_simple($row[$currency]['before']['total_credit'], $currency); ?></td>
                            <?php } else {
                                if($row[$currency]['before']['total_debit'] > $row[$currency]['before']['total_credit']){
                            ?>
                                <td class="text-right"><?php echo report_format_price_simple($row[$currency]['before']['total_debit'] - $row[$currency]['before']['total_credit'], $currency); ?></td>
                                <td class="text-right"><?php echo report_format_price_simple(0, $currency); ?></td>
                            <?php
                                }else {
                            ?>
                                <td class="text-right"><?php echo report_format_price_simple(0, $currency); ?></td>
                                <td class="text-right"><?php echo report_format_price_simple($row[$currency]['before']['total_credit'] - $row[$currency]['before']['total_debit'], $currency); ?></td>
                            <?php        

                                }
                            ?>
                            <?php } ?>        

							<?php } if(!$balanceOnly){ ?>
							<td class="text-right"><?php echo report_format_price_simple($row[$currency][0]['total_debit'], $currency); ?></td>
							<td class="text-right"><?php echo report_format_price_simple($row[$currency][0]['total_credit'], $currency); ?></td>
							<?php }if(!empty($dt) && !$balanceOnly){ ?>
							<td class="text-right"><?php echo report_format_price_simple($row[$currency]['after']['total_debit'], $currency); ?></td>
							<td class="text-right"><?php echo report_format_price_simple($row[$currency]['after']['total_credit'], $currency); ?></td>
							<?php } ?>
							<?php if($show_net) { ?>
							<td class="text-right"><?php echo empty($row[$currency]['net']['debit']) ? '':  report_format_price_simple($row[$currency]['net']['debit']); ?></td>
							<td class="text-right"><?php echo empty($row[$currency]['net']['credit']) ? '':  report_format_price_simple($row[$currency]['net']['credit']); ?></td>
							<?php } ?>
						<? } ?>
					<?php endforeach; ?>
				</tr>
				<? if ($is_multi_currency) { ?>	
					<tr class="<?php echo $class ?> xgreen">
						<td><?php __('Total Debit') ?></td>
						<td><?php __('Total Credit') ?></td>
						<?php foreach ($reportCurrencies as $currency): ?>
						<td><?php echo report_format_price_simple($row[$currency][0]['total_credit'], $currency); ?></td>
							
						<?php endforeach; ?>
					</tr>
					<tr class=" <?php echo $class ?> xred">

					</tr>
				<?php } ?>
			<?php endforeach; ?>
			<tr class="grand-total">
				<th class="grand-total-cell" colspan="2"<?php echo $rowspan ?> ><?php __('Total') ?></th>
				<? if ($is_multi_currency) { ?>
					<th class="grand-total-cell"><?php __('Total Debit') ?></th>
					<th class="grand-total-cell"><?php __('Total Credit') ?></th>
				<? } ?>

                <?php

                $sar_class = $currency =='SAR'?"style='font-family: saudi_riyal_symbol, sans-serif !important;' class='sar_symbol text-right'" :"class='text-right'" ;


                  foreach ($reportCurrencies as $currency): ?>
                    <?php if ($is_multi_currency): ?>
                        <th><?php echo fix_riyal_symbol($credits[$currency], $currency); ?></th>
                    <?php else: ?>
                        <?php if ((!empty($dt) || !empty($df)) && !$balanceOnly): ?>
                            <th <?=$sar_class?> ><?php echo fix_riyal_symbol($debits_before[$currency], $currency); ?></th>
                            <th <?=$sar_class?> ><?php echo fix_riyal_symbol($credits_before[$currency], $currency); ?></th>
                        <?php endif; ?>

                        <?php if (!$balanceOnly): ?>
                            <th <?=$sar_class?>><?php echo fix_riyal_symbol(round($debits[$currency], 3), $currency); ?></th>
                            <th <?=$sar_class?>><?php echo fix_riyal_symbol(round($credits[$currency], 3), $currency); ?></th>
                        <?php endif; ?>

                        <?php if (!empty($dt) && !$balanceOnly): ?>
                            <th <?=$sar_class?>><?php echo fix_riyal_symbol(round($debits_after[$currency], 3), $currency); ?></th>
                            <th <?=$sar_class?>><?php echo fix_riyal_symbol(round($credits_after[$currency], 3), $currency); ?></th>
                        <?php endif; ?>

                        <?php if ($show_net): ?>
                            <th <?=$sar_class?>><?php echo fix_riyal_symbol(round($net_debit[$currency], 3), $currency); ?></th>
                            <th <?=$sar_class?>><?php echo fix_riyal_symbol(round($net_credit[$currency], 3), $currency); ?></th>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endforeach; ?>

			</tr>
			<? if ($is_multi_currency) { ?>
				<tr class="section-net">
					<td ><?php __('Total Credit') ?></td>
					<?php foreach ($reportCurrencies as $currency): ?>
					<td><?php echo format_price($credits[$currency], $currency); ?></td>
						
					<?php endforeach; ?>
				</tr>

			<? } ?>
		</tbody>
		<tfoot>

		</tfoot>
	</table>
</div>
<script type="text/javascript">//<![CDATA[
    var currencies = <?php echo json_encode(include APP . 'config' . DS . 'currencies.php') ?>;
    var number_formats = <?php echo json_encode($number_formats) ?>;
    var country_code = '<?php echo getCurrentSite('country_code'); ?>';
    var currency_code = '<?php echo getCurrentSite('currency_code'); ?>';
	var padding_direction = "padding-left:";
<?php if (CurrentSiteLang() == "ara") { ?>
	 padding_direction = "padding-right:" ;
<?php } ?>

</script>

<script>
    $(function () {
        var DISPLAY_BEFORE_TYPE_FLAG = '<?= $flag ?? 'groups_only'; ?>';
        $('#expand-level').click(function() {
            $('.fa-plus:visible').each(function() {
                $(this).click();
            });
        })
		function generate_spaces(no)
		{
			var spaces = '';
			for(i=0;i<no;i++)
				spaces += '&nbsp;&nbsp;&nbsp;';
			return spaces ;
		}
		
		function get_number(value)
		{
			if(isNaN(value) || value == null)
				return 0
			else return value ;
		}
		
        function generate_cat_tr(v, currency_code, current_tr_class, td_padding, children_class_text) {
            
            //parent_cat_id could be array or just one parent
			spaces =  generate_spaces(td_padding);
            //to do check if arabic or english to decide padding left or right
            current_td_padding_style = "style='"+padding_direction+"" + td_padding + "px'";
            new_tr = "";
            new_tr += "<tr  class='" + current_tr_class + " " + children_class_text + " '  >";
            new_tr += "<td class='cat d-flex align-items-center' > "+spaces ;
           
            if (v[currency_code]['JA'] != true) {
                hyper = '&journal_cat_id=' + v[currency_code]['JC']['id'];
                if (v[currency_code]['JC']['has_children'] == true)
                {
                    new_tr += "<span   data-padding='" + td_padding + "' class='cat btn btn-xs btn-secondary mr-2' data-id='" + v[currency_code]['JC']['id'] + "' > <i class=\"fa fa-plus \"></i> </span>";
                } 
            } else { 
                hyper = '&journal_account_id[]=' + v['acc_id'] ;
                new_tr += "<span data-padding='0' class='btn-space mr-2' style='opacity:0'></span>";
            }
			new_tr +=   '<a href="<?= $url ?>' + hyper + '">' + v['name'] +'</a>';
            var credit = get_number(v[currency_code][0]['total_credit']);
            var debit = get_number(v[currency_code][0]['total_debit']);
			if(v[currency_code] && v[currency_code]['before'] !== null && typeof v[currency_code]['before'] != 'undefined')
            {
                var debit_before = get_number(v[currency_code]['before']['total_debit']);
                var credit_before = get_number(v[currency_code]['before']['total_credit']);
            }

            var debit_after = get_number(v[currency_code]['after']['total_debit']);
            var credit_after = get_number(v[currency_code]['after']['total_credit']);
			<?php if($show_net){ ?>
           var net_credit = get_number(v[currency_code]['net']['credit']);
           var net_debit = get_number(v[currency_code]['net']['debit']);
			<?php } ?>
			
           

            if (v[currency_code]['JA'] != true) {
                code = v[currency_code]['JC']['code'];
            } else
            {
                code = v[currency_code]['code'] ;
            }

			
            new_tr += " </td>";
            new_tr += "<td class='text-right' >"  + code + "</td>";
			<?php if((!empty($dt) || !empty($df)) && !$balanceOnly){ ?>
            if(typeof debit_before == 'undefined')
                debit_before = 0;

            if(typeof credit_before == 'undefined')
                credit_before = 0;
            if(DISPLAY_BEFORE_TYPE_FLAG == "balances_only") {
                if(debit_before > credit_before){
                    new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(debit_before - credit_before)) + "</td>";
                    new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(0))+ "</td>";
                } else {
                    new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(0)) + "</td>";
                    new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(credit_before - debit_before))+ "</td>";
                }

            } else {
                new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(debit_before)) + "</td>";
                new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(credit_before))+ "</td>";
            }

			<?php } if(!$balanceOnly){ ?>

			new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(debit)) + "</td>";
            new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(credit)) + "</td>";
			<?php } if(!empty($dt) && !$balanceOnly){ ?>

			new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(debit_after)) + "</td>";
            new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(credit_after)) + "</td>";
			<?php }?>
						<?php if($show_net){ ?>

			new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(net_debit)) + "</td>";
            new_tr += "<td class='text-right' >"  + numberWithSeprator(format_price(net_credit)) + "</td>";
						<?php } ?>
            new_tr += "</tr>";
            return new_tr;
        }

        function containsObject(obj, list) {
            var i;
            for (i = 0; i < list.length; i++) {
                if (list[i].attr('data-id') === obj.attr('data-id')) {
                    return true;
                }
            }

            return false;
        }
        let levelsToExpand = <?php echo (isset($_GET['level']) && !empty($_GET['level']) && $_GET['type'] == 'trial-net') ? $_GET['level'] : 0 ?>;
        <?php if(isset($_GET['expand_level']) && !empty(isset($_GET['expand_level']))) { ?>
            levelsToExpand = <?php echo $_GET['expand_level'] ?>;
        <?php } ?>
        levelsToExpand--; // treat base level as level 1 not level 0
        if(levelsToExpand > 0) {
            $('#PaymentTable').addClass('is-loading');
        }
        let startedExpanding = 0;
        let finishedExpanding = 0;
        var ajax_requests = [];

        $("body").on('click', 'td.cat', function (e) {
            //to handle multiple ajax request we will implement a queue to store the requests and excutes them after each requst is finished
            loading = $('#loading-progress');
            if (loading.length > 0 && false) {
                if (!containsObject($(this), ajax_requests)) {
                    ajax_requests.push($(this));
                }


            } else
            {
                var $this = $(this).children('span.cat');
                var currency_code = "";
                //for the styling of each row
                var parent_tr = $this.parents('tr');
                var parent_tr_class_text = parent_tr.attr("class");
                var parent_tr_class_parts = parent_tr_class_text.split(" ");
				
                var parent_tr_class = parent_tr_class_parts[0];
                var parent_cat_id = $this.attr('data-id');
                //now parent_tr_class_parts contain cat parents
                parent_tr_class_parts.shift();
				var parent_tr_class_parts = parent_tr_class_parts.filter(function(n){ return n != "" });
				var parents_count = parent_tr_class_parts.length;
				
                parent_tr_class === "even" ? current_tr_class = "odd" : current_tr_class = "even";
                var parent_td_padding = parseInt($this.attr('data-padding'));

                var children_class_text = " parent-cat-id-" + parent_cat_id + " " + parent_tr_class_parts.join(" ");

                var current_td_padding = parent_td_padding + 2;
                // Get the current URL
                var request_url = window.location.href;

                // Remove the trailing '#' if it exists
                if (request_url.endsWith('#')) {
                    request_url = request_url.slice(0, -1);
                }

                // Add the "cat_id" parameter
                request_url += (request_url.includes('?') ? '&' : '?') + "cat_id=" + parent_cat_id;
                
                var i_element = $this.children('i');
                //get cat text to know the required behaviour show children or hide them
                if (i_element.hasClass('fa-plus')) {
                    children_count = $("tr.parent-cat-id-" + parent_cat_id).length;
                    if (children_count == 0) {
                        startedExpanding++;
                        $this.attr('id', 'loading-progress');
                        i_element.attr('class','fa fa-spinner fa-pulse');
                        //children count is to check if he already has children or not 
                        //if he hasn't children bring them via ajax
                        //else if he has children show them
                        ajaxInProgress = true;
                        $.ajax({
                            'url': request_url,
                            dataType: 'JSON',
                            retryCount: 1, 
                            maxRetries: 10,
                            success: function (data) {
                                sortedData = {};
                                $.each(data, function (k, v) {
                                    //this part sorts the data by code
                                    $.each(v, function (k2, v2) {
                                        if(v2 && typeof v2 == 'object')
                                        {
                                            if(v2.code)
                                            {
                                                code = v2.code;
                                            }else{
                                                code = v2.JC.code;
                                            }
                                            sortedData[code] = v;
                                        }
                                    });
                                });
                                $.each(sortedData, function (k, v) {
                                    set_currency = true;
                                    //this under loop is to get the currency foreach record
                                    $.each(v, function (k2, v2) {
                                        if (set_currency || currency_code == "name") {
                                            currency_code = k2;
                                            set_currency = false;
                                        }
                                    });
                                    new_tr = generate_cat_tr(v, currency_code, current_tr_class, current_td_padding, children_class_text);
                                    parent_tr.after(new_tr);
                                    //reseting parent_tr so every record is inserted after the other next selects the next sibling
                                    parent_tr = parent_tr.next();

                                    current_tr_class === "even" ? current_tr_class = "odd" : current_tr_class = "even";


                                });
                                save_output_html();
                                finishedExpanding++;
                                if(startedExpanding == finishedExpanding)
                                {
                                    if(levelsToExpand > 1 && $('.fa-plus').length > 0)
                                    {
                                        levelsToExpand--;
                                        $('#expand-level').click();
                                    } else {
                                        $('#PaymentTable').removeClass('is-loading');
                                    }
                                    console.log('all finished');
                                    console.log(startedExpanding, finishedExpanding);
                                }
                                i_element.attr('class','fa fa-minus');
                                $this.attr('id', "");
                                if (ajax_requests.length != 0)
                                {
                                    var request_element = ajax_requests.shift();
                                    request_element.click();
                                }
                            },
                            error: function (xhr) {
                                let ajax = this;
                                if (xhr.status === 429 && ajax.retryCount <= ajax.maxRetries) {
                                    setTimeout(function () {
                                        $.ajax(ajax)
                                    }, ajax.retryCount * 1000)
                                    ajax.retryCount++;
                                }
                            }
                        });
                    } else 
                    {
                        $("tr.parent-cat-id-" + parent_cat_id).each(function(){
							//this block is used to show only the direct childs of the clicked row by checking if child parents = parent_parents + 1 then it's a direct child and we must show it
							var classText = $(this).attr("class");
							var child_parent_count = (classText.match(/parent\-cat\-id\-/g) || []).length;
							if(child_parent_count == parents_count + 1)
								$(this).show();
						});
						
                        i_element.attr('class','fa fa-minus');
                    }
                    //            e.preventDefault();

                } else if (i_element.attr('class').trim() == "fa fa-minus") {
                    $("tr.parent-cat-id-" + parent_cat_id).each(function()
					{
						$(this).hide();
						$(this).children('td.cat').children('span.cat').children('i').attr("class",'fa fa-plus ');
					})
					
                    //			$("tr.parent-cat-id-"+parent_cat_id).children('.cat').text("+");
                   i_element.attr('class','fa fa-plus ');
                }
            }
        });

        jQuery.fn.outerHTML = function (s) {
            return s
                    ? this.before(s).remove()
                    : jQuery("<p>").append(this.eq(0).clone()).html();
        };
        /*
         * this function is called after each ajax request, to save the output html to a file that will be used when printing a pdf
         * after the file is saved the tmp_file_name will be used to store the tmp file name obvisouly
         */
        var temp_file_name = ""
        var orginal_print_url = "";

        if(levelsToExpand > 0)
        {
            $('#expand-level').click();
        }
        let currentSaveOutputRequest = null; // Store the current AJAX request globally
        function save_output_html()
        {
            request_url = "/owner/reports/save_html/";
            if (currentSaveOutputRequest) {
                currentSaveOutputRequest.abort();
            }

            table_html = $("div.report").outerHTML();
            currentSaveOutputRequest = $.ajax({
                url: request_url,
                method: "POST",
                dataType: "TEXT",
                data: {html: table_html, filename: temp_file_name},
                success: function (data)
                {
                    temp_file_name = data;
                    //give the print buttons the path to the tmp_file_name
                    $(".Pdf").each(function ()
                    {
                        if (orginal_print_url == "") {
                            orginal_print_url = $(this).attr("href");
							}
                            print_url = orginal_print_url;
                            print_url = decodeURI(print_url);
                            print_url += "&temp_file_name=" + temp_file_name;
                            print_url = encodeURI(print_url);
                            $(this).attr("href", print_url);

                    });
                    $(".CSV").each(function ()
                    {
                        if (orginal_csv_url == "") {
                            orginal_csv_url = $(this).attr("href");
							}
                            print_url = orginal_csv_url;
                            print_url = decodeURI(print_url);
                            print_url += "&temp_file_name=" + temp_file_name;
                            print_url = encodeURI(print_url);
                            $(this).attr("href", print_url);
                        
                    });

                    $(".EXCEL").each(function ()
                    {
                            orginal_csv_url = $(this).attr("href");
                            print_url = orginal_csv_url;
                            print_url = decodeURI(print_url);
                            print_url += "&temp_file_name=" + temp_file_name;
                            print_url = encodeURI(print_url);
                            $(this).attr("href", print_url);
                        
                    });
                },
                complete: function () {
                    // Reset currentSaveOutputRequest once the request completes
                    currentSaveOutputRequest = null;
                }
            });
        }

        $('#PaymentTable').removeClass('is-loading');

    });

    var table = $('.reports_currency').get(0);
    var $thead = $(table).find('thead');
    if ($thead.length) {
        window.addEventListener('scroll', function() {
            var headerHeight = $('.header').length && $('.header').get(0).clientHeight || 0;
            var pageHeadHeight = $('.pages-head').length && $('.pages-head').get(0).clientHeight || 0;
            var theadHeight = $thead.get(0).clientHeight || 0;
            var theadPaddingHeight = 16;
            var offset = pageHeadHeight + headerHeight + theadHeight - 32;
            if((table.offsetTop - (pageHeadHeight > 0 ? pageHeadHeight : 0)) - window.scrollY <= 0){
                $thead.addClass('fixed-offset');
                $thead.get(0).style.setProperty("--listing-table-thead-offset", (window.scrollY - table.offsetTop - headerHeight + offset) + 'px');
            } else {
                $thead.removeClass('fixed-offset');
                $thead.get(0).style.setProperty("--listing-table-thead-offset", "0px");
            }
        });
    }
</script>
<?php ?>
