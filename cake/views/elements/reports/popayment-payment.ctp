<?php
debug($reportData);
?>
<div class="report">
    <?php
    $dateFormats = getDateFormats('std');
    $dateFormat = $dateFormats[getAuthOwner('date_format')];
    $totals = array();
    $is_summary = $params['is_summary'];
    $show_staff = $params['group_by'] != 'staff' && check_permission(View_All_Purchase_Orders) && ifPluginActive(StaffPlugin);
    $show_supplier = $params['group_by'] != 'supplier';
    $show_date = $params['group_by'] != 'daily';
    $colspan = 5 + $show_staff + $show_supplier + $show_date;
    foreach ($reportCurrencies as $cc => $currency) {
        $total = $paid = $unpaid = 0;
        $st_totals = array();
        $st_groups = array();
        ?>


        <div class="subreport">
            <div class="row chart-wrap">
                <?php $draw_pie_methods = ($params['group_by'] != 'payment_method' && count($reportData[$currency]['methods'] ?? []) > 1 ? true : false) ?>
                <div class=" col-md-<?php echo $draw_pie_methods ? '8' : '12' ?> col-sm-12" id="chart_<?php echo $currency ?>"></div>
                <?php if ($draw_pie_methods) { ?>
                    <div class="col-md-4 col-sm-12" id="pie_chart_<?php echo $currency ?>"></div>
                <?php } ?>

            </div>
            <br/>
            <?php if (!empty($_GET['quick'])) ob_start(); ?>
            <?php if (empty($is_summary)) { ?>
                <table cellspacing="0" cellpadding="4" width="100%" class="report reports_currency no-collapse fixed-table-head">
                    <thead>
                        <tr class="report-results-head">
                            <th   class="first-column no-sort"><?php __("ID") ?></th>
                            <th   class="first-column no-sort"><?php __("Invoice No.") ?></th>
                            <?php if ($show_date) { ?>
                                <th   class="no-sort"><?php __("Date") ?></th>
                            <? } ?>
                            <?php if ($show_supplier) { ?>
                                <th   class="no-sort"><?php __("Supplier") ?></th>
                            <? } ?>
                            <?php if ($show_staff) { ?>
                                <th   class="no-sort"><?php __("Invoiced By") ?></th>
                            <? } ?>
                            <th   class="no-sort no-sort"><?php __("Method") ?></th>
                            <th   class="no-sort"><?php __("Ref") ?></th>
                            <th   class="no-sort"><?php __("Total") ?> (<?php echo $currency ?>)</th>
                        </tr>
                    </thead>
                <? } else { ?>
                    <table cellspacing="0" cellpadding="4" width="100%" class="report reports_currency no-collapse fixed-table-head">
                        <thead>
                            <tr class="report-results-head">
                                <th   class="first-column no-sort"><?php echo $titles['column_title'] ?></th>
                                <th   class="no-sort"><?php __("Total") ?> (<?php echo $currency ?>)</th>
                            </tr>
                        </thead>
                    <? } ?>
                    <tbody>
                        <?php
						
                        foreach ($reportData[$currency] as $group => $subData) {
                           
                            
                            $subtotal = 0;
                            $group_title = $group;
                            if ($params['group_by'] == 'staff' || $params['group_by'] == 'collected_by')
                                $group_title = $staffs[$group];
                            if ($params['group_by'] == 'payment_method')
                                $group_title = $paymentMethods[$group];

                            if ($params['group_by'] == 'weekly' || $params['group_by'] == 'daily')
                                $group_title = format_date($group);
                            if (empty($group_title))
                                $group_title = '[' . __('REMOVED', true) . ']';
                            ?>
                            <?php if (empty($is_summary)) { ?>
                                <tr class="empty-row">
                                    <td class="empty-row" colspan="<?php echo $colspan ?>">&nbsp</td>
                                </tr>
                                <tr class="sub-head">
                                    <td colspan="<?php echo $colspan ?>" class="sub-head"><?php echo $group_title ?></td>
                                </tr>
                            <? } ?>
                            <?php
							
							
                            foreach ($subData as $payment) {

                                $subtotal+=round($payment['amount'], 2);
                                ?>
                                <?php if (empty($is_summary)) { ?>
                                    <tr class="indent-td">
                                        <td><?php echo $payment['id'] ?></td>
                                        <td>
                                            <?php 
                                                echo ($payment['no']) 
                                                    ? "<a href='/owner/purchase_invoices/view/" . $payment['purchase_order_id'] . "'>" . $payment['no'] . "</a>" 
                                                    : $payment['no']; 
                                            ?>
                                        </td>
                                        <?php if ($show_date) { ?>
                                            <td><?php echo format_date($payment['date']); ?></td>
                                        <? } ?>
                                        <?php if ($show_supplier) { ?>
                                            <td><?php echo $payment['supplier_business_name'] ?></td>
                                        <? } ?>
                                        <?php if ($show_staff) { ?>
                                            <td><?php echo $staffs[$payment['staff_id']] ?></td>
                                        <? } ?>
                                        <td> <?php echo empty($paymentMethods[$payment['payment_method']]) ? $payment['payment_method'] : $paymentMethods[$payment['payment_method']]; ?></td>
                                        <td><?php echo $payment['transaction_id'] ?></td>
                                        <td><?php echo format_price_simple($payment['amount'], $currency) ?></td>
                                    </tr>
                                <? } ?>
                                <?php
                            }
                            $total+=$subtotal;
                           if($group!='methods'){
                            $st_totals[] = $subtotal;
                            
                            if ($is_periodic){
                                $st_groups[] = $group . ($params['group_by'] == 'monthly' ? '-01' : '');
                            }else{
                                $st_groups[] = $group_title;
                            }
//                         
//                                
//                            unset($st_groups[count($st_groups)-1]);    
//                            unset($st_totals[count($st_totals)-1]);
//                            }
                           }
//                            
                            ?>
                            <?php if (empty($is_summary)) { ?>
                                <tr class="subtotal">
                                    <td colspan="<?php echo $colspan - 1 ?>"><?php echo __('Subtotal', true) ?></td>
                                    <td class=""><?php echo format_price_simple($subtotal, $currency) ?></td>
                                </tr>
                                <tr class="empty-row">
                                    <td class="empty-row" colspan="<?php echo $colspan ?>">&nbsp</td>
                                </tr>
                            <? } else { ?>
                                <tr>
                                    <td><?php echo $group_title ?></td>
                                    <td><?php echo format_price_simple($subtotal, $currency) ?></td>
                                </tr>
                            <? } ?>



                        <?php } ?>

                        <tr class="section-net">
                            <?php if (empty($is_summary)) { ?>
                                <td colspan="<?php echo $colspan - 1 ?>" class="first-column"><?php printf(__("NET (%s)", true), $currency) ?></td>
                                <td><?php echo format_price($total, $currency) ?></td>
                            <? } else { ?>
                                <td  class="first-column"><?php printf(__("NET (%s)", true), $currency) ?></td>
                                <td><?php echo format_price($total, $currency) ?></td>
                            <? } ?>
                        </tr>
                    </tbody>

                </table>
                <?php if (!empty($_GET['quick'])) ob_end_clean(); ?>
        </div>
        <br/>
        <?
        if ($this->params['url']['no_graph'] != "1") {
            ?>
            <?php
            $chart_params = array(
                'title' => $titles['graph_title'] . ' (' . $currency . ')',
                'chartType' => ($is_periodic ? 'area' : 'bar'),
                'group' => $params['group_by'],
                'xLabels' => $st_groups,
                'values' => array('Total'=>$st_totals));
         foreach($st_groups as $key=>$values){   
         $current_array[$values]=$st_totals[$key];
         }
        
        
        
        if (in_array($this->params['url']['group_by'], array("xdaily","xweekly","xmonthly"))) {
             function back_to_date($n){
             return date("Y-m-d",$n);    
         }
            foreach ($chart_params['xLabels'] as $key=>&$v){
            $v=  strtotime($v) ;   
            }
            sort($chart_params['xLabels'],SORT_NUMERIC);
            
         foreach($chart_params['xLabels'] as $key=>$vvv){
         $chart_params['xLabels'][$key]=back_to_date($vvv);
             
         }
          
      
      
          unset($chart_params['values']['Total']);
          foreach ($chart_params['xLabels'] as $xkey=>$xv){
         $chart_params['values']['Total'][$xkey]=$current_array[$xv];
          }         
         
        }
            

            echo $this->element('reports/charts', array('div' => 'chart_' . $currency, 'jsonParams' => $chart_params));

            if ($draw_pie_methods) {
                $lablels = array();
                $values = array();
                if (isset($reportData[$currency]['methods']) && is_iterable($reportData[$currency]['methods']))
                foreach ($reportData[$currency]['methods'] as $method => $amount) {
                    $lablels[] = $paymentMethods[$method] ? $paymentMethods[$method] : $method;
                    $values[] = ($amount);
                }
                $chart_params = array(
                    'title' => __('Payment Methods', true) . ' (' . $currency . ')',
                    'chartType' => 'pie',
                    'xLabels' => $lablels,
                    'values' => array($values),
                );
                echo $this->element('reports/charts', array('div' => 'pie_chart_' . $currency, 'jsonParams' => $chart_params));
            }



            //echo $this->element('reports/charts', array('div' => 'pie_'.$currency, 'not_first'=>$cc>0, 'jsonParams'=>$chart_params)); 	
        }
    }
    ?>
</div>
