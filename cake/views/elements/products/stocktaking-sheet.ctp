<?php
//name, Software Count, Physical count, Discrepancies, Remarks
$col_count=5;
$show_barcode=false;
$show_category=false;
$show_brand=false;
$softwareCountTotal = 0;
$new_products = [];
foreach($products as $product)
{
	$last_group_by='';
	
	if(!$show_barcode&&!empty($product['Product']['barcode']))
	{
		$show_barcode=true;
		$col_count++;
	}
	if(!$show_category&&$data['group_by']!='category')
	{
		$show_category=true;
		$col_count++;
	}
	if(!$show_brand&&$data['group_by']!='brand'&&!empty($product['Product']['brand']))
	{
		$show_brand=true;
		$col_count++;
	}
	

		if($data['group_by'] == 'category') 
		{
			$new_products[$product['ItemsCategories'][0]['category_id']][] = $product;
		}
		
	
}

    if($data['group_by'] == 'category') {

        // Sort products based on categories sort (ASC)
        $sortedProducts = [];
        foreach ($categories as $id => $category) {
            if (!empty($new_products[$id])) {
                $sortedProducts[$id] = $new_products[$id];
            }
        }

		unset($products);
		foreach($sortedProducts as $k => $new_product) {
			foreach($new_product as $k2 => $product)
				$products[] = $product ;
		}
		unset($sortedProducts, $new_products);
    }
?>
<div class="report">
	<table  cellspacing="0" cellpadding="4" width="100%" class="thickreport report reports_currency no-collapse fixed-table-head">
			<tr class="report-results-head">
				<th   class="no-sort" data-column-name="product_code"><?php __("Code") ?> <i class="fa fa-sort"></i></th>
				<th   class="no-sort" data-column-name="name"><?php __("Name") ?> <i class="fa fa-sort"></i></th>
				<?php if($show_barcode) {?> 
				<th   class="first-column no-sort" data-column-name="product_code"><?php __("Barcode") ?> <i class="fa fa-sort"></i></th>
				<? } ?>
				<?php if($show_category) {?> 
				<th   class="first-column no-sort" data-column-name="category"><?php __("Category") ?> <i class="fa fa-sort"></i></th>
				<? } ?>
				<?php if($show_brand) {?> 
				<th   class="first-column no-sort" data-column-name="brand"><?php __("Brand") ?> <i class="fa fa-sort"></i></th>
				<? } ?>
				<th width="150"    class="no-sort" data-column-name="stock_balance"><?php __("Software Count") ?> <i class="fa fa-sort"></i></th>
				<th  width="100"   class="no-sort" data-column-name="quantity"><?php __("Quantity") ?> <i class="fa fa-sort"></i></th>
				<th  style="min-width:170px;" class="no-sort"><?php __("Remarks") ?> </th>
			</tr>


		<?php foreach($products as $i=>$product) {
            if($this->data['category'] && !$product['ItemsCategories'][0]['id']){
				continue ;
			}
			?>
			<?php
            if(
            (($data['group_by'] != 'category' && !empty($data['group_by'])&&$last_group_by!=$product['Product'][$data['group_by']]) ||
                ($data['group_by'] == 'category' && $last_group_by!=$categories[$product['ItemsCategories'][0]['category_id']])) &&
                $showTotalCount
            ) {
                // this condition could be remove !($data['group_by'] == 'category'&& $product === $products[0]) but it causes a crazy issue which put an empty total count in case of group by category
                if (!($data['group_by'] == 'category'&& $product === $products[0])) {
                    ?>
                    <tr>
                        <td colspan="<?php echo $col_count - 3 ?>"><b><?= __("Total Count") ?></b></td>
                        <td colspan="3"><b><?php echo $softwareCountTotal; ?></b></td>
                    </tr>
                    <?
                    $softwareCountTotal = 0;
                }
            }
            if($data['group_by'] != 'category'){
            if(!empty($data['group_by'])&&$last_group_by!=$product['Product'][$data['group_by']]) {
                $last_group_by=$product['Product'][$data['group_by']];
                ?>
                	<tr class="empty-row">
						<td class="empty-row" colspan="<?php echo $col_count ?>">&nbsp;</td>
					</tr>
					<tr class="sub-head">
							<td colspan="<?php echo $col_count ?>" class="sub-head"><?php echo $product['Product'][$data['group_by']]  ?></td>
					</tr>
			<? }
			}else {
			    if($last_group_by!=$categories[$product['ItemsCategories'][0]['category_id']])
				{
                    $last_group_by=$categories[$product['ItemsCategories'][0]['category_id']];
            ?>
                    <tr class="empty-row">
						<td class="empty-row" colspan="<?php echo $col_count ?>">&nbsp;</td>
					</tr>
					<tr class="sub-head">
							<td colspan="<?php echo $col_count ?>" class="sub-head"><?php echo $last_group_by  ?></td>
					</tr>
					<?php
				}
			}
?>
			<tr class="<?php echo $i%2==0?'even':''?>" height="15px;" data-product-id="<?php echo $product['Product']['id']; ?>">
				<td><?php echo empty($product['Product']['product_code'])?$product['Product']['id']:$product['Product']['product_code'] ?></td>
				<td><?php echo $product['Product']['name'] ?></td>
				<?php if($show_barcode) {?> 
				<td><?php echo str_replace(' ', '&nbsp;', $product['Product']['barcode']) ?></td>
				<? } ?>
				<?php if($show_category) {
					
						?> 
				<td><?php 
				$categories_text = [];
				foreach($product['ItemsCategories'] as $k => $item_category){
					$categories_text[] = $categories[$item_category['category_id']] ;
					}
				echo implode(' - ', $categories_text);  
				
				?></td>
				<? } ?>
				<?php if($show_brand) {?> 
				<td><?php  echo $product['Product']['brand'] ?></td>
				<? } ?>
				<td><?php 
                                if (isset ($have_store) ){
                                    $balance = $product[0]['all_balance']?$product[0]['all_balance']: 0 ;
                                }else {
                                    $balance = $product['Product']['stock_balance']?$product['Product']['stock_balance']:0;
                                }
                                $softwareCountTotal +=$balance;
                                $extra = "";
                                if ( $enable_multi_units && !empty ( $product['factor']['factor']))
                                {
                                    $balance = $balance / $product['factor']['factor'];
                                    $extra = " ".  $product['factor']['factor_small_name'];
                                }
                                echo format_number ( $balance  ).$extra  ; ?></td>
				<td style="padding:0px;"><textarea rows="1" type="text" style="padding:0px;margin:0px; height:100%;border:solid 0px #fff;width:100%;" ></textarea></td>
				<td  style="padding:0px;"><textarea rows="1" type="text" style="padding:0px;margin:0px; height:100%;border:solid 0px #fff;width:100%;" /></textarea></td>
			</tr>
        <?php } ?>
        <?php if ($showTotalCount) {?>
            <tr>
                <td colspan="<?php echo $col_count - 3 ?>"><b><?=__("Total Count")?></b></td>
                <td colspan="3"><b><?php echo $softwareCountTotal;?></b></td>
            </tr>
            <?php
        }
        ?>
		
</table>
</div>
<style>
 .thickreport th {cursor: pointer;}
</style>
<script>
	const getCellValue = (tr, idx) => tr.children[idx].innerText || tr.children[idx].textContent;

	// Custom comparator for alphanumeric values
	const alphanumericComparer = (v1, v2) => {
		// Handle empty values
		if (v1 === '' || v2 === '') {
			return v1.toString().localeCompare(v2);
		}
		
		// Check if both are regular numbers
		if (!isNaN(v1) && !isNaN(v2)) {
			return v1 - v2;
		}
		
		// Safely extract numeric parts (default to 0 if no numbers found)
		const numPart1 = parseFloat((v1.match(/-?[\d,]+\.?\d*/) || [0])[0].replace(/,/g, ''));
		const numPart2 = parseFloat((v2.match(/-?[\d,]+\.?\d*/) || [0])[0].replace(/,/g, ''));
		
		// If numeric parts are different, compare them
		if (numPart1 !== numPart2) {
			return numPart1 - numPart2;
		}
		
		// Safely extract letter parts (default to empty string if no letters found)
		const letterPart1 = (v1.match(/[A-Za-z]+/) || [''])[0];
		const letterPart2 = (v2.match(/[A-Za-z]+/) || [''])[0];
		
		// If both values are identical, return 0
		if (v1 === v2) {
			return 0;
		}
		
		// Compare letter parts or fall back to string comparison
		const letterComparison = letterPart1.localeCompare(letterPart2);
		return letterComparison !== 0 ? letterComparison : v1.localeCompare(v2);
	};

	// Modified comparer function
	function comparer(index, asc) {
		return function(a, b) {
			const aValue = a.children[index].textContent.trim();
			const bValue = b.children[index].textContent.trim();
			
			// Use custom alphanumeric comparison
			const comparison = alphanumericComparer(aValue, bValue);
			
			return asc ? comparison : -comparison;
		};
	}

    const updateExportLinks = (sortColumn, sortDirection) => {
	const exportLinks = document.querySelectorAll('.op-export, .Pdf');
		exportLinks.forEach(link => {
			const url = new URL(link.href);
			url.searchParams.set('sort_column', sortColumn);
			url.searchParams.set('sort_direction', sortDirection);
			link.href = url.toString();
		});
	};	

	// do the work...
	document.querySelectorAll('th').forEach(th => th.addEventListener('click', (() => {
		const table = th.closest('table');
		Array.from(table.querySelectorAll('tr:nth-child(n+2)'))
			.sort(comparer(Array.from(th.parentNode.children).indexOf(th), this.asc = !this.asc))
			.forEach(tr => table.appendChild(tr) );
			const sortColumn = th.getAttribute('data-column-name');
		    updateExportLinks(sortColumn, this.asc ? 'asc' : 'desc');

	})));
</script>

 
<script>
// This code added to handle values added in 2 texetareas (quantity & Remarks). 
const textareaValues = {};

function handleTextareaChange(event) {
    const textarea = event.target;
    const row = textarea.closest('tr');
    const productId = row.getAttribute('data-product-id');
    const colIndex = Array.from(row.cells).indexOf(textarea.parentNode);

    if (!textareaValues[productId]) {
        textareaValues[productId] = {};
    }

	if (colIndex === 6) {
        textareaValues[productId].quantity = textarea.value;
    } else if (colIndex === 7) {
        textareaValues[productId].note = textarea.value;
    }
}

document.querySelectorAll('tr td:nth-child(7) textarea, tr td:nth-child(8) textarea')
    .forEach(textarea => {
        textarea.addEventListener('input', handleTextareaChange);
    });

document.querySelector('.Pdf').addEventListener('click', function(event) {
    event.preventDefault();

    const encodedObject = encodeURIComponent(JSON.stringify(textareaValues));

    const baseUrl = event.currentTarget.getAttribute('href');

    const newUrl = `${baseUrl}&textareaValues=${encodedObject}`;

    window.location.href = newUrl;
});

</script>	