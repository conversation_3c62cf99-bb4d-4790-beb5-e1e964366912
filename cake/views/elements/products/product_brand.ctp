<?php   
	$Brand = ClassRegistry::init('Brand');
	$found_brands = [];

	if (!empty($_GET['brand_id'])) {
		$value = mysql_escape_string($_GET['brand_id']); 
        $brands = $Brand->find('list' , ['conditions' => ['Brand.id' => $value]]); 
        foreach ($brands as $brandId => $brandName) {
			$found_brands[$brandId] = $brandName;
		}
	}

    if (empty($_GET['brand_id'])) { 
	    $brands = $Brand->find('all',['limit' => 20]);
        foreach ($brands as $brand) {
			$found_brands[$brand['Brand']['id']] = $brand['Brand']['name'];
        }
    }

    $disableWrapper = isset($disableWrapper) ? $disableWrapper : false;

	$options = [
		'label' => isset($label) ? $label : __('Brand', true),
		'id' => 'brand_selectpicker',
		'class' => 'selectpicker',
		'data-live-search' => 'true',
		'type' => 'select',
		'data-width' => !empty($width) ? $width : 'auto',
		'div' => isset($div) ? $div : false,
		'empty' => __('[Any Brand]', true),
		'name' => 'brand_id',
		'options' => array_filter($found_brands),
		'value' => !empty($_GET['brand_id']) ? $_GET['brand_id'] : null,
	];


	if (isset($multiple) && $multiple) {
		$options['multiple'] = true;
	}
	echo $javascript->link(array('bootstrap-select.js'));
	echo $html->css(array('bootstrap-select'));

	$options['class'].= ' with-ajax';
	echo $javascript->link(array('ajax-bootstrap-select.min.js'));
	echo $html->css(array( 'ajax-bootstrap-select'));
?>
    <script type="text/javascript">
        var brand_ajax_options = {
            ajax: {
                url: '/v2/api/entity/brand/list',
                type: 'get',
                dataType: 'json',
                data: {
                    filter: {
                        name: {
                            like: '{{{q}}}'
                        }
                    }
                }
            },
            locale: {
                emptyTitle: '<?php __('[Any Brand]') ?>',
                searchPlaceholder: '<?php __('enter brand') ?>',
            },
            preprocessData: function (data) {
                array = [];
                for (id in data.data) {
                    array.push({
                        text: data.data[id].name,
                        value: data.data[id].id,
                    });
                }
                return array;
            }
        };
        $(function () {
            $('#brand_selectpicker').selectpicker().filter('.with-ajax').ajaxSelectPicker(brand_ajax_options);
        })
    </script>

    <?php if (isset($full_width)) { ?>
        <style>
            .btn-group.bootstrap-select.with-ajax {
                width: 100% !important;
            }
        </style>
    <?php } ?>
<?php
$input = $form->input(isset($input_name) ? $input_name : 'brand_id', $options);
?>
<?php if(!$disableWrapper) {
    ?>
<div class="form-group <?php if (!isset($full_width)) { echo 'col-md-3 col-sm-4 col-xs-6'; } ?> text title primary-actions">

<?php
echo $input;

?>
</div>
<?php } else {
    echo $input;
}
