<?php
if($row['StockTransaction']['source_type']==StockTransaction::SOURCE_PO)
	$url=Router::url(array('controller'=>'purchase_invoices','action'=>'view',$row['StockTransaction']['order_id']));
elseif($row['StockTransaction']['source_type']==StockTransaction::SOURCE_PR)
	$url=Router::url(array('controller'=>'purchase_invoices','action'=>'view_refund',$row['StockTransaction']['order_id']));
elseif($row['StockTransaction']['source_type']==StockTransaction::SOURCE_PDN)
	$url=Router::url(array('controller'=>'purchase_invoices','action'=>'view_debit_note',$row['StockTransaction']['order_id']));

elseif($row['StockTransaction']['source_type']==StockTransaction::SOURCE_INVOICE)
	$url=Router::url(array('controller'=>'invoices','action'=>'view',$row['StockTransaction']['order_id']));
elseif($row['StockTransaction']['source_type']==StockTransaction::SOURCE_RR)
	$url=Router::url(array('controller'=>'invoices','action'=>'view_refund',$row['StockTransaction']['order_id']));
elseif($row['StockTransaction']['source_type']==StockTransaction::SOURCE_CN)
	$url=Router::url(array('controller'=>'invoices','action'=>'view_creditnote',$row['StockTransaction']['order_id']));
elseif($row['StockTransaction']['source_type']==StockTransaction::SOURCE_BUNDLE)
	$url=Router::url(array('controller'=>'products','action'=>'view',$row['StockTransaction']['ref_id']));
elseif(in_array($row['StockTransaction']['source_type'],StockTransaction::getSourceRequisition()))
	$url=Router::url(array('controller'=>'requisitions','action'=>'view',$row['StockTransaction']['order_id']));
else
	$url=Router::url(array('controller'=>'products','action'=>'edit_stock_transaction',$row['StockTransaction']['id']));
//	$url=Router::url(array('controller'=>'stock_transactions','action'=>'edit',$row['StockTransaction']['id']));

?>
<style>
  .tracking-number-link:hover{
    color:#3a3e63;
  }
</style>
<?php if($index==0) {?>
<li   class="<?php if(!empty($row['StockTransaction']['ignored'])) { ?>ignored<?php }?> day-view-entry no-hover">
			
            <table id="header" cellspacing="0" cellpadding="0" border="0">
              <tbody>
                <tr>
                  <td width="50%">
				   <?php __('Transaction') ?>
				  </td>
					<td style="" class="entry-time non-bold">
					    <?php __('Move') ?>
					</td>
                                 <?php if ( $type== 'product' || $type == 'tracking_number' ) {?>
                                  <td class="entry-time non-bold">
					<?php __('Stock Level After') ?>
				  </td>
                                 <?php } if(check_permission(Adjust_Product_Inventory)&& !$is_report){ ?>
                  <td class="small-actions">&nbsp;
				  </td> 
                                 <?php }  ?>
                </tr>
              </tbody>
            </table>
          </li>
<?php } ?>
<li   class="<?php if(!empty($row['StockTransaction']['ignored'])) { ?>ignored<?php }?> day-view-entry">
			
            <table cellspacing="0" cellpadding="0" border="0">
              <tbody>
                <tr>
                  <td width="50%">
				  <div  class="invoice-row entry-info">
					
                      
					  <div class="project-client" ><span class="project"><span class="expense-date"> <?php echo format_date($row['StockTransaction']['received_date']).' '.date('H:i',strtotime($row['StockTransaction']['received_date'])); ?> (#<?php echo $row['StockTransaction']['id']; ?>)</span> </span>  </div>
					  <div ><a href="<?php echo $url ?>" target="_blank"> <span class="project">
            <?php 
              echo $row['StockTransaction']['transaction_category_id'] ? 
                    $cat_list[$row['StockTransaction']['transaction_category_id']] : 
                    $sources[ $row['StockTransaction']['source_type']]; ?> 
                                              <?php if(!empty($row['StockTransaction']['order_no'])){?>(#<?php echo $row['StockTransaction']['order_no'] ?>)<?php } ?>
                                              <?php if(!empty($row['StockTransaction']['bundle_product_name'])){?> - <?php echo $row['StockTransaction']['bundle_product_name'] ?><?php } ?>
                                                  </span>  </a>
                          <?php
                          $isCompound=$product['Product']['bundle_type'] ==Settings::OPTION_BUNDLE_COMPOUND;
                          if($product['Product']['type'] == Product::BUNDLE_TYPE && (($isCompound && $row['StockTransaction']['transaction_type'] == StockTransaction::TRANSACTION_OUT) || (!$isCompound && $row['StockTransaction']['transaction_type'] == StockTransaction::TRANSACTION_IN ))) {
                              ?>
                              <a title="<?= __('Bundle Transaction Details',true) ?>" class="bundle-details btn edit-btn  btn-  btn-xs" href="<?= Router::url(['controller' => 'stock_transactions', 'action' => 'bundle_transaction_details', $row['StockTransaction']['id']]) ?>">
                                  <span><i class="fas s2020 fa-code-branch"></i></span>
                              </a>
                          <?php } ?>
                      </div>
					  
					  
					  
					  <div class="task-notes"> 
									<?php   if (!empty($row['StockTransaction']['notes'])) {
                                        echo nl2br($row['StockTransaction']['notes']);
                                        ?><div class="clearfix"></div>
                                    <?php } ?>
						</div>
                      
					   <?php  if($row['StockTransaction']['added_by']!=$staff_id) { ?>
                      <ul class="meta-details">
                      	<li>
                            <div class="added-by">
                              <span class="added-by-label"><i class="fa fa-user"></i>  <?php __('By') ?>: </span>
                              <span class="added-by-value"><strong><?php echo $staffs[$row['StockTransaction']['added_by']] ?></strong></span>
                            </div>
                        </li>
                      </ul>
                       <?php  } ?>
                                          <?php if ($store_count > 1 ){?><div class="store_handle"><?php echo $row['from_to_stores'] ?></div><?php }?>

                      <!-- Here We Print Tracking Number Data and Url -->
                      <div class="store_handle">
                        <?php
                        if(isset($row['TrackingNumber'])){
                            $text = $row['TrackingNumber']['text'];
                            $url = $row['TrackingNumber']['url'];
                            echo "<a class='tracking-number-link' href='$url' target='_top'  style='color: #0000ff; text-decoration: none;'>$text</a>";
                        }
                        ?>
                      </div>
                      
                    </div></td>
					<td style="" class="entry-time">
					  <?php if(!empty($row['StockTransaction']['ignored'])) {
					  echo '<small><span class="text-danger">'.__('Ignored',true).'</span><br/></small>';
					  }
                                          /* base unit */
                                          $extras = " ".$unit_factor['UnitTemplate']['unit_small_name'] ?? '';
                                          $curr_quantity = $row['StockTransaction']['quantity'] ;
                                          if ( !empty($row['StockTransaction']['unit_small_name'])&& !empty($row['StockTransaction']['unit_factor']) ) {
//                                              $extras = " ".$unit_factor['UnitFactor']['small_name']; 
                                              $extras = " ".$row['StockTransaction']['unit_small_name']; 
//                                              $curr_quantity = $row['StockTransaction']['quantity'] / $factor ; 
                                              $curr_quantity = $row['StockTransaction']['quantity'] / $row['StockTransaction']['unit_factor'] ; 
                                          }
                                          ?>
                                            <span <?php if(!empty($row['StockTransaction']['ignored'])) {?>style="text-decoration: line-through;opacity:0.6"<?php }?>> <?php echo  (($row['StockTransaction']['source_type'] == StockTransaction::STATUS_TRANSFER )?$row['from_to_str'] :( $row['StockTransaction']['quantity']>=0?'<span class=" text-success">+</span> ':'<span class="text-danger">-</span> ' )).format_number(abs($curr_quantity)).$extras; ?></span><br/>

                        <?php if (check_permission(Add_New_Purchase_Orders)): ?>
                        <span  class="expense-currency">
						 <abbr class="initialism" title="<?php __('Price per Unit')?>">
						 <?php
							 $price = $row['StockTransaction']['price'] - $row['StockTransaction']['discount'];

							 if ($enable_multi_units && !empty($row['StockTransaction']['unit_factor'])) {
								 $price = $price * $row['StockTransaction']['unit_factor'];
							 }
							 echo format_price(round($price, 5), $row['StockTransaction']['currency_code'], false);
						 ?>
                         </abbr></span>
                        <?php endif; ?>
						 
						
					</td>
                      <?php if ( $type== 'product' || $type =='tracking_number' ) {?>
                            <td class="entry-time">
				  <?php 
                                  if ( $enable_multi_units ){
                                      echo format_number($row['StockTransaction']['stock_after'] / $factor).' '.$unit_factor['UnitFactor']['small_name'];
                                  }else{
                                      echo format_number($row['StockTransaction']['stock_after']);
                                  }
                                  ?>
                <?php if (check_permission(Add_New_Purchase_Orders)): ?>
				  <span  class="expense-currency">
						 <abbr class="initialism" title="<?php __('Average Unit Cost')?>">
						 <?php
                                                 $price = $row['StockTransaction']['purchase_price'];
                                                 if (!empty($factor) && $enable_multi_units){
                                                     $price *= $factor ;
                                                 }
                                                 echo format_price(round($price, 5),$default_currency, false); ?></abbr>
                  </span>
                <?php endif; ?>
						 
						
				  </td>
                      <?php }?>

                    <td class="small-actions">&nbsp;
				 <?php if(check_permission(Adjust_Product_Inventory) && !$is_report && (!ifPluginActive(PRODUCT_TRACKING_PLUGIN) || $row['Product']['tracking_type'] == \App\Utils\TrackStockUtil::QUANTITY_ONLY) ){ ?>

                                    <?php $id = $row['StockTransaction']['id'] ; $edit_action = 'edit_stock_transaction' ; $delete_action = 'delete_stock_transaction';
                                    if ( $row['StockTransaction']['source_type'] == StockTransaction::STATUS_TRANSFER){
//                                        die(var_dump($row['StockTransaction']));
                                        $id = $row['StockTransaction']['stock_transfer_id'] ; $edit_action = 'edit_transfer_stock' ; $delete_action = 'delete_transfer_stock' ;
                                    }?>
                     <?php if ($row['Product']['status'] == \Izam\Daftra\Common\Utils\ProductStatusUtil::STATUS_ACTIVE || is_null($row['Product']['status']) ) : ?>
				  <a href="<?php echo Router::url(array('controller'=>'products','action'=>$edit_action,$id)) ?>" title="<?php __('Edit') ?> / <?php __('Add Note') ?>" class="btn edit-btn  btn-  btn-xs "><span><i class="fa fa-pencil"></i></span></a>
				 <?php if(!empty($row['StockTransaction']['ignored'])) {?>
				  <a href="<?php echo Router::url(array('controller'=>'products','action'=>$delete_action,$id,1)) ?>" title="<?php __('Reactivate') ?> / <?php __('Delete') ?>" class="btn ignore-btn btn-  btn-xs unignore_stock"><span><i class="fa fa-check"></i></span></a>
				  <?php }  else { ?>
				  <a href="<?php echo Router::url(array('controller'=>'products','action'=>$delete_action,$id)) ?>" title="<?php __('Ignore / Delete') ?>" class="btn unignore-btn btn-  btn-xs ignore_stock"><span><i class="fa fa-times"></i></span></a>
                                    <?php }  ?>
                     <?php endif; ?>
                 <?php  } ?>
                    </td>
                </tr>
              </tbody>
            </table>
          </li>
