<?php

$all_place_holder=PlaceHolder::invoice_get_all_place_holders($invoice['Invoice']);
$all_place_holder += PlaceHolder::invoice_custom_field_place_holder($invoice);
$enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
foreach ($invoice['InvoiceItem'] as $k => $item2) {
    $item2['unit_price'] = (float)$item2['unit_price'];
    $item2['unit_factor'] = (float)$item2['unit_factor'];
    $item2['quantity'] = (float)$item2['quantity'];
    if ($enable_multi_units && !empty($item2['unit_factor']) && !empty($item2['unit_name'])) {
        $invoice['InvoiceItem'][$k]['quantity_written'] = ($item2['quantity']/$item2['unit_factor']).' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
        $invoice['InvoiceItem'][$k]['unit_price_factor'] = ($item2['unit_price']*$item2['unit_factor']);//.' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
//        $invoice['InvoiceItem'][$k]['unit_price'] = ($item2['unit_price']*$item2['unit_factor']);//.' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
    }else {
        $invoice['InvoiceItem'][$k]['quantity_written'] = format_number((float)$item2['quantity'] );
        $invoice['InvoiceItem'][$k]['unit_price_factor'] = $item2['unit_price'];
    }
    $tracking = Product::displayTrackingData($invoice['InvoiceItem'][$k]['Product']['tracking_type'], $invoice['InvoiceItem'][$k]['tracking_data']);
    $invoice['InvoiceItem'][$k]['item'] .= $tracking != ""? "<br/>".$tracking : "" ;
}
$extra_details=json_decode($invoice['Invoice']['extra_details'],true);
ob_start();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
		<title><?php printf(__('Invoice %s', true), '#' . $invoice['Invoice']['no']) ?></title>
		<?php
                $old_address=false;
		$isPdf = (!empty($this->params['url']['ext']) && $this->params['url']['ext'] == 'pdf') || !empty($pdf);
		if (!isset($site)) {
			$site = getCurrentSite();
		}
		
		$hide_description=true;
		foreach ($invoice['InvoiceItem'] as $item) {
			if(!empty($item['description']))
			{
				$hide_description=false;
				break;
			}
			
		}
		
		if($hide_description) $cols=1;
		else
		$cols=2;

        if ( $show_discount ){$cols ++ ; }

     $style = '<style type="text/css">' . file_get_contents(WWW_ROOT . 'css/common.css') .  PHP_EOL . file_get_contents(WWW_ROOT . 'css/invoice-template.css');
        if(CurrentSiteLang()=='ara'){
        $style .=PHP_EOL . file_get_contents(WWW_ROOT . 'css/invoice-template-ar.css');
        }
        $style .= '</style>';
        echo $style;
		
		$show_shipping=false;
		if($invoice['Invoice']['shipping_options']==2) 
		{
			$show_shipping=true; 
			$shipping_name=$invoice['Invoice']['client_business_name'];
			$shipping_address=$invoice['Invoice']['client_address1'].(empty($invoice['Invoice']['client_address2'])?'':'<br/>'.$invoice['Invoice']['client_address2']);
			$shipping_city=$invoice['Invoice']['client_city'];$shipping_state=$invoice['Invoice']['client_state'];$shipping_postal_code=$invoice['Invoice']['client_postal_code'];
			$shipping_country_code=$invoice['Invoice']['client_country_code'];
		} 
		else if(
		(empty($invoice['Invoice']['shipping_options'])&&(!empty($invoice['Invoice']['client_active_secondary_address'])))
		||$invoice['Invoice']['shipping_options']==3)
		{
			$show_shipping=true;
			$shipping_name=$invoice['Invoice']['client_secondary_name'];
			$shipping_address=$invoice['Invoice']['client_secondary_address1'].(empty($invoice['Invoice']['client_secondary_address2'])?'':'<br/>'.$invoice['Invoice']['client_secondary_address2']);
			$shipping_city=$invoice['Invoice']['client_secondary_city'];$shipping_state=$invoice['Invoice']['client_secondary_state'];$shipping_postal_code=$invoice['Invoice']['client_secondary_postal_code'];
			$shipping_country_code=$invoice['Invoice']['client_secondary_country_code'];
			
			if($shipping_name.$shipping_address.$shipping_city.$shipping_country_code=='')
			$show_shipping=false;
		 
		}
		
		$logo = false;
                $width = 200;
                $height = 0;
                if ($site['site_logo']) {
                    $logoPath = DS . 'files' . DS . 'images' . DS . 'site-logos' . DS . ($site['site_logo']);
                    $logo = 'https://' . $site['subdomain'] . str_replace(DS, '/', $logoPath);
                    list($img_width, $img_height, $type, $attr) =  getimagesize($logo);
                    if(!empty($img_width)&&!empty($img_height)) 
                    {
                            $generated_height=200/$img_width*$img_height;
                            if($generated_height>120)
                            {
                                     $width = 0;
                                     $height = 120;
                            }

                    }


                }
                if ($logo) {
                    $logo_code = '<img src="' . $logo . '" ' . (!empty($width) ? ' width="' . $width . '" ' : '') . (($height)? ' height="' . $height . '" ' : '') . '  />';
                }

                    $invoice_title=__('Estimate',true);
                ?>
	</head>
	<body>
		<div class="invoice-wrap">
			<div class="invoice-inner">
				<table width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td class="text-left" valign="top" >
                            <p>
                                <?php if (!empty($invoice_title) && !empty($logo_code)) { ?>
                                    <?php echo $logo_code; ?><br/><br/>
                                <? } ?>


                                <span class="bussines-name"><?php echo $site['business_name'] ?></span><br />
								 <?php if (!empty($site['bn1_label'])&&!empty($site['bn1'])): ?>
                                    <strong><?php echo $site['bn1_label'] ?>:</strong> <?php echo $site['bn1'] ?><br />
                                <?php endif; ?>
								<?php if (!empty($site['bn2_label'])&&!empty($site['bn2'])): ?>
                                    <strong><?php echo $site['bn2_label'] ?>:</strong> <?php echo $site['bn2'] ?><br />
                                    <?php endif; ?>
                                    <?php  if($old_address) {  ?>
                                    <?php echo $site['address1'] ?><br />
                                    <?php if (!empty($site['address2'])): ?>
                                        <?php echo $site['address2'] ?><br />
                                    <?php endif; ?>
                                    <?php echo !empty($site['city'])?$site['city'].(!empty($site['state'])?', ':''):'' ?><?php echo !empty($site['state'])?$site['state']:''?> <?php echo $site['postal_code'] ?><br />
                                    <?php } else { ?>
                                            <?php  echo $this->element('format_address_html',$site);  ?>
                                    <?php } ?>

                                    <?php echo $site['phone1']; ?>
                                    <?php if (!empty($invoice['Invoice']['client_country_code'])&&$invoice['Invoice']['client_country_code']!=$site['country_code']): ?>
                                    <br/><?php echo $ownerCountry ?><br />
                                    <?php endif; ?>
                            </p>
                        </td>
						<td class="text-right" valign="top">
							<?php if (empty($invoice_title) && !empty($logo_code)) { ?>
                                    <?php echo $logo_code; ?>
                                <?php } else if (!empty($invoice_title)) { ?>
                                    <h1><?php echo $invoice_title ?></h1>
                                    <?php
                                    } else {

                                    }
                                    ?> 
								<?php if($show_shipping) { ?>
								<br/><br/><br/>
								<?php echo $this->element('invoice-std/estimate_custom_fields',array('site'=>$site)); ?>
								<?php } ?>
						</td>
					</tr>
				</table>

				<div class="invoice-address">
					<table width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td width="50%"class="text-left" valign="top">
								<p>
									<strong><?php __('To') ?>:</strong><br/>
									<?php echo $invoice['Invoice']['client_business_name'] ?><br />
									<?php   
									if(!empty($invoice['Invoice']['client_first_name'])||!empty($invoice['Invoice']['client_last_name'])) {
									echo (!empty($invoice['Invoice']['client_first_name'])?$invoice['Invoice']['client_first_name'] . " ":'') . $invoice['Invoice']['client_last_name'] ?><br />
									<?php } ?>
									
									<?php if($old_address) {  ?>
										<?php if(!empty($invoice['Invoice']['client_address1'])) {
										echo $invoice['Invoice']['client_address1'] ?><br />
										<?php } if (!empty($invoice['Invoice']['client_address2'])): ?>
											<?php echo $invoice['Invoice']['client_address2'] ?><br />
										<?php endif; ?>
										<?php if(!empty($invoice['Invoice']['client_city'])||!empty($invoice['Invoice']['client_state'])||!empty($invoice['Invoice']['client_postal_code'])) { ?>
										<?php echo (!empty($invoice['Invoice']['client_city'])?$invoice['Invoice']['client_city']. ', ':''), $invoice['Invoice']['client_state'], ' ', $invoice['Invoice']['client_postal_code'] ?><br />
										<?php } ?>
									<?php } else { ?>
										<?php echo $this->element('format_address_html',array('address1'=>$invoice['Invoice']['client_address1'],'address2'=>$invoice['Invoice']['client_address2'], 'city'=>$invoice['Invoice']['client_city'],'state'=>$invoice['Invoice']['client_state'],'postal_code'=>$invoice['Invoice']['client_postal_code'],'country_code'=>$invoice['Invoice']['client_country_code'])); ?>
									<?php } ?>
									
									<?php if (!empty($invoice['Invoice']['client_country_code'])&&$invoice['Invoice']['client_country_code']!=$site['country_code']): ?>
										<?php echo $clientCountry ?><br />
									<?php endif; ?>
								</p>
							</td>
							<td width="50%" valign="top" class="text-right">
								<?php  if($show_shipping) { ?>
								 <table border="0" cellspacing="0" class="float-right"  cellpadding="0"><tr><td class="text-left">
								<div id="shipping_info">
								<p>
                                                                <strong><?php __('Ship To') ?>:</strong><br/>
                                                                                                    <?php if(!empty($shipping_name )) { ?>
                                                                <?php echo $shipping_name ?><br />
                                                                    <?php } ?>

                                                                    <?php  if($old_address) {  ?>
                                                                            <?php if(!empty($shipping_address)) { ?>
                                                                            <?php echo $shipping_address?></br>
                                                                            <? } ?>
                                                                            <?php if (!empty($shipping_city) || !empty($shipping_state) || !empty($shipping_postal_code)) { ?>
                                                                                    <?php echo (!empty($shipping_city) ? $shipping_city . ', ' : ''), $shipping_state, ' ', $shipping_postal_code ?><br />
                                                                            <?php } ?> 
                                                                        <?php } else { ?>
                                                                            <?php echo $this->element('format_address_html',array('address1'=>$shipping_address, 'city'=>$shipping_city,'state'=>$shipping_state,'postal_code'=>$shipping_postal_code,'country_code'=>$shipping_country_code)); ?>
                                                                    <?php } ?>

                                                                    <?php if (!empty($shipping_country_code)&&($shipping_country_code!=$site['country_code']||$shipping_country_code!=$invoice['Invoice']['client_country_code'])) { ?>
                                                                            <?php echo $shippingCountry ?><br />
                                                                    <?php } ?>  
                                                            </p>
								</div>
								</td></tr></table>
								<?php } else { ?> 
                                                                <?php echo $this->element('invoice-std/estimate_custom_fields',array('site'=>$site)); ?>
								<? } ?>
							</td>
						</tr>
					</table>
				</div>
				<table cellspacing="0" cellpadding="2" border="0" width="100%" id="listing_table" class="invoice-listing-table total-table" style="">
					<?
                    if ($layout == '-1') {
                        ?>
                        <tr>
                            <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Item") ?></th>
                            <? if (!$hide_description) { ?>
                                <th width="210" bgcolor="#E5E5E5"><?php __("Details") ?></th>
    <? } ?>
                            <th width="80" bgcolor="#E5E5E5"><?php __("Hour Rate") ?></th>
                            <th width="40" bgcolor="#E5E5E5"><?php __("Hours") ?></th>
                            <?php if ($show_discount) {?>
                                <th id="label_item_discount" class="count-cell"><span><?php __("Discount") ?></span></th>
                            <?php }?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Subtotal") ?></th>
                        </tr>
                        <?
                    } else {
                        ?>
                        <tr>
                            <th width="" bgcolor="#E5E5E5" style="border-left:1px solid #555;"><?php __("Item") ?></th>
                            <? if (!$hide_description) { ?>
                                <th width="210" bgcolor="#E5E5E5"><?php __("Details") ?></th>
							<? } ?>
                            <th width="80" bgcolor="#E5E5E5"><?php __("Unit Price") ?></th>
                            <th width="40" bgcolor="#E5E5E5"><?php __("Qty") ?></th>
                            <?php if ($show_discount) {?>
                                <th id="label_item_discount" class="count-cell"><span><?php __("Discount") ?></span></th>
                            <?php }?>
                            <th width="60" bgcolor="#E5E5E5"><?php __("Subtotal") ?></th>
                        </tr>
                        <?
                    }
                    ?>

					<?php foreach ($invoice['InvoiceItem'] as $item): ?>
                        <tr>
                            <td width=""><?php echo nl2br(h($item['item'])) ?></td>
                            <? if (!$hide_description) { ?>
                            <td width="210"><?php echo nl2br(h($item['description'])) ?></td>
							<? } ?>
                            <td width="80"><?php echo format_price_simple($item['unit_price_factor'], $invoice['Invoice']['currency_code'], false) ?></td>
                            <td width="40"><?php echo $item['quantity_written'] ?></td>
                            <?php if ( $show_discount){?> <td width="80"><?php $item_subtotal = $item['item_subtotal'];
                                echo $item['discount_string'] ?></td>
                            <?}?>

                            <td width="60"><?php echo format_price_simple($item['item_subtotal'], $invoice['Invoice']['currency_code'], false) ?></td>
                        </tr>

                    <?php endforeach; ?>
						<?php if ($invoice['Invoice']['summary_subtotal']!=$invoice['Invoice']['summary_total']): ?>
						<tr>
							<td  style="border:none;" bgcolor="#FFF" colspan="<?=$cols?>"></td>
							<td colspan="2" style="border-left:none;border-right:none;" class="text-left"><strong><?php __("Subtotal") ?>:</strong></td>
							<td style="border-left:none;border-right:none;"class="text-left"><?php echo format_price($invoice['Invoice']['summary_subtotal'] + (!empty($invoice['Invoice']['adjustment_value']) ? $invoice['Invoice']['adjustment_value']  * -1 : 0), $invoice['Invoice']['currency_code'])  ?></td>
						</tr>
						<?php endif; ?>
						<?php
                        if(!empty($invoice['Invoice']['summary_total_discount'])) {$invoice['Invoice']['summary_discount']=$invoice['Invoice']['summary_total_discount'];$invoice['Invoice']['discount_amount']=1;}
                        if (!empty($invoice['Invoice']['summary_discount'])): ?>
                                        <tr>
                                            <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                            <td style="border-left:none;border-right:none;"  colspan="2" ><strong><?php empty($invoice['Invoice']['discount_amount'])? printf( __("Discount (%s)", true), round($invoice['Invoice']['discount'], 2) . '%'):__("Discount") ?>:</strong></td>
                                            <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['summary_discount'] * -1, $invoice['Invoice']['currency_code']) ?></td>
                                        </tr>
                        <?php endif; ?>
						<?php if (!empty($invoice['Invoice']['summary_deposit'])&&$invoice['Invoice']['summary_deposit']!=$invoice['Invoice']['summary_total']): ?>
							<tr>
								<td   style="border:none" bgcolor="#FFF" colspan="<?=$cols?>"></td>
								<td style="border-left:none;border-right:none;"  colspan="2" ><strong><?php printf(__("Advance payment", true))  ?>:</strong></td> 
								<td style="border-left:none;border-right:none;"class="text-left"><?php echo  format_price($invoice['Invoice']['summary_deposit'], $invoice['Invoice']['currency_code']) ?></td>
							</tr>
						<?php endif; ?>
						<?php
						if (!empty($invoice['InvoiceTax'])):
							foreach ($invoice['InvoiceTax'] as $tax):
								?>
								<tr>
									<td  style="border:none" bgcolor="#FFF" colspan="<?=$cols?>"></td>
									<td style="border-left:none;border-right:none;"  colspan="2"><strong><?php printf('%s (%s%%)', $tax['name'],round($tax['value'],2) ) ?>:</strong></td>
									<td style="border-left:none;border-right:none;"class="text-left"><?php echo format_price($tax['invoice_value'], $invoice['Invoice']['currency_code']) ?></td>
								</tr>
								<?php
							endforeach;
						endif;
						?>
						<?php if (!empty($invoice['Invoice']['shipping_amount'])): ?>
                                        <tr>
                                            <td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
                                            <td style="border-left:none;border-right:none;"  colspan="2" ><strong><?php echo __("Shipping", true) ?>:</strong></td>
                                            <td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['shipping_amount'], $invoice['Invoice']['currency_code']) ?></td>
                                        </tr>
                                    <?php endif; ?>
						<?php if (!empty($invoice['Invoice']['adjustment_value'])): ?>
						<tr>
							<td   style="border:none" bgcolor="#FFF" colspan="<?= $cols ?>"></td>
							<td style="border-left:none;border-right:none;"  colspan="2" ><strong><?php __($invoice['Invoice']['adjustment_label']) ?>:</strong></td>
							<td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice['Invoice']['adjustment_value'], $invoice['Invoice']['currency_code']) ?></td>
						</tr>
						<?php endif; ?>
					    <?php  if (!empty($extra_details['invoice_accounts'])&&is_array($extra_details['invoice_accounts'])) {
						foreach($extra_details['invoice_accounts'] as $account_id=>$invoice_account) {
							if(!empty($invoice_account['value'])&&abs($invoice_account['value'])>0) {
								?>
								<tr class="invoice_account" id="invoice_account_<?php echo $account_id ?>">
									<td   style="border:none"  bgcolor="#FFF" colspan="<?= $cols ?>"></td>
									<td style="border-left:none;border-right:none;"  colspan="2" ><strong><?php echo $invoice_account['label'] ?>:</strong></td>
									<td style="border-left:none;border-right:none;" class="text-left"><?php echo format_price($invoice_account['value'], $invoice['Invoice']['currency_code']) ?></td>
								</tr>
							<?php } } } ?>
						<tr class="total-row">
							<td style="border:none"  bgcolor="#FFF" colspan="<?=$cols?>"></td>
							<td style="border-left:none;border-right:none;"  colspan="2"><strong><?php __("Total") ?>:</strong></td>
							<td style="border-left:none;border-right:none;"class="text-left"><?php echo format_price($invoice['Invoice']['summary_total'], $invoice['Invoice']['currency_code']) ?></td>
						</tr>
					</table>
					<?php if(!empty($invoice['Invoice']['notes'])||!empty($invoice['Invoice']['html_notes'])) { ?>
					<div style="clear:both; height:1px;">&nbsp;</div>
				
				<br />
				<br />
				<div class="notes-block">
					<table width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td>
								<?php if(!empty($invoice['Invoice']['notes'])) {?>
								<p style="font-style:italic"><?php echo nl2br(h($invoice['Invoice']['notes'])) ?></p>
								<?php } ?>
								<?php if(!empty($invoice['Invoice']['html_notes'])) {?>
								<p ><?php echo strip_tags(str_replace(array('onclick','onmouseup','onmousewheel','onscroll','onmousedown','onmousemove','onmouseout','onmouseover'),'', $invoice['Invoice']['html_notes']),'<div><span><ul><li><ol><br><p><b><i><strong><font><small><big><h1><h2><h3><h4><h5><h6><h7><a>') ?></p>
								<?php } ?>
							</td>
						</tr>
					</table>
				</div>
				<br />
				<br />
				<br />
				<?php } ?>
				<?php
                if ($site['plan_id']==1) {
                    echo $this->element('branded',['isPdf'=>$isPdf]);
                }
                ?>
			</div>
		</div>
	</body>
</html>
<?php

$all_place_holder+=PlaceHolder::estimate_get_all_place_holders($invoice['Invoice']);
$out = ob_get_clean();
//$out=  str_replace(array_keys($all_place_holder), array_values($all_place_holder), $out);
$out= PlaceHolder::replace($out,array_keys($all_place_holder),array_values($all_place_holder));
$out = formatSaudiRiyalSymbol($out, $invoice['Invoice']['currency_code']);
echo $out;
?>