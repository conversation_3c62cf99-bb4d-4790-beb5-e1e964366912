<?php

if ($this->params['url']['ext'] != 'pdf') {
    header('Content-Type: text/html; charset=utf-8');
}

$all_place_holder=PlaceHolder::invoice_get_all_place_holders($invoice['Invoice']);
$all_place_holder += PlaceHolder::invoice_custom_field_place_holder($invoice);

foreach($layout['InvoiceLayout'] as $key=>$value) {
    if(strpos($key,"label_")==0) {
        $all_place_holder['{%' . $key . '%}'] = $layout['InvoiceLayout'][$key];
    }
}


$enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
foreach ($invoice['InvoiceItem'] as $k => $item2) {
    $item2['unit_factor'] = (float)$item2['unit_factor'];
    $item2['quantity'] = (float)$item2['quantity'];
    $item2['unit_price'] = (float)$item2['unit_price'];

    if ($enable_multi_units && !empty($item2['unit_factor']) && !empty($item2['unit_name'])) {
        $invoice['InvoiceItem'][$k]['quantity_written'] = ($item2['quantity']/$item2['unit_factor']).' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
        $invoice['InvoiceItem'][$k]['unit_price_factor'] = ($item2['unit_price']*$item2['unit_factor']);//.' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
//        $invoice['InvoiceItem'][$k]['unit_price'] = ($item2['unit_price']*$item2['unit_factor']);//.' <span class="unit_factor_name" >'.$item2['unit_small_name'].'</span>';
    }else {
        $invoice['InvoiceItem'][$k]['quantity_written'] = format_number($item2['quantity'] );
        $invoice['InvoiceItem'][$k]['unit_price_factor'] = $item2['unit_price'];
    }
}
ob_start();
$ViewCss = json_decode($layout['InvoiceLayout']['view_style'],true);

if(is_array($ViewCss))
foreach($ViewCss as $key=>$value) {
    if(strpos($key,"label_")==0) {
        $all_place_holder['{%' . $key . '%}'] = $value;
    }
}
$more_style = '';


$shipping_name = $invoice['Invoice']['client_secondary_name'];
$shipping_address = $invoice['Invoice']['client_secondary_address1'] . (empty($invoice['Invoice']['client_secondary_address2']) ? '' : '<br/>' . $invoice['Invoice']['client_secondary_address2']);
$shipping_city = $invoice['Invoice']['client_secondary_city'];
$shipping_state = $invoice['Invoice']['client_secondary_state'];
$shipping_postal_code = $invoice['Invoice']['client_secondary_postal_code'];
$shipping_country_code = $invoice['Invoice']['client_secondary_country_code'];


$show_shipping = false;
if ($invoice['Invoice']['shipping_options'] == 2 ||
        (empty($invoice['Invoice']['shipping_options']) && !empty($layout['InvoiceLayout']['show_ship']) && (empty($invoice['Invoice']['client_secondary_name']) && empty($invoice['Invoice']['client_secondary_address1'])))
) {
    $show_shipping = true;
    $shipping_name = $invoice['Invoice']['client_business_name'];
    $shipping_address = $invoice['Invoice']['client_address1'] . (empty($invoice['Invoice']['client_address2']) ? '' : '<br/>' . $invoice['Invoice']['client_address2']);
    $shipping_city = $invoice['Invoice']['client_city'];
    $shipping_state = $invoice['Invoice']['client_state'];
    $shipping_postal_code = $invoice['Invoice']['client_postal_code'];
    $shipping_country_code = $invoice['Invoice']['client_country_code'];
} else if (
        (empty($invoice['Invoice']['shipping_options']) && !empty($layout['InvoiceLayout']['show_ship']) && (!empty($invoice['Invoice']['client_secondary_name']) || !empty($invoice['Invoice']['client_secondary_address1']))) || $invoice['Invoice']['shipping_options'] == 3) {
    $show_shipping = true;
}
if ($show_shipping)
    $more_style.=' 
#shipping_options {display: table-row !important;}
';

$this->layout = '';


if (empty($site)) {
    $site = getCurrentSite();
}

$hide_description = true;
foreach ($invoice['InvoiceItem'] as $item) {
    if (!empty($item['description'])) {
        $hide_description = false;
        break;
    }
}

if ($hide_description)
    $cols = 1;
else
    $cols = 2;


$site['site_logo_full_path'] = str_replace('/', 'ZXASDFQ', $site['site_logo_full_path']);
$site['site_logo_full_path'] = rawurlencode($site['site_logo_full_path']);
$site['site_logo_full_path'] = str_replace('ZXASDFQ', '/', $site['site_logo_full_path']);


debug($layout['InvoiceLayout']['language_id']);
$layoutHTML = $layout['InvoiceLayout']['html'];

if(!empty($layout['InvoiceLayout']['sales_order_title'])){ $layout['InvoiceLayout']['invoice_title']=$layout['InvoiceLayout']['sales_order_title'];}
if(!empty($layout['InvoiceLayout']['label_sales_order_no'])){ $layout['InvoiceLayout']['label_invoice_no']=$layout['InvoiceLayout']['label_sales_order_no'];}
if(!empty($layout['InvoiceLayout']['label_sales_order_date'])){ $layout['InvoiceLayout']['label_date']=$layout['InvoiceLayout']['label_sales_order_date'];}


$fullUrl = 'https://' . $site['subdomain'];

$logo = $fullUrl.'/css/images/transparent.gif';

if (!empty($layout['InvoiceLayout']['logo']) && $layout['InvoiceLayout']['logo'] != '{%logo_site%}' && $layout['InvoiceLayout']['logo'] != '') {

    $logoPath = DS . 'files' . DS . 'images' . DS . 'logos' . DS . ($layout['InvoiceLayout']['logo']);


    $logo = $fullUrl . str_replace(DS, '/', $logoPath);

} elseif (!empty($site['site_logo'])) {
    $logoPath = DS . 'files' . DS . 'images' . DS . 'site-logos' . DS . ($site['site_logo']);
    $logo = $fullUrl . str_replace(DS, '/', $logoPath);
} else {
    $more_style.='
#logo{display:none;}';
}

$customFields = array();

$layoutCustomFields = '';
$customFieldsHtml = $layout['InvoiceLayout']['custom_fields'];
if (empty($customFieldsHtml) && !empty($parentLayout)) {
    $customFieldsHtml = $parentLayout['InvoiceLayout']['custom_fields'];
}
$fields = array();
if (!empty($layout['InvoiceLayoutCustomField'])||!empty($invoice['InvoiceCustomField'])) {
    
    foreach ($invoice['InvoiceCustomField'] as $field) {
 
        $field['real-label'] = $label = $field['label'];
        if (!empty($field['placeholder'])) {
            $placeholder = 'label_' . str_replace(array("%", '-'), array('', '_'), $field['placeholder']);

            if (isset($layout['InvoiceLayout'][$placeholder])) {
                $label = $layout['InvoiceLayout'][$placeholder];
            }
            $field['real-label'] = $label;
        }else{
         
        }
        
        $customFields[$field['label']] = $field;
    }


    if (!empty($site['enable_po']) && !empty($invoice['Invoice']['po_number'])) {
        $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array(!empty($layout['InvoiceLayout']['label_po_no']) ? $layout['InvoiceLayout']['label_po_no'] : (empty($site['po_no_label']) ? __('PO Number', true) : $site['po_no_label']), $invoice['Invoice']['po_number']), $customFieldsHtml);
    }
    if (!empty($invoice['Invoice']['show_from_to']) && !empty($invoice['Invoice']['from'])) {
        $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array((empty($layout['InvoiceLayout']['label_from_date']) ? __('From Date', true) : $layout['InvoiceLayout']['label_from_date']), $invoice['Invoice']['from']), $customFieldsHtml);
    }
    if (!empty($invoice['Invoice']['show_from_to']) && !empty($invoice['Invoice']['to'])) {
        $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array((empty($layout['InvoiceLayout']['label_to_date']) ? __('To Date', true) : $layout['InvoiceLayout']['label_to_date']), $invoice['Invoice']['to']), $customFieldsHtml);
    }
    
    foreach ($layout['InvoiceLayoutCustomField'] as $customField) {
   
        $field = $customFields[$customField['label']];
        if (!empty($customFields[$customField['label']]) && !empty($field['real-value'])) {
            $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array($field['real-label'], $field['real-value']), $customFieldsHtml);
            unset($customFields[$customField['label']]);
        }
    }

    foreach ($customFields as $label => $customField) {
 
        if (!empty($label) && !empty($customField['real-value'])) {
            $layoutCustomFields .= str_replace(array('{%label%}', '{%value%}'), array($customField['real-label'], $customField['real-value']), $customFieldsHtml);
        }
    }
}

$layoutHTML = str_replace(array('{%custom_fields%}', '<custom_field', '</custom_field'), array($layoutCustomFields, '<table', '</table'), $layoutHTML);

if ($layout['InvoiceLayout']['default_height'] == '0')
    $layout['InvoiceLayout']['default_height'] = '';
$layoutHTML = str_replace(array('{%logo%}', '{%logo-width%}', '{%logo-height%}'), array($logo, $layout['InvoiceLayout']['default_width'], $layout['InvoiceLayout']['default_height']), $layoutHTML);

if ($orderSource) {
    $layoutHTML = str_replace(array('{%invoice_number%}', '{%invoice_date%}', '{%invoice_order_source%}'), array($invoice['Invoice']['no'], $invoice['Invoice']['date'], $orderSource), $layoutHTML);
} else {
    $layoutHTML = str_replace(array('{%invoice_number%}', '{%invoice_date%}', '{%invoice_order_source%}', 'label_order_source">مصدر الطلب', 'label_order_source">Order Source'), array($invoice['Invoice']['no'], $invoice['Invoice']['date'], '', 'label_order_source">', 'label_order_source">'), $layoutHTML);
}

if(isset($invoice['Invoice']['id']) and !empty($invoice['Invoice']['id']) && empty($alt_template)){

    $layout['InvoiceLayout']['item_columns']=$invoice['Invoice']['item_columns']   ;
    
}



if(empty( $layout['InvoiceLayout']['item_columns'])){
$forcecols=1;   
$columns=array
(
    'field1' => array
        (
            'name' => 'name',
            'label' => $layout['InvoiceLayout']['label_item']
        ),

    'field2' => array
        (
            'name' => $hide_description==false?'description':'',
            'label' => $hide_description==false?$layout['InvoiceLayout']['label_description']:''
        ),

    'field3' => array
        (
            'name' => '',
            'label' => ''
        ),

    'field4' => array
        (
            'name' => '',
            'label' => ''
        ),

    'field5' => array
        (
            'name' => '',
            'label' => ''
        ),

);    

  
}else{
    
$columns=json_decode($layout['InvoiceLayout']['item_columns'],true);    

}


if(is_array($columns)&&count($columns))
{
   
	$layout['InvoiceLayout']['items_list']=$this->element('dynamic_items_list',array('invoice'=>$invoice,'InvoiceItem'=>$invoice['InvoiceItem'],'columns'=>$columns));
}else{
//   if($layout['InvoiceLayout']['quantity_price']=="show_quantity_before_price"){
//       
//$layout['InvoiceLayout']['items_list']=str_replace(array('label_quantity','label_unit_price'),array('_label_quantity','_label_unit_price'),$layout['InvoiceLayout']['items_list']);
//$layout['InvoiceLayout']['items_list']=str_replace(array('_label_quantity','_label_unit_price'),array('label_unit_price','label_quantity'),$layout['InvoiceLayout']['items_list']);
//
//   }elseif($layout['InvoiceLayout']['quantity_price']=="hide_price_and_quantity"){
//   $layout['InvoiceLayout']['items_list']=str_replace('<th width="60" bgcolor="#e5e5e5" class="editable-area" id="label_unit_price">{%label_unit_price%}</th>','',$layout['InvoiceLayout']['items_list'])    ;
//   $layout['InvoiceLayout']['items_list']=str_replace('<th width="30" bgcolor="#e5e5e5" class="editable-area" id="label_quantity">{%label_quantity%}</th>','',$layout['InvoiceLayout']['items_list'])    ;
//   }
   
}

if(strpos($invoice['Invoice']['html_notes'],'replace_template_notes'))  $layout['InvoiceLayout']['footer']= $invoice['Invoice']['html_notes'];
preg_match_all('/\{%([^%]+)%\}/', $layoutHTML, $m);
//debug($m);

$s = $m[0];
$r = array('{%business_info%}' => '', '{%client_info%}' => '');

foreach ($m[1] as $idx => $val) {
debug($val);

    $cellValue = isset($layout['InvoiceLayout'][$val])?$layout['InvoiceLayout'][$val]:$m[0][$idx];
    if ($val == 'business_info' || $val == 'client_info' || $val == 'ship_info') {
        $cellValue = nl2br_fix($layout['InvoiceLayout'][$val]);
    }
    $r[$s[$idx]] = $cellValue;
}
$ss=array_keys($r);
//debug($r);
$layoutHTML = str_replace(array_keys($r), $r, $layoutHTML);

//debug($invoice['InvoiceItem']);
$itemsList = '';
/* @var $html HtmlHelper */
if (!$hide_description)
    foreach ($invoice['InvoiceItem'] as $item) {
        $item['quantity'] =(float)$item['quantity'];
        $item['unit_price']=(float)$item['unit_price'];

        $itemsList .= $html->tableCells(array(
            nl2br($item['item']),
            nl2br($item['description']),
            empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['unit_price_factor'], $invoice['Invoice']['currency_code'], false) : format_price_simple($item['unit_price_factor'], $invoice['Invoice']['currency_code'], false),
            $item['quantity_written'],
            empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code'], false) : format_price_simple($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code'], false)
                ), array('class' => 'line'), array('class' => 'line'));
    }
else
    foreach ($invoice['InvoiceItem'] as $item) {
        $item['quantity'] =(float)$item['quantity'];
        $item['unit_price']=(float)$item['unit_price'];

        $itemsList .= $html->tableCells(array(
            nl2br($item['item']),
            empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['unit_price_factor'], $invoice['Invoice']['currency_code'], false) : format_price_simple($item['unit_price_factor'], $invoice['Invoice']['currency_code'], false),
            $item['quantity_written'],
            empty($layout['InvoiceLayout']['simple_item_currency']) ? format_price($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code'], false) : format_price_simple($item['quantity'] * $item['unit_price'], $invoice['Invoice']['currency_code'], false)
                ), array('class' => 'line'), array('class' => 'line'));
    }



$placeholders = $all_place_holder+array(
    '{%business_name%}' => $site['business_name'],
    '{%cols%}' => $cols,
    '{%first_name%}' => $site['first_name'],
    '{%last_name%}' => $site['last_name'],
    '{%subdomain%}' => $site['subdomain'],
    '{%address1%}' => $site['address1'],
    '{%address2%}' => $site['address2'],
    '{%city%}' => $site['city'],
    '{%state%}' => $site['state'],
    '{%postal_code%}' => $site['postal_code'],
    '{%full_business_address%}' => $this->element('format_address_html',$site+array('is_inline'=>true)),
    '{%phone1%}' => $site['phone1'],
    '{%phone2%}' => $site['phone2'],
    '{%telephone%}' => $site['phone1'],
    '{%mobile%}' => $site['phone2'],
    '{%client_organization%}' => $invoice['Invoice']['client_business_name'],
    '{%client_number%}' => $invoice['Client']['client_number'],
    '{%client_name%}' => $invoice['Invoice']['client_first_name'] . ' ' . $invoice['Invoice']['client_last_name'],
    '{%client_first_name%}' => $invoice['Invoice']['client_first_name'],
    '{%client_last_name%}' => $invoice['Invoice']['client_last_name'],
    '{%client_phone%}' => $invoice['Client']['phone1'],
    '{%client_mobile%}' => $invoice['Client']['phone2'],
    '{%client_email%}' => $invoice['Client']['email'],
    '{%client_address%}' => $invoice['Invoice']['client_address1'] . (empty($invoice['Invoice']['client_address2']) ? '' : '<br>' . $invoice['Invoice']['client_address2']),
    '{%client_city%}' => $invoice['Invoice']['client_city'],
    '{%client_state%}' => $invoice['Invoice']['client_state'],
    
    '{%full_client_address%}'=>$this->element('format_address_html',array('address1'=>$invoice['Invoice']['client_address1'],'address2'=>$invoice['Invoice']['client_address2'],'city'=>$invoice['Invoice']['client_city'],'state'=>$invoice['Invoice']['client_state'],'postal_code'=>$invoice['Invoice']['client_postal_code'],'is_inline'=>true)),
    '{%client_country%}' => !empty($clientCountry) ? $clientCountry : '',
    '{%client_secondary_name%}' => $shipping_name,
    '{%client_secondary_address%}' => $shipping_address,
    '{%client_secondary_city%}' => $shipping_city,
    '{%client_secondary_state%}' => $shipping_state,
    '{%client_secondary_country%}' => !empty($shippingCountry) ? $shippingCountry : '',
    '{%client_secondary_postcode%}' => $shipping_postal_code,
    '{%client_secondary_postal_code%}' => $shipping_postal_code,
    '{%items%}' => $itemsList,
    '{%invoice_notes%}' => nl2br(h($invoice['Invoice']['notes'])) . '<span style="font-style: normal;" >' . strip_tags(str_replace(array('onclick', 'onmouseup', 'onmousewheel', 'onscroll', 'onmousedown', 'onmousemove', 'onmouseout', 'onmouseover'), '', $invoice['Invoice']['html_notes']), '<div><span><ul><li><ol><br><p><b><i><strong><font><small><big><h1><h2><h3><h4><h5><h6><h7><a><style>') . '</span>',
    //'{%html_invoice_notes%}' => , 
    '{%from-date%}' => $invoice['Invoice']['from'],
    '{%to-date%}' => $invoice['Invoice']['to'],
);




if (!empty($layout['InvoiceLayoutTag'])) {
    foreach ($layout['InvoiceLayoutTag'] as $tag) {
        $placeholders[$tag['tag']] = $tag['replacement'];
    }
}

//	$labels = array('label_invoice_no', 'label_date', 'label_po_no', 'label_total', 'label_status', 'label_due_after', 'label_due_date', 'label_deposit', 'label_paid_amount', 'label_unpaid_amount', 'label_subtotal', 'label_description', 'label_item', 'label_tax1', 'label_tax2', 'label_quantity', 'label_unit_price', 'label_item_total', 'label_from_date', 'label_to_date');

preg_match_all('/{%(label_[^%]+)%}/', $layoutHTML, $m);
$labels = $m[1];

foreach ($labels as $label) {
    
    $placeholders['{%' . $label . '%}'] = $layout['InvoiceLayout'][$label];
    
}

$placeholders['{%value_item_total%}'] = format_price($invoice['Invoice']['summary_subtotal'] + (!empty($invoice['Invoice']['adjustment_value']) ? (float) $invoice['Invoice']['adjustment_value']  * -1 : 0), $invoice['Invoice']['currency_code']);
$placeholders['{%value_unpaid_amount%}'] = format_price($invoice['Invoice']['summary_unpaid'], $invoice['Invoice']['currency_code']);
$placeholders['{%value_paid_amount%}'] = format_price($invoice['Invoice']['summary_paid'] * -1, $invoice['Invoice']['currency_code']);
$placeholders['{%value_total%}'] = format_price($invoice['Invoice']['summary_total'], $invoice['Invoice']['currency_code']);

$taxes = '';
if(!empty( $layout['InvoiceLayout']['item_columns'])&&is_array($columns=json_decode($layout['InvoiceLayout']['item_columns'],true))&&count($columns))
{
$table_cell = '<tr>
					<td bgcolor="#FFF" colspan="1" style="border:none;"></td>
					<td colspan="1" style="border-left:none;border-right:none;"><strong id="label_item_total" class="editable-area">{%label%}</strong></td>
					<td class="text-left" style="border-left:none;border-right:none;">{%value%}</td>
			</tr>';
}else{
    
if($forcecols==1){
 $table_cell = '<tr>
					<td width="65%" style="border:none;" bgcolor="#FFF"></td>
					<td colspan="1" style="border-left:none;border-right:none;"><strong id="label_item_total" class="editable-area">{%label%}</strong></td>
					<td class="text-left" style="border-left:none;border-right:none;">{%value%}</td>
			</tr>';   
}else{
    
$table_cell = '<tr>
					<td bgcolor="#FFF" colspan="'.$cols.'" style="border:none;"></td>
					<td colspan="2" style="border-left:none;border-right:none;"><strong id="label_item_total" class="editable-area">{%label%}</strong></td>
					<td class="text-left" style="border-left:none;border-right:none;">{%value%}</td>
			</tr>';
}


}
			
if(!empty($invoice['Invoice']['summary_total_discount'])) {$invoice['Invoice']['summary_discount']=$invoice['Invoice']['summary_total_discount'];$invoice['Invoice']['discount_amount']=1;}
if ($invoice['Invoice']['summary_discount']) {
    $taxes .= str_replace(
            array('{%label%}', '{%value%}'), array(empty($invoice['Invoice']['discount_amount'])?sprintf('%s (%s%%)', $layout['InvoiceLayout']['label_discount'], round($invoice['Invoice']['discount'], 2)):$layout['InvoiceLayout']['label_discount'], format_price($invoice['Invoice']['summary_discount'] * -1, $invoice['Invoice']['currency_code'])), $table_cell);
}



foreach ($invoice['InvoiceTax'] as $tax) {
    $taxes .= str_replace(
            array('{%label%}', '{%value%}'), array(sprintf('%s (%s%%)', $tax['name'], round($tax['value'], 2)), format_price($tax['invoice_value'], $invoice['Invoice']['currency_code'])), $table_cell);
}
$style="";

if (!empty($invoice['Invoice']['shipping_amount'])) {
    $taxes .= str_replace(
            array('{%label%}', '{%value%}'), array(empty($layout['InvoiceLayout']['label_shipping'])?__('Shipping',true):$layout['InvoiceLayout']['label_shipping'],  format_price($invoice['Invoice']['shipping_amount'], $invoice['Invoice']['currency_code'])), $table_cell);
}

if (!empty($invoice['Invoice']['adjustment_value'])) {
    $taxes .= str_replace(
        array('{%label%}', '{%value%}'), array(
        empty($invoice['Invoice']['adjustment_label'])?__('Adjustment', true):$invoice['Invoice']['adjustment_label'],
        $invoice['Invoice']['adjustment_value'],
    ),
        $table_cell
    );
}

$placeholders['<head>'] = '<head>' . $style;

$placeholders['{%invoice-taxes%}'] = $taxes;
 if ($site['plan_id']==1) {
	$isPdf = (!empty($this->params['url']['ext']) && $this->params['url']['ext'] == 'pdf') || !empty($pdf); 
	$placeholders['</body>'] = $this->element('branded',['isPdf'=>$isPdf]) . '</body>';
}


foreach ($placeholders as $k => $v) {

    if (empty($v))
        $layoutHTML = preg_replace('/' . $k . '(\r|\s|\n|,|<br\/>|<br>|<br \/>|)*/', '', $layoutHTML);
    if(is_string($k)) {
        $layoutHTML = str_replace($k, (string)$v, $layoutHTML);
    } else {
        $layoutHTML = str_replace($k, $v, $layoutHTML);
    } 

}

if ($invoice['Invoice']['summary_deposit'] && $invoice['Invoice']['summary_deposit'] != $invoice['Invoice']['summary_unpaid'] && abs($invoice['Invoice']['summary_deposit'] - $invoice['Invoice']['summary_unpaid']) >= 0.01) {
    $next_payment_row = str_replace(
            array('{%label%}', '{%value%}'), array(sprintf('%s ', $layout['InvoiceLayout']['label_deposit']), format_price($invoice['Invoice']['summary_deposit'], $invoice['Invoice']['currency_code'])), $table_cell);

    if (strpos($layoutHTML, '#<!-- PaidAmount -->'))
        $layoutHTML = str_replace('#<!-- PaidAmount -->', $next_payment_row . ' #<!-- PaidAmount -->', $layoutHTML);
    else if (strpos($layoutHTML, '<!-- PaidAmount -->'))
        $layoutHTML = str_replace('<!-- PaidAmount -->', $next_payment_row . ' <!-- PaidAmount -->', $layoutHTML);
    else
        $taxes .= str_replace(
                array('{%label%}', '{%value%}'), array(sprintf('%s ', $layout['InvoiceLayout']['label_deposit']), format_price($invoice['Invoice']['summary_deposit'], $invoice['Invoice']['currency_code'])), $table_cell);
}


    $layoutHTML = preg_replace('#<!-- PaidAmount -->.*<!-- /PaidAmount -->#sm', '', $layoutHTML);


if ($invoice['Invoice']['summary_subtotal'] == $invoice['Invoice']['summary_total']) {
    $layoutHTML = preg_replace('#<!-- Subtotal -->.*<!-- /Subtotal -->#sm', '', $layoutHTML);
}

if ($hide_description) {
    $layoutHTML = preg_replace('#<!-- Description -->.*<!-- /Description -->#sm', '', $layoutHTML);
}

$layoutHTML = preg_replace('/<\/table[^>]*>[\r\n\s]*<table[^>]+custom_fields[^>]+>/', '', $layoutHTML);


echo $layoutHTML;
	
if ($layout['InvoiceLayout']['language_id'] == 'ara'||$layout['InvoiceLayout']['language_id'] == 'ar'||$layout['InvoiceLayout']['language_id'] == 7) {
		debug('GALLO');
    $style = '<style type="text/css">' . file_get_contents(WWW_ROOT . 'css/invoice-layout-template-ar.css') . (!empty($more_style) ? $more_style : '') . '</style>';
} else {
	debug('SSSALLO');
    $style = '<style type="text/css">' . file_get_contents(WWW_ROOT . 'css/invoice-layout-template.css') . (!empty($more_style) ? $more_style : '') . '</style>';
}
?>
    <? echo $style ?>    
<?php
echo $this->element('view-style',array('ViewCss'=>$ViewCss));
$out = ob_get_clean();
foreach($layout['InvoiceLayout'] as $key=>$value) {
    if(strpos($key,"label_")==0) {
        $all_place_holder['{%' . $key . '%}'] = $layout['InvoiceLayout'][$key];
    }
}
$all_place_holder['{%invoice_notes%}'] = nl2br(h($invoice['Invoice']['notes'])) . '<span style="font-style: normal;" >' . strip_tags(str_replace(array('onclick', 'onmouseup', 'onmousewheel', 'onscroll', 'onmousedown', 'onmousemove', 'onmouseout', 'onmouseover'), '', $invoice['Invoice']['html_notes']), '<div><span><ul><li><ol><br><p><b><i><strong><font><small><big><h1><h2><h3><h4><h5><h6><h7><a><style>') . '</span>';
$all_place_holder['{%logo-height%}']=$layout['InvoiceLayout']['default_height'];
$all_place_holder['{%invoice_title%}']=$layout['InvoiceLayout']['invoice_title'];
$all_place_holder['{%business_info%}']=$layout['InvoiceLayout']['business_info'];
$all_place_holder['{%business_info%}']=nl2br(PlaceHolder::replace($all_place_holder['{%business_info%}'],array_keys($all_place_holder),array_values($all_place_holder)));
$all_place_holder['{%client_info%}']=$layout['InvoiceLayout']['client_info'];
$all_place_holder['{%client_info%}']=nl2br(PlaceHolder::replace($all_place_holder['{%client_info%}'],array_keys($all_place_holder),array_values($all_place_holder)));
//$all_place_holder['{%client_info%}']=(PlaceHolder::replace($all_place_holder['{%client_info%}'],array_keys($all_place_holder),array_values($all_place_holder)));
$all_place_holder['{%ship_info%}']=$layout['InvoiceLayout']['ship_info'];
$all_place_holder['{%ship_info%}']=(PlaceHolder::replace($all_place_holder['{%ship_info%}'],array_keys($all_place_holder),array_values($all_place_holder)));
if ($this->params['url']['ext'] != 'pdf') {
    // print_pre($all_place_holder);
    //die();
    $all_place_holder['{%html_sticky_footer%}']=$html->tag("div",(PlaceHolder::replace($layout['InvoiceLayout']['sticky_footer'],array_keys($all_place_holder),array_values($all_place_holder))),array('class'=>'StickyFooter'));
    $all_place_holder['{%html_sticky_header%}']=$html->tag("div",(PlaceHolder::replace($layout['InvoiceLayout']['sticky_header'],array_keys($all_place_holder),array_values($all_place_holder))),array('class'=>'StickyHeader'));

}else{

    $sticky_footer_html= (PlaceHolder::replace($layout['InvoiceLayout']['sticky_footer'],array_keys($all_place_holder),array_values($all_place_holder)));
    $this->InvoiceFooter=$sticky_footer_html;

    $sticky_header_html= (PlaceHolder::replace($layout['InvoiceLayout']['sticky_header'],array_keys($all_place_holder),array_values($all_place_holder)));

    $this->InvoiceHeader=$sticky_header_html;
}
//$out=  str_replace(array_keys($all_place_holder), array_values($all_place_holder), $out);
$final_out_put= PlaceHolder::replace($out,array_keys($all_place_holder),array_values($all_place_holder));
$final_out_put = formatSaudiRiyalSymbol($final_out_put, $invoice['Invoice']['currency_code']);
echo $final_out_put;
?>
