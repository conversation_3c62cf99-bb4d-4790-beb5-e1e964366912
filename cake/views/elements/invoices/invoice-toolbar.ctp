<?

use Izam\Daftra\Common\Utils\ElectronicInvoicesStatus;
use Izam\Daftra\Common\Utils\PermissionUtil;

$owner= getAuthOwner();
$id=$invoice['Invoice']['id'];
$isAdvancePayment = $invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT;
$edit_action = $isAdvancePayment ? 'edit_advance_payment' : 'edit';
?>
<div class="invoice-actions btn-group dropdown-btn-group">
    <?php if ((check_permission(Invoices_Edit_All_Invoices) || (check_permission(Invoices_Edit_his_own_Invoices) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) && $isPosOpenedOrNotPos) { ?>
        <a href="<?php echo Router::url(array('controller'=>'invoices','action' => $edit_action, $id)); ?>" class="btn btn-default btn-sm quick-action-btn">
            <span class="fa fa-pencil" aria-hidden="true"></span>
            <?php __("Edit") ?></a>
    <? } ?>

    <?php if (IS_PC) { ?>
        <a href="<?php echo Router::url(array('controller'=>'invoices','action' => 'view', $id, 'print' => 1)) ?>" class="print btn btn-default btn-sm quick-action-btn">
            <span class="fa fa-print" aria-hidden="true"></span>

            <?php __("Print") ?></a>
    <?php } else { ?>
        <a onclick="return print_iframe('<?php echo Router::url(array('action' => 'preview', $id, 'print' => 1)); ?>');" href="<?php echo Router::url(array('action' => 'preview', $id, 'ext' => 'pdf')); ?>" class="print btn btn-default btn-sm quick-action-btn">
            <span class="fa fa-print" aria-hidden="true"></span>

            <?php __("Print") ?></a>
    <?php } ?>
    <?
    if(count($invoiceLayouts)==0){
    ?>
    <a href="<?php echo Router::url(array('controller'=>'invoices','action'=>'view',$id, 'ext' => 'pdf')); ?>" class="btn btn-default btn-sm quick-action-btn" download>
        <span class="fa fa-file-pdf-o" aria-hidden="true"></span>
        <?php __("PDF") ?></a>
    <?
    } else {
    ?>

 <div class="btn-group">
            <button type="button"   class="btn btn-sm btn-default" >
               <a href="<?php echo Router::url(array($id, 'ext' => 'pdf')); ?>">   <span class="fa fa-file-pdf-o" aria-hidden="true"></span> <? echo __('PDF', true) ?> </a>
            </button>
            <button type="button"   class="btn btn-sm btn-default" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          <span class="caret"></span>
            </button>
     <ul class="dropdown-menu ">

 <?php
            foreach ($invoiceLayouts as $key=>$invoiceLayout){
            ?>
            <li><a href="<?php echo Router::url(array('controller'=>'invoices','action'=>'view',$id,$key,'ext' => 'pdf')); ?>" ><?php echo $invoiceLayout?></a></li>
            <?
            }
            ?>
     </ul>
 </div>
    <?
    }
    ?>
    <?php if (!$isAdvancePayment && (check_permission(Invoices_Add_Payments_to_All) || (check_permission(Invoices_Add_Invoice_Payments) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) && $isPosOpenedOrNotPos) { ?>
        <a class="btn btn-default btn-sm quick-action-btn" href="<?php echo Router::url(array('controller'=>'invoices','action' => 'add_payment', $id)); ?>">
            <span class="fa fa-credit-card" aria-hidden="true"></span>
            <?php __("Add Payment") ?></a>
    <? } ?>
    <?php if (!IS_PC) { ?>
        <a href="<?php echo Router::url(array('controller'=>'invoices','action'=>'view',$id, 'ext' => 'jpeg')); ?>" class="btn btn-default btn-sm quick-action-btn">
            <span class="fa fa-file-image-o" aria-hidden="true"></span>
            <?php __("Image") ?></a>
    <? } ?>


    <?php
	if (ifPluginActive( ProductsPlugin)){
		?>
    <div class="btn-group">
            <button type="button"   class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Vouchers', true) ?> <span class="caret"></span>
            </button>
        <ul class="dropdown-menu ">

            <?php
            $printActions = ['packing_slip' => 'Packing Slip', 'pick_list' => 'Pick List', 'shipping_label' => 'Shipping Label'];
            foreach ($printActions as $type => $translation){
                $urlParams = ['controller' => 'invoices', 'action' => 'print', $type, $id];
                if (!IS_PC || IS_MOBILE_APPLICATION) {$urlParams['ext'] = 'pdf';}
                ?>
                <li><a href="<?php echo Router::url($urlParams); ?>"><?php __($translation) ?> </a></li>
                <?php
            }
            ?>

            <?
            foreach ($invoiceLayouts as $key => $invoiceLayout) {
                ?>
                <li>
                    <a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'view', $id, $key)); ?>"><?php echo $invoiceLayout ?></a>
                </li>
                <?
            }
            ?>
            <?php
            echo draw_templates_list($view_templates,$invoice['Invoice']['id']);
            ?>


        </ul>
        <?php }?>
    </div>
    <?php echo $this->element('apps/app_buttons'); ?>

    <?php if(ifPluginActive(INSTALLMENT_AGREEMENT_PLUGIN) && check_permission([Invoices_Edit_All_Invoices, Invoices_Edit_his_own_Invoices])) {?>
        <?php if($hasInstallmentAgreement) {?>
            <a class="btn btn-default btn-sm quick-action-btn" href="<?php echo "/v2/owner/invoice_installment_agreements/" . $hasInstallmentAgreement['id']; ?>">
                <span class="fa fa-file-o" aria-hidden="true"></span>
                <?php __("View Installment Agreement") ?>
            </a>
        <?php } else { ?>
            <a class="btn btn-default btn-sm quick-action-btn" href="<?php echo "/v2/owner/invoice_installment_agreements/create?invoice_id=".$invoice['Invoice']['id']; ?>">
                <span class="fa fa-file-o" aria-hidden="true"></span>
                <?php __("Set Up Installment Agreement") ?>
            </a>
        <?php }?>
    <?php }?>


	<? if ((!empty($invoice['Client']['phone1'])||!empty($invoice['Client']['phone2']))&&sms_enabled()) { ?>
		<a href="<?php echo Router::url(array('controller'=>"sms_campaigns", 'action' => 'send_sms', "invoices", $invoice['Invoice']['id'])) ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-comments-o"></i> <?php __("Send SMS") ?></a>
	<?php } ?>

    <!-- Electronic Invoices start  -->
    <?php if (ifPluginActive(ETA_PLUGIN) && !$invoice['Invoice']['draft'] && check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::SYNC_E_INVOICE_ETA) && (settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN, 'use_invoice') || settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN, 'use_receipt'))) { ?>
        <div class="btn-group">
            <button type="button" <?php /*  echo (
                !empty($invoice['EntityAppData'][0]["data"]) && in_array(json_decode($invoice['EntityAppData'][0]['data'], true)['status'],  [ElectronicInvoicesStatus::VALID, ElectronicInvoicesStatus::IN_PROGRESS, ElectronicInvoicesStatus::SUBMITTED]) ||
                empty($invoice['EntityAppData'][0]["data"]) && !empty($invoice['SubmitEntityAppData'][0]["id"])
            ) ? "disabled='disabled'" : ''*/?>   class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('ETA', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu ">
                <?php if (settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN, 'use_invoice')) { ?>
                <li>
                    <a class="send-invoice-eta" <?php   echo (
                        !empty($invoice['EntityAppData'][0]["data"]) && in_array(json_decode($invoice['EntityAppData'][0]['data'], true)['status'],  [ElectronicInvoicesStatus::VALID, ElectronicInvoicesStatus::IN_PROGRESS, ElectronicInvoicesStatus::SUBMITTED]) ||
                        empty($invoice['EntityAppData'][0]["data"]) && !empty($invoice['SubmitEntityAppData'][0]["id"])
                    ) ? "disabled='disabled'" : ''?>  data-id="<?= $invoice['Invoice']['id']?>" data-type="invoice"  href="#">
                        <span class="mdi mdi-receipt" aria-hidden="true"></span>
                        <?php __("Send Invoice") ?></a>
                </li>
                <?php } ?>

                <?php if (settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN, 'use_receipt')) { ?>
                <li>
                    <a class="send-invoice-eta" <?php   echo (
                        !empty($invoice['EntityAppData'][0]["data"]) && in_array(json_decode($invoice['EntityAppData'][0]['data'], true)['status'],  [ElectronicInvoicesStatus::VALID, ElectronicInvoicesStatus::IN_PROGRESS, ElectronicInvoicesStatus::SUBMITTED]) ||
                        empty($invoice['EntityAppData'][0]["data"]) && !empty($invoice['SubmitEntityAppData'][0]["id"])
                    ) ? "disabled='disabled'" : ''?> data-id="<?= $invoice['Invoice']['id']?>" data-type="receipt" href="#">
                        <span class="mdi mdi-paper-roll-outline" aria-hidden="true"></span>
                        <?php __("Send Receipt") ?></a>
                </li>
                <?php } ?>
                <li>
                    <a class="" <?php   echo (empty($invoice['SubmitEntityAppData'][0]["id"])) ? "disabled='disabled'" : ''?> href="<?php echo "/v2/owner/get_document_state/invoice/" . $invoice['Invoice']['id']; ?>">
                        <span class="fa fa-refresh" aria-hidden="true"></span>
                        <?php __("Refresh Status") ?></a>

                </li>

                <li>
                    <a class="" <?php   echo (empty($invoice['EntityAppData'][0]["data"]) || (json_decode($invoice['EntityAppData'][0]['data'], true)['status'] != ElectronicInvoicesStatus::VALID)

                    ) ? "disabled='disabled'" : ''?> href="<?php echo "/v2/owner/cancel_document/invoice/" . $invoice['Invoice']['id']; ?>">
                        <span class="fa fa-remove" aria-hidden="true"></span>
                        <?php __("Cancel Submission") ?></a>
                </li>
            </ul>
        </div>


    <? } ?>

    <?php
        if (settings::getValue(InvoicesPlugin ,settings::ENABLE_ADVANCE_PAYMENT) && $invoice['Invoice']['type'] == Invoice::ADVANCE_PAYMENT ){
           echo '<a href="/v2/owner/advance-payment-invoices/'. $invoice['Invoice']['id']. '/create" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-plus"></i> '.__t("Add Sales Invoice").'</a>';

        }
    ?>


    <?php
    if (settings::getValue(InvoicesPlugin ,settings::ENABLE_ADVANCE_PAYMENT) && $invoice['Invoice']['type'] == Invoice::Invoice ){
        echo '<a href="/v2/owner/invoices/'. $invoice['Invoice']['id']. '/advance_payments/create" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-plus"></i> '.__t("Distribute Advance Payment").'</a>';
    }
    ?>

    <? if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN) && !$invoice['Invoice']['draft'] && !empty(settings::getValueEvenEmpty(\Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_binarySecurityToken')) && check_permission(PermissionUtil::SYNC_E_INVOICE_KSA)) { ?>
        <a href="/v2/owner/zatca/invoice/<?= $id ?>" <?=  !empty($invoice['EntityAppData'][0]["data"]) && json_decode($invoice['EntityAppData'][0]['data'], true)['status'] == "Valid" ? "disabled":"" ?> class="btn btn-default btn-sm btn-5 "> <i class="fa fa-send-o"></i> <?php __("Send To ZATCA") ?></a>
    <?php } ?>

    <?php if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN) && !$invoice['Invoice']['draft'] && !empty(settings::getValueEvenEmpty(\Izam\Daftra\Common\Utils\PluginUtil::EINVOICE_PLUGIN, 'einvoice_production_binarySecurityToken')) && check_permission(PermissionUtil::SYNC_E_INVOICE_KSA)  && json_decode($invoice['EntityAppData'][0]['data'], true)['status'] == "Valid") { ?>
        <a href="/owner/invoices/get_json/<?= $id ?>"  class="btn btn-default btn-sm btn-5 "> <i class="fa fa-download"></i> <?php __("Export XML") ?></a>
    <?php } ?>
    <?php if (check_permission(PermissionUtil::SYNC_JORDANIAN_INVOICE) && !$invoice['Invoice']['draft'] && Invoice::checkIfJordanEInvoiceEnabled()) { ?>
        <a href="/v2/owner/jordanian-e-invoice/<?= $id ?>/send" <?=  !empty($invoice['EntityAppData'][0]["data"]) && json_decode($invoice['EntityAppData'][0]['data'], true)['status'] == "Valid" ? "disabled":"" ?> class="btn btn-default btn-sm btn-5 ">
            <i class="fa fa-send-o"></i>
            <?php __("Send To ISTD") ?>
        </a>
    <?php } ?>

    <div class="btn-group">
        <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown"
                aria-haspopup="true" aria-expanded="false">
            <span class="fa fa-share" aria-hidden="true"></span>
            <?php echo sprintf(__('Send Via %s', true), '') ?> <span class="caret"></span>
        </button>
        <ul class="dropdown-menu">
            <?php if (!empty($invoice['Client']['email']) && $invoice['Invoice']['draft'] != "1") { ?>
                <li><a href="<?php echo Router::url(array('controller'=>'invoices','action' => 'send_to_client', $id)); ?>">
                    <span class="fa fa-envelope-o" aria-hidden="true"></span>
                    <?php __("Email") ?></a></li>
            <? } ?>
            <? if ((!empty($invoice['Client']['phone1'])||!empty($invoice['Client']['phone2']))&&sms_enabled()) { ?>
                <li><a href="<?php echo Router::url(array('controller'=>"sms_campaigns", 'action' => 'send_sms', "invoices", $invoice['Invoice']['id'])) ?>"> <i class="fa fa-comments-o"></i> <?php __("SMS") ?></a></li>
            <?php } ?>
            <?php if (isset($shareWithSocialMedia) && $shareWithSocialMedia){
                foreach ($socialLinks as $key => $socialLink) {
                    ?>
                    <li>
                        <?= $socialLink['link'] ?>
                    </li>
                    <?php
                }
            } elseif (isset($useOldWhatsapp) && $useOldWhatsapp) { ?>
                <li><a id="whatsapp_btn" href="<?= $WhatsAppLink ?>"> <i class="fa fa-comments-o"></i> <?php __("WhatsApp") ?></a></li>
            <?php } ?>
        </ul>
    </div>
    <?php if(isset($printableTemplates)): ?>
        <div class="btn-group">
            <button type="button"   class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Voucher Templates', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" >
                <?php foreach($printableTemplates as $template) : ?>
                    <li>
                        <a href="<?php echo Router::url(array('controller'=>'printable_templates', 'action' => 'view',  $id, $template['PrintableTemplate']['id'], Inflector::singularize(strtolower($this->name)) )) ?>" class=""> <?= $template['PrintableTemplate']['name']; ?></a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="btn-group">
        <button type="button"   class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
			<? echo __('More Options', true) ?> <span class="caret"></span>
        </button>
        <ul class="dropdown-menu ">
            <? if (ifPluginActive(WorkOrderPlugin) && !$isAdvancePayment) { ?>
            <li><a   href="<?php echo Router::url(array('action' => 'assign_work_order',  $invoice['Invoice']['id'])); ?>" > <i class="fa fa-book"></i> <?php __("Assign work order") ?></a></li>
            <?}?>

			<? if (ifPluginActive(FollowupPlugin)) { ?>
                <li><a   href="<?php echo Router::url(array('controller' => 'posts', 'action' => 'post', Post::INVOICE_TYPE, $invoice['Invoice']['id'])); ?>" > <i class="fa fa-book"></i> <?php __("Add Note / Attachment") ?></a></li>
                <li><a   href="<?php echo Router::url(array('controller' => 'appointments', 'action' => 'add', $invoice['Invoice']['id'] , Post::INVOICE_TYPE)); ?>" > <i class="fa fa-calendar-o"></i> <?php __("Schedule Appointment") ?></a></li>
			<?  }?>
            <?php if ( (check_permission(Invoices_Add_New_Invoices) || (check_permission(INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS) && isset($ownerClients[$invoice['Invoice']['client_id']]))) && $isPosOpenedOrNotPos) { ?>
                <li><a href="<?php echo Router::url(array('controller'=>'invoices','action' => $isAdvancePayment ? 'add_advance_payment':'add', $id)); ?>" >
                        <span class="fa fa-copy" aria-hidden="true"></span>
						<?php __("Clone") ?></a></li>
			<? } ?>
			<?php if (check_permission(Invoices_Edit_All_Invoices) || (check_permission(Invoices_Edit_his_own_Invoices) && $owner['staff_id'] == $invoice['Invoice']['staff_id'])) { ?>
				<?php if (empty($invoice['Invoice']['draft']) && !ifPluginActive(EINVOICE_SA_PLUGIN)) { ?>
                    <li><a  href="<?php echo Router::url(array('controller'=>'invoices','action' => 'update_draft', $id, 1)); ?>"><span class="fa fa-pencil-square" aria-hidden="true"></span> <?php __("Mark as Draft") ?></a></li>
				<?php } else if($invoice['Invoice']['draft'] == "1"){ ?>
                    <li><a  href="<?php echo Router::url(array('controller'=>'invoices','action' => 'update_draft', $id, 0)); ?>"><span class="fa fa-usd" aria-hidden="true"></span> <?php __("Issue Invoice") ?></a></li>
				<? } ?>


				<?php if ($isPosOpenedOrNotPos) { ?>
                    <li><a  href="<?php echo Router::url(array('controller'=>'invoices','action' => 'delete', $id)); ?>">
                            <span class="fa fa-trash-o" aria-hidden="true"></span>
							<?php __("Delete") ?></a></li>
				<?php } ?>

			<? } ?>
            <?php if(settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::InvoicesPlugin, 'enable_debit_note') && !$isAdvancePayment){ ?>
                    <li>
                        <a  href="<?php echo Router::url(array('controller'=>'invoices','action' => 'add_debitnote', $id)); ?>">
                        <span class="mdi mdi-credit-card-plus" aria-hidden="true"></span>
                        <?php __("Create Debit Note") ?></a>   
                   </li>
            <?php } ?>  
        </ul>
    </div>

    <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Refund', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu ">
                <li>
                    <?php if (!$isAdvancePayment): ?>
                    <a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add_refund', $id)); ?>">
                        <span class="mdi mdi-credit-card-refund" aria-hidden="true"></span>
                        <?php __("Create Refund Receipt") ?></a>
                    <?php endif; ?>
                </li>

                <li>
                    <a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add_creditnote', $id)); ?>">
                        <span class="mdi mdi-credit-card-minus" aria-hidden="true"></span>
                        <?php __("Create Credit Note") ?></a>
                </li>

            </ul>
    </div>
    <?php if(ifPluginActive(AccountingPlugin) && count($cost_centers) > 0) { ?>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Assign Cost Centers', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu ">
                <?php foreach($journal_sales_transactions as $transaction){?>
                    <li>
                        <a href="<?php echo $transaction['url'] ?>" class="<?php if($transaction['description']){ ?>tip <?php } ?>" title="<?php echo $transaction['description']; ?>"> <?= $transaction['account_name']; ?> (<?= __('Credit', true).' '.format_price($transaction['currency_credit'], $transaction['currency_code']); ?> )</a>
                    </li>
                <?php } ?>
            </ul>
        </div>
    <? } ?>
    <?php if(ifPluginActive(MANUFACTURING_PLUGIN) && check_permission(PermissionUtil::ADD_PRODUCTION_PLANS)  &&  ($invoice['Invoice']['draft'] != 1) && !$isAdvancePayment) { ?>
        <a href="/v2/owner/entity/production_plan/create?invoice_id=<?= $id ?>" class="btn btn-default btn-sm quick-action-btn">
            <span class="mdi mdi-file-replace-outline" aria-hidden="true"></span>
            <?php __("Convert To Production Plan") ?></a>
    <? } ?>



    <div class="clearfix"></div>
</div>
