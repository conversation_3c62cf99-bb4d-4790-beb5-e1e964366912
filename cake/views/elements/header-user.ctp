<?php

$notificationUrl = '/v2/owner/site/notifications';
$systemUpdatesUrl = '/v2/owner/site/updates';
if(getAuthClient()){
    $notificationUrl = '/v2/client/site/notifications';
    $systemUpdatesUrl = '/v2/client/site/updates';
}
$about_url='https://'. Portal_Full_Name .'/about-'. Domain_Name_Only; 
if (Domain_Name_Only == 'enerpize') {
    $about_url='https://'. Portal_Full_Name .'/about'; 
}
if (!empty($user)) {?>

    <ul class="nav navbar-nav navbar-right user-nav">


        <!-- <li class="dropdown res-right hidden-lg hidden-md menu-burger">
             <a class="dropdown-toggle clearfix mob-nav-trigger" data-toggle="dropdown" href="#" aria-expanded="false">
                 <p class="p-b-none"><i class="fa fa-bars ico-1x "></i></p>
             </a>
         </li>-->


        <?php if (ifPluginActive(WebsiteFrontPlugin) && !isMobileApp()) { ?>
            <li class="dropdown res-right">
                <a class="px-0" href="/home">
                <span class="px-3 d-inline-flex align-content-center text-white">
                    <i class="fs-20 mdi mdi-storefront mr-lg-2"></i>
                    <span class="visible-sm d-xl-inline-block text-nowrap"><?php __('Go to website') ?></span>
                </span>
                </a>
            </li>
        <?php } ?>
        <?php $params = array_merge($_GET, ['stop_custom' => 1]); unset($params['url']); if (!empty($additional_code) && getAuthOwner('is_login_as_admin')): ?>
            <li class="dropdown res-right">
                <a class="px-0" href="?<?= http_build_query($params); ?>">
                    <span class="px-3 d-inline-flex align-content-center text-white">
                        <i class="fs-20 mdi mdi-close mr-lg-2"></i>
                        <span class="visible-sm d-xl-inline-block text-nowrap"><?php __('Stop Custom Code') ?></span>
                    </span>
                </a>
            </li>
        <?php endif; ?>
        <?php if((defined('IS_BETA') && IS_BETA == 1) && check_permission(CHANGE_SITE_VERSION)){ ?>
        <li class="dropdown s2020">
            <a class="dropdown-toggle" data-toggle="dropdown" id="beta-dropdown" href="#" aria-expanded="false">
                <i class="mdi mdi-rocket nav-item-icon"></i>
                <span> <?php __('Beta') ?> </span>
            </a>
            <div id="loading-div2" class="dropdown-menu beta-dropdown" aria-labelledby="beta-dropdown">
                <i class="mdi mdi-close" id="close-beta-dropdown"></i>
                <img src="/img/beta.png" alt="beta image" />
                <h2><?php __('BETA Version') ?></h2>
                <p><?php __('You are currently experiencing some new features and updates on our new beta version, If you have any issues please Contact us') ?></p>
                <a href="javascript:$('.popup-chat').click();" class="beta-btn beta-show-feedback"><?php __('Give Feedback') ?></a>
                <hr class="beta-line">
                <?php if(!isIos()){?>
                <a href="/owner/sites/switch_to_master" class="beta-btn beta-return-to-master"><?php __('Switch To Master Version') ?></a>
                <?php } ?>
            </div>
        </li>
        <?php }?>
        <li class="dropdown res-right user-drop hidden-xs">
            <a class="clearfix d-inline-flex align-items-center support-modal-btn support-btn">
                <span class="mdi mdi-lifebuoy fs-28"></span>
                <span class="ml-1">
                    <?php __('Help') ?>
                </span>
            </a>
            <ul class="dropdown-menu w header-dropdowns">
                <li>
                    <div class="panel-heading b-light bg-light">
                        <strong><?php __('Help') ?></strong>
                    </div>
                </li>
                <li>
                    <a href="https://docs.<?= Domain_Short_Name ?>" target="_blank">
                        <?= __('System Manual', true); ?>
                    </a>
                    <a href="<?= Router::url(['controller'=>'sites_enquiries','owner'=> true,'action' => 'index']) ?>" class="submit-feedback">
                        <?= __('Submit Feedback', true); ?>
                    </a>
                    <a href="<?=$about_url?>" target="_blank">
                        <?= __('About', true) . ' ' . Site_Full_name_NoSpace ?>
                    </a>
                </li>
            </ul>
        </li>

        <li class="dropdown s2020 res-left" id="notifications-dropdown-wrapper">
            <a class="dropdown-toggle ico-bell list_all_noti" data-toggle="dropdown" id="notifications-dropdown" href="#" aria-expanded="false">
                <i class="mdi mdi-bell nav-item-icon"></i>
                <?php if(!empty($notification_count)) {?>
                    <span id="notification-count" class="badge badge-sm up bg-danger count_noti"><?php  echo $notification_count; ?></span>
                <?php } ?>
            </a>
            <div id="loading-div" class="dropdown-menu notifications-dropdown-wrap <?php if(!is_rtl()) { ?> dropdown-menu-right <?php } ?>" aria-labelledby="notifications-dropdown">
                <h6 class="dropdown-header bg-light notifications-title border-bottom"><?php __('Notifications'); ?></h6>
                <div class="list-group notifications-wrap">
                    <div id = "notification-container-id" class="notifcation-list" data-simplebar="true">
                        <div id="group-back-btn" class="mb-3" hidden>
                            <button class="btn btn-disabled btn-icon font-weight-medium text-muted responsive">
                                <i class="mdi mdi-arrow-left"></i><span><?php __("Back");?></span>
                            </button>
                        </div>
                        <div id="notification-list" class="notifications-list">
                            <!-- notification will be loaded here through ajax call in drop-down-notifications.php -->
                        </div>
                    </div>
                    <div class="notifications-loader">
                        <div class="justify-content-center loading-partial">
                            <div class="d-flex h-full justify-content-center spinner-border" role="status">
                                <div class="notification-loader" role="status">
                                    <div class="inner-loader"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="notifications-bg"></div>
                </div>
                <a class="dropdown-item bg-light text-center notifications-btn border-top" href="<?php echo $notificationUrl; ?>">
                    <?php if(!is_rtl()) {
                        echo '<i class="mdi mdi-arrow-right"></i>';
                    }
                    __('View All');
                    if(is_rtl()) {
                        echo '<i class="mdi mdi-arrow-left"></i>';
                    }
                    ?>
                </a>
                <a class="dropdown-item bg-light text-center notifications-btn border-top" href="<?php echo $systemUpdatesUrl ?>">
                    <?php if(!is_rtl()) {
                        echo '<i class="mdi mdi-arrow-right"></i>';
                    }
                    __('System Updates');
                    if(is_rtl()) {
                        echo '<i class="mdi mdi-arrow-left"></i>';
                    }
                    ?>
                </a>
                <div class="panel bg-white">
                </div>

            </div>
            <!-- <li class="dropdown res-left"> <a title="Help" class="dropdown-toggle ico-help" data-toggle="dropdown" href="#" aria-expanded="false"><i class="fa fa-question-circle fa-3x"></i></a> -->
            <!-- dropdown -->


            <!-- / dropdown -->
        </li>
        <?php if (($user['staff_id'] == 0 && $user['type'] == 'owner')) : ?>
            <ul class="dropdown-menu w header-dropdowns">
                <li>
                    <div class="panel-heading b-light bg-light"> <strong>المساعده</strong> </div>
                </li>
                <li><a href="https://www.daftra.com/system-manual" target="_blank">دليل النظام </a></li>
                <li><a href="/owner/sites_enquiries/index" class="payment-options" onclick="$('.popup-chat').click();
                                return false;" target="_blank">أرسل لنا رأيك</a></li>
                <li><a href="https://www.daftra.com/about-daftra" target="_blank">عن Daftra </a></li>

            </ul>

        <?php endif; ?>
        <li class="dropdown res-right user-drop">
            <?php
            if ($user['type'] == 'owner') {
                $url = Router::url(array('controller' => 'sites', 'action' => 'change_settings'));
            } else {
                $url = Router::url(array('controller' => 'clients', 'action' => 'change_settings', 'client' => true));
            }
            ?>
            <?php

            if (($user['first_name'] or $user['last_name']) and isOwner()) {
                $username = "{$user['first_name']} {$user['last_name']}";
            } else if (!empty($user['business_name']) and isOwner()) {
                $username = $user['business_name'];
            } else {
                $username = $staff['name'];
            }

            ?>
            <a class="dropdown-toggle clearfix" data-toggle="dropdown" href="<?php echo $url; ?>" aria-expanded="false">
                <?php if($user['type'] == 'client') { ?>
                    <img class="user-avatar" src="<?= $user['photo'] ? $user['photo_full_path'].'?w=30&amp;h=30&amp;c=1' : \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($user['business_name'], $user['staff_id'], 30, null); ?>" width="30" height="30" />
                <?php }else{ ?>
                    <img class="user-avatar" src="<?= ($user['staff_id'] == 0 && $user['site_logo']) ? '/files/images/site-logos/' . $user['site_logo'] . '?w=30&amp;h=30&amp;c=1' : \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($username, $user['staff_id'], 30, empty($staff['photo']) ? null : $staff['photo']); ?>" width="30" height="30" />
                <?php } ?>

                <span class="drop-cont hidden-xs">
                <?php echo $username ?>
                <span class="caret"></span>
                    <?php
                    if($this->params['prefix']!='client'){
                        ?>
                        <p id="menu-subtitle"><?= isset($activeBranch) ? $allStaffBranches[$activeBranch] : $siteTime ?></p>
                        <?php
                    }
                    ?>
					</span>
            </a>
            <!-- dropdown -->
            <ul class="dropdown-menu w header-dropdowns">
                <li>
                    <div class="panel-heading b-light bg-light">
                        <strong class="hidden-xs"><?php __('My Account') ?></strong>
                        <span class="hidden visible-xs"><?php echo $username ?></span>
                        <small class=" hidden text-muted visible-xs"><?= isset($activeBranch) ? $allStaffBranches[$activeBranch] : $siteTime ?></small>
                    </div>
                </li>
                <?php if ($user['type'] == 'owner' && $user['staff_id'] == 0): ?>

                    <li><a href="<?php echo Router::url(array('controller' => 'owners', 'action' => 'change_email')); ?>"> <i class="fa fa-envelope middle"></i>
                            <?php __("Change Email") ?>
                        </a></li>
                    <li><a href="<?php echo Router::url(array('controller' => 'owners', 'action' => 'change_password')); ?>"> <i class="fa fa-lock middle"></i>
                            <?php __("Change Password") ?>
                        </a></li>
                    <?php if(showMobileApps()): ?>
                        <?php if(!isIos()){ ?>
                        <li><a href="/v2/owner/mobile-apps"><i class="fa fa-qrcode middle"></i><?php echo sprintf(__('%s Mobile Apps', true), Site_Full_name_NoSpace) ?></a></li>
                        <?php } ?>
                    <?php endif; ?>

                <?php elseif ($user['type'] == 'owner' and $user['staff_id'] != 0): ?>

                    <li><a href="<?php echo Router::url(array('controller' => 'staffs', 'action' => 'change_email')); ?>"> <i class="fa fa-envelope middle"></i>
                            <?php __("Change Email") ?>
                        </a></li>
                    <li><a href="<?php echo Router::url(array('controller' => 'staffs', 'action' => 'change_password')); ?>"> <i class="fa fa-lock middle"></i>
                            <?php __("Change Password") ?>
                        </a></li>
                    <?php if(showMobileApps()): ?>
                        <?php if(!isIos()){ ?>
                        <li><a href='/v2/owner/mobile-apps'><i class='fa fa-qrcode middle'></i> <?php echo sprintf(__('%s Mobile Apps', true), Site_Full_name_NoSpace) ?> </a></li>
                        <?php } ?>
                    <?php endif; ?>
                <?php elseif(getClientSettings("client_permission_edit_profile")): ?>
                    <li><a href="<?php echo Router::url(array('controller' => 'clients', 'action' => 'change_email', 'client' => 1)); ?>"> <i class="fa fa-envelope middle"></i>
                            <?php __("Change Email") ?>
                        </a></li>
                    <li><a href="<?php echo Router::url(array('controller' => 'clients', 'action' => 'change_password', 'client' => 1)); ?>"> <i class="fa fa-lock middle"></i>
                            <?php __("Change Password") ?>
                        </a></li>

                    <?php if(QR_ENABLED && !getAuthClient()): ?>
                        <?php if(!isIos()){ ?> 
                        <li><a href="<?php echo Router::url(array('controller' => 'qr', 'action' => 'client_show', 'client' => 1)); ?>"><i class='fa fa-qrcode middle'></i> <?php echo sprintf(__('%s Mobile Apps', true), Site_Full_name_NoSpace) ?> </a></li>
                        <?php } ?>
                    <?php endif; ?>

                    <li><a href="<?php echo Router::url(array('controller' => 'clients', 'action' => 'change_settings', 'client' => 1)); ?>"> <i class="fa fa-cog middle"></i>
                            <?php __("Edit My Details") ?>
                        </a></li>
                <?php endif; ?>

                    <?php
                        if (showEmailPreferences() && $user['type'] == 'owner') {
                            $mailSubscriptionUrl = '/v2/owner/email-prefrence/subscription';

                            ?>
                    <li><a href="<?= $mailSubscriptionUrl ?>"> <i class="fa fa-mail-reply middle"></i> <?php __("E-mail preferences") ?> </a> </li>
                    <li class="divider"></li>
                <?php } ?>


                <?php

                if ( $user['is_super_admin']) { ?>

                    <?php if (!strtotime($user['expiry_date'])) { ?>
                        <li><a href="<?php echo Router::url(array('controller' => 'sites', 'action' => 'upgrade')) ?>" target="_blank"><i class="fa fa-arrow-circle-up middle"></i><?php __('Upgrade Subscription') ?></a></li>
                    <?php } else { ?>
                        <?php if(!isIos()){ ?>
                        <li><a href="<?php echo Router::url(array('controller' => 'sites', 'action' => 'renew')) ?>" target="_blank"><i class="fa fa fa-retweet middle"></i><?php __('Renew Subscription') ?></a></li>
                        <?php } ?>
                    <? } ?>
                    <?php
                    $betaVersion = getCurrentSite('beta_version') == '1';
                    $text = $betaVersion ? 'Back to older version' : 'Move to Beta';
                    $arrow = $betaVersion ? 'down' : 'up';
                    ?>
                    <!--                            <li><a href="--><?php //echo Router::url(array('controller' => 'sites', 'action' => 'update_beta')) ?><!--"><i class="fa fa-arrow-circle---><?//= $arrow; ?><!-- middle"></i>--><?php //__($text) ?><!--</a></li>-->
                <?php } ?>
                <?php

                if (isset($staffBranches) && $this->params['prefix']!='client') {
                    foreach ($staffBranches as $staffBranchID => $staffBranch) {
                        $rr=$_GET;
                        unset($rr['url']);
                        $queryParams = !empty($rr) ? '?' . http_build_query($rr) : '';
                        $lastUrl = ($this->here !== '/' || !empty($queryParams)) ? $this->here . $queryParams : null;
                        // Build the URL with or without the 'last_url' parameter
                        $urlParams = array(
                            'client' => false,
                            'owner' => 'owner',
                            'controller' => 'staffs', 
                            'action' => 'update_current_branch',
                            $staffBranchID
                        );
                        if ($lastUrl) {
                            $urlParams['?'] = ['last_url' => base64_encode($lastUrl)];
                        }
                        ?>
                        <li><a href="<?php echo Router::url($urlParams); ?>" title="<?= sprintf(__('Switch to %s', true), $staffBranch) ?>"><i class="fa fa-building middle"></i><?= $staffBranch ?></a></li>
                    <?php }
                } ?>
                <li class="divider"></li>
                
                <li class="visible-xs">
                    <a href="https://docs.<?= Domain_Short_Name ?>" target="_blank">
                        <?= __('System Manual', true); ?>
                    </a>
                    <a href="<?= Router::url(['controller'=>'sites_enquiries','owner'=> true,'action' => 'index']) ?>" class="submit-feedback">
                        <?= __('Submit Feedback', true); ?>
                    </a>
                </li>
                
                <li class="divider"></li>

                <?php
                if ($user['type'] == 'owner') {
                    $url = Router::url(array('controller' => 'owners', 'action' => 'logout'));
                } else {
                    $url = Router::url(array('controller' => 'clients', 'action' => 'logout', 'client' => true));
                }
                ?>
                <li class=""> <a class="panel-heading b-light bg-light" href="<?php echo $url; ?>"><i class="fa fa-lock middle"></i>
                        <?php __("Log out") ?>
                    </a> </li>
            </ul>
            <!-- / dropdown -->
        </li>



    </ul>
    <div class="clear"></div>

<?php } ?>



<div class="modal" id="Shownoti" tabindex="-1" role="dialog" aria-labelledby="ShownotiLabel">
    <div class="modal-dialog modal-md"  role="document">
        <div class="modal-content">
            <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
            <div class="modal-header">
                <h4 class="modal-title" id="ShowactionLabel"><? echo __('Details'); ?></h4>
            </div>
            <div id="Shownoti_row" class="modal-body"> <? echo __('Loading ...'); ?> </div>
        </div>
    </div>
</div>

<!-- Start - Notification div will be loaded inside notification drop down list -->
<div id="notification-template" class="notification-item notification-item-hover border-0" hidden>
    <div class="d-flex justify-content-between">
        <div class="text">
            <a class="view-url" href="#">
                <p class="text-muted font-weight-medium message-content">
                    <!-- message will be here -->
                </p>
            </a>
        </div>
    </div>
    <div class="notification-meta d-flex justify-content-between">
        <span class="text-muted opacity-50 font-weight-bold date notification-date"><!-- date will be here --></span>
        <a href="#" class="text-success font-weight-bold link view-url"><?php __('View'); ?></a>
        <a href="#" data-dismiss-all="<?php __('Dismiss'); ?>" class="text-danger font-weight-bold dismissed-url">
            <i class="mdi mdi-minus-circle"></i>
            <span class="mr-1"><?php __('Dismiss'); ?></span>
        </a>
    </div>
</div>
<!-- End - Notification div will be loaded inside notification drop down list -->
<div id="no-notification-div" class="alert alert-warning text-center mb-0" hidden>
    <?php __('No active notifications to list'); ?>
</div>
<link id="help-css" rel="stylesheet" type="text/css" href="/dist/app/js/pages/help/help.styles.css" media="all">

<script>
    var notificationCountUrl = '/v2/api/owner/notifications/count';
    var notificationUrl = "/v2/api/owner/notifications";
    <?php  if(getAuthClient()){ ?>
    notificationCountUrl = '/v2/api/client/notifications/count';
    notificationUrl = "/v2/api/client/notifications";
    <?php  }?>
</script>

<script src='/v2/js/notifications/drop-down-notification_v<? echo JAVASCRIPT_VERSION; ?>.js'></script>
<script src='<?php echo CDN_ASSETS_URL ?>js/layout/plugin_simplebar.js?v=<? echo JAVASCRIPT_VERSION; ?>'></script>

