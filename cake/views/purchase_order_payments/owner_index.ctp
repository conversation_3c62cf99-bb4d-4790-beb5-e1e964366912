<div class="invoicePayments index">
    <div class="pages-head po-payments-pages-head">
    <?php if (!isset($_GET['from_supplier'])) { ?>
        <h1 class="ml-2 ml-md-0"><?php __('Payments'); ?></h1>
    <?php } ?>
        <?php if(!isset($_GET['box'])):?>
            <div class="right">
                <div class="top-actions">
                    <div class="button-toggle btn-group right">
                        <a class="btn btn-lg btn-default date-picker" href="/owner/reports/list#purchase_orders" tabindex="1" title="<?php __('Reports') ?>"> <i class="fa fa-pie-chart"></i> </a>
                    </div>
                </div>
                <div class="clear"></div>
            </div>
        <?php endif;?>


    </div>
    <?php if (isset($_GET['from_supplier'])) { ?>
        <base target="_top">
        <style>
            .po-payments-pages-head {
                display: none;
            }
        </style>
    <?php } ?>
    <style>
        .fixed{
            top:0;
            position:fixed;
            width:auto !important;
            display:none;
            border:none;
            z-index: 99;
        }
    </style>

    <script>
        (function($) {
            $.fn.fixMe = function() {
                return this.each(function() {
                    var $this = $(this), $t_fixed;
                    function init() {
                        $this.wrap('<div class="jcontainer" style="" />');
                        $t_fixed = $this.clone();
                        $t_fixed.find('tbody').remove().end().addClass('fixed').insertBefore($this);
                        resizeFixed();
                    }
                    function resizeFixed() {
                        $t_fixed.find('th').each(function(index) {
                            $(this).css('width', $this.find('th').eq(index).outerWidth() + 'px');
                        });
                    }
                    function scrollFixed() {
                        var offset = $(this).scrollTop(), tableOffsetTop = $this.offset().top, tableOffsetBottom = tableOffsetTop + $this.height() - $this.find('thead').height();
                        if (offset < tableOffsetTop || offset > tableOffsetBottom)
                            $t_fixed.hide();
                        else if (offset >= tableOffsetTop && offset <= tableOffsetBottom && $t_fixed.is(':hidden'))
                            $t_fixed.show();
                    }
                    $(window).resize(resizeFixed);
                    $(window).scroll(scrollFixed);
                    init();
                });
            };
        }(jQuery));
        $(document).ready(function() {
            $('table').fixMe();
            $('.up').click(function() {
                $('html, body').animate({scrollTop: 0}, 2000);
            });
        });
    </script>
    <?php
//    debug($filters);
     if(!isset($_GET['box'])) {
         echo $list->filter_form($modelName, $filters);
     }

    if (count($PurchaseOrderPayments) > 0) {
        $dateFormats = getDateFormats('std');
        $dateFormat = $dateFormats[getAuthOwner('date_format')];


        $PurchaseOrderPayments[0]['payment_methods'] = $payment_methods;
        $owner = getAuthOwner();
        $full_name = $owner['first_name'] . ' ' . $owner['last_name'];

        $fields = array(
            'PurchaseOrderPayment.id' => array('edit_link' => array('action' => 'view', '%id%')),
            'PurchaseOrder.no' => array('edit_link' => array('controller' => 'invoices', 'action' => 'view', '%id%'), 'target' => 'blank', 'title' => __('Invoice No', true)),
            'PurchaseOrder.date' => array('edit_link' => array('action' => 'view', '%id%'), 'date_format' => $dateFormat),
            'Name' => array('php_expression' => '".$row["PurchaseOrder"]["first_name"]." ".$row["PurchaseOrder"]["last_name"]."', 'edit_link' => array('action' => 'view', '%id%')),
            'Amount' => array('php_expression' => '".format_price($row[$basicModel]["amount"],$row["Invoice"]["currency_code"])."'),
            'Payment_method' => array('php_expression' => '".$data[0]["payment_methods"][$row[$basicModel]["payment_method"]]."'),
            'PurchaseOrderPayment.status' => array('format' => 'array', 'options' => array('items_list' => $statuses)),
        );

        foreach ($PurchaseOrderPayments as $key => &$payment) {
            if (isset($payment['PurchaseOrderPayment']['added_by']) and $payment['PurchaseOrderPayment']['added_by'] == 1) {
                $payment['PurchaseOrderPayment']['Staff_name'] = $payment['Staff']['id'] == "" ? $full_name : $payment['Staff']['name'];
            } else {
                $payment['PurchaseOrderPayment']['Staff_name'] = __("Client", true);
            }
        }

        if (ifPluginActive(StaffPlugin) && check_permission(Invoices_Add_Payments_to_All)) {
            $fields['Staff_name2'] = array('title' => __('By', true), 'php_expression' => '".$row["PurchaseOrder"]["Staff_name"]."');
        }
//	$links[]=$html->link(__('Edit', true), array('action' => 'edit', '%id%'), array('class' => 'Edit'));
        $links[] = $html->link(__('View', true), array('action' => 'view', '%id%'), array('class' => 'View'));
        $staff_id = getAuthStaff('id');
        if (check_permission(Invoices_Add_Invoice_Payments) && !check_permission(Invoices_Add_Payments_to_All)) {
            $links[] = array('php_expression' => '$row["PurchaseOrder"]["staff_id"]==' . $staff_id . ';', 'url' => $html->link(__('Edit', true), array('action' => 'edit', '%id%'), array('class' => 'Edit', 'title' => __('Edit', true))));
            $links[] = array('php_expression' => '$row["PurchaseOrder"]["staff_id"]==' . $staff_id . ';', 'url' => $html->link(__('Delete', true), array('action' => 'delete', '%id%'), array('class' => 'Delete', 'title' => __('Delete', true))));
        } elseif (check_permission(Invoices_Add_Payments_to_All)) {
            $links[] = array('url' => $html->link(__('Edit', true), array('action' => 'edit', '%id%'), array('class' => 'Edit', 'title' => __('Edit', true))));
            $links[] = $html->link(__('Delete', true), array('action' => 'delete', '%id%'), array('class' => 'Delete'));
        }




        if (check_permission(Invoices_Add_Payments_to_All)) {
            $multi_select_actions = array('delete' => array('action' => Router::url(array('action' => 'delete')), 'confirm' => true));
        }

        /* if(IS_PC)
          if (check_permission(Invoices_Add_Payments_to_All)){
          echo $list->adminIndexList($fields, $PurchaseOrderPayments, $links, true, $multi_select_actions);
          }else{
          echo $list->adminIndexList($fields, $PurchaseOrderPayments, $links, false, $multi_select_actions);
          }
          else */
        if (check_permission(Invoices_Add_Payments_to_All)) {
            echo $list->adminResponsiveList($PurchaseOrderPayments, 'purchase_order_payment_row', array('actions' => $links), array('multi_select' => true, 'multi_select_actions' => $multi_select_actions));
        } else {
            echo $list->adminResponsiveList($PurchaseOrderPayments, 'purchase_order_payment_row', array('actions' => $links));
        }
    } else {
        ?>

        <?php
        if ($search_filter == "true") {
            ?>
            <div class="Notemessage"><?php echo CakeString::insert(__('No result for your filter, :itemLink to clear Filters', true), array('itemName' => __('invoice payment', true), 'itemLink' => $html->link(__('click here', true), array('owner' => false, 'action' => 'reset_filter')))); ?></div>
        <?php } else { ?>
            <div class="Notemessage"><?php echo CakeString::insert(__('No :itemName found ', true), array('itemName' => __('payments', true))); ?></div>
        <?php } ?>



    <? } ?>
    <div class="clear"></div>
</div>
