<?php

/**
 * @property HtmlHelper $Html
 * @property PaginatorHelper $Paginator
 * @property JavascriptHelper $Javascript
 * @property FormHelper $Form
 */
class ListHelper extends AppHelper {

    var $helpers = array('Html', 'Paginator', 'Javascript', 'Form', 'Session', 'Time');

    function AdminIndexListRelations($fields, $data = array(), $actions = array(), $multi_select=false, $multi_select_actions=array(), $params=array()) {

        $out = '';
        $urls = array();
        $paging = !isset($params['no_paging']);

        if (empty($params['no_paging']) || !$params['no_paging']) {
            $url_params = $this->params['url'];
            unset($url_params['url'], $url_params['page'], $url_params['sort'], $url_params['direction'], $url_params['ext']);
            $url = array('?' => $url_params);
            if (!empty($this->params['prefix'])) {
                $url[$this->params['prefix']] = true;
            }
            $this->Paginator->options(array('url' => $url));
        }

        if ($multi_select) {
            $out .='<form id="MultiSelectForm" method="post" name="MultiSelectForm" action="/" >';
        }

        $basicModel = $this->params['models'][0];
        if (!empty($fields['basicModel'])) {
            $basicModel = $fields['basicModel'];
            unset($fields['basicModel']);
        }


        $out .= '<table id="Table" class="table table-small-font  table-striped b-t b-light" cellpadding="0" cellspacing="0" width="100%">';
        //generate header
        $out.= '<tr class="table-header">';
        $out.=$multi_select ? '<th class="multiSelect multi-select"><input type="checkbox" onclick=""/></th>' : '';
        $headers = array();
        $display_order = false;

        foreach ($fields as $name => $spec) {
            $model = $basicModel;
            $t = $name;
            if (strpos($name, '.')) {
                $a = explode('.', $name);
                $model = $a[0];
                $t = $a[1];
            }

            $title = empty($spec['title']) ? __(Inflector::humanize($t), true) : __($spec['title'], true) ;
            $sort = empty($spec['sort']) ? $name : $spec['sort'];
            $class = '';
            $urlSort = empty($this->params['url']['sort']) ? '' : $this->params['url']['sort'];
            if ($urlSort == $sort) {
                $class = $this->Paginator->sortDir();
            }
            if (strpos($name, '.')) {
                $out.="<th class=\"$class\">";
                if (!empty($spec['format']) && low($spec['format']) == 'checkbox') {
                    $t = low($t);
                    $out.="<input type='checkbox' class='CheckboxList' rel='{$t}'/>  ";
                }
                $out.="$title</th>";
            }
            else
                $out.="<th class=\"$class\">" . $title . "</th>";

            if (low($t) == 'display_order') {
                $do_action = array('action' => 'update_display_order');
                if (isset($params['update_display_order_link'])) {
                    $do_action = $params['update_display_order_link'];
                }
                $multi_select_actions['Update display order'] = array('action' => Router::url($do_action));
                $urls["display_order"] = Router::url($do_action);
            }
            if (!empty($spec['format']) && low($spec['format']) == 'checkbox') {
                $do_action = array('action' => 'update_active', low($t));
                $multi_select_actions["Update $title"] = array('action' => Router::url($do_action));
                $urls[low($t)] = Router::url($do_action);
            }
        }

        if (is_array($actions) && !empty($actions)) {
            $out .= '<th class="action">' . __('Actions', true) . '</th>';
        }
        $out .= "</tr>";

        foreach ($data as $row) {

            $cells = array();
            if ($multi_select)
                $cells[] = '<input class="check_row" type="checkbox" value="' . $row[$basicModel]['id'] . '" name="ids[]" /> ';
            foreach ($fields as $name => $spec) {

                if (strpos($name, '.')) {
                    $a = explode('.', $name);

                    $model = $a[0];
                    $t = $a[1];

                    $cell = $row[$model][$t];
                    if (low($t) == 'display_order') {
                        $cell = "<input type=\"text\" value=\"{$row[$model][$t]}\" class=\"AdditionOption\" rel='display_order' name=\"display_order_{$row[$basicModel]['id']}\"  size=\"5\" />";
                    }

                    if (isset($spec['format'])) {
                        if ($spec['format'] == 'checkbox') {
                            $t = low($t);
                            $check = 0;
                            if ($cell) {
                                $check = 1;
                            }
                            $cell = '<input  type="hidden" value="0" name="' . $t . '_' . $row[$basicModel]['id'] . '" /> ';
                            $cell .='<input ' . (($check) ? 'checked=checked' : '') . ' class="' . $t . '_checkbox AdditionOption" type="checkbox" rel="' . low($t) . '" value="1" name="' . $t . '_' . $row[$basicModel]['id'] . '" /> ';
                            $cell .= ( $check) ? __('Yes',true) : __('No',true);
                        } elseif ($spec['format'] == 'bool') {
                            if ($cell)
                                $cell = __("Yes", true); else
                                $cell = __("No", true);
                        }elseif ($spec['format'] == 'multi') {
                            $cell =' <ul class="dashed" ><div class="chzn-container  chzn-container-multi chzn-container-active" style="width: 255px;">';

                            foreach($row[$model] as $multi){
                                $cell .= '<li  class="">- <span>'.$multi[$t].'</span><a rel="1" class="search-choice-close" href="javascript:void(0)"></a></li>';
                            }
                            $cell.="</ul> </div>";
                        }
                        elseif ($spec['format'] == 'substr') {
                            $string = $cell;
                            $start = !empty($spec['options']['start']) ? $spec['options']['start'] : 0;
                            $length = !empty($spec['options']['length']) ? $spec['options']['length'] : null;
                            $cell = substr($string, $start, $length);
                        } elseif ($spec['format'] == 'url') {
                            $cell = "<a href='{$cell}' target='_blank'>{$cell}</a>";
                        } elseif ($spec['format'] == 'image') {
                            $image_src = $cell;
                            $options = '';
                            if (!empty($spec['options']['width'])) {
                                $options.="width={$spec['options']['width']}";
                            }
                            if (!empty($spec['options']['height'])) {
                                $options.="height={$spec['options']['height']}";
                            }
                            $cell = "<img src='$image_src' $options />";
                        } elseif ($spec['format'] == 'get_from_array') {
                            $selected = $cell;
                            $items_list = !empty($spec['options']['items_list']) ? $spec['options']['items_list'] : array();
                            $selected = !empty($selected) ? $selected : 0;
                            $empty = !empty($spec['options']['empty']) ? $spec['options']['empty'] : '';

                            $cell = empty($items_list[$selected]) ? $empty : $items_list[$selected];
                        } else {
                            $cell = sprintf($spec['format'], $cell);
                        }
                    } elseif (isset($spec['date_format'])) {
                        $cell = date($spec['date_format'], strtotime($cell));
                    }
                } elseif (isset($spec['format'])) {
                    if ($spec['format'] == 'bool') {
                        if ($cell) {
                            $cell = __("Yes", true);
                        } else {
                            $cell = __("No", true);
                        }
                    } elseif ($spec['format'] == 'image' && !empty($spec['options'])) {
                        eval('$src = "' . $spec['options']['src'] . '";');
                        $other_options = '';
                        if (!empty($spec['options']['width'])) {
                            $other_options = "width = '{$spec['options']['width']}'";
                        }
                        if (!empty($spec['options']['height'])) {
                            $other_options .= "height = '{$spec['options']['height']}'";
                        }
                        if (!empty($spec['options']['alt'])) {
                            $alt = $spec['options']['alt'];
                            if (strpos($spec['options']['alt'], '$row') !== false) {
                                eval('$alt = "' . $spec['options']['alt'] . '";');
                            }
                            $other_options .= "alt = '{$alt}'";
                        }
                        $cell = "<img src='{$src}' $other_options />";
                    } elseif ($spec['format'] == 'substr' && !empty($spec['options'])) {
                        eval('$string = "' . $spec['options']['string'] . '";');

                        $start = !empty($spec['options']['start']) ? $spec['options']['start'] : 0;
                        $length = !empty($spec['options']['length']) ? $spec['options']['length'] : null;
                        $cell = substr($string, $start, $length);
                    } elseif ($spec['format'] == 'get_from_array' && !empty($spec['options'])) {
                        eval('$selected = "' . $spec['options']['selected'] . '";');
                        $items_list = !empty($spec['options']['items_list']) ? $spec['options']['items_list'] : array();
                        $selected = !empty($selected) ? $selected : 0;

                        $empty = !empty($spec['options']['empty']) ? $spec['options']['empty'] : 'None';

                        $cell = empty($items_list[$selected]) ? $empty : $items_list[$selected];
                    } elseif ($spec['format'] == 'check' && !empty($spec['options'])) {
                        eval('$conditions = "' . $spec['options']['conditions'] . '";');
                        eval('$true = "' . $spec['options']['true'] . '";');
                        eval('$false = "' . $spec['options']['false'] . '";');

                        $true = !empty($true) ? $true : '';
                        $false = !empty($false) ? $false : '';

                        $cell = ($conditions) ? $true : $false;
                    } else {

                        $cell = sprintf($spec['format'], $cell);
                    }
                } else {
                    eval('$cell="' . $spec['php_expression'] . '";');
                }
                if (!empty($spec['edit_link'])) {

                    preg_match('/%(.+)%/', Router::url($spec['edit_link']), $matches);

                    $spec['edit_link'] = str_ireplace("%{$matches[1]}%", $row[$model][$matches[1]], $spec['edit_link']);

                    $cell = $this->Html->link($cell, $spec['edit_link']);
                }
                $cells[] = $cell;
            }
            $cell = '';
            if (is_array($actions) && !empty($actions)) {
                foreach ($actions as $action) {
                    preg_match('/%(.+)%/', $action, $matches);
                    $cell .= '<li>' . str_replace("%{$matches[1]}%", $row[$basicModel][$matches[1]], $action) . '</li>';
                }

                $cells[] = '<ul class="action">' . $cell . '</ul>';
            }
            $out .= $this->Html->tableCells($cells);
        }
        $out .= '</table>';

        if ($paging) {

            $out .='<div class="results-counter right">' . $this->Paginator->counter(array('format' => '<strong> %start%-%end% </strong>  ' . __('of', true) . ' %count% ' . __('results shown', true) . '</div>'));
        }

        if ($multi_select) {
            if (is_array($multi_select_actions) && sizeof($multi_select_actions)) {
                $out .='<div class="listing-actions"><div class="multi_select_operations bulk-actions">
					<label for="select_action"   >' . __('With Selected', true) . '</label>
					 <select style="display:none;" id="select_action" name="select_action2"  > ';
                foreach ($multi_select_actions as $title => $params) {
                    $out .='<option value="' . $params['action'] . '">' . __(Inflector::humanize($title), true) . '</option>';
                }
                $out.='</select> <input type="button" value="'.__("Delete",true).'" class="GoSubmit btn btn-md btn-primary" />  </div></div>';
            }
        }

        if ($paging) {
            $out.="<div class='Paging'>";
            $out.= $this->paging();
            $out.="</div>";
        }

        $urls = json_encode($urls);

        $out.= <<<CODEBLOCK
        <style>
            ul.dashed {
                list-style: none;
                padding-left: 0;
            }

            ul.dashed > li {
                margin-left: 15px;
            }

            /* Prevent nested li's from getting messed up */
            ul.dashed > li::before {
                content: "-";
                margin-left: -15px;
            }
        </style>
        <script type="text/javascript">
        var urls = $urls;
        $(document).ready(function(){
            $('.AdditionOption,.CheckboxList').bind('change click',function(){
                rel = $(this).attr('rel');
                url = urls[rel];
                $('#select_action').val(url);
            });
            $('.GoSubmit').click(function(){
                $('#MultiSelectForm').get()[0].action= $('#select_action').val();
                $('#MultiSelectForm').submit();
                return false;
            });
            $('.multiSelect input').click(function(){
                $('#MultiSelectForm input.check_row').attr('checked',$(this).attr('checked'));
            });
            $('.CheckboxList').click(function(){
                rel = $(this).attr('rel');
                $("."+rel+"_checkbox").attr('checked',$(this).attr('checked'));
            });
        });
        </script>
CODEBLOCK;
		if($_SESSION['conditioned'])
		{
			//we use this variable to enable dropdown that allow us to pick if we wanted to perform the filter_actions on a selected items or all filtered items
			//in the adminresponsive list this flag is set to true in filter_params if the count(conditions) > 0
			//so if the flag is set to true and its not an admin responsive list we want to set it to false so i dosent confilct with any view that uses that variable
			$_SESSION['conditioned'] = false;
		}
        return $out;
    }
    function adminResponsiveList2($data, $element, $element_data = array(), $params = array(),$action_selected_type = false) {

        $basicModel = $this->params['models'][0];

        if (!empty($params['basicModel'])) {
            $basicModel = $params['basicModel'];
        }

        $out = '';
        // fix third parameter to be array instead of boolean (php8)
        $out.= $this->Html->css(array('time-tracker.css?v=5', 'fontello.css'), false, ['inline' => false]);
        if (!empty($params['multi_select'])) {
					//check all

//            $out .='<div style="display:none;" id="select_all_div" class="clip-check check-info">
//			<input type="checkbox" id="select_all_chk" value=""  />
//			<label for="select_all_chk">' . __('All', true) . '</label>
//			</div>			';
            $out .='<form id="MultiSelectForm" method="post" name="MultiSelectForm" action="/" >';
        }
        $out.='<div class="index entry-content overflow-auto' . (IS_PC ? 'pc' : '') . '">
        <ul class="day-view-entry-list">';
        foreach ($data as $i => $row) {
			//debug ($element);
            $element_data['row'] = $row;
            $element_data['index'] = $i;
            $El = ClassRegistry::getObject('view');
			//debug ( $El );
            if (!empty($params['multi_select'])){
                $multiActionFieldModel = $basicModel;
                $multiActionField = 'id';
                if(isset($params['multi_select_model']))
                {
                    $multiActionFieldModel = $params['multi_select_model'];
                }

                if(isset($params['multi_select_field']))
                {
                    $multiActionField = $params['multi_select_field'];
                }

                $element_data['check_box'] = '<div class="clip-check check-info">
			<input class="responsive_check_row check-input check-item" type="checkbox" id="ch_' . $row[$multiActionFieldModel][$multiActionField] . '" value="' . $row[$multiActionFieldModel][$multiActionField] . '" name="ids[]" />
			<label for="ch_' . $row[$multiActionFieldModel][$multiActionField] . '"></label>
</div>			';
            }
            $out.= $El->element($element, $element_data);
        }
        $out.='</ul></div>';

        $paging = empty($params['no_paging']);
        if ($paging) {
            $url = array();
            if (!empty($this->Paginator->options['url'])) {
                $url = $this->Paginator->options['url'];
            }
            /*
             warning suppress
             Change default value to array . 
             */
            $extra_params = $El->passedArgs ?? [];
            
            if (!empty($extra_params))
                $url = am($url, $extra_params);
                
            // Remove duplicated params .  
            $url = array_unique($url);
                 
            $url_params = $this->params['url'];
            unset($url_params['url'], $url_params['page'], $url_params['sort'], $url_params['direction'], $url_params['ext']);
            $url = am(array('?' => $url_params), $url);



            if (!empty($this->params['prefix'])) {
                $url[$this->params['prefix']] = true;
            }
            $this->Paginator->options(array('url' => $url));
        }
        if (!empty($params['multi_select'])) {
            if (is_array($params['multi_select_actions']) && sizeof($params['multi_select_actions'])) {

                $listing_actions = '';
                foreach ($params['multi_select_actions'] as $title => $ps) {
                    $title2 = empty($ps['title']) ? __(Inflector::humanize($title), true) : $ps['title'];
                    //                    $out .='<a href="#" class="GoSubmit btn btn-md btn-'.(strpos($ps['action'],'dismiss')===false && strpos($ps['action'],'delete')===false?'primary':'danger').'" rel="' . $ps['action'] . '"><i class=""></i> ' . $title2 . '</a>';
                    // warning suppress
                    $class=$ps['class']??'GoSubmit';
                    $extra_data=isset($ps['data'])?'data="'.$ps['data'].'"':'';

                    $listing_actions .= '<li><a href="#" '.($extra_data).' class=" '. $class.'" rel="' . $ps['action'] . '"><i class=""></i> ' . $title2 . '</a></li>';
                    if(isset($ps['bulk_data'])){
				        $out .= "<input type='hidden' name='bulk_data' value='{$ps["bulk_data"]}'>";
                    }
                }
				$out .= $this->js_listing_actions($listing_actions);
                $out .='</div>';
            }
            $out.="</form>";
        }

        if ($paging) {
//            $out .='<div class="results-counter right">' . $this->Paginator->counter(array('format' => '<strong> %start%-%end% </strong>  ' . __('of', true) . ' %count% ' . __('results shown', true) . '</div>'));
//            $out.="<div class='Paging'>";
//            $out.= $this->paging(true, true, IS_MOBILE ? false : true);
            $out .= $this->jspaging(true, true, IS_MOBILE ? false : true);

//			die(debug(htmlentities($paging_data)));
//            $out.="</div>";
        }

		$message2=str_replace('\'','\'\\\'',__('Please select items first by clicking on the small left box',true));
        if (!empty($params['multi_select'])) {
            $deleteConfirmMessage = __('Do you want to continue with this action?', true);
            $out .= <<<CODEBLOCK
        <script type="text/javascript">
        
            function set_count_text(inc = 0)
            {
                var count = $(".check-item:checked").length;
                var actionSelectedCount = $('#action-selected-count');
                
                index_action_select_type = $('input[name="index_action_select_type"]').attr('value') == 'all';
				if( $('input[name="index_action_select_type"]').attr('value') == 'all')
                {
                 
                   let count = parseInt($('#nav-of-record').text(), 10);
                   let increment = parseInt(inc, 10);
                   count += increment;
                    
                   $('#nav-of-record').text(count);
                   actionSelectedCount.html("<small>(" + count + " SELECTED_ROWS" + ")</small>");
                   return;
                }


                if(count === 0 ) {
                    actionSelectedCount.html('');
                    return ;
                }
                actionSelectedCount.html("<small>(" + count + " SELECTED_ROWS" + ")</small>");
            }
            
            $(".check-item").click(function () {
                if ($(this).is(':checked')) {
                    set_count_text(1);
                } else {
                    set_count_text(-1);
                        var value = $(this).val();
                        var uniqueClass = 'excluded-id-' + value;
                        if ($('.' + uniqueClass).length === 0) {
                            $('<input type="hidden" name="ids_excluded[]" value="' + value + '" class="' + uniqueClass + '">').appendTo('#MultiSelectForm');
                        }
                }
            });
            
	    $(document).on('click','.multiple_template',function(){
				index_action_select_type = $('input[name="index_action_select_type"]').attr('value') == 'all' ;
				if($('.check-input:checked').length>0 || $('input[name="index_action_select_type"]').attr('value') == 'all')
				{
					//we eneter here if the checked items are > 0 or he selected all the filter results
					$('#MultiSelectForm').get()[0].action= $(this).attr('rel');
					if(!index_action_select_type)
					{
					//we enter here if he didnt select all filtered pages
					$('#MultiSelectForm').submit();
					}
					else
						{
							$('#MultiSelectForm').submit();
						}
				}
				else
				{
					alert('$message2');
                    return false;
				}
        });

	    $(document).on('click','.GoSubmit',function(){
				index_action_select_type = $('input[name="index_action_select_type"]').attr('value') == 'all' ;
				if($('.check-input:checked').length>0 || $('input[name="index_action_select_type"]').attr('value') == 'all')
				{
					//we eneter here if the checked items are > 0 or he selected all the filter results
					$('#MultiSelectForm').get()[0].action= $(this).attr('rel');
                    if (confirm('$deleteConfirmMessage')) {
					if(!index_action_select_type)
					{
					//we enter here if he didnt select all filtered pages
					$('#MultiSelectForm').submit();
					}
					else
						{
				//we enter here if he select all filtered pages

							$('#MultiSelectForm').submit();
						}
					}

				}
				else
				{
					alert('$message2');
                    return false;
				}
            });

		$('#select_all_chk').on('click',function(){
			if($(this).is(':checked')){
				$('.check-input').attr('checked','checked');
			}else{
				$('.check-input').removeAttr('checked');
			}
            set_count_text();
			clearTimeout(hider);
			hider=setTimeout(function() {\$("#select_all_div").hide("slow")}, 5000);
			highlight_selected();
	    });
		var hider=0;
		function highlight_selected()
		{
			$('.responsive_check_row:checked').closest('li').addClass('highlighted');
			$('.responsive_check_row:not(:checked)').closest('li').removeClass('highlighted');
			//to remove the filer all pages inputs you found it in the views/elements/top_bar_actions
			//item_checked();
		}
		$(function(){
			$('.responsive_check_row').change(function(){
				highlight_selected();
			});
			$('.responsive_check_row:first').change(function(){
				$('#select_all_div').show('slow');
				if(hider!=0)
				clearTimeout(hider);
				hider=setTimeout(function() {\$("#select_all_div").hide("slow")}, 5000);

			});
			highlight_selected();
		});

        </script>
        <script src="/js/bulk/bulk.js"></script>

CODEBLOCK;
        }
        $out = str_replace("SELECTED_ROWS", __('Selected', true), $out);
		if (isset($paging_data) && $paging_data)
            $out.= <<<CODEBLOCK
        <script type="text/javascript">
		$(function(){
			$('td.clickable,div.clickable').hover(function(){ $(this).closest('li').addClass('hovered');},function(){ $(this).closest('li').removeClass('hovered');});
		})
        </script>
CODEBLOCK;

        if (IS_PC)
            $out.= <<<CODEBLOCK
        <script type="text/javascript">
		$(function(){
			$('td.clickable,div.clickable').hover(function(){ $(this).closest('li').addClass('hovered');},function(){ $(this).closest('li').removeClass('hovered');});
		})
        </script>
CODEBLOCK;

// warning suppress
if(array_key_exists('conditioned', $_SESSION) && $_SESSION['conditioned'])
		{
			//we use this variable to enable dropdown that allow us to pick if we wanted to perform the filter_actions on a selected items or all filtered items
			//in the adminresponsive list this flag is set to true in filter_params if the count(conditions) > 0
			//so if the flag is set to true and its not an admin responsive list we want to set it to false so i dosent confilct with any view that uses that variable
			$_SESSION['conditioned'] = false;
		}

        return $out;
    }

    function adminResponsiveList($data, $element, $element_data = array(), $params = array(),$action_selected_type = false) {
        $basicModel = $this->params['models'][0];

        if (!empty($params['basicModel'])) {
            $basicModel = $params['basicModel'];
        }

        $out = '';
        $out.= $this->Html->css(array('time-tracker.css?v=5', 'fontello.css'), false, false, false);
        if (!empty($params['multi_select'])) {
            $out .='<div style="display:none;" id="select_all_div" class="clip-check check-info">
			<input type="checkbox" id="select_all_chk" value=""  />
			<label for="select_all_chk">' . __('All', true) . '</label>
			</div>';
            $out .='<form id="MultiSelectForm" method="post" name="MultiSelectForm" action="/" >';
        }
        $out.='<div class="index entry-content bordered overflow-auto' . (IS_PC ? 'pc' : '') . '">
        <ul class="day-view-entry-list">';
        foreach ($data as $i => $row) {
			//debug ($element);
            $element_data['row'] = $row;
            $element_data['index'] = $i;
            $El = ClassRegistry::getObject('view');
			//debug ( $El );
            if (!empty($params['multi_select']))
                $element_data['check_box'] = '<div class="clip-check check-info">
			<input class="responsive_check_row check-input" type="checkbox" id="ch_' . $row[$basicModel]['id'] . '" value="' . $row[$basicModel]['id'] . '" name="ids[]" />
			<label for="ch_' . $row[$basicModel]['id'] . '"></label>
</div>			';
            $out.= $El->element($element, $element_data);
        }
        $out.='</ul></div>';

        $paging = empty($params['no_paging']);
        if ($paging) {
            $url = array();
            if (!empty($this->Paginator->options['url'])) {
                $url = $this->Paginator->options['url'];
            }

            // warning suppress
            $extra_params = $El->passedArgs ?? [];
            if (!empty($extra_params))
                $url = am($url, $extra_params);
            if (is_array($url)) $url = array_unique($url);
            $url_params = $this->params['url'];
            unset($url_params['url'], $url_params['page'], $url_params['sort'], $url_params['direction'], $url_params['ext']);
            $url = am(array('?' => $url_params), $url);



            if (!empty($this->params['prefix'])) {
                $url[$this->params['prefix']] = true;
            }
            $this->Paginator->options(array('url' => $url));
        }

        if (!empty($params['multi_select'])) {
            if (is_array($params['multi_select_actions']) && sizeof($params['multi_select_actions'])) {

                $out .='<div class="bulk-actions">' ;
				if($action_selected_type){
				if(!$_SESSION['conditioned'] )
				{
				$out.= '<label for="select_action"   >' . __('With Selected', true) . '</label>';
				}else if ($action_selected_type){

					$_SESSION['conditioned'] = false ;
					$out .= '<select name="index_action_select_type"  class="index-action-select-type btn btn-md btn-default">'
							."<option value='selelcted'>".__('With Selected', true)."</option>"
							."<option value='all'>".__('With All Filtered', true)."</option>"
							. '</select>' ;
				}
				}else{
					$out.= '<label for="select_action"   >' . __('With Selected', true) . '</label>';
				}
                foreach ($params['multi_select_actions'] as $title => $ps) {

					$title2=empty($ps['title'])?__(Inflector::humanize($title),true):$ps['title'];
                    $out .='<a href="#" class="GoSubmit btn btn-md btn-'.(strpos($ps['action'],'dismiss')===false && strpos($ps['action'],'delete')===false?'primary':'danger').'" rel="' . $ps['action'] . '"><i class=""></i> ' . $title2 . '</a>';
                }
                $out .='</div>';
            }
            $out.="</form>";
        }
        if (array_key_exists('paging' , $this->Paginator->params)) {
            $pagingData = $this->Paginator->params['paging'][$basicModel];
        } else {
            $pagingData = null;
        }
        if ($paging && $pagingData['count']) {
            $out .='<div class="results-counter right">' . $this->Paginator->counter(array('format' => '<strong> %start%-%end% </strong>  ' . __('of', true) . ' %count% ' . __('results shown', true) . '</div>'));
            $out.="<div class='Paging'>";

            $out.= $this->jspaging(true, true, IS_MOBILE ? false : true);
            $out.="</div>";
        }

		$message2=str_replace('\'','\'\\\'',__('Please select items first by clicking on the small left box',true));
        if (!empty($params['multi_select']))
            $out.= <<<CODEBLOCK
        <script type="text/javascript">
		$(function(){
            $('.GoSubmit').click(function(){
				index_action_select_type = $('.index-action-select-type').find(':selected').attr('value') == 'all' ;
				if($('.check-input:checked').length>0 || $('.index-action-select-type').find(':selected').attr('value') == 'all')
				{

					$('#MultiSelectForm').get()[0].action= $(this).attr('rel');
					if(!index_action_select_type)
					{
					$('#MultiSelectForm').submit();
					}
					else
						{
							$('<input />').attr('type', 'hidden')
									.attr('name', "index_action_select_type")
									.attr('value', "all")
									.appendTo('#MultiSelectForm');
								conditions_link = $('.fa-cloud-download:last').parent('a').attr('href');
							$('<input />').attr('type', 'hidden')
									.attr('name', "conditions_link")
									.attr('value', conditions_link)
									.appendTo('#MultiSelectForm');
							$('#MultiSelectForm').submit();
						}

				}
				else
				{
					alert('$message2');
                    return false;
				}
            });
        });
		$('#select_all_chk').live('click',function(){
			if($(this).is(':checked')){
				$('.check-input').attr('checked','checked');
			}else{
				$('.check-input').removeAttr('checked');
			}
			clearTimeout(hider);
			hider=setTimeout(function() {
			    $("#select_all_div").hide("slow")
			}, 5000);
			highlight_selected();
	    });
		var hider=0;
		function highlight_selected()
		{

			$('.responsive_check_row:checked').closest('li').addClass('highlighted');
			$('.responsive_check_row:not(:checked)').closest('li').removeClass('highlighted');

		}
		$(function(){
			$('.responsive_check_row').change(function(){
				highlight_selected();
			});
			$('.responsive_check_row:first').change(function(){
				$('#select_all_div').show('slow');
				if(hider!=0)
				clearTimeout(hider);
				hider=setTimeout(function() {\$("#select_all_div").hide("slow")}, 5000);

			});
			highlight_selected();
		});

        </script>
CODEBLOCK;

        if (IS_PC)
            $out.= <<<CODEBLOCK
        <script type="text/javascript">
		$(function(){
			$('td.clickable').hover(function(){ $(this).closest('li').addClass('hovered');},function(){ $(this).closest('li').removeClass('hovered');});
		})
        </script>
CODEBLOCK;
//warning suppress
if(array_key_exists('conditioned',$_SESSION) && $_SESSION['conditioned'])
		{
			//we use this variable to enable dropdown that allow us to pick if we wanted to perform the filter_actions on a selected items or all filtered items
			//in the adminresponsive list this flag is set to true in filter_params if the count(conditions) > 0
			//so if the flag is set to true and its not an admin responsive list we want to set it to false so i dosent confilct with any view that uses that variable
			$_SESSION['conditioned'] = false;
		}

        return $out;
    }

    function adminIndexList($fields, $data = array(), $actions = array(), $multi_select = false, $multi_select_actions = array(), $params = array()) {

        $out = '';
        $urls = array();
        $paging = !isset($params['no_paging']);

        if ($paging) {
            $url = array();
            if (!empty($this->Paginator->options['url'])) {
                $url = $this->Paginator->options['url'];
            }
            $url_params = $this->params['url'];
            unset($url_params['url'], $url_params['page'], $url_params['sort'], $url_params['direction'], $url_params['ext']);
            $url = am(array('?' => $url_params), $url);
            if (!empty($this->params['prefix'])) {
                $url[$this->params['prefix']] = true;
            }
            $this->Paginator->options(array('url' => $url));

            //warning suppress
			if(array_key_exists('url',$params) && $params['url']){
				$this->Paginator->options(array('url' => $params['url']));
			}
        }


        if ($multi_select) {
            $out .='<form id="MultiSelectForm" method="post" name="MultiSelectForm" action="/" >';
        }
        # echo $this->Paginator->counter(array(
        # 'format' => 'Page %page% of %pages%, showing %current% records out of
        # %count% total, starting on record %start%, ending on %end%'
        # ));
        $out .= '<div class="table-responsive" data-pattern="priority-columns">';
        $out .= '<table class="table table-small-font  table-striped b-t b-light" cellpadding="0" cellspacing="0" width="100%">';
        //generate header
        $out.= '<thead>';
        $out.= '<tr class="table-header">';
        $out.=$multi_select ? '<th class="multi-select"  data-priority="2"><input type="checkbox" onclick=""/></th>' : '';
        $headers = array();
        $display_order = false;
        $basicModel = $this->params['models'][0];
        if (!empty($fields['basicModel'])) {
            $basicModel = $fields['basicModel'];
            unset($fields['basicModel']);
        }

        foreach ($fields as $name => $spec) {
            $model = $basicModel;
            $t = $name;
            if (strpos($name, '.')) {
                $a = explode('.', $name);
                $model = $a[0];
                $t = $a[1];
            }
            $title = empty($spec['title']) ? __(Inflector::humanize($t), true) : $spec['title'];
            $sort = empty($spec['sort']) ? $name : $spec['sort'];
            $class = '';
            $class_sort = '';
            $class = low(Inflector::slug(Inflector::humanize($t), '-'));
            $urlSort = empty($this->params['named']['sort']) ? '' : $this->params['named']['sort'];
            if ($paging && $urlSort == $sort) {
                $class_sort = $this->Paginator->sortDir();
            }
            if (strpos($name, '.')) {
                if ($paging) {
                    $type_sort = 'descending order';
                    if ($class_sort == 'desc') {
                        $type_sort = 'ascending order';
                    }
                    $title = $this->Paginator->sort($title, $sort, array('title' => "Sort " . low($title) . " in $type_sort"));
                }
                $class.=" $class_sort";
                // show on mobile view
                if ((isset($spec["show_on_resize"]) && $spec["show_on_resize"] == true) || (empty($spec["show_on_resize"]) && ($t == "title" || $t == "name"))) {
                    $out.="<th class=\"$class\"  data-priority=\"1\">";
                } else {
                    $out.="<th class=\"$class\"  data-priority=\"2\">";
                }


                if (!empty($spec['format']) && low($spec['format']) == 'checkbox') {
                    $t = low($t);
                    $out.="<input type='checkbox' class='CheckboxList' rel='{$t}'/>  ";
                }
//				if (!empty($spec['tooltip'])){
//					$title .= '<span class="tooltip" title="{tooltip}"></span>';
//				}
                $out.="$title</th>";
            } else {
//				if (!empty($spec['tooltip'])){
//					$title .= '<span class="tooltip" title="invoice-notes"></span>';
//				}
                $out.="<th class=\"$class\"  data-priority=\"1\">" . $title . "</th>";
            }

            if (low($t) == 'display_order') {
                $do_action = array('action' => 'update_display_order');
                if ($params['update_display_order_link']) {
                    $do_action = $params['update_display_order_link'];
                }
                /* if ($this->params['prefix']){
                  $do_action[$this->params['prefix']] = 1;
                  } */
                $multi_select_actions['Update display order'] = array('action' => Router::url($do_action));
                $urls["display_order"] = Router::url($do_action);
            }
            if (!empty($spec['format']) && low($spec['format']) == 'checkbox' && !empty($spec['bulk_action'])) {
                $do_action = array('action' => 'update_active', low($t));
//                if($params['update_active']) {
//                    $do_action = $params['update_active'];
//                }
                /* if ($this->params['prefix']){
                  $do_action[$this->params['prefix']] = 1;
                  } */
                $multi_select_actions["Update $title"] = array('action' => Router::url($do_action));
                $urls[low($t)] = Router::url($do_action);
            }
        }
        if (is_array($actions) && !empty($actions)) {
            $out .= '<th class="action"  data-priority="1">' . __('Actions', true) . '</th>';
        }
        $out .= "</tr>";
        $out.= '</thead>';
        $out.= '<tbody>';
        foreach ($data as $row) {
            $cells = array();
            if ($multi_select)
                $cells[] = '<input class="check_row check-input" type="checkbox" value="' . $row[$basicModel]['id'] . '" name="ids[]" /> ';
            foreach ($fields as $name => $spec) {
                if (strpos($name, '.')) {
                    $a = explode('.', $name);
                    $model = '';
                    $model = $a[0];
                    if (empty($model)) {
                        $model = $basicModel;
                    }

                    $t = $a[1];

                    $cell = $row[$model][$t];

                    if (low($t) == 'display_order') {
                        $cell = "<input type=\"text\" value=\"{$row[$model][$t]}\" class=\"AdditionOption\" rel='display_order' name=\"display_order_{$row[$basicModel]['id']}\"  size=\"5\" />";
                    }

                    if (isset($spec['format'])) {
                        if ($spec['format'] == 'checkbox') {
                            $t = low($t);
                            $check = 0;
                            if ($cell) {
                                $check = 1;
                            }
                            $cell = '<input  type="hidden" value="0" name="' . $t . '_' . $row[$basicModel]['id'] . '" /> ';
                            $cell .='<input ' . (($check) ? 'checked=checked' : '') . ' class="' . $t . '_checkbox AdditionOption" type="checkbox" rel="' . low($t) . '" value="1" name="' . $t . '_' . $row[$basicModel]['id'] . '" /> ';

                            $cell .= ( $check) ? 'Yes' : 'No';
                        } elseif ($spec['format'] == 'bool') {
                            if ($cell)
                                $cell = __("Yes", true);
                            else
                                $cell = __("No", true);
                        }
                        elseif ($spec['format'] == 'substr' && !empty($spec['options'])) {

                            //eval ('$string = "'.$spec['options']['string'].'";');
                            $string = $cell;
                            $start = !empty($spec['options']['start']) ? $spec['options']['start'] : 0;
                            $length = !empty($spec['options']['length']) ? $spec['options']['length'] : null;
                            $cell = substr($string, $start, $length);
                        } elseif ($spec['format'] == 'image' && !empty($spec['options'])) {
						//i.e {$row[Media][file_full_path]}
                            eval('$src = "' . $spec['options']['src'] . '";');
                            $other_options = '';
                            if (!empty($spec['options']['width'])) {
                                $other_options = "width = '{$spec['options']['width']}'";
                            }
                            if (!empty($spec['options']['height'])) {
                                $other_options .= "height = '{$spec['options']['height']}'";
                            }
                            if (!empty($spec['options']['alt'])) {
                                $alt = $spec['options']['alt'];
                                if (strpos($spec['options']['alt'], '$row') !== false) {
                                    eval('$alt = "' . $spec['options']['alt'] . '";');
                                }
                                $other_options .= "alt = '{$alt}'";
                            }
                            $cell = "<img src='{$src}' $other_options />";
                        } elseif ($spec['format'] == 'image') {

                            $image_src = $cell;
                            $options = '';
                            if (!empty($spec['options']['width'])) {
                                $options.="width={$spec['options']['width']}";
                            }
                            if (!empty($spec['options']['height'])) {
                                $options.="height={$spec['options']['height']}";
                            }
                            $cell = "<img src='$image_src' $options />";
                        } elseif ($spec['format'] == 'array' && !empty($spec['options'])) {
                            $items_list = !empty($spec['options']['items_list']) ? $spec['options']['items_list'] : array();
                            $selected = $cell;
                            if (isset($spec['options']['selected'])) {
                                eval('$selected = "' . $spec['options']['selected'] . '";');
                            }

                            $empty = !empty($spec['options']['empty']) ? $spec['options']['empty'] : __('None', true);
                            $cell = empty($items_list[$selected]) ? $empty : $items_list[$selected];
                        } elseif ($spec['format'] == 'check' && !empty($spec['options'])) {
                            eval('$conditions = "' . $spec['options']['conditions'] . '";');
                            eval('$true = "' . $spec['options']['true'] . '";');
                            eval('$false = "' . $spec['options']['false'] . '";');

                            $true = !empty($true) ? $true : '';
                            $false = !empty($false) ? $false : '';

                            $cell = ($conditions) ? $true : $false;
                        }elseif($spec['format'] == 'html'){
                            $cell = htmlspecialchars($cell);
                        } else {
                            $cell = sprintf($spec['format'], $cell);
                        }
                    } elseif (isset($spec['date_format'])) {
                        $time = ($cell);
                        if ($time) {
                            $cell = format_date($time);
                        } elseif (isset($spec['empty'])) {
                            $cell = $spec['empty'];
                        }
                    }
                } else {
                    $model = $basicModel;
                    eval('$cell="' . $spec['php_expression'] . '";');
                }

                if (!empty($spec['edit_link'])) {
                    $spec['edit_link'] = Router::url($spec['edit_link']);
                    foreach ($row[$model] as $field => $x) {

                        if (!is_array($x)) {
                            $spec['edit_link'] = str_ireplace("%{$field}%", $x, $spec['edit_link']);
                        }
                    }

                    $cell = $this->Html->link($cell, $spec['edit_link']);
                    //$cell = $this->Html->link($cell, $spec['edit_link'],array('target'=>$target));
                }
                $cells[] = $cell;
            }
            $cell = '';
            if (is_array($actions) && !empty($actions)) {

                $actions2 = $actions;

                $more_actions = empty($actions2['more-actions']) ? array() : $actions2['more-actions'];
                unset($actions2['more-actions']);

                foreach ($actions2 as $action) {

                    if (is_array($action)) {

                        if (!isset($action['php_expression'])) {
                            $more = true;
                        } else {
                            eval('$more =' . $action['php_expression']);
                        }
                        $action = $action['url'];
                        if ($more) {
                            foreach ($row[$basicModel] as $field => $x) {

                                if (!is_array($x)) {

                                    $action = str_ireplace("%{$field}%", $x, $action);
                                }
                            }

                            $cell .= '<li>' . $action . '</li>';
                        }
                    } else {
                        $action = $action;
                        foreach ($row[$basicModel] as $field => $x) {
                            if (!is_array($x)) {
                                $action = str_ireplace("%{$field}%", $x, $action);
                            }
                        }
                        $cell .= '<li>' . $action . '</li>';
                    }
                }
                if (!empty($more_actions)) {
                    $cell .='<li class="MoreActionsContainer actions-list"><a href="#" class="more-actions">' . __('More', true) . ' <i class="fa fa-ellipsis-h"></i></a><ul class="submenu-more-actions sub-list" style="display:none;">';
                    foreach ($more_actions as $action) {
                        if (is_array($action)) {
                            $more = true;
                            if (isset($action['php_expression']))
                                eval('$more =' . $action['php_expression']);
                            if ($more) {
                                preg_match('/%(.+)%/', $action['url'], $matches);
                                $cell .= '<li>' . str_replace("%{$matches[1]}%", $row[$basicModel][$matches[1]], $action['url']) . '</li>';
                            }
                        } else {
                            preg_match('/%(.+)%/', $action, $matches);

                            $cell .= '<li>' . str_replace("%{$matches[1]}%", $row[$basicModel][$matches[1]], $action) . '</li>';
                        }
                    }
                    $cell .="</ul></li>";
                }
                $cells[] = '<ul class="action">' . $cell . '</ul>';
            }
            $out .= $this->Html->tableCells($cells);
        }
        $out.= '<tbody>';
        $out .= '</table>';
        $out .= '</div>';


        if ($paging) {
            $out .='<div class="results-counter right">' . $this->Paginator->counter(array('format' => '<strong> %start%-%end% </strong>  ' . __('of', true) . ' %count% ' . __('results shown', true) . '</div>'));
        }

        if ($multi_select) {
            if (is_array($multi_select_actions) && sizeof($multi_select_actions)) {
                $out .='<div class="bulk-actions"><label for="select_action"   >' . __('With Selected', true) . '</label>';
                foreach ($multi_select_actions as $title => $params) {

					$title2=empty($ps['title'])?__(Inflector::humanize($title),true):$ps['title'];
                    $out .='<a href="#"  class="GoSubmit '. ((isset($params['allow_all']) && $params['allow_all']) ?  'allow-all' : '')  .' btn btn-md btn-'.(strpos('delete',$params['action'])===false?'primary':'danger').'" rel="' . $params['action'] . '"><i class=""></i> ' .$title2 . '</a>';
                }
                $out .='</div>';
            }
            $out.="</form>";
        }



        if ($paging) {
            $out.="<div class='Paging'>";
            $out.= $this->paging();
            $out.="</div>";
        }

        $urls = json_encode($urls);

		$message2=str_replace('\'','\'\\\'',__('Please select items first by clicking on the small left box',true));

        $out.= <<<CODEBLOCK
        <script type="text/javascript">
        var urls = $urls;
        $(document).ready(function(){
            $('.AdditionOption,.CheckboxList').bind('change click',function(){
                rel = $(this).attr('rel');
                url = urls[rel];
                $('#select_action').val(url);
            });
	        $('.GoSubmit').click(function(){
                if(!location.href.includes('email_templates')) {
                    if($('.check-input:checked').length> 0 || $(this).hasClass('allow-all'))
                    {
                        $('#MultiSelectForm').get()[0].action= $(this).attr('rel');
                        $('#MultiSelectForm').submit();
                    }
                    else
                    {
                        alert('$message2');
                        return false;
                    }
                }
            });

	    $('.table-header input:checkbox').live('click',function(){
			if($(this).is(':checked')){
				$('.check-input').attr('checked','checked');
			}else{
				$('.check-input').removeAttr('checked');
			}
	    });
            $('.multiSelect input').click(function(){
                $('#MultiSelectForm input.check_row').attr('checked',$(this).attr('checked'));
            });
            $('.CheckboxList').click(function(){
                rel = $(this).attr('rel');
                $("."+rel+"_checkbox").attr('checked',$(this).attr('checked'));
            });

    	 $('table.data-table tr td').click(function(){
			 if($(this).find('.check_row').length > 0){
				return true;
			 }
    		 $('table.data-table tr').each(function(){
    			if(!$(this).find('.check_row').is(":checked")){
    			    $(this).removeClass('selected');
    			}
    		 });
    		 if($(this).parent().find('.check_row').is(":checked")){
    		    $(this).parent().removeClass('selected');
    		    $(this).parent().find('.check_row').removeAttr('checked');
    		 }else{
    		    $(this).parent().addClass('selected');
    		    $(this).parent().find('.check_row').attr('checked','checked');
    		 }
    	 });

	    $('.check_row').click(function(){
			if(!$(this).is(":checked")){
				$(this).parents('tr').removeClass('selected');
			}else{
				 $(this).parents('tr').addClass('selected');
			}
	    });

        });
        </script>
CODEBLOCK;
        //warning suppress
        if(array_key_exists('conditioned', $_SESSION) && $_SESSION['conditioned'])
		{
			//we use this variable to enable dropdown that allow us to pick if we wanted to perform the filter_actions on a selected items or all filtered items
			//in the adminresponsive list this flag is set to true in filter_params if the count(conditions) > 0
			//so if the flag is set to true and its not an admin responsive list we want to set it to false so i dosent confilct with any view that uses that variable
			$_SESSION['conditioned'] = false;
		}
        return $out;
    }

    function adminIndexListDocument($fields, $data = array(), $actions = array(), $multi_select = false, $multi_select_actions = array(), $params = array()) {

        $out = '';
        $urls = array();
        $paging = !isset($params['no_paging']);

        if ($paging) {
            $url = array();
            if (!empty($this->Paginator->options['url'])) {
                $url = $this->Paginator->options['url'];
            }
            $url_params = $this->params['url'];
            unset($url_params['url'], $url_params['page'], $url_params['sort'], $url_params['direction'], $url_params['ext']);
            $url = am(array('?' => $url_params), $url);
            if (!empty($this->params['prefix'])) {
                $url[$this->params['prefix']] = true;
            }
            $this->Paginator->options(array('url' => $url));

            //warning suppress
			if(array_key_exists('url',$params) && $params['url']){
				$this->Paginator->options(array('url' => $params['url']));
			}
        }


        if ($multi_select) {
            $out .='<form id="MultiSelectForm" method="post" name="MultiSelectForm" action="/" >';
        }
        # echo $this->Paginator->counter(array(
        # 'format' => 'Page %page% of %pages%, showing %current% records out of
        # %count% total, starting on record %start%, ending on %end%'
        # ));
        $out .= '<div class="table-responsive" data-pattern="priority-columns">';
        $out .= '<table class="table table-small-font  table-striped b-t b-light" cellpadding="0" cellspacing="0" width="100%">';
        //generate header
        $out.= '<thead>';
        $out.= '<tr class="table-header">';
        $out.=$multi_select ? '<th class="multi-select"  data-priority="2"><input type="checkbox" onclick=""/></th>' : '';
        $headers = array();
        $display_order = false;
        $basicModel = $this->params['models'][0];
        if (!empty($fields['basicModel'])) {
            $basicModel = $fields['basicModel'];
            unset($fields['basicModel']);
        }

        foreach ($fields as $name => $spec) {
            $model = $basicModel;
            $t = $name;
            if (strpos($name, '.')) {
                $a = explode('.', $name);
                $model = $a[0];
                $t = $a[1];
            }
            $title = empty($spec['title']) ? __(Inflector::humanize($t), true) : $spec['title'];
            $sort = empty($spec['sort']) ? $name : $spec['sort'];
            $class = '';
            $class_sort = '';
            $class = low(Inflector::slug(Inflector::humanize($t), '-'));
            $urlSort = empty($this->params['named']['sort']) ? '' : $this->params['named']['sort'];
            if ($paging && $urlSort == $sort) {
                $class_sort = $this->Paginator->sortDir();
            }
            if (strpos($name, '.')) {
                if ($paging) {
                    $type_sort = 'descending order';
                    if ($class_sort == 'desc') {
                        $type_sort = 'ascending order';
                    }
                    $title = $this->Paginator->sort($title, $sort, array('title' => "Sort " . low($title) . " in $type_sort"));
                }
                $class.=" $class_sort";
                // show on mobile view
                if ((isset($spec["show_on_resize"]) && $spec["show_on_resize"] == true) || (empty($spec["show_on_resize"]) && ($t == "title" || $t == "name"))) {
                    $out.="<th class=\"$class\"  data-priority=\"1\">";
                } else {
                    $out.="<th class=\"$class\"  data-priority=\"2\">";
                }


                if (!empty($spec['format']) && low($spec['format']) == 'checkbox') {
                    $t = low($t);
                    $out.="<input type='checkbox' class='CheckboxList' rel='{$t}'/>  ";
                }
//				if (!empty($spec['tooltip'])){
//					$title .= '<span class="tooltip" title="{tooltip}"></span>';
//				}
                $out.="$title</th>";
            } else {
//				if (!empty($spec['tooltip'])){
//					$title .= '<span class="tooltip" title="invoice-notes"></span>';
//				}
                $out.="<th class=\"$class\"  data-priority=\"1\">" . $title . "</th>";
            }

            if (low($t) == 'display_order') {
                $do_action = array('action' => 'update_display_order');
                if ($params['update_display_order_link']) {
                    $do_action = $params['update_display_order_link'];
                }
                /* if ($this->params['prefix']){
                  $do_action[$this->params['prefix']] = 1;
                  } */
                $multi_select_actions['Update display order'] = array('action' => Router::url($do_action));
                $urls["display_order"] = Router::url($do_action);
            }
            if (!empty($spec['format']) && low($spec['format']) == 'checkbox' && !empty($spec['bulk_action'])) {
                $do_action = array('action' => 'update_active', low($t));
//                if($params['update_active']) {
//                    $do_action = $params['update_active'];
//                }
                /* if ($this->params['prefix']){
                  $do_action[$this->params['prefix']] = 1;
                  } */
                $multi_select_actions["Update $title"] = array('action' => Router::url($do_action));
                $urls[low($t)] = Router::url($do_action);
            }
        }
        if (is_array($actions) && !empty($actions)) {
            $out .= '<th class="action"  data-priority="1">' . __('Actions', true) . '</th>';
        }
        $out .= "</tr>";
        $out.= '</thead>';
        $out.= '<tbody>';
        foreach ($data as $row) {
            $cells = array();
            if ($multi_select)
                $cells[] = '<input class="check_row check-input" type="checkbox" value="' . $row[$basicModel]['id'] . '" name="ids[]" /> ';
            foreach ($fields as $name => $spec) {
                if (strpos($name, '.')) {
                    $a = explode('.', $name);
                    $model = '';
                    $model = $a[0];
                    if (empty($model)) {
                        $model = $basicModel;
                    }

                    $t = $a[1];

                  
                    
                    if($a[1] == 'file' && count($row['Attachments'])){
                        $cell = $row['Attachments'][0]['name'];
                   }else{
                    $cell = $row[$model][$t] ;
                  } 
            
                    
                } else {
                    $model = $basicModel;
                    eval('$cell="' . $spec['php_expression'] . '";');
                }

                if (!empty($spec['edit_link'])) {
                    $spec['edit_link'] = Router::url($spec['edit_link']);
                    foreach ($row[$model] as $field => $x) {

                        if (!is_array($x)) {
                            $spec['edit_link'] = str_ireplace("%{$field}%", $x, $spec['edit_link']);
                        }
                    }

                    $cell = $this->Html->link($cell, $spec['edit_link']);
                    //$cell = $this->Html->link($cell, $spec['edit_link'],array('target'=>$target));
                }
                 $cells[] = $cell;
            }
         
            $cell = '';
            if (is_array($actions) && !empty($actions)) {
          
                $actions2 = $actions;

                $more_actions = empty($actions2['more-actions']) ? array() : $actions2['more-actions'];
                unset($actions2['more-actions']);
 
                foreach ($actions2 as $action) {

                    if (is_array($action)) {

                        if (!isset($action['php_expression'])) {
                            $more = true;
                        } else {
                            eval('$more =' . $action['php_expression']);
                        }
                        $action = $action['url'];
                        if ($more) {
                            foreach ($row[$basicModel] as $field => $x) {

                                if (!is_array($x)) {

                                    $action = str_ireplace("%{$field}%", $x, $action);
                                }
                            }

                            $cell .= '<li>' . $action . '</li>';
                        }
                    } else {
                        $action = $action;
                        foreach ($row[$basicModel] as $field => $x) {
                            if (!is_array($x)) {
                                $action = str_ireplace("%{$field}%", $x, $action);
                            }
                        }
                        $cell .= '<li>' . $action . '</li>';
                    }
                }
                if (!empty($more_actions)) {
                    $cell .='<li class="MoreActionsContainer actions-list"><a href="#" class="more-actions">' . __('More', true) . ' <i class="fa fa-ellipsis-h"></i></a><ul class="submenu-more-actions sub-list" style="display:none;">';
                    foreach ($more_actions as $action) {
                        if (is_array($action)) {
                            $more = true;
                            if (isset($action['php_expression']))
                                eval('$more =' . $action['php_expression']);
                            if ($more) {
                                preg_match('/%(.+)%/', $action['url'], $matches);
                                $cell .= '<li>' . str_replace("%{$matches[1]}%", $row[$basicModel][$matches[1]], $action['url']) . '</li>';
                            }
                        } else {
                            preg_match('/%(.+)%/', $action, $matches);

                            $cell .= '<li>' . str_replace("%{$matches[1]}%", $row[$basicModel][$matches[1]], $action) . '</li>';
                        }
                    }
                    $cell .="</ul></li>";
                }
                $cells[] = '<ul class="action">' . $cell . '</ul>';
            }
             $out .= $this->Html->tableCells($cells);
        }
        $out.= '<tbody>';
        $out .= '</table>';
        $out .= '</div>';


        if ($paging) {
            $out .='<div class="results-counter right">' . $this->Paginator->counter(array('format' => '<strong> %start%-%end% </strong>  ' . __('of', true) . ' %count% ' . __('results shown', true) . '</div>'));
        }

        if ($multi_select) {
            if (is_array($multi_select_actions) && sizeof($multi_select_actions)) {
                $out .='<div class="bulk-actions"><label for="select_action"   >' . __('With Selected', true) . '</label>';
                foreach ($multi_select_actions as $title => $params) {

					$title2=empty($ps['title'])?__(Inflector::humanize($title),true):$ps['title'];
                    $out .='<a href="#"  class="GoSubmit '. ((isset($params['allow_all']) && $params['allow_all']) ?  'allow-all' : '')  .' btn btn-md btn-'.(strpos('delete',$params['action'])===false?'primary':'danger').'" rel="' . $params['action'] . '"><i class=""></i> ' .$title2 . '</a>';
                }
                $out .='</div>';
            }
            $out.="</form>";
        }



        if ($paging) {
            $out.="<div class='Paging'>";
            $out.= $this->paging();
            $out.="</div>";
        }

        $urls = json_encode($urls);

		$message2=str_replace('\'','\'\\\'',__('Please select items first by clicking on the small left box',true));

        $out.= <<<CODEBLOCK
        <script type="text/javascript">
        var urls = $urls;
        $(document).ready(function(){
            $('.AdditionOption,.CheckboxList').bind('change click',function(){
                rel = $(this).attr('rel');
                url = urls[rel];
                $('#select_action').val(url);
            });
	        $('.GoSubmit').click(function(){
                if(!location.href.includes('email_templates')) {
                    if($('.check-input:checked').length> 0 || $(this).hasClass('allow-all'))
                    {
                        $('#MultiSelectForm').get()[0].action= $(this).attr('rel');
                        $('#MultiSelectForm').submit();
                    }
                    else
                    {
                        alert('$message2');
                        return false;
                    }
                }
            });

	    $('.table-header input:checkbox').live('click',function(){
			if($(this).is(':checked')){
				$('.check-input').attr('checked','checked');
			}else{
				$('.check-input').removeAttr('checked');
			}
	    });
            $('.multiSelect input').click(function(){
                $('#MultiSelectForm input.check_row').attr('checked',$(this).attr('checked'));
            });
            $('.CheckboxList').click(function(){
                rel = $(this).attr('rel');
                $("."+rel+"_checkbox").attr('checked',$(this).attr('checked'));
            });

    	 $('table.data-table tr td').click(function(){
			 if($(this).find('.check_row').length > 0){
				return true;
			 }
    		 $('table.data-table tr').each(function(){
    			if(!$(this).find('.check_row').is(":checked")){
    			    $(this).removeClass('selected');
    			}
    		 });
    		 if($(this).parent().find('.check_row').is(":checked")){
    		    $(this).parent().removeClass('selected');
    		    $(this).parent().find('.check_row').removeAttr('checked');
    		 }else{
    		    $(this).parent().addClass('selected');
    		    $(this).parent().find('.check_row').attr('checked','checked');
    		 }
    	 });

	    $('.check_row').click(function(){
			if(!$(this).is(":checked")){
				$(this).parents('tr').removeClass('selected');
			}else{
				 $(this).parents('tr').addClass('selected');
			}
	    });

        });
        </script>
CODEBLOCK;
        //warning suppress
        if(array_key_exists('conditioned', $_SESSION) && $_SESSION['conditioned'])
		{
			//we use this variable to enable dropdown that allow us to pick if we wanted to perform the filter_actions on a selected items or all filtered items
			//in the adminresponsive list this flag is set to true in filter_params if the count(conditions) > 0
			//so if the flag is set to true and its not an admin responsive list we want to set it to false so i dosent confilct with any view that uses that variable
			$_SESSION['conditioned'] = false;
		}
        return $out;
    }




    function adminRelatedList($fields, $data, $basicModel, $actions = array()) {
        $out = '';
        $controller = Inflector::underscore(Inflector::pluralize($basicModel));
        if (empty($actions)) {
            $actions = array(
                $this->Html->link(__('edit', true), array('controller' => $controller, 'action' => 'edit', '%id%'), array('class' => 'Edit')),
                $this->Html->link(__('delete', true), array('controller' => $controller, 'action' => 'delete', '%id%'), array('class' => 'Delete'), __('Are you sure', true)),
            );
        }
        $out .= '<table id="Table" cellpadding="0" cellspacing="0">';
        $out.= '<tr class="table-header">';

        if (!empty($fields['basicModel'])) {
            $basicModel = $fields['basicModel'];
            unset($fields['basicModel']);
        }
        foreach ($fields as $name => $spec) {
            $model = $basicModel;
            $t = $name;
            if (strpos($name, '.')) {
                $a = explode('.', $name);
                $model = $a[0];
                $t = $a[1];
            }
            $title = empty($spec['title']) ? Inflector::humanize($t) : $spec['title'];
            $out.="<td>" . $title . "</td>";
        }
        if (is_array($actions) && !empty($actions)) {
            $out .= '<th class="Action">' . __('Actions', true) . '</th>';
        }
        $out .= "</tr>";

        foreach ($data as $row) {
            $cells = array();
            foreach ($fields as $name => $spec) {
                if (strpos($name, '.')) {
                    $a = explode('.', $name);

                    $model = $a[0];
                    $t = $a[1];

                    $cell = $row[$model][$t];
                    if (isset($spec['format']) && $spec['format'] == 'bool')
                        if ($cell)
                            $cell = __("Yes", true);
                        else
                            $cell = __("No", true);
                }
                if (!empty($spec['edit_link'])) {

                    preg_match('/%(.+)%/', Router::url($spec['edit_link']), $matches);

                    $spec['edit_link'] = str_ireplace("%{$matches[1]}%", $row[$model][$matches[1]], $spec['edit_link']);

                    $cell = $this->Html->link($cell, $spec['edit_link']);
                }
                $cells[] = $cell;
            }
            $cell = '';
            if (is_array($actions) && !empty($actions)) {
                foreach ($actions as $action) {
                    preg_match('/%(.+)%/', $action, $matches);
                    $cell .= '<li>' . str_replace("%{$matches[1]}%", $row[$basicModel][$matches[1]], $action) . '</li>';
                }

                $cells[] = '<ul class="action">' . $cell . '</ul>';
            }
            $out .= $this->Html->tableCells($cells);
        }
        $out .= '</table>';

        return $out;
    }

    function paging($prev = true, $next = true, $numbers = true) {

        $paging = '<ul>';
        if ($prev && $this->Paginator->hasPrev()) {
            $paging .= $this->Html->tag('li', $this->Paginator->prev(' < ' . __('Prev', true), array('class' => 'Prev'), null, array('class' => 'disabled')));
        }
        if ($numbers)
            $paging .= $this->Paginator->numbers(array('tag' => 'li', 'separator' => ''));
        if ($next && $this->Paginator->hasNext()) {
            $paging .= $this->Html->tag('li', $this->Paginator->next(__('Next', true) . ' > ', array('class' => 'Next'), null, array('class' => 'disabled')));
        }
        $paging .= '</ul>';
        return $this->Html->div('paging', $paging) . $this->Html->div('clear', '');
    }

	function get_page_number_from_html($html_content,$multi = false){
		if(!$multi){
		preg_match('/page\:(\d+)/', $html_content, $m);

		}
	else {
		preg_match_all('/page\:(\d+)/', $html_content,$m);
	}
		$page_no = $m[1];
		return $page_no;
	}

	function get_page_url_from_html($html_content,$multi = null)
	{

		if(!$multi){
		preg_match('/<a[^>]*href=[\'\"]([^\'\"]*)/', $html_content, $m);

		}
		else {
		preg_match_all('/<a[^>]*href=[\'\"]([^\'\"]*)/', $html_content, $m);
		}

        // warning suppress
        return $m[1] ?? null;
	}


	function jspaging($prev = true, $next = true, $numbers = true){

        $pagingModel = $this->params['models'][0];
        if($pagingModel=='Reseller'){
            $pagingModel='Invoice';
        }
        if($pagingModel == 'ClientAppointment')
        {
            $pagingModel = 'FollowUpReminder';
        }
		$paging_data = $this->Paginator->params['paging'][$pagingModel];
        
		$per_page = $paging_data['options']['limit'];
		$from_record = 1 + ($paging_data['page']*$per_page - $per_page);
		$to_record = $from_record +  $paging_data['current'] - 1  ;
        if($from_record < 0)
        {
            $from_record = 0;
        }

        if($to_record < 0)
        {
            $to_record = 0;
        }
        $count = $paging_data['count'];

        if($count > $per_page)
        {
            $of_record = $paging_data['count'] ;
            $content = "<script>"
                . '$topNavPrev = $("#top-nav-prev.btn.btn-default.btn-md, #moved_pagination");'
                .'$("#top-bar-nav-section").show();'
                .'if($topNavPrev.length==0) $(".Paging").append(\'<a href="'.htmlspecialchars_decode($this->get_page_url_from_html($this->Paginator->prev())).'" class="btn btn-default btn-md fa fa-chevron-left" id="top-nav-prev"   title="previous"></a><a href="'.htmlspecialchars_decode($this->get_page_url_from_html($this->Paginator->next())).'" class="btn btn-default btn-md fa fa-chevron-right" id="top-nav-next" title="next"></a>\');  '
                . '$topNavNext = $("#top-nav-next.btn.btn-default.btn-md");'
                . '$navFromRecord = $("#nav-from-record");'
                . '$navToRecord = $("#nav-to-record");'
                . '$topBarNavSecrion= $("#top-bar-nav-section");'
                . '$topBarNavSecrion.css("display","inline-block");'
                . '$topNavPrev.removeClass("disabled");'
                . '$topNavNext.removeClass("disabled");'



                . '$navOfRecord = $("#nav-of-record");';

            $content .= '$navFromRecord.text("'.$from_record.'");';
            $content .= '$navToRecord.text("'.$to_record.'");';
            $content .= '$navOfRecord.text("'.$of_record.'");';            
            $orderData = [];
            /**
             * Parse the url to get the named params
             * sort: created example
             * direction: desc example
             */
            $args = $this->params['url']['url'];
            $args = explode('/', $args);
    		foreach ($args as $param) {
                if (empty($param) && $param !== '0' && $param !== 0) {
                    continue;
                }
    
                $separatorIsPresent = strpos($param, ':') !== false;
                if ($separatorIsPresent) {
                    list($key, $val) = explode(':', $param, 2);
                    $named[$key] = $val;
                }
            }

            /**
             * Only append sort data in case the url already contains sort and direction
             */
            if(isset($named['sort']) && isset($named['direction'])){
                $orderData = [$named['sort'] => $named['direction']];
            }

            if ($prev && $this->Paginator->hasPrev()) {
                $content .= '$topNavPrev.attr("href","'.htmlspecialchars_decode($this->get_page_url_from_html($this->Paginator->prev('<< Previous', ['url' => ['order' => $orderData]]))).'");' ;
            }else{
                $content .= '$topNavPrev.addClass("disabled");';
            }
            if ($next && $this->Paginator->hasNext()) {
                $content .= '$topNavNext.attr("href","'.htmlspecialchars_decode($this->get_page_url_from_html($this->Paginator->next('Next >>', ['url' => ['order' => $orderData]]))).'");' ;
            }else{
                $content .= '$topNavNext.addClass("disabled");';
            }
           
            $content .= '</script>' ;
        }else{
            $content = "<script>
                            $('#top-bar-nav-section').hide();
                        </script>";
        }
        return $content;
    }
	/**
	 *
	 * @param type $model
	 * @param type $filters
	 * @param type $form_options
	 * @param type $extra
	 * @param type $sort_fields
	 * @return boolean
	 */
    function filter_form($model, $filters, $form_options = array(), $extra = array(),$sort_fields = array())
    {
        if (empty($filters)) {
            return false;
        }

        $url_params = $this->params['url'];
        unset($url_params['url'], $url_params['page'], $url_params['sort'], $url_params['direction']);

        $session_key = "{$model}_Filter";
        $lastFilter = $this->Session->read($session_key);
        if ($lastFilter && empty($url_params)) {
            $url_params = $lastFilter;
        }

        $display = 'none';
        if (!empty($url_params)) {
            foreach ($url_params as $variable) {
                if (!empty($variable) || (isset($variable) && strlen($variable))) {
                    $display = 'block';
                }
            }
        }

        $hasDate = false;
        $defaults = array('action' => $this->action, 'type' => 'get', 'id' => 'FilterForm', 'style' => "display:block;");
        $extra_defaults = array('input_class' => 'INPUT form-control', 'submit_class' => 'Submit', 'div_class' => 'no-bg-color  filter-results rounded-item panel panel-default listing-filters', 'div_id' => 'FilterDiv', 'toggle_class' => 'Filter_Me');
        $extra = array_merge($extra_defaults, $extra);
        $prefix = (empty($this->params['prefix'])) ? false : $this->params['prefix'];
        if ($prefix)
            $defaults[$prefix] = true;
        $form_options = array_merge($defaults, $form_options);
        $output = '';
        $top_actions = '';
        if (!empty($extra['quick-links']) && is_array($extra['quick-links'])) {

            $top_actions .= '<div class="top-links btn-group">';
            $count = count($extra['quick-links']);
            $counter = 0;
            foreach ($extra['quick-links'] as $title => $link) {
                ++$counter;
                $class = '';
                if ($counter == $count) {
                    $class = ' class="last"';
                }
                $top_actions .= $this->Html->link($title, $link, array("class" => "btn-s2020 btn-secondary-s2020 btn-sm-s2020"));
            }
            $top_actions .= '</div>';
        }
        // $lessOptions = __("Hide Advanced Search", true);
        $moreOptions = __("Advanced Search", true);
		$paging = empty($params['no_paging']);

        // warning suppress
        $sorting_by = '';
		if(!empty($sort_fields))
		{
			$sorting_by = $this->sort_fields_html($sort_fields);
		}

        $sortingHtml = $sorting_by ."<div class='clearfix'></div>";

        $searchWithTopActions =  __('Results', true) . (IS_PC ? $top_actions : '') .$sortingHtml;
        // <!-- Search form toggle button for mobile -->
        $output .=' <span id="page-head-search-btn" class="add-new-top-actions btn btn-action-blue only-mob  right page-head-btn btn-md"><i class="fa fa-search"></i></span>';



        // <!-- Start OF Panel Heading -->
        $output .='<div class="panel-heading border-like-list search-form-header-new">' ;
        $output .= __('Search', true);
        //Handle Filters
        $output .= '<div class="filter-tags-new btn-group ml-md-2 ml-0 mt-md-0 mt-2">';
//        die(var_dump($url_params,$filters));
        foreach ($url_params as $filterKey => $filterValue) {
            if ($filterValue !== '' && array_key_exists($filterKey, $_GET) && $_GET[$filterKey] !== '' && (isset($filters[$filterKey]) || isset($filters[preg_replace(["/_from$/","/_to$/","/_selector$/"],'',$filterKey)]) || isset($filters["CustomModel.".$filterKey]))) {
                $tmpParams = $url_params;
                $tmpParams[$filterKey] = '';
                unset($tmpParams['ext']);
                //$url = Router::url(array('?' => $tmpParams));
                if (isset($_SERVER['REDIRECT_URL'])) {
                $url = str_replace('/webroot/', '/', $_SERVER['REDIRECT_URL']) . '?' . http_build_query($tmpParams);
            }
                $inputName = $filterKey;
                //if the filter is range so it the key will contain additional _from or _to
                if (!isset($filters[$filterKey])) {
                    $orgFilterKey = $filterKey;
                    $filterKey = preg_replace(["/_from$/","/_to$/","/_selector$/"],'',$orgFilterKey);
                    //if the filter is from
                    if (preg_match("/_from$/",$orgFilterKey)) {
                        $label = isset($filters[$filterKey]['from_label']) ? $filters[$filterKey]['from_label'] : $orgFilterKey;
                    } else if (preg_match("/_to$/",$orgFilterKey)) {
                        $label = isset($filters[$filterKey]['to_label']) ? $filters[$filterKey]['to_label'] : $orgFilterKey;
                    } else if (preg_match("/_selector$/",$orgFilterKey)) {
                        $label = isset($filters[$filterKey]['label']) ? $filters[$filterKey]['label'] : $orgFilterKey;
                    } else {
                        $label = isset($filters['CustomModel.'.$filterKey]['label']) ? $filters['CustomModel.'.$filterKey]['label'] : $orgFilterKey;
                    }
                } else {
                    $label = isset($filters[$filterKey]['label']) ? $filters[$filterKey]['label'] : $filterKey;
                }
                if(!is_array($filterValue))
                    $filterValue = [$filterValue];
                else
                    $inputName = $inputName . '[]';
                $output .= '<a href="'.$url.'" class="btn btn-default btn-s2020 btn-secondary-s2020 btn-sm-s2020 btn-xs filters font-weight-medium text-main-s2020" data-input="'.$inputName.'">'.Inflector::humanize($label) . ': <span class="value">' . implode(', ',$filterValue).'</span> <i style="margin: 2px" class="s2020 bg-danger fal fa-close"></i></a>';
            }
        }
        $output .= "</div>";
        $output .= "</div>";

        // <!-- END OF Panel Heading -->

        // <!-- START OF Panel Body -->
        $output .= '<div class="panel-body m-30-search">' . (!IS_PC ? $top_actions : '');
        $output .= $this->Form->create($model, $form_options);
        $more_output = '';
        $default_output = '';

        foreach ($filters as $field => $filter) {
            // warning suppress
            $fieldArray = explode('.',$field);
            $field = end($fieldArray);
            $option_input = '';
            $inputLabel = !empty($filter['label']) ? $filter['label'] : $field;
            $empty = __('[Any ' . Inflector::humanize($inputLabel) . ']', true);
            if (!empty($filter['empty'])) {
                $empty = $filter['empty'];
            }

            $slugname = low(Inflector::slug(Inflector::humanize($field), '-'));

            $div_class = "";
            if ((is_array($filter) && !empty($filter['more-options']))) {
                // $div_class = "form-group text title col-md-4 ";
            // } else {
                // $div_class = "form-group text title col-md-4 primary-actions ";
            // }
                $div_class = "form-group text title col-md-6 ";
            } else {
                $div_class = "form-group text title col-md-6 primary-actions ";
            }
            if(!empty($filter['div_class'])){
            $div_class = $div_class.$filter['div_class'].' ';
            }


            $filterTypeCheck = isset($filter['type']) && is_string($filter['type']) ? $filter['type'] : null;
            if (is_numeric($field)) {
                $slugname = low(Inflector::slug(Inflector::humanize($filter), '-'));
                $empty = __('[Any ' . Inflector::humanize($filter) . ']', true);
                $div_id = 'Div' . $model . Inflector::slug($filter);
                $value = empty($url_params[$filter]) && !(isset($url_params[$filter]) && strlen($url_params[$filter])) ? '' : strval($url_params[$filter]);
                $option_input = $this->Form->input($filter, array('class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . $slugname, 'id' => $div_id)));
            } elseif (is_string($filter)) {

                $div_id = 'Div' . $model . Inflector::slug($field);

                $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                $option_input = $this->Form->input($field, array('class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-'), 'id' => $div_id)));
            } else {

                if (!empty($filter['input_type']) && $filter['input_type'] == 'advanced') {
                    $options = $filter['options'];
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $m_options=array( 'selected_field' => 'name','label' => $label, 'empty' => __('Please Select', true), 'input_name' => $field, 'value' => $value);
                    if(isset($options)&& is_array($options)) $m_options= array_merge ($m_options,$options);
                    $El = ClassRegistry::getObject('view');
                    $option_input = $El->element('advanced_search', array('controller' => $filter['advanced_controller'],'parents' => false,'model' =>$filter['advanced_model'],'action' => $filter['advanced_action'], 'input_options' => $m_options));
                }else if (!empty($filter['input_type']) && ($filter['input_type'] == 'advanced_staff' || $filter['input_type'] == 'advanced_client' || $filter['input_type'] == 'advanced_supplier')) {

                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('input_name' => $field, 'selected_client_id' => !empty($_GET[$field]) ? $_GET[$field] : null, 'label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-')));
                    if (!empty($filter['options']))
                        $foptions = ($filter['options'] + $foptions);
                    $El = ClassRegistry::getObject('view');
                    $option_input = $El->element($filter['input_type'], $foptions);
                }else if(!empty($filter['input_type']) && $filter['input_type'] == 'advanced_product') {

                }else if (!empty($filter['input_type']) && $filter['input_type'] == 'advanced_client') {
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('input_name' => $field, 'selected_client_id' => !empty($_GET[$field]) ? $_GET[$field] : null, 'label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-')));
                    if (!empty($filter['options']))
                        $foptions = ($filter['options'] + $foptions);
                    $El = ClassRegistry::getObject('view');
                    $option_input = $El->element('advanced_client', $foptions);

                }else if (!empty($filter['input_type']) && $filter['input_type'] == 'advanced_staff') {
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('input_name' => $field, 'selected_staff_id' => !empty($_GET[$field]) ? $_GET[$field] : null, 'label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-')));
                    if (!empty($filter['options']))
                        $foptions = ($filter['options'] + $foptions);
                    $El = ClassRegistry::getObject('view');
                    $option_input = $El->element('advanced_staff', $foptions);
                }
                 else if (!empty($filter['input_type']) && $filter['input_type'] == 'element') {
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $foptions = array('value' => $value);
                    if (!empty($filter['element_options']))
                        $foptions = ($filter['element_options'] + $foptions);
                    $El = ClassRegistry::getObject('view');
                    $option_input = $El->element($filter['element_name'], $foptions);
                } else if (!empty($filter['type']) && $filter['type'] == 'number_range') {
                    $from_div_id = "Div{$model}{$field}From";
                    $to_div_id = "Div$model{$field}To";
                    $label = isset($filter['label']) ? $filter['label'] : $field;
                    $from = empty($filter['from_label']) ? Inflector::humanize(sprintf(__('%s from',true), $label)) : $filter['from_label'];
                    $to = empty($filter['to_label']) ? Inflector::humanize(sprintf(__('%s to', true), $label)) : $filter['to_label'];
                    $from_value = empty($url_params[$field . '_from']) && !(isset($url_params[$from]) && strval($url_params[$field . '_from']) === '0') ? '' : strval($url_params[$field . '_from']);
                    $to_value = empty($url_params[$field . '_to']) && !(isset($url_params[$to]) && strval($url_params[$field . '_to']) === '0') ? '' : strval($url_params[$field . '_to']);
                    $option_input = '<div class="row">';
                    $option_input .= $this->Form->input($field . '_from', array('label' => $from, 'class' => $extra['input_class'], 'value' => $from_value, 'selected' => $from_value, 'div' => array('class' => $div_class . Inflector::slug($field . '_from', '-'), 'id' => $from_div_id)));
                    // $option_input = $this->Form->input($field . '_from', array('label' => $from, 'class' => $extra['input_class'], 'value' => $from_value, 'selected' => $from_value, 'div' => array('class' => $div_class . Inflector::slug($field . '_from', '-'), 'id' => $from_div_id)));
                    $option_input .=$this->Form->input($field . '_to', array('label' => $to, 'class' => $extra['input_class'], 'value' => $to_value, 'selected' => $to_value, 'div' => array('class' => $div_class . Inflector::slug($field . '_to', '-'), 'id' => $to_div_id)));
                    $option_input .= '</div>';
                } elseif (!empty($filter['type']) && low($filterTypeCheck) == 'date_range') {
                    $from_value = empty($url_params[$field . '_from']) ? '' : $url_params[$field . '_from'];
                    $to_value = empty($url_params[$field . '_to']) ? '' : $url_params[$field . '_to'];

                    $dateLabel = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $dateOptions = array('' => __('Custom', true), 'lastmonth' => __('Last month', true), 'lastyear' => __('Last year', true));
                    $selectorValue = empty($url_params[$field . '_selector']) ? '' : low($url_params[$field . '_selector']);
                    if (!isset($dateOptions[$selectorValue])) {
                        $selectorValue = '';
                    }
                    $inputs = '<label>'.__($dateLabel, true).'</label>';
                    $inputs .= '<div class="input-group-flex align-items-center">';
                    $inputs .= $this->Form->input($field . '_selector', array('autocomplete'=>'off','options' => $dateOptions, 'label' => false, 'class' => 'date-selector form-x3', 'value' => $selectorValue, 'div' => false));
                    $inputs .= $this->Form->input($field . '_from', array('placeholder'=>__('From',true), 'autocomplete'=>'off','class' => $extra['input_class'] . ' hasDate form-x3', 'id' => "{$field}From", 'value' => $from_value, 'div' => false, 'label' => false));
                    $inputs .= '<span> - </span>';
                    $inputs .= $this->Form->input($field . '_to', array('placeholder'=>__('To',true),'autocomplete'=>'off','class' => $extra['input_class'] . ' hasDate form-x3', 'id' => "{$field}To", 'value' => $to_value, 'div' => false, 'label' => false));
                    $inputs .= '</div>';
                    $div_class = "form-group text title col-md-6 col-sm-6 col-xs-12 ";
                    $option_input = $this->Html->div("{$div_class}range", $inputs);
                    $hasDate = true;
                } elseif (!empty($filter['type']) && low($filterTypeCheck) == 'bool') {
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);

                    if (!empty($filter['no_options'])) {
                        $option_input = $this->Form->input($field, array('label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-'))));
                    } else {
                        $option_input = $this->Form->input($field, array('label' => $label, 'options' => array(0 => 'No', 1 => 'Yes'), 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-'))));
                    }
                } elseif (!empty($filter['type']) && low($filterTypeCheck) == 'radio') {
//                    dd($filter);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
//                    dd($label);
                    $value = $url_params[$field];
                    $options = explode("\n",$filter['myval']['CustomFormField']['select_options']);
                    $options = array_combine($options, $options);
                    $additional_arr = [];

                    $additional_arr["label"] = "<label>".$label."</label>";
                    $additional_arr["name"] = $field;
                    $additional_arr['options'] = $options;
                    $additional_arr["before"] = '<ul class="radio_field_ul"><li class="clip-radio radio-info">';
                    $additional_arr["after"] = '</li></ul>';
                    $additional_arr["separator"] = '</li><li class="clip-radio radio-info" >';
                    $additional_arr["value"] = $value;
                    $additional_arr["type"] = 'radio';
                    $additional_arr["div"] = array('class' => $div_class . Inflector::slug($field, '-'));
                    $option_input = $this->Form->input(false,  $additional_arr);
                    $option_input .= '
<style>
    .radio_field_ul{
        list-style: none;
        display: inline-flex;
    }
    .radio_field_ul li{

    }
</style>'. "<script >
$(function () {
    $(\"input[type='radio'].INPUT.required\").css('display' , 'inline')
    $(\"input[type='radio'].INPUT.required\").css('opacity' , 0)
})

</script>";
                }  else if (!empty($filter['type']) && low($filterTypeCheck) == 'custom_field'){
						$view = ClassRegistry::getObject('view');
    					$value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : (is_array($url_params[$field]) ? implode(',', $url_params[$field]) :  strval($url_params[$field]));
	    				$option_input = $view->element ('custom_form_view/load_single_field' , ['filter' => true,'value' =>  $filter['myval'] , 'form_value' => $value , 'from_filter' => 1] );
				} else if(!empty($filter['input_type']) && $filter['input_type'] == 'text'){
					 $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('type' => 'text','label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => 'test ' . $div_class . Inflector::slug($field, '-')));

					if (!empty($filter['options'])){
                        $foptions = array_merge($filter['options'], $foptions);
                    }

                    $option_input = $this->Form->input($field, $foptions);

				}
                else if ( !empty ( $filter['type'] ) && $filter['type'] == 'hidden')
				{
                    //warning suppress
					$option_input = $this->Form->input ( $field , ['type' => 'hidden' , 'value' => $filter['value'] ?? '']);
				}
				else if(!empty($filter['input_type']) && $filter['input_type'] == 'tags-multiselect')
				{
					$value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('input_name' => $field, 'selected_ids' => !empty($_GET['tags']) ? $_GET['tags'] : null, 'label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-')));
                    if (!empty($filter['options']))
                    {
                        $foptions = ($filter['options'] + $foptions);
                    }
                    $El = ClassRegistry::getObject('view');
					$foptions['tag_type'] = $filter['tag_type'];
					$option_input = $this->Form->hidden('tag_type', array('value' => $filter['tag_type'],'hidden' => true));
                    $option_input .= $El->element('multiselect', $foptions);
                }else if(!empty($filter['input_type']) && $filter['input_type'] == 'tags-multiselect-radio')
				{
					$value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('input_name' => $field, 'selected_ids' => !empty($_GET['tags']) ? $_GET['tags'] : null, 'label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-')));
                    if (!empty($filter['options']))
                    {
                        $foptions = ($filter['options'] + $foptions);
                    }
                    $El = ClassRegistry::getObject('view');
					$foptions['tag_type'] = $filter['tag_type'];
					$option_input = $this->Form->hidden('tag_type', array('value' => $filter['tag_type'],'hidden' => true));
                    $option_input .= $El->element('multiselect-radio', $foptions);
                }else if(!empty($filter['input_type']) && $filter['input_type'] == 'multiselect')
                {
                    $filter['name']=$field;
                    $El = ClassRegistry::getObject('view');
                    $option_input .= $El->element('multiselect-list', $filter);
                }
                else {
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => 'test ' . $div_class . Inflector::slug($field, '-')));

					if (!empty($filter['options'])){
                        $foptions = array_merge($foptions ,$filter['options']);
                    }
                    $option_input = $this->Form->input($field, $foptions);
                }

            }

            if ((is_array($filter) && !empty($filter['more-options']))) {
                if (strpos($option_input, '<label') !== false) {
                    $more_output .= '<div class="col-filter-s2020 col-flex-lg-4">'.$option_input.'</div>';
                } else {
                    $more_output .= $option_input;
                }
                // $more_output .= $option_input;
            } else {
                if (strpos($option_input, '<label') !== false) {
                    $default_output .= '<div class="col-filter-s2020 col-flex-lg-4">'.$option_input.'</div>';
                } else {
                    $default_output .= $option_input;
                }
                // $default_output .= $option_input;
            }
        }


        $default_output = $this->Html->div('default-options-search', '<div class="form-row-flex">'.$default_output.'</div>');
        // $default_output = $this->Html->div('default-options-search', $default_output);

        if (!empty($more_output)) {
            $more_output = $this->Html->div('more-options-search', '<div class="form-row-flex">'.$more_output.'</div>', array('style' => 'display:none;'));
            // $more_output = $this->Html->div('more-options-search', $more_output, array('style' => 'display:none;'));
        }

        $output.=$default_output . $more_output;

        $filterAndResetHtml = $this->Html->div(' new-filter-actions filter-actions pull-right', '<button id="FilterClear" class="btn-s2020 btn-outline-secondary-s2020 text-main s2020" type="reset">' . __('Reset', true) . '</button>' . '<button type="submit" class="text-main s2020 btn-s2020 btn-secondary-s2020">' . __('Search', true) . '</button>');



        // warning suppress
		if(isset($sort_html) && isset($sort_fields) && $sort_fields) {
            $output .= $sort_html;
        }
        $moreOptionsHtml = "<div class='search-btns-bar-footer text title primary-actions full-width  follow_up_status'><a href='#' class='more-options btn-s2020 btn-outline-secondary-s2020 text-main s2020 btn-icon-s2020 pull-left' style='display:none;'><i class='s2020 far fa-sliders-h'></i><span class='hidden-xs'>" . $moreOptions . "</span></a>$filterAndResetHtml</div>";
        $output .= $moreOptionsHtml;
        $output .= $this->Form->end();
        $output .= "</div>";
        // <!-- END OF Panel Body -->

        $output .= $this->Html->div('clear', '');
        // <!-- Start OF Panel Heading -->
        $output .="<div class='panel-heading border-like-list entry-top-actions'>" ;
        $output .= $searchWithTopActions;
        $output .= "</div>";

        // <!-- END OF Panel Heading -->

        $clearUrl = Router::url(['action' => 'reset_filter', 'owner' => false]);


        $rest_url=$this->here.'?reset=1&test=1';
        $show_options = (!empty($more_output)) ? true : false;

        $script = <<<CODEBLOCK
		var show_options="$show_options";

		if(show_options){
			$(".more-options").show();
		}

		$(".more-options").click(function(){
			$('.more-options-search').toggle();
			if($(this).hasClass('Expanded')){
				$(this).removeClass('Expanded');
				// $(this).html('<i class="s2020 far fa-sliders-h"></i><span class="hidden-xs"> $moreOptions</span>');
				$(this).addClass('Collapse');
				$('#FilterDiv').removeClass('FilterExpanded');
			}else{
				$(this).addClass('Expanded');
				// $(this).html('<i class="s2020 far fa-sliders-h"></i> <span class="hidden-xs">$moreOptions</span>');
				$(this).removeClass('Collapse');
				$('#FilterDiv').addClass('FilterExpanded');
			}
			return false;
		});

		$('.more-options-search :input').each(function(){
			if ($(this).val() != '' && $(this).val() != null &&  $(this).val()!= "null"){

                if ( $(this).attr('type')=='radio' && $(this).is(":checked") ){
				$(".more-options").click();
				if($(".index").length)
				setTimeout(function(){if($(".index").length)
			$('html, body').animate({
					scrollTop: $(".index").offset().top
			}, 500); }, 500);
				return false;
			}}
		});
		$('#FilterClear').click(function(){ $.ajax({url : '$clearUrl', success: function(data, status){window.location = "$rest_url"; }});});

//Load filters Values
        $(function(){
            $('.filter-tags-new .filters').each(function(){
                var inputName = $(this).data('input');
                var inputNamewithoutBracket = inputName.replace('[]','');
                var elementType = $('[name='+inputNamewithoutBracket+']')[0];
                if (!elementType) {
                    inputName += '[]';
                }
                elementType = document.getElementsByName(inputName)[0].tagName;
                if (elementType.toLowerCase() == "select"){
                    var items = [];
                    var elem = $(document.getElementsByName(inputName)[0]);
                    $('option:selected',elem).each(function(){ items.push($(this).text()); });
                    var result = items.join(', ');
                    $('span', this).html(result);
                }
            });
        });
CODEBLOCK;
        if ($hasDate) {
            $output .= $this->Javascript->link('jqueryui');
            $this->Html->css('jqueryui', false, false, false);
            $formats = getDateFormats('js');
            $format = $formats[getCurrentSite('date_format')];
            $script .= '$(".hasDate").datepicker({dateFormat: "' . $format . '"});' . PHP_EOL;
            $script .=<<<CODEBLOCK
   $('.date-selector').change(function(){
	   $('.hasDate', $(this).parents('.range')[0]).attr('disabled', $(this).val() != '');
	}).change();
CODEBLOCK;
        }
        $output .= $this->Javascript->codeBlock('$(function(){' . $script . '});');

        return $this->Html->div($extra['div_class'], $output, array('id' => $extra['div_id']));
    }

	function sort_fields_html($sort_fields)
	{

		$query_string = $this->params['url'];
		unset($query_string['ext']);
		unset($query_string['url']);
		unset($query_string['debug']);
		$query_string = http_build_query($query_string);

		$paging = empty($this->params['no_paging']);
		$title_icon = '<i class="fa fa-sort pull-right cust-line"></i>';
		$sort_html = "<ul class='dropdown-menu'>";
		$this->Paginator->options(array('url' => array('?' => $query_string)));
        //warning suppress
		if(array_key_exists('default_order', $sort_fields) && $sort_fields['default_order'])
		{
			$default_order = $sort_fields['default_order']['Setting']['value'] ?? null;
			$default_order = explode(' ', $default_order);
			$default_order_dir = $default_order[1];
			$default_order_field = explode('.', $default_order[0])[1];
			if($default_order_dir == 'desc')
				$title_icon = ' <i class="fa fa-sort-alpha-desc"></i> ';
				else{

				$title_icon = ' <i class="fa fa-sort-alpha-asc"></i> ';
				}

		}
		unset($sort_fields['default_order']);

			foreach($sort_fields as $k => $field){
				$title = isset($field['title']) ? $field['title'] : $field;
				$sort = isset($field['field']) ? $field['field'] : $field;
				$class = '';
				$class_sort = '';
				$class = low(Inflector::slug(Inflector::humanize($t ?? null), '-'));
                // warning suppress
                if (!isset($default_order_field)) {
                    $default_order_field = null;
                }
                // end warning suppress
                $urlSort = empty($this->params['named']['sort']) ? '' : $this->params['named']['sort'];
				if ($paging && ($urlSort == $sort || $sort == $default_order_field)) {
					$class_sort = $default_order_field == $sort ? $default_order_dir : (isset( $field['default_dir'] ) ? $field['default_dir']  : $this->Paginator->sortDir());
					if($class_sort == 'desc' || $default_order_dir == 'desc')
						$title_icon = ' <i class="fa fa-sort-alpha-desc pull-right cust-line"></i> ';
					else {
						$title_icon = ' <i class="fa fa-sort-alpha-asc pull-right cust-line"></i> ';
					}
					$sorting_by_title = __($title,true);
				}

					if ($paging) {
						$type_sort = 'ascending order';
                        // warning suppress
                        $field['default'] = $field['default'] ?? '' ;
						if ($class_sort == 'desc' || $field['default'] == 'asc') {
							$icon = ' <i class="fa fa-sort-alpha-asc"></i> ';
							$type_sort = 'ascending order';
						} else if($class_sort == 'asc' ||  $field['default'] == 'desc')
						{
							$type_sort = 'descending order';
							$icon = ' <i class="fa fa-sort-alpha-desc"></i> ';
						}
						else{
							$icon = ' <i class="fa fa-sort-alpha-asc"></i> ';
						}
						$url = ['controller' => $this->params['controller'], 'action' => $this->params['action']];
						if(isset($this->params['pass']))
                        {
                            $url = array_merge($url, $this->params['pass']);
                        }
						$title = $this->Paginator->sort($icon.$title, $sort, array('url' => $url, 'escape' => false,'title' => sprintf(__("Sort %s in %s",true),$title,__($type_sort,true))));
						if($field['default'] && $class_sort == '')
						$title = str_replace('direction:asc', 'direction:'.$field['default'], $title);
                        $title = str_replace('page:2/', '', $title);
					}

					$class.=" $class_sort";
					$field_html = '<li>'.$title.'</li>';
					$sort_html.=$field_html;

			}
            // warning suppress
            if (isset($sort_html)) {
                $sort_html .= '</ul>';
            }
			if(!isset($sorting_by_title) || !$sorting_by_title)
			$sorting_by_title = __('Sort By',true);


			$sorting_by = '<div class="dropdown pull-right adv-sort-mb">
<a href="#" title="'.__('Sort By',true).'" class="sorting-by btn-addon btn  btn-md dropdown-toggle" data-toggle="dropdown" style="margin:-11px auto;">'. $title_icon. $sorting_by_title . '</a>'.$sort_html.'</div>';
			return $sorting_by;
	}



	function filter_form_field($filters,$filter_key,$model)
	{

			 $form_options = array('action' => $this->action, 'type' => 'get', 'id' => 'FilterForm', 'style' => "display:block;");
			 $this->Form->create($model, $form_options);
			 $extra_defaults = array('input_class' => 'INPUT form-control', 'submit_class' => 'Submit', 'div_class' => 'filter-results rounded-item panel panel-default listing-filters', 'div_id' => 'FilterDiv', 'toggle_class' => 'Filter_Me');
			 $extra =$extra_defaults;
		    foreach ($filters as $field => $filter) {

				if($filter_key && $field != $filter_key)
					continue;

            $option_input = '';
            $empty = __('[Any ' . Inflector::humanize($field) . ']', true);
            if (!empty($filter['empty'])) {
                $empty = $filter['empty'];
            }

            $slugname = low(Inflector::slug(Inflector::humanize($field), '-'));

            $div_class = "";
            if ((is_array($filter) && !empty($filter['more-options']))) {
                $div_class = "form-group text title col-md-3 col-sm-3 col-xs-6 ";
            } else {
                $div_class = "form-group text title col-md-3 col-sm-4 col-xs-6 primary-actions ";
            }
            if(!empty($filter['div_class'])){
            $div_class = $div_class.$filter['div_class'].' ';
            }


            if (is_numeric($field)) {
             //   debug(1);
                $slugname = low(Inflector::slug(Inflector::humanize($filter), '-'));
                $empty = __('[Any ' . Inflector::humanize($filter) . ']', true);
                $div_id = 'Div' . $model . Inflector::slug($filter);
                $value = empty($url_params[$filter]) && !(isset($url_params[$filter]) && strlen($url_params[$filter])) ? '' : strval($url_params[$filter]);
                $option_input = $this->Form->input($filter, array('class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . $slugname, 'id' => $div_id)));
            } elseif (is_string($filter)) {

                $div_id = 'Div' . $model . Inflector::slug($field);

                $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                $option_input = $this->Form->input($field, array('class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-'), 'id' => $div_id)));
            } else {

                if (!empty($filter['input_type']) && $filter['input_type'] == 'advanced_client') {
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('input_name' => $field, 'selected_client_id' => !empty($_GET[$field]) ? $_GET[$field] : null, 'label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-')));
                    if (!empty($filter['options']))
                        $foptions = ($filter['options'] + $foptions);
                    $El = ClassRegistry::getObject('view');
                    $option_input = $El->element('advanced_client', $foptions);
                }else if (!empty($filter['input_type']) && $filter['input_type'] == 'advanced_staff') {
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('input_name' => $field, 'selected_staff_id' => !empty($_GET[$field]) ? $_GET[$field] : null, 'label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-')));
                    if (!empty($filter['options']))
                        $foptions = ($filter['options'] + $foptions);
                    $El = ClassRegistry::getObject('view');
                    $option_input = $El->element('advanced_staff', $foptions);
                
                } else if (!empty($filter['type']) && $filter['type'] == 'number_range') {
                    $from_div_id = "Div{$model}{$field}From";
                    $to_div_id = "Div$model{$field}To";
                    $from = empty($filter['from_label']) ? __(Inflector::humanize($field . ' from'), true) : $filter['from_label'];
                    $to = empty($filter['to_label']) ? __(Inflector::humanize($field . ' to'), true) : $filter['to_label'];
                    $from_value = empty($url_params[$field . '_from']) && !(isset($url_params[$from]) && strval($url_params[$field . '_from']) === '0') ? '' : strval($url_params[$field . '_from']);
                    $to_value = empty($url_params[$field . '_to']) && !(isset($url_params[$to]) && strval($url_params[$field . '_to']) === '0') ? '' : strval($url_params[$field . '_to']);
                    $option_input = $this->Form->input($field . '_from', array('label' => $from, 'class' => $extra['input_class'], 'value' => $from_value, 'selected' => $from_value, 'div' => array('class' => $div_class . Inflector::slug($field . '_from', '-'), 'id' => $from_div_id)));
                    $option_input .=$this->Form->input($field . '_to', array('label' => $to, 'class' => $extra['input_class'], 'value' => $to_value, 'selected' => $to_value, 'div' => array('class' => $div_class . Inflector::slug($field . '_to', '-'), 'id' => $to_div_id)));
                } elseif (!empty($filter['type']) && low($filterTypeCheck) == 'date_range') {
                    $from_div_id = "{$model}{$field}From";
                    $to_div_id = "{$model}{$field}To";
//
                    $from_value = empty($url_params[$field . '_from']) ? '' : $url_params[$field . '_from'];
                    $to_value = empty($url_params[$field . '_to']) ? '' : $url_params[$field . '_to'];

                    $dateLabel = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $dateOptions = array('' => __('Custom', true), 'lastmonth' => __('Last month', true), 'lastyear' => __('Last year', true));
                    $selectorValue = empty($url_params[$field . '_selector']) ? '' : low($url_params[$field . '_selector']);
                    if (!isset($dateOptions[$selectorValue])) {
                        $selectorValue = '';
                    }

                    $inputs = $this->Form->input($field . '_selector', array('options' => $dateOptions, 'label' => $dateLabel, 'class' => 'date-selector form-x3', 'value' => $selectorValue, 'div' => false));
                    $inputs .= $this->Form->input($field . '_from', array('class' => $extra['input_class'] . ' hasDate form-x3', 'id' => "{$field}From", 'value' => $from_value, 'div' => false, 'label' => false));
                    $inputs .= '<span> - </span>';
                    $inputs .= $this->Form->input($field . '_to', array('class' => $extra['input_class'] . ' hasDate form-x3', 'id' => "{$field}To", 'value' => $to_value, 'div' => false, 'label' => false));
                    $div_class = "form-group text title col-md-6 col-sm-6 col-xs-12 ";
                    $option_input = $this->Html->div("{$div_class}range", $inputs);
                    $hasDate = true;
                } elseif (!empty($filter['type']) && low($filterTypeCheck) == 'bool') {
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);

                    if (!empty($filter['no_options'])) {
                        $option_input = $this->Form->input($field, array('label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-'))));
                    } else {
                        $option_input = $this->Form->input($field, array('label' => $label, 'options' => array(0 => 'No', 1 => 'Yes'), 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => $div_class . Inflector::slug($field, '-'))));
                    }
                }  else if (!empty($filter['type']) && low($filterTypeCheck) == 'custom_field'){

						debug ( $filter['myval'] );
						$view = ClassRegistry::getObject('view');
					$value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : (is_array($url_params[$field]) ? implode(',', $url_params[$field]) :  strval($url_params[$field]));
					 $option_input = $view->element ('custom_form_view/load_single_field' , ['value' =>  $filter['myval'] , 'form_value' => $value , 'from_filter' => 1] );


				} else if(!empty($filter['input_type']) && $filter['input_type'] == 'text'){
					 $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('type' => 'text','label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => 'test ' . $div_class . Inflector::slug($field, '-')));

					if (!empty($filter['options']))
                        $foptions = array_merge($filter['options'],$foptions );
//
                    $option_input = $this->Form->input($field, $foptions);

				}
				else {
                    $value = empty($url_params[$field]) && !(isset($url_params[$field]) && strval($url_params[$field]) === '0') ? '' : strval($url_params[$field]);
                    $label = empty($filter['label']) ? __(Inflector::humanize($field), true) : $filter['label'];
                    $foptions = array('label' => $label, 'class' => $extra['input_class'], 'value' => $value, 'selected' => $value, 'empty' => $empty, 'div' => array('class' => 'test ' . $div_class . Inflector::slug($field, '-')));

					if (!empty($filter['options']))
                        $foptions = array_merge($foptions ,$filter['options']);
//
                    $option_input = $this->Form->input($field, $foptions);

                }

            }

            if ((is_array($filter) && !empty($filter['more-options']))) {
                $more_output .= $option_input;
            } else {
                $default_output .= $option_input;
            }
        }

		        if ($hasDate) {
            $output .= $this->Javascript->link('jqueryui');
            $this->Html->css('jqueryui', false, false, false);
            $formats = getDateFormats('js');
            $format = $formats[getCurrentSite('date_format')];
            $script .= '$(".hasDate").datepicker({dateFormat: "' . $format . '"});' . PHP_EOL;
            $script .=<<<CODEBLOCK
   $('.date-selector').change(function(){
	   $('.hasDate', $(this).parents('.range')[0]).attr('disabled', $(this).val() != '');
	}).change();
CODEBLOCK;
        }
        $more_output .= $this->Javascript->codeBlock('$(function(){' . $script . '});');
		return $more_output;
	}

	function js_listing_actions($content)
	{
		$out = '<script>';
		$out .= 'var $listingSelectActions = $("#listing-select-actions");';

		$out .= "\$listingSelectActions.append('".$content."')";
		$out .= "</script>";
		return $out;
	}


}
