<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/create/create.min.css?v=".CSS_VERSION, null, []); ?>
<?php
$isBox = !empty($this->params['url']['box']);
?>
<?php
$title = __("Add Payment Method", true);
if (!empty($this->data['SitePaymentGateway']['id'])) {
	$title = sprintf(__('Edit %s', true), $this->data['SitePaymentGateway']['label']);
}
?>
<?php
if ($isBox) {
    echo $session->flash();
}

    if(!isset($url)) {
        $url = array('controller'=>'payment_gateways');
    }

	$options = array('class' =>'forms-content payment_gateway_template' ,'id' => 'SitePaymentGatewayFormAdd','url'=> $url);

	if ($isBox) {
		$options['url']['?'] =  array('box' => 1,'ajax'=> 1);

	}
	echo $form->create('SitePaymentGateway', $options);
?>
<style>
.is-backend-errors {
    position: relative;
    margin: -14px 0 15px;
    font-size: 12px;
}
</style>
<div class="DetailsBlock gateway-details">
    <div class="panel">
        <div class="panel-body <?php if ($isBox) {echo 'p-0';} ?>">
        <div class="d-none" id="flashMessageWrapper">
            <div class="Errormessage mt-0 d-flex">
                <span class="d-block" id="flashMessage"></span>
            </div>
            <div class="clearfix"></div>
        </div>
            <?php if ($isBox) { ?>
            <div class="payment_modal-head d-flex align-items-end justify-content-between">
                <img class="modal-logo" src="<?php echo Router::url('/css/images/payment/custom-payment-method.svg')?>">
                <?php if(!is_null($sitePaymentGateway['SitePaymentGateway']['id'])){ ?>
                 <a data-url="<?php  echo Router::url(array('controller' => 'payment_gateways','action' => 'delete', $sitePaymentGateway['SitePaymentGateway']['id'])) ?>?ajax=true" href="#" class="text-danger delete_payment_gateway" data-message="<?php __('Are you sure?'); ?>"><i class="mdi mdi-trash-can fs-28"></i></a>
                <?php } ?>
            </div>
            <?php } ?>

            <?php
		echo $form->input('id', array('class' => 'INPUT', 'div'=>'l-input-box'));
		echo $form->input('label', array('label' => [ 'text' => __('Name',true), 'class' => 'ui-input-label l-input-label' ], 'class' => 'INPUT required ui-input l-input payment_gateway_name', 'div'=>'l-input-box'));
        ?>
            <ul class="ui-input-errors is-backend-errors d-none">
                <li></li>
            </ul>
            <?php
		echo $form->input('option1', array('class' => 'INPUT  ui-input l-input', 'label' => [ 'text' => __('Instructions',true).'<span class="tooltip" title="offline-payment-instructions"></span>', 'class' => 'ui-input-label l-input-label' ], 'type'=>'textarea', 'div'=>'l-input-box instructions_div'));
        ?>


            <div class="l-input-box">
                <label for="status"
                    class="l-input-label ui-input-label"><?php __("Availability For Online Clients"); ?><span
                        class="tooltip" title="disable-for-client"></span></label>
                <div class="l-flex-row l-flex-row--spacing-5">
                    <div class="l-flex-col-6">
                        <div
                            class="l-input-placeholder ui-input-placeholder ui-input-placeholder--hover-dark ui-input-placeholder--spacing-0">
                            <label class="l-radio-label ui-radio-label ui-radio-label--spacing">
                                <input type="radio" name="data[SitePaymentGateway][disable_for_client]" <?php if($sitePaymentGateway['SitePaymentGateway']['disable_for_client'] == 0) { echo "checked";} ?>
                                    value="0" class="ui-radio pcheck" data-app-form-validate="required"
                                    data-app-form-validate-field-id="">
                                <span class="ui-radio-label-content"><?php __("Enabled"); ?></span>
                            </label>
                        </div>
                    </div>
                    <div class="l-flex-col-6">
                        <div
                            class="l-input-placeholder ui-input-placeholder ui-input-placeholder--hover-dark ui-input-placeholder--spacing-0">
                            <label class="l-radio-label ui-radio-label ui-radio-label--spacing">
                                <input type="radio" name="data[SitePaymentGateway][disable_for_client]" value="1" <?php if($sitePaymentGateway['SitePaymentGateway']['disable_for_client'] == 1) { echo "checked";} ?>
                                    class="ui-radio pcheck" data-app-form-validate="required"
                                    data-app-form-validate-field-id="">
                                <span class="ui-radio-label-content"><?php __("Disabled"); ?></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <?php
        
		echo $this->element("/payments/treasury2", array('sitePaymentGateway' => $sitePaymentGateway, 'i' => $i, 'offlines' => $offlines));
		?>
            <div class="l-input-box">
            <label for="status"
                    class="l-input-label ui-input-label"><?php __("Status"); ?><span
                        class="tooltip" title="disable-for-client"></span></label>
                <div class="l-flex-row l-flex-row--spacing-5">
                    <div class="l-flex-col-6">
                        <div
                            class="l-input-placeholder ui-input-placeholder ui-input-placeholder--hover-dark ui-input-placeholder--spacing-0">
                            <label class="l-radio-label ui-radio-label ui-radio-label--spacing">
                                <input type="radio" name="data[SitePaymentGateway][active]" <?php if( (isset($sitePaymentGateway['SitePaymentGateway']['active']) && $sitePaymentGateway['SitePaymentGateway']['active'] == 1)  || !isset($sitePaymentGateway)) { echo "checked";} ?> value="1"
                                    class="ui-radio" data-app-form-validate="required"
                                    data-app-form-validate-field-id="">
                                <span class="ui-radio-label-content"><?php __("Active"); ?></span>
                            </label>
                        </div>
                    </div>
                    <div class="l-flex-col-6">
                        <div
                            class="l-input-placeholder ui-input-placeholder ui-input-placeholder--hover-dark ui-input-placeholder--spacing-0">
                            <label class="l-radio-label ui-radio-label ui-radio-label--spacing">
                                <input type="radio" name="data[SitePaymentGateway][active]" <?php if(isset($sitePaymentGateway['SitePaymentGateway']['active']) && $sitePaymentGateway['SitePaymentGateway']['active'] == 0) { echo "checked";} ?> value="0" class="ui-radio"
                                    data-app-form-validate="required" data-app-form-validate-field-id="">
                                <span class="ui-radio-label-content"><?php __("Inactive"); ?></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php if ($isBox) { ?>
<div class="form-bottom">
    <button class="btn-s2020 btn-success-s2020 w-100 p-3 fs-16"><?php __('Save'); ?></button>
</div>
<?php } ?>
<?php if (!$isBox) { ?>
<div class="pages-head-s2020">
    <div class="container">
        <div class="row-flex align-items-center">
            <div class="col-flex-sm-6">
                <div class="pages-head-title">
                    <h2 class="pb-0"><?php echo $title ?></h2>
                </div>
            </div>
            <div class="col-flex-sm-6 d-flex justify-content-end">
                <a href="<?php echo Router::url(array('action'=>'index')) ?>"
                    class="btn s2020 btn-icn btn-secondary font-weight-medium ml-2 mt-0 cancel-btn">
                    <i class="mdi mdi-close fs-20"></i>
                    <span><?php __("Cancel") ?></span>
                </a>
                <button type="submit" class="btn s2020 btn-icn btn-success font-weight-medium ml-2">
                    <i class="mdi mdi-content-save-outline fs-20"></i>
                    <span><?php __("Save") ?></span>
                </button>
            </div>
        </div>
    </div>
</div>
<?php } ?>
<?php echo $form->end(); ?>
<script type="text/javascript">
$('document').ready(function() {
    $('#SitePaymentGatewayFormAdd').bind('submit', function(evt) {
        if (!validateFields()) {
            evt.preventDefault();
        }
    });

    function validateFields() {
        var rules = {
            'input.required': ['notEmpty']

        };

        var validationMessages = {
            notEmpty: __('Required')
        };

        return validate(rules, validationMessages);
    }

    $('body').on('change', '.calculate_fees', function() {
        if (this.value == 1) {
            $(this).parents('.calculate_fees_parent').first().find('.calculate_fees_div').show();
        } else {
            $(this).parents('.calculate_fees_parent').first().find('.calculate_fees_div').hide();
        }

    });


    $('.fees_type').change(function() {

        var index = $(this).data('index');
        if ($(this).val() == 'fixed_rate') {
            $('.percentage_group').hide();
            $('.fixed_group').show();
        } else {
            $('.percentage_group').show();
            $('.fixed_group').show();
        }
    })

    $(document).off('submit', 'form#SitePaymentGatewayFormAdd');

    $(document).on('submit', 'form#SitePaymentGatewayFormAdd', function() {

        if (!validateFields()) {
            evt.preventDefault();
        }

        let form = $(this).serialize();
        let serializedData = $(this).serializeArray();
        let action = $(this).attr('action');
        //Loader
        $.ajax({
            method: 'POST',
            dataType: 'JSON',
            data: form,
            url: action,
            success: function(data) {
                $('.payment_gateway_name').removeClass('is-invalid');
                $('.is-backend-errors').addClass('d-none');
                if (data && data.payment_gateway) {
                    if (!$('.payment_gateway_type_CUSTOM').length) {
                        let index = data.categories.findIndex(function(cat) {
                            return cat == "CUSTOM";
                        });
                        if (index == 0) {
                            let after = data.categories[index - 1];
                            $('.panel-body').prepend('<label class="fs-14 mb-3 mt-1">' +
                                data.customTitle +
                                '</label><div class="row payment_gateway_type_CUSTOM"></div>'
                            );
                        } else {
                            let after = data.categories[index - 1];
                            $('.payment_gateway_type_' + after).after(
                                '<label class="fs-14 mb-3 mt-1">' + data.customTitle +
                                '</label><div class="row payment_gateway_type_CUSTOM"></div>'
                            );
                        }

                    }
                    let newPaymentGateway = data.payment_gateway;
                    let card = document.querySelector('#payment_gateway_card').innerHTML;
                    card = card.replaceAll('#label', newPaymentGateway.label);
                    card = card.replaceAll('#id', newPaymentGateway.id);
                    card = card.replaceAll('#gateway', newPaymentGateway.payment_gateway);
                    if (newPaymentGateway.active == 1) {
                        card = card.replaceAll('#checked', 'checked');
                    } else {
                        card = card.replaceAll('#checked', '');
                    }
                    $('.payment_gateway_type_CUSTOM').append(card);
                    document.getElementById("payment_gateway_id_" + newPaymentGateway.id)
                        .scrollIntoView({
                            behavior: "smooth",
                            block: 'center'
                        });
                    IzamModal.closeModals();
                } else if(data && data.code == -1) {
                    $('.payment_gateway_name').addClass('is-invalid');
                    $('.is-backend-errors').removeClass('d-none');
                    $('.is-backend-errors').find('li').html(data.message);
                } else {
                    let statusData = serializedData.filter((field) => field.name == "data[SitePaymentGateway][active]");
                    let id = serializedData.filter((field) => field.name == "data[SitePaymentGateway][id]");
                    if(statusData.length && id.length) { 
                        id = id[0].value;
                        let idSelector = `#payment_gateway_id_${id}`;
                        let isActive = statusData[0].value == 1 ? true : false;
                        $(idSelector).find('.activate_payment_gateway').prop('checked', isActive);
                        
                    }
                    IzamModal.closeModals();
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(jqXHR);
            }
        })
        return false;
    });

    $('body').off('click', '.delete_payment_gateway');
        $('body').on('click','.delete_payment_gateway',function () { 
            let url = $(this).attr('data-url');
            if(confirm($(this).data('message'))) {
                $.ajax({
                method: 'POST',
                dataType: 'JSON',
                url: url,
                success: function (data) {
                    if(data && data.id && $("#payment_gateway_id_"+data.id).length) {
                        if(data.code) {
                            $("#payment_gateway_id_"+data.id).fadeOut(function(){
                                $(this).remove();
                                if($('.payment_gateway_type_CUSTOM').find('.col-md-4').length == 0) {
                                    $('.payment_gateway_type_CUSTOM').prev('label').remove();
                                    $('.payment_gateway_type_CUSTOM').remove();
                                }
                            });
                            IzamModal.closeModals(); 
                        } else {
                            $('#flashMessage').text(data.message);
                            $('#flashMessageWrapper').removeClass('d-none'); 
                        }
                    }

                },
                error: function (jqXHR, textStatus, errorThrown)
                {
                    
                }
            })
            }
            return false;
        });

    $('body').on('change', '.calculate_fees', function() {
        if (this.value == 1) {
            $(this).parents('.calculate_fees_parent').first().find('.calculate_fees_div').show();
        } else {
            $(this).parents('.calculate_fees_parent').first().find('.calculate_fees_div').hide();
        }

    });


    $('.pcheck').change(function() {
        if ($(this).val() == 0) {
            $('.instructions_div').show();
        }else{
            $('.instructions_div').hide();
        }
    });

    $('.pcheck:checked').trigger('change');

});


//]]>
</script>