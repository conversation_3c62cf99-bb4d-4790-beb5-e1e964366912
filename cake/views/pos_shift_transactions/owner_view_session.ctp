<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/show/show.min.css?v=".CSS_VERSION, null, []); ?>
<?php if(!$is_ajax) {
    echo $html->css(array("timeline", "invoicing", "bootstrap-multiselect", "jquery-ui-timepicker-addon"));
    echo $javascript->link(array('bootstrap-multiselect', 'jquery-ui-timepicker-addon'));
}
?>
<style>
    .table>tbody>tr.total-row>td,
    .table>tbody>tr.total-row>th {
        border-top: 2px solid #ddd !important;
    }
    #ui-datepicker-div{
        z-index: 1040 !important;
    }
    .arrow-side-pos
    {
            font-size: 18px;
            margin: 0 auto;
            width: 35px;
            display: block;
            text-align: center;
            border: 2px solid;
            height: 35px;
            border-radius: 50%;
            padding: 4px;
            padding-top: 8px;
            color:#1ba86f;
    }
    .arrow-side-pos-down
    {
        color:red;
    }
</style>
<?php if(!$is_ajax){?>
    <div class="pages-head">
        <?php
        $date = date('Y/m/d',strtotime($posShift['PosShift']['open_time']));
        $breadcrumbs = [
            ['title' => __('POS Sessions',true),'link' => Router::url(['controller' => 'pos_shifts','action' => 'index'])],
            ['title' => "{$posShift['PosDevice']['name']}/{$date}/{$posShift['PosShift']['id']}",'link' => Router::url(['controller' => 'pos_shifts','action' => 'view',$posShift['PosShift']['id']])],
            ['title' => __('Cash Movements',true)]
        ]
        ?>
        <h1 id="breadcrumb"></h1>
        <?= $this->element ('breadcrumbs' , ['breadcrumbs' =>array_values ( $breadcrumbs ) ]); ?>
        <div class="container">
            <h1 class="left no-margin">
                Session <span class="title_id_with_hash">#<?= "{$posShift['PosDevice']['name']}/{$date}/{$posShift['PosShift']['id']}" ?></span>
                <span class="invoice-client sub-heading sub-heading2">Cashier: <a href="" target="_blank"><?= $staffs[$posShift['PosShift']['staff_id']] ?></a></span>
            </h1>
            <div class="clear"></div>
        </div>
    </div>
<?php } ?>
<div class=" rounded-item panel panel-default">
    <div class="invoice-actions btn-group dropdown-btn-group">
        <?php if($posShift['PosShift']['status'] == PosShift::STATUS_OPENED){ ?>
        <div class="pull-right">
            <a class="btn-addon btn btn-sm btn-success pull-right m-l-sm" onclick="addCash(<?= PosShiftTransaction::TYPE_IN; ?>)"><i class="fa fa-plus"></i> <span class="hidden-xs"><?php __('Add Cash In'); ?></span></a>
            <a class="btn-addon btn btn-sm btn-danger pull-right m-l-sm" onclick="addCash(<?= PosShiftTransaction::TYPE_OUT; ?>)"><i class="fa fa-minus"></i> <span class="hidden-xs"><?php __('Take Cash Out'); ?></span></a>
        </div>
        <?php } ?>
        <div class="clearfix"></div>
    </div>
    <div class="panel-body">

        <div class="panel panel-default no-margin">

            <?php
            $dateFormats = getDateFormats('std');
            $dateFormat = $dateFormats[getAuthOwner('date_format')];
            if (!empty($posShiftTransactions)) {
                echo $list->adminResponsiveList2($posShiftTransactions, 'pos_shift_transaction_row', ['isOpened' => $posShift['PosShift']['status'] == PosShift::STATUS_OPENED]);
            } else {
                echo $html->div('Notemessage',__t('There are no cash transactions'));
            }
            ?>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<?php if($posShift['PosShift']['status'] == PosShift::STATUS_OPENED){ ?>
<div class="modal fade" id="add-popup" tabindex="-1" role="dialog">
    <div class="" role="document">
        <div class="container" style="margin-top:200px;">
            <?= $form->create('PosShiftTransaction',['id' => 'myform']); ?>
            <div class="row">
                <div class="col-md-4 col-sm-6 col-md-offset-4" style="background: #F3F3F3;display: grid">
                    <div class="panel panel-default">
                        <div class="panel-heading"> <?php __('Transaction Information') ?> </div>
                        <div class="panel-body p-0">
                            <div class="row no-margin">
                                <div class="col-xs-6  p-0 b-r"> <span class="m m-b-sm m-t-sm block"><strong id="partner_type_label">Store</strong></span>
                                    <div class="form-group m-b-0 b-t text primary-actions full-width">
                                        <?= $form->input('partner_type',['class' => 'form-control b-white','div' => false,'label' => false,'required' => true,'options' => [PosShiftTransaction::PARTNER_STAFF => __('Staff',true), PosShiftTransaction::PARTNER_TREASURY => __('Treasury',true)],'id'=>'partner_type']); ?>
                                    </div>
                                </div>
                                <div class="col-xs-6  p-0 "> <span><strong class="m m-b-sm m-t-sm block" id="partner_label"><?= __('Staff',true); ?></strong></span>
                                    <div class="form-group m-b-0 b-t text primary-actions full-width">
                                        <?php
                                        echo $form->input('partner_id',['class' => 'form-control b-white','div' => false,'required' => true,'options' => $staffs, 'id' => 'partner_id', 'label' => false]);
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                    </div>
                    <div class="panel panel-default">
                        <div class="panel-heading"> <?php __('Amount Information') ?> </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label><strong><?php __('Amount') ?></strong></label>
                                <?= $form->input('amount',['class' => '','label' => false,'required' => true,'div' => false]); ?>
                            </div>
                            <div class="form-group">
                                <label><strong><?php __('Note') ?></strong></label>
                                <?= $form->input('notes',['class' => '','label' => false,'div' => false]); ?>
                            </div>
                            <div class="form-group">
                                <label><strong><?php __('Date') ?></strong></label>
                                <?php
                                $dateFormats = getDateFormats('std');

                                $value = format_datetime(date('Y-m-d H:i:s',strtotime('now')));

                                echo $form->input('date_time', array('class' => 'has-calendar required', 'div' => false, 'type' => 'text', 'value' => $value, 'tabindex' => 14,'label' => false));
                                ?>
                            </div>

                            <?= $form->input('type',['type'=> 'hidden' ,'value' => 1, 'id' => 'type_id']); ?>
                            <?= $form->input('pos_shift_id',['type'=> 'hidden' ,'value' => $posShift['PosShift']['id']]); ?>
                            <?= $form->input('password',['type'=> 'hidden' ,'id' => 'password']); ?>
                        </div>
                        <div class="clear"></div>
                    </div>
                    <table class="w-100">
                        <tbody>
                        <tr>
                            <td style="padding:5px"><a href="#" class="btn btn-danger btn-lg btn-block padding h-auto" data-dismiss="modal"><?php __('CANCEL') ?></a></td>
                            <td style="padding:5px"><input type="submit" class="btn btn-success btn-lg btn-block padding h-auto" value="<?= __('CONFIRM',true) ?>"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <?= $form->end(); ?>
        </div>
    </div>
</div>
<div class="modal fade" id="edit-popup" tabindex="-1" role="dialog">
    <div class="" role="document">
        <div class="container" style="margin-top:200px;">
            <?= $form->create('PosShiftTransaction',['id' => 'myeditform','url' => Router::url(['action' => 'edit'])]); ?>
            <div class="row">
                <div class="col-md-4 col-sm-6 col-md-offset-4" style="background: #F3F3F3;display: grid">
                    <div class="panel panel-default">
                        <div class="panel-heading"> <?php __('Amount Information') ?> </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label><strong><?php __('Amount') ?></strong></label>
                                <?= $form->input('amount',['class' => '','label' => false,'required' => true,'div' => false,'id' => 'edit_amount']); ?>
                            </div>
                            <div class="form-group">
                                <label><strong><?php __('Note') ?></strong></label>
                                <?= $form->input('notes',['class' => '','label' => false,'div' => false,'id' => 'edit_note']); ?>
                            </div>

                            <?= $form->input('id',['type'=> 'hidden' ,'value' => 1, 'id' => 'transaction_id']); ?>
                            <?= $form->input('password',['type'=> 'hidden' ,'id' => 'password_edit']); ?>
                        </div>
                        <div class="clear"></div>
                    </div>
                    <table class="w-100">
                        <tbody>
                        <tr>
                            <td style="padding:5px"><a href="#" class="btn btn-danger btn-lg btn-block padding h-auto" data-dismiss="modal"><?php __('CANCEL') ?></a></td>
                            <td style="padding:5px"><input type="submit" class="btn btn-success btn-lg btn-block padding h-auto" value="<?= __('CONFIRM',true) ?>"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <?= $form->end(); ?>
        </div>
    </div>
</div>
<div class="modal fade" id="confirm-popup" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-md" role="document" style="width: 100%">
        <div class="container" style="margin-top:200px;">

            <div class="row">
                <div class="col-md-4 col-sm-6 col-md-offset-4" style="background: #F3F3F3;display: grid">
                    <div class="panel panel-default">
                        <div class="panel-heading"> <?php __('Source Information') ?> </div>
                        <div class="panel-body p-0">
                            <div class="row no-margin">
                                <div class="col-xs-12 p-0 ">
                                    <div class="b-b">
                                        <span class="m m-b-sm m-t-sm block"><strong><?php echo sprintf(__('%s\'s Password', true),"<span id=\"staff_name\"></span>") ?></strong></span>
                                    </div>
                                    <div class="form-group text primary-actions full-width m m-b-xs m-t-xs block">
                                        <input type="password" class="form-control b-white" style="border: none" placeholder="password" id="user_password">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                    </div>

                    <table class="w-100">
                        <tbody>
                        <tr>
                            <td><a href="#" class="btn btn-danger btn-lg btn-block padding h-auto" data-dismiss="modal" aria-label="Close" onclick="cancelPassword()"><?php __('CANCEL') ?></a></td>
                            <td><a href="#" class="btn btn-success btn-lg btn-block padding h-auto" data-dismiss="modal" aria-label="Close" onclick="enterPassword()"><?php __('CONFIRM') ?></a></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    let staffs = <?= json_encode($staffs); ?>;
    let treasuriesDeposit = <?= json_encode($treasuriesDeposit); ?>;
    let treasuriesWithdraw = <?= json_encode($treasuriesWithdraw); ?>;
    let transactions = <?= json_encode($posShiftTransactions); ?>;
    let cashType = 1;
    let editTransactionIndex = 0;
    let canSubmit = false;
    let confirmMode = 1;
    <?php $jsDateFormat = getDateFormats(false); ?>;
    var jsDateFormat = '<?php echo $jsDateFormat[getAuthOwner('date_format')] ?>';
    function addCash(type) {
        cashType = type;
        switch (type){
            case <?php echo PosShiftTransaction::TYPE_OUT ?>:
                $('#partner_type_label').text('<?= __('To',true); ?>');
                $('#type_id').val(type);
                $('#add-popup').modal('show');
                break;
            case <?php echo PosShiftTransaction::TYPE_IN ?>:
                $('#partner_type_label').text('<?= __('From',true); ?>');
                $('#type_id').val(type);
                $('#add-popup').modal('show');
                break;
            default:
                alert("Please Select the right type");
        }
        $('#myform').attr('action', '<?= Router::url(['action' => 'add'],true) ?>/' + type);
        $("#partner_type").trigger('change');
    }
    function editCash(index) {
        $('#edit_amount').val(transactions[index]['PosShiftTransaction']['amount']);
        $('#edit_note').val(transactions[index]['PosShiftTransaction']['notes']);
        $('#myeditform').attr('action', '<?= Router::url(['action' => 'edit'],true) ?>/' + transactions[index]['PosShiftTransaction']['id']);
        $('#transaction_id').val(transactions[index]['PosShiftTransaction']['id']);
        editTransactionIndex = index;
        $("#edit-popup").modal('show');
    }
    $('#myform').submit(function() {
        const submitBtn = $(this).find('input[type="submit"]');
        submitBtn.prop('disabled', true);
        if($("#partner_type").val() == <?= PosShiftTransaction::PARTNER_STAFF ?>) {
            if(canSubmit)
                return true;
            if($("#partner_id").val() != <?= getAuthOwner('staff_id'); ?>) {
                $('#staff_name').text(staffs[$("#partner_id").val()]);
                $('#confirm-popup').modal('show');
                confirmMode = 1;
                return false;
            }
        }else{
            return true;
        }
    });
    $('#myeditform').submit(function() {
        if(transactions[editTransactionIndex]['PosShiftTransaction']['partner_type'] == <?= PosShiftTransaction::PARTNER_STAFF ?>) {
            if(canSubmit)
                return true;
            if(transactions[editTransactionIndex]['PosShiftTransaction']['partner_id'] != <?= getAuthOwner('staff_id'); ?>) {
                $('#staff_name').text(staffs[transactions[editTransactionIndex]['PosShiftTransaction']['partner_id']]);
                $('#confirm-popup').modal('show');
                confirmMode = 2;
                return false;
            }
        }else{
            return true;
        }
    });
    function enterPassword() {
        canSubmit = true;
        if (confirmMode == 1) {
            $('#password').val($('#user_password').val());
            $('#myform').submit();
        }
        else {
            $('#password_edit').val($('#user_password').val());
            $('#myeditform').submit();
        }
    }
    function cancelPassword() {
        const submitBtn = $('#myform').find('input[type="submit"]');
        submitBtn.prop('disabled', false);
    }
    $(document).ready(function () {
        $('.has-calendar').datetimepicker({dateFormat: jsDateFormat, timeFormat: 'HH:mm'});
        $("#partner_type").change(function(){
            switch ($(this).val()){
                case "<?php echo PosShiftTransaction::PARTNER_STAFF ?>":
                    canSubmit = false;
                    $("#partner_id").empty();
                    for(let staff_id in staffs){
                        $("#partner_id").append('<option value="'+staff_id+'">'+staffs[staff_id]+'</option>');
                    }
                    $('#partner_label').text ('<?= __('Staff',true); ?>');
                    break;
                case "<?php echo PosShiftTransaction::PARTNER_TREASURY ?>":
                    $("#partner_id").empty();
                    let treasuries = cashType == <?= ItemPermission::PERMISSION_DEPOSIT ?> ? treasuriesDeposit : treasuriesWithdraw;
                    for(let treasury_id in treasuries){
                        $("#partner_id").append('<option value="'+treasury_id+'">'+treasuries[treasury_id]+'</option>');
                    }
                    $('#partner_label').text ('<?= __('Treasury',true); ?>');
                    break;
            }
        });
        <?php
        if (isset($_GET['action'])) {
            if ($_GET['action'] == 'cash_in') { ?>
        addCash(<?= PosShiftTransaction::TYPE_IN; ?>);
            <?php } elseif ($_GET['action'] == 'cash_out') { ?>
        addCash(<?= PosShiftTransaction::TYPE_OUT; ?>);
            <?php }
        }
        ?>
        $('#confirm-popup').on('shown.bs.modal', function () {
            $('#user_password').focus();
        });
    })
</script>
<?php } ?>