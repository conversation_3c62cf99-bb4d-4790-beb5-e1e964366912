<?php

use Izam\Entity\Service\S3FileManager;

 echo $html->css(array("timeline", "invoicing","bootstrap-multiselect"));
echo $javascript->link(array('bootstrap-multiselect'));
$breadcrumbs = [
    ['link' => Router::url(array('controller' => 'stocktakings', 'action' => 'index')), 'title' => __("Manage Stocktakings" , true ) ],
    ['link' => '#', 'title' => __("Stocktaking",true).' '.'#' . $stocktaking['Stocktaking']['number']],
];
echo $this->element ( 'breadcrumbs' , ['breadcrumbs' => $breadcrumbs , 'no_head_class' => true ]);
echo $html->css(['view_new_style.css' , 'tabs.css','owner-page.css']);
if($is_rtl){echo $html->css('view_new_style_ar.css'); }
?>
<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/show/show.min.css?v=".CSS_VERSION, null, []); ?>
<style type="text/css">
.physica-count-container {display: flex; align-items: center; gap: 3px;}
.modal-title{
    font-size: 16px;
    font-weight: 500;
    color: #3a3e63;
}
.mobile-invoicing tbody th{
    color: #3a3e63;
    font-weight: 500;
    padding-inline-start: 10px !important;
}
.mobile-invoicing .table-header {
    box-shadow: 0px 3px 10px 0px rgba(219,216,219,1);
    position: sticky;
    top: 0;
}
.invoice-items .table tr td{
    font-weight: 500;
    border-top: 0;
}

.invoice-items .lot-modal-header {
    border-bottom: 1px solid #dbe7f3;
}

.invoice-items .modal-body  {
    max-height: 400px;
    overflow-y: auto;
    border: 0;
    box-shadow: inset 0px 3px 8px rgba(0,0,0, 0.1);
}

.invoice-items .modal-body .table {
    border: 0;
    background: transparent;
}

.invoice-items .modal-body .table th,
.invoice-items .modal-body .table td {
    padding: 8px 20px !important;
}

::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}
::-webkit-scrollbar-track {
	background-color: rgba(255, 255, 255, 0.1);
	-webkit-border-radius: 5px;
}
::-webkit-scrollbar-thumb {
	background-color: rgba(0, 0, 0, 0.2);
	-webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:hover {
	background-color: rgba(0, 0, 0, 0.4);
}
</style>
<div class="pages-head fixed-div">
    <div class="container">
        <h1 class="left no-margin view-title-new m-b-10">
            <?php echo __("Stocktaking",true).' #'.$stocktaking['Stocktaking']['number']?>
            <div class="status">
                <span class="status-symble " style="color: <?php echo $statuses['color']?>;background-color: <?php echo $statuses['background-color']?>"><?php __($statuses['label'])?></span>
            </div>
        </h1>
        <div class="top-actions ">
            <div class="mb-opt-btn">

            </div>
            <? if ($stocktaking['Stocktaking']['status'] != Stocktaking::STATUS_ADJUSTED) { ?>
                <a href="<?php echo Router::url(['controller' => 'stocktakings' , 'action' => 'adjust' , $stocktaking['Stocktaking']['id']])?>" class="btn btn-success btn-addon"><i class="fa fa-check"></i><? echo __('Adjust', true); ?></a>
            <? } ?>
            <? if ($stocktaking['Stocktaking']['status'] == Stocktaking::STATUS_DRAFT) { ?>
                <a href="/v2/owner/stocktakings/<? echo $stocktaking['Stocktaking']['id']; ?>" class="btn btn-info btn-addon"><i class="fa fa-plus"></i><? echo __('Do Stocktaking', true); ?></a>
            <? } ?>
        </div>
    </div>
</div>
<div class="invoice-actions btn-group dropdown-btn-group">
    <a href="<?php echo Router::url(['controller' => 'stocktakings' , 'action' => 'edit' , $stocktaking['Stocktaking']['id']])?>" class="btn btn-default btn-sm quick-action-btn"><span class="fa fa-pencil" aria-hidden="true"></span> <?php __("Edit")?></a>
    <? if ($stocktaking['Stocktaking']['status'] == Stocktaking::STATUS_ADJUSTED) { ?>
    <a href="<?php echo Router::url(['controller' => 'stocktakings' , 'action' => 'update_draft' , $stocktaking['Stocktaking']['id']])?>" class="btn btn-default btn-sm quick-action-btn"><span class="fa fa-undo" aria-hidden="true"></span> <?php __("Undo Adjustment") ?></a>
    <?
    }
    ?>
    <a href="<?php echo Router::url(['action' => 'delete' , $stocktaking['Stocktaking']['id']])?>" class="pdf btn btn-default btn-sm quick-action-btn"><span class="fa fa-times" aria-hidden="true"></span> <?php __("Delete");?></a>
    <a href="/v2/owner/export/stocktaking_record?from_entity=stocktaking&filters[stocktaking_id]=<?php echo $stocktaking['Stocktaking']['id']; ?>" class="btn btn-default btn-sm quick-action-btn"><span class="fas fa-file-export"></span> <?php __("Export")?></a>
    <?php if ( !empty($has_templates)){ ?>
        <div class="btn-group">
            <button type="button"   class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Voucher', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" >
                <?php foreach($printableTemplates as $template) { ?>
                    <li>
                        <a href="<?php echo Router::url(array('controller'=>'printable_templates', 'action' => 'view', $stocktaking['Stocktaking']['id'], $template['PrintableTemplate']['id'], 'stocktaking' )) ?>" class=""> <?= $template['PrintableTemplate']['name']; ?></a>
                    </li>
                <?php } ?>
                <?php
                    echo draw_templates_list($view_templates, $stocktaking['Stocktaking']['id']);
                ?>
            </ul>
        </div>
    <?php }?>

    <div class="clearfix"></div>
</div>
<!-- <?php
echo $javascript->link(array('owner-view_v' . JAVASCRIPT_VERSION . '.js'));
?> -->
<div role="tabpanel" class="tabs-box box">
    <ul class="nav nav-tabs responsive">
        <li class="active" role="presentation"><a aria-controls="InvoiceBlock" role="tab" data-toggle="tab" href="#InvoiceBlock" title="Preview invoice" id="ViewInvoice"><span class="one-line"><?php __("Stocktaking") ?></span></a></li>
        <?php if ( !empty($defaultTemplate)){ ?>
            <li role="presentation"><a id="defaultPrintableTemplate" aria-controls="PrintBlock" role="tab" data-toggle="tab" href="#PrintBlock" title="Print"><span class="one-line"><?php __("Print") ?> </span></a></li>
        <?php }?>
        <?php if ( !empty($requisitions)){?>
            <li role="presentation"><a id="requisitionsButton" aria-controls="RequisitionBlock" role="tab" data-toggle="tab" href="#RequisitionBlock" title="Print"><span class="one-line"><?php __("Requisitions") ?> </span></a></li>
        <?php }?>
        <li role="presentation"><a id="Timeline" aria-controls="TimelineBlock" role="tab" data-toggle="tab" href="#TimelineBlock" title="Timeline for invoice"><span class="one-line"><?php __("Activity Log") ?> </span></a></li>
    </ul>
    <!-- / tabs-buttons -->
    <div class="tab-content responsive">
        <div class="tab-pane active" id="InvoiceBlock">
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading"><?php __("Information") ?> </div>
                        <div class="panel-body p-0">
                            <div class="row no-margin">
                                <div class="col-xs-3 p-0 b-r">
                                    <div class="m"> <span><strong><?php __("Store") ?></strong></span>
                                        <p style="margin-top: 2px;"><?= $stocktaking['Store']['name'] ?></p>
                                    </div>
                                </div>
                                <div class="col-xs-3 p-0 b-r">
                                    <div class="m"> <span><strong><?php __("Date") ?></strong></span>
                                        <p style="margin-top: 2px;"><?php echo format_datetime($stocktaking['Stocktaking']['date'])?></p>
                                    </div>
                                </div>
                                <div class="col-xs-3 p-0 b-r">
                                    <div class="m"> <span><strong><?php __("Number") ?></strong></span>
                                        <p style="margin-top: 2px;">#<?php echo $stocktaking['Stocktaking']['number']?></p>
                                    </div>
                                </div>
                                <div class="col-xs-3 p-0 ">
                                    <div class="m"> <span><strong><?php __("Summary") ?></strong></span>
                                        <div style="margin-top: 2px;">
                                            <?php if (!empty($stocktaking['RequisitionIn']['sum_all'])){ ?>
                                                <div><?php echo __("Overage", true) . " " . format_price_simple($stocktaking['RequisitionIn']['sum_all']); ?></div>
                                            <?php } if (!empty($stocktaking['RequisitionOut']['sum_all'])) { ?>
                                                <div><?php echo __("Shortage", true) . " " . format_price_simple($stocktaking['RequisitionOut']['sum_all']); ?></div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>


                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading"> <?php __("Other Information") ?> </div>
                        <div class="panel-body p-0">
                            <div class="row no-margin">

                                <div class="col-xs-12  p-0 b-l">
                                    <div class="m">
                                        <div class="form-group">
                                            <label for=""><?php __("Notes") ?></label>
                                            <p><?php echo $stocktaking['Stocktaking']['notes']?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="p-0">
                        <div class="panel panel-default no-margin">
                            <div class="panel-heading"> <?php __("Products List") ?> </div>
                            <div class="invoice-items m-2">
                                <div class=" table-responsive">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table b-light mobile-invoicing">
                                        <tr class="TableHeader table-header active unmovable">
                                            <th id="label_item" class="col-1 code-cell"><?php __("Code") ?></th>
                                            <th id="label_item" class="col-1 code-cell"><?php __("Barcode") ?></th>
                                            <th id="label_item" class="col-1 name-cell"><?php __("Name") ?></th>
                                            <th id="label_quantity"   class="physical-count-cell "><span>
                          <?php __("Physical Count") ?>
                          </span></th>
                                            <th id="label_quantity"   class="system-count-cell "><span>
                          <?php __("System Count") ?>
                          </span></th>
                          <th id="label_note"   class="shortage-cell "><span>
                          <?php __("Shortage/Overage") ?>
                          </span></th>
                          <th id="label_photo"   class="shortage-cell "><span>
                            <?php __("Image") ?>
                          </span></th>
                            <th id="label_quantity"   class="shortage-cell "><span>
                                <?php __("Note") ?>
                            </span></th>

                         
                       
                                        </tr>
                                        <?php $total = 0  ; foreach ( $stocktaking['StocktakingRecord'] as $record => $ri ){
                                            if ($ri['unit_factor']) {
                                                $ri['physical_count'] *= $ri['unit_factor'];
                                            }
                                            

                                            ?>
                                            
                                            <tr class="itemRow fix-clear movable">
                                                <td class="td_editable"><p class="p-0"><strong><a href="<?php echo Router::url(['controller' => 'products','action' => 'view' ,$ri['Product']['id'] ])?>" ><?php echo !empty($ri['Product']['product_code']) ? $ri['Product']['product_code'] : $ri['Product']['id'] ?></a></strong></p></td>
                                                <td class="td_editable"><p class="p-0"><strong><a href="<?php echo Router::url(['controller' => 'products','action' => 'view' ,$ri['Product']['id'] ])?>" ><i class="fa fa-barcode"></i> <?php echo $ri['Product']['barcode']?></a></strong></p></td>
                                                <td class="td_editable"><p class="p-0"><strong><a href="<?php echo Router::url(['controller' => 'products','action' => 'view' ,$ri['Product']['id'] ])?>" ><?php echo $ri['Product']['name']?></a></strong></p></td>
                                                <td class="td_editable">
                                                    <p class="p-0 physica-count-container">
                                                        <?php echo $ri['physical_count']?> 
                                                        <?php if($ri['Product']['tracking_type'] != \App\Utils\TrackStockUtil::QUANTITY_ONLY && ifPluginActive(PRODUCT_TRACKING_PLUGIN)): ?>
                                                            <a href="" data-toggle="modal" data-target="#exampleModal<?= $record?>" class="physica-count-container">
                                                                <i class="mdi mdi-clipboard-alert fs-20 lots-info-btn u-text-color-primary" style="cursor: pointer;"></i>
                                                                <span style="position: relative; top: -1px;"><? echo sprintf('(%s)',count($ri['tracking_numbers_type']['tracking_numbers'] ));?></span>
                                                            </a>
                                                        <?php endif; ?>
                                                    </p>
                                                </td>
                                                <td class="td_editable"><p class="p-0"><?php echo $ri['system_count']?></p></td>
                                                <td class="td_editable"><p class="p-0"><?php echo $ri['shortage_value']?></p></td>
                                                
                                                <td class="td_editable"><p class="p-0"> 
                                                    <img 
                                                        class="profile-pic" width="70px"
                                                        src="<?php
                                                        $s3FileManager = new S3FileManager();
                                                        echo isset($ri['photo']) && isset($ri['photo']['path']) 
                                                            ? $s3FileManager->getUrlAttribute($ri['photo']['path'], $ri['photo']['name'])
                                                            : ''; 
                                                        ?>" 
                                                    />
                                                    </p>
                                                </td>
                                                <td class="td_editable"><p class="p-0"><?php echo $ri['note']?></p></td>
                                               
                                            </tr>
                                            
                                        <?php } ?>
                                        <?php if ( $show_price ){ ?>
                                            <tfoot>
                                            <tr>
                                                <td ><strong><?php __("Total");?></strong></td>
                                                <td colspan="2">&nbsp;</td>
                                                <td ><?php echo format_price_simple($total) ;  ?></td>
                                            </tr>
                                            </tfoot>
                                        <?php } ?>
                                    </table>

                                    <?php  
                                    foreach ( $stocktaking['StocktakingRecord'] as $record => $ri ){
                                        $icon = ($ri['shortage_value'] > 0) ? 'mdi-plus pr-2 u-text-color-success': 'mdi-minus-thick pr-2 u-text-color-red';
                                        
                                           switch ($ri['tracking_numbers_type']['type']) {
                                            case 'serial': 
                                                include('partials/serial.ctp');
                                                break;

                                            case 'lot': 
                                                include('partials/lot.ctp');
                                                break;
                                            
                                            case 'expiry_date': 
                                                include('partials/expiry_date.ctp');
                                                break;        

                                            case 'lot_and_expiry_date': 
                                                include('partials/lot_and_expiry_date.ctp');
                                                break;        
                                           }
                                        }
                                    ?>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <?php if ( !empty($defaultTemplate)){?>
            <div class="tab-pane active" id="PrintBlock">

            </div>
        <?php } ?>
        <?php if ( !empty($requisitions)){?>
            <div class="tab-pane active" id="RequisitionBlock">

            </div>
        <?php } ?>

        <div class="tab-pane" id="TimelineBlock">


        </div>
    </div>
</div>
<div class="clearfix"></div>
<script type="text/javascript">
    $(document).ready(function() {
        $('#fliter-action').multiselect();
    });
    $(document).on("click", "#Timeline",function () {
        current_reload_url = "<?php echo Router::url(['controller' => 'stocktakings' , 'action' => 'timeline' ,$stocktaking['Stocktaking']['id']])?>";
        $("#TimelineBlock").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');
        $("#TimelineBlock").load(current_reload_url);
    })
    $(document).on("click", "#defaultPrintableTemplate",function () {
        current_reload_url = "<?php echo Router::url(['controller' => 'printable_templates' , 'action' => 'view' ,$stocktaking['Stocktaking']['id'],$defaultTemplate[0]['PrintableTemplate']['id'],'stocktaking' ])?>";
        $("#PrintBlock").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');
        $("#PrintBlock").load(current_reload_url);
    })
    $(document).on("click", "#requisitionsButton",function () {
        <?php $requisitions_qstring = ['order_id' => $stocktaking['Stocktaking']['id']];
        foreach ($requisitions as $k => $r){
            $requisitions_qstring['order_type['.$k.']'] = $r['Requisition']['order_type'];
        }                ?>
        current_reload_url = "<?php echo Router::url(['controller' => 'requisitions' , 'action' => 'index' ,'?' => $requisitions_qstring ])?>";
        $("#RequisitionBlock").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');
        $("#RequisitionBlock").load(current_reload_url);
    })

</script>
