

<?php echo $html->css('nav-tabs',false); ?>
<style>
    .modal .modal-dialog {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        min-height: calc(100% - (10px* 2));
        padding-top: 0px !important;
    }
    .modal .modal-content {
        width: 100%;
    }
    @media (min-width: 768px) { 
        .modal .modal-dialog {
            min-height: calc(100% - (30px* 2));
        }
    }
</style>
<?php
    $breadcrumbs = [
        ['link' => '/v2/owner/purchase-invoices/settings', 'title' => __("Purchase Invoice Settings", true) ],
        ['link' => '#', 'title' => __("Purchase Invoices", true)],
    ];
    echo $this->element ( 'breadcrumbs' , ['breadcrumbs' => $breadcrumbs , 'no_head_class' => true ]);
?>
  <!-- Content -->
<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/create/create.min.css?v=".CSS_VERSION, null, []); ?>
<div class="pages-head fixed-div">
</div>
<div class="bg-white m-t-30 no-border no-bg">
    <?php //echo $this->element ( 'inventory_settings_sidemenu' , ['selected_tab' => "purchase_orders"]);?>
	<div class="col-md-12 no-padding">
            <div class="FormExtended row">

	<?php

		echo $form->create(InventoryPlugin,array('id' => 'InvoiceForm',  'url'=>Router::url(array('action'=>'purchase_invoices'))));
		?>
                <div class="col-md-12">
                <div class="input-fields inside-form-box">
		<div  class="row">

			<?php
			echo $form->input('next_po_number', array('label'=>__('Next P.I. Number',true).'<a style="display: inline-block;font-weight: 100;    color: #353535;" tabindex="-1" class="right" id="EditCurrency" href="'.Router::url(['controller' => 'settings', 'action' => 'numbering',AutoNumber::TYPE_PURCHASE_INVOICE]).'" title="Auto Number Settings"><u><i class="fa fa-edit"></i>  '.__('Auto Number Settings',true).'</u> </a>','class' => 'INPUT form-control', 'div'=>'form-group left col-md-12', "readonly"=>true));
			?>
		</div>
<!--		<div  class="row">
			<?php
//			echo $form->input('shipping_billing', array_merge($form_data['shipping_billing'],array('label'=>__('Shipping & Billing information',true),'class' => 'INPUT form-control', 'div'=>'form-group left')));
			?>
		</div>-->
<!--                <div id="custom_address" class="row">
                    <div class="col-md-6">
                            <?php // echo $form->input('po_billing_address', array_merge($form_data['po_billing_address'],array('label'=>__('Bill to',true),'rows'=>6,'class' => 'INPUT form-control', 'div'=>'form-group'))); ?>
                    </div>
                    <div class="col-md-6">
                    <?php
//                            echo $form->input('po_shipping_address', array_merge($form_data['po_shipping_address'],array('label'=>__('Ship to',true),'rows'=>6,'class' => 'INPUT form-control', 'div'=>'form-group')));
                            ?>
                    </div>
                            <div class="clearfix"></div>
                </div>-->
<!--                <div  class="row">
			<?php
			//echo $form->input('calculation_method', array_merge($form_data['calculation_method'],array('label'=>__('Sales and Inventory cost calculation',true),'class' => 'INPUT form-control', 'div'=>'form-group left')));
			?>
		</div>-->

<!--		 <div class="row">
                <?php
                    //echo $form->input('sold_item_type', array_merge($form_data['sold_item_type'], array('type' => 'select', 'div' => 'col-md-6 col-sm-12 m-b-md', 'class' => 'form-control', 'label' => __('You Sell', true))));
//                    echo $form->input('product_code', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Show Product Code Column', true)));
                    ?>
            </div>-->

                    <div class="row">
                        <?php
                        //                    echo $form->input('sold_item_type', array_merge($form_data['sold_item_type'], array('type' => 'select', 'div' => 'col-md-6 col-sm-12 m-b-md', 'class' => 'form-control', 'label' => __('You Sell', true))));
                        echo $form->input('purchase_discount_option', array_merge($form_data['purchase_discount_option'], array('type' => 'select', 'div' => 'col-md-6 col-sm-12 m-b-md', 'class' => 'form-control', 'label' => __('Discount Option', true))));
                        ?>
                        <div class="clearfix"></div>
                    </div>
            <div class="row">
                <?php
                    //echo $form->input('sold_item_type', array_merge($form_data['sold_item_type'], array('type' => 'select', 'div' => 'col-md-6 col-sm-12 m-b-md', 'class' => 'form-control', 'label' => __('You Sell', true))));
                    echo $form->input('update_product_prices', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Update Product Prices After Purchase Invoice', true)));
                    ?>
            </div>
                    <div class="row">
                        <?php
                        echo $form->input('automatic_pay_po', array('type' => 'checkbox', 'label'=>__('Auto Pay Purchase Invoices If the Supplier Has Available Credit.',true),'class' => 'form-x1', 'div' => 'clip-check check-info' ));
                        ?>
                    </div>
                    <div class="row">
                        <?php
                        echo $form->input('po_always_paid', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Mark Purchase Invoices as Already Paid by Default', true),));
                        ?>
                        <div class="clearfix"></div>
                    </div>
                    <div class="row">
                        <?php
                        echo $form->input('po_received', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Mark Purchase Invoices As Received By Default', true),));
                        ?>
                        <div class="clearfix"></div>
                    </div>
                <?php if (ifPluginActive(PURCHASE_CYCLE_PLUGIN)) :  ?>
                    <div class="row">
                                <?php
                echo $form->input('enable_purchase_request_manual_status', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control  manual_status_edit', 'label' => __('Purchase Request Manual Statuses', true),
                'after' => '<span class="enable_purchase_order_link"'.(!settings::getValue(InventoryPlugin, 'enable_purchase_request_manual_status') ? "style='display:none;'" : "") .'><i class="fa fa fa-cog"></i> <a href="#" status_id="'.Post::PURCHASE_REQUEST_TYPE.'" class="click_status" >'. __("Edit Statuses List", true).'</a></span>' ));
                ?>

                                <div class="clearfix"></div>
                            </div>
                            <div class="row">
                                <?php
                echo $form->input('enable_quotation_request_manual_status', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control manual_status_edit', 'label' => __('Quotation Request Manual Statuses', true),
                'after' => '<span class="enable_purchase_order_link"'.(!settings::getValue(InventoryPlugin, 'enable_quotation_request_manual_status') ? "style='display:none;'" : "") .'><i class="fa fa fa-cog"></i> <a href="#" status_id="'.Post::QUOTATION_REQUEST_TYPE.'" class="click_status" >'. __("Edit Statuses List", true).'</a></span>' ));
                ?>

                                <div class="clearfix"></div>
                            </div>
                            <div class="row">
                                <?php
                echo $form->input('enable_purchase_quotation_manual_status', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control manual_status_edit', 'label' => __('Purchase Quotation Manual Statuses', true),
                'after' => '<span class="enable_purchase_order_link"'.(!settings::getValue(InventoryPlugin, 'enable_purchase_quotation_manual_status') ? "style='display:none;'" : "") .'><i class="fa fa fa-cog"></i> <a href="#" status_id="'.Post::PURCHASE_QUOTATION_TYPE.'" class="click_status" >'. __("Edit Statuses List", true).'</a></span>' ));
                ?>

                                <div class="clearfix"></div>
                            </div>
                            <div class="row">
                                <?php
                echo $form->input('enable_purchase_order_manual_status', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control manual_status_edit', 'label' => __('Purchase Order Manual Statuses', true),
                'after' => '<span class="enable_purchase_order_link"'.(!settings::getValue(InventoryPlugin, 'enable_purchase_order_manual_status') ? "style='display:none;'" : "") .'><i class="fa fa fa-cog"></i> <a href="#" status_id="'.Post::PURCHASE_ORDER_TYPE.'" class="click_status" >'. __("Edit Statuses List", true).'</a></span>' ));
                ?>

                                <div class="clearfix"></div>
                            </div>
                <?php endif  ?>
                    <div class="row">
                    <?php
                    echo $form->input('enable_purchase_manual_status', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control manual_status_edit', 'label' => __('Purchase Invoice Manual Statuses', true),
                      'after' => '<span class="enable_purchase_order_link"'.( !settings::getValue(InventoryPlugin, 'enable_purchase_manual_status') ? "style='display:none;'" : "") .'><i class="fa fa fa-cog"></i> <a href="#" status_id="'.Post::MANUAL_PURCHASE_ORDER.'" class="click_status" >'. __("Edit Statuses List" , true ).'</a></span>' ));
                    ?>

                    <div class="clearfix"></div>
                </div>

                <div class="form-group">
                    <?php
                    echo $form->input('enable_purchases_adjustment', array('type' => 'checkbox', 'label'=>__('Enable Adjustment',true),'class' => 'form-x1', 'div' => 'clip-check check-info' ));
                    ?>

                </div>
                <div class="form-group">
                    <?php echo $form->input('enable_purchase_credit_note', array('type'=> 'checkbox', 'div' => 'clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Enable Credit Note', true))); ?>
                    <div class="clearfix"></div>
                </div>

                    <div class="row">
                        <div class="form-group">
                            <?php
                            echo $form->input('custom_journal_description_check', array('checked'=>!empty(settings::getValue(InventoryPlugin, 'custom_journal_description')),'type' => 'checkbox', 'label' => __('Custom Journal Description', true), 'class' => 'form-x1', 'div' => 'clip-check check-info'));
                            ?>
                        </div>
                    </div>
                    <div class="row" id="custom_journal_description_input">
                        <div class="form-group">
                            <?php echo $form->input('custom_journal_description', array('div' => '', 'label' => __('Custom Journal Description Template', true) .'<a style="display: inline-block;font-weight: 100;    color: #353535;" tabindex="-1" class="right" target="_blank" href="'.Router::url(array('controller' => 'email_templates', 'action' => 'placeholder', 'purchaseorders')).'"><u><i class="fa fa-edit"></i>  '.__('Full Variables Guide',true).'</u> </a>', 'rows' => '2', 'class' => 'form-control')); ?>
                        </div>
                    </div>

                </div>
		<br/>		<br/>

</div>
</div>


	</div>

            </div>


    <div class="clearfix" ></div>

    <div class="pages-head-s2020">
        <div class="container">
            <div class="row-flex align-items-center">
                <div class="col-flex-sm-6">
                </div>
                <div class="col-flex-sm-6 d-flex justify-content-end">
                    <button type="button" class="btn s2020 btn-icn btn-secondary font-weight-medium ml-2 mt-0 cancel-btn">
                        <i class="mdi mdi-close fs-20"></i>
                        <span><?php __("Cancel") ?></span>
                    </button>
                    <button type="submit" class="btn s2020 btn-icn btn-success font-weight-medium ml-2">
                        <i class="mdi mdi-content-save-outline fs-20"></i>
                        <span><?php __("Save") ?></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php echo $form->end(); ?>
</div>
<div class="modal fade" id="status_box" tabindex="-1" role="dialog" aria-labelledby="status_box">
    <div class="modal-dialog"  role="document">
        <div class="modal-content">
            <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>

            <div class="modal-body">
                <iframe id="status_box_iframe" width="100%" height="600" src="" frameborder="0" allowfullscreen></iframe>
            </div>

        </div>
    </div>
</div>



  <div class="clearfix">


  </div>


<?php
if (IS_PC) {
    echo $javascript->link(array('summernote/summernote.min'));
    echo $html->css(array('../js/summernote/summernote'), false, ['inline' => false]);
}
?>
<!-- Enable the tabs -->
<script type="text/javascript">
    $(function () {
        $("#72CustomJournalDescriptionCheck").change ( function  ( ) {

            if ( $(this).prop('checked') ){
                $("#custom_journal_description_input").show();
            }else {
                $("#custom_journal_description_input").hide();
            }
        });
        $("#72CustomJournalDescriptionCheck").change ();
    });
  var initial_form_state = "";
    setTimeout(function() {
        initial_form_state = $('#InvoiceForm').serialize();
    }, 1000);

    $('#InvoiceForm').submit(function() {
        if ( !$(".html-invoice").is(":focus"))
        {
            initial_form_state = $('#InvoiceForm').serialize();
        }

    });

    $(window).bind('beforeunload', function(e) {
        var form_state = $('#InvoiceForm').serialize();
        if (initial_form_state != form_state && initial_form_state != "") {

            var message = __("You have unsaved changes on this page. Do you want to leave this page and discard your changes or stay on this page ?");
            e.returnValue = message; // Cross-browser compatibility (src: MDN)
            return message;
        }
    });
  $(document).ready(function() {


    $(".manual_status_edit").change ( function () {
        $(this).parent().find(".enable_purchase_order_link").toggle() ;
    })

    $(".click_status").click ( function (e) {
        e.preventDefault () ;
        $('#status_box').modal();
        $('#status_box_iframe').attr('src', '/owner/follow_up_statuses/index/'+$(this).attr('status_id')+'?box=1' );
    })  

  $('body').on('change', '#72ShippingBilling', function(){

  if($(this).val()=='0'){
  $('#custom_address').hide();
  }else{
  $('#custom_address').show();
  }
  });


  $('#72ShippingBilling').trigger('change');

   	$('#72PoBillingAddress,#72PoShippingAddress').summernote({toolbar:
                [['style', ['bold', 'italic', 'underline', 'clear']], ['font', ['strikethrough']], ['fontsize', ['fontsize']],
                    ['color', ['color']], ['para', ['ul', 'ol', 'paragraph']]]});

	  $('#myTabs a').click(function (e) {
	  if ($(this).attr('href').indexOf("#") != -1) {
			e.preventDefault();
			$(this).tab('show')
			if(history.pushState) {
				history.pushState(null, null, $(this).attr('href'));
			}
			else {
				location.hash = $(this).attr('href');
			}
		}

	});

	if ("onhashchange" in window) {
		function locationHashChanged() {
			$('a[href="' + location.hash + '"]').tab('show');
		}
		window.onhashchange = locationHashChanged;
	}


    if (location.hash !== '') $('a[href="' + location.hash + '"]').tab('show');

});
</script>
<?php
echo $javascript->link(array('report_sideMenu'));
?>
