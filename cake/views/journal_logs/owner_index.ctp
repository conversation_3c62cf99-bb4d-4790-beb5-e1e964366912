<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/index/index.min.css?v=".CSS_VERSION, null, []); ?>

<div class="pages-head fixed-div">
    <div class="row">
        <div class="col-xs-6 col-sm-5 actions-selections-head">
            <?php echo $this->element('top_bar_nav') ?>
        </div>
        <div class="col-sm-7 col-xs-6">
            <div class="top-actions">
            </div>
        </div>
    </div>
</div>
<div class="test">
<?php

echo $list->filter_form('JournalLog', $filters);

?>
</div>

<?php
	if(count($journal_logs) > 2) {


    $multi_select_actions = array();
    $multi_select = false;

$links[] = $html->link(__('View', true), array('action' => 'view_journal_log', '%id%'), array('class' => 'View', 'title' => __('View Journal Log', true)));
//die(debug($journals));
        $output = $list->adminResponsiveList(
            $journal_logs,
            'journals/journal_log_full_row_2',
            array('journal_type' => 'journal','actions' => $links)
            );
		}else{
			$output = '<p class="text-center alert-warning"><strong>'.__('No search results matches these filters, Please change the filters and try again',true).'</strong></p>' ;
		}
//$output = $list->adminResponsiveList($journals, 'invoice_row', array('actions' => $links,'invoice_type' => 'invoice'));
    echo $output;
	
?>
<?php
	echo $javascript->link(array('bootstrap-select.js'));
		echo $html->css(array('bootstrap-select'));

?>
<script>
$(function(){
	
	$('.selectpicker').selectpicker();
	<?php if(isset($_GET['journal_account_id']) && !empty(trim($_GET['journal_account_id']))) { ?>
	$('.selectpicker').selectpicker('val', "<?php echo $_GET['journal_account_id'] ?>");

	<?php } ?>
	
	//select all rows with automatic status to remove the delete link 
	$('.status-symble').each(function(){
		console.log($(this).parents('tr').find(".Delete"));
		$(this).parents('tr').find(".Delete").remove();
	})
	
	$('body').on('click','.log-row',function()
	{

		journal_id = $(this).data('journal-id');
		window.location = "<?php echo Router::url(array('controller' => 'journals','action' => 'view')) ?>/" + journal_id +"#journal-log-tab" ;
	})
})

</script>

<style>
	.form-group.action li {
		display: block;
		float: unset;
	}
	.form-group.action li a {
		margin: 0;
		color: #3a3e63 !important;
	}
</style>