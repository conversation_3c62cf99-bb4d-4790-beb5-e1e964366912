<div class="smsLogs index">
<div class="pages-head">
<h1><?php __('SMS Logs');?></h1>
</div>
<?php
echo $list->filter_form($modelName, $filters);
$fields=array(
	'SmsLog.id' => array('class'=>'text'),
    'SmsLog.sms_campaign_id' => array('class'=>'text'),
    'SmsLog.message' => array('class'=>'text', 'format' => 'html'),
    'SmsLog.phone_number' => array('class'=>'text'),
    'SmsLog.sent_time' => array('class'=>'text'),
    'SmsLog.cost' => array('class'=>'text'),
    'SmsLog.sms_size' => array('class'=>'text'),
    'SmsLog.error_number' => array('class'=>'text'),
    'SmsLog.error_message' => array('class'=>'text', 'format' => 'html'),
    'SmsCampaign.state' => array('title'=>'Campaign Status','class'=>'text'),
);
foreach($smsLogs as &$row){

    $my_datetime=$row['SmsLog']['sent_time'];
    $row['SmsLog']['sent_time']=$row['SmsLog']['sent_time']==null?($row['SmsCampaign']['state']=='finished'?'Fail':'Not Yet'):date('Y-m-d H:i:s',strtotime("$my_datetime UTC"));
}
echo $list->adminIndexList($fields, $smsLogs,true);
?>

</div>
