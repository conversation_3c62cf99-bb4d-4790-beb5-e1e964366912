<?php // $html->css('welcome', false, ['inline' => false]); 
?>
<?php
// warning suppress
echo $html->css(CDN_ASSETS_URL . "s2020/css/layout/first_settings_new.min.css?v=" . CSS_VERSION, false, ['inline' => false]); ?>
<style>
    #main-content {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .bootstrap-select.btn-group .dropdown-menu {
        max-width: 100%;
    }

    .currency-selectize .selectize-dropdown-content{
        max-height: 180px;
    }
    .full-height-selectize .selectize-dropdown-content{
        max-height: 400px;
    }

    .welcome .welcome-card .form-content .selectize-input {
        font-size: 16px;
        font-weight: 400;
        color: #3a3e63;
    }

    .rtl .welcome .welcome-card .title-container .btn-back i {
        transform: scaleX(-1);
    }

    .welcome .welcome-card .form-content .form-control{
        padding: 0px 16px;
    }

    .selectize-control.plugin-multiple_remove_button .item {
        display: block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .ltr .welcome .welcome-card .form-content .selectize-input {
        padding-right: 35px;
    }

    .rtl .welcome .welcome-card .form-content .selectize-input {
        padding-left: 35px;
    }

    .welcome .welcome-card .form-content .form-control{
        padding: 0px 16px;
    }

    .loader-container {
        position: fixed;
        inset: 0;
        background-color: rgba(0,0,0, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 99999;
    }
    .loader-container .loader {
        width: 48px;
        height: 48px;
        border: 5px solid #bbb;
        border-bottom-color: #666;
        border-radius: 50%;
        display: inline-block;
        box-sizing: border-box;
        animation: rotationLoader 1s linear infinite;
    }
    .attachment-container .attachment-meta-row{
        justify-content: space-between;
    }

    @keyframes rotationLoader {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    .d-none {
        display: none !important;
    }
</style>
<div class="loader-container d-none">
    <span class="loader"></span>
</div>
<div class="welcome">
    <div class="url-arrow"></div>
    <div class="row welcome-card">
        <div class="info-content">
            <div class="d-flex flex-nowrap justify-content-between align-items-center">
                <div class="d-md-block">
                    <img src="<?php echo CDN_ASSETS_URL . "s2020/img/layout/first_settings_celebrate_2.png" ?>" class="img-responsive w-100" alt="celebrate">
                </div>
                <div class="daftra-logo-container">
                    <img src="https://cdn.daftra.com/assets/imgs/<?= Domain_Name_Only ?><?= Domain_Name_Only == 'daftra' && getCurrentSite('language_code') == 41 ? "-en" : "" ?>.svg" alt="<?= Site_Full_name ?>" class="daftra-logo" width="103">
                </div>
                <div>
                    <img src="<?php echo CDN_ASSETS_URL . "s2020/img/layout/first_settings_celebrate_1.png" ?>" class="img-responsive w-100" alt="celebrate">
                </div>
            </div>
            <div class="welcome-content">
                <h2 class="welcome-title mt-3" style="font-weight: bold;">
                    <?= sprintf(__("Welcome to your customized version of %s", true), __(Site_Full_name, true)) ?>
                </h2>
                <p class="welcome-text" style="color: #63707c;">
                    <?= __("You can access your own portal via the following link")?>
                </p>
                <p class="domain-name text-center">
                    <span>
                        <?= 'https://' . getCurrentSite('subdomain') ?>
                    </span>
                </p>
            </div>
            <div class="d-flex justify-content-center">
                <div data-select-text-from-title="true" class="domain-action text-center"  title="<?= 'https://' . getCurrentSite('subdomain') ?>">
                    <span>
                        <i class="mdi mdi-content-copy"></i>
                        <?= __("Copy")?>
                    </span>
                </div>
            </div>
        </div>

        <?php echo $form->create('Site', array('action' => 'first_settings', 'type' => 'file')) ?>

        <!-- Stepper 1 Start -->
        <div id="stepper-1" class="form-content">
            <!-- <h2 class="text-left mb-5">
            <?= __("Your Daftra Account is all set, personalize it now")?>
            </h2> -->
            <div class="wbox-bg">
                <?php echo $session->flash(); ?>

                <?php $star = '<span class="ml-1" style="color:red">*</span>'; ?>
                <div class="row">
                    <?php
                    echo $form->input('first_name', array('div' => 'col-md-6 col-xs-6 m-b-md', 'autofocus' => 'true', 'class' => 'form-control', 'required' => 'required', 'label' => __('First Name', true) . $star, 'data-label' => __('First Name', true)));
                    echo $form->input('last_name', array('div' => 'col-md-6 col-xs-6 m-b-md', 'class' => 'form-control', 'required' => 'required', 'label' => __('Last Name', true) . $star, 'data-label' => __('Last Name', true)));?>
                    <div class="col-md-12 p-0">
                    <?php 
                    echo $form->input('position', array('type' => 'select','div' => 'col-md-6 col-xs-6 m-b-md full-height-selectize', 'class' => 'l-input ui-input', 'required' => 'required', 'label' => __('Position', true) . $star, 'placeholder' => __("Please Select", true), "data-app-form-select" => "true"));
                    echo $form->input('company_size', array('type' => 'select','div' => 'col-md-6 col-xs-6 m-b-md full-height-selectize', 'class' => 'l-input ui-input', 'label' => __('Company Size', true) . $star, 'placeholder' => __("Please Select", true), "data-app-form-select" => "true", 'options' => $company_sizes));
                    ?>
                    </div>
                    
                </div>
                <?php
                $disabled = !empty($hide_country_selection);
                ?>


                <div class="row">
                    <div class="col-md-12 more-data-text text-left"><?= __("It looks like you're in")?> <span class="user-data"><?= $countryCodes[$this->data['Site']['country_code']] ?></span><?= __(", your current time zone is")?> <span class="user-data"><?= $timezones[$this->data['Site']['timezone']] ?></span><?= __(", default currency is")?> <span class="user-data"><?= $currencyCodes[$this->data['Site']['currency_code']] ?></span><?= __(", and the Interface Language is")?> <span class="user-data"><?= $languageCodes[$user['language_code']] ?></span>.
                        <span class="show-more-iputs"><?= __("Change Region and Language Preferences")?>.</span>
                    </div>
                </div>
                <div id="prefilled-inputs" class="row d-none">
                    
                    <?php  echo $form->input('country_code', array('type' => 'select','div' => 'col-md-6 col-xs-6 m-b-md', 'disabled' => $disabled,'class' => 'l-input ui-input', 'required' => 'required', 'label' => __('Country', true) . $star, 'placeholder' => __('Select Country', true), 'empty' => __('Select Country', true), "data-app-form-select" => "true")); ?>
                    <?php  echo $form->input('timezone', array('type' => 'select','div' => 'col-md-6 col-xs-6 m-b-md', 'class' => 'l-input ui-input', 'required' => 'required', 'label' => __('Time Zone', true) . $star, 'placeholder' => __('Time Zone', true), "data-app-form-select" => "true")); ?>
                    <?php  echo $form->input('currency_code', array('type' => 'select', 'div' => 'col-md-6 col-xs-6 m-b-md currency-selectize', 'class' => 'l-input ui-input', 'required' => 'required', 'label' => __('Currency', true) . $star, 'placeholder' => __('Select Currency', true), 'empty' => __('Select Currency', true), "data-app-form-select" => "true")); ?>
                    
                    <?php
                    if (count($languageCodes) > 1) {
                        echo $form->input('language_code', array('type' => 'select','div' => 'col-md-6 col-xs-6 m-b-md', 'class' => 'l-input ui-input', 'required' => 'required', 'label' => __('Language', true) . $star, 'placeholder' => __('Select Language', true), 'empty' => __('Select Language', true), "data-app-form-select" => "true"));
                    }
                    ?>
                </div>
                <div class="submit text-center">
                    <button id="go-to-step-2" type="button" class="btn btn-primary btn-lg"><?= __("Next")?></button>
                </div>
            </div>

        </div>
        <!-- Stepper 1 End -->

        <!-- Stepper 2 Start -->
        <div id="stepper-2" class="col-12 col-md-6 form-content d-none">
            <div class="title-container">
                <h2 class="text-left">
                <?= __("Business Information")?>
                </h2>
                <button id="step-2" type="button" class="btn-back"><i class="mdi mdi-chevron-left"></i> <?= __("Back")?></button>
            </div>
            
            <p class="desc-text mb-5"><?= __("Please complete the following information to start using the system:")?></p>
            <div class="wbox-bg">
                <div>
                    <?php echo $session->flash(); ?>

                    <?php $star = '<span class="ml-1" style="color:red">*</span>'; ?>

                    <div class="row">
               
                        <?php
                        echo $form->input('address1', array('div' => 'col-12 m-b-md mx-1', 'class' => 'form-control', 'required' => 'required', 'label' => __('Address', true) . $star, 'data-label' => __('Street Address 1', true)));
                        // echo $form->input('address2', array('div' => 'col-md-6 m-b-md', 'class' => 'form-control', 'label' => __('Address 2', true), 'data-label' => __('Street Address 2', true)));
                        ?>

                        <?php
                        echo $form->input('city', array('div' => 'col-xs-4 m-b-md', 'class' => 'form-control', 'required' => 'required', 'label' => __('City', true) . $star, 'data-label' => __('Suburb/City', true)));
                        echo $form->input('state', array('div' => 'col-xs-4 m-b-md', 'class' => 'form-control', 'required' => 'required', 'label' => __('State', true) . $star, 'data-label' => __('State/Province', true)));
                        echo $form->input('postal_code', array('div' => 'col-xs-4 m-b-md', 'class' => 'form-control', 'label' => __('Postal code', true), 'data-label' => __('Postal code', true)));
                        ?>
                        <div class="clearfix"></div>
                        <div id="bn1" style="display:none;"><?php echo $form->input('bn1', array('div' => 'col-md-6 col-sm-6  m-b-md', 'class' => 'form-control', 'label' => __('Business Number', true))); ?></div>
                        <?php echo $form->input('bn1_label', array('type' => 'hidden')); ?>
                        <div id="bn2" style="display:none;"><?php echo $form->input('bn2', array('div' => 'col-md-6 col-sm-6 m-b-md', 'class' => 'form-control', 'label' => __('Business Number', true))); ?></div>
                        <?php echo $form->input('bn2_label', array('type' => 'hidden')); ?>
                        <div class="clearfix"></div>



                        <!-- <label><?php __("Your business logo") ?></label>
                    <label for="SiteSiteLogo">
                        <span class="container-flex upload-logo-container">
                            <span class="upload-logo-clear"><i class="fa fa-times"></i></span>
                            <span class="upload-logo-preview-container">
                                <span class="upload-logo-preview">
                                    <img src="<?php echo CDN_ASSETS_URL . "s2020/img/layout/first_settings_company_logo_placeholder.png" ?>" class="upload-logo-preview-img" alt="logo">
                                    <span class="upload-logo-file-details">
                                        first_settings_company_logo_placeholder.png
                                    </span>
                                </span>
                            </span>
                            <span class="row-flex align-items-center upload-logo-row">
                                <span class="col-flex-md-2 icon-col">
                                    <i class="mdi mdi-cloud-upload-outline"></i>
                                </span>
                                <span class="col-flex-md-10 caption-col">
                                    <span class="title">
                                        <span class="drag">
                                            <?php __('Drop file here or') ?></span>
                                        <span class="browse"><?php __('Select from your device') ?></span>
                                        <span class="icon"></span>
                                    </span>
                                </span>
                            </span>
                            <?php echo $form->input('site_logo', array('accept' => 'image/*', 'class' => 'file', 'label' => false, 'div' => false, 'type' => 'file')); ?>
                        </span>
                    </label> -->
                        <div class="col-xs-12">
                            <div class="form-group attachments-input">
                                <div class="l-input-box l-input-box--spacing-0">
                                    <label class="l-input-label ui-input-label"><?php __("Your business logo") ?></label>
                                    <input class="hidden" name="data[Site][site_logo]" type="file" id="attachments" name="images" style="display:none;" accept="image/*">

                                    <div class="attachments-container">
                                        <div class="file-drop-area l-uploader ui-uploader">

                                        <input class="file-input" type="file" name="data[Site][site_logo]" accept="image/jpeg, image/png, image/gif, image/jpg, image/webp, image/svg, image/tif">
                                        <div class="ui-uploader-drop-area l-uploader-drop-area">
                                            <span class="ui-uploader-drop-area-icon l-uploader-drop-area-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="33.002" height="44.003" viewBox="0 0 33.002 44.003">
                                                    <path d="M33-47.27a2.369,2.369,0,0,0-.6-1.459L23.98-57.146a2.37,2.37,0,0,0-1.459-.6H22v11H33ZM21.314-44a2.069,2.069,0,0,1-2.063-2.063V-57.75H2.063A2.063,2.063,0,0,0,0-55.687V-15.81a2.063,2.063,0,0,0,2.063,2.063H30.939A2.063,2.063,0,0,0,33-15.81V-44ZM9.672-42.624A4.125,4.125,0,0,1,13.8-38.5a4.125,4.125,0,0,1-4.125,4.125A4.125,4.125,0,0,1,5.547-38.5,4.125,4.125,0,0,1,9.672-42.624ZM27.548-22h-22l.042-4.167,3.4-3.4a.983.983,0,0,1,1.417.042l3.4,3.4,8.9-8.9a1.031,1.031,0,0,1,1.459,0l3.4,3.4Z" transform="translate(0 57.75)" fill="#e4ebf2"></path>
                                                </svg>
                                            </span>
                                            <span class="ui-uploader-drop-area-text l-uploader-drop-area-text">
                                                <i class="ui-uploader-drop-area-text-upload-icon fal fa-cloud-upload s2020 ui-icon--size-20 u-text-color-primary"></i>
                                                <p style="margin-bottom: 0;padding-bottom:0;"><span>&nbsp;<?= __("Drop file here or")?> &nbsp;</span><span class="u-text-color-primary"><?= __("select from your computer") ?></span></p>
                                            </span>
                                        </div>
                                    </div>

                                    </div>
                                    <div class="l-uploader-file-items l-uploader-file-items--spacing-top ui-uploader-file-items" style="max-height: none;">
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <?
                    if (isset($SupplierDirectory['SupplierDirectory'])) {
                    ?>
                        <?php echo $form->input('supplier_directory', array('value' => '1', 'type' => 'hidden')); ?>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="input-fields inside-form-box">
                                    <h3 class="rounded-item  head-bar theme-color-a"><span class="contact-info">
                                            <?php __("Supplier Directory Listing Information") ?>
                                        </span></h3>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <?php echo $form->input('market_message', array('type' => 'textarea', 'label' => __('Company Description', true), 'placeholder' => __('', true), 'div' => '', 'class' => 'form-control')); ?>
                                                        <div class="clear"></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <?php echo $form->input('product_service', array('type' => 'textarea', 'label' => __('Product and Service to Provide', true), 'div' => '', 'class' => 'form-control')); ?>
                                                        <div class="clear"></div>
                                                    </div>
                                                </div>
                                                <?php echo $form->input('industry_id', array('multiple' => 'multiple', 'label' => __('Industries', true), 'div' => '', 'class' => 'form-control')); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <? } ?>
                </div>
                <div class="submit text-center">
                    <button class="btn btn-primary btn-lg SiteFirstSettingsSubmit" type="submit"><?= __("Complete")?></button>
                </div>
            </div>

        </div>
        <!-- Stepper 2 End -->
        <?php echo $form->end(); ?>


    </div>
    <script>
        var imageInvalidFileMessage = "<?php __('Invalid image type'); ?>";
        var imageInvalidFileSizeMessage = "<?php __('File size is too large'); ?>";
    </script>
    <?php
    echo $html->css(array('bootstrap-select_v' . CSS_VERSION . '.css', 'ajax-bootstrap-select'), null, array('inline' => false));
    echo $javascript->link(array('bootstrap-select.js', 'ajax-bootstrap-select.min.js'));
    echo $javascript->link(array(CDN_ASSETS_URL . 's2020/js/layout/first_settings.js?v=' . JAVASCRIPT_VERSION));
    ?>
    <script type="text/javascript">
        //<![CDATA[
        var czones = <?php echo json_encode($czones) ?>;
        var ccurr = <?php echo json_encode($ccurr) ?>;
        var bnf = <?php echo json_encode($bnf) ?>;
        //]]>
    </script>
    <script type="text/javascript">
        //<![CDATA[
        var options = {
            ajax: {
                url: '<?php echo Router::url(array('action' => 'industry_json_find')); ?>',
                type: 'get',
                dataType: 'json',
                data: {
                    q: '{{{q}}}'
                }
            },
            locale: {
                searchPlaceholder: '<?php __('Enter keywords to Search') ?>',
            },
            preprocessData: function(data) {
                var i, l = data.length,
                    array = [];
                if (l) {
                    for (i = 0; i < l; i++) {
                        array.push($.extend(true, data[i], {
                            text: data[i].name,
                            value: data[i].id,
                            data: {
                                subtext: data[i].details
                            }
                        }));
                    }
                }
                return array;
            }
        };

        function updatePicker(selector) {
            val = $(selector).val();
            var text = $(selector + " option[value='" + val + "']").text();
            $(selector).parent().find('.bootstrap-select .filter-option').text(text);
        }

        function validateFields_step_1() {
                var rules = {
                    '#SiteFirstName': ['notEmpty'],
                    '#SiteLastName': ['notEmpty'],
                    '#SitePosition': ['notEmpty'],
                    '#SiteCompanySize': ['notEmpty'],
                    //TO DO => add position
                    '#SiteCountryCode': ['notEmpty'],
                    '#SiteTimezone': ['notEmpty'],
                    '#SiteCurrencyCode': ['notEmpty'],
                    '#SiteLanguageCode': ['notEmpty'],
                };
    
                var validationMessages = {
                    'notEmpty': __('Required'),
                };
    
                return validate(rules, validationMessages);
            }

            function validateFields_step_2() {
                var rules = {
                    '#SiteAddress1': ['notEmpty'],
                    '#SiteCity': ['notEmpty'],
                    '#SiteState': ['notEmpty'],
                };
    
                var validationMessages = {
                    'notEmpty': __('Required'),
                };
    
                return validate(rules, validationMessages);
            }


        function validateFields() {
            var rules = {
                '#SiteFirstName': ['notEmpty'],
                '#SiteLastName': ['notEmpty'],
                '#SiteAddress1': ['notEmpty'],
                '#SiteCity': ['notEmpty'],
                '#SiteState': ['notEmpty'],
                //            '#SitePostalCode': ['notEmpty'],
                '#SitePhone2': ['notEmpty'],
                '#SiteCountryCode': ['notEmpty'],
                '#SiteTimezone': ['notEmpty'],
                '#SiteCurrencyCode': ['notEmpty'],
                '#InvoicePaymentPaymentMethod': ['notEmpty'],
                '#InvoicePaymentAmount': ['notEmpty', 'isNumeric'],
                '#InvoicePaymentStatus': ['notEmpty'],
                '#InvoicePaymentNotes': {
                    'rules': [{
                        'rule': 'maxLength',
                        'value': 500
                    }],
                    empty: true
                }
            };

            var validationMessages = {
                'Email': __('Valid Email required'),
                'notEmpty': __('Required'),
                'isNumeric': __('Valid number required'),
                'isNotMinus': __('Positive number required'),
                'lessThanOrEqual': __('Value must be less or equal to :value'),
                'maxLength': __('Only :value characters allowed')
            };

            return validate(rules, validationMessages);
        }


        $(function() {

            $('.show-more-iputs').click(function() {
                $(this).parent().hide();
                $('#prefilled-inputs').removeClass('d-none');
                $(".welcome-card").addClass('show-more');
            });
            <?php if(empty($this->data['Site']['country_code']) || empty($this->data['Site']['timezone']) || empty($this->data['Site']['currency_code'])) {?>
                $('.show-more-iputs').trigger('click');
            <?php } ?>

            $('#go-to-step-2').click(function() {
                if (!validateFields_step_1()) {
                    return validateFields_step_1();
                }

                $('#stepper-2').removeClass('d-none');
                $('#stepper-1').addClass('d-none');

                $(".welcome-card").addClass('step2');
            })

            $(".btn-back").click(function() {
                $('#stepper-2').addClass('d-none');
                $('#stepper-1').removeClass('d-none');
                $(".welcome-card").removeClass('step2');
            })

            $(document).on('click', '.SiteFirstSettingsSubmit', function(e){
                e.preventDefault();
                console.log('validaaate', validateFields_step_2)
                if (!validateFields_step_2()) {
                    return validateFields_step_2();
                }
                $('#SiteFirstSettingsForm').submit();
                $('.loader-container').removeClass('d-none');

            })

            $('#SiteCountryCode').change(function() {
                var val = $(this).val();
                if($('#SiteTimezone')[0].selectize){
                    if (val != '' && czones.hasOwnProperty(val) && czones[val].hasOwnProperty(0)) {
                        $('#SiteTimezone')[0].selectize.setValue(czones[val][0]);
                    } else {
                        $('#SiteTimezone')[0].selectize.setValue('');
                    }
                }

                if (val != '') {
                    if($('#SiteCurrencyCode')[0].selectize){
                        $('#SiteCurrencyCode')[0].selectize.setValue(ccurr[val]);
                    }

                } else {
                    if($('#SiteTimezone')[0].selectize){
                        $('#SiteTimezone')[0].selectize.setValue('');
                    }
                }

                <?php for ($i = 1; $i <= 2; $i++) { ?>

                    if (val in bnf && 'bn<?php echo $i ?>' in bnf[val]) {
                        $('#bn<?php echo $i ?> label').html(bnf[val]['bn<?php echo $i ?>']['label'] + ' <small style="font-size:10px;color:#777">(<?php echo __('Optional', true) ?>)</small>');
                        $('#SiteBn<?php echo $i ?>Label').val(bnf[val]['bn<?php echo $i ?>']['label']);
                        $('#bn<?php echo $i ?>').show();
                    } else {
                        $('#bn<?php echo $i ?> label').html('');
                        $('#SiteBn<?php echo $i ?>Label').val('');
                        $('#bn<?php echo $i ?>').hide();
                    }
                <? } ?>



                updatePicker('#SiteCurrencyCode');
                updatePicker('#SiteTimezone');



            });


            $('#SiteCountryCode').change();



            $('.selectpicker').selectpicker().filter('.with-ajax').ajaxSelectPicker(options);

        });
        //]]>
    </script>


    <?php
    echo $javascript->link(array('summernote/summernote.min'));
    echo $html->css(array('../js/summernote/summernote', '../js/summernote/summernote-bs3_v' . CSS_VERSION), false, ['inline' => false]);


    ?>
    <script>
        $('#SiteMarketMessage,#SiteProductService').summernote({
            toolbar: [
                ['style', ['bold', 'italic', 'underline', 'clear']],
                ['font', ['strikethrough']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']]
            ]
        });
    </script>
    <?php
    if (((strpos($_SERVER['HTTP_USER_AGENT'], 'Mobile/') !== false) && (strpos($_SERVER['HTTP_USER_AGENT'], 'Safari/') == false))) { ?>
        <script>
            $(function() {
                var message = {
                    "Site": {
                        "id": "<?php echo $this->data['Site']['id'] ?>",
                        "business_name": "<?php echo $this->data['Site']['business_name'] ?>",
                        "subdomain": "<?php echo $this->data['Site']['subdomain'] ?>",

                    }
                };
                window.webkit.messageHandlers.siteAdd.postMessage(message);

            })
        </script>
    <?php } ?>
<?php if (Domain_Name_Only == 'daftra'): ?>
    <!-- Event snippet for Reg for a new account conversion page -->
    <script>
        gtag('event', 'conversion', {
            'send_to': 'AW-*********/Q8XkCPKNz3IQgK6MlAM'
        });
    </script>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-9R42S45PXK"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-9R42S45PXK');
    </script>
    <?php @include('../../tracking_code.php') ?>
<?php endif ?>
    <script>
        $(function() {
            var dataTransfer = new DataTransfer();
            // upload files
            function returnFileSize(number) {
                if (number < 1024) {
                    return `${number} bytes`;
                } else if (number >= 1024 && number < 1048576) {
                    return `${(number / 1024).toFixed(1)} KB`;
                } else if (number >= 1048576) {
                    return `${(number / 1048576).toFixed(1)} MB`;
                }
            }

            $(document).on("click", "[data-app-form-uploader-delete-btn]", function() {
                dataTransfer.items.remove($(this).attr("tabIndex"));
                $("#attachments")[0].files = dataTransfer.files;
                $(this).parents(".l-uploader-file-item").remove();
                $(".attachments-container").removeClass('attached');
                $(".file-input").val('');
                $(".l-uploader-file-items").html('');
                $("#attachments")[0].files.forEach(function(currFile, i) {
                    $("[data-app-form-uploader-delete-btn]").eq(i).attr("tabIndex", i);
                });
            });

            var $fileInput = $(".file-input");
            var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
            $fileInput.on("change", function() {
                var filesCount = $(this)[0].files.length;
                var $textContainer = $(this).prev();
                $(".attachments-container").addClass('attached');

                $(this)[0].files.forEach(function(currFile, i) {
                    dataTransfer.items.clear();
                    dataTransfer.items.add(currFile);
                    $("#attachments")[0].files = dataTransfer.files;
                    var fileType = currFile["type"];
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $(".ui-uploader-file-items")
                            .html(`<div class="attachment-container">
                                <div class="attachment-thumb" data-app-form-uploader-edit-btn="true">
                                    <img src="${e.target.result}" alt="${e.target.result}">
                                </div>
                                <div class="attachment-meta" >
                                    <div class="attachment-meta-row">
                                        <div class="attachment-name" data-app-form-uploader-edit-btn="true">
                                            <i class="mdi mdi-file"></i>
                                            <span>${currFile.name}</span>
                                        </div>
                                        <div class="attachment-actions">
                                            <span>${returnFileSize(currFile.size)}</span>
                                            <i class="ui-uploader-file-item-action mdi mdi-trash-can text-danger u-text-color-danger fs-26" tabindex="0"  data-app-keyboard-support-options=\'{"keypress": {"keyCode": "13", "action": "click"}}\' title="Delete" data-app-form-uploader-delete-btn="true"></i>

                                        </div>
                                    </div>
                                </div>
                            </div>
                    `);
                    };
                    reader.readAsDataURL(currFile);
                });
            });

            $(document).on('click', "[data-app-form-uploader-edit-btn]", function() {
                $(".file-input").trigger("click");
            })

            $("#SiteFirstName, #SiteLastName").on("keyup", function(e){
                var str = e.target.value
                e.target.value = str.charAt(0).toUpperCase() + str.slice(1);
            })
        });
    </script>
