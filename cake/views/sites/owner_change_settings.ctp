<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/create/create.min.css?v=".CSS_VERSION, null, []); ?>

<div class="FormExtended row"> <?php echo $form->create('Site', array('type' => 'file', 'action' => 'change_settings')); ?>
    <div class="col-md-6">
        <div class="input-fields inside-form-box">
            <h3 class="rounded-item  head-bar theme-color-a"><span class="contact-info"></span><?php __("Business Details") ?></h3>
            <?php echo $form->input('business_name', array('div' => 'form-group', 'class' => 'INPUT form-control required form-control')); ?>
            <div class="form-group row">
                <?php
                echo $form->input('first_name', array('div' => 'col-md-6 col-xs-6', 'class' => ' form-control INPUT form-control required'));
                echo $form->input('last_name', array('div' => 'col-md-6 col-xs-6', 'class' => ' form-control INPUT form-control required'));
                ?>
            </div>

            <?php
            /* 		<div class="form-group sub-domain">
              <?php echo $form->input('subdomain', array('div' => 'text left', 'class' => ' form-control required', 'label' => __('Subdomain', true))); ?>
              <div class="subdomain"><strong class="left sub-text">.<? echo Domain_Short_Name ?></strong><span class="tooltip" title="settings-subdomain"></span></div>
              <div class="clear"></div>
              </div> */
            ?>
            <div class="form-group row">
                <?php echo $form->input('phone2', array('div' => 'col-md-6 col-xs-6', 'class' => 'INPUT  form-control', 'type' => 'tel', 'label' => __('Mobile', true))); ?>
                <?php echo $form->input('phone1', array('div' => 'col-md-6 col-xs-6', 'class' => 'INPUT  form-control', 'type' => 'tel', 'label' => __('Telephone', true))); ?>
            </div>
            <div class="form-group row">
                <?php echo $form->input('address1', array('div' => 'col-md-6 col-xs-6', 'class' => 'INPUT  form-control', 'label' => __('Street Address 1', true))); ?>
                <?php echo $form->input('address2', array('div' => 'col-md-6 col-xs-6', 'class' => 'INPUT  form-control', 'label' => __('Street Address 2', true))); ?>
            </div>
            <div class="form-group row">
                <?php
                echo $form->input('city', array('div' => 'col-md-4 col-sm-4', 'class' => 'INPUT  form-control '));
                echo $form->input('state', array('div' => 'col-md-4 col-sm-4', 'class' => 'INPUT  form-control'));
                echo $form->input('postal_code', array('div' => 'col-md-4 col-sm-4', 'class' => 'INPUT  form-control'));
                ?>
            </div>
            <div class="form-group">
                <?php echo $form->input('country_code', array("disabled"=>"disabled",'label' => __('Country', true), 'div' => 'form-group', 'class' => 'INPUT form-control required', 'empty' => __('Select Country', true))); ?>
            </div>
            <div class="form-group">
                <div id="bn1" style="display:none;"><?php echo $form->input('bn1', array('div' => 'col-md-6 col-sm-6 m-b-md', 'class' => 'form-control', 'disabled' => $this->data['Site']['bn1_locked'],'label' => __('Business Number', true))); ?></div><?php
                echo $form->input('bn1_label', array('type' => 'hidden'));
                ?><div id="bn2" style="display:none;"><?php echo $form->input('bn2', array('div' => 'col-md-6 col-sm-6 m-b-md', 'class' => 'form-control', 'disabled' => $this->data['Site']['bn2_locked'], 'label' => __('Business Number', true))); ?></div><?php
                echo $form->input('bn2_label', array('type' => 'hidden'));
                ?>
            </div>
            <div class="clear"></div>


        </div>
    </div>
    <div class="col-md-6">

        <div class="input-fields inside-form-box">
            <h3 class="rounded-item  head-bar theme-color-a"><span class="contact-info">
                    <?php __("Account Settings") ?>
                </span></h3>
            <div class="form-group">

                <?php
//                if (empty($this->data['Site']['next_invoice_number'])) {
//                    $value = '000001';
//                } else {
//                    $value = $this->data['Site']['next_invoice_number'];
//                }
//                echo $form->input('next_invoice_number', array('div' => 'form-group', 'type' => 'number', 'class' => 'INPUT form-control required', 'value' => $value));
//
//                echo $form->input('edit_invoice_number', array('div' => 'input clip-check check-info', 'class' => 'INPUT form-control required', 'label' => __('Enable changing invoice number for each invoice', true)));
                ?>
                <div class="form-group mb-0 row">

                    <?php
                    echo $form->input('currency_code', array('div' => 'form-group col-md-6 col-sm-12 ','disabled' =>settings::getValue(ExpensesPlugin, 'journals_local_currency_code')? 'disabled' : false  ,'class' => 'INPUT  form-control required', 'empty' => __('Select Currecny', true), 'label' => __('Currency <span class="tooltip" title="settings-currency"></span>', true) .'<a style="display: inline-block;font-weight: 100;    color: #353535;" tabindex="-1" class="right" id="EditCurrency" href="'.Router::url(['controller' => 'local_currency_rates', 'action' => 'index']).'" title="Local Currency Rates"><u><i class="fa fa-edit"></i>  '.__('Currency Rates', true).'</u> </a>'));
                    echo $form->input('timezone', array('div' => 'form-group col-md-6 col-sm-12', 'class' => 'INPUT form-control required ', 'empty' => __('Select Time Zone', true), 'label' => __('Time Zone', true), 'after' => '</span>'));
                    ?>
                </div>
                <div class="form-group mb-0 row">
                <?php
                echo $form->input('negative_currency_formats', array('options' => $negative_currency_formats,'div' => 'form-group col-md-6 col-sm-12', 'class' => 'INPUT form-control required', 'label' => __('Negative Currency Formats', true)));
                echo $form->input('date_format', array('div' => 'form-group col-md-6 col-sm-12', 'class' => 'INPUT form-control required', 'empty' => __('Select Date format', true), 'label' => __('Date Format <span class="tooltip" title="settings-dateformat"></span>', true)));
                ?>
                </div>
                <div class="form-group mb-0 row">
                <?php debug($languageCodes);
                if(count($languageCodes)>1){
                echo $form->input('language_code', array('div' => 'form-group col-md-12 col-sm-12', 'class' => 'INPUT form-control required', 'empty' => __('Select Language', true), 'label' => __('Language', true)));
                }
                ?>
                </div>
                <div class="form-group row">
                    <?php
                    echo $form->input('sold_item_type', array_merge($form_data['sold_item_type'], array('type' => 'select', 'div' => 'col-md-12 col-sm-12', 'class' => 'form-control', 'label' => __('You Sell', true))));
//                    echo $form->input('invoicing_method', array_merge($form_data['invoicing_method'], array('type' => 'select', 'div' => 'col-md-6 col-sm-12 m-b-md', 'class' => 'form-control', 'label' => __('Invoicing Method', true))));
                    ?>
                </div>
             <div class="form-group row">
                   <?php
                   echo $form->input('invoice_print_method', array_merge($form_data['invoice_print_method'], array('type' => 'select', 'div' => 'col-md-12 col-sm-12', 'class' => 'form-control', 'label' => __('Printing Method', true))));
                  ?>

             </div>
            </div>
        </div>
        <div class="input-fields inside-form-box">
            <h3 class="rounded-item  head-bar theme-color-a"><span class="contact-info">
                    <?php __("Logo") ?><span class="tooltip" title="settings-logo"></span>
                </span></h3>
            <div class="form-group">
                <?php
                echo $form->input('site_logo', array('accept'=>".png,.jpg,.jpeg,.gif,.tif,.tiff,.svg,image/png,image/jpeg,image/gif,image/tiff,image/svg+xml",'label' => false, 'div' => 'form-group file','', 'class' => 'INPUT', 'type' => 'file', 'between' =>
                    $this->element('image_input_between', array('info' => $image_settings['site_logo'], 'field' => 'site_logo', 'id' => (is_array($this->data) ? $this->data['Site']['id'] : null), 'base_name' => (is_array($this->data) ? $this->data['Site']['site_logo'] : '')))
                ));
                ?>
            </div>
        </div>
    </div>
 </div>
        <div class="pages-head-s2020">
            <div class="container">
                <div class="row-flex align-items-center">
                    <div class="col-flex-sm-6">
                        <div class="pages-head-title">
                            <h2 class="pb-1 pb-md-0"><?php __("Account Settings") ?></h2>
                        </div>
                    </div>
                    <div class="col-flex-sm-6 d-flex justify-content-end">
                        <a href="<?php echo Router::url(array('controller' => 'owners', 'action' => 'change_email')) ?>" class="btn s2020 fs-12 fs-14-md btn-secondary font-weight-medium mt-0">
                            <span><?php __("Change Email") ?></span>
                        </a>
                        <a href="<?php echo Router::url(array('controller' => 'owners', 'action' => 'change_password')) ?>" class="btn s2020 fs-12 fs-14-md btn-secondary font-weight-medium ml-2 mt-0">
                            <span><?php __("Change Password") ?></span>
                        </a>
                        <button type="submit" class="btn s2020 btn-icn btn-success font-weight-medium ml-2">
                            <i class="mdi mdi-content-save-outline fs-20"></i>
                            <span><?php __("Save") ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
     </div>
   </div>
     <?php echo $form->end(); ?>
<script type="text/javascript">



    var bnf = <?php echo json_encode($bnf) ?>;

    $('document').ready(function() {
        $('div.required').removeClass('required')

        $('#SiteCountryCode').change(function() {
            var val = $(this).val();

<?php for ($i = 1; $i <= 2; $i++) { ?>

                if (val in bnf && 'bn<?php echo $i ?>' in bnf[val]) {
                    $('#bn<?php echo $i ?> label').html(__(bnf[val]['bn<?php echo $i ?>']['label']) + ' <small style="font-size:10px;color:#777">(<?php echo __('Optional', true) ?>)</small>');
                    $('#SiteBn<?php echo $i ?>Label').val(__(bnf[val]['bn<?php echo $i ?>']['label']));
                    $('#bn<?php echo $i ?>').show();
                }
                else
                {
                    $('#bn<?php echo $i ?> label').html('');
                    $('#SiteBn<?php echo $i ?>Label').val('');
                    $('#bn<?php echo $i ?>').hide();
                }
<? } ?>
        });


        $('#SiteCountryCode').change();

        $('form#SiteChangeSettingsForm').bind('submit', function(evt) {
            if (!validateFields()) {
                evt.preventDefault();
            }
        });

			var initial_form_state = "";
			setTimeout(function() {
				initial_form_state = $('#SiteChangeSettingsForm').serialize();
			}, 1000);

			$('#SiteChangeSettingsForm').submit(function() {
				if ( !$(".html-invoice").is(":focus"))
				{
					initial_form_state = $('#SiteChangeSettingsForm').serialize();
				}

			});

			$(window).bind('beforeunload', function(e) {
				var form_state = $('#SiteChangeSettingsForm').serialize();

				if (initial_form_state != form_state && initial_form_state != "") {

					var message = __("You have unsaved changes on this page. Do you want to leave this page and discard your changes or stay on this page ?");
					e.returnValue = message; // Cross-browser compatibility (src: MDN)
					return message;
				}
			});
    });

    function validateFields() {
        var rules = {
            '.required': ['notEmpty'],
            '.numeric': ['isNumeric']
        };

        var validationMessages = {
            notEmpty: __('Required'),
            isNumeric: __('Please enter a valid number')
        };



        return validate(rules, validationMessages);
    }
</script>
