<!DOCTYPE html>
<!--[if lt IE 7 ]> <html lang="en" class="no-js lt-ie10 lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7 ]>    <html lang="en" class="no-js lt-ie10 lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8 ]>    <html lang="en" class="no-js lt-ie10 lt-ie9"> <![endif]-->
<!--[if IE 9 ]>    <html lang="en" class="no-js lt-ie10"> <![endif]-->
<!--[if (gt IE 9)|!(IE)]><!-->
<?php
    $dateFormats = getDateFormats('std');
    $dateFormat = $dateFormats[getAuthOwner('date_format')];
?>
<html lang="en" class="no-js" data-site-data='{"dateFormat": "<?= $dateFormat ?>"}'>
    <!--<![endif]-->
    <head>
        <?php if ('enerpize' == Domain_Name_Only) { ?>
        <!-- Google Tag Manager -->
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-MFGLNCR');</script>
        <!-- End Google Tag Manager -->
        <?php } ?>

        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <title><?php echo $title_for_layout; ?> - <?php echo h(getCurrentSite('business_name')); ?> </title>
        <meta http-equiv="content-type" content="text/html; charset=utf-8" />

        <link rel="shortcut icon" href="/css/images/<?php echo Site_Full_name_NoSpace ?>-favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" type="text/css" href="/css/oifonts.css?v=10" />
        <link rel="stylesheet" type="text/css" href="/css/fonts/ff/flaticon-test.css?v=10"/>
        <link rel="stylesheet" type="text/css" href="/css/flaticon.css?v=10" />
        <link rel="stylesheet" type="text/css" href="/css/nav-ico/simple-line-icons.css?v=9" />
        <link rel="stylesheet" type="text/css" href="/css/jquery.tooltip.css?v=130" />
        <link rel="stylesheet" type="text/css" href="/css/jqueryui.css?v=<?= CSS_VERSION ?>" />
        <link rel="stylesheet" type="text/css" href="/css/app<?=$is_rtl?'_rtl':''?>_v<?= CSS_VERSION ?>.css" />
        <link rel="stylesheet" type="text/css" href="/css/common_v<?= CSS_VERSION ?>.css" />
        <link rel="stylesheet" type="text/css" href="/css/fonts_v<?= CSS_VERSION ?>.css" />
        <link rel="stylesheet" type="text/css" href="/css/dashboard.css?v=13" />
        <link rel="stylesheet" type="text/css" href="/css/pages.css?v=14" />
        <link rel="stylesheet" type="text/css" href="/css/listing.css?v=13" />
        <link rel="stylesheet" type="text/css" href="/css/payment.css?v=16" />
        <link rel="stylesheet" type="text/css" href="/css/import.css?v=13" />
        <link rel="stylesheet" type="text/css" href="/css/messages.css?v=14" />
        <link rel="stylesheet" type="text/css" href="/css/pos-app.css?v=1" />
        <link rel="stylesheet" type="text/css" href="/css/beta.css?v=2" />
        <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/MaterialDesign-Webfont/4.8.95/css/materialdesignicons.min.css" />
        <link rel="stylesheet" type="text/css" href="/css/buttons.css?v=13" />
        <link rel="stylesheet" type="text/css" href="/css/breadcrumb_new.css?v=1" />
        <link rel="stylesheet" href="/css/app-manager-dropdown.css" />
        <link rel="stylesheet" type="text/css" href="/css/cal-css/jquery.calendars.picker.css?v=1" />
        <link rel="stylesheet" href="<?php echo CDN_ASSETS_URL ?>s2020/css/layout/app.min.css?v=<?= CSS_VERSION ?>" />
        <link rel="stylesheet" href="<?php echo CDN_ASSETS_URL ?>s2020/css/layout/dark.min.css?v=<?= CSS_VERSION ?>" />
          <?php    if($is_rtl){?>
            <link rel="stylesheet" type="text/css" href="/css/breadcrumb_new_ar_v1.css" />
         <?php }?>
        <link rel="stylesheet" type="text/css" href="/css/delete_message_v1.css" />
		<link href="https://fonts.googleapis.com/css?family=Amiri|Cairo:600|Roboto&amp;subset=arabic" rel="stylesheet">
        <link id="help-css" rel="stylesheet" type="text/css" href="/dist/app/js/pages/help/help.styles.css" media="all">
        <script>
            isLocalStorageSupported = function() {
                var testKey = 'test', storage = window.sessionStorage;
                try {
                    storage.setItem(testKey, '1');
                    storage.removeItem(testKey);
                    return true;
                } catch (error) {
                    return false;
                }
            };

            if(isLocalStorageSupported() && localStorage.getItem('dark')) {
                document.documentElement.classList.add('dark-active');
            } else {
                document.documentElement.classList.remove('dark-active');
            }

            var IS_APP = <?php echo !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] == "com.daftra.app" ? 'true' : 'false' ?>;
            var IS_SAFARI = <?php echo strpos($_SERVER['HTTP_USER_AGENT'], 'Safari') && !strpos($_SERVER['HTTP_USER_AGENT'], 'Chrome') ? 'true' : 'false' ?>;

        </script>
        <?php

        $site = getCurrentSite();
        $hotjar_accounts = getenv('HOTJAR_ACCOUNTS') ? explode(',', getenv('HOTJAR_ACCOUNTS')) : [];

        if ($site && (Domain_Name_Only == "daftra" || Domain_Name_Only == "default") && (($site['created'] <= "2024-03-26 23:59:59" && $site['country_code'] == "EG") || in_array($site['id'], $hotjar_accounts))){ ?>
            <script type="text/javascript">
                (function(c,l,a,r,i,t,y){
                    c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                    t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                    y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "bt74mtyy5y");
            </script>

            <!-- Hotjar Tracking Code for Site 3913455 (name missing) -->
            <script>
                (function(h,o,t,j,a,r){
                    h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                    h._hjSettings={hjid:3913455,hjsv:6};
                    a=o.getElementsByTagName('head')[0];
                    r=o.createElement('script');r.async=1;
                    r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                    a.appendChild(r);
                })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            </script>
         <?php }

        if ($this->action != 'owner_dashboard' && $this->action != 'client_dashboard') {
            ?>
            <link rel="stylesheet" type="text/css" href="/css/forms_v4.css?v=7" />
            <?php
        }

        if (!empty($cssFiles))
            echo $html->css($cssFiles, null, []);
        if (!IS_PC) {
            echo $html->css('rwd-table.min.css', null, []);
        }
        ?>
        <?php $bodyClass = ''; ?>
		<?php if($is_rtl){ ?>
			<link rel="stylesheet" type="text/css" href="/css/rtl-fix.css" />
                        <!-- <link rel="stylesheet" type="text/css" href="/css/rtl-breadcrumb.css" /> !-->

		<?php } ?>
        <link rel="stylesheet" type="text/css" href="/css/screen-sidenav_v<?= CSS_VERSION; ?>.css?v=27" />
       <?php
        if($is_rtl)
        {
            echo  '<link rel="stylesheet" type="text/css" href="/css/all-screen-responsive-ar.css?v=1" />';

        }
        else
        {
         echo  '<link rel="stylesheet" type="text/css" href="/css/all-screen-responsive.css?v=1" />';
        }

		$formats = getDateFormats('js');
		$jsFormat = $formats[getCurrentSite('date_format')];
		$isClient =  isset($client);
       ?>
       <script>
		   var __jsDateFormat = '<?php echo $jsFormat ?>';
		   var __countryCode = '<?php echo getCurrentSite('country_code')?>';
           var __currencyCode = '<?php echo getCurrentSite('currency_code')?>';
       </script>


        <style type="text/css" media="screen">
            /* #main-content, .main-area{
                margin-top: 5px !important;
            }
            .main-area {
                padding-top: 0 !important;
            }
            #nav_control {
                margin-bottom: 5px !important;
            }        */
            
            
            .xdebug-var-dump {
                text-align: left;
            }
            .appoint-bg-panel,.master-widget .widget-head, .paging ul li.current, .paging ul li a:hover,.header, .login-box-container h1, .footer, .action a:hover, .mob-nav-trigger, .mob-nav-trigger:hover, .mob-nav-trigger:focus, .head-bar, .primary-bg {
                background:<?php echo ($site['theme_color'] ? (strpos($site['theme_color'],'linear-gradient') !== false ? $site['theme_color'] : '#'.$site['theme_color']) : '#000000') ?>;
            }
           .appoint-bg-panel .panel-heading,.expense-item p,.expense-item span,.table table-striped b-light td a:hover, .master-widget .widget-head h3, .paging ul li.current, .paging ul li a:hover, .login-box-container h1, .action a:hover, .mob-nav-trigger, .mob-nav-trigger:hover, .mob-nav-trigger:focus, .head-bar, .head-bar a,.navbar-nav > li > a > i,.navbar-nav > li > a,.current-date,#shrink-sidebar i,.header .breadcrumb a,.header .breadcrumb > .active , .header-breadcrumb .breadcrumb-item + .breadcrumb-item:before ,.header .mdi-account-circle,.header .drop-cont,.navbar-nav > li > a p, .res-right .text-white  {
                color: #<?php echo $site['font_color'] ? $site['font_color'] . '!important' : 'ffffff' ?>
            }
            .calendars-month {
                width: 25em !important;
            }
            .calendars-month-header {
                height: 2.5em !important;
            }
            .calendars-popup{
                z-index: 10000;
            }
            .day-view-entry-label.day-view-entry-label { position: relative; border: none;border-left: 3px solid #ddd;  }
            .xdebug-var-dump{
                direction: ltr !important;
            }
			<?php if(Domain_Full_Name == 'www.daftra.com'){ ?>
				.light .footer-logo{
						background: url(<?= Router::url('/css/images/en_daftra_small_logo.png',true) ?>) no-repeat !important;
						width: 113px;
						height: 39px;
						background-size: cover;
				}
			<?php } ?>
        </style>
        <script>
            function getImageLightness(imageSrc,callback) {
    var img = document.createElement("img");
    img.src = imageSrc;
    img.style.display = "none";
    document.body.appendChild(img);

    var colorSum = 0;

    img.onload = function() {
        // create canvas
        var canvas = document.createElement("canvas");
        canvas.width = this.width;
        canvas.height = this.height;

        var ctx = canvas.getContext("2d");
        ctx.drawImage(this,0,0);

        var imageData = ctx.getImageData(0,0,canvas.width,canvas.height);
        var data = imageData.data;
        var r,g,b,avg;

        for(var x = 0, len = data.length; x < len; x+=4) {
            r = data[x];
            g = data[x+1];
            b = data[x+2];

            avg = Math.floor((r+g+b)/3);
            colorSum += avg;
        }

        var brightness = Math.floor(colorSum / (this.width*this.height));
        callback(brightness);
    }
}
            </script>
        <?php
        if (intval($site['font_color'], 16)) {
            $bodyClass = 'light ';
        } else {
            $bodyClass = 'dark ';
        }
        ?>
        <?php echo $this->element('layout_js_variables');?>
        <?php
        echo $javascript->link(array('jquery', 'jquery-migrate-1.2.1.min', 'jqueryui', 'app.js?v=3', 'perfect-scrollbar.jquery.min', 'functions_v'.JAVASCRIPT_VERSION));
        if (!IS_PC) {
            echo $javascript->link(array('mobile.js','rwd-table.min.js', 'responsive-tabs.js?v=5'));
        }
        echo $scripts_for_layout;
        ?>
        <?php if (!empty($_GET['clearcache'])): ?>
            <script type="text/javascript">
                window.localStorage.clear();
            </script>
        <?php endif; ?>
        <?php
        if (empty($site['theme_color']))
            $site['theme_color'] = '000000';
        if (empty($site['font_color']))
            $site['font_color'] = 'ffffff';

        if (!IS_PC) {

			$css_array = ['responsive_v1.css','mobile.css?v=2'];
			if($is_rtl) $css_array[] = 'responsive-ar_v1.css';
            echo $html->css($css_array, null, []);
        } else {
            echo $html->css('pc.css', null, []);
        }
        if ( !empty($additional_code)){
            echo $additional_code;
        }
		if($is_rtl){
			echo $html->css('rtl_v2.css', null, []);
		}
		if (isset($_COOKIE['menuClass'])) {
		    $bodyClass .= $_COOKIE['menuClass'];
        }
        ?>
        <!--[if lt IE 8]>
             <script src="js/html5shiv.min.js"></script>
             <script src="js/respond.min.js"></script>
           <![endif]-->

           <link rel="stylesheet" type="text/css" href="/css/app_new_layout_v<?php echo CSS_VERSION ?>.css" />
           <?php if($is_rtl){ ?>
            <link rel="stylesheet" type="text/css" href="/css/app_new_layout_ar_v<?php echo CSS_VERSION ?>.css" />
            <?php }?>
            <?php
            echo $javascript->link(['cal-js/jquery.plugin.min', 'cal-js/jquery.calendars', 'cal-js/jquery.calendars.plus', 'cal-js/jquery.calendars.picker', 'cal-js/jquery.calendars.ummalqura.js?v=1']);
            if ($is_rtl)
                echo $javascript->link(['cal-js/jquery.calendars-ar-EG_v'.JAVASCRIPT_VERSION, 'cal-js/jquery.calendars.picker-ar-EG', 'cal-js/jquery.calendars.ummalqura-ar']);
            echo $javascript->link(array('moment.min','daterangepicker_v'.JAVASCRIPT_VERSION));
            echo $html->css('daterangepicker', null, []);
            ?>
            <link rel="stylesheet" type="text/css" href="/css/new-style_v8.css" />
            <?php if($is_rtl)
        {
            echo '<link rel="stylesheet" type="text/css" href="/css/new-style-ar_v1.css" />';
            echo '<link rel="stylesheet" type="text/css" href="/css/s2020_rtl_v' . CSS_VERSION . '.css" />';

        } else {
            echo '<link rel="stylesheet" type="text/css" href="/css/s2020_ltr_v' . CSS_VERSION . '.css" />';
        }?>

        <?php if (Domain_Name_Only == 'daftra'): ?>
            <!-- Global site tag (gtag.js) - Google Ads: 847451904 -->
            <script async src="https://www.googletagmanager.com/gtag/js?id=AW-847451904"></script>
            <script>
                window.dataLayer = window.dataLayer || [];

                function gtag() {
                    dataLayer.push(arguments);
                }

                gtag('js', new Date());
                gtag('config', 'AW-847451904');
            </script>
        <?php endif ?>



        <?php
        if (/** warning suppress */ array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && $_SERVER['HTTP_X_REQUESTED_WITH'] == "com.daftra.app") {
            ?>
            <style>
                #main-nav  {<?php if(!IS_MOBILE) {?>overflow:scroll !important;<?php } ?> position: absolute !important; height: auto !important;}
                body { overflow: scroll !important; }
                .main-nav .mob-nav { height: auto !important; }
            </style>
            <?php
        }
        ?>
        <script src="<?php echo CDN_ASSETS_URL ?>s2020/js/layout/helpers.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
        <?php echo isset($google_analytics_head) ? $google_analytics_head : '' ?>
        <link rel="stylesheet" type="text/css" href="/dist/daftra/daftra.compat.styles.css?v=<?php echo CSS_VERSION ?>" />
        <link rel="stylesheet" href="/frontend/assets/css/layout/compat.css?v=<?= CSS_VERSION ?>" />
        <script src="/frontend/assets/js/build/default/compat-1.js?v=<?= JAVASCRIPT_VERSION ?>"></script>

        <?php if (strtolower($site['country_code']) == 'sa'): ?>
            <?php $fontURL = Router::url('/css/saudi_riyal_symbol-regular.ttf', true); ?>
            <style>
                @font-face {
                    font-family: 'saudi_riyal_symbol';
                    src: url('<?= $fontURL ?>') format('truetype');
                    font-weight: normal;
                    font-style: normal;
                    font-display: swap;
                }

                .sar_symbol {
                    font-family: 'saudi_riyal_symbol', sans-serif !important;
                }

                #SiteNegativeCurrencyFormats{
                    font-family: 'saudi_riyal_symbol', sans-serif !important;
                }
            </style>
        <?php endif; ?>
    </head>
    <script>
        var savedRecord = null;
    </script>
    <body  dir="<?= $is_rtl ? 'rtl' : 'ltr' ?>" class="fluid <?php echo $is_rtl ? 'rtl' : 'ltr'; ?> <?php echo $bodyClass ?>">

        <?php if ('enerpize' == Domain_Name_Only) { ?>
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MFGLNCR"
                          height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
        <?php } ?>

        <?php echo isset($google_analytics_body) ? $google_analytics_body : '' ?>
        <?php

	//echo  $this->element('maintainence_bar',array('main_date' => "2018-03-20 12:00:00","end_date" => '2018-03-20 14:00:00'));
        if($site['status'] == SITE_STATUS_SUSPENDED){
            echo $this->element("suspended_bar");
        } else if (!empty($expired) || !empty($about_expire)) {
//            echo $this->element("expired_bar", array('about_expire' => (empty($about_expire) ? 0 : 1)));
        } else if (!empty($logged_as_client)) {
            echo $this->element("logged_as");
        } else if (!empty($site['incorrect_email'])) {
            echo $this->element("incorrect_email", array('site' => $site));
        }
        ?>
        <?php if(empty(Configure::read('debug'))&&empty($is_ajax)){ ?>
		<?php if ($this->action != 'owner_first_settings') { ?>
				<?PHP if(!($client_settings['client_disable_online_access'] == '1' && isset($_SESSION['CLIENT']))) { ?>

                    <div class="menu-overlay"></div>

                            <div class="main-nav" id="main-nav">
                                <?
                                    echo get_subscription_countdown($this);
                                ?>
                                <div class="logo"> <a class="logo-img" href="<?php echo Router::url('/') ?>" title="<?php echo h($site['business_name']); ?>">
                                        <?php   if (!empty($site['site_logo'])): ?>
                                                <img  src="<?php echo $site['site_logo_full_path'] ?>?w=200&h=100" alt="<?php echo h($site['business_name']); ?>" title="<?php echo h($site['business_name']); ?>" 
                                                loading="lazy"
                                                onerror="
                                                    this.onerror=null;
                                                    this.src='<?= \Izam\Daftra\Common\Services\AvatarURLGenerator::generate(h($site['business_name']), $user['staff_id'] ?? 0, 150, null) ?>';
                                                    this.style.width='47%';
                                                    this.style.height='100%';
                                                "
                                                />
                                        <?php else : ?>
                                                <?php echo h($site['business_name']); ?>
                                            <?php endif; ?>
                                        </a>

									 <div class="clearfix"></div>
                                </div>
                                <?php if (!empty($user)){

                                    echo get_menus($this, $user['type'], getAuthClient()?false:getAuthStaff('role_id'));
                                } else { ?>
                                    <ul>
                                        <li class="left"><a class="menu-item" href="<?php echo Router::url('/') ?>" title="Home"><span>
                                                    <?php __("Home") ?>
                                                </span></a></li>
                                        <li class="left"><a class="menu-item" href="<?php echo Router::url("/login") ?>" title="<?php echo __("Login") ?>"><span>
                                                    <?php __("Login") ?>
                                                </span></a></li>
                                    </ul>
								<?php } ?>
                                <div class="clear"></div>

                            </div>

				<?php } ?>
                <?php } ?>
		<?php } ?>
        <div class="layout" id="main-content" <?php if(/** warning suppress */ isset($is_ajax) && $is_ajax) {  ?> style="margin-left: 0;margin-right: 0" <?php } ?>>
            <div class="layout-container">
                <?php
                if (!empty($this->params['prefix']) && $this->params['prefix'] == 'owner') {
                    if (!empty($renderColors)) {
                        echo $this->element('color_selector');
                    }
                }
                ?>
                <?php if(empty(Configure::read('debug'))&&empty($is_ajax)){ ?>
                <?php if ($this->action != 'owner_first_settings') { ?>
				<?PHP if(!($client_settings['client_disable_online_access'] == '1' && isset($_SESSION['CLIENT']))) { ?>
                    <div class="header clearfix">
                        <div class="container-fluid no-padder">
                            <div class="header-elements row">
                                <div class="col-md-8 col-sm-8 col-xs-7 head-pos-fix">
									<a href="#" id="shrink-sidebar">
                                    <i class="mdi mdi-backburger"></i></a>
                                    <ol class="breadcrumb up-bread header-breadcrumb" id="breadcrumb" style="display: none"></ol>

                                </div>
                                <!-- <div class="col-xs-5 col-sm-6 header-breadcrumbs-wrap visible-xs">
                                  <div id="top-header-title"></div>
                                </div> -->
                                <div class="col-md-4 col-sm-4 col-xs-5">
                                    <div class="user-log">
                                        <?php echo $this->element('header-user',array('white_label'=>!empty($site['white_label']))); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
				<?php } ?>
                <?php } ?>
                <?php } ?>
                <div class="main-area clearfix <?php if (!empty($logged_as_client) || isset($isHash)) { echo "main-area_clients"; }; ?>">
                    <div class="container">
                        <div class="flash-wrapper master-widget-container" style="padding-bottom: 0;padding-top: 0;">
                            <?php
                            //if (!empty($this->params['prefix']) && $this->action != 'owner_first_settings') {
                            //echo $this->element('crumbs');}


                            echo $session->flash();
                            echo $session->flash('secondaryMessage');
                            echo displayCustomValidationFlash($session);
                            unset($_SESSION['Message']);
                            ?>
                        </div>
                        <script>
                            $(function() {
                                if($('.side-nav').length && $('.side-nav').is(':visible')){
                                    $('.flash-wrapper').addClass('nav-wrapper');
                                }
                            });
                        </script>
                        <ol>
                            <?php if(!empty($_PageBreadCrumbs))
                            {
                                echo $this->element ('breadcrumbs' , ['breadcrumbs' =>$_PageBreadCrumbs ]);
                            }
                            
                            if (strtolower($site['country_code']) == 'sa') {
                                $content_for_layout = str_replace([' ر.س'], '<span style="display:inline-block;font-family: saudi_riyal_symbol, sans-serif !important;" class="sar_symbol"> &#x5143;</span>', $content_for_layout);

                                $content_for_layout = preg_replace_callback('/([-?\d.,]+) SR/', 'replace_sr_symbol', $content_for_layout);
                            }
                            echo $content_for_layout;
                            /* @var $session SessionHelper */
                            ?>
                        </ol>
                    </div>

                    <?php

                        if (!empty($logged_as_client) || isset($isHash) ) {
                            echo $this->element("clients_footer");
                        }
                    ?>

                </div>
                
                
                <!-- / main-area -->
                <div class="clear"></div>
            </div>
        </div>
        <script type="text/javascript">
            var prefix = '';
	        <?php if ($this->params['prefix'] == 'owner'): ?>
                prefix = 'owner';
	        <?php elseif ($this->params['prefix'] == 'client'): ?>
                prefix = 'client';
	        <?php endif; ?>
        </script>
        <?php if (isset($hasTabs)): ?>
            <script type="text/javascript">
                $(function() {
					<?php if($is_rtl){ ?>
					// if ($.datepicker) {
                    //     $.datepicker.regional[ "ar" ];
                    // }
					<?php } ?>
                    function switchTab(_this) {
                        var myHref = $(_this).attr('href');
                        $('.tabs-buttons li').removeClass('current');
                        $('.tabs-box').hide();
                        $(myHref).show();
                        window.location.hash = 'tab:' + myHref.substr(1);
                        $(_this).parents('li').addClass('current');
                        return false;
                    }
                    if (window.location.hash) {
                        var href = window.location.hash.replace('tab:', '');
                        var targetLink = $('a[href=' + href + ']', '.tabs-buttons');
                        if (targetLink.length) {
                            switchTab(targetLink[0]);
                        }
                    }
                    $('.tabs-buttons a').click(function() {
                        return switchTab(this);

                    });
                });
            </script>
        <?php endif; ?>
        <script src="<?php echo CDN_ASSETS_URL ?>s2020/js/layout/libs.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
        <script src="<?php echo CDN_ASSETS_URL ?>s2020/js/layout/helpers.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
        <script src="<?php echo CDN_ASSETS_URL ?>s2020/js/layout/app.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
        <?php echo $javascript->link(array('jquery.lightbox_me', 'jquery.tooltip', 'layout.js?v='.JAVASCRIPT_VERSION)); ?>
        <?php if (!IS_PC) { ?>
            <script type="text/javascript">
                (function($) {
                    fakewaffle.responsiveTabs(['xs', 'sm']);
                })(jQuery);

            </script>
        <?php } ?>

        <?php if($this->params['prefix'] == 'owner' &&	empty($site['white_label']) && !isset($_GET['iframe'])) {  ?>
            <button type="button" class="popup-chat hidden-xs support-btn">
                <span><?php __('Have a Question') ?></span> <img src="/css/img/q.png" alt="">
            </button>

        <!-- Modal -->

        <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-md"  role="document">
                <div class="modal-content">
                    <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
                    <div class="modal-header">
                   
                        <h4 class="modal-title" id="myModalLabel"><?php __('Send feedback or ask a question') ?></h4>
                    </div>
                    <div class="modal-body">
  
                        <iframe id="contact_iframe" width="100%" height="100%" src="" frameborder="0" allowfullscreen></iframe>
                    </div>

                </div>
            </div>
        </div>
        <?php } ?>






        <div class="modal fade" id="myModalGeneral" tabindex="-1" role="dialog" aria-labelledby="myModalGeneralLabel">
            <div id="myModalGeneralSize" class="modal-dialog modal-lg"  role="document">
                <div class="modal-content">
                    <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
                    <div id="myModalGeneralheader" class="modal-header">

                        <h4 class="modal-title" id="myModalGeneralLabel"><?php __('Tax') ?></h4>
                    </div>
                    <div class="modal-body">
                        <iframe id="myModalGeneral_iframe" width="100%" height="100%" src="" frameborder="0" allowfullscreen></iframe>
                    </div>

                </div>
            </div>
        </div>


        <script>
            setTimeout(function() {
                if ($('.isDatePicker').length) {
                    $('.isDatePicker').attr('autocomplete', 'off');
                }
            }, 500);
        </script>

<?php
//	if($is_rtl) echo $javascript->link(array('datepicker-ar'));
?>
<?php if($this->action != "owner_assign_cost_centers"){ ?>
<div class="modal fade" id="promptModalDelete-global" tabindex="-1" role="dialog" aria-labelledby="promptModalLabel-global"
        style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-transparent border-0 position-absolute w-100" style="z-index: 9;">
                <h5 class="modal-title d-none" id="promptModalLabel-global"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body px-4 text-center">
                <i class="mdi mdi-trash-can-outline text-danger d-block"></i>
                <div id="prompt-body-global"></div>
            </div>
            <form method="post" id="row-action-form-global" name="row-action-form-delete" action="">
                <input type="hidden" name="_token" id="prompt-token-global" value=""/>
                <input type="hidden" name="_method" id="prompt-method-global" value=""/>
                <div class="modal-footer d-flex justify-content-center align-items-center">
                    <button type="submit" class="btn btn-danger"><?= __('Yes') ?></button>
                    <button type="button" class="btn btn-secondary"
                            data-dismiss="modal"><?= __('No') ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<style>
    @media (min-width: 576px) {
    #promptModalDelete-global .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }
    }
    #promptModalDelete-global .close {
        font-size: 1.5rem;
    }
    #promptModalDelete-global .modal-body .mdi {
        font-size: 3.5rem;
        font-weight: 300;
        line-height: 1.2;
    
    }
    #promptModalDelete-global .modal-footer {
        padding: .75rem;
    }
    #promptModalDelete-global .modal-footer>* {
        margin: .25rem;
    }
    #promptModalDelete-global .modal-footer .btn{
        padding: .375rem .875rem;
        font-size: .875rem;
        line-height: 1.5;
        font-weight: 500;
    }
</style>
<?php } ?>
<div id="help-container">
<div class="modal-bg-container">
        <div class="modal-bg"></div>
    </div>
    <div id="modal-container" >
    </div>
</div>

        <?php echo $javascript->link(array('submit_handler_v'.JAVASCRIPT_VERSION)); ?>
        <?php echo $javascript->link(['jquery.scannerdetection.js']); ?>
        <link rel="stylesheet" media="print" type="text/css" href="/css/print_v<?php echo CSS_VERSION ?>.css?v=5"  />
        <script src="/dist/daftra/daftra.compat.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
        <script src="/frontend/assets/js/vendors/tinymce.min.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
        <script src="/frontend/assets/js/build/default/compat-2.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
        <script type="text/javascript" src="/js/reports.js?v=10"></script>
        <script type="text/javascript" src="/js/beta.js?v=1"></script>
        <script type="text/javascript" src="/js/modals.js"></script>
        <script type="text/javascript">
               $(document).on('click','.support-btn, .support-modal-btn', function() {
                IzamModal.closeModals();
                    IzamModal.addUrlModal('/v2/owner/support-channels/index', '', '', false, false)
                })
        </script>     
        <?php if (Domain_Name_Only == 'daftra'): ?>
        <script type="text/javascript" src="/js/navigation-shortcuts.js"></script>
        <script type="text/javascript" src="/js/navigation-listener.js"></script>  

        
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-9R42S45PXK"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'G-9R42S45PXK');
        </script>
        <?php endif; ?>
        <?php include(dirname(dirname(dirname(__DIR__))) . '/izam-views/entitiy/partials/custom-js.php'); ?>
    </body>
</html>


<script type="text/javascript">
    <?php if ( !empty ( $_GET['colors'] )){?>
    $(function ( ) {
        $("html").css ( 'overflow-x' , 'hidden')
    })
    <?php }?>
window.onerror = function (msg, url, lineNo, columnNo, error) {
  /*   
 $.post("<?= Router::url('/report_js.php',true) ?>", {msg:msg,error:error,lineNo:lineNo,url: url,real_url:window.location.href}, function(result){
});

  */

  return false;
}
</script>
<?php echo $javascript->link(array('iframe-resizer')); ?>
<?php if (isset($_GET['have_aquestion']) && $_GET['have_aquestion'] == 1){?>
    <script>
        $( document ).ready(function(){
            $('.popup-chat').click();
        });
    </script>
<?php } ?>

<?php if (!isset($izamNavigation)) {
    $izamNavigation = new  Izam\Navigation\Navigation();
} ?>
<script>
    var izamNavigation = <?= $izamNavigation->toJson() ?>
</script>

<?php if(isIos()){  ?>
<script>
 $(document).ready(function() { 
   $('a[href="https://docs.daftra.com/"],a[href="/owner/api_keys/dashboard"]').remove();
  });
</script>

<?php } ?>
<?php if (isset($_GET['welcome']) && $_GET['welcome'] == 1 && Domain_Name_Only == "daftra") { ?>
    <?= $this->element('pixels_script') ?>
<?php } ?>

<script>
    const onUserAddedNewInvoiceEvent = new Event("onUserAddedNewInvoice");
    if ($('#flashMessage').length && window.location.pathname.startsWith("/owner/invoices/view/")) {
        // Dispatch the event.
        document.dispatchEvent(onUserAddedNewInvoiceEvent);
    }
</script>
