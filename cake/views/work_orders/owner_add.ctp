<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/create/create.min.css?v=".CSS_VERSION, null, []); ?>
<!-- <div class="pages-head fixed-div"><div class="container"></div></div> -->
 <style>
	#choose_staff_div .btn-group {width: 100%;}
 </style>
<div class="work-order-form">
			<?php
		echo $html->css('breadcrumbs.css');
		echo $html->css('bootstrap-multiselect_v'.CSS_VERSION.'.css');
		echo $html->css('invoicing.css');
		echo $javascript->link(array('bootstrap-multiselect.js','new-invoice-v2_v'.JAVASCRIPT_VERSION.'.js','jquery.elastic','summernote/summernote.min'));
		$breadcrumbs = [
			['link' => Router::url(['controller' => 'work_orders' , 'action' => 'index'])  ,'title' => __("Work Orders", true )],
			['link' => Router::url(['controller' => 'work_orders' , 'action' => 'view' , $this->data['WorkOrder']['id'] /*warning suppress*/ ?? '']) ,'title' => $this->data['WorkOrder']['title'] /*warning suppress*/ ?? ''.' #'.$this->data['WorkOrder']['number']],
			['link' => '#' ,'title' => (empty ( $this->data['WorkOrder']['id'])?__("Add",true ): __("Edit",true))],
			];
		if  ( empty ( $this->data['WorkOrder']['id']) ) {
			unset ( $breadcrumbs[1]);
		}
		echo $this->element ('breadcrumbs' , ['breadcrumbs' =>array_values ( $breadcrumbs ) ]);?>	

		<?php echo $form->create('WorkOrder', array( 'id' => 'WorkOrderForm', 'type' => 'file')); ?>
		<div class="pages-head-s2020">
			<div class="container">
				<div class="row-flex align-items-center">
					<div class="col-flex-6">
						<button type="submit" id="is_draft_button" class="btn s2020 btn-secondary font-weight-medium mt-0">
							<span><?php __("Save as Draft") ?></span>
						</button>
					</div>
					<div class="col-flex-6 d-flex justify-content-end">
						<button type="submit" class="btn s2020 btn-icn btn-success font-weight-medium ml-2">
							<i class="mdi mdi-content-save-outline fs-20"></i>
							<span><?php __("Save") ?></span>
						</button>
					</div>
				</div>
			</div>
		</div>
                <?php echo $form->input('is_draft', array('id' => 'is_draft' ,  'type' => 'hidden' ,'value' => 0)); ?>
		<div class="row">
			<div class="col-md-12">
				<div class="input-fields inside-form-box">
					<h3 class="rounded-item  head-bar theme-color-a"><span class="contact-info">
							<?php __("Work Order") ?>
						</span></h3>
					<div class="row">
						<div class="col-md-3">
							<div class="form-group">
								<?php echo $form->input('title', array('div' => '', 'class' => 'form-control required', 'label' => __('Title', true) . ' <span class="required">*</span>'/* , 'after' => '<span class="tooltip" title="client-businessname"></span>' */)); ?>
							</div>
						</div>
						<div class="col-md-3">
							<div class="form-group">
								<?php echo $form->input('number', array('div' => '', 'class' => 'form-control required', 'label' => __('Order Number', true) . ' <span class="required">*</span>'/* , 'after' => '<span class="tooltip" title="client-businessname"></span>' */)); ?>
								<?= $form->hidden('hidden_number') ?>
							</div>
						</div>
						<div class="col-md-3">
							<div class="form-group">
								<label ><? echo __('Start Date') ?> <span class="required">*</span></label>
							<?php
                            echo $this->element('datePicker',['options' => ['name' => 'start_date', 'value' => $this->data['WorkOrder']['start_date']], 'dateFormat' => getDateFormats('std')[getCurrentSite('date_format')]]);
							?>
						</div>
						</div>
						<div class="col-md-3">
							<div class="form-group">
								<label ><? echo __('Finish Date') ?> </label>
							<?php
							echo $this->element('datePicker',['options' => ['name' => 'delivery_date', 'value' => $this->data['WorkOrder']['delivery_date']], 'dateFormat' => getDateFormats('std')[getCurrentSite('date_format')]]);
							?>
							</div>
						</div>
						<div class="col-md-12">
							<div class="form-group">
								<?php echo $form->input('description', array('type'=> 'textarea',  'div' => '', 'class' => 'form-control', 'label' => __('Description', true) )); ?>
							</div>
						</div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="InvoiceClientId"><? echo __('Client') ?> </label>
                                <?php
                                $options = array('label' => false, 'class' => 'selectpicker width-flude-fix', /*warning suppress*/ 'style'=>'', 'data-live-search' => 'true', 'type' => 'select', 'data-width' => '300', 'div' => false, 'empty' => __('(Select Client)', true), 'options' => $clients ?? [], 'tabindex' => 10);
                                ?>


                                <?php
                                if ($ajax_clients) {
                                    $options['class'].= ' with-ajax ';
                                    $options['style'].= 'width: 190px';
                                }
                                echo $form->input('client_id', $options);
                                ?>
                                <a href="<?php echo Router::url(array('controller' => 'clients', 'action' => 'add')); ?>" class="<?php echo $extra_class /*warning suppress*/ ?? '' ?> rounded-item btn btn-md btn-success btn-blue" id="AddNewClient"><?php __("New") ?></a>
                            </div>
                        </div>
						<div class="col-md-4 form-group">
							<?php echo $this->element('tag_input',array('label' => __('Tags',true),'include_js' => true,'item_id' => $this->data['WorkOrder']['id'] /*warning suppress*/ ?? '','model_name' => 'WorkOrder')) ?>
						</div>

						<div class="">

                                                    <input type="hidden" value="<?php if ( $default_invoice_method == 1 ){ echo "1";}else{echo "0";}?>" name="data[Invoice][is_offline]" id="InvoiceIsOffline" />
							</div>

						<div class="col-md-4">
							<div class="form-group">
								<label for="ExpenseAmount"><? echo __('Budget') ?></label>
								<div class="input-group">
									<?php echo $form->input('budget', array('type'=> 'number', 'div' => false,'placeholder' => '0.00', 'class' => 'form-control', 'label' =>false )); ?>

									<div class="input-group-btn">
										<?php $value = getCurrentSite('currency_code');
											if ( !empty ( $this->data['WorkOrder']['budget_currency']))
											{
												$value = $this->data['WorkOrder']['budget_currency'];
											}
											echo $form->input('budget_currency', array('value'=>$value, 'options' => $budget_currencies, 'div' =>false,'data-live-search' => 'true', 'class' => 'selectpicker', 'label' => false ,'empty' => '[' . __('Default', true) . ']',)); ?>
									</div><!-- /btn-group -->
								</div><!-- /input-group -->
							</div>
						</div>
<div class="col-md-12">
	<?php
		echo $this->element('clients/client_details_ajax', ['mymodel' => 'Invoice']);
		echo $this->element('advanced_create_client');
	?>
</div>
					</div>



					<?php if ( !empty ( $this->data['WorkOrder']['id'] ) ){?>
		<!--            <div class="row">
						<div class="form-group">
							<?php echo $form->input('status', array( 'div' => '', 'class' => 'form-control', 'label' => __('Status', true) )); ?>

						<div class="clear"></div>
					</div>
					</div>-->
					<?php }?>
					<?php if ( ifPluginActive  ( StaffPlugin ) ){?>
					<div  class="col-md-4">
						<?php
						echo $form->input('choose_staff', array('label'=>__('Assign Staff',true), 'type' => 'checkbox','checked'=>empty ($selected_staffs)?false:true , 'div' => 'clip-check check-info', 'class' => ''));
						?>

							<div id="choose_staff_div" style="margin-bottom: 15px;<?php if ( empty ($selected_staffs) ){?> display: none; <?php }?>">
							<!--<label ><?php __("Staff" )?></label>-->
							<?php debug ( $selected_staffs ) ;?>
							<select name="data[WorkOrder][staff_id][]" id="fliter-staff" multiple="multiple">
								<? foreach ($staff_ids as $id => $name) { ?>
									<option <?php if ( in_array ($id , $selected_staffs ?? []   ) ){ ?>selected='selected'<?php }?> value="<? echo $id ?>"><? echo $name ?></option>
								<? } ?>
							</select>
							<?php //echo $form->input('staff_id', array('empty' => "-",'options' => $staff_ids, 'label' => __('Staff', true), 'class' => 'INPUT  form-control selectpicker', 'div' => 'form-group')); ?>

							</div>

					</div>


					<div class="clearfix"></div>
					<?php }?>

				</div>


			</div>
		</div>





		<div class="">
			<div id="insert-here">
			<?php echo $this->element('custom_forms/custom_form'); ?>

		</div>





		</div>

		<?php
		echo $form->end();
		?>
		<?php echo $javascript->link(array('add_client.js?v=2')); ?>

		<?php
		echo $html->css(array('magicsuggest-min.css'), false, ['inline' => false]);
		echo $javascript->link(array('magicsuggest-min.js'));
		?>
		<?php $formats = getDateFormats('js'); $jsFormat = $formats[getCurrentSite('date_format')];?>

		<script src="<? echo Router::url(array('controller'=>'clients','action' => 'czones'),true) ?>/czones.js"></script>

		<link rel="stylesheet" type="text/css" href="/css/bootstrap-select_v<?php echo CSS_VERSION ?>.css" inline="" />
		<link rel="stylesheet" type="text/css" href="/css/ajax-bootstrap-select.css" inline="" />

		<script type="text/javascript" src="/js/bootstrap-select.js"></script>
		<script type="text/javascript" src="/js/ajax-bootstrap-select.min.js"></script>
        <?php echo $javascript->link(array('barcode-detection-init_v'.JAVASCRIPT_VERSION.'.js')); ?>
		<script>

                    $("#is_draft_button").click(function () {
                        $("#is_draft").val(1)
                        $("*").prop('required' , false)
                    })

			 var summary_paid = 0;
			 var jsDateFormat = '<?php echo $jsFormat ?>';
				  var aps = 1;var productsHTML = '' ; var taxHTML = '';
				  var addClientLimit = <?php echo json_encode($addClientLimit) ?>;
			</script>
		<?php
			$html->css(array('ajax-bootstrap-select'), false, ['inline' => false]);
			?>
			<script type="text/javascript" src="/js/ajax-bootstrap-select.min.js?v=3"></script>

			<script type="text/javascript">


				var invoice_selector = "#WorkOrderClientId"
				$("#WorkOrderChooseStaff").change ( function ( e ) {
				$("#choose_staff_div").toggle ( ) ;
				console.log ( $("#WorkOrderChooseStaff").val() ) ;
				$("#fliter-staff").prop('disabled', !$("#WorkOrderChooseStaff:checked").length);
			})
			var allStaffTxt = "<?php __('Please Select') ?>";
			$('#fliter-staff').multiselect({
			 enableFiltering: true,
			 enableCaseInsensitiveFiltering: true,
			 allSelectedText: allStaffTxt,
			 nonSelectedText: allStaffTxt,
			 onChange: function(option, checked, select) {
				//reload_filter();
			}
			});
				//Variables for compatibility for new-invoice-v2.js so we can add NewClient. - set to default values .







			<?php if (!IS_PC) { ?>
					$(function() {
						setTimeout(function() {
							$('#extra-settings').addClass('collapse');
						}, 1000);
					});
			<?php } ?>
				var options = {
					ajax: {
						url: '<?php echo Router::url(array('controller' => 'clients', 'action' => 'json_find')); ?>',
						type: 'get',
						dataType: 'json',
						data: {
							q: '{{{q}}}',
							suspend: 0
						}
					},
					locale: {
						emptyTitle: '<?php __('Please Select') ?>',
						searchPlaceholder: '<?php __('enter name, phones or ID') ?>',
					},
					preprocessData: function(data) {
						var i, l = data.length, array = [];
						if (l) {
							for (i = 0; i < l; i++) {
								array.push($.extend(true, data[i], {
									text: data[i].name,
									value: data[i].id,
									data: {
										subtext: data[i].details
									}
								}));
							}
						}
						return array;
					}
				};

				$(function() {


					//$('#extra-settings').click(function(){fakewaffle.checkResize();});
					$('.selectpicker').selectpicker().filter('.with-ajax').ajaxSelectPicker(options);
					$("#CancelClient").click () ;
				})
				 $(function () {
					 $("#WorkOrderClientId").change ( function ( ) {
						 if ( $("#WorkOrderClientId").val() != "") {
							 $.ajax({dataType: 'json' ,url: '<?php echo Router::url(['controller' => 'work_orders' , 'action' => 'get_mapped_fields' ])?>/'+$("#WorkOrderClientId").val() ,
							 success: function ( data ) {
								 for ( i = 0 ; i < data.length ; i++ ){
									 if ( data[i]['value'] != "" && data[i]['value'] != null )
									 {
                                                                             data_selector = "[data-id='CustomModelField"+data[i]['id']+"']";
										 $(data_selector).val(data[i]['value'])
                                                                                 $("[name='data[CustomModel][field_"+data[i]['id']+"]']").each (  function (){
                                                                                     if ( $(this).val() == data[i]['value'])
                                                                                     {
                                                                                         $(this).attr('checked','checked');
                                                                                     }
                                                                                 })
										if ( typeof all_autocompletes[data[i]['id']] !== 'undefined')
										{
											all_autocompletes[data[i]['id']].clear () ;
											all_autocompletes[data[i]['id']].setValue([ data[i]['value'] ])
										}
                                                                                $("[name='field_"+data[i]['id']+"']").selectpicker('refresh')
									 }


								 }
							 }})
		//                     alert ( 'test' ) ;
						 }
					 });
	 
					 if(!window.loadedAllCompletes.includes(false)) {
						$("#WorkOrderClientId").trigger('change');
					 }


					 $('body').on('complete_field_loaded', function(e, data) {
						if(!window.loadedAllCompletes.includes(false)) {
							$("#WorkOrderClientId").trigger('change');
						}
					 });

					 $("#AddNewClient").click ( function ( ) {
						 setTimeout(function () {
//							 $("#InvoiceIsOffline").val("1")
							 $("#InvoiceClientType3").click()
//							 $("#ClientIsOffline").val ("1" )
						 },150)

					 })


				})
			</script>
</div>
