<?php echo $html->css(CDN_ASSETS_URL . "s2020/css/show/show.min.css?v=" . CSS_VERSION, null, []); ?>
<?php
echo $javascript->link(array('invoices/view-invoice', 'magicsuggest-min'));
echo $html->css(array('magicsuggest-min.css', 'timeline_v' . CSS_VERSION . '.css', 'time-tracker.css?v=2', 'fontello.css', 'tabs'));
echo $html->css('view_new_style.css');
if ($is_rtl) {
    echo $html->css('view_new_style_ar.css');
}
echo $html->css('bootstrap-multiselect.css');
echo $javascript->link(array('bootstrap-multiselect.js'));
$isFirst = 0;
$manufacturingModalForm = new \Laminas\Form\Form();
$manufacturingModalForm->add([
    'name' => 'data[Requisition][ids]',
    'type' => Izam\View\Form\Element\Select::class,
    'attributes' => [
        "placeholder" => __t("Search"),
        'multiple' => 'multiple',
    ],
    'options' => [
        'label' => __t("Requisitions"),
        'ajax' => false,
        "filter_size"=> 'col-12',
        'allow_beside'=> false,
        'value_options' => $manualOutboundRequisitions,
        "property" => "text",
        "identifier" => "id",
        'auto_suggest_url' => '/v2/owner/manufacturing-orders/get-in-progress-orders?term=d&_type=query&q=__q__',
        "is_single"=>false,
        'size'=>'x-large'
    ]
]);
$filterHelper = new  \Izam\View\Form\Helper\FormCollection();
?>


<?php if(!empty($_SESSION['StockRequest']['deleteSuccess'])): ?>
    <div class="flash-wrapper master-widget-container" style="padding-bottom: 0;padding-top: 0;">
        <div class="Sucmessage">
            <?php
                echo $_SESSION['StockRequest']['deleteSuccess'];
                unset($_SESSION['StockRequest']['deleteSuccess']);
            ?>
        </div>
    </div>
<?php endif; ?>

<script>
    var url = "<?php echo $url ?>";

    function reloadTab(i, url) {
        $('#tab-iframe-' + i).attr('src', url)
    }

    function reloadWorkOrderJournals() {
            document.getElementById('journalsIframe').src = '/owner/journals/index?box=1&entity_type=work_order&entity_id=<?= $work_order['WorkOrder']['id'] ?>';
    }

    function reloadStockRequests() {
        document.getElementById('stockRequestIframe').src = '/v2/owner/entity/stock_request/list?iframe=1&show_row_actions=1&filter[work_flow_id]=<?= $work_order['WorkOrder']['id'] ?>';
    }

    var $detailsIframe = $('#detailsIframe');
    if ($detailsIframe.length) {
        $detailsIframe.get(0).addEventListener('load', function () {
            $detailsIframe.contents().find('body').append('<style>body{background: #fff;}</style>');
        });
    }

    $(document).ready(function() {
        function reloadWorkOrderDetails() {
            document.getElementById('detailsIframe').src = url + '?iframe=1&tab=details&level=3';
        }

        function reloadWorkflowActivityLog() {
            document.getElementById('activityLogIframe').src = url + '?iframe=1&tab=activity_log';
        }

        if (typeof reloadWorkOrderDetails == 'function') {
            reloadWorkOrderDetails();
        }

        if (typeof reloadWorkflowActivityLog == 'function') {
            reloadWorkflowActivityLog();
        }

        if (typeof reloadWorkOrderJournals == 'function') {
            <?php if ($journalCount) : ?>
                reloadWorkOrderJournals();
            <?php endif;?>
        }
    });
</script>
<?php echo $this->element('work_orders/head', []); ?>
<!--<div class="pages-head">
    <h1 class="left no-margin">
        <?php echo __('Work Order', true) . ' <span class="title_id_with_hash">' . $work_order['WorkOrder']['title'] . '(#' . $work_order['WorkOrder']['number'] . ')</span>'; ?>
        <?php if (!empty($work_order['WorkOrder']['client_id'])  && check_permission(Clients_View_his_own_Clients)) { ?>
            <span class="invoice-client sub-heading sub-heading2"><?php printf(__('Recipient: %s', true), $html->link($work_order['Client']['business_name'], array('controller' => 'clients', 'action' => 'view', $work_order['WorkOrder']['client_id']), array('target' => '_blank'))); ?></span>
        <?php } ?>
    </h1>


</div>-->
<!--start hold_dropdown_in_tab to more tab--->
<?php if (IS_PC) { ?>
    <li style=" display: none;" class="dropdown new-li-in-tab">
        <a class="dropdown-toggle" data-toggle="dropdown" href="#"><?php echo  __('More') ?><span class="caret"></span></a>
        <ul class="dropdown-menu new-dp-tabs">
        </ul>
    </li>
    <?php echo $javascript->link(array('hold_dropdown_in_tab.js', 'jquery.ocupload.js')); ?>
    <style>
        .tab_into_drop {
            display: none !important;
        }
    </style>
<?php } ?>
<!--end hold_dropdown_in_tab to more tab--->

<?php if ($view_reports) { ?>
    <!---start work order statics----->
    <div class="row">
        <?php if (!empty($revenue_data)) { ?>
            <div class="col-md-<?php echo (12 / $report_count) ?>">
                <div class="wo-box m-b-20">
                    <div class="media">
                        <div id="pie_invoices"></div>
                        <?php
                        //                            $revenue_data[__("Paid",true)] = 100;
                        $i_chart_params = array(
                            'title' => __('Invoices', true),
                            'chartType' => 'pie',
                            'xLabels' => array_keys($revenue_data),
                            'colors' => array('#27c24c', '#f05050'),
                            'values' => array(array_values($revenue_data))
                        );
                        debug($i_chart_params);
                        echo $this->element('reports/charts', array('div' => 'pie_invoices', 'not_first' => $isFirst, 'jsonParams' => $i_chart_params));
                        $isFirst++;
                        ?>

                    </div>
                </div>
            </div>
        <?php } ?>
        <?php if (!empty($expense_data)) { ?>
            <div class="col-md-<?php echo (12 / $report_count) ?>">
                <div class="wo-box m-b-20">
                    <div class="media">
                        <div id="pie_expenses"></div>
                        <?php
                        $e_chart_params = array(
                            'title' => __('Expenses', true),
                            'chartType' => 'pie',
                            'xLabels' => array_keys($expense_data),
                            'values' => array(array_values($expense_data))
                        );

                        echo $this->element('reports/charts', array('div' => 'pie_expenses', 'not_first' => $isFirst, 'jsonParams' => $e_chart_params));
                        $isFirst++;
                        ?>

                    </div>
                </div>
            </div>
        <?php } ?>
        <?php if (!empty($budget_data)) { ?>
            <div class="col-md-<?php echo (12 / $report_count) ?>">
                <div class="wo-box m-b-20">
                    <div class="media">
                        <div id="pie_budget"></div>
                        <?php
                        $b_chart_params = array(
                            'title' => __('Budget', true),
                            'chartType' => 'pie',
                            'xLabels' => array_keys($budget_data),
                            'colors' => array('#f05050', '#27c24c'),
                            'values' => array(array_values($budget_data))
                        );

                        echo $this->element('reports/charts', array('div' => 'pie_budget', 'not_first' => $isFirst++, 'jsonParams' => $b_chart_params));
                        $isFirst++;
                        ?>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
    <style>
        .wo-box {
            background: #fff;
            border: 1px solid #dee5e7;
            padding: 15px;
            border-bottom-width: 2px;
        }
    </style>
    <!---end work order statics----->
<?php } ?>

<div class="invoice-actions btn-group dropdown-btn-group">
    <?php if ($showEditButton) { ?>
        <a href="<?php echo "/v2/owner/entity/$type/$id/edit" ?>" class="btn btn-default btn-sm btn-5 ">
            <i class="fa fa-pencil"></i> <?php __("Edit") ?>
        </a>
    <?php } ?>
    <?php
    $print_url = '';
    if ($default_template)
        $print_url = $print_link;
    else
        $print_url = Router::url(array('action' => 'view', $work_order['WorkOrder']['id'], 'print:1'));
    ?>
    <a href="<?= $print_url; ?>" class="btn btn-default btn-sm btn-5 " id="print_button"> <i class="fa fa-print"></i> <?php __("Print") ?></a>
    
    <?php echo $this->element('share-with-social-media');?>

    <?php
    $pdf_url = '';
    if ($default_template)
        $pdf_url = $pdf_link;
    else
        $pdf_url = Router::url(array('action' => 'view', $work_order['WorkOrder']['id'] . '.pdf'));
    ?>
    <a href="<?= $pdf_url ?>" class="btn btn-default btn-sm btn-5 " id="print_button"> <i class="fa fa-file-pdf-o"></i> <?php __("PDF") ?></a>
    <?php if (!empty($work_order['Client']['email'])) { ?>
        <?php
        $email_url = '';
        if ($default_template)
            $email_url = $email_link;
        else
            $email_url = Router::url(array('action' => 'send_to_client', $work_order['WorkOrder']['id']));
        ?>
        <a href="<?= $email_url; ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-file-message-o"></i> <?php __("Email") ?></a>
    <?php } ?>
    <?php if (ifPluginActive(FollowupPlugin)) { ?>
        <a href="<?php echo '/v2/owner/entity/post/create/' . Post::WORKFLOW_TYPE . '/' . $work_order['WorkOrder']['id'] ?>" class=" btn btn-default btn-sm btn-5 "> <i class="fa fa-book"></i>
            <?php __("Add Note / Attachment") ?></a>
    <? } ?>
    <?php if($showAssignTransaction){ ?>
        <a class="btn btn-default btn-sm quick-action-btn" href="#" data-toggle="modal" data-target="#assignTransactionModal">
        <span class="fa fa-file-text" aria-hidden="true"></span> <?php __("Assign Transaction") ?></a>
        <?php if($hasAnyTransaction){ ?> 
        <a class="btn btn-default btn-sm quick-action-btn" href="#" data-toggle="modal" data-target="#unassign-assignTransactionModal">
        <span class="fa fa-file-text" aria-hidden="true"></span> <?php __("Un-assign Transactions") ?></a>
        <?php } ?>
    <?php } ?> 
    <?php if (ifPluginActive(FollowupPlugin)) { ?>
        <a href="<?php echo Router::url(array('controller' => 'appointments', 'action' => 'add', $work_order['WorkOrder']['id'], Post::WORKFLOW_TYPE)); ?>" class=" btn btn-default btn-sm btn-5 "> <i class="fa fa-calendar-o"></i> <?php __("Schedule Appointment") ?></a>
    <? } ?>
    <a href="<?php echo '/v2/owner/entity/' . $type . '/' . $id . '/clone/' . $type ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-copy"></i> <?php __("Clone") ?></a>
    <?php if ($manualOutboundRequisitions) { ?>
        <a class="btn btn-default btn-sm quick-action-btn" href="#" data-toggle="modal" data-target="#convertRequisitionsModal">
            <span class="fa fa-copy" aria-hidden="true"></span> <?php __("Convert outbound requisitions to invoice") ?></a>
    <?php } ?>
    <?php if ($showDeleteButton) { ?>
        <a class="btn btn-default btn-sm quick-action-btn" href="<?php echo Router::url(['action' => 'delete_workflow', $type, $work_order['WorkOrder']['id']]); ?>">
            <span class="fa fa-trash-o" aria-hidden="true"></span>
            <?php __("Delete") ?></a>
    <?php  } ?>

    <?php if (count((array)$printableTemplates) == 1 && $default_template) : ?>

    <?php elseif (count((array)$printableTemplates) > 0) : ?>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Voucher', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <?php foreach ($printableTemplates as $template) : ?>
                    <li>
                        <a href="<?php echo Router::url(array('controller' => 'printable_templates', 'action' => 'view', $work_order['WorkOrder']['id'], $template['PrintableTemplate']['id'], 'work_order')) ?>" class="">
                            <?= $template['PrintableTemplate']['name']; ?></a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if (count($workflow_view_templates['global']) || count($workflow_view_templates['local'])) : ?>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Printables', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <?php
                echo draw_templates_list($workflow_view_templates, $work_order['WorkOrder']['id']);
                ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if (count($workflow_email_templates['global']) || count($workflow_email_templates['local'])) : ?>
        <?php
        // Determine the first available template
        $templateType = count($workflow_email_templates['local']) ? 'local' : 'global';
        $templateId = $workflow_email_templates[$templateType][0]['id'];
        $workOrderId = $work_order['WorkOrder']['id'];
        ?>

        <a href="<?= show_email_template_view_page($templateId, $workOrderId) ?>?template=<?= $templateType ?>" class="btn btn-sm btn-default">
            <i class="mdi mdi-email mr-1"></i>
            <?= __('Send Email', true) ?>
        </a>
    <?php
        $key = 'workflow'.'-'.\Izam\Template\Utils\TemplateTypeUtil::EMAIL.'-';
        set_bread_crumb_printable_template_session($key, $workOrderId, $_PageBreadCrumbs ?? []);
        endif;
    ?>
</div>
<?php
echo $javascript->link(array('owner-view_v' . JAVASCRIPT_VERSION . '.js'));
?>
<div role="tabpanel" class="tabs-box box">

    <ul class="nav nav-tabs responsive">
        <li class="active" role="presentation">
            <a aria-controls="WorkOrderDetails" role="tab" data-toggle="tab" onclick="reloadWorkOrderDetails()" href="#WorkOrderDetails" title="<?php __('Work Order') ?>" id="details">
                <span class="one-line"><?php __("Details") ?></span>
            </a>
        </li>

        <? foreach ($tab_actions as $i => $tabAction) : ?>
            <li role="presentation">
                <a aria-controls="tab-<?= $i ?>" role="tab" data-toggle="tab" onclick="reloadTab('<?= $i ?>','<?= $tabAction->getUrl() ?>')" href="#tab-<?= $i ?>" title="<?= $tabAction->getLabel() ?>">
                    <span class="one-line"><?= $tabAction->getLabel() ?></span>
                </a>
            </li>
        <? endforeach; ?>

        <?
        if (ifPluginActive(BNR) && $bnr_invoice) {
        ?>
            <li class="active" role="presentation">
                <a aria-controls="BnrBlock" role="tab" data-toggle="tab" href="#BnrBlock" title="<?php __('Pnr') ?>" id="ViewInvoice">
                    <span class="one-line"><?php __("Pnr") ?>
                    </span>
                </a>
            </li>
        <?php
        }
        ?>
            
        <?php
            if(
                ifPluginActive(InventoryPlugin) 
                && settings::getValue(InventoryPlugin, \Settings::ENABLE_STOCK_REQUESTS)
                && $workflow_stock_request_count> 0
                && check_permission(Izam\Daftra\Common\Utils\PermissionUtil::VIEW_STOCK_REQUESTS)
            ){
        ?>

                <li class="" role="presentation">
                    <a aria-controls="StockRequests" class="ajax-tab" role="tab" data-toggle="tab" onclick="reloadStockRequests()" href="#StockRequests" title="<?php __('Stock Requests') ?>" id="ViewStockRequests">
                        <span class="one-line"><?php echo __("Stock Requests", true) . " (" . $workflow_stock_request_count . ")" ?></span>
                    </a>
                </li>
            
        <?php 
            }
        ?>

        <li role="presentation">
            <a aria-controls="WorkOrderBlock" role="tab" data-toggle="tab" href="#WorkOrderBlock" title="<?php __('Workflow') ?>">
                <span class="one-line"><?php echo $workflowTypeName ?></span>
            </a>
        </li>
        <?php if ($invoice_count > 0) { ?>
            <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" onclick="Reloadany('#InvoiceBlockDiv','<?php echo Router::url(['controller' => 'invoices', 'action' => 'index', '?' => 'work_order_id=' . $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#InvoiceBlock" title="<?php __('Invoices') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Invoices", true) . " (" . $invoice_count . ")" ?></span></a></li>
        <?php } ?>
        <?php if ($estimate_count > 0) { ?>
            <li class="" role="presentation"><a class="ajax-tab" aria-controls="EstimateBlock" onclick="Reloadany('#EstimateBlockDiv','<?php echo Router::url(['controller' => 'invoices', 'action' => 'estimates', '?' => 'work_order_id=' . $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#EstimateBlock" title="<?php __('Estimates') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Estimates", true) . " (" . $estimate_count . ")" ?></span></a></li>
        <?php } ?>
        <?php if ($sales_order_count > 0) { ?>
            <li role="presentation">
                <a aria-controls="RelatedSalesOrderBlock" role="tab" data-toggle="tab" onclick="reloadRelatedSalesOrders()" href="#RelatedSalesOrderBlock" title="<?php __('Sales Orders') ?>">
                    <span class="one-line"><?php echo __("Sales Orders", true) . " (" . $sales_order_count . ")" ?></span>
                </a>
            </li>
        <?php } ?>
        <?php //Removed on purpose
        if (false && $creditnote_count > 0) { ?>
            <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" onclick="Reloadany('#CNBlockDiv','<?php echo Router::url(['controller' => 'invoices', 'action' => 'creditnotes', '?' => 'work_order_id=' . $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#CNBlock" title="<?php __('Credit Notes') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Credit Notes", true) . " (" . $creditnote_count . ")" ?></span></a></li>
        <?php } ?>
        <?php //Removed on purpose
        if (false && $refund_count > 0) { ?>
            <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" onclick="Reloadany('#RRBlockDiv','<?php echo Router::url(['controller' => 'invoices', 'action' => 'refund', '?' => 'work_order_id=' . $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#RRBlock" title="<?php __("Refund Receipts") ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Refund Receipts", true) . " (" . $refund_count . ")" ?></span></a></li>
        <?php } ?>
        <?php if (ifPluginActive(ExpensesPlugin)) { ?>
            <?php if ($expense_count) { ?>
                <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" onclick="Reloadany('#ExpenseBlockDiv','<?php echo Router::url(['controller' => 'expenses', 'action' => 'index', '?' => 'work_order_id=' . $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#ExpenseBlock" title="<?php __('Expenses') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Expenses", true) . " (" . $expense_count . ")"; ?></span></a></li>
            <?php } ?>
            <?php if ($income_count) { ?>
                <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" onclick="Reloadany('#IncomeBlockDiv','<?php echo Router::url(['controller' => 'incomes', 'action' => 'index', '?' => 'work_order_id=' . $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#IncomeBlock" title="<?php __('Incomes') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Incomes", true) . " (" . $income_count . ")" ?></span></a></li>
            <?php } ?>
        <?php } ?>
        <?php if ($appointments_count > 0) { ?>
            <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" onclick="Reloadany('#AppointmentsBlockDiv','<?php echo Router::url(['controller' => 'appointments', 'action' => 'index', Post::WORKFLOW_TYPE, '?' => 'compact=0&item_id=' . $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#AppointmentsBlock" title="<?php __('Appointments') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Appointments", true) . " (" . $appointments_count . ")" ?></span></a>
            </li>
        <?php } ?>
        <?php if ($posts_count) { ?>
            <li role="presentation"><a aria-controls="actions-tab" role="tab" data-toggle="tab" onclick="reload_posts()" href="#NotesBlock" title="<?php __('Notes / Attachments') ?>"><span class="one-line"><?php __('Notes / Attachments') ?> <small class="counter"> (<? echo $posts_count ?>)</small> </span></a></li>
        <?php } ?>
        <!--<li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" reload_url="<?php echo Router::url(['controller' => 'invoice_payments', 'action' => 'index', '?' => 'work_order_id=' . $work_order['WorkOrder']['id']]); ?>" role="tab" data-toggle="tab" href="#PaymentBlock" title="<?php __('Payments') ?>" id="ViewInvoice"><span class="one-line"><?php __("Payments") ?></span></a></li>-->
        <?php if ($purchaseorder_count) { ?>
            <li role="presentation">
                <a aria-controls="POBlock" role="tab" data-toggle="tab" onclick="reloadPurchaseInvoices()" href="#POBlock" title="<?php __('Purchase Invoices') ?>">
                    <span class="one-line"><?php echo __("Purchase Invoices",true)." (".$purchaseorder_count.")" ?></span>
                </a>
            </li>
        <?php } ?>
        <?php 
            if (
                $requisitions_count
                && check_permission(Izam\Daftra\Common\Utils\PermissionUtil::REQUISITION_VIEW)
            ) {
        ?>
            <li class="" role="presentation"><a class="ajax-tab" aria-controls="" onclick="Reloadany('#RequisitionBlockDiv','<?php echo Router::url(['controller' => 'requisitions', 'action' => 'index', '?' => 'work_order_id=' . $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#RequisitionBlock" title="<?php __('Requisitions') ?>" id=""><span class="one-line"><?php echo __("Requisitions", true) . " (" . $requisitions_count . ")" ?></span></a></li>
        <?php } ?>
        <?php if (ifPluginActive(TimeTrackingPlugin) && $timetracking_count > 0) { ?>
            <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" onclick="Reloadany('#TTBlockDiv','<?php echo Router::url(['controller' => 'time_tracking', 'action' => 'index', '?' => 'list&work_order_id=' . $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#TTBlock" title="<?php __('Time Tracking') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Time Tracking", true) . " (" . $timetracking_count . ")" ?></span></a></li>
        <?php } ?>
        <?php if (!empty($financial_results)) { ?>
            <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" onclick="Reloadany('#FinancialListDiv','<?php echo Router::url(['action' => 'financial_list', $work_order['WorkOrder']['id']]); ?>')" role="tab" data-toggle="tab" href="#FinancialList" id="ViewInvoice"><span class="one-line"><?php __("Financial Transactions") ?></span></a></li>
        <?php } ?>
        <?php if ( $transaction_count > 0 && (check_permission(Invoices_View_All_Invoices) || check_permission(Invoices_View_Invoices_Details))) {?>
            <li role="presentation"><a onclick="reload_transaction()"  href="#TransactionList"  aria-controls="TransactionList" role="tab" data-toggle="tab" title=""><span class="one-line"><?php __("Transaction List ") ?><small class="counter">(<?php echo $transaction_count; ?>)</small></span></a></li>
        <?php }?>


        <li role="presentation">
            <a aria-controls="WorkOrderDetails" role="tab" data-toggle="tab" onclick="reloadWorkflowActivityLog()" href="#WorkOrderActivityLog" title="<?php __('Activity Log') ?>" <span class="one-line">
                <?php __("Activity Log") ?>
                </span>
            </a>
        </li>

        <?php if ($journalCount) : ?>
            <li role="presentation">
                <a aria-controls="WorkOrderJournals" role="tab" data-toggle="tab" onclick="reloadWorkOrderJournals()" href="#WorkOrderJournals" title="<?php __('Work order journals') ?>">
                    <span class="one-line"><?php echo  sprintf(__("Journals (%s)", true), $journalCount); ?></span>
                </a>
            </li>
        <? endif; ?>
    </ul>
    <?php
    $iframe_url = '';
    if ($default_template)
        $iframe_url = $iframe_link;
    else
        $iframe_url = Router::url(array('action' => 'preview', $work_order['WorkOrder']['id']));
    ?>
    <div class="tab-content responsive">
        <?php

        if (ifPluginActive(BNR) && $bnr_invoice) {
        ?>
            <div class="tab-pane active" id="BnrBlock">
                <h2>
                    <? echo sprintf(__('Pnr #%s', true), $bnr_invoice['Invoice']['no']) ?> <a href="<? echo Router::url(array('controller' => 'resellers', 'action' => 'edit', $bnr_invoice['Invoice']['id'])) ?>"><small>
                            <? echo __('Edit', true) ?></small></a></h2>
                <div class="row">
                    <div class="col-md-4 col-sm-6">
                        <div class="panel panel-default">
                            <div class="panel-heading"><?php __("Total purchase") ?> </div>
                            <div class="panel-body p-0">
                                <div class="row no-margin">
                                    <div class="col-xs-12  p-0 b-r">
                                        <div class="m"> <span><strong><?php echo format_price($total_po, $po_currency_code) ?></strong></span>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <div class="panel panel-default">
                            <div class="panel-heading"><?php __("Total Sales") ?> </div>
                            <div class="panel-body p-0">
                                <div class="row no-margin">
                                    <div class="col-xs-12  p-0 b-r">
                                        <div class="m"> <span><strong><?php echo format_price($client_invoice['Invoice']['summary_total'], $client_invoice['Invoice']['currency_code']) ?></strong></span>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <div class="panel panel-default">
                            <div class="panel-heading"><?php __("Profit") ?> </div>
                            <div class="panel-body p-0">
                                <div class="row no-margin">
                                    <div class="col-xs-12  p-0 b-r">
                                        <div class="m"> <span><strong><?php echo format_price($total_profit, $client_invoice['Invoice']['currency_code']) ?></strong></span>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="p-0">
                            <div class="panel panel-default no-margin">
                                <div class="panel-heading">
                                    <? echo __('Products List', true) ?>
                                </div>
                                <div class="invoice-items">
                                    <div class=" table-responsive">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table b-light mobile-invoicing">
                                            <tr class="TableHeader table-header active unmovable">
                                                <th id="label_item" class="col-1 name-cell"><?php __("Items") ?></th>
                                                <th id="label_unit_price" class="count-cell "><span><?php __("Sell Price") ?></span></th>
                                                <th id="label_unit_price" class="count-cell "><span><?php __("Buy Price") ?></span></th>
                                                <th id="label_quantity" class="count-cell "><span><?php __("Quantity") ?></span></th>
                                                <th id="label_Supplier" class="count-cell "><span><?php __("Supplier") ?></span></th>
                                                <th id="label_subtotal" class="count-cell"><span><?php __("Sell Price") ?></span></th>
                                                <th id="label_subtotal" class="count-cell"><span><?php __("Buy Price") ?></span></th>
                                                <th id="label_subtotal" class="count-cell"><span><?php __("Profit") ?></span></th>
                                            </tr>
                                            <?php $total = 0;
                                            foreach ($bnr_invoice['InvoiceItem'] as $ri) { ?>
                                                <tr class="itemRow fix-clear movable">
                                                    <td class="">
                                                        <p class="p-0"><strong><?php echo $ri['Product']['name'] ?></strong> <small>#<?php echo $ri['Product']['id'] ?></small></p>
                                                        <small><i class="fa fa-barcode"></i> <?php echo $ri['Product']['barcode'] ?></small>
                                                    </td>
                                                    <td class=""><?php echo format_price_simple($ri['unit_price']) ?></td>
                                                    <td class=""><?php echo format_price_simple($ri['col_3']) ?></td>
                                                    <td class=""><?php echo format_number($ri['quantity']) ?></td>
                                                    <td class=""><?php echo ($ri['Supplier']['business_name']) ?></td>
                                                    <td><?php $total += $ri['quantity'] * $ri['unit_price'];
                                                        echo format_price_simple($ri['quantity'] * $ri['unit_price']); ?></td>
                                                    <td><?php $total += $ri['quantity'] * $ri['col_3'];
                                                        echo format_price_simple($ri['quantity'] * $ri['col_3']); ?></td>
                                                    <td><?php $total += $ri['quantity'] * ($ri['subtotal'] - $ri['col_3']);
                                                        echo format_price_simple($ri['quantity'] * ($ri['subtotal'] - $ri['col_3'])); ?></td>
                                                </tr>
                                            <?php } ?>

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        <?php
        }
        ?>
        <div class="tab-pane active" id="WorkOrderDetails">
            <iframe id="detailsIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>

        <div class="tab-pane" id="StockRequests">
            <iframe id="stockRequestIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>

        <div class="tab-pane" id="WorkOrderJournals">
            <iframe id="journalsIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>

        <?php foreach ($tab_actions as $i => $tab_action) : ?>
            <div class="tab-pane" id="tab-<?= $i ?>">
                <iframe id="tab-iframe-<?= $i ?>" src="about:blank" class="tab-iframe">
                </iframe>
            </div>
        <?php endforeach; ?>
        <style>
            .tab-iframe {
                width: 100%;
                border: 0;
            }
        </style>
        <script>
            $('.tab-iframe').load(function(e) {
                if ($(e.currentTarget).contents().find('body')[0]) {
                    var iframeContentsHeight = $(e.currentTarget).contents().find('body')[0].scrollHeight;
                    $(e.currentTarget).height(iframeContentsHeight + 10 + 'px');
                }
            });
        </script>
        <div class="tab-pane" id="WorkOrderActivityLog">
            <iframe id="activityLogIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
            <script>
                var $activityLogIframe = $('#activityLogIframe');
                if ($activityLogIframe.length) {
                    $activityLogIframe.get(0).addEventListener('load', function () {
                        $activityLogIframe.contents().find('body').append('<style>body{background: #fff;}.activity-log-loaded{margin: 0;}</style>');
                    });
                }
            </script>
        </div>
        <div class="tab-pane" id="WorkOrderBlock">
            <div class="preview-invoice invoice-Block">
                <? if (IS_PC) {
                    $invoice_print_method = settings::getValue(InvoicesPlugin, 'invoice_print_method');

                ?>
                    <div class="invoice-template">
                        <iframe frameBorder="0" src="<?= $iframe_url; ?><?php if ($invoice_print_method == settings::OPTION_PRINT_PDF) { ?>.pdf?inline=true <?php } ?>" id="InvoicePreview" name="InvoicePreview" width="100%" height="800"></iframe>
                    </div>
                <? } else { ?>
                    <div class="responsive-invoice">
                        <img style="max-width:100%" src="<?php echo $iframe_url ?>.jpeg" />
                    </div>
                <? } ?>
            </div>
            <?php
            $fields_empty = true;
            foreach ($field_values as $k) {
                if (!empty($k)) {
                    $fields_empty = false;
                    break;
                }
            }
            if (!$fields_empty && !empty($field_values)) { ?>
                <br>
                <div class="input-fields ">
                    <h3 class="head-bar theme-color-a"><span class="details-info">
                            <?php echo $custom_form_name ?>
                        </span></h3>
                    <div class="row">
                        <?php echo $this->element('custom_forms/view_fields_form'); ?>
                    </div>
                </div>
            <?php } ?>
        </div>
        <div class="tab-pane " id="InvoiceBlock">
            <div id="InvoiceBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="EstimateBlock">
            <div id="EstimateBlockDiv"> </div>
        </div>

        <div class="tab-pane" id="RelatedSalesOrderBlock">
            <iframe id="RelatedSalesOrderIframe" src="About:blank"
                    style="width: 100%; min-height: 500px;border: 0;"></iframe>
        </div>
        <div class="tab-pane " id="CNBlock">
            <div id="CNBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="RRBlock">
            <div id="RRBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="ExpenseBlock">
            <div id="ExpenseBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="IncomeBlock">
            <div id="IncomeBlockDiv"> </div>
        </div>
        <?php if (array_sum($appointment_status_counts) > 0) { ?>
            <div class="tab-pane" id="AppointmentsBlock">
                <div id="AppointmentsBlockDiv">
                    <div class="panel panel-default">
                        <div class="">
                            <div class="panel panel-default no-margin" style="border-bottom: none;">
                                <div class="panel-heading" style="border-bottom: none;">

                                    <div class="btn-group  pull-right pos3">
                                        <?php foreach ($appointment_statuses as $k => $s) { ?>
                                            <a class=" btn btn-default reload_status <?php if ($k == 0) { ?>active<?php } ?>" title="<? echo $s ?>" status="<?php echo $k; ?>"><?php echo $s . " ($appointment_status_counts[$k])"; ?></a>
                                        <?php } ?>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12" id="AppointmentsBlock_rel">
                                    <div class="notifcation-loader">
                                        <div class="inner-loader"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php } ?>
        <div class="tab-pane " id="NotesBlock">
            <?php echo $this->element('clients/client_note', ['item_type' => Post::WORKFLOW_TYPE, 'item_id' => $work_order['WorkOrder']['id']]); ?>
        </div>
        <div class="tab-pane " id="PaymentBlock">
            <div id="PaymentBlockDiv"> </div>
        </div>
        <div class="tab-pane" id="POBlock">
            <iframe id="POBlockDiv" src="About:blank" style="width: 100%; min-height: 500px;border: 0;">
            </iframe>
        </div>
        <div class="tab-pane " id="TTBlock">
            <div id="TTBlockDiv"> </div>
        </div>

        <?php if (!empty($financial_results)) { ?>
            <div class="tab-pane " id="FinancialList">
                <div id="FinancialListDiv"> </div>
            </div>
        <?php } ?>
        <?php if($transaction_count > 0){ ?>
            <div class="tab-pane" role="tabpanel" id="TransactionList">
                <div id="ReportTable">
                    <?php echo $this->element('clients/client_transaction', array('id' => $work_order['WorkOrder']['id'])); ?>
                </div>
            </div>
        <?php } ?>
        <div class="tab-pane " id="RequisitionBlock">
            <div id="RequisitionBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="TimelineBlock">
            <div id="TimelineBlockDiv"> </div>
        </div>
    </div>

</div>
<script>
    var current_status = 0;
    $(function() {
        <?php if (!empty($this->params['named']['print'])) { ?>
            print_inv();
        <?php } ?>
        $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
            if (history.pushState) {
                history.pushState(null, null, '#' + $(e.target).attr('href').substr(1));
            } else {
                location.hash = '#' + $(e.target).attr('href').substr(1);
            }
        });
        if (location.hash !== '') {
            $('a[href="' + location.hash + '"]').tab('show');
            $('a[href="' + location.hash + '"]').click()
        }
    });
    $("#print_button").click(function(e) {
        e.preventDefault();
        print_inv()
    });
    $(".reload_status").click(function() {
        $(".reload_status").removeClass('active')
        $(this).addClass('active')
        window.current_status = $(this).attr('status');
        $(".ajax-tab[href='#AppointmentsBlock']").click();

    });

    var print_log_url = "<? echo Router::url(array('action' => 'print_log', $this->params['pass'][0])) ?>";
</script>

<!-- Global Delete Modal -->
<div class="modal fade l-delete-modal" id="modalDelete" data-bs-backdrop="static" tabindex="-1" aria-labelledby="modalDeleteLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-dialog-centered modal-md" style="max-width: 500px">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <i class="mdi mdi-trash-can-outline trash-icon u-text-color-dangerligth"></i>
                <div data-md-form-message="true"></div>
            </div>
            <form method="post" action="" data-md-form="true">
                <button type="submit" class="ui-btn u-bg-color-dangerligth u-text-color-white"> <?= __t('Yes') ?></button>
                <button type="button" class="ui-btn u-bg-color-secondary" data-bs-dismiss="modal"><?= __t('No') ?></button>
            </form>
        </div>
    </div>
</div>
<script>
    $('#modalDelete [data-bs-dismiss="modal"]').on('click', () => {
        $('#modalDelete').modal('hide');
    })
</script>
<!-- /Global Delete Modal -->
<?php
echo $javascript->link(array('work_order_v' . JAVASCRIPT_VERSION));
?>
    <!-- App Delete Modal -->
    <div class="modal fade l-delete-modal" data-app-delete-modal="true" id="appDeleteModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" style="display: none;">
        <div class="modal-backdrop fade in" style="height: 100vh;"></div>    
        <div class="modal-dialog modal-dialog-centered modal-md" style="max-width: 500px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <i class="mdi mdi-trash-can-outline trash-icon u-text-color-dangerligth"></i>
                    <div data-app-delete-modal-message="true"></div>
                </div>
                <form method="post" action="" data-app-delete-modal-url="">
                    <input type="hidden" name="_token" value="" data-app-delete-modal-token="true" />
                    <input type="hidden" name="_method" value="DELETE" data-app-delete-modal-method="DELETE" />
                    <button type="submit" class="ui-btn u-bg-color-dangerligth u-text-color-white"> <?= __t('Yes') ?></button>
                    <button type="button" class="ui-btn u-bg-color-secondary" data-bs-dismiss="modal"><?= __t('No') ?></button>
                </form>
            </div>
        </div>
    </div>
        
<?php if ($manualOutboundRequisitions) { ?>
    <div class="modal fade" id="convertRequisitionsModal" tabindex="-1" role="dialog" aria-labelledby="convertRequisitionsModal">
        <div class="modal-dialog"  role="document">
            <div class="modal-content">
                <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
                <?= $form->create('Requisition',['url' => Router::url(['action' => 'convert_requisitions', $work_order['WorkOrder']['id']])]); ?>
                <div class="modal-header">
                    <h3><?php __('Select the requisitions you want to convert') ?></h3>
                </div>
                <div class="modal-body">
                <div class="row compat">
                    <?php echo  $filterHelper->render($manufacturingModalForm) ?>
                </div>
                </div>
                <div class="modal-footer flex-row d-flex">
                    <button class="btn btn-secondary font-weight-medium col-flex-4" data-dismiss="modal" aria-label="Close"><?php __('CANCEL');?></button>
                    <?php 
                        echo $form->submit(__('CONVERT',true), array('class' => "btn btn-success btn-lg btn-block padding h-auto ml-2 mr-0", 'div' => false));
                        echo $form->end()
                    ?>
                </div>

            </div>
        </div>
    </div>
    <style>
    .modal .modal-dialog {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        min-height: calc(100% - (10px* 2));
        padding-top: 0px !important;
    }
    .modal .modal-content {
        width: 100%;
    }

    #convertRequisitionsModal .btn-group {
        width: 100%;
    }
    #convertRequisitionsModal .modal-footer .btn {
        font-size: 14px;
        padding: 13px 24px;
    }
    @media(min-width: 768px) {
        #convertRequisitionsModal .modal-dialog {
            max-width: 600px;
            min-height: calc(100% - (30px* 2));
        }
    }
    </style>
    <script>
        $(function() {
            $(".requisitions-selectpicker").multiselect({
                enableFiltering: true,
                enableCaseInsensitiveFiltering: true
            });
        });
        $(document).on('click', '#top-nav-next,#top-nav-prev', function(event) {
            event.preventDefault();
            var current_url = $(this).attr('href');
            if (current_url == '') {
                return;
            }
            $("#RequisitionBlockDiv").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');
            $.get(current_url, function(content) {
                $("#RequisitionBlockDiv").html(content);
            });
        });
    </script>
<?php } ?>
<?php
    echo $this->element('work_orders/assign_transaction_modal',["is_workflow" => "1"]); 
    echo $this->element('work_orders/un_assign_transaction_modal',["is_workflow" => "1"]);
 ?>


<script src="/dist/app/js/pages/workflow/show/show.js"></script>
<style>
    @media (min-width: 768px) {
        #WorkOrderDetails {
            margin: -20px;
        }
    }
</style>
<script !src="">
    function reloadRelatedSalesOrders() {
        document.getElementById('RelatedSalesOrderIframe').src = '/v2/owner/entity/sales_order/list?iframe=1&filter[work_order_id]=<?= $work_order['WorkOrder']['id'] ?>';
    }

    function reloadPurchaseInvoices() {
        document.getElementById('POBlockDiv').src = '/v2/owner/entity/purchase_order/list?iframe=1&hide_page_header=1&hide_actions=1&filter[work_order_id]=<?= $work_order['WorkOrder']['id'] ?>';
    }
</script>

<script>
       var appDeleteModal = document.getElementById('appDeleteModal');

        appDeleteModal.addEventListener('shown.bs.modal', function() {
            appDeleteModal.classList.add("in");
        })
</script>