<?php
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
echo $html->css(CDN_ASSETS_URL ."s2020/css/show/show.min.css?v=".CSS_VERSION, null, []);
echo $javascript->link(array('invoices/view-invoice','magicsuggest-min'));
echo $html->css(array( 'magicsuggest-min.css', 'timeline_v'.CSS_VERSION.'.css', 'time-tracker.css?v=2', 'fontello.css','tabs' ));
echo $html->css('view_new_style.css');
if($is_rtl){echo $html->css('view_new_style_ar.css'); }
echo $html->css('bootstrap-multiselect.css');
echo $javascript->link(array('bootstrap-multiselect.js'));
$cc = 0;
$manufacturingModalForm = new \Laminas\Form\Form();
$manufacturingModalForm->add([
    'name' => 'data[Requisition][ids]',
    'type' => Izam\View\Form\Element\Select::class,
    'attributes' => [
        "placeholder" => __t("Search"),
        'multiple' => 'multiple',
    ],
    'options' => [
        'label' => __t("Requisitions"),
        'ajax' => false,
        "filter_size"=> 'col-12',
        'allow_beside'=> false,
        'value_options' => $manualOutboundRequisitions,
        "property" => "text",
        "identifier" => "id",
        'auto_suggest_url' => '/v2/owner/manufacturing-orders/get-in-progress-orders?term=d&_type=query&q=__q__',
        "is_single"=>false,
        'size'=>'x-large'
    ]
]);
$filterHelper = new  \Izam\View\Form\Helper\FormCollection();

?>
<script>

    function reloadWorkOrderJournals() {
        
            if(!document.getElementById('journalsIframe')){
                return false;
            }

            document.getElementById('journalsIframe').src = '/owner/journals/index?box=1&entity_type=work_order&entity_id=<?= $work_order['WorkOrder']['id'] ?>';
    }
    function reloadStockRequests() {
        document.getElementById('stockRequestIframe').src = '/v2/owner/entity/stock_request/list?iframe=1&show_row_actions=1&hide_actions=1&filter[work_order_id]=<?= $work_order['WorkOrder']['id'] ?>';
    }

    $(document).ready(function() {
        function reloadWorkOrderDetails() {
            document.getElementById('detailsIframe').src = '/v2/owner/work_orders/view/<?= $work_order['WorkOrder']['id'] ?>?iframe=1';
        }


        if (typeof reloadWorkOrderDetails == 'function') {
            reloadWorkOrderDetails();
        }

            
        if (typeof reloadWorkOrderJournals == 'function') {
            reloadWorkOrderJournals();
        }
        
    });

    function onRequisitionLoad(){
        Reloadany('#RequisitionBlockDiv','<?php echo Router::url(['controller' => 'requisitions' , 'action' => 'index' , '?' => 'work_order_id='.$work_order['WorkOrder']['id']]) ;?>')
        $(document).on('click', '#top-nav-next,#top-nav-prev', function(event) {
            event.preventDefault();
            var current_url=$(this).attr('href');
            if(current_url==''){
                return;
            }
            $("#RequisitionBlockDiv").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');
            $.get(current_url,function (content) {
                $("#RequisitionBlockDiv").html(content);
            });
        });
    }
</script>
<div class="pages-head fixed-div">

    <div class="container">

        



        <div class="top-actions w-100">
            <?php if (!IS_MOBILE&&!empty($work_order['WorkOrder']['client_id']) &&(check_permission(Clients_View_All_Clients) || check_permission(Clients_View_his_own_Clients))) { ?>
            <div class="sub-headings">
                    <div class="invoice-client sub-heading sub-heading2">
                        <span class="tags-elems mr-2">
                        <?php echo __('Client', true).': '. $html->link($work_order['Client']['business_name'].' #'.$work_order['Client']['client_number'], array('controller' => 'clients', 'action' => 'view', $work_order['WorkOrder']['client_id']), array('target' => '_blank')); ?>
                        </span>
                        <?php 	echo $this->element('tags_view',array('item_id' => $work_order['WorkOrder']['id'],'model_name' => 'WorkOrder','get_ajax' => true)); ?>
                    </div>
            </div>
            <?php } ?>
            <div class="mb-opt-btn"></div>
            <div class="pull-right">
                <?php // warning suppress 
                    if(!isset($FollowUpStatus[$work_order['WorkOrder']['follow_up_status_id']]['status']) || $FollowUpStatus[$work_order['WorkOrder']['follow_up_status_id']]['status'] != FollowUpStatus::STATUS_CLOSED){?>
                <div class="btn-group pull-right">
                    <a href="#" class="btn btn-lg add-new-btn btn-addon btn-success" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true"><span><i class="fa fa-plus"></i><?php __("Add") ;?></span> </a>

                    <ul class="dropdown-menu">
                        <?php if (userHavePermissionToAddInvoice()) {?>
                            <?php if (ifPluginActive(BNR)) { ?>
                                <li><a href="<?php echo Router::url(array('controller' => 'resellers', 'action' => 'add','?' =>  'work_order_id='.$work_order['WorkOrder']['id'])); ?>"> <?php __("New Pnr") ?></a></li>
                            <?php } ?>
                            <li><a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add','?' =>  'work_order_id='.$work_order['WorkOrder']['id'])); ?>"> <?php __("New Invoice") ?></a></li>
                            <li><a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add_creditnote','?' =>  'work_order_id='.$work_order['WorkOrder']['id'] )); ?>">
                                    <?php __("Create Credit Note") ?></a></li>
                            <!--<li><a   href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add_refund','?' =>  'work_order_id='.$work_order['WorkOrder']['id'] )); ?>" >  <?php __("Add Refund Receipt") ?></a></li>-->
                        <?php } ?>

                        <?php if (userHavePermissionToAddEstimate()) {?>
                        <li><a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add_estimate','?' =>  'work_order_id='.$work_order['WorkOrder']['id'])); ?>"> <?php __("New Estimate") ?></a>
                        </li>
                        <?php } ?>
                        <?php if (settings::getValue(PluginUtil::SalesPlugin, SettingsUtil::ENABLE_SALES_ORDER) && check_permission([SALES_ORDER_ADD_NEW_TO_ALL_CLIENTS, SALES_ORDER_ADD_NEW_TO_HIS_OWN_CLIENTS])) {?>
                        <li>
                            <a href="<?php echo Router::url(array('controller' => 'invoices', 'action' => 'add_sales_order','?' =>  'work_order_id='.$work_order['WorkOrder']['id'])); ?>" target="_blank"> <?php __("New Sales Order") ?></a>
                        </li>
                        <?php } ?>
                        <?php if (userHavePermissionToAddInvoice()  && !empty($work_order['WorkOrder']['client_id'])) { ?>
                            <li><a href="<?php echo Router::url(array('controller' => 'clients','action' => 'add_payment_credit',$work_order['WorkOrder']['client_id'],$work_order['WorkOrder']['id'])); ?>" class=""> <?php __("Add Payment Credit") ?> </a>
                            </li>
                        <?php } ?>
                        <?php if (ifPluginActive(ExpensesPlugin)) { ?>
                        <?php if (check_permission(Add_New_Expenses)) {?><li><a
                                href="<?php echo Router::url(array('controller' => 'expenses', 'action' => 'add','?' =>  'work_order_id='.$work_order['WorkOrder']['id'] )); ?>"> <?php __("Add Expense") ?></a></li>
                        <?php } ?>
                        <?php if (check_permission(Add_New_Incomes)) {?><li><a href="<?php echo Router::url(array('controller' => 'incomes', 'action' => 'add','?' =>  'work_order_id='.$work_order['WorkOrder']['id'] )); ?>">
                                <?php __("Add Income") ?></a></li>
                        <?php } ?>
                        <? } ?>
                        <?php if (check_permission(Add_New_Purchase_Orders)) {?>
                        <li><a href="<?php echo Router::url(array('controller' => 'purchase_invoices', 'action' => 'add','?' =>  'work_order_id='.$work_order['WorkOrder']['id'])); ?>"> <?php __("New Purchase Invoice") ?></a>
                        </li>
                        <?php } ?>



                        <li><a href="<?php echo Router::url(array('controller' => 'requisitions', 'action' => 'add',Requisition::ORDER_TYPE_MANUAL_INBOUND,'?' =>  'work_order_id='.$work_order['WorkOrder']['id'] )); ?>">
                                <?php __("Inbound Requisition") ?></a></li>
                        <li><a href="<?php echo Router::url(array('controller' => 'requisitions', 'action' => 'add',Requisition::ORDER_TYPE_MANUAL_OUTBOUND,'?' =>  'work_order_id='.$work_order['WorkOrder']['id'] )); ?>">
                                <?php __("Outbound Requisition") ?></a></li>

                        <?php 
                        if($showAssignTransaction) {
                        ?>
                          <li><a data-target="#assignTransactionModal" data-toggle="modal" href="#"> <?php __("Add Existing Transaction") ?></a>
                        <?php
                          }    
                        ?>        

                        <?php if ( ifPluginActive(TimeTrackingPlugin ) && check_permission(Enter_Timesheet)){?>
                            <li><a href="<?php echo Router::url(array('controller' => 'time_tracking', 'action' => 'index','?' =>  'list&add_work_order_id='.$work_order['WorkOrder']['id'] )); ?>"> <?php __("Add Time") ?></a>
                            </li>
                        <?php } ?>

                        <?php
                         if ( ifPluginActive(AccountingPlugin) && (check_permission(MANAGE_ALL_JOURNALS) || check_permission(MANAGE_OWN_JOURNALS)) ){?>
                            <li><a href="<?php echo Router::url(array('controller' => 'journals', 'action' => 'add','?' =>'entity_type=work_order&entity_id='.$work_order['WorkOrder']['id'] )); ?>"> <?php __("Add Entry") ?></a>
                            </li>
                        <?php } ?>

                        <?php
                        if (
                            ifPluginActive(InventoryPlugin)
                            && settings::getValue(InventoryPlugin, \Settings::ENABLE_STOCK_REQUESTS)
                            && check_permission(Izam\Daftra\Common\Utils\PermissionUtil::ADD_STOCK_REQUEST)
                        ){
                            ?>
                            <li><a href="/v2/owner/entity/stock_request/create?work_order_id=<?= $work_order['WorkOrder']['id'] ?>"> <?php __("Stock Request") ?></a>
                            </li>
                            <?php
                        }
                        ?>
                    </ul>

                </div>
                <?php } ?>
                <?php
                if  ( !empty ($FollowUpStatus))
                    {


                        $drop = "";
                        $buttons = [];
                        // warning suppress
                        $k = $k ?? null;
                            foreach ( $grouped_statuses[__('Open',true)] as  $key => $status_row) {
                                $drop_style[$key] = $status_row['style'];
                                if ( $key != $work_order['WorkOrder']['follow_up_status_id'] ){
                                    $buttons[$k][$key] = '<a href="' . Router::url(array('action' => 'change_status', $work_order['WorkOrder']['id'], $key)) . '" class="" tabindex="-1"><span class="badge shadow-light rounded-3px" ' . $status_row['style'] . '>' . $status_row['name'] . '</span></a>';
                                }
                                $button_no_link[$key] = '<a href="#" ' . $status_row['style'] . ' class="btn btn-lg not dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"> ' . $status_row['name'] . '</a>';
                                $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $work_order['WorkOrder']['id'], $key)) . '" class="" tabindex="-1"><span class="badge shadow-light rounded-3px" ' . $status_row['style'] . '>' . $status_row['name'] . '</span></a>';
                            }

                            foreach ( $grouped_statuses[__('Closed',true)] as  $key => $status_row) {
                                $drop_style[$key] = $status_row['style'];
                                if ( $key != $work_order['WorkOrder']['follow_up_status_id'] ){
                                    $buttons[$k][$key] = '<a href="' . Router::url(array('action' => 'change_status', $work_order['WorkOrder']['id'], $key)) . '" class="" tabindex="-1"><span class="badge shadow-light rounded-3px" ' . $status_row['style'] . '>' . $status_row['name'] . '</span></a>';
                                }
                                $button_no_link[$key] = '<a href="#" ' . $status_row['style'] . ' class="btn btn-lg not dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"> ' . $status_row['name'] . '</a>';
                                $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $work_order['WorkOrder']['id'], $key)) . '" class="" tabindex="-1"><span class="badge shadow-light rounded-3px" ' . $status_row['style'] . '>' . $status_row['name'] . '</span></a>';
                            }
                            //                $drop_style[$key] = $status_row['style'];
                            //                $open_button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $work_order['WorkOrder']['id'], $key)) . '" class="" tabindex="-1"><span class="badge shadow-light rounded-3px" ' . $status_row['style'] . '>' . $status_row['name'] . '</span></a>';
                            //                $button_no_link[$key] = '<a href="#" class="btn   dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"><span class="badge shadow-light rounded-3px" ' . $status_row['style'] . '>' . $status_row['name'] . '</span></a>';

                            //            foreach ($FollowUpStatus as $key => $status_row) {
                            //                $drop_style[$key] = $status_row['style'];
                            //                $button[$key] = '<a href="' . Router::url(array('action' => 'change_status', $work_order['WorkOrder']['id'], $key)) . '" class="" tabindex="-1"><span class="badge shadow-light rounded-3px" ' . $status_row['style'] . '>' . $status_row['name'] . '</span></a>';
                            //                $button_no_link[$key] = '<a href="#" class="btn   dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1"><span class="badge shadow-light rounded-3px" ' . $status_row['style'] . '>' . $status_row['name'] . '</span></a>';
                            //            }
                        ?>

                <div class="btn-group status-list pull-right float-right-mob button-toggle">
                    <?
                        foreach ($button as $key => $button_code) {
                            if ($key == $work_order['WorkOrder']['follow_up_status_id']) {
                                $drop = $drop_style[$key];
                                echo $button_no_link[$key];
                            }
                        }
                        ?>

                    <button type="button" <? echo $drop ?> class="btn btn-lg not btn-grey dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <?
                        if (empty($work_order['WorkOrder']['follow_up_status_id'])) {
                            echo __('Select Status', true);
                        }
                        ?> <span class="caret"></span>
                    </button>



                    <ul class="dropdown-menu">
                        <?
                        foreach ( $buttons as $k => $button ){
                            if ( empty ( $button ) ){continue ; }?>
                        <li class="dropdown-header"><?php echo $k ?></li>
                        <?php
                        foreach ($button as $key => $button_code) {
                            if ($key != $work_order['WorkOrder']['follow_up_status_id']) { ?>
                        <li><?php echo str_replace('<i class="fa fa-check"></i>', '', $button_code) ?></li>
                        <? }}?>
                        <li style="height: 3px !important;" class="divider"></li>
                        <?php }?>
                        <li> <a href="<?php echo Router::url(['controller' => "follow_up_statuses" , 'action' => 'index',Post::WORK_ORDER_TYPE])?>" class="" tabindex="-1"><i class="fa fa fa-cog"></i>
                                <?php __('Edit Statuses List') ?></a></li>
                    </ul>



                </div>
                <?php } ?>
                <?php if(check_permission(View_His_Own_Reports)&&!IS_MOBILE) {?>
                <a class="btn m-r-xs btn-lg btn-default pull-right" href="/owner/reports/report/work_order_profit_details?keywords=&work_order_id=<?php echo $work_order['WorkOrder']['id'] ?>&from_date=&to_date=&group_by=Work+Order&summary=&field_27=&show_report=1" tabindex="1" title="<?php __('Work Orders Profit') ?>"> <i class="fa fa-pie-chart"></i> </a>
                <?php } ?>
                <!--            <div class="btn-group status-list">
                    <button type="button"  style="border-color : <?php echo $colored_statuses[$work_order['WorkOrder']['status']] ?>;background-color: <?php echo $colored_statuses[$work_order['WorkOrder']['status']] ?>;color: white; " class="btn  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <?
                        if (empty($work_order['WorkOrder']['status'])) {
                            echo __('Select Status', true);
                        }else {
                            echo $statuses[$work_order['WorkOrder']['status']];
                        }
                        ?> <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu">
                        <?
                        foreach ($colored_statuses as $key => $button_code) {
                            if ($key != $work_order['WorkOrder']['status']) { ?>
                                <li><a href="<?php echo Router::url(array('action' => 'change_wo_status', $work_order['WorkOrder']['id'], $key))?>" style="border-color: <?php echo $colored_statuses[$work_order['WorkOrder']['status']] ?>;background-color: <?php echo $button_code ?>;color: white; " class="" tabindex="-1"><?php echo $statuses[$key]?></a></li>
                                <? } } ?>

                        </ul>
                    </div>-->
                <?php
                    $permissions_or = [Add_New_Expenses , Add_New_Incomes ,Add_New_Purchase_Orders,Invoices_Add_New_Invoices, INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS, Enter_Timesheet ];
                    if ( $work_order['WorkOrder']['status'] == WorkOrder::STATUS_OPEN && check_permission($permissions_or) ) {
                    ?>

                <?php }?>
                <div class="clear"></div>
            </div>
            <div class="clear"></div>
        </div>
        <div class="clear"></div>



    </div>
</div>
<!--<div class="pages-head">
    <h1 class="left no-margin">
        <?php echo __('Work Order', true).' <span class="title_id_with_hash">'. $work_order['WorkOrder']['title'].'(#' . $work_order['WorkOrder']['number'].')</span>'; ?>
        <?php if (!empty ( $work_order['WorkOrder']['client_id'] )  && check_permission(Clients_View_his_own_Clients)){ ?>
            <span class="invoice-client sub-heading sub-heading2"><?php printf(__('Recipient: %s', true), $html->link($work_order['Client']['business_name'], array('controller' => 'clients', 'action' => 'view', $work_order['WorkOrder']['client_id']), array('target' => '_blank'))); ?></span>
        <?php } ?>
    </h1>


</div>-->
<!--start hold_dropdown_in_tab to more tab--->
<?php if(IS_PC){ ?>
<li style=" display: none;" class="dropdown new-li-in-tab">
    <a class="dropdown-toggle" data-toggle="dropdown" href="#"><?php echo  __('More')?><span class="caret"></span></a>
    <ul class="dropdown-menu new-dp-tabs">
    </ul>
</li>
<?php echo $javascript->link(array('hold_dropdown_in_tab.js','jquery.ocupload.js')); ?>
<style>
.tab_into_drop {
    display: none !important;
}
</style>
<?php } ?>
<!--end hold_dropdown_in_tab to more tab--->

<?php if ($view_reports){?>
<!---start work order statics----->
<div class="row">
    <?php if ( !empty ( $revenue_data ) ){?>
    <div class="col-md-<?php echo (12/$report_count) ?>">
        <div class="wo-box m-b-20">
            <div class="media">
                <div id="pie_invoices"></div>
                <?php
//                            $revenue_data[__("Paid",true)] = 100;
                            $i_chart_params = array(
                                'title' => __('Invoices', true),
                                'chartType' => 'pie',
                                'xLabels' => array_keys($revenue_data),
                                'colors' => array( '#27c24c','#f05050'),
                                'values' => array(array_values($revenue_data)));
                            debug ( $i_chart_params ) ;
                            echo $this->element('reports/charts', array('div' => 'pie_invoices', 'not_first' => $cc, 'jsonParams' => $i_chart_params));
                            $cc++;
                            ?>

            </div>
        </div>
    </div>
    <?php } ?>
    <?php if ( !empty ( $expense_data ) ){?>
    <div class="col-md-<?php echo (12/$report_count) ?>">
        <div class="wo-box m-b-20">
            <div class="media">
                <div id="pie_expenses"></div>
                <?php
                            $e_chart_params = array(
                                'title' => __('Expenses', true),
                                'chartType' => 'pie',
                                'xLabels' => array_keys($expense_data),
                                'values' => array(array_values($expense_data)));

                            echo $this->element('reports/charts', array('div' => 'pie_expenses', 'not_first' => $cc > 0  ? 1 : $cc++, 'jsonParams' => $e_chart_params));
                            ?>

            </div>
        </div>
    </div>
    <?php } ?>
    <?php if ( !empty ( $budget_data ) ){?>
    <div class="col-md-<?php echo (12/$report_count) ?>">
        <div class="wo-box m-b-20">
            <div class="media">
                <div id="pie_budget"></div>
                <?php
                            $b_chart_params = array(
                                'title' => __('Budget', true),
                                'chartType' => 'pie',
                                'xLabels' => array_keys($budget_data),
                                'colors' => array( '#f05050','#27c24c'),
                                'values' => array(array_values($budget_data)));

                            echo $this->element('reports/charts', array('div' => 'pie_budget', 'not_first' => $cc > 0 ? 1 : $cc++, 'jsonParams' => $b_chart_params));
                            ?>
            </div>
        </div>
    </div>
    <?php } ?>
</div>
<style>
.wo-box {
    background: #fff;
    border: 1px solid #dee5e7;
    padding: 15px;
    border-bottom-width: 2px;
}
</style>
<!---end work order statics----->
<?php }?>

<div class="invoice-actions btn-group dropdown-btn-group">
    <?php if ( $work_order['WorkOrder']['status'] != WorkOrder::STATUS_CLOSED ) {?>
    <a href="<?php echo Router::url(array('action' => 'edit',$work_order['WorkOrder']['id'])) ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-pencil"></i> <?php __("Edit") ?></a>
    <?php }?>
    <?php
        $print_url = '';
        // warning suppress
        $default_template = $default_template ?? null;
        if( $default_template )
            $print_url = $print_link;
        else
            $print_url = Router::url(array('action' => 'view',$work_order['WorkOrder']['id'] , 'print:1'));
    ?>
    <a href="<?= $print_url; ?>" class="btn btn-default btn-sm btn-5 " id="print_button"> <i class="fa fa-print"></i> <?php __("Print") ?></a>

    <?php
        $pdf_url = '';
        if( $default_template )
            $pdf_url = $pdf_link;
        else
            $pdf_url = Router::url(array('action' => 'view',$work_order['WorkOrder']['id'] .'.pdf'));
    ?>
    <a href="<?= $pdf_url ?>" class="btn btn-default btn-sm btn-5 " id="print_button"> <i class="fa fa-file-pdf-o"></i> <?php __("PDF") ?></a>
    <?php if ( !empty($work_order['Client']['email'])){?>
    <?php
        $email_url = '';
        if( $default_template )
            $email_url = $email_link;
        else
            $email_url = Router::url(array('action' => 'send_to_client',$work_order['WorkOrder']['id'] ));
        ?>
    <a href="<?= $email_url; ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-file-message-o"></i> <?php __("Email") ?></a>
    <?php }?>
    <?php if (ifPluginActive(FollowupPlugin)) { ?>
    <a href="<?php echo Router::url(array('controller' => 'posts', 'action' => 'post', Post::WORK_ORDER_TYPE, $work_order['WorkOrder']['id'])); ?>" class=" btn btn-default btn-sm btn-5 "> <i class="fa fa-book"></i>
        <?php __("Add Note / Attachment") ?></a>
    <a href="<?php echo Router::url(array('controller' => 'appointments', 'action' => 'add', $work_order['WorkOrder']['id'] , Post::WORK_ORDER_TYPE)); ?>" class=" btn btn-default btn-sm btn-5 "> <i
            class="fa fa-calendar-o"></i> <?php __("Schedule Appointment") ?></a>

    <? } ?>
    <?php echo $this->element('share-with-social-media');?>
    <?php if($showAssignTransaction){ ?>
        <a class="btn btn-default btn-sm quick-action-btn" href="#" data-toggle="modal" data-target="#assignTransactionModal">
        <span class="fa fa-file-text" aria-hidden="true"></span> <?php __("Assign Transaction") ?></a>

        <?php if($hasAnyTransaction){ ?> 
            <a class="btn btn-default btn-sm quick-action-btn" href="#" data-toggle="modal" data-target="#unassign-assignTransactionModal">
            <span class="fa fa-file-text" aria-hidden="true"></span> <?php __("Un-assign Transactions") ?></a>
        <?php } ?>
        
    <?php } ?>
    <a href="<?php echo Router::url(array('action' => 'add','?' => ['clone_wo_id' =>$work_order['WorkOrder']['id'] ]   )) ?>" class="btn btn-default btn-sm btn-5 "> <i class="fa fa-copy"></i> <?php __("Clone") ?></a>
    <?php if ($manualOutboundRequisitions) { ?>
    <a class="btn btn-default btn-sm quick-action-btn" href="#" data-toggle="modal" data-target="#convertRequisitionsModal">
        <span class="fa fa-copy" aria-hidden="true"></span> <?php __("Convert outbound requisitions to invoice") ?></a>
    <?php } ?>
    <?php  if ($showDeleteButton){?>
    <a class="btn btn-default btn-sm quick-action-btn" href="<?php echo Router::url(array('action' => 'delete', $work_order['WorkOrder']['id'])); ?>">
        <span class="fa fa-trash-o" aria-hidden="true"></span>
        <?php __("Delete") ?></a>
    <?php  }?>

    <?php if( count($printableTemplates ?? []) == 1 && $default_template ): ?>

    <?php elseif($has_templates): ?>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Voucher', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <?php foreach($printableTemplates as $template) : ?>
                <li>
                    <a href="<?php echo Router::url(array('controller'=>'printable_templates', 'action' => 'view', $work_order['WorkOrder']['id'], $template['PrintableTemplate']['id'], 'work_order' )) ?>" class="">
                        <?= $template['PrintableTemplate']['name']; ?></a>
                </li>
                <?php endforeach; ?>
                <?php
                    echo draw_templates_list($view_templates, $work_order['WorkOrder']['id']);
                ?>
            </ul>
        </div>
    <?php endif; ?>

</div>
<?php
echo $javascript->link(array('owner-view_v' . JAVASCRIPT_VERSION . '.js'));
?>
<div role="tabpanel" class="tabs-box box">

    <ul class="nav nav-tabs responsive">
        <li class="active" role="presentation">
            <a aria-controls="WorkOrderDetails" role="tab" data-toggle="tab" onclick="reloadWorkOrderDetails()" href="#WorkOrderDetails" title="<?php __('Work Order') ?>" id="ViewInvoice">
                <span class="one-line"><?php __("Details") ?></span>
            </a>
        </li>
        <?
		if(ifPluginActive(BNR) && $bnr_invoice){
		?>
        <li class="active" role="presentation"><a aria-controls="BnrBlock" role="tab" data-toggle="tab" href="#BnrBlock" title="<?php __('Pnr') ?>" id="ViewInvoice"><span class="one-line"><?php __("Pnr") ?></span></a>
        </li>
        <?php
		}
		?>
        <li role="presentation"><a aria-controls="WorkOrderBlock" role="tab" data-toggle="tab" href="#WorkOrderBlock" title="<?php __('Work Order') ?>"
                id="ViewInvoice"><span class="one-line"><?php __("Work Order") ?></span></a></li>
        <?php if ( $invoice_count> 0 ) {?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock"
                onclick="Reloadany('#InvoiceBlockDiv','<?php echo Router::url(['controller' => 'invoices' , 'action' => 'index' , '?' => 'work_order_id='.$work_order['WorkOrder']['id']]) ;?>')" role="tab" data-toggle="tab" href="#InvoiceBlock"
                title="<?php __('Invoices') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Invoices",true)." (".$invoice_count.")" ?></span></a></li>
        <?php } ?>
        <?php if ( $estimate_count> 0 ) {?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls="EstimateBlock"
                 onclick="Reloadany('#EstimateBlockDiv','<?php echo Router::url(['controller' => 'invoices' , 'action' => 'estimates' , '?' => 'work_order_id='.$work_order['WorkOrder']['id'] . '&from_workorder=1']) ;?>')" role="tab" data-toggle="tab" href="#EstimateBlock"
                title="<?php __('Estimates') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Estimates",true)." (".$estimate_count.")" ?></span></a></li>
        <?php } ?>
        <?php if ($sales_order_count > 0) { ?>
            <li role="presentation">
                <a aria-controls="RelatedSalesOrderBlock" role="tab" data-toggle="tab" onclick="reloadRelatedSalesOrders()" href="#RelatedSalesOrderBlock" title="<?php __('Sales Orders') ?>">
                    <span class="one-line"><?php echo __("Sales Orders", true) . " (" . $sales_order_count . ")" ?></span>
                </a>
            </li>
        <?php } ?>
        <?php //Removed on purpose
        if ( false && $creditnote_count> 0 ) {?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock"
                 onclick="Reloadany('#CNBlockDiv','<?php echo Router::url(['controller' => 'invoices' , 'action' => 'creditnotes' , '?' => 'work_order_id='.$work_order['WorkOrder']['id']]) ;?>')" role="tab" data-toggle="tab" href="#CNBlock"
                title="<?php __('Credit Notes') ?>" id="ViewInvoice"><span class="one-line"><?php  echo __("Credit Notes",true)." (".$creditnote_count.")"?></span></a></li>
        <?php }?>

        <?php
        if(ifPluginActive(InventoryPlugin) && settings::getValue(InventoryPlugin, \Settings::ENABLE_STOCK_REQUESTS)
            && check_permission(Izam\Daftra\Common\Utils\PermissionUtil::VIEW_STOCK_REQUESTS)
              && $stock_request_count
        ){
            ?>

            <li class="" role="presentation">
                <a aria-controls="StockRequests" class="ajax-tab" role="tab" data-toggle="tab" onclick="reloadStockRequests()" href="#StockRequests" title="<?php __('Stock Requests') ?>" id="ViewStockRequests">
                    <span class="one-line"><?php echo __("Stock Requests", true) . " (" . $stock_request_count . ")" ?></span>
                </a>
            </li>

            <?php
        }
        ?>

        <?php //Removed on purpose
        if ( false && $refund_count> 0 ) {?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock"
                 onclick="Reloadany('#RRBlockDiv','<?php echo Router::url(['controller' => 'invoices' , 'action' => 'refund' , '?' => 'work_order_id='.$work_order['WorkOrder']['id']]) ;?>')" role="tab" data-toggle="tab" href="#RRBlock"
                title="<?php __("Refund Receipts") ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Refund Receipts",true)." (".$refund_count.")" ?></span></a></li>
        <?php }?>
        <?php if ( ifPluginActive(ExpensesPlugin) ){?>
        <?php if ( $expense_count  ){?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock"
                 onclick="Reloadany('#ExpenseBlockDiv','<?php echo Router::url(['controller' => 'expenses' , 'action' => 'index' , '?' => 'work_order_id='.$work_order['WorkOrder']['id']. '']) ;?>')" role="tab" data-toggle="tab" href="#ExpenseBlock"
                title="<?php __('Expenses') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Expenses",true)." (".$expense_count.")";?></span></a></li>
        <?php } ?>
        <?php if ( $income_count  ){?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock"
                  onclick="Reloadany('#IncomeBlockDiv','<?php echo Router::url(['controller' => 'incomes' , 'action' => 'index' , '?' => 'work_order_id='.$work_order['WorkOrder']['id']]) ;?>')" role="tab" data-toggle="tab" href="#IncomeBlock"
                title="<?php __('Incomes') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Incomes",true)." (".$income_count.")"?></span></a></li>
        <?php } ?>
        <?php }?>
        <?php if ($appointments_count > 0  ) {?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock"
                   onclick="Reloadany('#AppointmentsBlockDiv','<?php echo Router::url(['controller' => 'appointments' , 'action' => 'index' ,Post::WORK_ORDER_TYPE, '?' => 'compact=0&item_id='.$work_order['WorkOrder']['id']]) ;?>')" role="tab"
                data-toggle="tab" href="#AppointmentsBlock" title="<?php __('Appointments') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Appointments",true)." (".$appointments_count.")" ?></span></a>
        </li>
        <?php } ?>
        <?php if ( $posts_count ) {?>
            <li role="presentation"><a aria-controls="actions-tab" role="tab" data-toggle="tab" onclick="reload_posts()" href="#NotesBlock" title="<?php __('Notes / Attachments') ?>"><span class="one-line"><?php __('Notes / Attachments') ?>  <small class="counter"> (<? echo $posts_count ?>)</small> </span></a></li>
        <?php } ?>
        <!--<li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock" reload_url="<?php echo Router::url(['controller' => 'invoice_payments' , 'action' => 'index' , '?' => 'work_order_id='.$work_order['WorkOrder']['id']]) ;?>" role="tab" data-toggle="tab" href="#PaymentBlock" title="<?php __('Payments') ?>" id="ViewInvoice"><span class="one-line"><?php __("Payments") ?></span></a></li>-->
        <?php if ( $purchaseorder_count ) {?>
            <li role="presentation">
                <a aria-controls="POBlock" role="tab" data-toggle="tab" onclick="reloadPurchaseInvoices()" href="#POBlock" title="<?php __('Purchase Invoices') ?>">
                    <span class="one-line"><?php echo __("Purchase Invoices",true)." (".$purchaseorder_count.")" ?></span>
                </a>
            </li>
        <?php } ?>
        <?php
            if (
                $requisitions_count
                && check_permission(Izam\Daftra\Common\Utils\PermissionUtil::REQUISITION_VIEW)
            ) {
        ?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls=""
                  onclick="onRequisitionLoad()" role="tab" data-toggle="tab"
                href="#RequisitionBlock" title="<?php __('Requisitions') ?>" id=""><span class="one-line"><?php echo __("Requisitions",true)." (".$requisitions_count.")" ?></span></a></li>
        <?php } ?>
        <?php if ( ifPluginActive(TimeTrackingPlugin ) && $timetracking_count > 0){?>
        <li class="" role="presentation"><a class="ajax-tab time_tracking" aria-controls="InvoiceBlock"
                  onclick="Reloadany('#TTBlockDiv','<?php echo Router::url(['controller' => 'time_tracking' , 'action' => 'index' , '?' => 'list&work_order_id='.$work_order['WorkOrder']['id']]) ;?>')" role="tab" data-toggle="tab" href="#TTBlock"
                title="<?php __('Time Tracking') ?>" id="ViewInvoice"><span class="one-line"><?php echo __("Time Tracking",true)." (".$timetracking_count.")"?></span></a></li>
        <?php }?>
        <?php if ( !empty ( $financial_results ) ){?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock"
                  onclick="Reloadany('#FinancialListDiv','<?php echo Router::url([ 'action' => 'financial_list' , $work_order['WorkOrder']['id']]) ;?>')" role="tab"
                data-toggle="tab" href="#FinancialList" id="ViewInvoice"><span class="one-line"><?php __("Financial Transactions") ?></span></a></li>
        <?php }?>
        <?php if ( $transaction_count > 0 && (check_permission(Invoices_View_All_Invoices) || check_permission(Invoices_View_Invoices_Details))) {?>
            <li role="presentation"><a onclick="reload_transaction()"  href="#TransactionList"  aria-controls="TransactionList" role="tab" data-toggle="tab" title=""><span class="one-line">
                        <?php __("Transaction List ") ?>
                        <small class="counter">(<?php echo $transaction_count; ?>)</small></span></a></li>
        <?php }?>
        <li class="" role="presentation"><a class="ajax-tab" aria-controls="InvoiceBlock"
               onclick="Reloadany('#TimelineBlockDiv','<?php echo Router::url([ 'action' => 'timeline' , $work_order['WorkOrder']['id']]) ;?>')" role="tab" data-toggle="tab"
                href="#TimelineBlock" id="ViewInvoice"><span class="one-line"><?php __("Activity Log") ?></span></a></li>
        <?php if($journalCount  && ifPluginActive(AccountingPlugin) && (check_permission(MANAGE_ALL_JOURNALS) || check_permission(MANAGE_OWN_JOURNALS))):?>
        <li  role="presentation">
            <a aria-controls="WorkOrderJournals" role="tab" data-toggle="tab" onclick="reloadWorkOrderJournals()" href="#WorkOrderJournals" title="<?php __('Work order journals') ?>" id="ViewInvoice">
                <span class="one-line"><?php echo  sprintf(__("Journals (%s)",true),$journalCount); ?></span>
            </a>
        </li> 
        <? endif; ?>   
    </ul>
    <?php
        $iframe_url = '';
        if( $default_template )
            $iframe_url = $iframe_link;
        else
            $iframe_url = Router::url(array('action' => 'preview', $work_order['WorkOrder']['id']));
    ?>
    <div class="tab-content responsive">
        <?php

		if(ifPluginActive(BNR) && $bnr_invoice){
		?>
        <div class="tab-pane active" id="BnrBlock">
            <h2>
                <? echo sprintf(__('Pnr #%s',true),$bnr_invoice['Invoice']['no']) ?> <a href="<? echo Router::url(array('controller'=>'resellers','action'=>'edit',$bnr_invoice['Invoice']['id'])) ?>"><small>
                        <? echo __('Edit',true) ?></small></a></h2>
            <div class="row">
                <div class="col-md-4 col-sm-6">
                    <div class="panel panel-default">
                        <div class="panel-heading"><?php  __("Total purchase") ?> </div>
                        <div class="panel-body p-0">
                            <div class="row no-margin">
                                <div class="col-xs-12  p-0 b-r">
                                    <div class="m"> <span><strong><?php  echo format_price($total_po, $po_currency_code) ?></strong></span>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="panel panel-default">
                        <div class="panel-heading"><?php  __("Total Sales") ?> </div>
                        <div class="panel-body p-0">
                            <div class="row no-margin">
                                <div class="col-xs-12  p-0 b-r">
                                    <div class="m"> <span><strong><?php  echo format_price($client_invoice['Invoice']['summary_total'], $client_invoice['Invoice']['currency_code']) ?></strong></span>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="panel panel-default">
                        <div class="panel-heading"><?php  __("Profit") ?> </div>
                        <div class="panel-body p-0">
                            <div class="row no-margin">
                                <div class="col-xs-12  p-0 b-r">
                                    <div class="m"> <span><strong><?php  echo format_price($total_profit, $client_invoice['Invoice']['currency_code']) ?></strong></span>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="p-0">
                        <div class="panel panel-default no-margin">
                            <div class="panel-heading">
                                <? echo __('Products List',true) ?>
                            </div>
                            <div class="invoice-items">
                                <div class=" table-responsive">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table b-light mobile-invoicing">
                                        <tr class="TableHeader table-header active unmovable">
                                            <th id="label_item" class="col-1 name-cell"><?php __("Items") ?></th>
                                            <th id="label_unit_price" class="count-cell "><span><?php __("Sell Price") ?></span></th>
                                            <th id="label_unit_price" class="count-cell "><span><?php __("Buy Price") ?></span></th>
                                            <th id="label_quantity" class="count-cell "><span><?php __("Quantity") ?></span></th>
                                            <th id="label_Supplier" class="count-cell "><span><?php __("Supplier") ?></span></th>
                                            <th id="label_subtotal" class="count-cell"><span><?php __("Sell Price") ?></span></th>
                                            <th id="label_subtotal" class="count-cell"><span><?php __("Buy Price") ?></span></th>
                                            <th id="label_subtotal" class="count-cell"><span><?php __("Profit") ?></span></th>
                                        </tr>
                                        <?php $total = 0  ; foreach ( $bnr_invoice['InvoiceItem'] as $ri ){
                                            $ri['col_3']=(float)$ri['col_3'];
                                            ?>
                                        <tr class="itemRow fix-clear movable">
                                            <td class="">
                                                <p class="p-0"><strong><?php echo $ri['Product']['name']?></strong> <small>#<?php echo $ri['Product']['id']?></small></p>
                                                <small><i class="fa fa-barcode"></i> <?php echo $ri['Product']['barcode']?></small>
                                            </td>
                                            <td class=""><?php echo format_price_simple($ri['unit_price'])?></td>
                                            <td class=""><?php echo format_price_simple($ri['col_3'])?></td>
                                            <td class=""><?php echo format_number($ri['quantity'])?></td>
                                            <td class=""><?php echo ($ri['Supplier']['business_name'])?></td>
                                            <td><?php $total += $ri['quantity'] * $ri['unit_price'] ;echo format_price_simple($ri['quantity'] * $ri['unit_price']) ;?></td>
                                            <td><?php $total += $ri['quantity'] * $ri['col_3'] ;echo format_price_simple($ri['quantity'] * $ri['col_3']) ;?></td>
                                            <td><?php $total += $ri['quantity'] * ($ri['subtotal']-$ri['col_3']) ;echo format_price_simple($ri['quantity'] * ($ri['subtotal']-$ri['col_3'])) ;?></td>
                                        </tr>
                                        <?php } ?>

                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <?php
		}
		?>
        <div class="tab-pane active" id="WorkOrderDetails">
            <iframe id="detailsIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>

        <div class="tab-pane" id="StockRequests">
            <iframe id="stockRequestIframe" src="about:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>
        <?php if ( ifPluginActive(AccountingPlugin) && (check_permission(MANAGE_ALL_JOURNALS) || check_permission(MANAGE_OWN_JOURNALS)) ){?>
        <div class="tab-pane" id="WorkOrderJournals">
            <iframe id="journalsIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>
        <?php }?>

        <div class="tab-pane" id="WorkOrderBlock">
            <div class="preview-invoice invoice-Block">
                <? if (IS_PC) {
                    $invoice_print_method = settings::getValue(InvoicesPlugin, 'invoice_print_method');

                    ?>
                <div class="invoice-template">
                    <iframe frameBorder="0" src="<?= $iframe_url; ?><?php if($invoice_print_method==settings::OPTION_PRINT_PDF){?>.pdf?inline=true <?php } ?>" id="InvoicePreview" name="InvoicePreview" width="100%"
                        height="800"></iframe>
                </div>
                <? } else { ?>
                <div class="responsive-invoice">
                    <img style="max-width:100%" src="<?php echo $iframe_url ?>.jpeg" />
                </div>
                <? } ?>
            </div>
            <?php
                $fields_empty = true ;
                foreach ( $field_values as $k ) {
                    if ( !empty ( $k ) ){
                        $fields_empty = false ; break ;
                    }
                }
                if ( !$fields_empty && !empty ($field_values ) ){?>
            <br>
            <div class="input-fields ">
                <h3 class="head-bar theme-color-a"><span class="details-info">
                        <?php echo $custom_form_name ?>
                    </span></h3>
                <div class="row">
                    <?php echo $this->element ('custom_forms/view_fields_form') ;?>
                </div>
            </div>
            <?php }?>
        </div>
        <div class="tab-pane " id="InvoiceBlock">
        <div id="InvoiceBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="EstimateBlock">
            <div id="EstimateBlockDiv"> </div>
        </div>
        <div class="tab-pane" id="RelatedSalesOrderBlock">
            <iframe id="RelatedSalesOrderIframe" src="About:blank"
                    style="width: 100%; min-height: 500px;border: 0;"></iframe>
        </div>
        <div class="tab-pane " id="CNBlock">
            <div id="CNBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="RRBlock">
            <div id="RRBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="ExpenseBlock">
            <div id="ExpenseBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="IncomeBlock">
            <div id="IncomeBlockDiv"> </div>
        </div>
        <?php if (array_sum($appointment_status_counts ) > 0  ) { ?>
        <div class="tab-pane" id="AppointmentsBlock">
            <div id="AppointmentsBlockDiv">
            <div class="panel panel-default">
                <div class="">
                    <div class="panel panel-default no-margin" style="border-bottom: none;">
                        <div class="panel-heading" style="border-bottom: none;">

                            <div class="btn-group  pull-right pos3">
                                <?php foreach ( $appointment_statuses as $k => $s ){?>
                                <a class=" btn btn-default reload_status <?php if ( $k == 0 ) {?>active<?php } ?>" title="<? echo $s ?>"
                                    status="<?php echo $k;?>"><?php echo $s . " ($appointment_status_counts[$k])";?></a>
                                <?php }?>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12" id="AppointmentsBlock_rel">
                            <div class="notifcation-loader">
                                <div class="inner-loader"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
        <?php }?>
        <div class="tab-pane " id="NotesBlock">
            <?php echo $this->element('clients/client_note' , ['item_type' => Post::WORK_ORDER_TYPE , 'item_id' => $work_order['WorkOrder']['id']]); ?>
        </div>
        <div class="tab-pane " id="PaymentBlock">
            <div id="PaymentBlockDiv"> </div>
        </div>

        <div class="tab-pane" id="POBlock">
            <iframe id="POBlockDiv" src="About:blank" style="width: 100%; min-height: 500px;border: 0;">
            </iframe>
        </div>
        <div class="tab-pane " id="TTBlock">
            <div id="TTBlockDiv"> </div>
        </div>

        <?php if ( !empty ( $financial_results ) ){?>
        <div class="tab-pane " id="FinancialList">
            <div id="FinancialListDiv"> </div>
        </div>
        <?php }?>
        <?php if($transaction_count > 0){ ?>
            <div class="tab-pane" role="tabpanel" id="TransactionList">
                <div id="ReportTable">
                    <?php echo $this->element('clients/client_transaction', array('id' => $work_order['WorkOrder']['id'])); ?>
                </div>
            </div>
        <?php } ?>
        <div class="tab-pane " id="RequisitionBlock">
            <div id="RequisitionBlockDiv"> </div>
        </div>
        <div class="tab-pane " id="TimelineBlock">
            <div id="TimelineBlockDiv"> </div>
        </div>
    </div>

</div>
<script>
var current_status = 0;
$(function() {
    <?php if (!empty($this->params['named']['print'])) { ?>
    print_inv();
    <?php }?>
    $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
        if (history.pushState) {
            history.pushState(null, null, '#' + $(e.target).attr('href').substr(1));
        } else {
            location.hash = '#' + $(e.target).attr('href').substr(1);
        }
    });
    if (location.hash !== '') {
        $('a[href="' + location.hash + '"]').tab('show');
        $('a[href="' + location.hash + '"]').click()
    }



});
$("#print_button").click(function(e) {
    e.preventDefault();
    print_inv()
});
$(".reload_status").click(function() {
    $(".reload_status").removeClass('active')
    $(this).addClass('active')
    window.current_status = $(this).attr('status');
    $(".ajax-tab[href='#AppointmentsBlock']").click();

});

var print_log_url = "<? echo Router::url(array('action' => 'print_log', $this->params['pass'][0])) ?>";
</script>
<?php
echo $javascript->link(array('work_order_v'.JAVASCRIPT_VERSION));
?>

    <!-- App Delete Modal -->
    <div class="modal fade l-delete-modal" data-app-delete-modal="true" id="appDeleteModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" style="display: none;">
    <div class="modal-backdrop fade in" style="height: 100vh;"></div>

        <div class="modal-dialog modal-dialog-centered modal-md" style="max-width: 500px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <i class="mdi mdi-trash-can-outline trash-icon u-text-color-dangerligth"></i>
                    <div data-app-delete-modal-message="true"></div>
                </div>
                <form method="post" action="" data-app-delete-modal-url="">
                    <input type="hidden" name="_token" value="" data-app-delete-modal-token="true" />
                    <input type="hidden" name="_method" value="DELETE" data-app-delete-modal-method="DELETE" />
                    <button type="submit" class="ui-btn u-bg-color-dangerligth u-text-color-white"> <?= __t('Yes') ?></button>
                    <button type="button" class="ui-btn u-bg-color-secondary" data-bs-dismiss="modal"><?= __t('No') ?></button>
                </form>
            </div>
        </div>
    </div>

<?php if ($manualOutboundRequisitions) { ?>

<div class="modal fade" id="convertRequisitionsModal" tabindex="-1" role="dialog" aria-labelledby="convertRequisitionsModal">
    <div class="modal-dialog"  role="document">
        <div class="modal-content">
            <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
            <?= $form->create('Requisition',['url' => Router::url(['action' => 'convert_requisitions', $work_order['WorkOrder']['id']])]); ?>
            <div class="modal-header">
                <h3><?php __('Select the requisitions you want to convert') ?></h3>
            </div>
            <div class="modal-body">
               <div class="row compat">
                   <?php echo  $filterHelper->render($manufacturingModalForm) ?>
               </div>
            </div>
            <div class="modal-footer flex-row d-flex">
                <button class="btn btn-secondary font-weight-medium col-flex-4" data-dismiss="modal" aria-label="Close"><?php __('CANCEL');?></button>
                <?php 
                    echo $form->submit(__('CONVERT',true), array('class' => "btn btn-success btn-lg btn-block padding h-auto ml-2 mr-0", 'div' => false));
                    echo $form->end()
                ?>
            </div>

        </div>
    </div>
</div>
<style>
.modal .modal-dialog {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    min-height: calc(100% - (10px* 2));
    padding-top: 0px !important;
}
.modal .modal-content {
    width: 100%;
}

#convertRequisitionsModal .btn-group {
    width: 100%;
}
#convertRequisitionsModal .modal-footer .btn {
    font-size: 14px;
    padding: 13px 24px;
}
@media(min-width: 768px) {
    #convertRequisitionsModal .modal-dialog {
        max-width: 600px;
        min-height: calc(100% - (30px* 2));
    }
}
</style>
<script>
    $(function() {
        $(".requisitions-selectpicker").multiselect({
            enableFiltering: true,
            enableCaseInsensitiveFiltering: true
        });
    });

</script>
<?php } ?>
<?php 
  echo $this->element('work_orders/assign_transaction_modal',["is_workflow" => "0"]); 
  echo $this->element('work_orders/un_assign_transaction_modal',["is_workflow" => "0"]);
?>

<script src="/dist/app/js/pages/workflow/show/show.js"></script>
<style>
    @media (min-width: 768px) {
        #WorkOrderDetails {
            margin: -20px;
        }
    }
</style>
<script !src="">
    function reloadRelatedSalesOrders() {
        document.getElementById('RelatedSalesOrderIframe').src = '/v2/owner/entity/sales_order/list?iframe=1&hide_page_header=1&hide_actions=1&filter[work_order_id]=<?= $work_order['WorkOrder']['id'] ?>';
    }

    function reloadPurchaseInvoices() {
        document.getElementById('POBlockDiv').src = '/v2/owner/entity/purchase_order/list?iframe=1&hide_page_header=1&hide_actions=1&filter[work_order_id]=<?= $work_order['WorkOrder']['id'] ?>';
    }
    $(document).on('click', '#TTBlock #top-nav-next, #TTBlock #top-nav-prev', function (event) {
        event.preventDefault();
        var current_url = $(this).attr('href');
        if ('' == current_url) {
            $(this).addClass('disabled');
            return;
        }
        $(document).find('.time_tracking').attr('onclick', "Reloadany('#TTBlockDiv','" + current_url + "')").click();
    });
</script>


</script>
<script>
       var appDeleteModal = document.getElementById('appDeleteModal');

        appDeleteModal.addEventListener('shown.bs.modal', function() {
            appDeleteModal.classList.add("in");
        })
</script>
<script>
    $(document).on('click', '#TTBlock #top-nav-next, #TTBlock #top-nav-prev', function (event) {
        event.preventDefault();
        var current_url = $(this).attr('href');
        if ('' == current_url) {
            $(this).addClass('disabled');
            return;
        }
        $(document).find('.time_tracking').attr('onclick', "Reloadany('#TTBlockDiv','" + current_url + "')").click();
    });
</script>
