
<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/create/create.min.css?v=".CSS_VERSION, null, []); ?>

<?php

$isBox = !empty($this->params['url']['box']);
// warning suppress
$categories_values = $categories_values ?? [];
?>
<?php
$options = array('id' => 'ProductForm');

if ($isBox) {
    $options['url'] = array('?' => array('box' => 1));

}
$options['type'] = 'file';
// warning suppress
$id = !empty($this->data['Product']['id']) ? $this->data['Product']['id'] : null;
$dataProductId = $id ?? $this->data['Product']['type'];
$options['url'][] = $dataProductId ?? $you_sell;
$options['url'][] = $clone_id;
?>
<?php echo $form->create('Product', $options); ?>
<script>
    var brands = [];
    $(function() {
        //ProductInitialStockLevel
        $("body").on( 'keyup' ,'#virtual_stock', function () {
            $("#ProductInitialStockLevel").val($("#stock_factor option:selected").attr('factor') * $("#virtual_stock").val() )
        })
        $("#unit_template").change(function(){
            if ( $(this).val() == "" ){
                $("#stock_factor").val("")
            }
            if ($("#ProductTrackingType").val() !== "quantity_only") return;
            $("#stock_factor").change();
        })
        $("#stock_factor").change(function ( ) {
            if ( ($(this).val() == "" || $(this).val() == null) && $("#ProductTrackingType").val() === "quantity_only") {

                $("#ProductInitialStockLevel").show()
                $("#virtual_stock").hide()

            }else {
                if ($("#virtual_stock").length == 0 && $("#ProductTrackingType").val() === "quantity_only"){
                    myclone = $("#ProductInitialStockLevel").clone();
                    myclone.attr('id' ,"virtual_stock" )
                    myclone.attr('name' ,"" )
                    $("#ProductInitialStockLevel").after(myclone[0].outerHTML)
                }
                $("#ProductInitialStockLevel").hide()
                $("#virtual_stock").show()
            }
            $("#virtual_stock").keyup();
        })
        var is_submitted = false;
        $('#ProductForm').on('submit', function(){
            is_submitted = true
        });
        $('#add_other').click(function(e) {
            if (!is_submitted) {
                $('#sbt_btn').val('add_new');
                  // disable submit-btn
                $('#submit-btn').prop('disabled', true);
                $('#ProductForm').submit();
                setTimeout(function(){ is_submitted = false}, 30000);
            }
            return false;
        });
        $('#enable-multi-taxes').click(function () {
            $('#is_multi_tax_enabled').val(1);
            $(this).hide().fadeOut(500);
            $('#sales-tax-1').find('div label').text('Sales Tax 1');
            $('#sales-tax-2').find('div label').text('Sales Tax 2');
            // append to purchasing tax id div the template
            var temp = $($('#purchasing-tax-template').html());

            temp.find('div label').text('Purchasing Tax 1');
            temp.find('div select').attr('name', 'data[Product][purchasing_tax1]');

            $('#purchasing-tax').append(temp).show('slow');

            var temp2 = $($('#purchasing-tax-template').html());
            temp2.find('div label').text('Purchasing Tax 2');
            temp2.find('div select').attr('name', 'data[Product][purchasing_tax2]');
            $('#purchasing-tax').append(temp2);
        });
    });
</script>
<link rel="stylesheet" type="text/css" href="/css/product-add-en_v1.css" />
<?php if (CurrentSiteLang() == "ara") { ?>
	<link rel="stylesheet" type="text/css" href="/css/product-add-ar.css" />
<?php } ?>
<?php
echo $html->css(array('bootstrap-select_v'.CSS_VERSION.'.css', 'magicsuggest-min.css', 'product-add.css', 'tabs.css'), false, ['inline' => false]);
echo $javascript->link(array('bootstrap-select.js', 'magicsuggest2.1.5-min.js', 'product-add.js'));
echo $html->css(array('jquery-ui-timepicker-addon'), false, ['inline' => false]);
echo $javascript->link(array('jquery-ui-timepicker-addon'));
?>
    <?php

    $title = __("Add New Product", true);

	?>
    <!--<h1><?php echo $title ?></h1>-->
<?php if (!$isBox) { ?>
    <div class="panel-defaultz">
        <div class="panel-bodyz">
        <?php } ?>
        <?php if ($isBox) {
            echo $session->flash();
             if ($this->params["url"]["close-modal"] == 0 && $this->params["url"]["success"] == 1) {?>
                 <script>
                     window.location.href =  SITE_ROOT + '/owner/products/add?box=1';
                 </script>
            <?php } ?>
        <?php } ?>

        <?php if (empty($_GET['success'])): ?>


                <?php echo $form->input('id', array('class' => 'INPUT', 'div' => 'form-group')); ?>
                <?php echo isset($booking) ? $form->input('booking', ['type' => 'hidden', 'value' => $booking]) : "" ?>
            <?php echo $form->input('type', array('type'=>'hidden','class' => 'INPUT required form-control', 'div' => 'form-group')); ?>
            <?php if (isset($clone_id)) { echo $form->input('clone_id', array('type'=>'hidden', 'value'=>$clone_id));  }?>
            <div <?php if ($isBox) { echo 'style="width: 98%; margin: 0 auto;"'; } ?>>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="input-fields inside-form-box">
                            <h3 class="rounded-item  head-bar theme-color-a" style="<?php if ($isBox) { echo 'padding: 15px;margin-bottom: 15px;'; } ?>">
                                <span class="contact-info">
                                    <?php __("Item Details") ?>
                                </span>
                            </h3>
                            <div class="form-row-flex">
                                <div class="col-flex-sm-12">
                                    <div class="form-row">
                                        <?php echo $form->input('name', array('label' => __('Name', true) . '<span class="required"> *</span>','class' => 'INPUT required form-control', 'div' => 'form-group col-sm-8 col-input-m-0')); ?>
                                        <?php echo $form->input('product_code', array('label'=>$you_sell== settings::OPTION_SOLD_TYPE_SERVICES?__('Service Code',true):__('Item SKU',true),'class' => 'INPUT   form-control', 'between' => '', 'type' => 'text', 'div' => 'form-group col-sm-4 col-input-m-0')); ?>
                                        <?= $form->hidden('default_product_code') ?>
                                    </div>
                                </div>
                                <?= $form->hidden('item_group_id') ?>
                                <?php echo $form->input('description', array('class' => 'resizable INPUT  form-control', 'rows' => '2', 'div' => 'form-group col-flex-sm-12 col-input-m-0')); ?>
                                <?php if(IS_PC && !$isBox){ ?>
                                    <?php if(isset($this->data['ProductAttributeOption'])): ?>
                                        <?php foreach ($this->data['ProductAttributeOption'] as $idx => $productAttribute): ?>
                                            <div class="form-group col-flex-sm-6 <?= ($idx ==2 || count($this->data['ProductAttributeOption']) == 1)  ? 'col-flex-sm-12' : '' ?> col-input-m-0 required">
                                                <div class="form-group col-input-m-0">
                                                    <div class="l-input-box">
                                                        <label ><span><?=$productAttribute['attribute']?></span></label>
                                                        <select name="data[ProductAttributeOption][<?= $idx ?>][option]" class="l-input ui-input" placeholder="<?= __('Select product attribute option', false) ?>"
                                                                data-app-form-select-template="ajax-simple-2" data-app-form-select="true"
                                                                data-select-product-attribute-option="true" data-attribute-option-row="<?= $idx ?>"
                                                                data-app-form-select-options='{"ajax": {"url": "/owner/item_group/get_attribute_options?&productId=<?= $this->data['Product']['id'] ?>&attribute=<?=$productAttribute['attribute']?>&attributeId=<?= $productAttribute['id'] ?>&q=__q__" }}' >
                                                            <option value=""></option>
                                                            <option selected value="<?= $productAttribute['option'] ?>"><?= $productAttribute['option'] ?></option>
                                                            <?php foreach ($productAttribute['other_options'] as $other_option): ?>
                                                                <option value="<?= $other_option['value'] ?>"><?= $other_option['value'] ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                        <input type="hidden" name="data[ProductAttributeOption][<?= $idx ?>][attribute]" value="<?= $productAttribute['attribute'] ?>">
                                                        <input type="hidden" name="data[ProductAttributeOption][<?= $idx ?>][id]" value="<?= $productAttribute['id'] ?>">
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif ?>
                                <?php } ?>
                                <div class="col-flex-sm-12 form-group">
                                    <?php
                                        echo $this->element('new_s3_file_uploader', ['recordAttachments' => $this->data['Attachments'], 'name' => 'data[Product][attachment]', 'entityKey' => 'product', 'fieldKey' => 'products.product_images', 'options' => ['multiple' => true, 'sortable' => true , 'label' => __('Photos',true), 'mimes' => []]]);
                                                   
                               ?>
                                
                                
                                <?php if(!empty($id)):?> 
                                    
                                    <div class="l-flex-row l-flex-row--spacing-5 l-uploader-file-items--spacing-top ui-uploader-file-items" id="thumbnail-loaded">
                                        <?php foreach ($this->data['ProductImage'] as $key => $photo) { ?>
                                            <div class="l-flex-col-sm-4 l-flex-col-12 draggable" draggable="true" data-photo-id="<? echo $photo['id']; ?>" data-photo-type="legacy" id="photo_<? echo $photo['id']; ?>">
                                                <div class="ui-show-card-section-item-content" style="margin-bottom: 15px;">
                                                    <div class="ui-attachment-box">
                                                        <a href="<? echo Router::url(array('action' => 'set_default', $photo['id'])) ?>" title="<?= ucwords(__('set as default', true)) ?>" data-photo-default-btn="true" class="set_default_photo"><i class="mdi mdi-image-multiple"></i></a>
                                                        <a class="l-attachment-box-header l-attachment-box-header--spacing-0 u-bg-color-secondary" data-app-lightbox="<? echo $photo['file_full_path']; ?>" href="<? echo $photo['file_full_path']; ?>" data-app-form-uploader-img-container="true" style="display: inline-block;">
                                                            <img src="<? echo $photo['file_full_path']; ?>?w=300&h=250&c=1" />
                                                        </a>
                                                        <a class="l-attachment-box-header l-attachment-box-header--icon-only l-attachment-box-header--spacing-0 u-bg-color-secondary" href="<? echo $photo['file_full_path']; ?>" data-app-form-uploader-icon-container="true" style="display: none;">
                                                            <i class="ui-icon--size-50 ui-view-icon u-text-color-subtitle mdi mdi-file-image"></i>
                                                        </a>
                                                        <div class="l-attachment-box-content">
                                                            <a href="<? echo $photo['file_full_path']; ?>" class="l-flex l-flex--align-center" id="photo_<? echo $photo['id']; ?>">
                                                                <i class="ui-icon--size-16 mdi mdi-file ui-view-icon u-text-color-subtitle"></i>
                                                                <span>&nbsp;</span>
                                                                <span class="u-text-color-black"><?= $photo['file'] ?></span>
                                                            </a>
                                                            <span class="l-attachment-action">
                                                            <span class="l-flex l-flex--align-center u-text-color-subtitle">
                                                                <span>&nbsp;</span>
                                                                <div class="l-uploader-file-item-actions">
                                                                    <a class="l-uploader-file-item-action ui-uploader-file-item-action mdi mdi-download u-text-color-primary ui-attachment-link-item" tabindex="0" data-app-keyboard-support="true" data-app-keyboard-support-options="{&quot;keypress&quot;: {&quot;keyCode&quot;: &quot;13&quot;, &quot;action&quot;: &quot;click&quot;}}" title="Download" href="<? echo $photo['file_full_path']; ?>"></a>
                                                                    <a class="l-uploader-file-item-action ui-uploader-file-item-action mdi mdi-trash-can u-text-color-red" tabindex="0" data-app-keyboard-support="true" data-app-keyboard-support-options="{&quot;keypress&quot;: {&quot;keyCode&quot;: &quot;13&quot;, &quot;action&quot;: &quot;click&quot;}}" title="Delete" data-app-form-uploader-delete-btn="true" href="<? echo Router::url(array('action' => 'remove_photo_redirect', $photo['id'])) ?>" onclick="return confirm('<?php echo __('Are you sure you want to delete this photo?') ?>');"></a>
                                                                </div>
                                                            </span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                    </div>
                               
                                    <?php endif;?>    
                            </div>


                                <?php echo $form->input('Product.Category', array('label' => __('Category', true), 'class' => 'INPUT   form-control', 'between' => '', 'type' => 'text', 'div' => 'form-group col-flex-sm-6 col-input-m-0 hint-fix')); ?>
                                <?php if ($you_sell != settings::OPTION_SOLD_TYPE_SERVICES && ifPluginActive(InventoryPlugin)) { ?>
                                    <?php echo $form->input('brand', array('label' => __('Brand', true), 'class' => 'INPUT   form-control', 'type' => 'text', 'div' => 'form-group col-flex-sm-6 col-input-m-0', 'between' => '')); ?>
                                <?php } ?>
                                <?php if (!empty($product_sales_routing) && $product_sales_routing == settings::MANUAL_ACCOUNTS_ROUTING) { ?>
                                    <div class="col-flex-sm-6 col-input-m-0">
                                        <?php echo $this->element('ajax_search/journal_accounts',array('options' => ['getEmptyOption' => true,'id' => 'profits-account','data-placeholder' => __('Default', true),'div' => 'account-type-account form-group text title','multiple' => false,'label' => __('Sales Account', true),'empty' => __('Without Routing', true),'input_name' => "sales_account_id"],'value' => /** warning suppress */ $sales_account_id??null)); ?>
                                    </div>
                                <?php } ?>
                                <?php if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::AccountingPlugin) && !empty($product_sales_routing) && $product_sales_routing == settings::MANUAL_ACCOUNTS_ROUTING) {  ?>
                                    <div class="col-flex-sm-6 col-input-m-0">
                                        <?php echo $this->element('ajax_search/journal_accounts',array('options' => ['getEmptyOption' => true,'id' => 'profits-account2','data-placeholder' => __('Default', true),'div' => 'account-type-account form-group text title','multiple' => false,'label' => __('Sales Cost Account', true),'empty' => __('Without Routing', true),'input_name' => "sales_cost_account_id"],'value' => /** warning suppress */ $sales_cost_account_id??null)); ?>
                                    </div>
                                <?php } ?>
                                <?php if (ifPluginActive(InventoryPlugin) && $enable_multi_units && ( isset($tracking_type) && $tracking_type != 'serial') ){?>
                                    <div class='form-group col-flex-sm-6 col-input-m-0'>
                                        <?php echo $form->input('unit_template_id', array('id' => 'unit_template' ,  'class' => 'INPUT form-control', 'options' => $unit_templates, 'empty' => __('[Select Unit]', true), 'div' => false));?>
                                        <span class="text-danger hidden" id ='unit_template_validation'>-</span>
                                    </div>
                                <?php } ?>

                                <?php if (ifPluginActive(InventoryPlugin) and $supplier_count>0) { ?>
                                    <!-- <div class="clear-both" > -->
                                        <div class="col-flex-sm-6 col-input-m-0">
                                            <?php echo $form->input('supplier_id', array('empty' => __('Select Supplier', true), 'class' => 'INPUT form-control', 'div' => 'form-group')); ?>
                                        </div>
                                        <div id="ProductProductCodeDiv" class="col-flex-sm-6" style="<?if(empty($this->data['Product']['supplier_id'])){echo 'display:none';}?>">
                                            <?php echo $form->input('supplier_code', array('class' => 'INPUT   form-control', 'between' => '', 'type' => 'text', 'div' => 'form-group')); ?>
                                        </div>
                                    <!-- </div> -->
                                <?php } ?>

                                <?php if ($you_sell != settings::OPTION_SOLD_TYPE_SERVICES) { ?>
                                    <div class="col-flex-sm-6 col-input-m-0" id="item_barcode_div">
                                        <?php echo $this->element('OIBarcode_generator' , ['field_name'=>'barcode', 'item_type' => PRODUCT_BARCODE ,'item_id' => $dataProductId ]); ?>
                                        <?php if(ifPluginActive(InventoryPlugin) && $enable_multi_units && ( isset($tracking_type) && $tracking_type != 'serial')) { ?>
                                        <span id="item_barcode_span">
                                            <a class="show-item-barcodes fs-12" href="#" onclick="showBarcodeSection(event)">
                                                <u><?php __('Multiple') ?> </u>
                                            </a>
                                        </span>
                                        <?php } ?>
                                    </div>
                                    <style>
                                        #item_barcode_div {
                                            position: relative;
                                        }
                                        #item_barcode_span {
                                            position: absolute;
                                            top: 0;
                                        }
                                        .ltr #item_barcode_span {
                                            right: 10px;
                                        }
                                        .rtl #item_barcode_span {
                                            left: 10px;
                                        }

                                    </style>
                                <?php } ?>


                                <?php if(ifPluginActive(WebsiteFrontPlugin)) { ?>
                                    <div class="col-flex-sm-12">
                                        <?php
                                            echo $form->input('availabe_online',
                                            [
                                                'label' => __('Available Online', true),
                                                'type' => 'checkbox',
                                                'id' => 'availabe_online',
                                                'checked' => ($dataProductId ? $this->data['Product']['availabe_online'] : 'true'),
                                                'div' => 'clip-check check-info subactions-check',
                                                'class' => 'INPUT form-control'
                                            ]);
	                                        echo $form->input('is_featured',
                                            [
                                                'label' => __('Featured Item', true),
                                                'type' => 'checkbox',
                                                'id' => 'is_featured',
                                                'checked' => ($dataProductId ? $this->data['Product']['is_featured'] : 'false'),
                                                'div' => 'clip-check check-info subactions-check',
                                                'class' => 'INPUT form-control',
                                                'disabled' => !($dataProductId ? $this->data['Product']['is_featured'] : 'false')
                                            ]);
                                        ?>
                                    </div>
                                <?php } ?>



                                <?php if(ifPluginActive(BookingPlugin) && $you_sell == settings::OPTION_SOLD_TYPE_SERVICES) { ?>
                                    <div class="col-sm-12"> <?php echo $form->input('duration_minutes', array('label' => __('Duration In Minutes', true), 'class' => 'INPUT  form-control', 'div' => 'form-group col-input-m-0')); ?></div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="input-fields inside-form-box">
                            <h3 class="rounded-item  head-bar theme-color-a" style="<?php if ($isBox) { echo 'padding: 15px;margin-bottom: 15px;'; } ?>">
                                <span class="contact-info">
                                    <?php __("Pricing Details") ?>
                                </span>
                            </h3>
                            <div class="row">
                                <div class="col-sm-<?php echo (ifPluginActive(InventoryPlugin) && $enable_multi_units) ? '12' : '6'; ?>">
                                    <div class="input-group-flex-inputs form-group">
                                        <?php echo $form->input('buy_price', array('label' => __("Purchase Price", true), 'class' => 'INPUT numeric  form-control', 'type' => 'number', 'div' => 'form-group col-input-m-0', 'between' => '')); ?>
                                        <?php echo $form->input('buy_price_hidden', array('type' => 'hidden')); ?>
                                        <?php if (ifPluginActive(InventoryPlugin) && $enable_multi_units){?>
                                            <?php echo $form->input('default_buy_factor_id', array('label' => "&nbsp;", 'id' => 'buy_factor' ,'type' => 'select' , 'class'=> 'factor_select w-100','div' => 'col-input-m-0', 'options' => NULL, 'empty' => sprintf(__t('Select %s'), __t('Factor')) ));?>
                                        <?php }?>
                                    </div>
                                </div>
                                <div class="col-sm-<?php echo (ifPluginActive(InventoryPlugin) && $enable_multi_units) ? '12' : '6'; ?> form-group">
                                    <label><?php echo  ($you_sell == settings::OPTION_SOLD_TYPE_SERVICES ? __('Unit Price', true) : __('Selling Price', true)) . ' <span class="tooltip" title="product-unit-price"></span>'; ?></label>
                                    <div class="input-group-flex-inputs">
                                        <?php echo $form->input('unit_price', array('label' =>false, 'class' => 'INPUT numeric  form-control', 'type' => 'number', 'div' => 'form-group mb-0 col-input-m-0', 'between' => '')); ?>
                                        <?php echo $form->input('unit_price_hidden', array('type' => 'hidden')); ?>
                                        <?php if (ifPluginActive(InventoryPlugin) && $enable_multi_units){?>
                                            <div class="form-group mb-0 col-input-m-0">
                                                <?php
                                                echo $form->input('default_retail_factor_id', array('label' => false , 'id' => 'retail_factor' ,'type' => 'select',  'options' => NULL, 'empty' => sprintf(__t('Select %s'), __t('Factor')),'class'=> 'factor_select', 'div' => false));?>
                                            </div>
                                        <?php }?>
                                    </div>
                                </div>
                                <?php if (!$isBox || count($taxes) > 0) { ?>
                                    <?php
                                    if (!$isBox) {
                                        $taxes[-1] = __('Taxes Settings', true);
                                    }
                                    ?>
                                    <div class="col-sm-6 col-input-m-0" id="sales-tax-1">
                                    <?php
                                    echo $form->input('tax1', array('class' => 'INPUT form-control item-tax', 'div' => 'form-group', 'label' => array(
                                        'text' => '<div class="d-flex justify-content-between"><span id="tax1-name-span">'.__t("Tax 1").'</span> <span class="underline_text pointer toggle_taxes_span">'.__t("Advanced").'</span> </div>',
                                    ), 'options' => $taxes, 'empty' => __('[Select Tax]', true)));
                                    if (count($taxes) > 1) {
                                        ?></div>
                                    <div class="col-sm-6 col-input-m-0" id="sales-tax-2"> <?php
                                        echo $form->input('tax2', array('class' => 'INPUT form-control item-tax', 'div' => 'form-group','label'=>__t('Tax 2'), 'options' => $taxes, 'empty' => __('[Select Tax]', true)));
                                    }
                                    ?>
                                    </div><div class="clearfix">
                                        <div class="col-md-12 col-input-m-0 d-none purchase_taxes_column">
                                                <div class="form-group col-md-6">
                                                    <?php
                                                    // remove from $taxes taxes with id -1
                                                    unset($taxes[-1]);
                                                    $taxes[-1] = __('No Tax', true);
                                                    echo $form->input('purchasing_tax1',
                                                        array(
                                                            'class' => 'INPUT form-control item-tax purchasing-tax',
                                                            'label' => __('Purchase Tax', true) . ' 1',
                                                            'div' => 'form-group',
                                                            'options' => $taxes,
                                                            'empty' => __('Same as Sales Tax 1', true)
                                                        ),
                                                    );
                                                    ?>
                                                </div>

                                            <div class="form-group col-md-6">
                                                <?php
                                                echo $form->input('purchasing_tax2',
                                                    array(
                                                        'class' => 'INPUT form-control item-tax purchasing-tax',
                                                        'div' => 'form-group',
                                                        'label' => __('Purchase Tax', true). ' 2',
                                                        'options' => $taxes,
                                                        'empty' => __('Same as Sales Tax 2', true)
                                                    ),
                                                );
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if ( $advanced_pricing_options ){ ?>
                                    <div class="col-sm-6">
                                        <?php echo $form->input('minimum_price', array('class' => 'INPUT   form-control', 'between' => '', 'type' => 'number', 'div' => 'form-group col-input-m-0')); ?>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="input-group-flex-inputs">
                                            <?php echo $form->input('discount', array('class' => 'INPUT   form-control', 'between' => '', 'type' => 'number', 'div' => 'form-group col-input-m-0')); ?>
                                            <?php echo $form->input('discout_type', array('label' => __("Discount Type",true), 'type' => 'select' , 'options' => $discount_types , 'class' => 'INPUT   form-control', 'div' => 'form-group col-input-m-0')); ?>
                                        </div>
                                    </div>
                                <?php } ?>

                                <?php if ( $advanced_pricing_options ){ ?>
                                    <div class="col-sm-6">
                                        <div class="input-group-flex-inputs">
                                            <?php echo $form->input('profit_margin', array('class' => 'INPUT   form-control', 'between' => '', 'type' => 'number', 'div' => 'form-group col-input-m-0')); ?>
                                            <?php //echo $form->input('profit_margin_type', array('label' => __("Profit Margin Type",true), 'type' => 'select' , 'disabled' => true , 'options' => [0 => '%'] , 'class' => 'INPUT   form-control', 'div' => 'form-group col-input-m-0')); ?>
                                        </div>
                                    </div>
                                <?php } ?>
                                <div class="clearfix"></div>
                                <?php if ($allowPriceList) { ?>
                                <div class="col-md-12">
                                    <hr style="border-color: #ececec">
                                    <div class="form-row">
                                        <div class="col-sm-12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h4 class="text-dark-blue-s2020 m-b-20"><strong><?php echo __('Price Lists') ?></strong></h4>
                                                <div class="btn-group">
                                                    <button type="button" id="add-price-list-btn" class="btn-s2020 btn-icon-s2020 btn-secondary-s2020 btn-sm-s2020 dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <i class="fa fa-plus mr-1"></i> <?php echo __('New Price List'); ?> <span class="caret"></span>
                                                    </button>
                                                    <ul class="dropdown-menu" id="add-price-list-items">
                                                        <?php foreach ($GroupPrices as $key => $group_price) { ?>
                                                            <li><a class="add-price-list-item" group_price_id="<?=$key?>" href="#"><?=$group_price?></a></li>
                                                        <?php } ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <br>
                                    <div class="clearfix"></div>
                                    <div class="client_contact_rowz fix-bottom-pz no-marginz">
                                        <div class="form-row" id="assigned-price-lists">
                                            <?php if (isset($PriceList) && count($PriceList) > 0) { ?>
                                                <?php foreach ($PriceList as $groupPriceId => $group_price) { ?>
                                                    <div class="col-sm-12">
                                                        <div class="col-input-m-0">
                                                            <div class="form-group has-feedback">
                                                                <label class="control-label group-price-name" for=""><?=$group_price['name']?></label>
                                                                <input name="data[priceList][<?=$groupPriceId?>]" group_price_id = "<?=$groupPriceId?>"
                                                                       type="number" step="any" value="<?=$group_price['price']?>"
                                                                       class="INPUT form-control onfocus group-price-input" min="0.01"
                                                                       placeholder="use default price" id="ProductPrice1Price">
                                                                <a title="Remove This Product From Price List" href="javascript:void(0)" ref_id="1" class="form-control-feedback remove-item rmbtn">
                                                                    <i class="fa fa-times-circle"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="clearfix"></div>
                                                    </div>

                                                    <?php
                                                }
                                                ?>
                                                <div class="clearfix"></div>
                                            <?php } ?>
                                        </div>
                                    </div>

                                </div>
                                <?php } ?>

                            </div>
                        </div>
                    </div>

                    <?php if(ifPluginActive(InventoryPlugin) && $enable_multi_units && ( isset($tracking_type) && $tracking_type != 'serial')) { ?>
                        <div class="col-sm-12">
                            <div class="multi-item-barcodes-section input-fields inside-form-box">
                                <h3 class="rounded-item  head-bar theme-color-a text-left">
                                    <?php __('Product Item Barcodes') ?>
                                    <span onclick="hideBarcodeSection(event)" class="pull-right"><a href="#" id="single-account"><?= __('Single',true); ?></a> </span>
                                </h3>
                                <div class="">
                                    <?php echo $this->element('products/mutli_item_barcode'); ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>


                    <!-- start of the bundle section-->
                    <?php if($enable_bundles && $you_sell == settings::OPTION_SOLD_TYPE_BUNDLES ) { ?>
                        <div class="col-sm-12">
                            <div class="input-fields inside-form-box">
                                <h3 class="rounded-item  head-bar theme-color-a">
                                <span class="contact-info">
                                    <?= __("Bundle Items"); ?>
                                </span>
                                </h3>
                                <div class="row">
                                    <div class="col-md-6">
                                        <?php echo $form->input('raw_store_id', array('empty' => __('Please Select', true),'options' => $stores_list, 'label' => __("Raw Warehouse", true) , 'class' => 'form-control auto-width', 'div' => 'form-group text col-md-6')); ?>
                                            <div class="col-md-6">
                                                <?php echo $form->input('bundle_type', array('label' =>  __('Bundle Type', true) . '<span class="tooltip" title="product-bundle-type"></span>', 'default' => $default_bundle_type,'class' => 'INPUT form-control', 'div' => 'form-group', 'options' => $bundle_types, 'div' => 'form-group')); ?>
                                            </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <?= $this->element('product_bundles');?>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    <?php } ?>
                    <div class="col-sm-12">
                        <?php if (ifPluginActive(InventoryPlugin) and $you_sell!="2") {

                            $show_track_stock = true;
                        } else {
                            $show_track_stock = false;
                        }
                        $options = array('label' => __('Track Stock', true), 'type' => 'checkbox', 'div' => 'clip-check check-info subactions-check');
                        $options['disabled'] = (isset($disable_track_stock) && $disable_track_stock ? 'disabled' : false);

                        if ($show_track_stock) {
                            ?>

                            <div class="clearfix"></div>
                            <div class="col-sm-12z">
                                <div class="input-fields inside-form-box">
                                    <h3 class="rounded-item  head-bar theme-color-a" style="<?php if ($isBox) { echo 'padding: 15px;margin-bottom: 15px;'; } ?>">
                                            <span class="contact-info">
                                                <?php __("Inventory Management") ?>
                                            </span>
                                    </h3>
                                    <div class="row">

                                        <div class="col-md-12">
                                            <div class="form-row-flex">
                                                <div class="col-flex-lg-6">
                                                    <div class="checkbox-container-s2020 mb-3">
                                                        <?php echo $form->input('track_stock', $options); ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--                        <div class="col-md-12"> --><?php ////echo $form->input('track_stock', $options);    ?><!-- </div>-->

                                            <div class="wellz form-row" id="initial_stock_level_div">
                                                <?php if (ifPluginActive(InventoryPlugin)) { ?>
                                                <?php if (ifPluginActive(PRODUCT_TRACKING_PLUGIN) && isset($tracking_types) && isset($tracking_type) && !$isBox) {
                                                echo $form->input('tracking_type', array('options' => $tracking_types, 'value'=>$tracking_type ,'label' => __("Tracking Type", true) , 'class' => 'form-control auto-width', 'div' => 'form-group text col-sm-6'));
                                                } ?>
                                                <?php if (!$enable_requisitions  && !$enable_requisitions_po && (!isset($product_stock_count) || $product_stock_count == 0) && $disable_track_stock === null) { ?>
                                                <div class="col-sm-6">
                                                    <div class="input-group-flex-inputs">
                                                        <?php
                                                            if ( count ( $stores_list ) > 1 ){
                                                            echo $form->input('store_id', array('options' => $stores_list, 'label' => __("Warehouse", true),'value' => $primary_store , 'class' => 'form-control auto-width', 'div' => 'form-group text col-sm-7'));
                                                            } else {
                                                            echo $form->input('store_id', ['type' => 'hidden' , 'value' => $primary_store]);
                                                            } ?>
                                                            <div class="form-group text col-sm-5 col-input-m-0" style="<?php echo (isset($extra_style) ? $extra_style : "")?>">
                                                                <label for="ProductInitialStockLevel"><?php echo  __('Initial Stock Level', true) . ' <span class="tooltip" title="product-inital-stock"></span>'; ?></label>
                                                                <div class="input-group w-100">
                                                                    <?php echo $form->input('initial_stock_level', array('label' => false, 'type' => 'number', 'class' => 'form-control', 'div' => false)); ?>
                                                                    <?php if (ifPluginActive(InventoryPlugin) && $enable_multi_units){?>
                                                                        <div class="input-group-btn"  >
                                                                            <?php echo $form->input('stock_factor_id', array('label' => false , 'id' => 'stock_factor' ,'type' => 'select',  'options' => NULL, 'empty' => sprintf(__t('Select %s'), __t('Factor')),'class' => 'factor_select', 'div' => false));?>
                                                                        </div>
                                                                    <?php } ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php } ?>
                                                <?php echo $form->input('low_stock_thershold', array('label' => __('Low Stock Threshold', true) . ' <span class="tooltip" title="product-threshold"></span>' ,'type' => 'number', 'class' => 'form-control', 'div' => array('class' => 'form-group col-sm-6'))); ?>
                                                <?php } ?>
                                                <div class="clearfix"></div>
                                            </div>


                                            <div class="clearfix"></div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                        ?>

                        <div class="clearfix"></div>
                    </div>
                    <div class="col-sm-12">
                        <div class="input-fields inside-form-box">
                                <h3 class="rounded-item  head-bar theme-color-a" style="<?php if ($isBox) { echo 'padding: 15px;margin-bottom: 15px;'; } ?>">
                                    <span class="contact-info">
                                        <?php __("More Details") ?>
                                    </span>
                                </h3>
                            <div class="row">
                                <div class="col-sm-6"> <?php echo $form->input('notes', array('label' => __('Internal Notes', true), 'class' => 'INPUT  form-control', 'rows' => '2', 'div' => 'form-group col-input-m-0')); ?></div>
                                <?php if(IS_PC && !$isBox){ ?>
                                    <div class="col-sm-6 tag_inputz">
                                        <label class="control-label"><?php echo __('Tags', true); ?></label>
                                        <?php echo $this->element('tag_input',array('include_js' => true,'item_id' => $dataProductId,'model_name' => 'Product')) ?>
                                    </div>
                                    <div class="clearfix"></div>
                                <?php } ?>
                                <?php if (!$isBox) { ?>
                                    <!-- <div class="form-row-flex"> -->
                                        <div class="col-sm-6">
                                                <?php echo $form->input('status', array('class' => 'INPUT form-control', 'div' => 'form-group', 'options' => $statusList, 'div' => 'form-group')); ?>
                                        </div>
                                    <!-- </div> -->
                                    <div class="clearfix"></div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

                    <!--div class="col-md-6"><?php echo $form->input('default_quantity', array('class' => 'INPUT numeric  form-control', 'between' => '', 'type' => 'number', 'div' => 'form-group hint-fix')); ?></div-->

                <div class="clearfix"></div>
                <?
                //if($you_sell!=settings::OPTION_SOLD_TYPE_SERVICES){
                ?>
                <?//}?>
                <div class="clearfix"></div>
<!--                    <div class="col-sm-6">--><?php //echo $form->input('category', array('label' => __('Category', true), 'class' => 'INPUT   form-control', 'between' => '', 'type' => 'text', 'div' => 'form-group hint-fix')); ?><!--</div>-->

            </div>

            <div class="clearfix"></div>



        <div class="submit-btn m-t">
        </div>
        <div <?php if ($isBox) { echo 'style="width: 98%; margin: 0 auto;"'; } ?>>
             <?php echo $this->element('custom_forms/custom_form'); ?>
        </div>
        <!-- end of the bundle section-->

        <div class="row">
            <div class="col-lg-12">
                <div id="insert-here">
                    <?php echo $this->element('custom_forms/le_forms', ['forms'=> $forms]); ?>
                </div>
            </div>
        </div>

<div class="clear"></div>



        <div class="pages-head-s2020">
            <div class="container">
                <div class="row-flex align-items-center">
                    <div class="col-flex-sm-6">
                    </div>
                    <div class="col-flex-sm-6 d-flex justify-content-end">
                        <?php if (!$isBox) { ?>
                            <a href="<?php echo Router::url(array('controller' => $controller = 'products', 'action' => 'index')) ?>" class="btn s2020 btn-icn btn-secondary font-weight-medium ml-2 mt-0">
                                <i class="mdi mdi-close fs-20"></i>
                                <span><?php __("Cancel") ?></span>
                            </a>
                        <?php } else { ?>
                            <a onclick="top.CloseProducts();" href="#" class="btn s2020 btn-icn btn-secondary font-weight-medium ml-2 mt-0">
                                <i class="mdi mdi-close fs-20"></i>
                                <span><?php __("Cancel") ?></span>
                            </a>
                        <?php } ?>
                        <div class="btn-group">
                            <button type="submit" class="btn s2020 btn-icn btn-success font-weight-medium ml-2" id="submit-btn">
                                <i class="mdi mdi-content-save-outline fs-20"></i>
                                <span><?php echo empty($this->data['Product']['id']) ? __('Save', true) : __('Update', true) ?></span>
                            </button>
                            <input type="hidden" name="next_action"  id="sbt_btn" value="go_index" />
                            <?php if (!$isBox) : ?>
                                <button type="button" class="btn btn-success font-weight-medium" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <span class="caret"></span>
                                    <span class="sr-only"><?php __('More Options') ?></span>
                                </button>
                            <? endif; ?>
                            <ul class="dropdown-menu">
                                <li><a id="add_other" href="#"><i class="fa fa-check"></i> <?php echo empty($this->data['Product']['id']) ? __('Save', true) : __('Update', true) ?> &amp; <?php __('Add New') ?></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php echo $form->end(); ?></div>
    <?php
    if ($isBox) {
        echo $javascript->link('functions');
        $html->css(array('buttons', 'new-product'), false, ['inline' => false]);
    }
    ?>

<!-- Product Item Barcode -->
<script>
    var showItemBarcodeBtn = $(".show-item-barcodes");
    var itemBarcodesSection = $(".multi-item-barcodes-section");
    itemBarcodesSection.hide();
    itemBarcodesSection.find(':input').each(function () {
        $(this).attr('disabled', 'disabled');
    });

    function showBarcodeSection(evt) {
        if (evt) {
            evt.preventDefault();
        }

        itemBarcodesSection.show();
        showItemBarcodeBtn.hide();
        itemBarcodesSection.find(':input').each(function () {
            $(this).removeAttr('disabled');
            $(this).removeAttr('style');
        });
    }

    function hideBarcodeSection(evt) {
        if (evt) {
            evt.preventDefault();
        }

        itemBarcodesSection.hide();
        showItemBarcodeBtn.show();
        itemBarcodesSection.find(':input').each(function () {
            $(this).attr('disabled', 'disabled');
        });
    }

    <?php if (!empty($this->data['ItemBarcode'])) { ?>
        showBarcodeSection();
    <?php } ?>
</script>
    <style>
        #ProductProductCode, #ProductName{
            direction: ltr;
        }
        [dir="rtl"] #ProductProductCode, [dir="rtl"] #ProductName{
            text-align: right;
        }
        [dir="rtl"] #ProductName{
            direction: rtl;
        }
        .multi-item-barcodes-section label {
            display: none;
        }
        .multi-item-barcodes-section .barcode-sec .fa-barcode {
            top: 10px;
            left: 10px;
        }
        .multi-item-barcodes-section .generate_code {
            top: 0;
        }
    </style>

    <script type="text/javascript">
        <?php if ( $advanced_pricing_options ){?>
            $("#ProductProfitMargin,#ProductBuyPrice").on("change keyup",function() {
                buy_price = parseFloat($("#ProductBuyPrice").val())
                profit_margin = parseFloat($("#ProductProfitMargin").val())
                if ( !isNaN(buy_price) && !isNaN(profit_margin) )
                {
                    sell_price= buy_price + (buy_price * profit_margin / 100 )
                    $("#ProductUnitPrice").val(sell_price)
                }
            })
        <?php } ?>
        <?php if ( $enable_multi_units &&!empty ( $factors_list ) ){?>

        var factors = <?php echo json_encode($factors_list)?>

        var first_buy = 1 ;
        var first_pur = 1 ;
        $("#unit_template").change(function () {

            handle_unit_template_change()
        });
        function handle_unit_template_change(){
            if ($("#unit_template").val() == "") {
                $(".factor_select").hide();
            }
            if ($('#ProductTrackingType').val() === 'serial') {
                $(".factor_select").hide();
            }
            else if ($('#ProductTrackingType').val() !== 'quantity_only') {
                $("#buy_factor").show();
                $("#retail_factor").show();
            } else {
                $(".factor_select").show();
            }
            if ( typeof factors[$("#unit_template").val() ] !== "undefined" ){
                current_factors = factors[$("#unit_template").val() ];
                text_str_buy = "";
                text_str_purchase = "";
                $.each (current_factors ,  function (key,val){
                    text_str_buy += "<option factor='"+val.factor+"' value='"+val.id+"' ";
                    <?php // warning suppress ?>
                    if ( first_buy == 1 &&  val.id == '<?php echo $this->data['Product']['default_buy_factor_id'] ?? '' ?>' ){
                        text_str_buy += "selected='selected'";
                        // first_buy = 2 ;
                    }
                    text_str_buy +=">"+(val.small_name?val.small_name:val.factor_name)+"</option>";

                    text_str_purchase += "<option factor='"+val.factor+"' value='"+val.id+"' ";
                    if ( first_pur == 1 && val.id == '<?php echo $this->data['Product']['default_retail_factor_id'] ?? '' ?>' ){
                        // first_pur = 2 ;
                        text_str_purchase += "selected='selected'";
                    }
                    text_str_purchase +=">"+(val.small_name?val.small_name:val.factor_name)+"</option>";
                })
                $("#buy_factor").html(text_str_buy);
                $("#retail_factor").html(text_str_purchase);
                $(".item_barcode_factor_unit").html(text_str_purchase);

                $(".item_barcode_factor_unit").each(function () {
                   if ($(this).attr('data-value')) {
                       $(this).val($(this).attr('data-value'));
                   }
                });

                if ($("#ProductTrackingType").val() !== "quantity_only") return;
                $("#stock_factor").html(text_str_purchase);
            }
        }
        $(window).load(function () {
            $("#unit_template").change();
            handle_unit_template_change();
        });
        <?php }?>
function resizeItem(item){
	$(item).parents('.itemRow').find('.item-name a.select-arrow').height($(item).height());
}


        $('.onfocus').focus(
                function() {
                    $(this).parent('div').addClass('onfocusW');
                }).blur(
                function() {
                    $(this).parent('div').removeClass('onfocusW');
                });



        var brands = <?php echo json_encode($brands_list); ?>;

        var categories = <?php echo json_encode($categories_list); ?>;
<?
if(count($categories_values)==0){
?>
        var categories_values = [];
<?
}else{
?>
        var categories_values = <?php echo json_encode($categories_values); ?>;
<?
}
?>
        var tags = <?php echo json_encode($tags_list); ?>;
        var have_tags = <?php echo !empty($this->data['Product']['tags']) ? $this->data['Product']['tags'] : 'null'; ?>;
        $('document').ready(function() {

            $('#ProductSupplierId').change(function() {
  if($(this).val()!=''){
      $('#ProductProductCodeDiv').show();
  }else{
      $('#ProductProductCodeDiv').hide();
  }
});
        <?php if(ifPluginActive(PRODUCT_TRACKING_PLUGIN)){
        $TemplateNotAllowedForChosenProductTrackingTypeMessage = __('You cannot select unit template for a product already has a tracking type',true);
        ?>
        $('#ProductTrackingType').change(function(e){
            if (null == this.value) return;

            // hide Product Item Barcodes div
            if (this.value === 'serial') {
                hideBarcodeSection();
                showItemBarcodeBtn.hide();
            } else {
                <?php if(ifPluginActive(InventoryPlugin) && $enable_multi_units) { ?>
                showItemBarcodeBtn.show();
                $(".factor_select").show();
                <?php } ?>
            }

            if (this.value !== 'quantity_only') {
                $('#ProductInitialStockLevel').hide().attr('disabled', 'disabled');
                $('label[for="ProductInitialStockLevel"]').hide().attr('disabled', 'disabled');
                $('label[for="ProductInitialStockLevel"]').parent().parent().parent().hide();
                $('#ProductInitialStockLevel').hide();
                $("#virtual_stock").hide();
                $("#stock_factor").hide().attr('disabled', 'disabled');
            } else {
                $('#ProductInitialStockLevel').show().removeAttr('disabled');
                $('label[for="ProductInitialStockLevel"]').show().removeAttr('disabled');
                $('label[for="ProductInitialStockLevel"]').parent().parent().parent().show();
                $("#stock_factor").show().removeAttr('disabled');
                if ($('#ProductInitialStockLevel').is(":visible")) {
                    $("#virtual_stock").hide();
                } else {
                    $("#virtual_stock").show();
                }

            }

            if(this.value === 'serial'){
                if($('#unit_template').val() !== ''){
                $('#unit_template_validation').html('<?php echo $TemplateNotAllowedForChosenProductTrackingTypeMessage; ?>');
                $('#unit_template_validation').removeClass('hidden');}
                $('#unit_template').val('');
                $('#unit_template').change();
            } else{
                $('#unit_template_validation').addClass('hidden');
            }
        });
        $(function()
        {
            $('#ProductTrackingType').change();
        })
        $('#unit_template').change(function(e){
            if (null == $('#ProductTrackingType').val()) return;
            if($('#ProductTrackingType').val() === 'serial'){
                if($('#unit_template').val() !== ''){
                    $('#unit_template_validation').html('<?php echo $TemplateNotAllowedForChosenProductTrackingTypeMessage; ?>');
                    $('#unit_template_validation').removeClass('hidden');
                }
                $('#unit_template').val('');
                handle_unit_template_change()
            }
        })

<?php } ?>
$('textarea.resizable').elastic({
		callback:resizeItem
	});
            $('body').find('.remove-item').live('click', function() {
                let elementWillBeRemoved = $(this).parent();
                let groupPriceId = elementWillBeRemoved.find('.group-price-input').attr('group_price_id');
                let groupPriceName = elementWillBeRemoved.find('.group-price-name').text();
                let clonedPriceGroupSelectItem = $('#price-list-select-item-clone').clone(true);
                clonedPriceGroupSelectItem.find('.li').attr('group_price_id', groupPriceId).text(groupPriceName);
                clonedPriceGroupSelectItem.removeAttr('hidden').removeAttr('id');
                elementWillBeRemoved.remove();
                $('#add-price-list-items').append(clonedPriceGroupSelectItem);
            });

            $('body').find('.add-price-list-item').live('click', function(e) {
                e.preventDefault();
                let priceGroupName = $(this).text();
                let priceGroupId = $(this).attr('group_price_id');
                $(this).remove();
                let clonedPriceGroup = $('#group-price-div-clone').clone(true);
                clonedPriceGroup.find('.group-price-name').text(priceGroupName);
                clonedPriceGroup.find('.group-price-input').attr('group_price_id', priceGroupId)
                    .attr('name', "data[priceList][" + priceGroupId + "]");
                clonedPriceGroup.removeAttr('hidden').removeAttr('id');
                $('#assigned-price-lists').append(clonedPriceGroup).change();
            });

        });
        //]]></script>
<?php else: ?>
    <script type="text/javascript">//<![CDATA[

        <?php $product['Product']['stock_balance'] = (float)$product['Product']['stock_balance']; ?>
        <?php
                if ( !empty ( $product['Product']['unit_template_id']) ){
                        $unitFactor = GetObjectOrLoadModel('UnitFactor');
                        $unitTemplateObj = GetObjectOrLoadModel('UnitTemplate');
                        $unitTemplate = $unitTemplateObj->find('first' , [ 'recursive'=>-1, 'conditions' => ['id' =>$product['Product']['unit_template_id'] ] ]);   
                        $factors = $unitFactor->find('all' , [ 'recursive'=>-1, 'conditions' => ['unit_template_id' =>$product['Product']['unit_template_id'] ] ]);
                        $unit_factors[$product['Product']['unit_template_id']][] = [
                            'id' => 0 ,
                            'small_name' => ($unitTemplate['UnitTemplate']['unit_small_name']? $unitTemplate['UnitTemplate']['unit_small_name']: "" ),
                            'factor_name' =>$unitTemplate['UnitTemplate']['main_unit_name'],
                            'factor' => 1];
                        foreach ( $factors as $f ) {
                            $unit_factors[$product['Product']['unit_template_id']][] = $f['UnitFactor'] ;
                        }
                        
                        $product['Product']['unit_factors'] = $unit_factors[$product['Product']['unit_template_id']];
                }
        ?>
        top.addProduct(<?php echo json_encode($product['Product']) ?>);
        top.CloseProducts();
        //]]></script>
<?php endif; ?>

<?php if (!empty($this->params['url']['close-modal'])) { ?>
    <script>
        top.CloseProducts();
    </script>
<?php }?>
<?php if (!empty($dataProductId)) { ?>
    <script>
        window.onload = function () {
            let current_buy_factor = $('#buy_factor').find(":selected").attr('factor');
            let current_retail_factor = $('#retail_factor').find(":selected").attr('factor');
            let product_buy_price = $("#ProductBuyPrice");
            let product_buy_price_hidden = $("#ProductBuyPriceHidden");
            let product_retail_price = $("#ProductUnitPrice");
            let product_retail_price_hidden = $("#ProductUnitPriceHidden");

            $("#buy_factor").on('change', function () {
                let new_factor = getNewFactor($(this))
                calculateNewPrice(current_buy_factor, new_factor, product_buy_price, product_buy_price_hidden);
                current_buy_factor = new_factor;
            });
            $("#retail_factor").on('change', function () {
                let new_factor = getNewFactor($(this))
                calculateNewPrice(current_retail_factor, new_factor, product_retail_price, product_retail_price_hidden);
                current_retail_factor = new_factor;
            });

            // Functions
            function getNewFactor (element) {
                return element.find(":selected").attr('factor');
            }
            function calculateNewPrice (current_factor, new_factor, input, hiddenInput) {
                let percentage = new_factor / current_factor;
                let value = 0;
                if (hiddenInput.val() !== "") {
                    value = hiddenInput.val() * percentage;
                } else {
                    value = input.val() * percentage;
                }
                hiddenInput.val(value);
                input.val(Math.round(value * 10000000) / 10000000);
            }
        }
    </script>
<?php }?>
<div id="group-price-div-clone" class="col-sm-12" hidden>
    <div class="col-input-m-0">
        <div class="form-group has-feedback">
            <label class="control-label group-price-name" for="">112</label>
            <input name="data[priceList][group_price_id]" group_price_id = "group_price_id" type="number" step="any"
                   value="" class="INPUT form-control onfocus group-price-input"
                   placeholder="use default price" id="ProductPrice1Price" min="0.001">

            <a title="Remove This Product From Price List" href="javascript:void(0)" ref_id="1" class="form-control-feedback remove-item rmbtn">
                <i class="fa fa-times-circle"></i>
            </a>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<li id = "price-list-select-item-clone" hidden><a class="add-price-list-item li" group_price_id="group_price_id" href="#">TEXT</a></li>
<script type="text/javascript" src="/js/jquery.elastic.js"></script>
<?php echo $javascript->link(['jquery.scannerdetection.js']); ?>
<script>
    $(document).scannerDetection({
        onComplete(code) {
            let barcode = code;
            const isDataMatrix = /^01\d{14}/.test(code) && code.length > 32 && code.includes(17) && code.includes(10) && code.includes(21);
            if (isDataMatrix) {
                barcode = code.substr(2, 14);
            }
            $('#ProductBarcode').val(barcode);
        }
    });
    if(typeof tags !== 'undefined') {
        var ProductTags = $('#tag-input-id').magicSuggest({
        placeholder: '',
        strictSuggest: true,
        useTabKey: true,
        maxSelectionRenderer: function(v) {
            return ''
        },
        data: tags,
        value: have_tags,
        useCommaKey: false,
    });
    }

</script>
<script>
    $(document).ready(function(){
        var defaultAttributeOptions = <?= json_encode($this->data['ProductAttributeOption']) ?? []; ?>;
        var attributes = [
            {name:'' , options: []},
            {name:'' , options: []},
            {name:'' , options: []},
        ];
        attributes.forEach((attribute,index)=>{
            if (!defaultAttributeOptions || !defaultAttributeOptions.length) return;
            if (defaultAttributeOptions[index]){
                if(defaultAttributeOptions[index].other_options){
                    attribute.options = defaultAttributeOptions[index]?.other_options.map((option)=>{
                        return option.value
                    })
                }else{
                    attribute.options.push( defaultAttributeOptions[index].option)
                }

            }
        })
        $('[data-select-product-attribute]').on('change',function(){
            var $rowTemplateDataset = $(this)[0].dataset;
            var $rowTemplateDatasetRow = $rowTemplateDataset.attributeSelectRow;
            attributes[$rowTemplateDatasetRow].name = $(this).val();
            var $rowOptionsSelect = $(`[data-attribute-option-row="${$rowTemplateDatasetRow}"]`)
            $rowOptionsSelect[0].selectize.settings.create= true;
            initializeOptionsSelect($rowOptionsSelect[0],$rowTemplateDatasetRow)
        })

        var selectedValue = [];
        var evaluateNewName = function (oldName){
            if (oldName.includes('-')){
                oldName = oldName.split("-")[0]
            }
            var newName = oldName +  "-";
            selectedValue.forEach((value , index)=>{
                if (index === 0){
                    newName  += value
                }else{
                    newName +='/' + value
                }
            })
            return newName
        }

        $('[data-select-product-attribute-option]').on('change',function(){
            var productNameInput = $('#ProductName');
            var oldName = productNameInput.val();
            var attributeOptionRow = $(this)[0].dataset.attributeOptionRow;
            selectedValue[attributeOptionRow] = $(this).val();
            $(this)[0].selectize.settings.create= true;
            setSelectizeOptions($(this)[0],attributes[attributeOptionRow].options)
            var newName = evaluateNewName(oldName);
            productNameInput.val(newName)
        })

        var initializeOptionsSelect = function (input,$rowTemplateDatasetRow){
            input.selectize.clearOptions();
            $.ajax({
                url: "/owner/item_group/get_attribute_options?attribute=" + attributes[$rowTemplateDatasetRow].name +"&q=",
                type: "GET",
                success: function(res) {
                   if (res && res.length){
                       var optionsArray = JSON.parse(res)
                       setSelectizeOptions(input,optionsArray)
                   }
                },
            });
        }

        var setSelectizeOptions = function (input, options){
            for (i = 0; i <= options.length - 1 ; i++) {
                var value = options[i].name || options[i]
                input.selectize.addOption({id:value,name:value});
            }
            console.log(options)
        }

        setTimeout(()=>{
            $('[data-select-product-attribute]').each((index, input)=>{
                input.selectize.settings.create=true
            })
        },800)
    });

</script>
<style>
@media (max-width: 768px) {
    .overlay-box {
        max-width: 100%;
        left: 0 !important;
        margin: 0 !important;
    }
    .overlay-box iframe {
        max-width: 100%;
    }
}
</style>

<script src="<?= CDN_ASSETS_URL ?>js/forms/plugin_select2.js"></script>
<link rel="stylesheet" href="<?php echo CDN_ASSETS_URL ?>css/forms/plugin_select2.min.css?v=<?= CSS_VERSION ?>" />
<link rel="stylesheet" href="<?php echo CDN_ASSETS_URL ?>css/forms/plugin_select2_rtl.min.css?v=<?= CSS_VERSION ?>" />
<script>
    var supplierSelect2Options = {
        theme: 'bootstrap4',
        width: '100%',
        placeholder: function () {
            return $(this).data('placeholder');
        },
    };
    if ($('html').is(':lang(ara)')) {
        supplierSelect2Options['language'] = "ar";
    }
    $(document).ready(function () {
        $('#ProductSupplierId').select2(supplierSelect2Options)
    })
</script>

<script>

    var salesTax1Label = "<?= __t("Sales Tax") ." 1"?>";
    var salesTax2Label = "<?= __t("Sales Tax") ." 2" ?>";


    function togglePurchaseTaxesColumnVisibility() {
        $('.purchase_taxes_column').toggleClass('d-none');
    }

    function renameLabels() {
        $('#tax1-name-span').text(salesTax1Label);
        $('#sales-tax-2 label').text(salesTax2Label)
    }

    function advancedClickHandler() {
        togglePurchaseTaxesColumnVisibility()
        renameLabels();
        $('.toggle_taxes_span').addClass('d-none')
    }
    
    $('.toggle_taxes_span').click(advancedClickHandler)

    if ($('#ProductPurchasingTax2').val() || $('#ProductPurchasingTax1').val()){
        advancedClickHandler()
    }
</script>