<?php

use Izam\Daftra\Common\Utils\ProductStatusUtil;

 echo $html->css(CDN_ASSETS_URL ."s2020/css/index/index.min.css?v=".CSS_VERSION, null, []); ?>
        <?php if ($you_sell == settings::OPTION_SOLD_TYPE_SERVICES) { ?>
               <?php $title_bread =  h(__('Services', true)); ?>
        <?php } else if ($you_sell == settings::OPTION_SOLD_TYPE_PRODUCTS) { ?>

                <?php $title_bread =  h(__('Products', true)); ?>

        <?php } else { ?>

             <?php $title_bread =  h(__('Products & Services', true)); ?>

        <?php } ?>


<?php echo $html->css(array('jquery-ui-timepicker-addon','jquery.funkytooltips'), false, ['inline' => false]);
echo $javascript->link(array('jquery-ui-timepicker-addon'));
echo $html->css(array('bootstrap-select_v'.CSS_VERSION.'.css'), false, ['inline' => false]);
echo $javascript->link(array('bootstrap-select.js'));
	echo $javascript->link(array('magicsuggest-min.js'));
echo $html->css(array( 'magicsuggest-min.css'));
$json_templates_array = [];
// warning suppress
$getParamType = $_GET['type'] ?? null;
$is_multi_select = IS_MOBILE ? false : true;
if (isset($printableTemplates))
foreach($printableTemplates as $template)
{
    $json_templates_array[] = [
        "url" =>"/owner/printable_templates/multi_pdfs/product/" . $template['PrintableTemplate']['id'],
        "template_name" =>  $template['PrintableTemplate']['name']
    ];

}

?>
<div class="products index">
    <div class="pages-head py-0 py-lg-2">
        <div class="container px-md-1 px-0">
		<div class="d-flex justify-content-between">

<div class="actions-selections-head">
    <div class="top-bar-row">
        <?php echo $this->element('top_bar_actions_touch', array('is_multi_select'=> $is_multi_select )) ;?>
        <?php echo $this->element('top_bar_nav_touch') ?>
    </div>
</div>

    <div class="top-actions">

   


<?php if ($you_sell == settings::OPTION_SOLD_TYPE_SERVICES && !$enable_bundles) { ?>
    <?php if(check_permission(Proudcts_Add_New_Proudct)):?>
        <a class="add-new-btn  btn btn-touch btn-success btn-addon button right btn-lg" href="<?php echo Router::url(array('action' => 'add',2)) ?>"><i class="fa fa-plus"></i><span><?php echo $you_sell == settings::OPTION_SOLD_TYPE_SERVICES ? __('New Service',true) : __('New Product',true) ?></span></a>
    <?php endif; ?>
  <?php } else if ($you_sell == settings::OPTION_SOLD_TYPE_PRODUCTS && !$enable_bundles) { ?>
        <?php if(check_permission(Proudcts_Add_New_Proudct)):?>
            <a class="add-new-btn btn-touch  btn btn-touch btn-success btn-addon button right btn-lg" href="<?php echo Router::url(array('action' => 'add',1)) ?>"><i class="fa fa-plus"></i><span><?php echo $you_sell == settings::OPTION_SOLD_TYPE_SERVICES ? __('New Service',true) : __('New Product',true) ?></span></a>
        <?php endif; ?>
        <a class=" btn btn-touch btn-info button right btn-lg" href="/v2/owner/entity/<?=\Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY?>/list?reset=1"><span><?= __('Item Groups') ?></span></a>
  <?php } else { ?>

      <div class="pull-right">


          <div class="btn-group ml-lg-1">
              <a class="d-none d-sm-block btn btn-touch btn-info mr-1 button right btn-lg" href="/v2/owner/entity/<?=\Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY?>/list?reset=1"><span><?= __('Item Groups') ?></span></a>
              <a class="d-block d-sm-none btn btn-touch btn-info btn-addon add-new-btn btn-touch mr-lg-1 button right btn-lg" href="/v2/owner/entity/<?=\Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY?>/list?reset=1"><i class="mdi mdi-group"></i><span><?= __('Item Groups') ?></span></a>
              <?php if(check_permission(Proudcts_Add_New_Proudct)):?>
                      <button type="button" class="add-new-btn btn-touch  btn btn-touch btn-success btn-addon width-auto btn-lg  button right" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                   <i class="fa fa-plus"></i> <? echo __('Add') ?>
        </button><?php endif;?>
        <?php if(check_permission(Proudcts_Add_New_Proudct)):?>
        <button type="button" class="btn btn-touch btn-success dropdown-toggle btn-lg <?php if(!$is_rtl) echo  'pad-r-16' ?>" data-toggle="dropdown" aria-expanded="false">
            <span class="caret"></span>
            <span class="sr-only">Toggle Dropdown</span>
           </button>

         <ul class="dropdown-menu pull-right">
              <?php if (in_array($you_sell , [settings::OPTION_SOLD_TYPE_PRODUCTS,settings::OPTION_SOLD_TYPE_BOTH] ) ) {?>
                                   <li>
                  <a href="<? echo Router::url(array('action'=>'add',Product::PRODUCT_TYPE)) ?>"><? echo __('New Product',true) ?></a>
                                     </li>
              <?php } ?>
              <?php if (in_array($you_sell , [settings::OPTION_SOLD_TYPE_SERVICES,settings::OPTION_SOLD_TYPE_BOTH] ) ) {?>
                                <li>
                  <a href="<? echo Router::url(array('action'=>'add',Product::SERVICE_TYPE)) ?>"><? echo __('New Service',true) ?></a>
                                     </li>
              <?php } ?>
                                     <?php if ( $enable_bundles ){ ?>
                                     <li>
                  <a href="<? echo Router::url(array('action'=>'add',Product::BUNDLE_TYPE)) ?>"><? echo __('New Bundle',true) ?></a>
                                     </li>
                                     <?php } ?>
                            </ul>
        <?php endif;?>
    </div>

    
</div>
    <a title="<?php __('Categories') ?>" href="<?= Router::url(['controller'=>'categories' , 'action'=>'index',1]); ?>"  class="d-block d-sm-none btn btn-touch  mr-lg-1 button right btn-lg btn-default"> <i class="fa fa-cog"></i> </a>
    <a href="<?php echo Router::url(array('action' => 'import')) ?>" title="Import" class="d-block d-sm-none btn btn-touch  mr-lg-1 button right btn-lg btn-default" tabindex="1"> <i class="fa fa-cloud-upload"></i> <?php echo __('Import') ?></a>

      <?php
  }
  ?>
  <?
    if (IS_PC) {
        ?>
        <a title="<?php __('Categories') ?>" href="<?= Router::url(['controller'=>'categories' , 'action'=>'index',1]); ?>"  class="btn  btn-lg btn-default right"> <i class="fa fa-cog"></i> </a>
        <a href="<?php echo Router::url(array('action' => 'import')) ?>" title="Import" class="btn  btn-lg btn-default right none-view" tabindex="1"> <i class="fa fa-cloud-upload"></i> <?php echo __('Import') ?></a>
        <?
    }
  ?>

        <?php echo $this->element('search_btn_touch'); ?>
</div>
  



	  </div>
	  </div>
    </div>

    <div class="page-snippets">
        <?php echo $content ?>
    </div>
    <?php
    $i = 0 ;
    // warning suppress
    if (isset($fields))
	foreach ( $fields as $v ) {
		$v ['CustomFormField']['is_required'] = 0 ;
		if ( $v ['CustomFormField']['is_filtered'] == 1){
			$filters['field_'.$v['CustomFormField']['id']] = array('div_class' => 'full-width status-list','type' => 'custom_field','more-options' => true ,  'label' => $v['CustomFormField']['label'] , 'myval'=>$v );
		}
                $i++ ;
	}
    if (!empty($products)) {
        foreach ($products as $key => $product) {
            if ($product['Product']['unit_price'] < 0.1) {
                $products[$key]['Product']['unit_price'] = sprintf('%0.5f', $product['Product']['unit_price']);
            } elseif ($product['Product']['unit_price'] < 1) {
                $products[$key]['Product']['unit_price'] = sprintf('%0.2f', $product['Product']['unit_price']);
            } else {
                $products[$key]['Product']['unit_price'] = sprintf('%0.2f', $product['Product']['unit_price']);
            }
        }

		debug ( $filters ) ;
        echo $list->filter_form($modelName, $filters,array(),array(),
				$sortFields
				);

        $query_string = explode ('?', $_SERVER['REQUEST_URI'] )[1];
        $query_string = preg_replace('/type=\d*/','', $query_string);
        ?>

        <div class="panel panel-default no-margin" style="border-bottom: none;">
            <div class="panel-heading flex-root" style="border-bottom: none;">

                <!--        --><?php //if ( ($invoice_count > 0 || $estimate_count > 0 || $work_order_count > 0 ) || ($item_type != 1)){ ?>
                <div  class="d-inline-flex  pull-right pos3">
                    <?php if($counts[Product::PRODUCT_TYPE] > 0) {
                        $newQueryString = $query_string.'&type='.Product::PRODUCT_TYPE;

                        ?>
                        <a class="type_link btn-s2020 btn-secondary-s2020 font-weight-medium border s2020 border-light <?php echo (($getParamType==Product::PRODUCT_TYPE )? "active":""); ?>" title="<? echo __('Products', true) ?>"  href="<?php echo Router::url (['action' => "index" ,'?' => $newQueryString] ) ;?>" ><?php echo __("Products" , true ) . " (".$counts[Product::PRODUCT_TYPE].")";?></a>
                    <?php } ?>


                    <?php if($counts[Product::SERVICE_TYPE] > 0) {
                        $newQueryString = $query_string.'&type='.Product::SERVICE_TYPE;
                        ?>
                        <a class="type_link btn-s2020 btn-secondary-s2020 font-weight-medium border s2020 border-light <?php echo (($getParamType==Product::SERVICE_TYPE )? "active":""); ?>" title="<? echo __('Services', true) ?>"  href="<?php echo Router::url (['action' => "index" ,'?' => $newQueryString] ) ;?>" ><?php echo __("Services" , true ) . " (".$counts[Product::SERVICE_TYPE].")";?></a>
                    <?php } ?>


                    <?php if($counts[Product::BUNDLE_TYPE] > 0) {
                        $newQueryString = $query_string.'&type='.Product::BUNDLE_TYPE;
                        ?>
                        <a class="type_link btn-s2020 btn-secondary-s2020 font-weight-medium border s2020 border-light <?php echo (($getParamType==Product::BUNDLE_TYPE )? "active":""); ?>" title="<? echo __('Bundles', true) ?>"  href="<?php echo Router::url (['action' => "index" ,'?' => $newQueryString] ) ;?>" ><?php echo __("Bundles" , true ) . " (".$counts[Product::BUNDLE_TYPE].")";?></a>
                    <?php } ?>
                    <div class="clear-fix"></div>

                </div>
        </div>
        </div>
    <?php
        $fields = array(
            'Product.id' => array('edit_link' => array('action' => 'view', '%id%')),
            'Product.product_code' => array('edit_link' => array('action' => 'view', '%id%')),
            'Product.name' => array("show_on_resize" => true, 'edit_link' => array('action' => 'view', '%id%')),
            'Product.unit_price' => array('title' => __('Retail Price', true)),
        );
        //$fields['Supplier.business_name']=array('title'=>'Supplier');
        if (ifPluginActive(InventoryPlugin)) {
            $fields['Product.stock_balance'] = array('title' => __('Stock Balance', true));
            $fields['Product.buy_price'] = array('title' => __('Buy Price', true));
        }
        //	$links = array(
        //		$html->link(__('Edit', true), array('action' => 'edit', '%id%'), array('class' => 'Edit')),
        //		$html->link(__('Delete', true), array('action' => 'delete', '%id%'), array('class' => 'Delete')),
        //	);
        $staff_id = getAuthStaff('id');
        if (!check_permission(Edit_Delete_all_Products) && check_permission(Edit_And_delete_his_own_added_Products)) {
            $links[] = array('php_expression' => '$row["Product"]["staff_id"]==' . $staff_id . ';', 'url' => $html->link(__('Edit', true), array('action' => 'edit', '%id%'), array('class' => 'Edit', 'title' => __('Edit', true))));
            $links[] = array('php_expression' => '$row["Product"]["staff_id"]==' . $staff_id . ';', 'url' => $html->link(__('Delete', true), array('action' => 'delete', '%id%'), array('class' => 'Delete', 'title' => __('Delete', true))));
        } elseif (check_permission(Edit_Delete_all_Products)) {
            $links[] = array('url' => $html->link(__('View', true), array('action' => 'view', '%id%'), array('class' => 'View', 'title' => __('View', true))));
            $links[] = array('url' => $html->link(__('Edit', true), array('action' => 'edit', '%id%'), array('class' => 'Edit', 'title' => __('Edit', true))));
            $links[] = array('url' => $html->link(__('Delete', true), array('action' => 'delete', '%id%'), array('class' => 'Delete', 'title' => __('Delete', true))));
        }
        $links[] = $html->link(__('Clone', true), array('action' => 'add', '%type%', '%id%'), array('class' => 'Copy', 'title' => __('Clone', true)));

        if (check_permission(Edit_Delete_all_Products)) {
            $deactivate_data = [
                'entity_name'=> 'product',
                'target_url' => '/owner/products/change_status/__id__/__status__',
                'params' => ['status' => ProductStatusUtil::STATUS_INACTIVE],
            ];
            $activate_data = [
                'entity_name'=> 'product',
                'target_url' => '/owner/products/change_status/__id__/__status__',
                'params' => ['status' => ProductStatusUtil::STATUS_ACTIVE],
            ];
            $suspend_data = [
                'entity_name'=> 'product',
                'target_url' => '/owner/products/change_status/__id__/__status__',
                'params' => ['status' => ProductStatusUtil::STATUS_SUSPENDED],
            ];
    
            $multi_select_actions = array(
                'delete' => array('action' => Router::url(array('action' => 'delete'))),
                'activate' => array('data' => htmlspecialchars(json_encode($activate_data), ENT_QUOTES, 'UTF-8'), 'action' => Router::url(array('controller'=>'bulk', 'action' => 'update')), 'confirm' => true, 'class' => 'GoBulk'),
                'deactivate' => array('data' => htmlspecialchars(json_encode($deactivate_data), ENT_QUOTES, 'UTF-8'), 'action' => Router::url(array('controller'=>'bulk', 'action' => 'update')), 'confirm' => true, 'class' => 'GoBulk'),
                'suspend' => array('data' => htmlspecialchars(json_encode($suspend_data), ENT_QUOTES, 'UTF-8'), 'action' => Router::url(array('controller'=>'bulk', 'action' => 'update')), 'confirm' => true, 'class' => 'GoBulk'),
            );
            if ( settings::getValue(InventoryPlugin,"advanced_pricing_options"))
            {
                $multi_select_actions['update_profit_margin'] = array('title' => __("Update Profit Margin",true),  'action' => Router::url(array('action' => 'update_profit_margin')));
            }
            $multi_select_actions['export'] = array('title'=> __('Export', true), 'action' => Router::url(array('action' => 'export', 'owner' => true, $conditions_key)), 'confirm' => false);
        }

        if(check_permission(View_All_Products) || check_permission(View_his_own_Products)){
            $print_pdf = array('class'=>"multiple_template", 'title'=>__('Print PDF',true),'action' => Router::url(array('controller'=>'printable_templates', 'owner' => true, 'action' => 'multi_pdfs', 'product')), 'confirm' => true);
            if(!empty($multi_select_actions)){
                $reordered_select_options = [];
                foreach ($multi_select_actions as $key => $value) {
                    $reordered_select_options[$key] = $value;

                    if ($key === 'suspend') {
                        $reordered_select_options['print_pdf'] = $print_pdf;
                    }
                }
                $multi_select_actions = $reordered_select_options;
            }
          $multi_select_actions['print_pdf'] = $print_pdf;
        }
        echo $list->adminResponsiveList2($products, 'products/product_row', array('actions' => $links), array('multi_select' => $is_multi_select, 'multi_select_actions' => $multi_select_actions));
    } else if (empty($_GET)) {
        echo $html->div('Notemessage', CakeString::insert(__('No :itemName found, :itemLink',true), array('itemName' => __('products', true), 'itemLink' => $html->link(__('click here to add product', true), array('action' => 'add')))));
    } else {
        echo $list->filter_form($modelName, $filters);
        echo $html->div('Notemessage', CakeString::insert(__('No :itemName found, :itemLink',true), array('itemName' => __('products', true), 'itemLink' => $html->link(__('try to clear the search', true), array('action' => 'index', '?' => ['reset' => 1])))));
    }
    ?>
</div>
	<?php echo $javascript->link(array('tags-view','jquery.funkytooltips')) ; ?>

<?= $this->element('barcode_redirector'); ?>
<style>
    .template_multi_menu{
        margin-right: 40%;
        position: absolute;
        right: -11%;
        top: auto;
        margin-right: 38%;
    }
    .attachment-file img{
        border-radius: 5px;
        background: linear-gradient(to bottom, rgba(128, 128, 128, 0.43), rgba(158, 158, 158, 0.05), rgba(128, 128, 128, 0.43));
        padding: 3px;
    }
</style>

<script>
    var json_links = <?php echo json_encode($json_templates_array); ?>;

//    console.log(json_links);
    //if there is only one template make and do not append the menu
    if(json_links.length == 1)
    {
       $("a[rel='/owner/printable_templates/multi_pdfs/product']").attr('rel', json_links[0]['url'] );
    }
    else
    {
        var a = $("a[rel='/owner/printable_templates/multi_pdfs/product']");

        var button = '<a class="btn btn-md btn-primary multiple_pdfs" type="button" data-toggle="dropdown"><?= __("Print PDF",true); ?></a>';
        $("a[rel='/owner/printable_templates/multi_pdfs/product']").replaceWith(button);

        var select_menu = '<ul class="dropdown-menu template_multi_menu">';

        for(var i = 0 ; i < json_links.length ; i++)
        {
            select_menu += "<li><a class='GoSubmit btn btn-md' rel=" + json_links[i]["url"] + ">" + json_links[i]["template_name"] + "</a></li>";
        }

        select_menu += '</ul>';
        console.log(select_menu);
        $('.multiple_pdfs').after( select_menu );

        $('.template_multi_menu').hide();

        $('.multiple_pdfs').on("click",function(){
            $('.template_multi_menu').toggle();
        });

        console.log(select_menu);
    }
    $(function () {
        $(".attachment-file").funkytooltips({
            is_rtl: <?= $is_rtl ? 'true' : 'false'; ?>
        });
    })
    $('.product-attribute-wrapper select').on('change',function (e) {
        $(this).parent().find('.attribute-hidden-input').removeAttr('disabled')
    })

</script>
