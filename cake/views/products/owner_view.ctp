<?php

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

echo $html->css(CDN_ASSETS_URL . "s2020/css/show/show.min.css?v=" . CSS_VERSION, null, []); ?>
<?php

echo $html->css(['view_new_style.css', 'view_product']);
if (isset($_GET['info_screen']))
    echo $html->css(['info_screen.css?v=3']);


$show_buy_price = check_permission(Add_New_Purchase_Orders);
$can_edit = false;
$priceAfterDiscount = 0;
if (!empty($product['Product']['unit_price']) && !empty($product['Product']['discount']) && !empty($product['Product']['discout_type'])) {
    if ($product['Product']['discout_type'] == Product::DISCOUNT_TYPE_PERCENTAGE) {
        $priceAfterDiscount = $product['Product']['unit_price'] - $product['Product']['unit_price'] * $product['Product']['discount'] / 100;
        $price_extra = "(" . __('Discount', true) . " {$product['Product']['discount']}%)";
    } elseif ($product['Product']['discout_type'] == Product::DISCOUNT_TYPE_VALUE) {
        $price_extra = '';
        $priceAfterDiscount = $product['Product']['unit_price'] - $product['Product']['discount'];
    }
}
if (check_permission(Edit_Delete_all_Products) || check_permission(Edit_And_delete_his_own_added_Products)) {
    $can_edit = true;
}


if ($is_rtl) {
    echo $html->css('view_new_style_ar.css');
}
$col_class = "col-md-4 col-sm-4 col-xs-12";
$track_stock = false;
$tooltip = 'total-sold-no-stock';
if (ifPluginActive(InventoryPlugin) && !empty($product['Product']['track_stock'])) {
    $track_stock = true;
    $col_class = "col-md-3 col-sm-3 col-xs-12";
    $tooltip = 'total-sold-with-stock';
}
if (!empty($product['Product']['average_price']) && $show_buy_price) {
    $col_class = "col-md-2 col-sm-2 col-xs-12";
}
if ($showProductPendingQTY) {
    $col_class = "col-md-2 col-sm-2 col-xs-12";
    echo "<style> .m-t-xs.btn.btn-sm.btn-primary.btn-addon{font-size: 12px; padding: 5px 5px;} </style>";
}
echo $html->css(array('timeline_v' . CSS_VERSION . '.css', 'product_view_v' . CSS_VERSION . '.css', 'time-tracker.css?v=3', 'fontello.css', 'bootstrap-multiselect.css'));

echo $javascript->link(array('bootstrap-multiselect.js', 'product_view_v' . JAVASCRIPT_VERSION . '.js'));


echo $javascript->link(array('jqueryui', 'jquery.qtip.min'));
$html->css(array('jqueryui', 'jquery.qtip.min'), false, ['inline' => false]);
echo $javascript->link(array('owner-view_v' . JAVASCRIPT_VERSION . '.js'));
?>
<?php

$breadcrumbs_title = __("Products", true);
$you_sell = $product['Product']['type'];
if ($you_sell == settings::OPTION_SOLD_TYPE_SERVICES) {
    $breadcrumbs_title = __("Services", true);
} else if ($you_sell == settings::OPTION_SOLD_TYPE_BUNDLES) {
    $breadcrumbs_title = __("Bundles", true);
}
$product_name = $product['Product']['name'] . ' (#' . (empty($product['Product']['product_code']) ? $product['Product']['id'] : $product['Product']['product_code']) . ')';

$relatedItemGroupId = $product['Product']['item_group_id'];
$showItemGroupTab = $relatedItemGroupId > 0;
?>

<?php
if (empty($_GET['is_ajax'])) {
    echo $javascript->link('timeline');
}

if ($product['Product']['status'] == \Izam\Daftra\Common\Utils\ProductStatusUtil::STATUS_INACTIVE) {
    $status = __('Inactive', true);
    $status_class = 'grey';
} elseif ($product['Product']['status'] == \Izam\Daftra\Common\Utils\ProductStatusUtil::STATUS_SUSPENDED) {
    $status = __('Suspended', true);
    $status_class = 'blue';
} else if (empty($product['Product']['track_stock']) || !ifPluginActive(InventoryPlugin)) {
    $status = __('Active', true);
    $status_class = 'green';
} else if (!empty($product['Product']['track_stock']) && round(floatval($stock_level), 6)  <= floatval($product['Product']['low_stock_thershold'])) {
    if (round(floatval($stock_level), 6) <= 0) {
        $status = __('Out of Stock', true);
        $status_class = 'red';
    } else {
        $status = __('Low Stock', true);
        $status_class = 'orange';
    }
} else {
    $status = __('In Stock', true);
    $status_class = 'green';
}
?>
<?php if (isset($_GET['is_ajax'])) { ?>
    <style>
        div#collapse-tabs-0 .panel-heading {
            display: none;
        }

        .tab-content {
            padding: 0;
        }
    </style>
<?php } ?>
<div class="<?php echo isset($_GET['is_ajax']) ? 'bg-white py-2 px-1 mb-2' : 'pages-head-s2020' ?>" style="top: 0;">
    <div class="container pages-head-title">
        <h2 class="left pt-2 no-margin <?php echo isset($_GET['is_ajax']) ? 'fs-18' : '' ?>"><span class="pro_name_span"><?php echo $product['Product']['name'] ?></span> <span class="inline title_id_with_hash"> <?php if(empty($product['Product']['product_code'])):?>#<span dir="ltr"><?php echo $product['Product']['id']; ?></span><?php endif; ?></span>
            <div class="status rate-<?php echo $status_class ?>">
                <span class="status-symble "><?php echo $status ?></span>
            </div>
            <span class="invoice-Product sub-heading"></span>
        </h2>
        <?php if ($can_edit) { ?>
            <div class="top-actions top-actions-w100-mob">
                <div class="mb-opt-btn"></div>
                <div class="pull-right"> <?php if ($products_nav) echo $this->element('nav', array('id' => $product['Product']['id'])); ?> </div>
                <a class="action-global-btn btn-blue w-34 pull-right" href="<?php echo Router::url(array('action' => 'edit', $product['Product']['id'])) ?>">
                    <span><i class="fa fa-pencil"></i>
                        <span class="hidden-xs"> <? __("Edit"); ?> </span>
                    </span>
                </a>
                <div class="clear"></div>
            </div>
        <?php } else { ?>
            <div class="mb-opt-btn"></div>
            <div class="clear"></div>
        <?php } ?>
        <div class="clear"></div>
        <?php echo $this->element('tags_view', array('item_id' => $product['Product']['id'], 'model_name' => 'Product', 'get_ajax' => true)); ?>
    </div>
</div>
<div class="invoice-actions btn-group dropdown-btn-group">
    <?php if ($can_edit) { ?>
        <a href="<?php echo Router::url(array('action' => 'edit', $product['Product']['id'])) ?>" class="btn btn-default btn-sm btn-5 ">
            <i class="fa fa-pencil"></i>
            <?php __("Edit") ?>
        </a>
        <a href="<?php echo Router::url(array('action' => 'delete', $product['Product']['id'])) ?>" class="btn btn-default btn-sm btn-4 ">
            <i class="fa fa-trash-o"></i>
            <?php __("Delete") ?>
        </a>
    <?php } ?>
    <?php if (ifPluginActive(InventoryPlugin)  && $track_stock) { ?>
        <?php if (check_permission(TRANSFER_STOCK_PERMISSION)  && count($store_list) > 1) { ?>
            <?php if ($enable_requisitions) { ?>
                <a class="btn btn-default btn-sm btn-5" href="<?php echo Router::url(array('controller' => 'requisitions', 'action' => 'add', Requisition::ORDER_TYPE_TRANSFER_REQUISITION, '?' => ['product_id' => $product['Product']['id']])) ?>"> <i class="fa fa-recycle"></i> <?php __("Transfer Stock") ?></a>
            <?php } else { ?>
                <a class="btn btn-default btn-sm btn-5" href="<?php echo Router::url(array('action' => 'transfer_stock', $product['Product']['id'])) ?>"> <i class="fa fa-recycle"></i> <?php __("Transfer Stock") ?></a>
        <?php }
        } ?>
        <?php if ($enable_requisitions) { ?>
            <a class="btn btn-default btn-sm btn-5" href="<?php echo Router::url(['controller' => 'requisitions', 'action' => 'add', Requisition::TYPE_INBOUND, '?' => ['product_id' => $product['Product']['id']]]); ?>"><i class="fa fa-plus"></i> <?php __("Add Transaction") ?></a>
            <a class="btn btn-default btn-sm btn-5" href="<?php echo Router::url(['controller' => 'requisitions', 'action' => 'add', Requisition::TYPE_OUTBOUND, '?' => ['product_id' => $product['Product']['id']]]); ?>"><i class="fa fa-minus"></i> <?php __("Deduct Transaction") ?></a>
        <?php } else { ?>
            <a class="btn btn-default btn-sm btn-5" href="/owner/products/manual_stock_adjust/<?php echo $product['Product']['id']; ?>"><i class="fa fa-plus"></i> <?php __("Add Transaction") ?></a>
        <?php } ?>
    <?php } ?>

    <?php if ($has_templates): ?>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-default  dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <? echo __('Voucher', true) ?> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <?php foreach ($printableTemplates as $template) : ?>
                    <li>
                        <?php if ($barcode_count > 0) { ?>
                            <a href="<?php echo Router::url(array('controller' => 'products', 'action' => 'barcodes_printable_templates', $product['Product']['id'], $template['PrintableTemplate']['id'], Inflector::singularize(strtolower($this->name)))) ?>" class=""> <?= $template['PrintableTemplate']['name']; ?></a>
                        <?php  } else { ?>
                            <a href="<?php echo Router::url(array('controller' => 'printable_templates', 'action' => 'view', $product['Product']['id'], $template['PrintableTemplate']['id'], Inflector::singularize(strtolower($this->name)))) ?>" class=""> <?= $template['PrintableTemplate']['name']; ?></a>
                        <?php   } ?>

                    </li>
                <?php endforeach; ?>
                <?php
                echo draw_templates_list($view_templates, $product['Product']['id']);
                ?>
            </ul>
        </div>
    <?php endif; ?>
    <a href="<?php echo Router::url(array('action' => 'add', $product['Product']['type'], $product['Product']['id'])); ?>" class="btn btn-default btn-sm btn-5 "><i class="fa fa-copy"></i> <?php __("Clone") ?> </a>
</div>
<?php
echo $javascript->link(array('owner-view_v' . JAVASCRIPT_VERSION . '.js'));
?>
<div class="tabs-box box">

    <ul class="nav nav-tabs responsive" role="tablist">
        <li class="active current" role="presentation"><a aria-controls="actions-tab" role="tab" data-toggle="tab" href="#ProductBlock" title="<?php __('Information') ?>" id="ViewInvoice"><span class="one-line"><?php __("Information") ?></span></a></li>
        <?php if ($track_stock && (check_permission(Track_Inventory) || check_permission(Adjust_Product_Inventory))) { ?>
            <li role="presentation"><a aria-controls="StockTransactionsBlock" role="tab" data-toggle="tab" onclick="reload_stock()" href="#StockTransactionsBlock" title="<?php __('Stock Transactions') ?>"><span class="one-line"><?php __('Stock Transactions') ?> </span></a></li>
        <?php } ?>
        <?php if ($showActionLineTap) { ?>
            <li role="presentation"><a aria-controls="TimelineBlock" role="tab" data-toggle="tab" onclick="reload_filter()" href="#TimelineBlock" title="<?php __('Timeline for Product') ?>"><span class="one-line"><?php __('Timeline') ?></span></a></li>
        <?php } ?>
        <li role="presentation"><a aria-controls="LaravelTimelineBlock" role="tab" data-toggle="tab" href="#LaravelTimelineBlock" title="<?= __t('Timeline for invoice') ?>"><span class="one-line"><?= __('Activity Log') ?>
                </span></a></li>
        <?php if ($track_stock && ifPluginActive(PRODUCT_TRACKING_PLUGIN) && $product['Product']['tracking_type'] != \App\Utils\TrackStockUtil::QUANTITY_ONLY) { ?>
            <li role="presentation"><a aria-controls="TrackingBlock" role="tab" data-toggle="tab" onclick="reloadTrackingList()" href="#TrackingBlock" title="<?php __('Tracking List for Product') ?>"><span class="one-line"><?php __('Tracking List') ?></span></a></li>
        <?php } ?>
        <?php if ($showItemGroupTab) { ?>
            <li role="presentation"><a aria-controls="ItemGroupBlock" role="tab" data-toggle="tab" href="#ItemGroupBlock" title="<?= __t('Item Group') ?>"><span class="one-line"><?= __('Item Group') ?></span></a></li>
        <?php } ?>
    </ul>

    <!-- / tabs-buttons -->
    <div class="tab-content responsive  " style="display: <?= IS_MOBILE ? 'none' : 'block' ?>!important" id="product_details">
        <div class="tab-pane active" id="ProductBlock">
            <div class="row flex-row">
                <?php if ($track_stock) { ?>
                    <div class="value_highlight <?php echo $col_class ?>" style="overflow: hidden">
                        <span class="text-primary"><?php __('On Hand Stock') ?></span>
                        <h3><?php
                            $average_price = $product['Product']['average_price'];
                            $factor_name = "";
                            $factor_avg_name = "";
                            $has_factor = 0;
                            if ($enable_multi_units && !empty($factor)) {
                                $has_factor = 1;
                                if (!empty($factor['factor'])) {
                                    $stock_level = ($stock_level / $factor['factor']);
                                    $sold_all = ($sold_all / $factor['factor']);
                                }
                                $average_price = $average_price * $factor['factor'];
                                $factor_name = " " . $factor['factor_name'];
                                $factor_avg_name = " " . __("per", true) . ' ' . $factor['factor_name'];
                            }
                            echo format_number($stock_level, false, 2) . $factor_name; ?></h3>
                        <?php if (isset($productStock) && count($productStock) > 0) { ?>

                            <div id="local_time" class="stock-level-list">
                                <ul>
                                    <?php foreach ($productStock as $k => $s) {
                                        if (abs($s) > 0.0000001) { ?>
                                            <li><?php echo "<b>" . $store_list[$k] . "</b>: " . format_number(($s / ($has_factor && $factor['factor'] ? $factor['factor'] : 1))) . $factor_name ?></li>
                                    <?php }
                                    } ?>
                                </ul>
                            </div>
                        <?php } ?>
                        <?php if (check_permission(Adjust_Product_Inventory)) { ?>
                            <a class="m-t-xs  btn btn-sm btn-primary btn-addon" href="<?php echo Router::url(array('action' => 'manual_stock_adjust', $product['Product']['id'])) ?>"><?php __('Add Stock Transaction') ?></a>
                        <?php } ?>
                    </div>


                    <?php if ($showProductPendingQTY && count(array_filter($productPendingQTY))  > 0) { ?>
                        <div class="value_highlight <?php echo $col_class ?>">
                            <span class="text-primary"><?php __('Pending Stock') ?></span>
                            <h3><?= format_number(abs(array_sum($productPendingQTY))) ?></h3> <strong><?= $factor_name ?></strong>
                            <?php if (isset($productPendingQTY) && count($productPendingQTY) > 0) { ?>
                                <div id="local_time" class="stock-level-list">
                                    <ul>
                                        <?php foreach ($productPendingQTY as $k => $s) {
                                            if (abs($s) > 0.0000001) { ?>
                                                <li><?php echo "<b>" . $store_list[$k] . "</b>: " . format_number($s / ($has_factor && $factor['factor'] ? $factor['factor'] : 1)) . $factor_name ?></li>
                                        <?php }
                                        } ?>
                                    </ul>
                                </div>
                            <?php } ?>
                        </div>

                        <div class="value_highlight <?php echo $col_class ?>">
                            <span class="text-primary"><?php __('Available Stock') ?></span>
                            <h3><?= format_number(array_sum($productPendingQTY) + $stock_level) ?></h3> <strong><?= $factor_name ?></strong>
                            <div id="local_time" class="stock-level-list">
                                <ul>
                                    <?php foreach ($productAvailableQTY as $k => $s) {
                                        if (abs($s) > 0.0000001) { ?>
                                            <li><?php echo "<b>" . $store_list[$k] . "</b>: " . format_number(($s / ($has_factor && $factor['factor'] ? $factor['factor'] : 1))) . $factor_name ?></li>
                                    <?php }
                                    } ?>
                                </ul>
                            </div>
                        </div>
                    <?php } ?>



                <?php  } ?>
                <div class="value_highlight <?php echo $col_class ?>">
                    <span class="text-primary"><?php __('Total Sold Items') ?></span>
                    <span class="tooltip" title="<?php echo $tooltip ?>"></span>
                    <h3><?php echo format_number($sold_all, false, 2) . $factor_name; ?></h3>
                </div>
                <div class="value_highlight <?php echo $col_class ?>">
                    <span class="text-primary"><?php printf(__('Last %s Days', true), 28) ?></span>
                    <h3><?php echo format_number($sold_28); ?> <span class="<?php echo $sold_28 > $sold_28_before ? 'value_up success fa fa-arrow-up' : ($sold_28 < $sold_28_before ? 'value_down danger fa fa-arrow-down' : '') ?>"></span></h3>
                    <?php if ($sold_28 > 0 || $sold_28_before > 0) { ?>
                        <span class="before_period <?php echo $sold_28 > $sold_28_before ? 'success' : 'danger' ?>"> <?php echo ($sold_28 > $sold_28_before ? '+' : '') . format_number($sold_28 - $sold_28_before) . ($sold_28 > 0 && $sold_28_before > 0 ? ' (%' . abs(round(($sold_28 - $sold_28_before) / $sold_28_before * 100, 2)) . ')' : '') ?></span><br />
                        <span class="hint"><?php printf(__('vs. previous %s days', true), 28) ?></span>
                    <?php } ?>
                </div>
                <div class="value_highlight <?php echo $col_class ?>">
                    <span class="text-primary"><?php printf(__('Last %s Days', true), 7) ?></span>
                    <h3><?php echo format_number($sold_7); ?> <span class="<?php echo $sold_7 > $sold_7_before ? 'value_up success fa fa-arrow-up' : ($sold_7 < $sold_7_before ? 'value_down danger fa fa-arrow-down' : '') ?>"></span></h3>
                    <?php if ($sold_7 > 0 || $sold_7_before > 0) { ?>
                        <span class="before_period <?php echo $sold_7 > $sold_7_before ? 'success' : 'danger' ?>"> <?php echo ($sold_7 > $sold_7_before ? '+' : '') . format_number($sold_7 - $sold_7_before) . ($sold_7 > 0 && $sold_7_before > 0 ? ' (%' . abs(round(($sold_7 - $sold_7_before) / $sold_7_before * 100, 2)) . ')' : '') ?></span><br />
                        <span class="hint"><?php printf(__('vs. previous %s days', true), 7) ?></span>
                    <?php } ?>
                </div>
                <?php if (!empty($product['Product']['average_price']) && $show_buy_price) { ?>
                    <div class="value_highlight <?php echo $col_class ?>">
                        <span class="text-primary"><?php __('Average Unit Cost') ?></span>
                        <span class="tooltip" title="average_cost_explanation"></span>
                        <h3><?php echo format_price(round($average_price, 4), $default_currency, false) . $factor_avg_name; ?></h3>
                    </div>
                <?php } ?>
            </div>

            <div class="input-fieldsx">
                <h3 class="head-bar theme-color-a"><span class="details-info"><?php __('Details') ?></span></h3>
                <div class="row">
                    <div class="col-sm-3">
                        <div class="product-img <?php echo isset($product['defaultImage']['path']) ? '' : 'no-print'; ?>">

                            <?php  //MainImage

                            $path = '/css/images/no-product-image.png';
                            $height = "";
                            $width  = "";
                            if (isset($product['defaultImage']['path'])) {
                                if ($product['defaultImage']['is_s3']) {
                                    $height = "200";
                                    $width  = "200";
                                    $path = $product['defaultImage']['path'];
                                } else {
                                    $path = $product['defaultImage']['path'] . '?w=200&h=200&c=1';
                                }
                            }

                            if (IS_PC) {
                            ?>
                                <?php if ($can_edit) { ?>
                                    <div class="overly ">
                                        <a id="photoboxbutton" class="product_photo btn btn-md btn-primary" href="#" data-toggle="modal" data-target="#photobox"><?php echo isset($product['defaultImage']['path']) ? __('Product Photos', true) : __('Upload Photos', true) ?></a>
                                    </div>
                                <?php } ?>
                                <img height="<?= $height ?>" width="<?= $width ?>" src="<?= $path ?>">
                            <? } else { ?>
                                <?php if ($can_edit) { ?>
                                    <div class="overly">
                                        <a class="product_photo btn btn-md btn-primary" href="<?php echo Router::url(array('action' => 'photos', $product['Product']['id'])) ?>"><?php echo isset($product['defaultImage']['path']) ? __('Product Photos', true) : __('Upload Photos', true) ?></a>
                                    </div>
                                <?php } ?>
                                <img height="<?= $height ?>" width="<?= $width ?>" src="<?= $path ?>">
                            <? } ?>
                        </div>
                        <div class="clearfix"></div>
                        <!--<div class="product-img"><img src="/css/images/no_product.png" alt="no-product-image"> </div>-->
                    </div>
                    <div class="col-sm-4">
                        <table width="" cellspacing="0" cellpadding="0" border="0" class="view-table no-margin">
                            <tbody>
                                <?php if (!empty($product['Product']['product_code'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Product SKU') ?>: </strong></td>
                                        <td dir="ltr"><?php echo $product['Product']['product_code'] ?></td>
                                    </tr>
                                <?php } ?>
                                <?php if (!empty($product['Product']['unit_price'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Selling Price') ?>: </strong></td>
                                        <?php if (!empty($sellFactor)) { ?>
                                            <td><?= format_price($product['Product']['unit_price'] * $sellFactor['factor'], $default_currency) . " " . __("per", true) . ' ' . $sellFactor['factor_name'] ?></td>
                                        <?php } else { ?>
                                            <td><?= format_price($product['Product']['unit_price'], $default_currency) ?></td>
                                        <?php }  ?>
                                    </tr>
                                <?php } ?>
                                <?php if (!empty($product['Product']['unit_price']) && !empty($product['Product']['discount']) && !empty($product['Product']['discout_type'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Price After Discount') ?>: </strong></td>
                                        <td><?php echo format_number($priceAfterDiscount) . ' ' . $price_extra; //." ".getAuthOwner('currency_code'); 
                                            ?></td>
                                    </tr>
                                <?php } ?>
                                <?php if (!empty($product['Product']['minimum_price'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Minimum Price') ?>: </strong></td>
                                        <td><?php echo format_number($product['Product']['minimum_price']); //." ".getAuthOwner('currency_code'); 
                                            ?></td>
                                    </tr>
                                <?php } ?>
                                <?php if (!empty($product['Product']['profit_margin'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Profit Margin') ?>: </strong></td>
                                        <td><?php echo format_number($product['Product']['profit_margin']); //." ".getAuthOwner('currency_code'); 
                                            ?>%</td>
                                    </tr>
                                <?php } ?>


                                <?php if (!empty($product['Product']['duration_minutes'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Duration') ?>: </strong></td>
                                        <td><?php echo sprintf(__('%s Minutes', true), $product['Product']['duration_minutes']); //." ".getAuthOwner('currency_code'); 
                                            ?></td>
                                    </tr>
                                <?php } ?>


                                <?php if (!empty($product['Product']['description'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Description') ?>: </strong></td>
                                        <td><?php echo nl2br($product['Product']['description']) ?></td>
                                    </tr>
                                <?php } ?>

                                <?php if (!empty($product['Product']['notes'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Internal Notes') ?>: </strong></td>
                                        <td><?php echo nl2br($product['Product']['notes']) ?></td>
                                    </tr>
                                <?php } ?>


                            </tbody>
                        </table>
                    </div>
                    <div class="col-sm-4">
                        <table width="" cellspacing="0" cellpadding="0" border="0" class="view-table">
                            <tbody>
                                <?php if (!empty($product['Product']['buy_price']) && $show_buy_price) { ?>
                                    <tr>
                                        <td><strong><?php __('Purchase Price') ?>: </strong></td>
                                        <?php if (!empty($purchaseFactor)) { ?>
                                            <td><?= format_price($product['Product']['buy_price'] * $purchaseFactor['factor'], $default_currency) . " " . __("per", true) . ' ' . $purchaseFactor['factor_name'] ?></td>
                                        <?php } else { ?>
                                            <td><?= format_price($product['Product']['buy_price'], $default_currency) ?></td>
                                        <?php } ?>
                                    </tr>
                                <?php } ?>
                                <?php if (!empty($product['Product']['barcode'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Barcode') ?>: </strong></td>
                                        <td dir="ltr"><?php echo str_replace(' ', '&nbsp;', $product['Product']['barcode']) ?></td>
                                    </tr>
                                <?php } ?>
                                <?php if (!empty($product['Supplier']['business_name'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Supplier') ?>: </strong></td>
                                        <td><a target="_blank" href="<?php echo Router::url(array('controller' => 'suppliers', 'action' => 'view', $product['Supplier']['id'])) ?>"><u><?php echo $product['Supplier']['business_name']; ?></u></a></td>
                                    </tr>
                                <?php } ?>
                                <?php if (!empty($product['Product']['supplier_code'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Supplier Code') ?>: </strong></td>
                                        <td><?php echo $product['Product']['supplier_code'] ?></td>
                                        </td>
                                    </tr>
                                <?php } ?>

                                <?php if (!empty($product['Product']['brand'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Brand') ?>: </strong></td>
                                        <td><?php echo $product['Product']['brand'] ?></td>
                                    </tr>
                                <?php } ?>
                                <?php if (count($product['Category'] ?: [])) { ?>
                                    <tr>
                                        <td><strong><?php __('Categories') ?>: </strong></td>
                                        <td><?php foreach ($product['Category'] as $ii => $cat) {
                                                echo ($ii > 0 ? ', ' : '') . '<a href="' . Router::url('/owner/products/index?category=' . $cat['id']) . '" target="_blank" ><u>' . $cat['name'] . '</u></a> ';
                                            } ?></td>
                                    </tr>
                                <?php } ?>


                                <?php if (isset($trackingTypes) && $track_stock) { ?>
                                    <tr>
                                        <td><strong><?php __('Tracking Type') ?>: </strong></td>
                                        <td><?php echo $trackingTypes[$product['Product']['tracking_type']] ?></td>
                                    </tr>
                                <?php } ?>
                                <?php if (isset($package)) { ?>
                                    <tr>
                                        <td><strong><?php __('Package') ?>: </strong></td>
                                        <td><?php echo $package['Package']['name'] . " #" . $package['Package']['id']  ?></td>
                                    </tr>
                                <?php } ?>
                                <?php if (!empty($product['Product']['track_stock']) && !empty($product['Product']['low_stock_thershold'])) { ?>
                                    <tr>
                                        <td><strong><?php __('Low Stock Threshold') ?>: </strong></td>
                                        <td><?php echo $product['Product']['low_stock_thershold'] ?></td>
                                    </tr>
                                <?php } ?>
                                <?php if (isset($product['ProductAttributeOption'])): ?>
                                    <?php foreach ($product['ProductAttributeOption'] as $productAttribute): ?>
                                        <tr>
                                            <td><strong><?= $productAttribute['attribute'] ?>: </strong></td>
                                            <td><?= $productAttribute['option'] ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>

                            </tbody>
                        </table>

                    </div>

                    <div class="col-md-8 col-sm-8">
                        <?php


                        if (!empty($item_barcodes) && !empty($item_barcodes[0]['barcode'])) {
                        ?>
                            <table class="list-table table table-hover tableClass">
                                <thead>
                                    <tr>
                                        <th class=""> <?php __('Unit Factor') ?> </th>
                                        <th class=""> <?php __('Barcode') ?> </th>
                                        <th class=""> <?php __('Quantity') ?> </th>
                                        <th class=""> <?php __('Selling Price Per Unit') ?> </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    foreach ($item_barcodes as $barcode) { ?>
                                        <tr>
                                            <td><?php echo $barcode['unit_factor'] ?></td>
                                            <td><?php echo $barcode['barcode']; ?></td>
                                            <td><?php echo $barcode['quantity'] ?></td>
                                            <td><?php echo format_price($barcode['unit_price'], $product['Product']['currency']); ?></td>
                                        </tr>
                                    <?php } ?>
                                </tbody>
                                <tfoot>
                                </tfoot>
                            </table>
                        <?php } ?>
                    </div>


                </div>
            </div>

            </br>
            <div class="input-fields ">

                <div class="row">
                    <?php echo $this->element('custom_forms/view_fields_form2'); ?>
                    <?php echo $this->element('custom_forms/le_form_show'); ?>
                </div>
            </div>
            <?php if (!empty($bundles)) { ?>
                <h3 class="head-bar theme-color-a"><span class="details-info"><?php __('Bundle Items') ?></span></h3>
                <div class="row">
                    <?php foreach ($bundles as $b) { ?>
                        <div class="col-lg-6">
                            <table>
                                <tr>
                                    <td>
                                        <b><a href="<?php echo Router::url(['controller' => 'products', 'action' => 'view', $b['Product']['id']]); ?>"><?php echo $b['Product']['name'] ?></a></b>:
                                    </td>
                                    <td>
                                        <?php echo (empty($b['ProductBundle']['unit_factor']) ? ($b['ProductBundle']['quantity']) : (($b['ProductBundle']['quantity'] / $b['ProductBundle']['unit_factor']) . ' ' . $b['ProductBundle']['unit_small_name'])) ?>
                                    </td>
                                </tr>
                            </table>

                        </div>


                    <?php } ?>


                </div>
                <?php if (!empty($bundle_final_cost) && $show_buy_price) { ?>
                    <div class="value_highlight <?php echo $col_class ?>">
                        <span class="text-primary"><?php __('Average Unit Cost') ?></span>
                        <span class="tooltip" title="average_cost_explanation"></span>
                        <h3><?php echo format_price($bundle_final_cost, $default_currency) . $factor_avg_name; ?></h3>
                    </div>
                <? } ?>
            <?php } ?>
            <div class="clearfix"></div>
        </div>


        <div class="tab-pane" id="TimelineBlock">
            <script type="text/javascript">
                var timeline_url = "<?php echo Router::url(array('action' => 'timeline', $product['Product']['id'])) ?>";
                var timeline_row_url = "<?php echo Router::url(array('action' => 'timeline_row')) ?>";
            </script>
            <?php echo $this->element('timeline'); ?>
        </div>

        <div class="tab-pane" id="LaravelTimelineBlock">
            <iframe src="/v2/owner/activity_logs/entity/iframe?entity_key=product&entity_id=<?= $product['Product']['id'] ?>&sort=DESC&layout2022=1&iframe=1" style="width: 100%; min-height: 500px;border: 0"></iframe>
        </div>
        <?php if ($track_stock && (check_permission(Track_Inventory) || check_permission(Adjust_Product_Inventory))) { ?>
            <div class="tab-pane" id="StockTransactionsBlock">
                <?php echo $this->element('stock_transaction'); ?>
            </div>
        <?php } ?>
        <?php if ($track_stock && ifPluginActive(PRODUCT_TRACKING_PLUGIN) && $product['Product']['tracking_type'] != \App\Utils\TrackStockUtil::QUANTITY_ONLY) { ?>
            <div class="tab-pane" id="TrackingBlock">
                <iframe id="trackingIframe" src="About:blank" style="width: 100%; min-height: 500px;border: 0"></iframe>
            </div>
        <?php } ?>
        <?php if ($showItemGroupTab): ?>
            <div class="tab-pane" id="ItemGroupBlock">
                <iframe onload="onItemGroupIframeLoad(this)" id="itemGroupIframe" src="/v2/owner/entity/<?= EntityKeyTypesUtil::ITEM_GROUP_ENTITY_KEY ?>/<?= $relatedItemGroupId ?>/show?iframe=1#details" style="width: 100%; min-height: 500px;border: 0"></iframe>
            </div>
        <?php endif; ?>

    </div>
    <?php
    $this->set('hasTabs', true);
    echo $html->css(array('owner-page', 'tabs', 'reports'), false, false, false)
    ?>
    <style>
        #body_box .panel-body {
            padding: 0;
        }

        #body_box .panel-default {
            border: none !important;
        }

        .entry-time {
            width: 24%;
        }
        .modal-backdrop {
            position: fixed;
            inset: 0;
            height: 100vh !important;
        }
        .modal .modal-dialog {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            min-height: calc(100% - (10px* 2));
            padding-top: 0px !important;
        }

        .modal .modal-content {
            width: 100%;
        }

        @media (min-width: 768px) { 
            .modal .modal-dialog {
                min-height: calc(100% - (30px* 2));
            }
        }
    </style>



</div>
</div>

<?php if ($showItemGroupTab): ?>
    <script>
        function onItemGroupIframeLoad(iframe) {
            iframe.contentWindow.loadLinksOnParentWindow();
        }
    </script>
<?php endif; ?>

<script>
    $('#photobox').on('hidden.bs.modal', function(e) {
        location.reload();
    });
</script>

<div class="modal fade" id="photobox" tabindex="-1" role="dialog" aria-labelledby="photobox">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <button type="button" class="close-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true"></span></button>
            <div class="modal-header">
                <h3><?php echo __('Product Photos', true) ?></h3>
            </div>
            <div class="modal-body">
                <div id="photos_iframe_loading" class="text-center p-2" style="display: none;">
                    <div class="loading-spinner-icon mx-auto"></div>
                </div>
                <iframe id="photos_iframe" width="100%" height="600" src="" frameborder="0" allowfullscreen></iframe>
            </div>

        </div>
    </div>
</div>

<script>
    $(function() {
        if (typeof top !== 'undefined' && typeof top.reload_curr_stock !== 'undefined') {
            top.reload_curr_stock(); // This invokes Ajax on Page Load
            top.$('#edTransModal').modal('hide');
        }
        $('.has-tip').each(function() {

            $(this).qtip({
                content: $('#' + $(this).data('tip')).html(),
                style: {
                    classes: 'qtip-bootstrap'
                },
                position: {
                    my: 'top center',
                    at: 'bottom center'
                }
            });
        });
    })
    $('document').ready(function() {




        $('body').on('click', '.product_photo', function() {
            $('#photos_iframe_loading').show();
            $('#photos_iframe').hide();
            $('#photos_iframe').attr('src', '<?php echo Router::url(array('action' => 'photos', $product['Product']['id'])) ?>?box=1');
        });
    });

    $('#photos_iframe').get(0).addEventListener('load', function() {
        $('#photos_iframe_loading').hide();
        $('#photos_iframe').show();
    });

    function reloadTrackingList() {
        document.getElementById('trackingIframe').src = '/v2/owner/product_tracking?product=<?= $product['Product']['id'] ?>&type=<?= $product['Product']['tracking_type'] ?>&effect_on_stock=0&iframe=1&is_product_view';
    }
</script>