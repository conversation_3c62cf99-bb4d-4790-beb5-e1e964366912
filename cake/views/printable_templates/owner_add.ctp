<?php
//Configure::write('debug',2);
//dd( $printableTemplateDefault );
echo $javascript->link(array('main', 'jquery-ui-drag.min', 'nicEdit', 'humanize', 'tinymce/tinymce.min.js', 'colorpicker'));
echo $html->css(array('modal-window', 'invoicing', 'custom-invoice', 'colorpicker'));

$current_site = getCurrentSite('id');

if(isset($this->data['PrintableTemplate']) )  {
    foreach($exception_placeholder_replacement as $key => $value)
    {
        $this->data['PrintableTemplate']['content'] = str_replace($key, $value, $this->data['PrintableTemplate']['content']);
    }
} elseif (isset($printableTemplateDefault) ) {
    foreach($exception_placeholder_replacement as $key => $value)
    {
        $printableTemplateDefault['PrintableTemplate']['content'] = str_replace($key, $value, $printableTemplateDefault['PrintableTemplate']['content']);
    }
}

?>
<script src="/js/somescript.js"></script>
<style>
    .gray_area
    {
        background: rgb(195, 195, 195);
        padding: 20px;
    }

    .barcode_area
    {
        margin:auto;
        background-color: #FFFFFF;
        height: 151px;
    }

</style>
<?php
    $styles = [
        '//fonts.googleapis.com/css?family=Lato:300,300i,400,400i',
        '//www.tinymce.com/css/codepen.min.css',
        '//'.getCurrentSite('subdomain'). '/css/en_inside_printable_template.css?v='.rand(1,999999999)
    ];

    if(getCurrentSite('language_code') == 7)
        $styles[] = '//'.getCurrentSite('subdomain'). '/css/ar_inside_printable_template.css?v='.rand(1,999999999);

?>
<script type="text/javascript">
	var exit_setContent=false;
    var exception_placeholder_replacement = <?= json_encode($exception_placeholder_replacement); ?>;

   

    tinymce.init({
        selector: '#myfooter'
    });
    tinymce.init({
        selector: '#mytextarea',
        plugins: [
            "noneditable",
            'paste textpattern image',
            'autoresize',
            'code',
            'table',
			
        ],
        // protect:[/{{\/([a-zA-Z_0-9]*)}}/g,/{{#([a-zA-Z_0-9]*)}}/g,/{{\^([a-zA-Z_0-9]*)}}/g,/\<\/?(script)\>/g],
        protect:[/{{\^([a-zA-Z_0-9]*)}}/g],
       fix_table_elements:false,
        image_title: true,
        automatic_uploads: true,
        images_upload_url: '<?php echo Router::url(array('owner'=>true, 'action' => 'upload_image',$type)) ?>',
        file_picker_types: 'image', file_picker_callback: function(cb, value, meta) {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');


            input.onchange = function() {
                var file = this.files[0];

                var reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = function() {

                    var id = 'blobid' + (new Date()).getTime();
                    var blobCache = tinymce.activeEditor.editorUpload.blobCache;
                    var blobInfo = blobCache.create(id, file, reader.result);
                    blobCache.add(blobInfo);

                    // call the callback and populate the Title field with the file name
                    cb(blobInfo.blobUri(), {title: file.name});
                };
            };

            input.click();
        },
        height: 200,
        content_style: "body { background-color: #888888 !important; }",
        toolbar: 'undo redo | fontselect | bold italic | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright | image | code | table | mybutton',
        menubar: false,
        theme_advanced_fonts : "Arial=arial,helvetica,sans-serif;Courier New=courier new,courier,monospace;AkrutiKndPadmini=Akpdmi-n",
        fontsize_formats: "4pt 5pt 6pt 7pt 8pt 9pt 10pt 11pt 12pt 13pt 14pt 15pt 16pt 17pt 18pt 20pt 22pt 24pt 30pt 36pt",
        force_br_newlines: true,
//        valid_elements : '*[*]',
        valid_children : "+body[style],+div[style],+body[script],+div[script]",remove_script_host: false,verify_html: false,
                                    relative_urls: false,
//        extended_valid_elements: 'script',
<!--        content_css: --><?//= json_encode($styles); ?><!--,-->
        setup: function (editor) {

            editor.on('SetContent', function (e) {
			if(exit_setContent){
			exit_setContent=false;
			return;
			}	
			
                
               console.log(e.content);
			// alert("content sitten");
				var original_content = $.parseHTML( '<div id="temp">' + tinymce.activeEditor.getContent() + '</div>' );
				console.log(original_content);
				var content_inside_the_div = $(original_content).find('#barcode_template').html();
				
				
				//main_content
				console.log(content_inside_the_div);
				
				$(original_content).find('#barcode_template').remove();
				var content_outside_the_div = $(original_content).html();
				console.log(content_outside_the_div);
				if( ! content_outside_the_div ){
					return false;
					}
				if(content_inside_the_div==undefined){
			content_inside_the_div='';
			}
				var new_content = content_inside_the_div + content_outside_the_div;
				
				console.log('new_content'+new_content);
				console.log(new_content);
				if( new_content != 'undefined' )
				{
					console.log('setting the content');
					console.log($("#mytextarea_ifr").contents().find("#barcode_template"));
					console.log(new_content);
					if(content_inside_the_div==''){
						$("#mytextarea_ifr").contents().find('body').html(main_content);
						$("#mytextarea_ifr").contents().find("#barcode_template").html(new_content);
					}else{
					$("#mytextarea_ifr").contents().find("#barcode_template").html(new_content);
					}
					
				//	$("#mytextarea_ifr").contents().find("#barcode_template").nextAll().remove();
					  console.log( $("#mytextarea_ifr").html() );
					   exit_setContent=true;
					  tinymce.activeEditor.setContent( $("#mytextarea_ifr").contents().find('body').html() );
					  //reset the width & height
                var width = $('.width').val();
                var height =  $('.height').val();

                $("#mytextarea_ifr").contents().find("#barcode_template").width(width * 37.795275590551);
                $("#mytextarea_ifr").contents().find("#barcode_template").height(height * 37.795275590551);

                $('.width').val(width); // 37.795275590551
                $('.height').val(height);

                //reset the margins
                $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-top', $('#margin_top').val() + "px");
                $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-right', $('#margin_right').val() + "px");
                $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-down', $('#margin_down').val() + "px");
                $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-left', $('#margin_left').val() + "px");

					 
				}
            });

            editor.on('keydown', function(e)
            {
                var flag = 0;
                var tex = $(tinymce.activeEditor.getBody()).find('.mceEditable').text();

                flag = ( tex ? 1 : 0 );

                // if the key pressed is backspace of delete preventDefault
                if ( (e.keyCode === 8 || e.keyCode === 46) && flag == 0 )  // Backspace key & Delete key
                {
                    e.preventDefault();
                }



            });

            //assign the php array to js array
            var data = new Array();

            <?php foreach($placeholders as $key => $val){ ?>
            <?php
            if( $key == '{%barcode_width%}'  || $key == '{%barcode_height%}' )
                continue;
            ?>

            data.push({ text: '<?= addslashes($val); ?>' ,value: '<?= $key; ?>' });
            <?php } ?>

//            console.log( data );




            //add the dropdown list
            editor.addButton('mybutton', {
                type: 'listbox',
                text: 'Select placeholder',
                icon: false,
                onselect: function (e) {

                    //override the value if the value already in the exception_array called exception_placeholder_replacement
                    for(var key in exception_placeholder_replacement)
                    {
                        if( key === this.value() )
                        {
                            editor.insertContent(exception_placeholder_replacement[key]);
                            this.value('Select placeholder');
                            return false;
                        }
                    }

                    editor.insertContent('<span class="mceNonEditable" >'  + this.value() + '</span>');
                    this.value('Select placeholder');
                },
                values: data,
                onPostRender: function () {
                    this.value('Select placeholder');
                }
            });
        }

    });

</script>


<form method="post" id="start_print" style="display:none" target="_blank">
    <input type="hidden" class="content" name="content"/>
    <input type="hidden" class="type" name="type" value="<?= ( isset( $this->data['PrintableTemplate'] ) ? $this->data['PrintableTemplate']['type'] : $type ); ?>"/>
</form>

<form method="post" id="preview" style="display:none" target="_blank">
    <input type="hidden" class="content" name="content"/>
    <input type="hidden" class="type" name="type" value="<?= ( isset( $this->data['PrintableTemplate'] ) ? $this->data['PrintableTemplate']['type'] : $type ); ?>"/>
</form>

<div class="content-area">
    <div class="panel panel-default relative">
        <div class="panel-body">
            <?php
            echo $form->create('PrintableTemplate', array('url' => array('owner'=>true,'controller'=>'printable_templates', 'action' => isset( $this->data['PrintableTemplate'] ) ? 'edit' : 'add' )) );

            //de-comment if you want to make the user choose from the drop down
            //echo $form->input('type', array('id' => 'PrintableTemplateType', 'class' => 'form-control', 'type' => 'select','value'=>array_search($type, $types), 'options' => $types));

            //type is ok for editing portal and for normal editing
            echo $form->input('type', array('value'=>$type, 'type'=>'hidden', 'class' => 'INPUT required'));
            echo $form->input('default_template', array('value'=>( isset($printableTemplateDefault['PrintableTemplate']['default_template']) ? $printableTemplateDefault['PrintableTemplate']['default_template'] : (isset($this->data['PrintableTemplate']['default_template']) ? $this->data['PrintableTemplate']['default_template'] : '0' ) ), 'type'=>'hidden', 'class' => 'INPUT required'));

            if( empty($this->data) )
            {
                echo $form->input('language', array('value'=>(isset($printableTemplateDefault['PrintableTemplate']['language']) ? $printableTemplateDefault['PrintableTemplate']['language'] : getCurrentSite('language_code_code3') ), 'type'=>'hidden', 'class' => 'INPUT required'));
            }

            //default template id comming from the portal
            if(isset($printableTemplateDefault))
                echo $form->input('printable_layout_id', array('type'=>'hidden', 'value'=>$printableTemplateDefault['PrintableTemplate']['id']));

            if(isset($this->data['PrintableTemplate']))
                echo $form->input('id', array('type'=>'hidden', 'value'=>$this->data['PrintableTemplate']['id']));

            echo $form->input('site_id', array('type'=>'hidden', 'value'=>( isset($this->data['PrintableTemplate']) ? $this->data['PrintableTemplate']['site_id'] : $current_site )));

            //layout_override_id
            if(isset($portal) && $portal)
                echo $form->input('override_layout_id', array('type'=>'hidden', 'value'=>$printableTemplateDefault['PrintableTemplate']['id']));

			?>
			<?php
            if( (isset($portal) && $portal) || isset($printableTemplateDefault) )
                echo $form->input('name', array('id'=>'template_name','label'=>__('Template Name',true),'class' => 'INPUT form-control required form-control input-lg template_name', 'value'=>$printableTemplateDefault['PrintableTemplate']['name']));
            else
                echo $form->input('name', array('id'=>'template_name','label'=>__('Template Name',true),'class' => 'INPUT form-control required form-control input-lg template_name'));
            if (isset($extra_fields) && is_iterable($extra_fields)) {
                foreach ($extra_fields as $f) {
                    echo $form->input("PrintableTemplate.extra_fields." . $f['name'], ['label' => __($f['label'], true)]);
                }
            }
			?>
			<?php
//            if( !$portal )
//            {
//                if( !isset($this->data['PrintableTemplate']['override_layout_id']) )
//                {
//            if(!empty($printableTemplateDefault['PrintableTemplate']['for_galary_only']))
//            {
//                $for_galary_only_value = ( isset($printableTemplateDefault['PrintableTemplate']['for_galary_only']) ? $printableTemplateDefault['PrintableTemplate']['for_galary_only'] : $this->data['PrintableTemplate']['for_galary_only'] ) ;
                echo $form->input('for_galary_only',['type'=>'hidden', 'label'=>__('Activate',true),'div' => '  clip-check check-info']);
//            }else
//            {
//                echo $form->input('for_galary_only',['type'=>'hidden', 'value'=>NULL, 'label'=>__('Activate',true),'div' => '  clip-check check-info']);
//            }
//                }
//            }


            ?>
			<br>
            <textarea id="mytextarea" name="data[PrintableTemplate][content]">
                <?php if(strpos($this->data['PrintableTemplate']['content'], 'barcode_template') === false) {?>
                <div id="barcode_template" class="mceEditable" readonly="readonly" style="padding:0px 0px 0px 0px; margin:20px auto 0px; background-color:#FFFFFF;  margin-bottom: 20px;">
                <?php } ?>    
                <?php echo (isset($this->data['PrintableTemplate']) ? $this->data['PrintableTemplate']['content'] : ( isset($printableTemplateDefault) ? $printableTemplateDefault['PrintableTemplate']['content'] : '') ) ?>
                <?php if(strpos($this->data['PrintableTemplate']['content'], 'barcode_template') === false) {?>
                </div>
                <?php } ?>
            </textarea>
            <!--    <div style="margin: -270px 578px auto;position: absolute;">W:<input class="width" type="text" style="width:50px;" value="5.0"/> cm</div>-->
            <!--    <div style="margin: -270px 480px auto;position: absolute;">H:<input class="height" type="text" style="width:50px;" value="4.0"/> cm</div>-->
            <br/>


<!--            <div class="rounded-item theme-color-a send-row">-->
<!--                <div class="col-md-6 col-sm-6">-->
<!--                    <h3 id="AttachmentsToggle">-->
<!--                        <a id="sign" class="accordion-switch Expand" href="#">+</a>-->
<!--                        --><?php //__("More Settings") ?>
<!--                    </h3>-->
<!--                </div>-->
<!--                <div class="clear"></div>-->
<!--            </div>-->

            <div class="content-area accordion-content" id="AttachmentsArea" style="display: none;">
                <?php echo $form->input('page_footer', array('id'=>'myfooter','label'=>__('page footer',true), 'class' => 'INPUT required')); ?>
            </div>

    </div>
	    


</div>

<div class="pages-head-s2020">
	<div class="container">
		<div class="row-flex align-items-center">
			<div class="col-flex-6 d-inline-flex">
               <?php  // commented for duplicate behavior (where this one is even corrupt)
               if (false) { ?>
				<a class="btn s2020 btn-icn btn-secondary font-weight-medium print-button responsive mr-2">
					<i class="fa fa-print"></i>
					<span><?php __("Print Preview") ?></span>
				</a>
                 <?php } ?>
			</div>
			<div class="col-flex-6 d-flex justify-content-end">
				<a type="submit" class="btn s2020 btn-icn btn-secondary font-weight-medium preview-button responsive ml-2">
					<i class="mdi mdi-eye fs-20"></i>
					<span><?php __("Preview") ?></span>
				</a>
				<button type="submit" class="btn s2020 btn-icn btn-success font-weight-medium ml-2">
					<i class="mdi mdi-content-save-outline fs-20"></i>
					<span><?php __("Save") ?></span>
				</button>
			</div>
		</div>
	</div>
</div>

<?php echo $form->end(); ?>
<script>
	var main_content = '<div id="barcode_template" class="mceEditable" readonly="readonly" style="padding:0px 0px 0px 0px; margin:20px auto 0px; background-color:#FFFFFF;  margin-bottom: 20px;">&nbsp;</div>';
$(window).load(function(){
//      var $head = $("#mytextarea_ifr").contents().find("head");
//      $head.append($("<link/>",{ rel:"script", href: "https://use.fontawesome.com/20b95486ea.js" }));

    //we will use this variable to append our customizable settings to the tinymce toolbar
    var last_toolbar_element_id = "#" + $('.mce-toolbar').attr('id') + '-body';

    $(last_toolbar_element_id).append('<div style="height:16px;padding-top: 5px;" class="mce-container mce-flow-layout-item mce-btn-group" >' +
        '<select name="data[PrintableTemplate][paper_size]" class="paper_size" value="<?= ( isset($this->data["PrintableTemplate"]) ? $this->data["PrintableTemplate"]["paper_size"] : "A3" ); ?>">'+
        '<option>A4</option>'+
        '<option>A3</option>'+
        '<option>A5</option>'+
        '<option>Other</option>'+
        '</select>'+
        '</div>');

    /*the width and height buttons */
    $(last_toolbar_element_id).append('<div style="padding:2px;height:16px;padding-top: 5px;" class="mce-container mce-flow-layout-item mce-btn-group width-tm" ><b style="font-size: 16px;">W:</b><input name="data[PrintableTemplate][width]" id="width" class="width" type="text" style="padding:1px;border-radius: 2px; background-color:#FFFFFF;border:solid 1px;display:inline;width:30px;"/> cm</div>');
    <?php if( $type != "receipt" /*isset($placeholders['{%barcode_height%}'])*/ ): ?>
    $(last_toolbar_element_id).append('<div style="padding:2px;height:16px;padding-top: 5px;" class="mce-container mce-flow-layout-item mce-btn-group height-tm" ><b style="font-size: 16px;">H:</b><input name="data[PrintableTemplate][height]" id="height" class="height" type="text" style="padding:1px;border-radius: 2px; background-color:#FFFFFF;border:solid 1px;display:inline;width:30px;"/> cm</div>');
    <?php endif; ?>
    $(last_toolbar_element_id).append('<div style="height:16px;padding-top: 5px;" class="mce-container mce-flow-layout-item mce-btn-group set-tm" ><p style="text-align:center;width:50px;padding:2px;border:solid 1px;border-radius: 2px; background-color: #FFFFFF;" class="set_button">Set</p></div>');
    /*end of the widthand height buttons*/

    $('#width').keydown(function( event ) {
        if ( event.which == 13 ) {
            $('.set_button').click();
            event.preventDefault();
        }
    });

    $('#height').keydown(function( event ) {
        if ( event.which == 13 ) {
            $('.set_button').click();
            event.preventDefault();
        }
    });

    //edit form paper_size
    <?php if( isset($this->data['PrintableTemplate']) ): ?>
        $('.paper_size').val('<?= $this->data['PrintableTemplate']['paper_size'] ?>');

        if($('.paper_size').val() == "Other")
        {
            $('.width-tm').show();
            $('.height-tm').show();
            $('.set-tm').show();
        }
        else
        {
            $('.width-tm').hide();
            $('.height-tm').hide();
            $('.set-tm').hide();
        }
    <?php elseif( isset($printableTemplateDefault) ): ?>
        $('.paper_size').val('<?= $printableTemplateDefault['PrintableTemplate']['paper_size'] ?>');

        if($('.paper_size').val() == "Other")
        {
            $('.width-tm').show();
            $('.height-tm').show();
            $('.set-tm').show();
        }
        else
        {
            $('.width-tm').hide();
            $('.height-tm').hide();
            $('.set-tm').hide();
        }
    <?php else: ?>
        $('.width-tm').hide();
        $('.height-tm').hide();
        $('.set-tm').hide();
    <?php endif; ?>

    /*paper size dropdown*/
    var paper_sizes = [
        {type: 'A4', width:21.0, height:29.7 },
        {type: 'A3', width:29.7, height:42.0 },
        {type: 'A5', width:14.8, height:21.0 }
    ];

    //by default the Other is hidden and A4 is
    $("#mytextarea_ifr").contents().find("#barcode_template").width(<?php echo (isset($this->data['PrintableTemplate'])? $this->data['PrintableTemplate']['width'] : ( isset($printableTemplateDefault) ? $printableTemplateDefault['PrintableTemplate']['width'] : "paper_sizes[0].width") ); ?> * 37.795275590551);
    $("#mytextarea_ifr").contents().find("#barcode_template").height(<?php echo (isset($this->data['PrintableTemplate'])? $this->data['PrintableTemplate']['height'] : ( isset($printableTemplateDefault) ? $printableTemplateDefault['PrintableTemplate']['height'] : "paper_sizes[0].height") ); ?> * 37.795275590551);
    $("#mytextarea_ifr").contents().find("#barcode_template").css('overflow', 'auto');
    <?php if( isset($this->data['PrintableTemplate']) ): ?>
        $('.width').val(<?= $this->data['PrintableTemplate']['width']; ?>); // 37.795275590551
        $('.height').val(<?= $this->data['PrintableTemplate']['height']; ?>);
    <?php elseif( isset($printableTemplateDefault) ) : ?>
        $('.width').val(<?= $printableTemplateDefault['PrintableTemplate']['width']; ?>); // 37.795275590551
        $('.height').val(<?= $printableTemplateDefault['PrintableTemplate']['height']; ?>);
    <?php else: ?>
        $('.width').val(paper_sizes[0].width); // 37.795275590551
        $('.height').val(paper_sizes[0].height);
    <?php endif; ?>

    $('.set_button').on('click', function(){
        //get the value of the width
        //convert the cm to pixel
        var width = $('.width').val() * 37.795275590551;
        var height = $('.height').val() * 37.795275590551;

        //set the textarea > div width to the converted value
        var content = tinyMCE.get('mytextarea').getContent();
        content = content.replace('width:(*)px;', width);
        content = content.replace('height:(*)px;', height);
        $("#mytextarea_ifr").contents().find("#barcode_template").width(parseInt(width));
        $("#mytextarea_ifr").contents().find("#barcode_template").height(parseInt(height));
    });
    $('.paper_size').on('change',function(){
        for(var paper in paper_sizes )
        {
            if($('.paper_size').val() == paper_sizes[paper].type)
            {
                $("#mytextarea_ifr").contents().find("#barcode_template").width(paper_sizes[paper].width * 37.795275590551);
                $("#mytextarea_ifr").contents().find("#barcode_template").height(paper_sizes[paper].height * 37.795275590551);

                $('.width').val(paper_sizes[paper].width); // 37.795275590551
                $('.height').val(paper_sizes[paper].height);

                $('.width-tm').hide();
                $('.height-tm').hide();
                $('.set-tm').hide();
            }
        }

        if($('.paper_size').val() == "Other")
        {
            $('.width-tm').show();
            $('.height-tm').show();
            $('.set-tm').show();
        }
    });
    /*end of the paper size dropdown menu*/
    /*more settings button*/
    $(last_toolbar_element_id).append('<div style="float:right;height:16px;padding-top: 5px;" class="mce-container mce-flow-layout-item mce-btn-group" >' +
        '<select class="margins_dropsdown" >'+
        '<option>More settings</option>'+
        '<option>Margins</option>'+
        '</select>'+
        '</div>');
    $('.margins_dropsdown').on('change',function(){
        if( $('.margins_dropsdown').val() == 'Margins' )
        {
            $('.margins').show();
        }
        else
        {
            $('.margins').hide();
        }
    });
    /*end more settings button*/

    /*margins controll*/
    $(last_toolbar_element_id).append('<div class="margins" style="display: none;margin-top:10px;">' +
        '     <b style="font-size: 16px;padding-top:10px;padding-left: 20px;">margin-top:</b><input name="data[PrintableTemplate][margin_top]" id="margin_top" value="<?= (isset($this->data["PrintableTemplate"]) ? $this->data["PrintableTemplate"]["margin_top"] : ( isset($printableTemplateDefault) ? $printableTemplateDefault['PrintableTemplate']['margin_top'] : "0" ) ); ?>" class="margin_top" type="text" style="margin-top:-1px;padding:1px;border-radius: 2px; background-color:#FFFFFF;border:solid 1px;display:inline;width:30px;" placeholder="0"/> px' +
        '     <b style="font-size: 16px;padding-top:10px;padding-left: 20px;">margin-left:</b><input name="data[PrintableTemplate][margin_left]" id="margin_left" value="<?= (isset($this->data["PrintableTemplate"]) ? $this->data["PrintableTemplate"]["margin_left"] : ( isset($printableTemplateDefault) ? $printableTemplateDefault['PrintableTemplate']['margin_left'] : "0" ) ); ?>" class="margin_left" type="text" style="margin-top:-1px;margin-left:20px;padding:1px;border-radius: 2px; background-color:#FFFFFF;border:solid 1px;display:inline;width:30px;" placeholder="0"/> px' +
        '     <b style="font-size: 16px;padding-top:10px;padding-left: 20px;">margin-down:</b><input name="data[PrintableTemplate][margin_down]" id="margin_down" value="<?= (isset($this->data["PrintableTemplate"]) ? $this->data["PrintableTemplate"]["margin_down"] : ( isset($printableTemplateDefault) ? $printableTemplateDefault['PrintableTemplate']['margin_down'] : "0" ) ); ?>" class="margin_down" type="text" style="margin-top:-1px;margin-left:4px;;padding:1px;border-radius: 2px; background-color:#FFFFFF;border:solid 1px;display:inline;width:30px;" placeholder="0"/> px' +
        '     <b style="font-size: 16px;padding-top:10px;padding-left: 20px;">margin-right:</b><input name="data[PrintableTemplate][margin_right]" id="margin_right" value="<?= (isset($this->data["PrintableTemplate"]) ? $this->data["PrintableTemplate"]["margin_right"] : ( isset($printableTemplateDefault) ? $printableTemplateDefault['PrintableTemplate']['margin_right'] : "0" ) ); ?>" class="margin_right" type="text" style="margin-top:-1px;margin-left:10px;padding:1px;border-radius: 2px; background-color:#FFFFFF;border:solid 1px;display:inline;width:30px;" placeholder="0"/> px' +
        '</div>');

    <?php if(isset($this->data['PrintableTemplate'])): ?>
        $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-top',"<?= $this->data['PrintableTemplate']['margin_top']; ?>"+"px");
        $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-right',"<?= $this->data['PrintableTemplate']['margin_right']; ?>"+"px");
        $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-down',"<?= $this->data['PrintableTemplate']['margin_down']; ?>"+"px");
        $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-left',"<?= $this->data['PrintableTemplate']['margin_left']; ?>"+"px");
    <?php elseif( isset($printableTemplateDefault) ): ?>
        $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-top',"<?= $printableTemplateDefault['PrintableTemplate']['margin_top']; ?>"+"px");
        $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-right',"<?= $printableTemplateDefault['PrintableTemplate']['margin_right']; ?>"+"px");
        $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-down',"<?= $printableTemplateDefault['PrintableTemplate']['margin_down']; ?>"+"px");
        $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-left',"<?= $printableTemplateDefault['PrintableTemplate']['margin_left']; ?>"+"px");
    <?php endif; ?>

    $('#margin_top').on('change',function(){
        if( ! $('#margin_top').val() )
            $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-top',"0px");
        else
            $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-top',$('#margin_top').val() + "px");
    });
    $('#margin_right').on('change',function(){
        if( ! $('#margin_right').val() )
            $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-right',"0px");
        else
            $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-right',$('#margin_right').val() + "px");
    });
    $('#margin_down').on('change',function(){
        if( ! $('#margin_down').val() )
            $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-bottom',"0px");
        else
            $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-bottom',$('#margin_down').val() + "px");
    });
    $('#margin_left').on('change',function(){
        if( ! $('#margin_left').val() )
            $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-left',"0px");
        else
            $("#mytextarea_ifr").contents().find("#barcode_template").css('padding-left',$('#margin_left').val() + "px");
    });
    /*margins controll end*/

    $('.preview-button').on('click',
        function ()
        {
            var width = $('#mytextarea_ifr').contents().find("#barcode_template").width() * 1.5;
            var height = $('#mytextarea_ifr').contents().find("#barcode_template").height() * 1.5;

            var content =  $("#mytextarea_ifr").contents().find("#tinymce").contents().html();

            var margin_top = $('#margin_top').val();
            var margin_right = $('#margin_right').val();
            var margin_down = $('#margin_down').val();
            var margin_left = $('#margin_left').val();

            console.log(content);

            $('#preview').prop('action',"/owner/printable_templates/preview/"+width+"/"+height+"/"+margin_top+"/"+margin_right+"/"+margin_down+"/"+margin_left);
            $('#preview .content').val(content);
            $('#preview').submit();

            //send ajax post request to the method preview with the content of the tinyMCE.content
            //onSuccess reload the response in new window check : success
<!--            $.ajax({-->
<!--                url: "/owner/printable_templates/preview/"+width+"/"+height+"/"+margin_top+"/"+margin_right+"/"+margin_down+"/"+margin_left,-->
<!--                method: "POST",-->
<!--                dataType: "TEXT",-->
<!--                data: {content: content, type: '--><?//= $type; ?><!--'},-->
<!--                success: function( data ){-->
<!--                    var myWindow = window.open("", "Preview Window", "width="+width+",height="+height+"");-->
<!--                    myWindow.document.write('<html>');-->
<!--                    myWindow.document.write(data);-->
//<!--                    myWindow.document.write('</html>');-->
<!--                }-->
<!--            });-->
        }
    );

    //print button
    $('.print-button').on('click',
        function ()
        {
            var width = $('#width').val();
            var height = $("#height").val();

            var margin_top = $('#margin_top').val();
            var margin_right = $('#margin_right').val();
            var margin_down = $('#margin_down').val();
            var margin_left = $('#margin_left').val();

            var content =  $("#mytextarea_ifr").contents().find("#tinymce").contents().html();
            $('#start_print').prop('action',"/owner/printable_templates/print/"+width+"/"+height+"/"+margin_top+"/"+margin_right+"/"+margin_down+"/"+margin_left);
            $('#start_print .content').val(content);
            $('#start_print').submit();
            //send ajax post request to the method preview with the content of the tinyMCE.content
            //onSuccess reload the response in new window check : success
            /* $.ajax({
             url: "/owner/barcode_templates/print/"+width+"/"+height,
             method: "POST",
             dataType: "TEXT",
             data: {content: content},
             success: function(data ){
             //var myWindow = window.open("https://oidev2.daftra.dev/owner/barcode_templates/print", "Print Window", "width=800,height=600");
             //myWindow.document.write('<html>');

             //myWindow.document.write(data);
             //var uri = "data:text/html," + encodeURIComponent(data);
             //var newWindow = window.open(uri, "Print Window", "width=800,height=600");
             //myWindow.document.write('</html>');
             //myWindow.print();

             }
             });*/
        }
    );

    function validateFields(){

        var rules = {
            '.template_name': ['notEmpty']
        };


        var validationMessages = {
            isEqual: __('Total Debit Must be Equal to Total Credit'),
            notEmpty: __('Required'),
            moreThanOne: __('You Have to Add at Least 2 Journal Transactions'),
            isNumeric: __('Valid number required'),
            validDate: __('Valid date required'),
            isNotMinus: __('Positive number required'),
            lessThan: __('Value must be less than :value'),
            lessThanOrEqual: __('Value must be less than or equal to :value')

        };

        return validate(rules, validationMessages);
    }

//    $('.mce-i-code').click(function(
//        alert( "testing" );
//    ));


    $('#PrintableTemplateAddForm, #PrintableTemplateEditForm').submit(function( e ){
        var contentOfTextArea = $("#mytextarea_ifr").contents().find("#tinymce").contents().html();

        contentOfTextArea = contentOfTextArea.replace(/\.\.\/\.\.\/\.\./g,'https://'+'<?= getCurrentSite('subdomain'); ?>');
        for(var key in exception_placeholder_replacement)
        {
//            alert(exception_placeholder_replacement[key]);
//            alert(key);
//            contentOfTextArea.replace(exception_placeholder_replacement[key], key);
            contentOfTextArea = contentOfTextArea.replace(exception_placeholder_replacement[key], key);
        }

//        alert(contentOfTextArea);

        $('#mytextarea').val(contentOfTextArea) ;


        if (validateFields() ){
            console.log($('#PrintableTemplateAddForm, #PrintableTemplateEditForm').serialize());
        }
        else
        {
            e.preventDefault();
        }

    });


    $('#PrintableTemplateType').change(function(){

        var types = new Array();

        <?php foreach($types as $key => $val){ ?>
        types.push('<?= $val; ?>');
        <?php } ?>

        <!--          console.log(types);-->
        <!--          console.log($(this).val());-->
        <!--          console.log('--><?php //echo Router::url(array('action'=>'add'))?><!--/'+types[$(this).val()]);-->
        document.location.href='<?php echo Router::url(array('action'=>'add')); ?>/'+types[$(this).val()];
    });

});


</script>
<script type="text/javascript">

    $(function() {

        window.setInterval(function(){
            
            var current_content = tinymce.activeEditor.getContent();
            if( current_content.match(/id=\"barcode_template\"/gm) == null ){
               tinymce.activeEditor.setContent(main_content);
		   }
        }, 1000);

        $('#PrintableTemplateSendInvoice').change(function() {
            if ($(this).attr('checked')) {
                $('#PrintableTemplateSendDocuments').attr('checked', false);
                $('#PrintableTemplateSendDocuments').attr('disabled', false);
            } else {
                $('#PrintableTemplateSendDocuments').attr('checked', false);
                $('#PrintableTemplateSendDocuments').attr('disabled', true);
            }
        });

        $('#AttachmentsToggle').click(function() {
            $('#AttachmentsArea').toggle();
            $(this).toggleClass('Expand').toggleClass('Collapse');
            if ($('#sign').text() != '+') {
                $('#sign').text('+');
            } else {
                $('#sign').html('&ndash;');
            }
            return false;
        });

    });
</script>
