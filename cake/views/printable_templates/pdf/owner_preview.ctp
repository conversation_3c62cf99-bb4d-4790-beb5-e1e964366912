<?php
//$printableTemplate['PrintableTemplate']['content'] = str_replace([ "\r\n", "\n" ], '<br />', $printableTemplate['PrintableTemplate']['content'] );

//Configure::write("debug",2);
//dd($printableTemplate['PrintableTemplate']['content']);

$this->layout = '';

$site = getCurrentSite();

set_time_limit(600);

$files = array('screen.css', 'app.css', 'forms.css', 'box.css', 'common-pdf.css', 'pages.css','en_inside_printable_template.css');

if(getCurrentSite('language_code') == 7) $files[] = 'ar_inside_printable_template.css';

if (CurrentSiteLang() == 'ara') {
    $files[] = 'invoice-template-ar.css';
}else{
    $files[] = 'invoice-template.css';
}

$styleOut = '<style type="text/css">';
foreach ($files as $file) {
    $styleOut .= file_get_contents(CSS . $file);
}
$styleOut .= '</style>';

//Configure::write("debug",2);
//dd($printableTemplate);

$htmlOut .= '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml">';
$htmlOut .='<head><title>Printable Template</title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">';
$htmlOut .= $styleOut;

$footerOut = $htmlOut . '</head><body style="background: #fff">';
$footerOut .= $printableTemplate['PrintableTemplate']['page_footer'];
$footerOut .= '</body></html>';

if( $printableTemplate['PrintableTemplate']['paper_size'] == 'A4' )
{
    $paper_size = ' --page-size A4 ';
}
else
{
    $width = ' --page-width ' . $printableTemplate['PrintableTemplate']['width'] / 2.54 .  "inch";
    $height = ' --page-height ' .$printableTemplate['PrintableTemplate']['height'] / 2.54. "inch";

    $paper_size = $width . ' ' . $height;

//    die(debug($paper_size));
}

$dpi = " --dpi 96 --image-dpi 300 ";

$htmlOut .= '</head>
    <body style="background: #fff;margin:0px;padding:0px;">';
$htmlOut .= $printableTemplate['PrintableTemplate']['content'];

//die(debug( $printableTemplate['PrintableTemplate']['content'] ));

$htmlOut .='</body></html>';


$templates_dir  = '/tmp/' . SITE_HASH . DS;
if (!is_dir($templates_dir)) {

    mkdir($templates_dir, 0777, true);
}


$outPdf =    $templates_dir . 'Template_' . $printableTemplate['PrintableTemplate']['type'] . time() . '.pdf'; // getSiteFilesPath('printable_templates');

$outFile = $templates_dir.DS.$printableTemplate['PrintableTemplate']['type'] . '-' . time() . '.html';

file_put_contents($outFile, $htmlOut);

$margin = 
    " --margin-bottom " . ($printableTemplate['PrintableTemplate']['margin_down']  ?? 0) . "px" .
    " --margin-left "   . ($printableTemplate['PrintableTemplate']['margin_left']  ?? 0) . "px" .
    " --margin-right "  . ($printableTemplate['PrintableTemplate']['margin_right'] ?? 0) . "px" .
    " --margin-top "    . ($printableTemplate['PrintableTemplate']['margin_top']   ?? 0) . "px";

$printableTemplate['PrintableTemplate']['content'] = str_replace([ "\r\n", "\n" ], '', $printableTemplate['PrintableTemplate']['content'] );

debug($printableTemplate);
////FOOTER SECTION START////
$outFooter = "$templates_dir/Footer".time().".html";
file_put_contents($outFooter, $footerOut);
$footer = ' --footer-html '. $outFooter;
////FOOTER SECTION END//

//$brandOptions .= ' --header-html ' . APP . 'views' . DS . 'elements' . DS . 'templates_branded' . DS .'barcode_header.html --header-line ';
//dd($brandOptions );

$cmd = Wkhtmltopdf_Path . ' --encoding "utf-8" ' . $paper_size . ' ' . $margin .' ' . $footer .' ' . $dpi .  ' ' . $outFile . ' ' . $outPdf . ' 2>&1';
//die(debug($cmd));

//Configure::write('debug', 1);
//dd($cmd);

exec($cmd, $output, $status);
if (empty($save)) {
    header('Content-Type: application/pdf');
	     if(isset($this->params['url']['inline']) && $this->params['url']['inline']==true){
               header('Content-Disposition: inline; filename=Template-' . $printableTemplate['PrintableTemplate']['type'] . '.pdf');
            }else{
				header('Content-Disposition: attachment; filename=Template-' . $printableTemplate['PrintableTemplate']['type'] . '.pdf');
		
            }
    readfile($outPdf);
} else {
    $this->statementFileName = $outPdf;
}