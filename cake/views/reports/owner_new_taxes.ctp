<?php
$plugin = get_plugin_array();
$formats = getDateFormats('std');
$dateFormat = $formats[$owner['date_format']];
$total_col = 2;
echo $javascript->link(['report_sideMenu','report_adjust']);
function dataHasTaxes($data) {
    return (
        !empty($data['other']['details']) ||
        !empty($data['invoices']['details']) ||
        !empty($data['advance_payments']['details']) ||
        !empty($data['debit_note']['details']) ||
        !empty($data['incomes']['details']) ||
        !empty($data['expenses']['details'])  ||
        !empty($data['purchase_orders']['details']) ||
        !empty($data['purchase_orders_refunds']['details'])  ||
        !empty($data['purchase_orders_debit_notes']['details'])  ||
        !empty($data['purchase_orders_credit_notes']['details'])  ||
        !empty($data['refund_receipts']['details']) ||
        !empty($data['credit_notes']['details']) ||
        !empty($data['assets']['details'])
    );
}
?>
<div class="pages-head">
    <h1><?php __("Tax Declaration") ?></h1>
    <?php echo $this->element('reports/load-form') ?>
</div>
<div>
    <div class=" filter-results rounded-item" id="FilterDiv">
        <form action="" method="get" style="display: block;" id="FilterForm">
            <input type="hidden" name="details" value="<?php echo $details ?>" />
            <div class="row">
                <?
                if (false && check_permission(View_All_Tax_Report) and in_array(5, $plugin)) {
                    ?>
                    <div class="form-group text title col-md-<? echo $total_col ?> col-sm-<? echo $total_col ?> col-md-<? echo $total_col ?> col-xs-12">
                        <label for="Staff"><?php __("Staff") ?>:</label>
                        <select name="staff" id="staff"  class="form-control">
                            <option value=""><?php __('All Staff') ?></option>
                            <?php foreach ($staffs as $key => $value): ?>
                                <?php
                                $selected = '';
                                if ($selstaff == $key) {
                                    $selected = ' selected="selected"';
                                }
                                ?>
                                <option value="<?php echo $key; ?>"<?php echo $selected; ?>><?php echo $value; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?
                }
                ?>
                <div class="form-group text title col-md-<? echo $total_col ?> col-sm-<? echo $total_col ?> col-md-<? echo $total_col ?> col-xs-12">
                    <label for="Tax"><?php __("Tax") ?>:</label>
                    <select name="tax_id" id="Tax"  class="form-control">
                        <option value=""><?php __('All Taxes') ?></option>
                        <?php foreach ($taxes_list as $code => $tax): ?>
                            <?php
                            $selected = '';
                            if ($selTax == $code) {
                                $selected = ' selected="selected"';
                            }
                            ?>
                            <option value="<?php echo $code; ?>"<?php echo $selected; ?>><?php echo $tax; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div  class="form-group text title col-md-<? echo $total_col ?> col-sm-<? echo $total_col ?> col-md-<? echo $total_col ?> col-xs-12">
                    <label for="ReportType"><?php __("Revenue Type") ?>: <span class="tooltip" title="tax-revenue-type"></span></label>
                    <select name="invoice_type" id="ReportType"  class="form-control">
                        <?php $selected = ($invoice_type == 'accrual') ? ' selected="selected"' : '' ?>
                        <option value="accrual"<?php echo $selected; ?>><?php __("Issued (Accrual)") ?></option>
                        <?php $selected = ($invoice_type == 'cash') ? ' selected="selected"' : '' ?>
                        <option value="cash"<?php echo $selected; ?>><?php __("Fully Paid (Cash)") ?></option>
                        <?php $selected = ($invoice_type == 'partially_paid') ? ' selected="selected"' : '' ?>
                        <option value="partially_paid"<?php echo $selected; ?>><?php __("Partially Paid (Cash)") ?></option>

                    </select>
                </div>
                <div  class="form-group text title col-md-3 col-sm-6 col-xs-12>">
                    <?= $this->element('dateRangePicker'); ?>
                </div>
                <?php if(isset($_GET['details'])&&$_GET['details']==1) {?>
                <div  class="form-group text title col-md-2 col-sm-6 col-xs-12>">
                        <?php echo $form->input('group_by',['name'=>'group_by', 'type'=>'select','value'=>isset($_GET['group_by'])?$_GET['group_by']:'','options'=>['item'=>__('Item',true),'invoice'=>__('Invoice',true)]]); ?>
                </div>
                <?php } ?>

                <div class="form-group text title col-md-1 col-sm-2 col-md-2 col-xs-6">
                    <label for="Currency"><?php __("Currency") ?>:</label>
                    <select name="currency" id="Currency"  class="form-control">
                        <option <?= $selected ?> value="-1"><?php echo sprintf(__('All In (%s)', true), getAuthOwner("currency_code"))  ?></option>
                        <?php foreach ($currencies as $code => $crncy): ?>
                            <?php
                            $selected = '';
                            if ($currency == $code) {
                                $selected = ' selected="selected"';
                            }
                            ?>
                            <option value="<?php echo $code; ?>"<?php echo $selected; ?>><?php echo $code; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <?php if ( ifPluginActive(BranchesPlugin ) ){?>
                    <div  class="form-group left col-md-2">
                        <label for="BranchID"><?php __("Branch") ?>:</label>
                        <?php echo $this->element('branches/branches_select');?>
                    </div>
                <?php } ?>

                <div class="col-md-2">

                </div>
				
                <div class="col-md-2 col-sm-4 col-xs-12">
                    <label class="hidden-sm hidden-xs">&nbsp;</label>
                    <button class="Submit btn-md btn btn-block btn-success" type="submit"><?php __("Show Report") ?></button>
                </div>



            </div>



        </form>
        <div class="clear"></div>
    </div>
    <?php if (!empty($_GET['invoice_type'])) { ?>
        <?php if (!empty($report_data)): ?>
            <?php
            if (empty($details))
                echo $this->element('reports/new-tax-summary', ['tax_label' => $tax_label]), $this->element('reports/save-form', array('type' => 1));
            else
                echo $this->element('reports/tax-details'), $this->element('reports/save-form', array('type' => 1));
            ?>
        <?php else: ?>
            <div class="no-results">
                <h4><?php __("No results found to match these filters") ?></h4>
                <p><?php __("Change search filters and try again ") ?></p>
            </div>
        <?php endif; ?>
    <? } ?>
</div>
<?php
echo $javascript->link(array('jqueryui'));
echo $html->css(array('reports', 'jqueryui'), false, ['inline' => false]);
?>
<script type="text/javascript">//<![CDATA[
    function printReport() {
		window.print();
		if (typeof Android !== 'undefined') {
			Android.printIframe(window.location.href);
		}
	}
    $(function() {
        $('.Print').click(function() {
            printReport();
            return false;
        });
    });
    //]]></script>
