<?php
function dataHasTaxes($data) {
    return (
        !empty($data['other']['details']) ||
        !empty($data['invoices']['details']) ||
        !empty($data['incomes']['details']) ||
        !empty($data['expenses']['details'])  ||
        !empty($data['purchase_orders']['details']) ||
        !empty($data['purchase_orders_refunds']['details'])  ||
        !empty($data['purchase_orders_credit_notes']['details'])  ||
        !empty($data['refund_receipts']['details']) ||
        !empty($data['credit_notes']['details'])
    );
}
$this->autoLayout = $this->layout = '';
if (!isset($site)){
	$site = getCurrentSite();
}

$styles = '<style type="text/css">';

$styleFiles = array('common', 'screen', 'reports','reports-pdf');
if (CurrentSiteLang() == 'ara') {
$styleFiles[]='app_rtl';    
$styleFiles[]='rtl';    
}
foreach ($styleFiles as $file) {
	$styles .= file_get_contents(CSS . $file . '.css') . PHP_EOL; 
}

$styles .= '</style>';
$htmlOutput = $this->element('reports/'. (empty($details)?'tax-summary':'tax-details'), array('pdf' => true));
$htmlOutput = str_replace('"/js/', '"' . JS, $htmlOutput);

$js = getJsRootPath();
$layout =<<<LAYOUT
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
	:styles
	<script type="text/javascript" src="$js/jquery.js"></script>
</head>
<body style="background: #fff">
		<div class="content-area">
			:content
		</div>
</body>
</html>
LAYOUT;
$content = CakeString::insert($layout, array('styles' => $styles, 'content' => $htmlOutput));
$tok = md5(time());
$tmpHtml = '/tmp/' . $tok . '.html';

$SiteDir = '/tmp/'.SITE_HASH.DS;// getSiteFilesPath('invoices');
if (!is_dir($SiteDir)) {

	mkdir($SiteDir, 0777, true);
}

$tmpPdf = $SiteDir . $tok . '.pdf';
file_put_contents($tmpHtml, $content);


$cmd =  Wkhtmltopdf_Path.' --encoding "utf-8" ' . $tmpHtml . ' ' . $tmpPdf . ' 2>&1';

exec($cmd);
if($currency == '-1') {
    $currency = sprintf(__('All In (%s)', true), getAuthOwner('currency_code'));
}
$filename = $site['business_name'] . '-' . __('tax-report', true) . '-' . $currency . '.pdf';
header('Content-Type: application/pdf');
header('Content-Disposition: inline; filename="' . $filename.'"');

readfile($tmpPdf);
unlink($tmpHtml);
unlink($tmpPdf);
exit;
