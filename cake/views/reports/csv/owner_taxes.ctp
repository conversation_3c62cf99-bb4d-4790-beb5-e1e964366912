<?php
//when export to csv and user need to do sum by excel number format before give wrong data it return number as text
function csv_format_number($number, $code = false, $max_precision = 6)  
{
    return $number;
}

$taxes_count=0;
$has_bn1=false;
$has_bn2=false;
$undefined='[[!UNDEFINED?]]';
$site=getCurrentSite();
if(!empty($site['bn1_label']))
{
    $has_bn1=true;
}
if(!empty($site['bn2_label']))
{
    $has_bn2=true;
}

function remove_undefined($arr)
{
    $undefined='[[!UNDEFINED?]]';
    foreach ($arr as $i=>$item)
    {
        if($item==$undefined)
        {
            unset($arr[$i]);
        }
    }
    return $arr;

}

$net_taxable_total=0;
$net_tax_total=0;
if ($currency == '-1') {
    $allToSystemCurrency = 1;
    $currency = getAuthOwner('currency_code');
}

$this->autoLayout = $this->layout = false;
function dataHasTaxes($data) {
    return (
        !empty($data['other']['details']) ||
        !empty($data['invoices']['details']) ||
        !empty($data['incomes']['details']) ||
        !empty($data['expenses']['details'])  ||
        !empty($data['purchase_orders']['details']) ||
        !empty($data['purchase_orders_refunds']['details'])  ||
        !empty($data['purchase_orders_debit_notes']['details'])  ||
        !empty($data['purchase_orders_credit_notes']['details'])  ||
        !empty($data['refund_receipts']['details']) ||
        !empty($data['credit_notes']['details']) ||
        !empty($data['assets']['details'])

    );
}
foreach ($report_data as $tax_id => $data) {
    if (dataHasTaxes($data)) {
        $taxes_count++;
    }
}
$lines = array();
if (!empty($details))
    $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
        __('No.', true),__("Taxpayer",true), $has_bn1?__($site['bn1_label'],true):$undefined,$has_bn2?__($site['bn2_label'],true):$undefined,  __('Date', true), __('Item', true),__('Description', true), __('Taxable Amount', true), __('Taxes', true),
    ))));
else
    $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
        __('Tax Name', true), __('Taxable Amount', true), __('Taxes', true)
    )));


foreach ($report_data as $tax_id => $data) {



    if (dataHasTaxes($data)) {
        $taxable_total =
            $data['invoices']['total_without_tax'] +
            $data['incomes']['Income']['total_without_tax'] -
            $data['expenses'][__('Expense',true)]['total_without_tax']  -
            $data['assets'][__('Asset', true)]['total_without_tax'] -
            $data['assets'][__('Asset', true)]['total_sales_taxable_amount'] -
            $data['purchase_orders']['total_without_tax'] +
            $data['purchase_orders_refunds']['total_without_tax'] +
            $data['purchase_orders_credit_notes']['total_without_tax'] +
            $data['purchase_orders_debit_notes']['total_without_tax'] -
            ($data['refund_receipts']['total_without_tax']+$data['credit_notes']['total_without_tax']) +
            $data['other']['other']['total_without_tax'];

        $tax_total =
            $data['invoices']['total_tax'] +
            $data['incomes']['Income']['total_tax'] -
            $data['assets'][__('Asset', true)]['total_tax'] -
            $data['assets'][__('Asset', true)]['total_sales_tax'] -
            $data['expenses'][__('Expense',true)]['total_tax'] -
            $data['purchase_orders']['total_tax'] +
            $data['purchase_orders_refunds']['total_tax'] +
            $data['purchase_orders_credit_notes']['total_tax'] +
            $data['purchase_orders_debit_notes']['total_tax'] -
            ($data['refund_receipts']['total_tax'] + $data['credit_notes']['total_tax']) +
            $data['other']['other']['total_tax'];

        if (ifPluginActive(ExpensesPlugin)) {
            
            $taxable_total += $data['incomes'][__('Income', true)]['total_without_tax'] - $data['incomes'][__('Expense', true)]['total_without_tax'];
            $tax_total += $data['incomes'][__('Income', true)]['total_tax'] - $data['incomes'][__('Expense', true)]['total_tax'];
        }    


        if (empty($details)) {
            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                '', '', '',
            )));
            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                h($taxes[$tax_id]['name'] . ' (' . $taxes[$tax_id]['value'] . '%)'), '', '',
            )));
            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                $invoice_type == 'accrual' ? __('Sales Invoices', true) : __('Paid Invoices', true), csv_format_number($data['invoices']['total_without_tax'], $currency), csv_format_number($data['invoices']['total_tax'], $currency),
            )));

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                __('Sales Returns', true), csv_format_number(-1*($data['refund_receipts']['total_without_tax']+$data['credit_notes']['total_without_tax']), $currency), csv_format_number(-1*($data['refund_receipts']['total_tax']+$data['credit_notes']['total_tax']), $currency),
            )));

            if(ifPluginActive(InventoryPlugin)){
                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                    __('Purchase Invoices', true), csv_format_number(-1*$data['purchase_orders']['total_without_tax'], $currency), csv_format_number(-1*$data['purchase_orders']['total_tax'], $currency),
                )));

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                    __('Purchases Returns', true), csv_format_number($data['purchase_orders_refunds']['total_without_tax'] + $data['purchase_orders_debit_notes']['total_without_tax'] + $data['purchase_orders_credit_notes']['total_without_tax'], $currency), csv_format_number($data['purchase_orders_refunds']['total_tax'] + $data['purchase_orders_debit_notes']['total_tax'] + $data['purchase_orders_credit_notes']['total_tax'], $currency),
                )));
            }

             if(!empty($data['incomes'][__('Income',true)]['total_without_tax'])&&abs($data['incomes'][__('Income',true)]['total_without_tax'])>0.0001) {
                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                    __('Other Income', true), csv_format_number($data['incomes'][__('Income',true)]['total_without_tax'], $currency), csv_format_number($data['incomes'][__('Income',true)]['total_tax'], $currency),
                )));
                }

            if(!empty($data['expenses'][__('Expense',true)]['total_without_tax'])&&abs($data['expenses'][__('Expense',true)]['total_without_tax'])>0.0001) {
                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                    __('Expenses', true), csv_format_number(-1 * $data['expenses'][__('Expense', true)]['total_without_tax'], $currency), csv_format_number(-1 * $data['expenses'][__('Expense', true)]['total_tax'], $currency),
                )));
            }



                $key = __('Asset',true);
                $totalAssetValue = -1*($data['assets'][$key]['total_without_tax'] + $data['assets'][$key]['total_sales_taxable_amount']);
                $totalTaxValue = -1*($data['assets'][__('Asset',true)]['total_tax'] + $data['assets'][$key]['total_sales_tax']);


                /*$taxable_total += $totalAssetValue;
                $tax_total += $totalTaxValue;*/
                if(abs($data['other']['other']['total_without_tax']+$totalAssetValue)>0.0001 || abs($data['other']['other']['total_tax']+$totalTaxValue)>0.0001) {
                    $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                        __('Other', true), csv_format_number(($data['other']['other']['total_without_tax']+$totalAssetValue), $currency), csv_format_number(($data['other']['other']['total_tax']+$totalTaxValue), $currency),
                    )));
                }


            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                sprintf(__($taxes_count<2?"NET (%s)":"Total (%s)", true), $currency), csv_format_number($taxable_total, $currency), csv_format_number($tax_total, $currency),
            )));

            $net_taxable_total+=$taxable_total;
            $net_tax_total+=$tax_total;


        }
        else {
            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                '', '', '', '', '',
            )));

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                $taxes[$tax_id]['name'] . ' (' . $taxes[$tax_id]['value'] . '%)', '', '', '', '',
            )));

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                $invoice_type == 'accrual' ? __('Sales Invoices', true) : __('Paid Invoices', true), '', '', '', '',
            )));

            foreach ($data['invoices']['details'] as $row) {
                $client_t =  $clients[$row['client_id']]['Client']['business_name'];
                if (!$has_bn1 && !empty ( $clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label']))
                {
                    $client_t .= " - ".$clients[$row['client_id']]['Client']['bn1_label'].": ".$clients[$row['client_id']]['Client']['bn1'];
                }
                if (!$has_bn2 && !empty ( $clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label']))
                {
                    $client_t .= " - ".$clients[$row['client_id']]['Client']['bn2_label'].": ".$clients[$row['client_id']]['Client']['bn2'];
                }
                                                
                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    $row['id'], $client_t, $has_bn1?$clients[$row['client_id']]['Client']['bn1']:$undefined,$has_bn2?$clients[$row['client_id']]['Client']['bn2']:$undefined ,format_date(($row['date'])), $row['item'], strip_tags($row['description']),csv_format_number($row['subtotal_without_tax'], $currency), csv_format_number($row['tax'], $currency),
                ))));
            }

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                __('Subtotal', true), '',$has_bn1?'':$undefined, $has_bn2?'':$undefined,  '', '','', csv_format_number($data['invoices']['total_without_tax'], $currency), csv_format_number($data['invoices']['total_tax'], $currency),
            ))));

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                '', '', '', '', '',
            )));

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                __('Sales Returns', true), '', '', '', '',
            )));

            foreach ($data['refund_receipts']['details'] as $row) {
                $client_t =  $clients[$row['client_id']]['Client']['business_name'];
                if ( !empty ( $clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label']))
                {
                    $client_t .= " - ".$clients[$row['client_id']]['Client']['bn1_label'].": ".$clients[$row['client_id']]['Client']['bn1'];
                }
                if ( !empty ( $clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label']))
                {
                    $client_t .= " - ".$clients[$row['client_id']]['Client']['bn2_label'].": ".$clients[$row['client_id']]['Client']['bn2'];
                }

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    $row['id'], $client_t, $has_bn1?$clients[$row['client_id']]['Client']['bn1']:$undefined,$has_bn2?$clients[$row['client_id']]['Client']['bn2']:$undefined  , format_date(($row['date'])), $row['item'],strip_tags($row['description']), csv_format_number(-1 * $row['subtotal_without_tax'], $currency), csv_format_number(-1 * $row['tax'], $currency),
                ))));
            }

            foreach ($data['credit_notes']['details'] as $row) {
                $client_t =  $clients[$row['client_id']]['Client']['business_name'];
                if ( !empty ( $clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label']))
                {
                    $client_t .= " - ".$clients[$row['client_id']]['Client']['bn1_label'].": ".$clients[$row['client_id']]['Client']['bn1'];
                }
                if ( !empty ( $clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label']))
                {
                    $client_t .= " - ".$clients[$row['client_id']]['Client']['bn2_label'].": ".$clients[$row['client_id']]['Client']['bn2'];
                }

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    $row['id'], $client_t, $has_bn1?$clients[$row['client_id']]['Client']['bn1']:$undefined,$has_bn2?$clients[$row['client_id']]['Client']['bn2']:$undefined  , format_date(($row['date'])), $row['item'],strip_tags($row['description']), csv_format_number(-1 * $row['subtotal_without_tax'], $currency), csv_format_number( -1 * $row['tax'], $currency),
                ))));
            }

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                __( 'Subtotal', true),
                '',
                $has_bn1?'':$undefined,
                $has_bn2?'':$undefined,
                '',
                '',
                '',
                csv_format_number(-1 * ($data['refund_receipts']['total_without_tax'] +  $data['credit_notes']['total_without_tax']), $currency),
                csv_format_number(-1 * ($data['refund_receipts']['total_tax'] + $data['credit_notes']['total_tax']), $currency),
            ))));


            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                '', '', '', '', '',
            )));

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                __('Purchase Invoices', true), '', '', '', '',
            )));

            foreach($data['purchase_orders']['details'] as $row) {
                $supplier_t =  $suppliers[$row['supplier_id']]['Supplier']['business_name'];

                if (!empty ($suppliers[$row['supplier_id']]['Supplier']['bn1']) && empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label'])) {
                    $suppliers[$row['supplier_id']]['Supplier']['bn1_label'] = __("VAT Number", true);
                }
                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    $row['id'], $supplier_t,$has_bn1?$suppliers[$row['supplier_id']]['Supplier']['bn1']:$undefined, $has_bn2?$suppliers[$row['supplier_id']]['Supplier']['bn2']:$undefined , format_date(($row['date'])), $row['item'],strip_tags($row['description']), csv_format_number( -1 * $row['subtotal_without_tax'], $currency), csv_format_number(-1 * $row['tax'], $currency),
                ))));
            }

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                __('Subtotal', true), '',$has_bn1?'':$undefined, $has_bn2?'':$undefined, '', '', '', csv_format_number(-1 * $data['purchase_orders']['total_without_tax'], $currency), csv_format_number(-1 * $data['purchase_orders']['total_tax'], $currency),
            ))));
//            }

//            if(!empty($data['purchase_orders_refunds']['details'])) {
            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                '', '', '', '', '',
            )));



            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                __('Purchases Returns', true), '', '', '', '',
            )));

            foreach($data['purchase_orders_refunds']['details'] as $row) {
                $supplier_t =  $suppliers[$row['supplier_id']]['Supplier']['business_name'];
                if (!$has_bn1&& !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn1']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label']))
                {
                    $supplier_t .= " - ".$suppliers[$row['supplier_id']]['Supplier']['bn1_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn1'];
                }
                if (!$has_bn2&&  !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn2']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn2_label']))
                {
                    $supplier_t .= " - ".$suppliers[$row['supplier_id']]['Supplier']['bn2_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn2'];
                }

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    $row['id'], $supplier_t ,$has_bn1?$suppliers[$row['supplier_id']]['Supplier']['bn1']:$undefined, $has_bn2?$suppliers[$row['supplier_id']]['Supplier']['bn2']:$undefined , format_date(($row['date'])), $row['item'],strip_tags($row['description']), csv_format_number($row['subtotal_without_tax'], $currency), csv_format_number($row['tax'], $currency),
                ))));
            }

            foreach($data['purchase_orders_debit_notes']['details'] as $row) {
                $supplier_t =  $suppliers[$row['supplier_id']]['Supplier']['business_name'];
                if (!$has_bn1&& !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn1']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label']))
                {
                    $supplier_t .= " - ".$suppliers[$row['supplier_id']]['Supplier']['bn1_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn1'];
                }
                if (!$has_bn2&&  !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn2']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn2_label']))
                {
                    $supplier_t .= " - ".$suppliers[$row['supplier_id']]['Supplier']['bn2_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn2'];
                }

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    $row['id'], $supplier_t ,$has_bn1?$suppliers[$row['supplier_id']]['Supplier']['bn1']:$undefined, $has_bn2?$suppliers[$row['supplier_id']]['Supplier']['bn2']:$undefined , format_date(($row['date'])), $row['item'],strip_tags($row['description']), csv_format_number($row['subtotal_without_tax'], $currency), csv_format_number($row['tax'], $currency),
                ))));
            }

            foreach($data['purchase_orders_credit_notes']['details'] as $row) {
                $supplier_t =  $suppliers[$row['supplier_id']]['Supplier']['business_name'];
                if (!$has_bn1&& !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn1']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn1_label']))
                {
                    $supplier_t .= " - ".$suppliers[$row['supplier_id']]['Supplier']['bn1_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn1'];
                }
                if (!$has_bn2&&  !empty ( $suppliers[$row['supplier_id']]['Supplier']['bn2']) && !empty($suppliers[$row['supplier_id']]['Supplier']['bn2_label']))
                {
                    $supplier_t .= " - ".$suppliers[$row['supplier_id']]['Supplier']['bn2_label'].": ".$suppliers[$row['supplier_id']]['Supplier']['bn2'];
                }

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    $row['id'], $supplier_t ,$has_bn1?$suppliers[$row['supplier_id']]['Supplier']['bn1']:$undefined, $has_bn2?$suppliers[$row['supplier_id']]['Supplier']['bn2']:$undefined , format_date(($row['date'])), $row['item'],strip_tags($row['description']), csv_format_number($row['subtotal_without_tax'], $currency), csv_format_number($row['tax'], $currency),
                ))));
            }

            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                __( 'Subtotal', true), '',$has_bn1?'':$undefined, $has_bn2?'':$undefined, '', '', '',csv_format_number($data['purchase_orders_refunds']['total_without_tax'] + $data['purchase_orders_debit_notes']['total_without_tax'] + $data['purchase_orders_credit_notes']['total_without_tax'], $currency), csv_format_number($data['purchase_orders_refunds']['total_tax'] + $data['purchase_orders_debit_notes']['total_tax'] + $data['purchase_orders_credit_notes']['total_tax'], $currency),
            ))));
//            }





            if (!empty($data['incomes']['details'])) {


                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    '', '', '', '', '',
                ))));

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                    __('Other Income', true), '', '', '', '',
                )));


                foreach ($data['incomes']['details'] as $row) {
                    $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                        $row['id'], " ",$has_bn1?'':$undefined, $has_bn2?'':$undefined, format_date(($row['date'])), $row['item'], strip_tags($row['description']), csv_format_number($row['subtotal_without_tax'], $currency), csv_format_number($row['tax'], $currency),
                    ))));
                }

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    __('Subtotal', true), '',$has_bn1?'':$undefined, $has_bn2?'':$undefined, '', '', '', csv_format_number($data['incomes'][__('Income', true)]['total_without_tax'], $currency), csv_format_number($data['incomes'][__('Income',true)]['total_tax'], $currency),
                ))));
            }

            if (!empty($data['expenses']['details'])) {

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                    '', '', '', '', '',
                )));

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                    __('Expenses', true), '', '', '', '',
                )));

                foreach ($data['expenses']['details'] as $row) {
                    $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                        $row['id'],$row['business_name'] ,$has_bn1?$row['bn1']:$undefined, $has_bn2?'':$undefined,  format_date(($row['date'])), $row['item'], strip_tags($row['description']), csv_format_number( -1 * $row['subtotal_without_tax'], $currency), csv_format_number(-1 * $row['tax'], $currency),
                    ))));
                }

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    __('Subtotal', true), '',$has_bn1?'':$undefined, $has_bn2?'':$undefined, '', '','',
                    csv_format_number(-1*$data['expenses'][__('Expense',true)]['total_without_tax'], $currency), 
                    csv_format_number(-1*$data['expenses'][__('Expense',true)]['total_tax'], $currency),
                ))));

                
            }
//            if(!empty($data['purchase_orders']['details'])) {


            if (!empty($data['other']['details']) || !empty($data['assets']['details'])) {
                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                    '', '', '', '', '',
                )));


                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                    __('Other', true), '', '', '', '',
                )));
                foreach ($data['other']['details'] as $row) {
                    $client_t = $clients[$row['client_id']]['Client']['business_name'];
                    if (!$has_bn1 && !empty ($clients[$row['client_id']]['Client']['bn1']) && !empty($clients[$row['client_id']]['Client']['bn1_label'])) {
                        $client_t .= " - " . $clients[$row['client_id']]['Client']['bn1_label'] . ": " . $clients[$row['client_id']]['Client']['bn1'];
                    }
                    if (!$has_bn2 && !empty ($clients[$row['client_id']]['Client']['bn2']) && !empty($clients[$row['client_id']]['Client']['bn2_label'])) {
                        $client_t .= " - " . $clients[$row['client_id']]['Client']['bn2_label'] . ": " . $clients[$row['client_id']]['Client']['bn2'];
                    }

                    $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                        $row['id'], $client_t,$has_bn1?$clients[$row['client_id']]['Client']['bn1']:$undefined, $has_bn2?$clients[$row['client_id']]['Client']['bn2']:$undefined, format_date(($row['date'])), $row['item'], strip_tags($row['description']), csv_format_number($row['subtotal_without_tax'], $currency), csv_format_number($row['subtotal_with_tax'], $currency),
                    ))));
                }

                foreach ($data['assets']['details'] as $row) {
                    $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                        $row['id'],'',$has_bn1?'':$undefined, $has_bn2?'':$undefined, format_date(($row['date'])), $row['item'], strip_tags($row['description']), csv_format_number(-1 * $row['subtotal_without_tax'], $currency), csv_format_number(-1 * $row['tax'], $currency),
                    ))));
                }

                $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                    __('Subtotal', true),
                    '',
                    $has_bn1?'':$undefined,
                    $has_bn2?'':$undefined,
                    '',
                    '',
                    '',
                    csv_format_number($data['other']['other']['total_without_tax'] - 1 * ($data['assets'][__('Asset', true)]['total_without_tax']), $currency),
                    csv_format_number($data['other']['other']['total_tax'] - 1 * $data['assets'][__('Asset', true)]['total_tax'], $currency),
                ))));
            }


            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
                '', '', '', '', '',
            )));


            $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', remove_undefined(array(
                sprintf(__($taxes_count<2?"NET (%s)":"Total (%s)", true), $currency), '',$has_bn1?'':$undefined, $has_bn2?'':$undefined,  '', '', '', strip_tags(csv_format_number($taxable_total, $currency)), strip_tags(csv_format_number($tax_total, $currency)),
            ))));
            $net_taxable_total+=$taxable_total;
            $net_tax_total+=$tax_total;
        }
    }
}

if($taxes_count>1) {
    $lines[] = implode(CSV_SEPARATOR, array("", "", ""));
    $lines[] = implode(CSV_SEPARATOR, array_map('csv_quote', array(
        sprintf(__("NET (%s)", true), $currency), '', '', '', '', csv_format_number($net_taxable_total, $currency), csv_format_number($net_tax_total, $currency),
    )));
}



$lines[] = implode(CSV_SEPARATOR, array("", "", ""));

$data=implode(PHP_EOL, $lines);
header('Content-Disposition: attachment; filename=taxes.csv');
echo $data;


?>
