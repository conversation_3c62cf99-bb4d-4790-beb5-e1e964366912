<?php
$date_from = $_GET['date_from'] ?? null;
$date_to = $_GET['date_to'] ?? null;
$get_url = '?date_from='.$date_from.'&date_to='.$date_to.'&group_by_date=';
$get_url .= $_GET['type'] == 'yearly' ? 'annually' : $_GET['type'];
$get_url .= isset($_GET['income_type']) ? ('&income_type=' . $_GET['income_type']) : '';
echo $javascript->link(['report_sideMenu']);
if (!isset($_GET['quick'])) {
	echo $javascript->link(['report_adjust']);
}
if($_GET['currency'])
$get_url .= '&currency='.$_GET['currency'];
else
	$get_url .= '&currency=-1';

if (!empty($_GET['quick'])) {
 if(empty($reportData)){
    die(); 
 }
}
if (empty($_GET['quick'])) {
    $formats = getDateFormats('std');
    $format = $formats[$owner['date_format']];
    $this->set('dateFormat', $format);
    ?>
<div class="pages-head fixed-div">
    <div class="row" style="max-width: none;">
        <div class="col-xs-6 col-sm-5 actions-selections-head">
            <h1><?php __("Profit & Loss Report (Cash)") ?></h1>
        </div>
        <div class="col-sm-7 col-xs-6">
           
            <div class="top-actions">
                <div class="btn-group d-inline-flex">
                    <a href="<?= Router::url(array('action' => 'accounts_profit_accrual', '?' => $get_url)) ?>"
                        title="<?php __('Accrual') ?>" data-param1="report_type"
                        class="view_level text-main s2020 btn-s2020 btn-secondary-s2020">
                        <i class="fas fa-file-invoice s2020"></i> <?php __('Accrual (Billed)') ?></a>

                    <a href="#" title="<?php __('Cash') ?>"
                        class=" view_level text-main s2020 btn-s2020 btn-secondary-s2020 active">
                        <i style="display: inline-block; height: 12px;">

                            <svg fill="#3a3e63" class="d-block" xmlns="http://www.w3.org/2000/svg" height="1em"
                                viewBox="0 0 640 512">
                                <path
                                    d="M535 41c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l64 64c4.5 4.5 7 10.6 7 17s-2.5 12.5-7 17l-64 64c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l23-23L384 112c-13.3 0-24-10.7-24-24s10.7-24 24-24l174.1 0L535 41zM105 377l-23 23L256 400c13.3 0 24 10.7 24 24s-10.7 24-24 24L81.9 448l23 23c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0L7 441c-4.5-4.5-7-10.6-7-17s2.5-12.5 7-17l64-64c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9zM96 64H337.9c-3.7 7.2-5.9 15.3-5.9 24c0 28.7 23.3 52 52 52l117.4 0c-4 17 .6 35.5 13.8 48.8c20.3 20.3 53.2 20.3 73.5 0L608 169.5V384c0 35.3-28.7 64-64 64H302.1c3.7-7.2 5.9-15.3 5.9-24c0-28.7-23.3-52-52-52l-117.4 0c4-17-.6-35.5-13.8-48.8c-20.3-20.3-53.2-20.3-73.5 0L32 342.5V128c0-35.3 28.7-64 64-64zm64 64H96v64c35.3 0 64-28.7 64-64zM544 320c-35.3 0-64 28.7-64 64h64V320zM320 352a96 96 0 1 0 0-192 96 96 0 1 0 0 192z">
                                </path>
                            </svg>
                        </i>
                        <?php __('Cash (Collected)') ?></a>
                </div>
                <button class="Print text-main s2020 btn-s2020 btn-secondary-s2020" type="button"><i
                        class="fa fa-print"></i><?php __('Print') ?></button>
                <div class="btn-group">
                    <button type="button" class="s2020 btn-s2020 btn-primary-s2020 dropdown-toggle"
                        data-toggle="dropdown" aria-expanded="false"><i class="fa fa-cloud-download"></i>
                        <?php __("Export Options") ?>
                        <span class="caret"></span>
                    </button>

                    <ul class="dropdown-menu" role="menu">
                        <?php
                            $params = $this->params['url'];
                            unset($params['ext'], $params['url']);
                            $owner = getAuthOwner();
                            ?>
                        <li><?php echo $html->link(__('Export to CSV', true), array('ext' => 'csv', '?' => $params), array('class' => 'op-export')) ?>
                        </li>
                        <li><?php echo $html->link(__('Export to Excel', true), array('ext' => 'xlsx', '?' => $params), array('class' => 'op-export')) ?>
                        </li>
                        <li><?php echo $html->link(__('Export to PDF', true), array('ext' => 'pdf', '?' => $params), array('class' => 'Pdf')); ?>
                        </li>
                        <li><?php echo $html->link(__('Export to PDF no graph', true), array('ext' => 'pdf', '?' => array_merge($params, array('no_graph' => 1))), array('class' => 'Pdf', 'target' => '_blank')); ?>
                        </li>
                        <li class="divider"></li>
                        <li><?php echo $html->link(__('Print', true), '#', array('class' => 'Print')) ?></li>
                    </ul>
                </div>
            </div>
             
        </div>
    </div>
</div>
<div>
    <?php echo $this->element('reports/profit-form') ?>
    <?php } ?>
    <?php
            //debug(!empty($type));
            //debug(!empty(!empty($reportData)));
        ?>
    <?php if (!empty($type)): ?>
    <?php if (!empty($reportData)): ?>
    <?php if (empty($_GET['quick'])) { ?>



    <div class="clearfix"></div>
    <?php echo $this->element('reports/profit-header'); ?>
    <?php } ?>

    <?php echo $this->element('reports/profit'); ?>
    <?php if (empty($_GET['quick'])) { ?>
</div>
 
<div class="clearfix"></div>
<? } ?>

<?php else: ?>
<?php if (empty($_GET['quick'])) { ?>
<div class="no-results">
    <h4><?php __("No results found to match these filters") ?></h4>
    <p><?php __("Change search filters and try again ") ?></p>
</div>
<? } ?>

<?php endif; ?>
<?php endif; ?>
</div>
<?php
if (empty($_GET['quick'])) {
    echo $javascript->link(array('jqueryui'));
    echo $html->css(array('reports', 'jqueryui'), false, ['inline' => false]);
    $formats = getDateFormats('js');
    $ownerDateFormat = $formats[$owner['date_format']];
    ?>
<script type="text/javascript">
//<![CDATA[
var ownerDateFormat = '<?php echo $ownerDateFormat ?>';
function printReport() {
		window.print();
		if (typeof Android !== 'undefined') {
			Android.printIframe(window.location.href);
		}
	}
$(function() {
    $('.has-calendar').datepicker({
        dateFormat: ownerDateFormat
    });
    $('.Print').click(function() {
        printReport();
        return false;
    });
    $('#PaymentTable  div.report table td').each(function() {
        var cellText = $(this).html();
        cellText = cellText.replace("&nbsp;", " ");
        $(this).html(cellText);
    });
});
//]]>
</script>
<?php } ?>
<style>
body {
    overflow-x: auto;
}

#PaymentTable div.report {
    overflow-x: auto;
}

[aria-label="A tabular representation of the data in the chart."] {
    display: none;
}
@media print {
  @page {
    size: landscape;
  }
}
</style>
<?php //debug($jsonParams); ?>
<?php //debug($reportData); ?>