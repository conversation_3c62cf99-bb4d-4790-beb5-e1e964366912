<?php echo $html->css('nav-tabs', false);

/**
 * Get default url for single report
 * using buttons array
 */
function get_default_button_url($array) {
    $url = empty($array) ? '' : $array[0]['url'];
    foreach ($array as $button) {
        foreach ($button as $key => $value) {
            if ($key === 'default') {
                $url = $button['url'];
            }
        }
    }
    return $url;
}

function check_report_permission($permissions = []): bool
{
    if (getAuthOwner('staff_id') == 0) {
        return true;
    }
    return check_permission($permissions);
}

/**
 * Generate custom report array to use
 * in layout
 */
$get_custom_report_array_for_view = function ($type) use ($typeGroupedJsonReports) {
    $arr = [];
    //warning suppress
    if(array_key_exists($type, $typeGroupedJsonReports))
    foreach($typeGroupedJsonReports[$type] as $k => $jsonReport) {
        $arr[] = [
            'condition' => true,
            'title' => $jsonReport['Report']['name'],
            'icon' => 'fas fa-chart-pie fs-22 bg-warning-50 text-white',
            'buttons' => [
                [
                    'url' => Router::url(['action' => 'report', $jsonReport['Report']['id']]),
                    'title' => 'View',
                    'icon' => 'fas fa-eye s2020 fs-18',
                    'default' => true
                ],
            ]
        ];
    }
    return $arr;
};

$app_report_array_for_view = function ($type) use ($appReports) {
    if (!isset($appReports[$type])) {
        return [];
    }

    return array_map(function ($jsonReport) {
        return [
            'condition' => true,
            'title' => $jsonReport['Report']['name'] ?? 'Untitled Report',
            'icon' => 'fas fa-chart-pie fs-22 bg-warning-50 text-white',
            'buttons' => [
                [
                    'url' => Router::url(['action' => 'app_report', $jsonReport['Report']['id'] ?? 0]),
                    'title' => 'View',
                    'icon' => 'fas fa-eye s2020 fs-18',
                    'default' => true,
                ],
            ],
        ];
    }, $appReports[$type]);
};

/**
 * Custom reports
 */
$custom_report_types = [
    'sales_custom_reports' => Report::SALES_REPORT_TYPE,
    'purchases_custom_reports' => Report::PURCHASES_REPORT_TYPE,
    'inventory_custom_reports' => Report::STORES_REPORT_TYPE,
    'work_orders_custom_reports' => Report::WORK_ORDER_REPORT_TYPE,
    'clients_custom_reports' => Report::CLIENTS_REPORT_TYPE,
    'accounting_custom_reports' => Report::ACCOUNTING_REPORT_TYPE,
    'sms_custom_reports' => Report::SMS_REPORT_TYPE,
    'pnr_custom_reports' => Report::PNR_REPORT_TYPE,
];

foreach ($custom_report_types as $key => $report_type) {
    ${$key.'_type'} = $report_type;
    ${$key} = $get_custom_report_array_for_view($report_type);
}

$appSalesReports = $app_report_array_for_view(Report::SALES_REPORT_TYPE);
$appPurchasesReports = $app_report_array_for_view(Report::PURCHASES_REPORT_TYPE);
$appInventoryReports = $app_report_array_for_view(Report::STORES_REPORT_TYPE);
$appWorkOrdersReports = $app_report_array_for_view(Report::WORK_ORDER_REPORT_TYPE);
$appClientsReports = $app_report_array_for_view(Report::CLIENTS_REPORT_TYPE);
$appAccountingReports = $app_report_array_for_view(Report::ACCOUNTING_REPORT_TYPE);
$appSMSReports = $app_report_array_for_view(Report::SMS_REPORT_TYPE);
$appPNRRReports = $app_report_array_for_view(Report::PNR_REPORT_TYPE);
$appTimeTrackingReports = $app_report_array_for_view(Report::TIME_TRACKING_REPORT_TYPE);

/**
 * Reports List
 */

$reports_list = [
    // Invoices Reports
    'invoices' => [
        'condition' => isset($cats['invoices']) && check_report_permission([Invoices_View_Invoices_Details, Invoices_View_All_Invoices, View_His_Own_Reports]),
        'sections' => [
            // Invoices Segmented Reports
            [
                'condition' => true,
                'title' => 'Invoices Segmented Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['invoices']['subs']['client_revenue_summary']),
                        'title' => 'Sales by Client',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tie fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['client_revenue_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['client_revenue_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['staff_revenue_summary']),
                        'title' => 'Sales by Staff',
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['staff_revenue_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['staff_revenue_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['sales_person_revenue_summary']),
                        'title' => 'Sales by Sales Person',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tag fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['sales_person_revenue_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['sales_person_revenue_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Product Sales Periodic Reports
            [
                'condition' => true,
                'title' => 'Product Sales Periodic Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['inventory']['subs']['daily_report_summary']),
                        'title' => 'Daily Product Sales',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day fs-22 bg-primary-2 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['daily_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['daily_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['inventory']['subs']['weekly_report_summary']),
                        'title' => 'Weekly Product Sales',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['weekly_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['weekly_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['inventory']['subs']['monthly_report_summary']),
                        'title' => 'Monthly Product Sales',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['monthly_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['monthly_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['inventory']['subs']['yearly_report_summary']),
                        'title' => 'Yearly Product Sales',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['yearly_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['yearly_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Invoices Periodic Reports
            [
                'condition' => true,
                'title' => 'Invoices Periodic Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['invoices']['subs']['daily_revenue_summary']),
                        'title' => 'Daily Sales',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day fs-22 bg-primary-2 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['daily_revenue_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['daily_revenue_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['weekly_revenue_summary']),
                        'title' => 'Weekly Sales',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['weekly_revenue_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['weekly_revenue_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['monthly_revenue_summary']),
                        'title' => 'Monthly Sales',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['monthly_revenue_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['monthly_revenue_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['yearly_revenue_summary']),
                        'title' => 'Yearly Sales',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['yearly_revenue_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['yearly_revenue_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Payments Segmented Reports
            [
                'condition' => true,
                'title' => 'Payments Segmented Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['invoices']['subs']['client_payments_summary']),
                        'title' => 'Payments by Client',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tie fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['client_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['client_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['staff_payments_summary']),
                        'title' => 'Payments by Staff',
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['staff_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['staff_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['method_payments_summary']),
                        'title' => 'Payments by Method',
                        'subtitle' => '',
                        'icon' => 'fas fa-cash-register fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['method_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['method_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['advance_payments_report_detailed']),
                        'title' => 'Advance Payments',
                        'subtitle' => '',
                        'icon' => 'mdi mdi-note-text fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['advance_payments_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['advance_payments_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Payments Periodic Reports
            [
                'condition' => true,
                'title' => 'Payments Periodic Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['invoices']['subs']['daily_payments_summary']),
                        'title' => 'Daily Payments',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day fs-22 bg-primary-2 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['daily_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['daily_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['weekly_payments_summary']),
                        'title' => 'Weekly Payments',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['weekly_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['weekly_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['monthly_payments_summary']),
                        'title' => 'Monthly Payments',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['monthly_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['monthly_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['invoices']['subs']['yearly_payments_summary']),
                        'title' => 'Yearly Payments',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['yearly_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['invoices']['subs']['yearly_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Custom Sales Reports
            [
                'condition' => isset($typeGroupedJsonReports[$sales_custom_reports_type]) && !empty($typeGroupedJsonReports[$sales_custom_reports_type]),
                'title' => Report::$typesData[$sales_custom_reports_type]['title'],
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $sales_custom_reports
            ],
            // Product Sales Profit Reports
            [
                'condition' => ifPluginActive(InventoryPlugin) && isset($cats['accounting']['subs']['products_profit']['url']),
                'title' => 'Product Sales Profit',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => true,
                        'title' => __('Product Sales Profit', true) . ' - ' . __('Product', true),
                        'subtitle' => '',
                        'icon' => 'fas fa-box-open fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['products_profit']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => __('Product Sales Profit', true) . ' - ' . __('Client', true),
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tie fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['products_profit']['url'] . '?type=client',
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => __('Product Sales Profit', true) . ' - ' . __('Staff', true),
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['products_profit']['url'] . '?type=staff',
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => __('Product Sales Profit', true) . ' - ' . __('Sales Person', true),
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tag fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['products_profit']['url'] . '?type=sales_person',
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                ]
            ],
            // Profit Periodic Reports
            [
                'condition' => ifPluginActive(InventoryPlugin) && isset($cats['accounting']['subs']['daily_profit_report_summary']),
                'title' => 'Profit Periodic Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['accounting']['subs']['daily_profit_report_summary']),
                        'title' => 'Daily Profits',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day fs-22 bg-primary-2 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['daily_profit_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['accounting']['subs']['daily_profit_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['weekly_profit_report_summary']),
                        'title' => 'Weekly Profits',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['weekly_profit_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['accounting']['subs']['weekly_profit_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['monthly_payments_summary']),
                        'title' => 'Monthly Profits',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['monthly_profit_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['accounting']['subs']['monthly_payments_summary']['url'] /*warning suppress*/ ?? '',
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['yearly_profit_report_summary']),
                        'title' => 'Yearly Profits',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['yearly_profit_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['accounting']['subs']['yearly_profit_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Item Sales Segmented Reports
            [
                'condition' => ifPluginActive(InventoryPlugin),
                'title' => 'Item Sales Segmented Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => true,
                        'title' => 'Item Sales by Item',
                        'subtitle' => '',
                        'icon' => 'fas fa-box-open fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['product_product_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['product_product_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'Item Sales by Category',
                        'subtitle' => '',
                        'icon' => 'fas fa-boxes-alt fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['category_product_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['category_product_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'Item Sales by Brand',
                        'subtitle' => '',
                        'icon' => 'fas fa-tag fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['brand_product_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['brand_product_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => ifPluginActive(StaffPlugin),
                        'title' => 'Item Sales by Staff',
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['staff_product_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['staff_product_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => ifPluginActive(StaffPlugin),
                        'title' => 'Item Sales by Sales Person',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tag fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['sales_person_product_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['sales_person_product_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'Item Sales by Client',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tie fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['client_product_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['inventory']['subs']['client_product_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            [
                'condition' => (!empty($appSalesReports)),
                'title' => __t('Additional Reports'),
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $appSalesReports
            ],
        ]
    ],
    // Attendance Reports
    'employees_reports' => [
        'condition' => isset($cats['employees_reports']) && check_report_permission([View_His_Own_Reports]),
        'sections' => [
            // Employees Report
            [
                'condition' => check_permission(View_His_Own_Reports),
                'title' => 'Employees Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['employees_reports']['subs']['employees_residency_status']),
                        'title' => $cats['employees_reports']['subs']['employees_residency_status']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['employees_residency_status']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                ]
            ],
            // Attendance Report
            [
                'condition' => ifPluginActive(HRM_ATTENDANCE_PLUGIN) && check_permission(VIEW_ATTENDANCE_REPORT),
                'title' => 'Attendance Report',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['employees_reports']['subs']['detailed_attendance_report_single_employees']),
                        'title' => $cats['employees_reports']['subs']['detailed_attendance_report_single_employees']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['detailed_attendance_report_single_employees']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['detailed_attendance_report_multiple_employees']),
                        'title' => $cats['employees_reports']['subs']['detailed_attendance_report_multiple_employees']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-users fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['detailed_attendance_report_multiple_employees']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['leave_balance_report']),
                        'title' => $cats['employees_reports']['subs']['leave_balance_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-users fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['leave_balance_report']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],

                ]
            ],
            // Summary Attendance Report
            [
                'condition' => ifPluginActive(HRM_ATTENDANCE_PLUGIN) && check_permission(VIEW_ATTENDANCE_REPORT),
                'title' => 'Summary Attendance Report',
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['employees_reports']['subs']['summary_attendance_report_employee']),
                        'title' => $cats['employees_reports']['subs']['summary_attendance_report_employee']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['summary_attendance_report_employee']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['summary_attendance_report_day']),
                        'title' => $cats['employees_reports']['subs']['summary_attendance_report_day']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day fs-22 bg-primary-2 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['summary_attendance_report_day']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['summary_attendance_report_week']),
                        'title' => $cats['employees_reports']['subs']['summary_attendance_report_week']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['summary_attendance_report_week']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['summary_attendance_report_month']),
                        'title' => $cats['employees_reports']['subs']['summary_attendance_report_month']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['summary_attendance_report_month']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['summary_attendance_report_year']),
                        'title' => $cats['employees_reports']['subs']['summary_attendance_report_year']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['summary_attendance_report_year']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['summary_attendance_report_department']),
                        'title' => $cats['employees_reports']['subs']['summary_attendance_report_department']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tag fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['summary_attendance_report_department']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['detailed_attendance_sheets']),
                        'title' => $cats['employees_reports']['subs']['detailed_attendance_sheets']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tag fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['detailed_attendance_sheets']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                ]
            ],
            // Attendance Shift Report
            [
                'condition' => ifPluginActive(HRM_ATTENDANCE_PLUGIN) && check_permission(VIEW_ATTENDANCE_REPORT),
                'title' => 'Attendance Shift Report',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['employees_reports']['subs']['allocated_shift_report']),
                        'title' => $cats['employees_reports']['subs']['allocated_shift_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-clipboard-user fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['allocated_shift_report']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                ]
            ],
            [
                'condition' => ifPluginActive(HRM_PAYROLL_PLUGIN) && check_permission(VIEW_PAY_RUN),
                'title' => 'Salaries Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['employees_reports']['subs']['detailed_payroll_report_employee']),
                        'title' => $cats['employees_reports']['subs']['detailed_payroll_report_employee']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['detailed_payroll_report_employee']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['detailed_payroll_report_monthly']),
                        'title' => $cats['employees_reports']['subs']['detailed_payroll_report_monthly']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['detailed_payroll_report_monthly']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['detailed_payroll_report_yearly']),
                        'title' => $cats['employees_reports']['subs']['detailed_payroll_report_yearly']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['detailed_payroll_report_yearly']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['detailed_payroll_report_department']),
                        'title' => $cats['employees_reports']['subs']['detailed_payroll_report_department']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tag fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['detailed_payroll_report_department']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['employees_reports']['subs']['detailed_payroll_report_branch']) && ifPluginActive(BranchesPlugin),
                        'title' => $cats['employees_reports']['subs']['detailed_payroll_report_branch']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-store fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['detailed_payroll_report_branch']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => ($cats['employees_reports']['subs']['loans_report'] && check_permission(MANAGE_LOANS_AND_INSTALLMENTS)),
                        'title' => $cats['employees_reports']['subs']['loans_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-store fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['loans_report']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => ($cats['employees_reports']['subs']['contracts_report'] && (check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::VIEW_PAYROLL_CONTRACT) || \Izam\Daftra\Common\Utils\PermissionUtil::VIEW_HIS_OWN_CONTRACT)),
                        'title' => $cats['employees_reports']['subs']['contracts_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-store fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['employees_reports']['subs']['contracts_report']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],

                ]
            ]
        ]
    ],
    // Credits Reports
    'credits_reports' => [
        'condition' => isset($cats['credits_reports']) && check_report_permission([View_His_Own_Reports]),
        'sections' => [
            // Credits Reports
            [
                'condition' => true,
                'title' => 'Credits Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['credits_reports']['subs']['detailed_credit_usage_report']),
                        'title' => $cats['credits_reports']['subs']['detailed_credit_usage_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-layer-group fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['credits_reports']['subs']['detailed_credit_usage_report']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['credits_reports']['subs']['summary_credit_usage_report']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['credits_reports']['subs']['detailed_credit_charge_report']),
                        'title' => $cats['credits_reports']['subs']['detailed_credit_charge_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-layer-plus fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['credits_reports']['subs']['detailed_credit_charge_report']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['credits_reports']['subs']['summary_credit_charge_report']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
        ]
    ],
    // Membership Reports
    'memberships_reports' => [
        'condition' => isset($cats['memberships_reports']) && check_report_permission([View_His_Own_Reports]),
        'sections' => [
            // Membership Reports
            [
                'condition' => true,
                'title' => 'Membership Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['memberships_reports']['subs']['detailed_expired_memberships_report']),
                        'title' => $cats['memberships_reports']['subs']['detailed_expired_memberships_report']['title'],
                        'subtitle' => '',
                        'icon' => 'mdi mdi-account-cancel fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['memberships_reports']['subs']['detailed_expired_memberships_report']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['memberships_reports']['subs']['summary_expired_memberships_report']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['memberships_reports']['subs']['detailed_memberships_subscriptions_report']),
                        'title' => $cats['memberships_reports']['subs']['detailed_memberships_subscriptions_report']['title'],
                        'subtitle' => '',
                        'icon' => 'mdi mdi-account-badge-horizontal fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['memberships_reports']['subs']['detailed_memberships_subscriptions_report']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['memberships_reports']['subs']['summary_memberships_subscriptions_report']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['memberships_reports']['subs']['detailed_new_memberships_report']),
                        'title' => $cats['memberships_reports']['subs']['detailed_new_memberships_report']['title'],
                        'subtitle' => '',
                        'icon' => 'mdi mdi-account-check fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['memberships_reports']['subs']['detailed_new_memberships_report']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['memberships_reports']['subs']['summary_new_memberships_report']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
        ]
    ],
    // Rental Reports
    'rental_reports' => [
        'condition' => isset($cats['rental_reports']) && check_report_permission([View_His_Own_Reports]),
        'sections' => [
            // Rental Reports
            [
                'condition' => true,
                'title' => 'Rental Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['rental_reports']['subs']['unit_availability']),
                        'title' => $cats['rental_reports']['subs']['unit_availability']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['rental_reports']['subs']['unit_availability']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['rental_reports']['subs']['units_pricing']),
                        'title' => $cats['rental_reports']['subs']['units_pricing']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-alt fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['rental_reports']['subs']['units_pricing']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ]
                        ]
                    ]
                ]
            ],
            [
                'condition' => true,
                'title' => 'Rental Units Income and Expenses',
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['rental_reports']['subs']['summary_unit_type_report']),
                        'title' => $cats['rental_reports']['subs']['summary_unit_type_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-boxes fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['rental_reports']['subs']['summary_unit_type_report']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['rental_reports']['subs']['summary_unit_report']),
                        'title' => $cats['rental_reports']['subs']['summary_unit_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-key fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['rental_reports']['subs']['summary_unit_report']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['rental_reports']['subs']['summary_unit_daily_report']),
                        'title' => $cats['rental_reports']['subs']['summary_unit_daily_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day  fs-22 bg-primary-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['rental_reports']['subs']['summary_unit_daily_report']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['rental_reports']['subs']['summary_unit_weekly_date_report']),
                        'title' => $cats['rental_reports']['subs']['summary_unit_weekly_date_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['rental_reports']['subs']['summary_unit_weekly_date_report']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['rental_reports']['subs']['summary_unit_monthly_report']),
                        'title' => $cats['rental_reports']['subs']['summary_unit_monthly_report']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['rental_reports']['subs']['summary_unit_monthly_report']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                ]
            ],
            [
            'condition' => true,
            'title' => 'Lease Contracts',
            'reports' => [
                [
                    'condition' => $cats['lease_contract']['subs']['lease_contract_installment'],
                    'title' => $cats['lease_contract']['subs']['lease_contract_installment']['title'],
                    'icon' => 'fas fa-money-check-alt fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['lease_contract']['subs']['lease_contract_installment']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ]
                    ]
                ],
                [
                    'condition' => isset($cats['lease_contract']['subs']['summary_unit_type_report']),
                    'title' => $cats['lease_contract']['subs']['summary_unit_type_report']['title'],
                    'icon' => 'fas fa-boxes fs-22 bg-danger-50 text-white',
                    'buttons' => [
                        [
                            'url' => $cats['lease_contract']['subs']['summary_unit_type_report']['url'],
                            'title' => 'View',
                            'icon' => 'fas fa-eye s2020 fs-18',
                            'default' => true
                        ]
                        
                    ]
                ],
                [
                    'condition' => isset($cats['lease_contract']['subs']['summary_unit_report']),
                    'title' => $cats['lease_contract']['subs']['summary_unit_report']['title'],
                    'icon' => 'fas fa-key fs-22 bg-warning-50 text-white',
                    'buttons' => [
                        [
                            'url' => $cats['lease_contract']['subs']['summary_unit_report']['url'],
                            'title' => 'View',
                            'icon' => 'fas fa-eye s2020 fs-18',
                            'default' => true
                        ]
                        
                    ]
                ],
                [
                    'condition' => isset($cats['lease_contract']['subs']['summary_unit_daily_report']),
                    'title' => $cats['lease_contract']['subs']['summary_unit_daily_report']['title'],
                    'icon' => 'fas fa-calendar-day  fs-22 bg-primary-50 text-white',
                    'buttons' => [
                        [
                            'url' => $cats['lease_contract']['subs']['summary_unit_daily_report']['url'],
                            'title' => 'View',
                            'icon' => 'fas fa-eye s2020 fs-18',
                            'default' => true
                        ]
                        
                    ]
                ],
                [
                    'condition' => isset($cats['lease_contract']['subs']['summary_unit_weekly_date_report']),
                    'title' => $cats['lease_contract']['subs']['summary_unit_weekly_date_report']['title'],
                    'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                    'buttons' => [
                        [
                            'url' => $cats['lease_contract']['subs']['summary_unit_weekly_date_report']['url'],
                            'title' => 'View',
                            'icon' => 'fas fa-eye s2020 fs-18',
                            'default' => true
                        ]
                    
                    ]
                ],
                [
                    'condition' => isset($cats['lease_contract']['subs']['summary_unit_monthly_report']),
                    'title' => $cats['lease_contract']['subs']['summary_unit_monthly_report']['title'],
                    'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                    'buttons' => [
                        [
                            'url' => $cats['lease_contract']['subs']['summary_unit_monthly_report']['url'],
                            'title' => 'View',
                            'icon' => 'fas fa-eye s2020 fs-18',
                            'default' => true
                        ]
                        
                    ]
                ],
            
            ]
            ]
        ]
    ],
    // Purchases Reports
    'purchase_orders' => [
        'condition' => isset($cats['purchase_orders']) && check_report_permission([View_his_own_created_Purchase_Orders, View_All_Purchase_Orders, View_His_Own_Reports]),
        'sections' => [
            // Purchases Segmented Reports
            [
                'condition' => true,
                'title' => 'Purchases Segmented Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['client_revenue_summary']),
                        'title' => 'Purchases By Supplier',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tie fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['client_revenue_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['client_revenue_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['staff_revenue_summary']),
                        'title' => 'Purchases By Staff',
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['staff_revenue_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['staff_revenue_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Suppliers Reports
            [
                'condition' => true,
                'title' => 'Suppliers Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['supplier_list']),
                        'title' => $cats['purchase_orders']['subs']['supplier_list']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-users-class fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['supplier_list']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['supplier_balance']),
                        'title' => $cats['purchase_orders']['subs']['supplier_balance']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-usd-square fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['supplier_balance']['url'].'?summary=0',
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['supplier_balance']['url'].'?summary=1',
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['purchase_orders']['subs']['supplier_aged_ledger'],
                        'title' => $cats['purchase_orders']['subs']['supplier_aged_ledger']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-edit-alt fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['supplier_aged_ledger']['url'].'?to_date=',
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],

                    [
                        'condition' => isset($cats['purchase_orders']['subs']['purchases']),
                        'title' => $cats['purchase_orders']['subs']['purchases']['title'],
                        'subtitle' => '',
                        'icon' => 'mdi mdi-truck-delivery fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['purchases']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['paid_purchases']),
                        'title' => $cats['purchase_orders']['subs']['paid_purchases']['title'],
                        'subtitle' => '',
                        'icon' => 'mdi mdi-truck-fast fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['paid_purchases']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['supplier_ledger']),
                        'title' => $cats['purchase_orders']['subs']['supplier_ledger']['title'],
                        'subtitle' => '',
                        'icon' => 'mdi mdi-truck-delivery fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['supplier_ledger']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                ]
            ],
            // Product Purchases Report
            [
                'condition' => true,
                'title' => 'Product Purchases Report',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['product_purchases-product-details']),
                        'title' => $cats['purchase_orders']['subs']['product_purchases-product-details']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-box-open fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['product_purchases-product-details']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['product_purchases-product-summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['product_purchases-supplier-details']),
                        'title' => $cats['purchase_orders']['subs']['product_purchases-supplier-details']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tie fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['product_purchases-supplier-details']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['product_purchases-supplier-summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['product_purchases-staff-details']),
                        'title' => $cats['purchase_orders']['subs']['product_purchases-staff-details']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['product_purchases-staff-details']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['product_purchases-staff-summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Payments Periodic Reports
            [
                'condition' => true,
                'title' => 'Payments Periodic Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['daily_payments_summary']),
                        'title' => 'Daily Payments',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day fs-22 bg-primary-2 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['daily_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['daily_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['weekly_payments_summary']),
                        'title' => 'Weekly Payments',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['weekly_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['weekly_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['monthly_payments_summary']),
                        'title' => 'Monthly Payments',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['monthly_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['monthly_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['purchase_orders']['subs']['yearly_payments_summary']),
                        'title' => 'Yearly Payments',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['purchase_orders']['subs']['yearly_payments_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['purchase_orders']['subs']['yearly_payments_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Custom Purchases Reports
            [
                'condition' => isset($typeGroupedJsonReports[$purchases_custom_reports_type]) && !empty($typeGroupedJsonReports[$purchases_custom_reports_type]),
                'title' => Report::$typesData[$purchases_custom_reports_type]['title'],
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $purchases_custom_reports
            ],
            [
                'condition' => (!empty($appPurchasesReports)),
                'title' => __t('Additional Reports'),
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $appPurchasesReports
            ],
        ]
    ],
    // Inventory Reports
    'inventory' => [
        'condition' => isset($cats['inventory']) && check_report_permission([Track_Inventory]),
        'sections' => [
            // Inventory Reports
            [
                'condition' => true,
                'title' => 'Inventory',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['inventory']['subs']['stocktaking_sheet']),
                        'title' => 'Stocktaking Sheet',
                        'subtitle' => '',
                        'icon' => 'fas fa-ballot-check fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['stocktaking_sheet']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'Inventory Transactions Summary',
                        'subtitle' => '',
                        'icon' => 'fas fa-box-open fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['store_summary']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'Inventory Detailed Transactions',
                        'subtitle' => '',
                        'icon' => 'fas fa-box-full fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['transaction']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'Inventory Value',
                        'subtitle' => '',
                        'icon' => 'fas fa-usd-square fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['worthsheet']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['inventory']['subs']['summary_of_stock_balances'],
                        'title' => $cats['inventory']['subs']['summary_of_stock_balances']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-dolly-flatbed-alt fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['summary_of_stock_balances']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['inventory']['subs']['product_average_cost'],
                        'title' => $cats['inventory']['subs']['product_average_cost']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-badge-percent fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['product_average_cost']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['inventory']['subs']['detailed_stock_transactions'],
                        'title' => $cats['inventory']['subs']['detailed_stock_transactions']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-box-full fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['detailed_stock_transactions']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['inventory']['subs']['inventory_requistion'],
                        'title' => $cats['inventory']['subs']['inventory_requistion']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-edit-alt fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['inventory_requistion']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['inventory']['subs']['purchase_requistion'],
                        'title' => $cats['inventory']['subs']['purchase_requistion']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-edit-alt fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['purchase_requistion']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Product Tracking Reports
            [
                'condition' => $enableLotAndSerial,
                'title' => 'Track Products Using Serial, Lot or Expiry Date',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['product_tracking_reports']['subs']['tracking_products_by_lot_and_expiry_date']),
                        'title' => 'Tracking Products with Lot and Expiry date',
                        'subtitle' => '',
                        'icon' => 'far fa-ballot-check fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['product_tracking_reports']['subs']['tracking_products_by_lot_and_expiry_date']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['product_tracking_reports']['subs']['tracking_products_by_serial']),
                        'title' => $cats['product_tracking_reports']['subs']['tracking_products_by_serial']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-barcode-read fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['product_tracking_reports']['subs']['tracking_products_by_serial']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['product_tracking_reports']['subs']['tracking_products_by_lot']),
                        'title' => $cats['product_tracking_reports']['subs']['tracking_products_by_lot']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-edit fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['product_tracking_reports']['subs']['tracking_products_by_lot']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['product_tracking_reports']['subs']['tracking_products_by_expiry_date']),
                        'title' => 'Tracking Products with Expiry date',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-times fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['product_tracking_reports']['subs']['tracking_products_by_expiry_date']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Bundle Reports
            [
                'condition' => $enable_bundle,
                'title' => 'Bundle Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['inventory']['subs']['bundles_list']),
                        'title' => 'Bundles List',
                        'subtitle' => '',
                        'icon' => 'fas fa-box-full fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['bundles_list']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['inventory']['subs']['bundle_units']),
                        'title' => $cats['inventory']['subs']['bundle_units']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-ball-pile fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['inventory']['subs']['bundle_units']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Custom Inventory Reports
            [
                'condition' => isset($typeGroupedJsonReports[$inventory_custom_reports_type]) && !empty($typeGroupedJsonReports[$inventory_custom_reports_type]),
                'title' => Report::$typesData[$inventory_custom_reports_type]['title'],
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $inventory_custom_reports
            ],
            [
                'condition' => (!empty($appInventoryReports)),
                'title' => __t('Additional Reports'),
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $appInventoryReports
            ],
        ]
    ],
    // Work Order Reports
    'work_orders' => [
        'condition' => isset($cats['work_orders']) && check_report_permission([VIEW_ALL_WORK_ORDERS]),
        'sections' => [
            // Work Order Reports
            [
                'condition' => true,
                'title' => 'Work Order Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => $cats['work_orders']['subs']['work_orders'],
                        'title' => $cats['work_orders']['subs']['work_orders']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-clipboard-list-check fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['work_orders']['subs']['work_orders']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['work_orders']['subs']['work_orders_tags'],
                        'title' => $cats['work_orders']['subs']['work_orders_tags']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-tag fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['work_orders']['subs']['work_orders_tags']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['work_orders']['subs']['work_orders_appointment'],
                        'title' => $cats['work_orders']['subs']['work_orders_appointment']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-check fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['work_orders']['subs']['work_orders_appointment']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['work_orders']['subs']['work_orders_profit_summary'],
                        'title' => $cats['work_orders']['subs']['work_orders_profit_summary']['title'],
                        'subtitle' => '',
                        'icon' => 'far fa-usd-square fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['work_orders']['subs']['work_orders_profit_summary']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['work_orders']['subs']['work_orders_profit_details'],
                        'title' => $cats['work_orders']['subs']['work_orders_profit_details']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-usd-square fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['work_orders']['subs']['work_orders_profit_details']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Custom Work Order Reports
            [
                'condition' => isset($typeGroupedJsonReports[$work_orders_custom_reports_type]) && !empty($typeGroupedJsonReports[$work_orders_custom_reports_type]),
                'title' => Report::$typesData[$work_orders_custom_reports_type]['title'],
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $work_orders_custom_reports
            ],
            [
                'condition' => (!empty($appWorkOrdersReports)),
                'title' => __t('Additional Reports'),
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $appWorkOrdersReports
            ],
        ]
    ],
    // Clients Reports
    'clients' => [
        'condition' => isset($cats['clients']) && check_report_permission([VIEW_ALL_CLIENTS_REPORTS, VIEW_HIS_OWN_CLIENTS_REPORTS]),
        'sections' => [
            // Clients Reports
            [
                'condition' => true,
                'title' => "Client's Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => $cats['clients']['subs']['aged_debtors'],
                        'title' => $cats['clients']['subs']['aged_debtors']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-alt fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['clients']['subs']['aged_debtors']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['invoices']['subs']['client_aged_ledger'],
                        'title' => $cats['invoices']['subs']['client_aged_ledger']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-edit-alt fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['client_aged_ledger']['url'].'?to_date=',
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['invoices']['subs']['client_list'],
                        'title' => $cats['invoices']['subs']['client_list']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-users-class fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['client_list']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['invoices']['subs']['client_balance'],
                        'title' => $cats['invoices']['subs']['client_balance']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-edit-alt fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['client_balance']['url'].'?summary=0',
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['invoices']['subs']['clients_sales'],
                        'title' => $cats['invoices']['subs']['clients_sales']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-badge-dollar fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['clients_sales']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['invoices']['subs']['clients_payments'],
                        'title' => $cats['invoices']['subs']['clients_payments']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-wallet fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['clients_payments']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['invoices']['subs']['clients_ledger'],
                        'title' => $cats['invoices']['subs']['clients_ledger']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-search-dollar fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['invoices']['subs']['clients_ledger']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => $cats['clients']['subs']['appointments'],
                        'title' => $cats['clients']['subs']['appointments']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-user-clock fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['clients']['subs']['appointments']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => ifPluginActive(INSTALLMENT_AGREEMENT_PLUGIN) && isset($cats['clients']['subs']['installments']),
                        'title' => 'Clients Installments',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-check fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['clients']['subs']['installments']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Custom Clients Reports
            [
                'condition' => isset($typeGroupedJsonReports[$clients_custom_reports_type]) && !empty($typeGroupedJsonReports[$clients_custom_reports_type]),
                'title' => Report::$typesData[$clients_custom_reports_type]['title'],
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $clients_custom_reports
            ],
            [
                'condition' => (!empty($appClientsReports)),
                'title' => __t('Additional Reports'),
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $appClientsReports
            ],
        ]
    ],
    

    // Accounting Reports
    'accounting' => [
        'condition' => isset($cats['accounting']) && check_report_permission([Invoices_View_All_Invoices, View_His_Own_Reports, View_All_expenses]),
        'sections' => [
            // Accounting Reports
            [
                'condition' => true,
                'title' => "Accounting Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['accounting']['subs']['tax_summary']),
                        'title' => 'Tax Report',
                        'subtitle' => '',
                        'icon' => 'fas fa-hand-holding-usd fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['tax_details']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['accounting']['subs']['tax_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
	                [
		                'condition' => isset($cats['accounting']['subs']['tax_declartion']),
		                'title' => 'Tax Declaration',
		                'subtitle' => '',
		                'icon' => 'fas fa-hand-holding-usd fs-22 bg-warning-50 text-white',
		                'buttons' => [
			                [
				                'url' => $cats['accounting']['subs']['tax_declartion']['url'],
				                'title' => 'View',
				                'icon' => 'fas fa-eye s2020 fs-18',
			                ]
		                ]
	                ],
                    [
                        'condition' => isset($cats['accounting']['subs']['income_statement']),
                        'title' => $cats['accounting']['subs']['income_statement']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-hands-usd fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['income_statement']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['balance_sheet']),
                        'title' => $cats['accounting']['subs']['balance_sheet']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-scroll fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['balance_sheet']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['profit_lostt_accural']),
                        'title' => 'Profit & Loss',
                        'subtitle' => '',
                        'icon' => 'fas fa-badge-dollar fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['profit_lostt_accural']['url'],
                                'title' => $cats['accounting']['subs']['profit_lostt_accural']['title'],
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['accounting']['subs']['profit_lostt_cash']['url'],
                                'title' => $cats['accounting']['subs']['profit_lostt_cash']['title'],
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                
                            ]

                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'Financial Transactions',
                        'subtitle' => '',
                        'icon' => 'fas fa-ballot fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => Router::url(['controller' => 'reports',  'action' => 'financial_report' , '?' => 'date_range_selector=lastmonth']),
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'Cash Flow Report',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['cash_flow']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ]
                        ]
                    ],
                    [
                        'condition' => ifPluginActive(AccountingPlugin) && check_permission(View_His_Own_Reports),
                        'title' => 'Assets',
                        'subtitle' => '',
                        'icon' => 'fas fa-ballot fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => Router::url(['controller' => 'reports',  'action' => 'report' , 'assets']),
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                ]
            ],
            // Journals Reports
            [
                'condition' => ifPluginActive(AccountingPlugin),
                'title' => "Journals Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['accounting']['subs']['trial_balance&nets']),
                        'title' => $cats['accounting']['subs']['trial_balance&nets']['title'],
                        'subtitle' => '',
                        'icon' => 'mdi mdi-note-text fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['trial_balance&nets']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['trial_balance_only']),
                        'title' => $cats['accounting']['subs']['trial_balance_only']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-ballot-check fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['trial_balance_only']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['trial_nets']),
                        'title' => $cats['accounting']['subs']['trial_nets']['title'],
                        'subtitle' => '',
                        'icon' => 'mdi mdi-note fs-22 bg-navy text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['trial_nets']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['ledger']),
                        'title' => 'Ledger',
                        'subtitle' => '',
                        'icon' => 'mdi mdi-newspaper-variant fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['ledger']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['cost_center']),
                        'title' => 'Cost Centers',
                        'subtitle' => '',
                        'icon' => 'fas fa-calculator fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['cost_center']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['journals']),
                        'title' => 'Journals Report',
                        'subtitle' => '',
                        'icon' => 'fas fa-clipboard-list-check fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['journal_transactions']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['accounting']['subs']['journals']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['accounting']['subs']['journals']),
                        'title' => 'Chart of Accounts Directory',
                        'subtitle' => '',
                        'icon' => 'fas fa-folders fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['accounting']['subs']['chart_of_accounts']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ],
                        ]
                    ],
                ]
            ],
            // Custom Accounting Reports
            [
                'condition' => isset($typeGroupedJsonReports[$accounting_custom_reports_type]) && !empty($typeGroupedJsonReports[$accounting_custom_reports_type]),
                'title' => Report::$typesData[$accounting_custom_reports_type]['title'],
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $accounting_custom_reports
            ],
            // Expenses Segmented Reports
            [
                'condition' => isset($cats['finance']) && check_permission (View_All_expenses),
                'title' => "Expenses Segmented Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['finance']['subs']['category_expenses_summary']),
                        'title' => 'Expenses by Category',
                        'subtitle' => '',
                        'icon' => 'fas fa-boxes-alt fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['category_expenses_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['category_expenses_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['vendor_expenses_summary']),
                        'title' => 'Expenses by Vendor',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tag fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['vendor_expenses_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['vendor_expenses_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['staff_expenses_summary']),
                        'title' => 'Expenses by Staff',
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['staff_expenses_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['staff_expenses_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['client_expenses_summary']),
                        'title' => 'Expenses by Client',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tie fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['client_expenses_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['client_expenses_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                ]
            ],
            // Expenses Periodic Reports
            [
                'condition' => isset($cats['finance']) && check_permission (View_All_expenses),
                'title' => "Expenses Periodic Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['finance']['subs']['daily_expenses_summary']),
                        'title' => 'Daily Expenses',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day fs-22 bg-primary-2 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['daily_expenses_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['daily_expenses_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['weekly_expenses_summary']),
                        'title' => 'Weekly Expenses',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['weekly_expenses_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['weekly_expenses_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['monthly_expenses_summary']),
                        'title' => 'Monthly Expenses',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['monthly_expenses_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['monthly_expenses_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['yearly_expenses_summary']),
                        'title' => 'Yearly Expenses',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['yearly_expenses_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['yearly_expenses_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                ]
            ],
            // Incomes Segmented Reports
            [
                'condition' => isset($cats['finance']) && check_permission (View_All_incomes),
                'title' => "Incomes Segmented Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['finance']['subs']['category_incomes_summary']),
                        'title' => 'Incomes by Category',
                        'subtitle' => '',
                        'icon' => 'fas fa-boxes-alt fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['category_incomes_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['category_incomes_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['vendor_incomes_summary']),
                        'title' => 'Incomes by Vendor',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tag fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['vendor_incomes_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['vendor_incomes_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['staff_incomes_summary']),
                        'title' => 'Incomes by Staff',
                        'subtitle' => '',
                        'icon' => 'fas fa-user fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['staff_incomes_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['staff_incomes_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['client_incomes_summary']),
                        'title' => 'Incomes by Client',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-tie fs-22 bg-purple-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['client_incomes_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['client_incomes_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                ]
            ],
            // Incomes Periodic Reports
            [
                'condition' => isset($cats['finance']) && check_permission (View_All_incomes),
                'title' => "Incomes Periodic Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['finance']['subs']['daily_incomes_summary']),
                        'title' => 'Daily Incomes',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day fs-22 bg-primary-2 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['daily_incomes_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['daily_incomes_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['weekly_incomes_summary']),
                        'title' => 'Weekly Incomes',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['weekly_incomes_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['weekly_incomes_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['monthly_incomes_summary']),
                        'title' => 'Monthly Incomes',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['monthly_incomes_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['monthly_incomes_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['finance']['subs']['yearly_incomes_summary']),
                        'title' => 'Yearly Incomes',
                        'subtitle' => '',
                        'icon' => 'fas fa-clipboard-list-check fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['finance']['subs']['yearly_incomes_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['finance']['subs']['yearly_incomes_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ],
                        ]
                    ],
                ]
            ],
            [
                'condition' => (!empty($appAccountingReports)),
                'title' => __t('Additional Reports'),
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $appAccountingReports
            ],
        ]
    ],
    // Cheques
    'cheques' => [
        'condition' => isset($cats['cheques']) && check_report_permission([View_His_Own_Reports]),
        'sections' => [
            // Payable & Receivable Cheques
            [
                'condition' => true,
                'title' => "Cheques Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['cheques']['subs']['payable_cheques']),
                        'title' => $cats['cheques']['subs']['payable_cheques']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-alt fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['cheques']['subs']['payable_cheques']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    [
                        'condition' => isset($cats['cheques']['subs']['receivable_cheques']),
                        'title' => $cats['cheques']['subs']['receivable_cheques']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-edit-alt fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['cheques']['subs']['receivable_cheques']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                ]
            ],
        ]
    ],
    // Time Tracking Reports
    'time_tracking' => [
        'condition' => isset($cats['time_tracking']) && check_report_permission([Track_All_Staffs_Times, Edit_All_Timesheets]),
        'sections' => [
            // Time Tracking Segmented Reports
            [
                'condition' => true,
                'title' => "Time Tracking Segmented Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['time_tracking']['subs']['staff_report_summary']),
                        'title' => 'Report by Staff',
                        'subtitle' => '',
                        'icon' => 'fas fa-user-check fs-22 bg-olive text-white',
                        'buttons' => [
                            [
                                'url' => $cats['time_tracking']['subs']['staff_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['time_tracking']['subs']['staff_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['time_tracking']['subs']['project_report_summary']),
                        'title' => 'Report by Project',
                        'subtitle' => '',
                        'icon' => 'fas fa-clipboard-list-check fs-22 bg-danger-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['time_tracking']['subs']['project_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['time_tracking']['subs']['project_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['time_tracking']['subs']['activity_report_summary']),
                        'title' => 'Report by Activity',
                        'subtitle' => '',
                        'icon' => 'mdi mdi-alert-circle fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['time_tracking']['subs']['activity_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['time_tracking']['subs']['activity_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Time Tracking Periodic Reports
            [
                'condition' => true,
                'title' => "Time Tracking Periodic Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-line',
                'reports' => [
                    [
                        'condition' => isset($cats['time_tracking']['subs']['daily_report_summary']),
                        'title' => 'Daily Report',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-day fs-22 bg-primary-2 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['time_tracking']['subs']['daily_report_summary']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['time_tracking']['subs']['daily_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['time_tracking']['subs']['weekly_report_summary']),
                        'title' => 'Weekly Report',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-week fs-22 bg-primary-3 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['time_tracking']['subs']['weekly_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['time_tracking']['subs']['weekly_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['time_tracking']['subs']['monthly_report_summary']),
                        'title' => 'Monthly Report',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-alt fs-22 bg-primary-4 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['time_tracking']['subs']['monthly_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['time_tracking']['subs']['monthly_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => isset($cats['time_tracking']['subs']['yearly_report_summary']),
                        'title' => 'Yearly Report',
                        'subtitle' => '',
                        'icon' => 'fas fa-calendar-star fs-22 bg-primary-5 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['time_tracking']['subs']['yearly_report_detailed']['url'],
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                            [
                                'url' => $cats['time_tracking']['subs']['yearly_report_summary']['url'],
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                ]
            ],
            // Custom Time Tracking Reports
            [
                //warning suppress
                'condition' => isset($type_tracking_custom_reports_type) && isset($typeGroupedJsonReports[$type_tracking_custom_reports_type]) && !empty($typeGroupedJsonReports[$type_tracking_custom_reports_type]),
                'title' => isset($type_tracking_custom_reports_type) && array_key_exists($type_tracking_custom_reports_type, $typesData) ? Report::$typesData[$type_tracking_custom_reports_type]['title'] : '',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $type_tracking_custom_reports ?? ''
                //end warning suppress
            ],
            [
                'condition' => (!empty($appTimeTrackingReports)),
                'title' => __t('Additional Reports'),
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $appTimeTrackingReports
            ],
        ]
    ],
    // SMS Reports
    'sms_reports' => [
        'condition' => isset($cats['sms_reports']) && check_report_permission([Edit_General_Settings]),
        'sections' => [
            // SMS Reports
            [
                'condition' => true,
                'title' => 'SMS Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => true,
                        'title' => 'SMS Campaigns Log',
                        'subtitle' => '',
                        'icon' => 'fas fa-layer-group fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => Router::url(array('controller' => 'sms_campaigns', 'action' => 'index')),
                                'title' => 'Summary',
                                'icon' => 'fas fa-clipboard s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'SMS Log',
                        'subtitle' => '',
                        'icon' => 'fas fa-layer-plus fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => Router::url(array('controller' => 'sms_logs', 'action' => 'index')),
                                'title' => 'Details',
                                'icon' => 'fas fa-file-alt s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    // Custom SMS Reports
                    [
                        'condition' => isset($typeGroupedJsonReports[$sms_custom_reports_type]) && !empty($typeGroupedJsonReports[$sms_custom_reports_type]),
                        'title' => Report::$typesData[$sms_custom_reports_type]['title'],
                        'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                        'reports' => $sms_custom_reports
                    ],

                ]
            ],
            [
                'condition' => (!empty($appSMSReports)),
                'title' => __t('Additional Reports'),
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => $appSMSReports
            ],
        ]
    ],
    // BNR Reports
    'pnr_report' => [
        'condition' => ifPluginActive(BnrPlugin) && check_report_permission([VIEW_ALL_WORK_ORDERS]),
        'sections' => [
            // PNR Reports
            [
                'condition' => true,
                'title' => 'Pnr Reports',
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => true,
                        'title' => 'Pnr Report',
                        'subtitle' => '',
                        'icon' => 'fas fa-layer-group fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => Router::url(array('controller' => 'reports', 'action' => 'bnr')) . "?date_from=" . format_date(date("Y-m-d"))  ."&date_to=" . format_date(date("Y-m-d")),
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                            ]
                        ]
                    ],
                    [
                        'condition' => true,
                        'title' => 'Supplier Pnr',
                        'subtitle' => '',
                        'icon' => 'fas fa-layer-plus fs-22 bg-success-50 text-white',
                        'buttons' => [
                            [
                                'url' => Router::url(array('controller' => 'reports', 'action' => 'supplier_bnr')) . "?date_from=" . format_date(date("Y-m-d")) . "&date_to=" . format_date(date("Y-m-d")),
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                    // Custom PNR Reports
                    [
                        'condition' => isset($typeGroupedJsonReports[$pnr_custom_reports_type]) && !empty($typeGroupedJsonReports[$pnr_custom_reports_type]),
                        'title' => Report::$typesData[$pnr_custom_reports_type]['title'],
                        'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                        'reports' => $pnr_custom_reports
                    ],
                    [
                        'condition' => (!empty($appPNRRReports)),
                        'title' => __t('Additional Reports'),
                        'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                        'reports' => $appPNRRReports
                    ],
                ]
            ],
        ]
    ],
      // Manufacturing
    'manufacturing' => [
        'condition' => isset($cats['manufacturing']) && check_report_permission([VIEW_ALL_MANUFACTURING_ORDERS, VIEW_HIS_OWN_MANUFACTURING_ORDERS]),
        'sections' => [
            [
                'condition' => true,
                'title' => "Manufacturing Reports",
                'icon' => 's2020 fs-20 fas fa-file-chart-pie',
                'reports' => [
                    [
                        'condition' => isset($cats['manufacturing']['subs']['manufacturing_order_costs']),
                        'title' => $cats['manufacturing']['subs']['manufacturing_order_costs']['title'],
                        'subtitle' => '',
                        'icon' => 'fas fa-money-check-alt fs-22 bg-warning-50 text-white',
                        'buttons' => [
                            [
                                'url' => $cats['manufacturing']['subs']['manufacturing_order_costs']['url'],
                                'title' => 'View',
                                'icon' => 'fas fa-eye s2020 fs-18',
                                'default' => true
                            ],
                        ]
                    ],
                ]
            ],
        ]
    ],
    
];


if(isset($workflowTypes)) {
    $cats['workflows'] = ['title' => 'Workflows'];
    $reports_list['workflows'] = [
        'condition' => check_report_permission([View_His_Own_Reports]),
        'sections' => []
    ];
    foreach ($workflowTypes as $workflowType) {
        $id = $workflowType['WorkflowType']['id'];
        $pluralName = $workflowType['WorkflowType']['name'];
        $reports_list['workflows']['sections'][] =  [
            'condition' => true,
            'title' => sprintf(__('%s Reports', true), $pluralName),
            'icon' => 's2020 fs-20 fas fa-file-chart-pie',
            'reports' => [
                [
                    'condition' => true,
                    'title' => $pluralName,
                    'subtitle' => '',
                    'icon' => 'fas fa-clipboard-list-check fs-22 bg-warning-50 text-white',
                    'buttons' => [
                        [
                            'url' => "/owner/reports/report/workflows/$id",
                            'title' => 'View',
                            'icon' => 'fas fa-eye s2020 fs-18',
                        ]
                    ]
                ],
                [
                    'condition' => true,
                    'title' => sprintf(__('%s Appointments', true), $pluralName) ,
                    'subtitle' => '',
                    'icon' => 'fas fa-calendar-check fs-22 bg-success-50 text-white',
                    'buttons' => [
                        [
                            'url' => "/owner/reports/report/workflow_appointment/$id",
                            'title' => 'View',
                            'icon' => 'fas fa-eye s2020 fs-18',
                        ]
                    ]
                ],
                [
                    'condition' => true,
                    'title' => sprintf(__('%s Profit', true), $pluralName) . ' - ' . __('Summary', true),
                    'subtitle' => '',
                    'icon' => 'far fa-usd-square fs-22 bg-danger-50 text-white',
                    'buttons' => [
                        [
                            'url' => '/owner/reports/report/workflow_profit_summary/'.$id,
                            'title' => 'View',
                            'icon' => 'fas fa-eye s2020 fs-18',
                        ]
                    ]
                ],
                [
                    'condition' => true,
                    'title' => sprintf(__('%s Profit', true), $pluralName) . ' - ' . __('Details', true),
                    'subtitle' => '',
                    'icon' => 'fas fa-usd-square fs-22 bg-navy text-white',
                    'buttons' => [
                        [
                            'url' => '/owner/reports/report/workflow_profit_details/'.$id,
                            'title' => 'View',
                            'icon' => 'fas fa-eye s2020 fs-18',
                        ]
                    ]
                ],
            ]
        ];
    }

}
?>
<style>
.tabs-content {
    background: transparent
}
.report-list-item {
    transition: 0.3s all;
}
.report-list-item.border-bottom.border-disabled:last-child {
    border-bottom-color: transparent !important;
}
.report-list-item:hover {
    background: #edf0f3;
}
.report-list-item__btn.s2020.text-inactive {
    color: #a4a8cc !important;
}
.report-list-item__btn.s2020.text-inactive:hover {
    color: #75799d !important;
    background: #dee5eb !important;
}
.dropdown.s2020.open .dropdown-toggle:before {
    max-height: unset;
}
.report-list-item__link {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}
.report-list-item__btn {
    z-index: 2;
}
</style>
<div class="pages-head-s2020 d-flex align-items-center">
    <div class="w-100">
        <div class="container-flex">
            <div class="row-flex align-items-center">
                <div class="col-flex-sm-6">
                    <div class="pages-head-title">
                        <h2 id="report-page-header" class="pb-0 text-inactive s2020"><?php __('System Reports'); ?></h2>
                    </div>
                </div>
                <div class="col-flex-sm-6"></div>
            </div>
        </div>
    </div>
</div>

<!-- hidden tab links -->
<div class="panel" style="display: none;">
    <div class="panel-body">
        <div class="col-md-3 no-padding side-nav">
            <ul class="nav nav-pills nav-stacked" id="myTabs">
                <?php foreach ($cats as $key => $cat) { ?>
                <li class="">
                    <a href="#<?php echo $key ?>"><?php echo $cat['title'] ?></a>
                </li>
                <?php } ?>
                <?php if ($user['is_super_admin']) { ?>
                <li class="">
                    <a href="<?php echo Router::url(array('controller' => 'action_lines', 'action' => 'index')) ?>"><?php __('System Activity Log') ?></a>
                </li>
                <?php } ?>
            </ul>
        </div>
    </div>
</div>

<!-- tab contents -->
<div class="tab-content tabs-content side-content p-0 border-0">
    <?php foreach ($reports_list as $report_category_key => $report_category) { ?>
        <?php if ($report_category['condition']) { ?>
            <div class="tab-pane active" id="<?= $report_category_key ?>">
                <div class="container-flex">
                    <div class="row-flex">
                        <?php foreach ($report_category['sections'] as $report_section) {?>
                        <?php if ($report_section['condition'] && !empty(array_filter(array_column($report_section['reports'], 'condition')))) { ?>
                        <div class="col-flex-md-6 pb-5">
                            <div class="panel panel-default border-0 m-0 h-100">
                                <div class="px-4 py-3 border-bottom border-secondary s2020" style="background-color: rgba(246, 246, 246, 0.5);">
                                    <h3 class="text-dark-blue s2020 pb-0 fs-16 font-weight-bold d-flex align-items-center">
                                        <i class="mr-2 <?= $report_section['icon'] ?>"></i>
                                        <span><?php __($report_section['title']) ?></span>
                                    </h3>
                                </div>
                                <div class="panel-body p-0">
                                    <ul class="list-unstyled">
                                        <?php foreach ($report_section['reports'] as $report) { ?>
                                        <?php if ($report['condition']) { ?>
                                        <li class="border-bottom border-disabled s2020 report-list-item">
                                            <div class="px-3 py-2 position-relative dropdown s2020">
                                                <a href="<?= get_default_button_url($report['buttons']) ?>" class="report-list-item__link dropdown-toggle report-list-item__dropdown-toggle position-absolute" role="button"></a>
                                                <div class="row-flex">
                                                    <div class="col-flex-md-7 d-flex align-items-center">
                                                        <div class="mr-3 d-inline-block">
                                                            <i class="align-items-center d-flex justify-content-center rounded s2020 w-45px h-45px <?= $report['icon'] ?>"></i>
                                                        </div>
                                                        <div class="d-inline-block">
                                                            <?php if (isset($report['title'])) { ?>
                                                            <p class="pb-0 text-inactive s2020"><?php __($report['title']) ?></p>
                                                            <?php } ?>
                                                            <?php if (isset($report['subtitle'])) { ?>
                                                            <h5 class="p-0 m-0 s2020 text-dark-blue fs-16"><?php __($report['subtitle']) ?></h5>
                                                            <?php } ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-flex-md-5 d-none d-md-flex justify-content-end align-items-center mt-3 mt-lg-0">
                                                        <?php if (isset($report['buttons']) && count($report['buttons'])) { ?>
                                                        <?php foreach ($report['buttons'] as $button) { ?>
                                                        <a href="<?= $button['url'] ?>" class="report-list-item__btn btn-s2020 font-weight-medium btn-link text-inactive s2020 d-flex align-items-center">
                                                            <i class="<?= $button['icon'] ?> mr-2"></i>
                                                            <span><?php __($button['title']) ?></span>
                                                        </a>
                                                        <?php } ?>
                                                        <?php } ?>
                                                    </div>
                                                    <?php if (isset($report['buttons']) && count($report['buttons'])) { ?>
                                                    <div class="col-flex-12">
                                                        <div class="dropdown-menu s2020">
                                                        <?php foreach ($report['buttons'] as $button) { ?>
                                                        <a class="dropdown-item text-inactive w-100 p-3 s2020" href="<?= $button['url'] ?>">
                                                        <i class="<?= $button['icon'] ?> mr-2"></i>
                                                            <span><?php __($button['title']) ?></span>
                                                        </a>
                                                        <?php } ?>
                                                        </div>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                        </li>
                                        <?php } ?>
                                        <?php } ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                        <?php } ?>
                    </div>
                </div>
            </div>
        <?php } ?>
    <?php } ?>
</div>


<!-- Enable the tabs -->
<script type="text/javascript">
    $(document).ready(function() {

        /**
         * Handle report categories tab title click
         */
        $('#myTabs a').click(function(e) {
            if ($(this).attr('href').indexOf("#") != -1) {
                e.preventDefault();
                $(this).tab('show')
                if (history.pushState) {
                    history.pushState(null, null, $(this).attr('href'));
                }
                else {
                    location.hash = $(this).attr('href');
                }
            }
        });

        /**
         * Handle hash changes in url
         */
        if ("onhashchange" in window) {
            function locationHashChanged() {
                $('a[href="' + location.hash + '"]').tab('show');
            }
            window.onhashchange = locationHashChanged;
        }

        /**
         * Handle first hash in url
         */
        if (location.hash !== ''){
            $('a[href="' + location.hash + '"]').tab('show');
        } else {
            $('a[href=#invoices]' ).tab('show');
        }

    });

    /**
     * -
     */
    $('body').on('change','#ledger',function(){
        console.log($('#ledger').find(':selected').val());
        journal_account_id = $('#ledger').find(':selected').val();
        if(journal_account_id != ''){
            window.location = 'https://oidev.daftra.dev/owner/reports/journal_transactions?report_type=transaction&date_from=&date_to=&group_by=account&journal_account_id=' + journal_account_id + '&staff_id=&currency=-1'
        }
    });

    /**
     * Responsive report dropdowns
     */
    function responsiveReportDropdowns () {
        if ($(document).width() < 768){
            $('.report-list-item__dropdown-toggle').attr('data-toggle', 'dropdown');
        } else {
            $('.report-list-item__dropdown-toggle').removeAttr('data-toggle');
        }
    }
    responsiveReportDropdowns();
    $(window).resize(function() {
        responsiveReportDropdowns();
    });


    /**
     * Javascript to enable link to tab
     */
    var url = document.location.toString();
    if (url.match('#')) {
        $('.nav-pills a[href="#' + url.split('#')[1] + '"]').tab('show');
        var aTags = $('a[href="/owner/reports/list/#' + url.split('#')[1] + '"]');
        var span = document.createElement('span');
        span.innerHTML = aTags[0].innerHTML;
        $('#report-page-header').text(span.textContent || span.innerText);
        document.title = span.textContent || span.innerText;
    }
    $('.mn_reports ul a').click(function(event){
        var a = event.currentTarget;
        var span = document.createElement('span');
        span.innerHTML = a.innerHTML;
        $('#report-page-header').text(span.textContent || span.innerText);
        document.title = span.textContent || span.innerText;
    });
    /**
     * Change hash for page-reload
     */
    $('.nav-tabs a').on('shown.bs.tab', function (e) {
        window.location.hash = e.target.hash;
    })

    /**
     * Prevent auto scroll to report div
     */
    setTimeout(function () {
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
    }, 100);
    window.addEventListener("hashchange", function(){
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
    });
</script>
