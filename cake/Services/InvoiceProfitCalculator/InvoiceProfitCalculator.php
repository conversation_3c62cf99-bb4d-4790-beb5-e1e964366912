<?php

namespace App\Services\InvoiceProfitCalculator;

use App\Utils\TrackStockUtil;

class InvoiceProfitCalculator
{
    public static function calculateItemDiscountFromStockTransactions(CalculationItem $calculationItem) {
        if (!empty($calculationItem->getProduct()) && $calculationItem->getProduct()['tracking_type'] ==TrackStockUtil::TYPE_SERIAL ) {
           return InvoiceProfitCalculator::calculateItemDiscount( $calculationItem);
        }
        $finalUnitPrice = $calculationItem->getFinalPrice();
        $quantity = $calculationItem->getQuantity();
        if (empty($calculationItem->getCalculatedDiscount()) && $calculationItem->getDiscount()) {
            $discountAmount = $finalUnitPrice * ($calculationItem->getDiscount() / 100);
            $finalUnitPrice -= $discountAmount;
        }

        if ($calculationItem->getCalculatedDiscount() && $calculationItem->getDiscount()) {
            $discountAmount =  $calculationItem->getCalculatedDiscount();
            if (!empty($quantity)) {
                $discountAmount /= $quantity;
            }
            $finalUnitPrice -= $discountAmount;
        }
        $calculationItem->setFinalPrice($finalUnitPrice);
    }

    public static function calculateItemDiscount(CalculationItem $calculationItem) {
        $finalUnitPrice = $calculationItem->getFinalPrice();
        if ($calculationItem->getDiscount()) {
            $discountAmount = $finalUnitPrice * ($calculationItem->getDiscount() / 100);
            $finalUnitPrice -= $discountAmount;
        }
        if ($calculationItem->getDiscountAmount()) {
            $discountAmount =  $calculationItem->getUnitFactor() ? $calculationItem->getDiscountAmount()/$calculationItem->getUnitFactor() : $calculationItem->getDiscountAmount();
            $finalUnitPrice -= $discountAmount;
        }
        $calculationItem->setFinalPrice($finalUnitPrice);
    }

    public static function calculateInvoiceDiscount($invoice) {
        $Invoice = GetObjectOrLoadModel('Invoice');
        if (!empty($invoice['Invoice']['discount_amount']) && (float)$invoice['Invoice']['discount_amount'] != 0) {
            $is_discount_amount = true;
            $allSub = 0;
            foreach ($invoice['InvoiceItem'] as $c => $item) {
                $sub = (float)$item['unit_price'] * (float)$item['quantity'];
                $discount_val = $Invoice->calculate_item_discount($item, $sub);
                $allSub += ($sub - $discount_val);
            }
            $invoiceDiscount = $allSub != 0 ? $invoice['Invoice']['discount_amount'] / $allSub : false;
        } else {
            $invoiceDiscount = floatval($invoice['Invoice']['discount']) / 100;
        }
        return $invoiceDiscount;
    }

    public static function calculateInclusiveTax(CalculationItem $calculationItem, $tax) {
            $finalUnitPrice = ($calculationItem->getFinalPrice() / (100 + $tax)) * 100;
            $calculationItem->setFinalPrice($finalUnitPrice);
    }

    public static function calculateItemProfit(CalculationItem $calculationItem, $currencyRate) {
        return ($calculationItem->getFinalPrice() * $calculationItem->getQuantity() * $currencyRate) - ($calculationItem->getPurchasePrice() * $calculationItem->getQuantity());
    }

    public static function calculateInvoiceProfit($invoice) {
        $InvoiceModel = GetObjectOrLoadModel('Invoice');
        $Requisition = GetObjectOrLoadModel('Requisition');
        $InvoiceItem = GetObjectOrLoadModel('InvoiceItem');
        $invoiceItems = $InvoiceItem->getInvoiceProfitItems($invoice['Invoice']['id']);
        if(!$invoiceItems) {
            return false;
        }
        $defaultCurrencyCode = $InvoiceModel->get_default_currency();
        $currencyCode = $invoice['Invoice']['currency_code'];
        $StockTransaction = GetObjectOrLoadModel('StockTransaction');
        $Tax = GetObjectOrLoadModel('Tax');
        $mappedTaxes = [];
        $includedTaxes = $Tax->find('all', ['conditions' => ['included' => 1]]);
        foreach ($includedTaxes as $includedTax) {
            $mappedTaxes[$includedTax['Tax']['id']] = $includedTax;
        }
        $currencyRate = \CurrencyConverter::index($currencyCode, $defaultCurrencyCode, $InvoiceModel->formatDate($invoice['Invoice']['date']));
        $profits = [];
        $invoiceDiscount = InvoiceProfitCalculator::calculateInvoiceDiscount($invoice);
        $profitTotal = 0;
        $invoiceRequisitions = $Requisition->find('all', ['conditions' => ['order_id' => $invoice['Invoice']['id'], 'order_type' => \Requisition::ORDER_TYPE_INVOICE]]);
        $orderIds = [];
        $profitRequisitions = [];
        if($invoiceRequisitions) {
            $sourceType = \StockTransaction::SOURCE_RQ_INVOICE;
            $profitRequisitions = $invoiceRequisitions;
        } else if(!$invoiceRequisitions && $invoice['Invoice']['pos_shift_id']) {
            $posShiftRequisitions = $Requisition->find('all', ['conditions' => ['order_id' => $invoice['Invoice']['pos_shift_id'], 'order_type' => \Requisition::ORDER_TYPE_POS_OUTBOUND]]);
            if($posShiftRequisitions) {
                $sourceType = \StockTransaction::SOURCE_RQ_POS_OUTBOUND;
                $profitRequisitions = $posShiftRequisitions;
            }
        }

        if($profitRequisitions) {
            foreach ($profitRequisitions as $posShiftRequisition) {
                $orderIds[] = $posShiftRequisition['Requisition']['id'];
            }
            $conditions = [
                'StockTransaction.source_type' => $sourceType,
                'StockTransaction.order_id' => $orderIds,
            ];
            $profitStockTransactions = $StockTransaction->find('all', ['conditions' => $conditions]);
            $calculationItems = InvoiceProfitFactory::createCalculationItemsFromRequisition($invoiceItems, $profitRequisitions, $profitStockTransactions);
        } else {
            $conditions = [
                'StockTransaction.source_type' => \StockTransaction::SOURCE_INVOICE,
                'StockTransaction.order_id' => $invoice['Invoice']['id'],
            ];
            $stocks = $StockTransaction->find('all', ['conditions' => $conditions]);
            $calculationItems = InvoiceProfitFactory::createCalculationItemsFromStockTransactions($invoiceItems, $stocks);
        }

        if(empty($calculationItems)) {
            return false;
        }

        foreach($calculationItems as $k =>  $calculationItem) {
            if($profitRequisitions) {
                InvoiceProfitCalculator::calculateItemDiscount($calculationItem);
            }else{
                InvoiceProfitCalculator::calculateItemDiscountFromStockTransactions($calculationItem);
            }

            if ($invoiceDiscount) {
                $discountAmount = $calculationItem->getFinalPrice() * $invoiceDiscount;
                $finalUnitPrice = $calculationItem->getFinalPrice() - $discountAmount;
                $calculationItem->setFinalPrice($finalUnitPrice);
            }

            if (isset($mappedTaxes[$calculationItem->getTax1Id()])) {
                $tax = $mappedTaxes[$calculationItem->getTax1Id()];
                InvoiceProfitCalculator::calculateInclusiveTax($calculationItem, $tax['Tax']['value']);
            }

            if (isset($mappedTaxes[$calculationItem->getTax2Id()])) {
                $tax = $mappedTaxes[$calculationItem->getTax2Id()];
                InvoiceProfitCalculator::calculateInclusiveTax($calculationItem, $tax['Tax']['value']);
            }

            $profit = InvoiceProfitCalculator::calculateItemProfit($calculationItem, $currencyRate);
            $profitTotal += $profit;
            $profits['items'][$k] = [
                'name' => "#" . $calculationItem->getProductCode() .' '. $calculationItem->getProductName(),
                'profit' => $profit,
                'average_cost' => $calculationItem->getPurchasePrice(),
                'selling_price' => $calculationItem->getFinalPrice() * $currencyRate,
                'quantity' => $calculationItem->getQuantity(),
                'tracking_data' => $calculationItem->getTrackingData(),
            ];
        }
        $profits['total'] = $profitTotal;
        return $profits;
    }


}