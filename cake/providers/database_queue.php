<?php

use App\Auth\AuthRepository;
use App\Auth\AuthSession;

use App\TokenGenerator\CakeTokenGenerator;
use Izam\Daftra\Common\Auth\AuthHelper;
use Izam\Daftra\Common\Queue\EventPlatformUtil;
use Izam\Daftra\Queue\Config\QueueConfig;
use Izam\Daftra\JWT\JWT;
use Izam\Daftra\Queue\Events\EventDispatcher;
use Izam\Daftra\Queue\Services\QueueService;
use Izam\Daftra\Queue\Tokens\TokenData;
use Izam\Daftra\Queue\Repositories\EventActionRepository;
use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;

JWT::setConfig(JWT_SECRET, JWT_ALGO);
$queueRepo = new EventActionRepository(new \Izam\Daftra\Queue\Models\EventAction());
QueueConfig::init($queueRepo, new CakeTokenGenerator(new TokenData()));


$fullVersion = explode('.', PHP_VERSION);
$version = $fullVersion[0].'.'.$fullVersion[1];
$commands = [
    EventPlatformUtil::LARAVEL =>  "nohup php{$version} /var/www/html/".LARAVEL_DIR."/artisan laravel-listener:execute %d",
    EventPlatformUtil::CAKE => "nohup php{$version} /var/www/html/".CAKE_DIR."/webroot/cron.php /cron/cake_listener_execute/%d",
];

QueueConfig::setCallBack([
    function($listener) use ($commands) {

        exec(
            sprintf(
                $commands[$listener['platform']],
                $listener['id']
            ),
            $op
        );
    }
]);
AuthHelper::setConfig(new AuthRepository(), new AuthSession);

function isRunningInQueue() {
    return Configure::read('is_queue') || isset($_GET['ignore_queue']);
}

// warning suppress
if (!defined('WORKING_DIRECTORY')) {
    define('WORKING_DIRECTORY', getcwd());
}

// end warning suppress
register_shutdown_function(function () use ($queueRepo) {
    chdir(WORKING_DIRECTORY);
    if (EventActionRepository::$Saved) {
        $eventsIds = EventDispatcher::getInstance()->getEventList();
        if (empty($eventsIds)) {
            return;
        }

        $queueService = new QueueService($queueRepo);
        $queueService->dispatchQueueCommand(getCurrentSite('id'));


//        if (
//            (getCurrentSite('id') % 10 == 0  || !IzamDatabaseServiceProvider::getConfig("local_queue", "remote_server")) //remote server faild to connect
//                &&
//            IzamDatabaseServiceProvider::hasTable('local_queue', 'event_queue_commands')
//
//        )  {
//            $queueService = new QueueService($queueRepo);
//            $queueService->dispatchQueueCommand(getCurrentSite('id'));
//        } else if (IzamDatabaseServiceProvider::getConfig("local_queue", "remote_server")) {
//            $i = 0;
//            foreach ($eventsIds as $id) {
//                if ($i >= 5) {
//                    break;
//                }
//                $queueCommand = QUEUE_COMMAND;
//                $queueCommand = str_replace('$params', getCurrentSite('id')." ".$id, $queueCommand);
//                exec($queueCommand);
//                $i++;
//            }
//
//        }

    }
});
