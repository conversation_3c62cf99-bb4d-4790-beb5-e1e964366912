<?php


use App\Formatter\AppointmentFormatter;
use App\Formatter\PostFormatter;
use App\Helpers\PluginHelper;
use App\Services\LocalEntityForm\AppEntitiesFormService;
use App\Services\UserSessionRedisService;
use App\Services\UserKeyValueRedisService;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Aws\Repositories\FileRepository;
use Izam\Aws\S3UploadHandler;
use Izam\Daftra\AppManager\Repositories\AppReportRepository;
use Izam\Daftra\AppManager\Repositories\Cache\AppSiteRepository;
use Izam\Daftra\AppManager\Repositories\Interfaces\AppEntityRepositoryInterface;
use Izam\Daftra\AppManager\Repositories\PageScriptRepository;
use Izam\Daftra\AppManager\Services\Interfaces\AppEntitiesFormHandlerInterface;
use Izam\Daftra\Client\Services\ClientService;
use Izam\Daftra\Common\Entity\Actions\ShowAction\AppEntityShowAction;
use Izam\Daftra\Common\Entity\Actions\ShowAction\IdLoaders\BelongsToIdsLoader;
use Izam\Daftra\Common\Entity\Actions\ShowAction\IdLoaders\HasManyIdsLoader;
use Izam\Daftra\Common\Entity\Actions\ShowAction\IdLoaders\HasOneIdsLoader;
use Izam\Daftra\Common\Entity\Actions\ShowAction\IdLoaders\IdLoaderFactory;
use Izam\Daftra\Common\Entity\Actions\ShowAction\RelationIdsLoader;
use Izam\Daftra\Common\Entity\Actions\ShowAction\RelationLoader;
use Izam\Daftra\Common\Entity\Actions\ShowAction\RelationRecordAssigner;
use Izam\Daftra\Common\Entity\Actions\ShowAction\RelationRecordsLoader;
use Izam\Daftra\Common\EntityStructure\IEntityStructureGetter;
use Izam\Daftra\Common\Repositories\EntityIdsLoaderRepoInterface;
use Izam\Daftra\Common\Repositories\EntityRecordFinderRepositoryInterface;
use Izam\Daftra\Common\Repositories\EntityRecordsLoaderInterface;
use Izam\Daftra\Expense\Services\ExpenseService;
use Izam\Daftra\Invoice\Repositories\InvoiceRepository;
use Izam\Daftra\Invoice\Repositories\SalesOrderRepository;
use Izam\Daftra\Invoice\Services\ConvertSalesOrderToInvoiceService;
use Izam\Daftra\Invoice\Services\CreditNoteService;
use Izam\Daftra\Invoice\Services\DebitNoteService;
use Izam\Daftra\Invoice\Services\EstimateService;
use Izam\Daftra\Invoice\Services\InvoicePaymentService;
use Izam\Daftra\Invoice\Services\InvoiceService;
use Izam\Daftra\Invoice\Services\RefundReceiptService;
use Izam\Daftra\Invoice\Services\SalesOrderService;
use Izam\Daftra\Invoice\Services\AdvancePaymentService;
use Izam\Daftra\Journal\Services\JournalService;
use Izam\Daftra\Product\Repositories\ProductRepository;
use Izam\Daftra\Product\Services\ProductService;
use Izam\Daftra\PurchaseOrder\Services\PurchaseOrderService;
use Izam\Daftra\Supplier\Services\SupplierService;
use Izam\Daftra\Workflow\Repositories\WorkflowRepository;
use Izam\Daftra\Workflow\Repositories\WorkflowTypeRepository;
use Izam\Daftra\Workflow\WorkflowService;
use Izam\Dynamic\List\Repository\LocalEntityRepository;
use Izam\Entity\ListingValue\ListingValueManager;
use Izam\Entity\ListingValue\ListingValueRepositoryInterface;
use Izam\Entity\Repository\DynamicRepo;
use Izam\Entity\Repository\PortalFieldRelationRepository;
use Izam\Entity\Repository\SystemFieldRelationRepository;
use Izam\ManufacturingOrder\Services\ManufacturingOrderCostSheetService;
use Izam\View\Form\Tab\Services\CreateTabsServices;

$container = \Izam\ServiceProvider\ServiceContainer::getInstance();
$deps = array(

);
if(!IS_CRON) {
    \Izam\DynamicPermissions\DynamicPermissionService::init(new \Izam\DynamicPermissions\Repositories\DynamicPermissionsRepo(getPDO('default')));
}
$singletons = [
    \Izam\StockRequest\Repositories\StockRequestRepository::class,
    \Izam\StockRequest\Services\StockRequestService::class => function()use ($container){
        return new \Izam\StockRequest\Services\StockRequestService($container->get(\Izam\StockRequest\Repositories\StockRequestRepository::class));
    },
    \Izam\Daftra\Common\EntityStructure\CacheDriverInterface::class => function() {
        return new \App\Cache\CacheDriver();
    },
    \Izam\Diagnostics\DiagnosticsManager::class => function() {
        return new \Izam\Diagnostics\DiagnosticsManager(new \Izam\Diagnostics\DiagnosticRepository(), getPDO('default'), new PluginHelper());
    },
    \Izam\Daftra\Workflow\WorkflowService::class => function() {
        return new WorkflowService(new WorkflowRepository(getPDO('default')));
    },
    WorkflowTypeRepository::class => function() {
        return new WorkflowTypeRepository(getPDO('default'));
    },
    \Izam\Daftra\Journal\Repositories\JournalAccountRepository::class,
    \Izam\Daftra\Journal\Repositories\JournalAccountRouteRepository::class,
    \Izam\Daftra\AppManager\Repositories\AppEntityActionRepository::class => function() {
        return new \Izam\Daftra\AppManager\Repositories\AppEntityActionRepository();
    },
    \Izam\Daftra\AppManager\Repositories\Cache\AppRepository::class => function() {
        return new \Izam\Daftra\AppManager\Repositories\Cache\AppRepository(
            new \Izam\Daftra\AppManager\Repositories\AppRepository(),
            new \App\Cache\CacheDriver()
        );
    },
    \Izam\Daftra\AppManager\Repositories\Cache\AppSiteRepository::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Repositories\Cache\AppSiteRepository(
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppRepository::class),
            new \Izam\Daftra\AppManager\Repositories\AppSiteRepository(),
            new \App\Cache\CacheDriver()
        );
    },
    AppReportRepository::class => function() use ($container) {
        return new AppReportRepository();
    },
    \Izam\Daftra\AppManager\Repositories\Cache\AppButtonRepository::class => function() {
        return new \Izam\Daftra\AppManager\Repositories\Cache\AppButtonRepository(
            new \Izam\Daftra\AppManager\Repositories\AppButtonRepository(),
            new \App\Cache\CacheDriver()
        );
    },
    \Izam\ScoreCalculator\Repositories\SiteScoreRepository::class => function() {
        return new \Izam\ScoreCalculator\Repositories\SiteScoreRepository();
    },
    \Izam\Daftra\AppManager\Services\LockEntityService::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Services\LockEntityService(
            $container->get(\Izam\Daftra\AppManager\Repositories\AppEntityActionRepository::class),
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppRepository::class),
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppSiteRepository::class),
        );
    },
    \Izam\Daftra\AppManager\Services\ButtonService::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Services\ButtonService(
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppButtonRepository::class),
            new \Izam\Daftra\Common\Factories\TabActionFactory(),
            new \Izam\Daftra\Common\Factories\TabMenuItemFactory(),
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppSiteRepository::class),
        );
    },
    PageScriptRepository::class => function() use ($container) {
        return new PageScriptRepository(new \App\Repositories\PluginRepository());
    },
    \Izam\Daftra\AppManager\Repositories\Cache\PageScriptRepository::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Repositories\Cache\PageScriptRepository(
            $container->get(PageScriptRepository::class),
            new \App\Cache\CacheDriver(),
            new \App\Repositories\PluginRepository(),
        );
    },
    \Izam\Daftra\AppManager\Repositories\Cache\AppReportRepository::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Repositories\Cache\AppReportRepository(
            $container->get(AppReportRepository::class),
            new \App\Cache\CacheDriver(),
        );
    },
    \Izam\Daftra\AppManager\Services\PageScriptService::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Services\PageScriptService(
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\PageScriptRepository::class),
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppSiteRepository::class),
        );
    },
    \Izam\Daftra\AppManager\Services\AppReportService::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Services\AppReportService(
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppReportRepository::class),
            $container->get(AppSiteRepository::class),
        );
    },
    \Izam\Daftra\AppManager\Repositories\Cache\AppEntityRepository::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Repositories\Cache\AppEntityRepository(
            new \Izam\Daftra\AppManager\Repositories\AppEntityRepository(),
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppSiteRepository::class),
            new \App\Cache\CacheDriver()
        );
    },
    AppEntityRepositoryInterface::class => function() use ($container) {
        return $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppEntityRepository::class);
    },
    \Izam\Daftra\AppManager\Events\AppEntityEventRegistry::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Events\AppEntityEventRegistry(
            $container->get(AppEntityRepositoryInterface::class),
        );
    },
    \Izam\Daftra\AppManager\Events\AppTriggerEventRegistry::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Events\AppTriggerEventRegistry(
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppTriggerRepository::class),
        );
    },
    \Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\ButtonServiceErrorHandlerDecorator::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\ButtonServiceErrorHandlerDecorator(
            $container->get(\Izam\Daftra\AppManager\Services\ButtonService::class),
        );
    },
    \Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\PageScriptErrorHandlerDecorator::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\PageScriptErrorHandlerDecorator(
            $container->get(\Izam\Daftra\AppManager\Services\PageScriptService::class),
        );
    },
    \Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\AppReportErrorHandlerDecorator::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Services\ErrorHandlerDecorators\AppReportErrorHandlerDecorator(
            $container->get(\Izam\Daftra\AppManager\Services\AppReportService::class),
        );
    },

    \Izam\Daftra\AppManager\Repositories\Cache\AppTriggerRepository::class => function() use ($container) {
        return new \Izam\Daftra\AppManager\Repositories\Cache\AppTriggerRepository(
            new \Izam\Daftra\AppManager\Repositories\AppTriggerRepository(),
            $container->get(\Izam\Daftra\AppManager\Repositories\Cache\AppSiteRepository::class),
            new \App\Cache\CacheDriver()
        );
    },
    \Izam\Entity\Repository\Proxy\SchemaRepository::class => function() use ($container) {
        return new \Izam\Entity\Repository\Proxy\SchemaRepository(
            new \Izam\Entity\Repositories\SchemaPortalRepository(),
            new \Izam\Entity\Repositories\SchemaSystemRepository(),
        );
    },
    \Izam\Entity\Repository\Proxy\FieldRelationRepository::class => function() use ($container) {
        return new \Izam\Entity\Repository\Proxy\FieldRelationRepository(
            new PortalFieldRelationRepository(),
            new SystemFieldRelationRepository(),
        );
    },

    AttachmentsService::class => function(){
       return new AttachmentsService();
    },
    S3UploadHandler::class => function(){
        return new S3UploadHandler(new FileRepository());
     },
    AppointmentFormatter::class => function() {
        return new AppointmentFormatter();
    },

    PostFormatter::class => function() {
        return new PostFormatter();
    },
    UserSessionRedisService::class => function() {
        return (new UserSessionRedisService())->init();
    },
    UserKeyValueRedisService::class => function() {
        return (new UserKeyValueRedisService())->init();
    },

    CreateTabsServices::class => function(){
        return new CreateTabsServices();
    },
    SupplierService::class,
    ClientService::class,
    ProductService::class,
    PurchaseOrderService::class,
    ExpenseService::class,
    JournalService::class,
    InvoiceService::class,
    InvoicePaymentService::class,
    RefundReceiptService::class,
    CreditNoteService::class,
    DebitNoteService::class,
    EstimateService::class,
    EntityIdsLoaderRepoInterface::class => \App\Repositories\ShowActionRepo::class,
    EntityRecordFinderRepositoryInterface::class => \App\Repositories\ShowActionRepo::class,
    InvoiceRepository::class => InvoiceRepository::class,
    ConvertSalesOrderToInvoiceService::class => function() use($container) {
        return new ConvertSalesOrderToInvoiceService($container->get(InvoiceRepository::class));
    },
    AdvancePaymentService::class => function() use($container) {
        return new AdvancePaymentService($container->get(InvoiceRepository::class));
    },
    RelationLoader::class,
    RelationRecordsLoader::class => function() use($container) {
        return new RelationRecordsLoader($container->get(EntityRecordsLoaderInterface::class));
    },
    RelationIdsLoader::class => function() use($container) {
        return new RelationIdsLoader($container->get(IdLoaderFactory::class));
    },
    IdLoaderFactory::class => function() use($container) {
        return new IdLoaderFactory(
            $container->get(HasManyIdsLoader::class),
            $container->get(BelongsToIdsLoader::class),
            $container->get(HasOneIdsLoader::class)
        );
    },
    HasManyIdsLoader::class => function() use($container) {
        return new HasManyIdsLoader($container->get(EntityIdsLoaderRepoInterface::class));
    },
    \Izam\Entity\EntityDeleter\EntityDeleter::class => function() use($container) {
        return new Izam\Entity\EntityDeleter\EntityDeleter($container->get(IdLoaderFactory::class));
    },
    HasOneIdsLoader::class=> function() use($container) {
        return new HasOneIdsLoader($container->get(EntityIdsLoaderRepoInterface::class));
    },
    BelongsToIdsLoader::class=> function() use($container) {
        return new BelongsToIdsLoader($container->get(EntityIdsLoaderRepoInterface::class));
    },
    RelationRecordAssigner::class,
    \Izam\Daftra\Common\Entity\Actions\ShowAction\EagerRelationsLoader::class,
    IEntityStructureGetter::class => \App\Entity\EntityStructureGetter::class,
    EntityRecordsLoaderInterface::class => \App\Repositories\ShowActionRepo::class,
    DynamicRepo::class => DynamicRepo::class,
    AppEntityShowAction::class => function() use($container) {
        $showAction = new AppEntityShowAction(
            $container->get(DynamicRepo::class),
            $container->get(RelationLoader::class),
            $container->get(RelationIdsLoader::class),
            $container->get(RelationRecordAssigner::class),
            $container->get(RelationRecordsLoader::class),
            $container->get(IEntityStructureGetter::class),
            $container->get(\Izam\Daftra\Common\Entity\Actions\ShowAction\EagerRelationsLoader::class)
        );
        return $showAction;
    },
    ListingValueRepositoryInterface::class=> function() use($container) {
        return new \Izam\Entity\ListingValue\ListingValueRepository();
    },
    AppEntitiesFormHandlerInterface::class=> function() use($container) {
        return new AppEntitiesFormService();
    },
    ListingValueManager::class=> function() use($container) {
        return new \Izam\Entity\ListingValue\ListingValueManager(
            $container->get(ListingValueRepositoryInterface::class),
            $container->get(IEntityStructureGetter::class),
        );
    },
    \Izam\ManufacturingOrder\Repositories\ManufacturingOrderRepository::class,
    ProductRepository::class,
    \Izam\ManufacturingOrder\Services\ManufacturingOrderService::class => function() use ($container) {
        return new \Izam\ManufacturingOrder\Services\ManufacturingOrderService($container->get(\Izam\ManufacturingOrder\Repositories\ManufacturingOrderRepository::class));
    },
    \Izam\ManufacturingOrder\Repositories\ManufacturingOrderCostSheetRepository::class,
    \Izam\ManufacturingOrder\Services\ManufacturingOrderCostSheetService::class => function() use ($container) {
        return new \Izam\ManufacturingOrder\Services\ManufacturingOrderCostSheetService($container->get(\Izam\ManufacturingOrder\Repositories\ManufacturingOrderCostSheetRepository::class));
    },
    \Izam\ManufacturingOrder\Repositories\BomRepository::class,
    \Izam\ManufacturingOrder\Services\BomService::class => function() use ($container) {
        return new \Izam\ManufacturingOrder\Services\BomService($container->get(\Izam\ManufacturingOrder\Repositories\BomRepository::class));
    },
    SalesOrderRepository::class,
    LocalEntityRepository::class,
    SalesOrderService::class => function() use ($container) {
        return new SalesOrderService($container->get(SalesOrderRepository::class));
    },
    Izam\ManufacturingOrder\Services\HandleManufacturingOrderUpdated::class => function() use ($container) {
        $costSheetService = $container->get(\Izam\ManufacturingOrder\Services\ManufacturingOrderCostSheetService::class);
        $productionPlanService = new \Izam\ManufacturingOrder\Services\ProductionPlanService(new \Izam\ManufacturingOrder\Repositories\ProductionPlanRepository());
        $class= new \Izam\ManufacturingOrder\Services\HandleManufacturingOrderUpdated($costSheetService, $productionPlanService);
        return $class;
    },

];
foreach ($deps as $alias => $concrete) {
    if(is_numeric($alias)) {
        $alias = $concrete;
    }
    if(!$concrete instanceof \Closure) {
        $concrete = function () use ($concrete) {
            return new $concrete();
        };
    }
    if(is_numeric($alias)) {
        $container->add($concrete, $concrete);
    } else {
        $container->add($alias, $concrete);
    }
}

foreach ($singletons as $alias => $concrete) {
    if(is_numeric($alias)) {
        $alias = $concrete;
    }
    if(!$concrete instanceof \Closure) {
        $concrete = function () use ($concrete) {
            return new $concrete();
        };
    }
    if(is_numeric($alias)) {
        $container->add($alias, $concrete, true);
    } else {
        $container->add($alias, $concrete, true);
    }
}


