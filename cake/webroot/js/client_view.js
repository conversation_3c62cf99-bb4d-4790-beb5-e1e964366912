$(document).ready(function() {
    $(document.body).on('click', '.delete_staff', function(event) {
        $staff_row = $(this).attr('staff_id');

        $.ajax({url: $(this).attr('href'), success: function(result) {
                if (result == 'ok') {
                    $("#staff_" + $staff_row).remove();
                }
            }});



        return false;
    });

    // var box = $('<div class="box-loader responsive-lightbox" style="width: 720px; height: 400px; overflow: auto"></div>');
    // var boxImage = '<img src="/css/images/report-loader.gif" style="margin-top: 168px; margin-left: 318px"/>';
    // $('a.view-payment,a.view-emaillog').click(function() {
    //     $('#myModalTax').modal('show');
    //     var href = $(this).attr('href');
    //     box.html(boxImage).lightbox_me().load(href, {box: 1})
    //     return false;
    // });

    var boxImage = '<img src="/css/images/report-loader.gif" style="margin: 0 auto"/>';

    $('a.view-payment,a.view-emaillog').click(function() {
        var href = $(this).attr('href');
        $('#myModalTaxBody').html(boxImage);
        // Load content via AJAX
        $.get(href + (href.indexOf('?') > -1 ? '&' : '?') + 'box=1', function(data) {
            $('#myModalTaxBody').html(data);
        });
        $('#myModalTax').modal('show');
        return false;
    });

});

$(document).ready(function() {

    if (location.hash == "#TimelineBlock") {
        reload_filter();
    }
    if (location.hash == "#AppointmentsBlock") {
        reload_appointments();
    }

    // show active tab on reload
    if (location.hash !== '')
        $('a[href="' + location.hash + '"]').tab('show');

    // remember the hash in the URL without jumping
    $('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
        if (history.pushState) {
            history.pushState(null, null, '#' + $(e.target).attr('href').substr(1));
        } else {
            location.hash = '#' + $(e.target).attr('href').substr(1);
        }
    });

    if ($('.input-fields td').length == 0)
        $('.input-fields').hide();
});
/*
function reload_posts(note_page) {

  var selected = [];
	$('#fliter-actionid option:selected').each(function() {
		selected.push($(this).val());
	});

  var selected2 = [];
	$('#fliter-followupid option:selected').each(function() {
		selected2.push($(this).val());
	});

	var note_staff = [];
	$('#fliter-note_staff option:selected').each(function() {
		note_staff.push($(this).val());
	});


        var note_date_from=$('#NoteDateFrom').val();
	var note_date_to=$('#NoteDateTo').val();
	$("#notes_content").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');

        $.ajax({url: '/owner/posts/list_post/1/'+client_id+'?action='+selected+'&followupid='+selected2+'&staff_id='+note_staff+'&order='+note_order+'&date_from='+note_date_from+'&date_to='+note_date_to+'&page='+note_page+'&linked_note='+linked_note,cache:false, success: function(result) {

                $("#notes_content").html(result);

            }});
        linked_note='';
    }*/
function reload_appointments(status = 0)
{
    $("#AppointmentsBlock_rel").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');

    $('#AppointmentsBlock_rel').load('/owner/appointments/index/0/1?partner_id=' + client_id + '&compact=0&status_id='+status+'&selected_client=1&back_to_client=true' , function (){
		$('.has-tip').each(function(){
			$(this).qtip({content:$('#'+$(this).data('tip')).html(), style: {
                        classes: 'qtip-bootstrap'
                    }, position: {my: 'top center', at: 'bottom center'}});
		});
	});
//    $("#AppointmentsBlock").html('<div class="notifcation-loader"><div class="inner-loader"></div></div>');
//
//    $('#AppointmentsBlock').load('/owner/appointments/index/1?status_id=-1&no_client=1&compact=0&item_id=' + client_id + '&selected_client=1&back_to_client=true' , function (){
//		$('.has-tip').each(function(){
//			$(this).qtip({content:$('#'+$(this).data('tip')).html(), style: {
//                        classes: 'qtip-bootstrap'
//                    }, position: {my: 'top center', at: 'bottom center'}});
//		});
//	});


}
