var itemsColspan=4;
var loaded_uploaded_doc=0;
var lastItemIndex = 0;
var lastReminderIndex = 0;
var timeoutID;
var baseItemRow;
var baseReminderRow;
var baseDocumentRow;
var documentList;
var lastDocumentIndex = 0 ;
var baseItemIndex;
var baseReminderIndex;
var lastFieldIndex = 0;
var cuCh = 0;
var summary_paid = 0;
if (typeof group_price_id === 'undefined' || group_price_id === null) {
    var group_price_id = 0;
}
var db_group_price_id = 0;
var frame = null;
var ajaxUrl;
var totalValue=0;
var used_clients=[];
var lastTaxfocus=false;
var client_faddress='';
var columns={"col_1":'name',"col_2":'description',"col_3":'',"col_4":'',"col_5":''}
var org_columns=jQuery.extend({}, columns);
var saj=0;
var aps='1';
var ifram_css='calc(100vh - 55px)';
var tax_count_value=window['tax_count']==undefined?0:window['tax_count'];
var Sending=false;
var ClientLoyaltyPoints = 0;
window.lastFocusedItemNameInput = null;


function getUrlQuiry(quiryName)
{
	var hash = null;
	if(window.location.href.indexOf(quiryName) > 0){
		hash = window.location.href.slice(window.location.href.indexOf(quiryName) + quiryName.length + 1);
	}
    return hash;
}

function getUrlParams()
{
    var params = [], hash;
    var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
    for(var i = 0; i < hashes.length; i++)
    {
        hash = hashes[i].split('=');
        params.push(hash[0]);
        params[hash[0]] = hash[1];
    }
    return params;
}

function getCurrentMedule()
{
	var url = window.location.href;

	if(url.includes("estimate")){
		return "estimateDrafts";
	}
	else if(url.includes("creditnote")){
		return "creditDrafts";

	}
	else if(url.includes("refund")){
		return "refundDrafts";

	}
	else if(url.includes("/invoice") && !location.href.includes("invoices/add/")){
		return "invoicesDrafts";
	}
	else{
		return null;
	}
}


$(function(){

	// loadClientData();
    var draftId = getUrlParams()["draftId"];
	var sendRequest = getUrlParams()["send"];
	var storageName = getCurrentMedule();
   


        $('.MoreFieldsListDropDown').live('click',function(event){
            $('.MoreField', '#MoreFieldsList').click();
        });
	$('.add_as_newclient').live('click',function(event){
		$('#ClientData').append('<input name="data[Invoice][client_skip_email]" id="ClientSkipEmail" value="1" type="hidden">');
		$('#SaveClient').trigger('click');
	});
		$('.Update_Existed_Client').live('click',function(event){
			
			ajaxUrl=$('.Update_Existed_Client').data('data-url');
			
			$('#ClientID').val($('.Update_Existed_Client').data('id'));
			$('#InvoiceClientId').val($('.Update_Existed_Client').data('id'));
		$('#SaveClient').trigger('click');
	});
	
	if($('#ClientData .form-error').length>0){ 
	$('#ClientData').show();
	}
	$('a').attr('tabindex', -1);
	$('.attach-invoice-document').live('click',function(event){
		event.preventDefault();
//		if ($('.itemRow', '#DocumentList').length >= 10){
//			jAlert(__('You can add up to 10 documents only'), __('Documents limit'));
//			return false;
//		}
		document_id = $('#InvoiceDoc').val();

		if(document_id == ''){
			$('#InvoiceDoc').focus();
			return false;
		}
                load_doc(document_id);
                return  false;
		$('.empty-row', '#DocumentList').hide();
		if (lastDocumentIndex == 0 && $('.itemRow', '#DocumentList').length == 0){
			lastDocumentIndex = 0;
		} else {
			++lastDocumentIndex;
		}
		var newRow = baseDocumentRow.replace(/0/g, lastDocumentIndex );
		$('.invoice_docs_div').append(newRow);
		lastDoc = $('#DocumentList tr.itemRow:last');
		$('a.preview' , lastDoc).attr('href',documentList[document_id].file_full_path);
		$('span.document-title' , lastDoc).attr('rel', 'doument-'+document_id).html(documentList[document_id].title);
		$('#InvoiceDocument'+lastDocumentIndex+'DocumentId').val(document_id);
		
		//$(".attach-document option[value='"+document_id+"']").remove();
		return false;
	});

	$(document).click(function(evt){
		var store_id = $('#RequisitionStoreId option:selected').val();
		if (!$(evt.target).hasClass('item-arrow')){
			
			if($(evt.target).hasClass('item_name') && $(evt.target).attr('readonly')){
				return;
			}
			
		if($('#InvoiceProducts').data('visible')==true){
			list_products('', store_id);
			$('#search_product').val('').focus();
			}
			$('#InvoiceProducts').hide().data('visible', false);
		}
		
		if (! ($(evt.target).hasClass('tax-arrow') || $(evt.target).hasClass('item-tax-display') || $(evt.target).hasClass('tax-link'))){
			$('#TaxList').hide().data('visible', false);
		}
		if (!$(evt.target).hasClass('select-action-arrow')){
			$('.sub-actions').hide().data('visible', false);
		}
	});

	$('.removeDocument').live('click' , function(event){
            
		event.preventDefault();
                $id=$(this).attr('id');
                $('#invoice-doc-'+$id).remove();
                return;
		tr_obj = $(this).parents('tr');
		document_id = $('span.document-title' , tr_obj).attr('rel').replace('doument-' , '');
		tr_obj.remove();
		//var option = '<option value="'+document_id+'">'+documentList[document_id].title+'</option>';
		//$(".attach-document").append(option);
		if ($('.itemRow', '#DocumentList').length == 0){
			$('.empty-row', '#DocumentList').show();
		}
		return false;
	});

	if ($('.itemRow', '#DocumentList').length == 0){
		$('.empty-row', '#DocumentList').show();
	} else {
		$('.empty-row', '#DocumentList').hide();
	}
	/// Terms and conditions checkbox
	if(!$('#TextCheckbox').attr('checked')){
		$('#TextTerms').hide();
	}
	$('#TextCheckbox').change(function(){
		if($(this).attr('checked')){
			$('#TextTerms').show();
		}else{
			$('#TextTerms').hide();
		}
	});
	$('#discount_type').change(function(){
		
		if($(this).val()=='percent'){
			$('#InvoiceDiscountAmount').hide().val('');
			$('#InvoiceDiscount').show().val('');
			
		}else{
			$('#InvoiceDiscount').hide().val('');
			$('#InvoiceDiscountAmount').show().val('');
		}
	});
	if (!$('#InvoiceRequiredTermsFile').attr('checked')){
		$('#FileTerms').hide();
	}
	$('#InvoiceRequiredTermsFile').change(function(){
		if($(this).attr('checked')){
			$('#FileTerms').show();
		}else{
			$('#FileTerms').hide();
		}
	});

	
	baseItemRow = $('#listing_table tr.itemRow:first').html();
	if ( typeof baseItemRow !== 'undefined'){
		baseItemRow = $("<div/>").append(
			$('.not-cloneable', baseItemRow).remove().end()
		).html();
		baseItemIndex = $('.item_name', baseItemRow).attr('id').replace(/\D/g, '');
		baseReminderIndex = $('.item_name', baseItemRow).attr('id').replace(/\D/g, '');
		setFirstLastNotActive('#listing_table');
		setFirstLastNotActive('#InvoiceCustomFields');
	}

	// ---------------------------
	$('.add-reminder').bind('click',function(e){
		if ($('.itemRow', '#RemindersList').length >= 10){
			jAlert(__('You can add up to 10 reminders only'), __('Reminders limit'));
			return false;
		}

		$('.empty-row', '#RemindersList').hide();
		lastReminderIndex = 0;
		$('.itemRow', '#RemindersList').each(function(){
			var idx = string2number($(':input:first', this).attr('id').replace(/\D/g, ''));
			if (idx > lastReminderIndex){
				lastReminderIndex = idx;
			}
		});

		if (lastReminderIndex == 0 && $('.itemRow', '#RemindersList').length == 0){
			lastReminderIndex = 0;
		} else {
			++lastReminderIndex;
		}


		$('table#RemindersList').append(baseReminderRow);
		lastRow = $('#RemindersList tr.itemRow:last');

		$(':input', lastRow).each(function(){
			this.id = this.id.replace(baseItemIndex, lastReminderIndex);
			this.name = this.name.replace(baseItemIndex, lastReminderIndex);
			$(this).val('');
		});

		$(':input'. lastRow).removeClass('form-error');
		$('.error-message', lastRow).remove();
		$('a', lastRow).attr('rel', lastReminderIndex);
		return false;
	});
	// ------------------------
	// Remove Item
	$('.removeReminder', '#RemindersList').live('click', function(){
		$(this).parents('tr').remove();
		if ($('.itemRow', '#RemindersList').length == 0){
			$('.empty-row', '#RemindersList').show();
		}
		return false;
	});
	if ($('.itemRow', '#RemindersList').length == 0){
		$('.empty-row', '#RemindersList').show();
	} else {
		$('.empty-row', '#RemindersList').hide();
	}

	function addItemFun(){
		lastItemIndex = 0;

		$('tr.itemRow', '#listing_table').each(function(){
			var idx = string2number($(':input:first', this).attr('id').replace(/\D/g, ''));
			if (idx > lastItemIndex){
				lastItemIndex = idx;
			}
		});
		++lastItemIndex;
		$('.itemRow:last', '#listing_table').after('<tr class="itemRow movable" style="display:none;">' + baseItemRow + '</tr>');

		var lastRow = $('tr.itemRow:last', '#listing_table');
		$(':input', lastRow).each(function(){
			this.id = this.id.replace(baseItemIndex, lastItemIndex);
			this.name = this.name.replace(baseItemIndex, lastItemIndex);
			$(this).val('');
		}).removeClass('form-error');

        $('.item-qty',lastRow).removeAttr('data-track_stock');
		$('.error-message', lastRow).remove();
		$('a', lastRow).attr('rel', lastItemIndex);
		$('.qty', lastRow).val('1');
		$('td.subtotal', lastRow).text('0.00');
		$('.item-qty', lastRow).css('display', 'block')
		beforelastRow=lastRow.prev();
		$('.move-table .sortable-arrows .ArrawDown', beforelastRow).removeClass('move-down-notactive').removeClass('move-down').addClass('move-down');
		setFirstLastNotActive('#listing_table');
		$(lastRow).show();
	}

	if(draftId){
		$('#InvoiceForm').append(`<input type="hidden" name="data[non_completed_id]" id="nonCompletedID" value="${draftId}">`);
	}
	if(draftId && !sendRequest){
		var serializedData = JJLC.getItem(storageName);
        var invoicesDrafts = JSON.parse(serializedData);
        if (invoicesDrafts && invoicesDrafts[draftId]) {
	
			if(invoicesDrafts[draftId].invoiceItems>0){
				$('#InvoiceForm .invoice-items').html(invoicesDrafts[draftId].invoiceItemsData);

			}

            setTimeout(() => {
				if(invoicesDrafts[draftId].warehouseIsChecked){
					$('#show_item_stores, #show_item_stores_').attr('checked', true);
					$('#show_item_stores').trigger("change");
				}
            	$('#InvoiceForm').deserialize(JSON.stringify(invoicesDrafts[draftId].invoicesDraft));
				calculateTotals();

			
				if(invoicesDrafts[draftId].PaymentIsPaid){
					$('#PaymentIsPaid').attr('checked', true);
					$('#PaymentIsPaid').trigger("change");
				}
				

				if(invoicesDrafts[draftId].invoiceLayout){
					$('#InvoiceLayout').val(invoicesDrafts[draftId].invoiceLayout);
					$('#InvoiceLayout').trigger('change',["def"]);
				}
				if(invoicesDrafts[draftId].salesPerson){
					$('#InvoiceSalesPersonId').val(invoicesDrafts[draftId].salesPerson);
				}
				if(invoicesDrafts[draftId].journalAccount?.id){
					$('#journal_accounts').append('<option value="' + invoicesDrafts[draftId].journalAccount.id + '" ' + '>' + invoicesDrafts[draftId].journalAccount.value + '</option>');
					$('#journal_accounts').val(invoicesDrafts[draftId].journalAccount.id);
					$('#journal_accounts').selectpicker('refresh');
					
				}
			
			
				if(invoicesDrafts[draftId].clientID){
					loadClientsList(invoicesDrafts[draftId].clientID);
					$("#InvoiceClientId").val(invoicesDrafts[draftId].clientID);
					loadClientData();
					$('#ClientFromData').val(1);
					$('#ClientData').hide();
					$('.ClientSelect').show();
				}
				
				
			if(invoicesDrafts[draftId]?.tags?.length > 0){
				$('#tag-input-id').attr('class', 'ms-ctn form-control ms-ctn-focus').attr('style', 'width: 100%')
				var ms = $('#tag-input-id')?.magicSuggest({
				  });

				ms.setValue(invoicesDrafts[draftId].tags);
			}
			console.log('i,invoicesDrafts[draftId].notes' ,invoicesDrafts[draftId].notes)
			

            }, 0);

			setTimeout(() => {
				
				$('#DepositIsPaid').attr('checked', invoicesDrafts[draftId].depositIsPaid ).trigger('change');
				$("#InvoiceHtmlNotes").summernote("code",invoicesDrafts[draftId].notes);
				if(invoicesDrafts[draftId].advancedjournalAccount?.id){
					console.log('invoicesDrafts[draftId].advancedjournalAccount?.id', invoicesDrafts[draftId].advancedjournalAccount?.value, invoicesDrafts[draftId].advancedjournalAccount.id)
					$("select[name='data[JournalAccountAdjustmentRoute][account_id]']").prepend('<option value="' + invoicesDrafts[draftId].advancedjournalAccount.id + '" ' + '>' + invoicesDrafts[draftId].advancedjournalAccount.value + '</option>');
					$("select[name='data[JournalAccountAdjustmentRoute][account_id]']").val(invoicesDrafts[draftId].advancedjournalAccount.id).trigger('change');
					$("select[name='data[JournalAccountAdjustmentRoute][account_id]']").selectpicker('refresh');
					
				}
	
			}, 5000);
        }
    }



	// -------------------------
	// Add Item
	$('#AddItem').bind('click',function(e,manual){
		e.preventDefault();
		if($('tr.itemRow', '#listing_table').length > 500) {
			alert(__('You can add up to 500 items only'));
			return;
		}
		lastItemIndex = 0;

		$('tr.itemRow', '#listing_table').each(function(){
			var idx = string2number($(':input:first', this).attr('id').replace(/\D/g, ''));
			if (idx > lastItemIndex){
				lastItemIndex = idx;
			}
		});
		++lastItemIndex;
		$('.itemRow:last', '#listing_table').after('<tr class="itemRow movable" style="display:none;">' + baseItemRow + '</tr>');

		var lastRow = $('tr.itemRow:last', '#listing_table');
		$('.product-price-text', lastRow).remove();
		$(':input', lastRow).each(function(){
			this.id = this.id.replace(baseItemIndex, lastItemIndex);
			this.name = this.name.replace(baseItemIndex, lastItemIndex);
			$(this).val('');
		}).removeClass('form-error');

        $('.item-qty',lastRow).removeAttr('data-track_stock');
		$('.error-message', lastRow).remove();
		$('a', lastRow).attr('rel', lastItemIndex);
		$('.qty', lastRow).val('1');
		$('td.subtotal', lastRow).text('0.00');
		$('.item-qty', lastRow).css('display', 'block')
		beforelastRow=lastRow.prev();
		$('.move-table .sortable-arrows .ArrawDown', beforelastRow).removeClass('move-down-notactive').removeClass('move-down').addClass('move-down');
		setFirstLastNotActive('#listing_table');
		$(lastRow).show();
		if ($('.item_name').length < 20) {
			$('textarea.resizable:visible',lastRow).elastic({
				callback:function(textarea){
					resizeItem(textarea);
				},
				minHeight: 52,
			});
		}
                
		$('.item_name', lastRow).removeAttr('readonly');
		var itemNameChange = $('.item_name').change(function() {
			var index = itemNameChange.index(this);
			var current_id_element = $('.select-prod .item-id')[index];
			var td = $($('.col-1.td_editable')[index]);
			if ($(this).val() && !$(current_id_element).val()) {
				if (!td.find('.bg-warning').length) {
					td.append("<div class='bg-warning fs-12 p-1 product-price-text s2020 text-white-s2020' style='margin-bottom: 8px; padding: 11px; width: 230px'><a href='#' class='warning-add-product-link'>" + __('Non Created Item', true) + " (<b>" + __('Add New Product', true) + "</b>)</a></div>");
				}
			}
		});
		var itemIdChange = $('.item-id').change(function() {
			var index = itemIdChange.index(this);
			var td = $($('.col-1.td_editable')[index]);
			td.find('.bg-warning').remove();
		});
		if(manual!==true){
		$('.item_name', lastRow).focus();
		}
                if ( typeof enable_multi_units !== 'undefined' && enable_multi_units == 1 && $('.multi_units' , lastRow).length > 0  ){
                    $('.multi_units' , lastRow).remove()
                }
                $(".item-discount-type" , lastRow).val(1);
                $('.item-qty' , lastRow).attr("data-factor" , "")
                $('.item-qty' , lastRow).attr("data-factor_name" , "")
				updateTaxColumnsView();
                lock_selection ( lastRow ) ;
                if(typeof ItemColumns !== 'undefined' && !empty(ItemColumns)) show_hide_columns();
                $("#show_item_stores").change();
                $('#InvoiceSalesPersonId').trigger('change');
        // add form data to local storage
        if ($("#listing_table .itemRow").length >= 3) {
            var draftId = null;
            if (storageName != null) {
                draftId = getUrlParams()["draftId"];
            }

            if (!location.href.includes("/edit")) {

                if (draftId == null && storageName != null) {
                    var invoicesDrafts = JJLC.getItem(storageName);
                    var url = window.location.href;
                    var tagsValue = null;
                    if (typeof $magicSuggest != "undefined" && $magicSuggest.getValue()) {
                        tagsValue = $magicSuggest.getValue()
                    }
                    var invoicesDrafts = JJLC.getItem(storageName);
                    var url = window.location.href;
                    var tagsValue = null;
                    if (typeof $magicSuggest != "undefined" && $magicSuggest.getValue()) {
                        tagsValue = $magicSuggest.getValue()
                    }
                    if (!location.href.includes("/edit/") && !location.href.includes("/add/") && totalValue) {

                        if (draftId == null && storageName != null) {

                            var nowDate = Date.now();
                            if (invoicesDrafts == null) {
                                // add new invoicesDrafts in localStorage
                                try {

                                    JJLC.setItem(storageName, JSON.stringify({
                                        [nowDate]: {}
                                    }))

                                } catch (err) {
                                    console.log(err.message);
                                }
                            }

                            $('#InvoiceForm').append(`<input type="hidden" name="data[non_completed_id]" id="nonCompletedID" value="${nowDate}">`)

                            window.history.replaceState({}, '', window.location.href + '?draftId=' + nowDate);

                        }
                    }
                }
            }
        }
    }).attr('tabindex', 21);


    // ------------------------
	// Remove Item
	$('.removeItem', '#listing_table').live('click', function(){
		if ($('tr.itemRow', '#listing_table').length > 1){
			$(this).parents('tr').remove();
			setFirstLastNotActive('#listing_table');
			calculateTotals();
		}
		else
		{
			$(this).parents('tr').find('input, select, textarea').val('').removeAttr('readonly');
			//Clear multi unit special fields
			$(this).parents('tr').find('.multi_units').remove(); 
			$(this).parents('tr').find(".units_price_div").remove();
			$('.item-qty,.item-unit-price').show();
            if($(this).parents('tr').find('.item-serial').length) {
				$(this).parents('tr').find('.item-serial').empty();
			}
			lock_selection ( $(this).parents('tr') ) ;
			calculateTotals();
		}

		return false;
	});
	// for tabs
	$('.tabs-buttons li').click(function(e){
		var rel = $(this).attr('rel');

		if($(rel).hasClass('hidden')){
			activateTab(rel);
		} else{
			deactivateTabs();
		}

		e.preventDefault();
	});

	if ($('.item_name').length < 20) {
		$('textarea.resizable:visible', '#listing_table').elastic({
			callback:resizeItem,
			minHeight: 52,
		});
	}

	$('.sortable-arrows .move-down', '#listing_table').live('click',function(e){
		var current_tr =  $(this).parents('tr');
		
		var next_element = $(this).parents('tr').next('tr');
				
		var next_name = [];
		next_element.find("input,select,textarea").each(function(){
		next_name.push($(this).attr('name'));
		
		});
		var current_name = [];
		current_tr.find("input,select,textarea").each(function(){
		current_name.push($(this).attr('name'));
		
		});
		
		current_tr.remove();
		//current_tr=current_tr.html();
		
		next_element.after(current_tr);
		
		setNotActiveClass($(this).parents('.sortable-arrows'));
		setNotActiveClass(next_element.find('.sortable-arrows'));
		if ($('.item_name').length < 20) {
			$('textarea.resizable:visible', '#listing_table').elastic({
				callback: resizeItem,
				minHeight: 52,
			});
		}
		
		// next_name.forEach(function( i,k ) {
		// 	$('#listing_table').find('*[name="'+current_name[k]+'"]').prop('name','##'+i);
		// 	$('#listing_table').find('*[name="'+i+'"]').prop('name',current_name[k]);
		// 	$('#listing_table').find('*[name="'+'##'+i+'"]').prop('name',i);
		// 	//$('#listing_table').html($('#listing_table').html().replace(current_name[k],'##'+i));
		//
		// });

		
		e.preventDefault();
		
		
		recalcauto();
		ReNumberAll();
	});
	
	
	// ----------------------
	$('.sortable-arrows .move-up', '#listing_table').live('click',function(e){
		var current_tr =  $(this).parents('tr');
		var prev_element = $(this).parents('tr').prev('tr');
		current_tr.remove();
		prev_element.before(current_tr);

		setNotActiveClass($(this).parents('.sortable-arrows'));
		setNotActiveClass(prev_element.find('.sortable-arrows'));
		if ($('.item_name').length < 20) {
			$('textarea.resizable:visible', '#listing_table').elastic({
				callback:resizeItem,
				minHeight: 52,
			});
		}

var current_name = [];
		current_tr.find("input,select,textarea").each(function(){
		current_name.push($(this).attr('name'));
		
		});

		
var next_name = [];
		prev_element.find("input,select,textarea").each(function(){
		next_name.push($(this).attr('name'));
		
		});		
	// next_name.forEach(function( i,k ) {
	// 	console.log(current_name[k]);
	// 		$('#listing_table').find('*[name="'+current_name[k]+'"]').prop('name','##'+i);
	// 		$('#listing_table').find('*[name="'+i+'"]').prop('name',current_name[k]);
	// 		$('#listing_table').find('*[name="'+'##'+i+'"]').prop('name',i);
	// 		//$('#listing_table').html($('#listing_table').html().replace(current_name[k],'##'+i));
	//
	// 	});
		
		recalcauto();
		e.preventDefault();
		ReNumberAll();
	});
	// ----------------------
	$('.move-up-notactive,.move-down-notactive').live('click',function(e){
		e.preventDefault();
		recalcauto();
	});
	// -----------------------
	$('tr.itemRow td.td_editable').live('click',function(){
		if (!$(this).hasClass('select-item')){
			$(this).removeClass('edit-itemhover');
			$('.edit-item').removeClass('edit-item');
			$(this).addClass('edit-item');
			// Edited By Omar 2019/11/12 :)
			if (!$(this).find('.editable-input').hasClass('no-focus')) {
				$(this).find('.editable-input').focus();
			}
		}
	});
	// -----------------------
	$('td .editable-input').live('focus',function(){
		$(this).parents('td').removeClass('edit-itemhover');
		$(this).parents('td').addClass('edit-item');
	}).live('blur', function(){
		$(this).parents('td').removeClass('edit-itemhover');
		$(this).parents('td').removeClass('edit-item');
	});
	// -----------------------
	
	// -----------------------
	$('td.td_editable').live('mouseover',function(){
		$(this).addClass('edit-itemhover');
	}).live('mouseout',function(){
		$(this).removeClass('edit-itemhover');
	});
	// -----------------------
	
	
	function setNotActiveClass(element){
		var upNotActive = element.find('.move-up-notactive');
		if(upNotActive.length){
			upNotActive.removeClass('move-up-notactive').addClass('move-up');
		}

		var downNotActive = element.find('.move-down-notactive');
		if(downNotActive.length){
			downNotActive.removeClass('move-down-notactive').addClass('move-down');
		}

		var parent = element.parents('tr');
		if(!parent.prev('tr.movable').length){
			element.find('.move-up').removeClass('move-up').addClass('move-up-notactive');
		}

		if(!parent.next('tr.movable').length){
			element.find('.move-down').removeClass('move-down').addClass('move-down-notactive');
		}
	}
	// -------------------------
	function setFirstLastNotActive(container){
		$('.movable:first .ArrawUp',container).removeClass('move-up').removeClass('move-up-notactive').addClass('move-up-notactive');
		$('.movable:last .ArrawDown',container).removeClass('move-down').removeClass('move-down-notactive').addClass('move-down-notactive');
	}
	// -------------------------
	
	//---------------------------
	//---------------------------

	
	$('#InvoiceIsOffline').bind('change',function(){
		switchMethod();
	});
	
	switchMethod();
	//---------------------------
	//---------------------------
	
	$('.save').click(function(evt){

		const draftId = getUrlParams()["draftId"];
		
		if(Sending){
			return;
		}


        if(window['disable_invoice_edit']=="1") {
            $('.itemRow', '#listing_table').each(function () {
                if ($('.item-id', this).val() == '') {
                    $('.org_name', this).val('');
                    $('.item_name', this).val('');
                }
            });
        }

		if($(this).hasClass('disable-submit')){
			return;
		}

		evt.preventDefault();
		$('.actions-btns .sub-actions').hide();
		if ($('#ClientData', '#InvoiceForm').length == 0){
			$('#InvoiceForm').append($('#ClientData'));
		}
		RemoveEmptyItemRow();
		if (validateFields()){
                    
			var action = $('#InvoiceForm').attr('action');
                        if(action.indexOf('?') == -1)
                        {
                            action += '?';
                        }else{
                            action += '&';
                        }
			if ($(this).hasClass('direct')){
				action += 'send=direct'
			} else if ($(this).hasClass('revised')) {
				action += 'send=revised'
			}
			else if ($(this).hasClass('draft')) {
				action += 'send=draft'
			}
			else if ($(this).hasClass('print')) {
				action += 'send=print'
			}
			else if ($(this).hasClass('silent')) {
				action += 'send=silent'
			}
			if(draftId){
				action += `&draftId=${draftId}`
			}
			
			$('#InvoiceForm').attr('action', action);

			$(this).attr('disabled', 'disabled');
			$(this).addClass('disable-submit');
			//clearInterval(invoiceto_local_interval);
			// localStorage.removeItem('myinvoice');
			Sending=true;
			$('#InvoiceForm').submit();
		}
		else
		{ 
			$(this).removeAttr('disabled');
			// Close I frame Preview
			closepreview();

			if($('.more-options-box .error-message').length>0)
			{
				if($('.more-options-box .collapse.in').length==0) $('button[href="#extra-settings"]').click();
				if (!$('a[href=#'+$('.more-options-box .error-message').parents('.tab-pane').attr('id')+']').parent().hasClass('active')) {
					$('a[href=#'+$('.more-options-box .error-message').parents('.tab-pane').attr('id')+']').click(); 
				}
			}

			if (!$('.form-error:first').attr('disabled')) {
				$('.form-error:first').click();
			}

			if($('.error-message:first').length>0&&$('.error-message:first').is(':visible'))
			{
			
			 $('html,body').animate({
				scrollTop: $('.error-message:first').offset().top-100
			}, 600);
			}
			else
			{
				$('html,body').animate({
				scrollTop: 0
			}, 600);
			}
		}
		return false;
	});


	
	$('.item-unit-price, .multi_units_price, .item-qty', '#listing_table').live('change', calculateTotals);
	$('#InvoiceDiscount,#InvoiceDiscountAmount,#InvoiceAdjustmentValue,#InvoiceAdjustmentLabel,#InvoiceDeposit,#InvoiceDepositType,#InvoiceShippingAmount,#discount_type,.invoice_account').bind('change', calculateTotals);

	calculateTotals();
	

	$('#AddNewClient').bind('click', function(evt){
		ajaxUrl='owner/clients/add';
		$(':input', '#ClientData').not(':checkbox,:radio').each(function(){
			$(this).val($(this).data('default') || '');
		});
		$(':checkbox', '#ClientData').attr('checked', false);
		$('.error-meessage', '#ClientData').remove();
		$('#UpdateClientDataBox').hide();
		$('#ClientSendDetails').show();
		$('#ClientData').show();
		
		$('.ClientSelect').hide();
		$('#InvoiceClientBusinessName').focus();
		$('h3', '#ClientData').text(__('Client Details'));
		if (!addClientLimit.status){
			$('#NewClientMessage', '#ClientData').remove();
			$('h3', '#ClientData').after('<div class="Errormessage" style="width: 290px" id="NewClientMessage">' + addClientLimit.message + '</div>');
		}

		$("#clientAdvancedBtn").show();
		// hide client addresses section
		$("#StaticClientInfoHide").hide();
		evt.preventDefault();
	});

	$('#EditCurrentClient').bind('click', function(evt){
		ajaxUrl='/owner/clients/edit';
		var clientID = $('#InvoiceClientId').val();
		$('#NewClientMessage', '#ClientData').remove();
		if (clientID){
                    
			$('#ClientID', '#ClientData').val(clientID);
			$('#UpdateClientDataBox').hide();
			//$('.error-meessage', '#ClientData').remove();
			$('#ClientData').show();
			$('.ClientSelect').hide();
			

			$('#ClientFirstName').focus();
			//loadClientData();

			$('h3', '#ClientData').text(__('Client Details'));
			$('#ClientSendDetails').hide();
 
			if (!$('#ClientCountry').val() && typeof country_code !== 'undefined' && country_code) {
				$('#ClientCountry').val(country_code);
			} 
		}

		evt.preventDefault();
	});

	$('#CancelClient').click(function(){
		
		$('#ClientData').hide();
		$('.ClientSelect').show();
		loadClientData();

		// show client addresses section
		$("#StaticClientInfoHide").show()

		return false;
	});

	

	

	loadClientData();

	$('#InvoiceClientId').bind('change', loadClientData);
	$('#SaveClient').bind('click', function(){
	if($('#SaveClient').find("span").hasClass( "disabled" )){
	return false;
	}
		 ajaxUrl = 'owner/clients/add';
		var client_id = string2number($('#ClientID').val());
		pushCID(client_id);
		//		var clientSaved = false;
		if (client_id != 0){
			ajaxUrl = 'owner/clients/edit/' + client_id;
		}
		$('#NewClientMessage', '#ClientData').remove();
		if (/*document.getElementById('UpdateClientData').checked*/ true || client_id == 0){   
			var clientData = {
				Client: {}
			};
			if (client_id != 0){
				clientData.Client.id = client_id;
			}
			$(':input', '#ClientData').each(function(){
				var name = this.name.replace(/data\[Invoice\]\[client_(.*)\]/, '$1');
				if(name === '') {
					return;
				}
				if(name=='type' && this.checked==false){
					
				}else{
				clientData.Client[name] = this.value;
				}
			});


			clientData.Client['is_offline'] = $('#InvoiceIsOffline').val();
			if($('#InvoiceClientSendNotify').attr('checked')=='checked'){
				clientData.Client.send_notify=1;
			}
			else{
				clientData.Client.send_notify='';
			}
			
			if($('#InvoiceClientActiveSecondaryAddress').is(':checked')){
				clientData.Client.active_secondary_address=1;
			}
			else{

				clientData.Client.active_secondary_address=0;
			}
			
			$('#ClientSaveLoader').show();
			$('#SaveClient').find("span").addClass( "disabled" );
			$.ajax({
				url: SITE_ROOT + ajaxUrl,
				type: 'post',
				dataType: 'json',
				data: {
					data: clientData
				},
				success: function(data){
				$('#SaveClient').find("span").removeClass( "disabled" );
					$('#ClientSaveLoader').hide();
					$('.error-message', '#ClientData').remove();
					$('#NewClientMessage', '#ClientData').remove();

					// check limitation
					if (data.status !== undefined) {
						$('#ClientData h3').after(`<div class="alert alert-danger">
								${data.message}
								<a class="btn btn-success btn-addon m-l" target="_blank" href="${data.upgrade_url}" style="margin-top: 0">
									<i class="fa fa-chevron-circle-up" aria-hidden="true"></i>
									${data.upgrade_btn_title}
								</a>
							</div>`);
						return;
					}

					// show client addresses section
                    if (typeof clientAddressesDiv != 'undefined') {
                        clientAddressesDiv.empty();
                    }
					$("#StaticClientInfoHide").show()

					if (data.error){
						if (data.message != undefined){
							$('#ClientData h3').after('<div id="NewClientMessage" style="width: 290px" class="' + data['class'] + '">' + data.message + '</div>');
						}

						if (data.errors != undefined){
						
							for(field in data.errors){
								if((field=='email' || field=='phone1') && data.fmsg!=undefined  && data.fmsg!=''){
									$('.new-client-error').remove();
								$('#ClientData h3').after('<div id="NewClientMessage" style="" class="new-client-error alert alert-danger">' + data.fmsg + '</div>');										
								}
								var fieldName = 'data[Invoice][client_' + field + ']';


								
								var inputField = $(':input[name="'+ fieldName + '"]');
								if (inputField.siblings('.error-message').length < 1){
									inputField.parent().append('<div class="error-message">' + data.errors[field] + '</div>');
								}
							}
						}
					} else {
						client_faddress=data.client.full_address;
						$('#InvoiceClientId').val(data.client.id);

						loadClientsList(data.client.id);
						$('#ClientFromData').val(1);
						$('#ClientData').hide();
						$('.ClientSelect').show();
					}
				},
				error: function(){
					
				$('#ClientSaveLoader').hide();
				// TODO: implement user fail here.
				}
			});
		} else {
			$('#ClientData').hide();
			$('.ClientSelect').show();
		}
		
		if (client_id != 0){
			if(ajaxUrl!='owner/clients/add'){
			generateStaticClientData();
			}
		}
		
		return false;
	});
	// -------------------------
	$('.has-calendar').datepicker();

	$.datepicker.setDefaults({
		dateFormat: jsDateFormat
	});
	// -------------------------
	if ($('#InvoiceDateFormat').length){
		$('#InvoiceDateFormat').bind('change', function(){
			var newJsDateFormat = jsDateFormats[string2number(this.value)];
			$.datepicker.setDefaults({
				dateFormat: newJsDateFormat
			});
			$('.has-calendar').each(function(){
				if (this.value != ''){
					try{
						var thisDate = $.datepicker.parseDate(jsDateFormat, this.value);
						if (thisDate){
							this.value = $.datepicker.formatDate(newJsDateFormat, thisDate).toString();
						}
					} catch (e){
					}
				}
			});
			jsDateFormat = newJsDateFormat;
		}).trigger('change');
	}
	// -------------------------
	$('.reminder-sendwhen', '#RemindersList').live('change', function(){
		var value = string2number(this.value);
		if (value == 3 || value == 4){
			$(this).siblings('div.sub-item').show();
		} else {
			$(this).siblings('div.sub-item').hide();
		}
	}).trigger('change');

	
	// -------------------------

	function getOffsetFromBody(element) {
		var bodyRect = document.body.getBoundingClientRect(),
			elemRect = element[0].getBoundingClientRect();
		return {
			top: elemRect.top - bodyRect.top,
			left: elemRect.left - bodyRect.left
		};
	}

	$('.item-arrow', '#listing_table').live('click', function(evt){
		
		window.lastFocusedItemNameInput = $(this).parent().find('textarea').get(0);
		var list = $('#InvoiceProducts');
		var visible = list.data('visible');
		var parent = $(this).parents('td');

		var pos = getOffsetFromBody(parent);
		var poww=list.outerWidth();
		var powww=$('.td_editable').outerWidth();
		
		var height = parent.height();
		var pad = parseInt(parent.css('padding-top')) + parseInt(parent.css('padding-bottom'));
		if(SITE_LANG=='ara'){
		list.css({
			top: pos.top + height + pad,
			left: pos.left-poww+powww
		});
		}else{
		list.css({
			top: pos.top + height + pad,
			left: pos.left
		});			
		}
		
		
		if (!visible){
			list.data('visible', true).show();
		} else {
			list.data('visible', false).hide();
		}
		$('#search_product').val('').focus();
		list.data('element', $(this).parents('tr').get(0));
		var store_id = $('#RequisitionStoreId option:selected').val();
		if($('#InvoiceProducts').data('visible')==true){
			list_products('', store_id);
		}
		return false;
	});
	// -------------------------
	//	$('.select-arrow img').live('click', function(evt){
	//		$(this).parent().click();
	//		evt.preventDefault();
	//	});

	// -------------------------
	$('.item_name').live('click', function(){
		if($(this).attr('readonly')){
		$('.item-arrow', $(this).closest('div')).click();
		$('#search_product').focus();
		}
	});
	$('.item_name').live('change', function(e){reviewIds()});
	function moveBetweenProductsUsingKeyboard(e) {
		if ((e.keyCode == 38 && e.ctrlKey == false) ||
			(e.keyCode == 40 && e.ctrlKey == false) ||
			(e.keyCode == 13 && e.ctrlKey == false) ||
			(e.keyCode == 27 && e.ctrlKey == false)) {
			e.preventDefault();
			// find active focused product
			var $activeFocusedProduct = null;
			$('.products-list .products-list-ul li').each(function(i, element) {
				if ($(element).hasClass('active')) {
					$activeFocusedProduct = $(element);
					return false;
				}
			});
			// if no focused product focus first or last one
			if ($activeFocusedProduct == null && (e.keyCode == 38 || e.keyCode == 40)) {
				$('.products-list .products-list-ul li').first().addClass('active');
			}
			// if there is focused product .. move between products
			if ($activeFocusedProduct && (e.keyCode == 38 || e.keyCode == 40)) {
				$('.products-list .products-list-ul li').each(function(i, element) {
					if ($(element)[0] == $activeFocusedProduct[0]) {
                        var prevProduct = $('.products-list .products-list-ul li')[(i >= 1 ? i : 0) - 1];
                        var nextProduct = $('.products-list .products-list-ul li')[i + 1];
						$('.products-list .products-list-ul li').removeClass('active');
                        if (e.keyCode == 38) {
                            if (prevProduct) {
                                $('.products-list .products-list-ul').scrollTop(($(prevProduct).offset().top- $('.products-list-ul').offset().top + $('.products-list-ul').scrollTop() - 10));
                                $(prevProduct).addClass('active');
                                return false;
                            } else {
                                $('.products-list .products-list-ul').scrollTop(0);
								$('.products-list .products-list-ul li').first().addClass('active');
                                return false;
							}
                        } else {
                            if (nextProduct) {
                                $('.products-list .products-list-ul').scrollTop(($(nextProduct).offset().top- $('.products-list-ul').offset().top + $('.products-list-ul').scrollTop() - 10));
                                $(nextProduct).addClass('active');
                                return false;
                            } else {
                                $('.products-list .products-list-ul').scrollTop(0);
								$('.products-list .products-list-ul li').first().addClass('active');
                                return false;
							}
                        }
					}
				});
			}
			// select focused product
			if ($activeFocusedProduct && e.keyCode == 13) {
				$activeFocusedProduct.find('a').click();
				return false;
			}
			// hide products window
			if (e.keyCode == 27) {
				$('#InvoiceProducts').hide().data('visible', false);
			}
			return false;
		}
		return true;
	}
	$('#search_product').live('keydown', function(e){
		var continueLogic = !((e.keyCode == 38 && e.ctrlKey == false) ||
							  (e.keyCode == 40 && e.ctrlKey == false) ||
							  (e.keyCode == 13 && e.ctrlKey == false) ||
							  (e.keyCode == 27 && e.ctrlKey == false));
		if (!continueLogic) {
			return false;
		}
	});
	$('.item_name', '#listing_table').live('keydown', function(e){
		var continueLogic = !((e.keyCode == 38 && e.ctrlKey == false) ||
							  (e.keyCode == 40 && e.ctrlKey == false) ||
							  (e.keyCode == 13 && e.ctrlKey == false) ||
							  (e.keyCode == 27 && e.ctrlKey == false));
		if (!continueLogic) {
			return false;
		}
	});
	$('.item_name', '#listing_table').live('keyup', function(e){
		$('.item_name').removeClass('product-list-active');
		$(this).addClass('product-list-active');
		var continueLogic = moveBetweenProductsUsingKeyboard(e);
		if (!continueLogic) {
			return false;
		}
		if (aps == '1') {
			var father = $('#InvoiceProducts').data('element');
			var legacy_store_id = ($('.item-store', father).val() ? $('.item-store', father).val() : $("#InvoiceStoreId").val());
			var store_id = $('#RequisitionStoreId option:selected').val();
			list_products($(this).val(), legacy_store_id || store_id);
		}
		

		window.lastFocusedItemNameInput = this;
		var list = $('#InvoiceProducts');
		var visible = list.data('visible');
		var parent = $(this).parents('td');

		var pos = getOffsetFromBody(parent);
		var poww=list.outerWidth();
		if ($(this).attr('readonly')) {
			$('#search_product').val('').focus();
		}
		var powww=$('.td_editable').outerWidth();
		
		var height = parent.height();
		var pad = parseInt(parent.css('padding-top')) + parseInt(parent.css('padding-bottom'));
			if(SITE_LANG=='ara'){
		list.css({
			top: pos.top + height + pad,
			left: pos.left-poww+powww
		});
	}else{
		list.css({
			top: pos.top + height + pad,
			left: pos.left
		});
		
	}

			list.data('visible', true).show();
		list.data('element', $(this).parents('tr').get(0));
		return false;
		var list = $('#InvoiceProducts');
		var bind = list.is(':visible');
		if (bind){
			var thisValue = this.value.toLowerCase();
			if (this.value != ''){
				$('li', list).each(function(){
					if ($('a', this).text().toLowerCase().indexOf(thisValue) != -1){
						$(this).show();
					} else {
						$(this).hide();
					}
				});
			} else{
				$('li', list).show();
			}
		}
		
	});
	// -------------------------
	$('.delete-field','#InvoiceCustomFields').live('click',function(){
		if(!confirm(__("Are you sure ?"))){
		
			return false;
		}
		$(".MoreField[rel='"+ $(this).siblings('.custom-place-holder').attr('value')+"']").show();
	
		//TODO: Check if a real custom value exists
		placeHolder=$(this).parents('tr').find('input:hidden').val();
		$(this).parents('tr').remove();
		if (this.rel != ''){
			$('a[href=' + this.rel + ']','#MoreFieldsList').parent().show();
		}
		setFirstLastNotActive('#InvoiceCustomFields');
		return false;
	});
	// -------------------------

	function getProductPrice(productId, productMinPrice, clientId, unitId, callback) {
		if(typeof clientId == 'undefined' ||  '' == clientId){
			clientId = null;
		}
		if(typeof unitId == 'undefined' ||  '' == unitId){
			unitId = null;
		}
		return $.ajax({
			url: SITE_ROOT + '/owner/invoices/product_price?product_id=' + productId + '&client_id=' + clientId + '&unit_factor_id=' + unitId,
			dataType: 'json',
			cache: false,
			success: (function(productMinPrice) {
				return function(data) {
					if (data.status) {
						var lastSellingPrice = data[0] ? data[0]['calculated_price'] : '-';
						var productMinPriceText = productMinPrice 	? productMinPrice : '-';
						var productPriceText =  __('Min Price', true)  + ': ' + productMinPriceText + ', ' + __('Last Selling Price', true) + ': ' + lastSellingPrice;
						data.message = "<div class='bg-warning fs-12 p-1 product-price-text s2020 text-white-s2020' style='margin-bottom: 8px; padding: 11px; width: 230px'> " + productPriceText + "</div>";
						callback(data, productMinPrice);
					}
				};
			})(productMinPrice),
			error: function () {
				console.log('Error Product Price List')
			}
		});
	}

	$('.unit-factor-id').live('change', function () {
		var product = $(this).parents('td'),
			unitFactorId = $(this).val();
		var productMinPrice = product.data('product-minimum-price'),
			productId = product.data('product-id');
		var clientId = $('#ClientID').val();

		var unitFactor = $(this).parent().find('.unit-factor').val();
		productMinPrice = parseFloat(unitFactor) * parseFloat(productMinPrice);

		var canShowProductPrice = getcanShowProductPrice();
		if ('Invoice' == canShowProductPrice.modelName && 0 != canShowProductPrice.showProductPrice) {
			getProductPrice(productId, productMinPrice, clientId, unitFactorId, function (data) {
				if (data['status']) {
					product.parents('tr').find('.product-price-text').remove();
					product.parents('tr').find('.select-prod').append(data.message);
				}
			});
		}
	});

	function showProductPriceList(clientId = null) {
		var canShowProductPrice = getcanShowProductPrice();
		if ('Invoice' == canShowProductPrice.modelName && 0 != canShowProductPrice.showProductPrice) {
			$('#listing_table tr.itemRow').each(function () {
				var tr = $(this);
				var productId = tr.find('td.Quantity').data('product-id'),
					productMinPrice = tr.find('td.Quantity').data('product-minimum-price'),
					unitFactorId = tr.find('.unit-factor-id').val();
				if ('' != productId) {
					getProductPrice(productId, productMinPrice, clientId, unitFactorId, function (data) {
						if (data['status']) {
							tr.find('.product-price-text').remove();
							tr.find('.item-name').append(data.message);
						}
					});
				}
			});
		}
	}

	$('#InvoiceClientId').live('change', function () {
		var clientId = $(this).val();
		if (typeof canShowProductPrice != 'undefined' && 'Invoice' == canShowProductPrice.modelName && 2 == canShowProductPrice.showProductPrice) {
			showProductPriceList(clientId);
		}
		
		var url = window.location.href;
		if(url.includes("add_advance_payment")){
		   resetAdvancePaymentsForm();
		}
	});

	$(document).ready(function () {
		const urlParams = new URLSearchParams(window.location.search);
		var clientId = $('#InvoiceClientId').val();

		for (const [name, value] of urlParams) {
			if (('send' == name && 'silent' == value) || 'draftId' == name) {
				showProductPriceList(clientId);
				break;
			}
		}
	});

	function getcanShowProductPrice(){
		return {
			'modelName': $('#showProductPriceList').data('model-name'),
			'showProductPrice': $('#showProductPriceList').data('can-show-product-price'),
		};
	}

	$('a.product-link', '#InvoiceProducts').live('click', function(evt){
		
		var father = $('#InvoiceProducts').data('element');
		
		var productID = string2number($(this).attr('href').replace(/\D/g, ''));

		if (productID && typeof jsProducts[productID] != 'undefined'){
			var product = selectedProducts[productID] = jsProducts[productID];
			if ( product.track_stock != "1"){//Is a service
                            $(".item-store",father).hide();
                        }
			if(columns['col_1']=='auto'){
			$('.item_name', father).val('auto').change(); 	
			}else if(columns['col_1']=='product_image'){
			$('.item_name', father).val(product['name']).change(); 	
			}else{
			$('.item_name', father).val(product[columns['col_1']]).change(); 		
			}
			$('.item-discount', father).val('').change();
			$('.item-discount-type', father).val($(".item-discount-type option:first", father).val()).change();

			if(!empty(product.discout_type)) $('.item-discount-type', father).val(product.discout_type);
			$('.item-discount', father).val(product.discount).keyup();

			$('.org_name', father).val($('.item_name', father).val());
			$(':input[id$=ID]', father).val(product.id).change();
			$('.item-description', father).val(product[columns['col_2']]).trigger('keyup');
                        for(iv=3;iv<=5;iv++){
                        if(!empty(columns['col_'+iv])) $('.item_col_'+iv, father).val(product[columns['col_'+iv]]).trigger('keyup');
                        }						
						group_price_id = (group_price_id.length == 0) ? default_group_price_id : group_price_id;
                        if(group_price_id!=0){
                        var found_price=false;   
						if(!empty(product.prices))
                         $.each(product.prices, function( index, value ) {
                      
                       if(parseInt(value.group_price_id)==group_price_id){
                         $('.item-unit-price', father).val(value.price).change();          
                         found_price=true;
                          return false; 
                       }
                        });   
                         if(found_price==false){
                        $('.item-unit-price', father).val(product.unit_price).change();                         
                         }
                        }else{
                        $('.item-unit-price', father).val(product.unit_price).change();    
                        }
                        lock_product ( product.unit_price , father );
			if (typeof handleProductTracking == 'function') {
				handleProductTracking(product.id, father);
			}
			
			$('.item-tax1', father).val(product.tax1).change();
			$('.item-tax2', father).val(product.tax2).change();
			if(empty(product.default_quantity)) product.default_quantity=1;
			$('.item-qty', father).val(product.default_quantity).change();
			
			if (typeof trkInv != 'undefined'&&trkInv==1)
			afterProdSelect(productID,father);
				if(isAdvancePayment){
					$('.item_name', father).attr('readonly','readonly');
				}
                        if (typeof enable_multi_units !== 'undefined'&&enable_multi_units==1){
							if (product.ignoreDefault && product.quantity) { console.log(product.unit_price);
								$(".item-unit-price" , father ).attr('data-price', product.unit_price);    
							}
                            multi_units( product , father)
                        }
			
				if (typeof enable_multi_units !== 'undefined'&&enable_multi_units==1 && typeof product.unit_factors !=='undefined' && product.unit_factors.length >=1){
					$('.unit-quantity',father).focus().select();
				} else {
					$('.item-qty',father).focus().select();
				}
			calculateTotals();
		recalcauto();
		}

		var childElem = $('.Quantity', father);
		childElem.get(0).setAttribute("data-product-id", product['id']);
		childElem.get(0).setAttribute("data-product-minimum-price", product['minimum_price']);
		var unitId = product.factor ? product.factor.id : null,
			clientId = $('#ClientID').val();

		var canShowProductPrice = getcanShowProductPrice();
		if ('Invoice' == canShowProductPrice.modelName && 0 != canShowProductPrice.showProductPrice) {
			getProductPrice(product['id'], product['minimum_price'], clientId, unitId, function (data) {
				$('.select-prod', father).find('.item-name').next('div').remove();
				$('.select-prod', father).append(data.message);
			});
		}

		$(this).parents('.products-list').hide();
		
		        AddNewROw(true);
		return false;
	});
$('#search_product').live('click', function(evt){
return false;
});
if(aps=='1')
{
	$('#search_product').live('keyup', function (evt) {
		var continueLogic = moveBetweenProductsUsingKeyboard(evt);
		if (!continueLogic) {
			return false;
		}
		var keyword = $('#search_product').val();
		var father = $('#InvoiceProducts').data('element');
		var legacy_store_id = (($('.item-store', father).val() && $("#show_item_stores").prop('checked')) ? $('.item-store', father).val() : $("#InvoiceStoreId").val());
		var requisition_store_id = $('#RequisitionStoreId option:selected').val();
		list_products(keyword, legacy_store_id || requisition_store_id);
	});
}

	$('#AddField').bind('click', function(){
		$('.MoreField', '#MoreFieldsList').click();
		return false;
	});
	// -------------------------
	$('.MoreField', '#MoreFieldsList').click(function(evt){
	
		evt.preventDefault();
		lastFieldIndex = 0;
		$('.field-row', '#InvoiceCustomFields').each(function(){
			var idx = string2number($(':input:first', this).attr('id').replace(/\D/g, ''));
			if (idx > lastFieldIndex){
				lastFieldIndex = idx;
			}
		});

		if (lastFieldIndex == 0 && $('.field-row', '#InvoiceCustomFields').length == 0){
			lastFieldIndex = 0;
		} else {
			lastFieldIndex++;
		}

		$('#InvoiceCustomFields').append(baseFieldRow);
		var lastFieldRow = $('.field-row:last','#InvoiceCustomFields')[0];
		var lastRow = $('.field-row:last','#InvoiceCustomFields');
		$(':input', lastFieldRow).each(function(){
			this.id = this.id.replace('0', lastFieldIndex);
			this.name = this.name.replace('0', lastFieldIndex);
			this.value = '';
		});

		if (this.rel != ''){
			$('.custom-value', lastFieldRow)
			.attr('readonly', true)
			.val(this.rel)
			.addClass(this.rel.replace(/%/g, '') + '-value')
			.addClass('form-x4 read-only-field')
			.parent('.text');

			$('[name$="[placeholder]"]', lastFieldRow).val(this.rel);
			$('[name$="[placeholder]"]', lastFieldRow).addClass('custom-place-holder');
			$('.custom-label', lastFieldRow)
			.addClass(' read-only-field')
			.attr('readonly', true)
			.parent('.text').removeClass('text');
		}

		if (!$(this).hasClass('other')){
			$(this).parent().hide();
			$('.custom-label', lastFieldRow).val($(this).text());
		}

		$('.delete-field', lastFieldRow).attr('rel', $(this).attr('href'));
		$('.custom-label', lastFieldRow).focus();
		beforelastRow=lastRow.prev();

		$('.move-table .sortable-arrows .ArrawDown', beforelastRow).removeClass('move-down-notactive').removeClass('move-down').addClass('move-down');
		setFirstLastNotActive('#InvoiceCustomFields');

		calculateTotals();
		calculateDates();
		$('#InvoiceStatus').change();
		$('#InvoiceDeposit').change();
		$('#InvoiceDueDays').change();
		$('#InvoiceDueAfter').change();
                $('.custom-label:last').focus();
	});
	// -------------------------
	$('.edit-email-template', '#RemindersList').live('click', function(){
		var value = $(this).siblings('select.reminder-email-template').val();
		if (value){
			frame = $('<iframe>', {
				src: SITE_ROOT + '/owner/email_templates/edit/' + value +'?box=1',
				border: 0,
				id: 'lightBoxFrame',
				style: 'border: 0; width:800px; height: 600px'
			});
			
				$('#myModalGeneralSize').addClass("modal-dialog modal-lg");
				$('#myModalGeneral_iframe').attr('style','border: 0;  height: 600px');
				$('#myModalGeneralheader').hide();
				$('#myModalGeneral_iframe').attr('src', 'about:blank');
				$('#myModalGeneral_iframe').attr('src', SITE_ROOT + '/owner/email_templates/edit/' + value +'?box=1&from_invoice=1');
				$('#myModalGeneral').modal('show');					
			
			//$('<div>').append(frame).lightbox_me();
		}

		return false;
	});
	// -------------------------
	$('#TermsSelector').bind('change', function(){
		var value = string2number(this.value);
		if (value){
			var loader = $(this).siblings('.inline-loader');
			loader.show();
			$.ajax({
				url: SITE_ROOT + '/owner/terms/load/' + value,
				dataType: 'json',
				cache: false,
				success: function(data){
					if (data.error){
						this.error();
					} else {
						$('#InvoiceTerms').val(data.text);
						loader.hide();
					}
				},
				error: function(){
					//TODO: implement error function if terms failed to load
					$('#InvoiceTerms').val('');
					loader.hide();
				}
			});
		} else {
			$('#InvoiceTerms').val('');
		}
	});
	// -------------------------
	$('#InvoiceDueAfter,#InvoiceDate,#InvoiceIssueDate').bind('change', calculateDates);
	// -------------------------
	$('#InvoiceDueAfter').bind('change', function(){
		$('.due-days-value').val(string2number(this.value));
	});
	// -------------------------
	$('[name$="[placeholder]"]', '#InvoiceCustomFields').each(function(){
		if(this.value!="") $('[rel="'+ this.value.replace('"','\\\"') + '"]', '#MoreFieldsList').parent().hide();
	});
	// -------------------------
	$('#InvoiceSaveAsTemplate').bind('change click', function(){
		if (this.checked){
			$('#TemplateNmae').show();
		}
		else {
			$('#TemplateNmae').hide();
			$(':input', '#TemplateName').val('');
		}
	});
	// -------------------------
	// -------------------------
	$('#payment_method,#transaction_id').hide();
	$('#PaymentIsPaid').bind('change click', function(){
		if (this.checked){
			$('#DepositIsPaid').attr('checked', false);
			$('#payment_method').show();
			$('#transaction_id').show();
			$('#treasury_id').show();
			$('.well.already-paid-well').show();
			$(this).closest('[data-invoice-multiple-payment-methods-checkbox-container]').find('[data-invoice-multiple-payment-methods-wrapper]').removeClass('d-none');
		}else {
			$('#payment_method,#transaction_id,#treasury_id').hide();
			$('#transaction_id').val('');
			$('#treasury_id').val('');
			$('.well.already-paid-well').hide();
			$(this).closest('[data-invoice-multiple-payment-methods-checkbox-container]').find('[data-invoice-multiple-payment-methods-wrapper]').addClass('d-none');
			resetInvoicePaymentsForm();
		}
		calculateTotals();
	});

	$('#PaymentAdvancePayments').bind('change click', function(){
		toggleAdvancePaymentsBox(this.checked);
		loadClientAdvancePayments( $('#InvoiceClientId').val() );
		calculateTotals();
	});
	// -------------------------
	
	// -------------------------
	$('.deposit_is_paid_dev,#deposit_payment_method,#deposit_transaction_id').hide();
	$('#DepositIsPaid').bind('change click', function(){
		
		
		if (this.checked){
			$('#PaymentIsPaid').attr('checked', false);
			if($('#InvoiceDeposit').val()==''||$('#InvoiceDeposit').val()=='0')
			{
				$(this).attr('checked', false);   
				alert(__('You need to enter deposit the amount first')); 
				return false;
			}
			$('.deposit_is_paid_dev').show();
			$('#deposit_payment_method').show();
			$('#deposit_transaction_id').show();
		}
		else {
			
			$('.deposit_is_paid_dev').hide();
			$('#deposit_payment_method,#deposit_transaction_id').hide();  
			$('#deposit_transaction_id').val('');
		}
		calculateTotals();  
	});
	// -------------------------
	
	$('#LoadInvoiceTemplate').click(function(){
		var val = $('#SelectInvoiceTemplate').val();
		if (val != ''){
			var action = '';
			if (typeof IS_SUBSCRIPTION != 'undefined' && IS_SUBSCRIPTION){
				action = 'add_subscription';
			} else if (typeof IS_TEMPLATE != 'undefined' && IS_TEMPLATE) {
				action = 'add_template';
			} else if (typeof IS_ESTIMATE != 'undefined' && IS_ESTIMATE) {
				action = 'add_estimate';
			} else {
				action = 'add';
			}
			window.location = SITE_ROOT + '/owner/invoices/' + action + '/' + val;
		} else {
			jAlert(__('Please select invoice template to load'), 'Invoice Template');
		}

		return false;
	});
	// -------------------------
	$('.html-invoice').bind('click', function(){
		let disabledDiscount = $('.item-discount:disabled');
		let disabledDiscountType = $('.item-discount-type:disabled');
		disabledDiscount.removeAttr('disabled');
		disabledDiscountType.removeAttr('disabled');
		
		$('.actions-btns .sub-actions').hide();
		var form = $('#InvoiceForm');
		var action = form.attr('action');
		var target = form.attr('target');
		var targetAction = 'preview';
		if (IS_ESTIMATE){
			targetAction = 'preview_estimate';
		}
if(IS_MOBILE==1){	
document.querySelector("meta[name=viewport]").setAttribute('content', 'width=device-width, initial-scale=0.5');	
ifram_css='calc(100vh - 80px)';
}else{
ifram_css='calc(100vh - 55px)';	
}		
$('.layout').hide();
$('.footer').hide();

$('body').append('<div style="margin:5px 10px auto 10px;"  id="actionbutton"></div><div class="clear"></div><iframe onload="hidepreviewloader()" id="iframe_preview" name="iframe_preview" src="" style="display: block;border: 0; width: 100%; height:'+ifram_css+';margin-top: 5px;">Your browser doesn\'t support iFrames.</iframe>');
//$('.button.select-actions.btn-group.m-r-md').appendTo("#actionbutton");
$('.in_preview').appendTo("#actionbutton");
$('.close_preview').show();
 $("html, body").animate({ scrollTop: 0 });
		form.attr({
			action: SITE_ROOT + '/owner/invoices/' + targetAction,
			target: 'iframe_preview'
		});

		form.submit();
$('#actionbutton').after('<div class="preview-notifcation-loader notifcation-loader"><div class="inner-loader"></div></div>');
		form.attr({
			'action': action,
			'target': '_self'
		});
		disabledDiscount.prop('disabled', true);
		disabledDiscountType.prop('disabled', true);
		return false;
	});
	// -------------------------
	$('.pdf-invoice').bind('click', function(){
		var disabledDiscount = $('.item-discount:disabled');
		var disabledDiscountType = $('.item-discount-type:disabled');
	    if(disabledDiscount.length && disabledDiscountType.length){
			disabledDiscount.removeAttr('disabled');
			disabledDiscountType.removeAttr('disabled');
		}

		$('.actions-btns .sub-actions').hide();
		var form = $('#InvoiceForm');
		var action = form.attr('action');
		var target = form.attr('target');
		
		var targetAction = 'preview.pdf';
		if (IS_ESTIMATE){
			targetAction = 'preview_estimate.pdf';
		}
if(IS_MOBILE==1){	
document.querySelector("meta[name=viewport]").setAttribute('content', 'width=device-width, initial-scale=0.5');	
ifram_css='calc(100vh - 80px)';
}else{
ifram_css='calc(100vh - 55px)';	
}		
$('.layout').hide();
$('.footer').hide();

$('body').append('<div style="margin:5px 10px auto 10px;"  id="actionbutton"></div><div class="clear"></div><iframe onload="hidepreviewloader()" id="iframe_preview" name="iframe_preview" src="" style="display: block;border: 0; width: 100%; height:'+ifram_css+';margin-top: 5px;">Your browser doesn\'t support iFrames.</iframe>');
//$('.button.select-actions.btn-group.m-r-md').appendTo("#actionbutton");
$('.in_preview').appendTo("#actionbutton");
$('.close_preview').show();
 $("html, body").animate({ scrollTop: 0 });
		form.attr({
			action: SITE_ROOT + '/owner/invoices/' + targetAction,
			target: 'iframe_preview'
		});

		
$('#actionbutton').after('<div class="preview-notifcation-loader notifcation-loader"><div class="inner-loader"></div></div>');
		
		
		
		form.submit();

		form.attr({
			'action': action,
			'target': '_self'
		});
		if(disabledDiscount.length && disabledDiscountType.length){
			disabledDiscount.prop('disabled', true);
			disabledDiscountType.prop('disabled', true);
		}
		return false;
	});

	$('#EditCurrency').click(function(){
		$('#CurrencyInput').modal();
		return false;
	});

	$('#SaveCurrency').click(function(){
        update_client_balance();
		var currency = $('#InvoiceCurrencyCode').val();
		if(currency==''){
		currency=currency_code;	

		}
		// reset advance payment form if selected currency no equals new currency
		if(currency !=  $("#ClientDefaultCurrency").data("default")){
			resetAdvancePaymentsForm();
			$('select.advance_payment_invoices').val('').selectpicker('refresh');

		}
            $("#ClientDefaultCurrency").data("default", currency);
			$('#HiddenCurrencyCode').val(currency);
            $('#ClientDefaultCurrency').val(currency);
		$('#EditCurrency').html('<u>' + currency + '</u>');
		//removeModal($('#CurrencyInput'));
		$('#CurrencyInput').modal('hide');
		cuCh=1;
		calculateTotals();
		resetAdvancePaymentsForm();
		loadClientAdvancePayments($('#InvoiceClientId').val())
		return false;
	});

	$('#EditLanguage').click(function(){
		$('#LanguageInput').lightbox_me();
		return false;
	});

	$('#SaveLanguage').click(function(){
		var languageText = $('option:selected', '#InvoiceLanguageId').text();
		var languageId= $('#InvoiceLanguageId').val();
		$('#HiddenLanguageId').val(languageId);
		$('#EditLanguage').html('<u>' + languageText + '</u>');
		removeModal($('#LanguageInput'));
		return false;
	});

	$('#EditTerms').click(function(){
		var termID = $('#TermsSelector').val();
		if (termID){
			var frame = $('<iframe />', {
				src: SITE_ROOT + '/owner/terms/edit/' + termID + '?box=1',
				border: 0,
				id: 'lightBoxFrame',
				style: 'border: 0; width: 700px; height: 500px'
			});
				$('#myModalGeneralSize').addClass("modal-dialog modal-lg");
				$('#myModalGeneral_iframe').attr('style','border: 0;height: 500px');
				$('#myModalGeneralheader').hide();
				$('#myModalGeneral_iframe').attr('src', 'about:blank');
				$('#myModalGeneral_iframe').attr('src', SITE_ROOT + '/owner/terms/edit/' + termID + '?box=1');
				$('#myModalGeneral').modal('show');
			
			//$('<div>').append(frame).lightbox_me();
		}
		return false;
	});

	$('.add-product-link', '#InvoiceProducts').live('click', function(){
		aps = '1';
				window.newProductInitialName = $('#search_product').val();
				if (!window.newProductInitialName.length && typeof lastFocusedItemNameInput != 'undefined' && lastFocusedItemNameInput && lastFocusedItemNameInput.value) {
					window.newProductInitialName = lastFocusedItemNameInput.value;
				}
		//$('<div>').append(frame).lightbox_me();
		var productModalTitle= 'Add new product';
		if($('html').attr('dir') == 'rtl'){
			productModalTitle = 'إضافة منتج جديد'
		}
		
				$('#myModalGeneral #myModalGeneralSize').addClass("modal-dialog modal-xl product-modal");
				$('#myModalGeneral #myModalGeneralSize').attr("style", "padding-top:50px !important; width:90%;max-width:1200px !important;");
				$('#myModalGeneral #myModalGeneralSize .modal-body').attr("style", "padding:0px;");
				$('#myModalGeneral_iframe').attr('style','border: 0; height: 570px');
				$('#myModalGeneralheader').hide();
				$('#myModalGeneral_iframe').attr('src', 'about:blank');
				$('#myModalGeneral_iframe').attr('src', SITE_ROOT + '/owner/products/add?box=1');
				$('#myModalGeneral_iframe').load(function() {
					$('#myModalGeneral_iframe').contents().find("head").append(`<style>#body_box{background-color: #e4ebf2 !important;padding-top:90px !important;overflow-x:hidden;}#body_box .pages-head-s2020{top:0;bottom:unset;background-color:#FFF; border-bottom: 1px solid #dedede; box-shadow: 0px 2px 5px 0px rgba(228,235,242,1);} .pages-head-s2020 .container{width:100% !important; } .pages-head-s2020 .btn.s2020.btn-secondary{margin-inline-end:10px;} .pages-head-s2020 .btn.s2020.btn-success{padding: 0px 35px;} .row-flex {flex-wrap:nowrap;} 
					.inside-form-box{
						background: none repeat scroll 0 0 #fff;
						border: 1px solid #dee5e7;
						padding: 15px;
						margin-bottom: 15px;} 
						 .inside-form-box .head-bar{
							margin: -15px -15px 15px;
    						padding: 15px;
						}</style>`);  
					$('#myModalGeneral_iframe').contents().find('body .pages-head-s2020 .row-flex .col-flex-sm-6').eq(0).html('<p style="color: #4e5381; font-size: 16px;padding-bottom: 0px;">' +productModalTitle+'</p>');

				});
				$('#myModalGeneral').modal('show');		
		return false;
	});

	$('#myModalGeneral_iframe').load(function() {
		if (window.newProductInitialName && $('#myModalGeneral_iframe').contents().find('body').find('#ProductName').length) {
			$('#myModalGeneral_iframe').contents().find('body').find('#ProductName').val(window.newProductInitialName);
		}
	});

	$('#myModalGeneral_iframe').load(function() {
		if (window.newProductInitialName && $('#myModalGeneral_iframe').contents().find('body').find('#ProductName').length) {
			$('#myModalGeneral_iframe').contents().find('body').find('#ProductName').val(window.newProductInitialName);
		}
	});
	$(document).on('click', '.warning-add-product-link', function(e){
		e.preventDefault();
		lastFocusedItemNameInput = $(this).closest('td').find('textarea').get(0);
		$('#InvoiceProducts').data('element', $(this).closest('tr').get(0));
		$('.add-product-link').trigger('click');
	});
	$('#InvoiceLayout').bind('change', function(event,manual){
        $('#InvoiceInvoiceLayoutId').val(this.value);
		var InvoiceEstimateId = $('#InvoiceEstimateId').val();
		if(manual!="manual"&&this.value!="0" && (!InvoiceEstimateId || manual == undefined)){
		$.ajax({url: "/invoices/get_layout_footer/"+this.value}).done(function(data) {
			if (typeof IS_CLONE == 'undefined' || !IS_CLONE) {
				$('#InvoiceHtmlNotes').summernote('reset');
		    }
			if(data!="") {
				$("#InvoiceHtmlNotes").summernote("code",data);
			}
		});
		}
		template_id=this.value;
		
		if(manual=="manual"){
		template_id=-99;	
		}
		
		
		var layoutFields = customFields[template_id];
		var layoutLables = CustomLables[template_id];
		
	
		$('.field-row').remove();
		$('.MoreField').each(function() {
		  $(this).parent().show();
		  $(this).show();
		});
		var html = '';
		var placeholder=false;
		var placeholderArray=[];
		
		for (var i in layoutFields){

			var placeholderClass = 'width-160 form-control';
			var placeholderHtml = '';
			var readonly = '';
                        
			if (typeof(layoutFields[i]['placeholder']) !== 'undefined' && layoutFields[i]['placeholder'] != '' && layoutFields[i]['placeholder'] != null && layoutFields[i]['placeholder'] !== null ){
				placeholderClass += ' ' + layoutFields[i]['placeholder'].replace(/%/g, '') + '-value read-only-field';
                                
				placeholderHtml = '<input id="InvoiceCustomField'+i+'Placeholder" type="hidden" name="data[InvoiceCustomField][' + i + '][placeholder]" class="custom-place-holder" value="' + layoutFields[i]['placeholder'] + '"/>';
				readonly=' readonly="readonly"'
            placeholder=true;
			
			
			}else{
            placeholder=false;    
            }

            if(placeholder && $(".custom-place-holder[value='"+layoutFields[i]['placeholder']+"']").length==0){
			if(placeholderArray.indexOf(layoutFields[i]['placeholder'])==-1){
            html += '<tr class="field-row">';
			html +='<td class="text-right" width=""><label for="CustomField' + i + '">' + layoutFields[i]['label'] + '</label><input tabindex="14" id="InvoiceCustomField'+i+'Label" type="hidden" name="data[InvoiceCustomField][' + i + '][label]" value="' + layoutFields[i]['label'] + '"/></td>';
			html +='<td><div class="text"><input id="CustomField' + i + '" name="data[InvoiceCustomField][' + i + '][value]" type="text" value="' + layoutFields[i]['value'] + '" class="' + placeholderClass + '"' + readonly + '/>' + placeholderHtml + '<a href="#" class="delete-field del-pos delete-ico btn btn-xs btn-danger"><i class="fa fa fa-remove wtf"></i></a></div></td>';
			html += '</tr>';
			}
                        }else if(placeholder==false){
                        html += '<tr class="field-row">';
			html +='<td class="text-right"  width="130"><strong><div data-for="InvoicesCustom"><div class="text"><input class="width-160 form-control text-right custom-label" tabindex="14" id="InvoiceCustomField'+i+'Label" type="text" name="data[InvoiceCustomField][' + i + '][label]" value="' + layoutFields[i]['label'] + '"/></div></div></strong></td>';
			html +='<td><div class="text"><input tabindex="14" id="CustomField' + i + '" name="data[InvoiceCustomField][' + i + '][value]" type="text" value="' + layoutFields[i]['value'] + '" class="' + placeholderClass + '"' + readonly + '/>' + placeholderHtml + '<a href="#" class="delete-field del-pos delete-ico btn btn-xs btn-danger"><i class="fa fa fa-remove ccc"></i></a></div></td>';
			html += '</tr>';    
                        }
						
			placeholderArray.push(layoutFields[i]['placeholder']);			
          $(".MoreField[rel='"+layoutFields[i]['placeholder']+"']").hide();
			
            }
            $('#InvoiceCustomFields').append(html);
            if(typeof layoutLables!= 'undefined' && layoutLables)
            $.each(layoutLables, function( index, value ) {
            if(value!=='null' && value!==''  && value!==null){
            $('#'+index).html(value);

            }
            });
                
                if(typeof ItemColumns !== 'undefined' && !empty(ItemColumns)) set_item_columns();
                
		calculateTotals();
		calculateDates();
		$('.order-source-value').val($('#InvoiceOrderSourceId').find('option:selected').text());
	});

	if ($('.field-row').length < 1){
		$('#InvoiceLayout').trigger('change',["manual"]);
	}

	$('.actions-btns .sub-actions').hide();

	$('.actions-btns .select-action-arrow').click(function(){
		var subActions = $(this).parents('.select-actions').find('.sub-actions');
		var visible = subActions.is(':visible');
		$('.sub-actions').hide();

		if (!visible){
			subActions.fadeIn();
		}
		return false;
	});
	calculateDates();
	
	if($('#InvoiceClientId option').length < 2)
	$('#AddNewClient').trigger('click');
	
	updateTaxColumnsView();
});

function changeDiscountInputWidth() {
	var $discountInput = $('.item-discount');
	var $discountTypeSelect = $('.item-discount-type');
	if ($discountTypeSelect.length && $discountInput.length) {
		if ($discountTypeSelect.val() == 1) {
			$discountInput.prop('style').removeProperty('width');
			$discountInput.prop('style').removeProperty('max-width');
		} else if ($discountTypeSelect.val() == 2) {
			$discountInput.css({width: 'auto', maxWidth: '80px'});
		}
	}
}

$(document).on("change blur keyup" , ".item-discount,.item-discount-type" , function ( ) {
	changeDiscountInputWidth();
    calculateTotals();
	calculatePayments();
})
$(document).on("change" , ".item-unit-price, .multi_units_price, .item-qty, #listing_table" , function ( ) {
	calculatePayments();
});
$(document).on("change",".item-store,#show_item_stores" ,function(){
    if ( $("#show_item_stores").prop("checked"))
	{
        var father = $('#InvoiceProducts').data('element');
        updateStock($(".item-qty",father));
	}
})
$(document).on("change","#InvoiceStoreId,#show_item_stores" ,function(){
    if ( !$("#show_item_stores").prop("checked"))
    {
    	$(".item-qty").each(function(){
            father = $(this).parents(".itemRow");
            updateStock($(".item-qty",father)[0]);
		})
    }
})
//Handle Tab

// New BarCode Item Insertion Logic !
$(document).ready(function () {
	// Start BarCode Detection !
	startBarcodeDetection();
});


function getTaxAmount(shipping_fees, taxObject) {
	if (taxObject.included === '1') {
		return (shipping_fees - (shipping_fees / ((taxObject.value / 100) + 1)));
	} else {
		return taxObject.value * shipping_fees / 100;
	}
}

function autoSaveDraft() {
	if ($("#listing_table .itemRow").length < 3) {
		return;
	}
	var draftId = getUrlParams()["draftId"];
	var storageName = getCurrentMedule();
	if (!location.href.includes("/edit") && totalValue) {
		var invoicesDrafts = JJLC.getItem(storageName);
		var url = window.location.href;
		if (draftId == null && storageName != null) {
			var nowDate = Date.now();
			if (invoicesDrafts == null) {
				// add new invoicesDrafts in localStorage
				try {
					JJLC.setItem(storageName, JSON.stringify({
						[nowDate]: {}
					}))
				}
				catch (err) {
					console.log(err.message);
				}
			}
			$('#InvoiceForm').append(`<input type="hidden" name="data[non_completed_id]" id="nonCompletedID" value="${nowDate}">`)
			window.history.replaceState({}, '', window.location.href + '?draftId=' + nowDate);
		}
	}
}

function debounce(func, wait) {
	var timeout;
	return function executedFunction(...args) {
		var later = () => {
			clearTimeout(timeout);
			func(...args);
		};
		clearTimeout(timeout);
		timeout = setTimeout(later, wait);
	}
}

const autoSaveDraftDebounced = debounce(autoSaveDraft, 1000);

/**
* Calculates totals for an invoice
*/
function calculateTotals() {
	updateTaxColumnsView();
	var callApplyInsurance = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
	var callApplyOffers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
	var invoiceData = {
		'Invoice': {
			'date': $("#InvoiceDate").val(),
			'client_id': $("#InvoiceClientId").val(),
		},
		'InvoiceItem': []
	};
	recalcauto();
	var tax1 = string2number($('#InvoiceTax1').val());
	var tax2 = string2number($('#InvoiceTax2').val());
	var discount = string2number($('#InvoiceDiscount').val());
	var discount_amount = string2number($('#InvoiceDiscountAmount').val());
	var sum_items_discount=0;
	var is_discount_amount=false;
	if(discount_amount!=0&&discount_amount!='')
	{
			 var allSub=0;
			 is_discount_amount=true;
			 $('.itemRow', '#listing_table').each(function(){
					var unitPrice = string2number($('.item-unit-price', this).val());
					var qty = string2number($('.item-qty', this).val());
					var sub = (qty * unitPrice);
					total_item_discount = calculate_item_discount(this,sub);
				 	sum_items_discount+=total_item_discount;
                    sub_with_discount = sub - total_item_discount ;
					allSub += sub_with_discount;
			 });

			 discount =  (discount_amount/allSub * 100);
	}
	allSub += round(allSub,2);
	var currency = $('#EditCurrency u').html() ? $('#EditCurrency u').html() : $('#ClientDefaultCurrency').val();
	var shipping = string2number($('#InvoiceShippingAmount').val());
	var refunds = string2number($('#InvoiceSummaryRefund').val());

	var subtotal = 0.0, discountValue = 0.0, total = 0.0;
	var taxIDs = [];
	var shippingTaxes = [];
	var total_item=$('.itemRow', '#listing_table').length;
	var current_item=0;
	var sum_items_discount=0;
	var total_item_discount=0;
	var last_shipping_tax_id;
	$('.itemRow', '#listing_table').each(function(){
		total_item_discount=0;
		sum_items_discount=0;
		current_item++;
		var tax1 = string2number($('.item-tax1', this).val());
		var tax2 = string2number($('.item-tax2', this).val());
        var unitPrice = string2number($('.item-unit-price', this).val());
        var qty = string2number($('.item-qty', this).val());
        invoiceData.InvoiceItem.push({
            'product_id': $('.item-id', this).val(),
            'unit_price': unitPrice,
            'quantity': qty
        });
		var itemSubNoTax=0;
		var itemSubtotal = autoRound(qty * unitPrice);
                total_item_discount = calculate_item_discount(this,itemSubtotal)
				sum_items_discount+=total_item_discount;
                itemSubtotal -= total_item_discount ; 
		$('.item-subtotal span', this).text(format_price(itemSubtotal,currency));
		$('.item-subtotal span', this).data('subtotal',itemSubtotal);
		subtotal = parseFloat(subtotal);
		itemSubtotal = parseFloat(itemSubtotal);
		subtotal = autoRound(subtotal + itemSubtotal);
		

		var itemDiscount = autoRound(itemSubtotal * discount / 100);
		itemDiscount = parseFloat(itemDiscount)
		discountValue = autoRound(discountValue+itemDiscount);

		if ($("#InvoiceAdjustmentLabel").length && adjustmentLabel !== $("#InvoiceAdjustmentLabel").val()) {
			adjustmentLabel = $("#InvoiceAdjustmentLabel").val();
		}

		if (typeof adjustmentValue !== 'undefined' && adjustmentValue !== "0.0") {
			adjustmentValue = parseFloat(string2number($('#InvoiceAdjustmentValue').val()));
		}

		discountValue+=sum_items_discount;


		itemSubtotal = autoRound(itemSubtotal-itemDiscount );

		itemSubNoTax=itemSubtotal ;
		var incTax=0;
		if(tax1&&jsTaxes[tax1].included=='1')
		{
			incTax+=string2number(jsTaxes[tax1].value);
		}
		if(tax2&&jsTaxes[tax2].included=='1')
		{
			incTax+=string2number(jsTaxes[tax2].value);
		}
		

		if(incTax>0)
		{
			var itemNoTaxVal=autoRound((incTax  / (string2number(incTax) + 100)) * itemSubtotal);
			itemSubNoTax= autoRound(itemSubNoTax-itemNoTaxVal);
		}
	

		var itemTax1 = 0, itemTax2 = 0;
		
		if (tax1){
			var t1 = jsTaxes[tax1];
			itemTax1 = itemSubNoTax * t1.value / 100;
			itemTax1 = parseFloat(itemTax1);
			if (taxIDs[tax1] == undefined){
				taxIDs[tax1] = 0;
			}
			taxIDs[tax1] += itemTax1;
		}
		if (tax2){
			var t2 = jsTaxes[tax2];
			itemTax2 = itemSubNoTax * t2.value / 100;
			itemTax2 = parseFloat(itemTax2);
			if (taxIDs[tax2] == undefined){
				taxIDs[tax2] = 0;
			}
			taxIDs[tax2] += itemTax2;
		}

		// Prepare Shipping Tax
		var shipping_tax_id = $('#shipping_tax_id').children("option:selected").val();
		var shipping_fees =  $('#InvoiceShippingAmount').val();
		shipping_fees = parseFloat(shipping_fees);
		if (!empty(shipping_tax_id) && !empty(shipping_fees)) {
			shippingTaxes[shipping_tax_id] = getTaxAmount(shipping_fees, jsTaxes[shipping_tax_id]);
		}

		if (tax1&&t1.included!='1') {
			itemSubtotal = (parseFloat(itemSubtotal) + parseFloat(itemTax1));
		} else if (tax1&&t1.included=='1') {
			subtotal = (parseFloat(subtotal) -  parseFloat(itemTax1));
		}

		if (tax2&&t2.included!='1') {
			itemSubtotal = (parseFloat(itemSubtotal) + parseFloat(itemTax2));
		} else if (tax2&&t2.included=='1') {
			subtotal = (parseFloat(subtotal) - parseFloat(itemTax2));
		}
		

		// Substract Old Shipping Tax If It's not Included then it was added to the total in the next step.
		if (!empty(shipping_fees) && !empty(last_shipping_tax_id) && (jsTaxes[last_shipping_tax_id].included === '0' || jsTaxes[last_shipping_tax_id].included === null)) {
			total -= parseFloat(shippingTaxes[last_shipping_tax_id]);
		}

		// Add Shipping Tax to the total if it's not included
		if (!empty(shipping_fees) && !empty(shipping_tax_id) && (jsTaxes[shipping_tax_id].included === '0' || jsTaxes[shipping_tax_id].included === null)){
			total += parseFloat(shippingTaxes[shipping_tax_id]);
		}


		// Check If the tax is already exist in the invoice item and substract or delete the tax in taxIDs
		if (!empty(shipping_fees) && !empty(last_shipping_tax_id)) {
			if (taxIDs[shipping_tax_id] == shippingTaxes[shipping_tax_id]){
				delete taxIDs[shipping_tax_id];
			} else {
				taxIDs[shipping_tax_id] -= parseFloat(shippingTaxes[shipping_tax_id]);
			}
		}

		// Check If the tax is already exist in the invoice item and add or create the tax in taxIDs
		if (!empty(shipping_fees) && !empty(shipping_tax_id)) {
			if (typeof taxIDs[shipping_tax_id] == 'undefined') {
				taxIDs[shipping_tax_id] = parseFloat(shippingTaxes[shipping_tax_id]);
			} else {
				taxIDs[shipping_tax_id] += parseFloat(shippingTaxes[shipping_tax_id]);
			}
		}




		total = parseFloat(total)+parseFloat(itemSubtotal);
		total = parseFloat(total);

		// Save Last Tax Shipping Id to compare for the next invoice item
		last_shipping_tax_id = shipping_tax_id;
		
	});

	
	total+=parseFloat(shipping);
	if (total > 0 && typeof adjustmentValue != 'undefined') {
		total += adjustmentValue;
	}

	$('.invoice_account').each(function(){
		if($(this).val()!=='')
		{
			var account_value=	parseFloat(string2number($(this).val()));
			if(Math.abs(account_value)>0)
			{
				total += account_value;
			}
		}
	})



	var currency = $('#EditCurrency u').html() ? $('#EditCurrency u').html() : $('#ClientDefaultCurrency').val();
	$('span#products-total').text(format_price(total, currency));
	$('#SubtotalValue').text(format_price(subtotal,currency));
	$('#SubtotalValue').data('subtotal', subtotal);

	
	totalValue=total;
	

	var html = '';

	$('#DiscountRow').remove();
	if (discountValue > 0 && !$('#DiscountRow').length){
		var discountText = __('Disc.') + (!is_discount_amount&&sum_items_discount==0?' (' + discount + '%)':'');
		html += '<tr class="notEditable" id="DiscountRow"><td  class="tspan new-row-cell"></td>';
		html += '<td  class="text-right"><strong>' + discountText + '<strong></strong>';
		html += '<td class="text-left" id="DiscountValue">-' + format_price(discountValue, currency) + '</td>';
		html += '</tr>';
		$('.discount-value').val(format_price(discountValue, currency));
	} else {
		$('.discount-value').val(format_price('0.00', currency));
	}

	$('.tax-row').remove();
	for (var idx in taxIDs){
		var tax = jsTaxes[idx];
		let taxLabelValue = toFixedWithPrecision(tax.value, currency);
		var taxLabel =  tax.name + ' (' + taxLabelValue + '%)';

		html += '<tr id="Tax' + idx +'Row" class="tax-row notEditable"><td  class="tspan new-row-cell"></td>';
		html += '<td  class="text-right text-nowrap"><strong>' + taxLabel + '</strong></td>';
		html += '<td class="text-left">' + format_price(taxIDs[idx].toFixed(4), currency) + '</td>';
		html += '</tr>';
	}

	
		

	var deposit = string2number($('#InvoiceDeposit').val());
	var depositType = string2number($('#InvoiceDepositType').val());
	if (depositType == 2){
		deposit = total * deposit / 100;
	}
	
	if($('#PaymentIsPaid').length>0 && $('#PaymentIsPaid').get()[0].checked)
	{
		deposit=0;
		summary_paid=total;
	}
	else if($('#DepositIsPaid').length>0 && $('#DepositIsPaid').get()[0].checked)  
	{
		summary_paid=deposit;
		deposit=0;
	}
	else if($('#DepositIsPaid').length>0)
	{
		summary_paid=0;
	}
	
	var unpaidVal = 0;
	var paidText = __('Paid') ;
	var unpaidText = __('Balance Due:') ;
	var shipping_tax_id = $('#shipping_tax_id').children("option:selected").val();
	
	unpaidVal = total - summary_paid;
        if(refunds>0)
        unpaidVal -=refunds;
	
	
	$('#ShippingRow').remove();
	
		var shippingText = __('Shipping') ;
		if(shipping>0)
		{
			if (!empty(shipping_tax_id) && jsTaxes[shipping_tax_id].included === '1') {
				shipping -= shippingTaxes[shipping_tax_id];
			}
			html += '<tr class="notEditable" id="ShippingRow"><td  class="tspan new-row-cell"></td>';
			html += '<td  class="text-right"><strong>' + shippingText + '<strong></strong>';
			html += '<td class="text-left" id="ShippingValue">' + format_price(shipping, currency) + '</td>';
			html += '</tr>';
		}
			
		
	
	$('#DepositRow').remove();
	if (deposit > 0 && deposit != unpaidVal && !$('#DepositRow').length){
		var depositText = __('Next Payment') ;
		html += '<tr class="notEditable" id="DepositRow"><td class="tspan new-row-cell"></td>';
		html += '<td  class="text-right"><strong>' + depositText + '<strong></strong>';
		html += '<td class="text-left" id="DepositValue">' + format_price(deposit, currency) + '</td>';
		html += '</tr>';
		$('.deposit-value').val(format_price(deposit, currency));
	} else {
		$('.deposit-value').val(format_price('0.00', currency));
	}

	$('#AdjustmentRow').remove();
	if (typeof adjustmentValue !== 'undefined' && adjustmentValue !== 0.0 && !$('#AdjustmentRow').length) {
		html += '<tr class="notEditable" id="AdjustmentRow"><td  class="tspan new-row-cell"></td>';
		html += '<td  class="text-right"><strong>' + adjustmentLabel + '<strong></strong>';
		html += '<td class="text-left" id="AdjustmentValue">' + format_price(adjustmentValue, currency) + '</td>';
		html += '</tr>';
	}

	$('.invoice_account_summary').remove();
	$('.invoice_account').each(function(){
		if($(this).val()!=='')
		{
		    var account_value=	parseFloat(string2number($(this).val()));
			if(Math.abs(account_value)>0)
			{
				var account_label=$(this).data('label');
				html += '<tr class="invoice_account_summary notEditable" id="invoice_account_summary_'+$(this).data('accountid')+'" ><td  class="tspan new-row-cell"></td>';
				html += '<td  class="text-right"><strong>' + account_label + '<strong></strong>';
				html += '<td class="text-left" id="Account'+$(this).data('accountid')+'Value">' + format_price(account_value, currency) + '</td>';
				html += '</tr>';
			}
		}
	})

	$('#FinalTotalRow').before(html);

	$('#TotalValue').text(format_price(total,currency));
	$('.total-value').val(format_price(total,currency));
	
	$('#PaidRow').remove();
	$('#UnpaidRow').remove();

	html ='';
        
        $('#RefundsRowx').remove();	
                var RefundsText = __('Refunds') ;
		if(refunds>0)
		{
			
			html += '<tr id="RefundsRowx"><td  class="tspan new-row-cell"></td>';
			html += '<td  class="text-right"><strong>' + RefundsText + '<strong></strong>';
			html += '<td class="text-left" id="ShippingValue">-' + format_price(refunds, currency) + '</td>';
			html += '</tr>';
		}	
	
	if(unpaidVal>0&&unpaidVal!=total||refunds>0)    
	{
		html += '<tr class="notEditable" id="PaidRow"><td  class="tspan new-row-cell"></td>';
		html += '<td  class="text-right"><strong>' + paidText + '<strong></strong>';
		html += '<td class="text-left" id="DepositValue">' + format_price(summary_paid, currency) + '</td>';
		html += '</tr>';
		html += '<tr class="notEditable" id="UnpaidRow"><td  class="tspan new-row-cell"></td>';
		html += '<td  class="text-right"><strong>' + unpaidText + '<strong></strong>';
		html += '<td class="text-left" id="DepositValue">' + format_price(unpaidVal, currency) + '</td>';
		html += '</tr>';
		
		$('#FinalTotalRow').after(html);
	
	}
	
	if(summary_paid==0)
	status_text=__('Unpaid');
	else if(summary_paid<total)
	status_text=__('Partial Paid');
	else
	status_text=__('Paid');

	$('.status-value').val(status_text);
	$('.paid-amount-value').val(format_price(summary_paid, currency));
	$('.unpaid-amount-value').val(format_price(unpaidVal, currency));     
	//if(total==subtotal) 	$('.items-totals').hide(); else $('.items-totals').show();
        justifyColumns();
	if (typeof applyInsurance === "function" && callApplyInsurance) {
		if (applyInsurance(invoiceData)) {
			callApplyOffers = false;
		}
	}
    if (typeof applyOffers === "function" && callApplyOffers) {
        // safe to use the function
        applyOffers(invoiceData);
    }
	autoSaveDraftDebounced();
}

function getSubtotalsUsingPercentDiscount(discountPercent) {
	updateTaxColumnsView();
	var callApplyInsurance = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
	var callApplyOffers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
	var invoiceData = {
		'Invoice': {
			'date': $("#InvoiceDate").val(),
			'client_id': $("#InvoiceClientId").val(),
		},
		'InvoiceItem': []
	};
	recalcauto();
	var tax1 = string2number($('#InvoiceTax1').val());
	var tax2 = string2number($('#InvoiceTax2').val());
	var discount = string2number(discountPercent);
	var discount_amount = string2number($('#InvoiceDiscountAmount').val());
	var sum_items_discount=0;
	var is_discount_amount=false;
	if(discount_amount!=0&&discount_amount!='')
	{
			
			 var allSub=0;
			 is_discount_amount=true;
			 $('.itemRow', '#listing_table').each(function(){
					var unitPrice = string2number($('.item-unit-price', this).val());
					var qty = string2number($('.item-qty', this).val());
					var sub = (qty * unitPrice);
					total_item_discount = calculate_item_discount(this,sub);
				 	sum_items_discount+=total_item_discount;
                    sub_with_discount = sub - total_item_discount ;
					allSub += sub_with_discount;
			 });

			 discount =  (discount_amount/allSub * 100);
	}
	allSub += round(allSub,2);
	var currency=$('#EditCurrency u').html();
	var shipping = string2number($('#InvoiceShippingAmount').val());
	var refunds = string2number($('#InvoiceSummaryRefund').val());

	var subtotal = 0.0, discountValue = 0.0, total = 0.0;
	var taxIDs = [];
	var shippingTaxes = [];
	var total_item=$('.itemRow', '#listing_table').length;
	var current_item=0;
	var sum_items_discount=0;
	var total_item_discount=0;
	var last_shipping_tax_id;
	$('.itemRow', '#listing_table').each(function(){
		total_item_discount=0;
		sum_items_discount=0;
		current_item++;
		var tax1 = string2number($('.item-tax1', this).val());
		var tax2 = string2number($('.item-tax2', this).val());
        var unitPrice = string2number($('.item-unit-price', this).val());
        var qty = string2number($('.item-qty', this).val());
        invoiceData.InvoiceItem.push({
            'product_id': $('.item-id', this).val(),
            'unit_price': unitPrice,
            'quantity': qty
        });
		var itemSubNoTax=0;
		var itemSubtotal = autoRound(qty * unitPrice);
                total_item_discount = calculate_item_discount(this,itemSubtotal)
				sum_items_discount+=total_item_discount;
                itemSubtotal -= total_item_discount ; 
		$('.item-subtotal span', this).text(format_price(itemSubtotal,currency));
		$('.item-subtotal span', this).data('subtotal',itemSubtotal);
		subtotal = autoRound(subtotal+itemSubtotal);
		

		var itemDiscount = autoRound(itemSubtotal * discount / 100);

		discountValue = autoRound(discountValue+round(itemDiscount,2));

		discountValue+=sum_items_discount;


		itemSubtotal = autoRound(itemSubtotal-itemDiscount );

		itemSubNoTax=itemSubtotal ;
		var incTax=0;
		if(tax1&&jsTaxes[tax1].included=='1')
		{
			incTax+=string2number(jsTaxes[tax1].value);
		}
		if(tax2&&jsTaxes[tax2].included=='1')
		{
			incTax+=string2number(jsTaxes[tax2].value);
		}
		

		if(incTax>0)
		{
			var itemNoTaxVal=autoRound((incTax  / (string2number(incTax) + 100)) * itemSubtotal);
			itemSubNoTax= autoRound(itemSubNoTax-itemNoTaxVal);
		}
	

		var itemTax1 = 0, itemTax2 = 0;
		
		if (tax1){
			var t1 = jsTaxes[tax1];
			itemTax1 = itemSubNoTax * t1.value / 100;
			
			if (taxIDs[tax1] == undefined){
				taxIDs[tax1] = 0;
			}
			taxIDs[tax1] += itemTax1;
		}
		if (tax2){
			var t2 = jsTaxes[tax2];
			itemTax2 = itemSubNoTax * t2.value / 100;
			
			if (taxIDs[tax2] == undefined){
				taxIDs[tax2] = 0;
			}
			taxIDs[tax2] += itemTax2;
		}

		// Prepare Shipping Tax
		var shipping_tax_id = $('#shipping_tax_id').children("option:selected").val();
		var shipping_fees =  $('#InvoiceShippingAmount').val();
		if (!empty(shipping_tax_id) && !empty(shipping_fees)) {
			shippingTaxes[shipping_tax_id] = getTaxAmount(shipping_fees, jsTaxes[shipping_tax_id]);
		}
		
		if(tax1&&t1.included!='1')
		itemSubtotal += itemTax1;
		else if(tax1&&t1.included=='1')
		subtotal -=  itemTax1;
		
		if(tax2&&t2.included!='1')
		itemSubtotal += itemTax2 ;
		else if(tax2&&t2.included=='1')
		subtotal -= itemTax2 ;
	
		
	});

     return subtotal;
}

function calculate_item_discount(item , itemSubtotal){
    //Item Discount
    var item_discount = string2number($('.item-discount', item).val());
	var item_factor = 1;
	if($('.unit-factor-id', item)) {
		item_factor= string2number($('.unit-factor-id', item).find('option:selected').attr('factor'));
	}
	if(!item_factor) {
		item_factor = 1;
	}
	var item_quantity = string2number($('.item-qty', item).val());
    var item_discount_type = string2number($('.item-discount-type', item).val());
    var total_item_discount = 0 ;
	if (typeof item_discount !== "undefined" && typeof item_discount_type !== "undefined" && item_discount_type != "" && item_discount != "") {
		if (item_discount_type == 2) {
			total_item_discount = (item_discount * item_quantity) / item_factor;
		} else {
			total_item_discount = (itemSubtotal * item_discount / 100)
		}
	}
    return total_item_discount ; 
}
function calculateDates(){
	if ($('#InvoiceDate').length){
		try{

			var invoiceDate = $('#InvoiceDate').val();
			var date = $.datepicker.parseDate(jsDateFormat, invoiceDate);

			if (IS_INVOICE){
				var dueDays = string2number($('#InvoiceDueAfter').val());
				$('.due-days-value').val(dueDays);
				var dueDate = date;
				dueDate.setDate(date.getDate() + dueDays);
				var strDueDate = $.datepicker.formatDate(jsDateFormat, dueDate);
				$('.due-date-value').val(strDueDate);

			}
		} catch (e) {
		//			$('.due-days-value').val(0);
		//			$('.due-date-value').val($.datepicker.formatDate(jsDateFormat, new Date()));
		}
	}
}

function activateTab(rel){
	$('.tabs-buttons li').removeClass('current');
	$('.tabs-box:not(' + rel + ')').addClass('hidden');

	$('.tabs-buttons').removeClass('no-tabs');
	$('.tabs-buttons li[rel=' + rel + ']').addClass('current');
	$(rel).removeClass('hidden');
}

function deactivateTabs(){
	$('.tabs-buttons li').removeClass('current');
	$('.tabs-box').addClass('hidden');

	$('.tabs-buttons').addClass('no-tabs');
}
//--------------------------
function validDate(value){
	if ($(value).hasClass('is-calendarsPicker')) {
		if (!(notEmpty(value.value) && $(value).calendarsPicker('getDate').length > 0)) {
			$(value).after($('<div>', {
				'class': 'error-message'
			}).text(__('Invalid date. Date must match date format')));
			return false;
		}
		return true;
	}
	try {
		var result = (value == $.datepicker.formatDate(jsDateFormat, $.datepicker.parseDate(jsDateFormat, value)));
		if (!result){
			$(value).after($('<div>', {
				'class': 'error-message'
			}).text(__('Invalid date. Date must match date format')));
		}
		return result;
	} catch (exception) {
		return false;
	}
}
//--------------------------
function validateFields(){

	if($('#HiddenCurrencyCode').val()=='') {
		$('#InvoiceCurrencyCode').val(currency_code);
		$('#HiddenCurrencyCode').val(currency_code);
		$('u','#EditCurrency').text(currency_code);
	}
	

	var rules = {
		'#InvoiceClientId' : ['notEmpty'],
		'#InvoiceTax1' : {
			'rules': ['isNumeric', 'isNotMinus'],
			empty: true
		},
		'#InvoiceTax2' : {
			'rules': ['isNumeric', 'isNotMinus'],
			empty: true
		},
		'#InvoiceDiscount': {
			'rules': ['isNumeric', {
				rule: 'lessThanOrEqual',
				value: 100
			}],
			empty: true
		},
		'#InvoiceDiscountAmount': {
			'rules': ['isNumeric'],
			empty: true
		},

		'#InvoiceAdjustmentLabel': {
			'rules': [],
			empty: true
		},
		'#InvoiceAdjustmentValue': {
			'rules': ['isNumeric'],
			empty: true
		},
		
		
		'#InvoiceClientBusinessName' : {
			'rules': ['isClientSaved'],
			empty: false
		},
		'#new_address1' : {
			'rules': ['isClientAddressSaved'],
			empty: false
		},
		'#InvoiceDueAfter' : {
			'rules': ['isNumeric', 'isNotMinus'],
			empty: true
		},
		'#InvoiceIssueBefore' : {
			'rules': ['isNumeric', 'isNotMinus'],
			empty: true
		},
		'#InvoiceDate': {
			rules: ['notEmpty'],
			pass: 'selector'
		},
		'#InvoiceIssueDate': {
			rules: ['notEmpty'],
			pass: 'selector'
		},
		
		'.item-description': [{
			rule: 'maxLength',
			value: 100000
		}],
		'.item-unit-price': ['isNumeric'],
		'.item-qty': ['isNumeric'],
		'.reminder-days' :{
			'rules': ['checkReminderDays'],
			pass: 'selector'
		},
//		'.reminder-email-template': ['notEmpty'],
		'.reminder-sendwhen': ['notEmpty'],
		'.custom-label': {
			'rules': ['checkCustomValue'],
			pass: 'selector'
		},
		'.custom-value': {
			'rules': ['checkCustomValue'],
			pass: 'selector'
		},
		'#TermsSelector': ['validateTerms'],
		'.multiple_payment_treasury_id': {
			'rules': ['notEmpty'],
			pass: 'selector'
		},
		'.multiple_payment_amount': ['notEmpty','isNumeric'],
		'.multiple_payment_payment_method': {
			'rules': ['notEmpty'],
			pass: 'selector'
		}   
	};
	
	if(!IS_TEMPLATE)
	{
		$.extend(rules, {
			'.item_name': ['notEmpty']
		});
	}
        
	if($('#InvoiceDepositType').val() == 2){
		$.extend(rules, {
			'#InvoiceDeposit' : {
				'rules': ['isNumeric', 'betweenPercentage'],
				empty: true
			}
		});
	}else{
		$.extend(rules, {
			'#InvoiceDeposit' : {
				'rules': ['isNumeric',  {
					rule: 'lessThanOrEqual',
					value: totalValue
				}],
				empty: true
			}
		});
	}

	if (typeof IS_SUBSCRIPTION != 'undefined' && IS_SUBSCRIPTION){
		$.extend(rules, {
			'#InvoiceIssueBefore': {
				rules: ['isNumeric',  'checkSubscriptionIssue'],
				empty: true
			},
			'#InvoiceName': ['notEmpty'],
			'#InvoiceSubscriptionPeriod': ['isNumeric', 'isNotMinus'],
			'#InvoiceSubscriptionMaxRepeat': {
				'rules': ['isNumeric', 'isNotMinus'],
				empty: true
			}
		});
	} else if (typeof IS_TEMPLATE!= 'undefined' && IS_TEMPLATE){
		$.extend(rules, {
			'#InvoiceName': ['notEmpty'],
			'#InvoiceClientId': []
		});
	}
        if ( typeof IS_REQUISITION !== "undefined"){
            $.extend(rules, {
			'.item-qty' : {
				'rules': ['isNumeric', 'notEmpty']
			}
		});
        }

	if ($('#PaymentAdvancePayments').is(':checked')) {
		console.log("$('#PaymentAdvancePayments').val()")
		$.extend(rules, {
			'select.advance_payment_invoices': {
				'rules': ['notEmpty'],
				pass: 'selector'
			},
			'.multiple_advance_payment_amount': {
				'rules': ['isNumeric', 'notEmpty', 'isNotMinus'],
			}
		});
	}
	var validationMessages = {
		notEmpty: __('Required'),
		isNumeric: __('Valid number required'),
		validDate: __('Valid date required'),
		isNotMinus: __('Positive number required'),
		checkReminderDays: __('Invalid number of days'),
		checkCustomValue: __('Either label or value required'),
		lessThan: __('Value must be less than :value'),
		lessThanOrEqual: __('Value must be less than or equal to :value'),
		maxLength: __('Only :value characters allowed'),
		betweenPercentage:__('Value must be between [0-100]'),
		checkSubscriptionIssue: __('Issue days must be less than subscirption period'),
		validateTerms: __('Please select the required terms'),
		isClientSaved: __('Please Save the client details first by clicking below on Save Client button'),
		isClientAddressSaved: __('Please save the client address')
	};

	$('#InvoiceForm').trigger('validators', [
		rules, validationMessages
	]);

	return validate(rules, validationMessages);
}

function validateTerms(selector){
	if (document.getElementById('TextCheckbox').checked){
		return notEmpty(selector)
	}
	return true;
}


function checkCustomValue(selector){
	//	var _this = $(selector);
	//	var parent = _this.parents('.field-row');
	//	return _this.hasClass('.read-only-field') || notEmpty($('.custom-label', parent).val()) || notEmpty($('.custom-value', parent).val());
	return true;
}

function checkReminderDays(selector){
	var _this = $(selector);
	var when = string2number($('.reminder-sendwhen', _this.parents('td')[0]).val());

	if (when == 3 || when == 4){
		var value = _this.val();
		return notEmpty(value) && isNumeric(value) && isNotMinus(value);
	}
	return true;
}


function resizeItem(item){
	$(item).parents('.itemRow').find('.item-name a.select-arrow').height($(item).height());
}


function isClientSaved($val)
{
	if($('#ClientData').is(':visible')) {
		return false;
	}
	return true;
}

function isClientAddressSaved()
{
	if ($('.new_address_div').is(':visible')) {
		return false;
	}

	return true;
}

function checkSubscriptionIssue(value){
	var issueDays = string2number(value);
	if (issueDays){
		var subscriptionPeriod = string2number($('#InvoiceSubscriptionPeriod').val());
		var subscriptionUnit = 1;
		switch ($('#InvoiceSubscriptionUnit').val()){
			case 'weeks':
				subscriptionUnit = 7
				break;
			case 'months':
				subscriptionUnit = 30;
				break;
			case 'years':
				subscriptionUnit = 365;
				break;
		}

		subscriptionPeriod *= subscriptionUnit;
		return issueDays < subscriptionPeriod;
	}

	return true;
}


function justifyColumns()
{
    if($('.item_name').length < 100 && IS_PC)
    {
		$('#SubtotalValue').width($('.item-subtotal').width() + $('.delete-product-cell').width() + 2);
        $('.items-totals td:eq(1)').width($('.tax1').width()+$('.tax2').width()+$('.Quantity').width()+$('.unit-price').width()+4);
    }
    
   
}

function updateTaxColumnsView()
{
	var taxes_count= tax_count_value;
	if(taxes_count>=2)
	{
		$('.tax2').show();
	}
	else
	{
		$('.tax2').hide();
                $('.tax2 select').val('');
	}
        justifyColumns();
        
}
function CloseTaxes(){
$('#myModalTax').modal('hide');
}
function updateTaxes(){
	var taxesURL = SITE_ROOT + '/owner/taxes/taxeslist';
	if (typeof invoice_id != 'undefined' && invoice_id){
		taxesURL += '/' + invoice_id+'/1';
	}
	
	return $.ajax({
		url: taxesURL,
		dataType: 'json',
		success: function(data){
			jsTaxes = data.jsTaxes;

			var html = '<option value=""></option>';
			var list_html = '<li>' + $('li:first', '#TaxList').html() + '</li>';
			for (var t in data.taxes){
				if (data.taxes.hasOwnProperty(t)){
					html += '<option value="' + t + '">' + data.taxes[t] + '</option>';
					list_html += '<li><a href="#' + t + '" class="tax-link">' + data.taxes[t] + '</a></li>';
				}
			}
			$('.item-tax').each(function(){
				$(this).data('my-val', $(this).val());
				var opCount= $('option', this).length;
				var lastOption = $('option:last', this);
				$(this).html(html + '<option value="' + lastOption.val() + '">' + lastOption.html() + '</option>').val($(this).data('my-val'));
				if($(this).val()==''&&$(this).data('last')!='-1'&&$(this).data('last')!='') $(this).val($(this).data('last'));
				if($(this).attr('id')==$(lastTaxfocus).attr('id')&&$('option', this).length==opCount+1&&$(this).val()=='')  $(this).val($('option',this).eq(-2).val());
				$(this).change();
			});

			list_html += '<li>' + $('li:last', '#TaxList').html() + '</li>';
			$('#TaxList').html(list_html);
			baseItemRow = $('#listing_table tr.itemRow:first').html();
			updateTaxColumnsView();
			
			CloseTaxes();
		}
	});
}

function reviewIds()
{
	$('.itemRow', '#listing_table').each(function(){
		
		if($('.item_name', this).val().replace(/[^a-zA-Z0-9]/g,'').indexOf($('.org_name', this).val().replace(/[^a-zA-Z0-9]/g,'')) == -1 &&  $('.org_name', this).val().replace(/[^a-zA-Z0-9]/g,'').indexOf($('.item_name', this).val().replace(/[^a-zA-Z0-9]/g,'')) == -1 )
		$('.item-id', this).val(0);
	});
}

function CloseProducts(){
$('#myModalGeneral').modal('hide');	
}

function addProduct(product) {
	var t = $(ProLi(product)).insertBefore(".add-product-link");
    if (typeof jsProducts === 'undefined' || jsProducts === null) {	
        jsProducts = {};
    }
	jsProducts[product.id]=product;
	$('a.product-link:last', '#InvoiceProducts').click();
	 $('html,body').animate({scrollTop: $('.invoice-items').offset().top-100}, 600);
}

// Performance optimization for more than 50 items
function optimizeForLargeInvoices() {
	if ($('.invoice-items .itemRow').length > 50 && typeof initPageLoader === 'function') {
		$('.invoice-items').addClass('d-none');
		initPageLoader();

		var optimizeInterval = setInterval(function() {
			var subtotal = $('#SubtotalValue').data('subtotal');
			if (document.readyState === 'complete' && subtotal) {
				$('.invoice-items').removeClass('d-none');
				removePageLoader();
				clearInterval(optimizeInterval);
			}
		}, 1000);

		setTimeout(function () {
			$('.invoice-items').removeClass('d-none');
			removePageLoader();
			clearInterval(optimizeInterval);
		}, 5000);
	}
}

optimizeForLargeInvoices();

$(function() {
        $('body').append(productsHTML + taxHTML);
		
		$(document).ready(function() {
			var timeout = $('.invoice-items .itemRow').length > 50 ? 1000 : 0;
			setTimeout(function() {
				$('#InvoiceHtmlNotes').summernote({
					toolbar:[
						['style', ['bold', 'italic', 'underline', 'clear']],['font', ['strikethrough']],['fontsize', ['fontsize']],
						['color', ['color']],['para', ['ul', 'ol', 'paragraph']],
						['insert', ['link', 'unlink', 'hr']]
					]
				});
			}, timeout);
		});

        var listingTable = document.getElementById('listing_table');

        $('.tax-link', '#TaxList').live('click', function(evt) {
            
            var thisList = $('#TaxList');
            var parent = thisList.data('current-td');
            var value = $(this).data('value');
            $('.item-tax', parent).val(value).change();
            thisList.hide();
            return false;
        });

		function showTaxIframe() {
			var val = $(this).val();
			if (val == -1) {
				lastTaxfocus = this;
				$('#tax_iframe').attr('style', 'border: 0; height: 420px');
				$('#myModalTaxLabelheader').hide();
				$('#tax_iframe').attr('src', SITE_ROOT + '/owner/taxes?box=1');
				$('#myModalTax').modal('show');
				$(this).val('');
			} else {
				calculateTotals();
			}
		}

		$('.item-tax', listingTable).live('focus',function (){ $(this).data('lastfocus',$(this).val()) });
        $('.item-tax', listingTable).live('change', showTaxIframe).change();
		$('#shipping_tax_id').live('change', showTaxIframe).change();

        $('.add-tax-link', '#TaxList').live('click', function(evt) {
            var frame = $('<iframe />', {
                src: SITE_ROOT + '/owner/taxes?box=1',
                border: 0,
                frameBorder: 0,
                id: 'lightBoxFrame',
                style: 'border: 0; width: 400px; height: 420px'
            });
           // $('<div>').append(frame).lightbox_me();
				$('#tax_iframe').attr('style','border: 0; height: 420px');
				$('#myModalTaxLabelheader').hide();
				$('#tax_iframe').attr('src',SITE_ROOT + '/owner/taxes?box=1');
				$('#myModalTax').modal('show');
            evt.preventDefault();
        });

        function showTaxes(evt) {
            var $parent = $(this).parents('td');

            var thisList = $('#TaxList');
            var visible = thisList.is(':visible');

            var ofst = $parent.offset();
            var height = parseInt($parent.height());

            thisList.css({top: ofst.top + height, left: ofst.left});
            ;
            if (!visible) {
                thisList.show();
            } else if (evt.type == 'click' && thisList.data('current-td') == $parent[0]) {
                thisList.hide();
            }

            thisList.data('current-td', $parent[0]);
            return false;
        }

        $('.tax-arrow', listingTable).live('click', showTaxes);
        $('.item-tax-display', listingTable).live('focus', showTaxes).live('blur', function() {
            var $parent = $(this).parents('td');
            var thisList = $('#TaxList');
            if (thisList.data('current-td') == $parent[0]) {
                thisList.hide();
            }
        });
		
		$('#InvoiceLayout').val($('#InvoiceInvoiceLayoutId').val());
		
        
		if($('#InvoiceInvoiceLayoutId').val()==0 || $('#InvoiceId').val()==''){
		$("#InvoiceLayout").trigger("change",["def"]);	
		}else{
		$("#InvoiceLayout").trigger("change",["manual"]);	
		}
	
		$(document.body).on('change', '.active_secondary_address', function(event) {
            if ($(this).attr('checked') == 'checked') {
			$('#secondary_address_info').show();
            } else {
			$('#secondary_address_info').hide();
            }

        });
        
    });


	
function ProLi(product)
{   
	if (empty(product.product_code))  product.product_code=product.id;
		else 
			product.product_code=product.product_code;
                    
                    
                imageUrl = '';
                if(product.file != '' && typeof product.file != 'undefined' && product.file != null)
                {
 					if(product.is_s3){
						imageUrl = product.file ;
					}else{
						imageUrl = "/files/" + SITE_HASH + "/product-images/" + product.file +  '?w=30&h=30&c=1'  ;
					}
                    imageHtml = "<img src='" + imageUrl + "'/>"; 
                }
		product_line=  productLi.replaceAll('$file?w=30&h=30&c=1', imageUrl).replaceAll('$index',product.id).replaceAll('$name',product.name).replaceAll('#$product_code',product.product_code).replaceAll('$product_code',product.product_code);
				if(product.track_stock=="1"){
					product_line=product_line.replaceAll('$stock_balance',product.stock_balance);
					product_line=product_line.replaceAll('$stock_available',product.stock_available);
				}else{
					product_line=product_line.replaceAll('$none_stock','none');
				}
	return product_line;
						
}

function CancelClient(){
	$('#CancelClient').trigger('click');
	$('#NewClientMessage').remove();
	$('.error-message').remove();
}


function loadClientsList(client_id){
                if ( typeof invoice_selector == 'undefined'){
                    invoice_selector = '#InvoiceClientId'
                }
//		var invoice_selector = 
		var clientList = $(invoice_selector);
		pushCID(client_id);


		var selected = client_id;
		if (!selected){
			clientList.val();
		}

		var emptyOptionText = $('option:first', clientList[0]).text();
		var prevHtml = clientList.html();

		clientList.html('<option value="Loading">Loading</option>');

		$.ajax({
			url: SITE_ROOT + '/owner/clients/load_list/'+used_clients.join(),
			cache: false,
			dataType: 'json',
			success: function(data, status){
				
				
				if (!data.error){
					var html = '<option value="">' + emptyOptionText + '</option>';
					for (var c in data.clients){
						var sel = '';
						if (string2number(c) == selected){
							sel = ' selected="selected"';
						}
						html += '<option value="' + c + '"' + sel + '>' + data.clients[c] + '</option>';
					}
					clientList.html(html);
					$(invoice_selector).selectpicker('refresh');
					$(invoice_selector).val(selected);
					$(invoice_selector).selectpicker('render');
					$(invoice_selector).trigger('change');
					
					
					loadClientData();
				} else {
					this.error();
				}
			},
			error: function(){
				clientList.html(prevHtml);
				$('.selectpicker').selectpicker('render');
			}
		});
	}
	
	function loadClientData(){

		var client_id = $('#InvoiceClientId').val();
		if(client_id=='' || client_id==undefined){
			return ;
		}
		pushCID(client_id);
		var currentClientID = $("#ClientID").val();

		//if (client_id && client_id != currentClientID){
			
			
			$('#ClientLoader').show();
			//$('#ClientData').hide();
			$.ajax({
				url: SITE_ROOT + '/owner/clients/get_user_data/' + client_id,
				dataType: 'json',
				cache: false,
				data:{
                  date : $('#InvoiceDate').attr('value')
				},
				success: function(data){
					if (!data.error){
                      client_faddress=data.client['full_address'];
					    group_price_id =parseInt(data.client.group_price_id);
						db_group_price_id = group_price_id;

						setGroupPriceId(group_price_id);
						$(document).find("#group_price_id").val(group_price_id);
							
						for (var key in data.client){
							if(key=='type'){
							$('[name="data[Invoice][client_'+ key + ']"][value="'+data.client[key]+'"]').prop('checked',true).change();
							}else{
							$('[name="data[Invoice][client_'+ key + ']"]').val(data.client[key]);
							if($('[name="data[Invoice][client_'+ key + ']"]').attr('type')=='checkbox'&&data.client[key]&&data.client[key]!='0'){
								$('[name="data[Invoice][client_'+ key + ']"]').attr('checked',true);
							}
						}
						}

						try {
                            if (typeof clientAddressesDiv != 'undefined') {
                                clientAddressesDiv.empty()
                            }
                            addClientAddresses(data.client);
                        } catch (e) {
							console.log(e)
						}
						
						$('#InvoiceClientActiveSecondaryAddress').val('1');

						var is_offiline;
						if (data.client['email'] && data.client['is_offline']==0) {
							is_offiline = 0;
						} else {
							is_offiline = 1;
						}
						
						$('[name="data[Invoice][is_offline]"]').val(is_offiline);
                                                
						if($('#InvoiceClientActiveSecondaryAddress').is(':checked')){
							$('#secondary_address_info').show();
							
						}

						$("#ClientID").val(data.client.id);
						$("#InvoiceIsOffline").val(is_offiline);
						switchMethod();
						$('.selectpicker').selectpicker('render');
						//$('#InvoiceCurrencyCode').val(data.client.default_currency_code);
						resetAdvancePaymentsForm();
						loadClientAdvancePayments(data.client.id);
					} else {
						this.error();
					}

				  //Trigger events after loading client data	
				   $('#InvoiceClientId').trigger('loaded_client_data', [data]);
				   generateStaticClientData();
				   if(typeof clientFollowUpPluginActive !== 'undefined' && clientFollowUpPluginActive){
						let clientFollowUpStatuses = '';
						if(data.client.follow_up_status){
							clientFollowUpStatuses += clientFollowUpList[data.client.follow_up_status]['data-content'];
						}
						if(data.client.secondary_follow_up_status){
							clientFollowUpStatuses += '&nbsp; '+ clientFollowUpList[data.client.secondary_follow_up_status]['data-content'];
						}
						$('#clientFollowUpStatuses').html(clientFollowUpStatuses);
				   }


				   renderClientFollowUpStatus(data);
				   
                   update_client_balance();
					$('#ClientLoader').hide();

				},
				error: function(){
					
					clientDataLoaded = false;
					$('#ClientFromData').val(0);
				//	$(':input:not(:radio,:checkbox)', '#ClientData').val('');
					$('#ClientLoader').hide();
				}
			});
		//} else {
			
			//if(ajaxUrl!='owner/clients/add'){
			//generateStaticClientData();
			//}
		//}

	}


let advancePaymentOptionsHtml = '<option value="" selected>' + emptyAdvancePaymentOption + '</option>';
function loadClientAdvancePayments(clientId) {
	let invoiceCurrency = $('#InvoiceCurrencyCode').val();
	let targetUrl = SITE_ROOT + '/owner/invoices/client_advance_payments/' + clientId + '/' + invoiceCurrency;
	if (window.location.href.includes('owner/invoices/edit/')) {
		targetUrl += '/1';
	}
	$.ajax({
		url: targetUrl,
		dataType: 'json',
		cache: false,
		success: function (data) {
			if (!data.error) {
				let html = '<option value="" selected>' + emptyAdvancePaymentOption + '</option>';
				for (const invoiceId in data) {
					html += '<option data-unsettled="'+ data[invoiceId].unSettledAmount +'" value="' + invoiceId + '">' + data[invoiceId].no + '</option>';
				}
				$('select.advance_payment_invoices').html(html);
				advancePaymentOptionsHtml = html;
				selectAdvancePayments();
				$('.advance-payment-selectpicker').selectpicker('refresh');
			}
		},
		error: function () {
			$('#ClientAdvancePayments').html('');
		}
	});
}
function resetAdvancePaymentsForm() {
	let shouldTerminate = false;
	$('select.advance_payment_invoices').each(function (){
		if ($(this).data('selected-advance-payment')) {
			shouldTerminate = true;
		}
	})
	if (shouldTerminate) return;
	$('[data-advance-payment-invoices-row-delete]').click();
	$('.advance-payment-selectpicker').selectpicker('refresh');
	$('.multiple_advance_payment_amount').val('');
	$('.unsettled-amount').val('');
}

function resetInvoicePaymentsForm() {
	$('[data-invoice-payment-methods-row-delete]').click();
	$(".multiple_payment_payment_method, .multiple_payment_treasury_id").val("").trigger("change");
	$('.multiple_payment_amount').val('');
}

$('.advance-payments-well').on('change', 'select.advance_payment_invoices', function () {
	const selectedInvoice = $(this).find('option:selected');
	let currentName = $(this).attr('name');
	currentName = currentName.match(/\[AdvancePaymentInvoices\]\[(\d{1,3})\]/)
	let currentIndex = -1;
	if (currentName.length) currentIndex = currentName[1];
	selectedAdvancePayments[currentIndex] = selectedInvoice.val();
	const unsettledAmount = selectedInvoice.data('unsettled');
	$(this).closest('tr').find('input.unsettled-amount').val(unsettledAmount);
	calculateTotals();
});

$.fn.deserialize = function (serializedString)
{

    var $form = this;
    $form[0].reset();
    serializedString = serializedString.replace(/\+/g, '%20');
    var formFieldArray = serializedString.split("&");
    $.each(formFieldArray, function(i, pair){
        var nameValue = pair.split("=");
        var name = decodeURIComponent(nameValue[0]);
        var value = decodeURIComponent(nameValue[1]);
        var $field = $form.find("[name='" + name + "']");
        
        if ($field[0]?.type == "radio" 
            || $field[0]?.type == "checkbox") 
        {
            var $fieldWithValue = $field.filter('[value="' + value + '"]');            
            var isFound = ($fieldWithValue.length > 0);
            if (!isFound && value == "on") {
                $field.first().prop("checked", true);
            } else {
                $fieldWithValue.prop("checked", isFound);
            } 
        } else {
            $field.val(value);
        }
    });
	loadClientData();

}


	function setGroupPriceId(id)
	{
		$('#PriceList').val(id).trigger('change');
	}


	function generateStaticClientData(){
					var bndata1=$('#InvoiceClientBn1Label').val()+':'+$('#InvoiceClientBn1').val();
                    var bndata2=$('#InvoiceClientBn2Label').val()+':'+$('#InvoiceClientBn2').val();
                    if(bndata1==':'){
                        bndata1='';
                    }
                    if(bndata2==':'){
                        bndata2='';
                    }
                     $('#StaticBN').text(bndata1+bndata2);
					
					
		$('#ClientFromData').val(1);

		$('#StaticUserName').text($('#InvoiceClientFirstName').val() + ' ' + $('#InvoiceClientLastName').val());


		if ($('#InvoiceClientBusinessName').val() != ''){
			$('#StaticBusinessName').text($('#InvoiceClientBusinessName').val());
		} else {
			$('#StaticBusinessName').text('');
		}

		

		var csp = client_faddress;
		
		if (csp != ''){
			$('#StaticUserCSP').html(csp);
		} else{
			$('#StaticUserCSP').text('');
		}

		if ($('#InvoiceClientCountry').val()){
			$('#StaticUserCountry').text($('#InvoiceClientCountry').val());
		}else {
			$('#StaticUserCountry').text('');
		}


		if ($.trim($('#StaticBusinessName').text())!= ''&&!$('#ClientData').is(':visible')){
			
			$('#StaticClientInfo').show();
			$('.ClientSelect').show();
			$('html,body').animate({
				scrollTop: $('#StaticClientInfo').offset().top-100
			}, 600);

		}

		var clientCurrency = $('#ClientDefaultCurrency').val();   
		if (clientCurrency != ''&&cuCh==0&&($('#InvoiceId').val()==''||$('#InvoiceId').val()=='0')){
			$('#InvoiceCurrencyCode').val(clientCurrency);
			$('#HiddenCurrencyCode').val(clientCurrency);
			$('u','#EditCurrency').text(clientCurrency);
		}
		calculateTotals();
	}
	
		function switchMethod()
	{
		if($('#InvoiceIsOffline').val()=='0')
		{
			$('.print-method').hide();
			$('.email-method').show();
		}
		else
		{
			$('.email-method').hide();

			$('.print-method').show();
		}
	}
	
	function pushCID(cid)
	{
		if (typeof cid !== 'undefined')
		{
			cid=parseInt(cid);
			if(used_clients.indexOf(cid) === -1) used_clients.push(cid);
		}
	} 
	    
        function RemoveEmptyItemRow(){
        var lastRow = $('tr.itemRow', '#listing_table');
       if(lastRow.length==1){

           return false;
       }
       $(lastRow).each(function(index,value){
        	$(':input', value).each(function(){
if($(this).hasClass('item_name') && $(this).val()==''){

 var lastRow = $('tr.itemRow', '#listing_table');
 
        if(lastRow.length==1){
                    return ;
        }else{
		var descInp = $(value).find('.item-description');	
		if(descInp.length>0 && $(descInp).val()!="") return;
		$(value).remove();
        }
}
                });   
           
           
       });
       
        }
		
		
		function list_products(value,store_id = null ){
			var modelQueryParam = '';
			var orderTypeQueryParam = '';
			if (typeof model !== 'undefined') {
				var modelQueryParam = '&model=' + model;
			}
			if (typeof orderType !== 'undefined' && orderType != '') {
				var orderTypeQueryParam = '&orderType=' + orderType;
			}
				//if(aps=='0') return;
				lasts=value;
				$('.product-link').each(function(i, obj) {
					$(this).parent("li").remove();
				});
				get_all_products = false ;
				if ( (typeof all_products !== "undefined") && all_products )
				{
					get_all_products = true ;
				}
				$('<li><img class="product-link" src="'+SITE_ROOT+'/css/img/loader.gif"></li>').insertBefore(".add-product-link");
				if(saj!=0)
				saj.abort();
				saj=$.ajax({
				url: SITE_ROOT + '/plain.php?action=product_find&val=' + encodeURIComponent(value)+'&client='+$('#InvoiceClientId').val()+'&store_id='+store_id+ '&all_products='+get_all_products + modelQueryParam + orderTypeQueryParam,
				dataType: 'json',
				cache: false,
				success: function(data){
					
					jsProducts=data.data;
					$('.product-link').each(function(i, obj) {
						$(this).parent("li").remove();
					});
					var sk=value;
					var selx=0, cx=0;
					
					$.each(data.order, function( index, order_value ) {
						value=jsProducts[order_value.toString()];
						$('.products-list-ul').append(ProLi(value));
						// $(ProLi(value)).insertBefore(".add-product-link");
						
						if(!empty(value.barcode)&&value.barcode.trim()==sk.trim() && value.name.trim() != value.barcode.trim() ) 
							selx=value.id;
						// if (Object.keys(data.data).length == 1 && sk.startsWith(value.barcode.trim()))
						// 	selx=value.id;
						cx++;
					});
				
					
					if(selx!=0&&cx==1)
						$('#product-'+selx).click();
					
				},
				error: function(){
				}
			});
}
function ReNumberAll(){
	var LeIndex=0;
	$('.items-listing tr.movable').each(function(){
		$(this).find('input,select,textarea').each(function()  {
			if($(this).attr('name')!=undefined) {

				$(this).attr('name',$(this).attr('name').replace(/\[\d+]/g,"["+LeIndex+"]"));
			}
		});
		LeIndex++;
	});
}
function recalcauto(){
		if(columns['col_1']=='auto'){
			
			var i =1;	
			$('.item_name').each(function(){
				
			$(this).val(i++);	
			
			});
			var i =1;	
			
			$('.org_name').each(function(){
				
			$(this).val(i++);	
			
			});
			
			
			}
						/* for(iv=3;iv<=5;iv++){
                        if(!empty(columns['col_'+iv])) $('.item_col_'+iv).val(product[columns['col_'+iv]]).trigger('keyup');
                        } */
	
}
function LoadDocUploader(id){
    if(loaded_uploaded_doc==1){
        return ;
    }
    $('#invoice_doc_load').html('<div class="inner-loader"></div>');
                                $.ajax({
				url: SITE_ROOT + 'owner/invoices/docs/'+id,
				success: function(data){
                                    loaded_uploaded_doc=1;
                                 $('#invoice_doc_load').html(data);  
                                }});
    
}
function closepreview(){
$('.layout').show();
$('.footer').show();
if(IS_MOBILE==1){	

document.querySelector("meta[name=viewport]").setAttribute('content', 'width=device-width, initial-scale=1.0');	
}
$('#iframe_preview').remove();
$('.close_preview').hide();
$('body').show();	
if($('.button-2.select-actions.btn-group.m-r-md').after($('.in_preview'))){
$('#actionbutton').remove();
}
}

function hidepreviewloader(){
	$('.preview-notifcation-loader').remove();
}
function update_client_balance(){
	let currencyCode = $('#InvoiceCurrencyCode').val() ?? $('#HiddenCurrencyCode').val();
    $( "#StaticUserName" ).load( "/owner/clients/balance/"+$('#InvoiceClientId').val()+"/"+currencyCode, function() {

    });
}

function renderClientFollowUpStatus(data){
	if(typeof clientFollowUpPluginActive !== 'undefined' && clientFollowUpPluginActive){
		let clientFollowUpStatuses = '';
		if(data.client.follow_up_status){
			clientFollowUpStatuses += clientFollowUpList[data.client.follow_up_status]['data-content'];
		}
		if(data.client.secondary_follow_up_status && (data.client.secondary_follow_up_status != data.client.follow_up_status)){
			clientFollowUpStatuses += '&nbsp; '+ clientFollowUpList[data.client.secondary_follow_up_status]['data-content'];
		}
		$('#clientFollowUpStatuses').html(clientFollowUpStatuses);
   }
}

// this for changing shipping option and put fees
$('#shipping_option_id').on('change', function(e) {
	var fees = $(this).find(':selected').data('fees');
	var tax_id = $(this).find(':selected').data('tax-id');
	if (fees != undefined) {
		$('#InvoiceShippingAmount').val(parseFloat(fees))
		$('#InvoiceShippingAmount').attr('readonly','readonly')
	} else {
		$('#InvoiceShippingAmount').val('')
		$('#InvoiceShippingAmount').removeAttr('readonly')
	}
	if (tax_id != undefined) {
		$('#shipping_tax_id').val(tax_id)
	} else {
		$('#shipping_tax_id').val('');
	}
	$('#shipping_tax_id').trigger('change');
	calculateTotals();
})

/**
 * Keybaord Shortcuts
 */

// add title for tabs
function toggleAdvancePaymentsBox(checked) {
	if (checked){
		$('#DepositIsPaid').attr('checked', false);
		$('#payment_method').show();
		$('#transaction_id').show();
		$('#treasury_id').show();
		$('.well.advance-payments-well').show();
		$(this).closest('[data-advance-payment-methods-checkbox-container]').find('[data-advance-payment-methods-wrapper]').removeClass('d-none');
	} else {
		$('#payment_method,#transaction_id,#treasury_id').hide();
		$('#transaction_id').val('');
		$('#treasury_id').val('');
		$('.well.advance-payments-well').hide();
		$(this).closest('[data-advance-payment-methods-checkbox-container]').find('[data-advance-payment-methods-wrapper]').addClass('d-none');
		resetAdvancePaymentsForm();
	}
}

function selectAdvancePayments() {
	// if $('#PaymentAdvancePayments') disabled, enable
	if ($('#PaymentAdvancePayments').prop('disabled')) {
		$('#PaymentAdvancePayments').prop('disabled', false);
	}
	$('select.advance_payment_invoices').each(function () {
		if ($(this).data('selected-advance-payment')) {
			$(this).val($(this).data('selected-advance-payment')).trigger('change');
		}
	});
	calculatePayments();
}

$(document).ready(function() {
	$('#extra-settings .nav-tabs .main-tabs').each(function(i, element) {
		if ($(element).is(':visible')) {
			$(element).attr('title', "(ALT + " + (i + 1) + ")")
		}
	});
	$('.select-prod .item_name').each(function(i, element) {
		var current_id_element = $('.select-prod .item-id')[i];
		var td = $($('.col-1.td_editable')[i]);
		if ($(this).val() && !$(current_id_element).val()) {
			td.append("<div class='bg-warning fs-12 p-1 product-price-text s2020 text-white-s2020' style='margin-bottom: 8px; padding: 11px; width: 230px'><a href='#' class='warning-add-product-link'>" + __('Non Created Item', true) + " (<b>" + __('Add New Product', true) + "</b>)</a></div>");
		}
	});
	var itemNameChange = $('.select-prod .item_name').change(function () {
		var index = itemNameChange.index(this);
		var td = $($('.col-1.td_editable')[index]);
		var current_name_element = $($(this)[index]);
		var current_id_element = $($('.select-prod .item-id')[index]);
		setTimeout(() => {
			if (current_name_element.val() && !current_id_element.val()) {
				if (!td.find('.bg-warning').length) {
					td.append("<div class='bg-warning fs-12 p-1 product-price-text s2020 text-white-s2020' style='margin-bottom: 8px; padding: 11px; width: 230px'><a href='#' class='warning-add-product-link'>" + __('Non Created Item', true) + " (<b>" + __('Add New Product', true) + "</b>)</a></div>");
				}
			}
		}, 100);
	})
	var itemIdChange = $('.select-prod .item-id').change(function () {
		var index = itemIdChange.index(this);
		var td = $($('.col-1.td_editable')[index]);
		td.find('.bg-warning').remove();
	})
	if ($('#InvoiceClientId').val()) {
		$('#PaymentAdvancePayments').prop('disabled', false);
		loadClientAdvancePayments($('#InvoiceClientId').val());
	}
});

// keyboard shortcuts
$(document).on("keydown", function(e) {
	if ((e.ctrlKey == false &&
		e.altKey == false) &&
		(document.activeElement.nodeName == "INPUT" ||
		document.activeElement.nodeName == "SELECT" ||
		(document.activeElement.nodeName == "BUTTON" && $(document.activeElement).hasClass("selectpicker")))) {
		return true;
	}

	// alt + p (preview)
	var inPreviewEventListener = null;
	if (e.keyCode == 80 && e.altKey) {
		if (inPreviewEventListener == null) {
			inPreviewEventListener = $(document).on("keydown", function(e) {
				// esc (close preview)
				if (e.keyCode == 27) {
					closepreview();
					inPreviewEventListener = null;
					return false;
				}
			});
		}
		$('.html-invoice').click();
		return false;
	}

	// alt + d (save draft)
	if (e.keyCode == 68 && e.altKey) {
		$('.save.draft').click();
		return false;
	}

	// alt + n (add new client)
	if (e.keyCode == 78 && e.altKey) {
		$('html, body').animate({
			scrollTop: ($("#AddNewClient").offset().top - 150)
		}, 200);
		$("#AddNewClient").click();
		return false;
	}

	// alt + c (select client)
	if (e.keyCode == 67 && e.altKey) {
		$('html, body').animate({
			scrollTop: ($("#AddNewClient").offset().top - 150)
		}, 200);
		$('[data-id="InvoiceClientId"]').click();
		return false;
	}

	// alt + number (select tabs)
	if ((e.keyCode >= 49 && e.keyCode <= 57) && e.altKey) {
		$('html, body').animate({
			scrollTop: ($("#extra-settings .nav-tabs").offset().top - 150)
		}, 200);
		var $tabs = $('#extra-settings .nav-tabs .main-tabs');
		if (typeof ($tabs[Number(e.keyCode) - 49]) != 'undefined' &&
			$($tabs[Number(e.keyCode) - 49]).is(':visible')) {
			$($tabs[Number(e.keyCode) - 49]).find('a').click();
		}
		return false;
	}

	// alt + enter (save)
	if (e.keyCode == 13 && e.altKey) {
		var $activeSaveButton = $('.save:not(.draft)')[0];
		$('.save:not(.draft)').each(function (i, element) {
			var $elm = $(element);
			if ($elm.is(':visible')) {
				$activeSaveButton = $elm;
			}
		});
		$activeSaveButton.click();
		return false;
	}

	// ctrl + space (add new line)
	if (e.keyCode == 32 && e.ctrlKey) {
		$('html, body').animate({
			scrollTop: ($("#AddItem").offset().top - 150)
		}, 200);
		$('#AddItem').click();
		return false;
	}

	// ctrl + up/down (move between lines)
	if ((e.keyCode == 38 || e.keyCode == 40) && e.ctrlKey) {
		if ($(document.activeElement).closest('#listing_table').length || $('.product-list-active').length) {
			$('.select-prod .item_name').each(function(i, element) {
				var activeItem = null;
				if ($('.product-list-active')) {
					activeItem = $('.product-list-active')[0];
				} else {
					activeItem = $(document.activeElement).closest('tr').find('td.select-prod .item_name')[0]
				}
				if (element == activeItem) {
					var prevItem = $('.select-prod .item_name')[(i >= 1 ? i : 0) - 1];
					var nextItem = $('.select-prod .item_name')[i + 1];
					if (e.keyCode == 38) {
						if (prevItem) {
							$('html, body').animate({
								scrollTop: ($(prevItem).offset().top - 150)
							}, 200);
							$(prevItem).focus();
							setTimeout(function() {
								if ($(prevItem).attr('readonly')) {
									$('#search_product').focus();
								}
							}, 100);
							return false;
						}
					} else {
						if (nextItem) {
							$('html, body').animate({
								scrollTop: ($(nextItem).offset().top - 150)
							}, 200);
							$(nextItem).focus();
							setTimeout(function() {
								if ($(nextItem).attr('readonly')) {
									$('#search_product').focus();
								}
							}, 100);
							return false;
						}
					}
				}
			});
		} else {
			if (e.keyCode == 38) {
				$('html, body').animate({
					scrollTop: ($('.select-prod .item_name').first().offset().top - 150)
				}, 200);
				$('.select-prod .item_name').first().focus();
			} else {
				$('html, body').animate({
					scrollTop: ($('.select-prod .item_name').last().offset().top - 150)
				}, 200);
				$('.select-prod .item_name').last().focus();
			}
		}
		return false;
	}

});

$('#InvoiceOrderSourceId').on('change', function (e) {
	$('.order-source-value').val($(this).find('option:selected').text());
})
/**
 * Multiple payment methods
 */

$(document).on('click', '[data-invoice-multiple-payment-method-btn]', function(e) {
	var $select = $('[data-invoice-payment-method-wrapper]').find('select');
	if ($select.length && typeof $select.get(0).selectize != 'undefined') {
		if ($('[data-invoice-multiple-payment-methods-wrapper]').is(':visible')) {
			$select.get(0).selectize.enable();
			$('.single_payment_methods').show();
			$('[data-invoice-single-payment-method]').find(':input').removeAttr('disabled');
			$('[data-invoice-multiple-payment-methods-wrapper]').hide();
			resetInvoicePaymentsForm();
		} else {
			$select.get(0).selectize.disable();
			$('.single_payment_methods').hide();
			$('[data-invoice-single-payment-method]').find(':input').attr('disabled', 'disabled');
			$('[data-invoice-multiple-payment-methods-wrapper]').show();
			$('[data-invoice-multiple-payment-methods-wrapper]').removeClass('d-none');
			calculatePayments();
			$('[data-invoice-payment-methods-row-add]').trigger('click');
		}
    }
});

$('body').on('change', '.multiple_payment_payment_method',function () {
	let obj = $(this);
	pt = PaymentTreasury[obj.val()];
	let treasuryField = obj.parents('tr').first().find('.multiple_payment_treasury_id');
	if(treasuryField.length > 0) {
		if (pt) {
			treasuryField.val(pt);
			treasuryField.trigger('change');
		}
	}
	return false;
});

function calculatePayments() {
    // Standard Invoice Payments
    var standardTotal = calculateTableTotal('[data-invoice-payment-methods-row-amount]');
    var $standardTotalAmount = $('[data-invoice-payment-methods-amount]');

    // Advance Payment Invoices
    var advanceTotal = calculateTableTotal('[data-advance-payment-methods-row-amount]');
    var $advanceTotalAmount = $('[data-advance-payment-methods-amount]');

    // Shared Remaining Calculation
    var $totalRemaining = $('[data-invoice-payment-methods-remaining], [data-advance-payment-methods-remaining]');
    var remainingContainer = $totalRemaining.parent('p');
    var totalRemainingValue = totalValue || 0;
    var totalPaid = standardTotal + advanceTotal; // Sum both tables

    // Reset and apply correct remaining balance class
    resetRemainingClasses(remainingContainer);
    if (totalPaid > totalRemainingValue) {
        remainingContainer.addClass('text-danger');
    } else if (totalPaid < totalRemainingValue) {
        remainingContainer.addClass('text-warning');
    } else {
        remainingContainer.addClass('text-success');
    }

    // Currency formatting
    var currency = $('#EditCurrency u').html() || $('#ClientDefaultCurrency').val();
    var remainingValue = round(totalRemainingValue - totalPaid, 4); //need to add round its bug in js when say 115.115-100=15.114999999999995

    // Update DOM
    $standardTotalAmount.html(format_price(standardTotal));
    $advanceTotalAmount.html(format_price(advanceTotal));
    $totalRemaining.html(format_price(remainingValue, currency));
}

function resetRemainingClasses($totalRemaining) {
	let classes = ['text-danger','text-success','text-warning'];
	for(let c in classes) {
		$totalRemaining.removeClass(classes[c]);
	}
}

// Helper function to calculate total for a given table
function calculateTableTotal(selector) {
    var total = 0;
    $(selector).each(function () {
        var val = Number($(this).val());
        if (!isNaN(val) && val !== 0) {
            total += val;
        }
    });
    return total;
}

$(document).on('click', '[data-invoice-payment-methods-row-delete]', function (e) {
	e.preventDefault();
	let count = $(this).parents('tbody').first().find('tr').length;
	if(count > 1){
		$(this).parents('tr').remove();
		calculatePayments();
	}
});

$(document).on('click', '[data-invoice-payment-methods-row-add]', function (e) {
	e.preventDefault();
	var rowTemplateHTML = document.querySelector('[data-invoice-payment-methods-row-template]').innerHTML;
	var $rows = $('[data-invoice-payment-methods-table]').find('tbody tr');
	var index = 0;

    if($rows.length) {
        index = $rows.length;
    }
    rowTemplateHTML = rowTemplateHTML.replaceAll('#index', index);

	$('[data-invoice-payment-methods-table]').find('tbody').append(rowTemplateHTML);
});


let selectedAdvancePayments = {};

$(document).on('click', '[data-advance-payment-invoices-row-add]', function (e) {
	e.preventDefault();
	var rowTemplateHTML = document.querySelector('[data-advance-payment-invoices-row-template]').innerHTML;
	var $rows = $('[data-advance-payment-invoices-row-table]').find('tbody tr');
	var index = 0;

	if($rows.length) {
		index = $rows.length;
	}
	rowTemplateHTML = rowTemplateHTML.replaceAll('#index', index);

	$('[data-advance-payment-invoices-row-table]').find('tbody').append(rowTemplateHTML);
	$("select.advance_payment_invoices:last").html(advancePaymentOptionsHtml);
	$("select.advance_payment_invoices:last option").filter(function () {
		const val = $(this).val();
		return Object.values(selectedAdvancePayments).includes(val);
	}).remove();
	$('.advance-payment-selectpicker').selectpicker('render');
});

$(document).on('click', '[data-advance-payment-invoices-row-delete]', function (e) {
	e.preventDefault();
	let count = $(this).parents('tbody').first().find('tr').length;
	if(count > 1){
		$(this).parents('tr').remove();
		calculatePayments();
	}
});

$(document).on('change', '[data-invoice-payment-methods-row-amount],.item-unit-price,.item_name', function (e) {
	setTimeout(function() {
		calculateTotals();
		calculatePayments();
	});
});

$(document).on('change', '[data-advance-payment-methods-row-amount],.item-unit-price,.item_name', function (e) {
	setTimeout(function() {
		calculateTotals();
		calculatePayments();
	});
});

$(document).on('click', '.removeItem', function(e) {
	setTimeout(function() {
		calculateTotals();
		calculatePayments();
	});
});

$(document).ready(function () {
	calculatePayments();
});
