<?php


class TillNow
{
	private static $startTime;
	private static $lastTime;
	private static $counter = 0;

	public static function tillnow($mark = '')
	{
		if (!isset($_GET['tillnow'])) return;
		if (!isset(self::$startTime)) {
			self::$startTime = microtime(true);
			self::$lastTime = self::$startTime;
		}

		$currentTime = microtime(true);
		$elapsedSinceLast = ($currentTime - self::$lastTime) * 1000000; // Convert to microseconds
		$totalElapsed = ($currentTime - self::$startTime) * 1000000; // Convert to microseconds

		echo "<br/><br/><br/>Time since last call $mark: " . number_format($elapsedSinceLast, 2) . " microseconds\n";
		echo "<br/>Total time elapsed $mark: " . number_format($totalElapsed, 2) . " microseconds\n";

		self::$lastTime = $currentTime;
		if (isset($GET['die']) && $GET['die'] == self::$counter) die();
		self::$counter++;

	}
}

if(php_sapi_name()=='cli' && strpos($_SERVER['argv'][1], "debug=2")){
    ini_set('display_startup_errors',1);
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
}else {
    ini_set('display_errors', 0);
    error_reporting(0);
}
define('IS_CRON', true);
define('IS_PC',true);
define('IS_MOBILE',false);
define('WORKING_DIRECTORY', getcwd());
define('IS_TABLET',false);
require_once dirname(dirname(__DIR__)) . '/configure.php';
include dirname(__DIR__).'/config/pure_function.php';
function fatal_handler() {
	register_shutdown_function(function () {
		$errfile = "unknown file";
		$errstr = "shutdown";
		$errno = E_CORE_ERROR;
		$errline = 0;
		$error = error_get_last();
		if ($error !== NULL) {
			$errno = $error["type"];
			if (in_array($errno, array(E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR,))) {
				$message = "Time : ".date("Y-m-d H:i:s")."\n";
				$errfile = $error["file"];
				$errline = $error["line"];
				$errstr = $error["message"];
//	  $trace = print_r( debug_backtrace( false ), true );
				$calledFrom = debug_backtrace();

				$files = [];
				foreach ($calledFrom as $call => $fff) {
					$files[] = $fff['file'];

				}
				$files = print_r($files, true);
				$message .= "IP: ".get_real_ip()."\r\n";
				$message .= "ِAgent: $_SERVER[HTTP_USER_AGENT]\r\n";
				if (PHP_SAPI == 'cli') {
					$cliLine = print_r($GLOBALS['argv'], true) . "\r\n";
				}
				$postdata = print_r($_POST, true);

				$message = "  <table>  <thead><th>Item</th><th>Description</th></thead>
  <tbody>
  <tr>
    <th>IP</th>
    <td><pre>$_SERVER[REMOTE_ADDR]</pre></td>
  </tr>
  <tr>
    <th>AGENT</th>
    <td><pre>$_SERVER[HTTP_USER_AGENT]</pre></td>
  </tr>
  <tr>
    <th>URL</th>
    <td><pre>https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]</pre></td>
  </tr>  <tr>
    <th>Cli</th>
    <td><pre>$cliLine</pre></td>
  </tr>
  
  <tr>
  <tr>
    <th>REFERER</th>
    <td><pre>$_SERVER[HTTP_REFERER]</pre></td>
  </tr>
  <tr>
    <th>POST DATA</th>
    <td><pre>$postdata</pre></td>
  </tr>
  <tr>
    <th>Error</th>
    <td><pre>$errstr</pre></td>
  </tr>
  <tr>
    <th>Errno</th>
    <td><pre>$errno</pre></td>
  </tr>
  <tr>
    <th>File</th>
    <td>$errfile</td>
  </tr>
  <tr>
    <th>Trace</th>
    <td>$files</td>
  </tr>
  <tr>
    <th>Line</th>
    <td>$errline</td>
  </tr>
  <tr>
    <th>Trace</th>
    <td><pre>$trace</pre></td>
  </tr>
  </tbody>
  </table>";
				$headers = "MIME-Version: 1.0" . "\r\nContent-type:text/html;charset=UTF-8\r\nFrom: <<EMAIL>>\r\n";
				if (!strpos($errstr, 'static method') && !in_array(getenv('APP_ENV'), ['local'])) {
					$per_day_counter=0;
					if(file_exists($file_name='/tmp/error_counter_'.date('Y_m_d')))
						$per_day_counter=intval(file_get_contents($file_name));
					$per_day_counter++;
					file_put_contents($file_name, $per_day_counter);
					if($per_day_counter<1000||($per_day_counter<10000&&$per_day_counter%1000==0)||($per_day_counter<100000&&$per_day_counter%10000==0)||($per_day_counter<1000000&&$per_day_counter%100000==0)||($per_day_counter<10000000&&$per_day_counter%1000000==0))
					{

						$subject_extra = ($per_day_counter > 1000 ? floor(pow(10 , strlen((($per_day_counter/10).''))) ) . 'TH ' : '');


						if(!in_array(getenv('APP_ENV'), ['local','testing','staging'])) {
							mail('<EMAIL>', $subject_extra.' '.Domain_Name_Only.': FETAL ERROR From Cron', $message, $headers);
							mail('<EMAIL>', $subject_extra.' '.Domain_Name_Only.': FETAL ERROR From Cron', $message, $headers);
						}
						file_put_contents('/tmp/errors.html', $message, FILE_APPEND);

					}
				}
				echo $message;
			}
		}
	});
}

register_shutdown_function( "fatal_handler" );

/* SVN FILE: $Id: index.php 7945 2008-12-19 02:16:01Z gwoo $ */
/**
 * Short description for file.
 *
 * Long description for file
 *
 * PHP versions 4 and 5
 *
 * CakePHP(tm) :  Rapid Development Framework (http://www.cakephp.org)
 * Copyright 2005-2008, Cake Software Foundation, Inc. (http://www.cakefoundation.org)
 *
 * Licensed under The MIT License
 * Redistributions of files must retain the above copyright notice.
 *
 * @filesource
 * @copyright     Copyright 2005-2008, Cake Software Foundation, Inc. (http://www.cakefoundation.org)
 * @link          http://www.cakefoundation.org/projects/info/cakephp CakePHP(tm) Project
 * @package       cake
 * @subpackage    cake.app.webroot
 * @since         CakePHP(tm) v 0.2.9
 * @version       $Revision: 7945 $
 * @modifiedby    $LastChangedBy: gwoo $
 * @lastmodified  $Date: 2008-12-18 18:16:01 -0800 (Thu, 18 Dec 2008) $
 * @license       http://www.opensource.org/licenses/mit-license.php The MIT License
 */
/**
 * Use the DS to separate the directories in other defines
 */
	if (!defined('DS')) {
		define('DS', DIRECTORY_SEPARATOR);
	}

require_once dirname(__DIR__). DS . 'vendor' .DS .'autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
/**
 * These defines should only be edited if you have cake installed in
 * a directory layout other than the way it is distributed.
 * When using custom settings be sure to use the DS and do not add a trailing DS.
 */

/**
 * The full path to the directory which holds "app", WITHOUT a trailing DS.
 *
 */
	if (!defined('ROOT')) {
		define('ROOT', dirname(dirname(dirname(__FILE__))));
	}
/**
 * The actual directory name for the "app".
 *
 */
	if (!defined('APP_DIR')) {
		define('APP_DIR', basename(dirname(dirname(__FILE__))));
	}
/**
 * The absolute path to the "cake" directory, WITHOUT a trailing DS.
 *
 */
	if (!defined('CAKE_CORE_INCLUDE_PATH')) {
		if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
			define('CAKE_CORE_INCLUDE_PATH', dirname(dirname(dirname(__FILE__))));
		}else{
			define('CAKE_CORE_INCLUDE_PATH', realpath(dirname(dirname(dirname(__FILE__))) .'/cakephp'));
		}
	}

/**
 * Editing below this line should not be necessary.
 * Change at your own risk.
 *
 */
	if (!defined('WEBROOT_DIR')) {
		define('WEBROOT_DIR', basename(dirname(__FILE__)));
	}
	if (!defined('WWW_ROOT')) {
		define('WWW_ROOT', dirname(__FILE__) . DS);
	}
	if (!defined('CORE_PATH')) {
		if (function_exists('ini_set') && ini_set('include_path', CAKE_CORE_INCLUDE_PATH . PATH_SEPARATOR . ROOT . DS . APP_DIR . DS . PATH_SEPARATOR . ini_get('include_path'))) {
			define('APP_PATH', null);
			define('CORE_PATH', null);
		} else {
			define('APP_PATH', ROOT . DS . APP_DIR . DS);
			define('CORE_PATH', CAKE_CORE_INCLUDE_PATH . DS);
		}
	}
	if (!include(CORE_PATH . 'cake' . DS . 'bootstrap.php')) {
		trigger_error("CakePHP core could not be found.  Check the value of CAKE_CORE_INCLUDE_PATH in APP/webroot/index.php.  It should point to the directory containing your " . DS . "cake core directory and your " . DS . "vendors root directory.", E_USER_ERROR);
	}
if(!defined('TMP_PATH')){
	define('TMP_PATH', WWW_ROOT.'..'.DS.'tmp');
}


if ($argc == 2) {
	$Dispatcher = new Dispatcher();
	$Dispatcher->dispatch($argv[1]);
}
?>
