# stop access to tmp folder
<If "%{REQUEST_URI} =~ m#^/webroot/files/tmp($|/)#">
    Require all denied
</If>
<IfModule mod_rewrite.c>
    RewriteEngine On

	RewriteRule ^(.+)_v(\d+)\.(css|js)$ $1.$3 [QSA,NC,L]
	# not needed when move files to s3
	#RewriteCond %{REQUEST_FILENAME} .*\.(png|jpg|jpeg||gif|tif|JPEG|PNG|JPG|TIF|Jpg|Png|Jpg|Tif|Jpeg|svg|SVG)$
	RewriteRule ^files/images/site-logos/(.*\.(png|jpg|jpeg|gif|tif|svg))$ - [NC,E=origfile:$1]
	RewriteRule ^files/images/logos/(.*\.(png|jpg|jpeg|gif|tif|svg))$ - [NC,E=origfile:$1]
    RewriteRule files/images/site-logos/(.*\.(png|jpg|jpeg|gif|tif|JPEG|PNG|JPG|TIF|Jpg|Png|Jpg|Tif|Jpeg|svg|SVG)$) image.php?prefix=site-logos&s3_image=fake_name [QSA,L]
    RewriteRule files/images/logos/(.*\.(png|jpg|jpeg|gif|tif|JPEG|PNG|JPG|TIF|Jpg|Png|Jpg|Tif|Jpeg|svg|SVG)$) image.php?prefix=logos&s3_image=fake_name [QSA,L]
	RewriteCond %{QUERY_STRING} !=""
	RewriteRule (.*\.(png|jpg|jpeg||gif|tif|JPEG|PNG|JPG|TIF|Jpg|Png|Jpg|Tif|Jpeg|svg|SVG)$) image.php?image=$1 [QSA,L]
	RewriteRule resizescript/(.*$) image.php?image_link=$1 [QSA,L]
	RewriteRule s3/(.*$) awsImage.php?path=$1 [QSA,L]
    RewriteCond %{REQUEST_URI} !=/server_status
    RewriteCond %{REQUEST_URI} !=/server-status
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
</IfModule>

SetOutputFilter DEFLATE
AddOutputFilterByType DEFLATE text/html text/plain text/xml text/javascript application/javascript text/css

#KeepAlive on

<FilesMatch "\.(ico|pdf|flv|jpg|jpeg|png|gif|js|css|swf)$">
Header set Cache-Control "public,max-age=290304000"
</FilesMatch>

<IfModule mod_rewrite.c>
    RewriteEngine On

    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
	
</IfModule>

BrowserMatchNoCase ^YandexBot bad_bot
BrowserMatchNoCase ^Nimbostratus bad_bot
BrowserMatchNoCase ^GoogleBot bad_bot
BrowserMatchNoCase ^CyotekWebCopy bad_bot
Order Deny,Allow
Deny from env=bad_bot
Options -Indexes
<Files mysql_monitor.php>
    AuthName "Mysql Monitor Script"
    AuthType Basic
    AuthUserFile /var/www/html/daftra/.htpasswd
    Require valid-user
</Files>
