<?php
/**
 * Created by PhpStorm.
 * User: Izam
 * Date: 8/16/2018
 * Time: 11:20 AM
 */

use Izam\Aws\Aws;
use Izam\Logging\Service\RollbarLogService;

if(isset($_GET['debug']) && $_GET['debug'] == 2) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
    error_reporting(E_ALL);
}
ini_set('memory_limit','8G');
require_once dirname(dirname(__DIR__)) . '/configure.php';
include dirname(__DIR__).'/config/pure_function.php';
include dirname(__DIR__).'/vendor/autoload.php';
switchToRedis();
session_name("OISystem");
session_start();
setStatusInfo();
putenv("APP_ENV=" . APP_ENV);
require_once __DIR__ . '/../providers/rollbar_provider.php';
$db_config = $_SESSION['db_config'];
$isBranchPluginActive = isBranchPluginActive(); //Check if branch plugin is active

$link = connectToDatabase($db_config);
mysqli_select_db($link, $db_config['database']);
mysqli_query($link,'SET NAMES utf8');


// Quick version of check_permission, not same behavior as check_permission in system
function check_permission($permission, $staff_id, $link) {
    $role_query = mysqli_query($link,"SELECT role_id FROM staffs WHERE id = '$staff_id'");
    $role_id = mysqli_fetch_assoc($role_query)['role_id'];
    $permission_query = mysqli_query($link,"SELECT * FROM roles_permissions WHERE role_id = '$role_id' AND permission_id = '$permission'");
    return mysqli_num_rows($permission_query) > 0;
}

function isOwner() {
    return $_SESSION['OWNER']['staff_id'] == 0;
}

function isSuperAdmin() {
    return $_SESSION['OWNER']['is_super_admin'] == 1;
}


//get Branches Conditions
$branchCondition = '';
$currentStaffID = $_SESSION['OWNER']['staff_id'];
if ($isBranchPluginActive) {
    $BranchPlugin = 87;
    $currentStaffID = $_SESSION['OWNER']['staff_id'];
    $currentStaffBranchInfo = mysqli_query($link,"select `key`,`value` from settings where plugin_id = {$BranchPlugin} and (`key` = 'branch-{$currentStaffID}' or `key` = 'share_products')");
    while ($row = mysqli_fetch_assoc($currentStaffBranchInfo)) {
        if ($row['key'] == 'share_products') {
            if ($row['value'] == 1) {
                $branchCondition = '';
                break;
            }
        } else {
            $currentStaffBranch = $row['value'];
            $branchCondition = "and products.branch_id = {$currentStaffBranch}";
        }
    }
}

$store_id = (int) $_GET['store_id'] ?? 1;


// This is needed because we use group_concat to concat large string more than 1024 character
mysqli_query($link, 'SET GLOBAL group_concat_max_len = 1000000;');

$q ="SET SESSION sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))";
$result = mysqli_query($link, $q);


$q = "SELECT products.*,product_images.file,
      GROUP_CONCAT(DISTINCT(category_id)) as category_ids,
      GROUP_CONCAT(DISTINCT(CONCAT(group_price_id, ':',CASE WHEN group_prices.status = 1 THEN price ELSE unit_price END))) AS group_prices, 
      store_stock_balance.balance from products 
      left join items_categories on (item_id = products.id and item_type = 1) 
      left join product_prices on (product_id = products.id)
      left join group_prices ON (product_prices.group_price_id = group_prices.id)
      left join product_images on (product_images.product_id = products.id AND product_images.default = 1)
      left join store_stock_balance on (products.id = store_stock_balance.product_id and store_stock_balance.store_id = {$store_id})
      where (products.status in (0,1,2) or products.status is null) group by id, product_images.file, store_stock_balance.balance";
$result = mysqli_query($link, $q);
if (!$result) {
    die('Invalid query: ' . $q );
}
$ret = [];
while ($row = mysqli_fetch_assoc($result)) {
    $row['barcode']=strtolower($row['barcode']);

    $row['is_s3'] = false;
    $id = $row['id'];
    $q = "SELECT entity_attachments.* ,files.name , files.path, files.is_temp FROM entity_attachments JOIN files ON (files.id = entity_attachments.file_id) WHERE entity_attachments.entity_key = 'product' AND entity_attachments.entity_id = $id AND entity_attachments.is_default = 1 AND entity_attachments.deleted_at is null";
    $s3_result = mysqli_query($link,$q);
    if(mysqli_num_rows($s3_result)){
        $s3_result = mysqli_fetch_assoc($s3_result);
        $obj = new Aws();
        $s3_result['site_id'] = $_SESSION['OWNER']['id'];

        RollbarLogService::logTempFilesToRollbar($s3_result['is_temp'],$s3_result);
        $row['file_full_path'] = $obj->getPermanentUrl($s3_result ['path']);
        $row['file'] = $s3_result ['name'];
        $row['is_s3'] = true;
    }else if($row['file']){
        $row['file_full_path']=appendFullPath($current_site['id'],'product-images',$row['file']);
	}
    $row['stock_balance'] = $row['balance'];
	
    $ret['products'][] = $row;
}
//if(isPluginActivePure(92)) {
//    mysql_select_db($db_config['database'], $link);
//    $trackingNumbersMappedByProductId = [];
//    $trackingNumbersQuery = "SELECT * from tracking_numbers where store_id = $store_id AND quantity > 0";
//    $trackingNumbers = mysql_query($trackingNumbersQuery);
//    if(!$trackingNumbers) {
//        die('Invalid query: ' . mysql_error());
//    }
//    while ($row = mysql_fetch_assoc($trackingNumbers)) {
//        if($row['serial']) {
//            $number = $row['serial'];
//        } else if($row['lot'] && empty($row['expiry_date'])) {
//            $number = $row['lot'];
//        } else if ($row['expiry_date'] && empty($row['lot'])) {
//            $number = $row['expiry_date'];
//        } else if($row['expiry_date'] && $row['lot']) {
//            $number =$row['lot'].'_'.$row['expiry_date'];
//        }
//        if($number) {
//            $trackingNumbersMappedByProductId[$row['product_id']][$number] = $row['quantity'];
//        }
//    }
//    $ret['tracking_numbers'] = $trackingNumbersMappedByProductId;
//}


echo json_encode($ret);
