<?php
if (isset($_GET['debug']) && $_GET['debug']>0) {

    echo "<h1> Debug Is opened !! </h1>";
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}
require_once  dirname(dirname(dirname(__FILE__))) . '/configure.php';
require_once dirname(__DIR__).'/config/pure_function.php';
define('DS', DIRECTORY_SEPARATOR);
define('FPATH', dirname(dirname(__FILE__)) . DS . 'vendors' . DS . 'flourishlib-dev' . DS);
define('IROOT', dirname(__FILE__));
$_GET['image']=substr($_SERVER['REQUEST_URI'],0,strpos($_SERVER['REQUEST_URI'],'?'));


function fAutoload($className) {
    if (preg_match('/^f[A-Z]/', $className)) {
        require_once FPATH . $className . '.php';
    }
}

spl_autoload_register('fAutoload');

if (!empty($_GET['image'])) {
    $origImage = IROOT . DS . $_GET['image'];
} elseif (!empty($argv[1])) {
    if (strstr($argv[1], IROOT)) {
        $origImage = $argv[1];
    } else {
        $origImage = IROOT . DS . $argv[1];
    }
}

// Handle full image url
if( isset($_GET['image_link']) )
{
    $file = base64_decode($_GET['image_link']);
    $images_dir = '/tmp';
    $new_file = $images_dir . DS . parse_url(basename($file), PHP_URL_PATH);

    /**
     * @todo catch exception when copy fail
     *
     */
    $file_path = $file;
    // handle local and s3 files
    $is_local = is_null(parse_url($file, PHP_URL_SCHEME));
    if ($is_local) {
        $file_path = getcwd().$file;
    }
    if( $file && copy($file_path, $new_file) ) {
        $origImage = $new_file;
    } else {
        header($_SERVER["SERVER_PROTOCOL"] . " 404 Not Found");
        exit();
    }
}
if(isset($_GET['s3_image'])) {
    $_GET['s3_image']=$_SERVER['REDIRECT_origfile'];
    $DownloadFromS3 = true;
    $images_dir = '/tmp';
    $new_file = $images_dir . DS . $_GET['prefix'] . '_' . $_GET['s3_image'];
    $origImage=$new_file;
    $origImageCacheKey = $_GET['prefix'] . '_' . $_GET['s3_image'];
    $m = new Memcache();
    if (defined('AWS_LOGO_CACHE_SERVER')) {
        $m->addServer(AWS_LOGO_CACHE_SERVER, 11211);
    }
    // try to find logo on local machine
    if (!file_exists($new_file)) {
        // if logo file not in local machine try memcache
        if (defined('AWS_LOGO_CACHE_SERVER')) {
            $cacheContent = $m->get($origImageCacheKey);
            if($cacheContent && strlen($cacheContent)>0) {
                file_put_contents($new_file, $cacheContent);
                if(mime_content_type($new_file)=='text/plain') {
                $m->delete($origImageCacheKey);
                unlink($new_file);
                    $DownloadFromS3 = true;
                }else {
                    header('FROM-Cache: yes');
                    $DownloadFromS3 = false;
                }
            }else{
                $DownloadFromS3 = true;
            }
        } else {
            $DownloadFromS3 = true;

        }
    }else{

        if(mime_content_type($new_file)=='text/plain') {
            unlink($new_file);
            $DownloadFromS3 = true;
        }else{
            header('From-Disk: yes');
            $DownloadFromS3 = false;
        }
    }
    if ($DownloadFromS3) {
//todo remove this AWS_SUPPRESS_PHP_DEPRECATION_WARNING=true after upgrade aws package
        putenv("AWS_SUPPRESS_PHP_DEPRECATION_WARNING=true");
        require_once dirname(dirname(__DIR__)) . '/configure.php';
        require_once dirname(__DIR__) . '/config/pure_function.php';
        require_once dirname(__DIR__) . '/vendor/autoload.php';
        require_once dirname(__DIR__) . '/vendors/S3LogoUploader.php';
            S3LogoUploader::createS3Client();

            $res = S3LogoUploader::downloadLogo($_GET['prefix'] . '/' . $_GET['s3_image'], $new_file);
                header('FROM-S3: '.$res);
            // if logo downloaded from s3 cache it to local & memcache to use later ...
            if(isset($m) && $res){
                $m->set($origImageCacheKey, file_get_contents($new_file),0,86400 * 7);
            }
    }
}

$origImage=urldecode($origImage);


$change = false;
$width = 0;
$height = 0;
if (isset($_GET['w'])) {
    $width = intval($_GET['w']);
    $change = true;
} elseif (isset($argv[2])) {
    $width = intval($argv[2]);
    $change = true;
}

if (isset($_GET['h'])) {
    $height = intval($_GET['h']);
    $change = true;
} elseif (isset($argv[3])) {
    $height = intval($argv[3]);
    $change = true;
}
$cacheDir = IROOT . DS . 'img' . DS . 'cache';
if (!is_dir($cacheDir)) {
    mkdir($cacheDir, 0777, true);
}

$cacheFile = md5($origImage) . '_' . $width . 'x' . $height. (!empty($_GET['c']) ?'_'.$_GET[' c']:'');
$cacheImage = $cacheDir . DS . $cacheFile;

if(file_exists($cacheImage) && filesize($cacheImage)>1 && (($mime= mime_content_type($cacheImage))!= 'text/plain')){
    header('FROM-DISK2: yes');
    header("Content-Type: " . $mime);
    readfile($cacheImage);
    die();
}else{
    @unlink($cacheImage);
}
$cache_key="other_".$cacheFile;
if(isset( $_GET['prefix']) && $_GET['prefix']=="site-logos"){
    $cache_key="site-logos_".$cacheFile;
}elseif(isset( $_GET['prefix']) && $_GET['prefix']=="logos"){
    $cache_key="logos_".$cacheFile;
}




if (defined('AWS_LOGO_CACHE_SERVER') && (isset($_GET['s3_image']) and in_array($_GET['prefix'],["logos","site-logos"])) ) {
    $m = new Memcache();
    $m->addServer(AWS_LOGO_CACHE_SERVER, 11211);
    $cacheContent = $m->get($cache_key);
    $mimeContent = $m->get("mime_".$cache_key);
    if ((!$cacheContent || !$mimeContent) && file_exists($cacheImage) && filesize($cacheImage)>0) {
        if ($m->set($cache_key, file_get_contents($cacheImage), 0, 86400 * 7)) {
            $m->set("mime_".$cache_key, mime_content_type($origImage), 0, 86400 * 7);
            if ($m->get("total_cached_logo")) {
                $m->increment("total_cached_logo", 1);
            } else {
                $m->set("total_cached_logo", 1);
            }
        }
    }else{
        if($cacheContent) {
            file_put_contents($cacheImage, $cacheContent);
            if (mime_content_type($cacheImage) == 'text/plain') {
                $m->delete($cache_key);
                unlink($cacheImage);
            } else {
                header('Content-Type: ' . $mimeContent);
                header('FROM-CACHE2: yes');
                echo $cacheContent;
                die();
            }
        }
    }
    $m->close();
}


if (file_exists($origImage)) {

    if(strpos($_GET['image'],'.svg')){
        error_reporting(0);
        header("Content-Type: " . "image/svg+xml");
        if (!file_exists(@$cacheImage)) {
            readfile($origImage);
        }
        readfile($cacheImage);
        die();
    }

    try {
        $origImageObj = null;
        if (str_contains(strtolower(mime_content_type($origImage)), 'webp')) {
            $origImageObj = imagecreatefromwebp($origImage);
        } else {
            $origImageObj = new fImage($origImage);
        }
    } catch (Exception $e) {
        // $errorlogimg = true;
        header($_SERVER["SERVER_PROTOCOL"] . " 404 Not Found");
        exit();
    }


    if ($change) {
      


        if (file_exists(@$cacheImage)) {


            if (PHP_SAPI == 'cli') {
                echo realpath($cacheImage);
            } else {
                if (isset($origImageObj)) {
                    header("Content-Type: " . mime_content_type($origImage));
                } else {
                    header('Content-Type: image/png');
                }
                readfile($cacheImage);
            }
        } else {
            copy($origImage, $cacheImage);

            try {
                $newImgObj = null;
                if (str_contains(strtolower(mime_content_type($cacheImage)), 'webp')) {
                    $newImgObj = imagecreatefromwebp($cacheImage);
                } else {
                    $newImgObj = new fImage($cacheImage);
                }

                if (mime_content_type($cacheImage) == 'image/gif') {
                    if (!isset($_GET['h'])) {
                        $height = $width;
                    }
                    shell_exec("convert $cacheImage -coalesce -repage 0x0 -resize {$width}x{$height} $cacheImage");
                } else {


                    if (isset($_GET['c'])) {
                        /**
                         * @todo we can pass parameters to align image when crop
                         */
                        $imageWith = $origImageObj->getWidth();
                        $imageHeight = $origImageObj->getHeight();

                        if ($imageWith > $imageHeight) {
                            $newImgObj->resize(0, $height, true);
                        } else {
                            $newImgObj->resize($width, 0, true);
                        }
                        $newImgObj->crop($width, $height, 'center', 'center');

                    } else {
                          $newImgObj->resize($width, $height, true);
                    }
                    //$newImgObj->resize($width, $height, false);

                    $newImgObj->saveChanges();
                }
            } catch (Exception $e) {
                
                shell_exec("convert $cacheImage -coalesce -repage 0x0 -resize {$width}x{$height} $cacheImage");
            }
            if (defined('AWS_LOGO_CACHE_SERVER') && (isset($_GET['s3_image']) and in_array($_GET['prefix'],["logos","site-logos"])) ) {
                if ($m->set($cache_key, file_get_contents($cacheImage), 0, 86400 * 7)) {
                    $m->set("mime_" . $cache_key, mime_content_type($origImage), 0, 86400 * 7);
                    if ($m->get("total_cached_logo")) {
                        $m->increment("total_cached_logo", 1);
                    } else {
                        $m->set("total_cached_logo", 1);
                    }
                }
            }

            if (PHP_SAPI == 'cli') {
                echo realpath($cacheImage);
            } else {
                if (isset($newImgObj)) {
                    header("Content-Type: " . $newImgObj->getMimeType());
                } else {
                    header('Content-Type: image/png');
                }
                readfile($cacheImage);
            }
        }
    } else {
        if (PHP_SAPI == 'cli') {
            echo realpath($origImage);
        } else {
            header("Content-Type: " . $origImageObj->getMimeType());
            readfile($origImage);
        }
    }
} else {
    header($_SERVER["SERVER_PROTOCOL"] . " 404 Not Found");
    exit();
}
