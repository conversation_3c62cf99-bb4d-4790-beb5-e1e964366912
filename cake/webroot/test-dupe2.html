<!DOCTYPE html>
<!-- saved from url=(0047)https://desoky-fresh.daftra.com/test-dupe2.html -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
    <title>Duplicated Invoices</title>
    <style>
      :root {
        --bg: #0f172a;
        --card: #111827;
        --muted: #94a3b8;
        --text: #e5e7eb;
        --accent: #22c55e;
        --accent-2: #60a5fa;
        --border: #1f2937;
        --chip: #0b1220;
      }
      html,
      body {
        margin: 0;
        height: 100%;
        background: var(--bg);
        color: var(--text);
        font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto,
          Inter, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Apple Color Emoji",
          "Segoe UI Emoji";
      }
      .container {
        max-width: 1280px;
        margin: 0 auto;
        padding: 24px 16px 48px;
      }
      h1 {
        margin: 0 0 8px;
        font-size: 22px;
        font-weight: 700;
        letter-spacing: -0.01em;
      }
      .sub {
        color: var(--muted);
        font-size: 13px;
        margin-bottom: 16px;
      }
      .toolbar {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
        margin: 12px 0 16px;
      }
      .toolbar .left,
      .toolbar .right {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
      }
      .toolbar .spacer {
        flex: 1;
      }
      input[type="search"],
      select {
        background: var(--card);
        border: 1px solid var(--border);
        color: var(--text);
        padding: 8px 10px;
        border-radius: 8px;
        outline: none;
        font-size: 14px;
      }
      input[type="search"]:focus,
      select:focus {
        border-color: var(--accent-2);
        box-shadow: 0 0 0 3px #3b82f620;
      }
      button {
        background: linear-gradient(180deg, #1f2937, #0b1220);
        color: var(--text);
        border: 1px solid var(--border);
        padding: 8px 12px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
      }
      button.primary {
        background: linear-gradient(180deg, #16a34a, #065f46);
        border-color: #065f46;
      }
      .chip {
        padding: 2px 8px;
        font-size: 12px;
        border-radius: 999px;
        background: var(--chip);
        border: 1px solid var(--border);
        color: var(--muted);
      }
      .table-wrap {
        border: 1px solid var(--border);
        border-radius: 12px;
        overflow: clip;
        background: #0b1020;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        table-layout: auto;
      }
      thead th {
        position: sticky;
        top: 0;
        z-index: 2;
        background: #0c1224;
        text-align: left;
        font-size: 13px;
        color: var(--muted);
        padding: 10px 12px;
        border-bottom: 1px solid var(--border);
        white-space: nowrap;
      }
      thead th:nth-child(2) {
        width: auto;
        min-width: fit-content;
      }
      thead th.sortable {
        cursor: pointer;
      }
      tbody td {
        padding: 10px 12px;
        border-bottom: 1px solid var(--border);
        vertical-align: middle;
        word-wrap: break-word;
      }
      tbody td:nth-child(2) {
        white-space: nowrap;
      }
      tbody tr:hover {
        background: #0f1730;
      }
      a {
        color: #93c5fd;
        text-decoration: none;
      }
      a:hover {
        text-decoration: underline;
      }
      .filters {
        display: grid;
        grid-template-columns: repeat(4, minmax(0, 1fr));
        gap: 8px;
        margin: 8px 0 12px;
      }
      .filters input,
      .filters select {
        width: 100%;
      }
      .no-data {
        text-align: center;
        padding: 32px;
        color: var(--muted);
      }
      .footer {
        margin-top: 12px;
        color: var(--muted);
        font-size: 12px;
        display: flex;
        justify-content: space-between;
        gap: 8px;
        flex-wrap: wrap;
      }
      .count {
        color: var(--text);
      }
      @media (max-width: 900px) {
        .filters {
          grid-template-columns: repeat(2, minmax(0, 1fr));
        }
        thead th:nth-child(3),
        tbody td:nth-child(3),
        thead th:nth-child(4),
        tbody td:nth-child(4) {
          display: none; /* hide 'Login as' and 'Plan' on small screens */
        }
      }
      @media (max-width: 560px) {
        .filters {
          grid-template-columns: 1fr;
        }
        thead th:nth-child(6),
        tbody td:nth-child(6) {
          display: none; /* hide invoice number on very small screens */
        }
      }
      .pill {
        display: inline-flex;
        align-items: center;
        gap: 6px;
      }
      .pill svg {
        width: 14px;
        height: 14px;
        opacity: 0.8;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Duplicated Invoices</h1>
      <div class="toolbar">
        <div class="left">
          <input id="globalSearch" type="search" placeholder="Search all fields..." autocomplete="off">
          <button id="clearFilters">Clear filters</button>
        </div>
        <div class="spacer"></div>
        <div class="right">
          <button id="reload">Reload</button>
          <button id="exportCsv" class="primary">Export CSV (No URLs) </button>
        </div>
      </div>

      <div class="filters" id="columnFilters">
        <input data-field="subdomain" type="search" placeholder="Filter subdomain" autocomplete="off">
        <input data-field="plan" type="search" placeholder="Filter plan" autocomplete="off">
        <input data-field="invoice number" type="search" placeholder="Filter invoice number" autocomplete="off">
        <input data-field="date" type="search" placeholder="Filter date (YYYY-MM-DD)" autocomplete="off">
      </div>

      <div class="table-wrap">
        <table id="dataTable" aria-label="Duplicate invoices">
          <thead>
            <tr>
              <th class="sortable" data-sort="site_id">Site ID</th>
              <th class="sortable" data-sort="subdomain">Subdomain</th>
              <th>Login as</th>
              <th class="sortable" data-sort="plan">Plan</th>
              <th class="sortable" data-sort="invoice_id">Invoice ID</th>
              <th class="sortable" data-sort="invoice number">Invoice No</th>
              <th>Invoice</th>
              <th class="sortable" data-sort="date">Date</th>
              <th class="sortable" data-sort="subscription_id">
                Subscription ID
               ↓</th>
            </tr>
          </thead>
          <tbody id="tableBody">
          <tr><td colspan="9" class="no-data">Loading…</td></tr>
          </tbody>
        </table>
      </div>

      <div class="footer">
        <div>
          Rows: <span id="rowCount" class="count">773</span>
          <span id="rowTotal" class="muted"></span>
        </div>
        <div id="status" class="muted">Loaded 773 rows</div>
      </div>
    </div>

    <script>
      const JSON_PATH = "./duplicate_cron_invoices.json";

      // State
      let rawData = [];
      let viewData = [];
      let sortField = null;
      let sortDir = "asc"; // 'asc' | 'desc'
      const filters = {
        global: "",
        columns: {},
      };

      const els = {
        tableBody: document.getElementById("tableBody"),
        rowCount: document.getElementById("rowCount"),
        rowTotal: document.getElementById("rowTotal"),
        status: document.getElementById("status"),
        globalSearch: document.getElementById("globalSearch"),
        clearFilters: document.getElementById("clearFilters"),
        reload: document.getElementById("reload"),
        exportCsv: document.getElementById("exportCsv"),
        columnFilters: document.getElementById("columnFilters"),
        table: document.getElementById("dataTable"),
      };

      async function loadData(showStatus = true) {
        try {
          if (showStatus) setStatus("Loading data…");
          const res = await fetch(JSON_PATH, {
            cache: "no-store",
            headers: { "Content-Type": "application/json" },
          });
          if (!res.ok) throw new Error(`HTTP ${res.status}`);
          const data = await res.json();
          if (!Array.isArray(data)) throw new Error("JSON root is not an array");
          
          // Filter out rows with subscription_id = 0
          rawData = data.filter(row => {
            const subId = row.subscription_id;
            return subId !== 0 && subId !== "0" && subId !== '1' && subId !== '26' && subId !== null && subId !== undefined;
          });
          
          applyAll();
          setStatus(`Loaded ${rawData.length} rows`);
        } catch (err) {
          console.error(err);
          setStatus("Failed to load JSON. Check file path and CORS.");
          renderRows([]);
        }
      }

      function setStatus(msg) {
        els.status.textContent = msg || "";
      }

      function debounce(fn, ms) {
        let t;
        return (...args) => {
          clearTimeout(t);
          t = setTimeout(() => fn(...args), ms);
        };
      }

      function normalize(v) {
        if (v == null) return "";
        if (typeof v === "string") return v.toLowerCase();
        return String(v).toLowerCase();
      }

      function rowMatchesFilters(row) {
        // Column filters
        for (const [field, value] of Object.entries(filters.columns)) {
          if (!value) continue;
          const cell = normalize(row[field]);
          if (!cell.includes(value.toLowerCase())) return false;
        }
        // Global
        if (filters.global) {
          const q = filters.global.toLowerCase();
          const hit =
            Object.values(row)
              .map((v) => normalize(v))
              .join(" | ")
              .includes(q);
          if (!hit) return false;
        }
        return true;
      }

      function applyAll() {
        // Filter
        viewData = rawData.filter(rowMatchesFilters);

        // Sort
        if (sortField) {
          const dir = sortDir === "asc" ? 1 : -1;
          viewData.sort((a, b) => {
            const va = a?.[sortField];
            const vb = b?.[sortField];

            // Numeric compare if both are numbers
            const na = typeof va === "number" || /^\d+$/.test(String(va));
            const nb = typeof vb === "number" || /^\d+$/.test(String(vb));
            if (na && nb) return (Number(va) - Number(vb)) * dir;

            // Date compare (YYYY-MM-DD or ISO)
            const da = Date.parse(va);
            const db = Date.parse(vb);
            const bothDates = !Number.isNaN(da) && !Number.isNaN(db);
            if (bothDates) return (da - db) * dir;

            // String fallback
            return String(va).localeCompare(String(vb)) * dir;
          });
        }

        renderRows(viewData);
        els.rowCount.textContent = String(viewData.length);
        els.rowTotal.textContent =
          rawData.length && viewData.length !== rawData.length
            ? `/ ${rawData.length} total`
            : "";
      }

      function renderRows(rows) {
        if (!rows.length) {
          els.tableBody.innerHTML =
            '<tr><td colspan="9" class="no-data">No data</td></tr>';
          return;
        }

        const html = rows
          .map((r) => {
            const loginAsHtml = safeLinkHtml(r["Login as"]);
            const invoiceLinkHtml = safeLinkHtml(r["invoice"]);
            return `
            <tr>
              <td>${escapeHtml(r.site_id)}</td>
              <td>${escapeHtml(r.subdomain)}</td>
              <td>${loginAsHtml}</td>
              <td>${escapeHtml(r.plan)}</td>
              <td>${escapeHtml(r.invoice_id)}</td>
              <td>${escapeHtml(r["invoice number"])}</td>
              <td>${invoiceLinkHtml}</td>
              <td>${escapeHtml(r.date)}</td>
              <td>${escapeHtml(r.subscription_id)}</td>
            </tr>`;
          })
          .join("");
        els.tableBody.innerHTML = html;
      }

      // Accepts pre-rendered anchor tag strings from JSON (Login as, invoice)
      function safeLinkHtml(val) {
        if (typeof val !== "string") return "";
        // very light validation to avoid arbitrary HTML injection
        const m = val.match(
          /^<a\s+href=['"]https?:\/\/[^\s'"]+['"]\s*[^>]*>.*?<\/a>$/i
        );
        return m ? val : escapeHtml(val);
      }

      function escapeHtml(v) {
        const s = String(v ?? "");
        return s
          .replace(/&/g, "&amp;")
          .replace(/</g, "&lt;")
          .replace(/>/g, "&gt;")
          .replace(/"/g, "&quot;")
          .replace(/'/g, "&#39;");
      }

      function attachEvents() {
        // Global search
        els.globalSearch.addEventListener(
          "input",
          debounce((e) => {
            filters.global = e.target.value.trim();
            applyAll();
          }, 150)
        );

        // Column filters
        els.columnFilters.querySelectorAll("input,select").forEach((el) => {
          el.addEventListener(
            "input",
            debounce((e) => {
              const field = e.target.getAttribute("data-field");
              const val = e.target.value.trim();
              filters.columns[field] = val;
              applyAll();
            }, 150)
          );
        });

        // Clear filters
        els.clearFilters.addEventListener("click", () => {
          filters.global = "";
          filters.columns = {};
          els.globalSearch.value = "";
          els.columnFilters
            .querySelectorAll("input,select")
            .forEach((el) => (el.value = ""));
          applyAll();
        });

        // Reload
        els.reload.addEventListener("click", () => loadData());

        // Sorting
        document
          .querySelectorAll("thead th.sortable")
          .forEach((th) =>
            th.addEventListener("click", () => {
              const field = th.getAttribute("data-sort");
              if (sortField === field) {
                sortDir = sortDir === "asc" ? "desc" : "asc";
              } else {
                sortField = field;
                sortDir = "asc";
              }
              // update visual sort indicators
              document
                .querySelectorAll("thead th.sortable")
                .forEach((t) => (t.textContent = t.textContent.replace(/ ↑| ↓/g, "")));
              th.textContent =
                th.textContent.replace(/ ↑| ↓/g, "") +
                (sortDir === "asc" ? " ↑" : " ↓");
              applyAll();
            })
          );

        // CSV export
        els.exportCsv.addEventListener("click", () => {
          const csv = toCsv(viewData, [
            "site_id",
            "subdomain",
            "Login as",
            "plan",
            "invoice_id",
            "invoice number",
            "invoice",
            "date",
            "subscription_id",
          ]);
          const blob = new Blob([csv], { type: "text/csv;charset=utf-8" });
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = "duplicate_cron_invoices.csv";
          document.body.appendChild(a);
          a.click();
          a.remove();
          URL.revokeObjectURL(url);
        });
      }

      function toCsv(rows, headers) {
        const esc = (v) => {
          const s = String(
            v == null
              ? ""
              : typeof v === "string"
              ? v.replace(/<[^>]*>/g, "") // strip HTML in links
              : v
          );
          if (/[",\n]/.test(s)) return `"${s.replace(/"/g, '""')}"`;
          return s;
        };
        const head = headers.map(esc).join(",");
        const body = rows
          .map((r) => headers.map((h) => esc(r[h])).join(","))
          .join("\n");
        return head + "\n" + body;
      }

      // Init
      attachEvents();
      loadData();
    </script>
  

</body></html>