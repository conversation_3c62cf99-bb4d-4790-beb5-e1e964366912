$(document).ready(function() {
    var $input = $('[data-select-input="ajax-department"]');
    $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.multiple);
    $("#departments").change(function(){
        var val = $(this).val();
        if(val.includes('all') ){
            $("#departments")[0].selectize.setValue('all')
        }
   });
});

$(document).ready(function() {
    var $input = $('[data-select-input="ajax-branch"]');
    $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.multiple);
    $("#branches").change(function(){
        var val = $(this).val();
        if(val.includes('all') ){
            $("#branches")[0].selectize.setValue('all')
        }
    });
});

$(document).ready(function() {
    var $input = $('[data-select-input="ajax-type"]');
    $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.multiple);
    $("#selectType").change(function(){
        var val = $(this).val();
        if(val.includes('all') ){
            $("#selectType")[0].selectize.setValue('all')
        }
    });

    
});

$(document).ready(function() {
    var $input = $('[data-leave-type-select]');
    $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.multiple);
    $("#leaveType").change(function(){
        var val = $(this).val();
        if(val.includes('all') ){
            $("#leaveType")[0].selectize.setValue('all')
        }
    });
});

$(document).ready(function() {
    var $input = $('[data-select-input="ajax-request-type"]');
    $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.multiple);
    $("#requestType").change(function(){
        var val = $(this).val();
        if(val.includes('all')){
            $("#requestType")[0].selectize.setValue('all')
        }
    });
});

// Multi-cycle approval configuration code
$(document).ready(function() {
    var selectedApprovers;
    var selectedTransactionsIds = [];
    var $table = $('[data-subform-approval-cycle-levels]');
    var checkedDirectManager;
    var checkedAssignedManager;
    var checkedOtherEmployess;
    window.selectizeTimeout = {};
    window.instance = $table.subform({
        addBtn: '[data-row-add-approval-cycle-levels]',
        template: '[data-subform-template-approval-cycle-levels]',
        removeBtn: '[data-cell-remove-approval-cycle-levels]',
        dragBtn: false,
        onAddRow: function ($row, lastIndex) {
            var totalRows = $table.find('[data-select-input="approvers"]').length;
            if(totalRows >= 10){
                $.toast(window.translationss.ApproversLimitErrorMsg, 'danger');
                return false;
            }
            return true;
        },
        afterAddRow: function ($row, lastIndex) {
            // Select input approvels 
            var $input = $('[data-select-input="approvers"]');
            $input.selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.multiple, {
                valueField: "id",
                labelField: "text",
                load: loadData,
                onChange: changeData,
                onDropdownOpen: openDropdownSelectize,
                onItemRemove: itemRemoveSelectize,
                onClear: itemRemoveSelectize,
                plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.multiple.plugins, {
                    'template': {
                        'name': 'user'
                    },
                    'clear-button': {}
                }),
                render: {
                    option: function (data, escape) {
                        if(data.id == '__clear__') {
                            return '<div title="'+ escape(data.text) +'" class="option">' + escape(data.text) + '</div>';
                        }

                        return '<div title="'+ escape(data.text) +'" class="option"><span class="thumb-text-group"><span class="thumb thumb-sm">' + (data.img && data.img.indexOf('account-def-md.png') < 0 ? '<img src="' + data.img + '" loading="lazy" />' : escape(data.text).charAt(0).toUpperCase()) + '</span><span class="thumb-text"><p>' + escape(data.text) + '</p></span></span></div>';
                    },
                    item: function (data, escape) {
                        return '<div title="'+ escape(data.text) +'" class="item"><span class="thumb-text-group"><span class="thumb thumb-sm">' + (data.img && data.img.indexOf('account-def-md.png') < 0 ? '<img src="' + data.img + '" loading="lazy" />' : escape(data.text).charAt(0).toUpperCase()) + '</span><span class="thumb-text"><p>' + escape(data.text) + '</p></span></span></div>';
                    },
                },
            }));
            getCheckedDirectManager();
            getCheckedAssignedManager();
            getCheckedOtherEmployess();
        },
        onRemoveRow: function ($row, lastIndex) {
            return true;
        },
        afterRemoveRow: function ($row, lastIndex) {
            
        },
    });
    
    $table.on('subform:add', function (e, $row, lastIndex) {
    });

    $table.on('subform:remove', function (e, $row, lastIndex) {
    });

    // Function to get the currently checked value
    function getCheckedApprovalType() {
        let checkedApprovalType = $("input[name='approval_type']:checked").val();
        if(checkedApprovalType === 'single_cycle') {
            $('.dynamic-notes').text(window.constants.single_cycle_notes);
            $("[data-row-add-approval-cycle-levels]").addClass('d-none');
            $("[data-subform-approval-cycle-levels] tbody tr").each(function (index,input){
                if(index > 0) {
                    $(input).remove();
                }
            });
        } else {
            $("[data-row-add-approval-cycle-levels]").removeClass('d-none');
            $('.dynamic-notes').text(window.constants.multi_cycle_notes);

            $("[data-subform-approval-cycle-levels] tbody tr").each(function (index,input){
                if(index == 0) {
                    $("[data-row-add-approval-cycle-levels]").click();
                    $(".subform-cell-remove").remove();
                    $(".subform-cell-actions").html(`
                        <button class="subform-cell-no-action" type="button">
                        <i class="mdi mdi-lock"></i>
                        </button>
                    `);
                }
            });
        }
    }

    getCheckedApprovalType();
    
    // Get value on change
    $("input[name='approval_type']").on('change', function () {
        getCheckedApprovalType();
        $('[data-select-input="approvers"]')[0].selectize.setValue('');
        resetSelectizeValue();
    });

    // Direct Manager
    function getCheckedDirectManager() {
        checkedDirectManager = document.getElementById('directManagerSwitch').checked;
        setTimeout(() => {
            const optionDirectManager = 'has_direct_manager';
            const optionDirectManagerData = { id: optionDirectManager, text: window.translationss.DirectManager || 'Direct Manager' };
            $('[data-select-input="approvers"]').each(function () {
                let selectize = this.selectize;
                if (!selectize) return; // skip if Selectize not initialized
                if (checkedDirectManager) {
                    // Add option if it doesn't exist
                    if (!selectize.options[optionDirectManager]) {
                        selectize.addOption(optionDirectManagerData);
                    }
                } else {
                    // Remove option from dropdown
                    selectize.removeOption(optionDirectManager);
                }
            });
        }, 10);
    } 
    
    // on page load
    getCheckedDirectManager();
    
    //  on change
    $('#directManagerSwitch').on('change', function () {
        getCheckedDirectManager();
    });
    
    // reset selectize value 
    function resetSelectizeValue() {
        $('[data-select-input="approvers"]')[0].selectize.setValue('');
        var $input = $('[data-select-input="approvers"]');
        var defaultOptions = Object.values($input[0].selectize.options).filter(function(opt) {
            return opt.id === 'has_direct_manager' || opt.id === 'has_assigned_department_manager';
        });
        
        $input.each(function () {
            this.selectize.clearOptions();
            this.selectize.clearUnselectedOptions();
            this.selectize.addOption({id: '__clear__', text: window.constants.None, $order: -1 });
        });
      
        // Re-add default options after clearing
        defaultOptions.forEach(function(opt) {
            if(selectedApprovers) {
                if(!selectedApprovers.includes(opt.id)) {
                    $input.each(function () {
                        this.selectize.addOption(opt);
                    });
                } 
            }
            else {
                $input.each(function () {
                    this.selectize.addOption(opt);
                });
            }
        });
    }
    
    // Assigned Manager
    function getCheckedAssignedManager() {
        checkedAssignedManager = document.getElementById('assignedManagerSwitch').checked;
        setTimeout(() => {
            const optionAssignedManager = 'has_assigned_department_manager';
            const optionAssignedManagerData = { id: optionAssignedManager, text: window.translationss.AssignedManager || 'Assigned Manager' };
            $('[data-select-input="approvers"]').each(function () {
                let selectize = this.selectize;
                if (!selectize) return; // skip if Selectize not initialized
                if (checkedAssignedManager) {
                    // Add option if it doesn't exist
                    if (!selectize.options[optionAssignedManager]) {
                        selectize.addOption(optionAssignedManagerData);
                    }
                } else {
                    // Remove option from dropdown
                    selectize.removeOption(optionAssignedManager);
                }
            });
        }, 10);
    } 
    
    // on page load
    getCheckedAssignedManager();
    
    $('#assignedManagerSwitch').on('change', function () {
        getCheckedAssignedManager();
    });
    
    function getCheckedOtherEmployess() {
        checkedOtherEmployess = document.getElementById('otherEmployeesSwitch').checked;
    } 
    
    getCheckedOtherEmployess();
    
    $('#otherEmployeesSwitch').on('change', function () {
        getCheckedOtherEmployess();
        if(!checkedOtherEmployess) {
            resetSelectizeValue();
        }
    });

    function handleConfigurationUI() {
        const configurationType = $("input[name='configuration_type']:checked").val();
        const $typeInput = $('[data-type-select]');
        const typeValue = $typeInput.val();

        // Request type block
        if (configurationType === 'leaves') {
            $("#requestType").parents('.form-group').addClass('d-none');
            $("#selectType").parents('.form-group').removeClass('d-none');
        } else {
            $("#requestType").parents('.form-group').removeClass('d-none');
            $("#selectType").parents('.form-group').addClass('d-none');
        }

        // Leave type block — only show if config is 'leaves' AND type is HALF_LEAVE or LEAVE
        const showLeaveType =
            configurationType === 'leaves' && 
            (typeValue.includes(window.constants.HALF_LEAVE) || typeValue.includes(window.constants.LEAVE) || typeValue.includes("all"))
               
        if (showLeaveType) {
            $("#leaveType").parents('.form-group').removeClass('d-none');
        } else {
            $("#leaveType").parents('.form-group').addClass('d-none');
        }
    }

    handleConfigurationUI();

    // Bind to events
    $("input[name='configuration_type']").on('change', handleConfigurationUI);
    $('[data-type-select]').on('change', handleConfigurationUI);

    // Select input approvers 
    var $input = $('[data-select-input="approvers"]');
    
    function loadData(query, callback) {
        var url = `/v2/owner/staff/search?allow_suspended=1&allow_inactive=1&term=__q__&selected=${selectedApprovers}&_type=query&q=__q__`;
       
        // Cache default options before clearing
        var defaultOptions = Object.values($input[0].selectize.options).filter(function(opt) {
            return opt.id === 'has_direct_manager' || opt.id === 'has_assigned_department_manager';
        });
        
        if (!this.$input.attr('multiple')) {
            this.clearOptions();
        } else {
            this.clearUnselectedOptions();
        }
       
        this.addOption({id: '__clear__', text: window.constants.None, $order: -1 });
        
        // Re-add default options after clearing
        var self = this;
        defaultOptions.forEach(function(opt) {
            if(selectedApprovers) {
                if(!selectedApprovers.includes(opt.id)) {
                    self.addOption(opt);
                } 
            }
            else {
                self.addOption(opt);
            }
        });
        
        // Don't make the request
        if (!query.length || !checkedOtherEmployess) return callback();
        
        $.ajax({
            url: url.replaceAll('__q__', encodeURIComponent(query)),
            type: "GET",
            dataType: 'json',
            error: function () {
                callback();
            },
            success: function (response) {
                if (response.results) {
                    callback(response.results);
                } else {
                    callback();
                }
            },
        });
    }
    
    function changeData(value){
        var $input = $('[data-select-input="approvers"]');
        var selectedArr = [];
        $input.each(function (index,input){
            var value = input.selectize.getValue();
            selectedArr = selectedArr.concat(Array.isArray(value) ? value : [value]);
        });
        selectedApprovers = selectedArr;
    }
    
    function openDropdownSelectize($dropdown){
        let selectize = this;
        const selectedItems = selectize.items;
        const optionDirectManager = 'has_direct_manager';
        const optionAssignedManagerData = 'has_assigned_department_manager';
        if(selectedApprovers) {
            // If it's already selected, remove from dropdown options
            if (selectedApprovers.includes(optionDirectManager) && !selectedItems.includes(optionDirectManager)) {
                selectize.removeOption(optionDirectManager); // removes from dropdown only
            } 
            if (selectedApprovers.includes(optionAssignedManagerData) && !selectedItems.includes(optionAssignedManagerData)) {
                selectize.removeOption(optionAssignedManagerData); // removes from dropdown only
            } 
            selectize.addOption({id: '__clear__', text: window.constants.None, $order: -1 });
        }
    }
    
    function itemRemoveSelectize(value){
        if (value === 'has_direct_manager' && checkedDirectManager) {
            $('[data-select-input="approvers"]').each(function () {
                let selectize = this.selectize;
                selectize.addOption({ id: 'has_direct_manager', text: window.translationss.DirectManager || 'Direct Manager' });
            });
        } 
        if(value === 'has_assigned_department_manager' && checkedAssignedManager) {
            $('[data-select-input="approvers"]').each(function () {
                let selectize = this.selectize;
                selectize.addOption({ id: 'has_assigned_department_manager', text: window.translationss.AssignedManager || 'Assigned Manager' });
            }); 
        }
    }
    
    $input.selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.multiple, {
        valueField: "id",
        labelField: "text",
        load: loadData,
        onChange: changeData,
        onDropdownOpen: openDropdownSelectize,
        onItemRemove: itemRemoveSelectize,
        onClear: itemRemoveSelectize,
        plugins: Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.multiple.plugins, {
            'template': {
                'name': 'user'
            },
            'clear-button': {}
        }),
        render: {
            option: function (data, escape) {
                if(data.id == '__clear__') {
                    return '<div title="'+ escape(data.text) +'" class="option">' + escape(data.text) + '</div>';
                }

                return '<div title="'+ escape(data.text) +'" class="option"><span class="thumb-text-group"><span class="thumb thumb-sm">' + (data.img && data.img.indexOf('account-def-md.png') < 0 ? '<img src="' + data.img + '" loading="lazy" />' : escape(data.text).charAt(0).toUpperCase()) + '</span><span class="thumb-text"><p>' + escape(data.text) + '</p></span></span></div>';
            },
            item: function (data, escape) {
                return '<div title="'+ escape(data.text) +'" class="item"><span class="thumb-text-group"><span class="thumb thumb-sm">' + (data.img && data.img.indexOf('account-def-md.png') < 0 ? '<img src="' + data.img + '" loading="lazy" />' : escape(data.text).charAt(0).toUpperCase()) + '</span><span class="thumb-text"><p>' + escape(data.text) + '</p></span></span></div>';
            },
        },
    }));
    
      if (window.approvalLevelsData) {
        // Convert to array if it's an object with numeric keys
        const approvalLevels = Array.isArray(window.approvalLevelsData) ? 
            window.approvalLevelsData : 
            Object.values(window.approvalLevelsData);
        
        if (approvalLevels.length > 1) {
            for (let i = 0; i < approvalLevels.length - 2; i++) {
                $("[data-row-add-approval-cycle-levels]").click();
            }
        }
        
        approvalLevels.forEach((level, index) => {
            if (level && level.employees_obj) {
                level.employees_obj.forEach(employee => {
                    setTimeout(() => {
                        let selectize = $('[data-select-input="approvers"]')[index].selectize;
                        if (selectize) {
                            selectize.addOption({
                                id: employee.id,
                                text: `${employee.full_name} (${employee.email_address}) #${employee.code}`,
                                img: employee.avatar_url
                            });
                        }
                    }, 100);
                });
                
                delete level.employees_obj;
            }
            
            setTimeout(() => {
                const selectize = $('[data-select-input="approvers"]')[index].selectize;
                if (selectize && level) {
                    selectize.setValue(Array.isArray(level) ? level : Object.values(level));
                }
            }, 200);
        });
    }
});

// Set up translations object if it doesn't exist
window.translationss = window.translationss || {};

// Set up constants object if it doesn't exist
window.constants = window.constants || {};
