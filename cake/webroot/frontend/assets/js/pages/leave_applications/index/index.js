$(document).ready(function () {
    google.load("visualization", "1", {
        packages: ["corechart"]
    });
    function drawing(i){
        var data = google.visualization.arrayToDataTable([
            ['Content', 'Title'],
            ['Videos', parseInt($(".chart").eq(i).attr("taken"))],
            ['Photos', parseInt($(".chart").eq(i).attr("available"))],

        ]);
        var options = {
            title: "",
            pieHole: 0.8,
            pieSliceBorderColor: "none",
            width: $(".chart").eq(i).parent().width(),
            height: 150,
            colors: ['#E4EBF2', '#8CD921' ],
            legend: {
                position: "none"
            },
            pieSliceText: "none",
            tooltip: {
                trigger: "none"
            }

        };
        var chart = new google.visualization
            .PieChart($(".chart").eq(i).get(0));
        chart.draw(data, options);
    }
    function jsIsset(accessor){
        try {
            // Note we're seeing if the returned value of our function is not
            // undefined or null
            return accessor() !== undefined && accessor() !== null
        } catch (e) {
            // And we're able to catch the Error it would normally throw for
            // referencing a property of undefined
            return false
        }
    }

    if(!jsIsset(leaves_balance_data)){
        var leaves_balance_data = null
    }

    if(!jsIsset(changeAvailableBalance)){
        function changeAvailableBalance(staff_element_id = "staff_id", callback = null) {
            let staffIdJqueryElement = $(staff_element_id)
            let staff_id = staffIdJqueryElement.length ? $(staff_element_id).val() : null;
            if(staffIdJqueryElement.length != 0 && !$(staff_element_id).val()){
                console.log("staffIdJqueryElement" ,"staffIdJqueryElement")
                return;
            }
            let from_date = $("#date_from").attr('value');
            let to_date = $("#date_to").val();
            let leave_type_id = $("#leave_type_id").val() ?? null;
            let leave_balance_modal_btn = $("#balance-modal");

            $.ajax({

                url: `/v2/owner/reports/attendance/attendance_balance_for_auth${ !! staff_id ? "?staff_id=" + staff_id : ""}`,

                type: "GET",
                success: function(result) {
                    leaves_balance_data = JSON.parse(result)
                    if(callback){
                        callback();
                    }
                }
            });
        }
    }

    function drawComponents() {
        let leave_types_components = [];
        if(!leaves_balance_data || !leaves_balance_data['leaves']){
            return
        }
        for (const key in leaves_balance_data['leaves']) {
            if (Object.hasOwnProperty.call(leaves_balance_data['leaves'], key)) {
                $(".percentage-charts-components-container").addClass("loading");
                const element = leaves_balance_data['leaves'][key];
                leave_types_components.push(getLeaveTypeComponent(element));
            }
        }
        if(leave_types_components.length == 0){
            leave_types_components.push('<div class="col-lg-12 mb-8 mt-6"><div class="chart-container d-flex align-items-center justify-content-center"><h4>' + translations?.noLeavesForEmployee || '' +'</h4></div></div>');
        }
        $('#percentage-charts-components').html(leave_types_components.join(''));
        for (var i = 0; i < $(".chart").length; i++) {
            drawing(i)
        }
        if(leave_types_components.length < 3){
            $('#percentage-charts-components').css("justify-content","center");
        }
        else{
            $('#percentage-charts-components').css("justify-content","flex-start");
        }

        setTimeout(() => {
            $(".percentage-charts-components-container").removeClass("loading");
        }, 600);
    }

    changeAvailableBalance("#staff_id", function() {
        drawComponents()
    })

    document.getElementById('staff_leave_balance_modal').addEventListener('hide.bs.modal', function (event) {
        leaves_balance_data = null
        $("#leave_balance_staff_id").get(0).selectize.setValue('')
        $('#percentage-charts-components').html('')
    })


    $("#leave_balance_staff_id").on("change", function(e) {
        if ($(this).val()){
            $(".percentage-charts-components-container").addClass("loading");
            changeAvailableBalance("#leave_balance_staff_id", function() {
                drawComponents()
            })
        }else{
            $('#percentage-charts-components').html(null);
        }
    })

    $("#balance-modal").on("click", function(e) {
        drawComponents()
    })

    function getLeaveTypeComponent(leave_type_data) {
        let remaining_value = leave_type_data.remainingValue;
        return `<div class="col-lg-4">
            <div class="chart-container d-flex align-items-center justify-content-center">
                <h4>${leave_type_data.leave_type_name}</h4>
            <div class="chart" available="${remaining_value <= 0 || remaining_value}" taken="${leave_type_data.leaves}"></div>
            <div class="available-val leave-application-val ${remaining_value <= 0 ? 'zero-val' : ''}">${remaining_value}</div>
            <p>(${translations.takenLeaves + ' '}<span class="taken-val"  style="color:#202124;">${leave_type_data.leaves}</span>)</p>
            <p style="margin-top:4px;">(${translations.futureLeaves + ' '}<span class="taken-val"  style="color:#202124;">${leave_type_data.future || 0}</span>)</p>
            </div>
        </div>`;
    }

    // Auto apply filter on change or clear
    $(document).on('change', '#leave_application select, #leave_application input:not([type="text"])', submitForm);
    $(document).on('click', '#leave_application .btn-input-clear', submitForm);

    function submitForm() {
        $(this).closest('form#leave_application').submit();
    }
})