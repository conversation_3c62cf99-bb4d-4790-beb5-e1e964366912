[data-bs-theme="dark"] {
  --bs-white: #27282c;
  --bs-light: #313238;
  --bs-black: #ffffff;
  --bs-light-3: #adb0cb;
  --bs-dark-3: var(--bs-light-3);
  --bs-dark: var(--bs-light);
  --bs-dark-2: var(--bs-white);
  --bs-body-color: var(--bs-light-3);

  //   Sidebar
  body {
    --bs-body-bg: #3a3e44;
  }
  .sidebar-list-item-btn * {
    --bs-white: var(--bs-light-3);
  }
  .sidebar-list-item-sublist-item-link:hover,
  .sidebar-list-item-sublist-item-link:focus {
    background-color: rgba(255, 255, 255, 0.03);
  }
  .sidebar button.sidebar-list-item-btn::after {
    color: var(--bs-light-3);
  }

  // Buttons
  .btn-link {
    --#{$prefix}btn-color: rgba(
      var(--#{$prefix}link-color-rgb),
      var(--#{$prefix}link-opacity, 1)
    );
    &:hover {
      --#{$prefix}link-color-rgb: var(--#{$prefix}link-hover-color-rgb);
      color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
    }
  }

  .btn {
    --bs-btn-hover-bg: #40464e;
    --bs-btn-hover-border-color: #40464e;
  }
  .btn-search,
  .btn-dots {
    --bs-btn-bg: #303842;
    --bs-btn-border-color: #303842;
    color: var(--bs-light-3);
  }

  .btn-search:hover,
  .btn-dots:hover,
  .btn-search:active,
  .btn-dots:active,
  .btn-dots.show {
    --bs-btn-bg: #303842;
    --bs-btn-border-color: #303842;
    color: var(--bs-light-3);
  }

  //   Card
  .card {
    --bs-card-bg: var(--bs-white);
    --bs-card-cap-bg: var(--bs-light);
    // 333340
    --bs-card-cap-color: var(--bs-light-3);
  }

  //  Table
  .listing-table thead tr {
    border-bottom: 1px solid var(--bs-light);
  }
  .listing-table thead tr th {
    --bs-dark-3: var(--bs-light-3);
  }

  //   Selectize and dropdown
  .form-control{
    background-color: var(--bs-card-bg);
    color: var(--bs-black);
  }
  .link-black{
    color: var(--bs-black) !important;
  }
  .selectize-dropdown {
    background-color: var(--bs-light);
    color: var(--bs-light-3);
  }
  .selectize-control.form-control.plugin-search-helper-text
    .search-helper-text {
    color: var(--bs-light-3);
  }

  .selectize-control.form-control
    .selectize-dropdown
    .option.active:not(.selected) {
    background-color: var(--bs-white);
    color: var(--bs-light-3);
  }
  .form-group.floating-group.show-label .floating-label::after,
  .form-group.floating-group.has-value .floating-label::after {
    background-color: var(--bs-light);
  }
  .input-group.input-group-merge::after {
    border: 1px solid var(--input-border-color, var(--bs-light-3));
  }

  .dropdown-item {
    --bs-dropdown-link-color: var(--bs-light-3);
    --bs-dropdown-link-hover-color: var(--bs-light-3);
  }

  .selectize-input {
    input {
      color: var(--bs-black);
    }
  }
}
