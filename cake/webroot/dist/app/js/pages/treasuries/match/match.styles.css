#js-app{height:100%}.app{height:calc(100vh - 44px)}.transactions-content{display:flex;height:100%;margin:0 30px;position:relative}@media(max-width:800px){.transactions-content{display:block}}.item-link{color:var(--color-subtitle)}.item-link:hover{color:var(--color-default);text-decoration:none}.bank-transactions,.system-transactions{background:#fff;height:98%;overflow:hidden;position:relative}.bank-transactions{width:45%}@media(max-width:800px){.bank-transactions{width:100%}}.system-transactions{border-left:1px solid #ccc;position:relative;width:65%}@media(max-width:800px){.system-transactions{width:100%}}.system-transactions .bank-header{display:none}[dir=rtl] .system-transactions{border-left:0;border-right:1px solid #ccc}.details-bar{font-size:16px;min-height:45px;padding:5px 10px}.details-bar,.details-bar>div,.details-bar>div>div{align-items:center;display:flex}.details-bar>div>*,.details-bar>div>div>*{margin:0 4px}.badge-number{background:var(--color-orange);border-radius:2px;color:#fff;height:20px;line-height:20px;margin-right:7px;min-width:20px;padding:0 6px;text-align:center;width:auto}.transaction-title{font-size:16px;margin-bottom:0}.details-currency,.details-txt{color:var(--color-subtitle)}[dir=rtl] .details-currency{order:1}.details-total-amount{color:var(--color-orange);font-size:16px;font-weight:700}[dir=rtl] .details-total-amount{order:2}.filter-bar{background-color:#f1f4f8;border:1px solid #ededed;border-left:0;border-right:0;min-height:50px;position:relative}.filter-bar:hover{border-color:#d7d7d7}.filter-bar .ui-input:focus{box-shadow:0 0 0 1px var(--color-default)}[dir=ltr] .filter-bar .l-filter-input-box .l-filter-input-label{left:30px}[dir=rtl] .filter-bar .l-filter-input-box .l-filter-input-label{right:30px}.filter-items{margin:5px;padding-left:50px}[dir=rtl] .filter-items{padding-left:0;padding-right:50px}.filter-item{background:var(--color-default);border-radius:5px;color:#fff;display:inline-block;line-height:1.2;margin:0 3px;padding:2px 7px}.filter-item-remove{cursor:pointer}.filter-bar .wrapper-input{background-color:#f1f4f8;border:0;color:#7a7a7a;font-size:17px;height:100%;line-height:50px;min-height:50px;outline:none;padding:0 45px;width:100%}.filter-bar .wrapper-input.remove-padd{padding-left:0}[dir=rtl] .filter-bar .wrapper-input.remove-padd{padding-left:45px;padding-right:0}.filter-bar i{color:#000;font-size:20px;opacity:.2;position:absolute;top:50%;transform:translateY(-50%);z-index:10}.filter-bar i.filter-icon{cursor:pointer;font-size:30px;opacity:.6;padding:2px;right:10px}.filter-bar i.search-icon{left:10px}.filter-bar:hover i.filter-icon{background:#d7d7d7}[dir=rtl] .filter-bar i.filter-icon{left:10px;right:auto}[dir=rtl] .filter-bar i.search-icon{left:auto;right:10px}.transaction-wrapper{height:calc(100% - 152px)}.transaction-records{height:100%;overflow:auto;position:relative;scrollbar-color:#999 #f1f1f1}.transaction-records::-webkit-scrollbar{width:5px}.transaction-records::-webkit-scrollbar-track{background:#f1f1f1}.transaction-records::-webkit-scrollbar-thumb{background:#999}.transaction-records::-webkit-scrollbar-thumb:hover{background:#555}.scrolling-loader{padding:15px 0;text-align:center}.iframe-wrapper{display:flex;flex-direction:column;height:100%;position:relative}.transaction-record{align-items:center;border-bottom:1px solid #d5dbe2;box-shadow:0 1px 0 0 #d5dbe2;display:flex;justify-content:space-between;min-height:86px;padding:20px}.transaction-record.bank-item{cursor:auto;display:none}.transaction-record .date-time{display:flex;flex-direction:column}.transaction-record .date-time .date{color:var(--color-default);font-size:16px;font-weight:600}.transaction-record .date-time .ref{display:flex;font-size:14px}.transaction-record .description{-webkit-line-clamp:2;-webkit-box-orient:vertical;color:var(--color-subtitle);display:-webkit-box;font-size:12px;max-width:50%;overflow:hidden;text-overflow:ellipsis}.transaction-record .description .info{align-items:center;display:flex;margin-bottom:0;margin-top:5px}.transaction-record .description .info span{margin:0 8px}.transaction-record .description .info span:first-child{margin:0}.transaction-record .description .info i{font-size:14px}.transaction-record .amount{color:var(--color-save);font-size:16px;font-weight:700}.transaction-record.selected{background:#eef9fd;border-bottom:1px solid var(--color-save)}.transaction-record.not-done{cursor:not-allowed;pointer-events:none}.transaction-record i.edit-icon{color:var(--color-default);cursor:pointer;opacity:.2}.transaction-record i.edit-icon:hover{color:#001aff;opacity:.6}.transaction-record .match-btn{overflow:hidden}.transaction-record.desktop-record .action:first-child{width:5%}.transaction-record.desktop-record .action:last-child{width:2%}.transaction-record.desktop-record .date-time{width:15%}.transaction-record.desktop-record .amount{text-align:right;width:15%}.transaction-record.desktop-record .description{width:45%}.amount--red{color:var(--color-danger)!important}[dir=rtl] .transaction-record.desktop-record .amount{text-align:left}[dir=rtl] .transaction-record .date-time .ref span:first-child{order:2}[dir=rtl] .transaction-record .date-time .ref span:last-child{order:1}.mobile-record{display:none}.action-btns{bottom:0;display:flex;position:absolute;width:100%}.action-btn{border:0;color:#fff;cursor:pointer;font-size:20px;height:55px;position:relative;width:100%}.action-btn:hover{opacity:.9}.action-btn .btn-txt{font-size:17px}.finish-matching-btn{background-color:var(--color-green)}.add-transaction-btn{align-items:center;background-color:var(--color-orange);display:flex;justify-content:space-between;padding:0 15px}.add-transaction-btn .btn-amount{font-size:20px}[dir=rtl] .add-transaction-btn .btn-amount{direction:ltr;text-align:right}.match-transaction-btn{align-items:center;background-color:var(--color-primary);display:flex;justify-content:space-between;padding:0 15px}.match-transaction-btn .btn-amount{font-size:26px;font-weight:700}.btn-dimmed{background-color:#f1f4f8;color:#b9bbcd;pointer-events:none}.styled-checkbox{opacity:0;position:absolute}.styled-checkbox+label{cursor:pointer;margin-top:2px;padding:0;position:relative}.styled-checkbox+label:before{background:#fff;border:2px solid #d5dbe2;content:"";display:inline-block;height:20px;vertical-align:text-top;width:20px}.styled-checkbox:hover+label:before{background:var(--color-primary)}.styled-checkbox:focus+label:before{box-shadow:0 0 0 3px rgba(0,0,0,.12)}.styled-checkbox:checked+label:before{background:var(--color-primary)}.styled-checkbox:disabled+label{color:#b8b8b8;cursor:auto}.styled-checkbox:disabled+label:before{background:#ddd;box-shadow:none}.styled-checkbox:checked+label:after{background:#fff;box-shadow:2px 0 0 #fff,4px 0 0 #fff,4px -2px 0 #fff,4px -4px 0 #fff,4px -6px 0 #fff,4px -8px 0 #fff;content:"";height:2px;left:5px;position:absolute;top:10px;transform:rotate(45deg);width:2px}.shimmer{margin:50px auto;max-width:824px}.shimmer__wrapper{background-color:#fff;border-radius:4px;overflow:hidden}.shimmer__item{background:#fff;display:flex;flex-wrap:wrap;min-height:79px;padding:14px 32px 13px}.shimmer__item:not(:last-child){border-bottom:1px solid rgba(40,45,58,.1)}.shimmer__item>div{align-items:center;display:flex;flex-basis:100%;flex-wrap:wrap;max-width:100%}.shimmer__block{animation-delay:.5s;animation-duration:2s;animation-fill-mode:forwards;animation-iteration-count:infinite;animation-name:placeHolderShimmer;animation-timing-function:linear;background-color:#ededed;background-image:linear-gradient(90deg,#ededed 14.36%,#d7d6d6 56.29%,#ededed);background-repeat:no-repeat;background-size:244px 104px;border-radius:10px;height:19px;margin:3px 6px 3px 0;position:relative;width:100%}@keyframes placeHolderShimmer{0%{background-position:-300px 0}20%{background-position:-300px 0}80%{background-position:calc(100% + 300px) 0}to{background-position:calc(100% + 300px) 0}}.alert-msg{align-items:center;display:flex;justify-content:space-between;margin:10px 30px;padding:15px 20px}.alert-msg span{cursor:pointer;font-size:18px}.alert-msg.success{background-color:#d4edda;color:var(--color-success)}.alert-msg.danger{background-color:#f7d7da;color:#721c24}.spinner-loading{background-color:#fff;bottom:0;left:0;position:absolute;right:0;top:0;width:100%;z-index:20}.spinner-loading i{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);z-index:30}button.active{background:red}.filters-inputs{background:#fcfcfc;position:relative;z-index:10}.edit-title{background:#fff;box-shadow:1px 1px 3px #b5b5b5;font-size:20px;padding:10px}.edit-title span{font-weight:400}.add-cancel-btn{background-color:#dbe7f3;border:1px solid #c3c3c3;bottom:22px;color:#212529;min-height:34px;padding:5px 10px;position:absolute;right:107px}.ReactModal__Overlay{background-color:rgba(0,0,0,.75)!important;z-index:2000}.ReactModal__Content{left:50%!important;top:50%!important;transform:translate(-50%,-50%);width:60%}.ReactModal__Content iframe{height:320px}.ReactModal__Content iframe body{margin:0}.error-modal .ReactModal__Content{text-align:center;width:32%!important}.error-modal .modal-msg{border-bottom:1px solid #eee;font-size:17px;margin:14px 0;padding-bottom:30px}.error-modal button{font-size:18px;font-weight:700;padding:15px 20px}@media(max-width:768px){.error-modal .ReactModal__Content{width:80%!important}}.ui-modal-view-info{padding:0}.modal-header{display:flex;justify-content:flex-end}.modal-header .close-modal-icon{cursor:pointer;font-size:25px;opacity:.7}.modal-header .close-modal-icon:hover{opacity:1}@media(max-width:900px){.transactions-content{margin:0;overflow-x:hidden;padding:0;position:relative}.bank-transactions,.system-transactions{height:100%;position:absolute;top:0}.bank-transactions{z-index:10}.system-transactions{right:-100%;transition:right .5s;z-index:8}.system-transactions.open-system{right:0;z-index:12}.details-bar>div:first-child{flex:2}.details-bar>div:last-child{flex:1}.details-bar>div>div{align-items:flex-start;flex-direction:column}.details-bar>div>div .transaction-title{order:2}.details-bar>div>div .details-txt{order:1}.details-txt{font-size:10px}.details-bar>div.amount-currency{align-items:flex-end;flex-direction:column}.details-total-amount{order:2}.details-currency{font-size:10px;order:1}.filters-inputs .form-group{width:100%}.desktop-record{display:none}.mobile-record{align-items:normal;display:flex;flex-direction:column;justify-content:center}.record-header{display:flex;justify-content:space-between;margin-bottom:10px}.mobile-record .record-header+.description{margin-left:30px}.record-header>div{display:flex;flex:1}.record-header>div .action{margin-right:10px}[dir=rtl] .record-header>div .action{margin-left:10px;margin-right:0}.record-header>div:last-child{justify-content:flex-end}.mobile-record .description .info{align-items:flex-start;flex-direction:column}.mobile-record .description .info span{margin:0}.mobile-record .amount{margin-right:10px}.transaction-wrapper{height:100vh}.transaction-record.bank-item{display:flex}.bank-header{align-items:center;background:#fff;color:#a1a3b8;display:flex!important;justify-content:space-between;padding:8px 10px}.bank-header h4{color:#b4b6ce;font-size:14px;margin:0}.bank-header .back-link{cursor:pointer}.bank-header .back-link i{margin:0 3px}.bank-header .back-link:hover{color:var(--body-text-color)}.transaction-wrapper{height:calc(100% - 150px)}}.danger-alert{color:var(--color-danger)}.danger-alert,.zero-state{font-size:18px;font-weight:700;left:50%;padding:10px;position:absolute;text-align:center;text-transform:capitalize;top:50%;transform:translate(-50%,-50%);width:100%}.zero-state{color:var(--color-default)}.something-wrong{color:var(--color-danger);font-size:18px;left:50%;padding:10px;position:absolute;top:50%;transform:translate(-50%,-50%)}.did-floating-label-content{margin-bottom:20px;position:relative}.did-floating-label{background:#fff;color:#1e4c82;font-size:13px;font-weight:400;left:15px;padding:0 5px;pointer-events:none;position:absolute;top:11px;transition:all .2s ease;-moz-transition:all .2s ease;-webkit-transition:all .2s ease}.did-floating-input{background:#fff;border:1px solid #3d85d8;border-radius:4px;box-sizing:border-box;color:#323840;display:block;font-size:12px;height:36px;padding:0 20px;width:100%}.did-floating-input:focus{outline:none}.did-floating-input:focus~.did-floating-label{font-size:13px;top:-8px}.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow,.react-datepicker__navigation-icon:before,.react-datepicker__year-read-view--down-arrow{border-color:#ccc;border-style:solid;border-width:3px 3px 0 0;content:"";display:block;height:9px;position:absolute;top:6px;width:9px}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle,.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle{margin-left:-4px;position:absolute;width:0}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after,.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before,.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after,.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before{border:8px solid transparent;box-sizing:content-box;content:"";height:0;left:-8px;position:absolute;width:1px;z-index:-1}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before,.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before{border-bottom-color:#aeaeae}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle{margin-top:-8px;top:0}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after,.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before{border-bottom-color:#f0f0f0;border-top:none}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:after{top:0}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle:before{border-bottom-color:#aeaeae;top:-1px}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle{bottom:0;margin-bottom:-8px}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after,.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before{border-bottom:none;border-top-color:#fff}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:after{bottom:0}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle:before{border-top-color:#aeaeae;bottom:-1px}.react-datepicker-wrapper{border:0;display:inline-block;padding:0;width:100%}.react-datepicker{background-color:#fff;border:1px solid #aeaeae;border-radius:.3rem;color:#000;display:inline-block;font-family:Helvetica Neue,helvetica,arial,sans-serif;font-size:.8rem;position:relative}.react-datepicker--time-only .react-datepicker__triangle{left:35px}.react-datepicker--time-only .react-datepicker__time-container{border-left:0}.react-datepicker--time-only .react-datepicker__time,.react-datepicker--time-only .react-datepicker__time-box{border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem}.react-datepicker__triangle{left:50px;position:absolute}.react-datepicker-popper{z-index:1}.react-datepicker-popper[data-placement^=bottom]{padding-top:10px}.react-datepicker-popper[data-placement=bottom-end] .react-datepicker__triangle,.react-datepicker-popper[data-placement=top-end] .react-datepicker__triangle{left:auto;right:50px}.react-datepicker-popper[data-placement^=top]{padding-bottom:10px}.react-datepicker-popper[data-placement^=right]{padding-left:8px}.react-datepicker-popper[data-placement^=right] .react-datepicker__triangle{left:auto;right:42px}.react-datepicker-popper[data-placement^=left]{padding-right:8px}.react-datepicker-popper[data-placement^=left] .react-datepicker__triangle{left:42px;right:auto}.react-datepicker__header{background-color:#f0f0f0;border-bottom:1px solid #aeaeae;border-top-left-radius:.3rem;padding:8px 0;position:relative;text-align:center}.react-datepicker__header--time{padding-bottom:8px;padding-left:5px;padding-right:5px}.react-datepicker__header--time:not(.react-datepicker__header--time--only){border-top-left-radius:0}.react-datepicker__header:not(.react-datepicker__header--has-time-select){border-top-right-radius:.3rem}.react-datepicker__month-dropdown-container--scroll,.react-datepicker__month-dropdown-container--select,.react-datepicker__month-year-dropdown-container--scroll,.react-datepicker__month-year-dropdown-container--select,.react-datepicker__year-dropdown-container--scroll,.react-datepicker__year-dropdown-container--select{display:inline-block;margin:0 2px}.react-datepicker-time__header,.react-datepicker-year-header,.react-datepicker__current-month{color:#000;font-size:.944rem;font-weight:700;margin-top:0}.react-datepicker-time__header{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.react-datepicker__navigation{align-items:center;background:none;border:none;cursor:pointer;display:flex;height:32px;justify-content:center;overflow:hidden;padding:0;position:absolute;text-align:center;text-indent:-999em;top:2px;width:32px;z-index:1}.react-datepicker__navigation--previous{left:2px}.react-datepicker__navigation--next{right:2px}.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button){right:85px}.react-datepicker__navigation--years{display:block;margin-left:auto;margin-right:auto;position:relative;top:0}.react-datepicker__navigation--years-previous{top:4px}.react-datepicker__navigation--years-upcoming{top:-4px}.react-datepicker__navigation:hover :before{border-color:#a6a6a6}.react-datepicker__navigation-icon{font-size:20px;position:relative;top:-1px;width:0}.react-datepicker__navigation-icon--next{left:-2px}.react-datepicker__navigation-icon--next:before{left:-7px;transform:rotate(45deg)}.react-datepicker__navigation-icon--previous{right:-2px}.react-datepicker__navigation-icon--previous:before{right:-7px;transform:rotate(225deg)}.react-datepicker__month-container{float:left}.react-datepicker__year{margin:.4rem;text-align:center}.react-datepicker__year-wrapper{display:flex;flex-wrap:wrap;max-width:180px}.react-datepicker__year .react-datepicker__year-text{display:inline-block;margin:2px;width:4rem}.react-datepicker__month{margin:.4rem;text-align:center}.react-datepicker__month .react-datepicker__month-text,.react-datepicker__month .react-datepicker__quarter-text{display:inline-block;margin:2px;width:4rem}.react-datepicker__input-time-container{clear:both;float:left;margin:5px 0 10px 15px;text-align:left;width:100%}.react-datepicker__input-time-container .react-datepicker-time__caption,.react-datepicker__input-time-container .react-datepicker-time__input-container{display:inline-block}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input{display:inline-block;margin-left:10px}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input{width:auto}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-inner-spin-button,.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]{-moz-appearance:textfield}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__delimiter{display:inline-block;margin-left:5px}.react-datepicker__time-container{border-left:1px solid #aeaeae;float:right;width:85px}.react-datepicker__time-container--with-today-button{border:1px solid #aeaeae;border-radius:.3rem;display:inline;position:absolute;right:-72px;top:0}.react-datepicker__time-container .react-datepicker__time{background:#fff;border-bottom-right-radius:.3rem;position:relative}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box{border-bottom-right-radius:.3rem;margin:0 auto;overflow-x:hidden;text-align:center;width:85px}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list{box-sizing:content-box;height:calc(195px + .85rem);list-style:none;margin:0;overflow-y:scroll;padding-left:0;padding-right:0;width:100%}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item{height:30px;padding:5px 10px;white-space:nowrap}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover{background-color:#f0f0f0;cursor:pointer}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected{background-color:#216ba5;color:#fff;font-weight:700}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover{background-color:#216ba5}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled{color:#ccc}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover{background-color:transparent;cursor:default}.react-datepicker__week-number{color:#ccc;display:inline-block;line-height:1.7rem;margin:.166rem;text-align:center;width:1.7rem}.react-datepicker__week-number.react-datepicker__week-number--clickable{cursor:pointer}.react-datepicker__week-number.react-datepicker__week-number--clickable:hover{background-color:#f0f0f0;border-radius:.3rem}.react-datepicker__day-names,.react-datepicker__week{white-space:nowrap}.react-datepicker__day-names{margin-bottom:-8px}.react-datepicker__day,.react-datepicker__day-name,.react-datepicker__time-name{color:#000;display:inline-block;line-height:1.7rem;margin:.166rem;text-align:center;width:1.7rem}.react-datepicker__month--in-range,.react-datepicker__month--in-selecting-range,.react-datepicker__month--selected,.react-datepicker__quarter--in-range,.react-datepicker__quarter--in-selecting-range,.react-datepicker__quarter--selected{background-color:#216ba5;border-radius:.3rem;color:#fff}.react-datepicker__month--in-range:hover,.react-datepicker__month--in-selecting-range:hover,.react-datepicker__month--selected:hover,.react-datepicker__quarter--in-range:hover,.react-datepicker__quarter--in-selecting-range:hover,.react-datepicker__quarter--selected:hover{background-color:#1d5d90}.react-datepicker__month--disabled,.react-datepicker__quarter--disabled{color:#ccc;pointer-events:none}.react-datepicker__month--disabled:hover,.react-datepicker__quarter--disabled:hover{background-color:transparent;cursor:default}.react-datepicker__day,.react-datepicker__month-text,.react-datepicker__quarter-text,.react-datepicker__year-text{cursor:pointer}.react-datepicker__day:hover,.react-datepicker__month-text:hover,.react-datepicker__quarter-text:hover,.react-datepicker__year-text:hover{background-color:#f0f0f0;border-radius:.3rem}.react-datepicker__day--today,.react-datepicker__month-text--today,.react-datepicker__quarter-text--today,.react-datepicker__year-text--today{font-weight:700}.react-datepicker__day--highlighted,.react-datepicker__month-text--highlighted,.react-datepicker__quarter-text--highlighted,.react-datepicker__year-text--highlighted{background-color:#3dcc4a;border-radius:.3rem;color:#fff}.react-datepicker__day--highlighted:hover,.react-datepicker__month-text--highlighted:hover,.react-datepicker__quarter-text--highlighted:hover,.react-datepicker__year-text--highlighted:hover{background-color:#32be3f}.react-datepicker__day--highlighted-custom-1,.react-datepicker__month-text--highlighted-custom-1,.react-datepicker__quarter-text--highlighted-custom-1,.react-datepicker__year-text--highlighted-custom-1{color:#f0f}.react-datepicker__day--highlighted-custom-2,.react-datepicker__month-text--highlighted-custom-2,.react-datepicker__quarter-text--highlighted-custom-2,.react-datepicker__year-text--highlighted-custom-2{color:green}.react-datepicker__day--in-range,.react-datepicker__day--in-selecting-range,.react-datepicker__day--selected,.react-datepicker__month-text--in-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__month-text--selected,.react-datepicker__quarter-text--in-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__quarter-text--selected,.react-datepicker__year-text--in-range,.react-datepicker__year-text--in-selecting-range,.react-datepicker__year-text--selected{background-color:#216ba5;border-radius:.3rem;color:#fff}.react-datepicker__day--in-range:hover,.react-datepicker__day--in-selecting-range:hover,.react-datepicker__day--selected:hover,.react-datepicker__month-text--in-range:hover,.react-datepicker__month-text--in-selecting-range:hover,.react-datepicker__month-text--selected:hover,.react-datepicker__quarter-text--in-range:hover,.react-datepicker__quarter-text--in-selecting-range:hover,.react-datepicker__quarter-text--selected:hover,.react-datepicker__year-text--in-range:hover,.react-datepicker__year-text--in-selecting-range:hover,.react-datepicker__year-text--selected:hover{background-color:#1d5d90}.react-datepicker__day--keyboard-selected,.react-datepicker__month-text--keyboard-selected,.react-datepicker__quarter-text--keyboard-selected,.react-datepicker__year-text--keyboard-selected{background-color:#2579ba;border-radius:.3rem;color:#fff}.react-datepicker__day--keyboard-selected:hover,.react-datepicker__month-text--keyboard-selected:hover,.react-datepicker__quarter-text--keyboard-selected:hover,.react-datepicker__year-text--keyboard-selected:hover{background-color:#1d5d90}.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range){background-color:rgba(33,107,165,.5)}.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range){background-color:#f0f0f0;color:#000}.react-datepicker__day--disabled,.react-datepicker__month-text--disabled,.react-datepicker__quarter-text--disabled,.react-datepicker__year-text--disabled{color:#ccc;cursor:default}.react-datepicker__day--disabled:hover,.react-datepicker__month-text--disabled:hover,.react-datepicker__quarter-text--disabled:hover,.react-datepicker__year-text--disabled:hover{background-color:transparent}.react-datepicker__month-text.react-datepicker__month--in-range:hover,.react-datepicker__month-text.react-datepicker__month--selected:hover,.react-datepicker__month-text.react-datepicker__quarter--in-range:hover,.react-datepicker__month-text.react-datepicker__quarter--selected:hover,.react-datepicker__quarter-text.react-datepicker__month--in-range:hover,.react-datepicker__quarter-text.react-datepicker__month--selected:hover,.react-datepicker__quarter-text.react-datepicker__quarter--in-range:hover,.react-datepicker__quarter-text.react-datepicker__quarter--selected:hover{background-color:#216ba5}.react-datepicker__month-text:hover,.react-datepicker__quarter-text:hover{background-color:#f0f0f0}.react-datepicker__input-container{display:inline-block;position:relative;width:100%}.react-datepicker__month-read-view,.react-datepicker__month-year-read-view,.react-datepicker__year-read-view{border:1px solid transparent;border-radius:.3rem;position:relative}.react-datepicker__month-read-view:hover,.react-datepicker__month-year-read-view:hover,.react-datepicker__year-read-view:hover{cursor:pointer}.react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow{border-top-color:#b3b3b3}.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow,.react-datepicker__year-read-view--down-arrow{right:-16px;top:0;transform:rotate(135deg)}.react-datepicker__month-dropdown,.react-datepicker__month-year-dropdown,.react-datepicker__year-dropdown{background-color:#f0f0f0;border:1px solid #aeaeae;border-radius:.3rem;left:25%;position:absolute;text-align:center;top:30px;width:50%;z-index:1}.react-datepicker__month-dropdown:hover,.react-datepicker__month-year-dropdown:hover,.react-datepicker__year-dropdown:hover{cursor:pointer}.react-datepicker__month-dropdown--scrollable,.react-datepicker__month-year-dropdown--scrollable,.react-datepicker__year-dropdown--scrollable{height:150px;overflow-y:scroll}.react-datepicker__month-option,.react-datepicker__month-year-option,.react-datepicker__year-option{display:block;line-height:20px;margin-left:auto;margin-right:auto;width:100%}.react-datepicker__month-option:first-of-type,.react-datepicker__month-year-option:first-of-type,.react-datepicker__year-option:first-of-type{border-top-left-radius:.3rem;border-top-right-radius:.3rem}.react-datepicker__month-option:last-of-type,.react-datepicker__month-year-option:last-of-type,.react-datepicker__year-option:last-of-type{border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.react-datepicker__month-option:hover,.react-datepicker__month-year-option:hover,.react-datepicker__year-option:hover{background-color:#ccc}.react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming{border-bottom-color:#b3b3b3}.react-datepicker__month-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__year-option:hover .react-datepicker__navigation--years-previous{border-top-color:#b3b3b3}.react-datepicker__month-option--selected,.react-datepicker__month-year-option--selected,.react-datepicker__year-option--selected{left:15px;position:absolute}.react-datepicker__close-icon{background-color:transparent;border:0;cursor:pointer;display:table-cell;height:100%;outline:0;padding:0 6px 0 0;position:absolute;right:0;top:0;vertical-align:middle}.react-datepicker__close-icon:after{background-color:#216ba5;border-radius:50%;color:#fff;content:"×";cursor:pointer;display:table-cell;font-size:12px;height:16px;line-height:1;padding:2px;text-align:center;vertical-align:middle;width:16px}.react-datepicker__today-button{background:#f0f0f0;border-top:1px solid #aeaeae;clear:left;cursor:pointer;font-weight:700;padding:5px 0;text-align:center}.react-datepicker__portal{align-items:center;background-color:rgba(0,0,0,.8);display:flex;height:100vh;justify-content:center;left:0;position:fixed;top:0;width:100vw;z-index:2147483647}.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__time-name{line-height:3rem;width:3rem}@media(max-height:550px),(max-width:400px){.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__time-name{line-height:2rem;width:2rem}}.react-datepicker__portal .react-datepicker-time__header,.react-datepicker__portal .react-datepicker__current-month{font-size:1.44rem}