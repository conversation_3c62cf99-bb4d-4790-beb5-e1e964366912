<?php
ini_set('mysql.connect_timeout', '1');
if (isset($_GET['debug']) && $_GET['debug'] == 2) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}
require_once dirname(dirname(__DIR__)) . '/configure.php';
include dirname(__DIR__) . '/config/pure_function.php';
switchToRedis();
session_name('OISystem');
session_start();
if (!isset($_SESSION['db_config'])) {
    echo json_encode(["data" => [], "order" => []]);
    die();
}
session_write_close();
setStatusInfo();
require_once('../vendors/Repository/autoload.php');
require_once('../vendors/WarehouseService.php');
require_once('../../izam-packages/daftra-app-manager/src/Services/ProductSearches.php');
require_once(dirname(__FILE__, 2) . '/vendors/Services/EmbeddedBarcodeService.php');
include dirname(__DIR__).'/vendor/autoload.php';
putenv("APP_ENV=" . APP_ENV);
require_once __DIR__ . '/../providers/rollbar_provider.php';
use App\vendors\Services\EmbeddedBarcodeService;
use Izam\Aws\Aws;
use Izam\Logging\Service\RollbarLogService;
use Repository\ItemPermissionRepository;

function debug_mysql_query($q, $link = null)
{
    if (isset($_GET['debug']) && $_GET['debug'] == 2) {
        print_pre($q);
    }
    if ($link == null) {
        $link = plain_db_connect();
    }
    return mysqli_query($link, $q);
}

function sort_by_order($a, $b)
{
    return $a['id'] - $b['id'];
}

function plain_db_connect()
{

    $db_config = $_SESSION['db_config'];
    $link = connectToDatabase($db_config);

    mysqli_select_db($link, $db_config['database']);
    debug_mysql_query('SET NAMES utf8', $link);
    return $link;
}

function product_price($link, $val, $group_id, $price)
{
    $val = mysqli_real_escape_string($link, $val);
    $result = debug_mysql_query("select product_prices.* from  product_prices join group_prices on product_prices.group_price_id = group_prices.id where product_id='{$val}' AND group_prices.status = 1", $link);
    if ($result)
        while ($row = mysqli_fetch_assoc($result)) {
            $prices[] = $row;
        }
    return $prices ?? [];
}

function client_find($val)
{
    $link = plain_db_connect();
    $val = mysqli_real_escape_string($link, $val);
    $result = debug_mysql_query("select * from clients where id='{$val}'", $link) or die('mysql_error');
    $row = mysqli_fetch_assoc($result);

    return $row['group_price_id'] ?? null;
}

function calculate_bundle_cost($product_id)
{
    $q = "select * From product_bundles PB left join products P on P.id = PB.product_id where PB.bundle_product_id = {$product_id};";
    $link = plain_db_connect();
    $result = debug_mysql_query($q, $link) or die('mysql_error');
    $totalCost = 0;
    foreach ($result as $productBundle) {
        if($productBundle['type'] == 2) {
            $totalCost += $productBundle['quantity'] * $productBundle['buy_price'];
        } else {
            $totalCost += $productBundle['quantity'] * $productBundle['average_price'];
        }

    }
    return $totalCost;
}

function processTrackingData($row)
{
    foreach (['serials', 'lots', 'expiry_dates', 'quantities'] as $item) {
        if (isset($row[$item])) {
            $row[$item] = explode(',', $row[$item]);
        } else {
            $row[$item] = [];
        }
    }
    return $row;
}

function product_find(
    $val,
    $client = 0,
    $store_id = null,
    $get_all_products = true,
    $productsOnly = false,
    $type = null,
    $find_products_by_ids = [],
    $includeItemGroup = false,
    $itemGroupsOnly = false,
    $enablePurchasingTax = false
)
{
    $link = plain_db_connect();
    //NEW PERMISSION
    $staff_id = $_SESSION['OWNER']['staff_id'];
    $product_permitted = false;
    $extra_where = " 1 ";
    if(!$itemGroupsOnly){
        if ($staff_id == 0) {
            $product_permitted = true;
        } else {
            $q = "select * From roles_permissions rp "
                . " left join staffs on staffs.role_id = rp.role_id and staffs.id = " . $staff_id . " "
                . " where staffs.id = " . $staff_id . " AND rp.permission_id = 124 ";
            $queryResult = debug_mysql_query($q);
            if ($queryResult != false)
                $staff_permission = mysqli_fetch_assoc($queryResult);
            else
                $staff_permission = [];
            if (empty ($staff_permission) && empty($_SESSION['OWNER']['is_super_admin'])) {
                if (!empty($store_id) && !$get_all_products) {
                    debug_mysql_query("SET GLOBAL group_concat_max_len = 1000000;");
                    $allowed_products = mysqli_fetch_assoc(debug_mysql_query("SELECT GROUP_CONCAT(p.id) as allowed_products from products p 
                            left join store_stock_balance ssb on ssb.product_id = p.id and ssb.store_id = $store_id and ssb.balance > 0 
                            where (ssb.store_id = $store_id and ssb.balance > 0 and p.staff_id = $staff_id) OR (( p.track_stock <> 1 AND p.track_stock IS NULL) OR p.staff_id = $staff_id) "))['allowed_products'];

                    if (empty($allowed_products)) {
                        $extra_where = " false ";
                    } else {
                        $allowed_products = explode(',', $allowed_products);
                        $allowed_products = implode(',', array_filter($allowed_products));
                        $extra_where = " products.id in ( $allowed_products ) ";
                    }
                }
                $product_permitted = true;
            }
        }
    }
    //Check ProductTracking
    $productTrackingActive = isPluginActivePure(92);
    $productTrackingJoin = '';
    $productTrackingSelect = '';
    $productTrackingGroupBY = '';
    if ($productTrackingActive) {
        $productTrackingJoin = " LEFT JOIN (select * from tracking_numbers where tracking_numbers.quantity > 0) tn ON (tn.product_id = products.id and tn.quantity > 0)";
        $productTrackingSelect = ' , GROUP_CONCAT(tn.serial) AS serials, GROUP_CONCAT(tn.lot) AS lots, GROUP_CONCAT(tn.expiry_date) AS expiry_dates, GROUP_CONCAT(tn.quantity) AS quantities, GROUP_CONCAT( tn.store_id ) as store_ids ';
        $productTrackingGroupBY = ' GROUP BY products.id ';
    }
    //Check Branches
    $branchesActive = false;
    $InvoicePlugin = 2;
    $excludedCategories = [];
    $excludedCategoriesCondition = '';
    $barCodeExcludedCategoriesCondition = '';
    $barCodeBranchCondition = "";
    $shareProductsAcrossBranches = false;
    if (isBranchPluginActive()) {
        $branchesActive = true;
        $branchCondition = "";
        $barCodeBranchCondition = "";
        $BranchPlugin = 87;
        $currentStaffID = $_SESSION['OWNER']['staff_id'];
        $currentStaffBranchInfo = debug_mysql_query("select `key`,`value` from settings where plugin_id = {$BranchPlugin} and (`key` = 'branch-{$currentStaffID}' or `key` = 'share_products')");
        if (mysqli_num_rows($currentStaffBranchInfo) > 0) {
            while ($row = mysqli_fetch_assoc($currentStaffBranchInfo)) {
                if ($row['key'] == 'share_products' && $row['value'] == 1) {
                    $shareProductsAcrossBranches = true;
                    $branchCondition = '';
                    $barCodeBranchCondition = '';
                    break;
                } else {
                    $currentStaffBranch = $_SESSION['branch_id'];
                    if ($currentStaffBranch) {
                        $branchCondition = "and products.branch_id = {$currentStaffBranch}";
                        $barCodeBranchCondition = "and P.branch_id= {$currentStaffBranch}";
                    }
                }
            }
        } else {
            // in case share_products setting was never set before, defaults to 0
            $currentStaffBranch = $_SESSION['branch_id'];
            $branchCondition = "and products.branch_id = {$currentStaffBranch}";
            $barCodeBranchCondition = "and P.branch_id= {$currentStaffBranch}";
        }
        $extra_where .= " {$branchCondition} ";
    } else {
        plain_db_connect();
    }

     
     $itemGroups = [];
     if($includeItemGroup){
        $q = '
        SELECT *
        FROM `item_groups` 
        WHERE `name` LIKE "%' . $val . '%"
        ';
        if($branchesActive && !$shareProductsAcrossBranches){
            $currentStaffBranch = $_SESSION['branch_id'];
            $q .= " and branch_id = ". $currentStaffBranch;
        }
        $result = debug_mysql_query($q);
        if ($result) {
            $lang = $_SESSION['OWNER']['language_code'];
            $itemGroupLabel = ($lang == 7 ? ' (مجموعة البنود)' : ' (Item Group)');
            while ($row = mysqli_fetch_assoc($result)) {
                $row['record_type'] = 'item_group';
                $row['name'] = $row['name'] . $itemGroupLabel;
                $itemGroups['g'.$row['id']] = $row;
            }
        }
        if($itemGroupsOnly){
            echo json_encode(["data" => $itemGroups, "order" => array_keys($itemGroups)]);
            die();
        }
     }
  
   

    if (isset($_GET['model']) && in_array($_GET['model'], ['invoice'])) {
        if (isset($currentStaffBranch)) {
            $excludedCategoriesSettingQuery = debug_mysql_query("select `key`,`value` from settings where plugin_id = {$InvoicePlugin} and `key` = 'excluded_categories' and `branch_id` = {$currentStaffBranch}");
        } else {
            $currentStaffBranch = $_SESSION['branch_id'];
            $excludedCategoriesSettingQuery = debug_mysql_query("select `key`,`value` from settings where plugin_id = {$InvoicePlugin} and `key` = 'excluded_categories'" . ($currentStaffBranch ? " and `branch_id` = {$currentStaffBranch}" : ""));
        }
        $excludedCategoriesSettingRow = mysqli_fetch_assoc($excludedCategoriesSettingQuery);
        if ($excludedCategoriesSettingRow) {
            $excludedCategories = $excludedCategoriesSettingRow["value"];
            if (!empty($excludedCategories)) {
                $excludedCategoriesCondition = "  IF(items_categories.category_id is not null, items_categories.category_id, -1) not in ({$excludedCategories}) and ";
                $barCodeExcludedCategoriesCondition = " and IF(IC.category_id is not null, IC.category_id, -1) not in ({$excludedCategories}) ";
            }
        }
    }

    $result = debug_mysql_query("SELECT * FROM `custom_forms` where table_name='products' limit 1");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
    }
    if (isset($row) && $row) {
        $custom_froms = true;
    }

    $val = str_replace(['\\'],['\\\\\\'],$val);

    $val = mysqli_real_escape_string($link, $val);
    if ($client != 0) {
        $group_price_id = intval(client_find($client));
    } else {
        $group_price_id = 0;
    }
    
    $rank_stmt = " , (
        -- Exact match for product_code gets the highest score
        IF(`product_code` = '$val', 100, 0) +
        -- Exact match for name gets the highest score
        IF(`name` = '$val', 100, 0) +
        -- Case-insensitive exact match for name (without using BINARY)
        IF(`name` LIKE '$val', 90, 0) +
        -- Case-insensitive exact match for product_code (without using BINARY)
        IF(`product_code` LIKE '$val', 90, 0) +
        -- Partial match where name starts with the value
        IF(`name` LIKE '$val%', 50, 0) +
        -- Partial match where product_code starts with the value
        IF(`product_code` LIKE '$val%', 50, 0) +
        -- General match for name containing the value
        IF(`name` LIKE '%$val%', 25, 0) +
        -- General match for product_code containing the value
        IF(`product_code` LIKE '%$val%', 25, 0)
    ) AS `rank` ";
    
    // As instructed by Hatem and Hazem on 03/11/2024 bcs of performance issues
    // and its not needed by RSD
    $appsTablesJoins = "";
//    try {
//        $appsFieldsSearches = new \Izam\Daftra\AppManager\Services\ProductSearches();
//        $appsTablesJoins = $appsFieldsSearches->getJoins();
//        $extra_where .= $appsFieldsSearches->getConditions($val) . !empty($excludedCategoriesCondition) ? ' AND ' . $excludedCategoriesCondition . ' 1 ' : '';
//    } catch (\Throwable $e) {
//        $appsTablesJoins = '';
//    }
    $q = 'SELECT products.*,product_images.file ' . $rank_stmt . $productTrackingSelect . ' FROM products' . $productTrackingJoin . $appsTablesJoins
        . ' LEFT JOIN product_images on product_images.product_id = products.id AND product_images.default = 1'
        . (!empty($excludedCategoriesCondition) ? ' LEFT JOIN items_categories ON products.id = items_categories.item_id and items_categories.item_type = 1' : '')
        . ' WHERE ';
    if (!empty($productsOnly)) {
        $q .= " products.track_stock = 1 AND ";
    }
    if (!empty($type)) {
        $q .= " type = {$type} AND ";
    }
    if (!empty($_GET['model']) && $_GET['model'] == 'requisition') {
        $q .= " products.track_stock = 1 AND ";
    }
    preg_match('/^\\\"(.*)\\\"$/', $val, $exactMatch);
    if (count($exactMatch)) {
        $q .= '`name` = "' . $exactMatch[1] . '"';
    } else {
        $q .= '(';
        $keywords = explode(' ', $val);
        if (count($keywords) == 1) {
            $q .= '`name` like \'%' . $val . '%\' OR `name` like \'' . $val . '%\' OR ';
        } else {
            $q .= '(';
            foreach ($keywords as $k => $v) {
                $q .= '`name` like \'%' . $v . '%\'  ';
                if (isset($keywords[$k + 1])) $q .= ' AND ';
            }
            $q .= ') OR ';
        }

        if (isSimilarToDataMatrix($val)) {
            $q .= addDataMatrixConditions($val);
        }

        $extra_where .= isset($_GET['include_suspended']) && $_GET['include_suspended'] == 1
            ? ' and (status = 0 or status = 2 or status is null)'
            : ' and (status = 0 or status is null)';
        $q .= ' `product_code` like \'%' . $val . '%\' OR `barcode` like \'%' . $val . '%\') AND ' . $excludedCategoriesCondition . $extra_where . $productTrackingGroupBY . ' ORDER by `rank` DESC, `name` ASC LIMIT 30 ';

    }


    if(!empty($find_products_by_ids)) {
        $product_ids  = implode(',', $find_products_by_ids);
        $extra_where .= ' and products.id in ('.$product_ids.') ';
    }

    //if($_SERVER['REMOTE_ADDR'] == '156.209.203.28')
    //die($q);

    $products_count_result = debug_mysql_query("SELECT count(*) as count from products $appsTablesJoins" . (!empty($excludedCategoriesCondition) ? " LEFT JOIN items_categories ON products.id = items_categories.item_id and items_categories.item_type = 1" : "") . " where $excludedCategoriesCondition $extra_where ");

    if (empty($val) && $products_count_result) {
        $products_count = mysqli_fetch_array($products_count_result);
        $trackingCondition = '';
        if (!empty($productsOnly) || (!empty($_GET['model']) && $_GET['model'] == 'requisition')) {
            $trackingCondition = "products.track_stock = 1 AND ";
        }
        if ($products_count['count'] > 11) {

            $top_product_query = debug_mysql_query(
                "SELECT count(`InvoiceItem`.`product_id`) as total, `InvoiceItem`.`product_id` FROM (
                    SELECT `product_id`
                    FROM `invoice_items`
                    WHERE `product_id` != 0
                    ORDER BY `invoice_id` DESC
                    LIMIT 10000
                ) AS `InvoiceItem` WHERE `InvoiceItem`.`product_id` != 0 GROUP BY `InvoiceItem`.`product_id` ORDER BY total DESC LIMIT 20");

            while ($row = mysqli_fetch_array($top_product_query)) {
                $top_product_count = mysqli_num_rows($top_product_query);
                $ids[] = $row['product_id'];
            }

            if (!empty($type)) {
                $extra_where .= " AND type = {$type} ";
            }

        if(is_countable($ids) && count($ids)!=0){
            $q = 'SELECT products.*, product_images.file ' . $rank_stmt . $productTrackingSelect . ' FROM products ' . $productTrackingJoin . $appsTablesJoins . ' LEFT JOIN product_images on product_images.product_id = products.id AND product_images.default = 1 ' . (!empty($excludedCategoriesCondition) ? ' LEFT JOIN items_categories ON products.id = items_categories.item_id and items_categories.item_type = 1' : "") . '  WHERE ' . $trackingCondition . ' products.id in (' . implode(",", $ids) . ') AND ' . $excludedCategoriesCondition . $extra_where . $productTrackingGroupBY . ' order by `rank` DESC limit 11';
        }else{
            $q = 'SELECT products.*, product_images.file ' . $rank_stmt . $productTrackingSelect . ' FROM products ' . $productTrackingJoin . $appsTablesJoins . ' LEFT JOIN product_images on product_images.product_id = products.id AND product_images.default = 1 ' . (!empty($excludedCategoriesCondition) ? ' LEFT JOIN items_categories ON products.id = items_categories.item_id and items_categories.item_type = 1' : "") . '  where ' . $trackingCondition . $excludedCategoriesCondition . $extra_where . $productTrackingGroupBY . ' order by `rank` DESC limit 11';
        }
	}else{
        $q = 'SELECT products.*, product_images.file ' . $rank_stmt . $productTrackingSelect . ' FROM products ' . $productTrackingJoin . $appsTablesJoins . ' LEFT JOIN product_images on product_images.product_id = products.id AND product_images.default = 1 ' . (!empty($excludedCategoriesCondition) ? ' LEFT JOIN items_categories ON products.id = items_categories.item_id and items_categories.item_type = 1' : "") . '  where ' . $trackingCondition . $excludedCategoriesCondition . $extra_where . $productTrackingGroupBY . ' order by `rank` DESC';
	}
}
    $result = debug_mysql_query($q, $link) ;
    $products = array();

    // fetch products by barcode from product_item_barcode table
    if ($result && mysqli_num_rows($result) == 0) {
        $q = '
            SELECT 
                   P.*, 
                   IF (PB.unit_price IS NULL, P.unit_price, PB.unit_price) AS unit_price, 
                   PB.unit_factor_id AS default_retail_factor_id,
                   PB.unit_factor_id AS default_buy_factor_id,
                   PB.barcode AS barcode,
                   PB.quantity AS default_quantity
            FROM `products` P
            INNER JOIN `product_item_barcode` PB ON P.id = PB.product_id  '.$barCodeBranchCondition.
            (!empty($barCodeExcludedCategoriesCondition) ? ' LEFT JOIN `items_categories` IC ON P.id = IC.item_id and IC.item_type = 1 ' : '') . $barCodeExcludedCategoriesCondition . '
            WHERE PB.barcode = "' . $val . '"
        ';
        $result = debug_mysql_query($q);
    } 
//    elseif (($product = mysqli_fetch_assoc($result)) && mysqli_num_rows(debug_mysql_query("SELECT 1 FROM product_item_barcode WHERE product_id = {$product['id']} LIMIT 1", $link))) {
//        $q = '
//            SELECT 
//                   P.*, 
//                   PI.file,
//                   IF (PB.unit_price IS NULL, P.unit_price, PB.unit_price) AS unit_price, 
//                   PB.unit_factor_id AS default_retail_factor_id,
//                   PB.unit_factor_id AS default_buy_factor_id,
//                   PB.barcode AS barcode,
//                   PB.quantity AS default_quantity' .
//            $rank_stmt  . '
//            FROM `products` P
//            INNER JOIN `product_item_barcode` PB ON P.id = PB.product_id
//            LEFT JOIN product_images PI ON PI.product_id = P.id AND PI.default = 1 ' .
//            $appsTablesJoins .
//            (!empty($barCodeExcludedCategoriesCondition) ? ' LEFT JOIN `items_categories` IC ON P.id = IC.item_id and IC.item_type = 1 ' : '') .
//            $barCodeBranchCondition . $barCodeExcludedCategoriesCondition . '
//            WHERE P.name LIKE "%'. $val .'%" OR P.name LIKE  "'. $val . '%" OR P.name LIKE "%'. $val .'"
//            OR P.product_code LIKE "%'. $val .'%"  
//            ORDER BY `rank` DESC
//        ';
//        $result = debug_mysql_query($q);
//    }

//    if (mysqli_num_rows($result) > 0) {
//        // reset the result pointer to the first row to ignore the elseif check 
//        mysqli_data_seek($result, 0);
//    }
    
    // check for embedded barcode
    $embedded_barcode_service = new EmbeddedBarcodeService();
    $enable_embedded_barcode = false;
    $embedded_barcode_format = "";

    if ($result && mysqli_num_rows($result) == 0) {
        $enable_embedded_barcode = $embedded_barcode_service->embeddedBarcodeEnabled();

        if ($enable_embedded_barcode) {
            $embedded_barcodes = $embedded_barcode_service->embeddedBarcodeFormat();

            foreach ($embedded_barcodes as $embedded_barcode) {
                if (strlen($embedded_barcode['value']) != strlen($val)) {
                    continue;
                }

                list($product_code, $price, $weight) = $embedded_barcode_service->parseEmbeddedBarcode($val, $embedded_barcode['value']);

                $q = "SELECT * FROM `products` WHERE `barcode` = '$product_code'";
                $condition = '';
                if(!$shareProductsAcrossBranches && isBranchPluginActive()) {
                    $condition .= 'AND branch_id = '.$_SESSION['branch_id'];
                }
                $q = "SELECT * FROM `products` WHERE `barcode` = '$product_code' ".$condition;


                $result = debug_mysql_query($q);

                $branch_id = $embedded_barcode['branch_id'];
            }
        }
    }

    if ($productTrackingActive) {
        //Search for product by serial in case no results found
        if (mysqli_num_rows($result) == 0 && strlen($val) > 3) {

            $q = '
            SELECT product.*,
            tracking_numbers.serial
            FROM `products` AS product 
            INNER JOIN `tracking_numbers` ON (tracking_numbers.product_id = product.id)
            WHERE `tracking_numbers`.serial = "' . $val . '" AND product.tracking_type = "serial"
            ';

            $result = debug_mysql_query($q);

        }

        //Search for product by lot in case no results found
        if (mysqli_num_rows($result) == 0 && strlen($val) > 3) {

            $q = '
                SELECT product.*,
                tracking_numbers.lot,
                FROM `products` AS product 
                INNER JOIN `tracking_numbers` ON (tracking_numbers.product_id = product.id)
                WHERE `tracking_numbers`.lot = "' . $val . '" AND product.tracking_type = "lot"
            ';

            $result = debug_mysql_query($q);

        }

        //Search for product by lot and expiry in case no results found
        if ($result == false || mysqli_num_rows($result) == 0) {

            $q = '
                SELECT product.*,
                tracking_numbers.lot as lot,
                tracking_numbers.expiry_date as expiry_date,
                CONCAT_WS(" / ", tracking_numbers.lot, tracking_numbers.expiry_date) as lot_with_expiry
                FROM `products` AS product 
                INNER JOIN `tracking_numbers` ON (tracking_numbers.product_id = product.id)
                WHERE `tracking_numbers`.lot LIKE "%' . $val . '%" OR `tracking_numbers`.expiry_date LIKE "%' . $val . '%" AND  product.tracking_type = "lot_and_expiry_date"
            ';
            $result = debug_mysql_query($q);

        }
    }

    if ($result) {
        $obj = new Aws();
        while ($row = mysqli_fetch_assoc($result)) {

           $row['is_s3'] = false;
           $id = $row['id'];
            if ($id) {
                $q = "SELECT entity_attachments.* ,files.path ,files.is_temp,files.name, files.mime_type, files.entity_key FROM entity_attachments JOIN files ON (files.id = entity_attachments.file_id) WHERE entity_attachments.entity_key = 'product' AND entity_attachments.entity_id = $id AND entity_attachments.is_default = 1 AND entity_attachments.deleted_at is null";
                $s3_result = debug_mysql_query($q);
                if (mysqli_num_rows($s3_result)) {
                    $s3_result = mysqli_fetch_assoc($s3_result);
                    $s3_result['site_id'] = $_SESSION['OWNER']['id'];

                    RollbarLogService::logTempFilesToRollbar($s3_result['is_temp'], $s3_result);

                    $row['file'] = $obj->getPermanentUrl($s3_result['path']);
                    $row['is_s3'] = true;
                }

                $row['prices'] = product_price($link, $row['id'], $group_price_id, $row['unit_price']);
                if ($productTrackingActive) {
                    $row = processTrackingData($row);
                }
                $row['store_ids'] = explode(',', $row['store_ids'] ?? '');
                if ((int)$enablePurchasingTax == 1) {
                    $isTax1SetToNoTax = $row['purchasing_tax1'] == -1;
                    $isTax1SetToDoAsSales = $row['purchasing_tax1'] == null;
                    $isTax1SetToValue = $row['purchasing_tax1'] >= 0;
                    if ($isTax1SetToNoTax && !$isTax1SetToDoAsSales) {
                        $row['tax1'] = null;
                    } elseif($isTax1SetToValue && !$isTax1SetToDoAsSales) {
                        $row['tax1'] = $row['purchasing_tax1'];
                    }
                    $isTax2SetToNoTax = $row['purchasing_tax2'] == -1;
                    $isTax2SetToDoAsSales = $row['purchasing_tax2'] == null;
                    $isTax2SetToValue = $row['purchasing_tax2'] >= 0;
                    if ($isTax2SetToNoTax && !$isTax2SetToDoAsSales) {
                        $row['tax2'] = null;
                    } elseif($isTax2SetToValue && !$isTax2SetToDoAsSales) {
                        $row['tax2'] = $row['purchasing_tax2'];
                    }
                }
                $products[$row['id']] = $row;
                if (isset($custom_froms) && $custom_froms) {
                    $products_custom_data = debug_mysql_query("select * From products_custom_data where product_id=" . $row['id']);
                    $products_custom_data = mysqli_fetch_assoc($products_custom_data);
                    if ($products_custom_data) {
                        unset($products_custom_data['id']);
                        unset($products_custom_data['product_id']);
                        unset($products_custom_data['created']);
                        unset($products_custom_data['modified']);
                        unset($products_custom_data['staff_id']);
                        foreach ($products_custom_data as $k => $v) {
                            $products[$row['id']][$k] = $v;
                        }
                    }
                }
            }
        }
    }
  
    if (empty($val)) {


        $q = 'SELECT products.*, product_images.file  ' . $rank_stmt . $productTrackingSelect . ' FROM products ' . $productTrackingJoin . $appsTablesJoins . ' LEFT JOIN product_images on product_images.product_id = products.id AND product_images.default = 1 ' . (!empty($excludedCategoriesCondition)? ' LEFT JOIN items_categories ON products.id = items_categories.item_id and items_categories.item_type = 1 ' : '') . ' WHERE products.id not in (' . implode(",", (array)$ids) . ') AND ' . $excludedCategoriesCondition.$extra_where . $productTrackingGroupBY . ' order by `rank` DESC limit ' . (11 - count((array)$ids));
        $result = debug_mysql_query($q);
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {

                $row['prices'] = product_price($link, $row['id'], $group_price_id, $row['unit_price']);
                if ($productTrackingActive) {
                    $row = processTrackingData($row);
                }
                $products[$row['id']] = $row;

                if ($custom_froms) {
                    $products_custom_data = debug_mysql_query("select * From products_custom_data where product_id=" . $row['id']);
                    $products_custom_data = mysqli_fetch_assoc($products_custom_data);
                    if ($products_custom_data) {
                        unset($products_custom_data['id']);
                        unset($products_custom_data['product_id']);
                        unset($products_custom_data['created']);
                        unset($products_custom_data['modified']);
                        unset($products_custom_data['staff_id']);
                        foreach ($products_custom_data as $k => $v) {
                            $products[$row['id']] [$k] = $v;
                        }
                    }
                }

            }
        }

    }
    $q = "select * From settings where `plugin_id` = '72' and `key` = 'enable_multi_units' and `value` = 1";
    if ($result = debug_mysql_query($q)) {
        $enable_multi_units = mysqli_fetch_assoc($result)['value'];
        if ($enable_multi_units) {
            $unit_factors = [];
            $unit_templates = [];
            foreach ($products as &$p) {
                if (!empty ($p['unit_template_id'])) {
                    if (empty ($unit_templates[$p['unit_template_id']])) {
                        $q = debug_mysql_query("select * From unit_templates where id=" . $p['unit_template_id']);
                        $unit_templates[$p['unit_template_id']] = mysqli_fetch_assoc($q);
                    }

                    if (empty ($unit_factors[$p['unit_template_id']])) {
                        $q = debug_mysql_query("select * From unit_factors where unit_template_id=" . $p['unit_template_id']);
                        $te = $unit_templates[$p['unit_template_id']];
                        $unit_factors[$p['unit_template_id']][] = [
                            'id' => 0,
                            'small_name' => ($te['unit_small_name'] ? $te['unit_small_name'] : ""),
                            'factor_name' => $te['main_unit_name'],
                            'factor' => 1];
                        while ($row = mysqli_fetch_assoc($q)) {
                            $unit_factors[$p['unit_template_id']][] = $row;
                        }
                    }
                    $p['unit_factors'] = $unit_factors[$p ['unit_template_id']];
                    $q = "select * From settings where plugin_id='72' and `key`='default_display_unit' ";
                    $default_display_unit = mysqli_fetch_assoc(debug_mysql_query($q))['value'];
                    switch ($default_display_unit) {
                        case '1':
                            $unit_factor_id = 0;
                        case '2':
                            $unit_factor_id = $p['default_retail_factor_id'];
                            break;
                        case '3':
                            $unit_factor_id = $p['default_buy_factor_id'];
                            break;
                        default:
                            $unit_factor_id = 0;
                    }
                    $returned_factor = [];
                    if ($unit_factor_id == 0) {
                        $q = "select * From unit_templates where id='{$p['unit_template_id']}' ";
                        $ut = mysqli_fetch_assoc(debug_mysql_query($q));
                        $returned_factor['factor'] = 1;
                        $returned_factor['factor_name'] = $ut['main_unit_name'];
                        $returned_factor['factor_small_name'] = $ut['unit_small_name'];
                    } else {
                        $q = "select * From unit_factors where id='$unit_factor_id' ";
                        $uf = mysqli_fetch_assoc(debug_mysql_query($q));
                        $returned_factor['factor'] = $uf['factor'];
                        $returned_factor['factor_name'] = $uf['factor_name'];
                        $returned_factor['factor_small_name'] = $uf['small_name'];
                    }
                    $returned_factor['id'] = $unit_factor_id ?? 0;
                    $p['factor'] = $returned_factor;
                }
                unset ($p);
            }
        }
    }

    if ($enable_embedded_barcode && !empty($products)) {
        foreach ($products as $id => $product) {
            $product = $embedded_barcode_service->calculateBarcodeFactors($product, $price, $weight, $branch_id);

            $products[$id] = $product;
        }
    }

//    print_r ( $products ) ;

    //$new_products=array_reverse ($products,true);
    //echo "<pre>";
    //print_r($products);
//	print_r($new_products);
    //usort($products,function($a,$b){
    //return strnatcasecmp($a['name'],$b['name']);
    //});

    $enableShowBothOnHandAndAvailableStockOfProducts = showBothOnHandAndAvailableStockOfProducts($currentStaffBranch);
    $wService = new WarehouseService();
    $invoiceItemService = new \App\vendors\Services\InvoiceItemService();
    foreach ($products as $id => &$pp) {
        if ($pp['status'] == 1) {
            unset($products[$id]);
        }
        $pstock = $wService->getStaffProductStock(1, $id, $_SESSION['OWNER']['staff_id']);
        $pp['stock_balance'] = round((float)array_sum($pstock), 6);
        if ($pp['stock_balance'] == null) {
            $pp['stock_balance'] = 0;
        }
        plain_db_connect();
        $cat_query = debug_mysql_query("Select c.name from items_categories ic left join categories c on c.id = ic.category_id where ic.item_id = $id and item_type = '1' ", $link);
        $categories = [];
        if ($cat_query)
            while ($uf = mysqli_fetch_assoc($cat_query)) {
                $categories[] = $uf['name'];
            }
        $pp['category'] = implode(",", $categories);
        $pp['store_balance'] = $pstock;
        $pp['group_prices'] = "";
        if ($enableShowBothOnHandAndAvailableStockOfProducts){
            $invoiceItemService->stockPendingQTY = $wService->getStaffProductStock(1, $id, $_SESSION['OWNER']['staff_id'], null, true, 2);
            $pAvailableQTY = $invoiceItemService->getStaffProductAvailableStock($pstock,1,$id, $_SESSION['OWNER']['staff_id']);
            $pp['stock_available'] = round((float)array_sum($pAvailableQTY), 6);
            $pp['store_available'] = $pAvailableQTY;
        }
        if (!empty($pp['prices'])) {
            $group_prices = $pp['prices'];
            foreach ($group_prices as $index => $price_group) {
                if ($index > 0) {
                    $pp['group_prices'] .= ',';
                }
                $group_price_id = $price_group['group_price_id'];
                $group_price_value = $price_group['price'];
                $pp['group_prices'] .= "$group_price_id:$group_price_value";
            }
        }

    }

    $q = "select value From settings where plugin_id='72' and `key`='advanced_pricing_options' ";
    $advanced_pricing_options = mysqli_fetch_assoc(debug_mysql_query($q))['value'];

    foreach ($products as &$product) {
        if ($product['type'] == 3) {
            getProductAvgPrice($product);
        } //In case the product is a service fetch the averge price by its buy price will be reviewed with Ashraf
        else if($product['type'] == 2) {
            $product['average_price'] = $product['buy_price'];
        }
    }
    // handle item groups search when include item groups enabled
    if($includeItemGroup){
        $products = $products + $itemGroups;
    }

    /**
     * Skip discount calculation if advanced pricing options is disabled
     */
    if($advanced_pricing_options == 0){
        $product['discount'] = null;
    }

    echo json_encode(["data" => $products, "order" => array_keys($products)]);
    die();
}

function isSimilarToDataMatrix(string $val): bool
{
    // regex to validate data matrix
    return preg_match('/^01(\d{14})/', $val);
}

function addDataMatrixConditions(string $val): string
{
    $gtin = substr($val, 2, 14);
    return "(barcode LIKE '%$gtin%') OR ";
}

function showBothOnHandAndAvailableStockOfProducts($currentStaffBranch): bool
{
    if (!isset($currentStaffBranch)){
        $currentStaffBranch = $_SESSION['branch_id'];
    }
    $query = debug_mysql_query("select `key`,`value` from settings where plugin_id = 72 and `key` = 'enable_show_both_on_hand_and_available_stock_of_products' and `branch_id` = {$currentStaffBranch}");
    $result = mysqli_fetch_assoc($query);
    return (bool)!empty($result['value']);
}

function getProductAvgPrice(&$product)
{
    $cost = calculate_bundle_cost($product['id']);
    $avgPrice = $product['average_price'];
    if ($cost) {
        $product['average_price'] = $cost;
    }
    if (!(isset($_GET['model']) && $_GET['model'] == "requisition")) {
        return;
    }
    if (isset($_GET['orderType']) && $_GET['orderType'] == "2") {
        $product['average_price'] = $avgPrice;
    }
}

if ($_GET['action'] == 'product_find')
//php8 fix
    $client = isset($_GET['client']) ? $_GET['client'] : 0;
$store_id = isset($_GET['store_id']) ? $_GET['store_id'] : NULL;
$is_all_products = isset($_GET['all_products']) ? $_GET['all_products'] : true;
$enablePurchasingTax = $_GET['enable_purchasing_tax'] ?? false;
if ($is_all_products === "false") {
    $is_all_products = false;
}
$product_ids = isset($_GET['product_ids']) ? $_GET['product_ids'] : [];
product_find($_GET['val'], $client, $store_id, $is_all_products, ($_GET['products_only'] ?? false), (intval($_GET['type'] ?? null) ?: null), $product_ids,
 ($_GET['include_item_groups'] ?? false), ($_GET['item_groups_only'] ?? false), $enablePurchasingTax);
