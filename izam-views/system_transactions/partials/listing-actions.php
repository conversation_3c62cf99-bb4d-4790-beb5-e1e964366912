<div class="dropstart" data-lt-dropdown="true">
    <button class="btn btn-dots" type="button" data-bs-offset="0,10"
            data-bs-toggle="dropdown" aria-expanded="false">
        <i class="mdi mdi-dots-horizontal"></i>
    </button>
    <ul class="dropdown-menu text-start">
        <li>
            <a class="dropdown-item"  <?= isset($_GET['iframe']) ? 'target=_blank' : '' ?>  href="/owner/journals/view/<?= $record->journal_id ?>">
                <i class="mdi mdi-eye text-success"></i>
                <span><?= __t('View') ?></span>
            </a>
        </li>
        <li>
            <a class="dropdown-item" <?= isset($_GET['iframe']) ? 'target=_blank' : '' ?> href="/owner/journals/edit/<?= $record->journal_id ?>">
                <i class="mdi mdi-pencil text-info"></i>
                <span><?= __t('Edit') ?></span>
            </a>
        </li>
        <?php if (!$record->Journal->is_automatic) { ?>
            <li>
                <button class="dropdown-item" type="button"
                        data-bs-toggle="modal" data-bs-target="#modalDelete"
                        data-md-message="<?= sprintf(__t('Are You Sure You Want To Delete This %s ?'), __t('System Transactions')) ?>"
                        data-md-action="/owner/journals/delete/<?= $record->journal_id ?>"
                        data-md-method="GET"
                        data-md-data=''>
                    <i class="mdi mdi-delete text-danger"></i>
                    <span><?= __t('Delete') ?></span>
                </button>
            </li>
        <?php } ?>

        <?php if ($treasuryData->type == App\Utils\TreasuryTypeUtil::BANK and !empty($treasuryData->currency_code)) {
            if (!empty($record->bank_transaction_id)) { ?>
                <li>
                    <a class="dropdown-item"
                       href="/v2/owner/bank_transactions/<?= $record->id ?>/unmatch_system_transaction">
                        <i class="mdi mdi-arrow-collapse-vertical text-primary"></i>
                        <span><?= __t('Unmatch') ?></span>
                    </a>
                </li>
            <?php }
        } ?>
    </ul>
</div>
