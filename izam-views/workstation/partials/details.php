<?php

use Izam\View\Form\Element\LinkWithTooltip;
use Izam\View\Form\Helper\Show\LinkWithTooltipHelper;

$linkHelper = new LinkWithTooltipHelper();

$depreciateIconClasses = $data->depreciate ? 'mdi-check-bold text-primary' : 'mdi-close-thick text-danger';
$depreciateText = $data->depreciate ? __t('Depreciate') : __t('No depreciation');

?>


<div class="info-section">
    <!-- Info Section Title -->
    <div class="info-section-title">
        <span><?=sprintf(__t("%s Information"),__t('Workstation'),)?></span>
    </div>
    <!-- Info Section Body -->
    <div class="info-section-body">
        <div class="row">

            <!-- Info Item -->
            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('Name')?>
                    </div>
                    <div class="info-item-value">
                        <span class="fw-medium"><?=$data->name?></span>
                    </div>
                </div>
            </div>

            <!-- Info Item -->
            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('Total Cost')?>
                    </div>
                    <div class="info-item-value">
                        <span class="text-amount lh-1"><?= format_price($data->total_cost, getCurrentSite('currency_code') )  ?></span>
                    </div>
                </div>
            </div>

            <?php if ($data->unit): ?>
            <!-- Info Item -->
            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('Unit')?>
                    </div>
                    <div class="info-item-value">
                        <?=$data->unit?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($data->description): ?>
            <!-- Info Item -->
            <div class="col-lg-6">
                <div class="info-item">
                    <div class="info-item-label">
                        <?=__t('Description')?>
                    </div>
                    <div class="info-item-value">
                        <div class="info-item-pre"><?= $data->description ?></div>
                    </div>
                </div>
            </div>
            <?php endif; ?>


        </div>
    </div>
</div>

<div class="info-section">
                <!-- Info Section Title -->
                <div class="info-section-title">
                    <span><?=__t('Costs')?></span>
                </div>

                <?php if (count($data->other_expenses)): ?>
                    <div class="info-section">
                        <!-- Info Section Title -->
                        <div class="info-section-title bg-transparent">
                            <span><?=__t("Other Expenses")?></span>
                        </div>
                        <!-- Info Section Body -->
                        <div class="info-section-body pt-0 mb-17">
                            <div class="row">

                                <div class="col-lg-12">
                                    <div class="info-table-responsive">
                                        <table class="info-table info-table-border-inner">
                                            <thead>
                                            <tr>
                                                <th style="background-color: white" width="50%"> <?=__t('Cost Amount')?></th>
                                                <th style="background-color: white" width="50%"><?=__t('Account')?></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <?php
                                            foreach($data->other_expenses as $i => $expense):?>
                                            <tr>
                                                <td>
                                                    <span class="me-2"><?= $expense->amount ?></span>
                                                    <?php if ($data->unit): ?>
                                                        <span class="text-light-3">/<?= $data->unit ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="me-2" style="font-weight: 500 !important"><?= $expense->account->name ?></span>
                                                    <?= $linkHelper->render((new LinkWithTooltip())->setValue('/v2/owner/chart-of-accounts/accounts/'.$expense->account->id)->setLabel('#' .$expense->account->id )); ?>
                                                </td>
                                            </tr>
                                            <?php endforeach;?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Info Section Body -->
                
                <?php if ($data->wages_cost): ?>
                    <?php if ($data->other_expenses): ?>
                        <hr class="info-section-separator">
                    <?php endif; ?>
                    <div class="info-section">
                        <!-- Info Section Title -->
                        <div class="info-section-title bg-transparent">
                            <span><?=__t("Wages")?></span>
                        </div>
                        <!-- Info Section Body -->
                        <div class="info-section-body pt-0 mb-17">
                            <div class="row">

                                <div class="col-lg-12">
                                    <div class="info-table-responsive">
                                        <table class="info-table info-table-border-inner">
                                            <thead>
                                            <tr>
                                                <th style="background-color: white" width="50%"> <?=__t('Cost')?></th>
                                                <th style="background-color: white" width="50%"><?=__t('Account')?></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <span class="me-2"><?= $data->wages_cost ?></span>
                                                        <?php if ($data->unit): ?>
                                                            <span class="text-light-3">/<?= $data->unit ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="me-2" style="font-weight: 500 !important"><?= $data->account?->name ?></span>
                                                        <?= $linkHelper->render((new LinkWithTooltip())->setValue('/v2/owner/chart-of-accounts/accounts/'.$data->account->id)->setLabel('#' .$data->account->id )); ?>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                
                <?php if ($data->asset): ?>
                    <hr class="info-section-separator">
                    <div class="info-section">
                        <!-- Info Section Title -->
                        <div class="info-section-title bg-transparent">
                            <span><?=__t("Asset")?></span>
                        </div>
                        <!-- Info Section Body -->
                        <div class="info-section-body pt-0">
                            <div class="row">

                                <div class="col-lg-12">
                                    <div class="info-table-responsive">
                                        <table class="info-table info-table-border-inner">
                                            <thead>
                                            <tr>
                                                <th style="background-color: white" width="35%%"> <?=__t('Cost')?></th>
                                                <th style="background-color: white" width="35%"><?=__t('Asset')?></th>
                                                <th style="background-color: white" width="30%"><?=__t('Depreciate')?></th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <span class="me-2"><?= $data->assets_cost ?></span>
                                                        <?php if ($data->unit): ?>
                                                            <span class="text-light-3">/<?= $data->unit ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="me-2" style="font-weight: 500 !important"><?= $data->asset?->name ?></span>
                                                        <?= $linkHelper->render((new LinkWithTooltip())->setValue('/owner/assets/view/'. $data->asset?->id)->setLabel('#' . $data->asset?->id )); ?>
                                                    </td>
                                                    <td>
                                                       <span class="d-flex align-items-center ms-xl-4">
                                                               <i class="mdi <?= $depreciateIconClasses ?> fs-12 align-top mt-xl-n2 me-2"></i>
                                                               <span class="align-top"><?= $depreciateText ?></span>
                                                       </span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
