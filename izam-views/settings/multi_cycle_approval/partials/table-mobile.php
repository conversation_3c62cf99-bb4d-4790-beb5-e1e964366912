<?php

use App\Utils\AttendancePermissionTypesUtil;
use Izam\Daftra\Common\Utils\MultiCycleApprovalUtil;
use Izam\Daftra\Common\Utils\PluginUtil;

?>

<div class="d-block d-lg-none">
    <div>
        <div class="listing-table-wrapper" data-lt-wrapper="true">
            <div class="listing-table-responsive" data-lt-responsive="true">
                <table class="table listing-table">
                    <thead data-lt-head="true">
                    <tr>
                        <th><?= __t("Configuration") ?></th>
                        <th><?= __t("Name") ?></th>
                        <th><?= __t("Type") ?></th>
                        <th><?= __t("Leave") ?></th>
                        <th><?= __t("Departments") ?></th>
                        <?php if(ifPluginActive(PluginUtil::BranchesPlugin)): ?>
                            <th><?= __t("Branches") ?></th>
                        <?php endif; ?>
                        <th><?= __t("Status") ?></th>
                        <?php if(!isset($_GET['iframe'])): ?>
                            <th width="45" class="listing-cell-end text-end">
                                <?= !empty($table->getSorts()) ? $this->includeSection('partials/sorting_fields') : ''; ?>
                            </th>
                        <?php else: ?>
                            <th></th>
                        <?php endif; ?>
                    </tr>
                    </thead>
                    <tbody>


                    <?php 

                    $requestTypesMap = $listingExtraData['request_types'];
                    $leaveTypesMap = $listingExtraData['leave_types'];
                    $departmentsMap = $listingExtraData['departments'];
                    $branchesMap = $listingExtraData['branches'];
                    
                    foreach ($table->getRows() as $key => $row) {
                        $showUrl = 'javascript:void(0);';
                        $targetUrl = '';
                        foreach ($listingRowActions as $k => $action) {

                            if (strtolower($action['title']) == 'view' || strtolower($action['title']) == 'show') {
                                $showUrl = getUrlAction($action, $row->getData());
                                $targetUrl = '_blank';
                                continue;
                            }
                        }
                        $rowDataValue = json_decode($row->getData()->value, true);
                        $rowDepartments = __t("All");
                        $rowBranches = __t("All");
                        $rowConfigSubType = __t("All");
                        $rowLeaveType = __t("All");
                        $leaveMainTypes = AttendancePermissionTypesUtil::getOptions();
                        if(!empty($rowDataValue['configuration_type'])){
                            if($rowDataValue['configuration_type'] == MultiCycleApprovalUtil::CONFIG_TYPE_LEAVES){
                                if(!empty($rowDataValue['type'])){    
                                    $rowSubtypeCollection = collect($rowDataValue['type']);
                                    $rowConfigSubType =  $rowSubtypeCollection->map(function($item) use ($leaveMainTypes){
                                        return $leaveMainTypes[$item] ?? null;
                                    })->filter()->implode(', ');
                                }
                                if(!empty($rowDataValue['leave_type'])){                                    
                                    $rowSubtypeCollection = collect($rowDataValue['leave_type']);
                                    $rowLeaveType =  $rowSubtypeCollection->map(function($item) use ($leaveTypesMap){
                                        return $leaveTypesMap[$item] ?? null;
                                    })->filter()->implode('name', ', ');
                                }
                            }else{
                                if(!empty($rowDataValue['request_type'])){                                    
                                    $rowSubtypeCollection = collect($rowDataValue['request_type']);
                                    $rowConfigSubType =  $rowSubtypeCollection->map(function($item) use ($requestTypesMap){
                                        return $requestTypesMap[$item] ?? null;
                                    })->filter()->implode('name', ', ');
                                }
                                $rowLeaveType = "--";
                            }
                        } 
                        if(!empty($rowDataValue['departments'])){
                            $rowDeptCollection = collect($rowDataValue['departments']);
                            $rowDepartments =  $rowDeptCollection->map(function($item) use ($departmentsMap){
                                return $departmentsMap[$item] ?? null;
                            })->filter()->implode('name', ', ');
                        }
                        if(!empty($rowDataValue['branches'])){
                            $rowBranchesCollection = collect($rowDataValue['branches']);
                            $rowBranches = $rowBranchesCollection->map(function($item) use ($branchesMap){
                                return $branchesMap[$item] ?? null;
                            })->filter()->implode('name', ', ');
                        }
                        $statusColorClass = MultiCycleApprovalUtil::getStatusColorClass($rowDataValue['status'] ?? '');
                        ?>

                        <tr tabindex="<?= $key ?>>">
                            <td>
                                <?= MultiCycleApprovalUtil::getConfigTypesList()[$rowDataValue['configuration_type']] ?>
                            </td>
                            <td>
                                <a style="<?= $showUrl == 'javascript:void(0);' ? 'cursor:auto' : '' ?>" target="<?= $targetUrl ?>" href="<?= $showUrl ?>" class="listing-cell-link"></a>
                                <?= $rowDataValue['name'] ?>
                            </td>
                            <td>
                                <?= $rowConfigSubType ?>
                            </td>
                            <td>
                                <?= $rowLeaveType ?>
                            </td>
                            <td>
                                <?= $rowDepartments ?>
                            </td>
                            <?php if(ifPluginActive(PluginUtil::BranchesPlugin)): ?>
                                <td>
                                    <?=  $rowBranches ?>
                                </td>
                            <?php endif; ?>
                            <td>
                                <div class="text-nowrap">
                                    <div class="status-circle" style="display: flex;flex-direction: row;gap: .5rem;align-items: center">
                                        <i class="<?= $statusColorClass ?>" style="width: 10px;height: 10px;border-radius: 50px;"></i>
                                        <span><?= MultiCycleApprovalUtil::getStatusList()[$rowDataValue['status']] ?? '' ?></span>
                                    </div>
                                </div>
                            </td>
                            <td class="text-end h-0 listing-cell-end" data-lt-dropdown-cell="true">
                                <div class="d-flex flex-column h-100">
                                    <?= $this->includeSection('partials/row-actions-mobile', ['row' => $row]) ?>
                                </div>
                            </td>
                         </tr>

                        <?php
                    } ?>

                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>
