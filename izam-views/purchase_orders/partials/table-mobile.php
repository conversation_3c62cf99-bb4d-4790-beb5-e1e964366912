<?php

use Izam\Daftra\Common\Services\AvatarURLGenerator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\RequisitionDeliveryStatusUtil;
use Izam\Daftra\Common\Utils\PurchaseInvoicePaymentStatusUtil;

?>
<div class="d-block d-lg-none">
    <div class="listing-table-wrapper" data-lt-wrapper="true">
        <div class="listing-table-responsive" data-lt-responsive="true">
            <table class="table listing-table">
                <thead>
                <tr>
                    <th class="listing-cell-start"></th>
                    <th></th>
                    <th width="45" class="listing-cell-end text-end">
                        <?= !empty($table->getSorts()) ? $this->includeSection('partials/sorting_fields') : ''; ?>
                    </th>
                </tr>
                </thead>
                <tbody>
                <?php
                foreach ($table->getRows() as $row) {
                    $purchaseInvoice = $row->getData();
                    $viewKey = 'view'; 
                    if($purchaseInvoice->entity->getEntityKey() == EntityKeyTypesUtil::PURCHASE_REFUND){
                        $viewKey = 'view_refund';
                    }  

                    $viewUrl = "/owner/purchase_invoices/$viewKey/$purchaseInvoice->id";
                    $supplier = $purchaseInvoice->supplier;
                    $country_code = getCurrentSite('country_code');
                    $supplierBusinessName = $supplier->business_name ?? '';
                    $lastAction = $purchaseInvoice->lastAction['ActionLine'];
                    $delivery_statuses = RequisitionDeliveryStatusUtil::getList();
                    ?>

                    <tr>
                        <td>
                            <a href="<?= $viewUrl ?>" class="listing-cell-link"></a>
                            <?php if ($supplier){?>
                            <div class="thumb-text-group align-items-start">
                                <img class="thumb" loading="lazy"
                                     src="<?= AvatarURLGenerator::generate($supplierBusinessName, $supplier->id, 40, null) ?>"/>
                                <div class="thumb-text">
                                    <p>#<?= $purchaseInvoice->no . '- ' . format_date($purchaseInvoice->date) ?></p>
                                    <a class="thumb-subtitle-text text-decoration-none"
                                       href="/owner/suppliers/view/<?= $supplier->id ?>"><?= $supplierBusinessName ?>
                                        <span>#<?= $supplier->supplier_number ?></span></a>
                                </div>
                            </div>
                            <?php  } ?>
                            <div class="mt-3">
                                <p class="mb-0">
                                    <?= format_date($lastAction['created']) . ' ' . date('H:i:s', strtotime($lastAction['created'])) ?>
                                </p>
                            </div>
                        </td>
                        <td>
                            <div class="text-end">
                                <div class="mb-4">
                                    <strong><?= format_price($purchaseInvoice->summary_total, $purchaseInvoice->currency_code) ?></strong>
                                </div>
                                <?php if ($purchaseInvoice->summary_refund != 0) { ?>
                                    <div class="text-danger mb-4">
                                        <strong> <?= __t('Refunded') ?>:</strong>
                                        <?= format_price($purchaseInvoice->summary_refund, $purchaseInvoice->currency_code) ?>
                                    </div>
                                <?php } ?>
                                <?php if ($purchaseInvoice->payment_status == 1) { ?>
                                    <div class="text-dark-3 mb-4">
                                        <strong><?= __t('Balance Due') ?>
                                            :</strong> <?= format_price($purchaseInvoice->summary_unpaid, $purchaseInvoice->currency_code) ?>
                                    </div>
                                <?php } ?>
                                <div class="hstack justify-content-end flex-wrap gap-4">
                                    <?php
                                    $status = $purchaseInvoice->requisition_delivery_status ?? -1;
                                    if ($purchaseInvoice->is_received && is_null($purchaseInvoice->requisition_delivery_status)) {
                                        $status = $purchaseInvoice->is_received;
                                    }
                                    $has_requisitions = !empty($row->getData()->purchase_order_requisitions);
                                    if (!$purchaseInvoice->draft && $has_requisitions) { ?>
                                        <span class="badge text-bg-<?= $delivery_statuses[$status]['cssClass'] ?>">
                                            <?= __t($delivery_statuses[$status]['label']) ?>
                                        </span>
                                    <?php } ?>
                                </div>

                                <div class="hstack justify-content-end flex-wrap gap-4 mt-3">
                                    <?php if (($purchaseInvoice->summary_paid - $purchaseInvoice->summary_total) > MINOVERPAID && !$purchaseInvoice->draft) { ?>
                                        <span class="badge text-bg-<?= PurchaseInvoicePaymentStatusUtil::getStatusCssClass(PurchaseInvoicePaymentStatusUtil::PO_STATUS_OVERPAID) ?>"><?= PurchaseInvoicePaymentStatusUtil::getPaymentStatuses()[PurchaseInvoicePaymentStatusUtil::PO_STATUS_OVERPAID] ?></span>
                                    <?php } else {
                                        $status = $purchaseInvoice->draft ? -1 : $purchaseInvoice->payment_status ?? '0';
                                        $PurchaseOrderAllPaymentStatus = PurchaseInvoicePaymentStatusUtil::getPaymentStatuses();
                                        ?>
                                        <span class="badge text-bg-<?= PurchaseInvoicePaymentStatusUtil::getStatusCssClass($status) ?>">
                                    <?= PurchaseInvoicePaymentStatusUtil::getPaymentStatuses()[$status] ?>
                                </span>
                                    <?php } ?>
                                </div>


                            </div>
                        </td>
                        <td class="text-end h-0 listing-cell-end" data-lt-dropdown-cell="true">
                            <a href="<?= $viewUrl ?>" class="listing-cell-link"></a>
                            <?= $this->includeSection('partials/row-actions-mobile', ['row' => $row]) ?>
                        </td>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
