<style>
    @media (min-width: 768px) {
        .l-btn-group-box--responsive .l-btn-group {
            overflow: visible;
        }
    }
    [dir=ltr] .l-page-nav-btn-group>.l-btn-group {
        padding-left: 0px;
    }

    [dir=rtl] .l-page-nav-btn-group>.l-btn-group {
        padding-right: 0px;
    }

    .approve-reject-action-btn {
        height: 38px;
        margin-inline-end: 15px;
        font-weight: bold;
         outline: none;
        border: 0;
        padding: 0px 15px;
        display: flex;
        align-items: center;
        justify-content: center;

    }

    .approve-reject-action-btn i {
        font-size: 18px;
        margin-inline-end: 5px;
    }

    .reject-btn {
        background-color: #D1451c;
    }

    .approve-btn {
        background-color: #4d9b64;
    }

    .from-to-item {
        display: flex;
        align-items: center;
    }

    .from-to-item .from-to-label {
        color: var(--color-subtitle);
        display: block;
        font-weight: 400;
        margin-inline-end: 8px;
    }

    .from-to-item .from-to-arrow {
        color: #1977F2;
        margin: 0px 8px;
        font-size: 24px;
    }

    table {
        border-collapse: collapse;
        border: 1px solid var(--color-subtitle);

    }

    th {
        color: var(--color-subtitle);
        font-weight: 400;
        width: 50%;
    }

    th,
    td {
        border-bottom: 1px solid var(--color-subtitle);
        padding: 8px 10px;

    }

    .info-item .info-item-value {
        font-size: 0.875rem;
        color: var(--bs-black);
    }

    .thumb-text-group {
        display: flex;
        align-items: center;
    }

    .thumb {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 2px;
    }

    .thumb.thumb-4xl {
        width: 126px;
        height: 126px;
    }

    [dir=ltr] .ms-6,
    [dir=ltr] .thumb-text {
        margin-left: 0.75rem !important;
    }

    .thumb-text {
        font-size: 0.875rem;
    }

    .mb-0,
    .title-big-text p,
    .title-text p,
    .title-text small,
    .title-text .small,
    .date-text p,
    .thumb-text p {
        margin-bottom: 0 !important;
    }

    .text-light-3,
    .title-subtitle-text,
    .date-subtitle-text,
    .thumb-subtitle-text {
        --bs-text-opacity: 1;
        color: rgba(var(--bs-light-3-rgb), var(--bs-text-opacity)) !important;
    }

    .thumb-title-text {
        font-size: 20px;
        font-weight: 500;
    }
    [dir=rtl] .thumb-text {
        margin-right: 0.75rem !important;
    }

    .text-black,
    .thumb-title-text {
        --bs-text-opacity: 1;
    }

    .thumb-flex {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 126px;
    }

    .info-timeline {
        position: relative;
    }
    [dir=ltr] .info-timeline {
        padding-left: 35px;
    }
    [dir=rtl] .info-timeline {
        padding-right: 35px;
    }
    .info-timeline::before {
        content: "";
        position: absolute;
        top: 3px;
        height: calc(100% - 6px);
        border: 1px dashed #e4ebf2;
    }

    [dir=ltr] .info-timeline::before {
        left: 10px;
    }

    [dir=rtl] .info-timeline::before {
        right: 10px;
    }

    .info-timeline-item:not(:last-child) {
        margin-bottom: 30px;
    }

    .info-timeline-item .ui-show-card-section-item-label {
        margin-bottom: 6px;
    }

    
   

    .info-timeline-item::before {
        content: "";
        width: 12px;
        height: 12px;
        border-radius: 50px;
        background: #e4ebf2;
        position: absolute;
        transform: translateY(3px);
    }
    [dir=ltr] .info-timeline-item::before {
        left: 5px;
    }
    [dir=rtl] .info-timeline-item::before {
    right: 5px;
    }

    .info-timeline-item:last-child::before {
        bottom: 6px;
    }
    [dir=ltr] .info-link::after {
    margin-left: 5px;
    }

    .info-link::after {
        content: "\f08e";
        font-size: 12px;
        font-family: "Font Awesome 5 Pro";
        font-weight: 400;
        text-decoration: none;
    }
    .alert-warning {
        color: #855008;
        background-color: #ffebcf;
        border-color: #ffe2bc;
    }
</style>
<link rel="stylesheet" href="/v2/css/history-status.css">
<?php
     use App\Services\AttendanceReportsService;
     use App\Utils\LeaveApplicationStatusUtil;
     use App\Utils\EntityKeyTypesUtil;
     use Izam\Navigation\Navigation;
     use Izam\Daftra\Common\Utils\PermissionUtil;
     use App\Facades\Permissions;
use App\Repositories\PostRepository;
use Izam\Navigation\Services\PaginationService;
    use Izam\Navigation\Helpers\PaginationHelper;
    use Izam\Daftra\Common\Utils\PostTypeUtil;
    use App\Services\LeaveApplicationService;
    if (empty($_GET['iframe'])) {
        $extends = 'layouts/deprecated-layout';
    } else {
        $extends = 'layouts/deprecated-iframe-layout';
    }
    $entityKey = EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY;

    $this->viewVars['title_for_layout'] = sprintf(__t('Application #%s | %s'), $data->id, __t('Leave Applications'));
    $this->viewVars['_PageBreadCrumbs'] = $this->sharedVars['generalBreadCrumbs'];

     $hasApproveOrRejectPermission = Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_LEAVE_APPLICATION);
     $hasEditOrDeletePermission = Permissions::checkPermission(PermissionUtil::EDIT_DELETE_ALL_LEAVE_APPLICATIONS) ||  Permissions::checkPermission(PermissionUtil::EDIT_DELETE_HIS_OWN_LEAVE_APPLICATIONS);
 
     
     $reportsService = resolve(AttendanceReportsService::class);
     $LeaveBalanceForStaff = $reportsService->getLeaveBalanceForStaffIdByType($data->staff_leave_application->id);
     $displayNoLeaveBalanceWarning = false;
     $remainingBalance = '';
     if($data->leave_application_type){
        $remainingBalance = $LeaveBalanceForStaff['leaves'][$data->leave_application_type->id]['remaining'] ?? 0;
        $remainingBalance = (int)explode(" ",$remainingBalance)[0];
        if ($data->status == LeaveApplicationStatusUtil::Pending &&
        ($remainingBalance < $data->days)){
            $displayNoLeaveBalanceWarning = true;
        }   
    
    }
     $viewManager = resolve(\Izam\Forms\View\Helper\Form\ViewHelper\ViewActionManagerHelper::class);
     $row = new \Izam\Forms\View\Helper\Show\ShowRow();
 
     $showPagination = PaginationService::exists($entityKey) && empty(request()->get('iframe'));
     if($showPagination){
         $paginationHelper = (new PaginationHelper());
         $paginationHelper->setPaginationInfo(PaginationService::get($entityKey));
         $izamNavigation= new  Navigation ();
         if ($paginationHelper->hasNext($id))    $izamNavigation->setPageNextUrl($paginationHelper->getNextUrl($id));
         if ($paginationHelper->hasPrevious($id))    $izamNavigation->setPagePreviousUrl($paginationHelper->getPreviousUrl($id));
         $izamNavigation->setPageAddUrl(route('owner.entity.create', $entityKey));
     }
 
     $actionByRole = '';
     if($data['approved_by']) {
         if($data['staff_leave_application']['staff_info']['staff_direct_manager']){
             if($data['approved_by'] == $data['staff_leave_application']['staff_info']['staff_direct_manager']['id']){
                 $actionByRole = __t('Direct Manager');
             }
         } 
 
         $departmentManagers = $data['staff_leave_application']['staff_info']['department']['department_managers'] ?? [];
         foreach($departmentManagers as $departmentManager){
             if($departmentManager['manager_id'] == $data['approved_by']){
                 $actionByRole = __t('Department Manager');
                 break;
             }
         }
    }
    extract($extraData);
    $leaveApplicationPermissions =  getEntityPermissionsForRowActions($entityKey);
    $deletePermissions = $leaveApplicationPermissions['delete'];
    $updatePermissions = $leaveApplicationPermissions['update'];
    $is_own = $data->staff_id == getAuthOwner('staff_id');  
    $dateFormat = getDateFormats('std')[getCurrentSite('date_format')];

    $noteRepository = resolve(PostRepository::class);
    $notesCount = $noteRepository->getPostsCount($data->id ,PostTypeUtil::LEAVE_APPLICATION_TYPE);
    $leaveApplicationService = resolve(LeaveApplicationService::class);
    $userCanApprove = $leaveApplicationService->authenticateUserCanUpdateStatus($data->id);
    $notesAndAttachmentsPermission = Permissions::checkPermission(PermissionUtil::APPROVE_OR_REJECT_LEAVE_APPLICATION) && $userCanApprove;

    $repo = resolve(\Izam\Template\TemplateRepository::class);
    $viewTemplates = $repo->getEntityViewTemplates(EntityKeyTypesUtil::LEAVE_APPLICATION_ENTITY_KEY);
?>

<style>
    @media (min-width: 768px) {
        .ui-show-card-content-inner--spacing-20 {
            padding: 3px !important
        }
    }

    @media (max-width: 767px) {
        .ui-show-card-content-inner--spacing-20 {
            padding: 0 !important;
        }
    }
</style>

<?php $this->section('page-head') ?>
<div class="m-app-page-head">
</div>

<?php $this->endSection() ?>

<?php $this->section('page-head') ?>
<div class="m-app-page-head">
    <div class="m-app-page-head-pc">
        <div class="l-container">
            <div class="l-flex-row l-flex--align-center">
                <div class="l-flex-col-lg-6">
                    <div class="l-big-title l-big-title--title-only">
                            <div class="u-text-nowrap">
                                <h1 class="ui-big-title u-text-color-black">
                                    <span>
                                        <?=__t('Application')?> <span class="l-inline-block l-reservation-order-num">
                                            <span>#<?=$data->id?></span>
                                        </span>
                                    </span>
                                </h1>
                            </div>&nbsp;

                            <div class="l-v-line l-v-line--spacing-x">
                                <span class="u-bg-color-secondary"></span>
                            </div>
                            <?= $main_status ?>
                    </div>
                </div>
                <?php
                
                    $undoApprovalBtn = '<a href="#" type="button"
                    class="undoApprovalLeaveApplicationBtnJs l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default approve-reject-action-btn">
                        <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                        <span class="ui-btn-inner-content">
                            <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-arrow-u-right-top u-text-weight-normal"></i>
                            <span class="ui-btn-inner-text">' . __t('Undo Approval') . '</span>
                        </span>
                    </a>';
                ?>
                <div class="l-flex-col-lg-6 u-text-align-end status-list l-flex l-flex--align-center l-flex--wrap l-flex--justify-end gap-10">
                <?php if ($hasApproveOrRejectPermission ): ?>
                        <?php if($data->status == LeaveApplicationStatusUtil::Pending && $userCanApprove):?>
                
                            <button class="rejectLeaveApplicationBtnJs l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-danger u-text-color-white u-text-hover-color-white approve-reject-action-btn" type="button" data-bs-toggle="modal" data-bs-target="#updateLeaveApplicationStatusModal">
                                <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                <span class="ui-btn-inner-content">
                                    <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-close-circle u-text-weight-normal"></i>
                                    <span class="ui-btn-inner-text"><?=__t('Reject')?></span>
                                </span>
                            </button>

                            <button type="button" class="acceptLeaveApplicationBtnJs l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white approve-reject-action-btn"  data-bs-toggle="modal" data-bs-target="#updateLeaveApplicationStatusModal">
                                <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                <span class="ui-btn-inner-content">
                                    <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-check-circle u-text-weight-normal"></i>
                                    <span class="ui-btn-inner-text"><?=__t('Approve')?></span>
                                </span>
                            </button>

                            <?php  if( $applicationCanUndoApproval &&  $userCanUndo): 
                                        echo $undoApprovalBtn;
                                    endif;
                            ?>
                        <?php elseif ($data->status == LeaveApplicationStatusUtil::Rejected && $userCanUndo  ):?>
                                <a type="submit" href="#"  class="undoRejectionLeaveApplicationBtnJs l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple  u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default approve-reject-action-btn">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                    <span class="ui-btn-inner-content">
                                        <i class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-arrow-u-right-top u-text-weight-normal"></i>
                                        <span class="ui-btn-inner-text"><?=__t('Undo Rejection')?></span>
                                    </span>
                                </a>    
                        <?php elseif ($applicationCanUndoApproval &&  $userCanUndo): 
                                 echo $undoApprovalBtn;
                         endif;?>
                <?php endif; ?>

                    <div class="l-flex-inline l-flex--align-center l-page-nav-btn-group">
                        <div class="l-btn-group l-btn-group--separated" role="group">
                            <?php if($showPagination): ?>
                                <a onclick="initPageLoader()" title="<?=__t('Previous')?>" href="<?=$paginationHelper->getPreviousUrl($id)?>" <?=!$paginationHelper->hasPrevious($id)?'disabled=""':''?> class="l-btn-inline l-btn-group-btn ui-btn-icon u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default">
                                    <span class="ui-btn-inner-content">
                                        <i class="ui-btn-inner-icon l-icon l-icon--size-38 ui-icon--size-22 mdi mdi-chevron-up"></i>
                                    </span>
                                </a>
                                <a onclick="initPageLoader()" title="<?=__t('Next')?>" href="<?=$paginationHelper->getNextUrl($id)?>" <?=!$paginationHelper->hasNext($id)?'disabled=""':''?> class="l-btn-inline l-btn-group-btn ui-btn-icon u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default">
                                    <span class="ui-btn-inner-content">
                                        <i class="ui-btn-inner-icon l-icon l-icon--size-38 ui-icon--size-22 mdi mdi-chevron-down"></i>
                                    </span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="m-app-page-head-phone">
        <div class="l-container">
            <div class="l-items-list l-items-list--spacing-8 u-text-color-black">
                <div class="l-flex-row l-flex-row--spacing-5 l-flex--nowrap l-flex--align-center">
                    <div class="l-flex-col-auto l-flex-col--shrink-1 l-flex-col--overflow-auto">
                        <div class="l-big-title l-big-title--title-only">
                            <div class="u-text-nowrap">
                                <h1 class="ui-big-title u-text-color-black">
                                    <span><?=__t('Application')?>&nbsp;#<?=$data->id?></span>
                                </h1>
                            </div>
                        </div>
                        <?= $main_status ?>
                    </div>
                    <div class="l-flex-col-auto l-flex-col--ms-auto">
                        <div class="l-flex-inline l-flex--align-center l-page-nav-btn-group">
                        <?php if ($hasApproveOrRejectPermission && $userCanApprove): ?>
                            <?php if(ucfirst($data->status) == LeaveApplicationStatusUtil::Pending ):?>

                                <button class="rejectLeaveApplicationBtnJs l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-danger u-text-color-white u-text-hover-color-white approve-reject-action-btn" type="button" data-bs-toggle="modal" data-bs-target="#updateLeaveApplicationStatusModal">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                    <span class="ui-btn-inner-content">
                                        <i style="margin: 0px;" class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-close-circle"></i>
                                    </span>
                                </button>

                                <button type="button" class="acceptLeaveApplicationBtnJs l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple u-bg-color-save u-text-color-white u-text-hover-color-white approve-reject-action-btn"  data-bs-toggle="modal" data-bs-target="#updateLeaveApplicationStatusModal">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                    <span class="ui-btn-inner-content">
                                        <i style="margin: 0px;" class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-check-circle"></i>
                                    </span>
                                </button>
                            <?php elseif ($data->status == LeaveApplicationStatusUtil::Rejected ):?>
                                    <a type="submit" href="/v2/owner/entity/leave_application/<?=$data->id?>/revert-to-pending" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple  u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default approve-reject-action-btn">
                                        <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                        <span class="ui-btn-inner-content">
                                            <i style="margin: 0px;" class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-arrow-u-right-top"></i>
                                        </span>
                                    </a>
                            <?php elseif ($data->status == LeaveApplicationStatusUtil::Approved ):?>
                                    <a href="/v2/owner/entity/leave_application/<?=$data->id?>/revert-to-pending"  type="submit" class="l-btn-inline l-btn--text-center ui-btn-w-icon l-btn-w-icon ui-btn--hover-ripple  u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default approve-reject-action-btn" >
                                        <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark" style="top: 366.5px; left: -115.719px;"></span>
                                        <span class="ui-btn-inner-content">
                                            <i style="margin: 0px;" class="ui-btn-inner-icon l-icon l-icon--size-20 ui-icon--size-16 mdi mdi-arrow-u-right-top"></i>
                                        </span>
                                    </a>
                            <?php endif;?>
                        <?php endif; ?>
                            <div class="l-btn-group l-btn-group--separated" role="group">
                                <?php if($showPagination): ?>
                                    <a onclick="initPageLoader()" title="<?=__t('Previous')?>" href="<?=$paginationHelper->getPreviousUrl($id)?>"  <?=!$paginationHelper->hasPrevious($id)?'disabled=""':''?> class="l-btn-inline l-btn-group-btn ui-btn-icon u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default">
                                        <span class="ui-btn-inner-content">
                                            <i class="ui-btn-inner-icon l-icon l-icon--size-38 ui-icon--size-22 mdi mdi-chevron-up"></i>
                                        </span>
                                    </a>

                                    <a onclick="initPageLoader()" title="<?=__t('Next')?>" href="<?=$paginationHelper->getNextUrl($id)?>" <?=!$paginationHelper->hasNext($id)?'disabled=""':''?> class="l-btn-inline l-btn-group-btn ui-btn-icon u-text-color-default u-text-hover-color-white u-bg-color-secondary u-bg-hover-color-default">
                                        <span class="ui-btn-inner-content">
                                            <i class="ui-btn-inner-icon l-icon l-icon--size-38 ui-icon--size-22 mdi mdi-chevron-down"></i>
                                        </span>
                                    </a>
                                <?php endif;?>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection() ?>

<div class="m-app-content">
    <div class="l-list-box">
            <div class="l-show-card-box">
                <div class="l-container">
                    <div class="l-show-card-actions">
                        <div class="ui-show-card-actions">
                            <div class="l-btn-group-box l-btn-group-box--responsive" data-btn-group-box-responsive-btn-group-box="true">
                              
                                <div class="l-btn-group l-btn-group--separated-2" role="group" data-btn-group-box-responsive-btn-group="true">
                                    <?php if ($updatePermissions && (Permissions::checkPermission($deletePermissions['all']) || (isset($deletePermissions['own']) && Permissions::checkPermission($deletePermissions['own']) && $is_own))):?>
                                        <a href="<?=route('owner.entity.edit', ['entityKey'=>$entityKey, 'id' => $data->id])?>" class="l-btn-inline l-btn-group-btn ui-btn u-bg-color-white u-text-color-action u-text-hover-color-white u-bg-hover-color-default">
                                            <span class="ui-btn-inner-content">
                                                <i class="ui-btn-inner-icon ui-icon--size-16 l-icon l-icon--size-20 mdi mdi-pencil"></i>
                                                <span class="ui-btn-inner-text"><?=__t('Edit')?></span>
                                            </span>
                                        </a>
                                    <?php endif;?>

                                

                                    <?php if ($deletePermissions && (Permissions::checkPermission($deletePermissions['all']) || (isset($deletePermissions['own']) && Permissions::checkPermission($deletePermissions['own']) && $is_own))):?>
                                        <a data-app-delete-modal-btn="true" data-app-delete-modal-btn-url="<?=route('owner.entity.delete', ['entityKey'=>$entityKey, 'id' => $data->id])?>" data-app-delete-modal-btn-method="DELETE" data-app-delete-modal-btn-message="<?=sprintf(__t('Are you sure you want to delete this %s?' ), __t('Leave Application'))?>"  data-app-delete-modal-btn-token="<?=csrf_token()?>"   href="<?=route('owner.entity.delete', ['entityKey'=>$entityKey, 'id' => $data->id])?>" class="l-btn-group-btn ui-btn u-bg-color-white u-text-color-action u-text-hover-color-white u-bg-hover-color-default">
                                            <i class="ui-btn-inner-icon ui-icon--size-16 fas fa-trash-alt"></i>
                                            <span class=""><?=__t('Delete')?></span>
                                        </a>
                                    <?php endif;?>

                                    <?php if ($notesAndAttachmentsPermission):?>
                                        <a href="<?="/v2/owner/entity/post/create/". PostTypeUtil::LEAVE_APPLICATION_TYPE."/".$data->id?>" class="l-btn-inline l-btn-group-btn ui-btn u-bg-color-white u-text-color-action u-text-hover-color-white u-bg-hover-color-default">
                                            <span class="ui-btn-inner-content">
                                                <i class="ui-btn-inner-icon ui-icon--size-16 l-icon l-icon--size-20 mdi mdi-book"></i>
                                                <span class="ui-btn-inner-text"><?=__t('Add Note & Attachments')?></span>
                                            </span>
                                        </a>
                                    <?php endif;?>
                                    <?= $this->includeSection('printable_templates/printables_dropdown-deprecated', ['view_templates'=> $viewTemplates, 'id' => $data->id]) ?>
                                </div>

                                <div class="l-btn-group-responsive-dropdown dropdown">
                                    <button type="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false" class="ui-btn u-bg-color-white u-text-color-action u-text-hover-color-white u-bg-hover-color-default ui-btn-group-responsive-btn" data-btn-group-box-responsive-btn="true" style="display: none;">
                                        <i class="mdi mdi-dots-horizontal"></i>
                                    </button>
                                    <div class="dropdown-menu ui-dropdown-menu l-btn-group-responsive-dropdown-menu" data-btn-group-box-responsive-dropdown-menu="true">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="ui-show-card-content ui-show-card-content--with-tabs" data-tabs-box="true">
                        <div id="show-card-tabs">
                            <div class="ui-show-card-tabs-box">
                                <div class="l-flex-row l-flex--align-center">
                                    <div class="l-flex-col-auto">
                                        <ul class="ui-tabs nav" role="tablist" data-tabs-box-nav="true">
                                            <li class="ui-tab-item" role="presentation">
                                                <button class="ui-tab-link active" id="details-tab"  data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" data-tabs-box-nav-item-link="true" aria-selected="true" >
                                                    <?=__t('Details')?>
                                                </button>
                                            </li>

                                            <?php if($notesCount && $notesAndAttachmentsPermission): ?>
                                                <li class="ui-tab-item" role="presentation">
                                                    <button class="ui-tab-link " id="notes-attachments-tab" data-lazy-iframe-btn="notes-attachments-iframe"  data-bs-toggle="tab"  data-bs-target="#notes-attachments"  type="button" role="tab" aria-controls="notes-attachments" data-tabs-box-nav-item-link="true" aria-selected="false" >
                                                        <?=__t('Note & Attachments')?>
                                                    </button>
                                                </li>
                                            <?php endif; ?>

                                            <li class="ui-tab-item" role="presentation">
                                                <button class="ui-tab-link " id="activity-log-tab" data-lazy-iframe-btn="activity-log-iframe"  data-bs-toggle="tab"  data-bs-target="#activity-log"  type="button" role="tab" aria-controls="activity-log" data-tabs-box-nav-item-link="true" aria-selected="false" >
                                                    <?=__t('Activity Log')?>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="show-card-tabs-content">
                            <div class="tab-content" data-tabs-box-tab-content="true">
                            <div name="details" id="details" class="tab-pane fade active show" role="tabpanel" aria-labelledby="details-tab" data-tabs-box-tab-pane="true">
                                    <div class="ui-show-card-content-inner ui-show-card-content-inner--spacing-20">
                                        <div class="ui-show-card-content-inner">
                                        <div class="l-show-card-section-title-box">
                                                <div class="l-flex-row">
                                                    <div class="l-flex-col-lg-12">
                                                        <div class="ui-card-section-title-box u-bg-color-light">
                                                            <h6 class="ui-card-section-title u-text-color-black"><?= __t(\Illuminate\Support\Str::singular($form->getLabel()).' Information')?></h6>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="l-show-card-section-items-box ui-show-card-section-items-box">
                                                <div class="l-flex-row">
                                                    <div class="l-flex-col-lg-6">
                                                        <div class="l-show-card-section-item">
                                                            <div class="ui-show-card-section-item">
                                                                <?= $this->includeSection('partials/staff_info_with_avatar', [
                                                                    'staff' => $data->staff_leave_application,
                                                                    'role' => $data->staff_leave_application->role?->name
                                                                ]);?>
                                                            </div>
                                                        </div>
                                                        <?php if($data->description){?>
                                                            <div class="l-show-card-section-item">
                                                                <div class="ui-show-card-section-item">
                                                                    <span class="ui-show-card-section-item-label"><?=__t('Notes')?></span>
                                                                    <div class="ui-show-card-section-item-content">
                                                                        <span>
                                                                            <span class="l-inline-block l-reservation-order-num">
                                                                                <span><?=$data->description?></span>
                                                                            </span>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        <?php }?>
                                                    </div>

                                                    <div class="l-flex-col-lg-6">

                                                        <div class="l-flex-row">
                                                            <div class="l-flex-col-lg-6">
                                                            <div class="l-show-card-section-item">
                                                            <div class="ui-show-card-section-item">
                                                                <div class="thumb-text-group">
                                                                   
                                                                    <div class="thumb-text thumb-flex">
                                                                        <div>
                                                                            <p class="ui-show-card-section-item-label"><?=__t('Type')?></p>
                                                                            <?php if(in_array($data->type, ["leave", 'half_leave'])):?>
                                                                                <p class="thumb-title-text u-text-color-black"><?=$data->days.' '.__t($data->days == 1?'Day':'Days')?></p>
                                                                                <p class="thumb-title-text u-text-color-black">(<?=($data->type == 'half_leave'?'1/2':'').' '.($data->leave_application_type->name??'')?>)</p>
                                                                            
                                                                             <?php elseif($data->type == "early"):?>
                                                                            <p class="l-text-item ui-text-item u-text-color-black">
                                                                                <span class="thumb-title-text u-text-color-black" ><?=__t('Early Leave')?> (<?=$data->early_time?> <?=__t('min')?>)</span>
                                                                            </p>
                                                                            <?php elseif($data->type == "delay"):?>
                                                                            <p class="l-text-item ui-text-item u-text-color-black">
                                                                                <span class="thumb-title-text u-text-color-black"><?=__t('Late Arrival')?> (<?=$data->late_time?> <?=__t('min')?>)</span>
                                                                            </p>
                                                                            <?php endif;?>
                                                                      
                                                                        </div>
                                                                        
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                            </div>
                                                            <div class="l-flex-col-lg-6">
                                                                <?php if($data->date_from == $data->date_to): ?>
                                                                    <div class="info-timeline-item">
                                                                        <div class="ui-show-card-section-item-label"><?=__t('Date')?></div>
                                                                        <div class="info-item-value"><?= __t(date("l", strtotime($data->date_from))).', '.format_date($data->date_from)?></div>
                                                                    </div>
                                                                <?php else: ?>
                                                                    <div class="l-show-card-section-item">
                                                                    <div class="ui-show-card-section-item">
                                                                        <div class="info-timeline">
                                                                            <div class="info-timeline-item">
                                                                                <div class="ui-show-card-section-item-label"><?=__t('Creation Date')?></div>
                                                                                <div class="info-item-value"><?= __t(date("l", strtotime($data->created))).', '.format_date($data->created).' '.(date("h:iA", strtotime(convertFromUtc($data->created))))?></div>
                                                                            </div>
                                                                            <div class="info-timeline-item">
                                                                                <div class="ui-show-card-section-item-label"><?=__t('Start Date')?></div>
                                                                                <div class="info-item-value"><?= __t(date("l", strtotime($data->date_from))).', '.format_date($data->date_from)?></div>
                                                                            </div>
                                                                            <div class="info-timeline-item">
                                                                                <div class="ui-show-card-section-item-label"><?=__t('End Date')?></div>
                                                                                <div class="info-item-value"><?= __t(date("l", strtotime($data->date_to))).', '.format_date($data->date_to)?></div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>

                                            </div>
                                            <?php if($data->shift_type) :?>
                                                <div class="l-show-card-section-items-box ui-show-card-section-items-box">
                                                    <div class="l-flex-row">
                                                        <div class="l-flex-col-lg-6">
                                                            <div class="l-show-card-section-item">
                                                                <div class="ui-show-card-section-item">
                                                                    <span class="ui-show-card-section-item-label"><?=__t('Shift Type')?></span>
                                                                    <div class="ui-show-card-section-item-content">
                                                                        <span>
                                                                            <span class="l-inline-block l-reservation-order-num">
                                                                                <span><?=__t(ucfirst($data->shift_type))?></span>
                                                                            </span>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            <?php if($data->attachments  && isset($form->get('attachments')->getAttributes()['data-izam1-forms1-value'])) :?>
                                                <div class="l-show-card-section-items-box ui-show-card-section-items-box">
                                                    <div class="l-flex-row">    
                                                        
                                                        
                                                        <div class="l-flex-col-lg-12">
                                                            <div class="l-show-card-section-item">
                                                                <div class="ui-show-card-section-item">
                                                                <?= $this->includeSection('partials/deprecated/multiplefile_preview_attachments',['attachments' => json_decode($form->get('attachments')->getAttributes()['data-izam1-forms1-value']) ]);?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                                <?php if ($data->status != LeaveApplicationStatusUtil::Pending):?>
                                                    <div class="l-show-card-section-title-box">
                                                        <div class="l-flex-row">
                                                            <div class="l-flex-col-lg-12">
                                                                <div class="ui-card-section-title-box u-bg-color-light">
                                                                    <h6 class="ui-card-section-title u-text-color-black" ><i class="mdi mdi-message-reply-text"></i> <?=__t('Attendance Permission Details')?></h6>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif;?>
                                                <div class="l-show-card-section-items-box ui-show-card-section-items-box">
                                                    <div class="l-flex-row">
                                                        <div class="l-flex-col-lg-6">
                                                            <?php if ($data->status == LeaveApplicationStatusUtil::Approved && $data->attendance_permission_id ):?>
                                                                <div class="l-flex-col-lg-6">
                                                                    <div class="l-show-card-section-item">
                                                                        <div class="ui-show-card-section-item">
                                                                            <span class="ui-show-card-section-item-label"><?=__t('Attendance Permission ID')?></span>
                                                                            <div class="ui-show-card-section-item-content ui-show-card-section-item-content--text-pre ui-show-card-section-item-content--lh-normal">
                                                                                <a class="text-decoration-underline" target="_blank" href="<?=route('owner.attendance_permissions.show', ['attendance_permission' => $data->attendance_permission_id])?>">#<?=$data->attendance_permission_id?></a>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endif;?>
                                                            <?php if ($data['comment']):?>
                                                                <div class="l-flex-col-lg-6">
                                                                    <div class="l-show-card-section-item">
                                                                        <div class="ui-show-card-section-item">
                                                                            <span class="ui-show-card-section-item-label"><?=__t('Comment')?></span>
                                                                            <div class="ui-show-card-section-item-content ui-show-card-section-item-content--text-pre ui-show-card-section-item-content--lh-normal">
                                                                                <span class="l-inline-block l-reservation-order-num">
                                                                                    <span><?=$data->comment?></span>
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endif;?>

                                                        </div>
                                                        <div class="l-flex-col-lg-6">
                                                            <?php if ($data['approved_by']):?>
                                                                <?php $this->includeSection('partials/staff_info_with_avatar',
                                                                    [
                                                                        'staff' => $data->leave_application_approver,
                                                                        'role' => $actionByRole
                                                                    ]
                                                                )?>
                                                            <?php elseif ($data->status != LeaveApplicationStatusUtil::Pending):?>
                                                                <?php $this->includeSection('partials/owner_info_with_avatar',
                                                                    [
                                                                    ]
                                                                )?>
                                                            <?php endif;?>

                                                        </div>
                                                        <?php if (isset($matchedConfigurationLabel) ):?>
                                                            <div class="l-flex-col-lg-6">
                                                                <p> 
                                                                    <span class="ui-show-card-section-item-label"> <?=  __t('Approval Configuration'). ': '?> </span>
                                                                        <?= $matchedConfigurationLabel?> 
                                                                    </p>
                                                            </div>
                                                        <?php endif;?>
                                                        
                                                    </div>
                                                </div>
                                                <?php if(count($logs)):?>
                                                    <div class="l-show-card-section-title-box">
                                                        <div class="l-flex-row">
                                                            <div class="l-flex-col-lg-12">
                                                                <div class="ui-card-section-title-box u-bg-color-light">
                                                                    <h6 class="ui-card-section-title u-text-color-black"><?= __t('Request History')?></h6>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="l-show-card-section-items-box ui-show-card-section-items-box">
                                                        <ul class="history-list">
                                                            <?php foreach($logs as $log):
                                                                $log = $log->jsonSerialize();
                                                                if(!isset($log['title'])) continue;
                                                            ?>
                                                                <li class="history-list-item">
                                                                    <div class="history-list-item_icon item-approved"><img src="<?= $log['icon'] ?? ""?>" alt=""></div>
                                                                    <div class="history-list-item_content">
                                                                        <p><?= $log['date'] ?? "" ?> - <?= $log['time']  ?? "" ?></p>
                                                                        <h4><?=  $log['title'] ?></h4>
                                                                        <?php if(isset($log['oldData']) && $log['newData'] &&  $log['oldData']['status'] !=  $log['newData']['status']) :?>
                                                                        <div class="status-list l-flex l-flex--align-center l-flex--wrap">
                                                                            <div class="item-status item-status-stroke"><?= $log['oldData']['status'] ?? ""?></div>
                                                                            <i class="item-status-arrow mdi mdi-arrow-right"></i>
                                                                            <div class="item-status "><?= $log['newData']['status'] ?? ""?></div>
                                                                        </div>
                                                                        <?php endif?>
                                                                    </div>
                                                                </li> 
                                                        <?php endforeach; ?>
                                                            
                                                        </ul>
                                                    </div>
                                                <?php endif ?>
                                        </div>
                                    </div>
                                </div>
                            <?php if ($notesCount && $notesAndAttachmentsPermission):?>
                                <div name="notes-attachments" id="notes-attachments" class="tab-pane fade" role="tabpanel" aria-labelledby="notes-attachments-tab" data-tabs-box-tab-pane="true">
                                    <div class="ui-show-card-content-inner ui-show-card-content-inner--spacing-20">
                                        <div class="u-text-align-center u-text-color-default" data-lazy-iframe-placeholder="notes-attachments-iframe">
                                            <i class="ui-icon ui-icon--size-42 far fa-spin fa-spinner-third"></i>
                                        </div>
                                        <iframe id="notes-attachments-iframe" frameborder="0" style="display: none; overflow: hidden;" data-lazy-iframe="notes-attachments-iframe" src="about:blank" data-lazy-iframe-src="/v2/owner/entity/post/timeline?iframe=1&filter[item_id]=<?=$data->id?>&filter[item_type]=<?=PostTypeUtil::LEAVE_APPLICATION_TYPE?>" data-app-iframe="true" width="100%" scrolling="no"></iframe>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div name="activity-log" id="activity-log" class="tab-pane fade" role="tabpanel" aria-labelledby="activity-log-tab" data-tabs-box-tab-pane="true">
                                    <div class="ui-show-card-content-inner ui-show-card-content-inner--spacing-20">
                                        <div class="u-text-align-center u-text-color-default" data-lazy-iframe-placeholder="activity-log-iframe">
                                            <i class="ui-icon ui-icon--size-42 far fa-spin fa-spinner-third"></i>
                                        </div>
                                        <iframe id="activity-log-iframe" frameborder="0" style="display: none; overflow: hidden;" data-lazy-iframe="activity-log-iframe" src="about:blank" data-lazy-iframe-src="/v2/owner/activity_logs/entity/iframe?entity_key=<?=$entityKey?>&amp;entity_id=<?=$data->id?>&amp;sort=DESC&amp;layout2022=1" data-app-iframe="true" width="100%" scrolling="no"></iframe>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<?php if($hasApproveOrRejectPermission):?>
    <?php $this->includeSection('partials/accept_reject_leave_application_modal', [$entityKey=> 'leave_application' ,'id'=>$id, 'displayNoLeaveBalanceWarning'=>$displayNoLeaveBalanceWarning])?>
<?php endif; ?>