<?php
$iframe = isset($_GET['iframe']);
if ($isGlobal && $this->viewExists("$view_folder/partials/table")) {
    return $this->includeSection("$view_folder/partials/table", ['page_url_param' => $page_url_param]);
}
?>

<?php
$multi_listing_actions = getCustomMultiListingActions($entityKey, $entityLabel);
?>
<div class="d-none d-lg-block">
    <form action="" method="POST" data-lt-form="true">
        <div class="listing-table-wrapper" data-lt-wrapper="true">
            <div class="listing-table-responsive" data-lt-responsive="true">
                <table class="table listing-table" data-lt="true">
                    <colgroup>
                        <?php
                        foreach ($table->getHeader()->getCells() as $cell) {
                            echo "<col />";
                        } ?>
                    </colgroup>
                    <thead data-lt-head="true">
                    <tr>
                        <?php
                        foreach ($table->getHeader()->getCells() as $i => $cell) {
                            if (0 == $i && $multi_listing_actions && !isset($_GET['hide_actions'])) { ?>
                                <th class="listing-cell-start">
                                    <div class="d-flex gap-6">
                                        <div class="listing-cell-check"></div>
                                        <div>
                                            <?= $cell->getLabel() ?>
                                        </div>
                                    </div>
                                </th>
                            <?php } else {
                                echo "<th>" . $cell->getLabel() . "</th>";
                            }
                        } ?>
                        <?php if(!isset($_GET['iframe'])): ?>
                            <th width="45" class="listing-cell-end text-end">
                                <?= !empty($table->getSorts()) ? $this->includeSection('partials/sorting_fields') : ''; ?>
                            </th>
                        <?php elseif (!isset($_GET['hide_actions'])): ?>
                            <th></th>
                        <?php endif; ?>
                    </tr>
                    </thead>
                    <tbody>


                    <?php foreach ($table->getRows() as $key => $row) {
                        $showUrl = 'javascript:void(0);';
                        $targetUrl = '';
                        foreach ($listingRowActions as $k => $action) {

                            if (strtolower($action['title']) == 'view' || strtolower($action['title']) == 'show') {
                                $showUrl = getUrlAction($action, $row->getData());
                                $targetUrl = $iframe ? '_blank' : '';
                                continue;
                            }
                        }
                        ?>

                        <tr tabindex="<?= $key ?>>">
                            <?php foreach ($row->getCells() as $j => $cell) {
                                if (0 == $j && $multi_listing_actions && !isset($_GET['hide_actions'])) { ?>
                                    <td class="listing-cell-start">
                                        <a target="<?= $targetUrl ?>>" href="<?= $showUrl ?>" class="listing-cell-link"></a>
                                        <div class="text-nowrap">
                                            <div class="d-flex gap-6">
                                                <div class="listing-cell-check">
                                                    <input class="form-check-input" type="checkbox" name="ids[]"
                                                           value="<?= $row->getData()->id ?>" data-lc-item="true"/>
                                                </div>
                                                <div class="title-text">
                                                    <?= $cell->getValue(); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                <?php } else { ?>
                                    <td>
                                        <a style="<?= $showUrl == 'javascript:void(0);' ? 'cursor:auto' : '' ?>" target="<?= $targetUrl ?>" href="<?= $showUrl ?>" class="listing-cell-link"></a>
                                        <?= $cell->getValue(); ?>
                                    </td>
                                <?php }
                            } ?>
                            <?php
                            if (!isset($_GET['hide_actions'])) {
                                $this->includeSection('partials/row-actions', ['row' => $row]);
                            }
                            ?>
                         </tr>

                        <?php
                    } ?>

                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>
