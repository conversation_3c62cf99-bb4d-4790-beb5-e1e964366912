<?php
use Izam\View\Form\Helper\Form\FormHeader;
use Izam\View\Form\Helper\FormCollection;
$extends = 'layouts/layout';
$url = route('owner.mileage_rates.store');
$elementHelper = new FormCollection();
$helper = new FormHeader();
?>
<style>
.flatpickr-calendar.open{
    z-index: 9999;
}
</style>
<div class="m-app-page-head">
    <div class="m-app-page-head-pc">
        <div class="l-container">

    <?php $this->section('page-head') ?>
    <form class="m-app-form" action="<?= $url ?>" method="POST" >
    <input type="hidden" name="_token" value="<?= csrf_token() ?>">
    <?= $helper->render($pageHead); ?>

        <article class="content-body" data-content-body="true">
            <div class="page-content">
                <div class="my-25 mx-auto container">
                    <div class="card mb-10">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="subform-container" data-subform-test2="true">
                                        <table class="subform-table" >
                                            <thead>
                                                <tr>
                                                    <th width="50"></th>
                                                    <th width="1000"><?=__t('Rate')?></th>
                                                    <th width="1000"><?=__t('To Date')?></th>
                                                    <th width="150"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php if(!$mileageRates->isEmpty()): ?>
                                                    <?php foreach($mileageRates as $i => $mileageRate):?>
                                                        <tr>
                                                            <input class="id-input" type="hidden" name="mileageRates[<?=$i?>][id]" value="<?=$mileageRate->id?>" >
                                                            <td class="subform-cell-actions" width="50">
                                                                <button class="subform-cell-drag" type="button" data-cell-drag-test2="true">
                                                                    <i class="mdi mdi-drag-vertical"></i>
                                                                </button>
                                                            </td>
                                                            <td class="subform-cell-editable">
                                                                <div class="input-container">
                                                                    <div class="form-group" data-form-group="true">
                                                                        <input name="mileageRates[<?=$i?>][rate]" type="number" min="0.001" step="0.001" class="form-control" value ="<?=$mileageRate->rate?>" placeholder="Rate" data-form-rules="required" required>
                                                                        <div class="form-validation" data-form-validation="true"></div>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        
                                                            <td class="subform-cell-editable">
                                                                <div class="input-container">
                                                                    <label class="form-group" data-form-group="true">
                                                                        <div class="input-group input-group-merge">
                                                                            <input name="mileageRates[<?=$i?>][to_date]" type="text" class="form-control" value="<?=$mileageRate->to_date?>" placeholder="To Date" data-date-input="id-z1" required />
                                                                            <span class="input-group-text input-group-icon-end">
                                                                                <i class="mdi mdi-calendar-today"></i>
                                                                            </span>
                                                                        </div>
                                                                    </label>
                                                                </div>
                                                                <script>
                                                                    $(document).ready(function() {
                                                                            var $input = $('[data-date-input]');
                                                                        $input.flatpickr(Object.assign({}, window.APP.VENDORS.DEFAULTS.flatpickr.dateFilter, {
                                                                            positionElement: $input.closest('[data-form-group]').get(0),
                                                                        }));
                                                                    });
                                                                </script>
                                                            </td>
                                                            <td class="subform-cell-actions subform-cell-actions-end" width="150px">
                                                                <button class="subform-cell-remove" type="button" data-cell-remove-test2="true" style="width: 150px; gap:5px;">
                                                                    <i class="mdi mdi-minus-circle-outline"></i> <span style="color:#202124; font-size: 14px;"><?=__t('Remove')?></span>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <tr>
                                                            <td class="subform-cell-actions" width="50">
                                                                <button class="subform-cell-drag" type="button" data-cell-drag-test2="true">
                                                                    <i class="mdi mdi-drag-vertical"></i>
                                                                </button>
                                                            </td>
                                                            <td class="subform-cell-editable">
                                                                <div class="input-container">
                                                                    <div class="form-group" data-form-group="true">
                                                                        <input name="mileageRates[0][rate]" type="number" min="0.001" step="0.001" class="form-control" value ="" placeholder="Rate" data-form-rules="required" required>
                                                                        <div class="form-validation" data-form-validation="true"></div>
                                                                    </div>
                                                                </div>
                                                            </td>

                                                            <td class="subform-cell-editable">
                                                                <div class="input-container">
                                                                    <label class="form-group" data-form-group="true">
                                                                        <div class="input-group input-group-merge">
                                                                            <input name="mileageRates[0][to_date]" type="text" class="form-control" value="" placeholder="To Date" data-date-input="id-z1" required />
                                                                            <span class="input-group-text input-group-icon-end">
                                                                                <i class="mdi mdi-calendar-today"></i>
                                                                            </span>
                                                                        </div>
                                                                    </label>
                                                                </div>
                                                                <script>
                                                                    $(document).ready(function() {
                                                                            var $input = $('[data-date-input]');
                                                                        $input.flatpickr(Object.assign({}, window.APP.VENDORS.DEFAULTS.flatpickr.dateFilter, {
                                                                            positionElement: $input.closest('[data-form-group]').get(0),
                                                                        }));
                                                                    });
                                                                </script>
                                                            </td>
                                                            <td class="subform-cell-actions subform-cell-actions-end" width="150px">
                                                                <button class="subform-cell-remove" type="button" data-cell-remove-test2="true" style="width: 150px; gap:5px;">
                                                                    <i class="mdi mdi-minus-circle-outline"></i> <span style="color:#202124; font-size: 14px;"><?=__t('Remove')?></span>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                <?php endif; ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="12">
                                                        <div class="btn-group gap-1">
                                                            <button type="button" class="btn btn-secondary btn-responsive-icon" data-row-add-test2="true">
                                                                <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                                <span><?=__t('Add')?></span>
                                                            </button>
                                                            
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                        <template data-subform-template-test2="true">
                                            <tr>
                                                <td class="subform-cell-actions" width="50">
                                                    <button class="subform-cell-drag" type="button" data-cell-drag-test2="true">
                                                        <i class="mdi mdi-drag-vertical"></i>
                                                    </button>
                                                </td>
                                                <td class="subform-cell-editable">
                                                    <div class="input-container">
                                                        <div class="form-group" data-form-group="true">
                                                            <input name="mileageRates[__index__][rate]" type="number" min="0.001" step="0.001" class="form-control" placeholder="Rate" data-form-rules="required" data-subform-name="name" required>
                                                            <div class="form-validation" data-form-validation="true"></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                
                                                <td class="subform-cell-editable">
                                                    <div class="input-container">
                                                        <label class="form-group" data-form-group="true">
                                                            <div class="input-group input-group-merge">
                                                                <input name="mileageRates[__index__][to_date]" type="text" class="form-control" placeholder="To Date" data-date-input="id-z__index__" required/>
                                                                <span class="input-group-text input-group-icon-end">
                                                                    <i class="mdi mdi-calendar-today"></i>
                                                                </span>
                                                            </div>
                                                        </label>
                                                    </div>
                                                    <script>
                                                        $(document).ready(function() {
                                                            var $input = $('[data-date-input]');
                                                            $input.flatpickr(Object.assign({}, window.APP.VENDORS.DEFAULTS.flatpickr.dateFilter, {
                                                                positionElement: $input.closest('[data-form-group]').get(0),
                                                            }));
                                                        });
                                                    </script>
                                                </td>
                                                <td class="subform-cell-actions subform-cell-actions-end" width="150px">
                                                    <button class="subform-cell-remove" type="button" data-cell-remove-test2="true"  style="width: 150px; gap:5px;">
                                                        <i class="mdi mdi-minus-circle-outline"></i> <span style="color:#202124; font-size: 14px;">Remove</span>
                                                    </button>
                                                </td>
                                            </tr>
                                        </template>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <footer class="content-fixed-end"></footer>
            </form>

        </article>
        <?php $this->endSection() ?>

        </div>
    </div>
</div>
    <script src="/frontend/assets/js/vendors/jquery.js?v=<?= JAVASCRIPT_VERSION ?>"></script>
    <script>
    window.onload = function () {

    $(function() {
        window.instance = {};
            var $table = $('[data-subform-test2]');
        window.instance = $table.subform({
            addBtn: '[data-row-add-test2]',
            template: '[data-subform-template-test2]',
            removeBtn: '[data-cell-remove-test2]',
            dragBtn: '[data-cell-drag-test2]',
            lastIndex: <?=count($mileageRates)?>,
            minimumRows: 1,
            // validateRemove: function ($row, lastIndex) {
            onRemoveRow: function ($row, lastIndex) {

                return new Promise(function(resolve, reject) {
                    let id = $row.find('.id-input').val();
                    if($('.itemRow').length === 1) {
                        return reject();
                    }

                    if(!id) {
                        return resolve();
                    }

                    if(!confirm("<?=__t('Are you sure you want to delete?')?>")) {
                        return reject();
                    }
                    $.ajax({
                        url: "/v2/owner/mileage_rates/destroy/"+id,
                        method: "DELETE",
                        dataType: "json",
                        data: {
                        "_token":"<?=csrf_token()?>"
                        },
                        success: function (res) {
                            return resolve();
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            if(jqXHR.status == 400) {
                                $.toast(jqXHR.responseJSON.message, 'danger');
                            } else {
                                $.toast("<?=__t('Delete failed please try again or contact support')?>", 'danger');
                            }
                            reject();
                        }
                    })
                })

                return true;
            },
        });

    });}
    
    </script>

