<?php
    $locale = app()->getLocale() === 'ara' ? 'ar' : 'en';
    $appInstaller = resolve(\Izam\Daftra\AppManager\Services\AppInstaller::class);  
    /** @var \Izam\Daftra\AppManager\Services\AppSettingsService $service */
    $service = resolve(\Izam\Daftra\AppManager\Services\AppSettingsService::class);

$getAuthLink = function ($redirectUrl, $appId) use ($service, $appInstaller) {
    if ($service->isSettingsRequired($appId) && $appInstaller->isSettingsRecordIsEmpty($appId, getCurrentSite('id'))) {
        return route('owner.apps-manager.settings', $appId);
    }
    return 'https://' . Portal_Full_Name . '/app/authorize?' . http_build_query([
        'redirect_url' => $redirectUrl,
        'token' => __createToken(getCurrentSite('id')),
        'site_id' => getCurrentSite('id'),
        'app_id' => $appId,
    ]);
}
?>

<!-- add styling in mobile screen  -->
<style>
   @media (max-width: 993px) {
    .plugin-item{
        padding:10px !important;
    }
    .plugin-item > div{
        gap:10px;
    }
    .left-side-container{
        max-width: 80px;
    }
    .image-container {
        max-width: 80px;
    }
    }
</style>

<div class="container-full">
    <div class="my-10 vstack gap-10">
        <div class="plugin-group">
            <div class="plugin-group-card">
                <div class="plugin-group-body">
                    <div class="app-grid">
                        <?php foreach ($apps as $appSite): ?>
                            <?php
                                $app = $appSite->app;
                                $description = $app->getTranslation($locale)->description;
                                $name = $app->getTranslation($locale)->name;
                            ?>
                            <div class="plugin-item-col">
                                <div class="card plugin-item" data-plugin-id="<?= $appSite->app->id; ?>" data-plugin-error-url='{"2": "<?= route('owner.apps-manager.settings', $appSite->app->id); ?>", "4": "<?= $portalBaseUrl . sprintf('/sites/purchase_app/%d/%d', getCurrentSite('id'), $app->id) ?>"}'>
                                    <div class="d-flex align-items-md-start">
                                        <div class="left-side-container flex-shrink-0">
                                            <span class="image-container flex-shrink-0">
                                                <img src="<?= $appSite->app->image_url ?: 'https://placehold.co/100x100/blue/white?text=' . urlencode($name); ?>" />
                                            </span>

                                            <div class="plugin-item-footer-item">
                                                <button class="btn btn-link p-0 text-decoration-none rating-button"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#modalSimple">
                                                    <div class="d-flex flex-column align-items-start">
                                                        <?php if($appSite->app->avg_rating && $appSite->app->rating_count): ?>
                                                        <div>
                                                            <i class="mdi mdi-star text-warning"></i>
                                                            <span class="rating-value"><?= $appSite->app->avg_rating ?></span>
                                                            <span class="rating-count">(<?= $appSite->app->rating_count ?>)</span>
                                                        </div>
                                                        <?php endif; ?>
                                                        <small class="text-muted">
                                                            <!-- TODO-BE: review is empty even when there is a review -->
                                                            <?= $appSite->app->siteReview($appSite->site_id) ? (__t('Edit rating').' (' . $appSite->app->siteReview($appSite->site_id)->rate.')') : __t('Add rating'); ?>
                                                        </small>
                                                    </div>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="flex-fill h-75">
                                            <div class="d-flex flex-column h-100">
                                                <div class="d-flex justify-content-between w-100">
                                                    <h6 class="plugin-item-content">
                                                        <span class="plugin-item-name"><?= $name; ?></span>
                                                    </h6>
                                                    <div>
                                                        <div class="text-right">
                                                            <div class="plugin-item-checkbox-wrap align-middle">
                                                                <label class="plugin-item-status pr-2" for="<?= $appSite->app->id; ?>"><?= in_array($appSite->app->id, $activeSiteAppIds) ? __t('Activate') : __t('Deactivate'); ?></label>
                                                                <div class="toggle-switch-container">
                                                                    <label class="toggle-switch">
                                                                        <input
                                                                            data-link="<?= $getAuthLink(explode(',', $appSite->app->redirect)[0], $appSite->app->id) ?>"
                                                                            onclick="redirect(this)"
                                                                            value="0"
                                                                            type="checkbox"
                                                                            class="toggle-input plugin-item-checkbox"
                                                                            <?= in_array($appSite->app->id, $activeSiteAppIds) ? 'checked' : ''; ?>
                                                                            id="<?= $appSite->app->id; ?>"
                                                                            name="<?= $appSite->app->id; ?>">
                                                                        <span class="toggle-slider"></span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- Categories and availability -->
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <?php if (!empty($appSite->expires_at)): ?>
                                                        <div class="d-flex align-items-center mt-2">
                                                            <?php if (strtotime($appSite->expires_at) > time()): ?>
                                                                <span class="availability-pill valid">
                                                                    <?= __t('Valid until') .' '. date('Y-m-d ', strtotime($appSite->expires_at)) ?>
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="availability-pill expired">
                                                                    <?= __t('Expired') ?>
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="d-flex align-items-center mt-2">
                                                            <span class="availability-pill valid">
                                                                <?= (int)$appSite->app->price==0? __t('Free') : __t('Purchased') ?>
                                                            </span>
                                                        </div>
                                                    <?php endif; ?>


                                                    <div class="d-flex align-items-center mt-2">
                                                        <?php
                                                        $mockCategories = [];
                                                        if (isset($appSite->app->categories) && !empty($appSite->app->categories)) {
                                                            foreach ($appSite->app->categories as $cat) {
                                                                $mockCategories[] = $cat->getName();
                                                            }
                                                        }
                                                        $totalCategories = count($mockCategories);
                                                        $remainingCount = $totalCategories > 1 ? $totalCategories - 1 : 0;
                                                        foreach ($mockCategories as $index => $category) {
                                                            if ($index < 1) {
                                                                echo '<span class="category-pill me-2">' . $category . '</span>';
                                                            }
                                                        }
                                                        if ($remainingCount > 0) {
                                                            $remainingCategories = array_slice($mockCategories, 1);
                                                            $tooltipContent = implode(', ', $remainingCategories);
                                                            echo '<span class="category-pill" data-bs-toggle="tooltip" data-bs-placement="bottom" title="' . $tooltipContent . '">+' . $remainingCount . '</span>';
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                                <!-- Description -->
                                                <div class="w-100 mt-4">
                                                    <p class="text-muted description-text">
                                                        <?= $description; ?>
                                                    </p>
                                                </div>
                                            </div>

                                            <?php if(showAppSettingsToggleButton($appSite->app, $installedAppsIds)):?>
                                            <div class="plugin-item-footer-item flex-fill">
                                                <a href="<?= route('owner.apps-manager.settings', $appSite->app->id)?>" class="btn btn-secondary py-6 w-100 d-flex align-items-center justify-content-center">
                                                    <i class="mdi mdi-cog me-2"></i>
                                                    <?= __t('Settings') ?>
                                                </a>
                                            </div>
                                            <?php endif; ?>

                                        </div>
                                    </div>

                                </div>
                            </div>
                        <?php endforeach; ?>

                        <!-- TO-DO MUST REMOVED OR MERGED -->
                        <?php foreach ($installableApps as $appToInstall): ?>
                            <?php
                                $description = $appToInstall->getTranslation($locale)->description;
                                $name = $appToInstall->getTranslation($locale)->name;
                            ?>
                            <div class="plugin-item-col">
                                <div class="card plugin-item" data-plugin-id="<?= $appToInstall->id; ?>" data-plugin-error-url='{"2": "<?= route('owner.apps-manager.settings', $appToInstall->id); ?>", "4": "<?= $portalBaseUrl . sprintf('/sites/purchase_app/%d/%d', getCurrentSite('id'), $appToInstall->id) ?>"}'>
                                    <div class="d-flex align-items-md-start">
                                        <div class="left-side-container flex-shrink-0">
                                            <span class="image-container flex-shrink-0">
                                                <img src="<?= $appToInstall->image_url ?: 'https://placehold.co/100x100/blue/white?text=' . urlencode($name); ?>" />
                                            </span>
                                        </div>
                                        <div class="flex-fill h-75">
                                            <div class="d-flex flex-column h-100">
                                                <div class="d-flex justify-content-between w-100">
                                                    <h6 class="plugin-item-content">
                                                        <span class="plugin-item-name"><?= $name; ?></span>
                                                    </h6>
                                                    <div>
                                                        <div class="text-right">
                                                            <div class="plugin-item-checkbox-wrap align-middle">
                                                                <label class="plugin-item-status pr-2" for="<?= $appToInstall->id; ?>"><?= in_array($appToInstall->id, $activeSiteAppIds) ? __t('Activate') : __t('Deactivate'); ?></label>
                                                                <div class="toggle-switch-container">
                                                                    <label class="toggle-switch">
                                                                        <input
                                                                            data-link="<?= $getAuthLink(explode(',', $appToInstall->redirect)[0], $appToInstall->id) ?>"
                                                                            onclick="redirect(this)"
                                                                            value="0"
                                                                            type="checkbox"
                                                                            class="toggle-input plugin-item-checkbox"
                                                                            <?= in_array($appToInstall->id, $activeSiteAppIds) ? 'checked' : ''; ?>
                                                                            id="<?= $appToInstall->id; ?>"
                                                                            name="<?= $appToInstall->id; ?>">
                                                                        <span class="toggle-slider"></span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- Categories and availability -->
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div class="d-flex align-items-center mt-2">
                                                        <span class="availability-pill valid">
                                                            <?= (int)$appToInstall->price==0? __t('Free') : __t('Purchased') ?>
                                                        </span>
                                                    </div>

                                                    <div class="d-flex align-items-center mt-2">
                                                        <?php
                                                        $mockCategories = [];
                                                        if (isset($appToInstall->categories) && !empty($appToInstall->categories)) {
                                                            foreach ($appToInstall->categories as $cat) {
                                                                $mockCategories[] = $cat->getName();
                                                            }
                                                        }
                                                        $totalCategories = count($mockCategories);
                                                        $remainingCount = $totalCategories > 1 ? $totalCategories - 1 : 0;
                                                        foreach ($mockCategories as $index => $category) {
                                                            if ($index < 1) {
                                                                echo '<span class="category-pill me-2">' . $category . '</span>';
                                                            }
                                                        }
                                                        if ($remainingCount > 0) {
                                                            $remainingCategories = array_slice($mockCategories, 1);
                                                            $tooltipContent = implode(', ', $remainingCategories);
                                                            echo '<span class="category-pill" data-bs-toggle="tooltip" data-bs-placement="bottom" title="' . $tooltipContent . '">+' . $remainingCount . '</span>';
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                                <!-- Description -->
                                                <div class="w-100 mt-4">
                                                    <p class="text-muted description-text">
                                                        <?= $description; ?>
                                                    </p>
                                                </div>
                                            </div>

                                            <?php if(showAppSettingsToggleButton($appToInstall, $installedAppsIds)):?>
                                            <div class="plugin-item-footer-item flex-fill">
                                                <a href="<?= route('owner.apps-manager.settings', $appToInstall->id)?>" class="btn btn-secondary py-6 w-100 d-flex align-items-center justify-content-center">
                                                    <i class="mdi mdi-cog me-2"></i>
                                                    <?= __t('Settings') ?>
                                                </a>
                                            </div>
                                            <?php endif; ?>

                                        </div>
                                    </div>

                                </div>
                            </div>
                        <?php endforeach; ?> 
                    </div>
                </div>
                <!-- Global Rating Modal -->
                <div
                    class="modal fade"
                    id="modalSimple"
                    tabindex="-1"
                    aria-labelledby="modalSimpleLabel">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="modalSimpleLabel">
                                    <?= __t('Send Review for') ?>
                                </h5>
                                <button
                                    type="button"
                                    class="btn-close-2"
                                    data-bs-dismiss="modal"
                                    aria-label="Close">
                                    <i class="mdi mdi-close-thick"></i>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="rating-container mb-4">
                                    <label class="form-label"><?= __t('Rating') ?></label>
                                    <div class="d-flex align-items-center">
                                        <div class="star-rating">
                                            <span class="star" data-rating="1"><i class="mdi mdi-star-outline"></i></span>
                                            <span class="star" data-rating="2"><i class="mdi mdi-star-outline"></i></span>
                                            <span class="star" data-rating="3"><i class="mdi mdi-star-outline"></i></span>
                                            <span class="star" data-rating="4"><i class="mdi mdi-star-outline"></i></span>
                                            <span class="star" data-rating="5"><i class="mdi mdi-star-outline"></i></span>
                                        </div>
                                        <div class="rating-text ms-3"><?= __t('Click to rate') ?></div>
                                    </div>
                                    <input
                                        type="hidden"
                                        name="rating"
                                        id="selected-rating"
                                        value="0" />
                                </div>

                                <div class="mt-12">
                                    <div class="form-group">
                                        <label for="formControlInput" class="form-label"><?= __t('Review') ?></label>
                                        <textarea
                                            name="comment"
                                            id="formControlInput"
                                            class="form-control"
                                            placeholder="<?= __t('Write your review here') ?> ..."
                                            data-textarea-autosize="true"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button
                                    type="button"
                                    class="btn btn-cancel px-xl-25"
                                    data-bs-dismiss="modal">
                                    <?= __t('Cancel') ?>
                                </button>
                                <button type="button" class="btn btn-success rate-submit flex-grow-1">
                                <?= __t('Submit') ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<script>
    var csrfToken = '<?= csrf_token() ?>';
    const sendReviewTitle = '<?= __t('Send Review for') ?>';
    const emptyRateErrorMessage = '<?= __t('The rate is required') ?>';
    const editRatingText = '<?= __t('Edit rating') ?>';
    const ratingTexts = [
        '<?= __t("Click to rate") ?>',
        '<?= __t("Poor") ?>',
        '<?= __t("Fair") ?>',
        '<?= __t("Good") ?>',
        '<?= __t("Very Good") ?>',
        '<?= __t("Excellent") ?>',
    ];
</script>
<script src="/frontend/assets/js/pages/app-manager/index/index.js?v=-6"></script>
<script src="/frontend/assets/js/pages/app-manager/rating-modal.js?v=-1" defer></script>