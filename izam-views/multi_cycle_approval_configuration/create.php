<?php


use App\Modules\LocalEntity\Services\MultiCycleApproval\MultiCycleApprovalConfigurationService;
use App\Utils\AttendancePermissionTypesUtil;
use App\Utils\EntityKeyTypesUtil;
use Izam\Daftra\Common\Formatter\SupplierAvatar;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use Izam\View\Form\Helper\Deprecated\Utils\SizeUtil;
use Izam\View\Form\Helper\FormCollection;
use Izam\View\Form\Helper\FormRow;
use Izam\View\Form\Helper\Form\FormHeader;
use Izam\View\Form\Helper\FormRowWrapper;
use Laminas\Form\Element\Checkbox;
use Laminas\Form\Element\Radio;

$extends = 'layouts/layout';
$entityKey = EntityKeyTypesUtil::APPROVAL_CYCLE_CONFIGURATION;
$formUrl = route('owner.entity.store', ['entityKey' => $entityKey]);
$method = 'POST';
$data = count(old()) ? old() : (isset($extraData['data']) ? $extraData['data'] : null);
$isUpdate = false;
$elementHelper = new FormRowWrapper();
$helper = new FormHeader();
$formRowHelper = new FormRow();
$collection = new FormCollection();

if (!empty($data['approval_levels'])) {
    $service = resolve(MultiCycleApprovalConfigurationService::class);
    $data['approval_levels'] = $service->getLevelsEmployees($data['approval_levels']);
}
if (isset($data['id'])) {
    $formUrl = route('owner.entity.update', ['entityKey' => $entityKey, 'id' => $data['id']]);
    $method = 'PUT';
    $isUpdate = true;
}
$this->viewVars['_PageBreadCrumbs'] = $this->sharedVars['generalBreadCrumbs'];

$title = $isUpdate ? sprintf(__t('Edit %s'), __t('Approval Configuration')) : sprintf(__t('Add %s'), __t('Approval Configuration'));

$this->viewVars['title_for_layout'] = sprintf($title, __t('Approval Configuration'));

$form->prepare();
?>

<?php $this->section('article-start') ?>
<form id="leaseContractForm" action="<?= $formUrl ?>" method="POST" class="validate">
    <?php $this->endSection() ?>

    <?php
    $buttons = [
        'desktop' => [
            'start' => [
            ],
            'end' => [
                ['type' => 'link', 'class' => 'btn-cancel', 'icon' => 'mdi-close-thick', 'text' => __t('Cancel'), 'url' => $isUpdate? route('owner.entity.show', ['id'=>$data['id'], 'entityKey' => $entityKey]):route('owner.entity.list', ['entityKey' => $entityKey])],
                ['type' => 'button', 'class' => 'btn-success', 'icon' => 'mdi-content-save', 'text' => __t('Save'), 'url' => '#']
            ]
        ],
        'mobile' => [
            'end' => [
                ['type' => 'link', 'class' => 'btn-secondary', 'icon' => 'mdi-close-thick', 'text' => __t('Cancel'), 'url' => $isUpdate? route('owner.entity.show', ['id'=>$data['id'], 'entityKey' => $entityKey]):route('owner.entity.list', ['entityKey' => $entityKey])],
                ['type' => 'button', 'class' => 'btn-success', 'icon' => 'mdi-content-save', 'text' => __t('Save'), 'url' => '#']
            ]
        ]
    ];


    $formUrl = route('owner.entity.store', ['entityKey' => $entityKey]);

    $this->section('page-head');
    echo $this->includeSection('partials/form/head', ['buttons' => $buttons]);
    $this->endSection();
    ?>
    
    <input type="hidden" name="_token" value="<?= csrf_token() ?>">
    <input type="hidden" name="_method" value="<?= $method ?>">
    
    <input type="hidden" name="id" value="<?= $data['id'] ?? null ?>">
    <input type="hidden" name="key" value="multi_cycle_approval_configuration" >
    <input type="hidden" name="plugin_id" value="0" >


    <div class="page-content">
        <div class="container">
            <div class="my-10 vstack gap-10">
                        <div class="card">
                            <div class="card-header">
                                <?=__t('Basic Information') ?>
                            </div>
                            <div class="card-body">
                                <div class="card-row">
                                    <?php
                                     $helper = new FormCollection();
                                     $helper
                                         ->setElementHelper($elementHelper)
                                         ->setFieldsetHelper($elementHelper)
                                         ->setShouldWrap(false);
                                     $form->prepare();
                                     echo $helper->render($form);
                                    ?>



                                       
                                </div>
                            </div>
                        </div>

            </div>

            <!-- Approval Configuration -->
            <div class="my-10 vstack gap-10">
                        <div class="card">
                            <div class="card-header">
                                <?= __t('Approval Configurations')?>
                            </div>
                            <div class="card-body">
                                <div class="card-row">
                                    <!-- Configuration -->
                                    <div class="col-12">
                                        <div class="form-group">
                                            <div class="row g-8">
                                                
                                            <?php
                                                $configTypeElement = new Radio('approval_type');
                                                $configTypeElement->setOption('size', \Izam\Entity\Utils\SizeUtil::XLARGE);
                                                $configTypeElement->setLabel(__t('Approval Type'))
                                                    ->setAttribute('required', true)
                                                    ->setValue($data['approval_type'] ?? null)
                                                ->setValueOptions(['single_cycle' => __t('Single Approval'), 'multi_cycle' => __t('Multi-Cycle Approval')]);
                                                echo $elementHelper->render($configTypeElement);
                                            ?>
                                               
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <p class="form-label"><?=__t('Approvers')?><span class="text-danger"> *</span></p>
                                            <div class="row g-8">
                                                <div class="col-lg-6">
                                                    <?php
                                                        $hasDirectManagerElement = new Checkbox('has_direct_manager');
                                                        $hasDirectManagerElement->setOption('size', \Izam\Entity\Utils\SizeUtil::XLARGE);
                                                        $hasDirectManagerElement->setLabel(__t('Direct Manager'))
                                                            ->setLabelOption('hide_label', true)
                                                        ->setAttribute('id', 'directManagerSwitch')
                                                        ->setAttribute('role', 'switch' )
                                                        ->setValue('has_direct_manager')
                                                        ->setAttribute('checked', (!empty($data['has_direct_manager']) || !$isUpdate));
                                                        echo $elementHelper->render($hasDirectManagerElement);
                                                    ?>
                                                   
                                                </div>
                                                <div class="col-lg-6">
                                                    <?php
                                                        $assignedManagerElement = new Checkbox('has_assigned_department_manager');
                                                        $assignedManagerElement->setOption('size', \Izam\Entity\Utils\SizeUtil::XLARGE);
                                                        $assignedManagerElement->setLabel(__t('Department Managers'))
                                                            ->setLabelOption('hide_label', true)
                                                        ->setAttribute('id', 'assignedManagerSwitch')
                                                        ->setAttribute('role', 'switch' )
                                                        ->setValue('has_assigned_department_manager');
                                                        if(!empty($data['has_assigned_department_manager'])){
                                                            $assignedManagerElement->setAttribute('checked', true);
                                                        }
                                                        echo $elementHelper->render($assignedManagerElement);
                                                        ?>
                                                  
                                                </div>
                                                <div class="col-lg-12">
                                                <?php
                                                        $employeesElement = new Checkbox('employees');
                                                        $employeesElement->setOption('size', \Izam\Entity\Utils\SizeUtil::XLARGE);
                                                        $employeesElement->setLabel(__t('Other Employees'))
                                                            ->setLabelOption('hide_label', true)
                                                        ->setAttribute('id', 'otherEmployeesSwitch')
                                                        ->setAttribute('role', 'switch' )
                                                        ->setValue('employees');
                                                        if(!empty($data['employees'])){
                                                            $employeesElement->setAttribute('checked', true);
                                                        }
                                                        echo $elementHelper->render($employeesElement);
                                                        ?>
                                                      
                                                </div>
                                                <?= $this->includeSection('partials/input-error-message-v2', ['errors' => $errors, 'input_name' => "approvers"]) ?>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Approval Cycle Levels -->
                                <p class="form-label fw-bold">
                                    <?=__t('Approval Cycle Levels')?>
                                </p>
                                <p class="text-muted small dynamic-notes"><?=__t('Any Approver on the same level can process the request.')?></p>
                                <div class="subform-container" data-subform-approval-cycle-levels="true">
                                    <table class="subform-table">
                                        <thead>
                                            <tr>
                                                <th width="200"><?=__t('Name')?></th>
                                                <th width="1000" ><?=__t('Approvers')?></th>
                                                <th width="50"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="subform-cell-text">
                                                <?=__t('Approval Level')?> <span data-row-no="true"> 1</span>
                                                </td>
                                                <td class="subform-cell-editable border-start">
                                                    <div class="input-container">
                                                        <div class="form-group" data-form-group="true">
                                                            <?php
                                                                $employeeSelectElement = new \Izam\View\Form\Element\Select('approval_levels[1]');
                                                                $employeeSelectElement->setAttribute('placeholder', __t('Approvers'))
                                                                ->setAttribute('data-form-input', 'true')
                                                                ->setAttribute('data-select-input', 'approvers')
                                                                ->setAttribute('multiple', true)
                                                                ->setAttribute('required', true)
                                                                ->setValueOptions([
                                                                    '__clear__' => __t('None'),
                                                                ])
                                                                ->setAttribute('label', false);
                                                                $employeeSelectElement->setOption('size', SizeUtil::XLARGE);
                                                                echo $elementHelper->render($employeeSelectElement);
                                                            ?> 
                                                          
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="subform-cell-actions subform-cell-actions-end" width="50">
                                                    <button class="subform-cell-no-action" type="button">
                                                        <i class="mdi mdi-lock"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="12">
                                                    <div class="btn-group gap-1">
                                                        <button type="button" class="btn btn-secondary btn-responsive-icon" data-row-add-approval-cycle-levels="true">
                                                            <i class="mdi mdi-plus-thick text-success me-xl-4"></i>
                                                            <span><?= __t('Add')?></span>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                    <template data-subform-template-approval-cycle-levels="true">
                                        <tr>
                                            <td class="subform-cell-text">
                                                <?=__t('Approval Level')?> <span data-row-no="true"> 1</span>
                                            </td>
                                            <td class="subform-cell-editable border-start">
                                                <div class="input-container">
                                                    <div class="form-group" data-form-group="true">
                                                        <select class="form-control" placeholder="<?= __t('Approvers')?>"  name=approval_levels[__index__][] data-form-input="true" data-select-input="approvers" multiple required>
                                                            <option value="__clear__"><?=__t('None')?></option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="subform-cell-actions subform-cell-actions-end" width="50">
                                                <button class="subform-cell-remove" type="button" data-cell-remove-approval-cycle-levels="true">
                                                    <i class="mdi mdi-trash-can"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </template>
                                </div>
                                <?php  if(!empty($errors) && !empty($errors['approval_levels'])) { ?>
                                    <div class="form-validation" data-form-validation="true">
                                        <?php   foreach($errors["approval_levels"] as $error){ ?>
                                            <p class="error"><?= $error ?></p>
                                        <?php } ?>
                                    </div>
                                <?php } ?>

                            </div>
                        </div>
                    </div>
                </div>
    </div>
    <script>
        window.translationss = {
            DirectManager: '<?= __t('Direct Manager') ?>',
            AssignedManager: '<?= __t('Department Managers') ?>',
            ApproversLimitErrorMsg: '<?= __t('You can’t add more than 10 approval levels to this configuration.') ?>'
        };
        window.constants = {
            HALF_LEAVE: '<?= AttendancePermissionTypesUtil::HALF_LEAVE ?>',
            LEAVE: '<?= AttendancePermissionTypesUtil::LEAVE ?>',
            single_cycle_notes: '<?=__t('Any Approver on the same level can process the request.')?>',
            multi_cycle_notes: '<?=__t('Any Approver on the same level can process the request. Approvals on different levels will process the request sequentially.')?>',
            None : '<?=__t('None')?>'
        };
        <?php if (isset($data['approval_levels'])){
            foreach ($data['approval_levels'] as $key => $level) {
                if(isset($level['employees_obj'])){
                foreach ($level['employees_obj'] as $index => $employee) { 
                    $employee->avatar_url = AvatarURLGenerator::generate($employee->name, $employee->id, 30, $employee->photo);
             }}}
            ?>
        window.approvalLevelsData = <?= json_encode($data['approval_levels']) ?>;
        <?php } ?>
    </script>
    <script src="/frontend/assets/js/pages/multi-cycle-approval/create.js"></script>
    <?php $this->section('article-end') ?>
</form>

<?php $this->endSection() ?>

<?php $this->section('page-after-js') ?>

<?php $this->includeSection('entitiy/partials/custom-js');?>


<?php $this->endSection() ?>
