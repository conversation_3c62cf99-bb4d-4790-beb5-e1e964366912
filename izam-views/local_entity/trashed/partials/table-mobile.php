<div class="d-block d-lg-none">
    <div class="listing-table-wrapper" data-lt-wrapper="true">
        <div class="listing-table-responsive" data-lt-responsive="true">
            <table class="table listing-table">
                <thead>
                <tr>
                    <th class="listing-cell-start"></th>
                    <th></th>
                    <th width="45" class="listing-cell-end text-end">
                    </th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($fields as $field) {
                    $fieldId = $field['id'];
                    $fieldType = $field['type'];
                    $fieldDeletedAt = $field['deleted_at'];
                    if ($fieldType === 'StaticContent') {
                        $fieldKey = $fieldLabel = $fieldType;
                    } else {
                        $fieldKey = $field['attributes']['property']['key']['value'];
                        $fieldLabel = $field['attributes']['property']['label']['value'];
                    }
                    $fieldEntityKey = $field['entityKey'] ?? $entityKey;
                    ?>
                    <tr>
                        <td>
                            <a href="JAVASCRIPT::avoid(0)" class="listing-cell-link"></a>
                            <div class="title-text">
                                <p><?= $fieldKey ?></p>
                                <small class="title-subtitle-text"><?= $fieldType . ' #' . $fieldId ?></small>
                            </div>
                            <div class="mt-9">
                                <span><?= $fieldLabel ?></span>
                            </div>
                        </td>
                        <td>
                            <div class="date-text">
                                <p class="date-subtitle-text"><?= format_date($fieldDeletedAt, true, true) ?></p>
                            </div>
                        </td>
                        <td class="text-end h-0" data-lt-dropdown-cell="true">
                            <a href="JAVASCRIPT::avoid(0)" class="listing-cell-link"></a>
                            <div class="dropstart mb-auto" data-lt-dropdown="true">
                                <form action="<?= route('owner.local_entities_data.restore', $fieldEntityKey) ?>"
                                      method="POST">
                                    <button type="button" class="btn btn-primary"
                                            data-bs-toggle="modal" data-bs-target="#modalDelete"
                                            data-md-message="<?= sprintf(__t('Are you sure you want to restore %s?'), $fieldLabel) ?>"
                                            data-md-action="<?= route('owner.local_entities_data.restore', $fieldEntityKey) ?>"
                                            data-md-method="POST"
                                            data-md-data='<?= json_encode(['_method' => 'PATCH', '_token' => csrf_token(), 'id' => $fieldId, 'related_entity_key' => $entityKey, 'field_label' => $fieldLabel]) ?>'><?= __t('Restore') ?></button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
