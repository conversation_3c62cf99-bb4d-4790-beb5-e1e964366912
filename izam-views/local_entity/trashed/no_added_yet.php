<?php

$extends = 'layouts/layout';
$this->sharedVars['extraContainerClass'] = 'container';
?>

<?php $this->section('page-head') ?>
<div class="d-none d-lg-block">
    <div class="page-head view-page-head">
        <div class="container">
            <div class="page-head-row">
                <div class="hstack">
                    <h3 class="my-7">
                        <?= $label ?>
                    </h3>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection() ?>


<article class="content-body" data-content-body="true">
    <div class="page-content">
        <div class="container">
            <div class="my-10">
                <div class="listing-placeholder">
                    <img src="/webroot/frontend/assets/img/components/listing/listing-placeholder/no-added-yet.svg"
                         alt="No Records Added Yet"/>
                    <div class="listing-placeholder-body">
                        <div>
                            <h6><?= __t('No Records Added Yet') ?></h6>
                        </div>
                        <div class="listing-placeholder-actions">
                            <button class="link-primary" onclick="history.back()">
                                <i class="mdi mdi-arrow-left me-3"></i>
                                <span><?= __t("Go To Previous Page") ?></span>
                            </button>
                            <a class="link-success" href="<?= route('owner.local_entities.builder', array_merge(['entityKey' => $entityKey], $_GET)) ?>">
                                <i class=" me-3 fal fa-pager"></i>
                                <span><?= __t('Update Structure') ?></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</article>
<?php $this->section('page-before-js') ?>

<?php $this->endSection() ?>




