<div class="dropdown header-user">
    <button class="dropdown-toggle header-user-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
        <span class="header-user-img">
            <?php if ($user['type'] == 'client' && $user['photo']) { ?>
                <img src="<?= $user['photo_full_path'] . '?w=30&amp;h=30&amp;c=1'; ?>" loading="lazy"/>
            <?php } else { ?>
                <img
                    src="<?= ($user['staff_id'] == 0 && $user['site_logo']) ? '/files/images/site-logos/' . $user['site_logo'] : \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($username, $user['staff_id'], 30, empty($user['photo']) ? null : $user['photo']); ?>"
                    loading="lazy"
                    onerror="this.src = '<?= \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($username, $user['staff_id'], 30, null) ?>'"/>
            <?php } ?>
        </span>
        <span class="header-user-info">
            <span class="header-user-title"><?= $username ?></span>
            <span
                class="header-user-subtitle"><?= $branchName ? $branchName : format_date(date("Y-m-d H:i:s"), false, true) ?></span>
        </span>
    </button>
    <ul class="dropdown-menu dropdown-menu-end header-user-dropdown-menu">
        <?php if(is_countable($staffBranches) && count($staffBranches)) { ?>
        <li><h6 class="dropdown-header"><?= __t('Branches') ?></h6></li>
        <?php } ?>
        <?php
        $updateCurrentBranchParams = [];
        if (!in_array($_SERVER['REQUEST_URI'], [
            '/v2/owner/entity/production_routing/create',
            '/v2/owner/entity/bom/create'
        ])) {
            $updateCurrentBranchParams = ['last_url' => $_SERVER['REQUEST_URI']];
        }
        foreach ($staffBranches as $staffBranchID => $staffBranch) { ?>
            <li>
                <a class="dropdown-item"
                   href="<?= $this->url(['controller' => 'staffs', 'action' => 'update_current_branch', $staffBranchID, '?'=>['last_url'=> base64_encode($_SERVER['REQUEST_URI'])]]) ?>">
                    <i class="fa fa-building middle"></i>
                    <span><?= $staffBranch ?></span>
                </a>
            </li>
        <?php } ?>
        <li class="d-lg-none"><h6 class="dropdown-header"><?= __t('Help') ?></h6></li>
        <li class="d-lg-none">
            <a class="dropdown-item" href="https://docs.<?= Domain_Short_Name ?>" target="_blank">
                <i class="mdi mdi-lifebuoy"></i>
                <span><?= __t('System Manual') ?></span>
            </a>
        </li>
        <li class="d-lg-none">
            <a class="dropdown-item" href="/owner/sites_enquiries/index">
                <i class="mdi mdi-lifebuoy"></i>
                <span><?= __t('Submit Feedback') ?></span>
            </a>
        </li>
        <li class="d-lg-none">
            <a class="dropdown-item" href="https://<?= Portal_Full_Name ?>/about-<?= Domain_Name_Only ?>"
               target="_blank">
                <i class="mdi mdi-lifebuoy"></i>
                <span><?= __t('About') . ' ' . Site_Full_name_NoSpace ?></span>
            </a>
        </li>
        <li><h6 class="dropdown-header"><?= __t('My Account') ?></h6></li>
        <?php if ($changeEmailUrl) { ?>
            <li>
                <a class="dropdown-item" href="<?= $changeEmailUrl ?>">
                    <i class="mdi mdi-email"></i>
                    <span><?= __t("Change Email") ?></span>
                </a>
            </li>
        <?php } ?>
        <?php if ($changePasswordUrl) { ?>
            <li>
                <a class="dropdown-item" href="<?= $changePasswordUrl ?>">
                    <i class="mdi mdi-lock"></i>
                    <span><?= __t("Change Password") ?></span>
                </a>
            </li>
        <?php } ?>
        <?php
            if ($user['type'] == 'client') {
                $mailSubscriptionUrl = $this->url(array('controller' => 'entity_email_prefrences', 'action' => 'index', 'client' => true));
            } elseif (showEmailPreferences()) {
                $mailSubscriptionUrl = '/v2/owner/email-prefrence/subscription'; ?>
                <li>
                    <a class="dropdown-item" href="<?= $mailSubscriptionUrl ?>">
                        <i class="fa fa-mail-reply middle"></i>
                        <span><?= __t("E-mail preferences") ?> </span>
                    </a>
                </li>
            <?php } ?>

        <?php if ($QRCodeAccessUrl) { ?>
            <li>
                <a class="dropdown-item" href="<?= $QRCodeAccessUrl ?>">
                    <i class="fa fa-qrcode middle"></i>
                    <span><?= sprintf(__t('%s Mobile Apps', true), __t(Site_Full_name_NoSpace)) ?></span>
                </a>
            </li>
        <?php } ?>
        <?php if ($editMyDetails) { ?>
            <li>
                <a class="dropdown-item" href="<?= $editMyDetails ?>">
                    <i class="mdi mdi-user"></i>
                    <span><?= __t("Edit My Details") ?></span>
                </a>
            </li>
        <?php } ?>
        <?php if (!empty($user['is_super_admin']) && $user['is_super_admin']) { ?>
            <?php if (!strtotime($user['expiry_date'])) { ?>
                <li>
                    <a class="dropdown-item"
                       href="<?= $this->url(array('controller' => 'sites', 'action' => 'upgrade')) ?>" target="_blank">
                        <i class="mdi mdi-refresh"></i>
                        <span><?= __t('Upgrade Subscription') ?></span>
                    </a>
                </li>
            <?php } else { ?>
                <li>
                    <a class="dropdown-item"
                       href="<?= $this->url(array('controller' => 'sites', 'action' => 'renew')) ?>" target="_blank">
                        <i class="mdi mdi-refresh"></i>
                        <span><?= __t('Renew Subscription') ?></span>
                    </a>
                </li>
            <?php } ?>
        <?php } ?>
        <?php if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::WebsiteFrontPlugin) && !IS_MOBILE_APPLICATION) { ?>
            <li class="d-lg-none">
                <a class="dropdown-item" href="/home" target="_blank">
                    <i class="mdi mdi-storefront"></i>
                    <span><?= __t('Go to website') ?></span>
                </a>
            </li>
        <?php } ?>
        <?php if (getCurrentSite('beta_version') == 1) { ?>
            <li class="d-lg-none">
                <button class="dropdown-item bg-info text-white" type="button"
                        onclick="bootstrap.Dropdown.getOrCreateInstance(document.querySelector('[data-header-beta-dropdown]')).toggle()">
                    <i class="mdi mdi-rocket-launch"></i>
                    <span><?= ucfirst(__t('beta')) ?></span>
                </button>
            </li>
        <?php } ?>
        <li>
            <a class="dropdown-item" href="<?= $LogoutUrl; ?>">
                <i class="mdi mdi-logout"></i>
                <span><?= __t("Log out") ?></span>
            </a>
        </li>
    </ul>
</div>
