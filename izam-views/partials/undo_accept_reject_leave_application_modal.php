<div class="modal fade" id="undoLeaveApplicationStatusModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="ui-modal-content ui-modal-content--height-auto">
                <form method="post" id="undo-leave-application-status-form" action="/v2/owner/entity/leave_application/<?=$id?>/update-status">
                     <?=csrf_field()?>
                     <input type="hidden" name="_method" value="put">
                     <div class="l-modal-header ui-modal-header">
                        <h5 class="l-modal-title ui-modal-title">
                            <span id="undoLeaveApplicationStatusModalLabel"></span>
                        </h5>
                        <button type="button" class="ui-btn-icon l-modal-header-btn ui-modal-header-btn" data-bs-dismiss="modal" aria-label="Close">
                            <i class="mdi mdi-close-thick ui-icon l-icon ui-icon--size-14"></i>
                        </button>
                    </div>
                    <div class="ui-modal-body">
                        <div class="l-flex-row">
                            <div class="l-flex-col-lg-12">
                                <div class="l-flex l-flex--align-center l-flex--justify-between">
                                    <p class="form-control" id="undoCommentDisplay">
                                    <?=__t("Undoing this action will trigger the system to re-evaluate the leave application against the latest approval configurations. It may result in the request being reassigned to another configuration, and you might lose access to manage it further. Do you want to proceed?")?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="l-modal-footer ui-modal-footer">
                        <div class="l-flex-row l-flex-row--spacing-5 l-flex-col">

                            <div class="l-flex-col-lg-4">
                                <button class="l-btn-inline l-btn--text-center ui-btn ui-btn--py-12 l-btn ui-btn--hover-ripple u-bg-color-secondary u-text-color-action u-text-hover-color-action" type="button" data-bs-dismiss="modal">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark-2"></span>
                                    <span class="ui-btn-inner-content">
                                        <span class="ui-btn-inner-text l-flex l-flex--align-center">
                                            <span><?=__t('Cancel')?></span>
                                        </span>
                                    </span>
                                </button>
                            </div>
                            <input type="hidden" name="status" id="undoApplicationStatusInput">
                            <input type="hidden" name="comment" id="undoCommentInput">
                            <div class="l-flex-col-lg-8">
                                <button type="submit" id="undoLeaveApplicationStatusBtn" class="l-btn-inline l-btn--text-center ui-btn ui-btn--py-12 l-btn ui-btn--hover-ripple u-bg-color-danger u-text-color-white u-text-hover-color-white">
                                    <span class="ui-btn-inner-ripple ui-btn-inner-ripple-dark"></span>
                                        <span class="ui-btn-inner-content">
                                            <span class="ui-btn-inner-text l-flex l-flex--align-center">
                                                <span id="undoSubmitBtnTxt"></span>
                                            </span>
                                    </span>
                                </button>
                            </div>

                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

<script>
    function removeQueryParams(paramsToRemove = []) {
        const url = new URL(window.location.href);
        for (const param of paramsToRemove) {
            url.searchParams.delete(param);
        }
        window.history.replaceState({}, document.title, url.toString());
    }

    window.onload = function () {

        $(function() {
            const acceptBtn = $('.acceptLeaveApplicationBtnJs');
            const rejectBtn = $('.rejectLeaveApplicationBtnJs');
            const submitBtn = $('#undoLeaveApplicationStatusBtn');
            const submitBtnTxt = $('#undoSubmitBtnTxt');
            const commentDisplay = $('#undoCommentDisplay');
            const commentInput = $('#undoCommentInput');
            const applicationStatusInput = $('#undoApplicationStatusInput');
            const modalLabel = $('#undoLeaveApplicationStatusModalLabel')[0];
            const undoApprovalBtn = $('.undoApprovalLeaveApplicationBtnJs');

            let prevStatus = '';

            const changeModalContent = function(status) {
                // if (prevStatus != status) {
                //     commentInput.val('')
                // }
                if (status == 'undo') {
                    modalLabel.innerText = "<?= __t('Undo Approval Comment') ?>"
                    submitBtnTxt.text("<?= __t('Undo Approval') ?>")
                    submitBtn.removeClass("u-bg-color-danger");
                    submitBtn.addClass("u-bg-color-success");
                    applicationStatusInput.val('pending')
                    prevStatus = status;
                }
            }

            undoApprovalBtn.click(function() {
                changeModalContent("undo");
            })

            commentDisplay.on('input', function(e) {
                commentInput.val(e.target.value)
            })
            
           
            
        })
    }
</script>
