<?php

namespace App\Console\Commands\Queue\Izam;

use Illuminate\Console\Command;
use Izam\Daftra\Queue\Services\QueueServerService;

class RunSitesQueueCommands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'izam-queue:run-commands {server_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * @var QueueServerService $queueService
     */
    private $queueService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(QueueServerService $queueService)
    {
        parent::__construct();
        $this->queueService = $queueService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $fullVersion = explode('.', PHP_VERSION);
        $version = $fullVersion[0].'.'.$fullVersion[1];
       while (true) {
           sleep(1);
           $commands = $this->queueService->getCommandsWillBeRun(50);
           if ($commands->count()) {
               echo "Get Commands Will be run with count ".$commands->count().PHP_EOL;
           }
           foreach ($commands as $command) {
               $events = $this->queueService->getEventsPerSite($command->site_id);
               foreach ($events as $event) {
                   echo "excuting command izam-queue:run-event {$command->site_id} {$event->id}".PHP_EOL;
                   exec("php{$version} /var/www/html/".LARAVEL_DIR."/artisan izam-queue:run-event {$command->site_id} {$event->id} > /dev/null 2>&1 &");
               }
               $this->queueService->deleteCommandBySite($command->site_id);
           }
       }

    }
}
