<?php

namespace App\Console\Commands\Queue\Izam;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Izam\Daftra\Common\Queue\EventPlatformUtil;
use Izam\Daftra\Common\Queue\ListenerStatusUtil;
use Izam\Daftra\Queue\Services\QueueServerService;

class EventQueueExecutor extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     * 10 - 15,16
     */
    protected $signature = 'izam-queue:run-event {site_id} {event_id} {server_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gets Sites that has events and run 2 processes for each one of them';

    /**
     * @var QueueServerService $queueService
     */
    private $queueService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(QueueServerService $queueService)
    {
        parent::__construct();
        $this->queueService = $queueService;
        DB::disconnect('mysql');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $siteId = $this->argument('site_id');

        if ($this->queueService->getCurrentProcessingListenersCount() > config('event_queue.max_process_count')) {
            exit;
        }

        if ($this->queueService->isSiteExceedMaxProcessing($siteId)) {
            exit;
        }

        $eventId = $this->argument('event_id');
        $event = $this->queueService->getEvent($eventId);

        if ( empty($event) || $this->queueService->isEventRunningBySignature($event->signature, $siteId)) {
            exit;
        }
        $listenerRunningTimes = [];
        $listeners = $event->listenerUnFinished;
        if ($listeners->count() == 0) {
            if ($event->listeners->count() == 0) {
                Log::error("Event have no listeners to run : {$event->id}");
            }
            $this->queueService->finishEvent($event);
            return;
        }
        $this->queueService->processEvent($event);
        while ($listeners->count()) {
            $listener = $listeners->first();
            if(!isset($listenerRunningTimes[$listener->id])) {
                $listenerRunningTimes[$listener->id] = 0;
            }
            $listenerRunningTimes[$listener->id] += 1;
            if($listenerRunningTimes[$listener->id] > 5) {
                $listener->status = ListenerStatusUtil::KILLED;
                if(is_array($output)) {
                    $listener->error.= implode('',$output);
                } else {
                    $listener->error.= $output;
                }
                $listener->save();
            }
            $fullVersion = explode('.', PHP_VERSION);
            $version = $fullVersion[0].'.'.$fullVersion[1];
            if ($listener->platform == EventPlatformUtil::LARAVEL) {
                exec("php{$version} /var/www/html/".LARAVEL_DIR."/artisan laravel-listener:execute {$listener->id}", $output);
            } else {
                exec(
                    "/usr/bin/php{$version} /var/www/html/".CAKE_DIR."/webroot/cron.php /cron/cake_listener_execute/{$listener->id}",
                    $output
                );
            }
            $event->load('listenerUnFinished');
            $listeners = $event->listenerUnFinished;
        }
        $this->queueService->finishEvent($event);
    }


}
