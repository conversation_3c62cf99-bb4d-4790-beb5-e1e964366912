<?php

namespace App\Console\Commands\Queue\Izam;

use App\Facades\Branch;
use App\Models\Site;
use App\Modules\LocalEntity\EntityCacheDriver;
use App\Providers\CurrentSiteDatabaseProvider;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\URL;
use Izam\Daftra\Common\Auth\AuthHelper;
use Izam\Daftra\Common\DatabaseHelper;
use Izam\Daftra\Common\EntityStructure\EntityAndRelationCache;
use Izam\Daftra\Common\Queue\ListenerStatusUtil;
use Izam\Daftra\Common\Services\RedisLockService;
use Izam\Daftra\Queue\Services\QueueServerService;
use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;

class LaravelListenerExecutor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'laravel-listener:execute {id} {server_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'run every listener as single process';

    /**
     * @var QueueServerService $queueService
     */
    private $queueService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(QueueServerService $queueService)
    {
        parent::__construct();
        $this->queueService = $queueService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $redisLockAquired = RedisLockService::acquireLock("lock_queue_listener_id:" . $this->argument('id'));
        if (!$redisLockAquired) {
            exit;
        }
        $listener = $this->queueService->getListener($this->argument('id'));
        if (empty($listener)) {
            exit;
        }
        $site =  Site::find($listener->site_id);
        if (empty($site)) {
            $this->queueService->finishListener($listener, [],  "Site Not Found");
            exit;
        }
        CurrentSiteDatabaseProvider::resetDefaultConnection($site);
        //every listener when execute must run on master queue database so if any job created new job
        //we need to reset connection to be in server
        IzamDatabaseServiceProvider::addConnection( config()['database']['connections']['queue_server'], 'local_queue');
        URL::forceRootUrl(formatSubdomain($site->subdomain));
        if ($listener->status == ListenerStatusUtil::PROCESSING) {
            exit;
        }
        if (empty($listener->action_event)) {
            $this->queueService->killProcess($listener, "There is no event");
            exit;
        }
        try {
            $this->handleAuth($listener->action_event->token);
            \App\Modules\LocalEntity\Helpers\EntityAndRelationCache::setCacheDriver(new EntityCacheDriver(getCurrentSite('id')));
            EntityAndRelationCache::loadFromCache();
            DatabaseHelper::setPdo(getPdo('currentSite'));
        } catch (\Exception $exception) {
            $listener->status = ListenerStatusUtil::FINISHED;
            $listener->error = "Not Authorized :".$exception;
            $listener->finished_at = "{$this->getTime()}";
            $listener->save();
            exit;
        }
        $this->runListener($listener);
    }

    private function runListener($listener) {
        $serverId = $this->argument('server_id');
        // value retrieved from env or setting
        $this->queueService->checkProcessExceedTries($listener);
        try {
            set_time_limit(300);
            $this->queueService->executeListener($listener, $serverId);
            $handler = $listener->handler;
            $handlerObj = $handler::getInstance();
            $result = $handlerObj->handle($listener->action_event);
            $this->queueService->finishListener($listener, $result);
//            $this->queueService->finishEvent($listener->action_event);
        } catch (\Throwable $exception) {
            $this->queueService->listenerHasError($listener, $exception);
            throw new Exception($exception);
        }
        exit;
    }


    public function handleAuth($token) {
        $tokenData = jwt_parse_token($token);
        Branch::setCurrentBranchId($tokenData->userBranch);
        $sessionData = AuthHelper::generateLoggedUserSessionData($tokenData->userType, $tokenData->userId);
        AuthHelper::setLoggedUserSessionData($sessionData);
    }

    private function getTime()
    {
        return get_utc_date_time();
    }

}
